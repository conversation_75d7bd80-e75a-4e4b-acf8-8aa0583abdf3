# GitLab CI/CD Pipeline for Publish AI Microservices
# Comprehensive pipeline with testing, security scanning, building, and deployment

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # Registry configuration
  REGISTRY: $CI_REGISTRY
  REGISTRY_USER: $CI_REGISTRY_USER
  REGISTRY_PASSWORD: $CI_REGISTRY_PASSWORD
  
  # Kubernetes configuration
  KUBECONFIG: /tmp/kubeconfig
  
  # Environment configuration
  DEVELOPMENT_NAMESPACE: "publish-ai-development"
  STAGING_NAMESPACE: "publish-ai-staging"
  PRODUCTION_NAMESPACE: "publish-ai-production"
  
  # Security scanning
  SAST_DISABLED: "false"
  DEPENDENCY_SCANNING_DISABLED: "false"
  CONTAINER_SCANNING_DISABLED: "false"
  
  # Testing configuration
  PYTEST_OPTS: "--tb=short --strict-markers --disable-warnings"
  COVERAGE_THRESHOLD: "80"

# Pipeline stages
stages:
  - validate
  - test
  - security
  - build
  - deploy-dev
  - integration-test
  - deploy-staging
  - e2e-test
  - deploy-production
  - post-deploy

# Include templates
include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/Container-Scanning.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml

# Global before_script
before_script:
  - echo "Starting CI/CD pipeline for commit $CI_COMMIT_SHA"
  - echo "Pipeline ID: $CI_PIPELINE_ID"
  - echo "Branch: $CI_COMMIT_REF_NAME"

# ============================================
# VALIDATION STAGE
# ============================================

validate-code-style:
  stage: validate
  image: python:3.11-slim
  before_script:
    - pip install black isort flake8 mypy
  script:
    - echo "Running code style validation..."
    - black --check --diff app/ tests/
    - isort --check-only --diff app/ tests/
    - flake8 app/ tests/
    - mypy app/ --ignore-missing-imports
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

validate-docker-files:
  stage: validate
  image: hadolint/hadolint:latest-debian
  script:
    - echo "Validating Dockerfiles..."
    - find . -name "Dockerfile*" -exec hadolint {} \;
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

validate-kubernetes-manifests:
  stage: validate
  image: alpine/k8s:1.28.4
  script:
    - echo "Validating Kubernetes manifests..."
    - find infrastructure/ -name "*.yaml" -exec kubectl apply --dry-run=client -f {} \;
    - find infrastructure/ -name "*.yml" -exec kubectl apply --dry-run=client -f {} \;
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================
# TESTING STAGE
# ============================================

unit-tests:
  stage: test
  image: python:3.11-slim
  services:
    - postgres:15
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_publishai
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    REDIS_URL: redis://redis:6379
    DATABASE_URL: **************************************************/test_publishai
  before_script:
    - apt-get update && apt-get install -y build-essential
    - pip install poetry
    - poetry config virtualenvs.create false
    - poetry install
  script:
    - echo "Running unit tests..."
    - pytest tests/unit/ $PYTEST_OPTS --cov=app --cov-report=xml --cov-report=term
    - echo "Checking coverage threshold..."
    - coverage report --fail-under=$COVERAGE_THRESHOLD
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov/
    expire_in: 1 week
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

integration-tests:
  stage: test
  image: python:3.11-slim
  services:
    - postgres:15
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_publishai
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    REDIS_URL: redis://redis:6379
    DATABASE_URL: **************************************************/test_publishai
  before_script:
    - apt-get update && apt-get install -y build-essential
    - pip install poetry
    - poetry config virtualenvs.create false
    - poetry install
  script:
    - echo "Running integration tests..."
    - pytest tests/integration/ $PYTEST_OPTS --tb=long
  artifacts:
    reports:
      junit: tests/integration/test-results.xml
    paths:
      - tests/integration/logs/
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

agent-tests:
  stage: test
  image: python:3.11-slim
  services:
    - postgres:15
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_publishai
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    REDIS_URL: redis://redis:6379
    DATABASE_URL: **************************************************/test_publishai
    # Mock AI API keys for testing
    OPENAI_API_KEY: "test-key-openai"
    ANTHROPIC_API_KEY: "test-key-anthropic"
  before_script:
    - apt-get update && apt-get install -y build-essential
    - pip install poetry
    - poetry config virtualenvs.create false
    - poetry install
  script:
    - echo "Running PydanticAI agent tests..."
    - python tests/test_agents/run_agent_tests.py --suite all --mock-only
  artifacts:
    reports:
      junit: tests/test_agents/test-results.xml
    paths:
      - tests/test_agents/logs/
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================
# BUILD STAGE
# ============================================

.build-service-template: &build-service
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $REGISTRY_PASSWORD | docker login -u $REGISTRY_USER --password-stdin $REGISTRY
  script:
    - echo "Building $SERVICE_NAME..."
    - cd services/$SERVICE_NAME
    - |
      # Build multi-stage Docker image
      docker build \
        --target production \
        --build-arg SERVICE_NAME=$SERVICE_NAME \
        --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
        --build-arg VCS_REF=$CI_COMMIT_SHA \
        --build-arg VERSION=$CI_COMMIT_TAG \
        -t $REGISTRY/$CI_PROJECT_PATH/$SERVICE_NAME:$CI_COMMIT_SHA \
        -t $REGISTRY/$CI_PROJECT_PATH/$SERVICE_NAME:latest \
        .
    - docker push $REGISTRY/$CI_PROJECT_PATH/$SERVICE_NAME:$CI_COMMIT_SHA
    - docker push $REGISTRY/$CI_PROJECT_PATH/$SERVICE_NAME:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

build-api-gateway:
  <<: *build-service
  variables:
    SERVICE_NAME: "api-gateway"

build-content-generation:
  <<: *build-service
  variables:
    SERVICE_NAME: "content-generation"

build-market-intelligence:
  <<: *build-service
  variables:
    SERVICE_NAME: "market-intelligence"

build-publishing-service:
  <<: *build-service
  variables:
    SERVICE_NAME: "publishing-service"

build-cover-designer:
  <<: *build-service
  variables:
    SERVICE_NAME: "cover-designer"

build-sales-monitor:
  <<: *build-service
  variables:
    SERVICE_NAME: "sales-monitor"

build-personalization:
  <<: *build-service
  variables:
    SERVICE_NAME: "personalization"

build-research:
  <<: *build-service
  variables:
    SERVICE_NAME: "research"

build-multimodal-generator:
  <<: *build-service
  variables:
    SERVICE_NAME: "multimodal-generator"

# ============================================
# DEVELOPMENT DEPLOYMENT
# ============================================

deploy-development:
  stage: deploy-dev
  image: alpine/k8s:1.28.4
  environment:
    name: development
    url: https://dev.publishai.com
  before_script:
    - echo $KUBECONFIG_DEV | base64 -d > $KUBECONFIG
    - kubectl config current-context
  script:
    - echo "Deploying to development environment..."
    - |
      # Update image tags in development manifests
      for service in api-gateway content-generation market-intelligence publishing-service cover-designer sales-monitor personalization research multimodal-generator; do
        kubectl set image deployment/$service \
          $service=$REGISTRY/$CI_PROJECT_PATH/$service:$CI_COMMIT_SHA \
          -n $DEVELOPMENT_NAMESPACE
      done
    - kubectl rollout status deployment -n $DEVELOPMENT_NAMESPACE --timeout=600s
    - echo "Development deployment completed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================
# INTEGRATION TESTING IN DEV
# ============================================

integration-test-dev:
  stage: integration-test
  image: python:3.11-slim
  environment:
    name: development
  variables:
    API_GATEWAY_URL: "https://dev.publishai.com"
  before_script:
    - pip install requests pytest pytest-html
  script:
    - echo "Running integration tests against development environment..."
    - |
      cat << 'EOF' > test_dev_integration.py
      import requests
      import pytest
      import time
      import os

      API_BASE = os.environ.get('API_GATEWAY_URL', 'https://dev.publishai.com')

      def test_api_gateway_health():
          """Test API Gateway health endpoint"""
          response = requests.get(f"{API_BASE}/health", timeout=30)
          assert response.status_code == 200
          assert "healthy" in response.text.lower() or "ok" in response.text.lower()

      def test_service_discovery():
          """Test service discovery through API Gateway"""
          services = ['content-generation', 'market-intelligence', 'publishing-service']
          for service in services:
              response = requests.get(f"{API_BASE}/api/{service}/health", timeout=30)
              assert response.status_code == 200

      def test_api_routing():
          """Test API Gateway routing functionality"""
          response = requests.get(f"{API_BASE}/status", timeout=30)
          assert response.status_code == 200

      def test_load_balancing():
          """Test load balancing by making multiple requests"""
          for i in range(5):
              response = requests.get(f"{API_BASE}/health", timeout=30)
              assert response.status_code == 200
              time.sleep(1)

      if __name__ == "__main__":
          pytest.main([__file__, "-v", "--html=integration_report.html"])
      EOF
    - python test_dev_integration.py
  artifacts:
    reports:
      junit: integration_report.xml
    paths:
      - integration_report.html
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================
# STAGING DEPLOYMENT
# ============================================

deploy-staging:
  stage: deploy-staging
  image: alpine/k8s:1.28.4
  environment:
    name: staging
    url: https://staging.publishai.com
  before_script:
    - echo $KUBECONFIG_STAGING | base64 -d > $KUBECONFIG
    - kubectl config current-context
  script:
    - echo "Deploying to staging environment..."
    - |
      # Update staging manifests with specific image tags
      kubectl patch deployment api-gateway \
        -p '{"spec":{"template":{"spec":{"containers":[{"name":"api-gateway","image":"'$REGISTRY/$CI_PROJECT_PATH'/api-gateway:'$CI_COMMIT_SHA'"}]}}}}' \
        -n $STAGING_NAMESPACE
      
      kubectl patch deployment content-generation \
        -p '{"spec":{"template":{"spec":{"containers":[{"name":"content-generation","image":"'$REGISTRY/$CI_PROJECT_PATH'/content-generation:'$CI_COMMIT_SHA'"}]}}}}' \
        -n $STAGING_NAMESPACE
        
      # Add other services...
      for service in market-intelligence publishing-service cover-designer sales-monitor personalization research multimodal-generator; do
        kubectl patch deployment $service \
          -p '{"spec":{"template":{"spec":{"containers":[{"name":"'$service'","image":"'$REGISTRY/$CI_PROJECT_PATH'/'$service':'$CI_COMMIT_SHA'"}]}}}}' \
          -n $STAGING_NAMESPACE
      done
    - kubectl rollout status deployment -n $STAGING_NAMESPACE --timeout=600s
    - echo "Staging deployment completed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual

# ============================================
# E2E TESTING IN STAGING
# ============================================

e2e-test-staging:
  stage: e2e-test
  image: mcr.microsoft.com/playwright/python:v1.40.0-jammy
  environment:
    name: staging
  variables:
    API_GATEWAY_URL: "https://staging.publishai.com"
    FRONTEND_URL: "https://staging-app.publishai.com"
  before_script:
    - pip install playwright pytest-playwright
    - playwright install chromium
  script:
    - echo "Running E2E tests against staging environment..."
    - |
      cat << 'EOF' > test_e2e_staging.py
      import pytest
      from playwright.sync_api import sync_playwright
      import os

      API_BASE = os.environ.get('API_GATEWAY_URL', 'https://staging.publishai.com')
      FRONTEND_BASE = os.environ.get('FRONTEND_URL', 'https://staging-app.publishai.com')

      def test_full_user_journey():
          """Test complete user journey from registration to book creation"""
          with sync_playwright() as p:
              browser = p.chromium.launch()
              page = browser.new_page()
              
              # Test homepage load
              page.goto(FRONTEND_BASE)
              page.wait_for_load_state("networkidle")
              assert "Publish AI" in page.title()
              
              # Test API endpoints
              response = page.request.get(f"{API_BASE}/health")
              assert response.status == 200
              
              browser.close()

      def test_api_performance():
          """Test API response times"""
          with sync_playwright() as p:
              browser = p.chromium.launch()
              context = browser.new_context()
              page = context.new_page()
              
              # Test API response times
              import time
              start_time = time.time()
              response = page.request.get(f"{API_BASE}/health")
              end_time = time.time()
              
              assert response.status == 200
              assert (end_time - start_time) < 2.0  # Response should be under 2 seconds
              
              browser.close()

      def test_service_integration():
          """Test integration between frontend and backend services"""
          with sync_playwright() as p:
              browser = p.chromium.launch()
              page = browser.new_page()
              
              # Test service status page
              response = page.request.get(f"{API_BASE}/status")
              assert response.status == 200
              
              browser.close()

      if __name__ == "__main__":
          pytest.main([__file__, "-v", "--html=e2e_report.html"])
      EOF
    - python test_e2e_staging.py
  artifacts:
    paths:
      - e2e_report.html
      - test-results/
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================
# PRODUCTION DEPLOYMENT
# ============================================

deploy-production:
  stage: deploy-production
  image: alpine/k8s:1.28.4
  environment:
    name: production
    url: https://publishai.com
  before_script:
    - echo $KUBECONFIG_PROD | base64 -d > $KUBECONFIG
    - kubectl config current-context
  script:
    - echo "Deploying to production environment..."
    - echo "Running pre-deployment checks..."
    - |
      # Run production readiness check
      curl -fsSL https://raw.githubusercontent.com/publishai/infrastructure/main/production/scripts/production-readiness-check.sh | bash -s -- --severity critical
    - echo "Deploying with canary strategy..."
    - |
      # Deploy using canary strategy
      for service in api-gateway content-generation market-intelligence publishing-service cover-designer sales-monitor personalization research multimodal-generator; do
        echo "Deploying $service with canary strategy..."
        
        # Update the canary deployment
        kubectl patch deployment $service-canary \
          -p '{"spec":{"template":{"spec":{"containers":[{"name":"'$service'","image":"'$REGISTRY/$CI_PROJECT_PATH'/'$service':'$CI_COMMIT_SHA'"}]}}}}' \
          -n $PRODUCTION_NAMESPACE || true
        
        # Wait for canary to be ready
        kubectl rollout status deployment/$service-canary -n $PRODUCTION_NAMESPACE --timeout=300s || true
        
        # Run health checks on canary
        sleep 30
        
        # If canary is healthy, update main deployment
        kubectl patch deployment $service \
          -p '{"spec":{"template":{"spec":{"containers":[{"name":"'$service'","image":"'$REGISTRY/$CI_PROJECT_PATH'/'$service':'$CI_COMMIT_SHA'"}]}}}}' \
          -n $PRODUCTION_NAMESPACE
        
        # Wait for main deployment
        kubectl rollout status deployment/$service -n $PRODUCTION_NAMESPACE --timeout=600s
        
        echo "$service deployed successfully"
      done
    - echo "Production deployment completed successfully"
  rules:
    - if: $CI_COMMIT_TAG
  when: manual

# ============================================
# POST-DEPLOYMENT
# ============================================

post-deploy-verification:
  stage: post-deploy
  image: alpine/k8s:1.28.4
  environment:
    name: production
  before_script:
    - echo $KUBECONFIG_PROD | base64 -d > $KUBECONFIG
    - apk add --no-cache curl
  script:
    - echo "Running post-deployment verification..."
    - |
      # Run comprehensive verification
      curl -fsSL https://raw.githubusercontent.com/publishai/infrastructure/main/production/scripts/verify-production-deployment.sh | bash
    - echo "Sending deployment notification..."
    - |
      # Send Slack notification
      if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"🚀 Publish AI deployed to production successfully!\nCommit: '$CI_COMMIT_SHA'\nPipeline: '$CI_PIPELINE_URL'"}' \
          $SLACK_WEBHOOK_URL
      fi
  rules:
    - if: $CI_COMMIT_TAG

# ============================================
# CLEANUP
# ============================================

cleanup-registry:
  stage: post-deploy
  image: alpine:latest
  script:
    - echo "Cleaning up old container images..."
    - |
      # Keep only last 10 images per service
      apk add --no-cache curl jq
      
      for service in api-gateway content-generation market-intelligence publishing-service cover-designer sales-monitor personalization research multimodal-generator; do
        echo "Cleaning up $service images..."
        # This would integrate with your registry API to clean up old images
        # Implementation depends on your registry (GitLab, Docker Hub, etc.)
      done
  rules:
    - if: $CI_COMMIT_TAG
  when: manual

# ============================================
# CACHE CONFIGURATION
# ============================================

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .pip-cache/
    - node_modules/
    - ~/.cache/pip/
    - ~/.cache/poetry/

# ============================================
# ARTIFACTS CONFIGURATION
# ============================================

# Global artifacts configuration
.artifacts_template: &artifacts_config
  expire_in: 1 week
  when: always
  paths:
    - logs/
    - reports/