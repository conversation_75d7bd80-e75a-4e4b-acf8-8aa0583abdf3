# Monitoring Integration Documentation

## Overview

This document describes the comprehensive monitoring and logging integration implemented in the AI-powered e-book generation system. The integration includes **Sentry** for exception tracking and performance monitoring, and **Logflare** for structured logging and analytics.

## 🎯 Features Implemented

### ✅ Sentry Integration
- **Exception Tracking**: Automatic capture of errors with full stack traces
- **Performance Monitoring**: Request tracing and performance metrics
- **Custom Context**: User ID, request ID, and operation context
- **Data Scrubbing**: Automatic removal of sensitive information
- **Environment-specific Configuration**: Separate configs for dev/staging/production

### ✅ Logflare Integration  
- **Structured Logging**: JSON-formatted logs with metadata
- **Real-time Analytics**: Queryable log data for insights
- **Batch Processing**: Efficient log transmission
- **Custom Fields**: Request IDs, user context, and performance metrics

### ✅ Request Monitoring
- **Request Tracking**: Unique request IDs for correlation
- **Performance Monitoring**: Slow request detection
- **Security Monitoring**: Suspicious pattern detection
- **User Action Auditing**: Comprehensive audit trails

### ✅ Agent Execution Monitoring
- **Operation Tracking**: Complete agent execution monitoring
- **Error Context**: Detailed error information with execution context
- **Performance Metrics**: Execution time and resource usage
- **Success/Failure Rates**: Statistical monitoring

## 🏗 Architecture

### Core Components

```
app/monitoring/
├── monitoring_setup.py       # Central monitoring manager
└── ...

app/middleware/
├── monitoring_middleware.py  # Request monitoring middleware
└── ...

app/config.py                 # Monitoring configuration
.env.monitoring.example       # Environment configuration template
```

### Integration Points

1. **Application Startup** (`app/main_supabase.py`)
   - Monitoring initialization
   - Configuration validation
   - Health checks

2. **Request Processing** (Middleware)
   - Request context setup
   - Performance tracking
   - Security monitoring

3. **Agent Execution** (`app/agents/`)
   - Operation monitoring
   - Error capture
   - Performance metrics

4. **API Endpoints** (`app/api/`)
   - User action logging
   - Operation tracking
   - Error handling

## ⚙️ Configuration

### Environment Variables

Copy `.env.monitoring.example` to `.env` and configure:

```bash
# Sentry Configuration
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production
SENTRY_SAMPLE_RATE=1.0
SENTRY_TRACES_SAMPLE_RATE=0.1

# Logflare Configuration  
LOGFLARE_API_KEY=your-api-key
LOGFLARE_SOURCE_ID=your-source-id
LOGFLARE_BATCH_SIZE=100
LOGFLARE_FLUSH_INTERVAL=5

# General Settings
LOG_LEVEL=INFO
ENABLE_PERFORMANCE_MONITORING=true
```

### Configuration Validation

The system automatically validates monitoring configuration:

```python
from app.config import settings

validation_result = settings.validate_configuration()
print(validation_result["monitoring_status"])
```

## 🚀 Usage

### Basic Logging

```python
from app.monitoring.monitoring_setup import get_logger

logger = get_logger(__name__)

logger.info("Operation completed", user_id="123", duration=1.5)
logger.error("Operation failed", error="Connection timeout")
```

### Operation Monitoring

```python
from app.monitoring.monitoring_setup import monitor_operation

async def my_operation():
    async with monitor_operation("data_processing", user_id="123") as operation_id:
        # Your operation code here
        result = await process_data()
        return result
```

### Exception Capture

```python
from app.monitoring.monitoring_setup import capture_exception

try:
    risky_operation()
except Exception as e:
    capture_exception(e, {
        'user_id': current_user_id,
        'operation': 'data_processing',
        'additional_context': 'value'
    })
    raise
```

### User Action Logging

```python
from app.monitoring.monitoring_setup import log_user_action

log_user_action("book_created", user_id, {
    'book_id': book.id,
    'book_title': book.title,
    'creation_method': 'ai_generated'
})
```

## 🔒 Security Features

### Data Scrubbing

Sensitive data is automatically removed from logs and error reports:

```python
# These fields are automatically redacted:
sensitive_keys = [
    'password', 'secret', 'token', 'key', 'authorization',
    'kdp_password', 'openai_api_key', 'anthropic_api_key',
    'supabase_service_key', 'logflare_api_key'
]
```

### Request Security Monitoring

The security middleware automatically detects and logs:
- SQL injection attempts
- XSS attempts  
- Path traversal attempts
- Large request sizes
- Authentication failures

## 📊 Monitoring Dashboards

### Sentry Dashboard

Access your Sentry dashboard to view:
- **Issues**: Errors and exceptions with full context
- **Performance**: Request traces and performance metrics
- **Releases**: Track deployments and error rates
- **Alerts**: Configure alerts for critical issues

### Logflare Dashboard

Access your Logflare dashboard to:
- **Search Logs**: Query structured log data
- **Create Alerts**: Set up custom alerts
- **Build Dashboards**: Create custom analytics dashboards
- **Monitor Trends**: Track application usage patterns

## 🧪 Testing

### Running Tests

```bash
# Run monitoring tests
pytest tests/test_monitoring/ -v

# Run with monitoring mocks
pytest tests/ --monitoring-mock

# Run integration tests
pytest tests/test_monitoring/test_monitoring_integration.py -v
```

### Test Configuration

Tests automatically mock monitoring services:

```python
def test_with_monitoring(monitoring_test_setup):
    # monitoring_test_setup provides mocked services
    result = my_function_that_logs()
    assert result is not None
```

## 🚨 Alerting

### Automatic Alerts

The system automatically creates alerts for:
- **Critical Errors**: Exceptions in core functionality
- **Slow Requests**: Requests exceeding thresholds
- **High Error Rates**: Unusual error frequency
- **Security Events**: Suspicious activity patterns

### Custom Alerts

Configure custom alerts in Sentry and Logflare:

1. **Sentry Alerts**:
   - Navigate to Alerts in your Sentry project
   - Create issue alerts and metric alerts
   - Configure notification channels (email, Slack, etc.)

2. **Logflare Alerts**:
   - Create alert rules based on log patterns
   - Set up notification endpoints
   - Configure alert frequency and thresholds

## 🔧 Troubleshooting

### Common Issues

1. **Monitoring Not Initializing**
   ```bash
   # Check environment variables
   echo $SENTRY_DSN
   echo $LOGFLARE_API_KEY
   
   # Verify configuration
   python -c "from app.config import settings; print(settings.get_monitoring_status())"
   ```

2. **Missing Logs in Logflare**
   ```bash
   # Check Logflare source configuration
   # Verify API key permissions
   # Check network connectivity
   ```

3. **High Sentry Costs**
   ```bash
   # Reduce trace sample rate
   SENTRY_TRACES_SAMPLE_RATE=0.01
   
   # Filter out noisy errors
   # Configure release-based sampling
   ```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
DEBUG=true
SENTRY_DEBUG=true
LOG_LEVEL=DEBUG
```

## 📈 Performance Considerations

### Sentry Optimization

- **Sample Rate**: Use 0.01-0.1 for production traces
- **Error Filtering**: Filter out expected errors
- **Release Tracking**: Use releases for better context

### Logflare Optimization

- **Batch Size**: Optimize for your log volume
- **Flush Interval**: Balance latency vs. efficiency  
- **Log Level**: Use appropriate log levels for production

### Application Impact

The monitoring integration is designed for minimal performance impact:
- **Async Processing**: Non-blocking log transmission
- **Batching**: Efficient data transmission
- **Sampling**: Configurable data collection rates
- **Caching**: Intelligent caching of monitoring metadata

## 🔄 Maintenance

### Regular Tasks

1. **Monitor Costs**: Track Sentry and Logflare usage
2. **Review Alerts**: Adjust alert thresholds
3. **Rotate Keys**: Regularly rotate API keys
4. **Update Configs**: Review and update configurations
5. **Audit Logs**: Regular log audit and cleanup

### Upgrades

```bash
# Update monitoring dependencies
poetry update sentry-sdk logflare structlog

# Test after upgrades
pytest tests/test_monitoring/ -v
```

## 📝 Best Practices

### Development

1. **Use Test Environment**: Separate monitoring for dev/staging/prod
2. **Mock in Tests**: Always mock monitoring in unit tests
3. **Log Meaningfully**: Include relevant context in logs
4. **Handle Gracefully**: Monitoring failures shouldn't break app

### Production

1. **Monitor the Monitors**: Set up monitoring for monitoring services
2. **Data Retention**: Configure appropriate retention policies
3. **Access Control**: Limit access to monitoring dashboards
4. **Compliance**: Ensure monitoring meets regulatory requirements

### Security

1. **Scrub Sensitive Data**: Never log passwords or API keys
2. **Use HTTPS**: Ensure encrypted transmission
3. **Audit Access**: Regular audit of dashboard access
4. **Incident Response**: Have monitoring incident response plan

## 🆘 Support

### Resources

- **Sentry Documentation**: https://docs.sentry.io/
- **Logflare Documentation**: https://logflare.app/docs
- **Structlog Documentation**: https://structlog.org/
- **Project Issues**: Check project issue tracker for known problems

### Getting Help

1. Check this documentation first
2. Review application logs for errors
3. Check Sentry and Logflare status pages
4. Contact project maintainers with specific issues

## 📋 Summary

The monitoring integration provides comprehensive observability into the AI e-book generation system:

- ✅ **Complete Error Tracking** with Sentry
- ✅ **Structured Logging** with Logflare  
- ✅ **Performance Monitoring** for all operations
- ✅ **Security Monitoring** for threat detection
- ✅ **User Activity Auditing** for compliance
- ✅ **Automated Alerting** for critical issues
- ✅ **Production-Ready** configuration and security

The system is designed to provide maximum visibility with minimal performance impact, ensuring reliable operation and rapid issue resolution.