#!/bin/bash
# monitor_system.sh - Monitor AI Publishing Platform health

echo "🔍 AI Publishing Platform Health Monitor"
echo "========================================"

echo ""
echo "🏥 Service Health:"

# Check if backend is running
if curl -s http://localhost:8000/health 2>/dev/null | grep -q "healthy"; then
    echo "✅ Backend API: Running (http://localhost:8000)"
    # Get API info
    VERSION=$(curl -s http://localhost:8000/health 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$VERSION" ]; then
        echo "   📋 Version: $VERSION"
    fi
else
    echo "❌ Backend API: Not running"
    echo "   💡 Start with: poetry run uvicorn app.main_supabase:app --reload"
fi

# Check frontend
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ Frontend: Running (http://localhost:3000)"
else
    echo "❌ Frontend: Not running"
    echo "   💡 Start with: cd frontend && npm run dev"
fi

# Check database connection
echo ""
echo "🗄️ Database Status:"
if poetry run python -c "
from app.utils.supabase.supabase_client import get_supabase_client
try:
    client = get_supabase_client()
    result = client.client.table('users').select('count', count='exact').execute()
    print('✅ Supabase: Connected')
    print(f'   📊 Total users: {result.count}')
except Exception as e:
    print(f'❌ Supabase: Connection failed')
" 2>/dev/null; then
    echo "✅ Database connection verified"
else
    echo "❌ Database connection issues"
fi

echo ""
echo "💾 Storage Status:"
if [ -d "storage" ]; then
    echo "📁 Storage directory exists"
    echo "   📚 Manuscripts: $(find storage/manuscripts -name "*.txt" 2>/dev/null | wc -l) files"
    echo "   🎨 Covers: $(find storage/covers -name "*.png" -o -name "*.jpg" 2>/dev/null | wc -l) files"
    echo "   📖 Published: $(find storage/published -name "*" 2>/dev/null | wc -l) files"
    echo "   💾 Total size: $(du -sh storage 2>/dev/null | cut -f1)"
else
    echo "❌ Storage directory missing"
fi

echo ""
echo "🔧 Python Environment:"
PYTHON_VERSION=$(python3 --version 2>/dev/null)
echo "   🐍 Python: $PYTHON_VERSION"

if command -v poetry >/dev/null 2>&1; then
    POETRY_VERSION=$(poetry --version 2>/dev/null)
    echo "   📦 Poetry: $POETRY_VERSION"
    echo "   📋 Virtual env: $(poetry env info --path 2>/dev/null)"
else
    echo "   ❌ Poetry: Not found"
fi

echo ""
echo "📈 Recent Activity:"
if [ -f "logs/app.log" ]; then
    echo "Recent log entries:"
    tail -3 logs/app.log 2>/dev/null || echo "No recent log activity"
else
    echo "No log file found"
fi

