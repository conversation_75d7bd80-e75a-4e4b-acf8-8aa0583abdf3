{"Mock-based Tests": {"description": "Mock-based Tests", "success": false, "exit_code": 1, "execution_time": 25.47, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.12, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/bin/python\ncachedir: .pytest_cache\nrootdir: /Volumes/Baby_SSD/github/AI projects/publish-ai\nconfigfile: pyproject.toml\nplugins: anyio-4.9.0, snapshottest-0.6.0, cov-6.2.1, mock-3.14.1, hydra-core-1.3.2, asyncio-1.0.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 59 items / 1 deselected / 58 selected\n\ntests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_single_agent_execution_time FAILED [  1%]\ntests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_concurrent_agent_performance PASSED [  3%]\ntests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_workflow_execution_performance PASSED [  5%]\ntests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_usage_single_execution PASSED [  6%]\ntests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_usage_multiple_executions PASSED [  8%]\ntests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_leak_detection PASSED [ 10%]\ntests/test_agents/test_performance_load.py::TestScalabilityLimits::test_high_concurrency_limits PASSED [ 12%]\ntests/test_agents/test_performance_load.py::TestScalabilityLimits::test_large_data_processing PASSED [ 13%]\ntests/test_agents/test_performance_load.py::TestScalabilityLimits::test_rapid_sequential_requests FAILED [ 15%]\ntests/test_agents/test_performance_load.py::TestResourceUtilization::test_cpu_utilization PASSED [ 17%]\ntests/test_agents/test_performance_load.py::TestResourceUtilization::test_thread_pool_efficiency PASSED [ 18%]\ntests/test_agents/test_performance_load.py::TestStressTests::test_sustained_load PASSED [ 20%]\ntests/test_agents/test_performance_load.py::TestStressTests::test_error_recovery_under_load PASSED [ 22%]\ntests/test_agents/test_pydantic_ai_agents.py::TestAgentRegistration::test_agent_registry_is_populated PASSED [ 24%]\ntests/test_agents/test_pydantic_ai_agents.py::TestAgentRegistration::test_registry_lookup PASSED [ 25%]\ntests/test_agents/test_pydantic_ai_agents.py::TestManager::test_agent_manager_ready PASSED [ 27%]\ntests/test_agents/test_pydantic_ai_agents.py::TestManager::test_get_status_structure PASSED [ 29%]\ntests/test_agents/test_pydantic_ai_agents.py::TestWorkflow::test_basic_workflow_success PASSED [ 31%]\ntests/test_agents/test_pydantic_ai_agents.py::TestAgents::test_manuscript_generator_success PASSED [ 32%]\ntests/test_agents/test_pydantic_ai_agents.py::TestAgents::test_trend_analyzer_success FAILED [ 34%]\ntests/test_agents/test_pydantic_ai_agents.py::TestAgents::test_upload_to_kdp_success FAILED [ 36%]\ntests/test_agents/test_pydantic_ai_agents.py::TestErrorCases::test_missing_agent_fails PASSED [ 37%]\ntests/test_agents/test_pydantic_ai_agents.py::TestErrorCases::test_agent_run_exception PASSED [ 39%]\ntests/test_agents/test_pydantic_ai_agents.py::TestConcurrent::test_concurrent_execution_success PASSED [ 41%]\ntests/test_agents/test_pydantic_ai_agents.py::test_monitor_sales_performance_success FAILED [ 43%]\ntests/test_agents/test_pydantic_ai_agents.py::test_design_book_cover_success PASSED [ 44%]\ntests/test_agents/test_pydantic_ai_agents.py::test_research_topic_success FAILED [ 46%]\ntests/test_agents/test_pydantic_ai_agents.py::test_personalize_content_success FAILED [ 48%]\ntests/test_agents/test_pydantic_ai_agents.py::test_generate_multimodal_content_success FAILED [ 50%]\ntests/test_agents/test_pydantic_ai_agents.py::TestDependencyClasses::test_database_dependencies PASSED [ 51%]\ntests/test_agents/test_pydantic_ai_agents.py::TestDependencyClasses::test_ai_model_dependencies PASSED [ 53%]\ntests/test_agents/test_pydantic_ai_agents.py::TestDependencyClasses::test_scraping_dependencies PASSED [ 55%]\ntests/test_agents/test_pydantic_ai_agents.py::TestDependencyClasses::test_manuscript_dependencies PASSED [ 56%]\ntests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_empty_result PASSED [ 58%]\ntests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_database_error PASSED [ 60%]\ntests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_save_book_draft_success PASSED [ 62%]\ntests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_store_user_api_key_success PASSED [ 63%]\ntests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_api_key_from_supabase_success PASSED [ 65%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_bestsellers_success FAILED [ 67%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_no_api_key FAILED [ 68%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_success FAILED [ 70%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_no_credentials FAILED [ 72%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_analyze_competitor_books_success PASSED [ 74%]\ntests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_analyze_competitor_books_error_handling PASSED [ 75%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_success PASSED [ 77%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_with_criteria PASSED [ 79%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_empty_content PASSED [ 81%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_success PASSED [ 82%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_custom_min_freq PASSED [ 84%]\ntests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_short_content PASSED [ 86%]\ntests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_research_market_trends_success PASSED [ 87%]\ntests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_research_market_trends_error_handling PASSED [ 89%]\ntests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_validate_book_concept_success PASSED [ 91%]\ntests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_validate_book_concept_edge_cases PASSED [ 93%]\ntests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_network_timeout_handling FAILED [ 94%]\ntests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_invalid_input_handling PASSED [ 96%]\ntests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_large_data_processing PASSED [ 98%]\ntests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_concurrent_tool_execution PASSED [100%]\n\n=================================== FAILURES ===================================\n___________ TestPerformanceMetrics.test_single_agent_execution_time ____________\ntests/test_agents/test_performance_load.py:43: in test_single_agent_execution_time\n    assert result.status == ExecutionStatus.SUCCESS\nE   AssertionError: assert <ExecutionSta...RROR: 'error'> == <ExecutionSta...SS: 'success'>\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_trend_analyzer:pydantic_ai_trend_analyzer execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721394896'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_trend_analyzer.py\", line 166, in analyze_market_trends\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721394896'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_trend_analyzer:pydantic_ai_common.py:178 pydantic_ai_trend_analyzer execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721394896'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_trend_analyzer.py\", line 166, in analyze_market_trends\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721394896'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n_____________ TestScalabilityLimits.test_rapid_sequential_requests _____________\ntests/test_agents/test_performance_load.py:285: in test_rapid_sequential_requests\n    assert result.status == ExecutionStatus.SUCCESS\nE   AssertionError: assert <ExecutionSta...RROR: 'error'> == <ExecutionSta...SS: 'success'>\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_sales_monitor:pydantic_ai_sales_monitor execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721127184'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_sales_monitor.py\", line 118, in monitor_sales_performance\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721127184'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_sales_monitor:pydantic_ai_common.py:178 pydantic_ai_sales_monitor execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721127184'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_sales_monitor.py\", line 118, in monitor_sales_performance\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721127184'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n____________________ TestAgents.test_trend_analyzer_success ____________________\ntests/test_agents/test_pydantic_ai_agents.py:126: in test_trend_analyzer_success\n    assert result.status == ExecutionStatus.SUCCESS\nE   AssertionError: assert <ExecutionSta...RROR: 'error'> == <ExecutionSta...SS: 'success'>\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_trend_analyzer:pydantic_ai_trend_analyzer execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721052048'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_trend_analyzer.py\", line 166, in analyze_market_trends\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721052048'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_trend_analyzer:pydantic_ai_common.py:178 pydantic_ai_trend_analyzer execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721052048'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_trend_analyzer.py\", line 166, in analyze_market_trends\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721052048'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n____________________ TestAgents.test_upload_to_kdp_success _____________________\ntests/test_agents/test_pydantic_ai_agents.py:138: in test_upload_to_kdp_success\n    assert result.status == ExecutionStatus.SUCCESS\nE   AssertionError: assert <ExecutionSta...RROR: 'error'> == <ExecutionSta...SS: 'success'>\nE     \nE     - success\nE     + error\n____________________ test_monitor_sales_performance_success ____________________\ntests/test_agents/test_pydantic_ai_agents.py:188: in test_monitor_sales_performance_success\n    assert result.status.value == \"success\"\nE   AssertionError: assert 'error' == 'success'\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_sales_monitor:pydantic_ai_sales_monitor execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721021008'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_sales_monitor.py\", line 118, in monitor_sales_performance\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721021008'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_sales_monitor:pydantic_ai_common.py:178 pydantic_ai_sales_monitor execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721021008'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_sales_monitor.py\", line 118, in monitor_sales_performance\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721021008'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n_________________________ test_research_topic_success __________________________\ntests/test_agents/test_pydantic_ai_agents.py:221: in test_research_topic_success\n    assert result.status.value == \"success\"\nE   AssertionError: assert 'error' == 'success'\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_additional_agents:pydantic_ai_research_assistant execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721299792'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 148, in research_topic\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721299792'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_additional_agents:pydantic_ai_common.py:178 pydantic_ai_research_assistant execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721299792'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 148, in research_topic\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721299792'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n_______________________ test_personalize_content_success _______________________\ntests/test_agents/test_pydantic_ai_agents.py:239: in test_personalize_content_success\n    assert result.status.value == \"success\"\nE   AssertionError: assert 'error' == 'success'\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_additional_agents:pydantic_ai_personalization_engine execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721023376'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 195, in personalize_content\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721023376'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_additional_agents:pydantic_ai_common.py:178 pydantic_ai_personalization_engine execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721023376'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 195, in personalize_content\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4721023376'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n___________________ test_generate_multimodal_content_success ___________________\ntests/test_agents/test_pydantic_ai_agents.py:253: in test_generate_multimodal_content_success\n    assert result.status.value == \"success\"\nE   AssertionError: assert 'error' == 'success'\nE     \nE     - success\nE     + error\n----------------------------- Captured stderr call -----------------------------\nERROR:app.agents.pydantic_ai_additional_agents:pydantic_ai_multimodal_generator execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4720841296'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 242, in generate_multimodal_content\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4720841296'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n------------------------------ Captured log call -------------------------------\nERROR    app.agents.pydantic_ai_additional_agents:pydantic_ai_common.py:178 pydantic_ai_multimodal_generator execution failed: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4720841296'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\nTraceback (most recent call last):\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 175, in wrapper\n    return await func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_additional_agents.py\", line 242, in generate_multimodal_content\n    return create_success_result(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/agents/pydantic_ai_common.py\", line 99, in create_success_result\n    return AgentExecutionResult(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py\", line 253, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=<Mock name='run().metrics...l_time' id='4720841296'>, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type\n___________ TestScrapingTools.test_scrape_amazon_bestsellers_success ___________\n/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pytest_asyncio/plugin.py:508: in runtest\n    super().runtest()\n/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pytest_asyncio/plugin.py:773: in inner\n    _loop.run_until_complete(task)\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py:654: in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1392: in patched\n    with self.decoration_helper(patched,\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:137: in __enter__\n    return next(self.gen)\n           ^^^^^^^^^^^^^^\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1357: in decoration_helper\n    arg = exit_stack.enter_context(patching)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:517: in enter_context\n    result = _enter(cm)\n             ^^^^^^^^^^\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1446: in __enter__\n    original, local = self.get_original()\n                      ^^^^^^^^^^^^^^^^^^^\n/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1419: in get_original\n    raise AttributeError(\nE   AttributeError: <module 'app.models.supabase_models' from '/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/models/supabase_models.py'> does not have the attribute 'get_market_data_model'\n_______________ TestScrapingTools.test_scrape_amazon_no_api_key ________________\ntests/test_agents/test_pydantic_ai_tools.py:238: in test_scrape_amazon_no_api_key\n    assert len(result) == 0  # Should return empty list when no API key\n    ^^^^^^^^^^^^^^^^^^^^^^^\nE   AssertionError: assert 10 == 0\nE    +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])\n_____________ TestScrapingTools.test_scrape_reddit_trends_success ______________\ntests/test_agents/test_pydantic_ai_tools.py:272: in test_scrape_reddit_trends_success\n    assert 'keyword' in post\nE   AssertionError: assert 'keyword' in {'comments': 49, 'score': 162, 'subreddit': 'productivity', 'title': 'Top post in r/productivity', ...}\n__________ TestScrapingTools.test_scrape_reddit_trends_no_credentials __________\ntests/test_agents/test_pydantic_ai_tools.py:290: in test_scrape_reddit_trends_no_credentials\n    assert len(result) == 0  # Should return empty list when no credentials\n    ^^^^^^^^^^^^^^^^^^^^^^^\nE   AssertionError: assert 1 == 0\nE    +  where 1 = len([{'comments': 41, 'score': 158, 'subreddit': 'business', 'title': 'Top post in r/business', ...}])\n_________ TestErrorHandlingAndEdgeCases.test_network_timeout_handling __________\ntests/test_agents/test_pydantic_ai_tools.py:555: in test_network_timeout_handling\n    assert len(result) == 0  # No API key = empty results\n    ^^^^^^^^^^^^^^^^^^^^^^^\nE   AssertionError: assert 10 == 0\nE    +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])\n=============================== warnings summary ===============================\ntests/test_agents/test_pydantic_ai_agents.py::TestAgentRegistration::test_agent_registry_is_populated\n  tests/test_agents/test_pydantic_ai_agents.py:42: PytestWarning: The test <Function test_agent_registry_is_populated> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.\n    def test_agent_registry_is_populated(self):\n\ntests/test_agents/test_pydantic_ai_agents.py::TestAgentRegistration::test_registry_lookup\n  tests/test_agents/test_pydantic_ai_agents.py:47: PytestWarning: The test <Function test_registry_lookup> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.\n    def test_registry_lookup(self):\n\ntests/test_agents/test_pydantic_ai_agents.py::TestManager::test_agent_manager_ready\n  tests/test_agents/test_pydantic_ai_agents.py:62: PytestWarning: The test <Function test_agent_manager_ready> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.\n    def test_agent_manager_ready(self):\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n- generated xml file: /Volumes/Baby_SSD/github/AI projects/publish-ai/test-results/agent-mock-tests.xml -\n=========================== short test summary info ============================\nFAILED tests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_single_agent_execution_time\nFAILED tests/test_agents/test_performance_load.py::TestScalabilityLimits::test_rapid_sequential_requests\nFAILED tests/test_agents/test_pydantic_ai_agents.py::TestAgents::test_trend_analyzer_success\nFAILED tests/test_agents/test_pydantic_ai_agents.py::TestAgents::test_upload_to_kdp_success\nFAILED tests/test_agents/test_pydantic_ai_agents.py::test_monitor_sales_performance_success\nFAILED tests/test_agents/test_pydantic_ai_agents.py::test_research_topic_success\nFAILED tests/test_agents/test_pydantic_ai_agents.py::test_personalize_content_success\nFAILED tests/test_agents/test_pydantic_ai_agents.py::test_generate_multimodal_content_success\nFAILED tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_bestsellers_success\nFAILED tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_no_api_key\nFAILED tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_success\nFAILED tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_no_credentials\nFAILED tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_network_timeout_handling\n=========== 13 failed, 45 passed, 1 deselected, 3 warnings in 21.74s ===========", "stderr": ""}}