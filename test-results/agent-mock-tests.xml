<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="13" skipped="0" tests="58" time="21.742" timestamp="2025-06-27T16:36:06.015282-04:00" hostname="Marckensies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_single_agent_execution_time" time="0.093"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_performance_load.py:43: in test_single_agent_execution_time
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_concurrent_agent_performance" time="0.108" /><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_workflow_execution_performance" time="0.111" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_usage_single_execution" time="0.171" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_usage_multiple_executions" time="0.430" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_leak_detection" time="0.628" /><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_high_concurrency_limits" time="0.086" /><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_large_data_processing" time="0.086" /><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_rapid_sequential_requests" time="0.091"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_performance_load.py:285: in test_rapid_sequential_requests
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_performance_load.TestResourceUtilization" name="test_cpu_utilization" time="3.187" /><testcase classname="tests.test_agents.test_performance_load.TestResourceUtilization" name="test_thread_pool_efficiency" time="0.314" /><testcase classname="tests.test_agents.test_performance_load.TestStressTests" name="test_sustained_load" time="10.113" /><testcase classname="tests.test_agents.test_performance_load.TestStressTests" name="test_error_recovery_under_load" time="0.123" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAgentRegistration" name="test_agent_registry_is_populated" time="0.088" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAgentRegistration" name="test_registry_lookup" time="0.144" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManager" name="test_agent_manager_ready" time="0.095" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManager" name="test_get_status_structure" time="0.121" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestWorkflow" name="test_basic_workflow_success" time="0.186" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAgents" name="test_manuscript_generator_success" time="0.092" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAgents" name="test_trend_analyzer_success" time="0.087"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:126: in test_trend_analyzer_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAgents" name="test_upload_to_kdp_success" time="0.087"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:138: in test_upload_to_kdp_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorCases" name="test_missing_agent_fails" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorCases" name="test_agent_run_exception" time="0.096" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestConcurrent" name="test_concurrent_execution_success" time="0.091" /><testcase classname="tests.test_agents.test_pydantic_ai_agents" name="test_monitor_sales_performance_success" time="0.148"><failure message="AssertionError: assert 'error' == 'success'&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:188: in test_monitor_sales_performance_success
    assert result.status.value == "success"
E   AssertionError: assert 'error' == 'success'
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents" name="test_design_book_cover_success" time="0.090" /><testcase classname="tests.test_agents.test_pydantic_ai_agents" name="test_research_topic_success" time="0.092"><failure message="AssertionError: assert 'error' == 'success'&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:221: in test_research_topic_success
    assert result.status.value == "success"
E   AssertionError: assert 'error' == 'success'
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents" name="test_personalize_content_success" time="0.122"><failure message="AssertionError: assert 'error' == 'success'&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:239: in test_personalize_content_success
    assert result.status.value == "success"
E   AssertionError: assert 'error' == 'success'
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents" name="test_generate_multimodal_content_success" time="0.112"><failure message="AssertionError: assert 'error' == 'success'&#10;  &#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:253: in test_generate_multimodal_content_success
    assert result.status.value == "success"
E   AssertionError: assert 'error' == 'success'
E     
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDependencyClasses" name="test_database_dependencies" time="0.083" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDependencyClasses" name="test_ai_model_dependencies" time="0.086" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDependencyClasses" name="test_scraping_dependencies" time="0.084" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDependencyClasses" name="test_manuscript_dependencies" time="0.102" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_empty_result" time="0.092" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_database_error" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_save_book_draft_success" time="0.136" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_store_user_api_key_success" time="0.089" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_api_key_from_supabase_success" time="0.088" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_amazon_bestsellers_success" time="0.086"><failure message="AttributeError: &lt;module 'app.models.supabase_models' from '/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/models/supabase_models.py'&gt; does not have the attribute 'get_market_data_model'">/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pytest_asyncio/plugin.py:508: in runtest
    super().runtest()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pytest_asyncio/plugin.py:773: in inner
    _loop.run_until_complete(task)
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py:654: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1392: in patched
    with self.decoration_helper(patched,
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:137: in __enter__
    return next(self.gen)
           ^^^^^^^^^^^^^^
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1357: in decoration_helper
    arg = exit_stack.enter_context(patching)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:517: in enter_context
    result = _enter(cm)
             ^^^^^^^^^^
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1446: in __enter__
    original, local = self.get_original()
                      ^^^^^^^^^^^^^^^^^^^
/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:1419: in get_original
    raise AttributeError(
E   AttributeError: &lt;module 'app.models.supabase_models' from '/Volumes/Baby_SSD/github/AI projects/publish-ai/tests/../app/models/supabase_models.py'&gt; does not have the attribute 'get_market_data_model'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_amazon_no_api_key" time="0.085"><failure message="AssertionError: assert 10 == 0&#10; +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])">tests/test_agents/test_pydantic_ai_tools.py:238: in test_scrape_amazon_no_api_key
    assert len(result) == 0  # Should return empty list when no API key
    ^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: assert 10 == 0
E    +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_reddit_trends_success" time="0.088"><failure message="AssertionError: assert 'keyword' in {'comments': 49, 'score': 162, 'subreddit': 'productivity', 'title': 'Top post in r/productivity', ...}">tests/test_agents/test_pydantic_ai_tools.py:272: in test_scrape_reddit_trends_success
    assert 'keyword' in post
E   AssertionError: assert 'keyword' in {'comments': 49, 'score': 162, 'subreddit': 'productivity', 'title': 'Top post in r/productivity', ...}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_reddit_trends_no_credentials" time="0.087"><failure message="AssertionError: assert 1 == 0&#10; +  where 1 = len([{'comments': 41, 'score': 158, 'subreddit': 'business', 'title': 'Top post in r/business', ...}])">tests/test_agents/test_pydantic_ai_tools.py:290: in test_scrape_reddit_trends_no_credentials
    assert len(result) == 0  # Should return empty list when no credentials
    ^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: assert 1 == 0
E    +  where 1 = len([{'comments': 41, 'score': 158, 'subreddit': 'business', 'title': 'Top post in r/business', ...}])</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_analyze_competitor_books_success" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_analyze_competitor_books_error_handling" time="0.086" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_success" time="0.085" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_with_criteria" time="0.139" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_empty_content" time="0.084" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_success" time="0.104" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_custom_min_freq" time="0.086" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_short_content" time="0.106" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_research_market_trends_success" time="0.089" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_research_market_trends_error_handling" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_validate_book_concept_success" time="0.085" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_validate_book_concept_edge_cases" time="0.084" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_network_timeout_handling" time="0.085"><failure message="AssertionError: assert 10 == 0&#10; +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])">tests/test_agents/test_pydantic_ai_tools.py:555: in test_network_timeout_handling
    assert len(result) == 0  # No API key = empty results
    ^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: assert 10 == 0
E    +  where 10 = len([{'author': 'Author 1', 'price': 2.99, 'rating': 4.0, 'reviews': 50, ...}, {'author': 'Author 2', 'price': 3.99, 'rating': 4.1, 'reviews': 60, ...}, {'author': 'Author 3', 'price': 4.99, 'rating': 4.2, 'reviews': 70, ...}, {'author': 'Author 4', 'price': 5.99, 'rating': 4.3, 'reviews': 80, ...}, {'author': 'Author 5', 'price': 6.99, 'rating': 4.4, 'reviews': 90, ...}, {'author': 'Author 6', 'price': 7.99, 'rating': 4.0, 'reviews': 100, ...}, ...])</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_invalid_input_handling" time="0.083" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_large_data_processing" time="0.090" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_concurrent_tool_execution" time="0.139" /></testsuite></testsuites>