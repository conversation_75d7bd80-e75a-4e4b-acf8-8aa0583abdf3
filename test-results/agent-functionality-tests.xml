<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="13" skipped="0" tests="38" time="8.641" timestamp="2025-06-27T00:01:25.201568" hostname="Marckensies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_agent_registry_initialization" time="0.123" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_all_agents_registered" time="0.118" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_dependency_classes" time="0.109" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_manager_initialization" time="0.105" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_get_agent_status" time="0.099" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_execute_agent_manuscript_generator" time="0.103" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_execute_workflow" time="0.103" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_generate_manuscript_success" time="0.153" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_generate_manuscript_with_custom_params" time="0.110" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_manuscript_generation_error_handling" time="0.112"><failure message="AssertionError: assert &lt;ExecutionStatus.ERROR: 'error'&gt; != &lt;ExecutionStatus.ERROR: 'error'&gt;&#10; +  where &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='pydantic_ai_manuscript_generator', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='AI model error', execution_time=0.008513, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 28, 119856), metadata={}, warnings=[]).status&#10; +  and   &lt;ExecutionStatus.ERROR: 'error'&gt; = ExecutionStatus.ERROR">tests/test_agents/test_pydantic_ai_agents.py:281: in test_manuscript_generation_error_handling
    assert result.status != ExecutionStatus.ERROR
E   AssertionError: assert &lt;ExecutionStatus.ERROR: 'error'&gt; != &lt;ExecutionStatus.ERROR: 'error'&gt;
E    +  where &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='pydantic_ai_manuscript_generator', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='AI model error', execution_time=0.008513, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 28, 119856), metadata={}, warnings=[]).status
E    +  and   &lt;ExecutionStatus.ERROR: 'error'&gt; = ExecutionStatus.ERROR</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_analyze_market_trends_success" time="0.113"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:316: in test_analyze_market_trends_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_trend_analysis_with_depth" time="0.109"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:336: in test_trend_analysis_with_depth
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_trend_analysis_error_handling" time="0.169" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestSalesMonitor" name="test_monitor_sales_performance_success" time="0.114"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:387: in test_monitor_sales_performance_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestSalesMonitor" name="test_sales_monitoring_custom_params" time="0.147"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:408: in test_sales_monitoring_custom_params
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestCoverDesigner" name="test_design_book_cover_success" time="0.108" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestKDPUploader" name="test_upload_to_kdp_success" time="0.102"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:500: in test_upload_to_kdp_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_research_assistant_success" time="0.108"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:535: in test_research_assistant_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_personalization_engine_success" time="0.108"><failure message="assert None is not None&#10; +  where None = AgentExecutionResult(agent_name='pydantic_ai_personalization_engine', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message=&quot;1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=&lt;Mock name='run().metrics...l_time' id='4663067856'&gt;, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type&quot;, execution_time=0.003531, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 29, 697868), metadata={}, warnings=[]).data">tests/test_agents/test_pydantic_ai_agents.py:569: in test_personalization_engine_success
    assert result.data is not None
E   assert None is not None
E    +  where None = AgentExecutionResult(agent_name='pydantic_ai_personalization_engine', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message="1 validation error for AgentExecutionResult\nexecution_time\n  Input should be a valid number [type=float_type, input_value=&lt;Mock name='run().metrics...l_time' id='4663067856'&gt;, input_type=Mock]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_type", execution_time=0.003531, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 29, 697868), metadata={}, warnings=[]).data</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_multimodal_generator_success" time="0.152"><failure message="AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;&#10;  - success&#10;  + error">tests/test_agents/test_pydantic_ai_agents.py:597: in test_multimodal_generator_success
    assert result.status == ExecutionStatus.SUCCESS
E   AssertionError: assert &lt;ExecutionSta...RROR: 'error'&gt; == &lt;ExecutionSta...SS: 'success'&gt;
E     - success
E     + error</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_agent_execution_timeout" time="0.106" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_invalid_agent_name" time="0.121"><failure message="AssertionError: assert ('unknown agent' in &lt;ExecutionStatus.ERROR: 'error'&gt; or 'not found' in &lt;ExecutionStatus.ERROR: 'error'&gt;)&#10; +  where &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='nonexistent_agent', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='Unknown agent: nonexistent_agent', execution_time=9e-06, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 30, 99483), metadata={}, warnings=[]).status&#10; +  and   &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='nonexistent_agent', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='Unknown agent: nonexistent_agent', execution_time=9e-06, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 30, 99483), metadata={}, warnings=[]).status">tests/test_agents/test_pydantic_ai_agents.py:632: in test_invalid_agent_name
    assert 'unknown agent' in result.status == ExecutionStatus.ERROR.lower() or 'not found' in result.status == ExecutionStatus.ERROR.lower()
E   AssertionError: assert ('unknown agent' in &lt;ExecutionStatus.ERROR: 'error'&gt; or 'not found' in &lt;ExecutionStatus.ERROR: 'error'&gt;)
E    +  where &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='nonexistent_agent', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='Unknown agent: nonexistent_agent', execution_time=9e-06, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 30, 99483), metadata={}, warnings=[]).status
E    +  and   &lt;ExecutionStatus.ERROR: 'error'&gt; = AgentExecutionResult(agent_name='nonexistent_agent', status=&lt;ExecutionStatus.ERROR: 'error'&gt;, data=None, error_message='Unknown agent: nonexistent_agent', execution_time=9e-06, timestamp=datetime.datetime(2025, 6, 27, 0, 1, 30, 99483), metadata={}, warnings=[]).status</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_malformed_workflow" time="0.894" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_full_book_creation_workflow" time="0.135" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_agent_registry_consistency" time="0.132" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPerformanceAndScaling" name="test_memory_usage_optimization" time="0.114" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_input_sanitization" time="0.113"><failure message="TypeError: upload_to_kdp() got an unexpected keyword argument 'title'">tests/test_agents/test_pydantic_ai_agents.py:776: in test_input_sanitization
    result = await upload_to_kdp(**malicious_input)
E   TypeError: upload_to_kdp() got an unexpected keyword argument 'title'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_data_type_validation" time="0.130" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_empty_input_handling" time="0.323" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestConfigurationAndSettings" name="test_agent_configuration" time="0.115"><failure message="AssertionError: assert False&#10; +  where False = hasattr(&lt;function with_error_handling.&lt;locals&gt;.decorator.&lt;locals&gt;.wrapper at 0x10d3847c0&gt;, 'model')">tests/test_agents/test_pydantic_ai_agents.py:830: in test_agent_configuration
    assert hasattr(agent, 'model')
E   AssertionError: assert False
E    +  where False = hasattr(&lt;function with_error_handling.&lt;locals&gt;.decorator.&lt;locals&gt;.wrapper at 0x10d3847c0&gt;, 'model')</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestConfigurationAndSettings" name="test_dependency_injection" time="0.131" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestMonitoringAndLogging" name="test_execution_tracking" time="0.134" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestMonitoringAndLogging" name="test_error_logging" time="0.109" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIEnhancements" name="test_registry_based_execution" time="0.111"><failure message="TypeError: object Mock can't be used in 'await' expression">tests/test_agents/test_pydantic_ai_agents.py:892: in test_registry_based_execution
    result = await agent.run("What are AI trends?") # Use the mock agent
E   TypeError: object Mock can't be used in 'await' expression</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIEnhancements" name="test_missing_required_fields" time="0.248" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIEnhancements" name="test_execution_time_check" time="0.264" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIEnhancements" name="test_agent_deregistration" time="0.115" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIEnhancements" name="test_logs_for_error" time="0.134" /></testsuite></testsuites>