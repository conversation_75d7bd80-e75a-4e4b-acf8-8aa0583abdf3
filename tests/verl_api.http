# VERL API Tests for VS Code REST Client Extension
# Save this as: tests/verl_api.http
# Use with REST Client extension (humao.rest-client)

### Variables
@baseUrl = http://localhost:8000
@verlUrl = http://localhost:8001

### Test Basic Health Check
GET {{baseUrl}}/health
Accept: application/json

### Test Enhanced Health with VERL Status
GET {{baseUrl}}/health
Accept: application/json

###

### Test VERL Service Health
GET {{baseUrl}}/api/verl/health
Accept: application/json

### Test Direct VERL Service (if running in Docker)
GET {{verlUrl}}/health
Accept: application/json

###

### Test VERL Statistics
GET {{baseUrl}}/api/verl/stats
Accept: application/json

###

### Test Existing Endpoints (Should Still Work)
GET {{baseUrl}}/api/books
Accept: application/json

###

### Test Trends Endpoint
GET {{baseUrl}}/api/trends
Accept: application/json

###

### Test Analytics Endpoint  
GET {{baseUrl}}/api/analytics
Accept: application/json

###

### Test Publications Endpoint
GET {{baseUrl}}/api/publications
Accept: application/json

###

### Test Root Endpoint (Enhanced with VERL Status)
GET {{baseUrl}}/
Accept: application/json

###

### Test VERL Training Status (Replace with actual training ID)
@trainingId = test-training-123
GET {{baseUrl}}/api/verl/training/{{trainingId}}
Accept: application/json

###

### Test Manual VERL Training Trigger
POST {{baseUrl}}/api/verl/trigger-training
Content-Type: application/json

{
  "note": "Manual trigger test"
}

###

### Test Book Generation (Your Existing Functionality)
POST {{baseUrl}}/api/books/generate
Content-Type: application/json

{
  "topic": "AI and Machine Learning",
  "category": "Technology",
  "target_length": 10000
}

###

### Test User Authentication (If You Have It)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "testpassword"
}

###

### Test with Authentication Token (If Needed)
@authToken = {{login.response.body.access_token}}
GET {{baseUrl}}/api/books
Authorization: Bearer {{authToken}}
Accept: application/json

###

### Test Configuration Validation
GET {{baseUrl}}/api/system/config
Accept: application/json

###

### Test Docker Compose Services Health
GET {{baseUrl}}/api/system/services
Accept: application/json

###

### Stress Test - Multiple Health Checks
GET {{baseUrl}}/health

###

GET {{baseUrl}}/api/verl/health

###

GET {{baseUrl}}/api/verl/stats

###

# Development Notes:
# 1. Install REST Client extension: humao.rest-client
# 2. Click "Send Request" above any ### line
# 3. View responses in side panel
# 4. Save responses for testing
# 5. Use variables for different environments

# Environment Variables (Add to .vscode/settings.json):
# "rest-client.environmentVariables": {
#   "local": {
#     "baseUrl": "http://localhost:8000",
#     "verlUrl": "http://localhost:8001"
#   },
#   "docker": {
#     "baseUrl": "http://localhost:8000", 
#     "verlUrl": "http://localhost:8001"
#   },
#   "production": {
#     "baseUrl": "https://your-production-domain.com",
#     "verlUrl": "https://verl.your-production-domain.com"
#   }
# }