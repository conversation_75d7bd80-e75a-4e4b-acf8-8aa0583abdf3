# tests/test_imports.py - Test that all critical imports work

import pytest


def test_api_imports():
    """Test that all API modules can be imported"""
    from app.api import (
        supabase_auth,
        supabase_books,
        trends,
        publications,
        monitoring,
        predictions,
        analytics,
        feedback,
        agents
    )
    
    # Verify routers exist
    assert hasattr(supabase_auth, 'router')
    assert hasattr(supabase_books, 'router')
    assert hasattr(trends, 'router')
    assert hasattr(publications, 'router')
    assert hasattr(monitoring, 'router')
    assert hasattr(predictions, 'router')
    assert hasattr(analytics, 'router')
    assert hasattr(feedback, 'router')
    assert hasattr(agents, 'router')


def test_model_imports():
    """Test that all model functions can be imported"""
    from app.models.supabase_models import (
        get_user_model,
        get_book_model,
        get_feedback_model,
        get_model_performance_model,
        User,
        Book
    )
    
    # Basic import test - if we got here, imports worked
    assert callable(get_user_model)
    assert callable(get_book_model)
    assert callable(get_feedback_model)
    assert callable(get_model_performance_model)


def test_schema_imports():
    """Test that schema imports work"""
    from app.schemas.book import Manuscript, Chapter, BookOutline
    from app.schemas.user import UserCreate, UserResponse
    
    # Basic validation that classes exist
    assert Manuscript is not None
    assert Chapter is not None
    assert BookOutline is not None


def test_agent_imports():
    """Test that agent modules can be imported"""
    from app.agents import (
        pydantic_ai_base,
        pydantic_ai_common,
        pydantic_ai_manager
    )
    
    # Verify key classes exist
    assert hasattr(pydantic_ai_base, 'DatabaseDependencies')
    assert hasattr(pydantic_ai_common, 'AgentExecutionResult')


def test_fixed_module_imports():
    """Test that recently fixed modules can be imported"""
    from app.translation.auto_translator import IntelligentTranslator
    from app.publishing.multi_platform import MultiPlatformPublisher
    from app.pricing.dynamic_pricing import DynamicPricingEngine
    from app.education.course_creator import CourseCreator
    from app.content.interactive_generator import InteractiveContentGenerator
    from app.audio.audiobook_generator import AudiobookGenerator
    
    # All classes should be importable
    assert IntelligentTranslator is not None
    assert MultiPlatformPublisher is not None
    assert DynamicPricingEngine is not None
    assert CourseCreator is not None
    assert InteractiveContentGenerator is not None
    assert AudiobookGenerator is not None


def test_config_import():
    """Test that config imports and validates"""
    from app.config import settings, get_settings
    
    assert settings is not None
    assert callable(get_settings)
    
    # Test validation method exists
    validation = settings.validate_configuration()
    assert isinstance(validation, dict)
    assert 'valid' in validation
    assert 'issues' in validation
    assert 'warnings' in validation