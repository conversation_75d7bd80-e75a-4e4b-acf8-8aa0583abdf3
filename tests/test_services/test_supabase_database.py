# test_supabase_database.py - Unit tests for SupabaseDatabase

import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from app.utils.supabase.supabase_database import SupabaseDatabase


@pytest.fixture
def mock_settings(monkeypatch):
    monkeypatch.setenv("SUPABASE_URL", "https://test.supabase.co")
    monkeypatch.setenv("SUPABASE_SERVICE_KEY", "test-key")
    monkeypatch.setenv("DATABASE_URL", "***********************************")


@patch("app.database.supabase_database.create_client")
def test_initialize_success(mock_create):
    db = SupabaseDatabase()
    assert db.get_client() is not None


@patch("app.database.supabase_database.asyncpg.create_pool", new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_initialize_pool_success(mock_pool):
    db = SupabaseDatabase()
    await db.initialize_pool()
    assert db.db_pool is not None


@patch("app.database.supabase_database.create_client")
@patch("app.database.supabase_database.SupabaseDatabase.validate_schema")
@pytest.mark.asyncio
async def test_init_database(mock_validate, mock_create):
    from app.utils.supabase.supabase_database import init_database

    mock_validate.return_value = {"schema_valid": True}
    assert await init_database() is True


@patch("app.database.supabase_database.SupabaseDatabase.validate_schema")
@pytest.mark.asyncio
async def test_schema_validation_logic(mock_validate):
    db = SupabaseDatabase()
    db.client = MagicMock()
    db.client.table.return_value.select.return_value.limit.return_value.execute.return_value = MagicMock(
        count=1
    )
    result = await db.validate_schema()
    assert result["schema_valid"] in [True, False]


@patch("app.database.supabase_database.SupabaseDatabase.get_connection")
@pytest.mark.asyncio
async def test_health_check(mock_get_conn):
    db = SupabaseDatabase()
    db.client = MagicMock()
    db.client.table.return_value.select.return_value.limit.return_value.execute.return_value = MagicMock(
        count=1
    )
    mock_get_conn.return_value.__aenter__.return_value.fetchval = AsyncMock(
        return_value=1
    )
    result = await db.health_check()
    assert result["status"] in ["healthy", "unhealthy"]
