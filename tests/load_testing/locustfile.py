"""
Comprehensive Load Testing for Publish AI Platform.

This module contains production-grade load testing scenarios that simulate
realistic user behavior and system usage patterns.
"""

import json
import random
import time
import uuid
from typing import Dict, Any

from locust import HttpUser, task, between, events
from locust.exception import RescheduleTask
from faker import Faker

fake = Faker()


class AuthenticatedUser(HttpUser):
    """Base user class with authentication capabilities."""
    
    wait_time = between(1, 5)  # Wait 1-5 seconds between requests
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.auth_token = None
        self.user_id = None
        self.user_email = None
        self.user_password = "LoadTest123!"
        
    def on_start(self):
        """Called when a user starts. Handles authentication."""
        self.register_and_login()
    
    def register_and_login(self):
        """Register a new user and login to get auth token."""
        # Generate unique user data
        self.user_email = f"loadtest_{uuid.uuid4().hex[:8]}@example.com"
        
        # Register user
        registration_data = {
            "email": self.user_email,
            "password": self.user_password,
            "name": fake.name()
        }
        
        with self.client.post(
            "/api/auth/register",
            json=registration_data,
            catch_response=True,
            name="auth_register"
        ) as response:
            if response.status_code == 201:
                response.success()
                user_data = response.json()
                self.user_id = user_data.get("user", {}).get("id")
            elif response.status_code == 400 and "already exists" in response.text:
                # User exists, just login
                response.success()
            else:
                response.failure(f"Registration failed: {response.status_code}")
        
        # Login to get token
        login_data = {
            "email": self.user_email,
            "password": self.user_password
        }
        
        with self.client.post(
            "/api/auth/login",
            json=login_data,
            catch_response=True,
            name="auth_login"
        ) as response:
            if response.status_code == 200:
                response.success()
                auth_data = response.json()
                self.auth_token = auth_data.get("access_token")
                if not self.user_id:
                    self.user_id = auth_data.get("user", {}).get("id")
            else:
                response.failure(f"Login failed: {response.status_code}")
                raise RescheduleTask()
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers."""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}


class RegularUser(AuthenticatedUser):
    """Regular user performing typical platform operations."""
    
    weight = 70  # 70% of users are regular users
    
    @task(3)
    def view_dashboard(self):
        """User views their dashboard."""
        headers = self.get_auth_headers()
        with self.client.get("/api/books", headers=headers, name="view_dashboard") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Dashboard view failed: {response.status_code}")
    
    @task(2)
    def create_book(self):
        """User creates a new book."""
        headers = self.get_auth_headers()
        book_data = {
            "title": fake.catch_phrase(),
            "description": fake.text(max_nb_chars=200),
            "genre": random.choice(["fiction", "non-fiction", "mystery", "romance", "sci-fi"]),
            "target_audience": random.choice(["adults", "young_adults", "children"]),
            "content_type": "standard"
        }
        
        with self.client.post(
            "/api/books",
            json=book_data,
            headers=headers,
            name="create_book"
        ) as response:
            if response.status_code == 201:
                response.success()
                return response.json().get("id")
            else:
                response.failure(f"Book creation failed: {response.status_code}")
                return None
    
    @task(2)
    def generate_manuscript(self):
        """User generates manuscript content."""
        book_id = self.create_book()
        if not book_id:
            return
        
        headers = self.get_auth_headers()
        generation_request = {
            "style": random.choice(["narrative", "instructional", "conversational"]),
            "length": random.choice(["short", "medium", "long"]),
            "outline_only": random.choice([True, False])
        }
        
        with self.client.post(
            f"/api/books/{book_id}/generate",
            json=generation_request,
            headers=headers,
            name="generate_manuscript"
        ) as response:
            if response.status_code in [200, 202]:  # Accept both sync and async responses
                response.success()
            else:
                response.failure(f"Manuscript generation failed: {response.status_code}")
    
    @task(1)
    def analyze_trends(self):
        """User analyzes market trends."""
        headers = self.get_auth_headers()
        
        with self.client.get("/api/trends", headers=headers, name="analyze_trends") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Trend analysis failed: {response.status_code}")
    
    @task(1)
    def view_analytics(self):
        """User views their analytics."""
        headers = self.get_auth_headers()
        
        with self.client.get("/api/analytics/user", headers=headers, name="view_analytics") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Analytics view failed: {response.status_code}")


class PowerUser(AuthenticatedUser):
    """Power user performing advanced operations."""
    
    weight = 20  # 20% of users are power users
    
    @task(2)
    def bulk_operations(self):
        """Power user performs bulk operations."""
        headers = self.get_auth_headers()
        
        # Create multiple books
        book_ids = []
        for _ in range(3):
            book_data = {
                "title": fake.catch_phrase(),
                "description": fake.text(max_nb_chars=200),
                "genre": random.choice(["fiction", "non-fiction", "mystery", "romance", "sci-fi"]),
                "target_audience": random.choice(["adults", "young_adults", "children"])
            }
            
            with self.client.post(
                "/api/books",
                json=book_data,
                headers=headers,
                name="bulk_create_books"
            ) as response:
                if response.status_code == 201:
                    book_ids.append(response.json().get("id"))
        
        # Generate manuscripts for all books
        for book_id in book_ids:
            if book_id:
                with self.client.post(
                    f"/api/books/{book_id}/generate",
                    json={"style": "narrative", "length": "medium"},
                    headers=headers,
                    name="bulk_generate_manuscripts"
                ) as response:
                    if response.status_code not in [200, 202]:
                        response.failure(f"Bulk generation failed: {response.status_code}")
    
    @task(2)
    def advanced_analytics(self):
        """Power user accesses advanced analytics."""
        headers = self.get_auth_headers()
        
        # Get comprehensive analytics
        endpoints = [
            "/api/analytics/user",
            "/api/analytics/books/performance",
            "/api/analytics/trends",
            "/api/predictions/sales"
        ]
        
        for endpoint in endpoints:
            with self.client.get(endpoint, headers=headers, name="advanced_analytics") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Advanced analytics failed: {response.status_code}")
    
    @task(1)
    def agent_workflows(self):
        """Power user executes complex agent workflows."""
        headers = self.get_auth_headers()
        
        # Execute trend analyzer
        workflow_data = {
            "agent_type": "trend_analyzer",
            "parameters": {
                "timeframe": "7d",
                "categories": ["fiction", "non-fiction"],
                "depth": "comprehensive"
            }
        }
        
        with self.client.post(
            "/api/agents/execute",
            json=workflow_data,
            headers=headers,
            name="agent_workflows"
        ) as response:
            if response.status_code in [200, 202]:
                response.success()
            else:
                response.failure(f"Agent workflow failed: {response.status_code}")


class AdminUser(AuthenticatedUser):
    """Admin user performing administrative operations."""
    
    weight = 10  # 10% of users are admin users
    
    def on_start(self):
        """Admin users need special authentication."""
        # For load testing, we'll simulate admin operations without actual admin privileges
        super().on_start()
    
    @task(2)
    def monitoring_operations(self):
        """Admin performs monitoring operations."""
        headers = self.get_auth_headers()
        
        endpoints = [
            "/health",
            "/api/monitoring/health",
            "/api/monitoring/performance"
        ]
        
        for endpoint in endpoints:
            with self.client.get(endpoint, headers=headers, name="admin_monitoring") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Monitoring failed: {response.status_code}")
    
    @task(1)
    def system_analytics(self):
        """Admin views system-wide analytics."""
        headers = self.get_auth_headers()
        
        with self.client.get("/api/analytics/system", headers=headers, name="system_analytics") as response:
            if response.status_code in [200, 403]:  # 403 is acceptable for non-admin users
                response.success()
            else:
                response.failure(f"System analytics failed: {response.status_code}")


class APIOnlyUser(HttpUser):
    """User that only makes API calls without authentication (public endpoints)."""
    
    weight = 5  # 5% of traffic is unauthenticated
    wait_time = between(0.5, 2)
    
    @task(3)
    def health_checks(self):
        """Check public health endpoints."""
        with self.client.get("/health", name="public_health") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(2)
    def public_endpoints(self):
        """Access public API endpoints."""
        public_endpoints = [
            "/",
            "/api/setup/validate"
        ]
        
        endpoint = random.choice(public_endpoints)
        with self.client.get(endpoint, name="public_endpoints") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Public endpoint failed: {response.status_code}")


# Custom event handlers for enhanced metrics
@events.request.add_listener
def record_custom_metrics(request_type, name, response_time, response_length, response, context, exception, **kwargs):
    """Record custom performance metrics."""
    if hasattr(response, 'status_code'):
        # Log slow requests
        if response_time > 5000:  # 5 seconds
            print(f"SLOW REQUEST: {name} took {response_time}ms")
        
        # Log errors
        if response.status_code >= 400:
            print(f"ERROR: {name} returned {response.status_code}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Initialize test environment."""
    print("🚀 Starting Publish AI Load Test")
    print(f"Target host: {environment.host}")
    print(f"User classes: {[cls.__name__ for cls in environment.user_classes]}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Clean up after test completion."""
    print("🏁 Load test completed")
    
    # Calculate custom metrics
    stats = environment.stats
    total_requests = stats.total.num_requests
    total_failures = stats.total.num_failures
    
    if total_requests > 0:
        failure_rate = (total_failures / total_requests) * 100
        print(f"📊 Test Results:")
        print(f"  Total Requests: {total_requests}")
        print(f"  Total Failures: {total_failures}")
        print(f"  Failure Rate: {failure_rate:.2f}%")
        print(f"  Average Response Time: {stats.total.avg_response_time:.2f}ms")
        print(f"  Max Response Time: {stats.total.max_response_time}ms")
        
        # Set exit code based on failure rate
        if failure_rate > 5:  # More than 5% failure rate
            environment.process_exit_code = 1