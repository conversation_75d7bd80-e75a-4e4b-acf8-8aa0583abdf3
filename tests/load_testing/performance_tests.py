"""
Performance Testing Scenarios for Publish AI Platform.

This module provides specific performance test scenarios with custom
measurement and validation capabilities.
"""

import asyncio
import json
import time
import statistics
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

import httpx
import pytest
from faker import Faker

fake = Faker()


@dataclass
class PerformanceMetrics:
    """Performance metrics container."""
    response_times: List[float]
    success_count: int
    failure_count: int
    throughput: float
    error_rate: float
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float


class PerformanceTestRunner:
    """Advanced performance test runner with custom metrics."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token = None
    
    async def authenticate(self) -> bool:
        """Authenticate and get auth token."""
        try:
            # Register test user
            user_email = f"perftest_{int(time.time())}@example.com"
            registration_data = {
                "email": user_email,
                "password": "PerfTest123!",
                "name": "Performance Test User"
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/auth/register",
                json=registration_data
            )
            
            if response.status_code not in [201, 400]:  # 400 if user exists
                return False
            
            # Login to get token
            login_data = {
                "email": user_email,
                "password": "PerfTest123!"
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/auth/login",
                json=login_data
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                self.auth_token = auth_data.get("access_token")
                return True
            
            return False
            
        except Exception as e:
            print(f"Authentication failed: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers."""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    async def measure_endpoint_performance(
        self,
        endpoint: str,
        method: str = "GET",
        data: Dict = None,
        concurrent_requests: int = 10,
        total_requests: int = 100
    ) -> PerformanceMetrics:
        """Measure performance of a specific endpoint."""
        
        response_times = []
        success_count = 0
        failure_count = 0
        
        headers = self.get_auth_headers()
        
        async def make_request():
            start_time = time.time()
            try:
                if method.upper() == "GET":
                    response = await self.client.get(f"{self.base_url}{endpoint}", headers=headers)
                elif method.upper() == "POST":
                    response = await self.client.post(f"{self.base_url}{endpoint}", json=data, headers=headers)
                else:
                    response = await self.client.request(method, f"{self.base_url}{endpoint}", json=data, headers=headers)
                
                response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                response_times.append(response_time)
                
                if response.status_code < 400:
                    return "success"
                else:
                    return "failure"
                    
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                response_times.append(response_time)
                return "failure"
        
        # Execute requests in batches with concurrency control
        start_time = time.time()
        
        for batch_start in range(0, total_requests, concurrent_requests):
            batch_size = min(concurrent_requests, total_requests - batch_start)
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def limited_request():
                async with semaphore:
                    return await make_request()
            
            # Execute batch
            tasks = [limited_request() for _ in range(batch_size)]
            results = await asyncio.gather(*tasks)
            
            # Count results
            for result in results:
                if result == "success":
                    success_count += 1
                else:
                    failure_count += 1
        
        total_time = time.time() - start_time
        
        # Calculate metrics
        if response_times:
            avg_response_time = statistics.mean(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        throughput = total_requests / total_time if total_time > 0 else 0
        error_rate = (failure_count / total_requests) * 100 if total_requests > 0 else 0
        
        return PerformanceMetrics(
            response_times=response_times,
            success_count=success_count,
            failure_count=failure_count,
            throughput=throughput,
            error_rate=error_rate,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time
        )
    
    async def stress_test_auth_endpoints(self) -> Dict[str, PerformanceMetrics]:
        """Stress test authentication endpoints."""
        results = {}
        
        # Test registration endpoint
        registration_data = {
            "email": f"stress_{int(time.time())}@example.com",
            "password": "StressTest123!",
            "name": "Stress Test User"
        }
        
        results["registration"] = await self.measure_endpoint_performance(
            "/api/auth/register",
            method="POST",
            data=registration_data,
            concurrent_requests=5,  # Lower concurrency for registration
            total_requests=50
        )
        
        # Test login endpoint
        login_data = {
            "email": registration_data["email"],
            "password": registration_data["password"]
        }
        
        results["login"] = await self.measure_endpoint_performance(
            "/api/auth/login",
            method="POST",
            data=login_data,
            concurrent_requests=10,
            total_requests=100
        )
        
        return results
    
    async def stress_test_api_endpoints(self) -> Dict[str, PerformanceMetrics]:
        """Stress test main API endpoints."""
        if not await self.authenticate():
            raise Exception("Failed to authenticate for API stress test")
        
        results = {}
        
        # Test health endpoint
        results["health"] = await self.measure_endpoint_performance(
            "/health",
            concurrent_requests=20,
            total_requests=200
        )
        
        # Test books list endpoint
        results["books_list"] = await self.measure_endpoint_performance(
            "/api/books",
            concurrent_requests=15,
            total_requests=150
        )
        
        # Test book creation
        book_data = {
            "title": fake.catch_phrase(),
            "description": fake.text(max_nb_chars=200),
            "genre": "fiction",
            "target_audience": "adults"
        }
        
        results["book_creation"] = await self.measure_endpoint_performance(
            "/api/books",
            method="POST",
            data=book_data,
            concurrent_requests=5,  # Lower concurrency for creation
            total_requests=25
        )
        
        # Test analytics endpoint
        results["analytics"] = await self.measure_endpoint_performance(
            "/api/analytics/user",
            concurrent_requests=10,
            total_requests=100
        )
        
        return results
    
    async def memory_leak_test(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """Test for memory leaks over extended period."""
        if not await self.authenticate():
            raise Exception("Failed to authenticate for memory leak test")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        response_times = []
        memory_samples = []
        request_count = 0
        
        while time.time() < end_time:
            # Make requests continuously
            start_request = time.time()
            try:
                response = await self.client.get(
                    f"{self.base_url}/api/books",
                    headers=self.get_auth_headers()
                )
                response_time = (time.time() - start_request) * 1000
                response_times.append(response_time)
                request_count += 1
                
                # Sample every 100 requests
                if request_count % 100 == 0:
                    memory_samples.append({
                        "timestamp": datetime.utcnow().isoformat(),
                        "request_count": request_count,
                        "avg_response_time": statistics.mean(response_times[-100:]),
                        "response_time": response_time
                    })
                
            except Exception as e:
                print(f"Request failed during memory leak test: {e}")
            
            # Small delay between requests
            await asyncio.sleep(0.1)
        
        # Analyze for memory leak indicators
        if len(memory_samples) >= 2:
            initial_avg = statistics.mean([s["avg_response_time"] for s in memory_samples[:5]])
            final_avg = statistics.mean([s["avg_response_time"] for s in memory_samples[-5:]])
            degradation_ratio = final_avg / initial_avg if initial_avg > 0 else 1
        else:
            degradation_ratio = 1
        
        return {
            "duration_minutes": duration_minutes,
            "total_requests": request_count,
            "memory_samples": memory_samples,
            "performance_degradation_ratio": degradation_ratio,
            "avg_response_time": statistics.mean(response_times) if response_times else 0,
            "potential_memory_leak": degradation_ratio > 1.5  # 50% degradation indicates potential leak
        }
    
    async def close(self):
        """Clean up resources."""
        await self.client.aclose()


# Pytest integration
@pytest.mark.asyncio
async def test_endpoint_performance_thresholds():
    """Test that endpoints meet performance thresholds."""
    runner = PerformanceTestRunner()
    
    try:
        # Authenticate
        assert await runner.authenticate(), "Failed to authenticate"
        
        # Test critical endpoints
        critical_endpoints = [
            ("/health", "GET", None, 500),  # Max 500ms for health check
            ("/api/books", "GET", None, 1000),  # Max 1s for books list
        ]
        
        for endpoint, method, data, threshold_ms in critical_endpoints:
            metrics = await runner.measure_endpoint_performance(
                endpoint, method, data, concurrent_requests=5, total_requests=20
            )
            
            # Assert performance thresholds
            assert metrics.avg_response_time < threshold_ms, \
                f"{endpoint} avg response time {metrics.avg_response_time:.2f}ms exceeds threshold {threshold_ms}ms"
            
            assert metrics.error_rate < 5, \
                f"{endpoint} error rate {metrics.error_rate:.2f}% exceeds 5%"
            
            assert metrics.p95_response_time < (threshold_ms * 2), \
                f"{endpoint} P95 response time {metrics.p95_response_time:.2f}ms exceeds threshold"
    
    finally:
        await runner.close()


@pytest.mark.asyncio
async def test_concurrent_load_handling():
    """Test system behavior under concurrent load."""
    runner = PerformanceTestRunner()
    
    try:
        assert await runner.authenticate(), "Failed to authenticate"
        
        # Test with high concurrency
        metrics = await runner.measure_endpoint_performance(
            "/health",
            concurrent_requests=50,
            total_requests=500
        )
        
        # Assert system stability under load
        assert metrics.error_rate < 10, f"Error rate {metrics.error_rate:.2f}% too high under load"
        assert metrics.throughput > 10, f"Throughput {metrics.throughput:.2f} req/s too low"
        
    finally:
        await runner.close()


if __name__ == "__main__":
    # Run performance tests manually
    async def main():
        runner = PerformanceTestRunner()
        
        try:
            print("🚀 Starting Performance Tests")
            
            # Authenticate
            if not await runner.authenticate():
                print("❌ Authentication failed")
                return
            
            print("✅ Authentication successful")
            
            # Run stress tests
            print("\n📊 Running API endpoint stress tests...")
            api_results = await runner.stress_test_api_endpoints()
            
            print("\n📋 Performance Results:")
            for endpoint, metrics in api_results.items():
                print(f"\n{endpoint}:")
                print(f"  Avg Response Time: {metrics.avg_response_time:.2f}ms")
                print(f"  P95 Response Time: {metrics.p95_response_time:.2f}ms")
                print(f"  Throughput: {metrics.throughput:.2f} req/s")
                print(f"  Error Rate: {metrics.error_rate:.2f}%")
                print(f"  Success: {metrics.success_count}, Failures: {metrics.failure_count}")
            
            print("\n🔍 Running memory leak test (2 minutes)...")
            memory_test = await runner.memory_leak_test(duration_minutes=2)
            
            print(f"\n🧠 Memory Test Results:")
            print(f"  Total Requests: {memory_test['total_requests']}")
            print(f"  Performance Degradation: {memory_test['performance_degradation_ratio']:.2f}x")
            print(f"  Potential Memory Leak: {memory_test['potential_memory_leak']}")
            
        finally:
            await runner.close()
    
    asyncio.run(main())