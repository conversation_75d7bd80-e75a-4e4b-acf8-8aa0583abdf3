# Load Testing Configuration for Publish AI Platform

# Test Environment Settings
environment:
  development:
    host: "http://localhost:8000"
    users: 10
    spawn_rate: 2
    run_time: "5m"
    
  staging:
    host: "https://staging.publish-ai.com"
    users: 50
    spawn_rate: 5
    run_time: "15m"
    
  production:
    host: "https://api.publish-ai.com"
    users: 100
    spawn_rate: 10
    run_time: "30m"

# Test Scenarios
scenarios:
  smoke_test:
    description: "Quick smoke test with minimal load"
    users: 5
    spawn_rate: 1
    run_time: "2m"
    user_classes:
      - "RegularUser:80"
      - "APIOnlyUser:20"
  
  load_test:
    description: "Standard load test simulating normal usage"
    users: 25
    spawn_rate: 3
    run_time: "10m"
    user_classes:
      - "RegularUser:60"
      - "PowerUser:25"
      - "AdminUser:10"
      - "APIOnlyUser:5"
  
  stress_test:
    description: "Stress test with high load to find breaking points"
    users: 100
    spawn_rate: 10
    run_time: "20m"
    user_classes:
      - "RegularUser:70"
      - "PowerUser:20"
      - "AdminUser:5"
      - "APIOnlyUser:5"
  
  spike_test:
    description: "Spike test with sudden load increases"
    users: 200
    spawn_rate: 50
    run_time: "15m"
    user_classes:
      - "RegularUser:80"
      - "PowerUser:15"
      - "APIOnlyUser:5"
  
  endurance_test:
    description: "Long-running test to check for memory leaks"
    users: 30
    spawn_rate: 2
    run_time: "60m"
    user_classes:
      - "RegularUser:70"
      - "PowerUser:20"
      - "AdminUser:10"

# Performance Thresholds
thresholds:
  response_time:
    health_check: 500      # milliseconds
    authentication: 2000   # milliseconds
    book_operations: 3000  # milliseconds
    ai_generation: 30000   # milliseconds (30 seconds)
    analytics: 5000        # milliseconds
  
  error_rates:
    acceptable: 1.0        # 1% error rate acceptable
    warning: 5.0           # 5% error rate warning
    critical: 10.0         # 10% error rate critical
  
  throughput:
    minimum_rps: 10        # requests per second
    target_rps: 50         # requests per second
    maximum_rps: 200       # requests per second

# Test Data Configuration
test_data:
  user_pool_size: 1000     # Number of test users to create
  book_templates:
    - title: "Test Fiction Book"
      genre: "fiction"
      target_audience: "adults"
    - title: "Test Non-Fiction Guide"
      genre: "non-fiction"
      target_audience: "adults"
    - title: "Test Children's Story"
      genre: "children"
      target_audience: "children"
  
  realistic_data:
    use_faker: true
    seed: 42               # For reproducible test data
    locales: ["en_US"]

# Monitoring and Reporting
monitoring:
  collect_custom_metrics: true
  export_format: ["csv", "json", "html"]
  real_time_stats: true
  
  alerts:
    high_response_time: 5000   # milliseconds
    high_error_rate: 10        # percentage
    low_throughput: 5          # requests per second
  
  integrations:
    prometheus: false          # Set to true if Prometheus available
    grafana: false            # Set to true if Grafana available
    datadog: false            # Set to true if Datadog available

# CI/CD Integration
ci_cd:
  fail_on_threshold_breach: true
  generate_reports: true
  upload_artifacts: true
  
  github_actions:
    artifact_name: "load-test-results"
    comment_on_pr: true
    
  performance_regression:
    compare_with_baseline: true
    baseline_file: "tests/load_testing/baseline_results.json"
    regression_threshold: 20   # 20% performance degradation fails test

# Docker Configuration
docker:
  image: "locustio/locust:latest"
  master_port: 8089
  worker_count: 4
  
  environment_variables:
    - LOCUST_HOST
    - LOCUST_USERS
    - LOCUST_SPAWN_RATE
    - LOCUST_RUN_TIME

# Advanced Configuration
advanced:
  connection_pool_size: 10
  request_timeout: 30        # seconds
  retry_attempts: 3
  retry_backoff: 1.5        # exponential backoff multiplier
  
  distributed_testing:
    enabled: false
    master_host: "localhost"
    master_port: 5557
    worker_nodes: []

# Security Configuration
security:
  test_user_prefix: "loadtest_"
  cleanup_test_data: true
  rate_limit_bypass: false   # Set to true if rate limiting should be bypassed for testing
  
  authentication:
    cache_tokens: true
    token_refresh_threshold: 300  # seconds before expiry to refresh