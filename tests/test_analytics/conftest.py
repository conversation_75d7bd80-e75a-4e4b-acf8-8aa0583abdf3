"""
Pytest configuration for analytics tests

This module provides shared fixtures and configuration for all analytics tests.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Configure asyncio for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_logger():
    """Create a mock logger for testing"""
    return Mock()


@pytest.fixture
def sample_datetime():
    """Provide a consistent datetime for testing"""
    return datetime(2024, 1, 15, 14, 30, 0)  # January 15, 2024, 2:30 PM


@pytest.fixture
def mock_database_session():
    """Create a mock database session"""
    session = Mock()
    session.query.return_value.filter.return_value.all.return_value = []
    session.commit.return_value = None
    session.rollback.return_value = None
    return session


@pytest.fixture
def mock_book_model():
    """Create a mock book model"""
    book = Mock()
    book.id = 1
    book.title = "Test Book"
    book.author = "Test Author"
    book.category = "business"
    book.content = "This is test content for the book. " * 100
    book.word_count = 500
    book.created_at = datetime.now()
    return book


@pytest.fixture
def mock_user_model():
    """Create a mock user model"""
    user = Mock()
    user.id = 1
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.created_at = datetime.now()
    return user


@pytest.fixture
def mock_scraper_data():
    """Create mock scraper data for testing"""
    return [
        {
            "title": "Sample Book 1",
            "author": "Author 1",
            "rating": 4.2,
            "price": 3.99,
            "sales_rank": 150,
            "category": "business",
            "publication_date": datetime.now() - timedelta(days=10)
        },
        {
            "title": "Sample Book 2",
            "author": "Author 2",
            "rating": 4.5,
            "price": 4.99,
            "sales_rank": 89,
            "category": "business",
            "publication_date": datetime.now() - timedelta(days=5)
        },
        {
            "title": "Sample Book 3",
            "author": "Author 3",
            "rating": 3.8,
            "price": 2.99,
            "sales_rank": 250,
            "category": "business",
            "publication_date": datetime.now() - timedelta(days=15)
        }
    ]


@pytest.fixture
def mock_market_conditions():
    """Create mock market conditions for testing"""
    from app.analytics.predictive_models import MarketConditions
    
    return MarketConditions(
        season="Q1",
        economic_indicators={
            "gdp_growth": 2.5,
            "unemployment": 3.8,
            "consumer_confidence": 105.2
        },
        trending_topics=["productivity", "ai", "remote work", "business"],
        competition_level="medium"
    )


@pytest.fixture
def mock_engagement_events():
    """Create mock engagement events for testing"""
    from app.analytics.reader_behavior import ReaderEngagementEvent, EngagementType
    
    base_time = datetime.now() - timedelta(days=1)
    events = []
    
    # Create a variety of engagement events
    for i in range(20):
        events.append(
            ReaderEngagementEvent(
                user_id=1,
                book_id=1,
                event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=i*5),
                page_number=i+1,
                chapter_number=(i//10)+1,
                time_spent_seconds=60 + (i % 3) * 30,
                sentiment_score=0.7 + (i % 4) * 0.1
            )
        )
    
    # Add special events
    events.extend([
        ReaderEngagementEvent(
            user_id=1,
            book_id=1,
            event_type=EngagementType.HIGHLIGHT,
            timestamp=base_time + timedelta(minutes=30),
            chapter_number=3,
            sentiment_score=0.8
        ),
        ReaderEngagementEvent(
            user_id=1,
            book_id=1,
            event_type=EngagementType.BOOKMARK,
            timestamp=base_time + timedelta(minutes=45),
            chapter_number=5,
            sentiment_score=0.9
        ),
        ReaderEngagementEvent(
            user_id=1,
            book_id=1,
            event_type=EngagementType.COMPLETION,
            timestamp=base_time + timedelta(hours=2),
            chapter_number=10,
            sentiment_score=0.9
        )
    ])
    
    return events


@pytest.fixture
def mock_amazon_scraper():
    """Create a mock Amazon scraper"""
    scraper = Mock()
    scraper.get_kindle_new_releases.return_value = []
    scraper.get_bestsellers.return_value = []
    return scraper


# Async test helpers
@pytest.fixture
def async_mock():
    """Create an async mock function"""
    async def _async_mock(*args, **kwargs):
        return Mock()
    return _async_mock


# Patch decorators for common mocks
@pytest.fixture
def patch_scraper():
    """Patch the Amazon scraper"""
    with patch('app.utils.scrapers.AmazonScraper') as mock:
        yield mock


@pytest.fixture
def patch_database():
    """Patch database operations"""
    with patch('app.database.get_db') as mock:
        yield mock


@pytest.fixture
def patch_logger():
    """Patch logging operations"""
    with patch('logging.getLogger') as mock:
        yield mock


# Test data generators
def generate_engagement_events(count=10, user_id=1, book_id=1):
    """Generate test engagement events"""
    from app.analytics.reader_behavior import ReaderEngagementEvent, EngagementType
    
    base_time = datetime.now()
    events = []
    
    for i in range(count):
        events.append(
            ReaderEngagementEvent(
                user_id=user_id,
                book_id=book_id,
                event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=i*2),
                page_number=i+1,
                chapter_number=(i//5)+1,
                time_spent_seconds=30 + (i % 5) * 15,
                sentiment_score=0.5 + (i % 6) * 0.1
            )
        )
    
    return events


def generate_market_releases(count=5, category="business"):
    """Generate test market release data"""
    releases = []
    base_date = datetime.now()
    
    for i in range(count):
        releases.append({
            "title": f"Test Book {i+1}",
            "author": f"Author {i+1}",
            "rating": 3.5 + (i % 4) * 0.3,
            "price": 2.99 + (i % 3) * 1.5,
            "sales_rank": 100 + i * 50,
            "category": category,
            "publication_date": base_date - timedelta(days=i*3)
        })
    
    return releases


# Export test utilities
__all__ = [
    'generate_engagement_events',
    'generate_market_releases'
]
