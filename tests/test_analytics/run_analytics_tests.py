#!/usr/bin/env python3
"""
Analytics Test Runner

This script runs all analytics tests with proper configuration and reporting.
"""

import sys
import os
import pytest
import asyncio
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def run_analytics_tests():
    """Run all analytics tests with comprehensive reporting"""
    
    # Test configuration
    test_args = [
        # Test directory
        str(Path(__file__).parent),
        
        # Verbose output
        "-v",
        
        # Show test coverage
        "--tb=short",
        
        # Show durations of slowest tests
        "--durations=10",
        
        # Capture output
        "-s",
        
        # Run tests in parallel (if pytest-xdist is available)
        # "-n", "auto",
        
        # Generate test report
        "--junit-xml=tests/test_analytics/analytics_test_report.xml",
        
        # Show local variables in tracebacks
        "-l",
        
        # Exit on first failure (optional)
        # "-x",
        
        # Run specific test patterns (uncomment as needed)
        # "-k", "test_reader_behavior",
        # "-k", "test_predictive_models",
    ]
    
    print("🧪 Running Analytics Tests...")
    print("=" * 60)
    
    # Run the tests
    exit_code = pytest.main(test_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ All analytics tests passed!")
    else:
        print("❌ Some analytics tests failed!")
    
    return exit_code


def run_reader_behavior_tests():
    """Run only reader behavior tests"""
    test_args = [
        str(Path(__file__).parent / "test_reader_behavior.py"),
        "-v",
        "--tb=short",
        "-s"
    ]
    
    print("🧪 Running Reader Behavior Tests...")
    print("=" * 60)
    
    exit_code = pytest.main(test_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ Reader behavior tests passed!")
    else:
        print("❌ Reader behavior tests failed!")
    
    return exit_code


def run_predictive_models_tests():
    """Run only predictive models tests"""
    test_args = [
        str(Path(__file__).parent / "test_predictive_models.py"),
        "-v",
        "--tb=short",
        "-s"
    ]
    
    print("🧪 Running Predictive Models Tests...")
    print("=" * 60)
    
    exit_code = pytest.main(test_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ Predictive models tests passed!")
    else:
        print("❌ Predictive models tests failed!")
    
    return exit_code


def run_performance_tests():
    """Run performance tests for analytics components"""
    test_args = [
        str(Path(__file__).parent),
        "-v",
        "--tb=short",
        "-s",
        "-k", "performance or load or stress",
        "--durations=0"
    ]
    
    print("🧪 Running Analytics Performance Tests...")
    print("=" * 60)
    
    exit_code = pytest.main(test_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ Performance tests passed!")
    else:
        print("❌ Performance tests failed!")
    
    return exit_code


def run_integration_tests():
    """Run integration tests for analytics components"""
    test_args = [
        str(Path(__file__).parent),
        "-v",
        "--tb=short",
        "-s",
        "-k", "integration or full_pipeline",
        "--durations=0"
    ]
    
    print("🧪 Running Analytics Integration Tests...")
    print("=" * 60)
    
    exit_code = pytest.main(test_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ Integration tests passed!")
    else:
        print("❌ Integration tests failed!")
    
    return exit_code


def check_test_environment():
    """Check if the test environment is properly set up"""
    print("🔍 Checking test environment...")
    
    # Check if required modules can be imported
    try:
        from app.analytics.reader_behavior import ReaderBehaviorAnalyzer
        print("✅ Reader behavior module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import reader behavior module: {e}")
        return False
    
    try:
        from app.analytics.predictive_models import PredictiveSalesEngine
        print("✅ Predictive models module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import predictive models module: {e}")
        return False
    
    # Check if pytest is available
    try:
        import pytest
        print(f"✅ Pytest version {pytest.__version__} available")
    except ImportError:
        print("❌ Pytest not available")
        return False
    
    # Check if asyncio is working
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.close()
        print("✅ Asyncio working correctly")
    except Exception as e:
        print(f"❌ Asyncio issue: {e}")
        return False
    
    print("✅ Test environment is ready!")
    return True


def main():
    """Main test runner function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "reader":
            return run_reader_behavior_tests()
        elif command == "predictive":
            return run_predictive_models_tests()
        elif command == "performance":
            return run_performance_tests()
        elif command == "integration":
            return run_integration_tests()
        elif command == "check":
            return 0 if check_test_environment() else 1
        elif command == "help":
            print_help()
            return 0
        else:
            print(f"Unknown command: {command}")
            print_help()
            return 1
    else:
        # Check environment first
        if not check_test_environment():
            return 1
        
        # Run all tests
        return run_analytics_tests()


def print_help():
    """Print help information"""
    print("""
Analytics Test Runner

Usage:
    python run_analytics_tests.py [command]

Commands:
    (no command)  - Run all analytics tests
    reader        - Run only reader behavior tests
    predictive    - Run only predictive models tests
    performance   - Run performance tests
    integration   - Run integration tests
    check         - Check test environment
    help          - Show this help message

Examples:
    python run_analytics_tests.py
    python run_analytics_tests.py reader
    python run_analytics_tests.py predictive
    python run_analytics_tests.py check
    """)


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
