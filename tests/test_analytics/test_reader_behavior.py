"""
Tests for Reader Behavior Analytics

This module contains comprehensive tests for the ReaderBehaviorAnalyzer
and all related components including data classes, enums, and analysis methods.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from app.analytics.reader_behavior import (
    ReaderBehaviorAnalyzer,
    ReaderEngagementEvent,
    ReaderInsights,
    EngagementType,
    ReadingSpeed,
    ReadingPattern,
    DropOffAnalysis,
    EngagementHeatmap,
    SentimentAnalysis,
    CompletionPredictors,
    PersonalizationOpportunities
)


class TestReaderBehaviorAnalyzer:
    """Test suite for ReaderBehaviorAnalyzer"""
    
    @pytest.fixture
    def analyzer(self):
        """Create a ReaderBehaviorAnalyzer instance"""
        return ReaderBehaviorAnalyzer()
    
    @pytest.fixture
    def mock_book(self):
        """Create a mock book object"""
        book = Mock()
        book.id = 1
        book.title = "Test Book"
        return book
    
    @pytest.fixture
    def sample_engagement_data(self):
        """Create sample engagement data for testing"""
        base_time = datetime.now() - timedelta(days=1)
        return [
            ReaderEngagementEvent(
                user_id=1,
                book_id=1,
                event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=i*5),
                page_number=i+1,
                chapter_number=(i//10)+1,
                time_spent_seconds=60 + (i % 3) * 30,
                sentiment_score=0.7 + (i % 4) * 0.1
            )
            for i in range(20)
        ]
    
    @pytest.fixture
    def engagement_with_special_events(self, sample_engagement_data):
        """Add special events to sample data"""
        base_time = datetime.now() - timedelta(days=1)
        special_events = [
            ReaderEngagementEvent(
                user_id=1,
                book_id=1,
                event_type=EngagementType.HIGHLIGHT,
                timestamp=base_time + timedelta(minutes=30),
                chapter_number=3,
                sentiment_score=0.8
            ),
            ReaderEngagementEvent(
                user_id=1,
                book_id=1,
                event_type=EngagementType.BOOKMARK,
                timestamp=base_time + timedelta(minutes=45),
                chapter_number=5,
                sentiment_score=0.9
            ),
            ReaderEngagementEvent(
                user_id=1,
                book_id=1,
                event_type=EngagementType.COMPLETION,
                timestamp=base_time + timedelta(hours=2),
                chapter_number=10,
                sentiment_score=0.9
            ),
            ReaderEngagementEvent(
                user_id=2,
                book_id=1,
                event_type=EngagementType.DROP_OFF,
                timestamp=base_time + timedelta(minutes=20),
                chapter_number=2,
                sentiment_score=0.3
            )
        ]
        return sample_engagement_data + special_events


class TestReaderBehaviorAnalyzerBasic(TestReaderBehaviorAnalyzer):
    """Basic functionality tests"""
    
    def test_analyzer_initialization(self, analyzer):
        """Test analyzer initialization"""
        assert analyzer is not None
        assert hasattr(analyzer, 'logger')
    
    @pytest.mark.asyncio
    async def test_analyze_reader_engagement_success(self, analyzer, mock_book, engagement_with_special_events):
        """Test successful reader engagement analysis"""
        result = await analyzer.analyze_reader_engagement(mock_book, engagement_with_special_events)
        
        assert isinstance(result, ReaderInsights)
        assert result.book_id == 1
        assert isinstance(result.reading_patterns, ReadingPattern)
        assert isinstance(result.drop_off_analysis, DropOffAnalysis)
        assert isinstance(result.engagement_heatmap, EngagementHeatmap)
        assert isinstance(result.sentiment_analysis, SentimentAnalysis)
        assert isinstance(result.completion_predictors, CompletionPredictors)
        assert isinstance(result.personalization_opportunities, PersonalizationOpportunities)
        assert isinstance(result.recommended_improvements, list)
        assert result.confidence_score > 0
    
    @pytest.mark.asyncio
    async def test_analyze_reader_engagement_empty_data(self, analyzer, mock_book):
        """Test analysis with empty engagement data"""
        result = await analyzer.analyze_reader_engagement(mock_book, [])
        
        assert isinstance(result, ReaderInsights)
        assert result.book_id == 0  # Default when no engagement data
        assert isinstance(result.reading_patterns, ReadingPattern)
        assert result.reading_patterns.reading_speed == ReadingSpeed.NORMAL
    
    @pytest.mark.asyncio
    async def test_analyze_reader_engagement_error_handling(self, analyzer):
        """Test error handling in main analysis method"""
        # Test with invalid book object
        invalid_book = None

        # The method should handle errors gracefully and return defaults
        result = await analyzer.analyze_reader_engagement(invalid_book, [])

        # Should still return valid ReaderInsights with defaults
        assert isinstance(result, ReaderInsights)


class TestReadingPatternAnalysis(TestReaderBehaviorAnalyzer):
    """Test reading pattern analysis methods"""
    
    @pytest.mark.asyncio
    async def test_analyze_reading_patterns_success(self, analyzer, sample_engagement_data):
        """Test successful reading pattern analysis"""
        result = await analyzer._analyze_reading_patterns(sample_engagement_data)
        
        assert isinstance(result, ReadingPattern)
        assert result.average_session_duration >= 0
        assert isinstance(result.reading_speed, ReadingSpeed)
        assert isinstance(result.preferred_reading_times, list)
        assert 0 <= result.engagement_frequency <= 100
        assert 0 <= result.completion_rate <= 1
        assert 0 <= result.retention_rate <= 1
    
    @pytest.mark.asyncio
    async def test_analyze_reading_patterns_empty_data(self, analyzer):
        """Test reading pattern analysis with empty data"""
        result = await analyzer._analyze_reading_patterns([])
        
        assert isinstance(result, ReadingPattern)
        assert result.reading_speed == ReadingSpeed.NORMAL
        assert result.completion_rate == 0.6  # Default value
    
    def test_calculate_reading_speed_categories(self, analyzer):
        """Test reading speed calculation"""
        base_time = datetime.now()
        
        # Test slow reading
        slow_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time, page_number=1, time_spent_seconds=120
            )
        ]
        speed = analyzer._calculate_reading_speed(slow_events)
        assert speed == ReadingSpeed.SLOW
        
        # Test fast reading
        fast_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time, page_number=1, time_spent_seconds=20
            )
        ]
        speed = analyzer._calculate_reading_speed(fast_events)
        assert speed == ReadingSpeed.FAST
    
    def test_analyze_reading_times(self, analyzer):
        """Test reading time analysis"""
        base_time = datetime.now().replace(hour=14, minute=0)  # 2 PM
        
        events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(hours=i),
                page_number=i+1
            )
            for i in range(4)  # Events at 2PM, 3PM, 4PM, 5PM (afternoon/evening)
        ]
        
        preferred_times = analyzer._analyze_reading_times(events)
        assert isinstance(preferred_times, list)
        assert len(preferred_times) > 0
        assert 'afternoon' in preferred_times or 'evening' in preferred_times


class TestDropOffAnalysis(TestReaderBehaviorAnalyzer):
    """Test drop-off analysis methods"""
    
    @pytest.mark.asyncio
    async def test_identify_drop_off_points_success(self, analyzer):
        """Test successful drop-off point identification"""
        base_time = datetime.now()
        drop_off_events = [
            ReaderEngagementEvent(
                user_id=i, book_id=1, event_type=EngagementType.DROP_OFF,
                timestamp=base_time, chapter_number=3
            )
            for i in range(5)  # 5 users drop off at chapter 3
        ]
        
        result = await analyzer._identify_drop_off_points(drop_off_events)
        
        assert isinstance(result, DropOffAnalysis)
        assert isinstance(result.critical_drop_points, list)
        assert isinstance(result.drop_off_rate_by_chapter, dict)
        assert isinstance(result.common_drop_off_reasons, list)
        assert isinstance(result.recovery_opportunities, list)
    
    @pytest.mark.asyncio
    async def test_identify_drop_off_points_empty_data(self, analyzer):
        """Test drop-off analysis with empty data"""
        result = await analyzer._identify_drop_off_points([])
        
        assert isinstance(result, DropOffAnalysis)
        assert len(result.critical_drop_points) == 0
        assert len(result.drop_off_rate_by_chapter) == 0


class TestEngagementHeatmap(TestReaderBehaviorAnalyzer):
    """Test engagement heatmap creation"""
    
    @pytest.mark.asyncio
    async def test_create_engagement_heatmap_success(self, analyzer, sample_engagement_data):
        """Test successful engagement heatmap creation"""
        result = await analyzer._create_engagement_heatmap(sample_engagement_data)
        
        assert isinstance(result, EngagementHeatmap)
        assert isinstance(result.chapter_engagement_scores, dict)
        assert isinstance(result.page_engagement_scores, dict)
        assert isinstance(result.time_based_engagement, dict)
        assert isinstance(result.interaction_hotspots, list)
        
        # Check that scores are normalized (0-100)
        for score in result.chapter_engagement_scores.values():
            assert 0 <= score <= 100
    
    @pytest.mark.asyncio
    async def test_create_engagement_heatmap_empty_data(self, analyzer):
        """Test heatmap creation with empty data"""
        result = await analyzer._create_engagement_heatmap([])
        
        assert isinstance(result, EngagementHeatmap)
        assert len(result.chapter_engagement_scores) == 10  # Default chapters
        assert len(result.page_engagement_scores) == 20  # Default pages


class TestSentimentAnalysis(TestReaderBehaviorAnalyzer):
    """Test sentiment analysis methods"""

    @pytest.mark.asyncio
    async def test_analyze_reader_sentiment_success(self, analyzer):
        """Test successful sentiment analysis"""
        base_time = datetime.now()
        sentiment_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.RATING,
                timestamp=base_time, chapter_number=i+1, sentiment_score=0.8 + (i % 3) * 0.1
            )
            for i in range(5)
        ]

        result = await analyzer._analyze_reader_sentiment(sentiment_events)

        assert isinstance(result, SentimentAnalysis)
        assert 0 <= result.overall_sentiment <= 1
        assert isinstance(result.sentiment_by_chapter, dict)
        assert isinstance(result.sentiment_trends, list)
        assert isinstance(result.emotional_peaks, list)
        assert isinstance(result.satisfaction_indicators, dict)

    @pytest.mark.asyncio
    async def test_analyze_reader_sentiment_no_sentiment_data(self, analyzer, sample_engagement_data):
        """Test sentiment analysis with no sentiment scores"""
        # Remove sentiment scores
        for event in sample_engagement_data:
            event.sentiment_score = None

        result = await analyzer._analyze_reader_sentiment(sample_engagement_data)

        assert isinstance(result, SentimentAnalysis)
        assert result.overall_sentiment == 0.7  # Mock default

    @pytest.mark.asyncio
    async def test_analyze_reader_sentiment_empty_data(self, analyzer):
        """Test sentiment analysis with empty data"""
        result = await analyzer._analyze_reader_sentiment([])

        assert isinstance(result, SentimentAnalysis)
        assert result.overall_sentiment == 0.7  # Default value


class TestCompletionPredictors(TestReaderBehaviorAnalyzer):
    """Test completion predictor analysis"""

    @pytest.mark.asyncio
    async def test_identify_completion_predictors_success(self, analyzer, engagement_with_special_events):
        """Test successful completion predictor identification"""
        result = await analyzer._identify_completion_predictors(engagement_with_special_events)

        assert isinstance(result, CompletionPredictors)
        assert isinstance(result.early_engagement_indicators, list)
        assert isinstance(result.risk_factors, list)
        assert isinstance(result.positive_signals, list)
        assert isinstance(result.completion_probability_model, dict)

        # Should detect completion event
        assert 'completion_achieved' in result.positive_signals

    @pytest.mark.asyncio
    async def test_identify_completion_predictors_high_engagement(self, analyzer):
        """Test completion predictors with high early engagement"""
        base_time = datetime.now()
        high_engagement_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.HIGHLIGHT,
                timestamp=base_time, chapter_number=1
            ),
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.BOOKMARK,
                timestamp=base_time, chapter_number=2
            ),
        ] + [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=i),
                chapter_number=1, page_number=i+1
            )
            for i in range(10)  # High early engagement
        ]

        result = await analyzer._identify_completion_predictors(high_engagement_events)

        assert 'high_early_engagement' in result.early_engagement_indicators
        assert 'early_highlighting' in result.early_engagement_indicators
        assert 'early_bookmarking' in result.early_engagement_indicators


class TestPersonalizationOpportunities(TestReaderBehaviorAnalyzer):
    """Test personalization opportunity identification"""

    @pytest.mark.asyncio
    async def test_identify_personalization_opportunities_success(self, analyzer, engagement_with_special_events):
        """Test successful personalization opportunity identification"""
        result = await analyzer._identify_personalization_opportunities(engagement_with_special_events)

        assert isinstance(result, PersonalizationOpportunities)
        assert isinstance(result.content_preferences, dict)
        assert isinstance(result.optimal_chapter_length, int)
        assert isinstance(result.preferred_content_types, list)
        assert isinstance(result.engagement_triggers, list)
        assert isinstance(result.customization_suggestions, list)

        assert result.optimal_chapter_length > 0

    @pytest.mark.asyncio
    async def test_identify_personalization_opportunities_highlight_preference(self, analyzer):
        """Test personalization with highlight preference"""
        base_time = datetime.now()
        highlight_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.HIGHLIGHT,
                timestamp=base_time + timedelta(minutes=i),
                chapter_number=i+1
            )
            for i in range(10)  # Many highlights
        ]

        result = await analyzer._identify_personalization_opportunities(highlight_events)

        assert result.content_preferences['prefers_highlights'] is True
        assert 'visual_content' in result.preferred_content_types


class TestHelperMethods(TestReaderBehaviorAnalyzer):
    """Test helper and utility methods"""

    def test_group_events_by_session(self, analyzer):
        """Test event grouping by session"""
        base_time = datetime.now()
        events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=i*5),
                page_number=i+1
            )
            for i in range(5)
        ] + [
            # Gap of more than 30 minutes - new session
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=base_time + timedelta(minutes=60),
                page_number=6
            )
        ]

        sessions = analyzer._group_events_by_session(events)

        assert isinstance(sessions, list)
        assert len(sessions) >= 1
        for session in sessions:
            assert 'events' in session
            assert 'duration' in session
            assert 'start_time' in session
            assert 'end_time' in session


class TestDefaultMethods(TestReaderBehaviorAnalyzer):
    """Test default data methods"""

    def test_get_default_reading_pattern(self, analyzer):
        """Test default reading pattern"""
        result = analyzer._get_default_reading_pattern()

        assert isinstance(result, ReadingPattern)
        assert result.reading_speed == ReadingSpeed.NORMAL
        assert result.average_session_duration > 0
        assert 0 <= result.completion_rate <= 1

    def test_get_default_drop_off_analysis(self, analyzer):
        """Test default drop-off analysis"""
        result = analyzer._get_default_drop_off_analysis()

        assert isinstance(result, DropOffAnalysis)
        assert isinstance(result.critical_drop_points, list)
        assert isinstance(result.common_drop_off_reasons, list)

    def test_get_default_engagement_heatmap(self, analyzer):
        """Test default engagement heatmap"""
        result = analyzer._get_default_engagement_heatmap()

        assert isinstance(result, EngagementHeatmap)
        assert len(result.chapter_engagement_scores) == 10
        assert len(result.page_engagement_scores) == 20

    def test_get_default_sentiment_analysis(self, analyzer):
        """Test default sentiment analysis"""
        result = analyzer._get_default_sentiment_analysis()

        assert isinstance(result, SentimentAnalysis)
        assert 0 <= result.overall_sentiment <= 1

    def test_get_default_completion_predictors(self, analyzer):
        """Test default completion predictors"""
        result = analyzer._get_default_completion_predictors()

        assert isinstance(result, CompletionPredictors)
        assert isinstance(result.completion_probability_model, dict)

    def test_get_default_personalization_opportunities(self, analyzer):
        """Test default personalization opportunities"""
        result = analyzer._get_default_personalization_opportunities()

        assert isinstance(result, PersonalizationOpportunities)
        assert result.optimal_chapter_length > 0


class TestDataClasses:
    """Test data class functionality"""

    def test_reader_engagement_event_creation(self):
        """Test ReaderEngagementEvent creation"""
        event = ReaderEngagementEvent(
            user_id=1,
            book_id=1,
            event_type=EngagementType.PAGE_VIEW,
            timestamp=datetime.now()
        )

        assert event.user_id == 1
        assert event.book_id == 1
        assert event.event_type == EngagementType.PAGE_VIEW
        assert event.page_number is None  # Optional field

    def test_engagement_type_enum(self):
        """Test EngagementType enum values"""
        assert EngagementType.PAGE_VIEW.value == "page_view"
        assert EngagementType.HIGHLIGHT.value == "highlight"
        assert EngagementType.COMPLETION.value == "completion"

    def test_reading_speed_enum(self):
        """Test ReadingSpeed enum values"""
        assert ReadingSpeed.SLOW.value == "slow"
        assert ReadingSpeed.NORMAL.value == "normal"
        assert ReadingSpeed.FAST.value == "fast"
        assert ReadingSpeed.SKIMMING.value == "skimming"


class TestErrorHandling(TestReaderBehaviorAnalyzer):
    """Test error handling scenarios"""

    @pytest.mark.asyncio
    async def test_analyze_reading_patterns_error_handling(self, analyzer):
        """Test error handling in reading pattern analysis"""
        # Create events that might cause errors
        problematic_events = [
            ReaderEngagementEvent(
                user_id=1, book_id=1, event_type=EngagementType.PAGE_VIEW,
                timestamp=datetime.now(), time_spent_seconds=None
            )
        ]

        result = await analyzer._analyze_reading_patterns(problematic_events)

        # Should return default pattern on error
        assert isinstance(result, ReadingPattern)

    def test_sentiment_analysis_error_handling(self, analyzer):
        """Test error handling in sentiment analysis"""
        # Test the default method (not async)
        result = analyzer._get_default_sentiment_analysis()

        assert isinstance(result, SentimentAnalysis)

    def test_helper_method_error_handling(self, analyzer):
        """Test error handling in helper methods"""
        # Test with empty or invalid data
        result = analyzer._group_events_by_session([])
        assert isinstance(result, list)

        result = analyzer._calculate_reading_speed([])
        assert result == ReadingSpeed.NORMAL

        result = analyzer._analyze_reading_times([])
        assert isinstance(result, list)


if __name__ == "__main__":
    pytest.main([__file__])
