<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="85" time="4.015" timestamp="2025-06-23T16:07:19.471535" hostname="Marckensies-Mac-Pro-2.local"><testcase classname="tests.test_analytics.test_predictive_models.TestPredictiveSalesEngineBasic" name="test_engine_initialization" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestPredictiveSalesEngineBasic" name="test_predict_book_performance_success" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestPredictiveSalesEngineBasic" name="test_predict_book_performance_with_market_data" time="0.052" /><testcase classname="tests.test_analytics.test_predictive_models.TestPredictiveSalesEngineBasic" name="test_predict_book_performance_error_handling" time="0.052" /><testcase classname="tests.test_analytics.test_predictive_models.TestMarketSaturationAnalysis" name="test_analyze_market_saturation_success" time="0.043" /><testcase classname="tests.test_analytics.test_predictive_models.TestMarketSaturationAnalysis" name="test_analyze_market_saturation_empty_data" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestMarketSaturationAnalysis" name="test_analyze_market_saturation_error_handling" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestContentFeatureAnalysis" name="test_analyze_content_features_success" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestContentFeatureAnalysis" name="test_analyze_content_features_empty_content" time="0.043" /><testcase classname="tests.test_analytics.test_predictive_models.TestContentFeatureAnalysis" name="test_analyze_content_features_no_content_attribute" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestSeasonalFactors" name="test_get_seasonal_factors_business" time="0.043" /><testcase classname="tests.test_analytics.test_predictive_models.TestSeasonalFactors" name="test_get_seasonal_factors_unknown_category" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestSeasonalFactors" name="test_get_seasonal_factors_error_handling" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestCompetitionAnalysis" name="test_analyze_competition_success" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestCompetitionAnalysis" name="test_analyze_competition_different_categories" time="0.048" /><testcase classname="tests.test_analytics.test_predictive_models.TestCompetitionAnalysis" name="test_analyze_competition_error_handling" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestHistoricalPerformance" name="test_get_historical_performance_success" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestHistoricalPerformance" name="test_get_historical_performance_error_handling" time="0.048" /><testcase classname="tests.test_analytics.test_predictive_models.TestQualityAssessment" name="test_assess_average_quality_success" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestQualityAssessment" name="test_assess_average_quality_empty_releases" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestQualityAssessment" name="test_assess_average_quality_no_ratings" time="0.049" /><testcase classname="tests.test_analytics.test_predictive_models.TestPriceCompetitionAnalysis" name="test_analyze_price_competition_success" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestPriceCompetitionAnalysis" name="test_analyze_price_competition_high_competition" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestPriceCompetitionAnalysis" name="test_analyze_price_competition_low_competition" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestSaturationCalculation" name="test_calculate_saturation_level_high" time="0.047" /><testcase classname="tests.test_analytics.test_predictive_models.TestSaturationCalculation" name="test_calculate_saturation_level_medium" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestSaturationCalculation" name="test_calculate_saturation_level_low" time="0.051" /><testcase classname="tests.test_analytics.test_predictive_models.TestSaturationCalculation" name="test_calculate_saturation_level_error_handling" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestOpportunityGapIdentification" name="test_identify_opportunity_gaps_low_competition" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestOpportunityGapIdentification" name="test_identify_opportunity_gaps_price_gap" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestOpportunityGapIdentification" name="test_identify_opportunity_gaps_quality_gap" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestMockMLModel" name="test_mock_ml_model_predict_success" time="0.047" /><testcase classname="tests.test_analytics.test_predictive_models.TestMockMLModel" name="test_mock_ml_model_predict_dict_features" time="0.048" /><testcase classname="tests.test_analytics.test_predictive_models.TestMockMLModel" name="test_mock_ml_model_predict_low_competition" time="0.047" /><testcase classname="tests.test_analytics.test_predictive_models.TestMockMLModel" name="test_mock_ml_model_predict_high_competition" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestMockMLModel" name="test_mock_ml_model_predict_error_handling" time="0.047" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_market_conditions_creation" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_sales_prediction_creation" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_market_saturation_creation" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_content_features_creation" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_competition_analysis_creation" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestDataClasses" name="test_prediction_result_creation" time="0.044" /><testcase classname="tests.test_analytics.test_predictive_models.TestIntegrationScenarios" name="test_full_prediction_pipeline_business_book" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestIntegrationScenarios" name="test_full_prediction_pipeline_fiction_book" time="0.045" /><testcase classname="tests.test_analytics.test_predictive_models.TestIntegrationScenarios" name="test_prediction_with_saturated_market" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestIntegrationScenarios" name="test_prediction_with_network_errors" time="0.046" /><testcase classname="tests.test_analytics.test_predictive_models.TestIntegrationScenarios" name="test_prediction_with_minimal_manuscript_data" time="0.049" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_market_saturation_analysis_network_error" time="0.048" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_content_analysis_with_none_content" time="0.049" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_seasonal_factors_with_invalid_category" time="0.049" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_competition_analysis_with_missing_category" time="0.053" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_quality_assessment_with_invalid_data" time="0.049" /><testcase classname="tests.test_analytics.test_predictive_models.TestErrorHandlingAndEdgeCases" name="test_price_competition_with_missing_prices" time="0.046" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReaderBehaviorAnalyzerBasic" name="test_analyzer_initialization" time="0.045" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReaderBehaviorAnalyzerBasic" name="test_analyze_reader_engagement_success" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReaderBehaviorAnalyzerBasic" name="test_analyze_reader_engagement_empty_data" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReaderBehaviorAnalyzerBasic" name="test_analyze_reader_engagement_error_handling" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReadingPatternAnalysis" name="test_analyze_reading_patterns_success" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReadingPatternAnalysis" name="test_analyze_reading_patterns_empty_data" time="0.045" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReadingPatternAnalysis" name="test_calculate_reading_speed_categories" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestReadingPatternAnalysis" name="test_analyze_reading_times" time="0.041" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDropOffAnalysis" name="test_identify_drop_off_points_success" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDropOffAnalysis" name="test_identify_drop_off_points_empty_data" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestEngagementHeatmap" name="test_create_engagement_heatmap_success" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestEngagementHeatmap" name="test_create_engagement_heatmap_empty_data" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestSentimentAnalysis" name="test_analyze_reader_sentiment_success" time="0.045" /><testcase classname="tests.test_analytics.test_reader_behavior.TestSentimentAnalysis" name="test_analyze_reader_sentiment_no_sentiment_data" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestSentimentAnalysis" name="test_analyze_reader_sentiment_empty_data" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestCompletionPredictors" name="test_identify_completion_predictors_success" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestCompletionPredictors" name="test_identify_completion_predictors_high_engagement" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestPersonalizationOpportunities" name="test_identify_personalization_opportunities_success" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestPersonalizationOpportunities" name="test_identify_personalization_opportunities_highlight_preference" time="0.041" /><testcase classname="tests.test_analytics.test_reader_behavior.TestHelperMethods" name="test_group_events_by_session" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_reading_pattern" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_drop_off_analysis" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_engagement_heatmap" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_sentiment_analysis" time="0.042" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_completion_predictors" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDefaultMethods" name="test_get_default_personalization_opportunities" time="0.040" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDataClasses" name="test_reader_engagement_event_creation" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDataClasses" name="test_engagement_type_enum" time="0.044" /><testcase classname="tests.test_analytics.test_reader_behavior.TestDataClasses" name="test_reading_speed_enum" time="0.045" /><testcase classname="tests.test_analytics.test_reader_behavior.TestErrorHandling" name="test_analyze_reading_patterns_error_handling" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestErrorHandling" name="test_sentiment_analysis_error_handling" time="0.043" /><testcase classname="tests.test_analytics.test_reader_behavior.TestErrorHandling" name="test_helper_method_error_handling" time="0.044" /></testsuite></testsuites>