"""
Tests for Predictive Models Analytics

This module contains comprehensive tests for the PredictiveSalesEngine
and all related components including data classes, ML models, and prediction methods.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from app.analytics.predictive_models import (
    PredictiveSalesEngine,
    MockMLModel,
    MarketConditions,
    SalesPrediction,
    MarketSaturation,
    ContentFeatures,
    CompetitionAnalysis,
    PredictionResult
)


class TestPredictiveSalesEngine:
    """Test suite for PredictiveSalesEngine"""
    
    @pytest.fixture
    def engine(self):
        """Create a PredictiveSalesEngine instance"""
        return PredictiveSalesEngine()
    
    @pytest.fixture
    def mock_manuscript(self):
        """Create a mock manuscript object"""
        manuscript = Mock()
        manuscript.content = "This is a test manuscript about productivity and business success. " * 100
        manuscript.category = "business"
        manuscript.title = "Test Business Book"
        return manuscript
    
    @pytest.fixture
    def sample_market_conditions(self):
        """Create sample market conditions"""
        return MarketConditions(
            season="Q1",
            economic_indicators={"gdp_growth": 2.5, "unemployment": 3.8},
            trending_topics=["productivity", "remote work", "ai"],
            competition_level="medium"
        )
    
    @pytest.fixture
    def mock_recent_releases(self):
        """Create mock recent releases data"""
        return [
            {
                "title": "Business Book 1",
                "rating": 4.2,
                "price": 3.99,
                "sales_rank": 150,
                "publication_date": datetime.now() - timedelta(days=10)
            },
            {
                "title": "Business Book 2", 
                "rating": 4.5,
                "price": 4.99,
                "sales_rank": 89,
                "publication_date": datetime.now() - timedelta(days=5)
            },
            {
                "title": "Business Book 3",
                "rating": 3.8,
                "price": 2.99,
                "sales_rank": 250,
                "publication_date": datetime.now() - timedelta(days=15)
            }
        ]


class TestPredictiveSalesEngineBasic(TestPredictiveSalesEngine):
    """Basic functionality tests"""
    
    def test_engine_initialization(self, engine):
        """Test engine initialization"""
        assert engine is not None
        assert hasattr(engine, 'market_scraper')
        assert hasattr(engine, 'prediction_model')
        assert hasattr(engine, 'logger')
    
    @pytest.mark.asyncio
    async def test_predict_book_performance_success(self, engine, mock_manuscript, sample_market_conditions):
        """Test successful book performance prediction"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=[]):
            result = await engine.predict_book_performance(mock_manuscript, sample_market_conditions)
            
            assert isinstance(result, SalesPrediction)
            assert result.predicted_sales_30_days > 0
            assert result.predicted_revenue_30_days > 0
            assert isinstance(result.confidence_interval, tuple)
            assert len(result.confidence_interval) == 2
            assert result.recommended_price > 0
            assert 0 <= result.market_opportunity_score <= 100
            assert isinstance(result.risk_factors, list)
            assert 0 <= result.success_probability <= 100
    
    @pytest.mark.asyncio
    async def test_predict_book_performance_with_market_data(self, engine, mock_manuscript, sample_market_conditions, mock_recent_releases):
        """Test prediction with market data"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=mock_recent_releases):
            result = await engine.predict_book_performance(mock_manuscript, sample_market_conditions)
            
            assert isinstance(result, SalesPrediction)
            assert result.predicted_sales_30_days > 0
            # Should have market analysis data
            assert len(result.risk_factors) >= 0
    
    @pytest.mark.asyncio
    async def test_predict_book_performance_error_handling(self, engine, mock_manuscript, sample_market_conditions):
        """Test error handling in prediction"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', side_effect=Exception("Scraper error")):
            result = await engine.predict_book_performance(mock_manuscript, sample_market_conditions)
            
            # Should still return valid prediction with defaults
            assert isinstance(result, SalesPrediction)
            assert result.predicted_sales_30_days > 0


class TestMarketSaturationAnalysis(TestPredictiveSalesEngine):
    """Test market saturation analysis methods"""
    
    @pytest.mark.asyncio
    async def test_analyze_market_saturation_success(self, engine, mock_recent_releases):
        """Test successful market saturation analysis"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=mock_recent_releases):
            result = await engine._analyze_market_saturation("business")
            
            assert isinstance(result, MarketSaturation)
            assert result.publication_rate >= 0
            assert 0 <= result.average_quality <= 100
            assert result.price_competition in ['low', 'medium', 'high']
            assert result.saturation_level in ['low', 'medium', 'high']
            assert isinstance(result.opportunity_gaps, list)
    
    @pytest.mark.asyncio
    async def test_analyze_market_saturation_empty_data(self, engine):
        """Test market saturation analysis with empty data"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=[]):
            result = await engine._analyze_market_saturation("business")
            
            assert isinstance(result, MarketSaturation)
            assert result.publication_rate == 0
            assert result.saturation_level == 'low'
    
    @pytest.mark.asyncio
    async def test_analyze_market_saturation_error_handling(self, engine):
        """Test error handling in market saturation analysis"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', side_effect=Exception("Network error")):
            result = await engine._analyze_market_saturation("business")
            
            assert isinstance(result, MarketSaturation)
            # Should return default values on error


class TestContentFeatureAnalysis(TestPredictiveSalesEngine):
    """Test content feature analysis methods"""
    
    @pytest.mark.asyncio
    async def test_analyze_content_features_success(self, engine, mock_manuscript):
        """Test successful content feature analysis"""
        result = await engine._analyze_content_features(mock_manuscript)
        
        assert isinstance(result, ContentFeatures)
        assert result.word_count > 0
        assert 0 <= result.readability_score <= 100
        assert 0 <= result.topic_relevance <= 100
        assert 0 <= result.uniqueness_score <= 100
        assert 0 <= result.engagement_potential <= 100
    
    @pytest.mark.asyncio
    async def test_analyze_content_features_empty_content(self, engine):
        """Test content analysis with empty content"""
        empty_manuscript = Mock()
        empty_manuscript.content = ""
        
        result = await engine._analyze_content_features(empty_manuscript)
        
        assert isinstance(result, ContentFeatures)
        assert result.word_count == 0
        assert result.readability_score >= 0
    
    @pytest.mark.asyncio
    async def test_analyze_content_features_no_content_attribute(self, engine):
        """Test content analysis with missing content attribute"""
        no_content_manuscript = Mock()
        del no_content_manuscript.content  # Remove content attribute
        
        result = await engine._analyze_content_features(no_content_manuscript)
        
        assert isinstance(result, ContentFeatures)
        assert result.word_count == 0


class TestSeasonalFactors(TestPredictiveSalesEngine):
    """Test seasonal factor analysis"""
    
    @pytest.mark.asyncio
    async def test_get_seasonal_factors_business(self, engine):
        """Test seasonal factors for business category"""
        result = await engine._get_seasonal_factors("business")
        
        assert isinstance(result, dict)
        assert 'q1' in result
        assert 'q2' in result
        assert 'q3' in result
        assert 'q4' in result
        
        # All factors should be positive
        for factor in result.values():
            assert factor > 0
    
    @pytest.mark.asyncio
    async def test_get_seasonal_factors_unknown_category(self, engine):
        """Test seasonal factors for unknown category"""
        result = await engine._get_seasonal_factors("unknown_category")
        
        assert isinstance(result, dict)
        # Should return default factors (all 1.0)
        for factor in result.values():
            assert factor == 1.0
    
    @pytest.mark.asyncio
    async def test_get_seasonal_factors_error_handling(self, engine):
        """Test error handling in seasonal factor analysis"""
        # Test with invalid category that might cause errors
        result = await engine._get_seasonal_factors(None)

        # Should return default factors
        assert isinstance(result, dict)
        for factor in result.values():
            assert factor == 1.0


class TestCompetitionAnalysis(TestPredictiveSalesEngine):
    """Test competition analysis methods"""
    
    @pytest.mark.asyncio
    async def test_analyze_competition_success(self, engine, mock_manuscript):
        """Test successful competition analysis"""
        result = await engine._analyze_competition(mock_manuscript)
        
        assert isinstance(result, CompetitionAnalysis)
        assert result.competition_level in ['low', 'medium', 'high']
        assert isinstance(result.top_competitors, list)
        assert isinstance(result.market_gaps, list)
        assert isinstance(result.differentiation_opportunities, list)
    
    @pytest.mark.asyncio
    async def test_analyze_competition_different_categories(self, engine):
        """Test competition analysis for different categories"""
        categories = ['business', 'health', 'fiction', 'technology']
        
        for category in categories:
            manuscript = Mock()
            manuscript.category = category
            
            result = await engine._analyze_competition(manuscript)
            
            assert isinstance(result, CompetitionAnalysis)
            assert result.competition_level in ['low', 'medium', 'high']
    
    @pytest.mark.asyncio
    async def test_analyze_competition_error_handling(self, engine):
        """Test error handling in competition analysis"""
        # Test with manuscript that has no category attribute
        invalid_manuscript = Mock()
        # Don't set category attribute to trigger error handling

        result = await engine._analyze_competition(invalid_manuscript)

        assert isinstance(result, CompetitionAnalysis)
        assert result.competition_level == 'medium'  # Default


class TestHistoricalPerformance(TestPredictiveSalesEngine):
    """Test historical performance analysis"""
    
    @pytest.mark.asyncio
    async def test_get_historical_performance_success(self, engine):
        """Test successful historical performance retrieval"""
        result = await engine._get_historical_performance()
        
        assert isinstance(result, dict)
        assert 'avg_sales_30d' in result
        assert 'avg_revenue_30d' in result
        assert 'success_rate' in result
        assert 'top_performing_categories' in result
        assert 'seasonal_trends' in result
        
        assert result['avg_sales_30d'] > 0
        assert result['avg_revenue_30d'] > 0
        assert 0 <= result['success_rate'] <= 1
    
    @pytest.mark.asyncio
    async def test_get_historical_performance_error_handling(self, engine):
        """Test error handling in historical performance"""
        # The method has built-in error handling, so just test normal operation
        result = await engine._get_historical_performance()

        assert isinstance(result, dict)
        # Should return valid data
        assert 'avg_sales_30d' in result
        assert 'avg_revenue_30d' in result


class TestQualityAssessment(TestPredictiveSalesEngine):
    """Test quality assessment methods"""
    
    @pytest.mark.asyncio
    async def test_assess_average_quality_success(self, engine, mock_recent_releases):
        """Test successful quality assessment"""
        result = await engine._assess_average_quality(mock_recent_releases)
        
        assert isinstance(result, float)
        assert 0 <= result <= 100
    
    @pytest.mark.asyncio
    async def test_assess_average_quality_empty_releases(self, engine):
        """Test quality assessment with empty releases"""
        result = await engine._assess_average_quality([])
        
        assert isinstance(result, float)
        assert result == 50.0  # Default value
    
    @pytest.mark.asyncio
    async def test_assess_average_quality_no_ratings(self, engine):
        """Test quality assessment with releases without ratings"""
        releases_no_ratings = [
            {"title": "Book 1", "price": 3.99},
            {"title": "Book 2", "price": 4.99}
        ]
        
        result = await engine._assess_average_quality(releases_no_ratings)
        
        assert isinstance(result, float)
        assert result > 0  # Should use default rating


class TestPriceCompetitionAnalysis(TestPredictiveSalesEngine):
    """Test price competition analysis"""
    
    @pytest.mark.asyncio
    async def test_analyze_price_competition_success(self, engine, mock_recent_releases):
        """Test successful price competition analysis"""
        result = await engine._analyze_price_competition(mock_recent_releases)
        
        assert isinstance(result, str)
        assert result in ['low', 'medium', 'high']
    
    @pytest.mark.asyncio
    async def test_analyze_price_competition_high_competition(self, engine):
        """Test price competition with low prices (high competition)"""
        low_price_releases = [
            {"price": 1.99},
            {"price": 2.49},
            {"price": 2.99}
        ]
        
        result = await engine._analyze_price_competition(low_price_releases)
        
        assert result == 'high'  # Low prices = high competition
    
    @pytest.mark.asyncio
    async def test_analyze_price_competition_low_competition(self, engine):
        """Test price competition with high prices (low competition)"""
        high_price_releases = [
            {"price": 7.99},
            {"price": 8.99},
            {"price": 9.99}
        ]
        
        result = await engine._analyze_price_competition(high_price_releases)
        
        assert result == 'low'  # High prices = low competition


class TestSaturationCalculation(TestPredictiveSalesEngine):
    """Test saturation level calculation"""
    
    def test_calculate_saturation_level_high(self, engine):
        """Test high saturation calculation"""
        result = engine._calculate_saturation_level(6.0, 80.0)
        assert result == 'high'
    
    def test_calculate_saturation_level_medium(self, engine):
        """Test medium saturation calculation"""
        result = engine._calculate_saturation_level(4.0, 65.0)
        assert result == 'medium'
    
    def test_calculate_saturation_level_low(self, engine):
        """Test low saturation calculation"""
        result = engine._calculate_saturation_level(2.0, 50.0)
        assert result == 'low'
    
    def test_calculate_saturation_level_error_handling(self, engine):
        """Test error handling in saturation calculation"""
        # Test with edge case values that might cause issues
        result = engine._calculate_saturation_level(0.0, 0.0)
        assert result in ['low', 'medium', 'high']


class TestOpportunityGapIdentification(TestPredictiveSalesEngine):
    """Test opportunity gap identification"""
    
    @pytest.mark.asyncio
    async def test_identify_opportunity_gaps_low_competition(self, engine):
        """Test gap identification with low competition"""
        few_releases = [
            {"title": "Book 1", "rating": 4.0, "price": 3.99}
        ]  # Only 1 release = low competition
        
        result = await engine._identify_opportunity_gaps(few_releases)
        
        assert isinstance(result, list)
        assert 'low_competition_window' in result
    
    @pytest.mark.asyncio
    async def test_identify_opportunity_gaps_price_gap(self, engine):
        """Test gap identification with price gap"""
        expensive_releases = [
            {"title": "Book 1", "rating": 4.0, "price": 5.99},
            {"title": "Book 2", "rating": 4.2, "price": 6.99}
        ]
        
        result = await engine._identify_opportunity_gaps(expensive_releases)
        
        assert isinstance(result, list)
        assert 'budget_price_gap' in result
    
    @pytest.mark.asyncio
    async def test_identify_opportunity_gaps_quality_gap(self, engine):
        """Test gap identification with quality gap"""
        low_quality_releases = [
            {"title": "Book 1", "rating": 3.5, "price": 3.99},
            {"title": "Book 2", "rating": 3.8, "price": 4.99}
        ]
        
        result = await engine._identify_opportunity_gaps(low_quality_releases)
        
        assert isinstance(result, list)
        assert 'quality_improvement_opportunity' in result


class TestMockMLModel:
    """Test MockMLModel functionality"""

    @pytest.fixture
    def ml_model(self):
        """Create a MockMLModel instance"""
        return MockMLModel()

    @pytest.mark.asyncio
    async def test_mock_ml_model_predict_success(self, ml_model):
        """Test successful ML model prediction"""
        features = {
            'content_features': Mock(uniqueness_score=80.0),
            'market_saturation': Mock(publication_rate=5.0),
            'competition_level': 'medium'
        }

        result = await ml_model.predict(features)

        assert isinstance(result, PredictionResult)
        assert result.sales_30d > 0
        assert result.revenue_30d > 0
        assert isinstance(result.confidence, tuple)
        assert len(result.confidence) == 2
        assert result.optimal_price > 0
        assert 0 <= result.opportunity_score <= 100
        assert 0 <= result.success_probability <= 100

    @pytest.mark.asyncio
    async def test_mock_ml_model_predict_dict_features(self, ml_model):
        """Test ML model prediction with dictionary features"""
        features = {
            'content_features': {'uniqueness_score': 75},
            'market_saturation': {'publication_rate': 8},
            'competition_level': 'high'
        }

        result = await ml_model.predict(features)

        assert isinstance(result, PredictionResult)
        assert result.sales_30d > 0

    @pytest.mark.asyncio
    async def test_mock_ml_model_predict_low_competition(self, ml_model):
        """Test ML model prediction with low competition"""
        features = {
            'content_features': Mock(uniqueness_score=90.0),
            'market_saturation': Mock(publication_rate=2.0),
            'competition_level': 'low'
        }

        result = await ml_model.predict(features)

        # Low competition should result in higher sales
        assert result.sales_30d >= 100  # Base sales * 1.5 for low competition

    @pytest.mark.asyncio
    async def test_mock_ml_model_predict_high_competition(self, ml_model):
        """Test ML model prediction with high competition"""
        features = {
            'content_features': Mock(uniqueness_score=60.0),
            'market_saturation': Mock(publication_rate=15.0),
            'competition_level': 'high'
        }

        result = await ml_model.predict(features)

        # High competition should result in lower sales
        assert result.sales_30d <= 100  # Base sales * 0.7 for high competition

    @pytest.mark.asyncio
    async def test_mock_ml_model_predict_error_handling(self, ml_model):
        """Test ML model error handling"""
        # Test with invalid features that might cause errors
        features = {
            'content_features': None,
            'market_saturation': None,
            'competition_level': None
        }

        result = await ml_model.predict(features)

        # Should return conservative prediction on error
        assert isinstance(result, PredictionResult)
        assert result.sales_30d == 50  # Conservative default
        assert 'prediction_uncertainty' in result.risk_factors


class TestDataClasses:
    """Test data class functionality"""

    def test_market_conditions_creation(self):
        """Test MarketConditions creation"""
        conditions = MarketConditions(
            season="Q2",
            economic_indicators={"gdp": 3.0},
            trending_topics=["ai", "automation"],
            competition_level="high"
        )

        assert conditions.season == "Q2"
        assert conditions.economic_indicators["gdp"] == 3.0
        assert "ai" in conditions.trending_topics
        assert conditions.competition_level == "high"

    def test_sales_prediction_creation(self):
        """Test SalesPrediction creation"""
        prediction = SalesPrediction(
            predicted_sales_30_days=150,
            predicted_revenue_30_days=598.50,
            confidence_interval=(120, 180),
            peak_sales_period="week_2",
            recommended_price=3.99,
            market_opportunity_score=75.0,
            risk_factors=["market_saturation"],
            success_probability=80.0
        )

        assert prediction.predicted_sales_30_days == 150
        assert prediction.predicted_revenue_30_days == 598.50
        assert prediction.confidence_interval == (120, 180)
        assert prediction.peak_sales_period == "week_2"
        assert prediction.recommended_price == 3.99
        assert prediction.market_opportunity_score == 75.0
        assert "market_saturation" in prediction.risk_factors
        assert prediction.success_probability == 80.0

    def test_market_saturation_creation(self):
        """Test MarketSaturation creation"""
        saturation = MarketSaturation(
            publication_rate=5.2,
            average_quality=78.5,
            price_competition="medium",
            saturation_level="high",
            opportunity_gaps=["niche_market", "price_gap"]
        )

        assert saturation.publication_rate == 5.2
        assert saturation.average_quality == 78.5
        assert saturation.price_competition == "medium"
        assert saturation.saturation_level == "high"
        assert "niche_market" in saturation.opportunity_gaps

    def test_content_features_creation(self):
        """Test ContentFeatures creation"""
        features = ContentFeatures(
            word_count=5000,
            readability_score=85.0,
            topic_relevance=90.0,
            uniqueness_score=75.0,
            engagement_potential=80.0
        )

        assert features.word_count == 5000
        assert features.readability_score == 85.0
        assert features.topic_relevance == 90.0
        assert features.uniqueness_score == 75.0
        assert features.engagement_potential == 80.0

    def test_competition_analysis_creation(self):
        """Test CompetitionAnalysis creation"""
        analysis = CompetitionAnalysis(
            competition_level="medium",
            top_competitors=[{"title": "Competitor 1", "rating": 4.5}],
            market_gaps=["underserved_niche"],
            differentiation_opportunities=["unique_angle"]
        )

        assert analysis.competition_level == "medium"
        assert len(analysis.top_competitors) == 1
        assert "underserved_niche" in analysis.market_gaps
        assert "unique_angle" in analysis.differentiation_opportunities

    def test_prediction_result_creation(self):
        """Test PredictionResult creation"""
        result = PredictionResult(
            sales_30d=125,
            revenue_30d=498.75,
            confidence=(100, 150),
            peak_period="week_3",
            optimal_price=3.99,
            opportunity_score=82.5,
            risk_factors=["seasonal_variation"],
            success_probability=85.0
        )

        assert result.sales_30d == 125
        assert result.revenue_30d == 498.75
        assert result.confidence == (100, 150)
        assert result.peak_period == "week_3"
        assert result.optimal_price == 3.99
        assert result.opportunity_score == 82.5
        assert "seasonal_variation" in result.risk_factors
        assert result.success_probability == 85.0


class TestIntegrationScenarios(TestPredictiveSalesEngine):
    """Test integration scenarios and edge cases"""

    @pytest.mark.asyncio
    async def test_full_prediction_pipeline_business_book(self, engine):
        """Test full prediction pipeline for business book"""
        manuscript = Mock()
        manuscript.content = "A comprehensive guide to business productivity and success. " * 200
        manuscript.category = "business"
        manuscript.title = "The Ultimate Business Guide"

        market_conditions = MarketConditions(
            season="Q1",
            economic_indicators={"gdp_growth": 2.8},
            trending_topics=["productivity", "business", "success"],
            competition_level="medium"
        )

        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=[]):
            result = await engine.predict_book_performance(manuscript, market_conditions)

            assert isinstance(result, SalesPrediction)
            assert result.predicted_sales_30_days > 0
            assert result.predicted_revenue_30_days > 0
            assert result.success_probability > 0

    @pytest.mark.asyncio
    async def test_full_prediction_pipeline_fiction_book(self, engine):
        """Test full prediction pipeline for fiction book"""
        manuscript = Mock()
        manuscript.content = "An exciting adventure story with compelling characters. " * 300
        manuscript.category = "fiction"
        manuscript.title = "The Great Adventure"

        market_conditions = MarketConditions(
            season="Q4",
            economic_indicators={"consumer_spending": 105.2},
            trending_topics=["adventure", "fiction", "entertainment"],
            competition_level="high"
        )

        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=[]):
            result = await engine.predict_book_performance(manuscript, market_conditions)

            assert isinstance(result, SalesPrediction)
            # Fiction might have different prediction patterns
            assert result.predicted_sales_30_days > 0

    @pytest.mark.asyncio
    async def test_prediction_with_saturated_market(self, engine, mock_manuscript, sample_market_conditions):
        """Test prediction in saturated market conditions"""
        saturated_market_releases = [
            {
                "title": f"Business Book {i}",
                "rating": 4.0 + (i % 3) * 0.2,
                "price": 3.99 + (i % 2) * 1.0,
                "sales_rank": 50 + i * 10
            }
            for i in range(20)  # Many recent releases = saturated market
        ]

        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=saturated_market_releases):
            result = await engine.predict_book_performance(mock_manuscript, sample_market_conditions)

            assert isinstance(result, SalesPrediction)
            # Saturated market should affect predictions
            assert len(result.risk_factors) >= 0

    @pytest.mark.asyncio
    async def test_prediction_with_network_errors(self, engine, mock_manuscript, sample_market_conditions):
        """Test prediction handling network errors"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', side_effect=Exception("Network timeout")):
            result = await engine.predict_book_performance(mock_manuscript, sample_market_conditions)

            # Should still provide prediction despite network errors
            assert isinstance(result, SalesPrediction)
            assert result.predicted_sales_30_days > 0

    @pytest.mark.asyncio
    async def test_prediction_with_minimal_manuscript_data(self, engine, sample_market_conditions):
        """Test prediction with minimal manuscript data"""
        minimal_manuscript = Mock()
        minimal_manuscript.content = "Short content."
        minimal_manuscript.category = "unknown"
        minimal_manuscript.title = "Test"

        with patch.object(engine.market_scraper, 'get_kindle_new_releases', return_value=[]):
            result = await engine.predict_book_performance(minimal_manuscript, sample_market_conditions)

            assert isinstance(result, SalesPrediction)
            # Should handle minimal data gracefully
            assert result.predicted_sales_30_days > 0


class TestErrorHandlingAndEdgeCases(TestPredictiveSalesEngine):
    """Test error handling and edge cases"""

    @pytest.mark.asyncio
    async def test_market_saturation_analysis_network_error(self, engine):
        """Test market saturation analysis with network error"""
        with patch.object(engine.market_scraper, 'get_kindle_new_releases', side_effect=Exception("Connection failed")):
            result = await engine._analyze_market_saturation("business")

            assert isinstance(result, MarketSaturation)
            # Should return default values on error
            assert result.publication_rate >= 0

    @pytest.mark.asyncio
    async def test_content_analysis_with_none_content(self, engine):
        """Test content analysis with None content"""
        none_manuscript = Mock()
        none_manuscript.content = None

        result = await engine._analyze_content_features(none_manuscript)

        assert isinstance(result, ContentFeatures)
        assert result.word_count == 0

    @pytest.mark.asyncio
    async def test_seasonal_factors_with_invalid_category(self, engine):
        """Test seasonal factors with invalid category"""
        result = await engine._get_seasonal_factors(None)

        assert isinstance(result, dict)
        # Should return default factors
        for factor in result.values():
            assert factor == 1.0

    @pytest.mark.asyncio
    async def test_competition_analysis_with_missing_category(self, engine):
        """Test competition analysis with missing category"""
        no_category_manuscript = Mock()
        # Don't set category attribute

        result = await engine._analyze_competition(no_category_manuscript)

        assert isinstance(result, CompetitionAnalysis)
        assert result.competition_level == 'medium'  # Default

    @pytest.mark.asyncio
    async def test_quality_assessment_with_invalid_data(self, engine):
        """Test quality assessment with invalid data"""
        invalid_releases = [
            {"title": "Book 1"},  # Missing rating
            {"title": "Book 2", "rating": "invalid"},  # Invalid rating type
            {"title": "Book 3", "rating": None}  # None rating
        ]

        result = await engine._assess_average_quality(invalid_releases)

        assert isinstance(result, float)
        assert result >= 0

    @pytest.mark.asyncio
    async def test_price_competition_with_missing_prices(self, engine):
        """Test price competition with missing prices"""
        no_price_releases = [
            {"title": "Book 1"},  # Missing price
            {"title": "Book 2", "price": None},  # None price
            {"title": "Book 3", "price": "free"}  # Invalid price type
        ]

        result = await engine._analyze_price_competition(no_price_releases)

        assert isinstance(result, str)
        assert result in ['low', 'medium', 'high']


if __name__ == "__main__":
    pytest.main([__file__])
