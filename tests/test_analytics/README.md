# Analytics Tests

This directory contains comprehensive tests for the analytics components of the e-book publishing platform.

## Overview

The analytics tests cover two main components:

1. **Reader Behavior Analytics** (`test_reader_behavior.py`)
2. **Predictive Models** (`test_predictive_models.py`)

## Test Structure

### Reader Behavior Tests (`test_reader_behavior.py`)

Tests for the `ReaderBehaviorAnalyzer` class and related components:

- **Basic Functionality Tests**
  - Analyzer initialization
  - Reader engagement analysis
  - Error handling

- **Reading Pattern Analysis**
  - Session duration calculation
  - Reading speed categorization
  - Preferred reading time analysis
  - Engagement frequency metrics

- **Drop-off Analysis**
  - Critical drop-off point identification
  - Chapter-by-chapter drop-off rates
  - Recovery opportunity identification

- **Engagement Heatmap**
  - Chapter engagement scoring
  - Page engagement tracking
  - Time-based engagement patterns
  - Interaction hotspot identification

- **Sentiment Analysis**
  - Overall sentiment scoring
  - Chapter-by-chapter sentiment
  - Emotional peak detection
  - Satisfaction indicators

- **Completion Predictors**
  - Early engagement indicators
  - Risk factor identification
  - Positive signal detection
  - Completion probability modeling

- **Personalization Opportunities**
  - Content preference analysis
  - Optimal chapter length calculation
  - Engagement trigger identification

- **Helper Methods**
  - Session grouping
  - Reading speed calculation
  - Time analysis utilities

- **Data Classes**
  - Enum functionality
  - Data class creation and validation

### Predictive Models Tests (`test_predictive_models.py`)

Tests for the `PredictiveSalesEngine` class and related components:

- **Basic Functionality Tests**
  - Engine initialization
  - Book performance prediction
  - Error handling

- **Market Saturation Analysis**
  - Publication rate calculation
  - Quality assessment
  - Price competition analysis
  - Saturation level determination

- **Content Feature Analysis**
  - Word count analysis
  - Readability scoring
  - Topic relevance assessment
  - Uniqueness scoring

- **Seasonal Factor Analysis**
  - Category-specific seasonal patterns
  - Quarterly factor calculation
  - Unknown category handling

- **Competition Analysis**
  - Competition level assessment
  - Top competitor identification
  - Market gap analysis
  - Differentiation opportunities

- **Historical Performance**
  - Performance data retrieval
  - Success rate calculation
  - Trend analysis

- **Quality Assessment**
  - Average quality calculation
  - Rating analysis
  - Quality scoring

- **Price Competition**
  - Price level analysis
  - Competition intensity calculation
  - Market positioning

- **MockMLModel Tests**
  - Prediction functionality
  - Feature processing
  - Error handling
  - Competition level impact

- **Integration Scenarios**
  - Full prediction pipeline
  - Different book categories
  - Market condition variations
  - Error recovery

## Running Tests

### Run All Analytics Tests
```bash
python tests/test_analytics/run_analytics_tests.py
```

### Run Specific Test Suites
```bash
# Reader behavior tests only
python tests/test_analytics/run_analytics_tests.py reader

# Predictive models tests only
python tests/test_analytics/run_analytics_tests.py predictive

# Performance tests
python tests/test_analytics/run_analytics_tests.py performance

# Integration tests
python tests/test_analytics/run_analytics_tests.py integration
```

### Check Test Environment
```bash
python tests/test_analytics/run_analytics_tests.py check
```

### Using Pytest Directly
```bash
# Run all analytics tests
pytest tests/test_analytics/ -v

# Run specific test file
pytest tests/test_analytics/test_reader_behavior.py -v
pytest tests/test_analytics/test_predictive_models.py -v

# Run specific test class
pytest tests/test_analytics/test_reader_behavior.py::TestReaderBehaviorAnalyzer -v

# Run specific test method
pytest tests/test_analytics/test_reader_behavior.py::TestReaderBehaviorAnalyzer::test_analyzer_initialization -v

# Run with coverage
pytest tests/test_analytics/ --cov=app.analytics --cov-report=html
```

## Test Configuration

### Fixtures (`conftest.py`)

The test configuration provides shared fixtures:

- `mock_book_model` - Mock book objects
- `mock_user_model` - Mock user objects
- `mock_engagement_events` - Sample engagement data
- `mock_market_conditions` - Sample market conditions
- `mock_scraper_data` - Mock scraper responses
- `mock_amazon_scraper` - Mock Amazon scraper
- `sample_datetime` - Consistent test datetime

### Test Data Generators

- `generate_engagement_events()` - Generate test engagement events
- `generate_market_releases()` - Generate test market release data

## Test Categories

### Unit Tests
- Individual method testing
- Data class validation
- Error handling
- Edge cases

### Integration Tests
- Full pipeline testing
- Component interaction
- End-to-end scenarios

### Performance Tests
- Large data processing
- Memory usage
- Execution time
- Scalability

## Mock Objects

The tests use extensive mocking to isolate components:

- **Database Operations** - Mocked to avoid database dependencies
- **External APIs** - Mocked scraper responses
- **File System** - Mocked file operations
- **Network Requests** - Mocked HTTP requests

## Test Data

### Sample Engagement Events
- Page views with timing data
- Highlights and bookmarks
- Completion events
- Drop-off events
- Sentiment scores

### Sample Market Data
- Book releases with ratings
- Price information
- Sales rankings
- Publication dates

### Sample Manuscripts
- Various content lengths
- Different categories
- Multiple quality levels

## Assertions and Validations

Tests validate:

- **Data Types** - Correct return types
- **Value Ranges** - Scores within expected ranges
- **Required Fields** - All necessary data present
- **Business Logic** - Correct calculations and analysis
- **Error Handling** - Graceful failure handling

## Coverage Goals

- **Line Coverage**: >95%
- **Branch Coverage**: >90%
- **Function Coverage**: 100%

## Performance Benchmarks

- **Reader Analysis**: <2 seconds for 1000 events
- **Market Analysis**: <5 seconds for 100 releases
- **Prediction Generation**: <3 seconds per manuscript
- **Memory Usage**: <100MB for typical datasets

## Continuous Integration

Tests are designed to run in CI/CD environments:

- No external dependencies
- Deterministic results
- Fast execution
- Clear failure reporting

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure project root is in Python path
   - Check virtual environment activation

2. **Async Test Failures**
   - Verify asyncio event loop configuration
   - Check async/await usage

3. **Mock Failures**
   - Verify mock object setup
   - Check patch decorators

4. **Data Validation Errors**
   - Verify test data structure
   - Check data type consistency

### Debug Mode

Run tests with debug output:
```bash
pytest tests/test_analytics/ -v -s --tb=long
```

## Contributing

When adding new tests:

1. Follow existing naming conventions
2. Use appropriate fixtures
3. Include error handling tests
4. Add performance considerations
5. Update documentation

## Dependencies

- `pytest` - Test framework
- `pytest-asyncio` - Async test support
- `unittest.mock` - Mocking utilities
- `datetime` - Time handling
- `pathlib` - Path utilities
