#!/usr/bin/env python3
"""
Dedicated Test Runner for PydanticAI Agents
Runs all agent test suites with detailed reporting and coverage analysis.
Includes:
- Rich CLI summary
- HTML coverage report viewer
- JSON logging
- CLI options for test scope
"""

import os
import sys
import subprocess
import argparse
import time
import json
import webbrowser
from pathlib import Path
from typing import List, Dict, Any
from rich.console import Console
from rich.table import Table


console = Console()


class AgentTestRunner:
    def __init__(self):
        self.project_root = Path(__file__).resolve().parent.parent.parent
        self.test_agents_dir = self.project_root / "tests/test_agents"
        self.results: Dict[str, Dict[str, Any]] = {}
        self.result_json = self.project_root / "test-results/results.json"

    def run_command(self, command: List[str], description: str) -> Dict[str, Any]:
        console.rule(f"[bold cyan]Running: {description}")
        start = time.time()
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,
            )
            elapsed = time.time() - start
            return {
                "description": description,
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "execution_time": round(elapsed, 2),
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
            }
        except Exception as e:
            return {
                "description": description,
                "success": False,
                "exit_code": -1,
                "execution_time": 0,
                "stdout": "",
                "stderr": str(e),
            }

    def run_pytest_suite(self, label: str, args: List[str]) -> None:
        command = ["poetry", "run", "pytest"] + args
        self.results[label] = self.run_command(command, label)

    def run_agent_functionality_tests(self):
        self.run_pytest_suite(
            "Agent Functionality Tests",
            [
                "tests/test_agents/test_pydantic_ai_agents.py",
                "-v",
                "--tb=short",
                "--cov=app.agents",
                "--cov-report=html:htmlcov/agents",
                "--cov-report=term-missing",
                "--junit-xml=test-results/agent-functionality-tests.xml",
                "-m",
                "not slow",
            ],
        )

    def run_agent_tools_tests(self):
        self.run_pytest_suite(
            "Agent Tools Tests",
            [
                "tests/test_agents/test_pydantic_ai_tools.py",
                "-v",
                "--tb=short",
                "--cov=app.agents.pydantic_ai_tools",
                "--cov-report=html:htmlcov/agent-tools",
                "--cov-report=term-missing",
                "--junit-xml=test-results/agent-tools-tests.xml",
            ],
        )

    def run_agent_performance_tests(self):
        self.run_pytest_suite(
            "Agent Performance Tests",
            [
                "tests/test_agents/test_performance_load.py",
                "-v",
                "--tb=short",
                "--junit-xml=test-results/agent-performance-tests.xml",
                "-s",
            ],
        )

    def run_individual_agent_tests(self, agent_name: str):
        self.run_pytest_suite(
            f"{agent_name} Tests",
            [
                "tests/test_agents/",
                "-v",
                "--tb=short",
                "-k",
                agent_name,
                "--junit-xml",
                f"test-results/{agent_name}-tests.xml",
            ],
        )

    def run_integration_tests(self):
        self.run_pytest_suite(
            "Agent Integration Tests",
            [
                "tests/test_agents/",
                "-v",
                "--tb=short",
                "-m",
                "integration",
                "--junit-xml=test-results/agent-integration-tests.xml",
            ],
        )

    def run_error_handling_tests(self):
        self.run_pytest_suite(
            "Agent Error Handling Tests",
            [
                "tests/test_agents/",
                "-v",
                "--tb=short",
                "-k",
                "error or Error or exception or Exception",
                "--junit-xml=test-results/agent-error-handling-tests.xml",
            ],
        )

    def run_supabase_tests(self):
        self.run_pytest_suite(
            "Supabase Database Tests",
            [
                "tests/test_agents/",
                "-v",
                "--tb=short",
                "-m",
                "supabase",
                "--junit-xml=test-results/agent-supabase-tests.xml",
            ],
        )

    def run_mock_tests(self):
        self.run_pytest_suite(
            "Mock-based Tests",
            [
                "tests/test_agents/",
                "-v",
                "--tb=short",
                "-m",
                "not supabase",
                "--junit-xml=test-results/agent-mock-tests.xml",
            ],
        )

    def setup_environment(self):
        os.environ["PYTHONPATH"] = str(self.project_root)
        os.environ["TESTING"] = "true"
        os.environ["AGENT_TESTING"] = "true"
        (self.project_root / "test-results").mkdir(exist_ok=True)
        (self.project_root / "htmlcov").mkdir(exist_ok=True)

    def summarize(self, open_html=False):
        console.rule("[bold green]Agent Test Execution Summary")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Suite")
        table.add_column("Status")
        table.add_column("Time (s)", justify="right")

        for label, result in self.results.items():
            status = "✅ PASSED" if result["success"] else "❌ FAILED"
            table.add_row(label, status, str(result["execution_time"]))

        console.print(table)

        with open(self.result_json, "w") as f:
            json.dump(self.results, f, indent=2)
        print(f"\n🧪 Saved JSON: {self.result_json}")

        if open_html:
            html_path = self.project_root / "htmlcov" / "agents" / "index.html"
            if html_path.exists():
                print(f"📊 Opening coverage report: {html_path}")
                webbrowser.open(html_path.as_uri())

    def check_dependencies(self):
        console.print("[blue]Checking dependencies...")
        try:
            subprocess.run(["poetry", "run", "pytest", "--version"], check=True)
        except subprocess.CalledProcessError:
            console.print("[red]❌ pytest not found via poetry")
            return False
        return True

    def main(self):
        parser = argparse.ArgumentParser()
        parser.add_argument(
            "--suite",
            choices=[
                "functionality",
                "tools",
                "performance",
                "integration",
                "error-handling",
                "supabase",
                "mock",
                "all",
            ],
            default="all",
        )
        parser.add_argument("--agent", type=str)
        parser.add_argument("--html-report", action="store_true")
        args = parser.parse_args()

        if not self.check_dependencies():
            sys.exit(1)

        self.setup_environment()

        if args.agent:
            self.run_individual_agent_tests(args.agent)
        elif args.suite == "functionality":
            self.run_agent_functionality_tests()
        elif args.suite == "tools":
            self.run_agent_tools_tests()
        elif args.suite == "performance":
            self.run_agent_performance_tests()
        elif args.suite == "integration":
            self.run_integration_tests()
        elif args.suite == "error-handling":
            self.run_error_handling_tests()
        elif args.suite == "supabase":
            self.run_supabase_tests()
        elif args.suite == "mock":
            self.run_mock_tests()
        elif args.suite == "all":
            self.run_agent_functionality_tests()
            self.run_agent_tools_tests()
            self.run_integration_tests()
            self.run_error_handling_tests()
            self.run_agent_performance_tests()
            if os.getenv("SUPABASE_URL") and os.getenv("SUPABASE_SERVICE_KEY"):
                self.run_supabase_tests()
            else:
                print("⚠️  Skipping Supabase tests — missing credentials")

        self.summarize(open_html=args.html_report)

        if any(not r["success"] for r in self.results.values()):
            sys.exit(1)


if __name__ == "__main__":
    AgentTestRunner().main()
