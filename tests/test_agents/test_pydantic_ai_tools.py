"""
Comprehensive Test Suite for PydanticAI Tools
Tests all tool functions with full coverage of functionality, error handling, and edge cases.
"""

import pytest
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from datetime import datetime
import asyncio

# Import all tools (updated for Supabase)
from app.agents.pydantic_ai_tools import (
    get_user_books,
    save_book_draft,
    get_book_by_id,
    get_book_performance,
    record_user_feedback,
    get_sales_data,
    get_books_by_category,
    save_trend_analysis,
    get_user_performance_summary,
    store_user_api_key,
    get_user_api_key,
    get_api_key_from_supabase,
    scrape_bestselling_books,
    scrape_reddit_discussions,
    analyze_competitor_books,
    analyze_content_quality,
    extract_keywords,
    research_market_trends,
    validate_book_concept,
    fetch_kdp_sales_summary,
    list_author_kdp_books,
    # Import the result models
    ContentQualityResult,
    ConceptValidationResult
)

from app.agents.pydantic_ai_base import (
    DatabaseDependencies,
    ScrapingDependencies,
    AIModelDependencies,
    TrendAnalysisDependencies
)

# RunContext import removed as it's not used in tests


class TestDatabaseTools:
    """Test Supabase database-related tools"""
    
    @pytest.mark.asyncio
    @pytest.mark.supabase
    async def test_get_user_books_success(self, test_user, test_book):
        """Test successful retrieval of user books using real Supabase database"""
        # Create mock context
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        # Call the function with real database
        result = await get_user_books(mock_ctx, test_user["id"])
        
        # Verify results
        assert isinstance(result, list)
        assert len(result) >= 1  # Should have at least our test book
        
        # Find our test book in the results
        test_book_found = any(book['id'] == test_book['id'] for book in result)
        assert test_book_found, "Test book should be found in results"
    
    @pytest.mark.asyncio 
    @patch('app.models.supabase_models.get_book_model')
    async def test_get_user_books_empty_result(self, mock_get_book_model):
        """Test retrieval when user has no books"""
        mock_book_model = AsyncMock()
        mock_get_book_model.return_value = mock_book_model
        mock_book_model.get_user_books.return_value = []
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await get_user_books(mock_ctx, "empty-user-id")
        
        assert isinstance(result, list)
        assert len(result) == 0
    
    @pytest.mark.asyncio
    @patch('app.models.supabase_models.get_book_model')
    async def test_get_user_books_database_error(self, mock_get_book_model):
        """Test handling of Supabase database errors"""
        mock_book_model = AsyncMock()
        mock_get_book_model.return_value = mock_book_model
        mock_book_model.get_user_books.side_effect = Exception("Supabase connection failed")
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await get_user_books(mock_ctx, "error-user-id")
        
        assert isinstance(result, list)
        assert len(result) == 0  # Should return empty list on error

    @pytest.mark.asyncio
    @patch('app.models.supabase_models.get_book_model')
    async def test_save_book_draft_success(self, mock_get_book_model):
        """Test successful book draft saving"""
        mock_book_model = AsyncMock()
        mock_get_book_model.return_value = mock_book_model
        
        mock_book = {
            "id": "new-book-uuid",
            "title": "New Book",
            "status": "draft",
            "word_count": 1500
        }
        mock_book_model.create_book.return_value = mock_book
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await save_book_draft(
            mock_ctx, 
            "New Book", 
            "This is the content", 
            "business", 
            "user-id"
        )
        
        assert result.book_id == "new-book-uuid"
        assert result.title == "New Book"
        assert result.status == "draft"
        assert result.word_count == 1500

    @pytest.mark.asyncio
    @patch('app.models.supabase_models.get_user_model')
    async def test_store_user_api_key_success(self, mock_get_user_model):
        """Test successful API key storage"""
        mock_user_model = AsyncMock()
        mock_get_user_model.return_value = mock_user_model
        mock_user_model.store_user_api_key.return_value = True
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await store_user_api_key(mock_ctx, "user-id", "rainforest_api", "test-key")
        
        assert result is True
        mock_user_model.store_user_api_key.assert_called_once_with("user-id", "rainforest_api", "test-key")

    @pytest.mark.asyncio
    @patch('app.models.supabase_models.get_user_model')  
    async def test_get_api_key_from_supabase_success(self, mock_get_user_model):
        """Test successful API key retrieval from Supabase"""
        mock_user_model = AsyncMock()
        mock_get_user_model.return_value = mock_user_model
        mock_user_model.get_user_api_key.return_value = "test-api-key"
        
        mock_deps = Mock()
        
        result = await get_api_key_from_supabase(mock_deps, "rainforest_api", "user-id")
        
        assert result == "test-api-key"
        mock_user_model.get_user_api_key.assert_called_once_with("user-id", "rainforest_api")


class TestScrapingTools:
    """Test web scraping tools with Supabase caching"""
    
    @pytest.mark.asyncio
    @patch('app.models.supabase_models.get_market_data_model')
    @patch('app.agents.pydantic_ai_tools.get_api_key_from_supabase')
    async def test_scrape_amazon_bestsellers_success(self, mock_get_api_key, mock_get_market_model):
        """Test successful Amazon bestsellers scraping with Supabase"""
        # Mock API key retrieval
        mock_get_api_key.return_value = "test-rainforest-api-key"
        
        # Mock market data model for caching
        mock_market_model = AsyncMock()
        mock_get_market_model.return_value = mock_market_model
        mock_market_model.get_cached_data.return_value = None  # No cache hit
        mock_market_model.store_scraped_data.return_value = {"id": "cache-id"}
        mock_market_model.cleanup_expired_data.return_value = 5
        
        # Mock HTTP response
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "bestsellers": [
                    {
                        "title": "Test Business Book",
                        "asin": "B123456789",
                        "price": {"value": 9.99},
                        "rating": 4.5,
                        "ratings_total": 1200,
                        "link": "https://amazon.com/dp/B123456789"
                    }
                ]
            }
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.__aexit__.return_value = None
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance
            
            mock_ctx = Mock()
            mock_ctx.deps = Mock()
            mock_ctx.deps.db_deps = Mock()
            
            result = await scrape_bestselling_books(mock_ctx, 'business', limit=10)
            
            assert isinstance(result, list)
            assert len(result) <= 10
            
            if result:  # If data is returned
                book = result[0]
                assert 'title' in book
                assert 'asin' in book
                assert 'rank' in book
                assert 'price' in book
    

    @pytest.mark.asyncio
    @patch('app.agents.pydantic_ai_tools.get_api_key_from_supabase')
    async def test_scrape_amazon_no_api_key(self, mock_get_api_key):
        """Test Amazon scraping when no API key is found"""
        mock_get_api_key.return_value = None
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        mock_ctx.deps.db_deps = Mock()
        
        result = await scrape_bestselling_books(mock_ctx, 'business')
        
        assert isinstance(result, list)
        assert len(result) == 0  # Should return empty list when no API key
    
    @pytest.mark.asyncio
    @patch('app.agents.pydantic_ai_tools.get_api_key_from_supabase')
    async def test_scrape_reddit_trends_success(self, mock_get_api_key):
        """Test successful Reddit trends scraping with Supabase API keys"""
        # Mock Reddit API credentials
        mock_get_api_key.side_effect = lambda deps, service, user_id: {
            "reddit_client_id": "test_client_id",
            "reddit_client_secret": "test_client_secret"
        }.get(service)
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        mock_ctx.deps.db_deps = Mock()
        
        with patch('asyncpraw.Reddit') as mock_reddit:
            mock_subreddit = Mock()
            mock_submission = Mock()
            mock_submission.title = "Test Post"
            mock_submission.score = 100
            mock_submission.num_comments = 25
            mock_submission.url = "https://reddit.com/test"
            
            mock_subreddit.search.return_value.__aiter__ = lambda: iter([mock_submission])
            mock_reddit.return_value.subreddit.return_value = mock_subreddit
            
            keywords = ['productivity', 'business']
            result = await scrape_reddit_discussions(mock_ctx, keywords, limit=5)
            
            assert isinstance(result, list)
            
            if result:  # If data is returned
                post = result[0]
                assert 'keyword' in post
                assert 'title' in post
                assert 'upvotes' in post
                assert 'comments' in post
    
    @pytest.mark.asyncio
    @patch('app.agents.pydantic_ai_tools.get_api_key_from_supabase')
    async def test_scrape_reddit_trends_no_credentials(self, mock_get_api_key):
        """Test Reddit scraping when no API credentials found"""
        mock_get_api_key.return_value = None
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        mock_ctx.deps.db_deps = Mock()
        
        result = await scrape_reddit_discussions(mock_ctx, ['business'], limit=10)
        
        assert isinstance(result, list)
        assert len(result) == 0  # Should return empty list when no credentials
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_books_success(self):
        """Test successful competitor analysis"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = {
                "items": [
                    {
                        "volumeInfo": {
                            "title": "Business Book 1",
                            "authors": ["Author 1"],
                            "description": "A great business book",
                            "averageRating": 4.5
                        }
                    }
                ]
            }
            
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.__aexit__.return_value = None
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance
            
            result = await analyze_competitor_books(mock_ctx, 'business')
            
            assert isinstance(result, list)
            if result:
                book = result[0]
                assert 'title' in book
                assert 'authors' in book
                assert 'description' in book
                assert 'averageRating' in book
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_books_error_handling(self):
        """Test error handling in competitor analysis"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.side_effect = Exception("Network error")
            mock_client.return_value = mock_client_instance
            
            result = await analyze_competitor_books(mock_ctx, 'business')
            
            # Should handle gracefully and return empty list
            assert isinstance(result, list)
            assert len(result) == 0


class TestContentAnalysisTools:
    """Test content analysis tools"""
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_success(self):
        """Test successful content quality analysis"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        content = """
        This is a sample piece of content for testing quality analysis.
        It contains multiple sentences and paragraphs to evaluate.
        The content should be analyzed for readability, engagement, and value.
        """
        
        result = await analyze_content_quality(mock_ctx, content)
        
        assert isinstance(result, ContentQualityResult)
        assert hasattr(result, 'overall_score')
        assert hasattr(result, 'criteria_scores') 
        assert hasattr(result, 'word_count')
        assert hasattr(result, 'recommendations')
        
        # Check score ranges
        assert 0 <= result.overall_score <= 100
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_with_criteria(self):
        """Test content analysis with custom criteria"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        content = "Short content for testing."
        criteria = {'readability': 0.3, 'engagement': 0.4, 'originality': 0.3}
        
        result = await analyze_content_quality(mock_ctx, content, criteria)
        
        assert isinstance(result, ContentQualityResult)
        assert hasattr(result, 'criteria_scores')
        
        # Should include the provided criteria
        assert isinstance(result.criteria_scores, dict)
        assert len(result.criteria_scores) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_empty_content(self):
        """Test content analysis with empty content"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await analyze_content_quality(mock_ctx, "")
        
        assert isinstance(result, ContentQualityResult)
        # Should handle empty content gracefully
        assert result.word_count == 0
    
    @pytest.mark.asyncio
    async def test_extract_keywords_success(self):
        """Test successful keyword extraction"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        content = """
        Productivity and time management are essential skills for success.
        Effective productivity techniques include goal setting, prioritization,
        and time blocking. These productivity methods help improve efficiency
        and achieve better results in both personal and professional settings.
        """
        
        result = await extract_keywords(mock_ctx, content, min_freq=2)
        
        assert isinstance(result, list)
        
        # Should extract relevant keywords
        if result:
            assert all(isinstance(keyword, str) for keyword in result)
            # Should include 'productivity' as it appears frequently
            assert any('productivity' in keyword.lower() for keyword in result)
    
    @pytest.mark.asyncio
    async def test_extract_keywords_custom_min_freq(self):
        """Test keyword extraction with custom minimum frequency"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        content = "business success productivity efficiency management leadership business success"
        
        result = await extract_keywords(mock_ctx, content, min_freq=2)
        
        assert isinstance(result, list)
        # Should only include words that appear at least 2 times
        if result:
            assert 'business' in result
            assert 'success' in result
    
    @pytest.mark.asyncio
    async def test_extract_keywords_short_content(self):
        """Test keyword extraction with very short content"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await extract_keywords(mock_ctx, "test", min_freq=1)
        
        assert isinstance(result, list)
        # Should handle short content gracefully
        if result:
            assert 'test' in result


class TestMarketResearchTools:
    """Test market research and analysis tools"""
    
    @pytest.mark.asyncio
    async def test_research_market_trends_success(self):
        """Test successful market trends research"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.__aexit__.return_value = None
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance
            
            result = await research_market_trends(mock_ctx, 'business')
            
            assert isinstance(result, dict)
            assert 'category' in result
            assert 'trends' in result
            assert 'forecast' in result
            
            # Check data types
            assert isinstance(result['trends'], list)
            assert result['category'] == 'business'
    
    @pytest.mark.asyncio
    async def test_research_market_trends_error_handling(self):
        """Test market research error handling"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.side_effect = Exception("Network error")
            mock_client.return_value = mock_client_instance
            
            result = await research_market_trends(mock_ctx, 'fiction')
            
            assert isinstance(result, dict)
            assert 'error' in result
    
    @pytest.mark.asyncio
    async def test_validate_book_concept_success(self):
        """Test successful book concept validation"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        result = await validate_book_concept(
            mock_ctx,
            'The Ultimate Productivity Guide',
            'business'
        )
        
        assert isinstance(result, ConceptValidationResult)
        assert hasattr(result, 'viability_score')
        assert hasattr(result, 'market_saturation')
        assert hasattr(result, 'competition_level')
        assert hasattr(result, 'recommendations')
        
        # Check score range
        assert 0 <= result.viability_score <= 100
    
    @pytest.mark.asyncio
    async def test_validate_book_concept_edge_cases(self):
        """Test book concept validation with edge cases"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        # Test with empty title
        result_empty = await validate_book_concept(mock_ctx, '', 'business')
        assert isinstance(result_empty, ConceptValidationResult)
        
        # Test with very long title
        long_title = 'A' * 500
        result_long = await validate_book_concept(mock_ctx, long_title, 'fiction')
        assert isinstance(result_long, ConceptValidationResult)


class TestErrorHandlingAndEdgeCases:
    """Test error handling and edge cases across all tools"""
    
    @pytest.mark.asyncio
    @patch('app.agents.pydantic_ai_tools.get_api_key_from_supabase')
    async def test_network_timeout_handling(self, mock_get_api_key):
        """Test handling of network timeouts"""
        mock_get_api_key.return_value = None  # No API key = graceful failure
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        mock_ctx.deps.db_deps = Mock()
        
        # Should handle timeout gracefully
        result = await scrape_bestselling_books(mock_ctx, 'business')
        assert isinstance(result, list)
        assert len(result) == 0  # No API key = empty results
    
    @pytest.mark.asyncio
    async def test_invalid_input_handling(self):
        """Test handling of invalid inputs"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        # Test with empty string content (instead of None)
        result = await analyze_content_quality(mock_ctx, "")
        assert isinstance(result, ContentQualityResult)
        assert result.word_count == 0
    
    @pytest.mark.asyncio
    async def test_large_data_processing(self):
        """Test processing of large data sets"""
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        # Create large content
        large_content = "word " * 10000  # 10,000 words
        
        result = await extract_keywords(mock_ctx, large_content, min_freq=100)
        
        assert isinstance(result, list)
        # Should extract 'word' as it appears frequently
        if result:
            assert 'word' in result
    
    @pytest.mark.asyncio
    async def test_concurrent_tool_execution(self):
        """Test concurrent execution of multiple tools"""
        import asyncio
        
        mock_ctx = Mock()
        mock_ctx.deps = Mock()
        
        # Execute multiple tools concurrently
        tasks = [
            analyze_content_quality(mock_ctx, "test content 1"),
            analyze_content_quality(mock_ctx, "test content 2"),
            extract_keywords(mock_ctx, "keyword test content"),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        assert len(results) == 3
        # All should complete without exceptions
        assert all(not isinstance(result, Exception) for result in results)
        
        # Check result types
        assert isinstance(results[0], ContentQualityResult)
        assert isinstance(results[1], ContentQualityResult)
        assert isinstance(results[2], list)


if __name__ == '__main__':
    pytest.main([__file__, '-v', '--tb=short', '--cov=app.agents.pydantic_ai_tools'])
