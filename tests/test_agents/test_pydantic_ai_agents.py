"""
Comprehensive Test Suite for PydanticAI Agents
Tests all 13 agents with full coverage of functionality, error handling, and edge cases.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from app.agents.pydantic_ai_base import (
    DatabaseDependencies,
    AIModelDependencies,
    ScrapingDependencies,
    ManuscriptDependencies,
    agent_registry,
)

from app.agents.pydantic_ai_common import ExecutionStatus
from app.agents.pydantic_ai_manager import (
    PydanticAIAgentManager,
    agent_manager,
    execute_agent,
    execute_workflow,
    get_agent_status,
)

from app.agents.pydantic_ai_manuscript_generator import generate_manuscript
from app.agents.pydantic_ai_trend_analyzer import analyze_market_trends
from app.agents.pydantic_ai_sales_monitor import monitor_sales_performance
from app.agents.pydantic_ai_cover_designer import design_book_cover
from app.agents.pydantic_ai_kdp_uploader import upload_to_kdp
from app.agents.pydantic_ai_additional_agents import (
    ResearchResult,
    research_topic,
    personalize_content,
    generate_multimodal_content,
)


@pytest.mark.asyncio
class TestAgentRegistration:
    def test_agent_registry_is_populated(self):
        agents = agent_registry.list_agents()
        assert isinstance(agents, list)
        assert len(agents) >= 9

    def test_registry_lookup(self):
        for name in [
            "manuscript_generator",
            "trend_analyzer",
            "sales_monitor",
            "cover_designer",
            "kdp_uploader",
            "research_assistant",
        ]:
            agent = agent_registry.get_agent(name)
            assert agent is not None


@pytest.mark.asyncio
class TestManager:
    def test_agent_manager_ready(self):
        assert isinstance(agent_manager, PydanticAIAgentManager)

    async def test_get_status_structure(self):
        status = get_agent_status()
        assert "registered_agents" in status
        assert "total_executions" in status


@pytest.mark.asyncio
class TestWorkflow:
    async def test_basic_workflow_success(self):
        with patch(
            "app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent",
            new_callable=AsyncMock,
        ) as mock_exec:
            mock_exec.return_value.status = ExecutionStatus.SUCCESS
            mock_exec.return_value.data = {"result": "ok"}

            result = await execute_workflow(
                [
                    {
                        "agent_name": "trend_analyzer",
                        "task_data": {"categories": ["business"]},
                    },
                    {
                        "agent_name": "manuscript_generator",
                        "task_data": {"style": "pro"},
                    },
                ]
            )

            assert len(result) == 2
            assert all(r.status == ExecutionStatus.SUCCESS for r in result)


@pytest.mark.asyncio
class TestAgents:
    async def test_manuscript_generator_success(self):
        with patch(
            "app.agents.pydantic_ai_manuscript_generator.manuscript_agent.run",
            new_callable=AsyncMock,
        ) as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {
                "manuscript": {"title": "Test", "content": "Some content"},
                "word_count": 8000,
            }
            mock_run.return_value = mock_result
            result = await generate_manuscript(trend_data={"category": "test"})
            assert result.status == ExecutionStatus.SUCCESS

    async def test_trend_analyzer_success(self):
        with patch(
            "app.agents.pydantic_ai_trend_analyzer.trend_analyzer.run",
            new_callable=AsyncMock,
        ) as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {
                "market_analysis": {"trending_categories": ["AI"]},
                "recommendations": ["write more"],
            }
            mock_run.return_value = mock_result
            result = await analyze_market_trends(categories=["tech"])
            assert result.status == ExecutionStatus.SUCCESS

    async def test_upload_to_kdp_success(self):
        with patch(
            "app.agents.pydantic_ai_kdp_uploader.upload_to_kdp", new_callable=AsyncMock
        ) as mock_upload:
            mock_result = Mock()
            mock_result.status = ExecutionStatus.SUCCESS
            mock_result.data = {"upload_status": "success"}
            mock_upload.return_value = mock_result

            result = await upload_to_kdp(book_data={}, file_paths={})
            assert result.status == ExecutionStatus.SUCCESS
            assert (
                isinstance(result.data, dict)
                and result.data.get("upload_status") == "success"
            )


@pytest.mark.asyncio
class TestErrorCases:
    async def test_missing_agent_fails(self):
        result = await execute_agent("nonexistent", {"test": "value"})
        assert result.status == ExecutionStatus.ERROR

    async def test_agent_run_exception(self):
        with patch(
            "app.agents.pydantic_ai_manuscript_generator.manuscript_agent.run",
            new_callable=AsyncMock,
        ) as mock_run:
            mock_run.side_effect = Exception("fail")
            result = await generate_manuscript(trend_data={})
            assert result.status == ExecutionStatus.ERROR


@pytest.mark.asyncio
class TestConcurrent:
    async def test_concurrent_execution_success(self):
        with patch(
            "app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent",
            new_callable=AsyncMock,
        ) as mock_exec:
            mock_exec.return_value.status = ExecutionStatus.SUCCESS
            tasks = [
                execute_agent("trend_analyzer", {"categories": ["tech"]})
                for _ in range(10)
            ]
            results = await asyncio.gather(*tasks)
            assert all(r.status == ExecutionStatus.SUCCESS for r in results)


@pytest.mark.asyncio
async def test_monitor_sales_performance_success():
    with patch("app.agents.pydantic_ai_sales_monitor.sales_monitor.run") as mock_run:
        mock_result = Mock()
        mock_result.output.model_dump.return_value = {
            "performance_metrics": {"total_sales": 100},
            "insights": {"key_insights": ["strong Q1"]},
        }
        mock_run.return_value = mock_result

        result = await monitor_sales_performance(date_range="last_30_days")
        assert result.status.value == "success"
        assert result.data is not None and "performance_metrics" in result.data


@pytest.mark.asyncio
async def test_design_book_cover_success():
    with patch("app.agents.pydantic_ai_cover_designer.cover_designer.run") as mock_run:
        mock_result = Mock()
        mock_result.output.model_dump.return_value = {
            "design_concept": {"theme": "modern"},
            "design_files": {"thumbnail": "thumb.jpg"},
        }
        mock_run.return_value = mock_result

        result = await design_book_cover(
            book_title="Test Book", author_name="Author", genre="business"
        )
        assert result.status.value == "success"
        assert result.data is not None and "design_concept" in result.data


@pytest.mark.asyncio
async def test_research_topic_success():
    with patch(
        "app.agents.pydantic_ai_additional_agents.research_assistant.run"
    ) as mock_run:
        mock_result = Mock()
        mock_result.output.model_dump.return_value = {
            "research_findings": {"topic_viability": 85}
        }
        mock_run.return_value = mock_result

        result = await research_topic(topic="AI")
        assert result.status.value == "success"
        assert result.data is not None and "research_findings" in result.data


@pytest.mark.asyncio
async def test_personalize_content_success():
    with patch(
        "app.agents.pydantic_ai_additional_agents.personalization_engine.run"
    ) as mock_run:
        mock_result = Mock()
        mock_result.output.model_dump.return_value = {
            "personalized_content": {"tone": "professional"}
        }
        mock_run.return_value = mock_result

        result = await personalize_content(
            content="test content", user_profile={"role": "developer"}
        )
        assert result.status.value == "success"
        assert result.data is not None and "personalized_content" in result.data


@pytest.mark.asyncio
async def test_generate_multimodal_content_success():
    with patch(
        "app.agents.pydantic_ai_additional_agents.multimodal_generator.run"
    ) as mock_run:
        mock_result = Mock()
        mock_result.output.model_dump.return_value = {"text_content": "sample text"}
        mock_run.return_value = mock_result

        result = await generate_multimodal_content(content_brief="test brief")
        assert result.status.value == "success"
        assert result.data is not None and "text_content" in result.data


class TestDependencyClasses:
    def test_database_dependencies(self):
        deps = DatabaseDependencies(user_id="test-user-123")
        assert deps.user_id == "test-user-123"

    def test_ai_model_dependencies(self):
        deps = AIModelDependencies(
            openai_api_key="test-key",
            preferred_model="gpt-4",
            temperature=0.8,
            max_tokens=1024,
        )
        assert deps.openai_api_key == "test-key"
        assert deps.preferred_model == "gpt-4"
        assert deps.temperature == 0.8
        assert deps.max_tokens == 1024

    def test_scraping_dependencies(self):
        deps = ScrapingDependencies(headless=True)
        assert deps.headless is True
        assert deps.amazon_scraper is not None
        assert deps.reddit_scraper is not None

    def test_manuscript_dependencies(self):
        db = DatabaseDependencies(user_id="abc")
        ai = AIModelDependencies(openai_api_key="key")
        deps = ManuscriptDependencies(
            db_deps=db,
            ai_deps=ai,
            target_length=8000,
            style="professional",
            target_audience="general",
            output_formats=["docx", "pdf"],
        )
        assert deps.db_deps == db
        assert deps.ai_deps == ai
        assert deps.target_length == 8000
        assert deps.style == "professional"
        assert deps.output_formats == ["docx", "pdf"]


if __name__ == "__main__":
    pytest.main(
        [__file__, "-v", "--tb=short", "--cov=app.agents", "--cov-report=term-missing"]
    )
