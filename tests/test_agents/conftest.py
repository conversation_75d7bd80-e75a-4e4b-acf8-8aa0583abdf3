"""
Pytest configuration and fixtures specifically for PydanticAI agent testing
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, patch
from typing import Dict, Any, Generator

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# Import test dependencies
from app.agents.pydantic_ai_base import (
    DatabaseDependencies,
    AIModelDependencies,
    ScrapingDependencies,
    ManuscriptDependencies,
    TrendAnalysisDependencies,
    SalesMonitorDependencies,
    CoverDesignDependencies,
    KDPUploadDependencies,
    agent_registry
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_database_deps() -> DatabaseDependencies:
    """Provide mock database dependencies"""
    return DatabaseDependencies(user_id=str(123))


@pytest.fixture
def mock_ai_deps() -> AIModelDependencies:
    """Provide mock AI model dependencies"""
    return AIModelDependencies(
        openai_api_key="test_api_key",
        preferred_model="gpt-4",
        temperature=0.7,
        max_tokens=2000,
    )


@pytest.fixture
def mock_scraping_deps() -> ScrapingDependencies:
    """Provide mock scraping dependencies"""
    return ScrapingDependencies(
        headless=True,
        amazon_scraper=Mock(),
        reddit_scraper=Mock()
        
    )


@pytest.fixture
def mock_manuscript_deps(mock_database_deps, mock_ai_deps) -> ManuscriptDependencies:
    """Provide mock manuscript generation dependencies"""
    return ManuscriptDependencies(
        db_deps=mock_database_deps,
        ai_deps=mock_ai_deps,
        target_length=8000,
        style="professional",
        target_audience="general adults",
        output_formats=["docx", "pdf", "epub"]
    )


@pytest.fixture
def mock_trend_deps(mock_database_deps, mock_scraping_deps) -> TrendAnalysisDependencies:
    """Provide mock trend analysis dependencies"""
    return TrendAnalysisDependencies(
        db_deps=mock_database_deps,
        scraping_deps=mock_scraping_deps,
        categories=["business", "self-help", "fiction"],
        max_results=50
    )


@pytest.fixture
def sample_trend_data() -> Dict[str, Any]:
    """Provide sample trend analysis data"""
    return {
        'category': 'business',
        'keywords': ['productivity', 'success', 'leadership'],
        'competition_level': 'moderate',
        'market_size': 'large',
        'trending_topics': [
            'remote work productivity',
            'AI-powered business tools',
            'sustainable business practices'
        ],
        'opportunity_score': 85,
        'opportunities': [
            {
                'title_suggestion': 'The Ultimate Productivity Guide',
                'market_potential': 'high',
                'competition_level': 'moderate',
                'target_audience': 'professionals',
                'estimated_sales': 500
            },
            {
                'title_suggestion': 'Remote Work Mastery',
                'market_potential': 'medium',
                'competition_level': 'low',
                'target_audience': 'remote workers',
                'estimated_sales': 300
            }
        ]
    }


@pytest.fixture
def sample_manuscript_data() -> Dict[str, Any]:
    """Provide sample manuscript data"""
    return {
        'title': 'The Ultimate Productivity Guide',
        'content': '''
        Chapter 1: Introduction to Productivity
        
        Productivity is the key to success in both personal and professional life.
        This comprehensive guide will teach you proven strategies and techniques
        to maximize your efficiency and achieve your goals.
        
        Chapter 2: Time Management Fundamentals
        
        Effective time management is the foundation of productivity...
        ''',
        'word_count': 8500,
        'chapters': [
            'Introduction to Productivity',
            'Time Management Fundamentals',
            'Goal Setting and Planning',
            'Eliminating Distractions',
            'Building Productive Habits'
        ],
        'quality_score': 88
    }


@pytest.fixture
def sample_sales_data() -> Dict[str, Any]:
    """Provide sample sales monitoring data"""
    return {
        'reporting_period': 'last_30_days',
        'books': [
            {
                'book_id': 'B001',
                'title': 'Productivity Mastery',
                'units_sold': 45,
                'revenue': 135.00,
                'royalties': 94.50,
                'page_reads': 1250,
                'ranking': 15000,
                'rating': 4.3,
                'review_count': 23
            }
        ],
        'total_sales': 77,
        'total_revenue': 231.00,
        'growth_rate': 15.2
    }


@pytest.fixture
def mock_agent_responses():
    """Provide mock responses for all agents"""
    return {
        'manuscript_generator': {
            'manuscript': {
                'title': 'Test Book',
                'content': 'Generated manuscript content...',
                'word_count': 8000,
                'chapters': ['Chapter 1', 'Chapter 2', 'Chapter 3']
            },
            'quality_metrics': {
                'readability': 85,
                'engagement': 80,
                'value': 90
            }
        },
        'trend_analyzer': {
            'market_analysis': {
                'trending_categories': ['productivity', 'wellness'],
                'competition_levels': {'productivity': 'high', 'wellness': 'medium'},
                'opportunities': ['mindfulness apps', 'remote work guides']
            },
            'recommendations': ['Focus on wellness subcategories']
        },
        'sales_monitor': {
            'performance_metrics': {
                'total_sales': 150,
                'total_revenue': 450.00,
                'growth_rate': 12.5
            },
            'insights': {
                'key_insights': ['Strong Q4 performance'],
                'recommendations': ['Increase marketing spend']
            }
        }
    }


@pytest.fixture(autouse=True)
def reset_agent_registry():
    """Reset agent registry state before each test"""
    # Store original state
    original_agents = agent_registry._agents.copy()
    
    yield
    
    # Restore original state
    agent_registry._agents = original_agents


@pytest.fixture
def mock_successful_agent_run():
    """Mock successful agent run for testing"""
    def _mock_run(return_data: Dict[str, Any]):
        mock_result = Mock()
        mock_result.output.model_dump.return_value = return_data
        return mock_result
    
    return _mock_run


@pytest.fixture
def mock_failed_agent_run():
    """Mock failed agent run for testing"""
    def _mock_run(error_message: str = "Test error"):
        raise Exception(error_message)
    
    return _mock_run


@pytest.fixture
def performance_thresholds():
    """Define performance thresholds for testing"""
    return {
        'single_agent_max_time': 5.0,  # seconds
        'workflow_max_time': 10.0,  # seconds
        'memory_max_increase': 100,  # MB
        'cpu_max_usage': 90,  # percent
        'min_success_rate': 0.95,  # 95%
        'max_concurrent_agents': 50
    }


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test"""
    yield
    
    # Force garbage collection
    import gc
    gc.collect()


# Environment setup for agent tests
@pytest.fixture(scope="session", autouse=True)
def setup_agent_test_environment():
    """Setup test environment for agent tests"""
    # Set test environment variables
    os.environ['TESTING'] = 'true'
    os.environ['LOG_LEVEL'] = 'WARNING'  # Reduce log noise during testing
    
    yield
    
    # Cleanup environment
    if 'TESTING' in os.environ:
        del os.environ['TESTING']
    if 'LOG_LEVEL' in os.environ:
        del os.environ['LOG_LEVEL']


# =====================================================
# SUPABASE FIXTURES FOR AGENT TESTING
# =====================================================

@pytest.fixture(scope="session")
async def supabase_client():
    """
    Provide Supabase client for agent testing
    
    Requires SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables
    """
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY") 

    if not supabase_url or not supabase_key:
        pytest.skip("Supabase credentials not configured. Set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.")

    try:
        from app.utils.supabase.supabase_database import get_supabase

        client = get_supabase()

        # Test connection by trying to query a system table
        if client is None:
            raise Exception("Supabase client not initialized")
        result = client.table("users").select("id").limit(1).execute()

        return client

    except Exception as e:
        pytest.skip(f"Could not connect to Supabase: {e}")


@pytest.fixture
async def test_user(supabase_client):
    """Create a test user for agent testing and clean up after test"""
    user_data = {
        "id": "test-agent-user-12345",
        "email": "<EMAIL>", 
        "full_name": "Test Agent User",
        "subscription_tier": "free",
        "content_preferences": {
            "api_keys": {
                "rainforest_api": "test-rainforest-key",
                "reddit_client_id": "test-reddit-client", 
                "reddit_client_secret": "test-reddit-secret",
                "kdp_email": "<EMAIL>",
                "kdp_password": "test-kdp-password"
            }
        }
    }
    
    # Create test user
    result = supabase_client.table("users").insert(user_data).execute()
    created_user = result.data[0] if result.data else user_data
    
    yield created_user
    
    # Cleanup test user and related data
    try:
        # Delete in order due to foreign key constraints
        supabase_client.table("feedback_metrics").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("sales_data").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("publications").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("books").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("users").delete().eq("id", created_user["id"]).execute()
    except Exception as e:
        print(f"Warning: Could not cleanup test user: {e}")


@pytest.fixture
async def test_book(supabase_client, test_user):
    """Create a test book for agent testing and clean up after test"""
    book_data = {
        "id": "test-agent-book-67890",
        "user_id": test_user["id"],
        "title": "Test Agent Book",
        "category": "business", 
        "content": "This is test content for agent testing.",
        "status": "draft",
        "word_count": 100,
        "quality_score": 85.0
    }
    
    # Create test book
    result = supabase_client.table("books").insert(book_data).execute()
    created_book = result.data[0] if result.data else book_data
    
    yield created_book
    
    # Cleanup handled by test_user fixture


@pytest.fixture
def supabase_database_deps(test_user):
    """Provide DatabaseDependencies that use real Supabase with test user"""
    from app.config import settings
    
    if not settings.supabase_url or not settings.supabase_service_key:
        pytest.skip("Supabase credentials not configured")
    
    # Note: DatabaseDependencies only takes user_id, the Supabase connection
    # is handled internally by the database layer
    return DatabaseDependencies(
        user_id=test_user["id"]
    )


@pytest.fixture
def supabase_trend_deps(supabase_database_deps, mock_scraping_deps):
    """Provide TrendAnalysisDependencies with real Supabase"""
    return TrendAnalysisDependencies(
        db_deps=supabase_database_deps,
        scraping_deps=mock_scraping_deps,
        categories=["business", "self-help", "fiction"],
        max_results=50
    )


# Test markers for agent tests
def pytest_configure(config):
    """Configure pytest markers for agent tests"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "supabase: mark test as requiring Supabase connection"
    )
    config.addinivalue_line(
        "markers", "agent: mark test as agent-specific test"
    )
