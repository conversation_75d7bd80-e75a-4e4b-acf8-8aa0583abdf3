"""
Chaos Engineering Experiments for Publish AI Platform.

This module implements comprehensive chaos engineering experiments to test
system resilience, fault tolerance, and recovery mechanisms.
"""

import asyncio
import json
import logging
import random
import signal
import subprocess
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from contextlib import asynccontextmanager
import threading

import httpx
import psutil
import pytest

logger = logging.getLogger(__name__)


@dataclass
class ChaosExperimentResult:
    """Result of a chaos engineering experiment."""
    experiment_name: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    success: bool
    error_message: Optional[str]
    
    # System metrics
    baseline_metrics: Dict[str, Any]
    chaos_metrics: Dict[str, Any]
    recovery_metrics: Dict[str, Any]
    
    # Recovery information
    recovery_time_seconds: Optional[float]
    full_recovery_achieved: bool
    
    # Additional data
    experiment_parameters: Dict[str, Any]
    observations: List[str]


class ChaosExperiment(ABC):
    """Base class for chaos engineering experiments."""
    
    def __init__(self, name: str, description: str, target_url: str = "http://localhost:8000"):
        self.name = name
        self.description = description
        self.target_url = target_url
        self.client = httpx.AsyncClient(timeout=10.0)
        self.observations = []
        
    @abstractmethod
    async def inject_chaos(self) -> Dict[str, Any]:
        """Inject chaos into the system. Return chaos parameters."""
        pass
    
    @abstractmethod
    async def cleanup_chaos(self) -> bool:
        """Clean up chaos injection. Return success status."""
        pass
    
    async def measure_system_health(self) -> Dict[str, Any]:
        """Measure current system health metrics."""
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "response_times": {},
            "error_rates": {},
            "system_resources": {}
        }
        
        # Test critical endpoints
        endpoints = [
            ("/health", "health_check"),
            ("/api/books", "books_api"),
            ("/api/auth/status", "auth_status")
        ]
        
        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = await self.client.get(f"{self.target_url}{endpoint}")
                response_time = (time.time() - start_time) * 1000
                
                metrics["response_times"][name] = response_time
                metrics["error_rates"][name] = 0 if response.status_code < 400 else 1
                
            except Exception as e:
                metrics["response_times"][name] = float('inf')
                metrics["error_rates"][name] = 1
                self.observations.append(f"Endpoint {name} failed: {str(e)}")
        
        # System resource metrics
        try:
            metrics["system_resources"] = {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "network_connections": len(psutil.net_connections())
            }
        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")
        
        return metrics
    
    async def wait_for_recovery(self, max_wait_seconds: int = 60) -> float:
        """Wait for system to recover and return recovery time."""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_seconds:
            try:
                # Test if system is responding
                response = await self.client.get(f"{self.target_url}/health")
                if response.status_code == 200:
                    recovery_time = time.time() - start_time
                    self.observations.append(f"System recovered after {recovery_time:.2f} seconds")
                    return recovery_time
                    
            except Exception:
                pass
            
            await asyncio.sleep(1)
        
        self.observations.append(f"System did not recover within {max_wait_seconds} seconds")
        return max_wait_seconds
    
    async def run_experiment(self, duration_seconds: int = 30) -> ChaosExperimentResult:
        """Run the complete chaos experiment."""
        start_time = datetime.utcnow()
        
        try:
            # Measure baseline
            self.observations.append("Collecting baseline metrics")
            baseline_metrics = await self.measure_system_health()
            
            # Inject chaos
            self.observations.append("Injecting chaos")
            chaos_parameters = await self.inject_chaos()
            
            # Wait for chaos duration
            await asyncio.sleep(duration_seconds)
            
            # Measure during chaos
            self.observations.append("Measuring system during chaos")
            chaos_metrics = await self.measure_system_health()
            
            # Clean up chaos
            self.observations.append("Cleaning up chaos injection")
            cleanup_success = await self.cleanup_chaos()
            
            # Wait for recovery
            self.observations.append("Waiting for system recovery")
            recovery_time = await self.wait_for_recovery()
            
            # Measure recovery
            recovery_metrics = await self.measure_system_health()
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            # Determine if experiment was successful
            success = (
                cleanup_success and
                recovery_time < 60 and
                self._validate_recovery(baseline_metrics, recovery_metrics)
            )
            
            return ChaosExperimentResult(
                experiment_name=self.name,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                success=success,
                error_message=None,
                baseline_metrics=baseline_metrics,
                chaos_metrics=chaos_metrics,
                recovery_metrics=recovery_metrics,
                recovery_time_seconds=recovery_time,
                full_recovery_achieved=recovery_time < 60,
                experiment_parameters=chaos_parameters,
                observations=self.observations.copy()
            )
            
        except Exception as e:
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            # Attempt cleanup
            try:
                await self.cleanup_chaos()
            except Exception:
                pass
            
            return ChaosExperimentResult(
                experiment_name=self.name,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                success=False,
                error_message=str(e),
                baseline_metrics={},
                chaos_metrics={},
                recovery_metrics={},
                recovery_time_seconds=None,
                full_recovery_achieved=False,
                experiment_parameters={},
                observations=self.observations.copy()
            )
    
    def _validate_recovery(self, baseline: Dict, recovery: Dict) -> bool:
        """Validate that system has recovered to acceptable levels."""
        try:
            # Check if critical endpoints are responding
            baseline_health = baseline.get("response_times", {}).get("health_check", float('inf'))
            recovery_health = recovery.get("response_times", {}).get("health_check", float('inf'))
            
            # Recovery is successful if health check is working and response time is reasonable
            return (
                recovery_health < float('inf') and
                recovery_health < baseline_health * 2  # Allow 2x slower response
            )
        except Exception:
            return False
    
    async def cleanup(self):
        """Clean up experiment resources."""
        await self.client.aclose()


class NetworkLatencyChaos(ChaosExperiment):
    """Inject network latency to test timeout handling."""
    
    def __init__(self, target_url: str = "http://localhost:8000", latency_ms: int = 5000):
        super().__init__(
            "Network Latency Chaos",
            f"Inject {latency_ms}ms network latency",
            target_url
        )
        self.latency_ms = latency_ms
        self.original_timeout = None
    
    async def inject_chaos(self) -> Dict[str, Any]:
        """Inject network latency by modifying client timeout."""
        self.original_timeout = self.client.timeout
        
        # Simulate network latency by adding delay to requests
        self._add_request_delay()
        
        return {"latency_ms": self.latency_ms}
    
    def _add_request_delay(self):
        """Add delay to HTTP requests to simulate network latency."""
        original_send = self.client._send
        
        async def delayed_send(request):
            await asyncio.sleep(self.latency_ms / 1000.0)  # Convert to seconds
            return await original_send(request)
        
        self.client._send = delayed_send
    
    async def cleanup_chaos(self) -> bool:
        """Remove network latency injection."""
        try:
            # Reset client timeout
            if self.original_timeout:
                self.client.timeout = self.original_timeout
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup network latency chaos: {e}")
            return False


class MemoryPressureChaos(ChaosExperiment):
    """Create memory pressure to test system behavior under low memory."""
    
    def __init__(self, target_url: str = "http://localhost:8000", memory_mb: int = 500):
        super().__init__(
            "Memory Pressure Chaos",
            f"Allocate {memory_mb}MB to create memory pressure",
            target_url
        )
        self.memory_mb = memory_mb
        self.memory_hog = None
    
    async def inject_chaos(self) -> Dict[str, Any]:
        """Allocate memory to create pressure."""
        try:
            # Allocate memory in a separate thread
            self.memory_hog = []
            
            def allocate_memory():
                # Allocate memory in chunks
                chunk_size = 1024 * 1024  # 1MB chunks
                for _ in range(self.memory_mb):
                    chunk = bytearray(chunk_size)
                    self.memory_hog.append(chunk)
            
            thread = threading.Thread(target=allocate_memory)
            thread.start()
            thread.join()
            
            return {"allocated_memory_mb": self.memory_mb}
            
        except Exception as e:
            logger.error(f"Failed to inject memory pressure: {e}")
            return {"error": str(e)}
    
    async def cleanup_chaos(self) -> bool:
        """Release allocated memory."""
        try:
            if self.memory_hog:
                self.memory_hog.clear()
                self.memory_hog = None
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup memory pressure: {e}")
            return False


class CPUStressChaos(ChaosExperiment):
    """Create CPU stress to test system performance under high CPU load."""
    
    def __init__(self, target_url: str = "http://localhost:8000", cpu_threads: int = 2):
        super().__init__(
            "CPU Stress Chaos",
            f"Create CPU stress with {cpu_threads} threads",
            target_url
        )
        self.cpu_threads = cpu_threads
        self.stress_processes = []
    
    async def inject_chaos(self) -> Dict[str, Any]:
        """Start CPU stress processes."""
        try:
            # Start CPU stress threads
            def cpu_stress():
                start_time = time.time()
                while time.time() - start_time < 60:  # Run for max 60 seconds
                    pass  # Busy loop to consume CPU
            
            for _ in range(self.cpu_threads):
                thread = threading.Thread(target=cpu_stress)
                thread.daemon = True
                thread.start()
                self.stress_processes.append(thread)
            
            return {"cpu_stress_threads": self.cpu_threads}
            
        except Exception as e:
            logger.error(f"Failed to inject CPU stress: {e}")
            return {"error": str(e)}
    
    async def cleanup_chaos(self) -> bool:
        """Stop CPU stress processes."""
        try:
            # Threads will stop automatically after their duration
            self.stress_processes.clear()
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup CPU stress: {e}")
            return False


class DatabaseConnectionChaos(ChaosExperiment):
    """Simulate database connection issues."""
    
    def __init__(self, target_url: str = "http://localhost:8000"):
        super().__init__(
            "Database Connection Chaos",
            "Simulate database connection failures",
            target_url
        )
        self.connection_blocked = False
    
    async def inject_chaos(self) -> Dict[str, Any]:
        """Simulate database connection issues."""
        # This is a simulation - in a real environment, you might:
        # - Block database ports using iptables
        # - Stop database containers
        # - Inject network failures between app and database
        
        self.connection_blocked = True
        
        # For this simulation, we'll just note that chaos was injected
        return {"chaos_type": "database_connection_simulation"}
    
    async def cleanup_chaos(self) -> bool:
        """Restore database connections."""
        self.connection_blocked = False
        return True


class ChaosTestSuite:
    """Comprehensive chaos engineering test suite."""
    
    def __init__(self, target_url: str = "http://localhost:8000"):
        self.target_url = target_url
        self.experiments = []
        self.results = []
    
    def add_experiment(self, experiment: ChaosExperiment):
        """Add a chaos experiment to the suite."""
        self.experiments.append(experiment)
    
    async def run_all_experiments(self, experiment_duration: int = 30) -> List[ChaosExperimentResult]:
        """Run all chaos experiments in the suite."""
        self.results = []
        
        for experiment in self.experiments:
            logger.info(f"Running chaos experiment: {experiment.name}")
            
            try:
                result = await experiment.run_experiment(experiment_duration)
                self.results.append(result)
                
                logger.info(f"Experiment {experiment.name} completed. Success: {result.success}")
                
                # Wait between experiments for system to stabilize
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Experiment {experiment.name} failed with exception: {e}")
                
            finally:
                await experiment.cleanup()
        
        return self.results
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive chaos engineering report."""
        total_experiments = len(self.results)
        successful_experiments = sum(1 for r in self.results if r.success)
        
        report = {
            "summary": {
                "total_experiments": total_experiments,
                "successful_experiments": successful_experiments,
                "success_rate": (successful_experiments / total_experiments * 100) if total_experiments > 0 else 0,
                "generated_at": datetime.utcnow().isoformat()
            },
            "experiments": [],
            "recommendations": []
        }
        
        for result in self.results:
            experiment_data = asdict(result)
            report["experiments"].append(experiment_data)
        
        # Generate recommendations based on results
        failed_experiments = [r for r in self.results if not r.success]
        
        if failed_experiments:
            report["recommendations"].append({
                "priority": "high",
                "category": "resilience",
                "description": f"{len(failed_experiments)} experiments failed",
                "action": "Review and improve system resilience for failed scenarios"
            })
        
        # Check recovery times
        slow_recovery = [r for r in self.results if r.recovery_time_seconds and r.recovery_time_seconds > 30]
        
        if slow_recovery:
            report["recommendations"].append({
                "priority": "medium",
                "category": "recovery",
                "description": f"{len(slow_recovery)} experiments had slow recovery times",
                "action": "Optimize system recovery mechanisms and monitoring"
            })
        
        return report
    
    async def cleanup_all(self):
        """Clean up all experiments."""
        for experiment in self.experiments:
            try:
                await experiment.cleanup()
            except Exception as e:
                logger.error(f"Failed to cleanup experiment {experiment.name}: {e}")


# Pytest integration
@pytest.mark.asyncio
async def test_network_latency_resilience():
    """Test system resilience to network latency."""
    experiment = NetworkLatencyChaos(latency_ms=3000)
    
    try:
        result = await experiment.run_experiment(duration_seconds=15)
        
        # Assert that system handles latency gracefully
        assert result.success, f"Network latency test failed: {result.error_message}"
        assert result.recovery_time_seconds < 30, "Recovery took too long"
        
    finally:
        await experiment.cleanup()


@pytest.mark.asyncio
async def test_memory_pressure_resilience():
    """Test system behavior under memory pressure."""
    experiment = MemoryPressureChaos(memory_mb=200)  # Moderate memory pressure
    
    try:
        result = await experiment.run_experiment(duration_seconds=20)
        
        # System should handle moderate memory pressure
        assert result.success, f"Memory pressure test failed: {result.error_message}"
        
    finally:
        await experiment.cleanup()


@pytest.mark.asyncio
async def test_cpu_stress_resilience():
    """Test system performance under CPU stress."""
    experiment = CPUStressChaos(cpu_threads=2)
    
    try:
        result = await experiment.run_experiment(duration_seconds=15)
        
        # System should maintain basic functionality under CPU stress
        assert result.success, f"CPU stress test failed: {result.error_message}"
        
    finally:
        await experiment.cleanup()


@pytest.mark.asyncio
async def test_comprehensive_chaos_suite():
    """Run comprehensive chaos engineering test suite."""
    suite = ChaosTestSuite()
    
    # Add all chaos experiments
    suite.add_experiment(NetworkLatencyChaos(latency_ms=2000))
    suite.add_experiment(MemoryPressureChaos(memory_mb=100))
    suite.add_experiment(CPUStressChaos(cpu_threads=1))
    suite.add_experiment(DatabaseConnectionChaos())
    
    try:
        results = await suite.run_all_experiments(experiment_duration=10)
        
        # Generate report
        report = suite.generate_report()
        
        # At least 75% of experiments should succeed
        assert report["summary"]["success_rate"] >= 75, \
            f"Chaos engineering success rate too low: {report['summary']['success_rate']}%"
        
        # Save report for analysis
        with open("chaos_engineering_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
    finally:
        await suite.cleanup_all()


if __name__ == "__main__":
    # Run chaos experiments manually
    async def main():
        print("🔥 Starting Chaos Engineering Tests")
        
        suite = ChaosTestSuite()
        
        # Add experiments
        suite.add_experiment(NetworkLatencyChaos(latency_ms=3000))
        suite.add_experiment(MemoryPressureChaos(memory_mb=300))
        suite.add_experiment(CPUStressChaos(cpu_threads=2))
        suite.add_experiment(DatabaseConnectionChaos())
        
        try:
            # Run all experiments
            results = await suite.run_all_experiments(experiment_duration=20)
            
            # Generate and display report
            report = suite.generate_report()
            
            print(f"\n📊 Chaos Engineering Results:")
            print(f"Total Experiments: {report['summary']['total_experiments']}")
            print(f"Successful: {report['summary']['successful_experiments']}")
            print(f"Success Rate: {report['summary']['success_rate']:.1f}%")
            
            print(f"\n📋 Experiment Details:")
            for result in results:
                status = "✅" if result.success else "❌"
                recovery = f" (Recovery: {result.recovery_time_seconds:.1f}s)" if result.recovery_time_seconds else ""
                print(f"{status} {result.experiment_name}{recovery}")
            
            # Save detailed report
            with open("chaos_engineering_report.json", "w") as f:
                json.dump(report, f, indent=2)
            
            print("\n📁 Detailed report saved to chaos_engineering_report.json")
            
        finally:
            await suite.cleanup_all()
    
    asyncio.run(main())