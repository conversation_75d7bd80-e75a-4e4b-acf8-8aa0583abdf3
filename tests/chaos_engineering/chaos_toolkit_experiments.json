{"version": "1.0.0", "title": "Publish AI Platform Resilience Experiments", "description": "Chaos engineering experiments to validate system resilience and recovery mechanisms", "tags": ["platform", "resilience", "microservices"], "steady-state-hypothesis": {"title": "Application is healthy and responsive", "probes": [{"name": "health-check-responds", "type": "probe", "tolerance": 200, "provider": {"type": "http", "url": "http://localhost:8000/health", "method": "GET", "timeout": 5}}, {"name": "books-api-responds", "type": "probe", "tolerance": [200, 401], "provider": {"type": "http", "url": "http://localhost:8000/api/books", "method": "GET", "timeout": 10}}, {"name": "response-time-acceptable", "type": "probe", "tolerance": {"type": "range", "range": [0, 2000]}, "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_probes", "func": "measure_response_time", "arguments": {"url": "http://localhost:8000/health"}}}]}, "method": [{"type": "action", "name": "network-latency-injection", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "inject_network_latency", "arguments": {"latency_ms": 2000, "target_service": "publish-ai"}}, "pauses": {"after": 30}}], "rollbacks": [{"type": "action", "name": "remove-network-latency", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "remove_network_latency", "arguments": {"target_service": "publish-ai"}}}], "experiments": [{"title": "Database Connection Failure", "description": "Test application behavior when database connections fail", "steady-state-hypothesis": {"title": "Application handles database failures gracefully", "probes": [{"name": "health-endpoint-responds", "type": "probe", "tolerance": 200, "provider": {"type": "http", "url": "http://localhost:8000/health", "timeout": 10}}]}, "method": [{"type": "action", "name": "block-database-connections", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "block_database_connections", "arguments": {"database_host": "localhost", "database_port": 5432}}, "pauses": {"after": 45}}], "rollbacks": [{"type": "action", "name": "restore-database-connections", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "restore_database_connections", "arguments": {"database_host": "localhost", "database_port": 5432}}}]}, {"title": "High Memory Pressure", "description": "Test application performance under memory pressure", "steady-state-hypothesis": {"title": "Application maintains performance under memory pressure", "probes": [{"name": "memory-usage-acceptable", "type": "probe", "tolerance": {"type": "range", "range": [0, 90]}, "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_probes", "func": "get_memory_usage_percent"}}, {"name": "response-time-stable", "type": "probe", "tolerance": {"type": "range", "range": [0, 3000]}, "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_probes", "func": "measure_response_time", "arguments": {"url": "http://localhost:8000/api/books"}}}]}, "method": [{"type": "action", "name": "create-memory-pressure", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "create_memory_pressure", "arguments": {"memory_mb": 500, "duration_seconds": 60}}}]}, {"title": "CPU Stress Test", "description": "Test application behavior under high CPU load", "steady-state-hypothesis": {"title": "Application responds despite CPU stress", "probes": [{"name": "cpu-usage-monitored", "type": "probe", "tolerance": {"type": "range", "range": [0, 100]}, "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_probes", "func": "get_cpu_usage_percent"}}, {"name": "api-still-responds", "type": "probe", "tolerance": [200, 401, 429], "provider": {"type": "http", "url": "http://localhost:8000/api/books", "timeout": 15}}]}, "method": [{"type": "action", "name": "create-cpu-stress", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "create_cpu_stress", "arguments": {"cpu_threads": 4, "duration_seconds": 45}}}]}, {"title": "External API Failure", "description": "Test behavior when external AI APIs fail", "steady-state-hypothesis": {"title": "Application gracefully handles AI API failures", "probes": [{"name": "basic-operations-work", "type": "probe", "tolerance": 200, "provider": {"type": "http", "url": "http://localhost:8000/api/books", "timeout": 10}}]}, "method": [{"type": "action", "name": "block-external-apis", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "block_external_apis", "arguments": {"api_domains": ["api.openai.com", "api.anthropic.com"], "duration_seconds": 60}}}], "rollbacks": [{"type": "action", "name": "restore-external-apis", "provider": {"type": "python", "module": "tests.chaos_engineering.chaos_actions", "func": "restore_external_apis", "arguments": {"api_domains": ["api.openai.com", "api.anthropic.com"]}}}]}], "configuration": {"max_experiment_duration": 300, "rollback_strategy": "always", "notifications": {"slack": {"enabled": false, "webhook_url": "${SLACK_WEBHOOK_URL}"}, "email": {"enabled": false, "recipients": ["<EMAIL>"]}}, "monitoring": {"enabled": true, "metrics_endpoint": "http://localhost:8000/api/monitoring/metrics", "alert_threshold": {"response_time_ms": 5000, "error_rate_percent": 10, "cpu_percent": 90, "memory_percent": 90}}}, "runtime": {"hypothesis": {"strategy": "default"}, "dry": false, "verbose": true, "fail_fast": false}}