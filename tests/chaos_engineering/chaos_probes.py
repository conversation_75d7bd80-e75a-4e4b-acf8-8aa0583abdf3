"""
Chaos Engineering Probes for System Monitoring.

This module provides probe functions for monitoring system health
during chaos engineering experiments.
"""

import time
import logging
from typing import Dict, Any

import httpx
import psutil

logger = logging.getLogger(__name__)


def measure_response_time(url: str, timeout: int = 10) -> float:
    """Measure HTTP response time in milliseconds."""
    try:
        start_time = time.time()
        
        with httpx.Client(timeout=timeout) as client:
            response = client.get(url)
            
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        logger.info(f"Response time for {url}: {response_time:.2f}ms (Status: {response.status_code})")
        return response_time
        
    except Exception as e:
        logger.error(f"Failed to measure response time for {url}: {e}")
        return float('inf')


def get_memory_usage_percent() -> float:
    """Get current memory usage percentage."""
    try:
        memory_info = psutil.virtual_memory()
        usage_percent = memory_info.percent
        
        logger.info(f"Memory usage: {usage_percent:.1f}%")
        return usage_percent
        
    except Exception as e:
        logger.error(f"Failed to get memory usage: {e}")
        return 100.0  # Assume worst case


def get_cpu_usage_percent(interval: float = 1.0) -> float:
    """Get current CPU usage percentage."""
    try:
        cpu_percent = psutil.cpu_percent(interval=interval)
        
        logger.info(f"CPU usage: {cpu_percent:.1f}%")
        return cpu_percent
        
    except Exception as e:
        logger.error(f"Failed to get CPU usage: {e}")
        return 100.0  # Assume worst case


def get_disk_usage_percent(path: str = "/") -> float:
    """Get disk usage percentage for specified path."""
    try:
        disk_usage = psutil.disk_usage(path)
        usage_percent = (disk_usage.used / disk_usage.total) * 100
        
        logger.info(f"Disk usage for {path}: {usage_percent:.1f}%")
        return usage_percent
        
    except Exception as e:
        logger.error(f"Failed to get disk usage for {path}: {e}")
        return 100.0  # Assume worst case


def check_service_health(service_url: str, expected_status: int = 200) -> bool:
    """Check if a service is healthy by testing its health endpoint."""
    try:
        with httpx.Client(timeout=10) as client:
            response = client.get(f"{service_url}/health")
            
        is_healthy = response.status_code == expected_status
        
        logger.info(f"Service health check for {service_url}: {'HEALTHY' if is_healthy else 'UNHEALTHY'} (Status: {response.status_code})")
        return is_healthy
        
    except Exception as e:
        logger.error(f"Health check failed for {service_url}: {e}")
        return False


def get_network_connections_count() -> int:
    """Get number of active network connections."""
    try:
        connections = psutil.net_connections()
        count = len(connections)
        
        logger.info(f"Active network connections: {count}")
        return count
        
    except Exception as e:
        logger.error(f"Failed to get network connections count: {e}")
        return 0


def measure_database_response_time(connection_string: str) -> float:
    """Measure database response time."""
    # This would be implemented with actual database connection
    # For now, we'll return a placeholder
    try:
        # Simulate database query time
        start_time = time.time()
        time.sleep(0.01)  # Simulate query
        response_time = (time.time() - start_time) * 1000
        
        logger.info(f"Database response time: {response_time:.2f}ms")
        return response_time
        
    except Exception as e:
        logger.error(f"Failed to measure database response time: {e}")
        return float('inf')


def get_system_load_average() -> Dict[str, float]:
    """Get system load averages."""
    try:
        load_avg = psutil.getloadavg()
        
        load_data = {
            "1min": load_avg[0],
            "5min": load_avg[1],
            "15min": load_avg[2]
        }
        
        logger.info(f"System load averages: {load_data}")
        return load_data
        
    except Exception as e:
        logger.error(f"Failed to get system load averages: {e}")
        return {"1min": 0.0, "5min": 0.0, "15min": 0.0}


def check_api_endpoint_availability(base_url: str, endpoints: list) -> Dict[str, bool]:
    """Check availability of multiple API endpoints."""
    results = {}
    
    try:
        with httpx.Client(timeout=5) as client:
            for endpoint in endpoints:
                try:
                    url = f"{base_url}{endpoint}"
                    response = client.get(url)
                    results[endpoint] = response.status_code < 500  # Accept 4xx but not 5xx
                    
                except Exception:
                    results[endpoint] = False
        
        available_count = sum(1 for available in results.values() if available)
        logger.info(f"API endpoints available: {available_count}/{len(endpoints)}")
        
        return results
        
    except Exception as e:
        logger.error(f"Failed to check API endpoint availability: {e}")
        return {endpoint: False for endpoint in endpoints}


def measure_throughput(url: str, duration_seconds: int = 10, concurrent_requests: int = 5) -> float:
    """Measure API throughput (requests per second)."""
    import asyncio
    import aiohttp
    
    async def make_request(session):
        try:
            async with session.get(url) as response:
                return response.status < 500
        except Exception:
            return False
    
    async def measure():
        start_time = time.time()
        successful_requests = 0
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            while time.time() - start_time < duration_seconds:
                # Make concurrent requests
                tasks = [make_request(session) for _ in range(concurrent_requests)]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                successful_requests += sum(1 for result in results if result is True)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
        
        total_time = time.time() - start_time
        throughput = successful_requests / total_time
        
        logger.info(f"Measured throughput for {url}: {throughput:.2f} req/s")
        return throughput
    
    try:
        return asyncio.run(measure())
    except Exception as e:
        logger.error(f"Failed to measure throughput for {url}: {e}")
        return 0.0