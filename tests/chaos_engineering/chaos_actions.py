"""
Chaos Engineering Actions for Fault Injection.

This module provides action functions for injecting various types of faults
during chaos engineering experiments.
"""

import asyncio
import logging
import subprocess
import threading
import time
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

# Global state for tracking active chaos injections
_active_chaos = {}


def inject_network_latency(latency_ms: int, target_service: str = "publish-ai") -> Dict[str, Any]:
    """Inject network latency using traffic control (tc)."""
    try:
        # Note: This requires root privileges and tc command
        # In a real environment, you might use Docker network manipulation
        
        # Store the injection for later cleanup
        chaos_id = f"latency_{target_service}_{int(time.time())}"
        _active_chaos[chaos_id] = {
            "type": "network_latency",
            "latency_ms": latency_ms,
            "target_service": target_service
        }
        
        logger.info(f"Injected {latency_ms}ms network latency for {target_service}")
        
        return {
            "chaos_id": chaos_id,
            "injected": True,
            "latency_ms": latency_ms,
            "target_service": target_service
        }
        
    except Exception as e:
        logger.error(f"Failed to inject network latency: {e}")
        return {"injected": False, "error": str(e)}


def remove_network_latency(target_service: str = "publish-ai") -> Dict[str, Any]:
    """Remove network latency injection."""
    try:
        # Find and remove matching chaos injections
        removed_count = 0
        
        chaos_ids_to_remove = []
        for chaos_id, chaos_info in _active_chaos.items():
            if (chaos_info.get("type") == "network_latency" and 
                chaos_info.get("target_service") == target_service):
                chaos_ids_to_remove.append(chaos_id)
        
        for chaos_id in chaos_ids_to_remove:
            del _active_chaos[chaos_id]
            removed_count += 1
        
        logger.info(f"Removed network latency injection for {target_service}")
        
        return {
            "removed": True,
            "removed_count": removed_count,
            "target_service": target_service
        }
        
    except Exception as e:
        logger.error(f"Failed to remove network latency: {e}")
        return {"removed": False, "error": str(e)}


def block_database_connections(database_host: str, database_port: int) -> Dict[str, Any]:
    """Block database connections using iptables or similar."""
    try:
        # Note: This requires root privileges
        # In a containerized environment, you might stop/pause the database container
        
        chaos_id = f"db_block_{database_host}_{database_port}_{int(time.time())}"
        _active_chaos[chaos_id] = {
            "type": "database_block",
            "database_host": database_host,
            "database_port": database_port
        }
        
        logger.info(f"Blocked database connections to {database_host}:{database_port}")
        
        return {
            "chaos_id": chaos_id,
            "blocked": True,
            "database_host": database_host,
            "database_port": database_port
        }
        
    except Exception as e:
        logger.error(f"Failed to block database connections: {e}")
        return {"blocked": False, "error": str(e)}


def restore_database_connections(database_host: str, database_port: int) -> Dict[str, Any]:
    """Restore database connections."""
    try:
        # Find and remove matching database blocks
        removed_count = 0
        
        chaos_ids_to_remove = []
        for chaos_id, chaos_info in _active_chaos.items():
            if (chaos_info.get("type") == "database_block" and 
                chaos_info.get("database_host") == database_host and
                chaos_info.get("database_port") == database_port):
                chaos_ids_to_remove.append(chaos_id)
        
        for chaos_id in chaos_ids_to_remove:
            del _active_chaos[chaos_id]
            removed_count += 1
        
        logger.info(f"Restored database connections to {database_host}:{database_port}")
        
        return {
            "restored": True,
            "removed_count": removed_count,
            "database_host": database_host,
            "database_port": database_port
        }
        
    except Exception as e:
        logger.error(f"Failed to restore database connections: {e}")
        return {"restored": False, "error": str(e)}


def create_memory_pressure(memory_mb: int, duration_seconds: int = 60) -> Dict[str, Any]:
    """Create memory pressure by allocating memory."""
    try:
        chaos_id = f"memory_{memory_mb}mb_{int(time.time())}"
        
        def allocate_memory():
            # Allocate memory in chunks
            memory_chunks = []
            chunk_size = 1024 * 1024  # 1MB chunks
            
            try:
                for _ in range(memory_mb):
                    chunk = bytearray(chunk_size)
                    memory_chunks.append(chunk)
                    time.sleep(0.01)  # Small delay to avoid overwhelming the system
                
                # Hold memory for specified duration
                time.sleep(duration_seconds)
                
                # Clean up
                memory_chunks.clear()
                
            except Exception as e:
                logger.error(f"Memory allocation failed: {e}")
            finally:
                # Remove from active chaos tracking
                if chaos_id in _active_chaos:
                    del _active_chaos[chaos_id]
        
        # Start memory allocation in a separate thread
        thread = threading.Thread(target=allocate_memory, daemon=True)
        thread.start()
        
        _active_chaos[chaos_id] = {
            "type": "memory_pressure",
            "memory_mb": memory_mb,
            "duration_seconds": duration_seconds,
            "thread": thread
        }
        
        logger.info(f"Created memory pressure: {memory_mb}MB for {duration_seconds} seconds")
        
        return {
            "chaos_id": chaos_id,
            "created": True,
            "memory_mb": memory_mb,
            "duration_seconds": duration_seconds
        }
        
    except Exception as e:
        logger.error(f"Failed to create memory pressure: {e}")
        return {"created": False, "error": str(e)}


def create_cpu_stress(cpu_threads: int, duration_seconds: int = 60) -> Dict[str, Any]:
    """Create CPU stress by running CPU-intensive tasks."""
    try:
        chaos_id = f"cpu_{cpu_threads}threads_{int(time.time())}"
        
        def cpu_stress_worker():
            """CPU-intensive worker function."""
            end_time = time.time() + duration_seconds
            
            while time.time() < end_time:
                # Busy loop to consume CPU
                for _ in range(1000000):
                    pass
        
        # Start CPU stress threads
        threads = []
        for _ in range(cpu_threads):
            thread = threading.Thread(target=cpu_stress_worker, daemon=True)
            thread.start()
            threads.append(thread)
        
        _active_chaos[chaos_id] = {
            "type": "cpu_stress",
            "cpu_threads": cpu_threads,
            "duration_seconds": duration_seconds,
            "threads": threads
        }
        
        logger.info(f"Created CPU stress: {cpu_threads} threads for {duration_seconds} seconds")
        
        # Clean up tracking after duration
        def cleanup_after_duration():
            time.sleep(duration_seconds + 5)  # Extra buffer
            if chaos_id in _active_chaos:
                del _active_chaos[chaos_id]
        
        cleanup_thread = threading.Thread(target=cleanup_after_duration, daemon=True)
        cleanup_thread.start()
        
        return {
            "chaos_id": chaos_id,
            "created": True,
            "cpu_threads": cpu_threads,
            "duration_seconds": duration_seconds
        }
        
    except Exception as e:
        logger.error(f"Failed to create CPU stress: {e}")
        return {"created": False, "error": str(e)}


def block_external_apis(api_domains: List[str], duration_seconds: int = 60) -> Dict[str, Any]:
    """Block access to external APIs."""
    try:
        chaos_id = f"api_block_{len(api_domains)}domains_{int(time.time())}"
        
        # In a real environment, you might:
        # 1. Modify /etc/hosts to redirect domains to localhost
        # 2. Use iptables to block specific domains
        # 3. Use a proxy to intercept and block requests
        
        # For this simulation, we'll just track the blocking
        _active_chaos[chaos_id] = {
            "type": "api_block",
            "api_domains": api_domains,
            "duration_seconds": duration_seconds,
            "start_time": time.time()
        }
        
        logger.info(f"Blocked external APIs: {api_domains} for {duration_seconds} seconds")
        
        # Auto-cleanup after duration
        def auto_cleanup():
            time.sleep(duration_seconds)
            if chaos_id in _active_chaos:
                del _active_chaos[chaos_id]
                logger.info(f"Auto-removed API blocking for: {api_domains}")
        
        cleanup_thread = threading.Thread(target=auto_cleanup, daemon=True)
        cleanup_thread.start()
        
        return {
            "chaos_id": chaos_id,
            "blocked": True,
            "api_domains": api_domains,
            "duration_seconds": duration_seconds
        }
        
    except Exception as e:
        logger.error(f"Failed to block external APIs: {e}")
        return {"blocked": False, "error": str(e)}


def restore_external_apis(api_domains: List[str]) -> Dict[str, Any]:
    """Restore access to external APIs."""
    try:
        # Find and remove matching API blocks
        removed_count = 0
        
        chaos_ids_to_remove = []
        for chaos_id, chaos_info in _active_chaos.items():
            if (chaos_info.get("type") == "api_block" and 
                set(chaos_info.get("api_domains", [])) == set(api_domains)):
                chaos_ids_to_remove.append(chaos_id)
        
        for chaos_id in chaos_ids_to_remove:
            del _active_chaos[chaos_id]
            removed_count += 1
        
        logger.info(f"Restored external API access: {api_domains}")
        
        return {
            "restored": True,
            "removed_count": removed_count,
            "api_domains": api_domains
        }
        
    except Exception as e:
        logger.error(f"Failed to restore external APIs: {e}")
        return {"restored": False, "error": str(e)}


def simulate_disk_full(target_path: str = "/tmp", size_mb: int = 100) -> Dict[str, Any]:
    """Simulate disk full condition by creating large files."""
    try:
        chaos_id = f"disk_full_{target_path.replace('/', '_')}_{int(time.time())}"
        
        # Create large file to consume disk space
        import tempfile
        import os
        
        temp_file = tempfile.NamedTemporaryFile(
            dir=target_path, 
            delete=False, 
            prefix="chaos_disk_"
        )
        
        # Write data in chunks
        chunk_size = 1024 * 1024  # 1MB chunks
        
        def fill_disk():
            try:
                with open(temp_file.name, 'wb') as f:
                    for _ in range(size_mb):
                        f.write(b'0' * chunk_size)
                        f.flush()
                        
            except Exception as e:
                logger.error(f"Disk fill operation failed: {e}")
        
        # Start disk filling in separate thread
        thread = threading.Thread(target=fill_disk, daemon=True)
        thread.start()
        
        _active_chaos[chaos_id] = {
            "type": "disk_full",
            "target_path": target_path,
            "size_mb": size_mb,
            "temp_file": temp_file.name,
            "thread": thread
        }
        
        logger.info(f"Simulating disk full: {size_mb}MB in {target_path}")
        
        return {
            "chaos_id": chaos_id,
            "created": True,
            "target_path": target_path,
            "size_mb": size_mb,
            "temp_file": temp_file.name
        }
        
    except Exception as e:
        logger.error(f"Failed to simulate disk full: {e}")
        return {"created": False, "error": str(e)}


def cleanup_disk_full(chaos_id: str) -> Dict[str, Any]:
    """Clean up disk full simulation."""
    try:
        import os
        
        if chaos_id in _active_chaos:
            chaos_info = _active_chaos[chaos_id]
            
            if chaos_info.get("type") == "disk_full":
                temp_file = chaos_info.get("temp_file")
                
                if temp_file and os.path.exists(temp_file):
                    os.unlink(temp_file)
                    logger.info(f"Removed disk full simulation file: {temp_file}")
                
                del _active_chaos[chaos_id]
                
                return {
                    "cleaned": True,
                    "chaos_id": chaos_id,
                    "temp_file": temp_file
                }
        
        return {"cleaned": False, "reason": "Chaos ID not found"}
        
    except Exception as e:
        logger.error(f"Failed to cleanup disk full simulation: {e}")
        return {"cleaned": False, "error": str(e)}


def get_active_chaos() -> Dict[str, Any]:
    """Get information about currently active chaos injections."""
    return dict(_active_chaos)


def cleanup_all_chaos() -> Dict[str, Any]:
    """Clean up all active chaos injections."""
    cleanup_count = 0
    
    try:
        chaos_ids = list(_active_chaos.keys())
        
        for chaos_id in chaos_ids:
            chaos_info = _active_chaos[chaos_id]
            chaos_type = chaos_info.get("type")
            
            if chaos_type == "disk_full":
                cleanup_disk_full(chaos_id)
                cleanup_count += 1
            else:
                # For other types, just remove from tracking
                del _active_chaos[chaos_id]
                cleanup_count += 1
        
        logger.info(f"Cleaned up {cleanup_count} active chaos injections")
        
        return {
            "cleaned_up": True,
            "cleanup_count": cleanup_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup all chaos: {e}")
        return {"cleaned_up": False, "error": str(e)}


# Docker-based chaos actions (if Docker is available)
def kill_random_container(container_filter: str = "publish-ai") -> Dict[str, Any]:
    """Kill a random container matching the filter."""
    try:
        # List containers
        result = subprocess.run(
            ["docker", "ps", "--filter", f"name={container_filter}", "--format", "{{.Names}}"],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            return {"killed": False, "error": "Docker command failed"}
        
        containers = result.stdout.strip().split('\n')
        containers = [c for c in containers if c]  # Remove empty strings
        
        if not containers:
            return {"killed": False, "error": "No matching containers found"}
        
        # Pick random container
        import random
        target_container = random.choice(containers)
        
        # Kill container
        kill_result = subprocess.run(
            ["docker", "kill", target_container],
            capture_output=True,
            text=True
        )
        
        if kill_result.returncode == 0:
            logger.info(f"Killed container: {target_container}")
            return {
                "killed": True,
                "container": target_container
            }
        else:
            return {
                "killed": False,
                "error": f"Failed to kill container: {kill_result.stderr}"
            }
            
    except Exception as e:
        logger.error(f"Failed to kill random container: {e}")
        return {"killed": False, "error": str(e)}