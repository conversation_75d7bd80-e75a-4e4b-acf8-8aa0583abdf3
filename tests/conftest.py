"""
Pytest configuration and fixtures for PydanticAI agent testing with monitoring
"""

import pytest
import asyncio
import os
import sys
import tempfile
import logging
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, Generator

# Configure test logging
logging.basicConfig(level=logging.DEBUG)

# Disable real monitoring in tests
os.environ["SENTRY_DSN"] = ""
os.environ["LOGFLARE_API_KEY"] = ""

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import test dependencies
from app.agents.pydantic_ai_base import (
    DatabaseDependencies,
    AIModelDependencies,
    ScrapingDependencies,
    ManuscriptDependencies,
    TrendAnalysisDependencies,
    SalesMonitorDependencies,
    CoverDesignDependencies,
    KDPUploadDependencies,
    agent_registry
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# =====================================================
# MONITORING TEST FIXTURES
# =====================================================

@pytest.fixture
def mock_monitoring_manager():
    """Mock the monitoring manager for tests"""
    with patch('app.monitoring.monitoring_setup.monitoring_manager') as mock_manager:
        mock_manager.initialize_sentry.return_value = True
        mock_manager.initialize_logflare.return_value = True
        mock_manager.sentry_initialized = True
        mock_manager.logflare_initialized = True
        mock_manager.structured_logger = Mock()
        mock_manager.request_id = None
        
        # Mock context manager for monitor_operation
        mock_manager.monitor_operation.return_value.__aenter__ = AsyncMock(return_value="test-operation-id")
        mock_manager.monitor_operation.return_value.__aexit__ = AsyncMock(return_value=None)
        
        yield mock_manager

@pytest.fixture
def mock_sentry():
    """Mock Sentry SDK for tests"""
    with patch('sentry_sdk.init') as mock_init, \
         patch('sentry_sdk.capture_exception') as mock_capture, \
         patch('sentry_sdk.configure_scope') as mock_scope:
        
        mock_init.return_value = None
        mock_capture.return_value = None
        mock_scope.return_value.__enter__ = Mock()
        mock_scope.return_value.__exit__ = Mock()
        
        yield {
            'init': mock_init,
            'capture_exception': mock_capture,
            'configure_scope': mock_scope
        }

@pytest.fixture
def mock_logflare():
    """Mock Logflare client for tests"""
    with patch('logflare.LogflareClient') as mock_client:
        mock_instance = Mock()
        mock_client.return_value = mock_instance
        
        yield mock_instance

@pytest.fixture
def mock_structured_logger():
    """Mock structured logger for tests"""
    with patch('structlog.get_logger') as mock_get_logger:
        mock_logger = Mock()
        mock_logger.info = Mock()
        mock_logger.error = Mock()
        mock_logger.warning = Mock()
        mock_logger.debug = Mock()
        mock_logger.bind.return_value = mock_logger
        
        mock_get_logger.return_value = mock_logger
        
        yield mock_logger

@pytest.fixture
def mock_monitor_operation():
    """Mock monitor_operation context manager"""
    class MockMonitorOperation:
        def __init__(self, operation_name: str, **context):
            self.operation_name = operation_name
            self.context = context
            self.operation_id = "test-operation-id"
        
        async def __aenter__(self):
            return self.operation_id
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            return None
    
    with patch('app.monitoring.monitoring_setup.monitor_operation', MockMonitorOperation):
        yield MockMonitorOperation

@pytest.fixture
def monitoring_test_setup(mock_monitoring_manager, mock_sentry, mock_logflare, mock_structured_logger):
    """Complete monitoring test setup"""
    return {
        'monitoring_manager': mock_monitoring_manager,
        'sentry': mock_sentry,
        'logflare': mock_logflare,
        'logger': mock_structured_logger
    }

@pytest.fixture
def temp_db_file():
    """Create a temporary database file for testing"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def mock_database_deps() -> DatabaseDependencies:
    """Provide mock database dependencies"""
    return DatabaseDependencies(
        user_id=str(123),
    )


@pytest.fixture
def mock_ai_deps() -> AIModelDependencies:
    """Provide mock AI model dependencies"""
    return AIModelDependencies(
        openai_api_key="test_api_key",
        anthropic_api_key="test_api_key",
        preferred_model="gpt-4",
        temperature=0.7,
        max_tokens=2000,
    )


@pytest.fixture
def mock_scraping_deps() -> ScrapingDependencies:
    """Provide mock scraping dependencies"""
    return ScrapingDependencies(
        headless=True, amazon_scraper=Mock(), reddit_scraper=Mock()
    )


@pytest.fixture
def mock_manuscript_deps(mock_database_deps, mock_ai_deps) -> ManuscriptDependencies:
    """Provide mock manuscript generation dependencies"""
    return ManuscriptDependencies(
        db_deps=mock_database_deps,
        ai_deps=mock_ai_deps,
        target_length=8000,
        style="professional",
        target_audience="general adults",
        output_formats=["docx", "pdf", "epub"]
    )


@pytest.fixture
def mock_trend_deps(mock_database_deps, mock_scraping_deps) -> TrendAnalysisDependencies:
    """Provide mock trend analysis dependencies"""
    return TrendAnalysisDependencies(
        db_deps=mock_database_deps,
        scraping_deps=mock_scraping_deps,
        categories=["business", "self-help", "fiction"],
        max_results=50
    )


@pytest.fixture
def mock_sales_deps(mock_database_deps, mock_scraping_deps) -> SalesMonitorDependencies:
    """Provide mock sales monitoring dependencies"""
    return SalesMonitorDependencies(
        db_deps=mock_database_deps,
        scraping_deps=mock_scraping_deps,
        kdp_email="<EMAIL>",
        kdp_password="test_password"
    )


@pytest.fixture
def mock_cover_deps(mock_database_deps, mock_ai_deps) -> CoverDesignDependencies:
    """Provide mock cover design dependencies"""
    return CoverDesignDependencies(
        db_deps=mock_database_deps,
        ai_deps=mock_ai_deps,
        style="professional",
        dimensions=(1600, 2560),
    )


@pytest.fixture
def mock_kdp_deps(mock_database_deps, mock_scraping_deps) -> KDPUploadDependencies:
    """Provide mock KDP upload dependencies"""
    return KDPUploadDependencies(
        db_deps=mock_database_deps,
        scraping_deps=mock_scraping_deps,
        kdp_email="<EMAIL>",
        kdp_password="test_password"
    )


@pytest.fixture
def sample_trend_data() -> Dict[str, Any]:
    """Provide sample trend analysis data"""
    return {
        'category': 'business',
        'keywords': ['productivity', 'success', 'leadership'],
        'competition_level': 'moderate',
        'market_size': 'large',
        'trending_topics': [
            'remote work productivity',
            'AI-powered business tools',
            'sustainable business practices'
        ],
        'opportunity_score': 85
    }


@pytest.fixture
def sample_manuscript_data() -> Dict[str, Any]:
    """Provide sample manuscript data"""
    return {
        'title': 'The Ultimate Productivity Guide',
        'content': '''
        Chapter 1: Introduction to Productivity
        
        Productivity is the key to success in both personal and professional life.
        This comprehensive guide will teach you proven strategies and techniques
        to maximize your efficiency and achieve your goals.
        
        Chapter 2: Time Management Fundamentals
        
        Effective time management is the foundation of productivity...
        ''',
        'word_count': 8500,
        'chapters': [
            'Introduction to Productivity',
            'Time Management Fundamentals',
            'Goal Setting and Planning',
            'Eliminating Distractions',
            'Building Productive Habits'
        ],
        'quality_score': 88
    }


@pytest.fixture
def sample_sales_data() -> Dict[str, Any]:
    """Provide sample sales monitoring data"""
    return {
        'reporting_period': 'last_30_days',
        'books': [
            {
                'book_id': 'B001',
                'title': 'Productivity Mastery',
                'units_sold': 45,
                'revenue': 135.00,
                'royalties': 94.50,
                'page_reads': 1250,
                'ranking': 15000,
                'rating': 4.3,
                'review_count': 23
            },
            {
                'book_id': 'B002',
                'title': 'Leadership Excellence',
                'units_sold': 32,
                'revenue': 96.00,
                'royalties': 67.20,
                'page_reads': 890,
                'ranking': 25000,
                'rating': 4.1,
                'review_count': 18
            }
        ],
        'total_sales': 77,
        'total_revenue': 231.00,
        'growth_rate': 15.2
    }


@pytest.fixture
def sample_cover_design() -> Dict[str, Any]:
    """Provide sample cover design data"""
    return {
        'design_concept': {
            'primary_colors': ['#1E3A8A', '#FFFFFF', '#F59E0B'],
            'secondary_colors': ['#64748B', '#E5E7EB'],
            'typography': {
                'title_font': 'Roboto Slab',
                'subtitle_font': 'Roboto',
                'author_font': 'Roboto Light'
            },
            'layout_style': 'professional',
            'imagery_style': 'minimalist'
        },
        'market_analysis': {
            'genre_conventions': 'Business books typically use blues and grays',
            'competitive_positioning': 'Premium professional appearance',
            'target_demographic': 'Business professionals aged 25-45'
        },
        'design_elements': [
            'Clean geometric shapes',
            'Professional color scheme',
            'Clear hierarchy',
            'Readable typography'
        ]
    }


@pytest.fixture
def mock_agent_responses():
    """Provide mock responses for all agents"""
    return {
        'manuscript_generator': {
            'manuscript': {
                'title': 'Test Book',
                'content': 'Generated manuscript content...',
                'word_count': 8000,
                'chapters': ['Chapter 1', 'Chapter 2', 'Chapter 3']
            },
            'quality_metrics': {
                'readability': 85,
                'engagement': 80,
                'value': 90
            }
        },
        'trend_analyzer': {
            'market_analysis': {
                'trending_categories': ['productivity', 'wellness'],
                'competition_levels': {'productivity': 'high', 'wellness': 'medium'},
                'opportunities': ['mindfulness apps', 'remote work guides']
            },
            'recommendations': ['Focus on wellness subcategories']
        },
        'sales_monitor': {
            'performance_metrics': {
                'total_sales': 150,
                'total_revenue': 450.00,
                'growth_rate': 12.5
            },
            'insights': {
                'key_insights': ['Strong Q4 performance'],
                'recommendations': ['Increase marketing spend']
            }
        },
        'cover_designer': {
            'design_concept': {
                'primary_colors': ['#1E3A8A', '#FFFFFF'],
                'typography': {'title_font': 'Roboto Slab'},
                'layout_style': 'professional'
            }
        },
        'kdp_uploader': {
            'upload_status': 'success',
            'book_id': 'KDP123456',
            'metadata_validation': {'title_valid': True}
        }
    }


@pytest.fixture(autouse=True)
def reset_agent_registry():
    """Reset agent registry state before each test"""
    # Store original state
    original_agents = agent_registry._agents.copy()

    yield

    # Restore original state
    agent_registry._agents = original_agents


@pytest.fixture
def mock_successful_agent_run():
    """Mock successful agent run for testing"""
    def _mock_run(return_data: Dict[str, Any]):
        mock_result = Mock()
        mock_result.output.model_dump.return_value = return_data
        return mock_result
    
    return _mock_run


@pytest.fixture
def mock_failed_agent_run():
    """Mock failed agent run for testing"""
    def _mock_run(error_message: str = "Test error"):
        raise Exception(error_message)
    
    return _mock_run


@pytest.fixture(scope="session")
def test_data_dir():
    """Provide path to test data directory"""
    return os.path.join(os.path.dirname(__file__), 'data')


@pytest.fixture
def performance_thresholds():
    """Define performance thresholds for testing"""
    return {
        'single_agent_max_time': 5.0,  # seconds
        'workflow_max_time': 10.0,  # seconds
        'memory_max_increase': 100,  # MB
        'cpu_max_usage': 90,  # percent
        'min_success_rate': 0.95,  # 95%
        'max_concurrent_agents': 50
    }


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names"""
    for item in items:
        # Add markers based on test file names
        if "performance" in item.nodeid:
            item.add_marker(pytest.mark.performance)
        if "load" in item.nodeid:
            item.add_marker(pytest.mark.load)
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker for tests that might take longer
        if any(keyword in item.name for keyword in ["concurrent", "large_data", "sustained"]):
            item.add_marker(pytest.mark.slow)


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test"""
    yield
    
    # Force garbage collection
    import gc
    gc.collect()
    
    # Clear any cached data (if method exists)
    # Note: AgentRegistry doesn't have clear_cache method currently


# Environment setup
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment"""
    # Set test environment variables
    os.environ['TESTING'] = 'true'
    os.environ['LOG_LEVEL'] = 'WARNING'  # Reduce log noise during testing
    
    yield
    
    # Cleanup environment
    if 'TESTING' in os.environ:
        del os.environ['TESTING']
    if 'LOG_LEVEL' in os.environ:
        del os.environ['LOG_LEVEL']


# =====================================================
# SUPABASE TEST FIXTURES
# =====================================================

@pytest.fixture(scope="session")
def supabase_client():
    """
    Provide Supabase client for testing
    
    Requires SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables
    """
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY") 

    if not supabase_url or not supabase_key:
        pytest.skip("Supabase credentials not configured. Set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.")

    try:
        from app.utils.supabase.supabase_database import get_supabase

        client = get_supabase()

        # Test connection by trying to query a system table
        if client is None:
            raise Exception("Supabase client not initialized")
        result = client.table("users").select("id").limit(1).execute()

        return client

    except Exception as e:
        pytest.skip(f"Could not connect to Supabase: {e}")


@pytest.fixture
async def test_user(supabase_client):
    """Create a test user and clean up after test"""
    user_data = {
        "id": "test-user-12345",
        "email": "<EMAIL>", 
        "full_name": "Test User",
        "subscription_tier": "free"
    }
    
    # Create test user
    result = supabase_client.table("users").insert(user_data).execute()
    created_user = result.data[0] if result.data else user_data
    
    yield created_user
    
    # Cleanup test user and related data
    try:
        # Delete in order due to foreign key constraints
        supabase_client.table("feedback_metrics").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("sales_data").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("publications").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("books").delete().eq("user_id", created_user["id"]).execute()
        supabase_client.table("users").delete().eq("id", created_user["id"]).execute()
    except Exception as e:
        print(f"Warning: Could not cleanup test user: {e}")


@pytest.fixture
async def test_book(supabase_client, test_user):
    """Create a test book and clean up after test"""
    book_data = {
        "id": "test-book-67890",
        "user_id": test_user["id"],
        "title": "Test Book",
        "category": "business", 
        "content": "This is test content for the book.",
        "status": "draft",
        "word_count": 100
    }
    
    # Create test book
    result = supabase_client.table("books").insert(book_data).execute()
    created_book = result.data[0] if result.data else book_data
    
    yield created_book
    
    # Cleanup handled by test_user fixture


@pytest.fixture
def database_deps_with_supabase():
    """Provide DatabaseDependencies that use real Supabase"""
    from app.config import settings
    
    if not settings.supabase_url or not settings.supabase_service_key:
        pytest.skip("Supabase credentials not configured")
    
    return DatabaseDependencies(
        user_id="test-user-12345"
    )


# Test markers for database tests
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "load: mark test as a load test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "database: mark test as requiring database access"
    )
    config.addinivalue_line(
        "markers", "supabase: mark test as requiring Supabase connection"
    )
