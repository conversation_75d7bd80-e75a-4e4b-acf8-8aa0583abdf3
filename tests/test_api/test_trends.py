"""
Tests for Trends API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from datetime import datetime, timezone

from app.models.supabase_models import TrendAnalysis


class TestTrendsAnalysis:
    """Test trend analysis endpoints"""

    @patch('app.api.trends.execute_agent')
    def test_analyze_trends_success(self, mock_execute_agent, client, auth_headers, sample_trend_data):
        """Test successful trend analysis"""
        # Mock agent response
        mock_result = AsyncMock()
        mock_result.success = True
        mock_result.data = {
            "trends": [
                {"keyword": "AI", "score": 0.95, "volume": 10000},
                {"keyword": "automation", "score": 0.88, "volume": 8500}
            ],
            "market_analysis": {"opportunity_score": 0.85}
        }
        mock_execute_agent.return_value = mock_result
        
        response = client.post("/api/trends/analyze", 
                             json=sample_trend_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Trend analysis started"
        assert "analysis_id" in data
        assert data["categories"] == sample_trend_data["categories"]
        assert data["status"] == "analyzing"

    def test_analyze_trends_unauthorized(self, client, sample_trend_data):
        """Test trend analysis without authentication"""
        response = client.post("/api/trends/analyze", json=sample_trend_data)
        
        assert response.status_code == 403

    def test_analyze_trends_invalid_data(self, client, auth_headers):
        """Test trend analysis with invalid data"""
        invalid_data = {
            "categories": [],  # Empty categories
            "analysis_depth": "invalid_depth"
        }
        
        response = client.post("/api/trends/analyze", 
                             json=invalid_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_analyze_trends_missing_fields(self, client, auth_headers):
        """Test trend analysis with missing required fields"""
        incomplete_data = {
            "analysis_depth": "comprehensive"
            # Missing categories
        }
        
        response = client.post("/api/trends/analyze", 
                             json=incomplete_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    @patch('app.api.trends.execute_agent')
    def test_analyze_trends_agent_failure(self, mock_execute_agent, client, auth_headers, sample_trend_data):
        """Test trend analysis when agent fails"""
        # Mock agent failure
        mock_result = AsyncMock()
        mock_result.success = False
        mock_result.error = "Agent execution failed"
        mock_execute_agent.return_value = mock_result
        
        response = client.post("/api/trends/analyze", 
                             json=sample_trend_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200  # Request accepted, but will fail in background
        data = response.json()
        assert "analysis_id" in data

    def test_analyze_trends_multiple_categories(self, client, auth_headers):
        """Test trend analysis with multiple categories"""
        multi_category_data = {
            "categories": ["business", "technology", "health", "finance"],
            "analysis_depth": "comprehensive",
            "include_keywords": True
        }
        
        with patch('app.api.trends.execute_agent') as mock_agent:
            mock_result = AsyncMock()
            mock_result.success = True
            mock_result.data = {"trends": []}
            mock_agent.return_value = mock_result
            
            response = client.post("/api/trends/analyze", 
                                 json=multi_category_data, 
                                 headers=auth_headers)
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["categories"]) == 4


class TestTrendsRetrieval:
    """Test trend analysis retrieval endpoints"""

    def test_get_trend_analyses(self, client, auth_headers, test_trend_analysis):
        """Test getting user's trend analyses"""
        response = client.get("/api/trends/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check that our test analysis is in the response
        analysis_ids = [analysis["id"] for analysis in data]
        assert test_trend_analysis.id in analysis_ids

    def test_get_trend_analyses_unauthorized(self, client):
        """Test getting trend analyses without authentication"""
        response = client.get("/api/trends/")
        
        assert response.status_code == 403

    def test_get_trend_analyses_empty(self, client, auth_headers):
        """Test getting trend analyses when user has no analyses"""
        response = client.get("/api/trends/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_specific_trend_analysis(self, client, auth_headers, test_trend_analysis):
        """Test getting a specific trend analysis"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_trend_analysis.id
        assert data["categories"] == test_trend_analysis.categories
        assert data["status"] == test_trend_analysis.status

    def test_get_specific_trend_analysis_unauthorized(self, client, test_trend_analysis):
        """Test getting trend analysis without authentication"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}")
        
        assert response.status_code == 403

    def test_get_specific_trend_analysis_not_found(self, client, auth_headers):
        """Test getting a non-existent trend analysis"""
        response = client.get("/api/trends/99999", headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_other_user_analysis(self, client, auth_headers, db_session):
        """Test getting another user's analysis (should fail)"""
        # Create an analysis for a different user
        from app.models.user import User
        
        import uuid
        unique_email = f"other-{uuid.uuid4().hex[:8]}@example.com"

        other_user = User(
            email=unique_email,
            full_name="Other User",
            password_hash="hashed",
            subscription_tier="free",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_user)
        db_session.commit()
        
        other_analysis = TrendAnalysis(
            user_id=other_user.id,
            request_config={"categories": ["technology"]},
            status="completed",
            result={"trends": []},
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_analysis)
        db_session.commit()
        
        response = client.get(f"/api/trends/{other_analysis.id}", 
                            headers=auth_headers)
        
        assert response.status_code == 404  # Should not find other user's analysis


class TestTrendsOpportunities:
    """Test trend opportunities endpoints"""

    def test_get_opportunities_completed_analysis(self, client, auth_headers, test_trend_analysis):
        """Test getting opportunities from completed analysis"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}/opportunities", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "opportunities" in data
        assert "total_found" in data
        assert "analysis_id" in data
        assert "status" in data
        assert isinstance(data["opportunities"], list)

    def test_get_opportunities_with_limit(self, client, auth_headers, test_trend_analysis):
        """Test getting opportunities with custom limit"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}/opportunities?limit=5", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["opportunities"]) <= 5

    def test_get_opportunities_incomplete_analysis(self, client, auth_headers, db_session, test_user):
        """Test getting opportunities from incomplete analysis"""
        # Create an incomplete analysis
        incomplete_analysis = TrendAnalysis(
            user_id=test_user.id,
            request_config={"categories": ["business"]},
            status="analyzing",
            result=None,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(incomplete_analysis)
        db_session.commit()
        
        response = client.get(f"/api/trends/{incomplete_analysis.id}/opportunities", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Analysis not completed yet"
        assert data["opportunities"] == []
        assert data["status"] == "analyzing"

    def test_get_opportunities_unauthorized(self, client, test_trend_analysis):
        """Test getting opportunities without authentication"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}/opportunities")
        
        assert response.status_code == 403

    def test_get_opportunities_nonexistent_analysis(self, client, auth_headers):
        """Test getting opportunities from non-existent analysis"""
        response = client.get("/api/trends/99999/opportunities", headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_opportunities_invalid_limit(self, client, auth_headers, test_trend_analysis):
        """Test getting opportunities with invalid limit"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}/opportunities?limit=-1", 
                            headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_get_opportunities_large_limit(self, client, auth_headers, test_trend_analysis):
        """Test getting opportunities with very large limit"""
        response = client.get(f"/api/trends/{test_trend_analysis.id}/opportunities?limit=1000", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        # Should return all available opportunities, not necessarily 1000
        assert isinstance(data["opportunities"], list)


class TestTrendsManagement:
    """Test trend analysis management endpoints"""

    def test_delete_trend_analysis(self, client, auth_headers, test_trend_analysis):
        """Test deleting a trend analysis"""
        response = client.delete(f"/api/trends/{test_trend_analysis.id}", 
                               headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Trend analysis deleted successfully"
        assert data["analysis_id"] == test_trend_analysis.id
        
        # Verify analysis is deleted
        get_response = client.get(f"/api/trends/{test_trend_analysis.id}", 
                                headers=auth_headers)
        assert get_response.status_code == 404

    def test_delete_trend_analysis_unauthorized(self, client, test_trend_analysis):
        """Test deleting trend analysis without authentication"""
        response = client.delete(f"/api/trends/{test_trend_analysis.id}")
        
        assert response.status_code == 403

    def test_delete_nonexistent_analysis(self, client, auth_headers):
        """Test deleting non-existent analysis"""
        response = client.delete("/api/trends/99999", headers=auth_headers)
        
        assert response.status_code == 404


class TestTrendsDashboard:
    """Test trends dashboard endpoints"""

    def test_get_trends_dashboard(self, client, auth_headers, test_trend_analysis):
        """Test getting trends dashboard"""
        response = client.get("/api/trends/dashboard/summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_analyses" in data
        assert "completed_count" in data
        assert "analyzing_count" in data
        assert "failed_count" in data
        assert "success_rate" in data
        assert "top_categories" in data
        assert "recent_analyses" in data
        assert isinstance(data["recent_analyses"], list)
        assert isinstance(data["top_categories"], list)

    def test_get_trends_dashboard_unauthorized(self, client):
        """Test getting dashboard without authentication"""
        response = client.get("/api/trends/dashboard/summary")
        
        assert response.status_code == 403

    def test_get_trends_dashboard_no_analyses(self, client, auth_headers):
        """Test getting dashboard with no analyses"""
        response = client.get("/api/trends/dashboard/summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_analyses"] == 0
        assert data["success_rate"] == 0
        assert data["recent_analyses"] == []
        assert data["top_categories"] == []

    def test_get_trends_dashboard_multiple_statuses(self, client, auth_headers, db_session, test_user):
        """Test dashboard with analyses in different statuses"""
        # Create analyses with different statuses
        analyses = [
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["business"]},
                status="completed",
                result={"trends": []},
                created_at=datetime.now(timezone.utc)
            ),
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["technology"]},
                status="analyzing",
                result=None,
                created_at=datetime.now(timezone.utc)
            ),
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["health"]},
                status="failed",
                result=None,
                error_message="Test error",
                created_at=datetime.now(timezone.utc)
            )
        ]
        
        db_session.add_all(analyses)
        db_session.commit()
        
        response = client.get("/api/trends/dashboard/summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_analyses"] >= 3
        assert data["completed_count"] >= 1
        assert data["analyzing_count"] >= 1
        assert data["failed_count"] >= 1
        assert 0 <= data["success_rate"] <= 100

    def test_get_trends_dashboard_top_categories(self, client, auth_headers, db_session, test_user):
        """Test dashboard top categories calculation"""
        # Create multiple analyses with overlapping categories
        analyses = [
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["business", "technology"]},
                status="completed",
                result={"trends": []},
                created_at=datetime.now(timezone.utc)
            ),
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["business", "finance"]},
                status="completed",
                result={"trends": []},
                created_at=datetime.now(timezone.utc)
            ),
            TrendAnalysis(
                user_id=test_user.id,
                request_config={"categories": ["technology"]},
                status="completed",
                result={"trends": []},
                created_at=datetime.now(timezone.utc)
            )
        ]
        
        db_session.add_all(analyses)
        db_session.commit()
        
        response = client.get("/api/trends/dashboard/summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Business should be top category (appears in 2 analyses)
        top_categories = data["top_categories"]
        if top_categories:
            assert top_categories[0]["category"] == "business"
            assert top_categories[0]["count"] >= 2
