"""
Tests for Monitoring API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone


class TestMonitoringHealth:
    """Test health check endpoints"""

    def test_health_check(self, client):
        """Test basic health check endpoint"""
        response = client.get("/api/monitoring/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
        assert "uptime" in data

    def test_health_check_no_auth_required(self, client):
        """Test that health check doesn't require authentication"""
        response = client.get("/api/monitoring/health")
        
        assert response.status_code == 200
        # Should work without any authentication headers

    def test_health_check_response_format(self, client):
        """Test health check response format"""
        response = client.get("/api/monitoring/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Check required fields
        required_fields = ["status", "timestamp", "version", "uptime"]
        for field in required_fields:
            assert field in data
        
        # Check data types
        assert isinstance(data["status"], str)
        assert isinstance(data["timestamp"], str)
        assert isinstance(data["uptime"], (int, float))

    def test_health_check_multiple_calls(self, client):
        """Test multiple health check calls"""
        responses = []
        for _ in range(5):
            response = client.get("/api/monitoring/health")
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
            assert response.json()["status"] == "healthy"


class TestMonitoringDetailed:
    """Test detailed health check endpoints"""

    def test_detailed_health_check(self, client, auth_headers):
        """Test detailed health check with authentication"""
        response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "services" in data
        assert "database" in data
        assert "memory" in data
        assert "disk" in data

    def test_detailed_health_check_unauthorized(self, client):
        """Test detailed health check without authentication"""
        response = client.get("/api/monitoring/health/detailed")
        
        assert response.status_code == 403

    def test_detailed_health_check_services(self, client, auth_headers):
        """Test detailed health check services status"""
        response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        services = data["services"]
        assert isinstance(services, dict)
        
        # Check for expected services
        expected_services = ["database", "redis", "celery"]
        for service in expected_services:
            if service in services:
                assert "status" in services[service]
                assert services[service]["status"] in ["healthy", "unhealthy", "unknown"]

    def test_detailed_health_check_database_status(self, client, auth_headers):
        """Test database status in detailed health check"""
        response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        database = data["database"]
        assert "status" in database
        assert "connection_pool" in database
        assert "response_time_ms" in database

    def test_detailed_health_check_memory_info(self, client, auth_headers):
        """Test memory information in detailed health check"""
        response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        memory = data["memory"]
        assert "usage_percent" in memory
        assert "available_mb" in memory
        assert "total_mb" in memory
        assert isinstance(memory["usage_percent"], (int, float))

    def test_detailed_health_check_disk_info(self, client, auth_headers):
        """Test disk information in detailed health check"""
        response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        disk = data["disk"]
        assert "usage_percent" in disk
        assert "free_gb" in disk
        assert "total_gb" in disk
        assert isinstance(disk["usage_percent"], (int, float))


class TestMonitoringMetrics:
    """Test metrics endpoints"""

    def test_get_system_metrics(self, client, auth_headers):
        """Test getting system metrics"""
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "cpu" in data
        assert "memory" in data
        assert "disk" in data
        assert "network" in data
        assert "timestamp" in data

    def test_get_system_metrics_unauthorized(self, client):
        """Test getting metrics without authentication"""
        response = client.get("/api/monitoring/metrics")
        
        assert response.status_code == 403

    def test_get_system_metrics_cpu_info(self, client, auth_headers):
        """Test CPU metrics"""
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        cpu = data["cpu"]
        assert "usage_percent" in cpu
        assert "load_average" in cpu
        assert isinstance(cpu["usage_percent"], (int, float))
        assert 0 <= cpu["usage_percent"] <= 100

    def test_get_system_metrics_memory_info(self, client, auth_headers):
        """Test memory metrics"""
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        memory = data["memory"]
        assert "usage_percent" in memory
        assert "available_mb" in memory
        assert "used_mb" in memory
        assert "total_mb" in memory

    def test_get_system_metrics_disk_info(self, client, auth_headers):
        """Test disk metrics"""
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        disk = data["disk"]
        assert "usage_percent" in disk
        assert "free_gb" in disk
        assert "used_gb" in disk
        assert "total_gb" in disk

    def test_get_system_metrics_network_info(self, client, auth_headers):
        """Test network metrics"""
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        network = data["network"]
        assert "bytes_sent" in network
        assert "bytes_received" in network
        assert "packets_sent" in network
        assert "packets_received" in network


class TestMonitoringLogs:
    """Test log endpoints"""

    def test_get_recent_logs(self, client, auth_headers):
        """Test getting recent logs"""
        response = client.get("/api/monitoring/logs", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "logs" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
        assert isinstance(data["logs"], list)

    def test_get_recent_logs_unauthorized(self, client):
        """Test getting logs without authentication"""
        response = client.get("/api/monitoring/logs")
        
        assert response.status_code == 403

    def test_get_recent_logs_with_level_filter(self, client, auth_headers):
        """Test getting logs with level filter"""
        response = client.get("/api/monitoring/logs?level=ERROR", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data["logs"], list)

    def test_get_recent_logs_with_pagination(self, client, auth_headers):
        """Test getting logs with pagination"""
        response = client.get("/api/monitoring/logs?page=1&page_size=10", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert len(data["logs"]) <= 10

    def test_get_recent_logs_invalid_level(self, client, auth_headers):
        """Test getting logs with invalid level"""
        response = client.get("/api/monitoring/logs?level=INVALID", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_get_recent_logs_invalid_pagination(self, client, auth_headers):
        """Test getting logs with invalid pagination"""
        response = client.get("/api/monitoring/logs?page=-1", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestMonitoringAlerts:
    """Test alert endpoints"""

    def test_get_active_alerts(self, client, auth_headers):
        """Test getting active alerts"""
        response = client.get("/api/monitoring/alerts", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "alerts" in data
        assert "total_count" in data
        assert isinstance(data["alerts"], list)

    def test_get_active_alerts_unauthorized(self, client):
        """Test getting alerts without authentication"""
        response = client.get("/api/monitoring/alerts")
        
        assert response.status_code == 403

    def test_get_active_alerts_with_severity_filter(self, client, auth_headers):
        """Test getting alerts with severity filter"""
        response = client.get("/api/monitoring/alerts?severity=critical", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data["alerts"], list)

    def test_create_test_alert(self, client, auth_headers):
        """Test creating a test alert"""
        alert_data = {
            "message": "Test alert",
            "severity": "warning",
            "component": "test"
        }
        
        response = client.post("/api/monitoring/alerts/test", 
                             json=alert_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Test alert created"

    def test_create_test_alert_unauthorized(self, client):
        """Test creating test alert without authentication"""
        alert_data = {
            "message": "Test alert",
            "severity": "warning",
            "component": "test"
        }
        
        response = client.post("/api/monitoring/alerts/test", json=alert_data)
        
        assert response.status_code == 403

    def test_create_test_alert_invalid_data(self, client, auth_headers):
        """Test creating test alert with invalid data"""
        invalid_data = {
            "message": "",  # Empty message
            "severity": "invalid_severity"
        }
        
        response = client.post("/api/monitoring/alerts/test", 
                             json=invalid_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestMonitoringPerformance:
    """Test performance monitoring endpoints"""

    def test_get_performance_stats(self, client, auth_headers):
        """Test getting performance statistics"""
        response = client.get("/api/monitoring/performance", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "response_times" in data
        assert "throughput" in data
        assert "error_rates" in data
        assert "active_connections" in data

    def test_get_performance_stats_unauthorized(self, client):
        """Test getting performance stats without authentication"""
        response = client.get("/api/monitoring/performance")
        
        assert response.status_code == 403

    def test_get_performance_stats_with_timeframe(self, client, auth_headers):
        """Test getting performance stats with timeframe"""
        response = client.get("/api/monitoring/performance?timeframe=1h", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "timeframe" in data
        assert data["timeframe"] == "1h"

    def test_get_performance_stats_invalid_timeframe(self, client, auth_headers):
        """Test getting performance stats with invalid timeframe"""
        response = client.get("/api/monitoring/performance?timeframe=invalid", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestMonitoringValidation:
    """Test input validation for monitoring endpoints"""

    def test_invalid_query_parameters(self, client, auth_headers):
        """Test endpoints with invalid query parameters"""
        # Test invalid page number
        response = client.get("/api/monitoring/logs?page=abc", headers=auth_headers)
        assert response.status_code == 422

        # Test invalid page size
        response = client.get("/api/monitoring/logs?page_size=-1", headers=auth_headers)
        assert response.status_code == 422

    def test_large_page_size(self, client, auth_headers):
        """Test endpoints with very large page size"""
        response = client.get("/api/monitoring/logs?page_size=10000", headers=auth_headers)
        
        # Should either limit the page size or return validation error
        assert response.status_code in [200, 422]

    def test_malformed_request_data(self, client, auth_headers):
        """Test endpoints with malformed request data"""
        response = client.post("/api/monitoring/alerts/test", 
                             data="invalid json", 
                             headers={**auth_headers, "Content-Type": "application/json"})
        
        assert response.status_code == 422


class TestMonitoringErrorHandling:
    """Test error handling in monitoring endpoints"""

    @patch('app.api.monitoring.psutil')
    def test_system_metrics_error_handling(self, mock_psutil, client, auth_headers):
        """Test handling of system metrics collection errors"""
        # Mock psutil to raise an exception
        mock_psutil.cpu_percent.side_effect = Exception("System error")
        
        response = client.get("/api/monitoring/metrics", headers=auth_headers)
        
        # Should handle the error gracefully
        assert response.status_code in [200, 500]

    def test_concurrent_monitoring_requests(self, client, auth_headers):
        """Test concurrent monitoring requests"""
        # Simulate concurrent requests
        responses = []
        for _ in range(5):
            response = client.get("/api/monitoring/health", headers=auth_headers)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200

    def test_monitoring_endpoints_performance(self, client, auth_headers):
        """Test that monitoring endpoints respond quickly"""
        import time
        
        start_time = time.time()
        response = client.get("/api/monitoring/health", headers=auth_headers)
        end_time = time.time()
        
        assert response.status_code == 200
        # Health check should be fast (under 1 second)
        assert (end_time - start_time) < 1.0


class TestMonitoringIntegration:
    """Test integration scenarios for monitoring"""

    def test_monitoring_workflow(self, client, auth_headers):
        """Test complete monitoring workflow"""
        # 1. Check basic health
        health_response = client.get("/api/monitoring/health")
        assert health_response.status_code == 200
        assert health_response.json()["status"] == "healthy"
        
        # 2. Get detailed health check
        detailed_response = client.get("/api/monitoring/health/detailed", headers=auth_headers)
        assert detailed_response.status_code == 200
        
        # 3. Get system metrics
        metrics_response = client.get("/api/monitoring/metrics", headers=auth_headers)
        assert metrics_response.status_code == 200
        
        # 4. Check logs
        logs_response = client.get("/api/monitoring/logs", headers=auth_headers)
        assert logs_response.status_code == 200
        
        # 5. Check alerts
        alerts_response = client.get("/api/monitoring/alerts", headers=auth_headers)
        assert alerts_response.status_code == 200

    def test_monitoring_data_consistency(self, client, auth_headers):
        """Test that monitoring data is consistent across calls"""
        # Make multiple calls and check for reasonable consistency
        responses = []
        for _ in range(3):
            response = client.get("/api/monitoring/metrics", headers=auth_headers)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
        
        # Memory usage should be reasonably consistent (within 10%)
        memory_usages = [r.json()["memory"]["usage_percent"] for r in responses]
        if len(memory_usages) > 1:
            max_usage = max(memory_usages)
            min_usage = min(memory_usages)
            # Allow for some variation but not too much
            assert (max_usage - min_usage) < 20  # Less than 20% difference
