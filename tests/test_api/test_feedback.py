"""
Tests for Feedback API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from datetime import datetime, timezone


class TestFeedbackSubmission:
    """Test feedback submission endpoints"""

    def test_submit_book_feedback(self, client, auth_headers, test_book):
        """Test submitting feedback for a book"""
        feedback_data = {
            "book_id": test_book.id,
            "approved": True,
            "approval_time_seconds": 120.5,
            "quality_rating": 4,
            "user_notes": "Great book, well written"
        }
        
        response = client.post("/api/feedback/book", 
                             json=feedback_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Feedback submitted successfully"
        assert "feedback_id" in data

    def test_submit_book_feedback_unauthorized(self, client, test_book):
        """Test submitting feedback without authentication"""
        feedback_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 4
        }
        
        response = client.post("/api/feedback/book", json=feedback_data)
        
        assert response.status_code == 403

    def test_submit_book_feedback_nonexistent_book(self, client, auth_headers):
        """Test submitting feedback for non-existent book"""
        feedback_data = {
            "book_id": 99999,
            "approved": False,
            "rejection_reason": "Quality issues"
        }
        
        response = client.post("/api/feedback/book", 
                             json=feedback_data, 
                             headers=auth_headers)
        
        assert response.status_code == 404

    def test_submit_book_feedback_invalid_data(self, client, auth_headers, test_book):
        """Test submitting feedback with invalid data"""
        invalid_data = {
            "book_id": test_book.id,
            "approved": "not_boolean",  # Should be boolean
            "quality_rating": 10,  # Should be 1-5
            "approval_time_seconds": -1  # Should be positive
        }
        
        response = client.post("/api/feedback/book", 
                             json=invalid_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_submit_book_feedback_missing_fields(self, client, auth_headers):
        """Test submitting feedback with missing required fields"""
        incomplete_data = {
            "approved": True
            # Missing book_id
        }
        
        response = client.post("/api/feedback/book", 
                             json=incomplete_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_submit_rejection_feedback(self, client, auth_headers, test_book):
        """Test submitting rejection feedback"""
        rejection_data = {
            "book_id": test_book.id,
            "approved": False,
            "rejection_reason": "Content quality needs improvement",
            "quality_rating": 2,
            "user_notes": "Needs more research and better structure"
        }
        
        response = client.post("/api/feedback/book", 
                             json=rejection_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Feedback submitted successfully"

    def test_submit_approval_feedback(self, client, auth_headers, test_book):
        """Test submitting approval feedback"""
        approval_data = {
            "book_id": test_book.id,
            "approved": True,
            "approval_time_seconds": 180.0,
            "quality_rating": 5,
            "user_notes": "Excellent content and structure"
        }
        
        response = client.post("/api/feedback/book", 
                             json=approval_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200


class TestFeedbackRetrieval:
    """Test feedback retrieval endpoints"""

    def test_get_user_feedback(self, client, auth_headers):
        """Test getting user's feedback history"""
        response = client.get("/api/feedback/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "feedback" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
        assert isinstance(data["feedback"], list)

    def test_get_user_feedback_unauthorized(self, client):
        """Test getting feedback without authentication"""
        response = client.get("/api/feedback/")
        
        assert response.status_code == 403

    def test_get_user_feedback_with_pagination(self, client, auth_headers):
        """Test getting feedback with pagination"""
        response = client.get("/api/feedback/?page=1&page_size=10", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert len(data["feedback"]) <= 10

    def test_get_user_feedback_with_filter(self, client, auth_headers):
        """Test getting feedback with approval filter"""
        # Test approved feedback only
        response = client.get("/api/feedback/?approved=true", headers=auth_headers)
        assert response.status_code == 200
        
        # Test rejected feedback only
        response = client.get("/api/feedback/?approved=false", headers=auth_headers)
        assert response.status_code == 200

    def test_get_book_feedback(self, client, auth_headers, test_book):
        """Test getting feedback for a specific book"""
        response = client.get(f"/api/feedback/book/{test_book.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "book_id" in data
        assert "feedback_history" in data
        assert data["book_id"] == test_book.id

    def test_get_book_feedback_unauthorized(self, client, test_book):
        """Test getting book feedback without authentication"""
        response = client.get(f"/api/feedback/book/{test_book.id}")
        
        assert response.status_code == 403

    def test_get_book_feedback_nonexistent_book(self, client, auth_headers):
        """Test getting feedback for non-existent book"""
        response = client.get("/api/feedback/book/99999", headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_book_feedback_other_user_book(self, client, auth_headers, db_session):
        """Test getting feedback for another user's book"""
        # Create a book for a different user
        from app.models.supabase_models import User, Book
        
        other_user = User(
            email="<EMAIL>",
            full_name="Other User",
            hashed_password="hashed",
            subscription_tier="free",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_user)
        db_session.commit()
        
        other_book = Book(
            title="Other User's Book",
            category="business",
            status="draft",
            user_id=other_user.id,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_book)
        db_session.commit()
        
        response = client.get(f"/api/feedback/book/{other_book.id}", headers=auth_headers)
        
        assert response.status_code == 404  # Should not find other user's book


class TestFeedbackAnalytics:
    """Test feedback analytics endpoints"""

    def test_get_feedback_analytics(self, client, auth_headers):
        """Test getting feedback analytics"""
        response = client.get("/api/feedback/analytics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_feedback" in data
        assert "approval_rate" in data
        assert "average_quality_rating" in data
        assert "feedback_trends" in data
        assert "common_rejection_reasons" in data

    def test_get_feedback_analytics_unauthorized(self, client):
        """Test getting feedback analytics without authentication"""
        response = client.get("/api/feedback/analytics")
        
        assert response.status_code == 403

    def test_get_feedback_analytics_with_timeframe(self, client, auth_headers):
        """Test getting feedback analytics with timeframe"""
        response = client.get("/api/feedback/analytics?timeframe=30d", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "timeframe" in data
        assert data["timeframe"] == "30d"

    def test_get_feedback_analytics_invalid_timeframe(self, client, auth_headers):
        """Test getting feedback analytics with invalid timeframe"""
        response = client.get("/api/feedback/analytics?timeframe=invalid", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_get_feedback_analytics_by_category(self, client, auth_headers):
        """Test getting feedback analytics by category"""
        response = client.get("/api/feedback/analytics?group_by=category", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "category_breakdown" in data


class TestFeedbackVERL:
    """Test VERL (feedback for reinforcement learning) endpoints"""

    @patch('app.api.feedback.LiveFeedbackCollector')
    def test_submit_verl_feedback(self, mock_collector, client, auth_headers, test_book):
        """Test submitting feedback for VERL training"""
        mock_instance = mock_collector.return_value
        mock_instance.capture_approval_feedback.return_value = AsyncMock()
        
        verl_data = {
            "book_id": test_book.id,
            "approved": True,
            "approval_time_seconds": 150.0,
            "quality_rating": 4,
            "user_notes": "Good quality content"
        }
        
        response = client.post("/api/feedback/verl", 
                             json=verl_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "VERL feedback captured successfully"

    def test_submit_verl_feedback_unauthorized(self, client, test_book):
        """Test submitting VERL feedback without authentication"""
        verl_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 4
        }
        
        response = client.post("/api/feedback/verl", json=verl_data)
        
        assert response.status_code == 403

    @patch('app.api.feedback.LiveFeedbackCollector')
    def test_submit_verl_feedback_with_rejection(self, mock_collector, client, auth_headers, test_book):
        """Test submitting VERL feedback for rejection"""
        mock_instance = mock_collector.return_value
        mock_instance.capture_approval_feedback.return_value = AsyncMock()
        
        verl_data = {
            "book_id": test_book.id,
            "approved": False,
            "rejection_reason": "Content needs improvement",
            "quality_rating": 2,
            "user_notes": "Requires more research"
        }
        
        response = client.post("/api/feedback/verl", 
                             json=verl_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200

    def test_get_verl_training_status(self, client, auth_headers):
        """Test getting VERL training status"""
        response = client.get("/api/feedback/verl/status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "training_status" in data
        assert "feedback_count" in data
        assert "last_training_run" in data
        assert "model_version" in data

    def test_get_verl_training_status_unauthorized(self, client):
        """Test getting VERL training status without authentication"""
        response = client.get("/api/feedback/verl/status")
        
        assert response.status_code == 403


class TestFeedbackBulkOperations:
    """Test bulk feedback operations"""

    def test_submit_bulk_feedback(self, client, auth_headers, test_book, db_session, test_user):
        """Test submitting feedback for multiple books"""
        # Create additional books
        from app.models.supabase_models import Book
        
        book2 = Book(
            title="Test Book 2",
            category="technology",
            status="draft",
            user_id=test_user.id,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(book2)
        db_session.commit()
        
        bulk_feedback = {
            "feedback_items": [
                {
                    "book_id": test_book.id,
                    "approved": True,
                    "quality_rating": 4,
                    "user_notes": "Good book"
                },
                {
                    "book_id": book2.id,
                    "approved": False,
                    "rejection_reason": "Needs improvement",
                    "quality_rating": 2
                }
            ]
        }
        
        response = client.post("/api/feedback/bulk", 
                             json=bulk_feedback, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Bulk feedback submitted successfully"
        assert "processed_count" in data
        assert data["processed_count"] == 2

    def test_submit_bulk_feedback_unauthorized(self, client):
        """Test submitting bulk feedback without authentication"""
        bulk_feedback = {
            "feedback_items": [
                {
                    "book_id": 1,
                    "approved": True,
                    "quality_rating": 4
                }
            ]
        }
        
        response = client.post("/api/feedback/bulk", json=bulk_feedback)
        
        assert response.status_code == 403

    def test_submit_bulk_feedback_invalid_data(self, client, auth_headers):
        """Test submitting bulk feedback with invalid data"""
        invalid_bulk = {
            "feedback_items": [
                {
                    "book_id": "invalid_id",  # Should be integer
                    "approved": "not_boolean",  # Should be boolean
                    "quality_rating": 10  # Should be 1-5
                }
            ]
        }
        
        response = client.post("/api/feedback/bulk", 
                             json=invalid_bulk, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_submit_bulk_feedback_empty_list(self, client, auth_headers):
        """Test submitting bulk feedback with empty list"""
        empty_bulk = {
            "feedback_items": []
        }
        
        response = client.post("/api/feedback/bulk", 
                             json=empty_bulk, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestFeedbackValidation:
    """Test input validation for feedback endpoints"""

    def test_invalid_book_id_format(self, client, auth_headers):
        """Test feedback with invalid book ID format"""
        feedback_data = {
            "book_id": "invalid_id",
            "approved": True,
            "quality_rating": 4
        }
        
        response = client.post("/api/feedback/book", 
                             json=feedback_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_negative_book_id(self, client, auth_headers):
        """Test feedback with negative book ID"""
        feedback_data = {
            "book_id": -1,
            "approved": True,
            "quality_rating": 4
        }
        
        response = client.post("/api/feedback/book", 
                             json=feedback_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_invalid_quality_rating(self, client, auth_headers, test_book):
        """Test feedback with invalid quality rating"""
        # Test rating too high
        high_rating_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 10  # Should be 1-5
        }
        
        response = client.post("/api/feedback/book", 
                             json=high_rating_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error
        
        # Test rating too low
        low_rating_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 0  # Should be 1-5
        }
        
        response = client.post("/api/feedback/book", 
                             json=low_rating_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_negative_approval_time(self, client, auth_headers, test_book):
        """Test feedback with negative approval time"""
        feedback_data = {
            "book_id": test_book.id,
            "approved": True,
            "approval_time_seconds": -10.0  # Should be positive
        }
        
        response = client.post("/api/feedback/book", 
                             json=feedback_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_invalid_pagination_parameters(self, client, auth_headers):
        """Test feedback endpoints with invalid pagination"""
        # Test invalid page number
        response = client.get("/api/feedback/?page=abc", headers=auth_headers)
        assert response.status_code == 422

        # Test invalid page size
        response = client.get("/api/feedback/?page_size=-1", headers=auth_headers)
        assert response.status_code == 422


class TestFeedbackErrorHandling:
    """Test error handling in feedback endpoints"""

    def test_malformed_feedback_request(self, client, auth_headers):
        """Test handling of malformed feedback request"""
        response = client.post("/api/feedback/book", 
                             data="invalid json", 
                             headers={**auth_headers, "Content-Type": "application/json"})
        
        assert response.status_code == 422  # Validation error

    def test_empty_feedback_request(self, client, auth_headers):
        """Test handling of empty feedback request"""
        response = client.post("/api/feedback/book", 
                             json={}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    @patch('app.api.feedback.LiveFeedbackCollector')
    def test_verl_collector_error(self, mock_collector, client, auth_headers, test_book):
        """Test handling of VERL collector errors"""
        # Mock collector to raise an exception
        mock_instance = mock_collector.return_value
        mock_instance.capture_approval_feedback.side_effect = Exception("VERL error")
        
        verl_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 4
        }
        
        response = client.post("/api/feedback/verl", 
                             json=verl_data, 
                             headers=auth_headers)
        
        # Should handle the error gracefully
        assert response.status_code in [200, 500]

    def test_concurrent_feedback_submission(self, client, auth_headers, test_book):
        """Test concurrent feedback submissions"""
        feedback_data = {
            "book_id": test_book.id,
            "approved": True,
            "quality_rating": 4
        }
        
        # Simulate concurrent requests
        responses = []
        for _ in range(3):
            response = client.post("/api/feedback/book", 
                                 json=feedback_data, 
                                 headers=auth_headers)
            responses.append(response)
        
        # All should succeed (or handle duplicates gracefully)
        for response in responses:
            assert response.status_code in [200, 409]  # 409 for conflict if duplicates not allowed
