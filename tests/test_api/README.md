# API Tests for Publish AI

This directory contains comprehensive tests for all API endpoints in the Publish AI application.

## 📁 Test Structure

```
tests/test_api/
├── __init__.py                 # Package initialization
├── conftest.py                 # Shared test fixtures and configuration
├── run_tests.py               # Test runner script
├── README.md                  # This file
├── test_auth.py               # Authentication API tests
├── test_books.py              # Books API tests
├── test_predictions.py        # Predictions API tests
├── test_publications.py       # Publications API tests
├── test_trends.py             # Trends API tests
├── test_monitoring.py         # Monitoring API tests
├── test_analytics.py          # Analytics API tests
└── test_feedback.py           # Feedback API tests
```

## 🧪 Test Coverage

### Authentication API (`test_auth.py`)
- **User Registration**: Valid/invalid data, duplicate emails, password validation
- **User Login**: Valid/invalid credentials, inactive users, token generation
- **User Profile**: Get/update profile, authentication requirements
- **Security**: Password protection, SQL injection prevention, token validation

### Books API (`test_books.py`)
- **Manuscript Generation**: Success/failure scenarios, agent integration
- **Book Retrieval**: User books, specific books, authorization checks
- **Book Approval**: Approve/reject manuscripts, status validation
- **Cover Generation**: Cover creation for approved books
- **Book Management**: Delete books, validation, error handling

### Predictions API (`test_predictions.py`)
- **Prediction Generation**: Book performance predictions, service availability
- **Model Management**: Training, status checks, premium features
- **Dashboard**: Analytics and metrics display
- **Accuracy Tracking**: Feedback collection and model improvement

### Publications API (`test_publications.py`)
- **Book Publishing**: KDP integration, Celery task management
- **Publication Management**: Status tracking, cancellation
- **Dashboard**: Publication analytics and statistics
- **Error Handling**: Service availability, task failures

### Trends API (`test_trends.py`)
- **Trend Analysis**: Market research, agent integration
- **Opportunity Discovery**: Actionable insights extraction
- **Analysis Management**: CRUD operations, user isolation
- **Dashboard**: Trend statistics and top categories

### Monitoring API (`test_monitoring.py`)
- **Health Checks**: Basic and detailed system health
- **System Metrics**: CPU, memory, disk, network monitoring
- **Logging**: Log retrieval and filtering
- **Alerts**: Alert management and notifications
- **Performance**: Response time and throughput monitoring

### Analytics API (`test_analytics.py`)
- **Overview Analytics**: Total metrics and KPIs
- **Book Analytics**: Individual book performance
- **Sales Analytics**: Revenue and sales tracking
- **Report Generation**: PDF/CSV report creation
- **Data Export**: Analytics data export functionality

### Feedback API (`test_feedback.py`)
- **Feedback Submission**: Book approval/rejection feedback
- **VERL Integration**: Reinforcement learning feedback collection
- **Bulk Operations**: Multiple feedback submissions
- **Analytics**: Feedback trends and statistics

## 🚀 Running Tests

### Quick Start

```bash
# Run all API tests
python tests/test_api/run_tests.py --all

# Run specific API tests
python tests/test_api/run_tests.py --auth
python tests/test_api/run_tests.py --books
python tests/test_api/run_tests.py --predictions

# Run with coverage
python tests/test_api/run_tests.py --all --coverage

# Run in parallel (faster)
python tests/test_api/run_tests.py --all --parallel
```

### Using pytest directly

```bash
# Run all API tests
pytest tests/test_api/

# Run specific test file
pytest tests/test_api/test_auth.py

# Run specific test class
pytest tests/test_api/test_auth.py::TestAuthLogin

# Run specific test method
pytest tests/test_api/test_auth.py::TestAuthLogin::test_login_valid_credentials

# Run with coverage
pytest tests/test_api/ --cov=app.api --cov-report=html

# Run in parallel
pytest tests/test_api/ -n auto
```

### Test Categories

```bash
# Unit tests only
python tests/test_api/run_tests.py --unit

# Integration tests only
python tests/test_api/run_tests.py --integration

# Performance tests only
python tests/test_api/run_tests.py --performance

# Smoke tests (critical functionality)
python tests/test_api/run_tests.py --smoke
```

## 🔧 Test Configuration

### Environment Setup

The tests use a separate SQLite database for isolation:

```python
# In conftest.py
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
```

### Test Fixtures

Common fixtures available in all test files:

- `client`: FastAPI test client
- `db_session`: Database session with automatic rollback
- `test_user`: Standard test user
- `premium_user`: Premium test user
- `auth_headers`: Authentication headers for test user
- `test_book`: Sample book for testing
- `approved_book`: Approved book for publication tests
- `test_publication`: Sample publication record
- `test_trend_analysis`: Sample trend analysis
- `test_prediction`: Sample sales prediction

### Sample Data Fixtures

- `sample_book_data`: Valid book creation data
- `sample_publication_data`: Valid publication request data
- `sample_trend_data`: Valid trend analysis request data

## 📊 Test Metrics

### Coverage Goals
- **Minimum Coverage**: 80% overall
- **Critical Paths**: 95% coverage for authentication and core business logic
- **Error Handling**: 90% coverage for error scenarios

### Test Types Distribution
- **Unit Tests**: ~60% (individual function/method testing)
- **Integration Tests**: ~30% (API endpoint testing)
- **Performance Tests**: ~10% (response time and load testing)

## 🛠️ Development Guidelines

### Writing New Tests

1. **Follow the existing pattern**:
   ```python
   class TestNewFeature:
       """Test new feature endpoints"""
       
       def test_feature_success(self, client, auth_headers):
           """Test successful feature operation"""
           response = client.post("/api/feature", json=data, headers=auth_headers)
           assert response.status_code == 200
       
       def test_feature_unauthorized(self, client):
           """Test feature without authentication"""
           response = client.post("/api/feature", json=data)
           assert response.status_code == 403
   ```

2. **Test both success and failure scenarios**
3. **Include authentication/authorization tests**
4. **Test input validation**
5. **Test error handling**
6. **Use descriptive test names**

### Test Organization

- Group related tests in classes
- Use descriptive class and method names
- Include docstrings for complex tests
- Follow the AAA pattern (Arrange, Act, Assert)

### Mocking Guidelines

- Mock external services (Celery tasks, AI agents)
- Don't mock the code under test
- Use `patch` for external dependencies
- Verify mock calls when relevant

## 🐛 Debugging Tests

### Common Issues

1. **Database state**: Tests should be isolated and not depend on each other
2. **Authentication**: Make sure to use proper auth headers
3. **Mocking**: Verify mocks are applied correctly
4. **Async code**: Use `AsyncMock` for async functions

### Debugging Commands

```bash
# Run with verbose output
pytest tests/test_api/ -v

# Run with detailed failure info
pytest tests/test_api/ --tb=long

# Run specific failing test
pytest tests/test_api/test_auth.py::test_login_valid_credentials -v

# Run with pdb debugger
pytest tests/test_api/ --pdb

# Show test durations
pytest tests/test_api/ --durations=10
```

## 📈 Performance Testing

### Response Time Expectations
- **Health checks**: < 100ms
- **Simple CRUD operations**: < 500ms
- **Complex operations** (predictions, analysis): < 5s
- **Report generation**: < 30s

### Load Testing
- Tests should handle concurrent requests
- Database operations should be efficient
- Memory usage should be reasonable

## 🔒 Security Testing

### Authentication Tests
- Invalid tokens
- Expired tokens
- Missing authentication
- User isolation (can't access other user's data)

### Input Validation Tests
- SQL injection attempts
- XSS prevention
- Invalid data types
- Boundary value testing

## 📝 Maintenance

### Regular Tasks
1. **Update test data** when schemas change
2. **Add tests** for new features
3. **Review coverage** reports monthly
4. **Update mocks** when external APIs change
5. **Performance baseline** updates quarterly

### Test Data Cleanup
- Tests automatically clean up after themselves
- Database is reset between test runs
- Temporary files are removed

## 🤝 Contributing

When adding new API endpoints:

1. Create corresponding test file or add to existing file
2. Include all test categories (success, failure, validation, auth)
3. Update this README if adding new test patterns
4. Ensure coverage meets minimum requirements
5. Run full test suite before submitting PR

## 📞 Support

For questions about the test suite:
- Check existing test patterns for examples
- Review conftest.py for available fixtures
- Run tests with verbose output for debugging
- Check coverage reports for gaps
