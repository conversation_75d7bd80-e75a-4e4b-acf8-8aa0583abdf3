"""
API Tests Package

This package contains comprehensive tests for all API endpoints in the Publish AI application.

Test Structure:
- test_auth.py: Authentication and authorization tests
- test_books.py: Book management and manuscript generation tests
- test_predictions.py: Sales prediction and ML model tests
- test_publications.py: KDP publication and management tests
- test_trends.py: Trend analysis and market research tests
- test_monitoring.py: System monitoring and health check tests
- test_analytics.py: Analytics and reporting tests
- test_feedback.py: User feedback and VERL training tests

Each test file includes:
- Unit tests for individual endpoints
- Integration tests for complete workflows
- Error handling and edge case tests
- Authentication and authorization tests
- Performance and load tests where applicable

Usage:
    # Run all API tests
    pytest tests/test_api/

    # Run specific API tests
    pytest tests/test_api/test_auth.py
    pytest tests/test_api/test_books.py

    # Run with coverage
    pytest tests/test_api/ --cov=app.api --cov-report=html
"""
