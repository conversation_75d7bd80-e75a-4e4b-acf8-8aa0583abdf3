"""
Tests for Predictions API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from datetime import datetime, timezone

# Import prediction models with error handling
try:
    from app.prediction.models import SalesPrediction
    PREDICTION_MODELS_AVAILABLE = True
except ImportError:
    PREDICTION_MODELS_AVAILABLE = False
    SalesPrediction = None


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsGeneration:
    """Test prediction generation endpoints"""

    @patch('app.api.predictions.sales_predictor')
    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', True)
    def test_predict_book_performance_success(self, mock_predictor, client, auth_headers, test_book):
        """Test successful book performance prediction"""
        # Mock predictor response
        mock_predictor._book_to_manuscript.return_value = {"title": "Test Book"}
        mock_predictor.predict_book_performance.return_value = {
            "predictions": {
                "sales_30d": 100,
                "revenue_30d": 499.0,
                "sales_90d": 300,
                "revenue_90d": 1497.0,
                "success_probability": 0.75
            },
            "confidence_score": 0.85,
            "risk_assessment": {"risk_level": "low"},
            "recommendations": ["Increase marketing"],
            "price_optimization": {"recommended_price": 4.99},
            "market_analysis": {
                "market_opportunity_score": 0.8,
                "competition_density": "medium"
            },
            "feature_importance": {"word_count": 0.3}
        }
        
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "prediction_id" in data
        assert "predictions" in data
        assert "confidence" in data

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', False)
    def test_predict_book_performance_service_unavailable(self, client, auth_headers, test_book):
        """Test prediction when service is unavailable"""
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 503
        assert "not available" in response.json()["detail"]

    def test_predict_book_performance_unauthorized(self, client, test_book):
        """Test prediction without authentication"""
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 4.99})
        
        assert response.status_code == 403

    def test_predict_book_performance_nonexistent_book(self, client, auth_headers):
        """Test prediction for non-existent book"""
        response = client.post("/api/predictions/books/99999/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 404

    def test_predict_book_performance_invalid_price(self, client, auth_headers, test_book):
        """Test prediction with invalid price"""
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": -1.0}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_predict_book_performance_default_price(self, client, auth_headers, test_book):
        """Test prediction with default price"""
        with patch('app.api.predictions.PREDICTIONS_AVAILABLE', True), \
             patch('app.api.predictions.sales_predictor') as mock_predictor:
            
            mock_predictor._book_to_manuscript.return_value = {"title": "Test Book"}
            mock_predictor.predict_book_performance.return_value = {
                "predictions": {"sales_30d": 100, "revenue_30d": 499.0, "sales_90d": 300, "revenue_90d": 1497.0, "success_probability": 0.75},
                "confidence_score": 0.85,
                "risk_assessment": {"risk_level": "low"},
                "recommendations": [],
                "price_optimization": {"recommended_price": 4.99},
                "market_analysis": {"market_opportunity_score": 0.8, "competition_density": "medium"},
                "feature_importance": {}
            }
            
            response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                                 json={}, 
                                 headers=auth_headers)
            
            assert response.status_code == 200


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsRetrieval:
    """Test prediction retrieval endpoints"""

    def test_get_book_predictions(self, client, auth_headers, test_book, test_prediction):
        """Test getting predictions for a book"""
        response = client.get(f"/api/predictions/books/{test_book.id}/predictions", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "book_id" in data
        assert "predictions" in data
        assert isinstance(data["predictions"], list)
        assert len(data["predictions"]) >= 1

    def test_get_book_predictions_unauthorized(self, client, test_book):
        """Test getting predictions without authentication"""
        response = client.get(f"/api/predictions/books/{test_book.id}/predictions")
        
        assert response.status_code == 403

    def test_get_book_predictions_nonexistent_book(self, client, auth_headers):
        """Test getting predictions for non-existent book"""
        response = client.get("/api/predictions/books/99999/predictions", 
                            headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_book_predictions_no_predictions(self, client, auth_headers, db_session, test_user):
        """Test getting predictions for book with no predictions"""
        from app.models.supabase_models import Book
        
        # Create a book with no predictions
        book = Book(
            title="No Predictions Book",
            category="business",
            status="draft",
            user_id=test_user.id,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(book)
        db_session.commit()
        
        response = client.get(f"/api/predictions/books/{book.id}/predictions", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["predictions"] == []


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsModelStatus:
    """Test model status endpoints"""

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', True)
    @patch('app.api.predictions.sales_predictor')
    def test_get_model_status_available(self, mock_predictor, client, auth_headers):
        """Test getting model status when service is available"""
        mock_predictor.sales_model = "mock_model"
        mock_predictor.last_trained = datetime.now(timezone.utc)
        mock_predictor.model_version = "1.0"
        mock_predictor.feature_names = ["feature1", "feature2"]
        
        response = client.get("/api/predictions/model-status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "models_trained" in data
        assert "last_trained" in data
        assert "model_version" in data
        assert "feature_count" in data
        assert data["status"] == "available"

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', False)
    def test_get_model_status_unavailable(self, client, auth_headers):
        """Test getting model status when service is unavailable"""
        response = client.get("/api/predictions/model-status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["models_trained"] == False
        assert data["status"] == "prediction_service_unavailable"

    def test_get_model_status_unauthorized(self, client):
        """Test getting model status without authentication"""
        response = client.get("/api/predictions/model-status")
        
        assert response.status_code == 403


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsTraining:
    """Test model training endpoints"""

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', True)
    @patch('app.api.predictions.sales_predictor')
    def test_train_models_success(self, mock_predictor, client, auth_headers):
        """Test successful model training"""
        mock_predictor.train_models.return_value = {"status": "completed"}
        
        response = client.post("/api/predictions/train-models", 
                             json={"retrain": False}, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Model training started"
        assert data["retrain"] == False

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', False)
    def test_train_models_service_unavailable(self, client, auth_headers):
        """Test model training when service is unavailable"""
        response = client.post("/api/predictions/train-models", 
                             json={"retrain": False}, 
                             headers=auth_headers)
        
        assert response.status_code == 503
        assert "not available" in response.json()["detail"]

    def test_train_models_unauthorized(self, client):
        """Test model training without authentication"""
        response = client.post("/api/predictions/train-models", 
                             json={"retrain": False})
        
        assert response.status_code == 403

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', True)
    def test_train_models_premium_required(self, client, auth_headers, test_user, db_session):
        """Test model training with premium requirement"""
        # Set user as non-premium
        test_user.is_premium = False
        db_session.commit()
        
        response = client.post("/api/predictions/train-models", 
                             json={"retrain": False}, 
                             headers=auth_headers)
        
        # Should work even for non-premium users in this implementation
        # If premium is required, this test would expect 403
        assert response.status_code in [200, 403]


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsDashboard:
    """Test predictions dashboard endpoints"""

    def test_get_prediction_dashboard(self, client, auth_headers, test_prediction):
        """Test getting prediction dashboard"""
        response = client.get("/api/predictions/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_predictions" in data
        assert "average_confidence" in data
        assert "average_success_probability" in data
        assert "high_potential_books" in data
        assert "recent_predictions" in data
        assert isinstance(data["recent_predictions"], list)

    def test_get_prediction_dashboard_unauthorized(self, client):
        """Test getting dashboard without authentication"""
        response = client.get("/api/predictions/dashboard")
        
        assert response.status_code == 403

    def test_get_prediction_dashboard_no_predictions(self, client, auth_headers):
        """Test getting dashboard with no predictions"""
        response = client.get("/api/predictions/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_predictions"] == 0
        assert data["average_confidence"] == 0
        assert data["recent_predictions"] == []


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsAccuracy:
    """Test prediction accuracy endpoints"""

    def test_update_prediction_accuracy(self, client, auth_headers):
        """Test updating prediction accuracy"""
        response = client.post("/api/predictions/accuracy/update", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Accuracy update started"

    def test_update_prediction_accuracy_unauthorized(self, client):
        """Test updating accuracy without authentication"""
        response = client.post("/api/predictions/accuracy/update")
        
        assert response.status_code == 403


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsValidation:
    """Test input validation for predictions endpoints"""

    def test_invalid_book_id_format(self, client, auth_headers):
        """Test prediction with invalid book ID format"""
        response = client.post("/api/predictions/books/invalid_id/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_negative_book_id(self, client, auth_headers):
        """Test prediction with negative book ID"""
        response = client.post("/api/predictions/books/-1/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_invalid_price_range(self, client, auth_headers, test_book):
        """Test prediction with price outside valid range"""
        # Test extremely high price
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 1000000.0}, 
                             headers=auth_headers)
        
        # Should either validate or handle gracefully
        assert response.status_code in [200, 422]

    def test_zero_price(self, client, auth_headers, test_book):
        """Test prediction with zero price"""
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 0.0}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Should validate price > 0


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsPerformance:
    """Test performance aspects of predictions endpoints"""

    def test_concurrent_predictions(self, client, auth_headers, test_book):
        """Test concurrent prediction requests"""
        with patch('app.api.predictions.PREDICTIONS_AVAILABLE', True), \
             patch('app.api.predictions.sales_predictor') as mock_predictor:
            
            mock_predictor._book_to_manuscript.return_value = {"title": "Test Book"}
            mock_predictor.predict_book_performance.return_value = {
                "predictions": {"sales_30d": 100, "revenue_30d": 499.0, "sales_90d": 300, "revenue_90d": 1497.0, "success_probability": 0.75},
                "confidence_score": 0.85,
                "risk_assessment": {"risk_level": "low"},
                "recommendations": [],
                "price_optimization": {"recommended_price": 4.99},
                "market_analysis": {"market_opportunity_score": 0.8, "competition_density": "medium"},
                "feature_importance": {}
            }
            
            # Simulate concurrent requests
            responses = []
            for _ in range(3):
                response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                                     json={"target_price": 4.99}, 
                                     headers=auth_headers)
                responses.append(response)
            
            # All should succeed
            for response in responses:
                assert response.status_code == 200

    def test_large_prediction_history(self, client, auth_headers, test_book, db_session):
        """Test retrieving large prediction history"""
        if not PREDICTION_MODELS_AVAILABLE or SalesPrediction is None:
            pytest.skip("Prediction models not available")

        # Create multiple predictions
        predictions = []
        for i in range(10):
            prediction = SalesPrediction(
                book_id=test_book.id,
                predicted_sales_30d=100 + i,
                predicted_revenue_30d=499.0 + i,
                predicted_sales_90d=300 + i,
                predicted_revenue_90d=1497.0 + i,
                confidence_score=0.8 + (i * 0.01),
                success_probability=0.7 + (i * 0.01),
                recommended_price=4.99,
                market_opportunity_score=0.8,
                competition_level=0.5,  # Float value (0.0-1.0)
                risk_level="low",
                features_used={},
                created_at=datetime.now(timezone.utc)
            )
            predictions.append(prediction)
        
        db_session.add_all(predictions)
        db_session.commit()
        
        response = client.get(f"/api/predictions/books/{test_book.id}/predictions", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["predictions"]) >= 10


@pytest.mark.skipif(not PREDICTION_MODELS_AVAILABLE, reason="Prediction models not available")
class TestPredictionsErrorHandling:
    """Test error handling in predictions endpoints"""

    def test_malformed_prediction_request(self, client, auth_headers, test_book):
        """Test handling of malformed prediction request"""
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             data="invalid json", 
                             headers={**auth_headers, "Content-Type": "application/json"})
        
        assert response.status_code == 422  # Validation error

    def test_empty_prediction_request(self, client, auth_headers, test_book):
        """Test handling of empty prediction request"""
        with patch('app.api.predictions.PREDICTIONS_AVAILABLE', True):
            response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                                 json={}, 
                                 headers=auth_headers)
            
            # Should use default values
            assert response.status_code in [200, 503]  # 503 if predictor not available

    @patch('app.api.predictions.PREDICTIONS_AVAILABLE', True)
    @patch('app.api.predictions.sales_predictor')
    def test_predictor_exception_handling(self, mock_predictor, client, auth_headers, test_book):
        """Test handling of predictor exceptions"""
        mock_predictor._book_to_manuscript.side_effect = Exception("Predictor error")
        
        response = client.post(f"/api/predictions/books/{test_book.id}/predict", 
                             json={"target_price": 4.99}, 
                             headers=auth_headers)
        
        assert response.status_code == 500
