#!/usr/bin/env python3
"""
Test Runner for API Tests

This script provides various ways to run the API tests with different configurations.

Usage:
    python tests/test_api/run_tests.py [options]

Options:
    --all                   Run all API tests
    --auth                  Run authentication tests only
    --books                 Run books API tests only
    --predictions           Run predictions API tests only
    --publications          Run publications API tests only
    --trends                Run trends API tests only
    --monitoring            Run monitoring API tests only
    --analytics             Run analytics API tests only
    --feedback              Run feedback API tests only
    --coverage              Run tests with coverage report
    --verbose               Run tests with verbose output
    --parallel              Run tests in parallel
    --fast                  Run tests with minimal output
    --integration           Run integration tests only
    --unit                  Run unit tests only
    --smoke                 Run smoke tests only
    --performance           Run performance tests only
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    if description:
        print(f"🚀 {description}")
    print(f"Running: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description or 'Command'} completed successfully!")
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description or 'Command'} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"\n❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install pytest")
        return False


def get_base_cmd(args):
    """Get base pytest command with common options"""
    cmd = ["python", "-m", "pytest"]
    
    if args.verbose:
        cmd.append("-v")
    elif args.fast:
        cmd.append("-q")
    
    if args.parallel:
        cmd.extend(["-n", "auto"])
    
    if args.coverage:
        cmd.extend([
            "--cov=app.api",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    return cmd


def run_specific_tests(args):
    """Run specific test modules"""
    base_cmd = get_base_cmd(args)
    test_dir = Path("tests/test_api")
    
    test_modules = {
        'auth': 'test_auth.py',
        'books': 'test_books.py',
        'predictions': 'test_predictions.py',
        'publications': 'test_publications.py',
        'trends': 'test_trends.py',
        'monitoring': 'test_monitoring.py',
        'analytics': 'test_analytics.py',
        'feedback': 'test_feedback.py'
    }
    
    success = True
    
    for module_name, filename in test_modules.items():
        if getattr(args, module_name, False):
            test_file = test_dir / filename
            if test_file.exists():
                cmd = base_cmd + [str(test_file)]
                if not run_command(cmd, f"Running {module_name.title()} API Tests"):
                    success = False
            else:
                print(f"❌ Test file not found: {test_file}")
                success = False
    
    return success


def run_all_tests(args):
    """Run all API tests"""
    base_cmd = get_base_cmd(args)
    test_dir = "tests/test_api"
    
    cmd = base_cmd + [test_dir]
    return run_command(cmd, "Running All API Tests")


def run_by_category(args):
    """Run tests by category (unit, integration, etc.)"""
    base_cmd = get_base_cmd(args)
    test_dir = "tests/test_api"
    
    if args.unit:
        cmd = base_cmd + [test_dir, "-m", "not integration and not performance"]
        return run_command(cmd, "Running Unit Tests")
    
    elif args.integration:
        cmd = base_cmd + [test_dir, "-m", "integration"]
        return run_command(cmd, "Running Integration Tests")
    
    elif args.performance:
        cmd = base_cmd + [test_dir, "-m", "performance"]
        return run_command(cmd, "Running Performance Tests")
    
    elif args.smoke:
        # Run a subset of critical tests
        cmd = base_cmd + [
            "tests/test_api/test_auth.py::TestAuthLogin::test_login_valid_credentials",
            "tests/test_api/test_books.py::TestBooksGeneration::test_generate_manuscript_success",
            "tests/test_api/test_monitoring.py::TestMonitoringHealth::test_health_check"
        ]
        return run_command(cmd, "Running Smoke Tests")
    
    return True


def setup_test_environment():
    """Setup test environment"""
    print("🔧 Setting up test environment...")
    
    # Check if pytest is installed
    try:
        subprocess.run(["python", "-m", "pytest", "--version"], 
                      check=True, capture_output=True)
        print("✅ pytest is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ pytest not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pytest"], check=True)
    
    # Check for optional dependencies
    optional_deps = {
        "pytest-cov": "coverage reporting",
        "pytest-xdist": "parallel test execution",
        "pytest-mock": "mocking support"
    }
    
    for dep, description in optional_deps.items():
        try:
            subprocess.run(["python", "-c", f"import {dep.replace('-', '_')}"], 
                          check=True, capture_output=True)
            print(f"✅ {dep} is available ({description})")
        except subprocess.CalledProcessError:
            print(f"⚠️  {dep} not found ({description})")
    
    # Create test database if needed
    print("✅ Test environment setup complete")


def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n📊 Generating Test Report...")
    
    # Run tests with detailed output
    cmd = [
        "python", "-m", "pytest",
        "tests/test_api",
        "--tb=short",
        "--durations=10",
        "--cov=app.api",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--junit-xml=test-results.xml"
    ]
    
    success = run_command(cmd, "Generating Comprehensive Test Report")
    
    if success:
        print("\n📋 Test Report Generated:")
        print("  - HTML Coverage Report: htmlcov/index.html")
        print("  - JUnit XML Report: test-results.xml")
        print("  - Terminal Coverage Report: displayed above")
    
    return success


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Run API tests for Publish AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Test selection options
    parser.add_argument("--all", action="store_true", help="Run all API tests")
    parser.add_argument("--auth", action="store_true", help="Run authentication tests")
    parser.add_argument("--books", action="store_true", help="Run books API tests")
    parser.add_argument("--predictions", action="store_true", help="Run predictions API tests")
    parser.add_argument("--publications", action="store_true", help="Run publications API tests")
    parser.add_argument("--trends", action="store_true", help="Run trends API tests")
    parser.add_argument("--monitoring", action="store_true", help="Run monitoring API tests")
    parser.add_argument("--analytics", action="store_true", help="Run analytics API tests")
    parser.add_argument("--feedback", action="store_true", help="Run feedback API tests")
    
    # Test category options
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--smoke", action="store_true", help="Run smoke tests only")
    
    # Output options
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", "-f", action="store_true", help="Fast/quiet output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage report")
    parser.add_argument("--parallel", "-p", action="store_true", help="Run tests in parallel")
    
    # Utility options
    parser.add_argument("--setup", action="store_true", help="Setup test environment")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent.parent
    os.chdir(project_root)
    
    print("🧪 Publish AI - API Test Runner")
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Handle setup
    if args.setup:
        setup_test_environment()
        return
    
    # Handle report generation
    if args.report:
        success = generate_test_report()
        sys.exit(0 if success else 1)
    
    # Determine what tests to run
    specific_tests = any([
        args.auth, args.books, args.predictions, args.publications,
        args.trends, args.monitoring, args.analytics, args.feedback
    ])
    
    category_tests = any([
        args.unit, args.integration, args.performance, args.smoke
    ])
    
    success = True
    
    if specific_tests:
        success = run_specific_tests(args)
    elif category_tests:
        success = run_by_category(args)
    elif args.all:
        success = run_all_tests(args)
    else:
        # Default: run all tests
        print("No specific tests selected, running all API tests...")
        args.all = True
        success = run_all_tests(args)
    
    # Print summary
    print(f"\n{'='*60}")
    if success:
        print("🎉 All tests completed successfully!")
        if args.coverage:
            print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    print(f"{'='*60}")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
