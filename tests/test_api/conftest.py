"""
Shared test fixtures for API tests (Supabase Version)
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone
import uuid

from app.main_supabase import app
from app.utils.supabase.supabase_client import supabase_client


# Mock Supabase client for testing
class MockSupabaseClient:
    """Mock Supabase client for testing without real database"""
    
    def __init__(self):
        self.tables = {
            'users': [],
            'books': [],
            'publications': [],
            'sales_data': [],
            'feedback_metrics': [],
            'trend_analyses': [],
            'verl_training_jobs': [],
            'sales_predictions': [],
            'market_analyses': [],
            'prediction_accuracy': []
        }
        self.client = MagicMock()
        self._setup_table_mocks()
    
    def _setup_table_mocks(self):
        """Setup table method mocks"""
        for table_name in self.tables.keys():
            table_mock = MagicMock()
            
            # Setup select method
            table_mock.select.return_value = table_mock
            table_mock.eq.return_value = table_mock
            table_mock.gt.return_value = table_mock
            table_mock.gte.return_value = table_mock
            table_mock.lt.return_value = table_mock
            table_mock.lte.return_value = table_mock
            table_mock.order.return_value = table_mock
            table_mock.limit.return_value = table_mock
            
            # Mock execute to return table data
            def create_execute(table_name):
                def execute():
                    result = MagicMock()
                    result.data = self.tables[table_name]
                    result.count = len(self.tables[table_name])
                    return result
                return execute
            
            table_mock.execute = create_execute(table_name)
            
            # Setup insert method
            def create_insert(table_name):
                def insert(data):
                    if isinstance(data, list):
                        for item in data:
                            if 'id' not in item:
                                item['id'] = str(uuid.uuid4())
                            if 'created_at' not in item:
                                item['created_at'] = datetime.now().isoformat()
                            self.tables[table_name].append(item)
                    else:
                        if 'id' not in data:
                            data['id'] = str(uuid.uuid4())
                        if 'created_at' not in data:
                            data['created_at'] = datetime.now().isoformat()
                        self.tables[table_name].append(data)
                    
                    result = MagicMock()
                    result.data = [data] if not isinstance(data, list) else data
                    return result
                return insert
            
            table_mock.insert = create_insert(table_name)
            
            # Setup update method
            table_mock.update.return_value = table_mock
            
            # Setup delete method
            table_mock.delete.return_value = table_mock
            
            # Attach to client
            setattr(self.client.table, table_name, lambda name=table_name: table_mock)
    
    def reset_data(self):
        """Reset all table data"""
        for table_name in self.tables.keys():
            self.tables[table_name].clear()
    
    def add_test_data(self, table_name: str, data: list):
        """Add test data to a table"""
        self.tables[table_name].extend(data)


def override_get_supabase():
    """Override Supabase client dependency for testing"""
    return mock_supabase_client


# Global mock client instance
mock_supabase_client = MockSupabaseClient()

# Override the Supabase dependency
from app.utils.supabase.supabase_client import get_supabase_client

app.dependency_overrides[get_supabase_client] = override_get_supabase


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment"""
    # Initialize mock data
    mock_supabase_client.reset_data()
    yield
    # Cleanup after tests
    mock_supabase_client.reset_data()


@pytest.fixture
def clean_db():
    """Clean database for each test"""
    mock_supabase_client.reset_data()
    yield mock_supabase_client
    mock_supabase_client.reset_data()


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def test_user(clean_db):
    """Create a test user"""
    unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
    user_id = str(uuid.uuid4())

    user_data = {
        "id": user_id,
        "email": unique_email,
        "full_name": "Test User",
        "subscription_tier": "free",
        "created_at": datetime.now(timezone.utc).isoformat()
    }
    
    clean_db.add_test_data('users', [user_data])
    return user_data


@pytest.fixture
def premium_user(clean_db):
    """Create a premium test user"""
    unique_email = f"premium-{uuid.uuid4().hex[:8]}@example.com"
    user_id = str(uuid.uuid4())

    user_data = {
        "id": user_id,
        "email": unique_email,
        "full_name": "Premium User",
        "subscription_tier": "pro",
        "created_at": datetime.now(timezone.utc).isoformat()
    }
    
    clean_db.add_test_data('users', [user_data])
    return user_data


@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user"""
    response = client.post(
        "/api/auth/login",
        json={"email": test_user.email, "password": "secret"}
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def premium_auth_headers(client, premium_user):
    """Get authentication headers for premium user"""
    response = client.post(
        "/api/auth/login",
        json={"email": premium_user.email, "password": "secret"}
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_book(db_session, test_user):
    """Create a test book"""
    book = Book(
        title="Test Book",
        category="business",
        description="A test book for testing",
        status="draft",
        word_count=1000,
        chapter_count=5,
        user_id=test_user.id,
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(book)
    db_session.commit()
    db_session.refresh(book)
    return book


@pytest.fixture
def approved_book(db_session, test_user):
    """Create an approved test book"""
    book = Book(
        title="Approved Test Book",
        category="business",
        description="An approved test book",
        status="approved",
        word_count=5000,
        chapter_count=10,
        user_id=test_user.id,
        created_at=datetime.now(timezone.utc),
        approved_at=datetime.now(timezone.utc)
    )
    db_session.add(book)
    db_session.commit()
    db_session.refresh(book)
    return book


@pytest.fixture
def test_publication(db_session, test_user, approved_book):
    """Create a test publication"""
    publication = Publication(
        book_id=approved_book.id,
        status="publishing",
        price=4.99,
        auto_publish=True,
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(publication)
    db_session.commit()
    db_session.refresh(publication)
    return publication


@pytest.fixture
def test_trend_analysis(db_session, test_user):
    """Create a test trend analysis"""
    analysis = TrendAnalysis(
        user_id=test_user.id,
        request_config={"categories": ["business", "technology"], "analysis_depth": "comprehensive"},
        status="completed",
        result={"trends": [{"keyword": "AI", "score": 0.95}]},
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(analysis)
    db_session.commit()
    db_session.refresh(analysis)
    return analysis


@pytest.fixture
def test_prediction(db_session, test_user, test_book):
    """Create a test sales prediction"""
    if not PREDICTION_MODELS_AVAILABLE or SalesPrediction is None:
        pytest.skip("Prediction models not available")

    prediction = SalesPrediction(
        book_id=test_book.id,
        predicted_sales_30d=100,
        predicted_revenue_30d=499.0,
        predicted_sales_90d=300,
        predicted_revenue_90d=1497.0,
        confidence_score=0.85,
        success_probability=0.75,
        recommended_price=4.99,
        market_opportunity_score=0.8,
        competition_level=0.5,  # Float value (0.0-1.0)
        risk_level="low",
        features_used={"word_count": 5000, "category": "business"},
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(prediction)
    db_session.commit()
    db_session.refresh(prediction)
    return prediction


@pytest.fixture
def temp_file():
    """Create a temporary file for testing"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp:
        tmp.write(b"Test file content")
        tmp.flush()
        yield tmp.name
    os.unlink(tmp.name)


# Test data fixtures
@pytest.fixture
def sample_book_data():
    """Sample book creation data"""
    return {
        "title": "Sample Book",
        "category": "business",
        "target_audience": "professionals",
        "writing_style": "professional",
        "style": "formal",
        "ai_provider": "openai",
        "trend_data": {"keywords": ["productivity", "business"]},
        "target_length": 5000,
        "output_formats": ["docx", "epub"]
    }


@pytest.fixture
def sample_publication_data():
    """Sample publication request data"""
    return {
        "manuscript_id": "test_manuscript_123",
        "price": 4.99,
        "royalty_rate": 70,
        "marketing_description": "A great book about business",
        "auto_publish": True
    }


@pytest.fixture
def sample_trend_data():
    """Sample trend analysis request data"""
    return {
        "categories": ["business", "technology"],
        "analysis_depth": "comprehensive",
        "include_keywords": True
    }
