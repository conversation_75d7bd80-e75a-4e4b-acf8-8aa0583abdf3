"""
Tests for Analytics API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta


class TestAnalyticsOverview:
    """Test analytics overview endpoints"""

    def test_get_analytics_overview(self, client, auth_headers, test_book):
        """Test getting analytics overview"""
        response = client.get("/api/analytics/overview", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_books" in data
        assert "total_revenue" in data
        assert "total_sales" in data
        assert "conversion_rate" in data
        assert "top_categories" in data
        assert "recent_activity" in data

    def test_get_analytics_overview_unauthorized(self, client):
        """Test getting analytics overview without authentication"""
        response = client.get("/api/analytics/overview")
        
        assert response.status_code == 403

    def test_get_analytics_overview_empty_data(self, client, auth_headers):
        """Test analytics overview with no data"""
        response = client.get("/api/analytics/overview", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        # Should return zero values for empty data
        assert data["total_books"] >= 0
        assert data["total_revenue"] >= 0
        assert data["total_sales"] >= 0

    def test_get_analytics_overview_with_timeframe(self, client, auth_headers):
        """Test analytics overview with timeframe filter"""
        response = client.get("/api/analytics/overview?timeframe=30d", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "timeframe" in data
        assert data["timeframe"] == "30d"

    def test_get_analytics_overview_invalid_timeframe(self, client, auth_headers):
        """Test analytics overview with invalid timeframe"""
        response = client.get("/api/analytics/overview?timeframe=invalid", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsBooks:
    """Test book analytics endpoints"""

    def test_get_book_analytics(self, client, auth_headers, test_book):
        """Test getting analytics for a specific book"""
        response = client.get(f"/api/analytics/books/{test_book.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "book_id" in data
        assert "title" in data
        assert "sales_data" in data
        assert "revenue_data" in data
        assert "performance_metrics" in data
        assert data["book_id"] == test_book.id

    def test_get_book_analytics_unauthorized(self, client, test_book):
        """Test getting book analytics without authentication"""
        response = client.get(f"/api/analytics/books/{test_book.id}")
        
        assert response.status_code == 403

    def test_get_book_analytics_nonexistent_book(self, client, auth_headers):
        """Test getting analytics for non-existent book"""
        response = client.get("/api/analytics/books/99999", headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_book_analytics_other_user_book(self, client, auth_headers, db_session):
        """Test getting analytics for another user's book"""
        # Create a book for a different user
        from app.models.supabase_models import User, Book
        
        other_user = User(
            email="<EMAIL>",
            full_name="Other User",
            hashed_password="hashed",
            subscription_tier="free",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_user)
        db_session.commit()
        
        other_book = Book(
            title="Other User's Book",
            category="business",
            status="published",
            user_id=other_user.id,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_book)
        db_session.commit()
        
        response = client.get(f"/api/analytics/books/{other_book.id}", headers=auth_headers)
        
        assert response.status_code == 404  # Should not find other user's book

    def test_get_book_analytics_with_date_range(self, client, auth_headers, test_book):
        """Test getting book analytics with date range"""
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(f"/api/analytics/books/{test_book.id}?start_date={start_date}&end_date={end_date}", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "date_range" in data

    def test_get_book_analytics_invalid_date_range(self, client, auth_headers, test_book):
        """Test getting book analytics with invalid date range"""
        response = client.get(f"/api/analytics/books/{test_book.id}?start_date=invalid_date", 
                            headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsSales:
    """Test sales analytics endpoints"""

    def test_get_sales_analytics(self, client, auth_headers):
        """Test getting sales analytics"""
        response = client.get("/api/analytics/sales", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_sales" in data
        assert "total_revenue" in data
        assert "sales_by_period" in data
        assert "top_selling_books" in data
        assert "sales_trends" in data

    def test_get_sales_analytics_unauthorized(self, client):
        """Test getting sales analytics without authentication"""
        response = client.get("/api/analytics/sales")
        
        assert response.status_code == 403

    def test_get_sales_analytics_with_grouping(self, client, auth_headers):
        """Test getting sales analytics with different groupings"""
        # Test daily grouping
        response = client.get("/api/analytics/sales?group_by=day", headers=auth_headers)
        assert response.status_code == 200
        
        # Test weekly grouping
        response = client.get("/api/analytics/sales?group_by=week", headers=auth_headers)
        assert response.status_code == 200
        
        # Test monthly grouping
        response = client.get("/api/analytics/sales?group_by=month", headers=auth_headers)
        assert response.status_code == 200

    def test_get_sales_analytics_invalid_grouping(self, client, auth_headers):
        """Test getting sales analytics with invalid grouping"""
        response = client.get("/api/analytics/sales?group_by=invalid", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_get_sales_analytics_with_category_filter(self, client, auth_headers):
        """Test getting sales analytics with category filter"""
        response = client.get("/api/analytics/sales?category=business", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "category_filter" in data
        assert data["category_filter"] == "business"


class TestAnalyticsRevenue:
    """Test revenue analytics endpoints"""

    def test_get_revenue_analytics(self, client, auth_headers):
        """Test getting revenue analytics"""
        response = client.get("/api/analytics/revenue", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_revenue" in data
        assert "revenue_by_period" in data
        assert "revenue_by_category" in data
        assert "average_revenue_per_book" in data
        assert "revenue_trends" in data

    def test_get_revenue_analytics_unauthorized(self, client):
        """Test getting revenue analytics without authentication"""
        response = client.get("/api/analytics/revenue")
        
        assert response.status_code == 403

    def test_get_revenue_analytics_with_currency(self, client, auth_headers):
        """Test getting revenue analytics with currency conversion"""
        response = client.get("/api/analytics/revenue?currency=EUR", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "currency" in data
        assert data["currency"] == "EUR"

    def test_get_revenue_analytics_invalid_currency(self, client, auth_headers):
        """Test getting revenue analytics with invalid currency"""
        response = client.get("/api/analytics/revenue?currency=INVALID", headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsPerformance:
    """Test performance analytics endpoints"""

    def test_get_performance_analytics(self, client, auth_headers):
        """Test getting performance analytics"""
        response = client.get("/api/analytics/performance", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "conversion_rates" in data
        assert "engagement_metrics" in data
        assert "quality_scores" in data
        assert "prediction_accuracy" in data

    def test_get_performance_analytics_unauthorized(self, client):
        """Test getting performance analytics without authentication"""
        response = client.get("/api/analytics/performance")
        
        assert response.status_code == 403

    def test_get_performance_analytics_with_metrics_filter(self, client, auth_headers):
        """Test getting performance analytics with specific metrics"""
        response = client.get("/api/analytics/performance?metrics=conversion_rate,engagement", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "metrics_filter" in data

    def test_get_performance_analytics_invalid_metrics(self, client, auth_headers):
        """Test getting performance analytics with invalid metrics"""
        response = client.get("/api/analytics/performance?metrics=invalid_metric", 
                            headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsReports:
    """Test analytics reports endpoints"""

    def test_generate_analytics_report(self, client, auth_headers):
        """Test generating analytics report"""
        report_data = {
            "report_type": "monthly",
            "include_sections": ["sales", "revenue", "performance"],
            "format": "pdf"
        }
        
        response = client.post("/api/analytics/reports/generate", 
                             json=report_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "report_id" in data
        assert "status" in data
        assert data["status"] == "generating"

    def test_generate_analytics_report_unauthorized(self, client):
        """Test generating report without authentication"""
        report_data = {
            "report_type": "monthly",
            "include_sections": ["sales"],
            "format": "pdf"
        }
        
        response = client.post("/api/analytics/reports/generate", json=report_data)
        
        assert response.status_code == 403

    def test_generate_analytics_report_invalid_data(self, client, auth_headers):
        """Test generating report with invalid data"""
        invalid_data = {
            "report_type": "invalid_type",
            "format": "invalid_format"
        }
        
        response = client.post("/api/analytics/reports/generate", 
                             json=invalid_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_get_report_status(self, client, auth_headers):
        """Test getting report generation status"""
        # First generate a report
        report_data = {
            "report_type": "weekly",
            "include_sections": ["sales"],
            "format": "csv"
        }
        
        generate_response = client.post("/api/analytics/reports/generate", 
                                      json=report_data, 
                                      headers=auth_headers)
        
        assert generate_response.status_code == 200
        report_id = generate_response.json()["report_id"]
        
        # Then check its status
        status_response = client.get(f"/api/analytics/reports/{report_id}/status", 
                                   headers=auth_headers)
        
        assert status_response.status_code == 200
        data = status_response.json()
        assert "report_id" in data
        assert "status" in data
        assert "created_at" in data

    def test_get_report_status_nonexistent(self, client, auth_headers):
        """Test getting status of non-existent report"""
        response = client.get("/api/analytics/reports/99999/status", headers=auth_headers)
        
        assert response.status_code == 404

    def test_download_report(self, client, auth_headers):
        """Test downloading generated report"""
        # This would typically require a completed report
        # For now, test the endpoint structure
        response = client.get("/api/analytics/reports/1/download", headers=auth_headers)
        
        # Should either return the file or 404 if not found
        assert response.status_code in [200, 404]

    def test_download_report_unauthorized(self, client):
        """Test downloading report without authentication"""
        response = client.get("/api/analytics/reports/1/download")
        
        assert response.status_code == 403


class TestAnalyticsExport:
    """Test analytics export endpoints"""

    def test_export_analytics_data(self, client, auth_headers):
        """Test exporting analytics data"""
        export_data = {
            "data_type": "sales",
            "format": "csv",
            "date_range": {
                "start": "2024-01-01",
                "end": "2024-12-31"
            }
        }
        
        response = client.post("/api/analytics/export", 
                             json=export_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "export_id" in data
        assert "status" in data

    def test_export_analytics_data_unauthorized(self, client):
        """Test exporting data without authentication"""
        export_data = {
            "data_type": "sales",
            "format": "csv"
        }
        
        response = client.post("/api/analytics/export", json=export_data)
        
        assert response.status_code == 403

    def test_export_analytics_data_invalid_format(self, client, auth_headers):
        """Test exporting data with invalid format"""
        export_data = {
            "data_type": "sales",
            "format": "invalid_format"
        }
        
        response = client.post("/api/analytics/export", 
                             json=export_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsValidation:
    """Test input validation for analytics endpoints"""

    def test_invalid_book_id_format(self, client, auth_headers):
        """Test analytics with invalid book ID format"""
        response = client.get("/api/analytics/books/invalid_id", headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_negative_book_id(self, client, auth_headers):
        """Test analytics with negative book ID"""
        response = client.get("/api/analytics/books/-1", headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_invalid_date_format(self, client, auth_headers):
        """Test analytics with invalid date format"""
        response = client.get("/api/analytics/overview?start_date=invalid_date", headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_future_date_range(self, client, auth_headers):
        """Test analytics with future date range"""
        future_date = (datetime.now() + timedelta(days=30)).isoformat()
        response = client.get(f"/api/analytics/overview?start_date={future_date}", headers=auth_headers)
        
        # Should either handle gracefully or return validation error
        assert response.status_code in [200, 422]

    def test_invalid_query_parameters(self, client, auth_headers):
        """Test analytics with invalid query parameters"""
        # Test invalid page number
        response = client.get("/api/analytics/sales?page=abc", headers=auth_headers)
        assert response.status_code == 422

        # Test invalid limit
        response = client.get("/api/analytics/sales?limit=-1", headers=auth_headers)
        assert response.status_code == 422


class TestAnalyticsPerformance:
    """Test performance aspects of analytics endpoints"""

    def test_analytics_response_time(self, client, auth_headers):
        """Test that analytics endpoints respond in reasonable time"""
        import time
        
        start_time = time.time()
        response = client.get("/api/analytics/overview", headers=auth_headers)
        end_time = time.time()
        
        assert response.status_code == 200
        # Analytics should respond within 5 seconds
        assert (end_time - start_time) < 5.0

    def test_concurrent_analytics_requests(self, client, auth_headers):
        """Test concurrent analytics requests"""
        # Simulate concurrent requests
        responses = []
        for _ in range(3):
            response = client.get("/api/analytics/overview", headers=auth_headers)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200

    def test_large_data_handling(self, client, auth_headers, db_session, test_user):
        """Test analytics with large amounts of data"""
        # Create multiple books for testing
        from app.models.supabase_models import Book
        
        books = []
        for i in range(50):
            book = Book(
                title=f"Test Book {i}",
                category="business",
                status="published",
                user_id=test_user.id,
                created_at=datetime.now(timezone.utc)
            )
            books.append(book)
        
        db_session.add_all(books)
        db_session.commit()
        
        response = client.get("/api/analytics/overview", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_books"] >= 50


class TestAnalyticsErrorHandling:
    """Test error handling in analytics endpoints"""

    def test_malformed_request_data(self, client, auth_headers):
        """Test handling of malformed request data"""
        response = client.post("/api/analytics/reports/generate", 
                             data="invalid json", 
                             headers={**auth_headers, "Content-Type": "application/json"})
        
        assert response.status_code == 422  # Validation error

    def test_empty_request_body(self, client, auth_headers):
        """Test handling of empty request body"""
        response = client.post("/api/analytics/reports/generate", 
                             json={}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    @patch('app.api.analytics.calculate_analytics')
    def test_analytics_calculation_error(self, mock_calculate, client, auth_headers):
        """Test handling of analytics calculation errors"""
        # Mock analytics calculation to raise an exception
        mock_calculate.side_effect = Exception("Calculation error")
        
        response = client.get("/api/analytics/overview", headers=auth_headers)
        
        # Should handle the error gracefully
        assert response.status_code in [200, 500]
