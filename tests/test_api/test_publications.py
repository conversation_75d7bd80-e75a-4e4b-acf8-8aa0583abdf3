"""
Tests for Publications API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timezone

from app.models.supabase_models import Publication


class TestPublicationsPublish:
    """Test book publishing endpoints"""

    @patch('app.api.publications.PUBLICATION_SERVICE_AVAILABLE', True)
    @patch('app.api.publications.is_celery_task')
    @patch('app.api.publications.publish_to_kdp_task')
    def test_publish_book_success(self, mock_task, mock_is_celery, client, auth_headers, approved_book, sample_publication_data):
        """Test successful book publishing"""
        mock_is_celery.return_value = True
        mock_task.delay.return_value = MagicMock(id="task_123")
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Publication started"
        assert data["book_id"] == approved_book.id
        assert "publication_id" in data
        assert data["status"] == "publishing"

    @patch('app.api.publications.PUBLICATION_SERVICE_AVAILABLE', False)
    def test_publish_book_service_unavailable(self, client, auth_headers, approved_book, sample_publication_data):
        """Test publishing when service is unavailable"""
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        
        assert response.status_code == 503
        assert "not available" in response.json()["detail"]

    def test_publish_book_unauthorized(self, client, approved_book, sample_publication_data):
        """Test publishing without authentication"""
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=sample_publication_data)
        
        assert response.status_code == 403

    def test_publish_book_not_approved(self, client, auth_headers, test_book, sample_publication_data):
        """Test publishing book that's not approved"""
        response = client.post(f"/api/publications/{test_book.id}/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        
        assert response.status_code == 400
        assert "must be approved" in response.json()["detail"]

    def test_publish_nonexistent_book(self, client, auth_headers, sample_publication_data):
        """Test publishing non-existent book"""
        response = client.post("/api/publications/99999/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        
        assert response.status_code == 404

    def test_publish_book_invalid_data(self, client, auth_headers, approved_book):
        """Test publishing with invalid publication data"""
        invalid_data = {
            "manuscript_id": "",  # Empty manuscript ID
            "price": -1.0,  # Invalid price
            "royalty_rate": 50  # Invalid royalty rate
        }
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=invalid_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_publish_book_missing_fields(self, client, auth_headers, approved_book):
        """Test publishing with missing required fields"""
        incomplete_data = {
            "price": 4.99
            # Missing manuscript_id and other required fields
        }
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=incomplete_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestPublicationsRetrieval:
    """Test publication retrieval endpoints"""

    def test_get_user_publications(self, client, auth_headers, test_publication):
        """Test getting user's publications"""
        response = client.get("/api/publications/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check that our test publication is in the response
        publication_ids = [pub["id"] for pub in data]
        assert test_publication.id in publication_ids

    def test_get_user_publications_unauthorized(self, client):
        """Test getting publications without authentication"""
        response = client.get("/api/publications/")
        
        assert response.status_code == 403

    def test_get_user_publications_empty(self, client, auth_headers):
        """Test getting publications when user has no publications"""
        response = client.get("/api/publications/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_publication_status(self, client, auth_headers, test_publication):
        """Test getting publication status"""
        response = client.get(f"/api/publications/{test_publication.id}/status", 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_publication.id
        assert "status" in data
        assert "created_at" in data
        assert "book_id" in data

    def test_get_publication_status_unauthorized(self, client, test_publication):
        """Test getting publication status without authentication"""
        response = client.get(f"/api/publications/{test_publication.id}/status")
        
        assert response.status_code == 403

    def test_get_publication_status_nonexistent(self, client, auth_headers):
        """Test getting status of non-existent publication"""
        response = client.get("/api/publications/99999/status", headers=auth_headers)
        
        assert response.status_code == 404

    def test_get_other_user_publication(self, client, auth_headers, db_session):
        """Test getting another user's publication (should fail)"""
        # Create a publication for a different user
        from app.models.user import User
        from app.models.book import Book
        
        import uuid
        unique_email = f"other-{uuid.uuid4().hex[:8]}@example.com"

        other_user = User(
            email=unique_email,
            full_name="Other User",
            password_hash="hashed",
            subscription_tier="free",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_user)
        db_session.commit()
        
        other_book = Book(
            title="Other User's Book",
            category="business",
            status="approved",
            user_id=other_user.id,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_book)
        db_session.commit()
        
        other_publication = Publication(
            book_id=other_book.id,
            status="published",
            price=9.99,
            auto_publish=False,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(other_publication)
        db_session.commit()
        
        response = client.get(f"/api/publications/{other_publication.id}/status", 
                            headers=auth_headers)
        
        assert response.status_code == 404  # Should not find other user's publication


class TestPublicationsManagement:
    """Test publication management endpoints"""

    def test_cancel_publication(self, client, auth_headers, test_publication):
        """Test cancelling a publication"""
        response = client.post(f"/api/publications/{test_publication.id}/cancel", 
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Publication cancelled successfully"
        assert data["publication_id"] == test_publication.id
        assert data["status"] == "cancelled"

    def test_cancel_publication_unauthorized(self, client, test_publication):
        """Test cancelling publication without authentication"""
        response = client.post(f"/api/publications/{test_publication.id}/cancel")
        
        assert response.status_code == 403

    def test_cancel_nonexistent_publication(self, client, auth_headers):
        """Test cancelling non-existent publication"""
        response = client.post("/api/publications/99999/cancel", headers=auth_headers)
        
        assert response.status_code == 404

    def test_cancel_completed_publication(self, client, auth_headers, db_session, test_user, approved_book):
        """Test cancelling a completed publication (should fail)"""
        # Create a completed publication
        completed_publication = Publication(
            book_id=approved_book.id,
            status="published",
            price=4.99,
            auto_publish=True,
            created_at=datetime.now(timezone.utc),
            published_at=datetime.now(timezone.utc)
        )
        db_session.add(completed_publication)
        db_session.commit()
        
        response = client.post(f"/api/publications/{completed_publication.id}/cancel", 
                             headers=auth_headers)
        
        assert response.status_code == 400
        assert "Cannot cancel" in response.json()["detail"]


class TestPublicationsDashboard:
    """Test publications dashboard endpoints"""

    def test_get_publications_dashboard(self, client, auth_headers, test_publication):
        """Test getting publications dashboard"""
        response = client.get("/api/publications/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_publications" in data
        assert "published_count" in data
        assert "publishing_count" in data
        assert "failed_count" in data
        assert "success_rate" in data
        assert "recent_publications" in data
        assert isinstance(data["recent_publications"], list)

    def test_get_publications_dashboard_unauthorized(self, client):
        """Test getting dashboard without authentication"""
        response = client.get("/api/publications/dashboard")
        
        assert response.status_code == 403

    def test_get_publications_dashboard_no_publications(self, client, auth_headers):
        """Test getting dashboard with no publications"""
        response = client.get("/api/publications/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_publications"] == 0
        assert data["success_rate"] == 0
        assert data["recent_publications"] == []

    def test_get_publications_dashboard_multiple_statuses(self, client, auth_headers, db_session, test_user, approved_book):
        """Test dashboard with publications in different statuses"""
        # Create publications with different statuses
        publications = [
            Publication(
                book_id=approved_book.id,
                status="published",
                price=4.99,
                auto_publish=True,
                created_at=datetime.now(timezone.utc),
                published_at=datetime.now(timezone.utc)
            ),
            Publication(
                book_id=approved_book.id,
                status="publishing",
                price=4.99,
                auto_publish=True,
                created_at=datetime.now(timezone.utc)
            ),
            Publication(
                book_id=approved_book.id,
                status="failed",
                price=4.99,
                auto_publish=True,
                error_message="Test error",
                created_at=datetime.now(timezone.utc)
            )
        ]
        
        db_session.add_all(publications)
        db_session.commit()
        
        response = client.get("/api/publications/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_publications"] >= 3
        assert data["published_count"] >= 1
        assert data["publishing_count"] >= 1
        assert data["failed_count"] >= 1
        assert 0 <= data["success_rate"] <= 100


class TestPublicationsValidation:
    """Test input validation for publications endpoints"""

    def test_invalid_publication_id_format(self, client, auth_headers):
        """Test endpoints with invalid publication ID format"""
        response = client.get("/api/publications/invalid_id/status", headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_negative_publication_id(self, client, auth_headers):
        """Test endpoints with negative publication ID"""
        response = client.get("/api/publications/-1/status", headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_zero_publication_id(self, client, auth_headers):
        """Test endpoints with zero publication ID"""
        response = client.get("/api/publications/0/status", headers=auth_headers)
        assert response.status_code == 404  # Not found

    def test_invalid_book_id_for_publish(self, client, auth_headers, sample_publication_data):
        """Test publishing with invalid book ID"""
        response = client.post("/api/publications/invalid_id/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        assert response.status_code == 422  # Validation error

    def test_publication_price_validation(self, client, auth_headers, approved_book):
        """Test publication price validation"""
        # Test price too low
        low_price_data = {
            "manuscript_id": "test_123",
            "price": 0.50,  # Below minimum
            "royalty_rate": 70,
            "auto_publish": True
        }
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=low_price_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    def test_publication_royalty_rate_validation(self, client, auth_headers, approved_book):
        """Test publication royalty rate validation"""
        invalid_royalty_data = {
            "manuscript_id": "test_123",
            "price": 4.99,
            "royalty_rate": 50,  # Invalid rate (should be 35 or 70)
            "auto_publish": True
        }
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=invalid_royalty_data, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error


class TestPublicationsPerformance:
    """Test performance aspects of publications endpoints"""

    def test_get_publications_pagination(self, client, auth_headers, db_session, test_user, approved_book):
        """Test that getting publications handles large numbers efficiently"""
        # Create multiple publications
        publications = []
        for i in range(15):
            publication = Publication(
                book_id=approved_book.id,
                status="published" if i % 2 == 0 else "failed",
                price=4.99,
                auto_publish=True,
                created_at=datetime.now(timezone.utc)
            )
            publications.append(publication)
        
        db_session.add_all(publications)
        db_session.commit()
        
        response = client.get("/api/publications/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 15  # Should return all publications

    def test_concurrent_publication_operations(self, client, auth_headers, test_publication):
        """Test that concurrent operations don't interfere"""
        # Simulate concurrent requests
        responses = []
        for _ in range(5):
            response = client.get(f"/api/publications/{test_publication.id}/status", 
                                headers=auth_headers)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
            assert response.json()["id"] == test_publication.id


class TestPublicationsErrorHandling:
    """Test error handling in publications endpoints"""

    def test_malformed_publication_request(self, client, auth_headers, approved_book):
        """Test handling of malformed publication request"""
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             data="invalid json", 
                             headers={**auth_headers, "Content-Type": "application/json"})
        
        assert response.status_code == 422  # Validation error

    def test_empty_publication_request(self, client, auth_headers, approved_book):
        """Test handling of empty publication request"""
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json={}, 
                             headers=auth_headers)
        
        assert response.status_code == 422  # Validation error

    @patch('app.api.publications.PUBLICATION_SERVICE_AVAILABLE', True)
    @patch('app.api.publications.is_celery_task')
    def test_celery_task_failure(self, mock_is_celery, client, auth_headers, approved_book, sample_publication_data):
        """Test handling of Celery task failures"""
        mock_is_celery.return_value = False  # Simulate task not available
        
        response = client.post(f"/api/publications/{approved_book.id}/publish", 
                             json=sample_publication_data, 
                             headers=auth_headers)
        
        # Should still create publication record but mark as failed
        assert response.status_code == 200

    def test_database_error_simulation(self, client, auth_headers):
        """Test handling of database errors"""
        # This would require mocking database failures
        # For now, test that normal operations work
        response = client.get("/api/publications/", headers=auth_headers)
        assert response.status_code == 200


class TestPublicationsIntegration:
    """Test integration scenarios for publications"""

    @patch('app.api.publications.PUBLICATION_SERVICE_AVAILABLE', True)
    @patch('app.api.publications.is_celery_task')
    @patch('app.api.publications.publish_to_kdp_task')
    def test_full_publication_workflow(self, mock_task, mock_is_celery, client, auth_headers, approved_book, sample_publication_data):
        """Test complete publication workflow"""
        mock_is_celery.return_value = True
        mock_task.delay.return_value = MagicMock(id="task_123")
        
        # 1. Start publication
        publish_response = client.post(f"/api/publications/{approved_book.id}/publish", 
                                     json=sample_publication_data, 
                                     headers=auth_headers)
        
        assert publish_response.status_code == 200
        publication_id = publish_response.json()["publication_id"]
        
        # 2. Check status
        status_response = client.get(f"/api/publications/{publication_id}/status", 
                                   headers=auth_headers)
        
        assert status_response.status_code == 200
        assert status_response.json()["status"] == "publishing"
        
        # 3. Check in publications list
        list_response = client.get("/api/publications/", headers=auth_headers)
        
        assert list_response.status_code == 200
        publication_ids = [pub["id"] for pub in list_response.json()]
        assert publication_id in publication_ids
        
        # 4. Check dashboard
        dashboard_response = client.get("/api/publications/dashboard", headers=auth_headers)
        
        assert dashboard_response.status_code == 200
        dashboard_data = dashboard_response.json()
        assert dashboard_data["total_publications"] >= 1
        assert dashboard_data["publishing_count"] >= 1
