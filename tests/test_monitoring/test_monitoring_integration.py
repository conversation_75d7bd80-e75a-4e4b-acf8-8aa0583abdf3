# tests/test_monitoring/test_monitoring_integration.py - Monitoring Integration Tests

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from app.monitoring.monitoring_setup import (
    MonitoringManager,
    initialize_monitoring,
    get_logger,
    monitor_operation,
    log_user_action,
    capture_exception
)


class TestMonitoringManager:
    """Test the MonitoringManager class"""
    
    def test_init(self):
        """Test MonitoringManager initialization"""
        manager = MonitoringManager()
        assert not manager.sentry_initialized
        assert not manager.logflare_initialized
        assert manager.structured_logger is None
        assert manager.request_id is None
    
    @patch('sentry_sdk.init')
    def test_initialize_sentry_success(self, mock_sentry_init, monkeypatch):
        """Test successful Sentry initialization"""
        monkeypatch.setenv("SENTRY_DSN", "https://<EMAIL>/123")
        
        manager = MonitoringManager()
        result = manager.initialize_sentry()
        
        assert result is True
        assert manager.sentry_initialized is True
        mock_sentry_init.assert_called_once()
    
    def test_initialize_sentry_no_dsn(self):
        """Test Sentry initialization with no DSN"""
        manager = MonitoringManager()
        result = manager.initialize_sentry()
        
        assert result is False
        assert manager.sentry_initialized is False
    
    @patch('logflare.LogflareClient')
    @patch('structlog.configure')
    @patch('structlog.get_logger')
    def test_initialize_logflare_success(self, mock_get_logger, mock_configure, 
                                       mock_client, monkeypatch):
        """Test successful Logflare initialization"""
        monkeypatch.setenv("LOGFLARE_API_KEY", "test-key")
        monkeypatch.setenv("LOGFLARE_SOURCE_ID", "test-source")
        
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        manager = MonitoringManager()
        result = manager.initialize_logflare()
        
        assert result is True
        assert manager.logflare_initialized is True
        mock_configure.assert_called_once()
        mock_client.assert_called_once()
    
    def test_initialize_logflare_no_credentials(self):
        """Test Logflare initialization with no credentials"""
        manager = MonitoringManager()
        result = manager.initialize_logflare()
        
        assert result is False
        assert manager.logflare_initialized is False
    
    @patch('sentry_sdk.configure_scope')
    def test_set_request_context(self, mock_configure_scope):
        """Test setting request context"""
        manager = MonitoringManager()
        manager.sentry_initialized = True
        
        mock_scope = Mock()
        mock_configure_scope.return_value.__enter__.return_value = mock_scope
        
        manager.set_request_context("test-request-id", "test-user-id", {"key": "value"})
        
        assert manager.request_id == "test-request-id"
        mock_scope.set_tag.assert_called_with("request_id", "test-request-id")
        mock_scope.set_user.assert_called_with({"id": "test-user-id"})
        mock_scope.set_extra.assert_called_with("key", "value")
    
    def test_clear_request_context(self):
        """Test clearing request context"""
        manager = MonitoringManager()
        manager.request_id = "test-request-id"
        
        manager.clear_request_context()
        
        assert manager.request_id is None
    
    @pytest.mark.asyncio
    async def test_monitor_operation_success(self):
        """Test successful operation monitoring"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        
        async with manager.monitor_operation("test_operation", key="value") as operation_id:
            assert operation_id is not None
            manager.structured_logger.info.assert_called_with(
                "Operation started", 
                operation_id=operation_id,
                operation_name="test_operation",
                key="value"
            )
        
        # Check completion log
        assert manager.structured_logger.info.call_count == 2
    
    @pytest.mark.asyncio
    async def test_monitor_operation_exception(self):
        """Test operation monitoring with exception"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        manager.sentry_initialized = True
        
        with patch('sentry_sdk.capture_exception') as mock_capture:
            with pytest.raises(ValueError):
                async with manager.monitor_operation("test_operation"):
                    raise ValueError("Test error")
            
            mock_capture.assert_called_once()
            manager.structured_logger.error.assert_called_once()
    
    def test_log_user_action(self):
        """Test user action logging"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        
        manager.log_user_action("login", "user-123", {"ip": "127.0.0.1"})
        
        manager.structured_logger.info.assert_called_once()
        call_args = manager.structured_logger.info.call_args[1]
        assert call_args['event_type'] == 'user_action'
        assert call_args['action'] == 'login'
        assert call_args['user_id'] == 'user-123'
        assert call_args['ip'] == '127.0.0.1'
    
    def test_log_system_event(self):
        """Test system event logging"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        
        manager.log_system_event("deployment", {"version": "1.2.3"})
        
        manager.structured_logger.info.assert_called_once()
        call_args = manager.structured_logger.info.call_args[1]
        assert call_args['event_type'] == 'system_event'
        assert call_args['system_event_type'] == 'deployment'
        assert call_args['version'] == '1.2.3'
    
    def test_log_performance_metric(self):
        """Test performance metric logging"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        
        manager.log_performance_metric("response_time", 123.45, {"endpoint": "/api/test"})
        
        manager.structured_logger.info.assert_called_once()
        call_args = manager.structured_logger.info.call_args[1]
        assert call_args['event_type'] == 'performance_metric'
        assert call_args['metric_name'] == 'response_time'
        assert call_args['metric_value'] == 123.45
        assert call_args['tags'] == {"endpoint": "/api/test"}
    
    @patch('sentry_sdk.capture_exception')
    @patch('sentry_sdk.configure_scope')
    def test_capture_exception(self, mock_configure_scope, mock_capture):
        """Test exception capture"""
        manager = MonitoringManager()
        manager.sentry_initialized = True
        manager.structured_logger = Mock()
        
        mock_scope = Mock()
        mock_configure_scope.return_value.__enter__.return_value = mock_scope
        
        test_exception = ValueError("Test error")
        manager.capture_exception(test_exception, {"context": "test"})
        
        mock_capture.assert_called_once_with(test_exception)
        mock_scope.set_extra.assert_called_with("context", "test")
        manager.structured_logger.error.assert_called_once()
    
    def test_get_logger(self):
        """Test logger retrieval"""
        manager = MonitoringManager()
        manager.structured_logger = Mock()
        
        logger = manager.get_logger("test_module")
        
        manager.structured_logger.bind.assert_called_with(logger_name="test_module")


class TestMonitoringHelpers:
    """Test monitoring helper functions"""
    
    @patch('app.monitoring.monitoring_setup.monitoring_manager')
    def test_initialize_monitoring(self, mock_manager):
        """Test monitoring initialization function"""
        mock_manager.initialize_sentry.return_value = True
        mock_manager.initialize_logflare.return_value = False
        
        result = initialize_monitoring()
        
        assert result == {'sentry': True, 'logflare': False}
        mock_manager.initialize_sentry.assert_called_once()
        mock_manager.initialize_logflare.assert_called_once()
    
    @patch('app.monitoring.monitoring_setup.monitoring_manager')
    def test_get_logger_helper(self, mock_manager):
        """Test get_logger helper function"""
        mock_logger = Mock()
        mock_manager.get_logger.return_value = mock_logger
        
        logger = get_logger("test_module")
        
        mock_manager.get_logger.assert_called_with("test_module")
        assert logger == mock_logger
    
    @patch('app.monitoring.monitoring_setup.monitoring_manager')
    def test_log_user_action_helper(self, mock_manager):
        """Test log_user_action helper function"""
        log_user_action("login", "user-123", {"ip": "127.0.0.1"})
        
        mock_manager.log_user_action.assert_called_with(
            "login", "user-123", {"ip": "127.0.0.1"}
        )
    
    @patch('app.monitoring.monitoring_setup.monitoring_manager')
    def test_capture_exception_helper(self, mock_manager):
        """Test capture_exception helper function"""
        test_exception = ValueError("Test error")
        
        capture_exception(test_exception, {"context": "test"})
        
        mock_manager.capture_exception.assert_called_with(
            test_exception, {"context": "test"}
        )


class TestMonitoringIntegration:
    """Integration tests for monitoring system"""
    
    @pytest.mark.asyncio
    async def test_monitor_operation_context_manager(self, monitoring_test_setup):
        """Test monitor_operation as context manager"""
        async with monitor_operation("test_operation", key="value") as operation_id:
            assert operation_id == "test-operation-id"
    
    @pytest.mark.asyncio
    async def test_agent_execution_monitoring(self, monitoring_test_setup):
        """Test monitoring during agent execution"""
        from app.agents.pydantic_ai_manager import PydanticAIAgentManager
        
        with patch.object(PydanticAIAgentManager, '_agent_executors', {}):
            manager = PydanticAIAgentManager()
            
            # Mock an executor
            mock_executor = AsyncMock()
            mock_executor.return_value = Mock(status=Mock(value="success"))
            manager._agent_executors["test_agent"] = mock_executor
            
            result = await manager.execute_agent("test_agent", {"key": "value"})
            
            # Verify monitoring was called
            assert result is not None
    
    def test_api_endpoint_monitoring(self, monitoring_test_setup):
        """Test monitoring in API endpoints"""
        # This would be tested with actual FastAPI test client
        # For now, just verify the monitoring imports work
        from app.api.supabase_books import logger
        assert logger is not None
    
    @pytest.mark.asyncio
    async def test_middleware_monitoring(self):
        """Test monitoring middleware functionality"""
        from app.middleware.monitoring_middleware import MonitoringMiddleware
        from starlette.testclient import TestClient
        from fastapi import FastAPI, Request
        
        app = FastAPI()
        app.add_middleware(MonitoringMiddleware)
        
        @app.get("/test")
        async def test_endpoint(request: Request):
            return {"request_id": getattr(request.state, 'request_id', None)}
        
        with TestClient(app) as client:
            response = client.get("/test")
            assert response.status_code == 200
            assert "X-Request-ID" in response.headers
    
    def test_security_monitoring_middleware(self):
        """Test security monitoring middleware"""
        from app.middleware.monitoring_middleware import SecurityMonitoringMiddleware
        from starlette.testclient import TestClient
        from fastapi import FastAPI
        
        app = FastAPI()
        app.add_middleware(SecurityMonitoringMiddleware)
        
        @app.get("/test")
        async def test_endpoint():
            return {"status": "ok"}
        
        with TestClient(app) as client:
            # Test suspicious pattern detection
            response = client.get("/test?q=SELECT%20*%20FROM%20users")
            assert response.status_code == 200  # Should still work but log warning
    
    def test_performance_monitoring_middleware(self):
        """Test performance monitoring middleware"""
        from app.middleware.monitoring_middleware import PerformanceMonitoringMiddleware
        from starlette.testclient import TestClient
        from fastapi import FastAPI
        
        app = FastAPI()
        app.add_middleware(PerformanceMonitoringMiddleware, slow_request_threshold=0.001)
        
        @app.get("/test")
        async def test_endpoint():
            await asyncio.sleep(0.002)  # Deliberately slow
            return {"status": "ok"}
        
        with TestClient(app) as client:
            response = client.get("/test")
            assert response.status_code == 200
    
    def test_data_scrubbing(self):
        """Test sensitive data scrubbing"""
        manager = MonitoringManager()
        
        # Test the before_send function (would be used in real Sentry init)
        test_event = {
            'extra': {
                'password': 'secret123',
                'openai_api_key': 'sk-test',
                'safe_data': 'this is fine'
            }
        }
        
        # This is a simplified test - in real implementation,
        # the before_send function would be called by Sentry
        sensitive_keys = ['password', 'openai_api_key']
        
        def scrub_dict(data):
            if isinstance(data, dict):
                for key, value in data.items():
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        data[key] = "[REDACTED]"
                    elif isinstance(value, (dict, list)):
                        scrub_dict(value)
        
        scrub_dict(test_event)
        
        assert test_event['extra']['password'] == "[REDACTED]"
        assert test_event['extra']['openai_api_key'] == "[REDACTED]"
        assert test_event['extra']['safe_data'] == "this is fine"