# tests/test_pytrends_integration.py - Test PyTrends integration

import pytest
from unittest.mock import Mock, patch
import pandas as pd


@pytest.mark.asyncio
async def test_pytrends_import():
    """Test that pytrends can be imported"""
    from pytrends.request import TrendReq
    assert TrendReq is not None


def test_get_pytrends_data_basic():
    """Test basic pytrends data retrieval"""
    from app.agents.pydantic_ai_trend_analyzer import get_pytrends_data
    
    # Mock pytrends to avoid actual API calls in tests
    with patch('app.agents.pydantic_ai_trend_analyzer.TrendReq') as mock_trends:
        # Setup mock
        mock_instance = Mock()
        mock_trends.return_value = mock_instance
        
        # Mock interest over time data
        mock_df = pd.DataFrame({
            'productivity': [85, 90, 88, 92, 87],
            'mindfulness': [70, 75, 73, 78, 74]
        })
        mock_instance.interest_over_time.return_value = mock_df
        mock_instance.related_queries = {'productivity': {}, 'mindfulness': {}}
        mock_instance.trending_searches.return_value = pd.DataFrame(['meditation', 'focus', 'goals'])
        
        # Test the function
        result = get_pytrends_data(['productivity', 'mindfulness'])
        
        # Verify structure
        assert 'keywords' in result
        assert 'timeframe' in result
        assert 'peak_popularity' in result
        assert 'average_interest' in result
        assert result['keywords'] == ['productivity', 'mindfulness']


def test_get_book_category_trends():
    """Test book category trend analysis"""
    from app.agents.pydantic_ai_trend_analyzer import get_book_category_trends
    
    with patch('app.agents.pydantic_ai_trend_analyzer.get_pytrends_data') as mock_trends:
        # Mock the trend data
        mock_trends.return_value = {
            'keywords': ['self improvement', 'personal development'],
            'average_interest': {'self improvement': 75.0, 'personal development': 68.0},
            'peak_popularity': {'self improvement': 95, 'personal development': 88},
            'trending_searches': ['productivity', 'mindfulness', 'goals']
        }
        
        result = get_book_category_trends('self help')
        
        # Verify structure
        assert 'category' in result
        assert 'book_potential' in result
        assert result['category'] == 'self help'
        assert 'score' in result['book_potential']
        assert 'recommendation' in result['book_potential']


@pytest.mark.asyncio 
async def test_analyze_market_trends_with_pytrends():
    """Test the enhanced market analysis with pytrends"""
    from app.agents.pydantic_ai_trend_analyzer import analyze_market_trends_with_pytrends
    
    with patch('app.agents.pydantic_ai_trend_analyzer.get_book_category_trends') as mock_category:
        # Mock category trend data
        mock_category.return_value = {
            'category': 'self help',
            'average_interest': {'productivity': 75.0, 'mindfulness': 68.0},
            'peak_popularity': {'productivity': 95, 'mindfulness': 88},
            'book_potential': {'score': 72.5, 'recommendation': 'high_potential'},
            'trending_searches': ['productivity tips', 'mindfulness guide']
        }
        
        result = await analyze_market_trends_with_pytrends(['self help'])
        
        # Verify result structure
        assert result.status.value == 'success'
        assert result.data is not None
        assert 'trend_data' in result.data
        assert 'high_potential_categories' in result.data
        assert 'recommendations' in result.data
        assert len(result.data['trend_data']) == 1
        assert result.data['high_potential_categories'] == ['self help']


def test_book_potential_calculation():
    """Test book publishing potential calculation"""
    from app.agents.pydantic_ai_trend_analyzer import _calculate_book_potential
    
    # Test high potential
    trend_data = {
        'average_interest': {'productivity': 80.0, 'mindfulness': 75.0},
        'peak_popularity': {'productivity': 95, 'mindfulness': 88}
    }
    
    result = _calculate_book_potential(trend_data)
    
    assert result['recommendation'] == 'high_potential'
    assert result['score'] > 70
    assert 'avg_interest' in result
    assert 'peak_interest' in result
    
    # Test low potential
    trend_data_low = {
        'average_interest': {'topic1': 15.0, 'topic2': 10.0},
        'peak_popularity': {'topic1': 25, 'topic2': 20}
    }
    
    result_low = _calculate_book_potential(trend_data_low)
    assert result_low['recommendation'] in ['low_potential', 'insufficient_interest']
    assert result_low['score'] < 40


def test_category_keywords_mapping():
    """Test that all book categories have proper keyword mappings"""
    from app.agents.pydantic_ai_trend_analyzer import get_book_category_trends
    
    categories = ['self help', 'business', 'health', 'technology', 'romance', 'mystery']
    
    with patch('app.agents.pydantic_ai_trend_analyzer.get_pytrends_data') as mock_trends:
        mock_trends.return_value = {
            'keywords': [],
            'average_interest': {},
            'peak_popularity': {},
            'trending_searches': []
        }
        
        for category in categories:
            result = get_book_category_trends(category)
            assert result['category'] == category
            mock_trends.assert_called()  # Verify it was called with keywords


def test_error_handling():
    """Test error handling in pytrends functions"""
    from app.agents.pydantic_ai_trend_analyzer import get_pytrends_data
    
    with patch('app.agents.pydantic_ai_trend_analyzer.TrendReq') as mock_trends:
        # Mock an exception
        mock_trends.side_effect = Exception("Network error")
        
        result = get_pytrends_data(['test'])
        
        # Should return error structure instead of crashing
        assert 'error' in result
        assert 'keywords' in result
        assert result['keywords'] == ['test']