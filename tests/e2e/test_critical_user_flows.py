"""
End-to-End Tests for Critical User Flows.

This module contains comprehensive E2E tests that validate the most
important user journeys through the Publish AI platform.
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import pytest
import httpx
from faker import Faker

fake = Faker()


class E2ETestUser:
    """Test user for end-to-end testing."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)  # Longer timeout for E2E
        
        # User data
        self.user_id = None
        self.email = f"e2e_test_{uuid.uuid4().hex[:8]}@example.com"
        self.password = "E2ETest123!"
        self.name = fake.name()
        self.auth_token = None
        
        # Test data storage
        self.created_books = []
        self.generated_manuscripts = []
        self.publications = []
        self.api_keys = []
    
    async def register(self) -> bool:
        """Register a new test user."""
        registration_data = {
            "email": self.email,
            "password": self.password,
            "name": self.name
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/auth/register",
            json=registration_data
        )
        
        if response.status_code == 201:
            user_data = response.json()
            self.user_id = user_data.get("user", {}).get("id")
            return True
        elif response.status_code == 400 and "already exists" in response.text:
            # User already exists, that's OK for testing
            return True
        
        return False
    
    async def login(self) -> bool:
        """Login and get authentication token."""
        login_data = {
            "email": self.email,
            "password": self.password
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/auth/login",
            json=login_data
        )
        
        if response.status_code == 200:
            auth_data = response.json()
            self.auth_token = auth_data.get("access_token")
            if not self.user_id:
                self.user_id = auth_data.get("user", {}).get("id")
            return True
        
        return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers."""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    async def create_book(self, book_data: Dict[str, Any]) -> Optional[str]:
        """Create a new book and return book ID."""
        response = await self.client.post(
            f"{self.base_url}/api/books",
            json=book_data,
            headers=self.get_auth_headers()
        )
        
        if response.status_code == 201:
            book = response.json()
            book_id = book.get("id")
            self.created_books.append(book_id)
            return book_id
        
        return None
    
    async def generate_manuscript(self, book_id: str, generation_params: Dict[str, Any]) -> bool:
        """Generate manuscript for a book."""
        response = await self.client.post(
            f"{self.base_url}/api/books/{book_id}/generate",
            json=generation_params,
            headers=self.get_auth_headers()
        )
        
        if response.status_code in [200, 202]:  # Accept both sync and async
            manuscript_data = response.json()
            self.generated_manuscripts.append({
                "book_id": book_id,
                "generation_id": manuscript_data.get("generation_id"),
                "status": manuscript_data.get("status", "pending")
            })
            return True
        
        return False
    
    async def publish_book(self, book_id: str, publication_params: Dict[str, Any]) -> Optional[str]:
        """Publish a book and return publication ID."""
        response = await self.client.post(
            f"{self.base_url}/api/publications",
            json={
                "book_id": book_id,
                **publication_params
            },
            headers=self.get_auth_headers()
        )
        
        if response.status_code in [200, 201, 202]:
            publication = response.json()
            publication_id = publication.get("id")
            self.publications.append(publication_id)
            return publication_id
        
        return None
    
    async def check_analytics(self) -> Dict[str, Any]:
        """Check user analytics."""
        response = await self.client.get(
            f"{self.base_url}/api/analytics/user",
            headers=self.get_auth_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        
        return {}
    
    async def cleanup(self):
        """Clean up test data."""
        # Delete created books
        for book_id in self.created_books:
            try:
                await self.client.delete(
                    f"{self.base_url}/api/books/{book_id}",
                    headers=self.get_auth_headers()
                )
            except Exception:
                pass
        
        # Delete API keys if any were created
        for api_key_id in self.api_keys:
            try:
                await self.client.delete(
                    f"{self.base_url}/api/security/api-keys/{api_key_id}",
                    headers=self.get_auth_headers()
                )
            except Exception:
                pass
        
        await self.client.aclose()


class E2ECriticalFlows:
    """Critical user flow test suite."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    async def test_complete_book_creation_flow(self) -> Dict[str, Any]:
        """
        Test the complete book creation flow:
        Register → Login → Create Book → Generate Content → Publish → Monitor
        """
        results = {
            "flow_name": "complete_book_creation_flow",
            "start_time": datetime.utcnow(),
            "steps_completed": [],
            "success": False,
            "error_message": None,
            "performance_metrics": {}
        }
        
        user = E2ETestUser(self.base_url)
        
        try:
            # Step 1: Register user
            step_start = time.time()
            if not await user.register():
                raise Exception("User registration failed")
            results["steps_completed"].append("registration")
            results["performance_metrics"]["registration_time"] = time.time() - step_start
            
            # Step 2: Login
            step_start = time.time()
            if not await user.login():
                raise Exception("User login failed")
            results["steps_completed"].append("login")
            results["performance_metrics"]["login_time"] = time.time() - step_start
            
            # Step 3: Create book
            step_start = time.time()
            book_data = {
                "title": f"E2E Test Book - {fake.catch_phrase()}",
                "description": fake.text(max_nb_chars=200),
                "genre": "fiction",
                "target_audience": "adults",
                "content_type": "standard"
            }
            
            book_id = await user.create_book(book_data)
            if not book_id:
                raise Exception("Book creation failed")
            results["steps_completed"].append("book_creation")
            results["performance_metrics"]["book_creation_time"] = time.time() - step_start
            
            # Step 4: Generate manuscript
            step_start = time.time()
            generation_params = {
                "style": "narrative",
                "length": "short",
                "outline_only": False
            }
            
            if not await user.generate_manuscript(book_id, generation_params):
                raise Exception("Manuscript generation failed")
            results["steps_completed"].append("manuscript_generation")
            results["performance_metrics"]["manuscript_generation_time"] = time.time() - step_start
            
            # Step 5: Wait for generation to complete (with timeout)
            step_start = time.time()
            generation_complete = False
            max_wait_time = 60  # 60 seconds timeout
            
            while time.time() - step_start < max_wait_time:
                response = await user.client.get(
                    f"{user.base_url}/api/books/{book_id}",
                    headers=user.get_auth_headers()
                )
                
                if response.status_code == 200:
                    book_status = response.json()
                    if book_status.get("content_status") == "completed":
                        generation_complete = True
                        break
                
                await asyncio.sleep(2)  # Wait 2 seconds before checking again
            
            if not generation_complete:
                # Generation might be async, that's OK for testing
                results["steps_completed"].append("manuscript_generation_pending")
            else:
                results["steps_completed"].append("manuscript_generation_complete")
            
            results["performance_metrics"]["generation_wait_time"] = time.time() - step_start
            
            # Step 6: Publish book (simulate)
            step_start = time.time()
            publication_params = {
                "platform": "test_platform",
                "pricing": {"currency": "USD", "amount": 9.99},
                "metadata": {"test_publication": True}
            }
            
            publication_id = await user.publish_book(book_id, publication_params)
            if publication_id:
                results["steps_completed"].append("publication")
            else:
                results["steps_completed"].append("publication_failed")
            
            results["performance_metrics"]["publication_time"] = time.time() - step_start
            
            # Step 7: Check analytics
            step_start = time.time()
            analytics = await user.check_analytics()
            if analytics:
                results["steps_completed"].append("analytics")
                results["analytics_data"] = analytics
            
            results["performance_metrics"]["analytics_time"] = time.time() - step_start
            
            # Flow completed successfully
            results["success"] = True
            
        except Exception as e:
            results["error_message"] = str(e)
            
        finally:
            results["end_time"] = datetime.utcnow()
            results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
            await user.cleanup()
        
        return results
    
    async def test_concurrent_user_flow(self, num_users: int = 5) -> Dict[str, Any]:
        """
        Test concurrent user operations to validate system behavior under load.
        """
        results = {
            "flow_name": "concurrent_user_flow",
            "start_time": datetime.utcnow(),
            "num_users": num_users,
            "user_results": [],
            "success": False,
            "error_message": None
        }
        
        async def single_user_flow(user_index: int) -> Dict[str, Any]:
            """Single user flow for concurrent testing."""
            user = E2ETestUser(self.base_url)
            user_result = {
                "user_index": user_index,
                "steps_completed": [],
                "success": False,
                "error": None
            }
            
            try:
                # Register and login
                if await user.register() and await user.login():
                    user_result["steps_completed"].append("auth")
                    
                    # Create book
                    book_data = {
                        "title": f"Concurrent Test Book {user_index}",
                        "description": f"Test book for concurrent user {user_index}",
                        "genre": "fiction",
                        "target_audience": "adults"
                    }
                    
                    book_id = await user.create_book(book_data)
                    if book_id:
                        user_result["steps_completed"].append("book_creation")
                        user_result["book_id"] = book_id
                        
                        # Check analytics
                        analytics = await user.check_analytics()
                        if analytics:
                            user_result["steps_completed"].append("analytics")
                        
                        user_result["success"] = True
                
            except Exception as e:
                user_result["error"] = str(e)
            
            finally:
                await user.cleanup()
            
            return user_result
        
        try:
            # Run concurrent user flows
            tasks = [single_user_flow(i) for i in range(num_users)]
            user_results = await asyncio.gather(*tasks)
            
            results["user_results"] = user_results
            
            # Calculate success metrics
            successful_users = sum(1 for result in user_results if result["success"])
            results["success_rate"] = successful_users / num_users
            results["successful_users"] = successful_users
            results["failed_users"] = num_users - successful_users
            
            # Consider the test successful if at least 80% of users succeed
            results["success"] = results["success_rate"] >= 0.8
            
        except Exception as e:
            results["error_message"] = str(e)
        
        finally:
            results["end_time"] = datetime.utcnow()
            results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
        
        return results
    
    async def test_api_integration_flow(self) -> Dict[str, Any]:
        """
        Test API integration flow including authentication, CRUD operations, and error handling.
        """
        results = {
            "flow_name": "api_integration_flow",
            "start_time": datetime.utcnow(),
            "api_tests": [],
            "success": False,
            "error_message": None
        }
        
        user = E2ETestUser(self.base_url)
        
        try:
            # Authentication flow
            auth_test = {"name": "authentication", "success": False}
            if await user.register() and await user.login():
                auth_test["success"] = True
            results["api_tests"].append(auth_test)
            
            # CRUD operations test
            crud_test = {"name": "crud_operations", "success": False, "operations": []}
            
            # Create
            book_data = {
                "title": "API Integration Test Book",
                "description": "Test book for API integration testing",
                "genre": "non-fiction",
                "target_audience": "adults"
            }
            
            book_id = await user.create_book(book_data)
            if book_id:
                crud_test["operations"].append({"operation": "create", "success": True})
                
                # Read
                response = await user.client.get(
                    f"{user.base_url}/api/books/{book_id}",
                    headers=user.get_auth_headers()
                )
                
                if response.status_code == 200:
                    crud_test["operations"].append({"operation": "read", "success": True})
                    
                    # Update
                    update_data = {"description": "Updated description for API test"}
                    response = await user.client.patch(
                        f"{user.base_url}/api/books/{book_id}",
                        json=update_data,
                        headers=user.get_auth_headers()
                    )
                    
                    if response.status_code == 200:
                        crud_test["operations"].append({"operation": "update", "success": True})
                
                # List
                response = await user.client.get(
                    f"{user.base_url}/api/books",
                    headers=user.get_auth_headers()
                )
                
                if response.status_code == 200:
                    books = response.json()
                    if any(book.get("id") == book_id for book in books.get("books", [])):
                        crud_test["operations"].append({"operation": "list", "success": True})
            
            crud_test["success"] = len(crud_test["operations"]) >= 3  # At least 3 operations successful
            results["api_tests"].append(crud_test)
            
            # Error handling test
            error_test = {"name": "error_handling", "success": False, "error_tests": []}
            
            # Test unauthorized access
            response = await user.client.get(f"{user.base_url}/api/books")  # No auth header
            if response.status_code == 401:
                error_test["error_tests"].append({"test": "unauthorized", "success": True})
            
            # Test invalid book ID
            response = await user.client.get(
                f"{user.base_url}/api/books/invalid-id",
                headers=user.get_auth_headers()
            )
            if response.status_code == 404:
                error_test["error_tests"].append({"test": "not_found", "success": True})
            
            # Test invalid input
            response = await user.client.post(
                f"{user.base_url}/api/books",
                json={"invalid": "data"},  # Missing required fields
                headers=user.get_auth_headers()
            )
            if response.status_code == 422:  # Validation error
                error_test["error_tests"].append({"test": "validation_error", "success": True})
            
            error_test["success"] = len(error_test["error_tests"]) >= 2
            results["api_tests"].append(error_test)
            
            # Overall success
            successful_tests = sum(1 for test in results["api_tests"] if test["success"])
            results["success"] = successful_tests == len(results["api_tests"])
            
        except Exception as e:
            results["error_message"] = str(e)
        
        finally:
            results["end_time"] = datetime.utcnow()
            results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
            await user.cleanup()
        
        return results
    
    async def test_data_consistency_flow(self) -> Dict[str, Any]:
        """
        Test data consistency across different operations and ensure database integrity.
        """
        results = {
            "flow_name": "data_consistency_flow",
            "start_time": datetime.utcnow(),
            "consistency_checks": [],
            "success": False,
            "error_message": None
        }
        
        user = E2ETestUser(self.base_url)
        
        try:
            # Setup
            if not (await user.register() and await user.login()):
                raise Exception("Failed to setup user for consistency testing")
            
            # Test 1: Book creation and retrieval consistency
            consistency_test_1 = {"name": "book_creation_consistency", "success": False}
            
            original_book_data = {
                "title": "Consistency Test Book",
                "description": "Testing data consistency",
                "genre": "fiction",
                "target_audience": "adults",
                "metadata": {"test": "consistency", "value": 42}
            }
            
            book_id = await user.create_book(original_book_data)
            if book_id:
                # Retrieve the book and compare data
                response = await user.client.get(
                    f"{user.base_url}/api/books/{book_id}",
                    headers=user.get_auth_headers()
                )
                
                if response.status_code == 200:
                    retrieved_book = response.json()
                    
                    # Check key fields match
                    consistency_test_1["success"] = (
                        retrieved_book.get("title") == original_book_data["title"] and
                        retrieved_book.get("description") == original_book_data["description"] and
                        retrieved_book.get("genre") == original_book_data["genre"]
                    )
                    
                    consistency_test_1["original_data"] = original_book_data
                    consistency_test_1["retrieved_data"] = retrieved_book
            
            results["consistency_checks"].append(consistency_test_1)
            
            # Test 2: User analytics consistency
            consistency_test_2 = {"name": "analytics_consistency", "success": False}
            
            # Get initial analytics
            initial_analytics = await user.check_analytics()
            initial_book_count = initial_analytics.get("total_books", 0)
            
            # Create another book
            book_data_2 = {
                "title": "Second Consistency Test Book",
                "description": "Second book for testing",
                "genre": "non-fiction",
                "target_audience": "adults"
            }
            
            book_id_2 = await user.create_book(book_data_2)
            if book_id_2:
                # Small delay to allow analytics to update
                await asyncio.sleep(1)
                
                # Get updated analytics
                updated_analytics = await user.check_analytics()
                updated_book_count = updated_analytics.get("total_books", 0)
                
                # Check if book count increased
                consistency_test_2["success"] = updated_book_count > initial_book_count
                consistency_test_2["initial_count"] = initial_book_count
                consistency_test_2["updated_count"] = updated_book_count
            
            results["consistency_checks"].append(consistency_test_2)
            
            # Test 3: Book listing consistency
            consistency_test_3 = {"name": "book_listing_consistency", "success": False}
            
            response = await user.client.get(
                f"{user.base_url}/api/books",
                headers=user.get_auth_headers()
            )
            
            if response.status_code == 200:
                books_list = response.json()
                user_books = books_list.get("books", [])
                
                # Check if both created books are in the list
                book_ids_in_list = [book.get("id") for book in user_books]
                
                consistency_test_3["success"] = (
                    book_id in book_ids_in_list and
                    book_id_2 in book_ids_in_list
                )
                
                consistency_test_3["expected_books"] = [book_id, book_id_2]
                consistency_test_3["found_books"] = book_ids_in_list
            
            results["consistency_checks"].append(consistency_test_3)
            
            # Overall success
            successful_checks = sum(1 for check in results["consistency_checks"] if check["success"])
            results["success"] = successful_checks == len(results["consistency_checks"])
            
        except Exception as e:
            results["error_message"] = str(e)
        
        finally:
            results["end_time"] = datetime.utcnow()
            results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
            await user.cleanup()
        
        return results


# Pytest integration
@pytest.mark.asyncio
@pytest.mark.e2e
async def test_complete_book_creation_flow():
    """Test complete book creation flow from registration to publication."""
    flow_tester = E2ECriticalFlows()
    result = await flow_tester.test_complete_book_creation_flow()
    
    # Assert critical steps completed
    assert result["success"], f"Book creation flow failed: {result.get('error_message')}"
    assert "registration" in result["steps_completed"]
    assert "login" in result["steps_completed"]
    assert "book_creation" in result["steps_completed"]
    
    # Performance assertions
    assert result["performance_metrics"]["registration_time"] < 5.0, "Registration too slow"
    assert result["performance_metrics"]["login_time"] < 3.0, "Login too slow"
    assert result["total_duration"] < 120.0, "Total flow took too long"


@pytest.mark.asyncio
@pytest.mark.e2e
async def test_concurrent_users():
    """Test system behavior with concurrent users."""
    flow_tester = E2ECriticalFlows()
    result = await flow_tester.test_concurrent_user_flow(num_users=3)
    
    # Assert system handles concurrent users
    assert result["success"], "Concurrent user test failed"
    assert result["success_rate"] >= 0.8, f"Success rate too low: {result['success_rate']}"
    assert result["successful_users"] >= 2, "Not enough users succeeded"


@pytest.mark.asyncio
@pytest.mark.e2e
async def test_api_integration():
    """Test API integration and error handling."""
    flow_tester = E2ECriticalFlows()
    result = await flow_tester.test_api_integration_flow()
    
    # Assert API integration works
    assert result["success"], f"API integration test failed: {result.get('error_message')}"
    
    # Check specific API tests
    auth_test = next((test for test in result["api_tests"] if test["name"] == "authentication"), None)
    assert auth_test and auth_test["success"], "Authentication test failed"
    
    crud_test = next((test for test in result["api_tests"] if test["name"] == "crud_operations"), None)
    assert crud_test and crud_test["success"], "CRUD operations test failed"


@pytest.mark.asyncio
@pytest.mark.e2e
async def test_data_consistency():
    """Test data consistency across operations."""
    flow_tester = E2ECriticalFlows()
    result = await flow_tester.test_data_consistency_flow()
    
    # Assert data consistency
    assert result["success"], f"Data consistency test failed: {result.get('error_message')}"
    
    # Check specific consistency tests
    for check in result["consistency_checks"]:
        assert check["success"], f"Consistency check failed: {check['name']}"


if __name__ == "__main__":
    # Run E2E tests manually
    async def main():
        print("🚀 Starting End-to-End Critical Flow Tests")
        
        flow_tester = E2ECriticalFlows()
        
        # Test 1: Complete book creation flow
        print("\n📖 Testing complete book creation flow...")
        result1 = await flow_tester.test_complete_book_creation_flow()
        
        print(f"✅ Book Creation Flow: {'PASSED' if result1['success'] else 'FAILED'}")
        print(f"   Steps: {', '.join(result1['steps_completed'])}")
        print(f"   Duration: {result1['total_duration']:.2f}s")
        
        # Test 2: Concurrent users
        print("\n👥 Testing concurrent users...")
        result2 = await flow_tester.test_concurrent_user_flow(num_users=3)
        
        print(f"✅ Concurrent Users: {'PASSED' if result2['success'] else 'FAILED'}")
        print(f"   Success Rate: {result2['success_rate']:.1%}")
        print(f"   Successful Users: {result2['successful_users']}/{result2['num_users']}")
        
        # Test 3: API integration
        print("\n🔌 Testing API integration...")
        result3 = await flow_tester.test_api_integration_flow()
        
        print(f"✅ API Integration: {'PASSED' if result3['success'] else 'FAILED'}")
        print(f"   Tests: {len([t for t in result3['api_tests'] if t['success']])}/{len(result3['api_tests'])} passed")
        
        # Test 4: Data consistency
        print("\n🔄 Testing data consistency...")
        result4 = await flow_tester.test_data_consistency_flow()
        
        print(f"✅ Data Consistency: {'PASSED' if result4['success'] else 'FAILED'}")
        print(f"   Checks: {len([c for c in result4['consistency_checks'] if c['success']])}/{len(result4['consistency_checks'])} passed")
        
        # Overall summary
        total_tests = 4
        passed_tests = sum([
            result1['success'],
            result2['success'],
            result3['success'],
            result4['success']
        ])
        
        print(f"\n📊 Overall E2E Test Results:")
        print(f"   Passed: {passed_tests}/{total_tests}")
        print(f"   Success Rate: {passed_tests/total_tests:.1%}")
        
        if passed_tests == total_tests:
            print("🎉 All critical user flows are working correctly!")
        else:
            print("⚠️  Some critical flows need attention.")
    
    asyncio.run(main())