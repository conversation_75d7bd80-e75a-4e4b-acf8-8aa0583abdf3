"""
Browser Automation Tests for Publish AI Platform.

This module provides browser automation tests using <PERSON><PERSON> to validate
the user interface and end-to-end user interactions.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from pathlib import Path

import pytest
from faker import Faker

fake = Faker()

# Try to import <PERSON><PERSON>, but gracefully handle if not available
try:
    from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Page = Browser = BrowserContext = None


class BrowserTestUser:
    """Browser automation test user."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:3000"):
        self.page = page
        self.base_url = base_url
        self.user_email = f"browser_test_{fake.random_int(min=1000, max=9999)}@example.com"
        self.user_password = "BrowserTest123!"
        self.user_name = fake.name()
    
    async def navigate_to_home(self):
        """Navigate to the home page."""
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state("networkidle")
    
    async def register_user(self) -> bool:
        """Register a new user through the UI."""
        try:
            # Look for registration form or link
            await self.page.click("text=Sign Up", timeout=5000)
            
            # Fill registration form
            await self.page.fill("input[name='email']", self.user_email)
            await self.page.fill("input[name='password']", self.user_password)
            await self.page.fill("input[name='name']", self.user_name)
            
            # Submit form
            await self.page.click("button[type='submit']")
            
            # Wait for success or redirect
            await self.page.wait_for_url("**/dashboard", timeout=10000)
            return True
            
        except Exception as e:
            print(f"Registration failed: {e}")
            return False
    
    async def login_user(self) -> bool:
        """Login user through the UI."""
        try:
            # Look for login form or link
            await self.page.click("text=Sign In", timeout=5000)
            
            # Fill login form
            await self.page.fill("input[name='email']", self.user_email)
            await self.page.fill("input[name='password']", self.user_password)
            
            # Submit form
            await self.page.click("button[type='submit']")
            
            # Wait for dashboard
            await self.page.wait_for_url("**/dashboard", timeout=10000)
            return True
            
        except Exception as e:
            print(f"Login failed: {e}")
            return False
    
    async def create_book_via_ui(self, book_title: str) -> bool:
        """Create a book through the UI."""
        try:
            # Navigate to book creation
            await self.page.click("text=Create Book", timeout=5000)
            
            # Fill book details
            await self.page.fill("input[name='title']", book_title)
            await self.page.fill("textarea[name='description']", fake.text(max_nb_chars=200))
            
            # Select genre
            await self.page.select_option("select[name='genre']", "fiction")
            
            # Submit
            await self.page.click("button[type='submit']")
            
            # Wait for book to be created
            await self.page.wait_for_selector(f"text={book_title}", timeout=10000)
            return True
            
        except Exception as e:
            print(f"Book creation failed: {e}")
            return False
    
    async def generate_content_via_ui(self) -> bool:
        """Generate content for a book through the UI."""
        try:
            # Click generate content button
            await self.page.click("text=Generate Content", timeout=5000)
            
            # Wait for generation to start
            await self.page.wait_for_selector("text=Generating", timeout=5000)
            
            # Wait for completion (with timeout)
            await self.page.wait_for_selector("text=Generation Complete", timeout=60000)
            return True
            
        except Exception as e:
            print(f"Content generation failed: {e}")
            return False
    
    async def check_analytics_page(self) -> bool:
        """Check if analytics page loads correctly."""
        try:
            # Navigate to analytics
            await self.page.click("text=Analytics", timeout=5000)
            
            # Wait for analytics data to load
            await self.page.wait_for_selector(".analytics-dashboard", timeout=10000)
            
            # Check if key metrics are displayed
            metrics = await self.page.query_selector_all(".metric-card")
            return len(metrics) > 0
            
        except Exception as e:
            print(f"Analytics check failed: {e}")
            return False


class BrowserTestSuite:
    """Browser automation test suite."""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.browser = None
        self.context = None
    
    async def setup_browser(self, headless: bool = True) -> Browser:
        """Setup browser for testing."""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright is not available. Install with: pip install playwright")
        
        playwright = await async_playwright().start()
        
        # Launch browser (Chromium by default)
        self.browser = await playwright.chromium.launch(headless=headless)
        
        # Create context with mobile viewport for responsive testing
        self.context = await self.browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (compatible; E2E-Test-Bot/1.0)"
        )
        
        return self.browser
    
    async def test_responsive_design(self) -> Dict[str, Any]:
        """Test responsive design across different viewport sizes."""
        results = {
            "test_name": "responsive_design",
            "viewports_tested": [],
            "success": False
        }
        
        viewports = [
            {"width": 1920, "height": 1080, "name": "desktop"},
            {"width": 768, "height": 1024, "name": "tablet"},
            {"width": 375, "height": 667, "name": "mobile"}
        ]
        
        try:
            for viewport in viewports:
                # Set viewport
                await self.context.set_viewport_size(viewport)
                
                page = await self.context.new_page()
                await page.goto(self.base_url)
                await page.wait_for_load_state("networkidle")
                
                # Check if key elements are visible
                key_elements = [
                    "nav", "header", ".main-content", "footer"
                ]
                
                visible_elements = 0
                for element in key_elements:
                    try:
                        if await page.is_visible(element, timeout=2000):
                            visible_elements += 1
                    except:
                        pass
                
                viewport_result = {
                    "name": viewport["name"],
                    "width": viewport["width"],
                    "height": viewport["height"],
                    "visible_elements": visible_elements,
                    "total_elements": len(key_elements),
                    "success": visible_elements >= len(key_elements) // 2
                }
                
                results["viewports_tested"].append(viewport_result)
                await page.close()
            
            # Test is successful if all viewports work
            results["success"] = all(vp["success"] for vp in results["viewports_tested"])
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
    
    async def test_complete_user_journey(self) -> Dict[str, Any]:
        """Test complete user journey through the application."""
        results = {
            "test_name": "complete_user_journey",
            "steps_completed": [],
            "success": False,
            "screenshots": []
        }
        
        try:
            page = await self.context.new_page()
            user = BrowserTestUser(page, self.base_url)
            
            # Step 1: Navigate to home
            await user.navigate_to_home()
            results["steps_completed"].append("navigation")
            
            # Take screenshot
            screenshot_path = f"test_screenshot_home_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path)
            results["screenshots"].append(screenshot_path)
            
            # Step 2: Register user
            if await user.register_user():
                results["steps_completed"].append("registration")
            
            # Step 3: Create book
            book_title = f"Browser Test Book {fake.random_int(min=100, max=999)}"
            if await user.create_book_via_ui(book_title):
                results["steps_completed"].append("book_creation")
            
            # Step 4: Check analytics
            if await user.check_analytics_page():
                results["steps_completed"].append("analytics")
            
            # Take final screenshot
            screenshot_path = f"test_screenshot_final_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path)
            results["screenshots"].append(screenshot_path)
            
            # Success if most steps completed
            results["success"] = len(results["steps_completed"]) >= 3
            
            await page.close()
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
    
    async def test_performance_metrics(self) -> Dict[str, Any]:
        """Test performance metrics of the web application."""
        results = {
            "test_name": "performance_metrics",
            "metrics": {},
            "success": False
        }
        
        try:
            page = await self.context.new_page()
            
            # Measure page load performance
            start_time = time.time()
            await page.goto(self.base_url)
            await page.wait_for_load_state("networkidle")
            load_time = time.time() - start_time
            
            # Get performance metrics
            metrics = await page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    return {
                        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
                    };
                }
            """)
            
            results["metrics"] = {
                "total_load_time": load_time,
                "dom_content_loaded": metrics.get("domContentLoaded", 0),
                "load_complete": metrics.get("loadComplete", 0),
                "first_paint": metrics.get("firstPaint", 0),
                "first_contentful_paint": metrics.get("firstContentfulPaint", 0)
            }
            
            # Performance thresholds
            results["success"] = (
                load_time < 5.0 and  # Total load time under 5 seconds
                results["metrics"]["first_contentful_paint"] < 2000  # FCP under 2 seconds
            )
            
            await page.close()
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
    
    async def test_accessibility(self) -> Dict[str, Any]:
        """Test basic accessibility features."""
        results = {
            "test_name": "accessibility",
            "checks": [],
            "success": False
        }
        
        try:
            page = await self.context.new_page()
            await page.goto(self.base_url)
            await page.wait_for_load_state("networkidle")
            
            # Check for alt attributes on images
            images = await page.query_selector_all("img")
            images_with_alt = 0
            
            for img in images:
                alt_text = await img.get_attribute("alt")
                if alt_text:
                    images_with_alt += 1
            
            alt_check = {
                "name": "image_alt_attributes",
                "total_images": len(images),
                "images_with_alt": images_with_alt,
                "success": len(images) == 0 or images_with_alt / len(images) >= 0.8
            }
            results["checks"].append(alt_check)
            
            # Check for form labels
            inputs = await page.query_selector_all("input[type='text'], input[type='email'], input[type='password'], textarea")
            labeled_inputs = 0
            
            for input_elem in inputs:
                # Check for associated label or aria-label
                input_id = await input_elem.get_attribute("id")
                aria_label = await input_elem.get_attribute("aria-label")
                
                if aria_label:
                    labeled_inputs += 1
                elif input_id:
                    label = await page.query_selector(f"label[for='{input_id}']")
                    if label:
                        labeled_inputs += 1
            
            label_check = {
                "name": "form_labels",
                "total_inputs": len(inputs),
                "labeled_inputs": labeled_inputs,
                "success": len(inputs) == 0 or labeled_inputs / len(inputs) >= 0.8
            }
            results["checks"].append(label_check)
            
            # Check for heading structure
            headings = await page.query_selector_all("h1, h2, h3, h4, h5, h6")
            has_h1 = len(await page.query_selector_all("h1")) > 0
            
            heading_check = {
                "name": "heading_structure",
                "total_headings": len(headings),
                "has_h1": has_h1,
                "success": has_h1 and len(headings) > 0
            }
            results["checks"].append(heading_check)
            
            # Overall success
            results["success"] = all(check["success"] for check in results["checks"])
            
            await page.close()
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
    
    async def cleanup(self):
        """Clean up browser resources."""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()


# Pytest integration
@pytest.mark.skipif(not PLAYWRIGHT_AVAILABLE, reason="Playwright not available")
@pytest.mark.asyncio
@pytest.mark.browser
async def test_responsive_design():
    """Test responsive design across different viewports."""
    suite = BrowserTestSuite()
    
    try:
        await suite.setup_browser(headless=True)
        result = await suite.test_responsive_design()
        
        assert result["success"], "Responsive design test failed"
        
        # Check that all viewports work
        for viewport in result["viewports_tested"]:
            assert viewport["success"], f"Viewport {viewport['name']} failed"
    
    finally:
        await suite.cleanup()


@pytest.mark.skipif(not PLAYWRIGHT_AVAILABLE, reason="Playwright not available")
@pytest.mark.asyncio
@pytest.mark.browser
async def test_user_journey():
    """Test complete user journey through the application."""
    suite = BrowserTestSuite()
    
    try:
        await suite.setup_browser(headless=True)
        result = await suite.test_complete_user_journey()
        
        # Should complete at least 2 major steps
        assert len(result["steps_completed"]) >= 2, "User journey too short"
        
        # Navigation should always work
        assert "navigation" in result["steps_completed"], "Navigation failed"
    
    finally:
        await suite.cleanup()


@pytest.mark.skipif(not PLAYWRIGHT_AVAILABLE, reason="Playwright not available")
@pytest.mark.asyncio
@pytest.mark.browser
async def test_performance():
    """Test web application performance."""
    suite = BrowserTestSuite()
    
    try:
        await suite.setup_browser(headless=True)
        result = await suite.test_performance_metrics()
        
        # Check performance thresholds
        assert result["metrics"]["total_load_time"] < 10.0, "Page load too slow"
        
        if result["metrics"]["first_contentful_paint"] > 0:
            assert result["metrics"]["first_contentful_paint"] < 5000, "FCP too slow"
    
    finally:
        await suite.cleanup()


@pytest.mark.skipif(not PLAYWRIGHT_AVAILABLE, reason="Playwright not available")
@pytest.mark.asyncio
@pytest.mark.browser
async def test_accessibility():
    """Test basic accessibility features."""
    suite = BrowserTestSuite()
    
    try:
        await suite.setup_browser(headless=True)
        result = await suite.test_accessibility()
        
        # Check individual accessibility features
        for check in result["checks"]:
            if check["name"] == "heading_structure":
                assert check["has_h1"], "Page should have at least one H1 heading"
    
    finally:
        await suite.cleanup()


if __name__ == "__main__":
    # Run browser tests manually
    async def main():
        if not PLAYWRIGHT_AVAILABLE:
            print("❌ Playwright not available. Install with: pip install playwright")
            print("   Then run: playwright install")
            return
        
        print("🌐 Starting Browser Automation Tests")
        
        suite = BrowserTestSuite()
        
        try:
            await suite.setup_browser(headless=False)  # Show browser for manual testing
            
            # Test 1: Responsive design
            print("\n📱 Testing responsive design...")
            result1 = await suite.test_responsive_design()
            print(f"✅ Responsive Design: {'PASSED' if result1['success'] else 'FAILED'}")
            
            for viewport in result1.get("viewports_tested", []):
                status = "✅" if viewport["success"] else "❌"
                print(f"   {status} {viewport['name']}: {viewport['visible_elements']}/{viewport['total_elements']} elements visible")
            
            # Test 2: Performance
            print("\n⚡ Testing performance...")
            result2 = await suite.test_performance_metrics()
            print(f"✅ Performance: {'PASSED' if result2['success'] else 'FAILED'}")
            
            if "metrics" in result2:
                metrics = result2["metrics"]
                print(f"   Load Time: {metrics['total_load_time']:.2f}s")
                print(f"   First Contentful Paint: {metrics['first_contentful_paint']:.0f}ms")
            
            # Test 3: Accessibility
            print("\n♿ Testing accessibility...")
            result3 = await suite.test_accessibility()
            print(f"✅ Accessibility: {'PASSED' if result3['success'] else 'FAILED'}")
            
            for check in result3.get("checks", []):
                status = "✅" if check["success"] else "❌"
                print(f"   {status} {check['name']}")
            
            # Test 4: User journey (optional - requires frontend)
            print("\n👤 Testing user journey...")
            try:
                result4 = await suite.test_complete_user_journey()
                print(f"✅ User Journey: {'PASSED' if result4['success'] else 'FAILED'}")
                print(f"   Steps: {', '.join(result4['steps_completed'])}")
                
                if "screenshots" in result4:
                    print(f"   Screenshots: {len(result4['screenshots'])} saved")
            
            except Exception as e:
                print(f"⚠️  User Journey: SKIPPED ({e})")
        
        finally:
            await suite.cleanup()
        
        print("\n🎉 Browser automation tests completed!")
    
    asyncio.run(main())