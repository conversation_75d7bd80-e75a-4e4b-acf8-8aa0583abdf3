INFO:     Will watch for changes in these directories: ['/Volumes/Baby_SSD/github/AI projects/publish-ai']
INFO:     <PERSON>vic<PERSON> running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [34534] using WatchFiles
INFO:     Started server process [34541]
INFO:     Waiting for application startup.
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 19:26:03,175 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 19:26:03,175 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 19:26:03,176 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 19:26:03,176 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 19:26:03,176 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 19:26:03,536 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 19:26:03,536 - root - INFO - Logflare initialized
2025-07-07 19:26:03,536 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 19:26:03,536 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 19:26:03,548 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 19:26:04,399 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 19:26:04,650 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:04,787 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:04,858 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:04,934 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,048 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,179 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,293 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,293 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 19:26:05,293 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 19:26:05,297 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 19:26:05,298 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 19:26:05,299 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 19:26:05,299 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 19:26:05,312 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 19:26:05,312 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 19:26:05,312 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 19:26:05,316 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 19:26:05,316 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 19:26:05,316 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 19:26:05,452 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,721 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,853 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:05,926 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:06,046 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:06,122 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:06,354 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:06,483 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 19:26:06,484 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 19:26:06,484 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 19:26:06,488 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 19:26:06,488 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 19:26:06,488 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 19:26:06,488 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 19:26:06,500 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 19:26:06,500 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 19:26:06,501 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 19:26:07,195 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 19:26:07,195 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 19:26:07,392 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-06-30T23%3A26%3A06.488610 "HTTP/2 200 OK"
2025-07-07 19:26:14,652 - app.middleware.monitoring_middleware - INFO - Request started - ID: b8b5ac23-e81d-4f67-b5c4-6a38b07efd22, Method: GET, Path: /health, User: None
2025-07-07 19:26:14,658 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "security_blocked_user_agent", "client_ip": "127.0.0.1", "user_agent": "curl/8.7.1", "url": "http://localhost:8000/health", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:26:14.658715Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "b8b5ac23-e81d-4f67-b5c4-6a38b07efd22"}
2025-07-07 19:26:14,659 - app.middleware.security_middleware - WARNING - Security event: blocked_user_agent
2025-07-07 19:26:14,661 - app.middleware.monitoring_middleware - ERROR - Request failed with performance data - Method: GET, Path: /health, Duration: 7.15ms, Error: 403: Access denied
2025-07-07 19:26:14,661 - app.middleware.monitoring_middleware - ERROR - Request failed - ID: b8b5ac23-e81d-4f67-b5c4-6a38b07efd22, Error: 403: Access denied, Type: HTTPException, Duration: 9.71ms
2025-07-07 19:26:14,662 - app.monitoring.monitoring_setup - ERROR - {"event_type": "exception", "error": "403: Access denied", "error_type": "HTTPException", "request_id": "b8b5ac23-e81d-4f67-b5c4-6a38b07efd22", "method": "GET", "path": "/health", "user_id": null, "duration_ms": 9.71, "event": "Exception captured", "logger": "app.monitoring.monitoring_setup", "level": "error", "timestamp": "2025-07-07T23:26:14.661988Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50050 - "GET /health HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 177, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 68, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    |     raise app_exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 140, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    |     raise app_exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 249, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    |     raise app_exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/cache_middleware.py", line 99, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    |     raise app_exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 275, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    |     raise app_exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 77, in dispatch
    |     await self._perform_security_checks(request, client_ip)
    |   File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 144, in _perform_security_checks
    |     raise HTTPException(status_code=403, detail="Access denied")
    | fastapi.exceptions.HTTPException: 403: Access denied
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 68, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 140, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/monitoring_middleware.py", line 249, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/cache_middleware.py", line 99, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 275, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 77, in dispatch
    await self._perform_security_checks(request, client_ip)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/middleware/security_middleware.py", line 144, in _perform_security_checks
    raise HTTPException(status_code=403, detail="Access denied")
fastapi.exceptions.HTTPException: 403: Access denied
2025-07-07 19:26:14,940 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:26:14,940 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:26:14,941 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:26:14,942 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:26:14,943 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:26:14,943 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: exception
2025-07-07 19:26:36,480 - app.middleware.monitoring_middleware - INFO - Request started - ID: 99c32da2-33e5-4355-9125-6384bdbe223a, Method: GET, Path: /api/books, User: None
2025-07-07 19:26:36,486 - app.api.supabase_books - INFO - Getting books with status=None, limit=50
2025-07-07 19:26:36,490 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 99c32da2-33e5-4355-9125-6384bdbe223a, Status: 200, Duration: 9.63ms
2025-07-07 19:26:36,490 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "99c32da2-33e5-4355-9125-6384bdbe223a", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 9.63, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:26:36.490411Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50153 - "GET /api/books HTTP/1.1" 200 OK
2025-07-07 19:26:36,749 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:26:36,749 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:26:36,749 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:28:06,111 - app.middleware.monitoring_middleware - INFO - Request started - ID: 13aa4bba-284f-4f3b-b9ee-d5bf314d5d39, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:28:06,433 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:28:06,572 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:28:06,574 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:28:06,576 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 13aa4bba-284f-4f3b-b9ee-d5bf314d5d39, Status: 200, Duration: 465.42ms
2025-07-07 19:28:06,576 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "13aa4bba-284f-4f3b-b9ee-d5bf314d5d39", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 465.42, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:06.576772Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50589 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:50596 - "OPTIONS /api/analytics/dashboard HTTP/1.1" 200 OK
2025-07-07 19:28:06,593 - app.middleware.monitoring_middleware - INFO - Request started - ID: 1017aa86-9c85-4af2-a216-f9c4cfacc382, Method: GET, Path: /api/analytics/dashboard, User: None
2025-07-07 19:28:06,594 - app.middleware.monitoring_middleware - INFO - Request started - ID: eefc69d2-2832-4d76-b012-1b86b2a72c2e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:28:06,714 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:28:06,771 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:28:06,774 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:28:06,932 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:28:07,010 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:28:07,013 - app.monitoring.monitoring_setup - INFO - {"operation_id": "e32c26db-f6d4-4d55-840a-aeeab7168083", "operation_name": "get_dashboard_analytics", "user_id": "fe99519c-29cd-4817-8ca4-d94b58ed884a", "event": "Operation started", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:07.012942Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "eefc69d2-2832-4d76-b012-1b86b2a72c2e"}
2025-07-07 19:28:07,068 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:28:07,127 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&status=eq.published "HTTP/2 200 OK"
2025-07-07 19:28:07,187 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:28:07,323 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-07-01T00%3A00%3A00&created_at=lt.2025-07-07T19%3A28%3A07.187722 "HTTP/2 200 OK"
2025-07-07 19:28:07,452 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-01T00%3A00%3A00&created_at=lt.2025-07-01T00%3A00%3A00 "HTTP/2 200 OK"
2025-07-07 19:28:07,515 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=metric_type%2Cmetric_value%2Capproved%2Ccreated_at&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-30T19%3A28%3A07.454866&order=created_at.asc "HTTP/2 200 OK"
2025-07-07 19:28:07,515 - app.api.analytics - INFO - Dashboard analytics retrieved for user fe99519c-29cd-4817-8ca4-d94b58ed884a
2025-07-07 19:28:07,515 - app.monitoring.monitoring_setup - INFO - {"operation_id": "e32c26db-f6d4-4d55-840a-aeeab7168083", "operation_name": "get_dashboard_analytics", "event": "Operation completed", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:07.515740Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "eefc69d2-2832-4d76-b012-1b86b2a72c2e"}
2025-07-07 19:28:07,517 - app.middleware.monitoring_middleware - INFO - Request completed - ID: eefc69d2-2832-4d76-b012-1b86b2a72c2e, Status: 200, Duration: 922.84ms
2025-07-07 19:28:07,518 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "eefc69d2-2832-4d76-b012-1b86b2a72c2e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 922.84, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:07.517929Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50589 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:28:07,519 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 1017aa86-9c85-4af2-a216-f9c4cfacc382, Status: 200, Duration: 925.85ms
2025-07-07 19:28:07,519 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "1017aa86-9c85-4af2-a216-f9c4cfacc382", "method": "GET", "path": "/api/analytics/dashboard", "status_code": 200, "duration_ms": 925.85, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:07.519271Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50596 - "GET /api/analytics/dashboard HTTP/1.1" 200 OK
INFO:     127.0.0.1:50596 - "OPTIONS /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 19:28:07,526 - app.middleware.monitoring_middleware - INFO - Request started - ID: b42a0757-1908-47d0-8d82-dfb05666a9bb, Method: GET, Path: /api/books, User: None
2025-07-07 19:28:07,527 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 19:28:07,528 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b42a0757-1908-47d0-8d82-dfb05666a9bb, Status: 200, Duration: 1.67ms
2025-07-07 19:28:07,528 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b42a0757-1908-47d0-8d82-dfb05666a9bb", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 1.67, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:28:07.528420Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:50596 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 19:28:07,698 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:28:07,698 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:28:07,698 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:28:07,722 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:28:07,724 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:28:07,724 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:30:35,847 - app.middleware.monitoring_middleware - INFO - Request started - ID: 653d3e3f-597e-43a9-aba4-76380806b256, Method: GET, Path: /api/agents, User: None
2025-07-07 19:30:35,852 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 653d3e3f-597e-43a9-aba4-76380806b256, Status: 404, Duration: 5.41ms
2025-07-07 19:30:35,852 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "653d3e3f-597e-43a9-aba4-76380806b256", "method": "GET", "path": "/api/agents", "status_code": 404, "duration_ms": 5.41, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:30:35.852717Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51253 - "GET /api/agents HTTP/1.1" 404 Not Found
2025-07-07 19:30:36,126 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:30:36,127 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:30:36,127 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:30:47,579 - app.middleware.monitoring_middleware - INFO - Request started - ID: 48d17f6f-4c94-4048-8945-df45016e5b83, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:30:47,884 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:30:48,109 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:30:48,110 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:30:48,111 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 48d17f6f-4c94-4048-8945-df45016e5b83, Status: 200, Duration: 531.85ms
2025-07-07 19:30:48,111 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "48d17f6f-4c94-4048-8945-df45016e5b83", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 531.85, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:30:48.111905Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51319 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:51328 - "OPTIONS /api/books HTTP/1.1" 200 OK
2025-07-07 19:30:48,124 - app.middleware.monitoring_middleware - INFO - Request started - ID: 39fab8d7-88ae-414c-b1a5-e9a1871da36e, Method: GET, Path: /api/books, User: None
2025-07-07 19:30:48,125 - app.api.supabase_books - INFO - Getting books with status=None, limit=50
2025-07-07 19:30:48,126 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 39fab8d7-88ae-414c-b1a5-e9a1871da36e, Status: 200, Duration: 1.37ms
2025-07-07 19:30:48,126 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "39fab8d7-88ae-414c-b1a5-e9a1871da36e", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 1.37, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:30:48.126327Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51328 - "GET /api/books HTTP/1.1" 200 OK
2025-07-07 19:30:48,310 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:30:48,311 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:30:48,311 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:31:06,526 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:31:25,042 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5e508b8f-6cba-45cc-95c1-6ee705148b28, Method: GET, Path: /docs, User: None
2025-07-07 19:31:25,046 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5e508b8f-6cba-45cc-95c1-6ee705148b28, Status: 200, Duration: 3.61ms
2025-07-07 19:31:25,046 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5e508b8f-6cba-45cc-95c1-6ee705148b28", "method": "GET", "path": "/docs", "status_code": 200, "duration_ms": 3.61, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:31:25.046418Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51500 - "GET /docs HTTP/1.1" 200 OK
2025-07-07 19:31:25,330 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:31:25,330 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:31:25,331 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:31:49,253 - app.middleware.monitoring_middleware - INFO - Request started - ID: f0077396-d423-40f3-b641-abbc211488e1, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:31:49,415 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:31:49,620 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:31:49,623 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:31:49,624 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f0077396-d423-40f3-b641-abbc211488e1, Status: 200, Duration: 370.61ms
2025-07-07 19:31:49,624 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f0077396-d423-40f3-b641-abbc211488e1", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 370.61, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:31:49.624197Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51669 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:32:04,175 - app.middleware.monitoring_middleware - INFO - Request started - ID: f514e490-7379-47c3-b23e-f39c204ae21a, Method: GET, Path: /api/agents/status, User: None
2025-07-07 19:32:04,177 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f514e490-7379-47c3-b23e-f39c204ae21a, Status: 404, Duration: 2.75ms
2025-07-07 19:32:04,178 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f514e490-7379-47c3-b23e-f39c204ae21a", "method": "GET", "path": "/api/agents/status", "status_code": 404, "duration_ms": 2.75, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:04.178143Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51734 - "GET /api/agents/status HTTP/1.1" 404 Not Found
2025-07-07 19:32:15,507 - app.middleware.monitoring_middleware - INFO - Request started - ID: 61843a5f-0cee-467d-a9d5-f008babffd91, Method: POST, Path: /api/auth/login, User: None
2025-07-07 19:32:15,866 - httpx - INFO - HTTP Request: POST https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/token?grant_type=password "HTTP/2 200 OK"
2025-07-07 19:32:16,070 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:16,154 - httpx - INFO - HTTP Request: PATCH https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:16,155 - app.auth.supabase_auth - INFO - User logged in successfully: <EMAIL>
2025-07-07 19:32:16,155 - app.api.supabase_auth - INFO - User login successful: <EMAIL>
2025-07-07 19:32:16,157 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 61843a5f-0cee-467d-a9d5-f008babffd91, Status: 200, Duration: 649.43ms
2025-07-07 19:32:16,157 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "61843a5f-0cee-467d-a9d5-f008babffd91", "method": "POST", "path": "/api/auth/login", "status_code": 200, "duration_ms": 649.43, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:16.157175Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51807 - "POST /api/auth/login HTTP/1.1" 200 OK
2025-07-07 19:32:16,184 - app.middleware.monitoring_middleware - INFO - Request started - ID: bf837385-74b4-4b46-9657-3904670cb6a9, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:32:16,325 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:32:16,380 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:16,393 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:32:16,397 - app.middleware.monitoring_middleware - INFO - Request completed - ID: bf837385-74b4-4b46-9657-3904670cb6a9, Status: 200, Duration: 212.77ms
2025-07-07 19:32:16,397 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "bf837385-74b4-4b46-9657-3904670cb6a9", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 212.77, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:16.397218Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51813 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:32:16,638 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:32:16,639 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:32:16,639 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:32:20,004 - app.middleware.monitoring_middleware - INFO - Request started - ID: dd61c263-5e86-4393-968e-6b451d9b4d79, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:32:20,163 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:32:20,237 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:20,238 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:32:20,239 - app.middleware.monitoring_middleware - INFO - Request completed - ID: dd61c263-5e86-4393-968e-6b451d9b4d79, Status: 200, Duration: 234.91ms
2025-07-07 19:32:20,241 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "dd61c263-5e86-4393-968e-6b451d9b4d79", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 234.91, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:20.241053Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51813 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:32:20,271 - app.middleware.monitoring_middleware - INFO - Request started - ID: 99b86d78-bfba-49b3-8946-b4571893f33e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:32:20,418 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:32:20,485 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:20,487 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:32:20,487 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:32:20,488 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:32:20,488 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:32:20,488 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 99b86d78-bfba-49b3-8946-b4571893f33e, Status: 200, Duration: 217.06ms
2025-07-07 19:32:20,489 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "99b86d78-bfba-49b3-8946-b4571893f33e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 217.06, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:20.488954Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51813 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:32:20,499 - app.middleware.monitoring_middleware - INFO - Request started - ID: 1f959836-24bd-4fdd-9657-3c081a6f3775, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:32:20,577 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:32:20,693 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:32:20,694 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:32:20,694 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:32:20,695 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:32:20,695 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:32:20,696 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 1f959836-24bd-4fdd-9657-3c081a6f3775, Status: 200, Duration: 196.24ms
2025-07-07 19:32:20,696 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "1f959836-24bd-4fdd-9657-3c081a6f3775", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 196.24, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:20.696175Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51813 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:32:20,703 - app.middleware.monitoring_middleware - INFO - Request started - ID: 3c450252-1977-42f8-a2e0-63c4ffc27883, Method: GET, Path: /api/books, User: None
2025-07-07 19:32:20,704 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 19:32:20,705 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 3c450252-1977-42f8-a2e0-63c4ffc27883, Status: 200, Duration: 1.96ms
2025-07-07 19:32:20,705 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "3c450252-1977-42f8-a2e0-63c4ffc27883", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 1.96, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:20.705856Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51844 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 19:32:20,836 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:32:20,837 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:32:20,837 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:32:56,399 - app.middleware.monitoring_middleware - INFO - Request started - ID: f2aaca16-e559-4ec1-b282-c45162f65c91, Method: GET, Path: /api/agents/tasks, User: None
2025-07-07 19:32:56,643 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 19:32:56,644 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token is malformed: token contains an invalid number of segments
2025-07-07 19:32:56,644 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 19:32:56,644 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/agents/tasks, IP: 127.0.0.1
2025-07-07 19:32:56,644 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/agents/tasks", "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:56.644800Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "f2aaca16-e559-4ec1-b282-c45162f65c91"}
2025-07-07 19:32:56,645 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f2aaca16-e559-4ec1-b282-c45162f65c91, Status: 401, Duration: 246.1ms
2025-07-07 19:32:56,645 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f2aaca16-e559-4ec1-b282-c45162f65c91", "method": "GET", "path": "/api/agents/tasks", "status_code": 401, "duration_ms": 246.1, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:32:56.645158Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:52056 - "GET /api/agents/tasks HTTP/1.1" 401 Unauthorized
2025-07-07 19:33:05,185 - app.middleware.monitoring_middleware - INFO - Request started - ID: 6c289377-f78b-4d29-a50e-6d4e34e6ee45, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:33:05,378 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:33:05,510 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:33:05,512 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:33:05,513 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 6c289377-f78b-4d29-a50e-6d4e34e6ee45, Status: 200, Duration: 327.98ms
2025-07-07 19:33:05,513 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "6c289377-f78b-4d29-a50e-6d4e34e6ee45", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 327.98, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:33:05.513576Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:52110 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:36:06,534 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:41:06,543 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:44:12,049 - app.middleware.monitoring_middleware - INFO - Request started - ID: cb39c196-0660-4cca-a609-672a051730fa, Method: GET, Path: /openapi.json, User: None
2025-07-07 19:44:12,223 - app.middleware.monitoring_middleware - INFO - Request completed - ID: cb39c196-0660-4cca-a609-672a051730fa, Status: 200, Duration: 175.0ms
2025-07-07 19:44:12,224 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "cb39c196-0660-4cca-a609-672a051730fa", "method": "GET", "path": "/openapi.json", "status_code": 200, "duration_ms": 175.0, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:44:12.223896Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:55196 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-07 19:46:06,553 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:51:06,566 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:56:06,594 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 19:56:52,958 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4e18c748-a62d-4412-87ec-4cb646b1dd2d, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:56:53,362 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:56:53,636 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:56:53,662 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:56:53,669 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4e18c748-a62d-4412-87ec-4cb646b1dd2d, Status: 200, Duration: 712.44ms
2025-07-07 19:56:53,673 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4e18c748-a62d-4412-87ec-4cb646b1dd2d", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 712.44, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:56:53.672402Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:58971 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:57:05,149 - app.middleware.monitoring_middleware - INFO - Request started - ID: 36d1ced9-2f96-480a-91cc-00fedc228155, Method: POST, Path: /api/auth/login, User: None
2025-07-07 19:57:05,513 - httpx - INFO - HTTP Request: POST https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/token?grant_type=password "HTTP/2 200 OK"
2025-07-07 19:57:05,712 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:05,906 - httpx - INFO - HTTP Request: PATCH https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:05,907 - app.auth.supabase_auth - INFO - User logged in successfully: <EMAIL>
2025-07-07 19:57:05,907 - app.api.supabase_auth - INFO - User login successful: <EMAIL>
2025-07-07 19:57:05,908 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 36d1ced9-2f96-480a-91cc-00fedc228155, Status: 200, Duration: 758.37ms
2025-07-07 19:57:05,908 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "36d1ced9-2f96-480a-91cc-00fedc228155", "method": "POST", "path": "/api/auth/login", "status_code": 200, "duration_ms": 758.37, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:05.908199Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59029 - "POST /api/auth/login HTTP/1.1" 200 OK
2025-07-07 19:57:05,933 - app.middleware.monitoring_middleware - INFO - Request started - ID: f97f4a3f-0d35-4a09-83b4-b4a6d34add6c, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:57:06,063 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:57:06,183 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:06,184 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:57:06,185 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f97f4a3f-0d35-4a09-83b4-b4a6d34add6c, Status: 200, Duration: 251.96ms
2025-07-07 19:57:06,185 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f97f4a3f-0d35-4a09-83b4-b4a6d34add6c", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 251.96, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:06.185217Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59037 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:57:09,593 - app.middleware.monitoring_middleware - INFO - Request started - ID: 8c099a51-6858-4e61-b2b5-a189d5c1d152, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:57:09,718 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:57:09,847 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:09,852 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:57:09,854 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 8c099a51-6858-4e61-b2b5-a189d5c1d152, Status: 200, Duration: 260.65ms
2025-07-07 19:57:09,854 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "8c099a51-6858-4e61-b2b5-a189d5c1d152", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 260.65, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:09.854736Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59037 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:57:09,906 - app.middleware.monitoring_middleware - INFO - Request started - ID: ace43b16-e0e0-47ee-9eb7-41a710aaf1c4, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:57:10,101 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:57:10,233 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:10,233 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 19:57:10,235 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ace43b16-e0e0-47ee-9eb7-41a710aaf1c4, Status: 200, Duration: 329.04ms
2025-07-07 19:57:10,236 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ace43b16-e0e0-47ee-9eb7-41a710aaf1c4", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 329.04, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:10.236048Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59037 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:57:10,257 - app.middleware.monitoring_middleware - INFO - Request started - ID: 984b4230-e8db-4e5e-8273-1954d8ca6282, Method: GET, Path: /api/auth/me, User: None
2025-07-07 19:57:10,402 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:57:10,551 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:10,554 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
INFO:     127.0.0.1:59070 - "OPTIONS /api/analytics/dashboard HTTP/1.1" 200 OK
2025-07-07 19:57:10,560 - app.middleware.monitoring_middleware - INFO - Request started - ID: 89cdbe82-6ba0-4ce9-b850-b5ed5018fad2, Method: GET, Path: /api/analytics/dashboard, User: None
2025-07-07 19:57:10,566 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 984b4230-e8db-4e5e-8273-1954d8ca6282, Status: 200, Duration: 308.4ms
2025-07-07 19:57:10,566 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "89cdbe82-6ba0-4ce9-b850-b5ed5018fad2", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 308.4, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:10.566321Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59037 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 19:57:10,759 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 19:57:10,940 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:10,944 - app.monitoring.monitoring_setup - INFO - {"operation_id": "84c53060-7984-4539-9bf6-c47e0347ef9e", "operation_name": "get_dashboard_analytics", "user_id": "fe99519c-29cd-4817-8ca4-d94b58ed884a", "event": "Operation started", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:10.944259Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
2025-07-07 19:57:11,148 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:11,298 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&status=eq.published "HTTP/2 200 OK"
2025-07-07 19:57:11,374 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 19:57:11,503 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-07-01T00%3A00%3A00&created_at=lt.2025-07-07T19%3A57%3A11.375072 "HTTP/2 200 OK"
2025-07-07 19:57:11,625 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-01T00%3A00%3A00&created_at=lt.2025-07-01T00%3A00%3A00 "HTTP/2 200 OK"
2025-07-07 19:57:11,805 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=metric_type%2Cmetric_value%2Capproved%2Ccreated_at&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-30T19%3A57%3A11.625787&order=created_at.asc "HTTP/2 200 OK"
2025-07-07 19:57:11,806 - app.api.analytics - INFO - Dashboard analytics retrieved for user fe99519c-29cd-4817-8ca4-d94b58ed884a
2025-07-07 19:57:11,806 - app.monitoring.monitoring_setup - INFO - {"operation_id": "84c53060-7984-4539-9bf6-c47e0347ef9e", "operation_name": "get_dashboard_analytics", "event": "Operation completed", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:11.806835Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59073 - "OPTIONS /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 19:57:11,814 - app.middleware.monitoring_middleware - INFO - Request started - ID: 53f6f792-00da-4c4c-a96b-d0917fe142cd, Method: GET, Path: /api/books, User: None
2025-07-07 19:57:11,818 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 19:57:11,819 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 89cdbe82-6ba0-4ce9-b850-b5ed5018fad2, Status: 200, Duration: 1258.86ms
2025-07-07 19:57:11,819 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "53f6f792-00da-4c4c-a96b-d0917fe142cd", "method": "GET", "path": "/api/analytics/dashboard", "status_code": 200, "duration_ms": 1258.86, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:11.819180Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59070 - "GET /api/analytics/dashboard HTTP/1.1" 200 OK
2025-07-07 19:57:11,820 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 53f6f792-00da-4c4c-a96b-d0917fe142cd, Status: 200, Duration: 5.17ms
2025-07-07 19:57:11,820 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "53f6f792-00da-4c4c-a96b-d0917fe142cd", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 5.17, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-07T23:57:11.820278Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59073 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 19:57:11,949 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:57:11,950 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:57:11,950 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 19:57:12,011 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 19:57:12,011 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 19:57:12,011 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:00:01,805 - app.middleware.monitoring_middleware - INFO - Request started - ID: 67eca05c-803e-4132-bf15-45b45537016f, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:02,201 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:02,415 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:02,418 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:02,419 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 67eca05c-803e-4132-bf15-45b45537016f, Status: 200, Duration: 613.76ms
2025-07-07 20:00:02,419 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "67eca05c-803e-4132-bf15-45b45537016f", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 613.76, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:02.419586Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59857 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:05,252 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7b7984a2-d899-4cd2-868c-52c5154add46, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:05,345 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:05,470 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:05,472 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:05,473 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7b7984a2-d899-4cd2-868c-52c5154add46, Status: 200, Duration: 220.53ms
2025-07-07 20:00:05,473 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7b7984a2-d899-4cd2-868c-52c5154add46", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 220.53, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:05.473400Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59857 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:10,748 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4a25bb95-9de4-44be-80ca-2f1739cfb177, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:10,899 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:11,013 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:11,016 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:11,017 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4a25bb95-9de4-44be-80ca-2f1739cfb177, Status: 200, Duration: 269.31ms
2025-07-07 20:00:11,017 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4a25bb95-9de4-44be-80ca-2f1739cfb177", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 269.31, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:11.017698Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59903 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:11,278 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:00:11,278 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:00:11,279 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:00:13,941 - app.middleware.monitoring_middleware - INFO - Request started - ID: 51d847a8-b295-4359-bd10-3aa01c388a05, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:14,099 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:14,167 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:14,167 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:14,168 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 51d847a8-b295-4359-bd10-3aa01c388a05, Status: 200, Duration: 227.47ms
2025-07-07 20:00:14,168 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "51d847a8-b295-4359-bd10-3aa01c388a05", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 227.47, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:14.168636Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59903 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:14,300 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:00:14,300 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:00:14,300 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:00:23,153 - app.middleware.monitoring_middleware - INFO - Request started - ID: a30c1e91-df3c-4731-89a3-9e7845e31a15, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:23,349 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:23,553 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:23,557 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:23,558 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a30c1e91-df3c-4731-89a3-9e7845e31a15, Status: 200, Duration: 404.71ms
2025-07-07 20:00:23,558 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a30c1e91-df3c-4731-89a3-9e7845e31a15", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 404.71, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:23.558274Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59967 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:26,077 - app.middleware.monitoring_middleware - INFO - Request started - ID: e4e42585-2a57-4656-a09d-da7c151821fa, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:00:26,205 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:00:26,354 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:00:26,354 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:00:26,355 - app.middleware.monitoring_middleware - INFO - Request completed - ID: e4e42585-2a57-4656-a09d-da7c151821fa, Status: 200, Duration: 277.99ms
2025-07-07 20:00:26,355 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "e4e42585-2a57-4656-a09d-da7c151821fa", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 277.99, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:00:26.355677Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:59967 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:00:26,557 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:00:26,558 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:00:26,559 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:01:06,627 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:05:18,461 - app.middleware.monitoring_middleware - INFO - Request started - ID: 781cf0c1-8796-4168-aac3-450b3228e217, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:18,951 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:19,129 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:19,148 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:19,151 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 781cf0c1-8796-4168-aac3-450b3228e217, Status: 200, Duration: 690.54ms
2025-07-07 20:05:19,152 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "781cf0c1-8796-4168-aac3-450b3228e217", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 690.54, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:19.152315Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61612 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:19,175 - app.middleware.monitoring_middleware - INFO - Request started - ID: f5336610-e92c-4b04-a96b-16f1e885a136, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:19,237 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:19,408 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:19,409 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:19,411 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f5336610-e92c-4b04-a96b-16f1e885a136, Status: 200, Duration: 236.1ms
2025-07-07 20:05:19,411 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f5336610-e92c-4b04-a96b-16f1e885a136", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 236.1, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:19.411897Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61612 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:61620 - "OPTIONS /api/users/preferences HTTP/1.1" 200 OK
2025-07-07 20:05:19,431 - app.middleware.monitoring_middleware - INFO - Request started - ID: 154169f6-e128-424e-8c01-609d5248418d, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:19,445 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 154169f6-e128-424e-8c01-609d5248418d, Status: 404, Duration: 14.87ms
2025-07-07 20:05:19,446 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "154169f6-e128-424e-8c01-609d5248418d", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 14.87, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:19.446003Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61620 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:05:19,800 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:19,801 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:19,801 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:20,467 - app.middleware.monitoring_middleware - INFO - Request started - ID: a541afc6-b054-4d9a-8e61-67d5a55a06ee, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:20,606 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:20,719 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:20,722 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:20,723 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a541afc6-b054-4d9a-8e61-67d5a55a06ee, Status: 200, Duration: 256.43ms
2025-07-07 20:05:20,723 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a541afc6-b054-4d9a-8e61-67d5a55a06ee", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 256.43, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:20.723823Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61612 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:20,731 - app.middleware.monitoring_middleware - INFO - Request started - ID: ba20d057-a4da-4ae5-b132-4d523b7afd28, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:20,733 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ba20d057-a4da-4ae5-b132-4d523b7afd28, Status: 404, Duration: 1.98ms
2025-07-07 20:05:20,733 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ba20d057-a4da-4ae5-b132-4d523b7afd28", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.98, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:20.733506Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61620 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:05:20,867 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:20,867 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:20,867 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:22,750 - app.middleware.monitoring_middleware - INFO - Request started - ID: 399fd2f9-88a3-4de1-bc2a-7123c29a164c, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:22,823 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:23,097 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:23,100 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:23,101 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 399fd2f9-88a3-4de1-bc2a-7123c29a164c, Status: 200, Duration: 350.52ms
2025-07-07 20:05:23,101 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "399fd2f9-88a3-4de1-bc2a-7123c29a164c", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 350.52, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:23.101283Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61612 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:23,107 - app.middleware.monitoring_middleware - INFO - Request started - ID: 8dee380d-16a9-4613-978b-48a91a43dd25, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:23,109 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 8dee380d-16a9-4613-978b-48a91a43dd25, Status: 404, Duration: 1.53ms
2025-07-07 20:05:23,109 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "8dee380d-16a9-4613-978b-48a91a43dd25", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.53, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:23.109480Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61620 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:05:23,243 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:23,244 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:23,244 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:23,318 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:23,321 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:23,322 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:38,340 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7f9a6b5e-d3ea-4a3b-9a4f-e9877b0ad7fa, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:38,537 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:38,808 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:38,810 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:38,811 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7f9a6b5e-d3ea-4a3b-9a4f-e9877b0ad7fa, Status: 200, Duration: 470.98ms
2025-07-07 20:05:38,811 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7f9a6b5e-d3ea-4a3b-9a4f-e9877b0ad7fa", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 470.98, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:38.811430Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61711 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:38,826 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5dda9659-b94d-4e1e-ba76-dba2146c4d73, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:38,948 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:39,086 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:39,087 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:39,090 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5dda9659-b94d-4e1e-ba76-dba2146c4d73, Status: 200, Duration: 263.72ms
2025-07-07 20:05:39,090 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5dda9659-b94d-4e1e-ba76-dba2146c4d73", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 263.72, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:39.090663Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61711 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:39,098 - app.middleware.monitoring_middleware - INFO - Request started - ID: 157366c8-fc2b-43ae-9e27-1a647a601153, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:39,100 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 157366c8-fc2b-43ae-9e27-1a647a601153, Status: 404, Duration: 1.95ms
2025-07-07 20:05:39,101 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "157366c8-fc2b-43ae-9e27-1a647a601153", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.95, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:39.101024Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61721 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:05:39,336 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:39,337 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:39,337 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:40,273 - app.middleware.monitoring_middleware - INFO - Request started - ID: f50f12cb-d1aa-42ea-8b01-14fd96a8e4e4, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:40,451 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:40,601 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:40,604 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:40,604 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f50f12cb-d1aa-42ea-8b01-14fd96a8e4e4, Status: 200, Duration: 330.96ms
2025-07-07 20:05:40,604 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f50f12cb-d1aa-42ea-8b01-14fd96a8e4e4", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 330.96, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:40.604892Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61711 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:40,611 - app.middleware.monitoring_middleware - INFO - Request started - ID: b471954c-f433-4bfd-bfb9-3170625de75d, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:40,613 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b471954c-f433-4bfd-bfb9-3170625de75d, Status: 404, Duration: 1.32ms
2025-07-07 20:05:40,613 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b471954c-f433-4bfd-bfb9-3170625de75d", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.32, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:40.613416Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61721 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:05:40,765 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:40,766 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:40,766 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:40,897 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:05:40,897 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:05:40,897 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:05:42,629 - app.middleware.monitoring_middleware - INFO - Request started - ID: f0b3ab6a-2a09-4322-9d5c-c33ae22bc5a4, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:05:42,696 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:05:42,864 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:05:42,865 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:05:42,865 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f0b3ab6a-2a09-4322-9d5c-c33ae22bc5a4, Status: 200, Duration: 236.6ms
2025-07-07 20:05:42,866 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f0b3ab6a-2a09-4322-9d5c-c33ae22bc5a4", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 236.6, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:42.866013Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61711 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:05:42,872 - app.middleware.monitoring_middleware - INFO - Request started - ID: 278948fc-ef35-4651-8f0f-ef5b399e8ed9, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:05:42,873 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 278948fc-ef35-4651-8f0f-ef5b399e8ed9, Status: 404, Duration: 1.28ms
2025-07-07 20:05:42,874 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "278948fc-ef35-4651-8f0f-ef5b399e8ed9", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.28, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:05:42.874040Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61721 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:06:06,637 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:06:21,155 - app.middleware.monitoring_middleware - INFO - Request started - ID: af6ab70f-958c-41d4-a482-c8a6dec0d259, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:06:21,411 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:06:21,552 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:06:21,553 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:06:21,554 - app.middleware.monitoring_middleware - INFO - Request completed - ID: af6ab70f-958c-41d4-a482-c8a6dec0d259, Status: 200, Duration: 398.81ms
2025-07-07 20:06:21,554 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "af6ab70f-958c-41d4-a482-c8a6dec0d259", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 398.81, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:21.554596Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61926 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:06:21,571 - app.middleware.monitoring_middleware - INFO - Request started - ID: 91c41ec6-a04f-4a6d-8652-2647784692c4, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:06:21,699 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:06:21,793 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:06:21,793 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:06:21,795 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 91c41ec6-a04f-4a6d-8652-2647784692c4, Status: 200, Duration: 224.44ms
2025-07-07 20:06:21,796 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "91c41ec6-a04f-4a6d-8652-2647784692c4", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 224.44, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:21.796097Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61926 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:06:21,807 - app.middleware.monitoring_middleware - INFO - Request started - ID: 0568112c-9305-4a99-9060-ee154c360f4f, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:06:21,809 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 0568112c-9305-4a99-9060-ee154c360f4f, Status: 404, Duration: 2.17ms
2025-07-07 20:06:21,809 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "0568112c-9305-4a99-9060-ee154c360f4f", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.17, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:21.809481Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61937 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:06:21,957 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:06:21,958 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:06:21,958 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:06:21,999 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:06:22,000 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:06:22,000 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:06:22,828 - app.middleware.monitoring_middleware - INFO - Request started - ID: 709ea551-235b-4d84-bc55-75e2c2751d9e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:06:22,971 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:06:23,049 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:06:23,053 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:06:23,054 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 709ea551-235b-4d84-bc55-75e2c2751d9e, Status: 200, Duration: 225.96ms
2025-07-07 20:06:23,054 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "709ea551-235b-4d84-bc55-75e2c2751d9e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 225.96, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:23.054141Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61926 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:06:23,060 - app.middleware.monitoring_middleware - INFO - Request started - ID: ab63b993-5296-4c8e-9b01-f51fcaa7a9e1, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:06:23,062 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ab63b993-5296-4c8e-9b01-f51fcaa7a9e1, Status: 404, Duration: 1.59ms
2025-07-07 20:06:23,062 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ab63b993-5296-4c8e-9b01-f51fcaa7a9e1", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.59, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:23.062639Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61937 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:06:23,179 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:06:23,180 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:06:23,180 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:06:25,082 - app.middleware.monitoring_middleware - INFO - Request started - ID: 28ab3335-8409-4c8a-86c4-6c029ad59eac, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:06:25,242 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:06:25,328 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:06:25,329 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:06:25,330 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 28ab3335-8409-4c8a-86c4-6c029ad59eac, Status: 200, Duration: 248.54ms
2025-07-07 20:06:25,330 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "28ab3335-8409-4c8a-86c4-6c029ad59eac", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 248.54, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:25.330687Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61926 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:06:25,337 - app.middleware.monitoring_middleware - INFO - Request started - ID: d781bfec-f677-4044-8c6d-e3ba94742fcf, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:06:25,339 - app.middleware.monitoring_middleware - INFO - Request completed - ID: d781bfec-f677-4044-8c6d-e3ba94742fcf, Status: 404, Duration: 1.89ms
2025-07-07 20:06:25,339 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "d781bfec-f677-4044-8c6d-e3ba94742fcf", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.89, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:06:25.339860Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:61937 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:06:25,467 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:06:25,469 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:06:25,469 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:09:47,074 - app.middleware.monitoring_middleware - INFO - Request started - ID: b64c5708-4fca-465c-a9b0-0b7356d68fe2, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:09:47,416 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:09:47,613 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:09:47,614 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:09:47,615 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b64c5708-4fca-465c-a9b0-0b7356d68fe2, Status: 200, Duration: 540.51ms
2025-07-07 20:09:47,615 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b64c5708-4fca-465c-a9b0-0b7356d68fe2", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 540.51, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:47.615238Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62971 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:09:47,643 - app.middleware.monitoring_middleware - INFO - Request started - ID: 8e35c98f-714c-426e-8c09-0a85717162b2, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:09:47,723 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:09:47,847 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:09:47,847 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:09:47,849 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 8e35c98f-714c-426e-8c09-0a85717162b2, Status: 200, Duration: 206.0ms
2025-07-07 20:09:47,850 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "8e35c98f-714c-426e-8c09-0a85717162b2", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 206.0, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:47.850117Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62971 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:09:47,858 - app.middleware.monitoring_middleware - INFO - Request started - ID: 69aa17dc-4511-4908-b7e5-edf10adfea46, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:09:47,861 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 69aa17dc-4511-4908-b7e5-edf10adfea46, Status: 404, Duration: 2.59ms
2025-07-07 20:09:47,861 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "69aa17dc-4511-4908-b7e5-edf10adfea46", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.59, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:47.861220Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62981 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:09:48,034 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:09:48,034 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:09:48,034 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:09:48,051 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:09:48,052 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:09:48,052 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:09:48,065 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:09:48,065 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:09:48,066 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:09:48,883 - app.middleware.monitoring_middleware - INFO - Request started - ID: 05782752-ee09-4686-9980-77c31c70444d, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:09:48,971 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:09:49,099 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:09:49,100 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:09:49,101 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 05782752-ee09-4686-9980-77c31c70444d, Status: 200, Duration: 218.54ms
2025-07-07 20:09:49,101 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "05782752-ee09-4686-9980-77c31c70444d", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 218.54, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:49.101831Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62971 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:09:49,109 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9ca45e85-b016-4e50-b888-2c0e26822c54, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:09:49,121 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9ca45e85-b016-4e50-b888-2c0e26822c54, Status: 404, Duration: 12.04ms
2025-07-07 20:09:49,121 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9ca45e85-b016-4e50-b888-2c0e26822c54", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 12.04, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:49.121330Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62981 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:09:49,288 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:09:49,288 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:09:49,288 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:09:51,138 - app.middleware.monitoring_middleware - INFO - Request started - ID: 49d5b631-3ba3-40bd-a5a1-ecf5e695c73e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:09:51,230 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:09:51,380 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:09:51,381 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:09:51,382 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 49d5b631-3ba3-40bd-a5a1-ecf5e695c73e, Status: 200, Duration: 243.32ms
2025-07-07 20:09:51,382 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "49d5b631-3ba3-40bd-a5a1-ecf5e695c73e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 243.32, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:51.382420Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62971 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:09:51,553 - app.middleware.monitoring_middleware - INFO - Request started - ID: 136190df-519b-41d8-b833-1fa6603f8687, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:09:51,555 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 136190df-519b-41d8-b833-1fa6603f8687, Status: 404, Duration: 2.08ms
2025-07-07 20:09:51,555 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "136190df-519b-41d8-b833-1fa6603f8687", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.08, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:09:51.555899Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:62981 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:09:51,688 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:09:51,689 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:09:51,689 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:10:07,553 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7747f37e-8407-452f-ad56-6172f5c48ce3, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:10:07,905 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:10:08,043 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:10:08,055 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:10:08,056 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7747f37e-8407-452f-ad56-6172f5c48ce3, Status: 200, Duration: 503.62ms
2025-07-07 20:10:08,058 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7747f37e-8407-452f-ad56-6172f5c48ce3", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 503.62, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:08.057985Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63072 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:10:08,079 - app.middleware.monitoring_middleware - INFO - Request started - ID: 1cdd870f-a56f-4adc-a5f9-1daf53e63f4c, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:10:08,364 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:10:08,427 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:10:08,428 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:10:08,428 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 1cdd870f-a56f-4adc-a5f9-1daf53e63f4c, Status: 200, Duration: 349.07ms
2025-07-07 20:10:08,428 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "1cdd870f-a56f-4adc-a5f9-1daf53e63f4c", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 349.07, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:08.428820Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63072 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:10:08,461 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5bf02435-46f7-4408-bd57-fc7beeb33ed1, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:10:08,463 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5bf02435-46f7-4408-bd57-fc7beeb33ed1, Status: 404, Duration: 2.87ms
2025-07-07 20:10:08,464 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5bf02435-46f7-4408-bd57-fc7beeb33ed1", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.87, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:08.464120Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63081 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:10:08,687 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:10:08,689 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:10:08,689 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:10:08,690 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:10:08,690 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:10:08,690 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:10:09,482 - app.middleware.monitoring_middleware - INFO - Request started - ID: 70535551-fcb7-4260-a32b-eaa546a1a6e8, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:10:09,615 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:10:09,685 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:10:09,690 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:10:09,691 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 70535551-fcb7-4260-a32b-eaa546a1a6e8, Status: 200, Duration: 208.95ms
2025-07-07 20:10:09,691 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "70535551-fcb7-4260-a32b-eaa546a1a6e8", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 208.95, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:09.691738Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63072 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:10:09,699 - app.middleware.monitoring_middleware - INFO - Request started - ID: 0d79fda4-7cf2-4230-a96f-1f19fabd966a, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:10:09,701 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 0d79fda4-7cf2-4230-a96f-1f19fabd966a, Status: 404, Duration: 2.05ms
2025-07-07 20:10:09,701 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "0d79fda4-7cf2-4230-a96f-1f19fabd966a", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.05, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:09.701258Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63081 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:10:09,871 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:10:09,871 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:10:09,872 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:10:11,717 - app.middleware.monitoring_middleware - INFO - Request started - ID: 6efa3089-2d63-4873-9d12-ee95750e0169, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:10:11,847 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:10:11,965 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:10:11,965 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:10:11,966 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 6efa3089-2d63-4873-9d12-ee95750e0169, Status: 200, Duration: 249.16ms
2025-07-07 20:10:11,966 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "6efa3089-2d63-4873-9d12-ee95750e0169", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 249.16, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:11.966640Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63072 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:10:11,973 - app.middleware.monitoring_middleware - INFO - Request started - ID: da298e59-a2ca-4369-9696-ab6893d57c56, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:10:11,974 - app.middleware.monitoring_middleware - INFO - Request completed - ID: da298e59-a2ca-4369-9696-ab6893d57c56, Status: 404, Duration: 1.29ms
2025-07-07 20:10:11,975 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "da298e59-a2ca-4369-9696-ab6893d57c56", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.29, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:10:11.975027Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63081 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:11:06,644 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:11:41,413 - app.middleware.monitoring_middleware - INFO - Request started - ID: aefe0fc3-85ed-415c-8d08-d7b3f4637756, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:11:41,784 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:11:41,915 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:11:41,916 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:11:41,917 - app.middleware.monitoring_middleware - INFO - Request completed - ID: aefe0fc3-85ed-415c-8d08-d7b3f4637756, Status: 200, Duration: 504.28ms
2025-07-07 20:11:41,917 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "aefe0fc3-85ed-415c-8d08-d7b3f4637756", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 504.28, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:41.917581Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63540 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:11:41,944 - app.middleware.monitoring_middleware - INFO - Request started - ID: b5044cda-ae5c-479e-8b69-7eaeb14637f3, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:11:42,148 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:11:42,220 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:11:42,221 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:11:42,222 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b5044cda-ae5c-479e-8b69-7eaeb14637f3, Status: 200, Duration: 277.65ms
2025-07-07 20:11:42,222 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b5044cda-ae5c-479e-8b69-7eaeb14637f3", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 277.65, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:42.222329Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63540 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:11:42,232 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7815bb96-0600-4222-b119-0c85f8ea047c, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:11:42,239 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7815bb96-0600-4222-b119-0c85f8ea047c, Status: 404, Duration: 6.81ms
2025-07-07 20:11:42,240 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7815bb96-0600-4222-b119-0c85f8ea047c", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 6.81, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:42.239921Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63548 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:11:42,675 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:11:42,676 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:11:42,676 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:11:42,676 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:11:42,677 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:11:42,677 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:11:43,262 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4c21a6cc-39c3-4cd1-8fde-e30728244776, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:11:43,351 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:11:43,423 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:11:43,423 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:11:43,424 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4c21a6cc-39c3-4cd1-8fde-e30728244776, Status: 200, Duration: 161.78ms
2025-07-07 20:11:43,424 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4c21a6cc-39c3-4cd1-8fde-e30728244776", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 161.78, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:43.424738Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63540 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:11:43,431 - app.middleware.monitoring_middleware - INFO - Request started - ID: b7a4a701-7388-47f5-97a4-2b62d747e6f8, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:11:43,433 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b7a4a701-7388-47f5-97a4-2b62d747e6f8, Status: 404, Duration: 1.64ms
2025-07-07 20:11:43,433 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b7a4a701-7388-47f5-97a4-2b62d747e6f8", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.64, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:43.433199Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63548 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:11:45,448 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5c200c95-730f-46e1-86b7-02000065e7e9, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:11:45,550 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:11:45,682 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:11:45,683 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:11:45,684 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5c200c95-730f-46e1-86b7-02000065e7e9, Status: 200, Duration: 235.66ms
2025-07-07 20:11:45,684 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5c200c95-730f-46e1-86b7-02000065e7e9", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 235.66, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:45.684328Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63540 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:11:45,691 - app.middleware.monitoring_middleware - INFO - Request started - ID: 913fc327-8c6f-427a-90d8-a93386a8e7a6, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:11:45,692 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 913fc327-8c6f-427a-90d8-a93386a8e7a6, Status: 404, Duration: 1.28ms
2025-07-07 20:11:45,692 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "913fc327-8c6f-427a-90d8-a93386a8e7a6", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.28, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:11:45.692772Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63548 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:11:45,817 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:11:45,817 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:11:45,817 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:11:45,820 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:11:45,821 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:11:45,821 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:12:14,014 - app.middleware.monitoring_middleware - INFO - Request started - ID: ad6afcdd-ff07-4c4b-98d7-15cf7b3af977, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:12:14,293 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:12:14,449 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:12:14,449 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:12:14,450 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ad6afcdd-ff07-4c4b-98d7-15cf7b3af977, Status: 200, Duration: 436.09ms
2025-07-07 20:12:14,451 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ad6afcdd-ff07-4c4b-98d7-15cf7b3af977", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 436.09, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:14.450966Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63722 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:12:14,684 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:12:14,686 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:12:14,686 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:12:26,648 - app.middleware.monitoring_middleware - INFO - Request started - ID: bbf8d84d-f404-4283-abd5-c478ba342dad, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:12:26,836 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:12:26,979 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:12:26,980 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:12:26,981 - app.middleware.monitoring_middleware - INFO - Request completed - ID: bbf8d84d-f404-4283-abd5-c478ba342dad, Status: 200, Duration: 332.38ms
2025-07-07 20:12:26,981 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "bbf8d84d-f404-4283-abd5-c478ba342dad", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 332.38, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:26.981248Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63800 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:12:27,042 - app.middleware.monitoring_middleware - INFO - Request started - ID: 86f26e74-caa9-4291-9f6d-81ebe87eeef5, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:12:27,130 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:12:27,233 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:12:27,234 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:12:27,235 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 86f26e74-caa9-4291-9f6d-81ebe87eeef5, Status: 200, Duration: 192.59ms
2025-07-07 20:12:27,235 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "86f26e74-caa9-4291-9f6d-81ebe87eeef5", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 192.59, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:27.235179Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63800 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:12:27,298 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9830a5ae-45e2-44fe-b2ad-1a26c5d1b83a, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:12:27,299 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9830a5ae-45e2-44fe-b2ad-1a26c5d1b83a, Status: 404, Duration: 1.47ms
2025-07-07 20:12:27,299 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9830a5ae-45e2-44fe-b2ad-1a26c5d1b83a", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.47, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:27.299777Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63808 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:12:27,515 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:12:27,515 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:12:27,515 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:12:28,318 - app.middleware.monitoring_middleware - INFO - Request started - ID: 019c632f-d00e-4926-87b6-b4ee4a802f85, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:12:28,398 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:12:28,520 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:12:28,521 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:12:28,522 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 019c632f-d00e-4926-87b6-b4ee4a802f85, Status: 200, Duration: 203.28ms
2025-07-07 20:12:28,522 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "019c632f-d00e-4926-87b6-b4ee4a802f85", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 203.28, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:28.522111Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63800 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:12:28,528 - app.middleware.monitoring_middleware - INFO - Request started - ID: 2383661e-a17b-4e98-8fb8-f4f2b3ed9309, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:12:28,529 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 2383661e-a17b-4e98-8fb8-f4f2b3ed9309, Status: 404, Duration: 1.31ms
2025-07-07 20:12:28,530 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "2383661e-a17b-4e98-8fb8-f4f2b3ed9309", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.31, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:28.530047Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63808 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:12:28,679 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:12:28,679 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:12:28,679 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:12:28,808 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:12:28,809 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:12:28,809 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:12:30,584 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7b0941d8-12bc-442d-b6c3-848400be9565, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:12:30,675 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:12:30,907 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:12:30,908 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:12:30,909 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7b0941d8-12bc-442d-b6c3-848400be9565, Status: 200, Duration: 325.03ms
2025-07-07 20:12:30,909 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7b0941d8-12bc-442d-b6c3-848400be9565", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 325.03, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:30.909143Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63800 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:12:30,916 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7569e7ed-1c1a-4b19-894e-4a96c3e19528, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:12:30,919 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7569e7ed-1c1a-4b19-894e-4a96c3e19528, Status: 404, Duration: 2.75ms
2025-07-07 20:12:30,919 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7569e7ed-1c1a-4b19-894e-4a96c3e19528", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.75, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:12:30.919535Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63808 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:12:31,056 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:12:31,057 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:12:31,057 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:13:38,892 - app.middleware.monitoring_middleware - INFO - Request started - ID: 78e3d171-736a-4dcb-a51b-90de590ae023, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:13:39,254 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:13:39,459 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:13:39,459 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:13:39,460 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 78e3d171-736a-4dcb-a51b-90de590ae023, Status: 200, Duration: 568.69ms
2025-07-07 20:13:39,461 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "78e3d171-736a-4dcb-a51b-90de590ae023", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 568.69, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:39.460951Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64151 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:13:39,529 - app.middleware.monitoring_middleware - INFO - Request started - ID: 466bc7c0-c162-40c6-b62f-10a545ac7bec, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:13:39,667 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:13:39,736 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:13:39,736 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:13:39,737 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 466bc7c0-c162-40c6-b62f-10a545ac7bec, Status: 200, Duration: 207.67ms
2025-07-07 20:13:39,737 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "466bc7c0-c162-40c6-b62f-10a545ac7bec", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 207.67, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:39.737450Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64151 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:13:39,799 - app.middleware.monitoring_middleware - INFO - Request started - ID: 97676504-76c3-4263-be76-c21e6060b827, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:13:39,801 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 97676504-76c3-4263-be76-c21e6060b827, Status: 404, Duration: 1.61ms
2025-07-07 20:13:39,801 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "97676504-76c3-4263-be76-c21e6060b827", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.61, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:39.801488Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64159 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:13:40,822 - app.middleware.monitoring_middleware - INFO - Request started - ID: e22313e6-aa7f-4712-bb86-d2d592918f7a, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:13:40,887 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:13:41,029 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:13:41,030 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:13:41,031 - app.middleware.monitoring_middleware - INFO - Request completed - ID: e22313e6-aa7f-4712-bb86-d2d592918f7a, Status: 200, Duration: 209.02ms
2025-07-07 20:13:41,031 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "e22313e6-aa7f-4712-bb86-d2d592918f7a", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 209.02, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:41.031405Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64151 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:13:41,038 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9a63d00e-e0f1-4728-9ac4-c99d9375081d, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:13:41,039 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9a63d00e-e0f1-4728-9ac4-c99d9375081d, Status: 404, Duration: 1.29ms
2025-07-07 20:13:41,039 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9a63d00e-e0f1-4728-9ac4-c99d9375081d", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.29, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:41.039583Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64159 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:13:43,057 - app.middleware.monitoring_middleware - INFO - Request started - ID: c34bab87-9a7a-48d5-9f80-9f80ed449a32, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:13:43,260 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:13:43,335 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:13:43,336 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:13:43,336 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c34bab87-9a7a-48d5-9f80-9f80ed449a32, Status: 200, Duration: 279.2ms
2025-07-07 20:13:43,336 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c34bab87-9a7a-48d5-9f80-9f80ed449a32", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 279.2, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:43.336921Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64151 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:13:43,343 - app.middleware.monitoring_middleware - INFO - Request started - ID: fdd00c0c-5a1f-481f-a3c0-ab394ac6b5fe, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:13:43,345 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fdd00c0c-5a1f-481f-a3c0-ab394ac6b5fe, Status: 404, Duration: 1.58ms
2025-07-07 20:13:43,345 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fdd00c0c-5a1f-481f-a3c0-ab394ac6b5fe", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.58, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:13:43.345290Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64159 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:13:43,559 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:13:43,559 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:13:43,559 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:04,263 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5459f1dc-3f0c-4ebb-89d1-96bb2051a8c3, Method: POST, Path: /api/auth/login, User: None
2025-07-07 20:14:04,495 - httpx - INFO - HTTP Request: POST https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/token?grant_type=password "HTTP/2 200 OK"
2025-07-07 20:14:04,752 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:04,836 - httpx - INFO - HTTP Request: PATCH https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:04,837 - app.auth.supabase_auth - INFO - User logged in successfully: <EMAIL>
2025-07-07 20:14:04,838 - app.api.supabase_auth - INFO - User login successful: <EMAIL>
2025-07-07 20:14:04,839 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5459f1dc-3f0c-4ebb-89d1-96bb2051a8c3, Status: 200, Duration: 575.97ms
2025-07-07 20:14:04,839 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5459f1dc-3f0c-4ebb-89d1-96bb2051a8c3", "method": "POST", "path": "/api/auth/login", "status_code": 200, "duration_ms": 575.97, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:04.839629Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64270 - "POST /api/auth/login HTTP/1.1" 200 OK
2025-07-07 20:14:04,861 - app.middleware.monitoring_middleware - INFO - Request started - ID: 567f0678-5192-4924-b9c1-9e807ec84ebf, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:04,943 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:05,027 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:05,028 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:05,029 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 567f0678-5192-4924-b9c1-9e807ec84ebf, Status: 200, Duration: 168.23ms
2025-07-07 20:14:05,029 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "567f0678-5192-4924-b9c1-9e807ec84ebf", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 168.23, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:05.029458Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:08,720 - app.middleware.monitoring_middleware - INFO - Request started - ID: 31083d1e-a4a8-4438-bb95-cee461bbfc7f, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:08,795 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:08,954 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:08,956 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:08,957 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 31083d1e-a4a8-4438-bb95-cee461bbfc7f, Status: 200, Duration: 236.88ms
2025-07-07 20:14:08,957 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "31083d1e-a4a8-4438-bb95-cee461bbfc7f", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 236.88, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:08.957519Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:08,973 - app.middleware.monitoring_middleware - INFO - Request started - ID: 625e5700-a654-427d-8111-5abb01de26e1, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:09,049 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:09,170 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:09,171 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:09,172 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 625e5700-a654-427d-8111-5abb01de26e1, Status: 200, Duration: 198.98ms
2025-07-07 20:14:09,173 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "625e5700-a654-427d-8111-5abb01de26e1", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 198.98, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:09.172985Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:64306 - "OPTIONS /api/analytics/dashboard HTTP/1.1" 200 OK
2025-07-07 20:14:09,187 - app.middleware.monitoring_middleware - INFO - Request started - ID: b8b55d6d-2c0f-4e8d-8995-2dea9cc03664, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:09,187 - app.middleware.monitoring_middleware - INFO - Request started - ID: fb834d1a-55da-4669-916b-348d011d7054, Method: GET, Path: /api/analytics/dashboard, User: None
2025-07-07 20:14:09,341 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:09,472 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:09,476 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:09,567 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:09,692 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:09,698 - app.monitoring.monitoring_setup - INFO - {"operation_id": "8e09ee98-21b5-4c70-a244-6f6bcae67202", "operation_name": "get_dashboard_analytics", "user_id": "fe99519c-29cd-4817-8ca4-d94b58ed884a", "event": "Operation started", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:09.698470Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "fb834d1a-55da-4669-916b-348d011d7054"}
2025-07-07 20:14:09,861 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:10,007 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&status=eq.published "HTTP/2 200 OK"
2025-07-07 20:14:10,142 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:10,228 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-07-01T00%3A00%3A00&created_at=lt.2025-07-07T20%3A14%3A10.143055 "HTTP/2 200 OK"
2025-07-07 20:14:10,388 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=royalty_earned&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-01T00%3A00%3A00&created_at=lt.2025-07-01T00%3A00%3A00 "HTTP/2 200 OK"
2025-07-07 20:14:10,529 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=metric_type%2Cmetric_value%2Capproved%2Ccreated_at&user_id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a&created_at=gte.2025-06-30T20%3A14%3A10.389617&order=created_at.asc "HTTP/2 200 OK"
2025-07-07 20:14:10,531 - app.api.analytics - INFO - Dashboard analytics retrieved for user fe99519c-29cd-4817-8ca4-d94b58ed884a
2025-07-07 20:14:10,531 - app.monitoring.monitoring_setup - INFO - {"operation_id": "8e09ee98-21b5-4c70-a244-6f6bcae67202", "operation_name": "get_dashboard_analytics", "event": "Operation completed", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.531361Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "fb834d1a-55da-4669-916b-348d011d7054"}
2025-07-07 20:14:10,534 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:10,535 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:10,535 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:10,536 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b8b55d6d-2c0f-4e8d-8995-2dea9cc03664, Status: 200, Duration: 1349.06ms
2025-07-07 20:14:10,536 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fb834d1a-55da-4669-916b-348d011d7054", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 1349.06, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.536159Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:10,541 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fb834d1a-55da-4669-916b-348d011d7054, Status: 200, Duration: 1353.65ms
2025-07-07 20:14:10,541 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fb834d1a-55da-4669-916b-348d011d7054", "method": "GET", "path": "/api/analytics/dashboard", "status_code": 200, "duration_ms": 1353.65, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.541190Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64306 - "GET /api/analytics/dashboard HTTP/1.1" 200 OK
INFO:     127.0.0.1:64306 - "OPTIONS /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 20:14:10,548 - app.middleware.monitoring_middleware - INFO - Request started - ID: dd94e2be-5c0a-4be4-aa71-d3e4a1bec2ab, Method: GET, Path: /api/books, User: None
2025-07-07 20:14:10,549 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 20:14:10,550 - app.middleware.monitoring_middleware - INFO - Request completed - ID: dd94e2be-5c0a-4be4-aa71-d3e4a1bec2ab, Status: 200, Duration: 2.34ms
2025-07-07 20:14:10,550 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "dd94e2be-5c0a-4be4-aa71-d3e4a1bec2ab", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 2.34, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.550432Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64306 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 20:14:10,553 - app.middleware.monitoring_middleware - INFO - Request started - ID: 0434225f-2c47-4b7a-9378-8324e6e752c9, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:10,621 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:10,798 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:10,799 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:10,800 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:10,800 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:10,800 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:10,801 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 0434225f-2c47-4b7a-9378-8324e6e752c9, Status: 200, Duration: 247.65ms
2025-07-07 20:14:10,801 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "0434225f-2c47-4b7a-9378-8324e6e752c9", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 247.65, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.801257Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:10,807 - app.middleware.monitoring_middleware - INFO - Request started - ID: 1cd04e2c-9f8c-49ce-b8cd-630f597ad803, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:10,809 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 1cd04e2c-9f8c-49ce-b8cd-630f597ad803, Status: 404, Duration: 1.6ms
2025-07-07 20:14:10,809 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "1cd04e2c-9f8c-49ce-b8cd-630f597ad803", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.6, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:10.809279Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64306 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:11,826 - app.middleware.monitoring_middleware - INFO - Request started - ID: d2fe8af2-e711-437f-8f6f-e87908260351, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:11,951 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:12,102 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:12,108 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:12,110 - app.middleware.monitoring_middleware - INFO - Request completed - ID: d2fe8af2-e711-437f-8f6f-e87908260351, Status: 200, Duration: 283.5ms
2025-07-07 20:14:12,110 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "d2fe8af2-e711-437f-8f6f-e87908260351", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 283.5, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:12.110377Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:12,120 - app.middleware.monitoring_middleware - INFO - Request started - ID: 45e12b94-00c1-4003-a92a-b4bf66dc5ddf, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:12,129 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 45e12b94-00c1-4003-a92a-b4bf66dc5ddf, Status: 404, Duration: 9.4ms
2025-07-07 20:14:12,129 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "45e12b94-00c1-4003-a92a-b4bf66dc5ddf", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 9.4, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:12.129868Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64306 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:12,267 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:12,268 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:12,268 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:14,149 - app.middleware.monitoring_middleware - INFO - Request started - ID: ec71cc14-83f4-413d-b91a-6636b355abda, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:14,275 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:14,341 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:14,343 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:14,344 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ec71cc14-83f4-413d-b91a-6636b355abda, Status: 200, Duration: 194.94ms
2025-07-07 20:14:14,345 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ec71cc14-83f4-413d-b91a-6636b355abda", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 194.94, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:14.345044Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64278 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:14,352 - app.middleware.monitoring_middleware - INFO - Request started - ID: 242961fc-147c-45db-b15a-afc74c74f439, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:14,354 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 242961fc-147c-45db-b15a-afc74c74f439, Status: 404, Duration: 1.66ms
2025-07-07 20:14:14,355 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "242961fc-147c-45db-b15a-afc74c74f439", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.66, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:14.355125Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64306 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:28,532 - app.middleware.monitoring_middleware - INFO - Request started - ID: 8278c668-6c83-466b-bede-7f21f8525daf, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:28,656 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:28,778 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:28,780 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:28,781 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 8278c668-6c83-466b-bede-7f21f8525daf, Status: 200, Duration: 249.45ms
2025-07-07 20:14:28,781 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "8278c668-6c83-466b-bede-7f21f8525daf", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 249.45, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:28.781781Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:28,795 - app.middleware.monitoring_middleware - INFO - Request started - ID: fb24d3cc-1f5b-429e-bfea-4fdf489232e2, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:28,928 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:29,018 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:29,018 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:29,019 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fb24d3cc-1f5b-429e-bfea-4fdf489232e2, Status: 200, Duration: 224.24ms
2025-07-07 20:14:29,019 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fb24d3cc-1f5b-429e-bfea-4fdf489232e2", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 224.24, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:29.019886Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:29,037 - app.middleware.monitoring_middleware - INFO - Request started - ID: dd7756a8-b5a3-46d9-8826-9383fb0ae5fe, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:29,105 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:29,264 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:29,267 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:29,268 - app.middleware.monitoring_middleware - INFO - Request completed - ID: dd7756a8-b5a3-46d9-8826-9383fb0ae5fe, Status: 200, Duration: 230.95ms
2025-07-07 20:14:29,268 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "dd7756a8-b5a3-46d9-8826-9383fb0ae5fe", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 230.95, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:29.268438Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:29,276 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9e311f9d-721d-4cb4-9bbe-63483ff4eb8b, Method: GET, Path: /api/books, User: None
2025-07-07 20:14:29,279 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 20:14:29,282 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9e311f9d-721d-4cb4-9bbe-63483ff4eb8b, Status: 200, Duration: 6.13ms
2025-07-07 20:14:29,284 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9e311f9d-721d-4cb4-9bbe-63483ff4eb8b", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 6.13, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:29.284455Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64418 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 20:14:29,296 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5fd46058-f40f-483f-88a2-5b72b2b01bcc, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:29,408 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:29,477 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:29,482 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:29,488 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5fd46058-f40f-483f-88a2-5b72b2b01bcc, Status: 200, Duration: 191.12ms
2025-07-07 20:14:29,488 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5fd46058-f40f-483f-88a2-5b72b2b01bcc", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 191.12, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:29.488267Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:29,496 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4200b351-7a84-4482-ad18-3b84cb8d5cd5, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:29,500 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4200b351-7a84-4482-ad18-3b84cb8d5cd5, Status: 404, Duration: 3.22ms
2025-07-07 20:14:29,500 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4200b351-7a84-4482-ad18-3b84cb8d5cd5", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 3.22, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:29.500261Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64418 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:29,654 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:29,655 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:29,655 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:29,711 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:29,712 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:29,712 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:31,726 - app.middleware.monitoring_middleware - INFO - Request started - ID: 16be5429-3ef9-4082-b005-947f2ec97b7e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:31,805 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:31,881 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:31,882 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:31,889 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 16be5429-3ef9-4082-b005-947f2ec97b7e, Status: 200, Duration: 162.89ms
2025-07-07 20:14:31,889 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "16be5429-3ef9-4082-b005-947f2ec97b7e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 162.89, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:31.889764Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:31,912 - app.middleware.monitoring_middleware - INFO - Request started - ID: b51e6978-ae60-4f74-990f-81e0b29336d2, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:31,915 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b51e6978-ae60-4f74-990f-81e0b29336d2, Status: 404, Duration: 3.51ms
2025-07-07 20:14:31,916 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b51e6978-ae60-4f74-990f-81e0b29336d2", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 3.51, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:31.916029Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64418 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:32,026 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:32,027 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:32,027 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:33,936 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5e755f3f-4960-482b-b040-a9c5b0d19207, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:14:34,011 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:14:34,123 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:14:34,128 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:14:34,130 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5e755f3f-4960-482b-b040-a9c5b0d19207, Status: 200, Duration: 193.3ms
2025-07-07 20:14:34,130 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5e755f3f-4960-482b-b040-a9c5b0d19207", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 193.3, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:34.130301Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64409 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:14:34,139 - app.middleware.monitoring_middleware - INFO - Request started - ID: 27ac1931-85dd-4ce2-b7bd-1b3f767ce136, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:14:34,145 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 27ac1931-85dd-4ce2-b7bd-1b3f767ce136, Status: 404, Duration: 6.08ms
2025-07-07 20:14:34,146 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "27ac1931-85dd-4ce2-b7bd-1b3f767ce136", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 6.08, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:14:34.146147Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64418 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:14:34,260 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:34,261 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:34,261 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:14:34,288 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:14:34,289 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:14:34,289 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:16:06,659 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:17:55,333 - app.middleware.monitoring_middleware - INFO - Request started - ID: d009f2dc-b7d3-4287-be98-acf8b2567419, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:17:55,655 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:17:55,837 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:17:55,843 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:17:55,845 - app.middleware.monitoring_middleware - INFO - Request completed - ID: d009f2dc-b7d3-4287-be98-acf8b2567419, Status: 200, Duration: 511.44ms
2025-07-07 20:17:55,845 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "d009f2dc-b7d3-4287-be98-acf8b2567419", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 511.44, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:55.845230Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:17:55,863 - app.middleware.monitoring_middleware - INFO - Request started - ID: 72b059b7-0e08-4da7-850a-ee8b1d37bf35, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:17:55,971 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:17:56,089 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:17:56,089 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:17:56,090 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 72b059b7-0e08-4da7-850a-ee8b1d37bf35, Status: 200, Duration: 227.4ms
2025-07-07 20:17:56,091 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "72b059b7-0e08-4da7-850a-ee8b1d37bf35", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 227.4, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:56.090951Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:17:56,106 - app.middleware.monitoring_middleware - INFO - Request started - ID: ad1e2540-174c-4183-b63a-7f5b0ea3d80e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:17:56,217 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:17:56,282 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:17:56,285 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:17:56,286 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ad1e2540-174c-4183-b63a-7f5b0ea3d80e, Status: 200, Duration: 180.54ms
2025-07-07 20:17:56,286 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ad1e2540-174c-4183-b63a-7f5b0ea3d80e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 180.54, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:56.286814Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:17:56,293 - app.middleware.monitoring_middleware - INFO - Request started - ID: c5ba0148-a860-4a01-bc55-895d4f46f0d9, Method: GET, Path: /api/books, User: None
2025-07-07 20:17:56,296 - app.api.supabase_books - INFO - Getting books with status=None, limit=6
2025-07-07 20:17:56,297 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c5ba0148-a860-4a01-bc55-895d4f46f0d9, Status: 200, Duration: 3.39ms
2025-07-07 20:17:56,297 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c5ba0148-a860-4a01-bc55-895d4f46f0d9", "method": "GET", "path": "/api/books", "status_code": 200, "duration_ms": 3.39, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:56.297593Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65344 - "GET /api/books?limit=6 HTTP/1.1" 200 OK
2025-07-07 20:17:56,301 - app.middleware.monitoring_middleware - INFO - Request started - ID: c7c18faa-b9b2-474a-afee-c044760ad70e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:17:56,498 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:17:56,614 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:17:56,618 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:17:56,620 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c7c18faa-b9b2-474a-afee-c044760ad70e, Status: 200, Duration: 318.55ms
2025-07-07 20:17:56,620 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c7c18faa-b9b2-474a-afee-c044760ad70e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 318.55, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:56.620229Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:65344 - "OPTIONS /api/users/preferences HTTP/1.1" 200 OK
2025-07-07 20:17:56,627 - app.middleware.monitoring_middleware - INFO - Request started - ID: c3f9f8a4-5a38-4cb5-bced-68cc643eccc8, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:17:56,629 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c3f9f8a4-5a38-4cb5-bced-68cc643eccc8, Status: 404, Duration: 1.43ms
2025-07-07 20:17:56,629 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c3f9f8a4-5a38-4cb5-bced-68cc643eccc8", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.43, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:56.629454Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65344 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:17:56,886 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:17:56,887 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:17:56,887 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:17:57,648 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7a41d70d-1222-464d-af38-0f93cb27d838, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:17:57,760 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:17:57,823 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:17:57,824 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:17:57,824 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7a41d70d-1222-464d-af38-0f93cb27d838, Status: 200, Duration: 176.09ms
2025-07-07 20:17:57,825 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7a41d70d-1222-464d-af38-0f93cb27d838", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 176.09, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:57.825001Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:17:57,831 - app.middleware.monitoring_middleware - INFO - Request started - ID: 992b9360-c3d8-46d0-a299-e5094a4b95ce, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:17:57,832 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 992b9360-c3d8-46d0-a299-e5094a4b95ce, Status: 404, Duration: 1.39ms
2025-07-07 20:17:57,833 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "992b9360-c3d8-46d0-a299-e5094a4b95ce", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.39, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:17:57.832976Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65344 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:17:57,963 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:17:57,963 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:17:57,963 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:17:59,850 - app.middleware.monitoring_middleware - INFO - Request started - ID: f2c748f1-2fec-4c4d-b035-82990d814573, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:18:00,028 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:18:00,158 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:18:00,160 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:18:00,161 - app.middleware.monitoring_middleware - INFO - Request completed - ID: f2c748f1-2fec-4c4d-b035-82990d814573, Status: 200, Duration: 311.04ms
2025-07-07 20:18:00,161 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "f2c748f1-2fec-4c4d-b035-82990d814573", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 311.04, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:18:00.161761Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65335 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:18:00,175 - app.middleware.monitoring_middleware - INFO - Request started - ID: 3c0c6155-192d-4430-8206-70f981a2e489, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:18:00,178 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 3c0c6155-192d-4430-8206-70f981a2e489, Status: 404, Duration: 3.13ms
2025-07-07 20:18:00,178 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "3c0c6155-192d-4430-8206-70f981a2e489", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 3.13, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:18:00.178791Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:65344 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:18:00,312 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:18:00,313 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:18:00,313 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:03,819 - app.middleware.monitoring_middleware - INFO - Request started - ID: 29d6057b-a175-4d7b-8e5b-bde656e3c8d0, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:04,030 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:04,246 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:04,249 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:04,251 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 29d6057b-a175-4d7b-8e5b-bde656e3c8d0, Status: 200, Duration: 432.34ms
2025-07-07 20:19:04,252 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "29d6057b-a175-4d7b-8e5b-bde656e3c8d0", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 432.34, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:04.252039Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:49299 - "OPTIONS /api/users/profile HTTP/1.1" 200 OK
2025-07-07 20:19:04,265 - app.middleware.monitoring_middleware - INFO - Request started - ID: 44e23e3f-939c-4bc0-8a55-ff87e4cf6ff0, Method: GET, Path: /api/users/profile, User: None
2025-07-07 20:19:04,268 - app.middleware.monitoring_middleware - INFO - Request started - ID: 86707d08-c02f-44e3-ad54-8b2851b1699d, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:04,394 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:04,472 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:04,474 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:04,475 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 44e23e3f-939c-4bc0-8a55-ff87e4cf6ff0, Status: 404, Duration: 210.04ms
2025-07-07 20:19:04,475 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "86707d08-c02f-44e3-ad54-8b2851b1699d", "method": "GET", "path": "/api/users/profile", "status_code": 404, "duration_ms": 210.04, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:04.475203Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49299 - "GET /api/users/profile HTTP/1.1" 404 Not Found
2025-07-07 20:19:04,476 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 86707d08-c02f-44e3-ad54-8b2851b1699d, Status: 200, Duration: 207.78ms
2025-07-07 20:19:04,476 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "86707d08-c02f-44e3-ad54-8b2851b1699d", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 207.78, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:04.476392Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:04,497 - app.middleware.monitoring_middleware - INFO - Request started - ID: ee29d09a-3731-40f9-83c2-16225574b93c, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:04,655 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:04,868 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:04,869 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:04,870 - app.middleware.monitoring_middleware - INFO - Request started - ID: fb9be87b-bbcd-49e7-be84-86f7e8c1f1e6, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:19:04,871 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ee29d09a-3731-40f9-83c2-16225574b93c, Status: 200, Duration: 374.69ms
2025-07-07 20:19:04,872 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fb9be87b-bbcd-49e7-be84-86f7e8c1f1e6", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 374.69, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:04.871989Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:04,872 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fb9be87b-bbcd-49e7-be84-86f7e8c1f1e6, Status: 404, Duration: 2.93ms
2025-07-07 20:19:04,873 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fb9be87b-bbcd-49e7-be84-86f7e8c1f1e6", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 2.93, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:04.873040Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49299 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:19:04,892 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9d1fd392-7db0-4751-8291-3e498a694ca5, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:05,049 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:05,319 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:05,323 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
INFO:     127.0.0.1:49299 - "OPTIONS /api/users/publishing-settings HTTP/1.1" 200 OK
2025-07-07 20:19:05,324 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9d1fd392-7db0-4751-8291-3e498a694ca5, Status: 200, Duration: 432.1ms
2025-07-07 20:19:05,324 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9d1fd392-7db0-4751-8291-3e498a694ca5", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 432.1, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:05.324551Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:05,327 - app.middleware.monitoring_middleware - INFO - Request started - ID: 2519afd6-3138-4873-9f72-54abe4e259ac, Method: GET, Path: /api/users/publishing-settings, User: None
2025-07-07 20:19:05,330 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 2519afd6-3138-4873-9f72-54abe4e259ac, Status: 404, Duration: 3.05ms
2025-07-07 20:19:05,331 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "2519afd6-3138-4873-9f72-54abe4e259ac", "method": "GET", "path": "/api/users/publishing-settings", "status_code": 404, "duration_ms": 3.05, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:05.331067Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49299 - "GET /api/users/publishing-settings HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49306 - "OPTIONS /api/users/integrations HTTP/1.1" 200 OK
2025-07-07 20:19:05,339 - app.middleware.monitoring_middleware - INFO - Request started - ID: 23c72321-6428-48ba-86a5-e4f3b2c70463, Method: GET, Path: /api/users/integrations, User: None
2025-07-07 20:19:05,348 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 23c72321-6428-48ba-86a5-e4f3b2c70463, Status: 404, Duration: 8.82ms
2025-07-07 20:19:05,348 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "23c72321-6428-48ba-86a5-e4f3b2c70463", "method": "GET", "path": "/api/users/integrations", "status_code": 404, "duration_ms": 8.82, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:05.348781Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/integrations HTTP/1.1" 404 Not Found
2025-07-07 20:19:05,357 - app.middleware.monitoring_middleware - INFO - Request started - ID: 552da0be-4df0-4919-9edf-a1bff5dbc891, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:05,507 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:05,706 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:05,707 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:05,719 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 552da0be-4df0-4919-9edf-a1bff5dbc891, Status: 200, Duration: 362.35ms
2025-07-07 20:19:05,719 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "552da0be-4df0-4919-9edf-a1bff5dbc891", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 362.35, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:05.719461Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:49306 - "OPTIONS /api/users/security HTTP/1.1" 200 OK
2025-07-07 20:19:05,729 - app.middleware.monitoring_middleware - INFO - Request started - ID: fa24b90d-7f8a-4aba-b9af-794eb7673137, Method: GET, Path: /api/users/security, User: None
2025-07-07 20:19:05,735 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fa24b90d-7f8a-4aba-b9af-794eb7673137, Status: 404, Duration: 6.27ms
2025-07-07 20:19:05,735 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fa24b90d-7f8a-4aba-b9af-794eb7673137", "method": "GET", "path": "/api/users/security", "status_code": 404, "duration_ms": 6.27, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:05.735733Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/security HTTP/1.1" 404 Not Found
2025-07-07 20:19:05,747 - app.middleware.monitoring_middleware - INFO - Request started - ID: 3de40030-eabb-4f32-a87c-cd6f08e06084, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:05,997 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:06,095 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:06,095 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:06,100 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,102 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,102 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,107 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 3de40030-eabb-4f32-a87c-cd6f08e06084, Status: 200, Duration: 360.1ms
2025-07-07 20:19:06,107 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "3de40030-eabb-4f32-a87c-cd6f08e06084", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 360.1, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.107859Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:49306 - "OPTIONS /api/users/sessions HTTP/1.1" 200 OK
2025-07-07 20:19:06,118 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5ed6de22-db1c-4648-9f6c-4ab586725233, Method: GET, Path: /api/users/sessions, User: None
2025-07-07 20:19:06,122 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5ed6de22-db1c-4648-9f6c-4ab586725233, Status: 404, Duration: 4.36ms
2025-07-07 20:19:06,122 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5ed6de22-db1c-4648-9f6c-4ab586725233", "method": "GET", "path": "/api/users/sessions", "status_code": 404, "duration_ms": 4.36, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.122896Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/sessions HTTP/1.1" 404 Not Found
2025-07-07 20:19:06,128 - app.middleware.monitoring_middleware - INFO - Request started - ID: 6ce2f6bd-08fd-4290-8ac5-a506757028ca, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:06,262 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:06,420 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:06,421 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:06,427 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,428 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,429 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,430 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,430 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,430 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,430 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,431 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,431 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,433 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 6ce2f6bd-08fd-4290-8ac5-a506757028ca, Status: 200, Duration: 304.63ms
2025-07-07 20:19:06,433 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "6ce2f6bd-08fd-4290-8ac5-a506757028ca", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 304.63, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.433377Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:06,447 - app.middleware.monitoring_middleware - INFO - Request started - ID: a17ab4c2-72e8-4ed2-a3cb-1082aad30ef6, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:06,583 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:06,752 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:06,752 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:06,753 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,754 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,755 - app.middleware.monitoring_middleware - INFO - Request started - ID: ba1d5419-3958-4230-9eeb-b648851a5a5f, Method: GET, Path: /api/users/profile, User: None
2025-07-07 20:19:06,755 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,756 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,756 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,756 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,756 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,757 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,757 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,758 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a17ab4c2-72e8-4ed2-a3cb-1082aad30ef6, Status: 200, Duration: 310.7ms
2025-07-07 20:19:06,758 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ba1d5419-3958-4230-9eeb-b648851a5a5f", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 310.7, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.758421Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:06,760 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ba1d5419-3958-4230-9eeb-b648851a5a5f, Status: 404, Duration: 5.32ms
2025-07-07 20:19:06,760 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ba1d5419-3958-4230-9eeb-b648851a5a5f", "method": "GET", "path": "/api/users/profile", "status_code": 404, "duration_ms": 5.32, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.760709Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/profile HTTP/1.1" 404 Not Found
2025-07-07 20:19:06,774 - app.middleware.monitoring_middleware - INFO - Request started - ID: 3991b700-694e-4878-9f14-da43eb8faf1f, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:06,841 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:06,909 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:06,909 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:06,910 - app.middleware.monitoring_middleware - INFO - Request started - ID: adc1254f-66b8-4dc5-88c8-7f083472e967, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:19:06,910 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,911 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:06,911 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,912 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,912 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:06,912 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:06,913 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 3991b700-694e-4878-9f14-da43eb8faf1f, Status: 200, Duration: 139.08ms
2025-07-07 20:19:06,913 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "adc1254f-66b8-4dc5-88c8-7f083472e967", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 139.08, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.913771Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:06,915 - app.middleware.monitoring_middleware - INFO - Request completed - ID: adc1254f-66b8-4dc5-88c8-7f083472e967, Status: 404, Duration: 5.53ms
2025-07-07 20:19:06,916 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "adc1254f-66b8-4dc5-88c8-7f083472e967", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 5.53, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.915936Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:19:06,921 - app.middleware.monitoring_middleware - INFO - Request started - ID: bf61176b-52e9-4eb2-8460-8e0f724db027, Method: GET, Path: /api/users/publishing-settings, User: None
2025-07-07 20:19:06,924 - app.middleware.monitoring_middleware - INFO - Request completed - ID: bf61176b-52e9-4eb2-8460-8e0f724db027, Status: 404, Duration: 2.94ms
2025-07-07 20:19:06,924 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "bf61176b-52e9-4eb2-8460-8e0f724db027", "method": "GET", "path": "/api/users/publishing-settings", "status_code": 404, "duration_ms": 2.94, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:06.924680Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/publishing-settings HTTP/1.1" 404 Not Found
2025-07-07 20:19:06,931 - app.middleware.monitoring_middleware - INFO - Request started - ID: b081e74c-4384-49b5-b55d-85b56bfc6e4b, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:07,141 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:07,276 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:07,277 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:07,278 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:07,278 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:07,279 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:07,279 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:07,280 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:07,280 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:07,280 - app.middleware.monitoring_middleware - INFO - Request completed - ID: b081e74c-4384-49b5-b55d-85b56bfc6e4b, Status: 200, Duration: 348.62ms
2025-07-07 20:19:07,280 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "b081e74c-4384-49b5-b55d-85b56bfc6e4b", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 348.62, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.280638Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:07,289 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7b99d5ba-f267-4607-88f2-556be10e2944, Method: GET, Path: /api/users/integrations, User: None
2025-07-07 20:19:07,291 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7b99d5ba-f267-4607-88f2-556be10e2944, Status: 404, Duration: 2.42ms
2025-07-07 20:19:07,291 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7b99d5ba-f267-4607-88f2-556be10e2944", "method": "GET", "path": "/api/users/integrations", "status_code": 404, "duration_ms": 2.42, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.291667Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/integrations HTTP/1.1" 404 Not Found
2025-07-07 20:19:07,302 - app.middleware.monitoring_middleware - INFO - Request started - ID: 27b3ff90-d4d7-4ce4-9227-ba19fc60e1e3, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:07,383 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:07,466 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:07,477 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:07,478 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:07,478 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:07,478 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:07,479 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 27b3ff90-d4d7-4ce4-9227-ba19fc60e1e3, Status: 200, Duration: 177.34ms
2025-07-07 20:19:07,479 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "27b3ff90-d4d7-4ce4-9227-ba19fc60e1e3", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 177.34, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.479482Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:07,487 - app.middleware.monitoring_middleware - INFO - Request started - ID: 7c229555-5f1a-47bd-a5f2-bcafeea5595e, Method: GET, Path: /api/users/security, User: None
2025-07-07 20:19:07,493 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 7c229555-5f1a-47bd-a5f2-bcafeea5595e, Status: 404, Duration: 6.27ms
2025-07-07 20:19:07,494 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "7c229555-5f1a-47bd-a5f2-bcafeea5595e", "method": "GET", "path": "/api/users/security", "status_code": 404, "duration_ms": 6.27, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.493914Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/security HTTP/1.1" 404 Not Found
2025-07-07 20:19:07,499 - app.middleware.monitoring_middleware - INFO - Request started - ID: e1a49586-a481-4ca9-8d47-52fc9bad7872, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:07,575 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:07,649 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:07,650 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:07,651 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:07,652 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:07,652 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:07,653 - app.middleware.monitoring_middleware - INFO - Request completed - ID: e1a49586-a481-4ca9-8d47-52fc9bad7872, Status: 200, Duration: 153.84ms
2025-07-07 20:19:07,653 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "e1a49586-a481-4ca9-8d47-52fc9bad7872", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 153.84, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.653561Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:07,660 - app.middleware.monitoring_middleware - INFO - Request started - ID: ecc3b0cf-0922-44c3-86ea-51d056b4293a, Method: GET, Path: /api/users/sessions, User: None
2025-07-07 20:19:07,661 - app.middleware.monitoring_middleware - INFO - Request completed - ID: ecc3b0cf-0922-44c3-86ea-51d056b4293a, Status: 404, Duration: 1.57ms
2025-07-07 20:19:07,661 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "ecc3b0cf-0922-44c3-86ea-51d056b4293a", "method": "GET", "path": "/api/users/sessions", "status_code": 404, "duration_ms": 1.57, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:07.661889Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/sessions HTTP/1.1" 404 Not Found
2025-07-07 20:19:07,803 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:07,804 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:07,804 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:08,781 - app.middleware.monitoring_middleware - INFO - Request started - ID: c78aa812-1bc6-4c97-90fa-cc1fde3532b0, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:08,973 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:09,042 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:09,044 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:09,044 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c78aa812-1bc6-4c97-90fa-cc1fde3532b0, Status: 200, Duration: 263.75ms
2025-07-07 20:19:09,045 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c78aa812-1bc6-4c97-90fa-cc1fde3532b0", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 263.75, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.045036Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:09,052 - app.middleware.monitoring_middleware - INFO - Request started - ID: c25fd14e-1780-4449-8107-f0969df8eb98, Method: GET, Path: /api/users/profile, User: None
2025-07-07 20:19:09,055 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c25fd14e-1780-4449-8107-f0969df8eb98, Status: 404, Duration: 3.34ms
2025-07-07 20:19:09,055 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c25fd14e-1780-4449-8107-f0969df8eb98", "method": "GET", "path": "/api/users/profile", "status_code": 404, "duration_ms": 3.34, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.055844Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/profile HTTP/1.1" 404 Not Found
2025-07-07 20:19:09,059 - app.middleware.monitoring_middleware - INFO - Request started - ID: c80bbc1c-778a-46fb-b8f9-92d9fc9ba9d8, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:09,233 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:09,365 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:09,368 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:09,369 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:09,370 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:09,370 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:09,370 - app.middleware.monitoring_middleware - INFO - Request completed - ID: c80bbc1c-778a-46fb-b8f9-92d9fc9ba9d8, Status: 200, Duration: 311.69ms
2025-07-07 20:19:09,370 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "c80bbc1c-778a-46fb-b8f9-92d9fc9ba9d8", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 311.69, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.370895Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:09,377 - app.middleware.monitoring_middleware - INFO - Request started - ID: fbcbf6be-c678-4ea1-b63d-acc05b5d30f6, Method: GET, Path: /api/users/preferences, User: None
2025-07-07 20:19:09,378 - app.middleware.monitoring_middleware - INFO - Request completed - ID: fbcbf6be-c678-4ea1-b63d-acc05b5d30f6, Status: 404, Duration: 1.61ms
2025-07-07 20:19:09,378 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "fbcbf6be-c678-4ea1-b63d-acc05b5d30f6", "method": "GET", "path": "/api/users/preferences", "status_code": 404, "duration_ms": 1.61, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.378894Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/preferences HTTP/1.1" 404 Not Found
2025-07-07 20:19:09,384 - app.middleware.monitoring_middleware - INFO - Request started - ID: 5822e6b1-2b73-41cd-84a8-e8383c90e475, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:09,485 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:09,603 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:09,605 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:09,606 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:09,607 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:09,607 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:09,609 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 5822e6b1-2b73-41cd-84a8-e8383c90e475, Status: 200, Duration: 224.51ms
2025-07-07 20:19:09,609 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "5822e6b1-2b73-41cd-84a8-e8383c90e475", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 224.51, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.609428Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:09,616 - app.middleware.monitoring_middleware - INFO - Request started - ID: 1b5098a0-44be-435c-997c-2dbd40a0b22e, Method: GET, Path: /api/users/publishing-settings, User: None
2025-07-07 20:19:09,622 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 1b5098a0-44be-435c-997c-2dbd40a0b22e, Status: 404, Duration: 5.39ms
2025-07-07 20:19:09,622 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "1b5098a0-44be-435c-997c-2dbd40a0b22e", "method": "GET", "path": "/api/users/publishing-settings", "status_code": 404, "duration_ms": 5.39, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.622610Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/publishing-settings HTTP/1.1" 404 Not Found
2025-07-07 20:19:09,638 - app.middleware.monitoring_middleware - INFO - Request started - ID: 86a03dd6-5db1-4a7c-90bd-2c75cac062f2, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:09,720 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:09,791 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:09,791 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:09,793 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 86a03dd6-5db1-4a7c-90bd-2c75cac062f2, Status: 200, Duration: 155.49ms
2025-07-07 20:19:09,793 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "86a03dd6-5db1-4a7c-90bd-2c75cac062f2", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 155.49, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.793779Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:09,802 - app.middleware.monitoring_middleware - INFO - Request started - ID: e9bfeeae-ac48-489c-ad0c-ed5efc305153, Method: GET, Path: /api/users/integrations, User: None
2025-07-07 20:19:09,819 - app.middleware.monitoring_middleware - INFO - Request completed - ID: e9bfeeae-ac48-489c-ad0c-ed5efc305153, Status: 404, Duration: 16.77ms
2025-07-07 20:19:09,819 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "e9bfeeae-ac48-489c-ad0c-ed5efc305153", "method": "GET", "path": "/api/users/integrations", "status_code": 404, "duration_ms": 16.77, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:09.819563Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/integrations HTTP/1.1" 404 Not Found
2025-07-07 20:19:09,829 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4462466e-ba73-4a82-acff-3504d0451239, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:09,965 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:10,037 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:10,041 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:10,042 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:10,043 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:10,044 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:10,044 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:10,045 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:10,045 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:10,046 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4462466e-ba73-4a82-acff-3504d0451239, Status: 200, Duration: 216.7ms
2025-07-07 20:19:10,046 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4462466e-ba73-4a82-acff-3504d0451239", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 216.7, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:10.046384Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:10,056 - app.middleware.monitoring_middleware - INFO - Request started - ID: a47e4b8a-97f1-4b62-b30b-3432e98de3b6, Method: GET, Path: /api/users/security, User: None
2025-07-07 20:19:10,063 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a47e4b8a-97f1-4b62-b30b-3432e98de3b6, Status: 404, Duration: 7.13ms
2025-07-07 20:19:10,064 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a47e4b8a-97f1-4b62-b30b-3432e98de3b6", "method": "GET", "path": "/api/users/security", "status_code": 404, "duration_ms": 7.13, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:10.064067Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/security HTTP/1.1" 404 Not Found
2025-07-07 20:19:10,072 - app.middleware.monitoring_middleware - INFO - Request started - ID: 648065ba-355a-4470-9ce6-3047b51ee52a, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:19:10,182 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:19:10,288 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:19:10,291 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:19:10,291 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:10,292 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:19:10,292 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:10,292 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:10,293 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:19:10,293 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:19:10,293 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 648065ba-355a-4470-9ce6-3047b51ee52a, Status: 200, Duration: 220.88ms
2025-07-07 20:19:10,293 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "648065ba-355a-4470-9ce6-3047b51ee52a", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 220.88, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:10.293849Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49295 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:19:10,299 - app.middleware.monitoring_middleware - INFO - Request started - ID: 0b5c793d-36d6-4a23-9558-642932ce8caf, Method: GET, Path: /api/users/sessions, User: None
2025-07-07 20:19:10,304 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 0b5c793d-36d6-4a23-9558-642932ce8caf, Status: 404, Duration: 4.44ms
2025-07-07 20:19:10,304 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "0b5c793d-36d6-4a23-9558-642932ce8caf", "method": "GET", "path": "/api/users/sessions", "status_code": 404, "duration_ms": 4.44, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:19:10.304451Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49306 - "GET /api/users/sessions HTTP/1.1" 404 Not Found
2025-07-07 20:21:06,678 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:26:06,669 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:26:07,637 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T00%3A26%3A07.393579 "HTTP/2 200 OK"
2025-07-07 20:31:06,677 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
WARNING:  WatchFiles detected changes in 'app/models/supabase_models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 20:32:30,693 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 20:32:30,704 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-07 20:32:30,704 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-07 20:32:30,722 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 20:32:30,726 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 20:32:30,726 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 20:32:30,727 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [34541]
INFO:     Started server process [61794]
INFO:     Waiting for application startup.
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 20:32:34,791 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 20:32:34,792 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 20:32:34,792 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 20:32:34,792 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 20:32:34,792 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 20:32:35,283 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 20:32:35,283 - root - INFO - Logflare initialized
2025-07-07 20:32:35,283 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 20:32:35,283 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 20:32:35,296 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:32:36,392 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 20:32:36,761 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:36,901 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,064 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,288 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,362 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,437 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,566 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:37,567 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 20:32:37,567 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 20:32:37,572 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 20:32:37,572 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 20:32:37,574 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 20:32:37,574 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 20:32:37,587 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:32:37,587 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:32:37,587 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 20:32:37,593 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 20:32:37,593 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 20:32:37,593 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 20:32:37,806 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,067 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,144 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,334 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,462 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,593 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,723 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,861 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:32:38,863 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 20:32:38,863 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 20:32:38,866 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 20:32:38,866 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 20:32:38,866 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 20:32:38,866 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 20:32:38,878 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:32:38,878 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:32:38,879 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 20:32:39,541 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 20:32:39,541 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 20:32:39,771 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T00%3A32%3A38.866861 "HTTP/2 200 OK"
WARNING:  WatchFiles detected changes in 'app/models/supabase_models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 20:32:56,609 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 20:32:56,720 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-07 20:32:56,720 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-07 20:32:56,721 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 20:32:56,721 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 20:32:56,721 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 20:32:56,721 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [61794]
INFO:     Started server process [62573]
INFO:     Waiting for application startup.
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 20:32:59,333 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 20:32:59,334 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 20:32:59,334 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 20:32:59,334 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 20:32:59,334 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 20:32:59,700 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 20:32:59,700 - root - INFO - Logflare initialized
2025-07-07 20:32:59,700 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 20:32:59,701 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 20:32:59,713 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:33:00,479 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 20:33:00,746 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:00,895 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,037 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,165 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,310 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,551 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,681 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:01,681 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 20:33:01,682 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 20:33:01,685 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 20:33:01,686 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 20:33:01,687 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 20:33:01,687 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 20:33:01,699 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:33:01,699 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:33:01,700 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 20:33:01,704 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 20:33:01,704 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 20:33:01,704 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 20:33:01,927 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,194 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,334 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,478 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,560 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,713 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,840 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,973 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:33:02,974 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 20:33:02,974 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 20:33:02,978 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 20:33:02,978 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 20:33:02,978 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 20:33:02,978 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 20:33:02,989 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:33:02,989 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:33:02,990 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 20:33:03,677 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 20:33:03,677 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 20:33:03,892 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T00%3A33%3A02.978397 "HTTP/2 200 OK"
WARNING:  WatchFiles detected changes in 'app/api/users.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 20:34:05,922 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 20:35:05,922 - asyncpg.pool - WARNING - Pool.close() is taking over 60 seconds to complete. Check if you have any unreleased connections left. Use asyncio.wait_for() to set a timeout for Pool.close().
2025-07-07 20:35:05,923 - app.utils.supabase.supabase_database - ERROR - ❌ Database cleanup error
Traceback (most recent call last):
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/utils/supabase/supabase_database.py", line 193, in cleanup_database
    await db.close_pool()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/utils/supabase/supabase_database.py", line 65, in close_pool
    await self.db_pool.close()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/pool.py", line 940, in close
    await asyncio.gather(*close_coros)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/pool.py", line 247, in close
    await self._con.close()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg/protocol/protocol.pyx", line 642, in close
TimeoutError
2025-07-07 20:35:05,925 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 20:35:05,926 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 20:35:05,926 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 20:35:05,926 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [62573]
WARNING:  WatchFiles detected changes in 'app/main_supabase.py'. Reloading...
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 69, in serve
    await self._serve(sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 76, in _serve
    config.load()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 434, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/main_supabase.py", line 14, in <module>
    from app.api import (
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/api/users.py", line 18, in <module>
    from app.middleware.rate_limiter import limiter
ModuleNotFoundError: No module named 'app.middleware.rate_limiter'
Process SpawnProcess-5:
Traceback (most recent call last):
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 69, in serve
    await self._serve(sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 76, in _serve
    config.load()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 434, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/main_supabase.py", line 14, in <module>
    from app.api import (
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/api/users.py", line 18, in <module>
    from app.middleware.rate_limiter import limiter
ModuleNotFoundError: No module named 'app.middleware.rate_limiter'
WARNING:  WatchFiles detected changes in 'app/api/users.py'. Reloading...
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 69, in serve
    await self._serve(sockets)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 76, in _serve
    config.load()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 434, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/main_supabase.py", line 14, in <module>
    from app.api import (
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/api/users.py", line 174, in <module>
    @limiter.limit("100/minute")
     ^^^^^^^
NameError: name 'limiter' is not defined
WARNING:  WatchFiles detected changes in 'app/api/users.py'. Reloading...
INFO:     Started server process [67887]
INFO:     Waiting for application startup.
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 20:35:58,417 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 20:35:58,417 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 20:35:58,417 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 20:35:58,417 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 20:35:58,417 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 20:35:58,797 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 20:35:58,797 - root - INFO - Logflare initialized
2025-07-07 20:35:58,797 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 20:35:58,797 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 20:35:58,810 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:35:59,661 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 20:35:59,949 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,052 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,186 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,316 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,410 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,471 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,538 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,539 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 20:36:00,539 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 20:36:00,543 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 20:36:00,544 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 20:36:00,544 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 20:36:00,545 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 20:36:00,557 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:36:00,557 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:36:00,557 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 20:36:00,560 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 20:36:00,560 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 20:36:00,561 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 20:36:00,707 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,894 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:00,953 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,099 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,233 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,311 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,448 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,526 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 20:36:01,528 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 20:36:01,528 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 20:36:01,532 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 20:36:01,532 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 20:36:01,532 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 20:36:01,533 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 20:36:01,546 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 20:36:01,546 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 20:36:01,547 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 20:36:02,394 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 20:36:02,394 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 20:36:02,549 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T00%3A36%3A01.533327 "HTTP/2 200 OK"
2025-07-07 20:38:42,954 - app.middleware.monitoring_middleware - INFO - Request started - ID: 268811b4-dec5-41ae-bb21-c440b7c46e19, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:38:43,273 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:38:43,456 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:38:43,460 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:38:43,463 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 268811b4-dec5-41ae-bb21-c440b7c46e19, Status: 200, Duration: 509.65ms
2025-07-07 20:38:43,464 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "268811b4-dec5-41ae-bb21-c440b7c46e19", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 509.65, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:38:43.464325Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:54955 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:38:43,904 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 20:38:43,906 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 20:38:43,906 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 20:38:44,366 - app.middleware.monitoring_middleware - INFO - Request started - ID: 597930c0-b676-412a-8143-42bd1099fee8, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:38:44,581 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:38:44,695 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:38:44,696 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:38:44,697 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 597930c0-b676-412a-8143-42bd1099fee8, Status: 200, Duration: 331.45ms
2025-07-07 20:38:44,697 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "597930c0-b676-412a-8143-42bd1099fee8", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 331.45, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:38:44.697924Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:54955 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:38:50,498 - app.middleware.monitoring_middleware - INFO - Request started - ID: a0c5ea2f-971e-4951-9334-b970fd88a08f, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:38:50,778 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:38:50,991 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:38:50,992 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:38:50,993 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a0c5ea2f-971e-4951-9334-b970fd88a08f, Status: 200, Duration: 495.46ms
2025-07-07 20:38:50,993 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a0c5ea2f-971e-4951-9334-b970fd88a08f", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 495.46, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:38:50.993796Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:55012 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:40:27,540 - app.middleware.monitoring_middleware - INFO - Request started - ID: 05db011f-f7b1-4904-b87f-f9300748c07e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:40:27,804 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:40:27,970 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:40:27,988 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:40:27,990 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 05db011f-f7b1-4904-b87f-f9300748c07e, Status: 200, Duration: 450.21ms
2025-07-07 20:40:27,992 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "05db011f-f7b1-4904-b87f-f9300748c07e", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 450.21, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:40:27.991677Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:55436 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:41:01,588 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:42:10,998 - app.middleware.monitoring_middleware - INFO - Request started - ID: de250d9e-a032-49ed-b0a6-6392a4dfad0f, Method: GET, Path: /api/auth/me, User: None
2025-07-07 20:42:11,273 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-07 20:42:11,572 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=%2A&id=eq.fe99519c-29cd-4817-8ca4-d94b58ed884a "HTTP/2 200 OK"
2025-07-07 20:42:11,580 - app.api.supabase_auth - INFO - Profile accessed: <EMAIL>
2025-07-07 20:42:11,581 - app.middleware.monitoring_middleware - INFO - Request completed - ID: de250d9e-a032-49ed-b0a6-6392a4dfad0f, Status: 200, Duration: 582.91ms
2025-07-07 20:42:11,582 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "de250d9e-a032-49ed-b0a6-6392a4dfad0f", "method": "GET", "path": "/api/auth/me", "status_code": 200, "duration_ms": 582.91, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T00:42:11.582517Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:55914 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-07 20:46:01,590 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:51:01,609 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 20:56:01,607 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:01:01,630 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:06:01,641 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:11:01,644 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:16:01,675 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:21:01,685 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:26:01,685 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:31:01,686 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:36:01,690 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:36:02,880 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T01%3A36%3A02.511509 "HTTP/2 200 OK"
2025-07-07 21:41:01,693 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:46:01,642 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:51:01,639 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 21:56:01,637 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:01:01,639 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:06:01,640 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:11:01,646 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:16:01,701 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:21:01,701 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:26:01,703 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:31:01,707 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:36:01,709 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:36:03,386 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T02%3A36%3A02.833659 "HTTP/2 200 OK"
2025-07-07 22:41:01,713 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:46:01,795 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:51:01,810 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 22:56:01,820 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:01:01,835 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:06:01,842 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:11:01,964 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:16:01,963 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:18:10,683 - app.middleware.monitoring_middleware - INFO - Request started - ID: 6b4d8adb-1f27-4c80-a701-013740e552dd, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:18:11,263 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:18:11,286 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:18:11,287 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:18:11,296 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:18:11,304 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:11.303053Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "6b4d8adb-1f27-4c80-a701-013740e552dd"}
2025-07-07 23:18:11,342 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 6b4d8adb-1f27-4c80-a701-013740e552dd, Status: 401, Duration: 660.55ms
2025-07-07 23:18:11,344 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "6b4d8adb-1f27-4c80-a701-013740e552dd", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 660.55, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:11.343947Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63121 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:18:12,156 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:18:12,159 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:18:12,160 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:18:12,167 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:18:12,169 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:18:12,169 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:18:24,437 - app.middleware.monitoring_middleware - INFO - Request started - ID: cf950561-274e-4d05-b841-36a6ca6c9676, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:18:24,717 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:18:24,725 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:18:24,726 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:18:24,731 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:18:24,733 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:24.733715Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "cf950561-274e-4d05-b841-36a6ca6c9676"}
2025-07-07 23:18:24,736 - app.middleware.monitoring_middleware - INFO - Request completed - ID: cf950561-274e-4d05-b841-36a6ca6c9676, Status: 401, Duration: 299.28ms
2025-07-07 23:18:24,737 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "cf950561-274e-4d05-b841-36a6ca6c9676", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 299.28, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:24.736927Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63205 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:18:24,994 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:18:24,996 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:18:24,997 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:18:25,003 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:18:25,012 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:18:25,012 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:18:41,885 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9ee3997e-c315-40a5-a526-97d7f1026743, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:18:42,099 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:18:42,101 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:18:42,102 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:18:42,102 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:18:42,103 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:42.102983Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "9ee3997e-c315-40a5-a526-97d7f1026743"}
2025-07-07 23:18:42,103 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9ee3997e-c315-40a5-a526-97d7f1026743, Status: 401, Duration: 218.59ms
2025-07-07 23:18:42,103 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9ee3997e-c315-40a5-a526-97d7f1026743", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 218.59, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:42.103948Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63301 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:18:52,405 - app.middleware.monitoring_middleware - INFO - Request started - ID: 179a3da1-b017-4ade-9d7c-312088e0f0c7, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:18:52,658 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:18:52,659 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:18:52,659 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:18:52,660 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:18:52,660 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:52.660311Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "179a3da1-b017-4ade-9d7c-312088e0f0c7"}
2025-07-07 23:18:52,660 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 179a3da1-b017-4ade-9d7c-312088e0f0c7, Status: 401, Duration: 255.2ms
2025-07-07 23:18:52,660 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "179a3da1-b017-4ade-9d7c-312088e0f0c7", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 255.2, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:18:52.660686Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63371 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:19:01,774 - app.middleware.monitoring_middleware - INFO - Request started - ID: a30d80d2-d036-4ec9-86ba-cffbfb2b0fda, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:19:01,953 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:19:01,955 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:19:01,955 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:19:01,956 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:19:01,956 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:19:01.956626Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "a30d80d2-d036-4ec9-86ba-cffbfb2b0fda"}
2025-07-07 23:19:01,957 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a30d80d2-d036-4ec9-86ba-cffbfb2b0fda, Status: 401, Duration: 183.16ms
2025-07-07 23:19:01,957 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a30d80d2-d036-4ec9-86ba-cffbfb2b0fda", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 183.16, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:19:01.957539Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:63425 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:21:01,973 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:22:08,743 - app.middleware.monitoring_middleware - INFO - Request started - ID: a60c9d28-1ec2-4974-a249-d2d7a3d19b80, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:22:09,092 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:22:09,094 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:22:09,094 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:22:09,095 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:22:09,096 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:22:09.096008Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "a60c9d28-1ec2-4974-a249-d2d7a3d19b80"}
2025-07-07 23:22:09,096 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a60c9d28-1ec2-4974-a249-d2d7a3d19b80, Status: 401, Duration: 353.15ms
2025-07-07 23:22:09,096 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a60c9d28-1ec2-4974-a249-d2d7a3d19b80", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 353.15, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:22:09.096942Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:64362 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:22:09,311 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:22:09,314 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:22:09,314 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:26:01,982 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:28:45,902 - app.middleware.monitoring_middleware - INFO - Request started - ID: 9624ce01-fdf3-4ede-9dae-c5f7bcd4ee26, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:28:46,274 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:28:46,280 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:28:46,280 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:28:46,283 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:28:46,284 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:28:46.284661Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "9624ce01-fdf3-4ede-9dae-c5f7bcd4ee26"}
2025-07-07 23:28:46,285 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 9624ce01-fdf3-4ede-9dae-c5f7bcd4ee26, Status: 401, Duration: 384.4ms
2025-07-07 23:28:46,285 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "9624ce01-fdf3-4ede-9dae-c5f7bcd4ee26", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 384.4, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:28:46.285807Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:49810 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:28:46,784 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:28:46,785 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:28:46,785 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:31:01,987 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:36:02,010 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:36:04,067 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T03%3A36%3A03.447205 "HTTP/2 200 OK"
2025-07-07 23:39:44,488 - app.middleware.monitoring_middleware - INFO - Request started - ID: a35d975a-56a3-47e3-a0c6-70ea79492be1, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:39:44,790 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:39:44,795 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:39:44,795 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:39:44,796 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:39:44,799 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:39:44.799365Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "a35d975a-56a3-47e3-a0c6-70ea79492be1"}
2025-07-07 23:39:44,806 - app.middleware.monitoring_middleware - INFO - Request completed - ID: a35d975a-56a3-47e3-a0c6-70ea79492be1, Status: 401, Duration: 317.51ms
2025-07-07 23:39:44,807 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "a35d975a-56a3-47e3-a0c6-70ea79492be1", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 317.51, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:39:44.806931Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:52923 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:39:45,165 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:39:45,167 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:39:45,167 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:41:02,020 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
WARNING:  WatchFiles detected changes in 'app/models/supabase_models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 23:43:12,060 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 23:43:12,067 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-07 23:43:12,068 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-07 23:43:12,087 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 23:43:12,093 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 23:43:12,095 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 23:43:12,095 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [67887]
INFO:     Started server process [99621]
INFO:     Waiting for application startup.
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 23:43:16,086 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 23:43:16,087 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 23:43:16,087 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 23:43:16,087 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 23:43:16,087 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 23:43:16,578 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 23:43:16,579 - root - INFO - Logflare initialized
2025-07-07 23:43:16,579 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 23:43:16,579 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 23:43:16,594 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:43:17,385 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 23:43:17,670 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:17,804 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:17,954 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,135 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,197 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,284 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,370 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,372 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 23:43:18,372 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 23:43:18,376 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 23:43:18,376 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 23:43:18,377 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 23:43:18,378 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 23:43:18,390 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:43:18,390 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:43:18,390 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 23:43:18,395 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 23:43:18,395 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 23:43:18,395 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 23:43:18,466 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,655 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,731 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,808 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,884 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:18,964 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:19,076 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:19,176 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:43:19,178 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 23:43:19,178 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 23:43:19,181 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 23:43:19,181 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 23:43:19,182 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 23:43:19,182 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 23:43:19,193 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:43:19,194 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:43:19,195 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 23:43:19,782 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 23:43:19,782 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 23:43:19,929 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T03%3A43%3A19.182253 "HTTP/2 200 OK"
WARNING:  WatchFiles detected changes in 'app/api/market_analysis.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 23:45:36,248 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 23:46:36,250 - asyncpg.pool - WARNING - Pool.close() is taking over 60 seconds to complete. Check if you have any unreleased connections left. Use asyncio.wait_for() to set a timeout for Pool.close().
2025-07-07 23:46:36,251 - app.utils.supabase.supabase_database - ERROR - ❌ Database cleanup error
Traceback (most recent call last):
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/utils/supabase/supabase_database.py", line 193, in cleanup_database
    await db.close_pool()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/app/utils/supabase/supabase_database.py", line 65, in close_pool
    await self.db_pool.close()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/pool.py", line 940, in close
    await asyncio.gather(*close_coros)
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/pool.py", line 247, in close
    await self._con.close()
  File "/Volumes/Baby_SSD/github/AI projects/publish-ai/.venv/lib/python3.11/site-packages/asyncpg/connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg/protocol/protocol.pyx", line 642, in close
TimeoutError
2025-07-07 23:46:36,253 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 23:46:36,254 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 23:46:36,254 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 23:46:36,254 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [99621]
WARNING:  WatchFiles detected changes in 'app/api/prediction_accuracy.py', 'app/main_supabase.py'. Reloading...
INFO:     Started server process [6194]
INFO:     Waiting for application startup.
2025-07-07 23:46:39,825 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 23:46:39,825 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 23:46:39,825 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 23:46:39,825 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 23:46:39,826 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 23:46:39,826 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 23:46:39,826 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 23:46:39,826 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 23:46:39,826 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 23:46:39,826 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 23:46:39,826 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 23:46:40,281 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 23:46:40,281 - root - INFO - Logflare initialized
2025-07-07 23:46:40,281 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 23:46:40,281 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 23:46:40,294 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:40,868 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 23:46:41,178 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,312 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,438 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,567 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,690 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,814 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,963 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:41,965 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 23:46:41,965 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 23:46:41,969 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 23:46:41,970 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 23:46:41,971 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 23:46:41,971 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 23:46:41,971 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 23:46:41,971 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 23:46:41,971 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 23:46:41,971 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 23:46:41,972 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 23:46:41,984 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:41,984 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:46:41,984 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 23:46:41,989 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 23:46:41,989 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 23:46:41,989 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 23:46:42,056 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,313 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,447 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,568 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,688 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,753 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:42,881 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:43,016 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:43,018 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 23:46:43,018 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 23:46:43,021 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 23:46:43,021 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 23:46:43,021 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 23:46:43,021 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 23:46:43,033 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:43,033 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:46:43,034 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
INFO:     Started server process [6424]
INFO:     Waiting for application startup.
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 23:46:46,295 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 23:46:46,295 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 23:46:46,295 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 23:46:46,295 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 23:46:46,295 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 23:46:46,794 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 23:46:46,794 - root - INFO - Logflare initialized
2025-07-07 23:46:46,794 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 23:46:46,794 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 23:46:46,829 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:47,410 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 23:46:47,618 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:47,758 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:47,897 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,026 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,096 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,163 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,293 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,295 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 23:46:48,296 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 23:46:48,300 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 23:46:48,301 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 23:46:48,302 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 23:46:48,302 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 23:46:48,303 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 23:46:48,303 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 23:46:48,303 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 23:46:48,303 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 23:46:48,303 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 23:46:48,316 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:48,316 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:46:48,316 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 23:46:48,320 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 23:46:48,320 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 23:46:48,321 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 23:46:48,463 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,769 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:48,929 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,059 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,140 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,273 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,360 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,487 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:46:49,489 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 23:46:49,489 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 23:46:49,493 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 23:46:49,493 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 23:46:49,493 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 23:46:49,493 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 23:46:49,505 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:46:49,505 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:46:49,506 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 23:46:50,078 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 23:46:50,078 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 23:46:50,287 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T03%3A46%3A49.493618 "HTTP/2 200 OK"
2025-07-07 23:51:49,571 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-07 23:55:14,039 - app.middleware.monitoring_middleware - INFO - Request started - ID: e661e53f-a456-411f-a83c-b786f8e52952, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:55:14,514 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:55:14,522 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:55:14,522 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:55:14,525 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:55:14,527 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:55:14.527432Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "e661e53f-a456-411f-a83c-b786f8e52952"}
2025-07-07 23:55:14,532 - app.middleware.monitoring_middleware - INFO - Request completed - ID: e661e53f-a456-411f-a83c-b786f8e52952, Status: 401, Duration: 496.62ms
2025-07-07 23:55:14,532 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "e661e53f-a456-411f-a83c-b786f8e52952", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 496.62, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:55:14.532835Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:57447 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:55:14,959 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:55:14,961 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:55:14,961 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
WARNING:  WatchFiles detected changes in 'app/models/supabase_models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 23:55:19,608 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-07 23:55:19,610 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-07 23:55:19,610 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-07 23:55:19,618 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-07 23:55:19,621 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-07 23:55:19,621 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-07 23:55:19,621 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [6424]
INFO:     Started server process [22065]
INFO:     Waiting for application startup.
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-07 23:55:26,429 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-07 23:55:26,429 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-07 23:55:26,429 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-07 23:55:26,429 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-07 23:55:26,429 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-07 23:55:26,939 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-07 23:55:26,940 - root - INFO - Logflare initialized
2025-07-07 23:55:26,940 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-07 23:55:26,940 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-07 23:55:26,952 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:55:27,809 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-07 23:55:28,056 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,182 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,314 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,430 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,542 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,651 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,707 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:28,710 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-07 23:55:28,710 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-07 23:55:28,715 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-07 23:55:28,715 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-07 23:55:28,716 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-07 23:55:28,716 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-07 23:55:28,716 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-07 23:55:28,716 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-07 23:55:28,717 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-07 23:55:28,717 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-07 23:55:28,717 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-07 23:55:28,729 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:55:28,730 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:55:28,730 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-07 23:55:28,734 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-07 23:55:28,734 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-07 23:55:28,734 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-07 23:55:28,947 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,194 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,259 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,369 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,475 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,547 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,662 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,866 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-07 23:55:29,868 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-07 23:55:29,868 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-07 23:55:29,872 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-07 23:55:29,872 - app.ml.verl_integration - INFO - VERL integration started
2025-07-07 23:55:29,872 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-07 23:55:29,872 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-07 23:55:29,883 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-07 23:55:29,883 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-07 23:55:29,884 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-07 23:55:29,886 - app.middleware.monitoring_middleware - INFO - Request started - ID: 0de613af-441e-4af7-ba45-217cd82bc52e, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:55:30,116 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:55:30,117 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:55:30,117 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:55:30,118 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:55:30,118 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:55:30.118253Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "0de613af-441e-4af7-ba45-217cd82bc52e"}
2025-07-07 23:55:30,119 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 0de613af-441e-4af7-ba45-217cd82bc52e, Status: 401, Duration: 233.21ms
2025-07-07 23:55:30,119 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "0de613af-441e-4af7-ba45-217cd82bc52e", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 233.21, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:55:30.119470Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:57520 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-07 23:55:30,316 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-07 23:55:30,318 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-07 23:55:30,318 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-07 23:55:30,918 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-07 23:55:30,918 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-07 23:55:31,157 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T03%3A55%3A29.872464 "HTTP/2 200 OK"
2025-07-07 23:56:58,962 - app.middleware.monitoring_middleware - INFO - Request started - ID: 4c5d14a2-b84e-40bb-9b1a-b60500721c6d, Method: GET, Path: /api/auth/me, User: None
2025-07-07 23:56:59,318 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-07 23:56:59,319 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-07 23:56:59,320 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-07 23:56:59,323 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-07 23:56:59,323 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:56:59.323746Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "4c5d14a2-b84e-40bb-9b1a-b60500721c6d"}
2025-07-07 23:56:59,329 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 4c5d14a2-b84e-40bb-9b1a-b60500721c6d, Status: 401, Duration: 366.42ms
2025-07-07 23:56:59,329 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "4c5d14a2-b84e-40bb-9b1a-b60500721c6d", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 366.42, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T03:56:59.329387Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:58014 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-08 00:00:29,886 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:05:29,901 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:10:29,914 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:15:29,930 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:20:30,003 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:25:30,022 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:30:30,084 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:32:08,633 - app.middleware.monitoring_middleware - INFO - Request started - ID: 467e5115-423b-4815-88c7-fd43a912179f, Method: GET, Path: /api/auth/me, User: None
2025-07-08 00:32:09,138 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-08 00:32:09,142 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-08 00:32:09,142 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-08 00:32:09,144 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-08 00:32:09,147 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T04:32:09.146968Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "467e5115-423b-4815-88c7-fd43a912179f"}
2025-07-08 00:32:09,155 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 467e5115-423b-4815-88c7-fd43a912179f, Status: 401, Duration: 522.71ms
2025-07-08 00:32:09,156 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "467e5115-423b-4815-88c7-fd43a912179f", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 522.71, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T04:32:09.156013Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:51237 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-08 00:32:09,482 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-08 00:32:09,485 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-08 00:32:09,485 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-08 00:35:30,098 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:40:30,106 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:45:30,123 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:50:30,166 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:55:30,188 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 00:55:31,648 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T04%3A55%3A31.218109 "HTTP/2 200 OK"
2025-07-08 01:00:06,836 - app.middleware.monitoring_middleware - INFO - Request started - ID: 62cd8c23-a881-446a-afbd-019a84606def, Method: GET, Path: /api/auth/me, User: None
2025-07-08 01:00:07,437 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-08 01:00:07,440 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-08 01:00:07,441 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-08 01:00:07,443 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/auth/me, IP: 127.0.0.1
2025-07-08 01:00:07,445 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/auth/me", "client_ip": "127.0.0.1", "user_agent": "node", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T05:00:07.444614Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "62cd8c23-a881-446a-afbd-019a84606def"}
2025-07-08 01:00:07,451 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 62cd8c23-a881-446a-afbd-019a84606def, Status: 401, Duration: 615.73ms
2025-07-08 01:00:07,452 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "62cd8c23-a881-446a-afbd-019a84606def", "method": "GET", "path": "/api/auth/me", "status_code": 401, "duration_ms": 615.73, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T05:00:07.451965Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:60223 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
2025-07-08 01:00:07,470 - app.middleware.monitoring_middleware - INFO - Request started - ID: 965fc765-d486-497b-b200-8ff7bab31b5f, Method: GET, Path: /api/users/preferences, User: None
2025-07-08 01:00:07,620 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/auth/v1/user "HTTP/2 403 Forbidden"
2025-07-08 01:00:07,621 - app.auth.supabase_auth - ERROR - Failed to get current user: invalid JWT: unable to parse or verify signature, token has invalid claims: token is expired
2025-07-08 01:00:07,621 - app.auth.supabase_auth - ERROR - Authentication error: 401: Could not validate credentials
2025-07-08 01:00:07,623 - app.middleware.monitoring_middleware - WARNING - Authentication failure - Path: /api/users/preferences, IP: 127.0.0.1
2025-07-08 01:00:07,623 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "auth_failure", "path": "/api/users/preferences", "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T05:00:07.623447Z", "service": "publish-ai", "version": "unknown", "environment": "development", "request_id": "965fc765-d486-497b-b200-8ff7bab31b5f"}
2025-07-08 01:00:07,624 - app.middleware.monitoring_middleware - INFO - Request completed - ID: 965fc765-d486-497b-b200-8ff7bab31b5f, Status: 401, Duration: 153.31ms
2025-07-08 01:00:07,624 - app.monitoring.monitoring_setup - INFO - {"event_type": "system_event", "system_event_type": "request_completed", "request_id": "965fc765-d486-497b-b200-8ff7bab31b5f", "method": "GET", "path": "/api/users/preferences", "status_code": 401, "duration_ms": 153.31, "user_id": null, "event": "System event", "logger": "app.monitoring.monitoring_setup", "level": "info", "timestamp": "2025-07-08T05:00:07.624244Z", "service": "publish-ai", "version": "unknown", "environment": "development"}
INFO:     127.0.0.1:60230 - "GET /api/users/preferences HTTP/1.1" 401 Unauthorized
2025-07-08 01:00:07,931 - httpx - INFO - HTTP Request: POST https://api.logflare.app/logs "HTTP/1.1 401 Unauthorized"
2025-07-08 01:00:07,936 - app.utils.logflare_client - ERROR - Logflare error 401: {"error":"Unauthorized"}
2025-07-08 01:00:07,936 - app.utils.logflare_client - ERROR - Logflare log failed after 3 attempts: Event: system_event
2025-07-08 01:00:30,200 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
2025-07-08 01:05:30,255 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
WARNING:  WatchFiles detected changes in 'app/main_supabase.py'. Reloading...
2025-07-08 01:10:30,291 - app.ml.verl_integration - WARNING - VERL health check failed: {'status': 'unreachable', 'error': "Cannot connect to host localhost:8001 ssl:default [Multiple exceptions: [Errno 61] Connect call failed ('::1', 8001, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 8001)]"}
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-08 01:10:30,569 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-08 01:10:30,664 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-08 01:10:30,670 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-08 01:10:30,762 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-08 01:10:30,809 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-08 01:10:30,813 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-08 01:10:30,816 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [22065]
INFO:     Started server process [68312]
INFO:     Waiting for application startup.
2025-07-08 01:10:38,987 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/analytics/*
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/trends/*
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/books/*/analytics
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/users/*/analytics
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/publications/*
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /health
2025-07-08 01:10:38,988 - app.middleware.cache_middleware - INFO - Added cache rule for pattern: /api/monitoring/*
2025-07-08 01:10:38,988 - app.main_supabase - INFO - 🚀 Starting AI E-book Generator with Supabase...
2025-07-08 01:10:38,988 - app.main_supabase - INFO - ⚙️ Validating configuration...
2025-07-08 01:10:38,988 - app.main_supabase - INFO - ✅ Critical configuration validated
2025-07-08 01:10:38,988 - app.main_supabase - INFO - 🔍 Initializing monitoring systems...
2025-07-08 01:10:39,713 - app.utils.logflare_client - INFO - Logflare client initialized successfully
2025-07-08 01:10:39,713 - root - INFO - Logflare initialized
2025-07-08 01:10:39,713 - app.main_supabase - INFO - ✅ Monitoring systems initialized
2025-07-08 01:10:39,713 - app.main_supabase - INFO - 🗄️ Initializing Supabase database connection...
2025-07-08 01:10:39,727 - app.utils.supabase.supabase_database - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-08 01:10:40,755 - app.utils.supabase.supabase_database - INFO - ✅ PostgreSQL connection pool initialized
2025-07-08 01:10:41,235 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,351 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,467 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,634 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,765 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,879 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,937 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:41,938 - app.utils.supabase.supabase_database - INFO - ✅ Database initialization complete
2025-07-08 01:10:41,938 - app.main_supabase - INFO - 💾 Initializing cache systems...
2025-07-08 01:10:41,942 - app.cache.redis_cache - INFO - Disabled stop-writes-on-bgsave-error to handle RDB issues
2025-07-08 01:10:41,943 - app.cache.redis_cache - INFO - Disabled RDB snapshots for development
2025-07-08 01:10:41,944 - app.cache.redis_cache - INFO - Redis cache initialized successfully
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_0_users:*
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_1_books:*
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_2_publications:*
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_3_trends:*
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Added invalidation rule: rule_4_analytics:*
2025-07-08 01:10:41,945 - app.cache.cache_invalidation - INFO - Cache invalidation manager initialized
2025-07-08 01:10:41,957 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-08 01:10:41,957 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-08 01:10:41,957 - app.cache.cache_invalidation - INFO - Started monitoring Supabase events for cache invalidation
2025-07-08 01:10:41,963 - app.cache.cdn_support - INFO - Built asset manifest with 4 assets
2025-07-08 01:10:41,963 - app.cache.cdn_support - INFO - CDN asset manager initialized
2025-07-08 01:10:41,963 - app.main_supabase - INFO - ✅ Cache systems initialized
2025-07-08 01:10:42,161 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,445 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/users?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,571 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/books?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,685 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/publications?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,737 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/sales_data?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,859 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:42,970 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/trend_analyses?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:43,088 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/verl_training_jobs?select=id&limit=1 "HTTP/2 200 OK"
2025-07-08 01:10:43,089 - app.main_supabase - INFO - ✅ Supabase database initialized successfully
2025-07-08 01:10:43,090 - app.main_supabase - INFO - 🧠 Initializing VERL integration...
2025-07-08 01:10:43,093 - app.ml.verl_integration - INFO - VERL integration initialized successfully with Redis
2025-07-08 01:10:43,093 - app.ml.verl_integration - INFO - VERL integration started
2025-07-08 01:10:43,093 - app.main_supabase - INFO - ✅ VERL integration initialized successfully
2025-07-08 01:10:43,093 - app.main_supabase - INFO - 🎉 Application startup complete!
2025-07-08 01:10:43,105 - app.utils.supabase.supabase_client - INFO - ✅ Supabase client initialized: https://nutrbmmtnvkuxygtgrch.supabase.co
2025-07-08 01:10:43,105 - app.utils.supabase.supabase_client - INFO - Supabase fallback mode deactivated
2025-07-08 01:10:43,106 - app.ml.verl_integration - INFO - 🔍 Starting VERL background monitor...
INFO:     Application startup complete.
2025-07-08 01:10:43,800 - app.utils.supabase.supabase_client - INFO - ✅ Database connection pool initialized (2-20 connections)
2025-07-08 01:10:43,800 - app.utils.supabase.supabase_client - INFO - 🔗 Shared Supabase client singleton initialized
2025-07-08 01:10:44,014 - httpx - INFO - HTTP Request: GET https://nutrbmmtnvkuxygtgrch.supabase.co/rest/v1/feedback_metrics?select=id&created_at=gte.2025-07-01T05%3A10%3A43.093533 "HTTP/2 200 OK"
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-08 01:11:17,118 - app.main_supabase - INFO - 👋 Shutting down AI E-book Generator...
2025-07-08 01:11:17,160 - app.utils.supabase.supabase_database - INFO - ✅ Database connection pool closed
2025-07-08 01:11:17,160 - app.utils.supabase.supabase_database - INFO - ✅ Database cleanup complete
2025-07-08 01:11:17,161 - app.main_supabase - INFO - ✅ Cache systems cleaned up
2025-07-08 01:11:17,161 - app.ml.verl_integration - ERROR - Notification listener error: Connection closed by server.
2025-07-08 01:11:17,161 - app.main_supabase - INFO - ✅ VERL integration cleaned up
2025-07-08 01:11:17,161 - app.main_supabase - INFO - ✅ Application shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [68312]
INFO:     Stopping reloader process [34534]
