# Microservices Architecture

This directory contains the microservices architecture implementation for the AI Publishing Platform.

## Service Overview

### Core Services

1. **VERL Training Service** (`verl-training/`)
   - Dedicated VERL reinforcement learning training
   - Isolated GPU resources and dependencies
   - Communicates via Redis message queues

2. **AI Agent Execution Service** (`agent-executor/`)
   - Dedicated PydanticAI agent execution
   - Resource isolation and scaling
   - Agent lifecycle management

3. **Event Bus Service** (`event-bus/`)
   - Central message routing and event handling
   - Redis-based pub/sub with persistence
   - Dead letter queues and retry logic

4. **Service Discovery** (`service-discovery/`)
   - Consul-based service registration
   - Health checking and load balancing
   - Configuration management

## Architecture Principles

- **Event-Driven**: Services communicate via events
- **Fault Tolerant**: Circuit breakers and graceful degradation
- **Scalable**: Horizontal scaling with load balancing
- **Observable**: Comprehensive monitoring and tracing
- **Secure**: mTLS and API key authentication

## Deployment

Each service includes:
- Dockerfile for containerization
- docker-compose.yml for local development
- <PERSON><PERSON><PERSON><PERSON> manifests for production
- Health checks and monitoring configs

## Getting Started

```bash
# Start all services
docker-compose up --build

# Scale specific service
docker-compose up --scale agent-executor=3

# Check service health
curl http://localhost:8500/v1/health/checks
```