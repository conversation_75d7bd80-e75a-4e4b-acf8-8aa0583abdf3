# AI Model Marketplace Service Dependencies
# Production-ready AI model management and marketplace platform

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database & Storage
asyncpg==0.29.0
supabase==2.3.0
redis[hiredis]==5.0.1
sqlalchemy[asyncio]==2.0.23

# AI & Machine Learning
torch==2.1.1
torchvision==0.16.1
transformers==4.36.0
huggingface-hub==0.19.4
safetensors==0.4.1
accelerate==0.25.0
bitsandbytes==0.41.3

# Model Serving & Optimization
optimum==1.16.0
onnx==1.15.0
onnxruntime==1.16.3
tensorrt==8.6.1.post1
vllm==0.2.7
text-generation-inference==1.4.0

# Model Security & Validation
model-scanner==1.0.0
bandit==1.7.5
safety==2.3.4
semgrep==1.45.0

# File Processing & Validation
python-multipart==0.0.6
aiofiles==23.2.1
python-magic==0.4.27
hashlib-compat==1.0.1

# Monitoring & Observability
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# Security & Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7
pycryptodome==3.19.0

# HTTP & API
httpx==0.25.2
requests==2.31.0
websockets==12.0

# Data Processing
pandas==2.1.4
numpy==1.25.2
pillow==10.1.0
scipy==1.11.4

# Async & Concurrency
asyncio-mqtt==0.16.1
aioredis==2.0.1
celery[redis]==5.3.4

# Model Registry & MLOps
mlflow==2.8.1
wandb==0.16.0
dvc==3.28.0
zenml==0.51.0

# Containerization & Deployment
docker==6.1.3
kubernetes==28.1.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0