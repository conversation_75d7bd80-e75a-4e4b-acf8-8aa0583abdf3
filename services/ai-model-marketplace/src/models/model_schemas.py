"""
Pydantic schemas for AI Model Marketplace.

Defines data models for:
- Model registration and metadata
- Model deployment and versioning
- API request/response schemas
- Model marketplace operations
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from pydantic.networks import HttpUrl


class ModelType(str, Enum):
    """Supported AI model types."""
    LANGUAGE_MODEL = "language_model"
    VISION_MODEL = "vision_model"
    AUDIO_MODEL = "audio_model"
    MULTIMODAL = "multimodal"
    CLASSIFIER = "classifier"
    REGRESSION = "regression"
    GENERATIVE = "generative"
    EMBEDDING = "embedding"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    TIME_SERIES = "time_series"
    RECOMMENDATION = "recommendation"
    CUSTOM = "custom"
    OTHER = "other"


class ModelFormat(str, Enum):
    """Supported model file formats."""
    PYTORCH = "pytorch"
    ONNX = "onnx"
    TENSORFLOW = "tensorflow"
    SAFETENSORS = "safetensors"
    HUGGINGFACE = "huggingface"
    CUSTOM = "custom"


class ModelVisibility(str, Enum):
    """Model visibility settings."""
    PUBLIC = "public"
    PRIVATE = "private"
    ORGANIZATION = "organization"


class ModelStatus(str, Enum):
    """Model processing and validation status."""
    PENDING = "pending"
    VALIDATING = "validating"
    ACTIVE = "active"
    FAILED = "failed"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


class DeploymentStatus(str, Enum):
    """Model deployment status."""
    PENDING = "pending"
    DEPLOYING = "deploying"
    RUNNING = "running"
    SCALING = "scaling"
    STOPPED = "stopped"
    FAILED = "failed"
    TERMINATING = "terminating"


class ResourceType(str, Enum):
    """Compute resource types."""
    CPU = "cpu"
    GPU_T4 = "gpu_t4"
    GPU_V100 = "gpu_v100"
    GPU_A100 = "gpu_a100"
    GPU_H100 = "gpu_h100"
    TPU_V3 = "tpu_v3"
    TPU_V4 = "tpu_v4"


# Base schemas

class BaseResponse(BaseModel):
    """Base response schema."""
    success: bool
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationInfo(BaseModel):
    """Pagination information."""
    total_count: int
    limit: int
    offset: int
    has_more: bool


# Model schemas

class ModelMetadata(BaseModel):
    """Comprehensive model metadata."""
    framework: Optional[str] = None
    model_size_mb: Optional[float] = None
    parameters_count: Optional[int] = None
    input_format: Optional[str] = None
    output_format: Optional[str] = None
    supported_tasks: List[str] = Field(default_factory=list)
    languages: List[str] = Field(default_factory=list)
    dataset_info: Optional[Dict[str, Any]] = None
    performance_metrics: Optional[Dict[str, float]] = None
    training_details: Optional[Dict[str, Any]] = None
    hardware_requirements: Optional[Dict[str, Any]] = None
    inference_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    custom_metadata: Optional[Dict[str, Any]] = None


class ModelFile(BaseModel):
    """Model file information."""
    filename: str
    file_path: str
    file_size: int
    file_hash: str
    uploaded_at: datetime
    format: ModelFormat
    compressed: bool = False
    encryption_enabled: bool = False


class ModelVersion(BaseModel):
    """Model version information."""
    id: str
    model_id: str
    version: str
    changelog: Optional[str] = None
    file_info: Optional[Dict[str, Any]] = None
    metadata: Optional[ModelMetadata] = None
    created_at: datetime
    is_latest: bool = False


class ModelRegistration(BaseModel):
    """Model registration request schema."""
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=10, max_length=5000)
    model_type: ModelType
    format: ModelFormat
    visibility: ModelVisibility = ModelVisibility.PRIVATE
    tags: List[str] = Field(default_factory=list, max_items=20)
    license: str = Field(default="custom", max_length=100)
    huggingface_model_id: Optional[str] = None
    repository_url: Optional[HttpUrl] = None
    documentation_url: Optional[HttpUrl] = None
    paper_url: Optional[HttpUrl] = None
    
    @validator("tags")
    def validate_tags(cls, v):
        """Validate tags."""
        if len(v) > 20:
            raise ValueError("Maximum 20 tags allowed")
        for tag in v:
            if len(tag) > 50:
                raise ValueError("Tag length cannot exceed 50 characters")
            if not tag.replace("-", "").replace("_", "").isalnum():
                raise ValueError("Tags can only contain alphanumeric characters, hyphens, and underscores")
        return v
    
    @validator("name")
    def validate_name(cls, v):
        """Validate model name."""
        if not v.replace("-", "").replace("_", "").replace(" ", "").isalnum():
            raise ValueError("Model name can only contain alphanumeric characters, spaces, hyphens, and underscores")
        return v


class ModelUpdateRequest(BaseModel):
    """Model update request schema."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=10, max_length=5000)
    tags: Optional[List[str]] = Field(None, max_items=20)
    license: Optional[str] = Field(None, max_length=100)
    visibility: Optional[ModelVisibility] = None
    repository_url: Optional[HttpUrl] = None
    documentation_url: Optional[HttpUrl] = None
    paper_url: Optional[HttpUrl] = None
    metadata: Optional[Dict[str, Any]] = None


class HuggingFaceImportRequest(BaseModel):
    """HuggingFace model import request."""
    model_name: str = Field(..., min_length=1, max_length=100)
    huggingface_model_id: str = Field(..., min_length=1)
    visibility: ModelVisibility = ModelVisibility.PRIVATE
    
    @validator("huggingface_model_id")
    def validate_hf_model_id(cls, v):
        """Validate HuggingFace model ID format."""
        if "/" not in v:
            raise ValueError("HuggingFace model ID must be in format 'organization/model'")
        return v


class ModelVersionRequest(BaseModel):
    """Model version creation request."""
    version: str = Field(..., min_length=1, max_length=50)
    changelog: Optional[str] = Field(None, max_length=2000)
    
    @validator("version")
    def validate_version(cls, v):
        """Validate version string."""
        # Basic semver validation
        import re
        pattern = r'^[0-9]+\.[0-9]+\.[0-9]+(?:-[0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*)?(?:\+[0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*)?$'
        if not re.match(pattern, v):
            raise ValueError("Version must follow semantic versioning (e.g., 1.0.0)")
        return v


# Deployment schemas

class ResourceRequirements(BaseModel):
    """Compute resource requirements."""
    cpu_cores: float = Field(default=1.0, ge=0.1, le=64.0)
    memory_gb: float = Field(default=2.0, ge=0.5, le=512.0)
    gpu_count: int = Field(default=0, ge=0, le=8)
    gpu_type: Optional[ResourceType] = None
    storage_gb: float = Field(default=10.0, ge=1.0, le=1000.0)
    bandwidth_mbps: Optional[float] = Field(None, ge=1.0, le=10000.0)


class ScalingConfig(BaseModel):
    """Auto-scaling configuration."""
    min_replicas: int = Field(default=1, ge=0, le=100)
    max_replicas: int = Field(default=10, ge=1, le=100)
    target_cpu_utilization: float = Field(default=70.0, ge=10.0, le=95.0)
    target_memory_utilization: float = Field(default=80.0, ge=10.0, le=95.0)
    scale_up_cooldown: int = Field(default=300, ge=60, le=3600)  # seconds
    scale_down_cooldown: int = Field(default=600, ge=120, le=7200)  # seconds
    
    @validator("max_replicas")
    def validate_max_replicas(cls, v, values):
        """Validate max replicas is greater than min replicas."""
        if "min_replicas" in values and v < values["min_replicas"]:
            raise ValueError("max_replicas must be greater than or equal to min_replicas")
        return v


class DeploymentRequest(BaseModel):
    """Model deployment request."""
    model_id: str
    model_version: Optional[str] = None
    deployment_name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    environment: str = Field(default="production")
    resource_requirements: ResourceRequirements = Field(default_factory=ResourceRequirements)
    scaling_config: ScalingConfig = Field(default_factory=ScalingConfig)
    environment_variables: Optional[Dict[str, str]] = None
    health_check_path: str = Field(default="/health")
    timeout_seconds: int = Field(default=300, ge=30, le=3600)
    
    @validator("deployment_name")
    def validate_deployment_name(cls, v):
        """Validate deployment name."""
        import re
        if not re.match(r'^[a-z0-9]([a-z0-9-]*[a-z0-9])?$', v):
            raise ValueError("Deployment name must be lowercase alphanumeric with hyphens")
        return v


class DeploymentUpdate(BaseModel):
    """Deployment update request."""
    description: Optional[str] = Field(None, max_length=500)
    resource_requirements: Optional[ResourceRequirements] = None
    scaling_config: Optional[ScalingConfig] = None
    environment_variables: Optional[Dict[str, str]] = None
    timeout_seconds: Optional[int] = Field(None, ge=30, le=3600)


class DeploymentInfo(BaseModel):
    """Deployment information."""
    id: str
    model_id: str
    model_version: str
    deployment_name: str
    description: Optional[str] = None
    status: DeploymentStatus
    environment: str
    endpoint_url: Optional[str] = None
    resource_requirements: ResourceRequirements
    scaling_config: ScalingConfig
    current_replicas: int
    ready_replicas: int
    environment_variables: Optional[Dict[str, str]] = None
    health_status: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    deployed_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    error_message: Optional[str] = None


# Marketplace schemas

class ModelRating(BaseModel):
    """Model rating and review."""
    user_id: str
    model_id: str
    rating: int = Field(..., ge=1, le=5)
    review: Optional[str] = Field(None, max_length=2000)
    created_at: datetime


class ModelStatistics(BaseModel):
    """Model usage and performance statistics."""
    model_id: str
    total_downloads: int = 0
    total_deployments: int = 0
    average_rating: Optional[float] = None
    total_ratings: int = 0
    active_deployments: int = 0
    total_inference_requests: int = 0
    average_inference_time_ms: Optional[float] = None
    success_rate: Optional[float] = None
    last_30_days_usage: int = 0
    popularity_score: Optional[float] = None


class MarketplaceModel(BaseModel):
    """Enhanced model information for marketplace display."""
    id: str
    name: str
    description: str
    model_type: ModelType
    format: ModelFormat
    visibility: ModelVisibility
    tags: List[str]
    license: str
    version: str
    author: str
    author_id: str
    metadata: Optional[ModelMetadata] = None
    statistics: Optional[ModelStatistics] = None
    created_at: datetime
    updated_at: datetime
    featured: bool = False
    verified: bool = False
    repository_url: Optional[str] = None
    documentation_url: Optional[str] = None
    paper_url: Optional[str] = None


# Response schemas

class ModelResponse(BaseResponse):
    """Model operation response."""
    data: Optional[Dict[str, Any]] = None


class ModelListResponse(BaseResponse):
    """Model list response with pagination."""
    data: Dict[str, Any]
    pagination: Optional[PaginationInfo] = None


class DeploymentResponse(BaseResponse):
    """Deployment operation response."""
    data: Optional[DeploymentInfo] = None


class DeploymentListResponse(BaseResponse):
    """Deployment list response."""
    data: List[DeploymentInfo]
    pagination: Optional[PaginationInfo] = None


class MarketplaceResponse(BaseResponse):
    """Marketplace response."""
    data: List[MarketplaceModel]
    pagination: Optional[PaginationInfo] = None


class StatisticsResponse(BaseResponse):
    """Statistics response."""
    data: Dict[str, Any]


# Analytics schemas

class ModelAnalytics(BaseModel):
    """Model analytics data."""
    model_id: str
    date_range: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_latency_ms: Optional[float] = None
    p95_latency_ms: Optional[float] = None
    p99_latency_ms: Optional[float] = None
    error_rate: Optional[float] = None
    throughput_rps: Optional[float] = None
    unique_users: int = 0
    geographic_distribution: Optional[Dict[str, int]] = None
    device_types: Optional[Dict[str, int]] = None
    usage_by_hour: Optional[List[Dict[str, Any]]] = None


class RevenueAnalytics(BaseModel):
    """Revenue analytics for model marketplace."""
    model_id: str
    date_range: str
    total_revenue: float = 0.0
    subscription_revenue: float = 0.0
    usage_based_revenue: float = 0.0
    marketplace_commission: float = 0.0
    net_revenue: float = 0.0
    total_transactions: int = 0
    average_transaction_value: float = 0.0
    monthly_recurring_revenue: float = 0.0
    revenue_growth_rate: Optional[float] = None


# Security schemas

class SecurityScanResult(BaseModel):
    """Security scan result."""
    model_id: str
    scan_type: str  # malware, code_analysis, license_check
    status: str  # passed, failed, warning
    findings: List[Dict[str, Any]] = Field(default_factory=list)
    score: Optional[float] = None  # 0-100
    scanned_at: datetime
    scan_duration_seconds: float


class ComplianceInfo(BaseModel):
    """Model compliance information."""
    model_id: str
    gdpr_compliant: Optional[bool] = None
    ccpa_compliant: Optional[bool] = None
    hipaa_compliant: Optional[bool] = None
    sox_compliant: Optional[bool] = None
    data_residency_requirements: Optional[List[str]] = None
    encryption_at_rest: bool = False
    encryption_in_transit: bool = False
    audit_logging_enabled: bool = False
    compliance_certifications: List[str] = Field(default_factory=list)


# Usage tracking schemas

class UsageEvent(BaseModel):
    """Model usage event."""
    model_id: str
    deployment_id: Optional[str] = None
    user_id: Optional[str] = None
    event_type: str  # inference, download, view, like
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    latency_ms: Optional[float] = None
    success: bool = True
    error_code: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    geographic_location: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class BillingInfo(BaseModel):
    """Billing information for model usage."""
    model_id: str
    user_id: str
    billing_period: str
    total_requests: int = 0
    total_tokens: int = 0
    compute_hours: float = 0.0
    storage_gb_hours: float = 0.0
    bandwidth_gb: float = 0.0
    total_cost: float = 0.0
    currency: str = "USD"
    billing_date: datetime