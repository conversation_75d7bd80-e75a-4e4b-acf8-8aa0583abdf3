"""
AI Model Marketplace Service - Main Application

A comprehensive marketplace for AI models allowing users to:
- Upload and register custom AI models
- Browse and discover models
- Deploy models with auto-scaling
- Monitor model performance and usage
- Manage model versions and updates
- Ensure security and compliance
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import uvicorn

from core.config import get_settings
from core.exceptions import ModelMarketplaceException
from core.logging import setup_logging
from core.monitoring import setup_monitoring, ModelMarketplaceMetrics
from core.security import SecurityMiddleware
from database.connection import DatabaseManager
from services.model_registry import ModelRegistryService
from services.model_deployment import ModelDeploymentService
from services.model_validation import ModelValidationService
from api.routes import models, deployments, marketplace, analytics, health


# Global state
app_state: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    logger = structlog.get_logger(__name__)
    
    try:
        logger.info("Starting AI Model Marketplace Service")
        
        # Initialize database
        database_manager = DatabaseManager()
        await database_manager.initialize()
        app_state["database"] = database_manager
        
        # Initialize services
        model_registry = ModelRegistryService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await model_registry.start()
        app_state["model_registry"] = model_registry
        
        model_deployment = ModelDeploymentService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await model_deployment.start()
        app_state["model_deployment"] = model_deployment
        
        model_validation = ModelValidationService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await model_validation.start()
        app_state["model_validation"] = model_validation
        
        # Initialize monitoring
        metrics = ModelMarketplaceMetrics()
        app_state["metrics"] = metrics
        
        logger.info("AI Model Marketplace Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start AI Model Marketplace Service: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down AI Model Marketplace Service")
        
        # Stop services
        if "model_registry" in app_state:
            await app_state["model_registry"].stop()
        if "model_deployment" in app_state:
            await app_state["model_deployment"].stop()
        if "model_validation" in app_state:
            await app_state["model_validation"].stop()
        
        # Close database connections
        if "database" in app_state:
            await app_state["database"].close()
        
        logger.info("AI Model Marketplace Service stopped")


# Create FastAPI application
def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    # Setup logging
    setup_logging()
    logger = structlog.get_logger(__name__)
    
    # Create app
    app = FastAPI(
        title="AI Model Marketplace Service",
        description="Comprehensive AI model marketplace with upload, deployment, and management capabilities",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        lifespan=lifespan
    )
    
    # Setup monitoring
    setup_monitoring(app)
    
    # Security middleware
    app.add_middleware(SecurityMiddleware)
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts
        )
    
    # Exception handlers
    @app.exception_handler(ModelMarketplaceException)
    async def marketplace_exception_handler(request: Request, exc: ModelMarketplaceException):
        logger = structlog.get_logger(__name__)
        logger.error(f"Marketplace error: {exc}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.error_type, "message": str(exc)}
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger = structlog.get_logger(__name__)
        logger.warning(f"HTTP error: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": "http_error", "message": exc.detail}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger = structlog.get_logger(__name__)
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": "internal_error", "message": "Internal server error"}
        )
    
    # Include API routes
    app.include_router(health.router, prefix="/health", tags=["Health"])
    app.include_router(models.router, prefix="/api/models", tags=["Models"])
    app.include_router(deployments.router, prefix="/api/deployments", tags=["Deployments"])
    app.include_router(marketplace.router, prefix="/api/marketplace", tags=["Marketplace"])
    app.include_router(analytics.router, prefix="/api/analytics", tags=["Analytics"])
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "AI Model Marketplace",
            "version": "1.0.0",
            "status": "operational",
            "features": [
                "Model Registry",
                "Model Deployment",
                "Security Validation",
                "Performance Analytics",
                "Auto-scaling",
                "Version Management"
            ]
        }
    
    return app


# Create app instance
app = create_app()


# Dependency injection functions
def get_model_registry() -> ModelRegistryService:
    """Get model registry service instance."""
    if "model_registry" not in app_state:
        raise HTTPException(status_code=503, detail="Model registry service not available")
    return app_state["model_registry"]


def get_model_deployment() -> ModelDeploymentService:
    """Get model deployment service instance."""
    if "model_deployment" not in app_state:
        raise HTTPException(status_code=503, detail="Model deployment service not available")
    return app_state["model_deployment"]


def get_model_validation() -> ModelValidationService:
    """Get model validation service instance."""
    if "model_validation" not in app_state:
        raise HTTPException(status_code=503, detail="Model validation service not available")
    return app_state["model_validation"]


def get_metrics() -> ModelMarketplaceMetrics:
    """Get metrics instance."""
    if "metrics" not in app_state:
        raise HTTPException(status_code=503, detail="Metrics service not available")
    return app_state["metrics"]


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        workers=1 if settings.environment == "development" else 4,
        log_config=None,  # Use our custom logging
        access_log=False  # Disable default access log
    )