"""
Configuration management for AI Model Marketplace Service.
Handles environment variables, settings validation, and configuration schemas.
"""

import os
from functools import lru_cache
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, Field, validator
from pydantic.networks import AnyHttpUrl


class Settings(BaseSettings):
    """Application settings with validation."""
    
    # Service Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    environment: str = Field(default="development", env="ENVIRONMENT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database Configuration
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="SUPABASE_ANON_KEY")
    supabase_service_key: str = Field(..., env="SUPABASE_SERVICE_KEY")
    database_url: str = Field(..., env="DATABASE_URL")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # AI Model Configuration
    model_storage_path: str = Field(default="/app/models", env="MODEL_STORAGE_PATH")
    max_model_size_mb: int = Field(default=10240, env="MAX_MODEL_SIZE_MB")  # 10GB
    supported_model_formats: List[str] = Field(
        default=["pytorch", "onnx", "tensorflow", "safetensors", "huggingface"],
        env="SUPPORTED_MODEL_FORMATS"
    )
    
    # Model Validation
    security_scanning_enabled: bool = Field(default=True, env="SECURITY_SCANNING_ENABLED")
    malware_scanning_enabled: bool = Field(default=True, env="MALWARE_SCANNING_ENABLED")
    code_analysis_enabled: bool = Field(default=True, env="CODE_ANALYSIS_ENABLED")
    license_validation_enabled: bool = Field(default=True, env="LICENSE_VALIDATION_ENABLED")
    
    # Model Deployment
    auto_scaling_enabled: bool = Field(default=True, env="AUTO_SCALING_ENABLED")
    min_replicas: int = Field(default=1, env="MIN_REPLICAS")
    max_replicas: int = Field(default=10, env="MAX_REPLICAS")
    gpu_enabled: bool = Field(default=True, env="GPU_ENABLED")
    cpu_limit: str = Field(default="2000m", env="CPU_LIMIT")
    memory_limit: str = Field(default="8Gi", env="MEMORY_LIMIT")
    gpu_limit: int = Field(default=1, env="GPU_LIMIT")
    
    # Model Registry
    huggingface_token: Optional[str] = Field(default=None, env="HUGGINGFACE_TOKEN")
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # Model Marketplace
    marketplace_enabled: bool = Field(default=True, env="MARKETPLACE_ENABLED")
    public_models_enabled: bool = Field(default=True, env="PUBLIC_MODELS_ENABLED")
    private_models_enabled: bool = Field(default=True, env="PRIVATE_MODELS_ENABLED")
    model_sharing_enabled: bool = Field(default=True, env="MODEL_SHARING_ENABLED")
    
    # Performance & Optimization
    model_caching_enabled: bool = Field(default=True, env="MODEL_CACHING_ENABLED")
    model_quantization_enabled: bool = Field(default=True, env="MODEL_QUANTIZATION_ENABLED")
    model_optimization_enabled: bool = Field(default=True, env="MODEL_OPTIMIZATION_ENABLED")
    batch_inference_enabled: bool = Field(default=True, env="BATCH_INFERENCE_ENABLED")
    
    # Security Configuration
    secret_key: str = Field(..., env="SECRET_KEY")
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # API Rate Limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(default=100, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    
    # Monitoring & Analytics
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    jaeger_endpoint: Optional[str] = Field(default=None, env="JAEGER_ENDPOINT")
    
    # External Services
    s3_bucket_name: Optional[str] = Field(default=None, env="S3_BUCKET_NAME")
    s3_access_key: Optional[str] = Field(default=None, env="S3_ACCESS_KEY")
    s3_secret_key: Optional[str] = Field(default=None, env="S3_SECRET_KEY")
    s3_region: str = Field(default="us-east-1", env="S3_REGION")
    
    # Container Registry
    container_registry_url: str = Field(default="ghcr.io", env="CONTAINER_REGISTRY_URL")
    container_registry_username: Optional[str] = Field(default=None, env="CONTAINER_REGISTRY_USERNAME")
    container_registry_password: Optional[str] = Field(default=None, env="CONTAINER_REGISTRY_PASSWORD")
    
    # Kubernetes Configuration
    kubernetes_namespace: str = Field(default="ai-models", env="KUBERNETES_NAMESPACE")
    kubernetes_service_account: str = Field(default="ai-model-service", env="KUBERNETES_SERVICE_ACCOUNT")
    
    # Email Configuration
    email_enabled: bool = Field(default=False, env="EMAIL_ENABLED")
    smtp_server: str = Field(default="smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    
    # Feature Flags
    experimental_features_enabled: bool = Field(default=False, env="EXPERIMENTAL_FEATURES_ENABLED")
    beta_features_enabled: bool = Field(default=False, env="BETA_FEATURES_ENABLED")
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "https://publishai.com"],
        env="ALLOWED_ORIGINS"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "publishai.com", "*.publishai.com"],
        env="ALLOWED_HOSTS"
    )
    
    @validator("environment")
    def validate_environment(cls, v):
        """Validate environment setting."""
        valid_environments = ["development", "staging", "production"]
        if v not in valid_environments:
            raise ValueError(f"Environment must be one of: {valid_environments}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level setting."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    @validator("supported_model_formats")
    def validate_model_formats(cls, v):
        """Validate supported model formats."""
        valid_formats = ["pytorch", "onnx", "tensorflow", "safetensors", "huggingface", "custom"]
        for format_type in v:
            if format_type not in valid_formats:
                raise ValueError(f"Unsupported model format: {format_type}")
        return v
    
    @validator("max_model_size_mb")
    def validate_max_model_size(cls, v):
        """Validate maximum model size."""
        if v <= 0 or v > 100000:  # Max 100GB
            raise ValueError("Max model size must be between 1MB and 100GB")
        return v
    
    @validator("min_replicas", "max_replicas")
    def validate_replicas(cls, v):
        """Validate replica counts."""
        if v <= 0 or v > 100:
            raise ValueError("Replica count must be between 1 and 100")
        return v
    
    @validator("rate_limit_requests_per_minute")
    def validate_rate_limit(cls, v):
        """Validate rate limit setting."""
        if v <= 0 or v > 10000:
            raise ValueError("Rate limit must be between 1 and 10000 requests per minute")
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Allow environment variables to override file settings
        env_prefix = ""


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance.
    
    Returns:
        Settings instance with all configuration
    """
    return Settings()


def get_model_config() -> Dict[str, Any]:
    """
    Get model-specific configuration.
    
    Returns:
        Dictionary with model configuration
    """
    settings = get_settings()
    
    return {
        "storage_path": settings.model_storage_path,
        "max_size_mb": settings.max_model_size_mb,
        "supported_formats": settings.supported_model_formats,
        "caching_enabled": settings.model_caching_enabled,
        "quantization_enabled": settings.model_quantization_enabled,
        "optimization_enabled": settings.model_optimization_enabled,
        "security_scanning": settings.security_scanning_enabled,
        "malware_scanning": settings.malware_scanning_enabled,
    }


def get_deployment_config() -> Dict[str, Any]:
    """
    Get deployment configuration.
    
    Returns:
        Dictionary with deployment configuration
    """
    settings = get_settings()
    
    return {
        "auto_scaling_enabled": settings.auto_scaling_enabled,
        "min_replicas": settings.min_replicas,
        "max_replicas": settings.max_replicas,
        "gpu_enabled": settings.gpu_enabled,
        "resources": {
            "cpu_limit": settings.cpu_limit,
            "memory_limit": settings.memory_limit,
            "gpu_limit": settings.gpu_limit,
        },
        "kubernetes": {
            "namespace": settings.kubernetes_namespace,
            "service_account": settings.kubernetes_service_account,
        }
    }


def get_security_config() -> Dict[str, Any]:
    """
    Get security configuration.
    
    Returns:
        Dictionary with security configuration
    """
    settings = get_settings()
    
    return {
        "security_scanning_enabled": settings.security_scanning_enabled,
        "malware_scanning_enabled": settings.malware_scanning_enabled,
        "code_analysis_enabled": settings.code_analysis_enabled,
        "license_validation_enabled": settings.license_validation_enabled,
        "rate_limit_enabled": settings.rate_limit_enabled,
        "rate_limit_rpm": settings.rate_limit_requests_per_minute,
        "jwt_expire_minutes": settings.access_token_expire_minutes,
    }


def is_production() -> bool:
    """
    Check if running in production environment.
    
    Returns:
        True if production environment
    """
    return get_settings().environment == "production"


def is_development() -> bool:
    """
    Check if running in development environment.
    
    Returns:
        True if development environment
    """
    return get_settings().environment == "development"


def get_api_keys() -> Dict[str, Optional[str]]:
    """
    Get API keys for external services.
    
    Returns:
        Dictionary with API keys
    """
    settings = get_settings()
    
    return {
        "huggingface": settings.huggingface_token,
        "openai": settings.openai_api_key,
        "anthropic": settings.anthropic_api_key,
    }