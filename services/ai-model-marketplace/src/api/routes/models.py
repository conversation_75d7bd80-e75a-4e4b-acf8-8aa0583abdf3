"""
Models API routes for AI Model Marketplace.

Provides endpoints for:
- Model registration and upload
- Model browsing and discovery
- Model metadata management
- Model versioning
- HuggingFace integration
"""

import io
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, Query
from fastapi.responses import JSONResponse, FileResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
import structlog

from models.model_schemas import (
    ModelRegistration, ModelResponse, ModelListResponse, ModelUpdateRequest,
    ModelFormat, ModelType, ModelVisibility, ModelStatistics,
    HuggingFaceImportRequest, ModelVersionRequest
)
from services.model_registry import ModelRegistryService
from core.exceptions import ModelMarketplaceException
from utils.auth import verify_token, get_current_user
from main import get_model_registry


router = APIRouter()
logger = structlog.get_logger(__name__)
security = HTTPBearer()


@router.post("/register", response_model=ModelResponse)
async def register_model(
    name: str = Form(...),
    description: str = Form(...),
    model_type: ModelType = Form(...),
    format: ModelFormat = Form(...),
    visibility: ModelVisibility = Form(ModelVisibility.PRIVATE),
    tags: str = Form(""),  # Comma-separated tags
    license: str = Form("custom"),
    model_file: Optional[UploadFile] = File(None),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Register a new AI model in the marketplace.
    
    Args:
        name: Model name
        description: Model description
        model_type: Type of AI model
        format: Model file format
        visibility: Model visibility (public/private)
        tags: Comma-separated tags
        license: Model license
        model_file: Optional model file upload
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Model registration response
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Parse tags
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        # Create model registration
        model_registration = ModelRegistration(
            name=name,
            description=description,
            model_type=model_type,
            format=format,
            visibility=visibility,
            tags=tag_list,
            license=license
        )
        
        # Process file upload
        file_obj = None
        if model_file:
            if model_file.size and model_file.size > 10 * 1024 * 1024 * 1024:  # 10GB limit
                raise HTTPException(status_code=413, detail="File too large")
            
            # Read file content
            content = await model_file.read()
            file_obj = io.BytesIO(content)
        
        # Register model
        result = await model_registry.register_model(
            user_id=user_id,
            model_registration=model_registration,
            model_file=file_obj
        )
        
        logger.info(
            "Model registered via API",
            model_id=result["model_id"],
            user_id=user_id,
            model_name=name
        )
        
        return ModelResponse(
            success=True,
            message="Model registered successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Model registration failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in model registration: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{model_id}", response_model=ModelResponse)
async def get_model(
    model_id: str,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Get detailed information about a specific model.
    
    Args:
        model_id: Model identifier
        credentials: Optional JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Model information
    """
    try:
        # Get user ID if authenticated
        user_id = None
        if credentials:
            try:
                user_id = await get_current_user(credentials.credentials)
            except:
                pass  # Allow anonymous access to public models
        
        # Get model
        model_data = await model_registry.get_model(model_id, user_id)
        
        return ModelResponse(
            success=True,
            message="Model retrieved successfully",
            data=model_data
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to get model {model_id}: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting model {model_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=ModelListResponse)
async def list_models(
    model_type: Optional[ModelType] = Query(None, description="Filter by model type"),
    format: Optional[ModelFormat] = Query(None, description="Filter by format"),
    visibility: Optional[ModelVisibility] = Query(None, description="Filter by visibility"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    limit: int = Query(50, ge=1, le=100, description="Number of models to return"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    List models with filtering and pagination.
    
    Args:
        model_type: Optional model type filter
        format: Optional format filter
        visibility: Optional visibility filter
        tags: Optional tags filter (comma-separated)
        search: Optional search query
        limit: Number of models to return
        offset: Pagination offset
        credentials: Optional JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        List of models with pagination info
    """
    try:
        # Get user ID if authenticated
        user_id = None
        if credentials:
            try:
                user_id = await get_current_user(credentials.credentials)
            except:
                pass  # Allow anonymous browsing
        
        # Parse tags
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # List models
        result = await model_registry.list_models(
            user_id=user_id,
            model_type=model_type,
            format=format,
            visibility=visibility,
            tags=tag_list,
            limit=limit,
            offset=offset
        )
        
        # Apply search filter if provided
        if search and result["models"]:
            search_lower = search.lower()
            filtered_models = []
            for model in result["models"]:
                if (search_lower in model["name"].lower() or 
                    search_lower in model["description"].lower()):
                    filtered_models.append(model)
            result["models"] = filtered_models
            result["total_count"] = len(filtered_models)
        
        return ModelListResponse(
            success=True,
            message="Models retrieved successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to list models: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing models: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{model_id}", response_model=ModelResponse)
async def update_model(
    model_id: str,
    update_request: ModelUpdateRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Update model information.
    
    Args:
        model_id: Model identifier
        update_request: Model update data
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Updated model information
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Update model
        result = await model_registry.update_model(
            model_id=model_id,
            user_id=user_id,
            updates=update_request.dict(exclude_unset=True)
        )
        
        logger.info(
            "Model updated via API",
            model_id=model_id,
            user_id=user_id
        )
        
        return ModelResponse(
            success=True,
            message="Model updated successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to update model {model_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error updating model {model_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{model_id}", response_model=ModelResponse)
async def delete_model(
    model_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Delete a model and its associated files.
    
    Args:
        model_id: Model identifier
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Deletion confirmation
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Delete model
        result = await model_registry.delete_model(model_id, user_id)
        
        logger.info(
            "Model deleted via API",
            model_id=model_id,
            user_id=user_id
        )
        
        return ModelResponse(
            success=True,
            message="Model deleted successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to delete model {model_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error deleting model {model_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{model_id}/versions", response_model=ModelResponse)
async def create_model_version(
    model_id: str,
    version: str = Form(...),
    changelog: str = Form(""),
    model_file: UploadFile = File(...),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Create a new version of an existing model.
    
    Args:
        model_id: Model identifier
        version: Version string
        changelog: Optional changelog
        model_file: New model file
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        New version information
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Validate file
        if not model_file.size or model_file.size > 10 * 1024 * 1024 * 1024:  # 10GB limit
            raise HTTPException(status_code=413, detail="Invalid file size")
        
        # Read file content
        content = await model_file.read()
        file_obj = io.BytesIO(content)
        
        # Create version
        result = await model_registry.create_model_version(
            model_id=model_id,
            user_id=user_id,
            version=version,
            model_file=file_obj,
            changelog=changelog or None
        )
        
        logger.info(
            "Model version created via API",
            model_id=model_id,
            version=version,
            user_id=user_id
        )
        
        return ModelResponse(
            success=True,
            message="Model version created successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to create model version: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating model version: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/import/huggingface", response_model=ModelResponse)
async def import_from_huggingface(
    import_request: HuggingFaceImportRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Import a model from HuggingFace Hub.
    
    Args:
        import_request: HuggingFace import details
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Import result
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Import model
        result = await model_registry.import_from_huggingface(
            user_id=user_id,
            model_name=import_request.model_name,
            hf_model_id=import_request.huggingface_model_id,
            visibility=import_request.visibility
        )
        
        logger.info(
            "Model imported from HuggingFace via API",
            model_id=result["model_id"],
            hf_model_id=import_request.huggingface_model_id,
            user_id=user_id
        )
        
        return ModelResponse(
            success=True,
            message="Model imported from HuggingFace successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to import from HuggingFace: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error importing from HuggingFace: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user/my-models", response_model=ModelListResponse)
async def get_my_models(
    limit: int = Query(50, ge=1, le=100, description="Number of models to return"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Get models owned by the authenticated user.
    
    Args:
        limit: Number of models to return
        offset: Pagination offset
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        User's models
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # Get user's models
        result = await model_registry.list_models(
            user_id=user_id,
            limit=limit,
            offset=offset
        )
        
        return ModelListResponse(
            success=True,
            message="User models retrieved successfully",
            data=result
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to get user models: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting user models: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=ModelResponse)
async def get_model_statistics(
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Get comprehensive model registry statistics.
    
    Args:
        model_registry: Model registry service
    
    Returns:
        Model registry statistics
    """
    try:
        # Get statistics
        stats = await model_registry.get_model_statistics()
        
        return ModelResponse(
            success=True,
            message="Statistics retrieved successfully",
            data=stats
        )
        
    except ModelMarketplaceException as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting statistics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{model_id}/download")
async def download_model(
    model_id: str,
    version: Optional[str] = Query(None, description="Specific version to download"),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Download a model file.
    
    Args:
        model_id: Model identifier
        version: Optional specific version
        credentials: Optional JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Model file download
    """
    try:
        # Get user ID if authenticated
        user_id = None
        if credentials:
            try:
                user_id = await get_current_user(credentials.credentials)
            except:
                pass  # Allow anonymous download of public models
        
        # Get model to check access
        model_data = await model_registry.get_model(model_id, user_id)
        
        # Check if model is downloadable
        if model_data["visibility"] == "private" and model_data["user_id"] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Find file path
        file_info = model_data.get("file_info", {})
        if not file_info.get("file_path"):
            raise HTTPException(status_code=404, detail="Model file not found")
        
        file_path = file_info["file_path"]
        filename = file_info.get("filename", f"model_{model_id}")
        
        # Return file
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="application/octet-stream"
        )
        
    except HTTPException:
        raise
    except ModelMarketplaceException as e:
        logger.error(f"Failed to download model {model_id}: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error downloading model {model_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{model_id}/like", response_model=ModelResponse)
async def like_model(
    model_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    model_registry: ModelRegistryService = Depends(get_model_registry)
):
    """
    Like/unlike a model.
    
    Args:
        model_id: Model identifier
        credentials: JWT authentication credentials
        model_registry: Model registry service
    
    Returns:
        Like operation result
    """
    try:
        # Verify authentication
        user_id = await get_current_user(credentials.credentials)
        
        # This would implement like/unlike functionality
        # For now, return a placeholder response
        
        return ModelResponse(
            success=True,
            message="Like operation completed",
            data={"model_id": model_id, "action": "liked"}
        )
        
    except Exception as e:
        logger.error(f"Unexpected error liking model {model_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")