"""
Role-Based Access Control (RBAC) Service

Provides comprehensive RBAC capabilities including:
- Dynamic role and permission management
- Policy-based access control
- Resource-level permissions
- Role inheritance and delegation
- Audit trail for all access decisions
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
import uuid

import casbin
import structlog
from casbin_async_sqlalchemy_adapter.adapter import Adapter

from core.config import get_settings
from core.exceptions import EnterpriseSecurityException
from core.monitoring import EnterpriseSecurityMetrics
from models.rbac_schemas import (
    Role, Permission, Policy, ResourcePermission, RoleAssignment,
    AccessRequest, AccessDecision, PolicyEvaluation
)


class PermissionEffect(Enum):
    """Permission effects for access control."""
    ALLOW = "allow"
    DENY = "deny"


class RBACService:
    """
    Comprehensive RBAC service with dynamic policy management
    and resource-level access control.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = EnterpriseSecurityMetrics()
        
        # Casbin enforcer for policy evaluation
        self.enforcer = None
        
        # Cache for roles and permissions
        self.roles_cache: Dict[str, Role] = {}
        self.permissions_cache: Dict[str, Permission] = {}
        self.user_roles_cache: Dict[str, Set[str]] = {}
        
        # Cache expiry
        self.cache_ttl = timedelta(minutes=15)
        self.cache_last_updated = datetime.utcnow()
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the RBAC service."""
        self.logger.info("Starting RBAC Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Initialize Casbin enforcer
            await self._initialize_casbin()
            
            # Load built-in roles and permissions
            await self._load_builtin_roles()
            
            # Start background tasks
            self.background_tasks.append(
                asyncio.create_task(self._cache_refresh())
            )
            self.background_tasks.append(
                asyncio.create_task(self._policy_sync())
            )
            
            self.is_running = True
            self.logger.info("RBAC Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start RBAC service: {e}")
            raise EnterpriseSecurityException(f"RBAC service startup failed: {e}")
    
    async def stop(self):
        """Stop the RBAC service."""
        self.logger.info("Stopping RBAC Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("RBAC Service stopped")
    
    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[str],
        parent_roles: Optional[List[str]] = None
    ) -> Role:
        """
        Create a new role with specified permissions.
        
        Args:
            name: Role name (unique)
            description: Role description
            permissions: List of permission names
            parent_roles: Optional parent roles for inheritance
        
        Returns:
            Created role object
        """
        try:
            # Validate permissions exist
            for perm_name in permissions:
                if not await self._permission_exists(perm_name):
                    raise EnterpriseSecurityException(f"Permission '{perm_name}' does not exist")
            
            # Validate parent roles exist
            if parent_roles:
                for parent_role in parent_roles:
                    if not await self._role_exists(parent_role):
                        raise EnterpriseSecurityException(f"Parent role '{parent_role}' does not exist")
            
            role_id = str(uuid.uuid4())
            role_data = {
                "id": role_id,
                "name": name,
                "description": description,
                "permissions": permissions,
                "parent_roles": parent_roles or [],
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "is_active": True
            }
            
            # Insert role
            result = await self.supabase.table("rbac_roles").insert(role_data).execute()
            
            # Create role object
            role = Role(
                id=role_id,
                name=name,
                description=description,
                permissions=permissions,
                parent_roles=parent_roles or [],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                is_active=True
            )
            
            # Add to cache
            self.roles_cache[role_id] = role
            
            # Update Casbin policies
            await self._add_role_policies(role)
            
            self.logger.info(f"Role created: {name}", role_id=role_id)
            
            # Update metrics
            self.metrics.rbac_roles_total.inc()
            
            return role
            
        except Exception as e:
            self.logger.error(f"Failed to create role: {e}")
            raise EnterpriseSecurityException(f"Role creation failed: {e}")
    
    async def assign_role_to_user(self, user_id: str, role_name: str) -> bool:
        """
        Assign a role to a user.
        
        Args:
            user_id: User identifier
            role_name: Role name to assign
        
        Returns:
            True if assignment successful
        """
        try:
            # Validate role exists
            if not await self._role_exists(role_name):
                raise EnterpriseSecurityException(f"Role '{role_name}' does not exist")
            
            # Check if assignment already exists
            existing = await self.supabase.table("rbac_user_roles").select("*").eq(
                "user_id", user_id
            ).eq("role_name", role_name).execute()
            
            if existing.data:
                return True  # Already assigned
            
            # Create assignment
            assignment_data = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "role_name": role_name,
                "assigned_at": datetime.utcnow().isoformat(),
                "assigned_by": "system",  # This would be the current user in practice
                "is_active": True
            }
            
            await self.supabase.table("rbac_user_roles").insert(assignment_data).execute()
            
            # Update cache
            if user_id not in self.user_roles_cache:
                self.user_roles_cache[user_id] = set()
            self.user_roles_cache[user_id].add(role_name)
            
            # Add to Casbin
            await self.enforcer.add_role_for_user(user_id, role_name)
            
            self.logger.info(f"Role assigned to user", user_id=user_id, role_name=role_name)
            
            # Update metrics
            self.metrics.rbac_role_assignments_total.inc()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to assign role to user: {e}")
            raise EnterpriseSecurityException(f"Role assignment failed: {e}")
    
    async def check_permission(
        self,
        user_id: str,
        resource: str,
        action: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AccessDecision:
        """
        Check if user has permission to perform action on resource.
        
        Args:
            user_id: User identifier
            resource: Resource identifier
            action: Action to perform
            context: Additional context for decision
        
        Returns:
            Access decision with reasoning
        """
        try:
            start_time = datetime.utcnow()
            
            # Use Casbin to evaluate permission
            allowed = await self.enforcer.enforce(user_id, resource, action)
            
            # Get user roles for context
            user_roles = await self._get_user_roles(user_id)
            
            # Create access decision
            decision = AccessDecision(
                user_id=user_id,
                resource=resource,
                action=action,
                allowed=allowed,
                roles=list(user_roles),
                evaluated_at=datetime.utcnow(),
                evaluation_time_ms=(datetime.utcnow() - start_time).total_seconds() * 1000,
                context=context or {}
            )
            
            # Log access decision
            await self._log_access_decision(decision)
            
            # Update metrics
            if allowed:
                self.metrics.rbac_access_granted_total.inc()
            else:
                self.metrics.rbac_access_denied_total.inc()
            
            self.logger.info(
                f"Access decision: {'ALLOW' if allowed else 'DENY'}",
                user_id=user_id,
                resource=resource,
                action=action,
                roles=list(user_roles)
            )
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Permission check failed: {e}")
            raise EnterpriseSecurityException(f"Permission check failed: {e}")
    
    async def get_user_permissions(self, user_id: str) -> List[str]:
        """
        Get all permissions for a user (direct and inherited).
        
        Args:
            user_id: User identifier
        
        Returns:
            List of permission names
        """
        try:
            # Get user roles
            user_roles = await self._get_user_roles(user_id)
            
            # Collect all permissions
            all_permissions = set()
            
            for role_name in user_roles:
                role = await self._get_role_by_name(role_name)
                if role:
                    all_permissions.update(role.permissions)
                    
                    # Add inherited permissions from parent roles
                    for parent_role_name in role.parent_roles:
                        parent_role = await self._get_role_by_name(parent_role_name)
                        if parent_role:
                            all_permissions.update(parent_role.permissions)
            
            return list(all_permissions)
            
        except Exception as e:
            self.logger.error(f"Failed to get user permissions: {e}")
            raise EnterpriseSecurityException(f"Get user permissions failed: {e}")
    
    async def get_user_roles(self, user_id: str) -> List[Role]:
        """
        Get all roles assigned to a user.
        
        Args:
            user_id: User identifier
        
        Returns:
            List of role objects
        """
        try:
            role_names = await self._get_user_roles(user_id)
            roles = []
            
            for role_name in role_names:
                role = await self._get_role_by_name(role_name)
                if role:
                    roles.append(role)
            
            return roles
            
        except Exception as e:
            self.logger.error(f"Failed to get user roles: {e}")
            raise EnterpriseSecurityException(f"Get user roles failed: {e}")
    
    async def create_permission(
        self,
        name: str,
        description: str,
        resource_type: str,
        actions: List[str]
    ) -> Permission:
        """
        Create a new permission.
        
        Args:
            name: Permission name (unique)
            description: Permission description
            resource_type: Type of resource this permission applies to
            actions: List of actions this permission allows
        
        Returns:
            Created permission object
        """
        try:
            permission_id = str(uuid.uuid4())
            permission_data = {
                "id": permission_id,
                "name": name,
                "description": description,
                "resource_type": resource_type,
                "actions": actions,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "is_active": True
            }
            
            # Insert permission
            result = await self.supabase.table("rbac_permissions").insert(permission_data).execute()
            
            # Create permission object
            permission = Permission(
                id=permission_id,
                name=name,
                description=description,
                resource_type=resource_type,
                actions=actions,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                is_active=True
            )
            
            # Add to cache
            self.permissions_cache[permission_id] = permission
            
            self.logger.info(f"Permission created: {name}", permission_id=permission_id)
            
            # Update metrics
            self.metrics.rbac_permissions_total.inc()
            
            return permission
            
        except Exception as e:
            self.logger.error(f"Failed to create permission: {e}")
            raise EnterpriseSecurityException(f"Permission creation failed: {e}")
    
    async def remove_role_from_user(self, user_id: str, role_name: str) -> bool:
        """
        Remove a role from a user.
        
        Args:
            user_id: User identifier
            role_name: Role name to remove
        
        Returns:
            True if removal successful
        """
        try:
            # Remove from database
            await self.supabase.table("rbac_user_roles").delete().eq(
                "user_id", user_id
            ).eq("role_name", role_name).execute()
            
            # Update cache
            if user_id in self.user_roles_cache:
                self.user_roles_cache[user_id].discard(role_name)
            
            # Remove from Casbin
            await self.enforcer.delete_role_for_user(user_id, role_name)
            
            self.logger.info(f"Role removed from user", user_id=user_id, role_name=role_name)
            
            # Update metrics
            self.metrics.rbac_role_assignments_total.dec()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove role from user: {e}")
            raise EnterpriseSecurityException(f"Role removal failed: {e}")
    
    # Private helper methods
    
    async def _initialize_tables(self):
        """Initialize database tables for RBAC."""
        # Tables are created via Supabase migrations
        pass
    
    async def _initialize_casbin(self):
        """Initialize Casbin enforcer."""
        try:
            # Create adapter for Casbin
            adapter = Adapter(self.settings.database_url)
            
            # Create enforcer with RBAC model
            self.enforcer = casbin.AsyncEnforcer()
            
            # Load RBAC model
            model_conf = """
            [request_definition]
            r = sub, obj, act
            
            [policy_definition]
            p = sub, obj, act
            
            [role_definition]
            g = _, _
            
            [policy_effect]
            e = some(where (p.eft == allow))
            
            [matchers]
            m = g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
            """
            
            self.enforcer.set_model_from_string(model_conf)
            self.enforcer.set_adapter(adapter)
            
            # Load policies
            await self.enforcer.load_policy()
            
            self.logger.info("Casbin enforcer initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Casbin: {e}")
            raise
    
    async def _load_builtin_roles(self):
        """Load built-in roles and permissions."""
        try:
            # Built-in permissions
            builtin_permissions = [
                {
                    "name": "read_own_profile",
                    "description": "Read own user profile",
                    "resource_type": "user",
                    "actions": ["read"]
                },
                {
                    "name": "update_own_profile",
                    "description": "Update own user profile",
                    "resource_type": "user",
                    "actions": ["update"]
                },
                {
                    "name": "manage_books",
                    "description": "Manage books and publications",
                    "resource_type": "book",
                    "actions": ["create", "read", "update", "delete"]
                },
                {
                    "name": "manage_users",
                    "description": "Manage user accounts",
                    "resource_type": "user",
                    "actions": ["create", "read", "update", "delete"]
                },
                {
                    "name": "manage_roles",
                    "description": "Manage roles and permissions",
                    "resource_type": "role",
                    "actions": ["create", "read", "update", "delete"]
                },
                {
                    "name": "view_analytics",
                    "description": "View analytics and reports",
                    "resource_type": "analytics",
                    "actions": ["read"]
                },
                {
                    "name": "manage_system",
                    "description": "Manage system configuration",
                    "resource_type": "system",
                    "actions": ["create", "read", "update", "delete"]
                }
            ]
            
            # Create built-in permissions
            for perm_data in builtin_permissions:
                try:
                    await self.create_permission(**perm_data)
                except EnterpriseSecurityException:
                    # Permission might already exist
                    pass
            
            # Built-in roles
            builtin_roles = [
                {
                    "name": "user",
                    "description": "Standard user with basic permissions",
                    "permissions": ["read_own_profile", "update_own_profile"]
                },
                {
                    "name": "author",
                    "description": "Author with book management permissions",
                    "permissions": ["read_own_profile", "update_own_profile", "manage_books", "view_analytics"],
                    "parent_roles": ["user"]
                },
                {
                    "name": "admin",
                    "description": "Administrator with full permissions",
                    "permissions": ["manage_users", "manage_roles", "manage_system"],
                    "parent_roles": ["author"]
                }
            ]
            
            # Create built-in roles
            for role_data in builtin_roles:
                try:
                    await self.create_role(**role_data)
                except EnterpriseSecurityException:
                    # Role might already exist
                    pass
            
            self.logger.info("Built-in roles and permissions loaded")
            
        except Exception as e:
            self.logger.error(f"Failed to load built-in roles: {e}")
    
    async def _get_user_roles(self, user_id: str) -> Set[str]:
        """Get roles assigned to a user."""
        # Check cache first
        if user_id in self.user_roles_cache:
            return self.user_roles_cache[user_id]
        
        # Query database
        result = await self.supabase.table("rbac_user_roles").select("role_name").eq(
            "user_id", user_id
        ).eq("is_active", True).execute()
        
        roles = {row["role_name"] for row in result.data}
        
        # Update cache
        self.user_roles_cache[user_id] = roles
        
        return roles
    
    async def _get_role_by_name(self, role_name: str) -> Optional[Role]:
        """Get role by name."""
        # Check cache first
        for role in self.roles_cache.values():
            if role.name == role_name:
                return role
        
        # Query database
        result = await self.supabase.table("rbac_roles").select("*").eq(
            "name", role_name
        ).eq("is_active", True).single().execute()
        
        if not result.data:
            return None
        
        # Create role object
        role_data = result.data
        role = Role(
            id=role_data["id"],
            name=role_data["name"],
            description=role_data["description"],
            permissions=role_data["permissions"],
            parent_roles=role_data["parent_roles"],
            created_at=datetime.fromisoformat(role_data["created_at"]),
            updated_at=datetime.fromisoformat(role_data["updated_at"]),
            is_active=role_data["is_active"]
        )
        
        # Add to cache
        self.roles_cache[role.id] = role
        
        return role
    
    async def _role_exists(self, role_name: str) -> bool:
        """Check if role exists."""
        role = await self._get_role_by_name(role_name)
        return role is not None
    
    async def _permission_exists(self, permission_name: str) -> bool:
        """Check if permission exists."""
        result = await self.supabase.table("rbac_permissions").select("id").eq(
            "name", permission_name
        ).eq("is_active", True).execute()
        
        return len(result.data) > 0
    
    async def _add_role_policies(self, role: Role):
        """Add Casbin policies for a role."""
        try:
            # Add policies for each permission
            for permission_name in role.permissions:
                # Get permission details
                perm_result = await self.supabase.table("rbac_permissions").select("*").eq(
                    "name", permission_name
                ).single().execute()
                
                if perm_result.data:
                    permission = perm_result.data
                    
                    # Add policy for each action
                    for action in permission["actions"]:
                        await self.enforcer.add_policy(
                            role.name,
                            permission["resource_type"],
                            action
                        )
            
            # Add role inheritance
            for parent_role in role.parent_roles:
                await self.enforcer.add_role_for_user(role.name, parent_role)
            
            # Save policies
            await self.enforcer.save_policy()
            
        except Exception as e:
            self.logger.error(f"Failed to add role policies: {e}")
    
    async def _log_access_decision(self, decision: AccessDecision):
        """Log access decision for audit trail."""
        try:
            decision_data = {
                "id": str(uuid.uuid4()),
                "user_id": decision.user_id,
                "resource": decision.resource,
                "action": decision.action,
                "allowed": decision.allowed,
                "roles": decision.roles,
                "evaluated_at": decision.evaluated_at.isoformat(),
                "evaluation_time_ms": decision.evaluation_time_ms,
                "context": decision.context
            }
            
            await self.supabase.table("rbac_access_log").insert(decision_data).execute()
            
        except Exception as e:
            self.logger.error(f"Failed to log access decision: {e}")
    
    async def _cache_refresh(self):
        """Background task to refresh cache."""
        while self.is_running:
            try:
                # Check if cache needs refresh
                if datetime.utcnow() - self.cache_last_updated > self.cache_ttl:
                    # Clear cache
                    self.roles_cache.clear()
                    self.permissions_cache.clear()
                    self.user_roles_cache.clear()
                    
                    # Update timestamp
                    self.cache_last_updated = datetime.utcnow()
                    
                    self.logger.info("RBAC cache refreshed")
                
                # Sleep for 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                self.logger.error(f"Cache refresh failed: {e}")
                await asyncio.sleep(60)
    
    async def _policy_sync(self):
        """Background task to sync policies with Casbin."""
        while self.is_running:
            try:
                # Reload policies from database
                await self.enforcer.load_policy()
                
                # Sleep for 10 minutes
                await asyncio.sleep(600)
                
            except Exception as e:
                self.logger.error(f"Policy sync failed: {e}")
                await asyncio.sleep(60)