"""
Audit Service - Comprehensive enterprise audit logging and monitoring.

Provides comprehensive audit capabilities including:
- Detailed activity logging
- Security event tracking
- Compliance audit trails
- Real-time monitoring and alerting
- Data integrity verification
- Forensic analysis support
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import uuid

import structlog
from cryptography.fernet import Fernet

from core.config import get_settings
from core.exceptions import EnterpriseSecurityException
from core.monitoring import EnterpriseSecurityMetrics
from models.audit_schemas import (
    AuditEvent, AuditCategory, SecurityEvent, ComplianceEvent,
    EventSeverity, DataIntegrityCheck, ForensicReport
)


class AuditEventType(Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    AUTHENTICATION_FAILURE = "authentication_failure"
    AUTHORIZATION_FAILURE = "authorization_failure"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    SYSTEM_CONFIGURATION = "system_configuration"
    SECURITY_VIOLATION = "security_violation"
    COMPLIANCE_CHECK = "compliance_check"
    ADMIN_ACTION = "admin_action"
    API_CALL = "api_call"
    FILE_ACCESS = "file_access"
    ROLE_CHANGE = "role_change"
    PERMISSION_CHANGE = "permission_change"
    PASSWORD_CHANGE = "password_change"


class AuditService:
    """
    Comprehensive audit service providing enterprise-grade
    audit logging and monitoring capabilities.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = EnterpriseSecurityMetrics()
        
        # Encryption for sensitive audit data
        self.encryption_key = self._get_encryption_key()
        
        # Event queues
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        self.security_event_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        
        # Real-time monitoring
        self.anomaly_detectors = {}
        self.security_alerts = []
        
        # Retention policies
        self.retention_days = self.settings.audit_retention_days or 2555  # 7 years default
        self.archival_threshold = timedelta(days=self.retention_days)
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the audit service."""
        self.logger.info("Starting Audit Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Initialize anomaly detectors
            await self._initialize_anomaly_detectors()
            
            # Start background tasks
            self.background_tasks.extend([
                asyncio.create_task(self._event_processor()),
                asyncio.create_task(self._security_monitor()),
                asyncio.create_task(self._data_integrity_checker()),
                asyncio.create_task(self._audit_archival()),
                asyncio.create_task(self._compliance_reporter())
            ])
            
            self.is_running = True
            self.logger.info("Audit Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start audit service: {e}")
            raise EnterpriseSecurityException(f"Audit service startup failed: {e}")
    
    async def stop(self):
        """Stop the audit service."""
        self.logger.info("Stopping Audit Service")
        
        self.is_running = False
        
        # Process remaining events
        await self._flush_event_queue()
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("Audit Service stopped")
    
    async def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        severity: EventSeverity = EventSeverity.INFO
    ) -> str:
        """
        Log an audit event.
        
        Args:
            event_type: Type of audit event
            user_id: User identifier
            resource: Resource being accessed/modified
            action: Action being performed
            details: Additional event details
            ip_address: Client IP address
            user_agent: Client user agent
            session_id: Session identifier
            severity: Event severity level
        
        Returns:
            Event ID for tracking
        """
        try:
            event_id = str(uuid.uuid4())
            timestamp = datetime.utcnow()
            
            # Create audit event
            event = AuditEvent(
                id=event_id,
                event_type=event_type.value,
                timestamp=timestamp,
                user_id=user_id,
                resource=resource,
                action=action,
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                severity=severity.value,
                checksum=self._calculate_checksum({
                    "event_type": event_type.value,
                    "timestamp": timestamp.isoformat(),
                    "user_id": user_id,
                    "resource": resource,
                    "action": action,
                    "details": details
                })
            )
            
            # Add to event queue for processing
            await self.event_queue.put(event)
            
            # Increment metrics
            self.metrics.audit_events_total.labels(
                event_type=event_type.value,
                severity=severity.value
            ).inc()
            
            # Check for security-sensitive events
            if self._is_security_event(event_type, severity):
                await self.security_event_queue.put(event)
            
            return event_id
            
        except Exception as e:
            self.logger.error(f"Failed to log audit event: {e}")
            raise EnterpriseSecurityException(f"Audit logging failed: {e}")
    
    async def log_authentication_event(
        self,
        user_id: str,
        success: bool,
        method: str,
        ip_address: str,
        user_agent: str,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log authentication event.
        
        Args:
            user_id: User identifier
            success: Whether authentication was successful
            method: Authentication method used
            ip_address: Client IP address
            user_agent: Client user agent
            details: Additional details
        
        Returns:
            Event ID
        """
        event_type = AuditEventType.USER_LOGIN if success else AuditEventType.AUTHENTICATION_FAILURE
        severity = EventSeverity.INFO if success else EventSeverity.WARNING
        
        event_details = {
            "method": method,
            "success": success,
            **(details or {})
        }
        
        return await self.log_event(
            event_type=event_type,
            user_id=user_id,
            action="authenticate",
            details=event_details,
            ip_address=ip_address,
            user_agent=user_agent,
            severity=severity
        )
    
    async def log_data_access(
        self,
        user_id: str,
        resource: str,
        action: str,
        success: bool,
        data_type: str,
        record_count: Optional[int] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """
        Log data access event.
        
        Args:
            user_id: User identifier
            resource: Resource being accessed
            action: Action performed (read, write, delete, etc.)
            success: Whether access was successful
            data_type: Type of data accessed
            record_count: Number of records accessed
            session_id: Session identifier
            ip_address: Client IP address
        
        Returns:
            Event ID
        """
        details = {
            "data_type": data_type,
            "success": success,
            "record_count": record_count
        }
        
        severity = EventSeverity.INFO if success else EventSeverity.WARNING
        
        return await self.log_event(
            event_type=AuditEventType.DATA_ACCESS,
            user_id=user_id,
            resource=resource,
            action=action,
            details=details,
            session_id=session_id,
            ip_address=ip_address,
            severity=severity
        )
    
    async def log_security_violation(
        self,
        user_id: Optional[str],
        violation_type: str,
        description: str,
        risk_level: str,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log security violation event.
        
        Args:
            user_id: User identifier (if known)
            violation_type: Type of security violation
            description: Description of the violation
            risk_level: Risk level (low, medium, high, critical)
            ip_address: Source IP address
            details: Additional violation details
        
        Returns:
            Event ID
        """
        severity_map = {
            "low": EventSeverity.INFO,
            "medium": EventSeverity.WARNING,
            "high": EventSeverity.ERROR,
            "critical": EventSeverity.CRITICAL
        }
        
        event_details = {
            "violation_type": violation_type,
            "description": description,
            "risk_level": risk_level,
            **(details or {})
        }
        
        return await self.log_event(
            event_type=AuditEventType.SECURITY_VIOLATION,
            user_id=user_id,
            action="security_violation",
            details=event_details,
            ip_address=ip_address,
            severity=severity_map.get(risk_level, EventSeverity.WARNING)
        )
    
    async def get_user_activity(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        event_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[AuditEvent]:
        """
        Get user activity logs.
        
        Args:
            user_id: User identifier
            start_date: Start date for search
            end_date: End date for search
            event_types: List of event types to filter
            limit: Maximum number of events to return
        
        Returns:
            List of audit events
        """
        try:
            query = self.supabase.table("audit_events").select("*").eq("user_id", user_id)
            
            if start_date:
                query = query.gte("timestamp", start_date.isoformat())
            
            if end_date:
                query = query.lte("timestamp", end_date.isoformat())
            
            if event_types:
                query = query.in_("event_type", event_types)
            
            result = await query.order("timestamp", desc=True).limit(limit).execute()
            
            events = []
            for row in result.data:
                event = AuditEvent(
                    id=row["id"],
                    event_type=row["event_type"],
                    timestamp=datetime.fromisoformat(row["timestamp"]),
                    user_id=row["user_id"],
                    resource=row.get("resource"),
                    action=row.get("action"),
                    details=row.get("details", {}),
                    ip_address=row.get("ip_address"),
                    user_agent=row.get("user_agent"),
                    session_id=row.get("session_id"),
                    severity=row["severity"],
                    checksum=row.get("checksum")
                )
                events.append(event)
            
            return events
            
        except Exception as e:
            self.logger.error(f"Failed to get user activity: {e}")
            raise EnterpriseSecurityException(f"Get user activity failed: {e}")
    
    async def get_security_events(
        self,
        severity: Optional[EventSeverity] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[AuditEvent]:
        """
        Get security events.
        
        Args:
            severity: Minimum severity level
            start_date: Start date for search
            end_date: End date for search
            limit: Maximum number of events to return
        
        Returns:
            List of security events
        """
        try:
            query = self.supabase.table("audit_events").select("*").in_(
                "event_type", [
                    AuditEventType.AUTHENTICATION_FAILURE.value,
                    AuditEventType.AUTHORIZATION_FAILURE.value,
                    AuditEventType.SECURITY_VIOLATION.value
                ]
            )
            
            if severity:
                severity_levels = {
                    EventSeverity.INFO: ["info", "warning", "error", "critical"],
                    EventSeverity.WARNING: ["warning", "error", "critical"],
                    EventSeverity.ERROR: ["error", "critical"],
                    EventSeverity.CRITICAL: ["critical"]
                }
                query = query.in_("severity", severity_levels[severity])
            
            if start_date:
                query = query.gte("timestamp", start_date.isoformat())
            
            if end_date:
                query = query.lte("timestamp", end_date.isoformat())
            
            result = await query.order("timestamp", desc=True).limit(limit).execute()
            
            events = []
            for row in result.data:
                event = AuditEvent(
                    id=row["id"],
                    event_type=row["event_type"],
                    timestamp=datetime.fromisoformat(row["timestamp"]),
                    user_id=row.get("user_id"),
                    resource=row.get("resource"),
                    action=row.get("action"),
                    details=row.get("details", {}),
                    ip_address=row.get("ip_address"),
                    user_agent=row.get("user_agent"),
                    session_id=row.get("session_id"),
                    severity=row["severity"],
                    checksum=row.get("checksum")
                )
                events.append(event)
            
            return events
            
        except Exception as e:
            self.logger.error(f"Failed to get security events: {e}")
            raise EnterpriseSecurityException(f"Get security events failed: {e}")
    
    async def verify_data_integrity(
        self,
        event_id: str
    ) -> bool:
        """
        Verify data integrity of an audit event.
        
        Args:
            event_id: Event identifier
        
        Returns:
            True if integrity is verified
        """
        try:
            # Get event from database
            result = await self.supabase.table("audit_events").select("*").eq(
                "id", event_id
            ).single().execute()
            
            if not result.data:
                return False
            
            event_data = result.data
            
            # Recalculate checksum
            checksum_data = {
                "event_type": event_data["event_type"],
                "timestamp": event_data["timestamp"],
                "user_id": event_data.get("user_id"),
                "resource": event_data.get("resource"),
                "action": event_data.get("action"),
                "details": event_data.get("details")
            }
            
            calculated_checksum = self._calculate_checksum(checksum_data)
            stored_checksum = event_data.get("checksum")
            
            return calculated_checksum == stored_checksum
            
        except Exception as e:
            self.logger.error(f"Data integrity verification failed: {e}")
            return False
    
    async def generate_compliance_report(
        self,
        report_type: str,
        start_date: datetime,
        end_date: datetime,
        format: str = "json"
    ) -> Dict[str, Any]:
        """
        Generate compliance report.
        
        Args:
            report_type: Type of compliance report (sox, gdpr, hipaa, iso27001)
            start_date: Report start date
            end_date: Report end date
            format: Report format (json, pdf, csv)
        
        Returns:
            Compliance report data
        """
        try:
            # Get events for reporting period
            events = await self._get_events_for_period(start_date, end_date)
            
            # Generate report based on type
            if report_type.lower() == "sox":
                report = await self._generate_sox_report(events, start_date, end_date)
            elif report_type.lower() == "gdpr":
                report = await self._generate_gdpr_report(events, start_date, end_date)
            elif report_type.lower() == "hipaa":
                report = await self._generate_hipaa_report(events, start_date, end_date)
            elif report_type.lower() == "iso27001":
                report = await self._generate_iso27001_report(events, start_date, end_date)
            else:
                raise EnterpriseSecurityException(f"Unknown report type: {report_type}")
            
            # Log report generation
            await self.log_event(
                event_type=AuditEventType.COMPLIANCE_CHECK,
                action="generate_report",
                details={
                    "report_type": report_type,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "format": format,
                    "event_count": len(events)
                }
            )
            
            return report
            
        except Exception as e:
            self.logger.error(f"Compliance report generation failed: {e}")
            raise EnterpriseSecurityException(f"Compliance report generation failed: {e}")
    
    # Private helper methods
    
    def _get_encryption_key(self) -> Fernet:
        """Get encryption key for sensitive audit data."""
        key = self.settings.audit_encryption_key
        if not key:
            key = Fernet.generate_key()
        return Fernet(key)
    
    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate checksum for data integrity."""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    def _is_security_event(self, event_type: AuditEventType, severity: EventSeverity) -> bool:
        """Check if event is security-sensitive."""
        security_event_types = {
            AuditEventType.AUTHENTICATION_FAILURE,
            AuditEventType.AUTHORIZATION_FAILURE,
            AuditEventType.SECURITY_VIOLATION,
            AuditEventType.ADMIN_ACTION,
            AuditEventType.ROLE_CHANGE,
            AuditEventType.PERMISSION_CHANGE
        }
        
        return (
            event_type in security_event_types or
            severity in [EventSeverity.ERROR, EventSeverity.CRITICAL]
        )
    
    async def _initialize_tables(self):
        """Initialize database tables for audit."""
        # Tables are created via Supabase migrations
        pass
    
    async def _initialize_anomaly_detectors(self):
        """Initialize anomaly detection systems."""
        # Initialize basic anomaly detectors
        self.anomaly_detectors = {
            "login_failures": {"threshold": 5, "window": 300},  # 5 failures in 5 minutes
            "data_access_volume": {"threshold": 1000, "window": 3600},  # 1000 accesses in 1 hour
            "admin_actions": {"threshold": 10, "window": 3600}  # 10 admin actions in 1 hour
        }
    
    async def _event_processor(self):
        """Background task to process audit events."""
        while self.is_running:
            try:
                # Process events from queue
                events_to_process = []
                
                # Collect events (batch processing)
                for _ in range(100):  # Process up to 100 events at once
                    try:
                        event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                        events_to_process.append(event)
                    except asyncio.TimeoutError:
                        break
                
                if events_to_process:
                    await self._store_events(events_to_process)
                
            except Exception as e:
                self.logger.error(f"Event processing failed: {e}")
                await asyncio.sleep(5)
    
    async def _store_events(self, events: List[AuditEvent]):
        """Store events in database."""
        try:
            events_data = []
            for event in events:
                event_data = {
                    "id": event.id,
                    "event_type": event.event_type,
                    "timestamp": event.timestamp.isoformat(),
                    "user_id": event.user_id,
                    "resource": event.resource,
                    "action": event.action,
                    "details": event.details,
                    "ip_address": event.ip_address,
                    "user_agent": event.user_agent,
                    "session_id": event.session_id,
                    "severity": event.severity,
                    "checksum": event.checksum
                }
                events_data.append(event_data)
            
            # Bulk insert
            await self.supabase.table("audit_events").insert(events_data).execute()
            
            self.logger.info(f"Stored {len(events)} audit events")
            
        except Exception as e:
            self.logger.error(f"Failed to store events: {e}")
    
    async def _security_monitor(self):
        """Background task for security monitoring."""
        while self.is_running:
            try:
                # Process security events
                while not self.security_event_queue.empty():
                    event = await self.security_event_queue.get()
                    await self._analyze_security_event(event)
                
                await asyncio.sleep(10)
                
            except Exception as e:
                self.logger.error(f"Security monitoring failed: {e}")
                await asyncio.sleep(30)
    
    async def _analyze_security_event(self, event: AuditEvent):
        """Analyze security event for threats."""
        # Basic threat detection logic
        # In production, this would use ML models and complex rules
        
        if event.event_type == AuditEventType.AUTHENTICATION_FAILURE.value:
            # Check for brute force attacks
            await self._check_brute_force(event)
        
        elif event.event_type == AuditEventType.SECURITY_VIOLATION.value:
            # Immediate alert for security violations
            await self._create_security_alert(event, "high")
    
    async def _check_brute_force(self, event: AuditEvent):
        """Check for brute force attacks."""
        # Count recent failures from same IP
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        
        result = await self.supabase.table("audit_events").select("id").eq(
            "event_type", AuditEventType.AUTHENTICATION_FAILURE.value
        ).eq("ip_address", event.ip_address).gte(
            "timestamp", one_hour_ago.isoformat()
        ).execute()
        
        failure_count = len(result.data)
        
        if failure_count >= 10:  # 10 failures in 1 hour
            await self._create_security_alert(event, "critical", f"Brute force attack detected: {failure_count} failures")
    
    async def _create_security_alert(self, event: AuditEvent, severity: str, description: str = None):
        """Create security alert."""
        alert = {
            "id": str(uuid.uuid4()),
            "event_id": event.id,
            "severity": severity,
            "description": description or f"Security event: {event.event_type}",
            "created_at": datetime.utcnow().isoformat(),
            "resolved": False
        }
        
        self.security_alerts.append(alert)
        
        # Store alert
        await self.supabase.table("security_alerts").insert(alert).execute()
        
        self.logger.warning(f"Security alert created: {alert['description']}")
    
    async def _data_integrity_checker(self):
        """Background task for data integrity checking."""
        while self.is_running:
            try:
                # Check integrity of recent events
                one_day_ago = datetime.utcnow() - timedelta(days=1)
                
                result = await self.supabase.table("audit_events").select("id").gte(
                    "timestamp", one_day_ago.isoformat()
                ).limit(100).execute()
                
                integrity_issues = 0
                for row in result.data:
                    if not await self.verify_data_integrity(row["id"]):
                        integrity_issues += 1
                
                if integrity_issues > 0:
                    await self.log_event(
                        event_type=AuditEventType.SECURITY_VIOLATION,
                        action="data_integrity_check",
                        details={"integrity_issues": integrity_issues},
                        severity=EventSeverity.ERROR
                    )
                
                # Sleep for 4 hours
                await asyncio.sleep(14400)
                
            except Exception as e:
                self.logger.error(f"Data integrity check failed: {e}")
                await asyncio.sleep(3600)
    
    async def _audit_archival(self):
        """Background task for audit log archival."""
        while self.is_running:
            try:
                # Archive old audit logs
                archive_date = datetime.utcnow() - self.archival_threshold
                
                # Move old events to archive table
                old_events = await self.supabase.table("audit_events").select("*").lt(
                    "timestamp", archive_date.isoformat()
                ).limit(1000).execute()
                
                if old_events.data:
                    # Copy to archive
                    await self.supabase.table("audit_events_archive").insert(old_events.data).execute()
                    
                    # Delete from main table
                    event_ids = [event["id"] for event in old_events.data]
                    await self.supabase.table("audit_events").delete().in_("id", event_ids).execute()
                    
                    self.logger.info(f"Archived {len(old_events.data)} audit events")
                
                # Sleep for 24 hours
                await asyncio.sleep(86400)
                
            except Exception as e:
                self.logger.error(f"Audit archival failed: {e}")
                await asyncio.sleep(3600)
    
    async def _compliance_reporter(self):
        """Background task for compliance reporting."""
        while self.is_running:
            try:
                # Generate weekly compliance summaries
                # This would run compliance checks and generate reports
                
                # Sleep for 7 days
                await asyncio.sleep(604800)
                
            except Exception as e:
                self.logger.error(f"Compliance reporting failed: {e}")
                await asyncio.sleep(3600)
    
    async def _flush_event_queue(self):
        """Flush remaining events in queue."""
        try:
            remaining_events = []
            
            while not self.event_queue.empty():
                event = await self.event_queue.get()
                remaining_events.append(event)
            
            if remaining_events:
                await self._store_events(remaining_events)
                self.logger.info(f"Flushed {len(remaining_events)} remaining events")
            
        except Exception as e:
            self.logger.error(f"Failed to flush event queue: {e}")
    
    async def _get_events_for_period(self, start_date: datetime, end_date: datetime) -> List[AuditEvent]:
        """Get all events for a time period."""
        result = await self.supabase.table("audit_events").select("*").gte(
            "timestamp", start_date.isoformat()
        ).lte("timestamp", end_date.isoformat()).execute()
        
        events = []
        for row in result.data:
            event = AuditEvent(
                id=row["id"],
                event_type=row["event_type"],
                timestamp=datetime.fromisoformat(row["timestamp"]),
                user_id=row.get("user_id"),
                resource=row.get("resource"),
                action=row.get("action"),
                details=row.get("details", {}),
                ip_address=row.get("ip_address"),
                user_agent=row.get("user_agent"),
                session_id=row.get("session_id"),
                severity=row["severity"],
                checksum=row.get("checksum")
            )
            events.append(event)
        
        return events
    
    async def _generate_sox_report(self, events: List[AuditEvent], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate SOX compliance report."""
        # SOX compliance focuses on financial controls and data integrity
        return {
            "report_type": "SOX Compliance Report",
            "period": {"start": start_date.isoformat(), "end": end_date.isoformat()},
            "total_events": len(events),
            "financial_access_events": len([e for e in events if "financial" in str(e.details).lower()]),
            "admin_changes": len([e for e in events if e.event_type == AuditEventType.ADMIN_ACTION.value]),
            "data_integrity_checks": len([e for e in events if "integrity" in str(e.details).lower()]),
            "security_violations": len([e for e in events if e.event_type == AuditEventType.SECURITY_VIOLATION.value])
        }
    
    async def _generate_gdpr_report(self, events: List[AuditEvent], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate GDPR compliance report."""
        # GDPR focuses on personal data access and user rights
        return {
            "report_type": "GDPR Compliance Report",
            "period": {"start": start_date.isoformat(), "end": end_date.isoformat()},
            "total_events": len(events),
            "personal_data_access": len([e for e in events if "personal" in str(e.details).lower()]),
            "data_deletion_requests": len([e for e in events if "delete" in e.action]),
            "consent_changes": len([e for e in events if "consent" in str(e.details).lower()]),
            "data_exports": len([e for e in events if "export" in e.action])
        }
    
    async def _generate_hipaa_report(self, events: List[AuditEvent], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate HIPAA compliance report."""
        # HIPAA focuses on healthcare data protection
        return {
            "report_type": "HIPAA Compliance Report",
            "period": {"start": start_date.isoformat(), "end": end_date.isoformat()},
            "total_events": len(events),
            "phi_access_events": len([e for e in events if "phi" in str(e.details).lower()]),
            "healthcare_data_access": len([e for e in events if "health" in str(e.details).lower()]),
            "authorization_failures": len([e for e in events if e.event_type == AuditEventType.AUTHORIZATION_FAILURE.value]),
            "security_incidents": len([e for e in events if e.event_type == AuditEventType.SECURITY_VIOLATION.value])
        }
    
    async def _generate_iso27001_report(self, events: List[AuditEvent], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate ISO 27001 compliance report."""
        # ISO 27001 focuses on information security management
        return {
            "report_type": "ISO 27001 Compliance Report",
            "period": {"start": start_date.isoformat(), "end": end_date.isoformat()},
            "total_events": len(events),
            "security_events": len([e for e in events if e.event_type == AuditEventType.SECURITY_VIOLATION.value]),
            "access_control_events": len([e for e in events if e.event_type in [AuditEventType.AUTHORIZATION_FAILURE.value, AuditEventType.DATA_ACCESS.value]]),
            "system_changes": len([e for e in events if e.event_type == AuditEventType.SYSTEM_CONFIGURATION.value]),
            "incident_response": len([e for e in events if "incident" in str(e.details).lower()])
        }