"""
Compliance Service - Enterprise compliance management and reporting.

Provides comprehensive compliance capabilities including:
- SOX (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) compliance
- GDPR (General Data Protection Regulation) compliance
- HIPAA (Health Insurance Portability and Accountability Act) compliance
- ISO 27001 information security management
- Automated compliance monitoring
- Real-time violation detection
- Compliance reporting and dashboards
"""

import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import uuid

import structlog
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import pandas as pd

from core.config import get_settings
from core.exceptions import EnterpriseSecurityException
from core.monitoring import EnterpriseSecurityMetrics
from models.compliance_schemas import (
    ComplianceFramework, ComplianceRule, ComplianceViolation,
    ComplianceReport, ComplianceStatus, RiskAssessment
)


class ComplianceFrameworkType(Enum):
    """Supported compliance frameworks."""
    SOX = "sox"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    ISO27001 = "iso27001"
    PCI_DSS = "pci_dss"
    CCPA = "ccpa"


class ViolationSeverity(Enum):
    """Compliance violation severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceService:
    """
    Comprehensive compliance service providing enterprise-grade
    compliance monitoring, reporting, and management.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = EnterpriseSecurityMetrics()
        
        # Compliance frameworks
        self.active_frameworks: Set[ComplianceFrameworkType] = set()
        self.compliance_rules: Dict[str, List[ComplianceRule]] = {}
        
        # Violation tracking
        self.active_violations: List[ComplianceViolation] = []
        self.violation_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        
        # Reporting
        self.report_templates = {}
        self.scheduled_reports = []
        
        # Monitoring intervals
        self.monitoring_interval = timedelta(minutes=15)
        self.reporting_interval = timedelta(days=1)
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the compliance service."""
        self.logger.info("Starting Compliance Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Load compliance frameworks
            await self._load_compliance_frameworks()
            
            # Initialize compliance rules
            await self._initialize_compliance_rules()
            
            # Load report templates
            await self._load_report_templates()
            
            # Start background tasks
            self.background_tasks.extend([
                asyncio.create_task(self._compliance_monitor()),
                asyncio.create_task(self._violation_processor()),
                asyncio.create_task(self._automated_reporting()),
                asyncio.create_task(self._risk_assessment()),
                asyncio.create_task(self._data_retention_monitor())
            ])
            
            self.is_running = True
            self.logger.info("Compliance Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start compliance service: {e}")
            raise EnterpriseSecurityException(f"Compliance service startup failed: {e}")
    
    async def stop(self):
        """Stop the compliance service."""
        self.logger.info("Stopping Compliance Service")
        
        self.is_running = False
        
        # Process remaining violations
        await self._flush_violation_queue()
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("Compliance Service stopped")
    
    async def enable_framework(self, framework: ComplianceFrameworkType) -> bool:
        """
        Enable a compliance framework.
        
        Args:
            framework: Compliance framework to enable
        
        Returns:
            True if framework was successfully enabled
        """
        try:
            if framework in self.active_frameworks:
                return True
            
            # Add framework
            self.active_frameworks.add(framework)
            
            # Load framework-specific rules
            await self._load_framework_rules(framework)
            
            # Store in database
            framework_data = {
                "id": str(uuid.uuid4()),
                "framework_type": framework.value,
                "enabled": True,
                "enabled_at": datetime.utcnow().isoformat(),
                "configuration": self._get_default_framework_config(framework)
            }
            
            await self.supabase.table("compliance_frameworks").insert(framework_data).execute()
            
            self.logger.info(f"Compliance framework enabled: {framework.value}")
            
            # Update metrics
            self.metrics.compliance_frameworks_enabled.inc()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to enable compliance framework: {e}")
            raise EnterpriseSecurityException(f"Framework enablement failed: {e}")
    
    async def check_compliance(
        self,
        framework: Optional[ComplianceFrameworkType] = None
    ) -> Dict[str, Any]:
        """
        Check compliance status.
        
        Args:
            framework: Specific framework to check (all if None)
        
        Returns:
            Compliance status and violations
        """
        try:
            frameworks_to_check = [framework] if framework else list(self.active_frameworks)
            
            compliance_status = {}
            total_violations = 0
            
            for fw in frameworks_to_check:
                if fw not in self.active_frameworks:
                    continue
                
                # Check framework-specific compliance
                framework_status = await self._check_framework_compliance(fw)
                compliance_status[fw.value] = framework_status
                total_violations += framework_status.get("violation_count", 0)
            
            overall_status = {
                "overall_compliant": total_violations == 0,
                "total_violations": total_violations,
                "frameworks": compliance_status,
                "last_checked": datetime.utcnow().isoformat()
            }
            
            # Store compliance check
            await self._store_compliance_check(overall_status)
            
            return overall_status
            
        except Exception as e:
            self.logger.error(f"Compliance check failed: {e}")
            raise EnterpriseSecurityException(f"Compliance check failed: {e}")
    
    async def report_violation(
        self,
        framework: ComplianceFrameworkType,
        rule_id: str,
        description: str,
        severity: ViolationSeverity,
        evidence: Dict[str, Any],
        affected_resources: List[str]
    ) -> str:
        """
        Report a compliance violation.
        
        Args:
            framework: Compliance framework
            rule_id: Rule that was violated
            description: Description of the violation
            severity: Violation severity
            evidence: Evidence of the violation
            affected_resources: List of affected resources
        
        Returns:
            Violation ID
        """
        try:
            violation_id = str(uuid.uuid4())
            
            violation = ComplianceViolation(
                id=violation_id,
                framework=framework.value,
                rule_id=rule_id,
                description=description,
                severity=severity.value,
                evidence=evidence,
                affected_resources=affected_resources,
                detected_at=datetime.utcnow(),
                status="open",
                risk_score=self._calculate_risk_score(severity, len(affected_resources))
            )
            
            # Add to violation queue
            await self.violation_queue.put(violation)
            
            # Add to active violations
            self.active_violations.append(violation)
            
            self.logger.warning(
                f"Compliance violation reported: {description}",
                framework=framework.value,
                severity=severity.value,
                violation_id=violation_id
            )
            
            # Update metrics
            self.metrics.compliance_violations_total.labels(
                framework=framework.value,
                severity=severity.value
            ).inc()
            
            # Send immediate alert for critical violations
            if severity == ViolationSeverity.CRITICAL:
                await self._send_critical_violation_alert(violation)
            
            return violation_id
            
        except Exception as e:
            self.logger.error(f"Failed to report compliance violation: {e}")
            raise EnterpriseSecurityException(f"Violation reporting failed: {e}")
    
    async def generate_compliance_report(
        self,
        framework: ComplianceFrameworkType,
        start_date: datetime,
        end_date: datetime,
        format: str = "pdf"
    ) -> Dict[str, Any]:
        """
        Generate comprehensive compliance report.
        
        Args:
            framework: Compliance framework
            start_date: Report start date
            end_date: Report end date
            format: Report format (pdf, json, excel)
        
        Returns:
            Report data and metadata
        """
        try:
            # Collect data for report
            report_data = await self._collect_report_data(framework, start_date, end_date)
            
            # Generate report based on format
            if format.lower() == "pdf":
                report_file = await self._generate_pdf_report(framework, report_data)
            elif format.lower() == "excel":
                report_file = await self._generate_excel_report(framework, report_data)
            else:  # JSON
                report_file = None
            
            # Create report metadata
            report_metadata = {
                "id": str(uuid.uuid4()),
                "framework": framework.value,
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "format": format,
                "generated_at": datetime.utcnow().isoformat(),
                "data": report_data,
                "file_path": report_file
            }
            
            # Store report metadata
            await self.supabase.table("compliance_reports").insert(report_metadata).execute()
            
            self.logger.info(
                f"Compliance report generated: {framework.value}",
                format=format,
                period=f"{start_date.date()} to {end_date.date()}"
            )
            
            return report_metadata
            
        except Exception as e:
            self.logger.error(f"Compliance report generation failed: {e}")
            raise EnterpriseSecurityException(f"Report generation failed: {e}")
    
    async def get_violations(
        self,
        framework: Optional[ComplianceFrameworkType] = None,
        severity: Optional[ViolationSeverity] = None,
        status: Optional[str] = None,
        limit: int = 100
    ) -> List[ComplianceViolation]:
        """
        Get compliance violations.
        
        Args:
            framework: Filter by framework
            severity: Filter by severity
            status: Filter by status
            limit: Maximum number of violations to return
        
        Returns:
            List of compliance violations
        """
        try:
            query = self.supabase.table("compliance_violations").select("*")
            
            if framework:
                query = query.eq("framework", framework.value)
            
            if severity:
                query = query.eq("severity", severity.value)
            
            if status:
                query = query.eq("status", status)
            
            result = await query.order("detected_at", desc=True).limit(limit).execute()
            
            violations = []
            for row in result.data:
                violation = ComplianceViolation(
                    id=row["id"],
                    framework=row["framework"],
                    rule_id=row["rule_id"],
                    description=row["description"],
                    severity=row["severity"],
                    evidence=row.get("evidence", {}),
                    affected_resources=row.get("affected_resources", []),
                    detected_at=datetime.fromisoformat(row["detected_at"]),
                    status=row["status"],
                    risk_score=row.get("risk_score", 0),
                    resolved_at=datetime.fromisoformat(row["resolved_at"]) if row.get("resolved_at") else None
                )
                violations.append(violation)
            
            return violations
            
        except Exception as e:
            self.logger.error(f"Failed to get violations: {e}")
            raise EnterpriseSecurityException(f"Get violations failed: {e}")
    
    async def resolve_violation(self, violation_id: str, resolution_notes: str) -> bool:
        """
        Resolve a compliance violation.
        
        Args:
            violation_id: Violation identifier
            resolution_notes: Notes about the resolution
        
        Returns:
            True if violation was resolved
        """
        try:
            # Update violation status
            await self.supabase.table("compliance_violations").update({
                "status": "resolved",
                "resolved_at": datetime.utcnow().isoformat(),
                "resolution_notes": resolution_notes
            }).eq("id", violation_id).execute()
            
            # Remove from active violations
            self.active_violations = [
                v for v in self.active_violations if v.id != violation_id
            ]
            
            self.logger.info(f"Compliance violation resolved: {violation_id}")
            
            # Update metrics
            self.metrics.compliance_violations_resolved_total.inc()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to resolve violation: {e}")
            raise EnterpriseSecurityException(f"Violation resolution failed: {e}")
    
    async def get_compliance_dashboard(self) -> Dict[str, Any]:
        """
        Get compliance dashboard data.
        
        Returns:
            Dashboard data with metrics and status
        """
        try:
            dashboard_data = {
                "active_frameworks": [fw.value for fw in self.active_frameworks],
                "total_violations": len(self.active_violations),
                "violations_by_severity": {},
                "violations_by_framework": {},
                "compliance_score": 0,
                "risk_assessment": {},
                "recent_reports": [],
                "upcoming_deadlines": []
            }
            
            # Calculate violation statistics
            for violation in self.active_violations:
                # By severity
                severity = violation.severity
                dashboard_data["violations_by_severity"][severity] = \
                    dashboard_data["violations_by_severity"].get(severity, 0) + 1
                
                # By framework
                framework = violation.framework
                dashboard_data["violations_by_framework"][framework] = \
                    dashboard_data["violations_by_framework"].get(framework, 0) + 1
            
            # Calculate compliance score (0-100)
            total_checks = len(self.active_frameworks) * 10  # Assume 10 checks per framework
            total_violations = len(self.active_violations)
            dashboard_data["compliance_score"] = max(0, 100 - (total_violations * 5))
            
            # Get recent reports
            recent_reports = await self.supabase.table("compliance_reports").select(
                "id, framework, generated_at, format"
            ).order("generated_at", desc=True).limit(5).execute()
            
            dashboard_data["recent_reports"] = recent_reports.data
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"Failed to get compliance dashboard: {e}")
            raise EnterpriseSecurityException(f"Dashboard generation failed: {e}")
    
    # Private helper methods
    
    async def _initialize_tables(self):
        """Initialize database tables for compliance."""
        # Tables are created via Supabase migrations
        pass
    
    async def _load_compliance_frameworks(self):
        """Load enabled compliance frameworks from database."""
        try:
            result = await self.supabase.table("compliance_frameworks").select("*").eq(
                "enabled", True
            ).execute()
            
            for row in result.data:
                framework_type = ComplianceFrameworkType(row["framework_type"])
                self.active_frameworks.add(framework_type)
            
            self.logger.info(f"Loaded {len(self.active_frameworks)} active compliance frameworks")
            
        except Exception as e:
            self.logger.error(f"Failed to load compliance frameworks: {e}")
    
    async def _initialize_compliance_rules(self):
        """Initialize compliance rules for all active frameworks."""
        for framework in self.active_frameworks:
            await self._load_framework_rules(framework)
    
    async def _load_framework_rules(self, framework: ComplianceFrameworkType):
        """Load rules for a specific compliance framework."""
        try:
            if framework == ComplianceFrameworkType.SOX:
                rules = self._get_sox_rules()
            elif framework == ComplianceFrameworkType.GDPR:
                rules = self._get_gdpr_rules()
            elif framework == ComplianceFrameworkType.HIPAA:
                rules = self._get_hipaa_rules()
            elif framework == ComplianceFrameworkType.ISO27001:
                rules = self._get_iso27001_rules()
            else:
                rules = []
            
            self.compliance_rules[framework.value] = rules
            
            self.logger.info(f"Loaded {len(rules)} rules for {framework.value}")
            
        except Exception as e:
            self.logger.error(f"Failed to load rules for {framework.value}: {e}")
    
    def _get_sox_rules(self) -> List[ComplianceRule]:
        """Get SOX compliance rules."""
        return [
            ComplianceRule(
                id="sox_001",
                name="Financial Data Access Control",
                description="All access to financial data must be logged and authorized",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_financial_access_control
            ),
            ComplianceRule(
                id="sox_002",
                name="Data Integrity Verification",
                description="Financial data integrity must be verified regularly",
                severity=ViolationSeverity.CRITICAL,
                check_function=self._check_data_integrity
            ),
            ComplianceRule(
                id="sox_003",
                name="Change Management",
                description="All system changes must be documented and approved",
                severity=ViolationSeverity.MEDIUM,
                check_function=self._check_change_management
            )
        ]
    
    def _get_gdpr_rules(self) -> List[ComplianceRule]:
        """Get GDPR compliance rules."""
        return [
            ComplianceRule(
                id="gdpr_001",
                name="Data Processing Consent",
                description="Personal data processing must have valid consent",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_data_consent
            ),
            ComplianceRule(
                id="gdpr_002",
                name="Data Retention Limits",
                description="Personal data must not be retained beyond necessary period",
                severity=ViolationSeverity.MEDIUM,
                check_function=self._check_data_retention
            ),
            ComplianceRule(
                id="gdpr_003",
                name="Right to Deletion",
                description="Users must be able to request data deletion",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_deletion_capability
            )
        ]
    
    def _get_hipaa_rules(self) -> List[ComplianceRule]:
        """Get HIPAA compliance rules."""
        return [
            ComplianceRule(
                id="hipaa_001",
                name="PHI Access Authorization",
                description="Access to PHI must be authorized and logged",
                severity=ViolationSeverity.CRITICAL,
                check_function=self._check_phi_access
            ),
            ComplianceRule(
                id="hipaa_002",
                name="PHI Encryption",
                description="PHI must be encrypted at rest and in transit",
                severity=ViolationSeverity.CRITICAL,
                check_function=self._check_phi_encryption
            ),
            ComplianceRule(
                id="hipaa_003",
                name="Audit Trail Requirements",
                description="All PHI access must have comprehensive audit trails",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_audit_trails
            )
        ]
    
    def _get_iso27001_rules(self) -> List[ComplianceRule]:
        """Get ISO 27001 compliance rules."""
        return [
            ComplianceRule(
                id="iso_001",
                name="Access Control Policy",
                description="Access control policies must be implemented and enforced",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_access_control_policy
            ),
            ComplianceRule(
                id="iso_002",
                name="Security Incident Response",
                description="Security incidents must be detected and responded to",
                severity=ViolationSeverity.HIGH,
                check_function=self._check_incident_response
            ),
            ComplianceRule(
                id="iso_003",
                name="Risk Assessment",
                description="Regular risk assessments must be conducted",
                severity=ViolationSeverity.MEDIUM,
                check_function=self._check_risk_assessment
            )
        ]
    
    def _get_default_framework_config(self, framework: ComplianceFrameworkType) -> Dict[str, Any]:
        """Get default configuration for a compliance framework."""
        configs = {
            ComplianceFrameworkType.SOX: {
                "financial_data_retention_years": 7,
                "audit_frequency_days": 30,
                "change_approval_required": True
            },
            ComplianceFrameworkType.GDPR: {
                "consent_tracking_enabled": True,
                "data_retention_days": 365,
                "deletion_request_processing_days": 30
            },
            ComplianceFrameworkType.HIPAA: {
                "phi_encryption_required": True,
                "audit_trail_retention_years": 6,
                "access_authorization_required": True
            },
            ComplianceFrameworkType.ISO27001: {
                "risk_assessment_frequency_months": 12,
                "incident_response_time_hours": 24,
                "access_review_frequency_months": 6
            }
        }
        
        return configs.get(framework, {})
    
    async def _check_framework_compliance(self, framework: ComplianceFrameworkType) -> Dict[str, Any]:
        """Check compliance for a specific framework."""
        try:
            rules = self.compliance_rules.get(framework.value, [])
            violations = []
            
            for rule in rules:
                try:
                    # Execute rule check function
                    rule_violations = await rule.check_function()
                    violations.extend(rule_violations)
                except Exception as e:
                    self.logger.error(f"Rule check failed for {rule.id}: {e}")
            
            return {
                "framework": framework.value,
                "total_rules": len(rules),
                "violations": violations,
                "violation_count": len(violations),
                "compliant": len(violations) == 0,
                "last_checked": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Framework compliance check failed: {e}")
            return {
                "framework": framework.value,
                "error": str(e),
                "compliant": False
            }
    
    def _calculate_risk_score(self, severity: ViolationSeverity, resource_count: int) -> int:
        """Calculate risk score for a violation."""
        severity_scores = {
            ViolationSeverity.LOW: 1,
            ViolationSeverity.MEDIUM: 3,
            ViolationSeverity.HIGH: 7,
            ViolationSeverity.CRITICAL: 10
        }
        
        base_score = severity_scores.get(severity, 5)
        resource_multiplier = min(resource_count, 10)  # Cap at 10
        
        return base_score * resource_multiplier
    
    async def _store_compliance_check(self, status: Dict[str, Any]):
        """Store compliance check results."""
        try:
            check_data = {
                "id": str(uuid.uuid4()),
                "check_timestamp": datetime.utcnow().isoformat(),
                "overall_compliant": status["overall_compliant"],
                "total_violations": status["total_violations"],
                "framework_status": status["frameworks"]
            }
            
            await self.supabase.table("compliance_checks").insert(check_data).execute()
            
        except Exception as e:
            self.logger.error(f"Failed to store compliance check: {e}")
    
    async def _compliance_monitor(self):
        """Background task for continuous compliance monitoring."""
        while self.is_running:
            try:
                # Run compliance checks for all active frameworks
                await self.check_compliance()
                
                # Sleep for monitoring interval
                await asyncio.sleep(self.monitoring_interval.total_seconds())
                
            except Exception as e:
                self.logger.error(f"Compliance monitoring failed: {e}")
                await asyncio.sleep(300)  # 5 minutes
    
    async def _violation_processor(self):
        """Background task to process compliance violations."""
        while self.is_running:
            try:
                violations_to_process = []
                
                # Collect violations (batch processing)
                for _ in range(50):  # Process up to 50 violations at once
                    try:
                        violation = await asyncio.wait_for(self.violation_queue.get(), timeout=1.0)
                        violations_to_process.append(violation)
                    except asyncio.TimeoutError:
                        break
                
                if violations_to_process:
                    await self._store_violations(violations_to_process)
                
            except Exception as e:
                self.logger.error(f"Violation processing failed: {e}")
                await asyncio.sleep(5)
    
    async def _store_violations(self, violations: List[ComplianceViolation]):
        """Store violations in database."""
        try:
            violations_data = []
            for violation in violations:
                violation_data = {
                    "id": violation.id,
                    "framework": violation.framework,
                    "rule_id": violation.rule_id,
                    "description": violation.description,
                    "severity": violation.severity,
                    "evidence": violation.evidence,
                    "affected_resources": violation.affected_resources,
                    "detected_at": violation.detected_at.isoformat(),
                    "status": violation.status,
                    "risk_score": violation.risk_score
                }
                violations_data.append(violation_data)
            
            # Bulk insert
            await self.supabase.table("compliance_violations").insert(violations_data).execute()
            
            self.logger.info(f"Stored {len(violations)} compliance violations")
            
        except Exception as e:
            self.logger.error(f"Failed to store violations: {e}")
    
    async def _automated_reporting(self):
        """Background task for automated compliance reporting."""
        while self.is_running:
            try:
                # Generate daily compliance summaries
                today = datetime.utcnow().date()
                yesterday = today - timedelta(days=1)
                
                for framework in self.active_frameworks:
                    await self.generate_compliance_report(
                        framework=framework,
                        start_date=datetime.combine(yesterday, datetime.min.time()),
                        end_date=datetime.combine(today, datetime.min.time()),
                        format="json"
                    )
                
                # Sleep for 24 hours
                await asyncio.sleep(86400)
                
            except Exception as e:
                self.logger.error(f"Automated reporting failed: {e}")
                await asyncio.sleep(3600)  # 1 hour
    
    async def _risk_assessment(self):
        """Background task for risk assessment."""
        while self.is_running:
            try:
                # Perform risk assessment
                # This would analyze violations, trends, and calculate overall risk
                
                # Sleep for 12 hours
                await asyncio.sleep(43200)
                
            except Exception as e:
                self.logger.error(f"Risk assessment failed: {e}")
                await asyncio.sleep(3600)
    
    async def _data_retention_monitor(self):
        """Background task for data retention monitoring."""
        while self.is_running:
            try:
                # Check data retention policies
                # This would identify data that should be deleted according to retention policies
                
                # Sleep for 24 hours
                await asyncio.sleep(86400)
                
            except Exception as e:
                self.logger.error(f"Data retention monitoring failed: {e}")
                await asyncio.sleep(3600)
    
    async def _flush_violation_queue(self):
        """Flush remaining violations in queue."""
        try:
            remaining_violations = []
            
            while not self.violation_queue.empty():
                violation = await self.violation_queue.get()
                remaining_violations.append(violation)
            
            if remaining_violations:
                await self._store_violations(remaining_violations)
                self.logger.info(f"Flushed {len(remaining_violations)} remaining violations")
            
        except Exception as e:
            self.logger.error(f"Failed to flush violation queue: {e}")
    
    async def _send_critical_violation_alert(self, violation: ComplianceViolation):
        """Send alert for critical compliance violations."""
        # This would send alerts via email, Slack, etc.
        self.logger.critical(
            f"CRITICAL COMPLIANCE VIOLATION: {violation.description}",
            framework=violation.framework,
            rule_id=violation.rule_id,
            violation_id=violation.id
        )
    
    async def _collect_report_data(self, framework: ComplianceFrameworkType, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Collect data for compliance report."""
        # Get violations for the period
        violations = await self.get_violations(framework=framework)
        period_violations = [
            v for v in violations
            if start_date <= v.detected_at <= end_date
        ]
        
        # Get compliance checks for the period
        checks_result = await self.supabase.table("compliance_checks").select("*").gte(
            "check_timestamp", start_date.isoformat()
        ).lte("check_timestamp", end_date.isoformat()).execute()
        
        return {
            "framework": framework.value,
            "period": {"start": start_date.isoformat(), "end": end_date.isoformat()},
            "total_violations": len(period_violations),
            "violations_by_severity": self._group_by_severity(period_violations),
            "compliance_checks": len(checks_result.data),
            "violations": [v.__dict__ for v in period_violations]
        }
    
    def _group_by_severity(self, violations: List[ComplianceViolation]) -> Dict[str, int]:
        """Group violations by severity."""
        groups = {}
        for violation in violations:
            severity = violation.severity
            groups[severity] = groups.get(severity, 0) + 1
        return groups
    
    async def _generate_pdf_report(self, framework: ComplianceFrameworkType, data: Dict[str, Any]) -> str:
        """Generate PDF compliance report."""
        # This would generate a comprehensive PDF report
        # For now, return a placeholder path
        return f"/app/reports/compliance_{framework.value}_{datetime.utcnow().strftime('%Y%m%d')}.pdf"
    
    async def _generate_excel_report(self, framework: ComplianceFrameworkType, data: Dict[str, Any]) -> str:
        """Generate Excel compliance report."""
        # This would generate an Excel report with multiple sheets
        # For now, return a placeholder path
        return f"/app/reports/compliance_{framework.value}_{datetime.utcnow().strftime('%Y%m%d')}.xlsx"
    
    async def _load_report_templates(self):
        """Load report templates."""
        # Load templates for different compliance reports
        self.report_templates = {
            "sox": "sox_report_template.html",
            "gdpr": "gdpr_report_template.html",
            "hipaa": "hipaa_report_template.html",
            "iso27001": "iso27001_report_template.html"
        }
    
    # Compliance rule check functions (placeholders)
    
    async def _check_financial_access_control(self) -> List[Dict[str, Any]]:
        """Check SOX financial access control compliance."""
        # This would check if financial data access is properly controlled
        return []
    
    async def _check_data_integrity(self) -> List[Dict[str, Any]]:
        """Check data integrity compliance."""
        # This would verify data integrity measures
        return []
    
    async def _check_change_management(self) -> List[Dict[str, Any]]:
        """Check change management compliance."""
        # This would verify change management processes
        return []
    
    async def _check_data_consent(self) -> List[Dict[str, Any]]:
        """Check GDPR data consent compliance."""
        # This would verify data processing consent
        return []
    
    async def _check_data_retention(self) -> List[Dict[str, Any]]:
        """Check GDPR data retention compliance."""
        # This would check data retention limits
        return []
    
    async def _check_deletion_capability(self) -> List[Dict[str, Any]]:
        """Check GDPR right to deletion compliance."""
        # This would verify deletion capabilities
        return []
    
    async def _check_phi_access(self) -> List[Dict[str, Any]]:
        """Check HIPAA PHI access compliance."""
        # This would verify PHI access controls
        return []
    
    async def _check_phi_encryption(self) -> List[Dict[str, Any]]:
        """Check HIPAA PHI encryption compliance."""
        # This would verify PHI encryption
        return []
    
    async def _check_audit_trails(self) -> List[Dict[str, Any]]:
        """Check audit trail compliance."""
        # This would verify audit trail completeness
        return []
    
    async def _check_access_control_policy(self) -> List[Dict[str, Any]]:
        """Check ISO 27001 access control policy compliance."""
        # This would verify access control policies
        return []
    
    async def _check_incident_response(self) -> List[Dict[str, Any]]:
        """Check ISO 27001 incident response compliance."""
        # This would verify incident response capabilities
        return []
    
    async def _check_risk_assessment(self) -> List[Dict[str, Any]]:
        """Check ISO 27001 risk assessment compliance."""
        # This would verify risk assessment processes
        return []