"""
Single Sign-On (SSO) Service - Comprehensive enterprise authentication.

Provides SSO capabilities including:
- SAML 2.0 authentication
- OpenID Connect (OIDC) integration
- Active Directory integration
- OAuth 2.0 providers
- Multi-factor authentication (MFA)
- Session management
"""

import asyncio
import json
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlencode, urlparse
import uuid
import base64
import hashlib

from authlib.integrations.base_client import BaseApp
from authlib.integrations.httpx_client import AsyncOAuth2Client
from authlib.jose import JsonWebSignature, JsonWebToken
from cryptography.fernet import Fernet
import httpx
import structlog
from saml2 import BINDING_HTTP_POST, BINDING_HTTP_REDIRECT
from saml2.client import Saml2Client
from saml2.config import Config as Saml2Config

from core.config import get_settings
from core.exceptions import EnterpriseSecurityException
from core.monitoring import EnterpriseSecurityMetrics
from models.sso_schemas import (
    SSOProvider, SSOConfig, SAMLRequest, SAMLResponse, 
    OIDCConfig, ActiveDirectoryConfig, SSOSession,
    AuthenticationRequest, AuthenticationResponse
)
from utils.crypto_utils import generate_key_pair, sign_data, verify_signature
from utils.session_utils import create_session, validate_session


class SSOService:
    """
    Comprehensive SSO service handling multiple authentication protocols
    and identity providers.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = EnterpriseSecurityMetrics()
        
        # SSO configuration
        self.sso_config = self._load_sso_config()
        self.encryption_key = self._get_encryption_key()
        
        # Provider clients
        self.saml_clients: Dict[str, Saml2Client] = {}
        self.oidc_clients: Dict[str, AsyncOAuth2Client] = {}
        self.oauth_clients: Dict[str, AsyncOAuth2Client] = {}
        
        # Session management
        self.active_sessions: Dict[str, SSOSession] = {}
        self.session_timeout = timedelta(hours=8)
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the SSO service."""
        self.logger.info("Starting SSO Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Initialize SAML clients
            await self._initialize_saml_clients()
            
            # Initialize OIDC clients
            await self._initialize_oidc_clients()
            
            # Initialize OAuth clients
            await self._initialize_oauth_clients()
            
            # Start background tasks
            self.background_tasks.append(
                asyncio.create_task(self._session_cleanup())
            )
            self.background_tasks.append(
                asyncio.create_task(self._security_monitor())
            )
            
            self.is_running = True
            self.logger.info("SSO Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start SSO service: {e}")
            raise EnterpriseSecurityException(f"SSO service startup failed: {e}")
    
    async def stop(self):
        """Stop the SSO service."""
        self.logger.info("Stopping SSO Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("SSO Service stopped")
    
    async def initiate_saml_authentication(
        self,
        provider_id: str,
        relay_state: Optional[str] = None,
        force_authn: bool = False
    ) -> Dict[str, Any]:
        """
        Initiate SAML authentication with an identity provider.
        
        Args:
            provider_id: SAML provider identifier
            relay_state: Optional relay state for redirect after authentication
            force_authn: Force re-authentication even if user has valid session
        
        Returns:
            SAML authentication request details
        """
        try:
            if provider_id not in self.saml_clients:
                raise EnterpriseSecurityException(f"SAML provider {provider_id} not configured")
            
            client = self.saml_clients[provider_id]
            
            # Generate authentication request
            request_id = str(uuid.uuid4())
            
            # Get SSO URL and authentication request
            sso_url, auth_request = client.prepare_for_authenticate(
                request_id=request_id,
                force_authn=force_authn,
                relay_state=relay_state
            )
            
            # Store request state
            await self._store_saml_request(request_id, provider_id, relay_state)
            
            self.logger.info(
                "SAML authentication initiated",
                provider_id=provider_id,
                request_id=request_id
            )
            
            # Update metrics
            self.metrics.sso_authentication_attempts.labels(
                provider=provider_id, protocol="saml"
            ).inc()
            
            return {
                "request_id": request_id,
                "sso_url": sso_url,
                "auth_request": auth_request,
                "provider_id": provider_id,
                "binding": BINDING_HTTP_REDIRECT
            }
            
        except Exception as e:
            self.logger.error(f"Failed to initiate SAML authentication: {e}")
            self.metrics.sso_authentication_failures.labels(
                provider=provider_id, protocol="saml"
            ).inc()
            raise EnterpriseSecurityException(f"SAML authentication initiation failed: {e}")
    
    async def process_saml_response(
        self,
        saml_response: str,
        relay_state: Optional[str] = None
    ) -> AuthenticationResponse:
        """
        Process SAML authentication response from identity provider.
        
        Args:
            saml_response: Base64 encoded SAML response
            relay_state: Optional relay state from authentication request
        
        Returns:
            Authentication response with user information and session
        """
        try:
            # Decode SAML response
            response_data = base64.b64decode(saml_response)
            
            # Parse SAML response to find provider
            provider_id = await self._identify_saml_provider(response_data)
            
            if provider_id not in self.saml_clients:
                raise EnterpriseSecurityException("Unknown SAML provider")
            
            client = self.saml_clients[provider_id]
            
            # Process the response
            authn_response = client.parse_authn_request_response(
                response_data, BINDING_HTTP_POST
            )
            
            # Validate response
            if not authn_response.is_valid():
                raise EnterpriseSecurityException("Invalid SAML response")
            
            # Extract user information
            user_info = self._extract_saml_user_info(authn_response)
            
            # Create or update user
            user = await self._create_or_update_sso_user(user_info, provider_id, "saml")
            
            # Create session
            session = await self._create_sso_session(user, provider_id, "saml")
            
            self.logger.info(
                "SAML authentication successful",
                provider_id=provider_id,
                user_id=user["id"],
                session_id=session.session_id
            )
            
            # Update metrics
            self.metrics.sso_authentication_successes.labels(
                provider=provider_id, protocol="saml"
            ).inc()
            
            return AuthenticationResponse(
                success=True,
                user=user,
                session=session,
                provider_id=provider_id,
                protocol="saml",
                relay_state=relay_state
            )
            
        except Exception as e:
            self.logger.error(f"Failed to process SAML response: {e}")
            self.metrics.sso_authentication_failures.labels(
                provider="unknown", protocol="saml"
            ).inc()
            raise EnterpriseSecurityException(f"SAML response processing failed: {e}")
    
    async def initiate_oidc_authentication(
        self,
        provider_id: str,
        redirect_uri: str,
        state: Optional[str] = None,
        nonce: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Initiate OpenID Connect authentication.
        
        Args:
            provider_id: OIDC provider identifier
            redirect_uri: Redirect URI after authentication
            state: Optional state parameter
            nonce: Optional nonce for security
        
        Returns:
            OIDC authentication request details
        """
        try:
            if provider_id not in self.oidc_clients:
                raise EnterpriseSecurityException(f"OIDC provider {provider_id} not configured")
            
            client = self.oidc_clients[provider_id]
            
            # Generate state and nonce if not provided
            if not state:
                state = str(uuid.uuid4())
            if not nonce:
                nonce = str(uuid.uuid4())
            
            # Create authorization URL
            authorization_url = client.create_authorization_url(
                redirect_uri=redirect_uri,
                scope="openid profile email",
                state=state,
                nonce=nonce
            )
            
            # Store request state
            await self._store_oidc_request(state, provider_id, redirect_uri, nonce)
            
            self.logger.info(
                "OIDC authentication initiated",
                provider_id=provider_id,
                state=state
            )
            
            # Update metrics
            self.metrics.sso_authentication_attempts.labels(
                provider=provider_id, protocol="oidc"
            ).inc()
            
            return {
                "authorization_url": authorization_url,
                "state": state,
                "nonce": nonce,
                "provider_id": provider_id
            }
            
        except Exception as e:
            self.logger.error(f"Failed to initiate OIDC authentication: {e}")
            self.metrics.sso_authentication_failures.labels(
                provider=provider_id, protocol="oidc"
            ).inc()
            raise EnterpriseSecurityException(f"OIDC authentication initiation failed: {e}")
    
    async def process_oidc_callback(
        self,
        provider_id: str,
        code: str,
        state: str,
        redirect_uri: str
    ) -> AuthenticationResponse:
        """
        Process OIDC authentication callback.
        
        Args:
            provider_id: OIDC provider identifier
            code: Authorization code from provider
            state: State parameter from authentication request
            redirect_uri: Redirect URI used in authentication request
        
        Returns:
            Authentication response with user information and session
        """
        try:
            if provider_id not in self.oidc_clients:
                raise EnterpriseSecurityException(f"OIDC provider {provider_id} not configured")
            
            client = self.oidc_clients[provider_id]
            
            # Validate state
            stored_request = await self._get_oidc_request(state)
            if not stored_request or stored_request["provider_id"] != provider_id:
                raise EnterpriseSecurityException("Invalid OIDC state")
            
            # Exchange code for tokens
            token_response = await client.fetch_token(
                code=code,
                redirect_uri=redirect_uri
            )
            
            # Get user info
            user_info_response = await client.get(
                client.server_metadata["userinfo_endpoint"],
                token=token_response
            )
            user_info = user_info_response.json()
            
            # Validate ID token if present
            if "id_token" in token_response:
                await self._validate_id_token(
                    token_response["id_token"],
                    provider_id,
                    stored_request["nonce"]
                )
            
            # Create or update user
            user = await self._create_or_update_sso_user(user_info, provider_id, "oidc")
            
            # Create session
            session = await self._create_sso_session(user, provider_id, "oidc")
            
            self.logger.info(
                "OIDC authentication successful",
                provider_id=provider_id,
                user_id=user["id"],
                session_id=session.session_id
            )
            
            # Update metrics
            self.metrics.sso_authentication_successes.labels(
                provider=provider_id, protocol="oidc"
            ).inc()
            
            return AuthenticationResponse(
                success=True,
                user=user,
                session=session,
                provider_id=provider_id,
                protocol="oidc"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to process OIDC callback: {e}")
            self.metrics.sso_authentication_failures.labels(
                provider=provider_id, protocol="oidc"
            ).inc()
            raise EnterpriseSecurityException(f"OIDC callback processing failed: {e}")
    
    async def authenticate_active_directory(
        self,
        username: str,
        password: str,
        domain: Optional[str] = None
    ) -> AuthenticationResponse:
        """
        Authenticate user against Active Directory.
        
        Args:
            username: Active Directory username
            password: User password
            domain: Optional domain (uses default if not provided)
        
        Returns:
            Authentication response with user information and session
        """
        try:
            ad_config = self.sso_config.get("active_directory")
            if not ad_config:
                raise EnterpriseSecurityException("Active Directory not configured")
            
            # Use provided domain or default
            if not domain:
                domain = ad_config.get("default_domain")
            
            # Authenticate against AD
            user_info = await self._authenticate_ldap(username, password, domain, ad_config)
            
            # Create or update user
            user = await self._create_or_update_sso_user(
                user_info, "active_directory", "ldap"
            )
            
            # Create session
            session = await self._create_sso_session(user, "active_directory", "ldap")
            
            self.logger.info(
                "Active Directory authentication successful",
                username=username,
                domain=domain,
                user_id=user["id"],
                session_id=session.session_id
            )
            
            # Update metrics
            self.metrics.sso_authentication_successes.labels(
                provider="active_directory", protocol="ldap"
            ).inc()
            
            return AuthenticationResponse(
                success=True,
                user=user,
                session=session,
                provider_id="active_directory",
                protocol="ldap"
            )
            
        except Exception as e:
            self.logger.error(f"Active Directory authentication failed: {e}")
            self.metrics.sso_authentication_failures.labels(
                provider="active_directory", protocol="ldap"
            ).inc()
            raise EnterpriseSecurityException(f"Active Directory authentication failed: {e}")
    
    async def validate_session(self, session_id: str) -> Optional[SSOSession]:
        """
        Validate an SSO session.
        
        Args:
            session_id: Session identifier
        
        Returns:
            Valid session or None if invalid/expired
        """
        try:
            # Check in-memory cache first
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                if session.expires_at > datetime.utcnow():
                    return session
                else:
                    # Remove expired session
                    del self.active_sessions[session_id]
            
            # Check database
            result = await self.supabase.table("sso_sessions").select("*").eq(
                "session_id", session_id
            ).single().execute()
            
            if not result.data:
                return None
            
            session_data = result.data
            
            # Check if session is expired
            expires_at = datetime.fromisoformat(session_data["expires_at"])
            if expires_at <= datetime.utcnow():
                # Delete expired session
                await self.supabase.table("sso_sessions").delete().eq(
                    "session_id", session_id
                ).execute()
                return None
            
            # Create session object
            session = SSOSession(
                session_id=session_data["session_id"],
                user_id=session_data["user_id"],
                provider_id=session_data["provider_id"],
                protocol=session_data["protocol"],
                created_at=datetime.fromisoformat(session_data["created_at"]),
                expires_at=expires_at,
                last_activity=datetime.fromisoformat(session_data["last_activity"]),
                metadata=session_data.get("metadata", {})
            )
            
            # Update cache
            self.active_sessions[session_id] = session
            
            # Update last activity
            await self._update_session_activity(session_id)
            
            return session
            
        except Exception as e:
            self.logger.error(f"Session validation failed: {e}")
            return None
    
    async def logout_session(self, session_id: str) -> bool:
        """
        Logout and invalidate an SSO session.
        
        Args:
            session_id: Session identifier to logout
        
        Returns:
            True if session was successfully logged out
        """
        try:
            # Remove from cache
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            # Remove from database
            result = await self.supabase.table("sso_sessions").delete().eq(
                "session_id", session_id
            ).execute()
            
            self.logger.info("Session logged out", session_id=session_id)
            
            # Update metrics
            self.metrics.sso_session_logouts.inc()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Session logout failed: {e}")
            return False
    
    async def get_sso_providers(self) -> List[Dict[str, Any]]:
        """
        Get list of configured SSO providers.
        
        Returns:
            List of available SSO providers with their configurations
        """
        try:
            providers = []
            
            # SAML providers
            for provider_id, config in self.sso_config.get("saml_providers", {}).items():
                providers.append({
                    "id": provider_id,
                    "name": config.get("name", provider_id),
                    "type": "saml",
                    "enabled": config.get("enabled", True),
                    "icon": config.get("icon"),
                    "description": config.get("description")
                })
            
            # OIDC providers
            for provider_id, config in self.sso_config.get("oidc_providers", {}).items():
                providers.append({
                    "id": provider_id,
                    "name": config.get("name", provider_id),
                    "type": "oidc",
                    "enabled": config.get("enabled", True),
                    "icon": config.get("icon"),
                    "description": config.get("description")
                })
            
            # Active Directory
            if self.sso_config.get("active_directory", {}).get("enabled"):
                providers.append({
                    "id": "active_directory",
                    "name": "Active Directory",
                    "type": "ldap",
                    "enabled": True,
                    "icon": "microsoft",
                    "description": "Enterprise Active Directory authentication"
                })
            
            return providers
            
        except Exception as e:
            self.logger.error(f"Failed to get SSO providers: {e}")
            raise EnterpriseSecurityException(f"Failed to get SSO providers: {e}")
    
    # Private helper methods
    
    def _load_sso_config(self) -> Dict[str, Any]:
        """Load SSO configuration from settings and environment."""
        return {
            "saml_providers": {
                "azure_ad": {
                    "name": "Azure Active Directory",
                    "enabled": bool(self.settings.azure_tenant_id),
                    "entity_id": f"https://sts.windows.net/{self.settings.azure_tenant_id}/",
                    "sso_url": f"https://login.microsoftonline.com/{self.settings.azure_tenant_id}/saml2",
                    "certificate": self.settings.azure_saml_certificate
                },
                "okta": {
                    "name": "Okta",
                    "enabled": bool(self.settings.okta_domain),
                    "entity_id": f"http://www.okta.com/{self.settings.okta_app_id}",
                    "sso_url": f"https://{self.settings.okta_domain}/app/{self.settings.okta_app_id}/sso/saml",
                    "certificate": self.settings.okta_saml_certificate
                }
            },
            "oidc_providers": {
                "google": {
                    "name": "Google",
                    "enabled": bool(self.settings.google_client_id),
                    "client_id": self.settings.google_client_id,
                    "client_secret": self.settings.google_client_secret,
                    "discovery_url": "https://accounts.google.com/.well-known/openid_configuration"
                },
                "microsoft": {
                    "name": "Microsoft",
                    "enabled": bool(self.settings.microsoft_client_id),
                    "client_id": self.settings.microsoft_client_id,
                    "client_secret": self.settings.microsoft_client_secret,
                    "discovery_url": f"https://login.microsoftonline.com/{self.settings.azure_tenant_id}/v2.0/.well-known/openid_configuration"
                }
            },
            "active_directory": {
                "enabled": bool(self.settings.ad_server),
                "server": self.settings.ad_server,
                "port": self.settings.ad_port or 389,
                "base_dn": self.settings.ad_base_dn,
                "bind_dn": self.settings.ad_bind_dn,
                "bind_password": self.settings.ad_bind_password,
                "default_domain": self.settings.ad_default_domain
            }
        }
    
    def _get_encryption_key(self) -> Fernet:
        """Get or generate encryption key for session data."""
        key = self.settings.session_encryption_key
        if not key:
            key = Fernet.generate_key()
        return Fernet(key)
    
    async def _initialize_tables(self):
        """Initialize database tables for SSO."""
        # Tables are created via Supabase migrations
        pass
    
    async def _initialize_saml_clients(self):
        """Initialize SAML clients for configured providers."""
        for provider_id, config in self.sso_config.get("saml_providers", {}).items():
            if not config.get("enabled"):
                continue
            
            try:
                # Create SAML configuration
                saml_config = Saml2Config()
                saml_config.load({
                    "entityid": self.settings.saml_entity_id,
                    "service": {
                        "sp": {
                            "endpoints": {
                                "assertion_consumer_service": [
                                    (f"{self.settings.base_url}/api/sso/saml/acs/{provider_id}", BINDING_HTTP_POST)
                                ],
                                "single_logout_service": [
                                    (f"{self.settings.base_url}/api/sso/saml/sls/{provider_id}", BINDING_HTTP_REDIRECT)
                                ]
                            }
                        }
                    },
                    "metadata": {
                        "remote": [
                            {
                                "url": config.get("metadata_url")
                            }
                        ]
                    } if config.get("metadata_url") else {
                        "inline": [
                            {
                                "entity_id": config["entity_id"],
                                "sso_url": config["sso_url"],
                                "certificate": config.get("certificate")
                            }
                        ]
                    }
                })
                
                # Create SAML client
                client = Saml2Client(config=saml_config)
                self.saml_clients[provider_id] = client
                
                self.logger.info(f"SAML client initialized for {provider_id}")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize SAML client for {provider_id}: {e}")
    
    async def _initialize_oidc_clients(self):
        """Initialize OIDC clients for configured providers."""
        for provider_id, config in self.sso_config.get("oidc_providers", {}).items():
            if not config.get("enabled"):
                continue
            
            try:
                # Create OIDC client
                client = AsyncOAuth2Client(
                    client_id=config["client_id"],
                    client_secret=config["client_secret"]
                )
                
                # Load discovery document
                if config.get("discovery_url"):
                    async with httpx.AsyncClient() as http_client:
                        response = await http_client.get(config["discovery_url"])
                        discovery_doc = response.json()
                        client.server_metadata = discovery_doc
                
                self.oidc_clients[provider_id] = client
                
                self.logger.info(f"OIDC client initialized for {provider_id}")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize OIDC client for {provider_id}: {e}")
    
    async def _initialize_oauth_clients(self):
        """Initialize OAuth clients for social providers."""
        # Initialize OAuth clients for social login providers
        pass
    
    async def _store_saml_request(self, request_id: str, provider_id: str, relay_state: Optional[str]):
        """Store SAML request state in database."""
        try:
            await self.supabase.table("saml_requests").insert({
                "request_id": request_id,
                "provider_id": provider_id,
                "relay_state": relay_state,
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(minutes=5)).isoformat()
            }).execute()
        except Exception as e:
            self.logger.error(f"Failed to store SAML request: {e}")
    
    async def _identify_saml_provider(self, response_data: bytes) -> str:
        """Identify SAML provider from response data."""
        try:
            # Parse XML to extract issuer
            root = ET.fromstring(response_data)
            
            # Find issuer element
            issuer = root.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Issuer')
            if issuer is not None:
                issuer_value = issuer.text
                
                # Match against configured providers
                for provider_id, config in self.sso_config.get("saml_providers", {}).items():
                    if config.get("entity_id") == issuer_value:
                        return provider_id
            
            raise EnterpriseSecurityException("Unknown SAML provider")
            
        except ET.ParseError:
            raise EnterpriseSecurityException("Invalid SAML response format")
    
    def _extract_saml_user_info(self, authn_response) -> Dict[str, Any]:
        """Extract user information from SAML response."""
        try:
            # Get user attributes
            attributes = authn_response.get_attributes()
            
            # Extract common user information
            user_info = {
                "email": attributes.get("email", [None])[0] or attributes.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress", [None])[0],
                "first_name": attributes.get("first_name", [None])[0] or attributes.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname", [None])[0],
                "last_name": attributes.get("last_name", [None])[0] or attributes.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname", [None])[0],
                "full_name": attributes.get("full_name", [None])[0] or attributes.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", [None])[0],
                "groups": attributes.get("groups", []) or attributes.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/groups", []),
                "department": attributes.get("department", [None])[0],
                "title": attributes.get("title", [None])[0]
            }
            
            # Get subject (unique identifier)
            subject = authn_response.get_subject()
            if subject:
                user_info["subject"] = subject.text
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"Failed to extract SAML user info: {e}")
            raise EnterpriseSecurityException("Failed to extract user information")
    
    async def _store_oidc_request(self, state: str, provider_id: str, redirect_uri: str, nonce: str):
        """Store OIDC request state in database."""
        try:
            await self.supabase.table("oidc_requests").insert({
                "state": state,
                "provider_id": provider_id,
                "redirect_uri": redirect_uri,
                "nonce": nonce,
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(minutes=5)).isoformat()
            }).execute()
        except Exception as e:
            self.logger.error(f"Failed to store OIDC request: {e}")
    
    async def _get_oidc_request(self, state: str) -> Optional[Dict[str, Any]]:
        """Get OIDC request from database."""
        try:
            result = await self.supabase.table("oidc_requests").select("*").eq(
                "state", state
            ).single().execute()
            
            if result.data:
                # Check if request is expired
                expires_at = datetime.fromisoformat(result.data["expires_at"])
                if expires_at <= datetime.utcnow():
                    # Delete expired request
                    await self.supabase.table("oidc_requests").delete().eq(
                        "state", state
                    ).execute()
                    return None
                
                return result.data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get OIDC request: {e}")
            return None
    
    async def _validate_id_token(self, id_token: str, provider_id: str, nonce: str):
        """Validate OIDC ID token."""
        try:
            # Get provider configuration
            provider_config = self.sso_config.get("oidc_providers", {}).get(provider_id)
            if not provider_config:
                raise EnterpriseSecurityException("Provider not configured")
            
            # Decode and validate JWT
            jwt = JsonWebToken()
            
            # Get JWKS for validation
            async with httpx.AsyncClient() as client:
                jwks_response = await client.get(provider_config.get("jwks_uri"))
                jwks = jwks_response.json()
            
            # Validate token
            claims = jwt.decode(id_token, jwks)
            
            # Validate nonce
            if claims.get("nonce") != nonce:
                raise EnterpriseSecurityException("Invalid nonce")
            
            # Validate issuer
            if claims.get("iss") != provider_config.get("issuer"):
                raise EnterpriseSecurityException("Invalid issuer")
            
            # Validate audience
            if claims.get("aud") != provider_config.get("client_id"):
                raise EnterpriseSecurityException("Invalid audience")
            
            return claims
            
        except Exception as e:
            self.logger.error(f"ID token validation failed: {e}")
            raise EnterpriseSecurityException("ID token validation failed")
    
    async def _authenticate_ldap(self, username: str, password: str, domain: str, ad_config: Dict[str, Any]) -> Dict[str, Any]:
        """Authenticate user against LDAP/Active Directory."""
        try:
            from ldap3 import Server, Connection, ALL, NTLM
            
            # Create LDAP server connection
            server = Server(
                ad_config["server"],
                port=ad_config["port"],
                get_info=ALL,
                use_ssl=ad_config.get("use_ssl", False)
            )
            
            # Create user DN
            user_dn = f"{username}@{domain}" if domain else username
            
            # Authenticate user
            conn = Connection(
                server,
                user=user_dn,
                password=password,
                authentication=NTLM if ad_config.get("use_ntlm") else None,
                auto_bind=True
            )
            
            if not conn.bind():
                raise EnterpriseSecurityException("Authentication failed")
            
            # Search for user details
            search_filter = f"(userPrincipalName={user_dn})"
            conn.search(
                ad_config["base_dn"],
                search_filter,
                attributes=["cn", "mail", "givenName", "sn", "department", "title", "memberOf"]
            )
            
            if not conn.entries:
                raise EnterpriseSecurityException("User not found")
            
            # Extract user information
            entry = conn.entries[0]
            user_info = {
                "email": str(entry.mail) if entry.mail else None,
                "first_name": str(entry.givenName) if entry.givenName else None,
                "last_name": str(entry.sn) if entry.sn else None,
                "full_name": str(entry.cn) if entry.cn else None,
                "department": str(entry.department) if entry.department else None,
                "title": str(entry.title) if entry.title else None,
                "groups": [str(group) for group in entry.memberOf] if entry.memberOf else [],
                "username": username,
                "domain": domain
            }
            
            conn.unbind()
            return user_info
            
        except Exception as e:
            self.logger.error(f"LDAP authentication failed: {e}")
            raise EnterpriseSecurityException(f"LDAP authentication failed: {e}")
    
    async def _create_or_update_sso_user(self, user_info: Dict[str, Any], provider_id: str, protocol: str) -> Dict[str, Any]:
        """Create or update SSO user in database."""
        try:
            # Check if user exists
            email = user_info.get("email")
            if not email:
                raise EnterpriseSecurityException("User email is required")
            
            # Try to find existing user
            existing_user = await self.supabase.table("sso_users").select("*").eq(
                "email", email
            ).single().execute()
            
            user_data = {
                "email": email,
                "first_name": user_info.get("first_name"),
                "last_name": user_info.get("last_name"),
                "full_name": user_info.get("full_name"),
                "department": user_info.get("department"),
                "title": user_info.get("title"),
                "groups": user_info.get("groups", []),
                "provider_id": provider_id,
                "protocol": protocol,
                "last_login": datetime.utcnow().isoformat(),
                "metadata": user_info
            }
            
            if existing_user.data:
                # Update existing user
                user_data["updated_at"] = datetime.utcnow().isoformat()
                result = await self.supabase.table("sso_users").update(user_data).eq(
                    "id", existing_user.data["id"]
                ).execute()
                return result.data[0]
            else:
                # Create new user
                user_data["id"] = str(uuid.uuid4())
                user_data["created_at"] = datetime.utcnow().isoformat()
                result = await self.supabase.table("sso_users").insert(user_data).execute()
                return result.data[0]
            
        except Exception as e:
            self.logger.error(f"Failed to create/update SSO user: {e}")
            raise EnterpriseSecurityException(f"User management failed: {e}")
    
    async def _create_sso_session(self, user: Dict[str, Any], provider_id: str, protocol: str) -> SSOSession:
        """Create SSO session for user."""
        try:
            session_id = str(uuid.uuid4())
            now = datetime.utcnow()
            expires_at = now + self.session_timeout
            
            session_data = {
                "session_id": session_id,
                "user_id": user["id"],
                "provider_id": provider_id,
                "protocol": protocol,
                "created_at": now.isoformat(),
                "expires_at": expires_at.isoformat(),
                "last_activity": now.isoformat(),
                "metadata": {
                    "user_agent": "",  # Will be populated from request
                    "ip_address": "",  # Will be populated from request
                    "provider_id": provider_id
                }
            }
            
            # Store in database
            await self.supabase.table("sso_sessions").insert(session_data).execute()
            
            # Create session object
            session = SSOSession(
                session_id=session_id,
                user_id=user["id"],
                provider_id=provider_id,
                protocol=protocol,
                created_at=now,
                expires_at=expires_at,
                last_activity=now,
                metadata=session_data["metadata"]
            )
            
            # Add to cache
            self.active_sessions[session_id] = session
            
            # Update metrics
            self.metrics.sso_active_sessions.inc()
            
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to create SSO session: {e}")
            raise EnterpriseSecurityException(f"Session creation failed: {e}")
    
    async def _update_session_activity(self, session_id: str):
        """Update session last activity timestamp."""
        try:
            now = datetime.utcnow()
            
            # Update in database
            await self.supabase.table("sso_sessions").update({
                "last_activity": now.isoformat()
            }).eq("session_id", session_id).execute()
            
            # Update in cache
            if session_id in self.active_sessions:
                self.active_sessions[session_id].last_activity = now
            
        except Exception as e:
            self.logger.error(f"Failed to update session activity: {e}")
    
    async def _session_cleanup(self):
        """Background task to clean up expired sessions."""
        while self.is_running:
            try:
                now = datetime.utcnow()
                
                # Remove expired sessions from database
                await self.supabase.table("sso_sessions").delete().lt(
                    "expires_at", now.isoformat()
                ).execute()
                
                # Remove expired sessions from cache
                expired_sessions = [
                    session_id for session_id, session in self.active_sessions.items()
                    if session.expires_at <= now
                ]
                
                for session_id in expired_sessions:
                    del self.active_sessions[session_id]
                    self.metrics.sso_active_sessions.dec()
                
                if expired_sessions:
                    self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
                # Sleep for 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                self.logger.error(f"Session cleanup failed: {e}")
                await asyncio.sleep(60)  # Retry after 1 minute
    
    async def _security_monitor(self):
        """Background task for security monitoring."""
        while self.is_running:
            try:
                # Monitor for suspicious activity
                # This would include rate limiting, brute force detection, etc.
                await asyncio.sleep(60)
                
            except Exception as e:
                self.logger.error(f"Security monitoring failed: {e}")
                await asyncio.sleep(30)