"""
Enterprise Security Service - Main Application

Comprehensive enterprise security platform providing:
- Single Sign-On (SSO) with SAML/OIDC
- Role-Based Access Control (RBAC)
- Comprehensive audit logging
- Compliance reporting (SOX, GDPR, HIPAA, ISO27001)
- Advanced security monitoring and threat detection
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import uvicorn

from core.config import get_settings
from core.exceptions import EnterpriseSecurityException
from core.logging import setup_logging
from core.monitoring import setup_monitoring, EnterpriseSecurityMetrics
from core.security import SecurityMiddleware
from database.connection import DatabaseManager
from services.sso_service import SSOService
from services.rbac_service import RBACService
from services.audit_service import AuditService
from services.compliance_service import ComplianceService
from api.routes import sso, rbac, audit, compliance, admin, health


# Global state
app_state: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    logger = structlog.get_logger(__name__)
    
    try:
        logger.info("Starting Enterprise Security Service")
        
        # Initialize database
        database_manager = DatabaseManager()
        await database_manager.initialize()
        app_state["database"] = database_manager
        
        # Initialize services
        sso_service = SSOService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await sso_service.start()
        app_state["sso_service"] = sso_service
        
        rbac_service = RBACService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await rbac_service.start()
        app_state["rbac_service"] = rbac_service
        
        audit_service = AuditService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await audit_service.start()
        app_state["audit_service"] = audit_service
        
        compliance_service = ComplianceService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await compliance_service.start()
        app_state["compliance_service"] = compliance_service
        
        # Initialize monitoring
        metrics = EnterpriseSecurityMetrics()
        app_state["metrics"] = metrics
        
        logger.info("Enterprise Security Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Enterprise Security Service: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Enterprise Security Service")
        
        # Stop services
        if "sso_service" in app_state:
            await app_state["sso_service"].stop()
        if "rbac_service" in app_state:
            await app_state["rbac_service"].stop()
        if "audit_service" in app_state:
            await app_state["audit_service"].stop()
        if "compliance_service" in app_state:
            await app_state["compliance_service"].stop()
        
        # Close database connections
        if "database" in app_state:
            await app_state["database"].close()
        
        logger.info("Enterprise Security Service stopped")


# Create FastAPI application
def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    # Setup logging
    setup_logging()
    logger = structlog.get_logger(__name__)
    
    # Create app
    app = FastAPI(
        title="Enterprise Security Service",
        description="Comprehensive enterprise security platform with SSO, RBAC, audit logging, and compliance reporting",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        lifespan=lifespan
    )
    
    # Setup monitoring
    setup_monitoring(app)
    
    # Security middleware
    app.add_middleware(SecurityMiddleware)
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts
        )
    
    # Exception handlers
    @app.exception_handler(EnterpriseSecurityException)
    async def security_exception_handler(request: Request, exc: EnterpriseSecurityException):
        logger = structlog.get_logger(__name__)
        logger.error(f"Security error: {exc}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.error_type, "message": str(exc)}
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger = structlog.get_logger(__name__)
        logger.warning(f"HTTP error: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": "http_error", "message": exc.detail}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger = structlog.get_logger(__name__)
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": "internal_error", "message": "Internal server error"}
        )
    
    # Include API routes
    app.include_router(health.router, prefix="/health", tags=["Health"])
    app.include_router(sso.router, prefix="/api/sso", tags=["Single Sign-On"])
    app.include_router(rbac.router, prefix="/api/rbac", tags=["Role-Based Access Control"])
    app.include_router(audit.router, prefix="/api/audit", tags=["Audit Logging"])
    app.include_router(compliance.router, prefix="/api/compliance", tags=["Compliance"])
    app.include_router(admin.router, prefix="/api/admin", tags=["Administration"])
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "Enterprise Security Service",
            "version": "1.0.0",
            "status": "operational",
            "features": [
                "Single Sign-On (SSO)",
                "SAML 2.0 Integration",
                "OpenID Connect (OIDC)",
                "Role-Based Access Control (RBAC)",
                "Comprehensive Audit Logging",
                "Compliance Reporting",
                "Active Directory Integration",
                "Security Monitoring",
                "Threat Detection",
                "Data Loss Prevention"
            ]
        }
    
    return app


# Create app instance
app = create_app()


# Dependency injection functions
def get_sso_service() -> SSOService:
    """Get SSO service instance."""
    if "sso_service" not in app_state:
        raise HTTPException(status_code=503, detail="SSO service not available")
    return app_state["sso_service"]


def get_rbac_service() -> RBACService:
    """Get RBAC service instance."""
    if "rbac_service" not in app_state:
        raise HTTPException(status_code=503, detail="RBAC service not available")
    return app_state["rbac_service"]


def get_audit_service() -> AuditService:
    """Get audit service instance."""
    if "audit_service" not in app_state:
        raise HTTPException(status_code=503, detail="Audit service not available")
    return app_state["audit_service"]


def get_compliance_service() -> ComplianceService:
    """Get compliance service instance."""
    if "compliance_service" not in app_state:
        raise HTTPException(status_code=503, detail="Compliance service not available")
    return app_state["compliance_service"]


def get_metrics() -> EnterpriseSecurityMetrics:
    """Get metrics instance."""
    if "metrics" not in app_state:
        raise HTTPException(status_code=503, detail="Metrics service not available")
    return app_state["metrics"]


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        workers=1 if settings.environment == "development" else 4,
        log_config=None,  # Use our custom logging
        access_log=False  # Disable default access log
    )