"""
Enterprise Security Service Security Middleware

Security middleware for comprehensive protection of the enterprise security service.
"""

import time
import hashlib
import secrets
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from fastapi import Request, Response, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

from core.config import get_settings
from core.exceptions import SecurityViolationException, RateLimitException


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive security middleware providing multiple layers of protection.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        
        # Rate limiting storage
        self.rate_limit_storage: Dict[str, List[float]] = {}
        
        # Security headers
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        }
        
        if self.settings.enable_hsts:
            self.security_headers["Strict-Transport-Security"] = f"max-age={self.settings.hsts_max_age}; includeSubDomains"
        
        # Suspicious patterns
        self.suspicious_patterns = [
            # SQL Injection patterns
            r"(\bunion\b|\bselect\b|\binsert\b|\bdelete\b|\bupdate\b|\bdrop\b)",
            # XSS patterns
            r"(<script|javascript:|onload=|onerror=)",
            # Path traversal patterns
            r"(\.\./|\.\.\\|/etc/|/proc/|/sys/)",
            # Command injection patterns
            r"(;|\||&|\$\(|\`)",
        ]
        
        # Blocked user agents
        self.blocked_user_agents = [
            "sqlmap",
            "nikto",
            "nmap",
            "masscan",
            "nuclei",
            "gobuster",
            "dirb",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Process request through security checks."""
        start_time = time.time()
        
        try:
            # Extract client information
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # Security checks
            await self._check_user_agent(user_agent, client_ip)
            await self._check_request_size(request)
            await self._check_suspicious_patterns(request)
            await self._check_rate_limit(client_ip, request.url.path)
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            for header, value in self.security_headers.items():
                response.headers[header] = value
            
            # Log successful request
            processing_time = time.time() - start_time
            self.logger.info(
                "Request processed",
                method=request.method,
                path=request.url.path,
                client_ip=client_ip,
                status_code=response.status_code,
                processing_time=processing_time
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(
                "Security middleware error",
                error=str(e),
                client_ip=client_ip,
                path=request.url.path,
                method=request.method
            )
            raise HTTPException(status_code=500, detail="Internal server error")
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to client host
        return request.client.host if request.client else "unknown"
    
    async def _check_user_agent(self, user_agent: str, client_ip: str):
        """Check for suspicious user agents."""
        if not user_agent:
            # Allow empty user agents for now, but log
            self.logger.warning("Empty user agent", client_ip=client_ip)
            return
        
        user_agent_lower = user_agent.lower()
        
        for blocked_agent in self.blocked_user_agents:
            if blocked_agent in user_agent_lower:
                self.logger.warning(
                    "Blocked user agent detected",
                    user_agent=user_agent,
                    client_ip=client_ip
                )
                raise SecurityViolationException(
                    "Suspicious user agent detected",
                    violation_type="blocked_user_agent",
                    risk_level="medium"
                )
    
    async def _check_request_size(self, request: Request):
        """Check request size limits."""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                max_size = 10 * 1024 * 1024  # 10MB
                
                if size > max_size:
                    self.logger.warning(
                        "Request size too large",
                        content_length=size,
                        max_size=max_size
                    )
                    raise HTTPException(
                        status_code=413,
                        detail="Request entity too large"
                    )
            except ValueError:
                pass
    
    async def _check_suspicious_patterns(self, request: Request):
        """Check for suspicious patterns in request."""
        import re
        
        # Check URL path
        path = str(request.url.path)
        query_params = str(request.url.query) if request.url.query else ""
        
        for pattern in self.suspicious_patterns:
            if re.search(pattern, path, re.IGNORECASE) or re.search(pattern, query_params, re.IGNORECASE):
                self.logger.warning(
                    "Suspicious pattern detected",
                    pattern=pattern,
                    path=path,
                    query_params=query_params,
                    client_ip=self._get_client_ip(request)
                )
                raise SecurityViolationException(
                    "Suspicious request pattern detected",
                    violation_type="suspicious_pattern",
                    risk_level="high"
                )
    
    async def _check_rate_limit(self, client_ip: str, path: str):
        """Check rate limiting for client."""
        if not self.settings.rate_limit_enabled:
            return
        
        now = time.time()
        window_start = now - 60  # 1-minute window
        
        # Clean old entries
        if client_ip in self.rate_limit_storage:
            self.rate_limit_storage[client_ip] = [
                timestamp for timestamp in self.rate_limit_storage[client_ip]
                if timestamp > window_start
            ]
        else:
            self.rate_limit_storage[client_ip] = []
        
        # Check current request count
        current_requests = len(self.rate_limit_storage[client_ip])
        
        if current_requests >= self.settings.rate_limit_requests_per_minute:
            self.logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                path=path,
                current_requests=current_requests,
                limit=self.settings.rate_limit_requests_per_minute
            )
            raise RateLimitException(
                "Rate limit exceeded",
                limit=self.settings.rate_limit_requests_per_minute,
                window_seconds=60,
                retry_after=60
            )
        
        # Add current request
        self.rate_limit_storage[client_ip].append(now)


class JWTBearer(HTTPBearer):
    """JWT Bearer token authentication."""
    
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)
        self.logger = structlog.get_logger(__name__)
    
    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(
                    status_code=403,
                    detail="Invalid authentication scheme."
                )
            if not self.verify_jwt(credentials.credentials):
                raise HTTPException(
                    status_code=403,
                    detail="Invalid token or expired token."
                )
            return credentials.credentials
        else:
            raise HTTPException(
                status_code=403,
                detail="Invalid authorization code."
            )
    
    def verify_jwt(self, token: str) -> bool:
        """Verify JWT token."""
        try:
            # This would typically decode and verify the JWT
            # For now, we'll implement a basic check
            return len(token) > 0
        except Exception as e:
            self.logger.error(f"JWT verification failed: {e}")
            return False


def generate_csrf_token() -> str:
    """Generate CSRF token."""
    return secrets.token_urlsafe(32)


def verify_csrf_token(token: str, session_token: str) -> bool:
    """Verify CSRF token."""
    try:
        # This would implement proper CSRF token verification
        # For now, we'll implement a basic check
        return len(token) > 0 and len(session_token) > 0
    except Exception:
        return False


def generate_secure_hash(data: str, salt: Optional[str] = None) -> Tuple[str, str]:
    """Generate secure hash with salt."""
    if salt is None:
        salt = secrets.token_hex(16)
    
    hash_obj = hashlib.pbkdf2_hmac('sha256', data.encode(), salt.encode(), 100000)
    return hash_obj.hex(), salt


def verify_secure_hash(data: str, hash_value: str, salt: str) -> bool:
    """Verify secure hash."""
    try:
        computed_hash, _ = generate_secure_hash(data, salt)
        return computed_hash == hash_value
    except Exception:
        return False


def generate_api_key() -> str:
    """Generate secure API key."""
    return secrets.token_urlsafe(32)


def mask_sensitive_data(data: Dict) -> Dict:
    """Mask sensitive data in logs."""
    sensitive_keys = [
        'password', 'secret', 'key', 'token', 'credential',
        'authorization', 'session', 'cookie', 'ssn', 'credit_card'
    ]
    
    masked_data = data.copy()
    
    for key, value in masked_data.items():
        if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
            if isinstance(value, str) and len(value) > 4:
                masked_data[key] = f"{value[:2]}{'*' * (len(value) - 4)}{value[-2:]}"
            else:
                masked_data[key] = "***"
    
    return masked_data


class IPWhitelist:
    """IP address whitelist manager."""
    
    def __init__(self, whitelist: List[str] = None):
        self.whitelist = set(whitelist or [])
        self.logger = structlog.get_logger(__name__)
    
    def is_allowed(self, ip_address: str) -> bool:
        """Check if IP address is whitelisted."""
        if not self.whitelist:
            return True  # Allow all if no whitelist configured
        
        return ip_address in self.whitelist
    
    def add_ip(self, ip_address: str):
        """Add IP to whitelist."""
        self.whitelist.add(ip_address)
        self.logger.info(f"IP added to whitelist: {ip_address}")
    
    def remove_ip(self, ip_address: str):
        """Remove IP from whitelist."""
        self.whitelist.discard(ip_address)
        self.logger.info(f"IP removed from whitelist: {ip_address}")


class SecurityHeaders:
    """Security headers manager."""
    
    @staticmethod
    def get_security_headers(environment: str = "production") -> Dict[str, str]:
        """Get security headers based on environment."""
        headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }
        
        if environment == "production":
            headers.update({
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';",
            })
        else:
            # More permissive CSP for development
            headers["Content-Security-Policy"] = "default-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: blob:;"
        
        return headers