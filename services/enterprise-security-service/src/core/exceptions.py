"""
Enterprise Security Service Exceptions

Custom exception classes for enterprise security operations.
"""

from typing import Optional, Dict, Any


class EnterpriseSecurityException(Exception):
    """Base exception for enterprise security service."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        status_code: int = 500,
        error_type: str = "enterprise_security_error",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.error_type = error_type
        self.details = details or {}
        super().__init__(self.message)


class SSOException(EnterpriseSecurityException):
    """Exception for SSO-related errors."""
    
    def __init__(
        self,
        message: str,
        provider_id: Optional[str] = None,
        protocol: Optional[str] = None,
        **kwargs
    ):
        self.provider_id = provider_id
        self.protocol = protocol
        kwargs.setdefault("error_type", "sso_error")
        kwargs.setdefault("status_code", 401)
        kwargs.setdefault("details", {}).update({
            "provider_id": provider_id,
            "protocol": protocol
        })
        super().__init__(message, **kwargs)


class AuthenticationException(SSOException):
    """Exception for authentication failures."""
    
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("error_type", "authentication_error")
        kwargs.setdefault("status_code", 401)
        super().__init__(message, **kwargs)


class AuthorizationException(EnterpriseSecurityException):
    """Exception for authorization failures."""
    
    def __init__(
        self,
        message: str,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        **kwargs
    ):
        self.user_id = user_id
        self.resource = resource
        self.action = action
        kwargs.setdefault("error_type", "authorization_error")
        kwargs.setdefault("status_code", 403)
        kwargs.setdefault("details", {}).update({
            "user_id": user_id,
            "resource": resource,
            "action": action
        })
        super().__init__(message, **kwargs)


class RBACException(EnterpriseSecurityException):
    """Exception for RBAC-related errors."""
    
    def __init__(
        self,
        message: str,
        role_name: Optional[str] = None,
        permission_name: Optional[str] = None,
        **kwargs
    ):
        self.role_name = role_name
        self.permission_name = permission_name
        kwargs.setdefault("error_type", "rbac_error")
        kwargs.setdefault("status_code", 403)
        kwargs.setdefault("details", {}).update({
            "role_name": role_name,
            "permission_name": permission_name
        })
        super().__init__(message, **kwargs)


class AuditException(EnterpriseSecurityException):
    """Exception for audit-related errors."""
    
    def __init__(
        self,
        message: str,
        event_id: Optional[str] = None,
        event_type: Optional[str] = None,
        **kwargs
    ):
        self.event_id = event_id
        self.event_type = event_type
        kwargs.setdefault("error_type", "audit_error")
        kwargs.setdefault("status_code", 500)
        kwargs.setdefault("details", {}).update({
            "event_id": event_id,
            "event_type": event_type
        })
        super().__init__(message, **kwargs)


class ComplianceException(EnterpriseSecurityException):
    """Exception for compliance-related errors."""
    
    def __init__(
        self,
        message: str,
        framework: Optional[str] = None,
        rule_id: Optional[str] = None,
        violation_id: Optional[str] = None,
        **kwargs
    ):
        self.framework = framework
        self.rule_id = rule_id
        self.violation_id = violation_id
        kwargs.setdefault("error_type", "compliance_error")
        kwargs.setdefault("status_code", 422)
        kwargs.setdefault("details", {}).update({
            "framework": framework,
            "rule_id": rule_id,
            "violation_id": violation_id
        })
        super().__init__(message, **kwargs)


class ConfigurationException(EnterpriseSecurityException):
    """Exception for configuration-related errors."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        **kwargs
    ):
        self.config_key = config_key
        kwargs.setdefault("error_type", "configuration_error")
        kwargs.setdefault("status_code", 500)
        kwargs.setdefault("details", {}).update({
            "config_key": config_key
        })
        super().__init__(message, **kwargs)


class ValidationException(EnterpriseSecurityException):
    """Exception for validation errors."""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[str] = None,
        **kwargs
    ):
        self.field_name = field_name
        self.field_value = field_value
        kwargs.setdefault("error_type", "validation_error")
        kwargs.setdefault("status_code", 400)
        kwargs.setdefault("details", {}).update({
            "field_name": field_name,
            "field_value": field_value
        })
        super().__init__(message, **kwargs)


class SecurityViolationException(EnterpriseSecurityException):
    """Exception for security violations."""
    
    def __init__(
        self,
        message: str,
        violation_type: Optional[str] = None,
        risk_level: Optional[str] = None,
        **kwargs
    ):
        self.violation_type = violation_type
        self.risk_level = risk_level
        kwargs.setdefault("error_type", "security_violation")
        kwargs.setdefault("status_code", 403)
        kwargs.setdefault("details", {}).update({
            "violation_type": violation_type,
            "risk_level": risk_level
        })
        super().__init__(message, **kwargs)


class SessionException(EnterpriseSecurityException):
    """Exception for session-related errors."""
    
    def __init__(
        self,
        message: str,
        session_id: Optional[str] = None,
        **kwargs
    ):
        self.session_id = session_id
        kwargs.setdefault("error_type", "session_error")
        kwargs.setdefault("status_code", 401)
        kwargs.setdefault("details", {}).update({
            "session_id": session_id
        })
        super().__init__(message, **kwargs)


class DatabaseException(EnterpriseSecurityException):
    """Exception for database-related errors."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        self.operation = operation
        self.table_name = table_name
        kwargs.setdefault("error_type", "database_error")
        kwargs.setdefault("status_code", 500)
        kwargs.setdefault("details", {}).update({
            "operation": operation,
            "table_name": table_name
        })
        super().__init__(message, **kwargs)


class ExternalServiceException(EnterpriseSecurityException):
    """Exception for external service errors."""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        endpoint: Optional[str] = None,
        **kwargs
    ):
        self.service_name = service_name
        self.endpoint = endpoint
        kwargs.setdefault("error_type", "external_service_error")
        kwargs.setdefault("status_code", 502)
        kwargs.setdefault("details", {}).update({
            "service_name": service_name,
            "endpoint": endpoint
        })
        super().__init__(message, **kwargs)


class CertificateException(EnterpriseSecurityException):
    """Exception for certificate-related errors."""
    
    def __init__(
        self,
        message: str,
        certificate_type: Optional[str] = None,
        certificate_path: Optional[str] = None,
        **kwargs
    ):
        self.certificate_type = certificate_type
        self.certificate_path = certificate_path
        kwargs.setdefault("error_type", "certificate_error")
        kwargs.setdefault("status_code", 500)
        kwargs.setdefault("details", {}).update({
            "certificate_type": certificate_type,
            "certificate_path": certificate_path
        })
        super().__init__(message, **kwargs)


class EncryptionException(EnterpriseSecurityException):
    """Exception for encryption/decryption errors."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        algorithm: Optional[str] = None,
        **kwargs
    ):
        self.operation = operation
        self.algorithm = algorithm
        kwargs.setdefault("error_type", "encryption_error")
        kwargs.setdefault("status_code", 500)
        kwargs.setdefault("details", {}).update({
            "operation": operation,
            "algorithm": algorithm
        })
        super().__init__(message, **kwargs)


class RateLimitException(EnterpriseSecurityException):
    """Exception for rate limiting violations."""
    
    def __init__(
        self,
        message: str,
        limit: Optional[int] = None,
        window_seconds: Optional[int] = None,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        self.limit = limit
        self.window_seconds = window_seconds
        self.retry_after = retry_after
        kwargs.setdefault("error_type", "rate_limit_error")
        kwargs.setdefault("status_code", 429)
        kwargs.setdefault("details", {}).update({
            "limit": limit,
            "window_seconds": window_seconds,
            "retry_after": retry_after
        })
        super().__init__(message, **kwargs)


class TokenException(EnterpriseSecurityException):
    """Exception for token-related errors."""
    
    def __init__(
        self,
        message: str,
        token_type: Optional[str] = None,
        **kwargs
    ):
        self.token_type = token_type
        kwargs.setdefault("error_type", "token_error")
        kwargs.setdefault("status_code", 401)
        kwargs.setdefault("details", {}).update({
            "token_type": token_type
        })
        super().__init__(message, **kwargs)