"""
Enterprise Security Service Monitoring

Comprehensive monitoring and metrics collection for enterprise security operations.
"""

import time
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
from prometheus_client import (
    Counter, Histogram, Gauge, Info, Enum,
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)
import structlog


class EnterpriseSecurityMetrics:
    """
    Comprehensive metrics collection for enterprise security service.
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self.logger = structlog.get_logger(__name__)
        
        # Service Information
        self.service_info = Info(
            "enterprise_security_service_info",
            "Information about the enterprise security service",
            registry=self.registry
        )
        
        # SSO Metrics
        self.sso_authentication_attempts = Counter(
            "sso_authentication_attempts_total",
            "Total number of SSO authentication attempts",
            ["provider", "protocol"],
            registry=self.registry
        )
        
        self.sso_authentication_successes = Counter(
            "sso_authentication_successes_total",
            "Total number of successful SSO authentications",
            ["provider", "protocol"],
            registry=self.registry
        )
        
        self.sso_authentication_failures = Counter(
            "sso_authentication_failures_total",
            "Total number of failed SSO authentications",
            ["provider", "protocol"],
            registry=self.registry
        )
        
        self.sso_authentication_duration = Histogram(
            "sso_authentication_duration_seconds",
            "Duration of SSO authentication requests",
            ["provider", "protocol"],
            registry=self.registry
        )
        
        self.sso_active_sessions = Gauge(
            "sso_active_sessions",
            "Number of active SSO sessions",
            registry=self.registry
        )
        
        self.sso_session_duration = Histogram(
            "sso_session_duration_seconds",
            "Duration of SSO sessions",
            ["provider"],
            registry=self.registry
        )
        
        self.sso_session_logouts = Counter(
            "sso_session_logouts_total",
            "Total number of SSO session logouts",
            registry=self.registry
        )
        
        # RBAC Metrics
        self.rbac_access_checks_total = Counter(
            "rbac_access_checks_total",
            "Total number of RBAC access checks",
            ["resource", "action"],
            registry=self.registry
        )
        
        self.rbac_access_granted_total = Counter(
            "rbac_access_granted_total",
            "Total number of RBAC access grants",
            ["resource", "action"],
            registry=self.registry
        )
        
        self.rbac_access_denied_total = Counter(
            "rbac_access_denied_total",
            "Total number of RBAC access denials",
            ["resource", "action"],
            registry=self.registry
        )
        
        self.rbac_access_check_duration = Histogram(
            "rbac_access_check_duration_seconds",
            "Duration of RBAC access checks",
            ["resource", "action"],
            registry=self.registry
        )
        
        self.rbac_roles_total = Gauge(
            "rbac_roles_total",
            "Total number of RBAC roles",
            registry=self.registry
        )
        
        self.rbac_permissions_total = Gauge(
            "rbac_permissions_total",
            "Total number of RBAC permissions",
            registry=self.registry
        )
        
        self.rbac_role_assignments_total = Gauge(
            "rbac_role_assignments_total",
            "Total number of RBAC role assignments",
            registry=self.registry
        )
        
        # Audit Metrics
        self.audit_events_total = Counter(
            "audit_events_total",
            "Total number of audit events",
            ["event_type", "severity"],
            registry=self.registry
        )
        
        self.audit_events_processed = Counter(
            "audit_events_processed_total",
            "Total number of processed audit events",
            registry=self.registry
        )
        
        self.audit_events_failed = Counter(
            "audit_events_failed_total",
            "Total number of failed audit events",
            ["error_type"],
            registry=self.registry
        )
        
        self.audit_event_processing_duration = Histogram(
            "audit_event_processing_duration_seconds",
            "Duration of audit event processing",
            registry=self.registry
        )
        
        self.audit_queue_size = Gauge(
            "audit_queue_size",
            "Size of the audit event queue",
            registry=self.registry
        )
        
        self.audit_data_integrity_checks = Counter(
            "audit_data_integrity_checks_total",
            "Total number of audit data integrity checks",
            ["result"],
            registry=self.registry
        )
        
        # Compliance Metrics
        self.compliance_frameworks_enabled = Gauge(
            "compliance_frameworks_enabled",
            "Number of enabled compliance frameworks",
            registry=self.registry
        )
        
        self.compliance_violations_total = Counter(
            "compliance_violations_total",
            "Total number of compliance violations",
            ["framework", "severity"],
            registry=self.registry
        )
        
        self.compliance_violations_resolved_total = Counter(
            "compliance_violations_resolved_total",
            "Total number of resolved compliance violations",
            ["framework"],
            registry=self.registry
        )
        
        self.compliance_checks_total = Counter(
            "compliance_checks_total",
            "Total number of compliance checks",
            ["framework", "result"],
            registry=self.registry
        )
        
        self.compliance_check_duration = Histogram(
            "compliance_check_duration_seconds",
            "Duration of compliance checks",
            ["framework"],
            registry=self.registry
        )
        
        self.compliance_reports_generated = Counter(
            "compliance_reports_generated_total",
            "Total number of compliance reports generated",
            ["framework", "format"],
            registry=self.registry
        )
        
        # Security Metrics
        self.security_violations_total = Counter(
            "security_violations_total",
            "Total number of security violations",
            ["violation_type", "severity"],
            registry=self.registry
        )
        
        self.security_alerts_total = Counter(
            "security_alerts_total",
            "Total number of security alerts",
            ["alert_type", "severity"],
            registry=self.registry
        )
        
        self.security_incidents_total = Counter(
            "security_incidents_total",
            "Total number of security incidents",
            ["incident_type"],
            registry=self.registry
        )
        
        self.brute_force_attempts = Counter(
            "brute_force_attempts_total",
            "Total number of brute force attempts",
            ["source_ip"],
            registry=self.registry
        )
        
        # Performance Metrics
        self.http_requests_total = Counter(
            "http_requests_total",
            "Total number of HTTP requests",
            ["method", "endpoint", "status_code"],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            "http_request_duration_seconds",
            "Duration of HTTP requests",
            ["method", "endpoint"],
            registry=self.registry
        )
        
        self.database_operations_total = Counter(
            "database_operations_total",
            "Total number of database operations",
            ["operation", "table", "result"],
            registry=self.registry
        )
        
        self.database_operation_duration = Histogram(
            "database_operation_duration_seconds",
            "Duration of database operations",
            ["operation", "table"],
            registry=self.registry
        )
        
        self.database_connections_active = Gauge(
            "database_connections_active",
            "Number of active database connections",
            registry=self.registry
        )
        
        # System Metrics
        self.service_status = Enum(
            "service_status",
            "Status of the enterprise security service",
            states=["starting", "running", "stopping", "stopped", "error"],
            registry=self.registry
        )
        
        self.background_tasks_active = Gauge(
            "background_tasks_active",
            "Number of active background tasks",
            registry=self.registry
        )
        
        self.memory_usage_bytes = Gauge(
            "memory_usage_bytes",
            "Memory usage in bytes",
            registry=self.registry
        )
        
        self.cpu_usage_percent = Gauge(
            "cpu_usage_percent",
            "CPU usage percentage",
            registry=self.registry
        )
        
        # Rate Limiting Metrics
        self.rate_limit_requests = Counter(
            "rate_limit_requests_total",
            "Total number of rate-limited requests",
            ["endpoint", "client_id"],
            registry=self.registry
        )
        
        self.rate_limit_violations = Counter(
            "rate_limit_violations_total",
            "Total number of rate limit violations",
            ["endpoint", "client_id"],
            registry=self.registry
        )
        
        # External Service Metrics
        self.external_service_requests = Counter(
            "external_service_requests_total",
            "Total number of external service requests",
            ["service", "operation", "result"],
            registry=self.registry
        )
        
        self.external_service_duration = Histogram(
            "external_service_duration_seconds",
            "Duration of external service requests",
            ["service", "operation"],
            registry=self.registry
        )
        
        # Initialize service info
        self._update_service_info()
    
    def _update_service_info(self):
        """Update service information metrics."""
        try:
            from core.config import get_settings
            settings = get_settings()
            
            self.service_info.info({
                "service_name": settings.service_name,
                "environment": settings.environment,
                "version": "1.0.0"
            })
        except Exception as e:
            self.logger.error(f"Failed to update service info: {e}")
    
    @contextmanager
    def time_operation(self, metric: Histogram, *labels):
        """Context manager to time operations."""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            metric.labels(*labels).observe(duration)
    
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        self.http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        self.http_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_database_operation(self, operation: str, table: str, success: bool, duration: float):
        """Record database operation metrics."""
        result = "success" if success else "failure"
        
        self.database_operations_total.labels(
            operation=operation,
            table=table,
            result=result
        ).inc()
        
        self.database_operation_duration.labels(
            operation=operation,
            table=table
        ).observe(duration)
    
    def record_external_service_request(self, service: str, operation: str, success: bool, duration: float):
        """Record external service request metrics."""
        result = "success" if success else "failure"
        
        self.external_service_requests.labels(
            service=service,
            operation=operation,
            result=result
        ).inc()
        
        self.external_service_duration.labels(
            service=service,
            operation=operation
        ).observe(duration)
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def get_content_type(self) -> str:
        """Get content type for metrics."""
        return CONTENT_TYPE_LATEST


# Global metrics instance
_metrics: Optional[EnterpriseSecurityMetrics] = None


def get_metrics() -> EnterpriseSecurityMetrics:
    """Get global metrics instance."""
    global _metrics
    if _metrics is None:
        _metrics = EnterpriseSecurityMetrics()
    return _metrics


def setup_monitoring(app):
    """Setup monitoring for FastAPI application."""
    from fastapi import Request, Response
    import time
    
    metrics = get_metrics()
    
    @app.middleware("http")
    async def metrics_middleware(request: Request, call_next):
        """Middleware to collect HTTP metrics."""
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Record metrics
            metrics.record_http_request(
                method=request.method,
                endpoint=request.url.path,
                status_code=response.status_code,
                duration=duration
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Record error metrics
            metrics.record_http_request(
                method=request.method,
                endpoint=request.url.path,
                status_code=500,
                duration=duration
            )
            
            raise
    
    @app.get("/metrics")
    async def metrics_endpoint():
        """Prometheus metrics endpoint."""
        return Response(
            content=metrics.get_metrics(),
            media_type=metrics.get_content_type()
        )
    
    # Set service status to running
    metrics.service_status.state("running")