"""
Enterprise Security Service Configuration

Centralized configuration management for the enterprise security service.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class EnterpriseSecuritySettings(BaseSettings):
    """Enterprise security service configuration settings."""
    
    # Service Configuration
    service_name: str = Field(default="enterprise-security-service", env="SERVICE_NAME")
    environment: str = Field(default="development", env="ENVIRONMENT")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database Configuration
    database_url: str = Field(..., env="DATABASE_URL")
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="SUPABASE_ANON_KEY")
    supabase_service_key: str = Field(..., env="SUPABASE_SERVICE_KEY")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Security Configuration
    secret_key: str = Field(..., env="SECRET_KEY")
    encryption_key: str = Field(..., env="ENCRYPTION_KEY")
    session_encryption_key: Optional[str] = Field(default=None, env="SESSION_ENCRYPTION_KEY")
    audit_encryption_key: Optional[str] = Field(default=None, env="AUDIT_ENCRYPTION_KEY")
    
    # SSO Configuration
    base_url: str = Field(..., env="BASE_URL")
    saml_entity_id: str = Field(..., env="SAML_ENTITY_ID")
    
    # Azure AD / Microsoft Configuration
    azure_tenant_id: Optional[str] = Field(default=None, env="AZURE_TENANT_ID")
    azure_client_id: Optional[str] = Field(default=None, env="AZURE_CLIENT_ID")
    azure_client_secret: Optional[str] = Field(default=None, env="AZURE_CLIENT_SECRET")
    azure_saml_certificate: Optional[str] = Field(default=None, env="AZURE_SAML_CERTIFICATE")
    microsoft_client_id: Optional[str] = Field(default=None, env="MICROSOFT_CLIENT_ID")
    microsoft_client_secret: Optional[str] = Field(default=None, env="MICROSOFT_CLIENT_SECRET")
    
    # Google Configuration
    google_client_id: Optional[str] = Field(default=None, env="GOOGLE_CLIENT_ID")
    google_client_secret: Optional[str] = Field(default=None, env="GOOGLE_CLIENT_SECRET")
    
    # Okta Configuration
    okta_domain: Optional[str] = Field(default=None, env="OKTA_DOMAIN")
    okta_app_id: Optional[str] = Field(default=None, env="OKTA_APP_ID")
    okta_saml_certificate: Optional[str] = Field(default=None, env="OKTA_SAML_CERTIFICATE")
    
    # Active Directory Configuration
    ad_server: Optional[str] = Field(default=None, env="AD_SERVER")
    ad_port: Optional[int] = Field(default=389, env="AD_PORT")
    ad_base_dn: Optional[str] = Field(default=None, env="AD_BASE_DN")
    ad_bind_dn: Optional[str] = Field(default=None, env="AD_BIND_DN")
    ad_bind_password: Optional[str] = Field(default=None, env="AD_BIND_PASSWORD")
    ad_default_domain: Optional[str] = Field(default=None, env="AD_DEFAULT_DOMAIN")
    ad_use_ssl: bool = Field(default=False, env="AD_USE_SSL")
    ad_use_ntlm: bool = Field(default=True, env="AD_USE_NTLM")
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "https://localhost:3000"],
        env="ALLOWED_ORIGINS"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Monitoring Configuration
    enable_prometheus: bool = Field(default=True, env="ENABLE_PROMETHEUS")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    enable_sentry: bool = Field(default=False, env="ENABLE_SENTRY")
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    enable_jaeger: bool = Field(default=False, env="ENABLE_JAEGER")
    jaeger_endpoint: Optional[str] = Field(default=None, env="JAEGER_ENDPOINT")
    
    # Audit Configuration
    audit_retention_days: int = Field(default=2555, env="AUDIT_RETENTION_DAYS")  # 7 years
    audit_log_level: str = Field(default="INFO", env="AUDIT_LOG_LEVEL")
    
    # Compliance Configuration
    enable_sox_compliance: bool = Field(default=False, env="ENABLE_SOX_COMPLIANCE")
    enable_gdpr_compliance: bool = Field(default=True, env="ENABLE_GDPR_COMPLIANCE")
    enable_hipaa_compliance: bool = Field(default=False, env="ENABLE_HIPAA_COMPLIANCE")
    enable_iso27001_compliance: bool = Field(default=True, env="ENABLE_ISO27001_COMPLIANCE")
    
    # Session Configuration
    session_timeout_hours: int = Field(default=8, env="SESSION_TIMEOUT_HOURS")
    session_refresh_threshold_hours: int = Field(default=1, env="SESSION_REFRESH_THRESHOLD_HOURS")
    max_concurrent_sessions: int = Field(default=5, env="MAX_CONCURRENT_SESSIONS")
    
    # Rate Limiting Configuration
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(default=100, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst_size: int = Field(default=200, env="RATE_LIMIT_BURST_SIZE")
    
    # Security Configuration
    enable_csrf_protection: bool = Field(default=True, env="ENABLE_CSRF_PROTECTION")
    enable_xss_protection: bool = Field(default=True, env="ENABLE_XSS_PROTECTION")
    enable_clickjacking_protection: bool = Field(default=True, env="ENABLE_CLICKJACKING_PROTECTION")
    enable_hsts: bool = Field(default=True, env="ENABLE_HSTS")
    hsts_max_age: int = Field(default=31536000, env="HSTS_MAX_AGE")  # 1 year
    
    # Encryption Configuration
    encryption_algorithm: str = Field(default="AES-256-GCM", env="ENCRYPTION_ALGORITHM")
    hash_algorithm: str = Field(default="SHA-256", env="HASH_ALGORITHM")
    password_hash_rounds: int = Field(default=12, env="PASSWORD_HASH_ROUNDS")
    
    # Certificate Configuration
    ssl_cert_path: Optional[str] = Field(default=None, env="SSL_CERT_PATH")
    ssl_key_path: Optional[str] = Field(default=None, env="SSL_KEY_PATH")
    ca_cert_path: Optional[str] = Field(default=None, env="CA_CERT_PATH")
    
    # Background Task Configuration
    max_background_tasks: int = Field(default=10, env="MAX_BACKGROUND_TASKS")
    task_timeout_seconds: int = Field(default=300, env="TASK_TIMEOUT_SECONDS")
    
    # External Service Configuration
    smtp_host: Optional[str] = Field(default=None, env="SMTP_HOST")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    smtp_use_tls: bool = Field(default=True, env="SMTP_USE_TLS")
    
    # Webhook Configuration
    webhook_secret: Optional[str] = Field(default=None, env="WEBHOOK_SECRET")
    webhook_timeout_seconds: int = Field(default=30, env="WEBHOOK_TIMEOUT_SECONDS")
    
    # Development Configuration
    debug: bool = Field(default=False, env="DEBUG")
    reload: bool = Field(default=False, env="RELOAD")
    workers: int = Field(default=1, env="WORKERS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
_settings: Optional[EnterpriseSecuritySettings] = None


def get_settings() -> EnterpriseSecuritySettings:
    """Get application settings (singleton pattern)."""
    global _settings
    if _settings is None:
        _settings = EnterpriseSecuritySettings()
    return _settings


def reload_settings() -> EnterpriseSecuritySettings:
    """Reload application settings."""
    global _settings
    _settings = EnterpriseSecuritySettings()
    return _settings