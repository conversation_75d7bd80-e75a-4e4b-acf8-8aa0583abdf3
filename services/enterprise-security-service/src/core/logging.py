"""
Enterprise Security Service Logging Configuration

Structured logging setup for enterprise security operations.
"""

import json
import logging
import sys
from typing import Any, Dict
from datetime import datetime

import structlog
from structlog.stdlib import LoggerFactory


def setup_logging():
    """Setup structured logging for the enterprise security service."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.INFO,
    )
    
    # Get logger
    logger = structlog.get_logger("enterprise-security-service")
    logger.info("Logging configured")
    
    return logger


class SecurityAuditFormatter(logging.Formatter):
    """Custom formatter for security audit logs."""
    
    def format(self, record):
        """Format log record for security audit."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ["name", "msg", "args", "levelname", "levelno", "pathname", 
                          "filename", "module", "lineno", "funcName", "created", 
                          "msecs", "relativeCreated", "thread", "threadName", 
                          "processName", "process", "getMessage"]:
                log_data[key] = value
        
        return json.dumps(log_data)


def get_security_logger(name: str = "security"):
    """Get a security-specific logger."""
    logger = logging.getLogger(f"enterprise-security-service.{name}")
    
    # Add security handler if not already added
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(SecurityAuditFormatter())
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger