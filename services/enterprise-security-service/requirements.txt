# Enterprise Security Service Dependencies
# Production-ready enterprise authentication, authorization, and compliance platform

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database & Storage
asyncpg==0.29.0
supabase==2.3.0
redis[hiredis]==5.0.1
sqlalchemy[asyncio]==2.0.23

# Authentication & Authorization
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
authlib==1.2.1
python-ldap==3.4.3
pysaml2==7.4.2

# SAML/OIDC Integration
xmlsec==1.3.13
lxml==4.9.3
cryptography==41.0.7
pycryptodome==3.19.0

# Active Directory Integration
ldap3==2.9.1
python-ad==0.8.0
pywinrm==0.4.3

# OAuth 2.0 / OpenID Connect
requests-oauthlib==1.3.1
oauthlib==3.2.2
PyJWT==2.8.0

# Role-Based Access Control (RBAC)
casbin==1.17.6
casbin-sqlalchemy-adapter==1.1.0
casbin-async-sqlalchemy-adapter==1.2.0

# Audit Logging & Compliance
structlog==23.2.0
python-json-logger==2.0.7
audit-log==1.0.0
compliance-checker==4.0.0

# Security & Validation
security-scanner==2.1.0
pii-detector==1.0.2
data-classifier==1.5.0
encryption-utils==2.0.1

# Monitoring & Observability
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# HTTP & API
httpx==0.25.2
requests==2.31.0
websockets==12.0

# Data Processing & Analysis
pandas==2.1.4
numpy==1.25.2
python-dateutil==2.8.2

# Async & Concurrency
asyncio-mqtt==0.16.1
aioredis==2.0.1
celery[redis]==5.3.4

# Report Generation
reportlab==4.0.7
weasyprint==60.2
jinja2==3.1.2
xlsxwriter==3.1.9

# File Processing
aiofiles==23.2.1
python-magic==0.4.27
pypdf==3.17.1

# Email & Notifications
aiosmtplib==3.0.1
emails==0.6.0
jinja2==3.1.2

# Enterprise Integrations
microsoft-graph==1.0.0
azure-identity==1.15.0
google-auth==2.23.4
okta-sdk-python==2.9.5

# Compliance Frameworks
sox-compliance==1.0.0
gdpr-toolkit==2.1.0
hipaa-compliance==1.5.0
iso27001-toolkit==1.0.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
factory-boy==3.3.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0