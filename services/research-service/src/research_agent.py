"""
Research Agent - Extracted from PydanticAI Additional Agents
Implements the core research functionality as a standalone component
"""

import asyncio
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent, RunContext
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel

# Local imports
from .prompt_template import PromptTemplate


# =========================================================================
# Models
# =========================================================================

class ResearchResult(BaseModel):
    """Research result data model"""
    topic: str = Field(description="The research topic")
    key_findings: List[str] = Field(description="Key research findings")
    sources: List[str] = Field(description="Source citations")
    research_depth: str = Field(description="Depth of research performed")
    focus_areas: List[str] = Field(description="Areas of focus in the research")
    summary: str = Field(description="Executive summary of research")
    timestamp: str = Field(description="When research was completed")


# =========================================================================
# Research Agent Implementation
# =========================================================================

class ResearchAgent:
    """
    Standalone Research Agent for topic research and information gathering
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._agent: Optional[TypedAgent[Any, ResearchResult]] = None
        self._model: Optional[Model] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the research agent with available AI model"""
        try:
            self._model = self._get_available_model()
            if not self._model:
                raise ValueError("No AI model available. Check API keys.")
            
            self._agent = TypedAgent(
                model=self._model,
                output_type=ResearchResult,
                system_prompt=self._get_system_prompt()
            )
            
            self._initialized = True
            self.logger.info("🔬 Research Agent initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Research Agent: {str(e)}")
            raise
    
    def _get_available_model(self) -> Optional[Model]:
        """Get available AI model based on environment configuration"""
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        elif os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the research agent"""
        return """
        You are an expert research assistant specialized in comprehensive topic research and analysis.
        
        Your capabilities include:
        - Conducting thorough research on any given topic
        - Identifying key trends, patterns, and insights
        - Providing well-sourced and cited information
        - Analyzing information from multiple perspectives
        - Summarizing complex topics into actionable insights
        
        When conducting research:
        1. Provide comprehensive key findings that address the core aspects of the topic
        2. Include diverse and credible sources for your information
        3. Adapt your research depth to the requested level (basic, moderate, comprehensive, expert)
        4. Focus on the specified areas of interest when provided
        5. Ensure your findings are current, relevant, and actionable
        6. Provide a clear executive summary that synthesizes your research
        
        Always structure your response to include:
        - Clear key findings with specific insights
        - Credible source citations
        - Executive summary that ties everything together
        - Relevant focus area analysis when specified
        """
    
    async def research_topic(
        self,
        topic: str,
        research_depth: str = "comprehensive",
        focus_areas: Optional[List[str]] = None,
        user_id: Optional[str] = None
    ) -> ResearchResult:
        """
        Research a given topic with specified parameters
        
        Args:
            topic: The topic to research
            research_depth: Level of research depth (basic, moderate, comprehensive, expert)
            focus_areas: Specific areas to focus the research on
            user_id: User requesting the research (for logging/tracking)
        
        Returns:
            ResearchResult with comprehensive research findings
        """
        if not self._initialized or not self._agent:
            raise RuntimeError("Research agent not initialized. Call initialize() first.")
        
        start_time = datetime.utcnow()
        
        try:
            # Prepare research prompt
            prompt_template = PromptTemplate.from_string("""
            Conduct {{ research_depth }} research on the following topic:
            
            Topic: "{{ topic }}"
            
            {% if focus_areas %}
            Please focus your research on these specific areas:
            {% for area in focus_areas %}
            - {{ area }}
            {% endfor %}
            {% endif %}
            
            Research Requirements:
            - Depth Level: {{ research_depth }}
            - Provide at least 5-10 key findings depending on depth
            - Include diverse and credible sources
            - Focus on current and relevant information
            - Provide actionable insights where possible
            
            {% if research_depth == "expert" %}
            For expert-level research, also include:
            - Industry-specific insights and trends
            - Technical details and specifications
            - Future outlook and predictions
            - Competitive landscape analysis
            {% elif research_depth == "comprehensive" %}
            For comprehensive research, include:
            - Market trends and statistics
            - Multiple perspectives on the topic
            - Historical context where relevant
            - Practical applications and implications
            {% endif %}
            """)
            
            prompt = prompt_template.format(
                topic=topic,
                research_depth=research_depth,
                focus_areas=focus_areas or []
            )
            
            self.logger.info(f"🔍 Starting research for topic: {topic} (depth: {research_depth})")
            
            # Execute research
            result = await self._agent.run(prompt)
            
            # Enhance result with metadata
            enhanced_result = ResearchResult(
                topic=topic,
                key_findings=result.output.key_findings,
                sources=result.output.sources,
                research_depth=research_depth,
                focus_areas=focus_areas or [],
                summary=result.output.summary if hasattr(result.output, 'summary') else "",
                timestamp=datetime.utcnow().isoformat()
            )
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.logger.info(
                f"✅ Research completed for topic: {topic} "
                f"(execution_time: {execution_time:.2f}s, "
                f"findings: {len(enhanced_result.key_findings)}, "
                f"sources: {len(enhanced_result.sources)})"
            )
            
            return enhanced_result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error(
                f"❌ Research failed for topic: {topic} "
                f"(execution_time: {execution_time:.2f}s, error: {str(e)})"
            )
            raise
    
    async def get_research_suggestions(self, topic: str) -> List[str]:
        """Get research topic suggestions related to a base topic"""
        if not self._initialized or not self._agent:
            raise RuntimeError("Research agent not initialized")
        
        try:
            prompt = f"""
            Suggest 5-8 related research topics for the main topic: "{topic}"
            
            Provide topics that would be valuable to research in conjunction with the main topic.
            Make sure suggestions are specific, actionable, and complement the main research area.
            """
            
            # This would need a different output type for suggestions
            # For now, we'll return a simple list
            return [
                f"Market trends in {topic}",
                f"Competitive analysis of {topic}",
                f"Future outlook for {topic}",
                f"Technical innovations in {topic}",
                f"Consumer behavior regarding {topic}"
            ]
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get research suggestions: {str(e)}")
            raise
    
    async def validate_research_topic(self, topic: str) -> Dict[str, Any]:
        """Validate if a topic is suitable for research"""
        try:
            # Basic validation logic
            if not topic or len(topic.strip()) < 3:
                return {
                    "valid": False,
                    "reason": "Topic too short or empty",
                    "suggestions": ["Please provide a more specific topic"]
                }
            
            if len(topic) > 500:
                return {
                    "valid": False,
                    "reason": "Topic too long",
                    "suggestions": ["Please shorten your topic to be more focused"]
                }
            
            # Check for inappropriate content (basic check)
            inappropriate_words = ["illegal", "harmful", "dangerous"]
            if any(word in topic.lower() for word in inappropriate_words):
                return {
                    "valid": False,
                    "reason": "Topic contains inappropriate content",
                    "suggestions": ["Please choose a different research topic"]
                }
            
            return {
                "valid": True,
                "reason": "Topic is suitable for research",
                "suggestions": []
            }
            
        except Exception as e:
            self.logger.error(f"❌ Topic validation failed: {str(e)}")
            return {
                "valid": False,
                "reason": f"Validation error: {str(e)}",
                "suggestions": ["Please try again"]
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self._agent:
                # No specific cleanup needed for PydanticAI agents
                pass
            
            self._initialized = False
            self.logger.info("🔄 Research Agent cleanup completed")
            
        except Exception as e:
            self.logger.error(f"❌ Research Agent cleanup failed: {str(e)}")
    
    @property
    def is_ready(self) -> bool:
        """Check if agent is ready for use"""
        return self._initialized and self._agent is not None