"""
Monitoring and Metrics for Research Service
Implements Prometheus metrics and observability
"""

import time
from typing import Optional, Dict, Any
from functools import wraps
import logging

try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, REGISTRY
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Mock classes for when Prometheus is not available
    Counter = Histogram = Gauge = CollectorRegistry = object


class MetricsManager:
    """Manages Prometheus metrics for the Research Service"""
    
    def __init__(self, service_name: str = "research_service"):
        self.service_name = service_name
        self.logger = logging.getLogger(__name__)
        self._registry: Optional[CollectorRegistry] = None
        self._metrics: Dict[str, Any] = {}
        
        if PROMETHEUS_AVAILABLE:
            self._setup_metrics()
        else:
            self.logger.warning("⚠️  Prometheus client not available, metrics disabled")
    
    def _setup_metrics(self):
        """Setup Prometheus metrics"""
        try:
            self._registry = REGISTRY
            
            # Request metrics
            self._metrics['request_count'] = Counter(
                'research_requests_total',
                'Total number of research requests',
                ['method', 'endpoint', 'status'],
                registry=self._registry
            )
            
            self._metrics['request_duration'] = Histogram(
                'research_request_duration_seconds',
                'Research request duration in seconds',
                ['method', 'endpoint'],
                registry=self._registry
            )
            
            # Research-specific metrics
            self._metrics['research_count'] = Counter(
                'research_operations_total',
                'Total number of research operations',
                ['research_depth', 'status'],
                registry=self._registry
            )
            
            self._metrics['research_duration'] = Histogram(
                'research_operation_duration_seconds',
                'Research operation duration in seconds',
                ['research_depth'],
                registry=self._registry
            )
            
            # Agent metrics
            self._metrics['agent_status'] = Gauge(
                'research_agent_status',
                'Research agent status (1=ready, 0=not ready)',
                registry=self._registry
            )
            
            # Event Bus metrics
            self._metrics['event_count'] = Counter(
                'research_events_total',
                'Total number of events published',
                ['event_type', 'status'],
                registry=self._registry
            )
            
            self.logger.info("📊 Metrics setup completed")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup metrics: {str(e)}")
    
    def record_request(self, method: str, endpoint: str, status: str, duration: float):
        """Record HTTP request metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['request_count'].labels(
                method=method,
                endpoint=endpoint,
                status=status
            ).inc()
            
            self._metrics['request_duration'].labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record request metrics: {str(e)}")
    
    def record_research_operation(self, research_depth: str, status: str, duration: float):
        """Record research operation metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['research_count'].labels(
                research_depth=research_depth,
                status=status
            ).inc()
            
            self._metrics['research_duration'].labels(
                research_depth=research_depth
            ).observe(duration)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record research metrics: {str(e)}")
    
    def set_agent_status(self, ready: bool):
        """Set agent status metric"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['agent_status'].set(1 if ready else 0)
        except Exception as e:
            self.logger.error(f"❌ Failed to set agent status: {str(e)}")
    
    def record_event(self, event_type: str, status: str):
        """Record event publication metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['event_count'].labels(
                event_type=event_type,
                status=status
            ).inc()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record event metrics: {str(e)}")
    
    def get_registry(self) -> Optional[CollectorRegistry]:
        """Get the metrics registry"""
        return self._registry


# Global metrics manager instance
_metrics_manager: Optional[MetricsManager] = None


def setup_monitoring(service_name: str = "research_service"):
    """Setup monitoring for the service"""
    global _metrics_manager
    _metrics_manager = MetricsManager(service_name)
    return _metrics_manager


def get_metrics_manager() -> Optional[MetricsManager]:
    """Get the global metrics manager"""
    return _metrics_manager


def get_metrics_registry() -> Optional[CollectorRegistry]:
    """Get the metrics registry"""
    if _metrics_manager:
        return _metrics_manager.get_registry()
    return None


def monitor_request(func):
    """Decorator to monitor HTTP requests"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        status = "success"
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status = "error"
            raise
        finally:
            if _metrics_manager:
                duration = time.time() - start_time
                # Extract method and endpoint from function context if available
                method = getattr(func, '_method', 'unknown')
                endpoint = getattr(func, '_endpoint', func.__name__)
                _metrics_manager.record_request(method, endpoint, status, duration)
    
    return wrapper


def monitor_research_operation(func):
    """Decorator to monitor research operations"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        status = "success"
        research_depth = "unknown"
        
        try:
            # Try to extract research_depth from kwargs
            research_depth = kwargs.get('research_depth', 'comprehensive')
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status = "error"
            raise
        finally:
            if _metrics_manager:
                duration = time.time() - start_time
                _metrics_manager.record_research_operation(research_depth, status, duration)
    
    return wrapper