"""
Research Service - Microservice for Research Assistant Agent
Provides topic research and information gathering capabilities
"""

import asyncio
import os
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from .research_agent import ResearchAgent, ResearchResult
from .event_client import EventClient
from .service_registry_client import ServiceRegistryClient
from .security_manager import SecurityManager
from .monitoring import setup_monitoring, get_metrics_registry


# =========================================================================
# Request/Response Models
# =========================================================================

class ResearchRequest(BaseModel):
    topic: str = Field(..., description="Research topic")
    research_depth: str = Field(default="comprehensive", description="Research depth level")
    focus_areas: Optional[List[str]] = Field(default=None, description="Specific focus areas")
    user_id: Optional[str] = Field(default=None, description="User requesting research")

class ResearchResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[ResearchResult] = Field(default=None, description="Research results")
    message: str = Field(default="", description="Status message")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service health status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    timestamp: str = Field(..., description="Health check timestamp")


# =========================================================================
# Global Components
# =========================================================================

research_agent: Optional[ResearchAgent] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None
security = HTTPBearer()


# =========================================================================
# Lifecycle Management
# =========================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage service lifecycle"""
    global research_agent, event_client, service_registry, security_manager
    
    # Startup
    print("🔬 Starting Research Service...")
    
    # Initialize monitoring
    setup_monitoring("research-service")
    
    # Initialize security
    security_manager = SecurityManager()
    
    # Initialize research agent
    research_agent = ResearchAgent()
    await research_agent.initialize()
    
    # Initialize event client
    event_client = EventClient(
        service_name="research-service",
        security_manager=security_manager
    )
    await event_client.connect()
    
    # Initialize service registry
    service_registry = ServiceRegistryClient(
        service_name="research-service",
        service_version="1.0.0",
        host=os.getenv("SERVICE_HOST", "localhost"),
        port=int(os.getenv("SERVICE_PORT", "8081")),
        security_manager=security_manager
    )
    await service_registry.register()
    
    print("✅ Research Service started successfully")
    
    yield
    
    # Shutdown
    print("🔄 Shutting down Research Service...")
    
    if service_registry:
        await service_registry.deregister()
    
    if event_client:
        await event_client.disconnect()
    
    if research_agent:
        await research_agent.cleanup()
    
    print("✅ Research Service shutdown complete")


# =========================================================================
# FastAPI Application
# =========================================================================

app = FastAPI(
    title="Research Service",
    description="Microservice for research assistant agent capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# =========================================================================
# Security Dependencies
# =========================================================================

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify API key authentication"""
    if not security_manager:
        raise HTTPException(status_code=500, detail="Security manager not initialized")
    
    api_key = credentials.credentials
    if not security_manager.verify_api_key(api_key):
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return api_key


# =========================================================================
# API Endpoints
# =========================================================================

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    
    return HealthResponse(
        status="healthy",
        service="research-service",
        version="1.0.0",
        timestamp=datetime.utcnow().isoformat()
    )


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    if not research_agent or not event_client or not service_registry:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return {"status": "ready"}


@app.post("/research", response_model=ResearchResponse)
async def research_topic(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Research a topic using the research assistant agent"""
    if not research_agent:
        raise HTTPException(status_code=503, detail="Research agent not available")
    
    request_id = str(uuid.uuid4())
    
    # Start background research task
    background_tasks.add_task(
        execute_research_task,
        request_id,
        request,
        api_key
    )
    
    return ResearchResponse(
        request_id=request_id,
        status="started",
        message="Research task started successfully"
    )


@app.get("/research/{request_id}", response_model=ResearchResponse)
async def get_research_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Get research task status and results"""
    # In a real implementation, this would check task status in database/cache
    # For now, return a placeholder response
    return ResearchResponse(
        request_id=request_id,
        status="completed",
        message="Research results available"
    )


@app.post("/research/sync", response_model=ResearchResponse)
async def research_topic_sync(
    request: ResearchRequest,
    api_key: str = Depends(verify_api_key)
):
    """Synchronous research execution (for testing/small requests)"""
    if not research_agent:
        raise HTTPException(status_code=503, detail="Research agent not available")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Execute research synchronously
        result = await research_agent.research_topic(
            topic=request.topic,
            research_depth=request.research_depth,
            focus_areas=request.focus_areas or [],
            user_id=request.user_id
        )
        
        return ResearchResponse(
            request_id=request_id,
            status="completed",
            result=result,
            message="Research completed successfully"
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")


@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    registry = get_metrics_registry()
    from prometheus_client import generate_latest
    return generate_latest(registry)


# =========================================================================
# Background Tasks
# =========================================================================

async def execute_research_task(
    request_id: str,
    request: ResearchRequest,
    api_key: str
):
    """Execute research task in background"""
    try:
        # Execute research
        result = await research_agent.research_topic(
            topic=request.topic,
            research_depth=request.research_depth,
            focus_areas=request.focus_areas or [],
            user_id=request.user_id
        )
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                event_type="research.completed",
                data={
                    "request_id": request_id,
                    "topic": request.topic,
                    "result": result.model_dump()
                }
            )
        
        print(f"✅ Research task {request_id} completed successfully")
    
    except Exception as e:
        # Publish error event
        if event_client:
            await event_client.publish_event(
                event_type="research.failed",
                data={
                    "request_id": request_id,
                    "topic": request.topic,
                    "error": str(e)
                }
            )
        
        print(f"❌ Research task {request_id} failed: {str(e)}")


# =========================================================================
# Main Entry Point
# =========================================================================

def run_server():
    """Run the Research Service server"""
    port = int(os.getenv("SERVICE_PORT", "8081"))
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=os.getenv("ENVIRONMENT", "production") == "development",
        log_level="info"
    )


if __name__ == "__main__":
    run_server()