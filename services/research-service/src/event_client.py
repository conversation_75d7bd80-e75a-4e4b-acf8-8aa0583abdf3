"""
Event Client for Research Service
Handles communication with the Event Bus Service
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional
from datetime import datetime
import logging

import aiohttp
from pydantic import BaseModel


class EventMessage(BaseModel):
    """Event message model"""
    event_id: str
    event_type: str
    source_service: str
    timestamp: str
    data: Dict[str, Any]


class EventClient:
    """
    Client for communicating with the Event Bus Service
    """
    
    def __init__(
        self,
        service_name: str,
        event_bus_url: Optional[str] = None,
        security_manager: Optional[Any] = None
    ):
        self.service_name = service_name
        self.event_bus_url = event_bus_url or "http://event-bus:8080"
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        self._connected = False
    
    async def connect(self):
        """Connect to Event Bus Service"""
        try:
            headers = {}
            if self.security_manager:
                api_key = self.security_manager.get_api_key()
                if api_key:
                    headers["Authorization"] = f"Bearer {api_key}"
            
            self._session = aiohttp.ClientSession(headers=headers)
            
            # Test connection
            async with self._session.get(f"{self.event_bus_url}/health") as response:
                if response.status == 200:
                    self._connected = True
                    self.logger.info(f"📡 Connected to Event Bus at {self.event_bus_url}")
                else:
                    raise Exception(f"Event Bus health check failed: {response.status}")
                    
        except Exception as e:
            self.logger.warning(f"⚠️  Failed to connect to Event Bus: {str(e)}")
            # Don't fail startup if Event Bus is not available
            self._connected = False
    
    async def disconnect(self):
        """Disconnect from Event Bus Service"""
        if self._session:
            await self._session.close()
            self._session = None
        
        self._connected = False
        self.logger.info("📡 Disconnected from Event Bus")
    
    async def publish_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        target_service: Optional[str] = None
    ) -> bool:
        """Publish an event to the Event Bus"""
        if not self._connected or not self._session:
            self.logger.warning("📡 Event Bus not connected, skipping event publication")
            return False
        
        try:
            event = EventMessage(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                source_service=self.service_name,
                timestamp=datetime.utcnow().isoformat(),
                data=data
            )
            
            payload = {
                "event_type": event.event_type,
                "payload": event.data,
                "source_service": event.source_service,
                "target_service": target_service
            }
            
            async with self._session.post(
                f"{self.event_bus_url}/events/publish",
                json=payload
            ) as response:
                if response.status == 200:
                    self.logger.info(f"📤 Published event: {event_type}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to publish event: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Event publication failed: {str(e)}")
            return False
    
    async def subscribe_to_events(
        self,
        event_types: list[str],
        callback: callable
    ):
        """Subscribe to specific event types (if needed)"""
        # Implementation would depend on Event Bus Service capabilities
        # For now, this is a placeholder
        self.logger.info(f"📥 Subscribed to events: {event_types}")
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to Event Bus"""
        return self._connected