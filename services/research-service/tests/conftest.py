"""
Pytest configuration and fixtures for Research Service tests
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
import os


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
def mock_environment():
    """Mock environment variables for testing"""
    env_vars = {
        'OPENAI_API_KEY': 'test-openai-key',
        'ANTHROPIC_API_KEY': 'test-anthropic-key',
        'PREFERRED_MODEL': 'openai',
        'SERVICE_HOST': 'localhost',
        'SERVICE_PORT': '8081',
        'EVENT_BUS_URL': 'http://localhost:8080',
        'SERVICE_DISCOVERY_URL': 'http://localhost:8070',
        'API_KEY': 'test-api-key',
        'ENVIRONMENT': 'testing'
    }
    
    with patch.dict(os.environ, env_vars):
        yield


@pytest.fixture
def mock_prometheus():
    """Mock Prometheus metrics"""
    with patch('src.monitoring.PROMETHEUS_AVAILABLE', False):
        yield


@pytest.fixture
def sample_research_result():
    """Sample research result for testing"""
    from src.research_agent import ResearchResult
    
    return ResearchResult(
        topic="Artificial Intelligence",
        key_findings=[
            "AI is transforming industries across the globe",
            "Machine learning algorithms are becoming more sophisticated",
            "Deep learning requires significant computational resources",
            "AI ethics is becoming increasingly important"
        ],
        sources=[
            "MIT Technology Review - AI Trends 2024",
            "Nature Machine Intelligence Journal",
            "IEEE Transactions on Artificial Intelligence",
            "Stanford AI Index Report"
        ],
        research_depth="comprehensive",
        focus_areas=["machine learning", "ethics"],
        summary="Artificial Intelligence continues to evolve rapidly with significant impacts on various sectors.",
        timestamp="2024-01-01T12:00:00Z"
    )