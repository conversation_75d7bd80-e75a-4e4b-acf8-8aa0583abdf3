"""
Tests for Research Service main application
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
import asyncio

from src.main import app
from src.research_agent import ResearchResult


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_security_manager():
    """Mock security manager"""
    mock_manager = Mock()
    mock_manager.verify_api_key.return_value = True
    return mock_manager


@pytest.fixture
def mock_research_agent():
    """Mock research agent"""
    mock_agent = Mock()
    mock_agent.is_ready = True
    
    # Mock research result
    mock_result = ResearchResult(
        topic="Test Topic",
        key_findings=["Finding 1", "Finding 2"],
        sources=["Source 1", "Source 2"],
        research_depth="comprehensive",
        focus_areas=[],
        summary="Test summary",
        timestamp="2024-01-01T00:00:00"
    )
    
    mock_agent.research_topic = AsyncMock(return_value=mock_result)
    return mock_agent


class TestResearchServiceAPI:
    """Test cases for Research Service API endpoints"""
    
    def test_health_check(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "research-service"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data
    
    def test_readiness_check_not_ready(self, client):
        """Test readiness check when service not ready"""
        with patch('src.main.research_agent', None):
            response = client.get("/ready")
            assert response.status_code == 503
            assert "not ready" in response.json()["detail"]
    
    def test_readiness_check_ready(self, client, mock_research_agent):
        """Test readiness check when service is ready"""
        with patch('src.main.research_agent', mock_research_agent), \
             patch('src.main.event_client', Mock()), \
             patch('src.main.service_registry', Mock()):
            
            response = client.get("/ready")
            assert response.status_code == 200
            assert response.json()["status"] == "ready"
    
    def test_research_topic_async_unauthorized(self, client):
        """Test async research endpoint without authentication"""
        response = client.post(
            "/research",
            json={
                "topic": "Artificial Intelligence",
                "research_depth": "comprehensive"
            }
        )
        
        assert response.status_code == 403  # No Authorization header
    
    def test_research_topic_async_success(self, client, mock_security_manager, mock_research_agent):
        """Test successful async research request"""
        with patch('src.main.security_manager', mock_security_manager), \
             patch('src.main.research_agent', mock_research_agent):
            
            response = client.post(
                "/research",
                json={
                    "topic": "Artificial Intelligence",
                    "research_depth": "comprehensive",
                    "focus_areas": ["machine learning"]
                },
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "started"
            assert "request_id" in data
            assert "Research task started" in data["message"]
    
    def test_research_topic_sync_success(self, client, mock_security_manager, mock_research_agent):
        """Test successful sync research request"""
        with patch('src.main.security_manager', mock_security_manager), \
             patch('src.main.research_agent', mock_research_agent):
            
            response = client.post(
                "/research/sync",
                json={
                    "topic": "Blockchain Technology",
                    "research_depth": "basic"
                },
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            assert data["result"]["topic"] == "Test Topic"
            assert len(data["result"]["key_findings"]) > 0
    
    def test_research_topic_sync_agent_unavailable(self, client, mock_security_manager):
        """Test sync research when agent is unavailable"""
        with patch('src.main.security_manager', mock_security_manager), \
             patch('src.main.research_agent', None):
            
            response = client.post(
                "/research/sync",
                json={"topic": "Test Topic"},
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 503
            assert "Research agent not available" in response.json()["detail"]
    
    def test_get_research_status(self, client, mock_security_manager):
        """Test getting research status"""
        with patch('src.main.security_manager', mock_security_manager):
            
            response = client.get(
                "/research/test-request-id",
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["request_id"] == "test-request-id"
            assert data["status"] == "completed"
    
    def test_metrics_endpoint(self, client):
        """Test metrics endpoint"""
        with patch('src.main.get_metrics_registry') as mock_registry:
            mock_registry.return_value = Mock()
            
            with patch('prometheus_client.generate_latest') as mock_generate:
                mock_generate.return_value = b"# HELP test_metric Test metric\n"
                
                response = client.get("/metrics")
                assert response.status_code == 200
    
    def test_invalid_research_request(self, client, mock_security_manager):
        """Test invalid research request"""
        with patch('src.main.security_manager', mock_security_manager):
            
            # Missing required topic field
            response = client.post(
                "/research/sync",
                json={"research_depth": "basic"},
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 422  # Validation error
    
    def test_research_request_validation(self, client, mock_security_manager, mock_research_agent):
        """Test research request with all fields"""
        with patch('src.main.security_manager', mock_security_manager), \
             patch('src.main.research_agent', mock_research_agent):
            
            response = client.post(
                "/research/sync",
                json={
                    "topic": "Quantum Computing",
                    "research_depth": "expert",
                    "focus_areas": ["quantum algorithms", "quantum hardware"],
                    "user_id": "user123"
                },
                headers={"Authorization": "Bearer test-key"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
    
    def test_cors_headers(self, client):
        """Test CORS headers are present"""
        response = client.options("/health")
        # CORS middleware should handle OPTIONS requests
        assert response.status_code in [200, 405]  # Either allowed or method not allowed


class TestResearchServiceModels:
    """Test Pydantic models used in the service"""
    
    def test_research_request_model(self):
        """Test ResearchRequest model validation"""
        from src.main import ResearchRequest
        
        # Valid request
        request = ResearchRequest(
            topic="AI Ethics",
            research_depth="comprehensive",
            focus_areas=["bias", "privacy"],
            user_id="user123"
        )
        
        assert request.topic == "AI Ethics"
        assert request.research_depth == "comprehensive"
        assert request.focus_areas == ["bias", "privacy"]
        assert request.user_id == "user123"
    
    def test_research_request_defaults(self):
        """Test ResearchRequest model defaults"""
        from src.main import ResearchRequest
        
        request = ResearchRequest(topic="Test Topic")
        
        assert request.topic == "Test Topic"
        assert request.research_depth == "comprehensive"
        assert request.focus_areas is None
        assert request.user_id is None
    
    def test_research_response_model(self):
        """Test ResearchResponse model"""
        from src.main import ResearchResponse
        from src.research_agent import ResearchResult
        
        result = ResearchResult(
            topic="Test",
            key_findings=["Finding"],
            sources=["Source"],
            research_depth="basic",
            focus_areas=[],
            summary="Summary",
            timestamp="2024-01-01T00:00:00"
        )
        
        response = ResearchResponse(
            request_id="test-id",
            status="completed",
            result=result,
            message="Success"
        )
        
        assert response.request_id == "test-id"
        assert response.status == "completed"
        assert response.result.topic == "Test"
        assert response.message == "Success"
    
    def test_health_response_model(self):
        """Test HealthResponse model"""
        from src.main import HealthResponse
        
        health = HealthResponse(
            status="healthy",
            service="research-service",
            version="1.0.0",
            timestamp="2024-01-01T00:00:00"
        )
        
        assert health.status == "healthy"
        assert health.service == "research-service"
        assert health.version == "1.0.0"