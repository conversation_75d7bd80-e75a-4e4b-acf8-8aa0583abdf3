"""
Simple Tests for Research Service main application
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient


def test_app_creation():
    """Test that the FastAPI app can be created"""
    with patch('src.main.research_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        assert app is not None
        assert app.title == "Research Service"


def test_health_endpoint():
    """Test health check endpoint"""
    with patch('src.main.research_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "research-service"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data


def test_readiness_endpoint_not_ready():
    """Test readiness endpoint when not ready"""
    with patch('src.main.research_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.get("/ready")
        
        assert response.status_code == 503
        assert "not ready" in response.json()["detail"]


def test_research_endpoint_no_auth():
    """Test research endpoint without authentication"""
    with patch('src.main.research_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.post(
            "/research",
            json={"topic": "Test Topic"}
        )
        
        # Should fail due to missing authorization
        assert response.status_code == 403


def test_models_validation():
    """Test Pydantic models"""
    from src.main import ResearchRequest, ResearchResponse, HealthResponse
    
    # Test ResearchRequest
    request = ResearchRequest(topic="AI Ethics")
    assert request.topic == "AI Ethics"
    assert request.research_depth == "comprehensive"  # default
    
    # Test ResearchResponse
    response = ResearchResponse(
        request_id="test-123",
        status="pending",
        message="Started"
    )
    assert response.request_id == "test-123"
    assert response.status == "pending"
    
    # Test HealthResponse
    health = HealthResponse(
        status="healthy",
        service="test-service",
        version="1.0.0",
        timestamp="2024-01-01T00:00:00Z"
    )
    assert health.status == "healthy"