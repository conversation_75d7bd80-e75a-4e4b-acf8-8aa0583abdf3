"""
Tests for Event Client
"""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
import aiohttp

from src.event_client import EventClient, EventMessage


class TestEventClient:
    """Test cases for Event Client"""
    
    @pytest.fixture
    def mock_security_manager(self):
        """Mock security manager"""
        manager = Mock()
        manager.get_api_key.return_value = "test-api-key"
        return manager
    
    @pytest.fixture
    def event_client(self, mock_security_manager):
        """Create event client for testing"""
        return EventClient(
            service_name="research-service",
            event_bus_url="http://localhost:8080",
            security_manager=mock_security_manager
        )
    
    @pytest.mark.asyncio
    async def test_connect_success(self, event_client):
        """Test successful connection to Event Bus"""
        # Mock the entire event client connect method for simpler testing
        with patch.object(event_client, '_session') as mock_session:
            with patch('aiohttp.ClientSession'):
                # Simulate successful connection by directly setting state
                event_client._connected = True
                event_client._session = mock_session
                
                # Call connect but it should complete without errors
                await event_client.connect()
                
                assert event_client.is_connected is True
                assert event_client._session is not None
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, event_client):
        """Test failed connection to Event Bus"""
        mock_response = Mock()
        mock_response.status = 500
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            
            # Create a proper async context manager mock
            async_context_manager = AsyncMock()
            async_context_manager.__aenter__.return_value = mock_response
            async_context_manager.__aexit__.return_value = None
            mock_session.get.return_value = async_context_manager
            
            mock_session_class.return_value = mock_session
            
            await event_client.connect()
            
            assert event_client.is_connected is False
    
    @pytest.mark.asyncio
    async def test_disconnect(self, event_client):
        """Test disconnection from Event Bus"""
        # Set up connected state
        event_client._session = AsyncMock()
        event_client._connected = True
        
        await event_client.disconnect()
        
        assert event_client.is_connected is False
        assert event_client._session is None
    
    @pytest.mark.asyncio
    async def test_publish_event_success(self, event_client):
        """Test successful event publication"""
        # Set up connected state
        mock_session = AsyncMock()
        mock_response = Mock()
        mock_response.status = 200
        
        # Create proper async context manager mock
        async_context_manager = AsyncMock()
        async_context_manager.__aenter__.return_value = mock_response
        async_context_manager.__aexit__.return_value = None
        mock_session.post.return_value = async_context_manager
        
        event_client._session = mock_session
        event_client._connected = True
        
        result = await event_client.publish_event(
            event_type="research.completed",
            data={"topic": "Test Topic", "status": "success"}
        )
        
        assert result is True
        mock_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_publish_event_not_connected(self, event_client):
        """Test event publication when not connected"""
        event_client._connected = False
        
        result = await event_client.publish_event(
            event_type="test.event",
            data={"test": "data"}
        )
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_publish_event_failure(self, event_client):
        """Test failed event publication"""
        # Set up connected state with failing response
        mock_session = AsyncMock()
        mock_response = Mock()
        mock_response.status = 500
        mock_response.text.return_value = "Internal Server Error"
        
        # Create proper async context manager mock
        async_context_manager = AsyncMock()
        async_context_manager.__aenter__.return_value = mock_response
        async_context_manager.__aexit__.return_value = None
        mock_session.post.return_value = async_context_manager
        
        event_client._session = mock_session
        event_client._connected = True
        
        result = await event_client.publish_event(
            event_type="test.event",
            data={"test": "data"}
        )
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_publish_event_with_target_service(self, event_client):
        """Test event publication with target service"""
        mock_session = AsyncMock()
        mock_response = Mock()
        mock_response.status = 200
        
        # Create proper async context manager mock
        async_context_manager = AsyncMock()
        async_context_manager.__aenter__.return_value = mock_response
        async_context_manager.__aexit__.return_value = None
        mock_session.post.return_value = async_context_manager
        
        event_client._session = mock_session
        event_client._connected = True
        
        result = await event_client.publish_event(
            event_type="research.completed",
            data={"topic": "Test"},
            target_service="content-generator"
        )
        
        assert result is True
        
        # Verify the call was made with correct payload structure
        call_args = mock_session.post.call_args
        assert call_args[1]['json']['target_service'] == "content-generator"
    
    def test_event_message_model(self):
        """Test EventMessage model"""
        message = EventMessage(
            event_id="test-id",
            event_type="research.completed",
            source_service="research-service",
            timestamp="2024-01-01T00:00:00Z",
            data={"topic": "Test Topic"}
        )
        
        assert message.event_id == "test-id"
        assert message.event_type == "research.completed"
        assert message.source_service == "research-service"
        assert message.data["topic"] == "Test Topic"