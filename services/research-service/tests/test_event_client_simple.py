"""
Simplified Tests for Event Client
"""

import pytest
from unittest.mock import Mock

from src.event_client import Event<PERSON>lient, EventMessage


class TestEventClientSimple:
    """Simple test cases for Event Client focusing on core functionality"""
    
    def test_event_client_initialization(self):
        """Test event client initialization"""
        client = EventClient(
            service_name="test-service",
            event_bus_url="http://localhost:8080"
        )
        
        assert client.service_name == "test-service"
        assert client.event_bus_url == "http://localhost:8080"
        assert client.is_connected is False
    
    def test_event_client_with_security_manager(self):
        """Test event client with security manager"""
        mock_security = Mock()
        mock_security.get_api_key.return_value = "test-key"
        
        client = EventClient(
            service_name="test-service",
            security_manager=mock_security
        )
        
        assert client.security_manager is not None
        assert client.security_manager.get_api_key() == "test-key"
    
    def test_event_message_model(self):
        """Test EventMessage model"""
        message = EventMessage(
            event_id="test-id",
            event_type="research.completed",
            source_service="research-service",
            timestamp="2024-01-01T00:00:00Z",
            data={"topic": "Test Topic"}
        )
        
        assert message.event_id == "test-id"
        assert message.event_type == "research.completed"
        assert message.source_service == "research-service"
        assert message.data["topic"] == "Test Topic"
    
    @pytest.mark.asyncio
    async def test_publish_event_not_connected(self):
        """Test event publication when not connected"""
        client = EventClient(service_name="test-service")
        
        result = await client.publish_event(
            event_type="test.event",
            data={"test": "data"}
        )
        
        # Should return False when not connected
        assert result is False
    
    @pytest.mark.asyncio
    async def test_disconnect_safe(self):
        """Test safe disconnection"""
        client = EventClient(service_name="test-service")
        
        # Should not raise an error even if not connected
        await client.disconnect()
        
        assert client.is_connected is False