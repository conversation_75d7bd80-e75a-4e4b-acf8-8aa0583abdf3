"""
Tests for Research Agent
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.research_agent import ResearchAgent, ResearchResult


class TestResearchAgent:
    """Test cases for Research Agent"""
    
    @pytest.fixture
    async def research_agent(self):
        """Create research agent instance for testing"""
        agent = ResearchAgent()
        
        # Mock the model initialization to avoid API calls
        with patch.object(agent, '_get_available_model') as mock_model:
            mock_model.return_value = Mock()
            await agent.initialize()
        
        return agent
    
    @pytest.mark.asyncio
    async def test_research_agent_initialization(self):
        """Test research agent initialization"""
        agent = ResearchAgent()
        
        with patch.object(agent, '_get_available_model') as mock_model:
            mock_model.return_value = Mock()
            await agent.initialize()
            
            assert agent._initialized is True
            assert agent._agent is not None
            assert agent.is_ready is True
    
    @pytest.mark.asyncio
    async def test_research_agent_initialization_failure(self):
        """Test research agent initialization failure"""
        agent = ResearchAgent()
        
        with patch.object(agent, '_get_available_model') as mock_model:
            mock_model.return_value = None
            
            with pytest.raises(ValueError, match="No AI model available"):
                await agent.initialize()
    
    @pytest.mark.asyncio
    async def test_research_topic_success(self, research_agent):
        """Test successful topic research"""
        # Mock the agent execution
        mock_result = Mock()
        mock_result.output = ResearchResult(
            topic="Artificial Intelligence",
            key_findings=[
                "AI is revolutionizing industries",
                "Machine learning is a subset of AI",
                "Deep learning uses neural networks"
            ],
            sources=[
                "MIT Technology Review",
                "Nature Machine Intelligence",
                "IEEE Transactions on AI"
            ],
            research_depth="comprehensive",
            focus_areas=["machine learning", "deep learning"],
            summary="AI is transforming various sectors through advanced algorithms",
            timestamp=datetime.utcnow().isoformat()
        )
        
        research_agent._agent.run = AsyncMock(return_value=mock_result)
        
        result = await research_agent.research_topic(
            topic="Artificial Intelligence",
            research_depth="comprehensive",
            focus_areas=["machine learning", "deep learning"]
        )
        
        assert isinstance(result, ResearchResult)
        assert result.topic == "Artificial Intelligence"
        assert len(result.key_findings) == 3
        assert len(result.sources) == 3
        assert result.research_depth == "comprehensive"
        assert "machine learning" in result.focus_areas
    
    @pytest.mark.asyncio
    async def test_research_topic_not_initialized(self):
        """Test research topic when agent not initialized"""
        agent = ResearchAgent()
        
        with pytest.raises(RuntimeError, match="Research agent not initialized"):
            await agent.research_topic("Test Topic")
    
    @pytest.mark.asyncio
    async def test_research_topic_with_defaults(self, research_agent):
        """Test research topic with default parameters"""
        mock_result = Mock()
        mock_result.output = ResearchResult(
            topic="Climate Change",
            key_findings=["Global temperatures rising", "CO2 levels increasing"],
            sources=["IPCC Report", "NASA Climate"],
            research_depth="comprehensive",
            focus_areas=[],
            summary="Climate change is a significant global challenge",
            timestamp=datetime.utcnow().isoformat()
        )
        
        research_agent._agent.run = AsyncMock(return_value=mock_result)
        
        result = await research_agent.research_topic("Climate Change")
        
        assert result.topic == "Climate Change"
        assert result.research_depth == "comprehensive"
        assert result.focus_areas == []
    
    @pytest.mark.asyncio
    async def test_get_research_suggestions(self, research_agent):
        """Test getting research suggestions"""
        suggestions = await research_agent.get_research_suggestions("Blockchain")
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        assert any("blockchain" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_validate_research_topic_valid(self, research_agent):
        """Test topic validation for valid topic"""
        result = await research_agent.validate_research_topic("Machine Learning Applications")
        
        assert result["valid"] is True
        assert result["reason"] == "Topic is suitable for research"
        assert len(result["suggestions"]) == 0
    
    @pytest.mark.asyncio
    async def test_validate_research_topic_too_short(self, research_agent):
        """Test topic validation for too short topic"""
        result = await research_agent.validate_research_topic("AI")
        
        assert result["valid"] is False
        assert "too short" in result["reason"].lower()
        assert len(result["suggestions"]) > 0
    
    @pytest.mark.asyncio
    async def test_validate_research_topic_too_long(self, research_agent):
        """Test topic validation for too long topic"""
        long_topic = "A" * 600  # 600 characters
        result = await research_agent.validate_research_topic(long_topic)
        
        assert result["valid"] is False
        assert "too long" in result["reason"].lower()
    
    @pytest.mark.asyncio
    async def test_validate_research_topic_inappropriate(self, research_agent):
        """Test topic validation for inappropriate content"""
        result = await research_agent.validate_research_topic("How to make illegal substances")
        
        assert result["valid"] is False
        assert "inappropriate" in result["reason"].lower()
    
    @pytest.mark.asyncio
    async def test_cleanup(self, research_agent):
        """Test agent cleanup"""
        await research_agent.cleanup()
        
        assert research_agent._initialized is False
    
    def test_get_available_model_openai(self):
        """Test getting OpenAI model"""
        agent = ResearchAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'openai',
            'OPENAI_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_anthropic(self):
        """Test getting Anthropic model"""
        agent = ResearchAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'anthropic',
            'ANTHROPIC_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_none(self):
        """Test when no model is available"""
        agent = ResearchAgent()
        
        with patch.dict('os.environ', {}, clear=True):
            model = agent._get_available_model()
            assert model is None
    
    def test_system_prompt_content(self):
        """Test system prompt contains required elements"""
        agent = ResearchAgent()
        prompt = agent._get_system_prompt()
        
        assert "research assistant" in prompt.lower()
        assert "key findings" in prompt.lower()
        assert "sources" in prompt.lower()
        assert "comprehensive" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_research_with_different_depths(self, research_agent):
        """Test research with different depth levels"""
        depths = ["basic", "moderate", "comprehensive", "expert"]
        
        for depth in depths:
            mock_result = Mock()
            mock_result.output = ResearchResult(
                topic="Test Topic",
                key_findings=[f"Finding for {depth}"],
                sources=[f"Source for {depth}"],
                research_depth=depth,
                focus_areas=[],
                summary=f"Summary for {depth}",
                timestamp=datetime.utcnow().isoformat()
            )
            
            research_agent._agent.run = AsyncMock(return_value=mock_result)
            
            result = await research_agent.research_topic(
                topic="Test Topic",
                research_depth=depth
            )
            
            assert result.research_depth == depth