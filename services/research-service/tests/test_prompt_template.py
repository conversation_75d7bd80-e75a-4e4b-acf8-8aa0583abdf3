"""
Tests for Prompt Template
"""

import pytest
from src.prompt_template import PromptTemplate, get_research_template


class TestPromptTemplate:
    """Test cases for Prompt Template"""
    
    def test_simple_variable_substitution(self):
        """Test simple variable substitution"""
        template = PromptTemplate.from_string("Hello {{ name }}!")
        result = template.format(name="World")
        assert result == "Hello World!"
    
    def test_multiple_variables(self):
        """Test multiple variable substitution"""
        template = PromptTemplate.from_string("{{ greeting }} {{ name }}!")
        result = template.format(greeting="Hello", name="<PERSON>")
        assert result == "Hello Claude!"
    
    def test_conditional_block_true(self):
        """Test conditional block when condition is true"""
        template = PromptTemplate.from_string("""
        Base content
        {% if show_extra %}
        Extra content
        {% endif %}
        """)
        result = template.format(show_extra=True)
        assert "Extra content" in result
        assert "Base content" in result
    
    def test_conditional_block_false(self):
        """Test conditional block when condition is false"""
        template = PromptTemplate.from_string("""
        Base content
        {% if show_extra %}
        Extra content
        {% endif %}
        """)
        result = template.format(show_extra=False)
        assert "Extra content" not in result
        assert "Base content" in result
    
    def test_loop_block(self):
        """Test loop block"""
        template = PromptTemplate.from_string("""
        Items:
        {% for item in items %}
        - {{ item }}
        {% endfor %}
        """)
        result = template.format(items=["apple", "banana", "cherry"])
        assert "- apple" in result
        assert "- banana" in result
        assert "- cherry" in result
    
    def test_empty_loop(self):
        """Test loop with empty list"""
        template = PromptTemplate.from_string("""
        Items:
        {% for item in items %}
        - {{ item }}
        {% endfor %}
        """)
        result = template.format(items=[])
        assert result.strip() == "Items:"
    
    def test_variable_extraction(self):
        """Test variable extraction from template"""
        template = PromptTemplate.from_string("{{ name }} and {{ age }}")
        variables = template.variables
        assert "name" in variables
        assert "age" in variables
        assert len(variables) == 2
    
    def test_validation_missing_variables(self):
        """Test validation with missing variables"""
        template = PromptTemplate.from_string("{{ name }} and {{ age }}")
        missing = template.validate(name="John")
        assert "age" in missing
        assert "name" not in missing
    
    def test_validation_all_provided(self):
        """Test validation with all variables provided"""
        template = PromptTemplate.from_string("{{ name }} and {{ age }}")
        missing = template.validate(name="John", age=25)
        assert len(missing) == 0
    
    def test_complex_template(self):
        """Test complex template with conditionals and loops"""
        template = PromptTemplate.from_string("""
        Research Topic: {{ topic }}
        
        {% if focus_areas %}
        Focus Areas:
        {% for area in focus_areas %}
        - {{ area }}
        {% endfor %}
        {% endif %}
        
        Depth: {{ depth }}
        """)
        
        result = template.format(
            topic="AI Ethics",
            focus_areas=["bias", "privacy"],
            depth="comprehensive"
        )
        
        assert "Research Topic: AI Ethics" in result
        assert "- bias" in result
        assert "- privacy" in result
        assert "Depth: comprehensive" in result
    
    def test_get_research_template_basic(self):
        """Test getting basic research template"""
        template = get_research_template("basic")
        assert isinstance(template, PromptTemplate)
        
        result = template.format(topic="Test Topic")
        assert "Test Topic" in result
        assert "3-5 key findings" in result
    
    def test_get_research_template_comprehensive(self):
        """Test getting comprehensive research template"""
        template = get_research_template("comprehensive")
        assert isinstance(template, PromptTemplate)
        
        result = template.format(
            topic="Machine Learning",
            focus_areas=["algorithms", "applications"]
        )
        assert "Machine Learning" in result
        assert "8-12 detailed key findings" in result
        assert "algorithms" in result
        assert "applications" in result
    
    def test_get_research_template_expert(self):
        """Test getting expert research template"""
        template = get_research_template("expert")
        assert isinstance(template, PromptTemplate)
        
        result = template.format(topic="Quantum Computing")
        assert "Quantum Computing" in result
        assert "12-15 detailed findings" in result
        assert "technical depth" in result
    
    def test_get_research_template_unknown_depth(self):
        """Test getting template for unknown research depth"""
        template = get_research_template("unknown_depth")
        # Should default to comprehensive
        assert isinstance(template, PromptTemplate)
        
        result = template.format(topic="Test")
        assert "8-12 detailed key findings" in result