# Research Service Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: research-service
  namespace: publish-ai
  labels:
    app: research-service
    component: microservice
    tier: tier-3
spec:
  replicas: 2
  selector:
    matchLabels:
      app: research-service
  template:
    metadata:
      labels:
        app: research-service
        component: microservice
        tier: tier-3
    spec:
      containers:
      - name: research-service
        image: publish-ai/research-service:latest
        ports:
        - containerPort: 8081
          name: http
        env:
        - name: SERVICE_HOST
          value: "0.0.0.0"
        - name: SERVICE_PORT
          value: "8081"
        - name: SERVICE_NAME
          value: "research-service"
        - name: PREFERRED_MODEL
          value: "openai"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: anthropic-api-key
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: research-service-tls
              key: api-key
        - name: EVENT_BUS_URL
          value: "https://event-bus.publish-ai.svc.cluster.local:8080"
        - name: SERVICE_DISCOVERY_URL
          value: "https://service-discovery.publish-ai.svc.cluster.local:8070"
        - name: ENVIRONMENT
          value: "production"
        
        # Resource limits
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
        
        # Security context
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        
        # Volume mounts for certificates
        volumeMounts:
        - name: tls-certs
          mountPath: /etc/ssl/certs
          readOnly: true
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/.cache
      
      # Security context for pod
      securityContext:
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Volumes
      volumes:
      - name: tls-certs
        secret:
          secretName: research-service-tls
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      
      # Service account
      serviceAccountName: research-service
      
      # Node selection
      nodeSelector:
        publish-ai/node-type: "worker"
      
      # Pod disruption budget considerations
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchLabels:
                  app: research-service
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: research-service
  namespace: publish-ai
  labels:
    app: research-service
    component: microservice
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: research-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: research-service
  namespace: publish-ai
  labels:
    app: research-service

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: research-service-pdb
  namespace: publish-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: research-service