# Research Service

A microservice implementation of the Research Assistant Agent, providing comprehensive topic research and information gathering capabilities.

## Overview

The Research Service is part of the Publish AI platform's microservices architecture migration. It extracts the research functionality from the monolithic system and provides it as a standalone, scalable service.

## Features

- **Comprehensive Research**: Multi-depth research capabilities (basic, moderate, comprehensive, expert)
- **AI-Powered**: Supports both OpenAI and Anthropic models with automatic fallback
- **Event-Driven**: Integrates with Event Bus for asynchronous communication
- **Service Discovery**: Automatic registration and health monitoring
- **Security**: API key authentication and mTLS support
- **Monitoring**: Prometheus metrics and health checks
- **Scalable**: Docker containerization with Kubernetes support

## API Endpoints

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### Research Operations
- `POST /research` - Start asynchronous research task
- `GET /research/{request_id}` - Get research status and results
- `POST /research/sync` - Synchronous research execution

## Request/Response Models

### Research Request
```json
{
  "topic": "Artificial Intelligence",
  "research_depth": "comprehensive",
  "focus_areas": ["machine learning", "ethics"],
  "user_id": "user123"
}
```

### Research Response
```json
{
  "request_id": "uuid-string",
  "status": "completed",
  "result": {
    "topic": "Artificial Intelligence",
    "key_findings": [
      "AI is transforming industries...",
      "Machine learning algorithms..."
    ],
    "sources": [
      "MIT Technology Review",
      "Nature Machine Intelligence"
    ],
    "research_depth": "comprehensive",
    "focus_areas": ["machine learning", "ethics"],
    "summary": "Executive summary...",
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "message": "Research completed successfully"
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVICE_HOST` | Service host address | `0.0.0.0` |
| `SERVICE_PORT` | Service port | `8081` |
| `PREFERRED_MODEL` | AI model preference | `openai` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `ANTHROPIC_API_KEY` | Anthropic API key | Optional |
| `EVENT_BUS_URL` | Event Bus service URL | `http://event-bus:8080` |
| `SERVICE_DISCOVERY_URL` | Service Discovery URL | `http://service-discovery:8070` |
| `API_KEY` | Service API key | Required |

### Research Depth Levels

- **basic**: 3-5 key findings, 2-3 sources, brief summary
- **moderate**: 5-8 findings, 3-5 sources, moderate detail
- **comprehensive**: 8-12 findings, 5-8 sources, detailed analysis
- **expert**: 12-15 findings, 8-12 sources, technical depth

## Development

### Local Development

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export API_KEY="your-service-api-key"
   ```

3. **Run Service**
   ```bash
   cd src
   python main.py
   ```

### Testing

Run all tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=src --cov-report=html
```

Run specific test category:
```bash
pytest -m unit
pytest -m integration
```

### Docker Development

1. **Build Image**
   ```bash
   docker build -t research-service .
   ```

2. **Run Container**
   ```bash
   docker run -p 8081:8081 \
     -e OPENAI_API_KEY="your-key" \
     -e API_KEY="your-api-key" \
     research-service
   ```

3. **Docker Compose**
   ```bash
   docker-compose up --build
   ```

## Architecture

### Components

- **Research Agent**: Core AI-powered research functionality
- **Event Client**: Communication with Event Bus service
- **Service Registry Client**: Registration with Service Discovery
- **Security Manager**: API key authentication and mTLS
- **Monitoring**: Prometheus metrics and health checks

### Dependencies

- **Infrastructure Services**: Event Bus, Service Discovery
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **Security**: mTLS certificates, API keys
- **Monitoring**: Prometheus metrics collection

### Data Flow

1. Client sends research request with authentication
2. Service validates request and API key
3. Research Agent processes request using AI model
4. Results published to Event Bus (async mode)
5. Metrics recorded for monitoring
6. Response returned to client

## Deployment

### Docker Compose
```yaml
services:
  research-service:
    build: .
    ports:
      - "8081:8081"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${RESEARCH_SERVICE_API_KEY}
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
    networks:
      - publish-ai-network
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: research-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: research-service
  template:
    metadata:
      labels:
        app: research-service
        component: microservice
    spec:
      containers:
      - name: research-service
        image: research-service:latest
        ports:
        - containerPort: 8081
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
```

## Security

### Authentication
- API key authentication for all endpoints
- Service-to-service authentication via mTLS
- Security headers and CORS configuration

### mTLS Configuration
- Service certificates generated by internal CA
- Mutual authentication between services
- Encrypted inter-service communication

### Network Security
- Kubernetes NetworkPolicies for micro-segmentation
- Zero Trust networking principles
- Firewall rules for service isolation

## Monitoring

### Metrics
- HTTP request count and duration
- Research operation metrics
- Agent health status
- Event publication counts

### Health Checks
- `/health` - Basic health status
- `/ready` - Readiness for traffic
- Service Discovery integration

### Logging
- Structured logging with context
- Request tracing and correlation
- Error tracking and alerting

## Testing

### Test Coverage
- Unit tests for all components
- Integration tests for API endpoints
- Mock testing for external dependencies
- Performance and load testing

### Test Categories
- **Unit Tests**: Component-level testing
- **Integration Tests**: API endpoint testing
- **Contract Tests**: Service interface testing
- **Performance Tests**: Load and stress testing

## Migration Status

✅ **Phase 2.1: Research Service Migration - COMPLETED**

- [x] Agent extraction and containerization
- [x] Event-driven communication integration
- [x] Service Discovery registration
- [x] Security implementation (API keys + mTLS)
- [x] Comprehensive testing suite
- [x] Docker containerization
- [x] Monitoring and metrics
- [x] Documentation and deployment guides

**Next Steps**: Personalization Engine Service migration

## Troubleshooting

### Common Issues

1. **AI Model Not Available**
   - Check API keys are set correctly
   - Verify model endpoints are accessible
   - Check rate limits and quotas

2. **Service Registration Failed**
   - Verify Service Discovery is running
   - Check network connectivity
   - Validate API key configuration

3. **Event Bus Connection Failed**
   - Check Event Bus service status
   - Verify network policies allow connection
   - Check authentication credentials

### Debug Commands

```bash
# Check service health
curl http://localhost:8081/health

# Verify readiness
curl http://localhost:8081/ready

# Get metrics
curl http://localhost:8081/metrics

# Test research endpoint
curl -X POST http://localhost:8081/research/sync \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"topic": "Test Topic"}'
```