# VERL Training Service Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
redis==5.0.1
pandas==2.1.3
numpy==1.24.3
psutil==5.9.6
prometheus-client==0.19.0

# VERL core dependencies
torch==2.1.0
transformers==4.35.0
datasets==2.14.6
accelerate==0.24.1
vllm==0.2.2
ray[default]==2.8.0

# Optional dependencies
wandb==0.16.0
tensorboard==2.15.1
omegaconf==2.3.0
hydra-core==1.3.2

# Database
asyncpg==0.29.0
supabase==2.0.3