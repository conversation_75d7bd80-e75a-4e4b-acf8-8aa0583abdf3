#!/usr/bin/env python3
"""
VERL Training Microservice

Dedicated service for VERL reinforcement learning training with:
- GPU resource isolation
- Distributed training via Ray
- Redis event communication
- Comprehensive monitoring
"""

import asyncio
import json
import logging
import os
import signal
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import redis.asyncio as redis
import torch
from fastapi import FastAPI, HTTPException, BackgroundTasks
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Prometheus metrics
TRAINING_REQUESTS = Counter('verl_training_requests_total', 'Total training requests')
TRAINING_DURATION = Histogram('verl_training_duration_seconds', 'Training duration in seconds')
ACTIVE_TRAININGS = Gauge('verl_active_trainings', 'Number of active training jobs')
GPU_MEMORY_USAGE = Gauge('verl_gpu_memory_usage_bytes', 'GPU memory usage', ['device'])

# VERL imports with graceful fallback
try:
    import ray
    from verl.trainer.ppo.ray_trainer import RayPPOTrainer
    from verl.utils.reward_model import RewardModel
    import hydra
    from omegaconf import DictConfig, OmegaConf
    VERL_AVAILABLE = True
    logger.info("VERL framework loaded successfully")
except ImportError as e:
    logger.warning(f"VERL framework not available: {e}")
    VERL_AVAILABLE = False
    
    # Mock classes for development
    class RayPPOTrainer:
        def __init__(self, config): pass
        def train_epoch(self): return {"loss": 0.5, "reward": 0.8}
        def save_model(self, path): pass
    
    class DictConfig(dict): pass
    class OmegaConf:
        @staticmethod
        def create(data): return DictConfig(data)
        @staticmethod
        def save(config, path): pass

# Pydantic models
class TrainingRequest(BaseModel):
    training_id: str
    model_name: str = "microsoft/DialoGPT-medium"
    dataset_path: Optional[str] = None
    num_epochs: int = 3
    learning_rate: float = 2e-5
    batch_size: int = 8
    max_response_length: int = 256
    reward_weights: Dict[str, float] = {
        "user_approval": 0.4,
        "content_quality": 0.3,
        "sales_performance": 0.3
    }
    priority: str = "normal"  # "low", "normal", "high"

class TrainingStatus(BaseModel):
    training_id: str
    status: str  # "queued", "training", "completed", "failed", "cancelled"
    progress: float = 0.0
    current_epoch: int = 0
    total_epochs: int = 0
    metrics: Optional[Dict] = None
    error_message: Optional[str] = None
    model_path: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    gpu_devices: List[int] = []

class VERLTrainingService:
    """Enhanced VERL Training Service with microservices architecture"""
    
    def __init__(self):
        self.training_jobs: Dict[str, TrainingStatus] = {}
        self.job_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = True
        
        # Directories
        self.base_dir = Path("/app")
        self.models_dir = self.base_dir / "models"
        self.checkpoints_dir = self.base_dir / "checkpoints"
        self.training_data_dir = self.base_dir / "training_data"
        self.logs_dir = self.base_dir / "logs"
        
        # Create directories
        for dir_path in [self.models_dir, self.checkpoints_dir, self.training_data_dir, self.logs_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Redis client for event communication
        self.redis_client = None
        
        # Ray initialization flag
        self.ray_initialized = False
        
        # GPU tracking
        self.available_gpus = self._detect_gpus()
        logger.info(f"Detected {len(self.available_gpus)} GPUs: {self.available_gpus}")
    
    def _detect_gpus(self) -> List[int]:
        """Detect available GPU devices"""
        if torch.cuda.is_available():
            return list(range(torch.cuda.device_count()))
        return []
    
    async def initialize(self):
        """Initialize service components"""
        try:
            # Initialize Redis connection
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            self.redis_client = redis.from_url(redis_url)
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Initialize Ray cluster
            if VERL_AVAILABLE and not self.ray_initialized:
                await self._initialize_ray()
            
            # Start background workers
            asyncio.create_task(self._training_worker())
            asyncio.create_task(self._gpu_monitor())
            
            logger.info("VERL Training Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Service initialization failed: {e}")
            raise
    
    async def _initialize_ray(self):
        """Initialize Ray cluster for distributed training"""
        try:
            ray_head = os.getenv("RAY_HEAD_NODE")
            if ray_head:
                ray.init(address=ray_head)
                logger.info(f"Connected to Ray cluster at {ray_head}")
            else:
                ray.init(
                    ignore_reinit_error=True,
                    runtime_env={
                        "pip": ["transformers", "datasets", "torch", "accelerate"]
                    }
                )
                logger.info("Started local Ray cluster")
            
            self.ray_initialized = True
            
        except Exception as e:
            logger.warning(f"Ray initialization failed: {e}")
            # Continue without Ray for basic functionality
    
    async def submit_training_job(self, request: TrainingRequest) -> TrainingStatus:
        """Submit a new training job to the queue"""
        TRAINING_REQUESTS.inc()
        
        # Create training status
        status = TrainingStatus(
            training_id=request.training_id,
            status="queued",
            total_epochs=request.num_epochs,
            created_at=datetime.utcnow()
        )
        
        self.training_jobs[request.training_id] = status
        
        # Add to processing queue
        await self.job_queue.put(request)
        
        # Publish event
        await self._publish_event("training_queued", {
            "training_id": request.training_id,
            "priority": request.priority,
            "queue_size": self.job_queue.qsize()
        })
        
        logger.info(f"Training job {request.training_id} queued")
        return status
    
    async def _training_worker(self):
        """Background worker to process training jobs"""
        logger.info("Training worker started")
        
        while self.is_running:
            try:
                # Get next job from queue
                request = await asyncio.wait_for(self.job_queue.get(), timeout=5.0)
                
                # Process the training job
                await self._process_training_job(request)
                
            except asyncio.TimeoutError:
                # No jobs in queue, continue
                continue
            except Exception as e:
                logger.error(f"Training worker error: {e}")
                await asyncio.sleep(5)  # Back off on error
    
    async def _process_training_job(self, request: TrainingRequest):
        """Process a single training job"""
        training_id = request.training_id
        status = self.training_jobs.get(training_id)
        
        if not status:
            logger.error(f"Training status not found for {training_id}")
            return
        
        try:
            # Update status to training
            status.status = "training"
            status.started_at = datetime.utcnow()
            ACTIVE_TRAININGS.inc()
            
            # Allocate GPU resources
            gpu_devices = self._allocate_gpus(1)  # Request 1 GPU
            status.gpu_devices = gpu_devices
            
            await self._publish_event("training_started", {
                "training_id": training_id,
                "gpu_devices": gpu_devices,
                "model_name": request.model_name
            })
            
            # Create VERL configuration
            config = self._create_verl_config(request)
            
            # Start training timer
            start_time = datetime.utcnow()
            
            if VERL_AVAILABLE:
                await self._run_verl_training(request, config, status)
            else:
                # Mock training for development
                await self._run_mock_training(request, status)
            
            # Calculate training duration
            duration = (datetime.utcnow() - start_time).total_seconds()
            TRAINING_DURATION.observe(duration)
            
            # Complete training
            status.status = "completed"
            status.completed_at = datetime.utcnow()
            status.progress = 1.0
            
            # Save model path
            model_path = self.models_dir / f"trained_model_{training_id}"
            status.model_path = str(model_path)
            
            await self._publish_event("training_completed", {
                "training_id": training_id,
                "duration_seconds": duration,
                "model_path": str(model_path),
                "final_metrics": status.metrics
            })
            
            logger.info(f"Training completed: {training_id} in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Training failed for {training_id}: {e}")
            status.status = "failed"
            status.error_message = str(e)
            status.completed_at = datetime.utcnow()
            
            await self._publish_event("training_failed", {
                "training_id": training_id,
                "error": str(e)
            })
        
        finally:
            # Release GPU resources
            if status.gpu_devices:
                self._release_gpus(status.gpu_devices)
                status.gpu_devices = []
            
            ACTIVE_TRAININGS.dec()
    
    def _allocate_gpus(self, count: int) -> List[int]:
        """Allocate GPU devices for training"""
        if not self.available_gpus or count > len(self.available_gpus):
            return []
        
        # Simple allocation - return first N available GPUs
        # In production, implement proper resource tracking
        return self.available_gpus[:count]
    
    def _release_gpus(self, gpu_devices: List[int]):
        """Release GPU devices after training"""
        # Clear GPU memory
        for device in gpu_devices:
            if torch.cuda.is_available():
                with torch.cuda.device(device):
                    torch.cuda.empty_cache()
    
    def _create_verl_config(self, request: TrainingRequest) -> DictConfig:
        """Create VERL training configuration"""
        config_dict = {
            "data": {
                "train_files": str(self.training_data_dir / f"training_data_{request.training_id}.parquet"),
                "val_files": str(self.training_data_dir / f"training_data_{request.training_id}.parquet"),
                "prompt_key": "prompt",
                "response_key": "response",
                "reward_key": "reward"
            },
            "actor_rollout_ref": {
                "model": {
                    "path": request.model_name,
                    "trust_remote_code": True,
                    "lora_rank": 32,
                    "lora_alpha": 16,
                    "target_modules": "all-linear",
                    "use_shm": True
                },
                "rollout": {
                    "log_prob_micro_batch_size": request.batch_size,
                    "micro_batch_size": request.batch_size,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_new_tokens": request.max_response_length,
                    "load_format": "safetensors"
                }
            },
            "critic": {
                "model": {
                    "path": request.model_name,
                    "trust_remote_code": True,
                    "lora_rank": 32,
                    "lora_alpha": 16,
                    "target_modules": "all-linear"
                },
                "optim": {
                    "lr": request.learning_rate * 10,
                    "beta1": 0.9,
                    "beta2": 0.95,
                    "eps": 1e-5,
                    "weight_decay": 0.01
                }
            },
            "algorithm": {
                "kl_ctrl": {
                    "kl_coeff": 0.001,
                    "adaptive_kl": True,
                    "target_kl": 6.0
                },
                "rollout_batch_size": request.batch_size * 4,
                "n_ppo_epochs": 4,
                "ppo_mini_batch_size": request.batch_size,
                "cliprange": 0.2,
                "cliprange_value": 0.2,
                "gamma": 0.99,
                "lam": 0.95,
                "entropy_bonus_coeff": 0.01,
                "value_loss_coeff": 0.5,
                "max_grad_norm": 1.0
            },
            "trainer": {
                "project_name": "publish_ai",
                "experiment_name": f"ebook_training_{request.training_id}",
                "total_epochs": request.num_epochs,
                "save_freq": 1,
                "test_freq": 1,
                "checkpoint_dir": str(self.checkpoints_dir),
                "enable_wandb": False
            }
        }
        
        return OmegaConf.create(config_dict)
    
    async def _run_verl_training(self, request: TrainingRequest, config: DictConfig, status: TrainingStatus):
        """Run actual VERL training"""
        # Save config to file
        config_path = self.checkpoints_dir / f"config_{request.training_id}.yaml"
        OmegaConf.save(config, config_path)
        
        # Initialize trainer
        trainer = RayPPOTrainer(config)
        
        # Training loop
        for epoch in range(request.num_epochs):
            epoch_start = datetime.utcnow()
            
            # Run training epoch
            metrics = trainer.train_epoch()
            
            # Update status
            status.current_epoch = epoch + 1
            status.progress = (epoch + 1) / request.num_epochs
            status.metrics = metrics
            
            # Calculate epoch duration
            epoch_duration = (datetime.utcnow() - epoch_start).total_seconds()
            
            # Publish progress
            await self._publish_event("training_progress", {
                "training_id": request.training_id,
                "epoch": epoch + 1,
                "total_epochs": request.num_epochs,
                "progress": status.progress,
                "metrics": metrics,
                "epoch_duration": epoch_duration
            })
            
            logger.info(f"Epoch {epoch + 1}/{request.num_epochs} completed in {epoch_duration:.2f}s")
        
        # Save final model
        model_path = self.models_dir / f"trained_model_{request.training_id}"
        trainer.save_model(str(model_path))
    
    async def _run_mock_training(self, request: TrainingRequest, status: TrainingStatus):
        """Run mock training for development/testing"""
        logger.info(f"Running mock training for {request.training_id}")
        
        for epoch in range(request.num_epochs):
            # Simulate training time
            await asyncio.sleep(2)
            
            # Mock metrics
            metrics = {
                "loss": max(0.1, 1.0 - (epoch * 0.2)),
                "reward": min(1.0, 0.3 + (epoch * 0.2)),
                "kl_divergence": 0.01 + (epoch * 0.005)
            }
            
            # Update status
            status.current_epoch = epoch + 1
            status.progress = (epoch + 1) / request.num_epochs
            status.metrics = metrics
            
            # Publish progress
            await self._publish_event("training_progress", {
                "training_id": request.training_id,
                "epoch": epoch + 1,
                "total_epochs": request.num_epochs,
                "progress": status.progress,
                "metrics": metrics,
                "mock": True
            })
        
        # Create mock model directory
        model_path = self.models_dir / f"trained_model_{request.training_id}"
        model_path.mkdir(exist_ok=True)
        (model_path / "pytorch_model.bin").touch()
    
    async def _gpu_monitor(self):
        """Monitor GPU usage and publish metrics"""
        while self.is_running:
            try:
                if torch.cuda.is_available():
                    for i in range(torch.cuda.device_count()):
                        memory_used = torch.cuda.memory_allocated(i)
                        GPU_MEMORY_USAGE.labels(device=f"cuda:{i}").set(memory_used)
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"GPU monitoring error: {e}")
                await asyncio.sleep(60)  # Back off on error
    
    async def _publish_event(self, event_type: str, data: Dict):
        """Publish event to Redis channel"""
        if not self.redis_client:
            return
        
        try:
            message = {
                "event_type": event_type,
                "timestamp": datetime.utcnow().isoformat(),
                "service": "verl-training",
                "data": data
            }
            
            await self.redis_client.publish("verl_events", json.dumps(message))
            logger.debug(f"Published event: {event_type}")
            
        except Exception as e:
            logger.error(f"Failed to publish event: {e}")
    
    async def get_training_status(self, training_id: str) -> Optional[TrainingStatus]:
        """Get training job status"""
        return self.training_jobs.get(training_id)
    
    async def cancel_training(self, training_id: str) -> bool:
        """Cancel a training job"""
        status = self.training_jobs.get(training_id)
        if not status:
            return False
        
        if status.status in ["queued", "training"]:
            status.status = "cancelled"
            status.completed_at = datetime.utcnow()
            
            await self._publish_event("training_cancelled", {
                "training_id": training_id
            })
            
            return True
        
        return False
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down VERL Training Service...")
        self.is_running = False
        
        # Cancel all running jobs
        for training_id, status in self.training_jobs.items():
            if status.status in ["queued", "training"]:
                await self.cancel_training(training_id)
        
        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()
        
        # Shutdown Ray
        if self.ray_initialized and ray.is_initialized():
            ray.shutdown()
        
        logger.info("VERL Training Service shut down complete")

# Global service instance
training_service = VERLTrainingService()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager"""
    # Startup
    await training_service.initialize()
    
    # Start Prometheus metrics server
    start_http_server(9090)
    logger.info("Prometheus metrics server started on port 9090")
    
    yield
    
    # Shutdown
    await training_service.shutdown()

# FastAPI application
app = FastAPI(
    title="VERL Training Service",
    description="Dedicated microservice for VERL reinforcement learning training",
    version="1.0.0",
    lifespan=lifespan
)

@app.post("/train", response_model=TrainingStatus)
async def start_training(request: TrainingRequest):
    """Submit a new training job"""
    try:
        return await training_service.submit_training_job(request)
    except Exception as e:
        logger.error(f"Training submission failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{training_id}", response_model=TrainingStatus)
async def get_training_status(training_id: str):
    """Get training job status"""
    status = await training_service.get_training_status(training_id)
    if not status:
        raise HTTPException(status_code=404, detail="Training job not found")
    return status

@app.delete("/training/{training_id}")
async def cancel_training(training_id: str):
    """Cancel a training job"""
    success = await training_service.cancel_training(training_id)
    if not success:
        raise HTTPException(status_code=404, detail="Training job not found or cannot be cancelled")
    return {"message": f"Training {training_id} cancelled successfully"}

@app.get("/health")
async def health_check():
    """Service health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "verl_available": VERL_AVAILABLE,
        "ray_initialized": training_service.ray_initialized,
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": len(training_service.available_gpus),
        "active_jobs": len([j for j in training_service.training_jobs.values() if j.status == "training"]),
        "queued_jobs": training_service.job_queue.qsize()
    }

@app.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    return {
        "training_jobs": {
            "total": len(training_service.training_jobs),
            "active": len([j for j in training_service.training_jobs.values() if j.status == "training"]),
            "queued": training_service.job_queue.qsize(),
            "completed": len([j for j in training_service.training_jobs.values() if j.status == "completed"]),
            "failed": len([j for j in training_service.training_jobs.values() if j.status == "failed"])
        },
        "gpu_info": {
            "available": training_service.available_gpus,
            "cuda_available": torch.cuda.is_available(),
            "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
    }

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}, initiating shutdown...")
    asyncio.create_task(training_service.shutdown())
    sys.exit(0)

if __name__ == "__main__":
    import uvicorn
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the service
    logger.info("Starting VERL Training Service...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        log_level="info",
        reload=False
    )