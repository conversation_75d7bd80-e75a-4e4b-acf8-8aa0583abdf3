#!/bin/bash

# VERL Training Service Startup Script

set -e

echo "Starting VERL Training Service..."

# Check for GPU availability
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
else
    echo "Warning: No NVIDIA GPU detected"
fi

# Check Ray cluster connectivity
if [ "${RAY_HEAD_NODE}" ]; then
    echo "Connecting to Ray cluster at ${RAY_HEAD_NODE}"
    export RAY_ADDRESS="${RAY_HEAD_NODE}"
else
    echo "Starting local Ray cluster"
fi

# Check Redis connectivity
echo "Testing Redis connection..."
python -c "
import redis
import os
client = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379'))
client.ping()
print('Redis connection successful')
"

# Check database connectivity
echo "Testing database connection..."
python -c "
import asyncio
import os
from supabase import create_client
url = os.getenv('SUPABASE_URL')
key = os.getenv('SUPABASE_SERVICE_KEY')
if url and key:
    client = create_client(url, key)
    print('Database connection successful')
else:
    print('Warning: Database credentials not found')
"

# Set memory limits for training
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export RAY_OBJECT_STORE_ALLOW_SLOW_STORAGE=1

# Start the service
echo "Starting VERL Training Service on port 8001..."
exec python -m uvicorn main:app --host 0.0.0.0 --port 8001 --workers 1