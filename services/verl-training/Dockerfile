# VERL Training Service Dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHON_VERSION=3.11
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python3-pip \
    git \
    wget \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -s /usr/bin/python${PYTHON_VERSION} /usr/bin/python

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install VERL dependencies
RUN pip install --no-cache-dir \
    torch==2.1.0 \
    transformers==4.35.0 \
    datasets==2.14.6 \
    accelerate==0.24.1 \
    vllm==0.2.2 \
    ray[default]==2.8.0 \
    wandb==0.16.0

# Install VERL framework
RUN git clone https://github.com/volcengine/verl.git /tmp/verl && \
    cd /tmp/verl && \
    pip install -e . && \
    rm -rf /tmp/verl

# Copy service code
COPY . .

# Create directories for models and checkpoints
RUN mkdir -p /app/models /app/checkpoints /app/training_data /app/logs

# Set permissions
RUN chmod +x /app/start.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Expose port
EXPOSE 8001

# Start command
CMD ["./start.sh"]