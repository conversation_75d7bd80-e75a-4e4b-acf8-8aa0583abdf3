# Publishing Service

Automated Amazon KDP publishing microservice that handles book publication workflows using AI-powered automation and Selenium WebDriver.

## Overview

The Publishing Service is a Tier 1 microservice that extracts the KDPUploader agent from the monolithic Publish AI platform. It provides comprehensive Amazon KDP (Kindle Direct Publishing) automation including book uploads, metadata management, pricing configuration, and publication analytics.

## Features

### Core Capabilities
- **Automated KDP Publishing**: Complete automation of Amazon KDP book publishing workflow
- **AI-Powered Analytics**: Intelligent publication performance predictions and optimization
- **Multi-Format Support**: Handles DOCX, PDF, EPUB manuscript formats
- **Selenium Automation**: Robust browser automation for KDP interface interaction
- **Publication Analytics**: Comprehensive performance tracking and insights
- **Bulk Operations**: Batch publishing and management capabilities

### Technical Features
- **PydanticAI Integration**: AI-powered publishing decisions and analytics
- **Multi-Model Support**: OpenAI GPT-4 and Anthropic Claude with automatic fallback
- **Chrome Automation**: Headless Chrome for production environments
- **Event-Driven Architecture**: Redis Streams for asynchronous processing
- **Kubernetes Ready**: Production-ready containerization with security policies

## API Endpoints

### Publishing Operations
- `POST /publish` - Publish a book to Amazon KDP
- `GET /publication/{publication_id}` - Get publication status
- `DELETE /publication/{publication_id}` - Cancel publication
- `PUT /publication/{publication_id}` - Update publication

### Bulk Operations  
- `POST /bulk/publish` - Bulk publish multiple books
- `GET /bulk/status/{batch_id}` - Get bulk operation status

### Analytics
- `GET /analytics/publication/{publication_id}` - Get publication analytics
- `GET /analytics/summary` - Get overall publishing summary
- `POST /analytics/record` - Record publication metrics

### Management
- `GET /health` - Service health check
- `GET /ready` - Service readiness check
- `GET /status` - Service status and metrics

## Architecture

### Core Components

#### KDPPublisher (PydanticAI Agent)
AI-powered publishing agent that orchestrates the entire publication workflow:
- Publication data validation and optimization
- AI-driven analytics and performance predictions
- Integration with KDP Selenium automation
- Error handling and retry logic

#### KDPSeleniumService
Robust Selenium WebDriver automation for Amazon KDP:
- Login and authentication handling
- Book creation and metadata entry
- File uploads (manuscript and cover)
- Pricing and rights configuration
- Publication submission and monitoring

#### PublicationAnalytics
Analytics engine for publication performance tracking:
- Real-time performance metrics
- Success rate calculations
- Revenue and sales tracking
- Optimization recommendations

### Data Models

```python
@dataclass
class BookMetadata:
    title: str
    subtitle: Optional[str]
    author: str
    description: str
    keywords: List[str]
    categories: List[str]
    language: str = "English"
    series_name: Optional[str] = None
    series_number: Optional[int] = None

@dataclass
class PricingConfig:
    price_usd: float
    territories: List[str]
    kdp_select: bool = True
    royalty_option: str = "70"
```

## Configuration

### Environment Variables

#### Service Configuration
- `SERVICE_HOST` - Service bind host (default: 0.0.0.0)
- `SERVICE_PORT` - Service port (default: 8087)
- `LOG_LEVEL` - Logging level (default: info)

#### AI Model Configuration
- `OPENAI_API_KEY` - OpenAI API key for GPT models
- `ANTHROPIC_API_KEY` - Anthropic API key for Claude models
- `PREFERRED_MODEL` - Preferred AI model (openai/anthropic)

#### KDP Configuration
- `KDP_EMAIL` - Amazon KDP account email
- `KDP_PASSWORD` - Amazon KDP account password
- `KDP_BACKUP_CODES` - Two-factor authentication backup codes
- `KDP_HEADLESS` - Run Chrome in headless mode (default: true)
- `KDP_TIMEOUT` - Selenium operation timeout in seconds (default: 60)

#### Infrastructure
- `EVENT_BUS_URL` - Event Bus service URL
- `SERVICE_DISCOVERY_URL` - Service Discovery URL
- `REDIS_URL` - Redis URL for caching and queuing

### Kubernetes Secrets

Create the following secrets in the `publish-ai` namespace:

```yaml
# AI API Keys
apiVersion: v1
kind: Secret
metadata:
  name: ai-api-keys
type: Opaque
data:
  openai-api-key: <base64-encoded-key>
  anthropic-api-key: <base64-encoded-key>

# KDP Credentials
apiVersion: v1
kind: Secret
metadata:
  name: kdp-credentials
type: Opaque
data:
  email: <base64-encoded-email>
  password: <base64-encoded-password>
  backup-codes: <base64-encoded-codes>
```

## Deployment

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export OPENAI_API_KEY="your-openai-key"
export KDP_EMAIL="your-kdp-email"
export KDP_PASSWORD="your-kdp-password"

# Run the service
python src/main.py
```

### Docker Deployment

```bash
# Build image
docker build -t publishing-service:latest .

# Run container
docker run -p 8087:8087 \
  -e OPENAI_API_KEY="your-key" \
  -e KDP_EMAIL="your-email" \
  -e KDP_PASSWORD="your-password" \
  publishing-service:latest
```

### Kubernetes Deployment

```bash
# Apply Kubernetes configuration
kubectl apply -f k8s/deployment.yaml

# Check deployment status
kubectl get pods -n publish-ai -l app=publishing-service

# View logs
kubectl logs -n publish-ai -l app=publishing-service
```

## Special Requirements

### Chrome/Selenium Dependencies
The service requires Google Chrome and ChromeDriver for KDP automation:
- Google Chrome stable version
- ChromeDriver matching Chrome version
- Xvfb for headless display (in containers)

### Kubernetes Security
Special security configurations for Chrome automation:
- `SYS_ADMIN` capability for Chrome sandboxing
- Shared memory volume (`/dev/shm`) for Chrome
- Increased memory limits (2Gi request, 8Gi limit)
- Non-root user execution for security

### Resource Requirements
- **CPU**: 1000m request, 4000m limit
- **Memory**: 2Gi request, 8Gi limit
- **Storage**: 2Gi temporary storage
- **Shared Memory**: 512Mi for Chrome

## Testing

### Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run simple tests only
python -m pytest tests/test_simple.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

### Test Coverage
- KDPPublisher agent functionality
- Selenium service automation
- Publication analytics
- Data validation and error handling
- API endpoint responses

## Monitoring

### Health Checks
- `/health` - Basic service health
- `/ready` - Service readiness with dependencies
- Kubernetes liveness and readiness probes

### Metrics
- Publication success rates
- Processing times
- Error rates and types
- Chrome/Selenium performance
- Resource utilization

### Logging
- Structured JSON logging
- Publication workflow tracking
- Selenium automation logs
- Error and exception tracking

## Security

### Authentication
- API key-based authentication
- Kubernetes secrets for credentials
- Service-to-service mTLS

### Chrome Security
- Runs as non-root user
- Minimal required capabilities
- Read-only root filesystem (where possible)
- Sandbox restrictions for Chrome processes

### Network Security
- Network policies for pod-to-pod communication
- Egress restrictions for external APIs only
- TLS for all external communications

## Error Handling

### Publication Errors
- KDP login failures and retries
- File upload errors and recovery
- Metadata validation failures
- Publication submission errors

### Selenium Errors
- WebDriver crashes and restarts
- Element not found handling
- Timeout management
- Page load failures

### Recovery Strategies
- Automatic retry with exponential backoff
- Circuit breaker for external services
- Graceful degradation for AI services
- Manual intervention alerts

## Performance

### Optimization Features
- Connection pooling for external APIs
- Intelligent caching of publication data
- Parallel processing for batch operations
- Resource cleanup and memory management

### Scaling
- Horizontal Pod Autoscaler (HPA) configuration
- CPU and memory-based scaling
- Pod Disruption Budget for availability
- Load balancing across instances

## Migration Notes

This service was extracted from the monolithic system with the following changes:
- Converted SQLAlchemy to Supabase integration
- Implemented event-driven architecture
- Added comprehensive error handling
- Enhanced security and monitoring
- Containerized for Kubernetes deployment

## Contributing

1. Follow existing code patterns and structure
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure security best practices
5. Test Chrome/Selenium functionality thoroughly

## License

Internal Publish AI platform component - All rights reserved.