apiVersion: apps/v1
kind: Deployment
metadata:
  name: publishing-service
  namespace: publish-ai
  labels:
    app: publishing-service
    tier: tier1
    component: publishing
spec:
  replicas: 2
  selector:
    matchLabels:
      app: publishing-service
  template:
    metadata:
      labels:
        app: publishing-service
        tier: tier1
        component: publishing
    spec:
      containers:
      - name: publishing-service
        image: publishing-service:latest
        ports:
        - containerPort: 8087
          name: http
        env:
        - name: SERVICE_PORT
          value: "8087"
        - name: SERVICE_HOST
          value: "0.0.0.0"
        - name: EVENT_BUS_URL
          value: "http://event-bus:8080"
        - name: SERVICE_DISCOVERY_URL
          value: "http://service-discovery:8070"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: anthropic-api-key
        - name: PREFERRED_MODEL
          value: "openai"
        - name: KDP_EMAIL
          valueFrom:
            secretKeyRef:
              name: kdp-credentials
              key: email
        - name: KDP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kdp-credentials
              key: password
        - name: KDP_BACKUP_CODES
          valueFrom:
            secretKeyRef:
              name: kdp-credentials
              key: backup-codes
              optional: true
        - name: KDP_HEADLESS
          value: "true"
        - name: KDP_TIMEOUT
          value: "60"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: LOG_LEVEL
          value: "info"
        volumeMounts:
        - name: tls-certs
          mountPath: /etc/ssl/certs/service
          readOnly: true
        - name: ca-cert
          mountPath: /etc/ssl/certs/ca
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
        - name: chrome-storage
          mountPath: /dev/shm
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8087
          initialDelaySeconds: 45
          periodSeconds: 15
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8087
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: false  # Chrome needs write access
          capabilities:
            drop:
            - ALL
            add:
            - SYS_ADMIN  # Required for Chrome sandboxing
      volumes:
      - name: tls-certs
        secret:
          secretName: publishing-service-tls
      - name: ca-cert
        secret:
          secretName: ca-certificate
      - name: temp-storage
        emptyDir:
          sizeLimit: 2Gi
      - name: chrome-storage
        emptyDir:
          medium: Memory
          sizeLimit: 512Mi
      securityContext:
        fsGroup: 1000
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: publishing-service
  namespace: publish-ai
  labels:
    app: publishing-service
    tier: tier1
spec:
  selector:
    app: publishing-service
  ports:
  - name: http
    port: 8087
    targetPort: 8087
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: publishing-service-tls
  namespace: publish-ai
type: kubernetes.io/tls
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCi4uLiAoYmFzZTY0IGVuY29kZWQgY2VydGlmaWNhdGUpCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCi4uLiAoYmFzZTY0IGVuY29kZWQgcHJpdmF0ZSBrZXkpCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0K
---
apiVersion: v1
kind: Secret
metadata:
  name: kdp-credentials
  namespace: publish-ai
type: Opaque
data:
  email: ************************  # base64 encoded email
  password: cGFzc3dvcmQxMjM=  # base64 encoded password
  backup-codes: Y29kZTEsY29kZTI=  # base64 encoded backup codes (optional)
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: publishing-service-network-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: publishing-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: api-gateway
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8087
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8080
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8070
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS for external APIs (KDP, AI providers)
    - protocol: TCP
      port: 80   # HTTP for external APIs
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: publishing-service-pdb
  namespace: publish-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: publishing-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: publishing-service-hpa
  namespace: publish-ai
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: publishing-service
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60