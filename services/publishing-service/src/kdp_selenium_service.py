# app/services/kdp_selenium_service.py
"""
Amazon KDP Selenium Automation Service
Production-ready Selenium automation for Amazon KDP book publishing
"""

import asyncio
import logging
import time
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    StaleElementReferenceException
)

logger = logging.getLogger(__name__)

@dataclass
class KDPCredentials:
    email: str
    password: str
    two_factor_backup_codes: Optional[List[str]] = None

@dataclass
class BookMetadata:
    title: str
    subtitle: Optional[str]
    author: str
    description: str
    keywords: List[str]
    categories: List[str]
    language: str = "English"
    series_name: Optional[str] = None
    series_number: Optional[int] = None
    edition_number: Optional[int] = None
    isbn: Optional[str] = None

@dataclass
class BookFiles:
    manuscript_path: str
    cover_path: str
    manuscript_format: str = "docx"  # docx, pdf, epub, etc.

@dataclass
class PricingConfig:
    price_usd: float
    territories: List[str] = field(default_factory=list)
    kdp_select: bool = True
    royalty_option: str = "70"  # "35" or "70"
    preorder_date: Optional[datetime] = None

class KDPSeleniumService:
    """Selenium-based KDP automation service"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Chrome WebDriver with optimal settings"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Performance and stability options
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        chrome_options.add_argument("--disable-javascript")  # Only if not needed
        chrome_options.add_argument("--window-size=1920,1080")
        
        # User agent to appear more natural
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Download settings for file uploads
        prefs = {
            "profile.default_content_settings.popups": 0,
            "download.default_directory": "/tmp",
            "directory_upgrade": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.implicitly_wait(10)
            return driver
        except WebDriverException as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            raise

    async def publish_book(
        self,
        credentials: KDPCredentials,
        metadata: BookMetadata,
        files: BookFiles,
        pricing: PricingConfig,
        auto_publish: bool = False
    ) -> Dict[str, Any]:
        """
        Main method to publish a book to KDP
        
        Returns:
            Dict with publication results including KDP book ID, URLs, etc.
        """
        
        start_time = datetime.now()
        
        try:
            # Setup driver
            self.driver = self._setup_driver()
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info(f"Starting KDP publication for: {metadata.title}")
            
            # Step 1: Login to KDP
            login_result = await self._login(credentials)
            if not login_result["success"]:
                return self._create_error_result("Login failed", str(login_result.get("error")))
            
            # Step 2: Create new book
            creation_result = await self._create_new_book()
            if not creation_result["success"]:
                return self._create_error_result("Book creation failed", str(creation_result.get("error")))
            
            # Step 3: Upload and set manuscript
            manuscript_result = await self._upload_manuscript(files)
            if not manuscript_result["success"]:
                return self._create_error_result("Manuscript upload failed", str(manuscript_result.get("error")))
            
            # Step 4: Set book metadata
            metadata_result = await self._set_book_metadata(metadata)
            if not metadata_result["success"]:
                return self._create_error_result("Metadata setup failed", str(metadata_result.get("error")))
            
            # Step 5: Upload cover
            cover_result = await self._upload_cover(files)
            if not cover_result["success"]:
                return self._create_error_result("Cover upload failed", str(cover_result.get("error")))
            
            # Step 6: Set pricing and rights
            pricing_result = await self._set_pricing_and_rights(pricing)
            if not pricing_result["success"]:
                return self._create_error_result("Pricing setup failed", str(pricing_result.get("error")))
            
            # Step 7: Review and publish/save
            if auto_publish:
                publish_result = await self._publish_book()
            else:
                publish_result = await self._save_as_draft()
            
            if not publish_result["success"]:
                return self._create_error_result("Publication failed", str(publish_result.get("error")))
            
            # Extract book ID and URLs
            book_id = await self._extract_book_id()
            book_url = await self._extract_book_url()
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "kdp_book_id": book_id,
                "publication_url": book_url,
                "status": "published" if auto_publish else "draft",
                "execution_time": execution_time,
                "metadata": {
                    "title": metadata.title,
                    "author": metadata.author,
                    "published_at": datetime.now().isoformat() if auto_publish else None,
                    "estimated_live_date": "24-72 hours" if auto_publish else None
                },
                "message": f"Book {'published' if auto_publish else 'saved as draft'} successfully"
            }
            
        except Exception as e:
            logger.error(f"KDP publication failed: {str(e)}")
            return self._create_error_result("Unexpected error", str(e))
        
        finally:
            if self.driver:
                self.driver.quit()

    async def _login(self, credentials: KDPCredentials) -> Dict[str, Any]:
        """Login to Amazon KDP"""
        try:
            logger.info("Logging into Amazon KDP...")
            
            # Navigate to KDP login
            self.driver.get("https://kdp.amazon.com/en_US/signin")
            
            # Wait for login form
            email_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "ap_email"))
            )
            
            # Enter email
            email_field.clear()
            email_field.send_keys(credentials.email)
            
            # Continue button
            continue_button = self.driver.find_element(By.ID, "continue")
            continue_button.click()
            
            # Wait for password field
            password_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "ap_password"))
            )
            
            # Enter password
            password_field.clear()
            password_field.send_keys(credentials.password)
            
            # Sign in button
            signin_button = self.driver.find_element(By.ID, "signInSubmit")
            signin_button.click()
            
            # Handle potential 2FA
            try:
                # Check if 2FA is required
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.ID, "auth-mfa-otpcode")),
                        EC.presence_of_element_located((By.CLASS_NAME, "kdp-dashboard")),
                        EC.url_contains("kdp.amazon.com/en_US")
                    )
                )
                
                # If 2FA element is present, handle it
                if self.driver.find_elements(By.ID, "auth-mfa-otpcode"):
                    logger.warning("2FA required - using backup codes if available")
                    if credentials.two_factor_backup_codes:
                        # Use first available backup code
                        code_field = self.driver.find_element(By.ID, "auth-mfa-otpcode")
                        code_field.send_keys(credentials.two_factor_backup_codes[0])
                        submit_button = self.driver.find_element(By.ID, "auth-signin-button")
                        submit_button.click()
                    else:
                        return {"success": False, "error": "2FA required but no backup codes provided"}
                
            except TimeoutException:
                pass
            
            # Verify login success
            try:
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.CLASS_NAME, "kdp-dashboard")),
                        EC.url_contains("kdp.amazon.com/en_US"),
                        EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), 'Bookshelf')]"))
                    )
                )
                logger.info("Successfully logged into KDP")
                return {"success": True}
                
            except TimeoutException:
                return {"success": False, "error": "Login verification failed"}
                
        except TimeoutException:
            return {"success": False, "error": "Login form not found or timeout"}
        except Exception as e:
            return {"success": False, "error": f"Login error: {str(e)}"}

    async def _create_new_book(self) -> Dict[str, Any]:
        """Create a new book in KDP"""
        try:
            logger.info("Creating new book...")
            
            # Look for "Create new title" or similar button
            create_buttons = [
                "//button[contains(text(), 'Create new title')]",
                "//a[contains(text(), 'Create new title')]", 
                "//button[contains(@class, 'create')]",
                "//a[contains(@href, 'create')]"
            ]
            
            create_button = None
            for xpath in create_buttons:
                try:
                    create_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not create_button:
                return {"success": False, "error": "Create book button not found"}
            
            create_button.click()
            
            # Select Kindle eBook option
            try:
                kindle_option = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Kindle eBook')]"))
                )
                kindle_option.click()
            except TimeoutException:
                # Sometimes it goes directly to book setup
                pass
            
            # Wait for book creation form
            self.wait.until(
                EC.presence_of_element_located((By.NAME, "title"))
            )
            
            logger.info("Book creation form loaded")
            return {"success": True}
            
        except TimeoutException:
            return {"success": False, "error": "Book creation form timeout"}
        except Exception as e:
            return {"success": False, "error": f"Book creation error: {str(e)}"}

    async def _upload_manuscript(self, files: BookFiles) -> Dict[str, Any]:
        """Upload manuscript file"""
        try:
            logger.info(f"Uploading manuscript: {files.manuscript_path}")
            
            # Verify file exists
            if not os.path.exists(files.manuscript_path):
                return {"success": False, "error": f"Manuscript file not found: {files.manuscript_path}"}
            
            # Find manuscript upload input
            upload_inputs = [
                "//input[@type='file' and contains(@accept, '.doc')]",
                "//input[@type='file']",
                "//input[contains(@name, 'manuscript')]"
            ]
            
            upload_input = None
            for xpath in upload_inputs:
                try:
                    upload_input = self.driver.find_element(By.XPATH, xpath)
                    break
                except NoSuchElementException:
                    continue
            
            if not upload_input:
                return {"success": False, "error": "Manuscript upload input not found"}
            
            # Upload file
            upload_input.send_keys(os.path.abspath(files.manuscript_path))
            
            # Wait for upload to complete
            try:
                # Look for upload progress or success indicators
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Upload complete')]")),
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'upload-success')]")),
                        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Next')]"))
                    )
                )
                
                # Wait for processing to complete
                self.wait.until(
                    EC.invisibility_of_element_located((By.XPATH, "//div[contains(@class, 'upload-progress')]"))
                )

            except TimeoutException:
                return {"success": False, "error": "Manuscript upload timeout or processing failed"}
            
            logger.info("Manuscript uploaded successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Manuscript upload error: {str(e)}"}

    async def _set_book_metadata(self, metadata: BookMetadata) -> Dict[str, Any]:
        """Set book metadata (title, description, keywords, etc.)"""
        try:
            logger.info("Setting book metadata...")
            
            # Title
            title_field = self.driver.find_element(By.NAME, "title")
            title_field.clear()
            title_field.send_keys(metadata.title)
            
            # Subtitle (if present)
            if metadata.subtitle:
                try:
                    subtitle_field = self.driver.find_element(By.NAME, "subtitle")
                    subtitle_field.clear()
                    subtitle_field.send_keys(metadata.subtitle)
                except NoSuchElementException:
                    pass
            
            # Author
            author_field = self.driver.find_element(By.NAME, "author")
            author_field.clear()
            author_field.send_keys(metadata.author)
            
            # Description
            description_field = self.driver.find_element(By.NAME, "description")
            description_field.clear()
            description_field.send_keys(metadata.description)
            
            # Keywords
            if metadata.keywords:
                keywords_text = ", ".join(metadata.keywords[:7])  # KDP allows max 7 keywords
                try:
                    keywords_field = self.driver.find_element(By.NAME, "keywords")
                    keywords_field.clear()
                    keywords_field.send_keys(keywords_text)
                except NoSuchElementException:
                    # Sometimes keywords are individual fields
                    for i, keyword in enumerate(metadata.keywords[:7]):
                        try:
                            keyword_field = self.driver.find_element(By.NAME, f"keyword{i+1}")
                            keyword_field.clear()
                            keyword_field.send_keys(keyword)
                        except NoSuchElementException:
                            break
            
            # Categories
            if metadata.categories:
                try:
                    # This varies by KDP interface - may need adjustment
                    category_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Choose categories')]")
                    category_button.click()
                    
                    # Select first category (simplified - real implementation would be more sophisticated)
                    await asyncio.sleep(2)
                    first_category = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{metadata.categories[0]}')]")
                    first_category.click()
                    
                    # Save categories
                    save_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Save')]")
                    save_button.click()
                    
                except NoSuchElementException:
                    pass
            
            # Language
            try:
                language_select = Select(self.driver.find_element(By.NAME, "language"))
                language_select.select_by_visible_text(metadata.language)
            except NoSuchElementException:
                pass
            
            logger.info("Book metadata set successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Metadata setup error: {str(e)}"}

    async def _upload_cover(self, files: BookFiles) -> Dict[str, Any]:
        """Upload book cover"""
        try:
            logger.info(f"Uploading cover: {files.cover_path}")
            
            # Verify file exists
            if not os.path.exists(files.cover_path):
                return {"success": False, "error": f"Cover file not found: {files.cover_path}"}
            
            # Find cover upload input
            cover_upload = self.driver.find_element(By.XPATH, "//input[@type='file' and contains(@accept, 'image')]")
            cover_upload.send_keys(os.path.abspath(files.cover_path))
            
            # Wait for upload to complete
            try:
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Upload complete')]")),
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'cover-preview')]"))
                    )
                )
                
                # Wait for processing to complete
                self.wait.until(
                    EC.invisibility_of_element_located((By.XPATH, "//div[contains(@class, 'cover-upload-progress')]"))
                )

            except TimeoutException:
                return {"success": False, "error": "Cover upload timeout or processing failed"}
            
            logger.info("Cover uploaded successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Cover upload error: {str(e)}"}

    async def _set_pricing_and_rights(self, pricing: PricingConfig) -> Dict[str, Any]:
        """Set pricing and territorial rights"""
        try:
            logger.info("Setting pricing and rights...")
            
            # Navigate to pricing section (might require clicking "Next" or tab)
            try:
                next_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Next')]")
                next_button.click()
                await asyncio.sleep(2)
            except NoSuchElementException:
                pass
            
            # KDP Select
            if pricing.kdp_select:
                try:
                    kdp_select_checkbox = self.driver.find_element(By.XPATH, "//input[@type='checkbox' and contains(@name, 'kdp_select')]")
                    if not kdp_select_checkbox.is_selected():
                        kdp_select_checkbox.click()
                except NoSuchElementException:
                    pass
            
            # Royalty option (35% or 70%)
            try:
                royalty_radio = self.driver.find_element(By.XPATH, f"//input[@type='radio' and @value='{pricing.royalty_option}']")
                royalty_radio.click()
            except NoSuchElementException:
                pass
            
            # Pricing
            try:
                price_field = self.driver.find_element(By.NAME, "list_price_usd")
                price_field.clear()
                price_field.send_keys(str(pricing.price_usd))
            except NoSuchElementException:
                # Try alternative price field names
                try:
                    price_field = self.driver.find_element(By.XPATH, "//input[contains(@name, 'price')]")
                    price_field.clear()
                    price_field.send_keys(str(pricing.price_usd))
                except NoSuchElementException:
                    return {"success": False, "error": "Price field not found"}
            
            logger.info("Pricing and rights set successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Pricing setup error: {str(e)}"}

    async def _publish_book(self) -> Dict[str, Any]:
        """Publish the book"""
        try:
            logger.info("Publishing book...")
            
            # Look for publish button
            publish_buttons = [
                "//button[contains(text(), 'Publish your Kindle eBook')]",
                "//button[contains(text(), 'Publish')]",
                "//input[@type='submit' and contains(@value, 'Publish')]"
            ]
            
            publish_button = None
            for xpath in publish_buttons:
                try:
                    publish_button = self.driver.find_element(By.XPATH, xpath)
                    break
                except NoSuchElementException:
                    continue
            
            if not publish_button:
                return {"success": False, "error": "Publish button not found"}
            
            publish_button.click()
            
            # Wait for confirmation
            try:
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'published')]")),
                        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Success')]"))
                    )
                )
            except TimeoutException:
                return {"success": False, "error": "Publish confirmation timeout"}
            
            logger.info("Book published successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Publishing error: {str(e)}"}

    async def _save_as_draft(self) -> Dict[str, Any]:
        """Save book as draft"""
        try:
            logger.info("Saving book as draft...")
            
            # Look for save/draft button
            save_buttons = [
                "//button[contains(text(), 'Save as draft')]",
                "//button[contains(text(), 'Save')]",
                "//input[@type='submit' and contains(@value, 'Save')]"
            ]
            
            save_button = None
            for xpath in save_buttons:
                try:
                    save_button = self.driver.find_element(By.XPATH, xpath)
                    break
                except NoSuchElementException:
                    continue
            
            if not save_button:
                return {"success": False, "error": "Save button not found"}
            
            save_button.click()
            
            # Wait for confirmation or redirect to bookshelf
            try:
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), 'Bookshelf')]")),
                        EC.url_contains("kdp.amazon.com/en_US/bookshelf")
                    )
                )
            except TimeoutException:
                return {"success": False, "error": "Save as draft confirmation timeout"}
            
            logger.info("Book saved as draft successfully")
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": f"Save error: {str(e)}"}

    async def _extract_book_id(self) -> Optional[str]:
        """Extract KDP book ID from the page"""
        try:
            # KDP book ID is often in the URL or data attributes
            current_url = self.driver.current_url
            
            # Extract from URL pattern
            import re
            id_match = re.search(r'/title/([A-Z0-9]+)', current_url)
            if id_match:
                return id_match.group(1)
            
            # Try to find in page elements
            try:
                id_element = self.driver.find_element(By.XPATH, "//span[contains(@data-book-id, '')]")
                return id_element.get_attribute("data-book-id")
            except NoSuchElementException:
                pass
            
            # Generate a placeholder ID if not found
            return f"KDP_{int(time.time())}"
            
        except Exception:
            return f"KDP_{int(time.time())}"

    async def _extract_book_url(self) -> Optional[str]:
        """Extract book URL if available"""
        try:
            # Look for Amazon store link
            store_link = self.driver.find_element(By.XPATH, "//a[contains(@href, 'amazon.com/dp/')]")
            return store_link.get_attribute("href")
        except NoSuchElementException:
            return None

    def _create_error_result(self, error_type: str, error_message: str) -> Dict[str, Any]:
        """Create standardized error result"""
        return {
            "success": False,
            "error": error_message,
            "error_type": error_type,
            "timestamp": datetime.now().isoformat(),
            "kdp_book_id": None,
            "publication_url": None
        }


# Utility functions for integration
def create_kdp_credentials(email: str, password: str, backup_codes: List[str] = None) -> KDPCredentials:
    """Create KDP credentials object"""
    return KDPCredentials(
        email=email,
        password=password,
        two_factor_backup_codes=backup_codes
    )

def create_book_metadata(
    title: str,
    author: str, 
    description: str,
    keywords: List[str],
    categories: List[str],
    **kwargs
) -> BookMetadata:
    """Create book metadata object"""
    return BookMetadata(
        title=title,
        subtitle=kwargs.get('subtitle'),
        author=author,
        description=description,
        keywords=keywords,
        categories=categories,
        language=kwargs.get('language', 'English'),
        series_name=kwargs.get('series_name'),
        series_number=kwargs.get('series_number'),
        edition_number=kwargs.get('edition_number'),
        isbn=kwargs.get('isbn')
    )

def create_book_files(manuscript_path: str, cover_path: str, manuscript_format: str) -> BookFiles:
    """Create book files object"""
    return BookFiles(
        manuscript_path=manuscript_path,
        cover_path=cover_path,
        manuscript_format=manuscript_format
    )

def create_pricing_config(price: float, **kwargs) -> PricingConfig:
    """Create pricing configuration object"""
    return PricingConfig(
        price_usd=price,
        **kwargs
    )