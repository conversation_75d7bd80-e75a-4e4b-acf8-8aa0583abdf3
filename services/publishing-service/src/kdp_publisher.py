"""
Publishing Service - KDP Publisher

PydanticAI agent and Selenium automation for Amazon KDP book publishing.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Callable

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent, RunContext

from kdp_selenium_service import (
    KDPSeleniumService,
    KDPCredentials,
    BookMetadata,
    BookFiles,
    PricingConfig
)
from event_client import EventClient
from monitoring import MetricsManager

logger = logging.getLogger(__name__)


# ============================================================================
# Result Models
# ============================================================================

class PublicationResult(BaseModel):
    """Result model for book publication"""
    success: bool = Field(description="Publication success status")
    kdp_book_id: Optional[str] = Field(default=None, description="KDP book identifier")
    publication_url: Optional[str] = Field(default=None, description="Publication URL")
    status: str = Field(description="Publication status (published, draft, failed)")
    message: str = Field(description="Publication message")
    execution_time: float = Field(description="Execution time in seconds")
    estimated_live_date: Optional[str] = Field(default=None, description="Estimated live date")
    performance_predictions: Optional[Dict[str, Any]] = Field(default=None, description="Performance predictions")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class PublicationAnalytics(BaseModel):
    """Analytics model for publication performance"""
    estimated_monthly_sales: int = Field(description="Estimated monthly sales")
    estimated_monthly_revenue: float = Field(description="Estimated monthly revenue")
    market_competitiveness: int = Field(description="Market competitiveness score (0-100)")
    optimization_suggestions: List[str] = Field(description="Optimization recommendations")
    success_probability: float = Field(description="Success probability (0-1)")


# ============================================================================
# KDP Publisher Agent
# ============================================================================

class KDPPublisher:
    """AI-powered KDP publishing agent with Selenium automation"""
    
    def __init__(self):
        self.agent: Optional[TypedAgent] = None
        self.selenium_service: Optional[KDPSeleniumService] = None
        self.is_ready = False
        self.event_client: Optional[EventClient] = None
        self.metrics = MetricsManager()
        
    async def initialize(self):
        """Initialize the KDP publisher agent"""
        try:
            # Import AI models here to avoid circular imports
            from pydantic_ai.models.openai import OpenAIModel
            from pydantic_ai.models.anthropic import AnthropicModel
            
            # Select model based on environment
            model = None
            preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
            
            if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
                model = OpenAIModel("gpt-4")
                logger.info("Initialized with OpenAI GPT-4 model")
            elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
                model = AnthropicModel("claude-3-sonnet-20240229")
                logger.info("Initialized with Anthropic Claude 3 Sonnet model")
            else:
                # Fallback to any available model
                if os.getenv("OPENAI_API_KEY"):
                    model = OpenAIModel("gpt-4")
                    logger.info("Fallback: Initialized with OpenAI GPT-4 model")
                elif os.getenv("ANTHROPIC_API_KEY"):
                    model = AnthropicModel("claude-3-sonnet-20240229")
                    logger.info("Fallback: Initialized with Anthropic Claude 3 Sonnet model")
            
            if not model:
                raise Exception("No AI model available. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY")
            
            # Create agent with comprehensive system prompt
            system_prompt = self._get_system_prompt()
            
            self.agent = TypedAgent(
                model=model,
                result_type=PublicationAnalytics,
                system_prompt=system_prompt
            )
            
            # Initialize Selenium service
            headless = os.getenv("KDP_HEADLESS", "true").lower() == "true"
            timeout = int(os.getenv("KDP_TIMEOUT", "60"))
            self.selenium_service = KDPSeleniumService(headless=headless, timeout=timeout)
            
            self.is_ready = True
            logger.info("KDP Publisher initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize KDP Publisher: {e}")
            self.is_ready = False
            raise
    
    def _get_system_prompt(self) -> str:
        """Get comprehensive system prompt for publication analytics"""
        return """
        You are an expert AI publishing consultant specializing in Amazon KDP optimization 
        and performance prediction. Your expertise includes:

        CORE CAPABILITIES:
        - Market analysis for book publishing success
        - Performance prediction based on metadata
        - Optimization recommendations for maximum sales
        - Competitive analysis and positioning
        - Pricing strategy optimization
        - Keyword and category analysis

        ANALYSIS PRINCIPLES:
        - Consider genre popularity and market saturation
        - Analyze keyword relevance and search volume
        - Evaluate pricing competitiveness
        - Assess cover design and title effectiveness
        - Factor in author platform and marketing potential
        - Account for seasonal trends and market timing

        PREDICTION METHODOLOGY:
        - Use historical performance data patterns
        - Consider category-specific success factors
        - Analyze metadata quality and optimization
        - Factor in market competition and saturation
        - Account for pricing and royalty strategies
        - Consider target audience alignment

        OUTPUT REQUIREMENTS:
        - Provide realistic sales and revenue estimates
        - Calculate market competitiveness scores
        - Generate actionable optimization suggestions
        - Assess success probability based on multiple factors
        - Include specific recommendations for improvement

        Always base predictions on realistic market conditions and provide 
        actionable insights that authors can implement to improve their 
        book's commercial success.
        """
    
    async def publish_book(
        self,
        book_data: Dict[str, Any],
        file_paths: Dict[str, str],
        pricing_config: Optional[Dict[str, Any]] = None,
        auto_publish: bool = False,
        user_id: Optional[str] = None,
        progress_callback: Optional[Callable[[int, str], None]] = None
    ) -> PublicationResult:
        """Publish a book to Amazon KDP"""
        
        if not self.is_ready or not self.selenium_service:
            raise Exception("KDP Publisher not initialized")
        
        start_time = datetime.utcnow()
        
        try:
            # Update progress
            if progress_callback:
                progress_callback(5, "validation")
            
            # Validate inputs
            validation_result = self._validate_publication_data(book_data, file_paths)
            if not validation_result["valid"]:
                return PublicationResult(
                    success=False,
                    status="failed",
                    message="Validation failed",
                    error=validation_result["error"],
                    execution_time=0
                )
            
            # Update progress
            if progress_callback:
                progress_callback(15, "credentials")
            
            # Get KDP credentials
            credentials = self._get_kdp_credentials(user_id)
            if not credentials:
                return PublicationResult(
                    success=False,
                    status="failed",
                    message="KDP credentials not configured",
                    error="Missing KDP credentials",
                    execution_time=0
                )
            
            # Update progress
            if progress_callback:
                progress_callback(25, "metadata_preparation")
            
            # Prepare metadata
            metadata = self._prepare_book_metadata(book_data)
            files = self._prepare_book_files(file_paths)
            pricing = self._prepare_pricing_config(pricing_config)
            
            # Update progress
            if progress_callback:
                progress_callback(35, "analytics_generation")
            
            # Generate analytics and predictions using AI
            analytics = await self._generate_publication_analytics(book_data, pricing_config)
            
            # Update progress
            if progress_callback:
                progress_callback(45, "kdp_upload")
            
            logger.info(f"Starting KDP publication for: {book_data['title']}")
            
            # Execute KDP publication using Selenium
            kdp_result = await self.selenium_service.publish_book(
                credentials, metadata, files, pricing, auto_publish
            )
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Update progress
            if progress_callback:
                progress_callback(90, "processing_results")
            
            # Process results
            if kdp_result.get("success"):
                result = PublicationResult(
                    success=True,
                    kdp_book_id=kdp_result["kdp_book_id"],
                    publication_url=kdp_result["publication_url"],
                    status=kdp_result["status"],
                    message=kdp_result["message"],
                    execution_time=execution_time,
                    estimated_live_date=kdp_result.get("metadata", {}).get("estimated_live_date"),
                    performance_predictions=analytics.model_dump(),
                    metadata={
                        "book_data": book_data,
                        "file_paths": file_paths,
                        "pricing_config": pricing_config,
                        "auto_publish": auto_publish,
                        "kdp_result": kdp_result
                    }
                )
            else:
                result = PublicationResult(
                    success=False,
                    status="failed",
                    message="KDP publication failed",
                    error=kdp_result.get("error", "Unknown error"),
                    execution_time=execution_time
                )
            
            # Update progress
            if progress_callback:
                progress_callback(95, "event_publishing")
            
            # Record metrics
            self.metrics.record_publication_attempt(
                book_data["title"],
                result.success,
                execution_time
            )
            
            # Publish event
            if self.event_client:
                await self.event_client.publish_event(
                    "kdp_publication_completed" if result.success else "kdp_publication_failed",
                    {
                        "book_title": book_data["title"],
                        "author": book_data["author"],
                        "success": result.success,
                        "kdp_book_id": result.kdp_book_id,
                        "execution_time": execution_time,
                        "user_id": user_id
                    }
                )
            
            # Final progress update
            if progress_callback:
                progress_callback(100, "completed")
            
            logger.info(f"KDP publication completed for: {book_data['title']}")
            return result
            
        except Exception as e:
            logger.error(f"KDP publication failed for {book_data.get('title', 'Unknown')}: {e}")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Record error metrics
            self.metrics.record_publication_error(str(e))
            
            # Publish error event
            if self.event_client:
                await self.event_client.publish_event(
                    "kdp_publication_error",
                    {
                        "book_title": book_data.get("title", "Unknown"),
                        "error": str(e),
                        "execution_time": execution_time,
                        "user_id": user_id
                    }
                )
            
            return PublicationResult(
                success=False,
                status="failed",
                message="Publication failed due to unexpected error",
                error=str(e),
                execution_time=execution_time
            )
    
    async def _generate_publication_analytics(
        self,
        book_data: Dict[str, Any],
        pricing_config: Optional[Dict[str, Any]] = None
    ) -> PublicationAnalytics:
        """Generate AI-powered publication analytics and predictions"""
        
        try:
            # Prepare analysis prompt
            prompt = self._create_analytics_prompt(book_data, pricing_config)
            
            logger.info(f"Generating publication analytics for: {book_data['title']}")
            
            # Generate analytics using AI agent
            result = await self.agent.run(prompt)
            
            logger.info(f"Publication analytics generated for: {book_data['title']}")
            return result.data
            
        except Exception as e:
            logger.error(f"Analytics generation failed for {book_data.get('title', 'Unknown')}: {e}")
            
            # Return fallback analytics
            return self._generate_fallback_analytics(book_data, pricing_config)
    
    def _create_analytics_prompt(
        self,
        book_data: Dict[str, Any],
        pricing_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a detailed analytics prompt"""
        
        price = pricing_config.get("price", 9.99) if pricing_config else 9.99
        
        prompt_parts = [
            f"Analyze the publication potential for this book:",
            f"",
            f"BOOK DETAILS:",
            f"- Title: {book_data.get('title', 'Unknown')}",
            f"- Author: {book_data.get('author', 'Unknown')}",
            f"- Categories: {', '.join(book_data.get('categories', ['General']))}",
            f"- Keywords: {', '.join(book_data.get('keywords', []))}",
            f"- Language: {book_data.get('language', 'English')}",
            f"- Price: ${price}",
        ]
        
        if book_data.get("description"):
            prompt_parts.extend([
                f"",
                f"DESCRIPTION:",
                f"{book_data['description'][:500]}..." if len(book_data['description']) > 500 else book_data['description']
            ])
        
        if pricing_config:
            prompt_parts.extend([
                f"",
                f"PRICING STRATEGY:",
                f"- Price: ${pricing_config.get('price', 9.99)}",
                f"- Royalty Rate: {pricing_config.get('royalty_option', '70')}%",
                f"- KDP Select: {'Yes' if pricing_config.get('kdp_select', True) else 'No'}",
                f"- Territories: {', '.join(pricing_config.get('territories', ['US']))}"
            ])
        
        prompt_parts.extend([
            f"",
            f"ANALYSIS REQUIREMENTS:",
            f"1. Estimate realistic monthly sales (consider market saturation)",
            f"2. Calculate monthly revenue based on pricing and royalties",
            f"3. Assess market competitiveness (0-100 score)",
            f"4. Provide 3-5 specific optimization suggestions",
            f"5. Calculate success probability (0-1) based on all factors",
            f"",
            f"Consider category popularity, keyword effectiveness, pricing strategy,",
            f"market competition, and title/description appeal in your analysis."
        ])
        
        return "\n".join(prompt_parts)
    
    def _generate_fallback_analytics(
        self,
        book_data: Dict[str, Any],
        pricing_config: Optional[Dict[str, Any]] = None
    ) -> PublicationAnalytics:
        """Generate fallback analytics when AI fails"""
        
        # Simple heuristic-based analytics
        base_score = 50
        
        # Category scoring
        popular_categories = ["Romance", "Mystery", "Self-Help", "Health", "Business"]
        if any(cat in popular_categories for cat in book_data.get("categories", [])):
            base_score += 15
        
        # Keyword scoring
        keyword_count = len(book_data.get("keywords", []))
        if keyword_count >= 5:
            base_score += 10
        elif keyword_count >= 3:
            base_score += 5
        
        # Price scoring
        price = pricing_config.get("price", 9.99) if pricing_config else 9.99
        if 2.99 <= price <= 9.99:
            base_score += 10
        elif price < 2.99:
            base_score -= 5
        
        # Calculate estimates
        estimated_sales = max(5, int(base_score * 1.5))
        royalty_rate = 0.7 if pricing_config and pricing_config.get("royalty_option") == "70" else 0.35
        estimated_revenue = estimated_sales * price * royalty_rate
        
        return PublicationAnalytics(
            estimated_monthly_sales=estimated_sales,
            estimated_monthly_revenue=round(estimated_revenue, 2),
            market_competitiveness=min(100, base_score + 20),
            optimization_suggestions=[
                "Optimize keywords for better discoverability",
                "Consider pricing strategy adjustment",
                "Improve book description for better conversion",
                "Research category placement for maximum visibility"
            ],
            success_probability=min(1.0, base_score / 100.0)
        )
    
    def _validate_publication_data(
        self,
        book_data: Dict[str, Any],
        file_paths: Dict[str, str]
    ) -> Dict[str, Any]:
        """Validate publication data"""
        errors = []
        
        # Required book data fields
        required_fields = ["title", "author", "description"]
        for field in required_fields:
            if not book_data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # File validation
        if not file_paths.get("manuscript") or not os.path.exists(file_paths["manuscript"]):
            errors.append("Manuscript file not found or not specified")
        
        if not file_paths.get("cover") or not os.path.exists(file_paths["cover"]):
            errors.append("Cover file not found or not specified")
        
        # Title length validation
        if len(book_data.get("title", "")) > 200:
            errors.append("Title too long (max 200 characters)")
        
        # Description length validation
        if len(book_data.get("description", "")) > 4000:
            errors.append("Description too long (max 4000 characters)")
        
        # Keywords validation
        keywords = book_data.get("keywords", [])
        if len(keywords) > 7:
            errors.append("Too many keywords (max 7)")
        
        return {
            "valid": len(errors) == 0,
            "error": "; ".join(errors),
            "errors": errors
        }
    
    def _get_kdp_credentials(self, user_id: Optional[str]) -> Optional[KDPCredentials]:
        """Get KDP credentials from environment"""
        email = os.getenv("KDP_EMAIL")
        password = os.getenv("KDP_PASSWORD")
        backup_codes = os.getenv("KDP_BACKUP_CODES", "").split(",") if os.getenv("KDP_BACKUP_CODES") else []
        
        if email and password:
            return KDPCredentials(
                email=email,
                password=password,
                two_factor_backup_codes=[code.strip() for code in backup_codes if code.strip()]
            )
        return None
    
    def _prepare_book_metadata(self, book_data: Dict[str, Any]) -> BookMetadata:
        """Prepare book metadata for KDP"""
        return BookMetadata(
            title=book_data["title"],
            subtitle=book_data.get("subtitle"),
            author=book_data["author"],
            description=book_data["description"],
            keywords=book_data.get("keywords", []),
            categories=book_data.get("categories", ["Fiction"]),
            language=book_data.get("language", "English"),
            series_name=book_data.get("series_name"),
            series_number=book_data.get("series_number"),
            isbn=book_data.get("isbn")
        )
    
    def _prepare_book_files(self, file_paths: Dict[str, str]) -> BookFiles:
        """Prepare book files for KDP"""
        manuscript_format = self._detect_file_format(file_paths["manuscript"])
        
        return BookFiles(
            manuscript_path=file_paths["manuscript"],
            cover_path=file_paths["cover"],
            manuscript_format=manuscript_format
        )
    
    def _prepare_pricing_config(self, pricing_config: Optional[Dict[str, Any]]) -> PricingConfig:
        """Prepare pricing configuration for KDP"""
        if not pricing_config:
            pricing_config = {}
        
        return PricingConfig(
            price_usd=pricing_config.get("price", 9.99),
            territories=pricing_config.get("territories", ["US", "UK", "CA"]),
            kdp_select=pricing_config.get("kdp_select", True),
            royalty_option=pricing_config.get("royalty_option", "70")
        )
    
    def _detect_file_format(self, file_path: str) -> str:
        """Detect file format from extension"""
        extension = os.path.splitext(file_path)[1].lower()
        format_map = {
            '.docx': 'docx',
            '.doc': 'doc',
            '.pdf': 'pdf',
            '.epub': 'epub',
            '.txt': 'txt',
            '.rtf': 'rtf'
        }
        return format_map.get(extension, "unknown")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.event_client:
                await self.event_client.disconnect()
            
            self.is_ready = False
            logger.info("KDP Publisher cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during KDP Publisher cleanup: {e}")