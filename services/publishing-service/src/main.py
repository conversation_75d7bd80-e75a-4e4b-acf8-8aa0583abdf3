"""
Publishing Service - Main Application

A comprehensive microservice providing automated book publishing capabilities,
including Amazon KDP integration, metadata management, and publishing analytics.
"""

import asyncio
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from kdp_publisher import KDPPublisher, PublicationResult
from publication_analytics import PublicationAnalytics
from event_client import EventClient
from service_registry_client import ServiceRegistryClient
from security_manager import SecurityManager, verify_api_key
from monitoring import MetricsCollector, setup_logging


# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

# Global instances
kdp_publisher: Optional[KDPPublisher] = None
publication_analytics: Optional[PublicationAnalytics] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None
metrics: Optional[MetricsCollector] = None

# ============================================================================
# Request/Response Models
# ============================================================================

class PublicationRequest(BaseModel):
    """Request model for book publication"""
    book_id: str = Field(..., description="Unique book identifier")
    title: str = Field(..., description="Book title")
    subtitle: Optional[str] = Field(default=None, description="Book subtitle")
    author: str = Field(..., description="Author name")
    description: str = Field(..., description="Book description")
    keywords: List[str] = Field(default=[], description="Keywords for discoverability")
    categories: List[str] = Field(default=["Fiction"], description="Book categories")
    language: str = Field(default="English", description="Book language")
    
    # File paths
    manuscript_path: str = Field(..., description="Path to manuscript file")
    cover_path: str = Field(..., description="Path to cover image")
    manuscript_format: str = Field(default="docx", description="Manuscript format")
    
    # Pricing configuration
    price_usd: float = Field(default=9.99, ge=0.99, le=999.99, description="Price in USD")
    royalty_option: str = Field(default="70", description="Royalty rate (35 or 70)")
    kdp_select: bool = Field(default=True, description="Enroll in KDP Select")
    territories: List[str] = Field(default=["US", "UK", "CA"], description="Publishing territories")
    
    # Publishing options
    auto_publish: bool = Field(default=False, description="Auto-publish or save as draft")
    publication_date: Optional[str] = Field(default=None, description="Scheduled publication date")
    
    # User context
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class BulkPublicationRequest(BaseModel):
    """Request model for bulk publication"""
    publications: List[PublicationRequest] = Field(..., description="List of publications to process")
    processing_priority: str = Field(default="normal", description="Processing priority: low, normal, high")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class PublicationStatusRequest(BaseModel):
    """Request model for updating publication status"""
    status: str = Field(..., description="New publication status")
    notes: Optional[str] = Field(default=None, description="Status update notes")

class ServiceResponse(BaseModel):
    """Standard service response model"""
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Publication results")
    message: str = Field(default="", description="Response message")
    execution_time: Optional[float] = Field(default=None, description="Execution time in seconds")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())

class PublicationStatusModel(BaseModel):
    """Model for publication job status"""
    request_id: str
    book_id: str
    status: str  # pending, uploading, processing, published, failed
    progress: Optional[int] = None  # 0-100
    current_step: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: str
    updated_at: str

class AnalyticsResponse(BaseModel):
    """Model for publication analytics"""
    publication_id: str
    total_publications: int
    success_rate: float
    average_processing_time: float
    performance_metrics: Dict[str, Any]
    recommendations: List[str]

# ============================================================================
# Startup/Shutdown
# ============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Publishing Service")
    
    global kdp_publisher, publication_analytics, event_client
    global service_registry, security_manager, metrics
    
    try:
        # Initialize components
        metrics = MetricsCollector()
        security_manager = SecurityManager()
        
        # Initialize KDP publisher
        kdp_publisher = KDPPublisher()
        await kdp_publisher.initialize()
        
        # Initialize publication analytics
        publication_analytics = PublicationAnalytics()
        await publication_analytics.initialize()
        
        # Initialize event client
        event_bus_url = os.getenv("EVENT_BUS_URL", "http://event-bus:8080")
        event_client = EventClient(event_bus_url)
        await event_client.connect()
        
        # Initialize service registry
        service_discovery_url = os.getenv("SERVICE_DISCOVERY_URL", "http://service-discovery:8070")
        service_registry = ServiceRegistryClient(service_discovery_url)
        
        # Register service
        service_info = {
            "name": "publishing-service",
            "host": os.getenv("SERVICE_HOST", "0.0.0.0"),
            "port": int(os.getenv("SERVICE_PORT", "8087")),
            "health_endpoint": "/health",
            "capabilities": [
                "kdp_publishing",
                "publication_management", 
                "publishing_analytics",
                "bulk_publishing",
                "status_tracking"
            ],
            "version": "1.0.0"
        }
        
        await service_registry.register_service(service_info)
        logger.info("Publishing Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Publishing Service: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down Publishing Service")
    
    try:
        if kdp_publisher:
            await kdp_publisher.cleanup()
        if publication_analytics:
            await publication_analytics.cleanup()
        if event_client:
            await event_client.disconnect()
        if service_registry:
            await service_registry.unregister_service("publishing-service")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# ============================================================================
# FastAPI Application
# ============================================================================

app = FastAPI(
    title="Publishing Service",
    description="Automated book publishing and distribution microservice",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for async jobs (in production, use Redis or database)
publication_jobs: Dict[str, PublicationStatusModel] = {}

# ============================================================================
# Health & Monitoring Endpoints
# ============================================================================

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check component availability
        publisher_status = "healthy" if kdp_publisher and kdp_publisher.is_ready else "unhealthy"
        analytics_status = "healthy" if publication_analytics and publication_analytics.is_ready else "unhealthy"
        
        # Check external dependencies
        event_status = "healthy" if event_client and event_client.is_connected else "unhealthy"
        
        overall_status = "healthy" if all([
            publisher_status == "healthy",
            analytics_status == "healthy",
            event_status == "healthy"
        ]) else "unhealthy"
        
        return {
            "status": overall_status,
            "service": "publishing-service",
            "version": "1.0.0",
            "components": {
                "kdp_publisher": publisher_status,
                "publication_analytics": analytics_status,
                "event_client": event_status,
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    try:
        if not kdp_publisher or not kdp_publisher.is_ready:
            raise HTTPException(status_code=503, detail="KDP Publisher not ready")
        
        if not publication_analytics or not publication_analytics.is_ready:
            raise HTTPException(status_code=503, detail="Publication Analytics not ready")
        
        return {"status": "ready", "service": "publishing-service"}
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    try:
        if metrics:
            return metrics.get_metrics()
        return {"message": "Metrics not available"}
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        return {"error": str(e)}

# ============================================================================
# Publication Endpoints
# ============================================================================

@app.post("/publish", response_model=ServiceResponse)
async def publish_book(
    request: PublicationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Publish a book to Amazon KDP asynchronously"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting publication for book: {request.title}")
        
        # Create publication job
        job = PublicationStatusModel(
            request_id=request_id,
            book_id=request.book_id,
            status="pending",
            progress=0,
            current_step="validation",
            created_at=start_time.isoformat(),
            updated_at=start_time.isoformat()
        )
        publication_jobs[request_id] = job
        
        # Start background task
        background_tasks.add_task(_execute_publication, request_id, request)
        
        # Record metrics
        if metrics:
            metrics.record_publication_request(request.title, request.auto_publish)
        
        return ServiceResponse(
            request_id=request_id,
            status="accepted",
            message=f"Publication started for book: {request.title}",
            timestamp=start_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Publication request failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_publication_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Publication failed: {str(e)}"
        )

@app.get("/publish/{request_id}", response_model=ServiceResponse)
async def get_publication_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Get publication status and results"""
    try:
        if request_id not in publication_jobs:
            raise HTTPException(status_code=404, detail="Publication job not found")
        
        job = publication_jobs[request_id]
        
        return ServiceResponse(
            request_id=request_id,
            status=job.status,
            result=job.result,
            message=job.error_message if job.status == "failed" else "Publication status retrieved",
            timestamp=job.updated_at
        )
        
    except Exception as e:
        logger.error(f"Failed to get publication status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/publish/sync", response_model=ServiceResponse)
async def publish_book_sync(
    request: PublicationRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Publish a book to Amazon KDP synchronously"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting synchronous publication for book: {request.title}")
        
        if not kdp_publisher:
            raise HTTPException(status_code=503, detail="KDP Publisher not available")
        
        # Execute publication
        result = await kdp_publisher.publish_book(
            book_data={
                "title": request.title,
                "subtitle": request.subtitle,
                "author": request.author,
                "description": request.description,
                "keywords": request.keywords,
                "categories": request.categories,
                "language": request.language
            },
            file_paths={
                "manuscript": request.manuscript_path,
                "cover": request.cover_path
            },
            pricing_config={
                "price": request.price_usd,
                "royalty_option": request.royalty_option,
                "kdp_select": request.kdp_select,
                "territories": request.territories
            },
            auto_publish=request.auto_publish,
            user_id=request.user_id
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "book_published" if request.auto_publish else "book_saved_draft",
                {
                    "request_id": request_id,
                    "book_id": request.book_id,
                    "title": request.title,
                    "author": request.author,
                    "kdp_book_id": result.kdp_book_id,
                    "publication_url": result.publication_url,
                    "status": result.status,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_publication_completed(request.title, execution_time, result.success)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result.model_dump(),
            message="Book published successfully" if result.success else f"Publication failed: {result.error}",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Synchronous publication failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_publication_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Publication failed: {str(e)}"
        )

# ============================================================================
# Bulk Publishing Endpoints
# ============================================================================

@app.post("/publish/bulk", response_model=ServiceResponse)
async def bulk_publish_books(
    request: BulkPublicationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Publish multiple books in bulk"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting bulk publication for {len(request.publications)} books")
        
        # Create bulk job
        job = PublicationStatusModel(
            request_id=request_id,
            book_id="bulk",
            status="pending",
            progress=0,
            current_step="initialization",
            created_at=start_time.isoformat(),
            updated_at=start_time.isoformat()
        )
        publication_jobs[request_id] = job
        
        # Start background task
        background_tasks.add_task(_execute_bulk_publication, request_id, request)
        
        # Record metrics
        if metrics:
            metrics.record_bulk_publication_request(len(request.publications))
        
        return ServiceResponse(
            request_id=request_id,
            status="accepted",
            message=f"Bulk publication started for {len(request.publications)} books",
            timestamp=start_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Bulk publication request failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_bulk_publication_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk publication failed: {str(e)}"
        )

# ============================================================================
# Analytics Endpoints
# ============================================================================

@app.get("/analytics/{publication_id}", response_model=AnalyticsResponse)
async def get_publication_analytics(
    publication_id: str,
    api_key: str = Depends(verify_api_key)
) -> AnalyticsResponse:
    """Get analytics for a specific publication"""
    try:
        if not publication_analytics:
            raise HTTPException(status_code=503, detail="Publication Analytics not available")
        
        analytics = await publication_analytics.get_publication_analytics(publication_id)
        
        return AnalyticsResponse(
            publication_id=publication_id,
            total_publications=analytics["total_publications"],
            success_rate=analytics["success_rate"],
            average_processing_time=analytics["average_processing_time"],
            performance_metrics=analytics["performance_metrics"],
            recommendations=analytics["recommendations"]
        )
        
    except Exception as e:
        logger.error(f"Failed to get publication analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/summary")
async def get_analytics_summary(
    api_key: str = Depends(verify_api_key)
):
    """Get overall publication analytics summary"""
    try:
        if not publication_analytics:
            raise HTTPException(status_code=503, detail="Publication Analytics not available")
        
        summary = await publication_analytics.get_summary_analytics()
        
        return summary
        
    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# Management Endpoints
# ============================================================================

@app.put("/publications/{publication_id}/status")
async def update_publication_status(
    publication_id: str,
    status_request: PublicationStatusRequest,
    api_key: str = Depends(verify_api_key)
):
    """Update publication status"""
    try:
        if publication_id not in publication_jobs:
            raise HTTPException(status_code=404, detail="Publication not found")
        
        job = publication_jobs[publication_id]
        job.status = status_request.status
        job.updated_at = datetime.utcnow().isoformat()
        
        # Publish status update event
        if event_client:
            await event_client.publish_event(
                "publication_status_updated",
                {
                    "publication_id": publication_id,
                    "status": status_request.status,
                    "notes": status_request.notes,
                    "timestamp": job.updated_at
                }
            )
        
        return {"message": "Publication status updated", "publication_id": publication_id}
        
    except Exception as e:
        logger.error(f"Failed to update publication status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/publications")
async def list_publications(
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[str] = None,
    api_key: str = Depends(verify_api_key)
):
    """List publications with filtering and pagination"""
    try:
        filtered_jobs = []
        
        for job in publication_jobs.values():
            if status_filter and job.status != status_filter:
                continue
            filtered_jobs.append(job)
        
        # Simple pagination
        paginated_jobs = filtered_jobs[offset:offset + limit]
        
        return {
            "publications": [job.model_dump() for job in paginated_jobs],
            "total": len(filtered_jobs),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Failed to list publications: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# Background Task Functions
# ============================================================================

async def _execute_publication(request_id: str, request: PublicationRequest):
    """Execute publication in background"""
    try:
        # Update job status
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "uploading"
            publication_jobs[request_id].progress = 10
            publication_jobs[request_id].current_step = "uploading"
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        if not kdp_publisher:
            raise Exception("KDP Publisher not available")
        
        # Execute publication
        result = await kdp_publisher.publish_book(
            book_data={
                "title": request.title,
                "subtitle": request.subtitle,
                "author": request.author,
                "description": request.description,
                "keywords": request.keywords,
                "categories": request.categories,
                "language": request.language
            },
            file_paths={
                "manuscript": request.manuscript_path,
                "cover": request.cover_path
            },
            pricing_config={
                "price": request.price_usd,
                "royalty_option": request.royalty_option,
                "kdp_select": request.kdp_select,
                "territories": request.territories
            },
            auto_publish=request.auto_publish,
            user_id=request.user_id
        )
        
        # Update job status - completed
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "published" if result.success else "failed"
            publication_jobs[request_id].progress = 100
            publication_jobs[request_id].result = result.model_dump()
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "book_published" if result.success else "publication_failed",
                {
                    "request_id": request_id,
                    "book_id": request.book_id,
                    "title": request.title,
                    "author": request.author,
                    "kdp_book_id": result.kdp_book_id,
                    "publication_url": result.publication_url,
                    "status": result.status,
                    "user_id": request.user_id,
                    "success": result.success
                }
            )
        
    except Exception as e:
        logger.error(f"Background publication failed: {e}")
        
        # Update job status - failed
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "failed"
            publication_jobs[request_id].error_message = str(e)
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish failure event
        if event_client:
            await event_client.publish_event(
                "publication_failed",
                {
                    "request_id": request_id,
                    "book_id": request.book_id,
                    "title": request.title,
                    "user_id": request.user_id,
                    "error": str(e)
                }
            )

async def _execute_bulk_publication(request_id: str, request: BulkPublicationRequest):
    """Execute bulk publication in background"""
    try:
        if not kdp_publisher:
            raise Exception("KDP Publisher not available")
        
        # Update job status
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "processing"
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        results = []
        total_books = len(request.publications)
        
        for i, pub_request in enumerate(request.publications):
            try:
                # Execute individual publication
                result = await kdp_publisher.publish_book(
                    book_data={
                        "title": pub_request.title,
                        "subtitle": pub_request.subtitle,
                        "author": pub_request.author,
                        "description": pub_request.description,
                        "keywords": pub_request.keywords,
                        "categories": pub_request.categories,
                        "language": pub_request.language
                    },
                    file_paths={
                        "manuscript": pub_request.manuscript_path,
                        "cover": pub_request.cover_path
                    },
                    pricing_config={
                        "price": pub_request.price_usd,
                        "royalty_option": pub_request.royalty_option,
                        "kdp_select": pub_request.kdp_select,
                        "territories": pub_request.territories
                    },
                    auto_publish=pub_request.auto_publish,
                    user_id=pub_request.user_id
                )
                
                results.append({
                    "book_id": pub_request.book_id,
                    "title": pub_request.title,
                    "success": result.success,
                    "result": result.model_dump()
                })
                
                # Update progress
                progress = int(((i + 1) / total_books) * 100)
                if request_id in publication_jobs:
                    publication_jobs[request_id].progress = progress
                    publication_jobs[request_id].current_step = f"Processing book {i + 1}/{total_books}"
                    publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
                
            except Exception as e:
                logger.error(f"Failed to publish book {pub_request.title}: {e}")
                results.append({
                    "book_id": pub_request.book_id,
                    "title": pub_request.title,
                    "success": False,
                    "error": str(e)
                })
        
        # Update job status - completed
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "completed"
            publication_jobs[request_id].progress = 100
            publication_jobs[request_id].result = {
                "bulk_results": results,
                "total_books": total_books,
                "successful": len([r for r in results if r.get("success")]),
                "failed": len([r for r in results if not r.get("success")])
            }
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "bulk_publication_completed",
                {
                    "request_id": request_id,
                    "total_books": total_books,
                    "successful": len([r for r in results if r.get("success")]),
                    "failed": len([r for r in results if not r.get("success")]),
                    "user_id": request.user_id
                }
            )
        
    except Exception as e:
        logger.error(f"Bulk publication failed: {e}")
        
        # Update job status - failed
        if request_id in publication_jobs:
            publication_jobs[request_id].status = "failed"
            publication_jobs[request_id].error_message = str(e)
            publication_jobs[request_id].updated_at = datetime.utcnow().isoformat()

# ============================================================================
# Application Entry Point
# ============================================================================

if __name__ == "__main__":
    port = int(os.getenv("SERVICE_PORT", "8087"))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )