"""
Simple tests for Publishing Service
"""

import pytest
import sys
import os
from unittest.mock import Async<PERSON>ock, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestSimpleKDPPublisher:
    """Simple tests for KDPPublisher"""
    
    @pytest.mark.asyncio
    async def test_initialization_no_api_keys(self):
        """Test initialization with no API keys"""
        from kdp_publisher import KDPPublisher
        
        with patch.dict(os.environ, {}, clear=True):
            publisher = KDPPublisher()
            
            with pytest.raises(Exception, match="No AI model available"):
                await publisher.initialize()
    
    @pytest.mark.asyncio
    async def test_initialization_with_selenium_service(self):
        """Test initialization with Selenium service"""
        from kdp_publisher import KDPPublisher
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}), \
             patch('kdp_publisher.KDPSeleniumService') as mock_selenium:
            
            publisher = KDPPublisher()
            await publisher.initialize()
            
            assert publisher.is_ready
            mock_selenium.assert_called_once()
    
    def test_validate_publication_data_valid(self):
        """Test validation with valid data"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        # Create test files
        test_manuscript = "/tmp/test_manuscript.docx"
        test_cover = "/tmp/test_cover.jpg"
        
        with open(test_manuscript, 'w') as f:
            f.write("test content")
        with open(test_cover, 'w') as f:
            f.write("test image")
        
        try:
            book_data = {
                "title": "Test Book",
                "author": "Test Author",
                "description": "Test description for the book"
            }
            
            file_paths = {
                "manuscript": test_manuscript,
                "cover": test_cover
            }
            
            result = publisher._validate_publication_data(book_data, file_paths)
            
            assert result["valid"] is True
            assert len(result["errors"]) == 0
            
        finally:
            # Cleanup test files
            if os.path.exists(test_manuscript):
                os.remove(test_manuscript)
            if os.path.exists(test_cover):
                os.remove(test_cover)
    
    def test_validate_publication_data_invalid(self):
        """Test validation with invalid data"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        book_data = {
            "title": "",  # Missing title
            "author": "Test Author"
            # Missing description
        }
        
        file_paths = {
            "manuscript": "/nonexistent/file.docx",
            "cover": "/nonexistent/cover.jpg"
        }
        
        result = publisher._validate_publication_data(book_data, file_paths)
        
        assert result["valid"] is False
        assert len(result["errors"]) > 0
        assert "Missing required field: title" in result["errors"]
        assert "Missing required field: description" in result["errors"]
    
    def test_detect_file_format(self):
        """Test file format detection"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        assert publisher._detect_file_format("test.docx") == "docx"
        assert publisher._detect_file_format("test.pdf") == "pdf"
        assert publisher._detect_file_format("test.epub") == "epub"
        assert publisher._detect_file_format("test.unknown") == "unknown"
    
    def test_prepare_book_metadata(self):
        """Test book metadata preparation"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        book_data = {
            "title": "Test Book",
            "subtitle": "A Test",
            "author": "Test Author",
            "description": "Test description",
            "keywords": ["test", "book"],
            "categories": ["Fiction"],
            "language": "English"
        }
        
        metadata = publisher._prepare_book_metadata(book_data)
        
        assert metadata.title == "Test Book"
        assert metadata.subtitle == "A Test"
        assert metadata.author == "Test Author"
        assert metadata.description == "Test description"
        assert metadata.keywords == ["test", "book"]
        assert metadata.categories == ["Fiction"]
        assert metadata.language == "English"
    
    def test_prepare_pricing_config(self):
        """Test pricing configuration preparation"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        pricing_config = {
            "price": 9.99,
            "royalty_option": "70",
            "kdp_select": True,
            "territories": ["US", "UK"]
        }
        
        pricing = publisher._prepare_pricing_config(pricing_config)
        
        assert pricing.price_usd == 9.99
        assert pricing.royalty_option == "70"
        assert pricing.kdp_select is True
        assert pricing.territories == ["US", "UK"]
    
    def test_prepare_pricing_config_defaults(self):
        """Test pricing configuration with defaults"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        pricing = publisher._prepare_pricing_config(None)
        
        assert pricing.price_usd == 9.99
        assert pricing.royalty_option == "70"
        assert pricing.kdp_select is True
        assert pricing.territories == ["US", "UK", "CA"]
    
    def test_generate_fallback_analytics(self):
        """Test fallback analytics generation"""
        from kdp_publisher import KDPPublisher
        
        publisher = KDPPublisher()
        
        book_data = {
            "title": "Test Romance Novel",
            "categories": ["Romance"],
            "keywords": ["love", "romance", "fiction", "novel", "relationship"]
        }
        
        pricing_config = {
            "price": 4.99,
            "royalty_option": "70"
        }
        
        analytics = publisher._generate_fallback_analytics(book_data, pricing_config)
        
        assert analytics.estimated_monthly_sales > 0
        assert analytics.estimated_monthly_revenue > 0
        assert 0 <= analytics.market_competitiveness <= 100
        assert len(analytics.optimization_suggestions) > 0
        assert 0 <= analytics.success_probability <= 1


class TestSimplePublicationAnalytics:
    """Simple tests for PublicationAnalytics"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """Test analytics initialization"""
        from publication_analytics import PublicationAnalytics
        
        analytics = PublicationAnalytics()
        await analytics.initialize()
        
        assert analytics.is_ready
    
    @pytest.mark.asyncio
    async def test_get_publication_analytics(self):
        """Test getting publication analytics"""
        from publication_analytics import PublicationAnalytics
        
        analytics = PublicationAnalytics()
        await analytics.initialize()
        
        result = await analytics.get_publication_analytics("test-pub-id")
        
        assert "total_publications" in result
        assert "success_rate" in result
        assert "average_processing_time" in result
        assert "performance_metrics" in result
        assert "recommendations" in result
        assert isinstance(result["recommendations"], list)
    
    @pytest.mark.asyncio
    async def test_get_summary_analytics(self):
        """Test getting summary analytics"""
        from publication_analytics import PublicationAnalytics
        
        analytics = PublicationAnalytics()
        await analytics.initialize()
        
        summary = await analytics.get_summary_analytics()
        
        assert "total_books_published" in summary
        assert "total_revenue" in summary
        assert "average_book_performance" in summary
        assert "top_performing_categories" in summary
        assert "trends" in summary
        assert "insights" in summary
    
    @pytest.mark.asyncio
    async def test_record_publication_metrics(self):
        """Test recording publication metrics"""
        from publication_analytics import PublicationAnalytics
        
        analytics = PublicationAnalytics()
        await analytics.initialize()
        
        metrics = {
            "sales": 25,
            "revenue": 125.50,
            "conversion_rate": 0.05
        }
        
        # Should not raise an exception
        await analytics.record_publication_metrics("test-pub-id", metrics)


class TestSimpleKDPSeleniumService:
    """Simple tests for KDPSeleniumService"""
    
    def test_service_initialization(self):
        """Test Selenium service initialization"""
        from kdp_selenium_service import KDPSeleniumService
        
        service = KDPSeleniumService(headless=True, timeout=30)
        
        assert service.headless is True
        assert service.timeout == 30
        assert service.driver is None
        assert service.wait is None
    
    def test_create_kdp_credentials(self):
        """Test KDP credentials creation"""
        from kdp_selenium_service import create_kdp_credentials
        
        credentials = create_kdp_credentials(
            "<EMAIL>",
            "password123",
            ["backup1", "backup2"]
        )
        
        assert credentials.email == "<EMAIL>"
        assert credentials.password == "password123"
        assert credentials.two_factor_backup_codes == ["backup1", "backup2"]
    
    def test_create_book_metadata(self):
        """Test book metadata creation"""
        from kdp_selenium_service import create_book_metadata
        
        metadata = create_book_metadata(
            title="Test Book",
            author="Test Author",
            description="Test description",
            keywords=["test", "book"],
            categories=["Fiction"],
            language="English"
        )
        
        assert metadata.title == "Test Book"
        assert metadata.author == "Test Author"
        assert metadata.description == "Test description"
        assert metadata.keywords == ["test", "book"]
        assert metadata.categories == ["Fiction"]
        assert metadata.language == "English"
    
    def test_create_book_files(self):
        """Test book files creation"""
        from kdp_selenium_service import create_book_files
        
        files = create_book_files(
            manuscript_path="/path/to/manuscript.docx",
            cover_path="/path/to/cover.jpg",
            manuscript_format="docx"
        )
        
        assert files.manuscript_path == "/path/to/manuscript.docx"
        assert files.cover_path == "/path/to/cover.jpg"
        assert files.manuscript_format == "docx"
    
    def test_create_pricing_config(self):
        """Test pricing configuration creation"""
        from kdp_selenium_service import create_pricing_config
        
        pricing = create_pricing_config(
            price=9.99,
            territories=["US", "UK"],
            kdp_select=True,
            royalty_option="70"
        )
        
        assert pricing.price_usd == 9.99
        assert pricing.territories == ["US", "UK"]
        assert pricing.kdp_select is True
        assert pricing.royalty_option == "70"


if __name__ == "__main__":
    pytest.main([__file__])