# Sales Monitor Service Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sales-monitor-service
  namespace: publish-ai
  labels:
    app: sales-monitor-service
    component: microservice
    tier: tier-2
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sales-monitor-service
  template:
    metadata:
      labels:
        app: sales-monitor-service
        component: microservice
        tier: tier-2
    spec:
      containers:
      - name: sales-monitor-service
        image: publish-ai/sales-monitor-service:latest
        ports:
        - containerPort: 8084
          name: http
        env:
        - name: SERVICE_HOST
          value: "0.0.0.0"
        - name: SERVICE_PORT
          value: "8084"
        - name: SERVICE_NAME
          value: "sales-monitor-service"
        - name: PREFERRED_MODEL
          value: "openai"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: anthropic-api-key
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: sales-monitor-service-tls
              key: api-key
        - name: EVENT_BUS_URL
          value: "https://event-bus.publish-ai.svc.cluster.local:8080"
        - name: SERVICE_DISCOVERY_URL
          value: "https://service-discovery.publish-ai.svc.cluster.local:8070"
        - name: ENVIRONMENT
          value: "production"
        
        # Resource limits
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8084
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          
        readinessProbe:
          httpGet:
            path: /ready
            port: 8084
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
        
        # Security context
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        
        # Volume mounts for certificates and storage
        volumeMounts:
        - name: tls-certs
          mountPath: /etc/ssl/certs
          readOnly: true
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/.cache
        - name: model-storage
          mountPath: /app/storage
      
      # Security context for pod
      securityContext:
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Volumes
      volumes:
      - name: tls-certs
        secret:
          secretName: sales-monitor-service-tls
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      - name: model-storage
        persistentVolumeClaim:
          claimName: sales-models-pvc
      
      # Service account
      serviceAccountName: sales-monitor-service
      
      # Node selection
      nodeSelector:
        publish-ai/node-type: "worker"
      
      # Pod disruption budget considerations
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchLabels:
                  app: sales-monitor-service
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: sales-monitor-service
  namespace: publish-ai
  labels:
    app: sales-monitor-service
    component: microservice
spec:
  type: ClusterIP
  ports:
  - port: 8084
    targetPort: 8084
    protocol: TCP
    name: http
  selector:
    app: sales-monitor-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: sales-monitor-service
  namespace: publish-ai
  labels:
    app: sales-monitor-service

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: sales-monitor-service-pdb
  namespace: publish-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: sales-monitor-service

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: sales-models-pvc
  namespace: publish-ai
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd