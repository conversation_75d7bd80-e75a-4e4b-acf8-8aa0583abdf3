# Sales Monitor Service

A comprehensive microservice implementation providing AI-powered sales monitoring, performance analytics, and predictive modeling capabilities for the Publish AI platform.

## Overview

The Sales Monitor Service is part of the Publish AI platform's microservices architecture migration. It combines advanced sales monitoring, performance analysis, and machine learning-based prediction capabilities into a standalone, scalable service for comprehensive business intelligence and revenue optimization.

## Features

- **AI-Powered Sales Monitoring**: Real-time sales performance tracking with intelligent insights
- **Predictive Analytics**: ML-based sales forecasting and performance prediction
- **Performance Analysis**: Comprehensive analytics across multiple books and time periods
- **Risk Assessment**: Automated risk identification and mitigation strategies
- **Market Intelligence**: Competitive analysis and market trend evaluation
- **Revenue Optimization**: Pricing optimization and strategic recommendations
- **Multiple AI Models**: Supports both OpenAI and Anthropic models with automatic fallback
- **Machine Learning**: Integrated scikit-learn models for advanced predictions
- **Event-Driven**: Integrates with Event Bus for asynchronous communication
- **Service Discovery**: Automatic registration and health monitoring
- **Security**: API key authentication and mTLS support
- **Monitoring**: Comprehensive Prometheus metrics and health checks
- **Scalable**: Docker containerization with Kubernetes support

## API Endpoints

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### Sales Monitoring
- `POST /monitor` - Start asynchronous sales monitoring
- `GET /monitor/{request_id}` - Get monitoring status and results
- `POST /monitor/sync` - Synchronous sales monitoring execution

### Sales Prediction
- `POST /predict` - Predict book sales performance
- `POST /train-models` - Train or retrain ML prediction models

### Sales Analysis
- `POST /analyze` - Analyze sales performance across multiple books

## Request/Response Models

### Sales Monitoring Request
```json
{
  "date_range": "last_30_days",
  "include_page_reads": true,
  "generate_insights": true,
  "books_filter": ["book_id_1", "book_id_2"],
  "user_id": "user123"
}
```

### Sales Monitoring Response
```json
{
  "request_id": "uuid-string",
  "status": "completed",
  "result": {
    "reporting_period": "last_30_days",
    "books_data": [
      {
        "book_id": "book_1",
        "title": "Sample Book",
        "units_sold": 150,
        "revenue": 747.50,
        "page_reads": 2500,
        "average_rating": 4.2
      }
    ],
    "performance_metrics": {
      "total_revenue": 1500.00,
      "growth_rate": 0.25,
      "conversion_rate": 0.08
    },
    "insights": {
      "key_insights": [
        "Strong performance in fiction category",
        "Page reads indicate high engagement"
      ],
      "recommendations": [
        "Consider expanding marketing for top performers",
        "Optimize pricing for underperforming titles"
      ]
    },
    "total_revenue": 1500.00,
    "total_units_sold": 300,
    "average_price": 5.00,
    "top_performers": [
      {
        "book_id": "book_1",
        "performance_score": 0.89
      }
    ],
    "growth_metrics": {
      "monthly_growth": 0.25,
      "year_over_year": 0.45
    },
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "message": "Sales monitoring completed successfully"
}
```

### Sales Prediction Request
```json
{
  "book_id": "book_123",
  "target_price": 4.99,
  "launch_date": "2024-02-01",
  "market_analysis": true,
  "user_id": "user123"
}
```

### Sales Prediction Response
```json
{
  "request_id": "prediction-uuid",
  "result": {
    "book_id": "book_123",
    "predictions": {
      "sales_30d": 75,
      "revenue_30d": 374.25,
      "sales_90d": 187,
      "revenue_90d": 933.13,
      "success_probability": 0.78,
      "confidence": 0.85,
      "peak_sales_period": "first_week",
      "roi_projection": 3.74
    },
    "risk_assessment": {
      "risk_level": "medium",
      "risk_score": 0.35,
      "identified_risks": [
        "High competition in category",
        "Seasonal demand factors"
      ],
      "mitigation_strategies": [
        "Consider unique positioning or niche targeting",
        "Optimize launch timing for seasonal factors"
      ]
    },
    "recommendations": [
      {
        "type": "content",
        "priority": "high",
        "title": "Enhance Content Value",
        "description": "Add more actionable examples and case studies"
      },
      {
        "type": "marketing",
        "priority": "medium",
        "title": "Optimize Title Appeal",
        "description": "Consider adding power words for better market appeal"
      }
    ],
    "price_optimization": {
      "recommended_price": 4.99,
      "market_average": 4.75,
      "optimal_range": {"min": 2.99, "max": 7.99},
      "price_elasticity": -1.2,
      "pricing_strategy": "competitive",
      "test_prices": [3.99, 4.99, 5.99]
    },
    "feature_importance": {
      "content_quality": 0.25,
      "market_conditions": 0.20,
      "title_appeal": 0.15,
      "pricing": 0.15,
      "readability": 0.10,
      "structure": 0.10,
      "seasonal_timing": 0.05
    },
    "market_analysis": {
      "avg_price": 4.75,
      "market_saturation": 0.65,
      "growth_trend": 0.12,
      "seasonal_demand": 1.05,
      "competition_level": 0.72
    },
    "confidence_score": 0.85,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### Sales Analysis Request
```json
{
  "book_ids": ["book_1", "book_2", "book_3"],
  "analysis_type": "performance",
  "time_period": "last_90_days",
  "include_benchmarks": true,
  "user_id": "user123"
}
```

### Sales Analysis Response
```json
{
  "request_id": "analysis-uuid",
  "result": {
    "analysis_type": "performance",
    "time_period": "last_90_days",
    "books_analyzed": 3,
    "performance_summary": {
      "average_performance": 0.73,
      "top_performer": "book_1",
      "total_revenue": 2250.00,
      "total_units": 450
    },
    "comparative_analysis": {
      "best_vs_worst_ratio": 2.4,
      "performance_variance": 0.18,
      "category_breakdown": {
        "fiction": {"avg_score": 0.78, "books": 2},
        "non-fiction": {"avg_score": 0.65, "books": 1}
      }
    },
    "benchmarks": {
      "industry_average": 0.58,
      "peer_group_average": 0.67,
      "top_quartile_threshold": 0.85
    },
    "recommendations": [
      {
        "type": "optimization",
        "description": "Focus marketing efforts on fiction titles"
      },
      {
        "type": "improvement",
        "description": "Review non-fiction content strategy"
      }
    ],
    "risk_factors": [
      "Declining performance in non-fiction category",
      "Increased market competition"
    ],
    "opportunities": [
      "Strong fiction performance suggests expansion potential",
      "Underperforming titles have optimization opportunities"
    ],
    "confidence_score": 0.91,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVICE_HOST` | Service host address | `0.0.0.0` |
| `SERVICE_PORT` | Service port | `8084` |
| `PREFERRED_MODEL` | AI model preference | `openai` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `ANTHROPIC_API_KEY` | Anthropic API key | Optional |
| `EVENT_BUS_URL` | Event Bus service URL | `http://event-bus:8080` |
| `SERVICE_DISCOVERY_URL` | Service Discovery URL | `http://service-discovery:8070` |
| `API_KEY` | Service API key | Required |

### Analysis Capabilities

- **Date Ranges**: last_7_days, last_30_days, last_90_days, custom ranges
- **Analysis Types**: performance, comparative, trend, benchmark
- **Prediction Horizons**: 30-day, 90-day, seasonal forecasts
- **Risk Levels**: low, medium, high with detailed mitigation strategies
- **Pricing Strategies**: premium, competitive, penetration

## Development

### Local Development

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export API_KEY="your-service-api-key"
   ```

3. **Run Service**
   ```bash
   cd src
   python main.py
   ```

### Testing

Run all tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=src --cov-report=html
```

Run specific test category:
```bash
pytest tests/test_main_simple.py
pytest tests/test_sales_agent.py
pytest tests/test_sales_predictor.py
```

### Docker Development

1. **Build Image**
   ```bash
   docker build -t sales-monitor-service .
   ```

2. **Run Container**
   ```bash
   docker run -p 8084:8084 \
     -e OPENAI_API_KEY="your-key" \
     -e API_KEY="your-api-key" \
     sales-monitor-service
   ```

## Architecture

### Components

- **Sales Monitor Agent**: AI-powered sales monitoring and performance analysis
- **Sales Predictor**: ML-based prediction engine with multiple models
- **Event Client**: Communication with Event Bus service
- **Service Registry Client**: Registration with Service Discovery
- **Security Manager**: API key authentication and mTLS
- **Monitoring**: Comprehensive Prometheus metrics and health checks

### Dependencies

- **Infrastructure Services**: Event Bus, Service Discovery
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **ML Libraries**: scikit-learn, numpy, joblib
- **Security**: mTLS certificates, API keys
- **Storage**: Persistent volume for ML models
- **Monitoring**: Prometheus metrics collection

### Data Flow

1. Client sends monitoring/prediction request with authentication
2. Service validates request and API key
3. Sales Monitor Agent or Predictor processes request
4. ML models provide predictions and risk assessment
5. AI generates insights and recommendations
6. Results published to Event Bus (async mode)
7. Metrics recorded for monitoring
8. Response returned to client

## Machine Learning Models

### Prediction Models
- **Sales Model**: Random Forest Regressor for sales volume prediction
- **Revenue Model**: Random Forest Regressor for revenue forecasting
- **Success Model**: Random Forest Classifier for success probability

### Features Used
- Content quality metrics (readability, structure, actionable content)
- Market conditions (competition, seasonality, trends)
- Pricing factors (price vs market, elasticity)
- Book characteristics (word count, genre, title appeal)

### Model Training
```bash
# Train models with current data
curl -X POST http://localhost:8084/train-models \
  -H "Authorization: Bearer your-api-key"

# Retrain models (force refresh)
curl -X POST http://localhost:8084/train-models?retrain=true \
  -H "Authorization: Bearer your-api-key"
```

## Deployment

### Docker Compose
```yaml
services:
  sales-monitor-service:
    build: .
    ports:
      - "8084:8084"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${SALES_MONITOR_SERVICE_API_KEY}
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
      - sales_models:/app/storage
    networks:
      - publish-ai-network

volumes:
  sales_models:
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sales-monitor-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sales-monitor-service
  template:
    metadata:
      labels:
        app: sales-monitor-service
        component: microservice
        tier: tier-2
    spec:
      containers:
      - name: sales-monitor-service
        image: sales-monitor-service:latest
        ports:
        - containerPort: 8084
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        volumeMounts:
        - name: model-storage
          mountPath: /app/storage
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: sales-models-pvc
```

## Security

### Authentication
- API key authentication for all endpoints
- Service-to-service authentication via mTLS
- Security headers and CORS configuration

### Data Protection
- Encrypted inter-service communication
- Secure model storage with persistent volumes
- Row-level security for sensitive analytics data

### Network Security
- Kubernetes NetworkPolicies for micro-segmentation
- Zero Trust networking principles
- Firewall rules for service isolation

## Monitoring

### Metrics
- Sales monitoring operation metrics (count, duration, success rate)
- Prediction accuracy and confidence distributions
- Model training metrics (duration, data size, performance)
- Business KPIs (revenue tracking, performance trends)
- Agent and predictor health status
- Event publication counts and errors

### Health Checks
- `/health` - Comprehensive health status including ML models
- `/ready` - Readiness for traffic including model availability
- Service Discovery integration with automatic registration

### Logging
- Structured logging with correlation IDs
- Model training and prediction audit logs
- Business event tracking for analytics
- Error tracking and alerting

## Testing

### Test Coverage
- Unit tests for all components (19 tests)
- Integration tests for API endpoints (13 tests)
- ML model testing and validation (19 tests)
- Performance and load testing capabilities

### Test Categories
- **Unit Tests**: Component-level testing (Sales Agent: 19 tests, Predictor: 19 tests)
- **Integration Tests**: API endpoint testing (13 tests)
- **ML Tests**: Model validation and feature testing
- **Contract Tests**: Service interface testing

**Total Test Coverage**: 51/51 tests passing (100%)

## Migration Status

✅ **Phase 2.2: Sales Monitor Service Migration - COMPLETED**

- [x] Dual-component architecture (monitoring + prediction)
- [x] AI-powered sales monitoring with PydanticAI
- [x] ML-based prediction engine with scikit-learn
- [x] Event-driven communication integration
- [x] Service Discovery registration
- [x] Security implementation (API keys + mTLS)
- [x] Comprehensive testing suite (51 tests)
- [x] Docker containerization with persistent storage
- [x] Kubernetes deployment manifests
- [x] Monitoring and metrics
- [x] Documentation and deployment guides

**Service Metrics**: 51/51 tests passing, 9 API endpoints, ML-enabled predictions

## Sales Intelligence Features

### Monitoring Capabilities
1. **Real-time Sales Tracking**: Live performance monitoring with trend analysis
2. **Multi-book Analysis**: Comparative performance across book portfolios
3. **Revenue Analytics**: Detailed revenue breakdowns and growth metrics
4. **Customer Insights**: Page read analysis and engagement metrics
5. **Performance Benchmarking**: Industry and peer group comparisons

### Prediction Features
1. **Sales Forecasting**: 30-day and 90-day sales predictions
2. **Revenue Projections**: Accurate revenue forecasting with confidence intervals
3. **Success Probability**: ML-based success likelihood assessment
4. **Risk Assessment**: Comprehensive risk analysis with mitigation strategies
5. **Price Optimization**: Data-driven pricing recommendations

### AI-Powered Insights
1. **Performance Analysis**: Deep dive into sales drivers and patterns
2. **Market Intelligence**: Competitive analysis and positioning insights
3. **Strategic Recommendations**: Actionable business optimization suggestions
4. **Trend Identification**: Early detection of performance patterns
5. **Opportunity Mapping**: Revenue optimization and growth opportunities

## Troubleshooting

### Common Issues

1. **ML Models Not Available**
   - Check if models are trained: `curl http://localhost:8084/train-models`
   - Verify sufficient training data exists
   - Check storage permissions for model persistence

2. **Prediction Accuracy Issues**
   - Retrain models with recent data
   - Verify feature quality and completeness
   - Check for data drift in input features

3. **Service Registration Failed**
   - Verify Service Discovery is running
   - Check network connectivity and API keys
   - Validate service health endpoints

### Debug Commands

```bash
# Check service health
curl http://localhost:8084/health

# Verify readiness
curl http://localhost:8084/ready

# Get metrics
curl http://localhost:8084/metrics

# Test sales monitoring
curl -X POST http://localhost:8084/monitor/sync \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "date_range": "last_30_days",
    "include_page_reads": true,
    "generate_insights": true
  }'

# Test sales prediction
curl -X POST http://localhost:8084/predict \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "book_id": "test_book",
    "target_price": 4.99,
    "market_analysis": true
  }'

# Train ML models
curl -X POST http://localhost:8084/train-models \
  -H "Authorization: Bearer your-api-key"
```

## Performance Optimization

### Model Training
- Automatic model retraining based on data volume thresholds
- Incremental learning for continuous improvement
- Feature importance tracking for model interpretation
- Performance monitoring with accuracy metrics

### Caching Strategy
- Prediction result caching for frequently requested books
- Market analysis caching with time-based invalidation
- Model artifact caching for fast startup times

### Scaling Considerations
- Horizontal scaling with stateless prediction services
- Persistent storage for ML models and training data
- Background job processing for model training
- Resource allocation based on prediction workload