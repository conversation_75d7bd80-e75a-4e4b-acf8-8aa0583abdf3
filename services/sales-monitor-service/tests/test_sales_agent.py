"""
Tests for Sales Monitor Agent
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.sales_agent import SalesMonitorAgent, SalesReportResult, SalesAnalysisResult


class TestSalesMonitorAgent:
    """Test cases for Sales Monitor Agent"""
    
    def test_sales_agent_initialization(self):
        """Test sales agent initialization"""
        agent = SalesMonitorAgent()
        
        assert agent._initialized is False
        assert agent._agent is None
        assert agent._analysis_agent is None
        assert agent.is_ready is False
    
    def test_get_available_model_openai(self):
        """Test getting OpenAI model"""
        agent = SalesMonitorAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'openai',
            'OPENAI_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_anthropic(self):
        """Test getting Anthropic model"""
        agent = SalesMonitorAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'anthropic',
            'ANTHROPIC_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_none(self):
        """Test when no model is available"""
        agent = SalesMonitorAgent()
        
        with patch.dict('os.environ', {}, clear=True):
            model = agent._get_available_model()
            assert model is None
    
    def test_monitoring_prompt_content(self):
        """Test monitoring prompt contains required elements"""
        agent = SalesMonitorAgent()
        prompt = agent._get_monitoring_prompt()
        
        assert "sales analyst" in prompt.lower()
        assert "performance metrics" in prompt.lower()
        assert "trend analysis" in prompt.lower()
        assert "revenue optimization" in prompt.lower()
    
    def test_analysis_prompt_content(self):
        """Test analysis prompt contains required elements"""
        agent = SalesMonitorAgent()
        prompt = agent._get_analysis_prompt()
        
        assert "business analyst" in prompt.lower()
        assert "performance analytics" in prompt.lower()
        assert "benchmarking" in prompt.lower()
        assert "strategic" in prompt.lower()
    
    def test_format_sales_data(self):
        """Test sales data formatting"""
        agent = SalesMonitorAgent()
        
        sales_data = {
            "period": {
                "start_date": "2024-01-01T00:00:00Z",
                "end_date": "2024-01-31T23:59:59Z"
            },
            "totals": {
                "total_revenue": 1250.50,
                "total_units": 250,
                "total_page_reads": 5000
            },
            "books": [
                {
                    "title": "Test Book 1",
                    "units_sold": 150,
                    "revenue": 750.00
                },
                {
                    "title": "Test Book 2", 
                    "units_sold": 100,
                    "revenue": 500.50
                }
            ]
        }
        
        formatted = agent._format_sales_data(sales_data)
        
        assert "2024-01-01T00:00:00Z" in formatted
        assert "$1250.50" in formatted
        assert "250" in formatted
        assert "Test Book 1" in formatted
        assert "Test Book 2" in formatted
    
    def test_format_sales_data_empty(self):
        """Test empty sales data formatting"""
        agent = SalesMonitorAgent()
        
        formatted = agent._format_sales_data({})
        assert "No sales data available" in formatted
    
    def test_format_benchmarks(self):
        """Test benchmark data formatting"""
        agent = SalesMonitorAgent()
        
        benchmarks = {
            "average_monthly_sales": 25,
            "average_revenue_per_book": 125.00,
            "industry_growth_rate": 0.15
        }
        
        formatted = agent._format_benchmarks(benchmarks)
        
        assert "average_monthly_sales: 25" in formatted
        assert "average_revenue_per_book: 125.0" in formatted
        assert "industry_growth_rate: 0.15" in formatted
    
    def test_format_benchmarks_empty(self):
        """Test empty benchmarks formatting"""
        agent = SalesMonitorAgent()
        
        formatted = agent._format_benchmarks({})
        assert "No benchmark data available" in formatted
    
    @pytest.mark.asyncio
    async def test_monitor_sales_not_initialized(self):
        """Test sales monitoring when agent not initialized"""
        agent = SalesMonitorAgent()
        
        with pytest.raises(RuntimeError, match="Sales monitor agent not initialized"):
            await agent.monitor_sales(
                date_range="last_30_days",
                include_page_reads=True
            )
    
    @pytest.mark.asyncio
    async def test_analyze_sales_performance_not_initialized(self):
        """Test sales analysis when agent not initialized"""
        agent = SalesMonitorAgent()
        
        with pytest.raises(RuntimeError, match="Sales analysis agent not initialized"):
            await agent.analyze_sales_performance(
                book_ids=["book1", "book2"],
                analysis_type="performance"
            )
    
    @pytest.mark.asyncio
    async def test_get_performance_insights(self):
        """Test getting performance insights"""
        agent = SalesMonitorAgent()
        
        performance_data = {
            "total_revenue": 1500.00,
            "total_units_sold": 300,
            "growth_rate": 0.25,
            "seasonal_factor": 1.3,
            "market_share": 0.12
        }
        
        insights = await agent.get_performance_insights(performance_data)
        
        assert isinstance(insights, list)
        assert len(insights) > 0
        # Should include price insight
        assert any("$5.00" in insight for insight in insights)
        # Should include growth insight
        assert any("strong growth" in insight.lower() for insight in insights)
        # Should include seasonal insight
        assert any("seasonal demand" in insight.lower() for insight in insights)
        # Should include market position insight
        assert any("market position" in insight.lower() for insight in insights)
    
    @pytest.mark.asyncio
    async def test_get_performance_insights_declining(self):
        """Test performance insights for declining performance"""
        agent = SalesMonitorAgent()
        
        performance_data = {
            "total_revenue": 100.00,
            "total_units_sold": 50,
            "growth_rate": -0.15,
            "seasonal_factor": 0.7,
            "market_share": 0.03
        }
        
        insights = await agent.get_performance_insights(performance_data)
        
        assert isinstance(insights, list)
        assert len(insights) > 0
        # Should include declining performance insight
        assert any("declining performance" in insight.lower() for insight in insights)
        # Should include seasonal factor insight
        assert any("low-demand season" in insight.lower() for insight in insights)
        # Should include market presence insight
        assert any("limited market presence" in insight.lower() for insight in insights)
    
    @pytest.mark.asyncio
    async def test_get_sales_data_mock(self):
        """Test getting sales data with mock implementation"""
        agent = SalesMonitorAgent()
        
        sales_data = await agent._get_sales_data("last_30_days", None, True)
        
        assert isinstance(sales_data, dict)
        assert "period" in sales_data
        assert "books" in sales_data
        assert "totals" in sales_data
        
        # Check period structure
        period = sales_data["period"]
        assert "start_date" in period
        assert "end_date" in period
        assert "days" in period
        
        # Check totals structure
        totals = sales_data["totals"]
        assert "total_revenue" in totals
        assert "total_units" in totals
        assert "total_page_reads" in totals
        
        # Check books structure
        books = sales_data["books"]
        assert len(books) > 0
        for book in books:
            assert "book_id" in book
            assert "title" in book
            assert "units_sold" in book
            assert "revenue" in book
    
    @pytest.mark.asyncio
    async def test_get_benchmarks_mock(self):
        """Test getting benchmarks with mock implementation"""
        agent = SalesMonitorAgent()
        
        benchmarks = await agent._get_benchmarks()
        
        assert isinstance(benchmarks, dict)
        assert "average_monthly_sales" in benchmarks
        assert "average_revenue_per_book" in benchmarks
        assert "average_price" in benchmarks
        assert "industry_growth_rate" in benchmarks
        assert "top_quartile_performance" in benchmarks
        
        # Check top quartile structure
        top_quartile = benchmarks["top_quartile_performance"]
        assert "monthly_sales" in top_quartile
        assert "revenue" in top_quartile
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test agent cleanup"""
        agent = SalesMonitorAgent()
        agent._initialized = True  # Simulate initialized state
        
        await agent.cleanup()
        
        assert agent._initialized is False
        assert agent._agent is None
        assert agent._analysis_agent is None
    
    def test_sales_report_result_model(self):
        """Test SalesReportResult model"""
        result = SalesReportResult(
            reporting_period="last_30_days",
            books_data=[
                {
                    "book_id": "book1",
                    "title": "Test Book",
                    "units_sold": 50,
                    "revenue": 250.00
                }
            ],
            performance_metrics={
                "total_revenue": 250.00,
                "growth_rate": 0.15
            },
            insights={
                "key_insights": ["Strong performance in fiction category"],
                "recommendations": ["Consider expanding marketing"]
            },
            total_revenue=250.00,
            total_units_sold=50,
            average_price=5.00,
            top_performers=[
                {
                    "book_id": "book1",
                    "performance_score": 0.85
                }
            ],
            growth_metrics={
                "monthly_growth": 0.15,
                "year_over_year": 0.25
            },
            timestamp="2024-01-01T00:00:00Z"
        )
        
        assert result.reporting_period == "last_30_days"
        assert len(result.books_data) == 1
        assert result.total_revenue == 250.00
        assert result.total_units_sold == 50
        assert result.average_price == 5.00
        assert len(result.top_performers) == 1
        assert "total_revenue" in result.performance_metrics
        assert "key_insights" in result.insights
    
    def test_sales_analysis_result_model(self):
        """Test SalesAnalysisResult model"""
        analysis = SalesAnalysisResult(
            analysis_type="performance",
            time_period="last_90_days",
            books_analyzed=5,
            performance_summary={
                "average_performance": 0.75,
                "top_performer": "book1"
            },
            comparative_analysis={
                "best_vs_worst": 0.6,
                "category_comparison": {"fiction": 0.8, "non-fiction": 0.7}
            },
            benchmarks={
                "industry_average": 0.65,
                "peer_group_average": 0.70
            },
            recommendations=[
                {
                    "type": "optimization",
                    "description": "Focus on high-performing categories"
                }
            ],
            risk_factors=["Market saturation in primary category"],
            opportunities=["Expansion into emerging genres"],
            confidence_score=0.87,
            timestamp="2024-01-01T00:00:00Z"
        )
        
        assert analysis.analysis_type == "performance"
        assert analysis.time_period == "last_90_days"
        assert analysis.books_analyzed == 5
        assert "average_performance" in analysis.performance_summary
        assert "best_vs_worst" in analysis.comparative_analysis
        assert "industry_average" in analysis.benchmarks
        assert len(analysis.recommendations) == 1
        assert len(analysis.risk_factors) == 1
        assert len(analysis.opportunities) == 1
        assert analysis.confidence_score == 0.87