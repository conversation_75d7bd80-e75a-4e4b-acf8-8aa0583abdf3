"""
Tests for Sales Predictor
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.sales_predictor import SalesPredictor, SalesPredictionResult


class TestSalesPredictor:
    """Test cases for Sales Predictor"""
    
    def test_sales_predictor_initialization(self):
        """Test sales predictor initialization"""
        predictor = SalesPredictor()
        
        assert predictor._initialized is False
        assert predictor.sales_model is None
        assert predictor.revenue_model is None
        assert predictor.success_model is None
        assert predictor.is_ready is False
    
    def test_feature_names_initialization(self):
        """Test feature names are properly initialized"""
        predictor = SalesPredictor()
        
        # After initialization, feature names should be set
        expected_features = [
            'word_count_normalized', 'avg_sentence_length', 'vocabulary_richness',
            'reading_ease', 'grade_level', 'title_power_words', 'actionable_content_score',
            'structure_completeness', 'competition_density', 'market_opportunity_score',
            'current_seasonal_factor', 'target_price', 'price_vs_market'
        ]
        
        # Feature names might be empty initially
        assert isinstance(predictor.feature_names, list)
    
    @pytest.mark.asyncio
    async def test_predict_book_performance_not_initialized(self):
        """Test prediction when predictor not initialized"""
        predictor = SalesPredictor()
        
        with pytest.raises(RuntimeError, match="Sales predictor not initialized"):
            await predictor.predict_book_performance(
                book_id="test_book",
                target_price=4.99
            )
    
    @pytest.mark.asyncio
    async def test_get_book_data_mock(self):
        """Test getting book data with mock implementation"""
        predictor = SalesPredictor()
        
        book_data = await predictor._get_book_data("test_book")
        
        assert isinstance(book_data, dict)
        assert "id" in book_data
        assert "title" in book_data
        assert "author" in book_data
        assert "category" in book_data
        assert "word_count" in book_data
        assert "price" in book_data
        assert "keywords" in book_data
        
        assert book_data["id"] == "test_book"
        assert isinstance(book_data["title"], str)
        assert isinstance(book_data["word_count"], int)
        assert isinstance(book_data["price"], float)
        assert isinstance(book_data["keywords"], list)
    
    @pytest.mark.asyncio
    async def test_extract_features(self):
        """Test feature extraction from book data"""
        predictor = SalesPredictor()
        
        book_data = {
            "word_count": 50000,
            "title": "Test Book",
            "category": "self-help"
        }
        
        features = await predictor._extract_features(book_data, 4.99)
        
        assert isinstance(features, dict)
        assert "word_count_normalized" in features
        assert "reading_ease" in features
        assert "target_price" in features
        assert "price_vs_market" in features
        
        # Check feature value ranges
        assert 0 <= features["word_count_normalized"] <= 1
        assert 0 <= features["reading_ease"] <= 1
        assert features["target_price"] == 4.99
        assert features["price_vs_market"] == 1.0  # 4.99 / 4.99
    
    @pytest.mark.asyncio
    async def test_analyze_market_conditions(self):
        """Test market conditions analysis"""
        predictor = SalesPredictor()
        
        market_data = await predictor._analyze_market_conditions("self-help")
        
        assert isinstance(market_data, dict)
        assert "avg_price" in market_data
        assert "market_saturation" in market_data
        assert "growth_trend" in market_data
        assert "seasonal_demand" in market_data
        assert "competition_level" in market_data
        
        # Check reasonable value ranges
        assert market_data["avg_price"] > 0
        assert 0 <= market_data["market_saturation"] <= 1
        assert market_data["seasonal_demand"] > 0
    
    @pytest.mark.asyncio
    async def test_heuristic_predictions(self):
        """Test heuristic predictions when ML models not available"""
        predictor = SalesPredictor()
        
        features = {
            'actionable_content_score': 0.8,
            'title_power_words': 0.7,
            'market_opportunity_score': 0.6,
            'reading_ease': 0.7,
            'structure_completeness': 0.8,
            'current_seasonal_factor': 1.2,
            'competition_density': 0.4,
            'target_price': 4.99
        }
        
        predictions = await predictor._heuristic_predictions(features)
        
        assert isinstance(predictions, dict)
        assert "sales_30d" in predictions
        assert "revenue_30d" in predictions
        assert "sales_90d" in predictions
        assert "revenue_90d" in predictions
        assert "success_probability" in predictions
        assert "confidence" in predictions
        assert "peak_sales_period" in predictions
        assert "roi_projection" in predictions
        
        # Check value types and ranges
        assert isinstance(predictions["sales_30d"], int)
        assert isinstance(predictions["revenue_30d"], float)
        assert 0 <= predictions["success_probability"] <= 1
        assert 0 <= predictions["confidence"] <= 1
        assert predictions["sales_30d"] >= 0
        assert predictions["revenue_30d"] >= 0
    
    @pytest.mark.asyncio
    async def test_assess_risks(self):
        """Test risk assessment"""
        predictor = SalesPredictor()
        
        features = {
            'competition_density': 0.8,  # High competition
            'market_saturation': 0.9,   # High saturation
            'reading_ease': 0.3,        # Low readability
            'title_power_words': 0.2,   # Low title appeal
            'current_seasonal_factor': 0.7,  # Low season
            'price_vs_market': 1.6      # High price
        }
        
        predictions = {'success_probability': 0.5}
        
        risk_assessment = await predictor._assess_risks(features, predictions)
        
        assert isinstance(risk_assessment, dict)
        assert "risk_level" in risk_assessment
        assert "risk_score" in risk_assessment
        assert "identified_risks" in risk_assessment
        assert "mitigation_strategies" in risk_assessment
        
        # Should identify multiple risks
        assert len(risk_assessment["identified_risks"]) > 0
        assert len(risk_assessment["mitigation_strategies"]) > 0
        
        # Risk level should be high given the features
        assert risk_assessment["risk_level"] in ["low", "medium", "high"]
        assert 0 <= risk_assessment["risk_score"] <= 1
    
    def test_generate_mitigation_strategies(self):
        """Test mitigation strategy generation"""
        predictor = SalesPredictor()
        
        risks = [
            "High competition in category",
            "Market appears saturated",
            "Content may be too difficult to read",
            "Title may lack market appeal",
            "Launching in low-demand season",
            "Price significantly above market average"
        ]
        
        strategies = predictor._generate_mitigation_strategies(risks)
        
        assert isinstance(strategies, list)
        assert len(strategies) > 0
        
        # Should provide specific strategies for each risk type
        strategy_text = " ".join(strategies).lower()
        assert "unique positioning" in strategy_text or "niche targeting" in strategy_text
        assert "unique angle" in strategy_text or "subtopic" in strategy_text
        assert "simplify language" in strategy_text or "examples" in strategy_text
        assert "title" in strategy_text or "power words" in strategy_text
    
    @pytest.mark.asyncio
    async def test_generate_recommendations(self):
        """Test recommendation generation"""
        predictor = SalesPredictor()
        
        features = {
            'actionable_content_score': 0.4,  # Low actionable content
            'title_power_words': 0.3,         # Low title appeal
            'current_seasonal_factor': 0.7    # Low season
        }
        
        predictions = {'success_probability': 0.5}
        
        recommendations = await predictor._generate_recommendations(features, predictions)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Each recommendation should have proper structure
        for rec in recommendations:
            assert "type" in rec
            assert "priority" in rec
            assert "title" in rec
            assert "description" in rec
            assert rec["priority"] in ["high", "medium", "low"]
    
    @pytest.mark.asyncio
    async def test_optimize_pricing(self):
        """Test pricing optimization"""
        predictor = SalesPredictor()
        
        features = {
            'actionable_content_score': 0.8,
            'reading_ease': 0.7,
            'structure_completeness': 0.9,
            'avg_price': 4.99
        }
        
        optimization = await predictor._optimize_pricing(features, "self-help")
        
        assert isinstance(optimization, dict)
        assert "recommended_price" in optimization
        assert "market_average" in optimization
        assert "optimal_range" in optimization
        assert "price_elasticity" in optimization
        assert "pricing_strategy" in optimization
        assert "test_prices" in optimization
        
        # Check value ranges and types
        assert optimization["recommended_price"] > 0
        assert len(optimization["test_prices"]) == 3
        assert optimization["pricing_strategy"] in ["premium", "competitive", "penetration"]
        
        # Optimal range should have min and max
        assert "min" in optimization["optimal_range"]
        assert "max" in optimization["optimal_range"]
    
    def test_suggest_pricing_strategy(self):
        """Test pricing strategy suggestion"""
        predictor = SalesPredictor()
        
        # Test premium strategy
        strategy = predictor._suggest_pricing_strategy(0.9)
        assert strategy == "premium"
        
        # Test competitive strategy
        strategy = predictor._suggest_pricing_strategy(0.7)
        assert strategy == "competitive"
        
        # Test penetration strategy
        strategy = predictor._suggest_pricing_strategy(0.4)
        assert strategy == "penetration"
    
    def test_prepare_feature_vector(self):
        """Test feature vector preparation"""
        predictor = SalesPredictor()
        
        # Set feature names
        predictor.feature_names = [
            'word_count_normalized', 'reading_ease', 'target_price'
        ]
        
        features = {
            'word_count_normalized': 0.5,
            'reading_ease': 0.7,
            'target_price': 4.99,
            'extra_feature': 0.8  # Should be ignored
        }
        
        vector = predictor._prepare_feature_vector(features)
        
        assert isinstance(vector, list)
        assert len(vector) == 3  # Only features in feature_names
        assert vector[0] == 0.5
        assert vector[1] == 0.7
        assert vector[2] == 4.99
    
    def test_calculate_prediction_confidence(self):
        """Test prediction confidence calculation"""
        predictor = SalesPredictor()
        
        features = {
            'actionable_content_score': 0.8,
            'reading_ease': 0.7,
            'structure_completeness': 0.9,
            'market_opportunity_score': 0.6
        }
        
        confidence = predictor._calculate_prediction_confidence(features)
        
        assert isinstance(confidence, float)
        assert 0 <= confidence <= 1
    
    def test_predict_peak_period(self):
        """Test peak period prediction"""
        predictor = SalesPredictor()
        
        # High season
        peak = predictor._predict_peak_period({'current_seasonal_factor': 1.3})
        assert peak == "immediate"
        
        # Normal season
        peak = predictor._predict_peak_period({'current_seasonal_factor': 1.0})
        assert peak == "first_week"
        
        # Low season
        peak = predictor._predict_peak_period({'current_seasonal_factor': 0.8})
        assert peak == "within_month"
    
    @pytest.mark.asyncio
    async def test_get_feature_importance_no_model(self):
        """Test feature importance when no ML model available"""
        predictor = SalesPredictor()
        
        features = {'test_feature': 0.5}
        importance = await predictor._get_feature_importance(features)
        
        assert isinstance(importance, dict)
        assert "content_quality" in importance
        assert "market_conditions" in importance
        assert "title_appeal" in importance
        assert "pricing" in importance
        
        # Should sum to approximately 1.0
        total_importance = sum(importance.values())
        assert abs(total_importance - 1.0) < 0.01
    
    @pytest.mark.asyncio
    async def test_train_models_insufficient_data(self):
        """Test model training with insufficient data"""
        predictor = SalesPredictor()
        predictor._initialized = True  # Simulate initialized state
        
        # Mock insufficient training data
        with patch.object(predictor, '_get_training_data', return_value=[]):
            result = await predictor.train_models()
            
            assert result["status"] == "insufficient_data"
            assert result["samples"] == 0
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test predictor cleanup"""
        predictor = SalesPredictor()
        predictor._initialized = True  # Simulate initialized state
        
        await predictor.cleanup()
        
        assert predictor._initialized is False
        assert predictor.sales_model is None
        assert predictor.revenue_model is None
        assert predictor.success_model is None
    
    def test_sales_prediction_result_model(self):
        """Test SalesPredictionResult model"""
        result = SalesPredictionResult(
            book_id="test_book",
            predictions={
                "sales_30d": 50,
                "revenue_30d": 249.50,
                "success_probability": 0.75
            },
            risk_assessment={
                "risk_level": "medium",
                "risk_score": 0.4,
                "identified_risks": ["High competition"]
            },
            recommendations=[
                {
                    "type": "content",
                    "priority": "high",
                    "title": "Improve Content",
                    "description": "Add more examples"
                }
            ],
            price_optimization={
                "recommended_price": 4.99,
                "pricing_strategy": "competitive"
            },
            feature_importance={
                "content_quality": 0.3,
                "market_conditions": 0.25
            },
            market_analysis={
                "avg_price": 4.99,
                "competition_level": 0.7
            },
            confidence_score=0.82,
            timestamp="2024-01-01T00:00:00Z"
        )
        
        assert result.book_id == "test_book"
        assert "sales_30d" in result.predictions
        assert "risk_level" in result.risk_assessment
        assert len(result.recommendations) == 1
        assert "recommended_price" in result.price_optimization
        assert "content_quality" in result.feature_importance
        assert "avg_price" in result.market_analysis
        assert result.confidence_score == 0.82