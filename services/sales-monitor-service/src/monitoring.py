"""
Monitoring and Metrics for Sales Monitor Service

Provides Prometheus metrics collection and health monitoring
specific to sales monitoring, analytics, and prediction operations.
"""

import logging
from typing import Optional

from fastapi import FastAPI

logger = logging.getLogger(__name__)

# Mock metrics classes when prometheus_client is not available
try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, make_asgi_app
    PROMETHEUS_AVAILABLE = True
except ImportError:
    logger.warning("prometheus_client not available, using mock metrics")
    Counter = Histogram = Gauge = CollectorRegistry = object
    PROMETHEUS_AVAILABLE = False
    
    def make_asgi_app(*args, **kwargs):
        """Mock ASGI app when prometheus not available"""
        async def mock_app(scope, receive, send):
            await send({
                'type': 'http.response.start',
                'status': 200,
                'headers': [[b'content-type', b'text/plain']],
            })
            await send({
                'type': 'http.response.body',
                'body': b'Metrics not available',
            })
        return mock_app

class SalesMonitorMetrics:
    """Metrics collection for Sales Monitor Service operations"""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or (CollectorRegistry() if PROMETHEUS_AVAILABLE else None)
        
        if PROMETHEUS_AVAILABLE and self.registry:
            # Sales monitoring operation metrics
            self.SALES_MONITOR_COUNT = Counter(
                'sales_monitor_total',
                'Total number of sales monitoring requests',
                ['date_range', 'user_type'],
                registry=self.registry
            )
            
            self.SALES_MONITOR_DURATION = Histogram(
                'sales_monitor_duration_seconds',
                'Time spent on sales monitoring operations',
                ['date_range', 'operation_type'],
                registry=self.registry
            )
            
            self.SALES_MONITOR_SUCCESS_COUNT = Counter(
                'sales_monitor_success_total',
                'Total number of successful sales monitoring operations',
                ['date_range'],
                registry=self.registry
            )
            
            self.SALES_MONITOR_ERROR_COUNT = Counter(
                'sales_monitor_error_total',
                'Total number of sales monitoring errors',
                ['error_type'],
                registry=self.registry
            )
            
            # Sales prediction metrics
            self.SALES_PREDICTION_COUNT = Counter(
                'sales_prediction_total',
                'Total number of sales prediction requests',
                ['book_category', 'prediction_type'],
                registry=self.registry
            )
            
            self.SALES_PREDICTION_DURATION = Histogram(
                'sales_prediction_duration_seconds',
                'Time spent on sales prediction operations',
                ['prediction_type'],
                registry=self.registry
            )
            
            self.SALES_PREDICTION_SUCCESS_COUNT = Counter(
                'sales_prediction_success_total',
                'Total number of successful sales predictions',
                ['book_category'],
                registry=self.registry
            )
            
            self.SALES_PREDICTION_ERROR_COUNT = Counter(
                'sales_prediction_error_total',
                'Total number of sales prediction errors',
                ['error_type'],
                registry=self.registry
            )
            
            # Sales analysis metrics
            self.SALES_ANALYSIS_COUNT = Counter(
                'sales_analysis_total',
                'Total number of sales analysis requests',
                ['analysis_type', 'time_period'],
                registry=self.registry
            )
            
            self.SALES_ANALYSIS_DURATION = Histogram(
                'sales_analysis_duration_seconds',
                'Time spent on sales analysis operations',
                ['analysis_type'],
                registry=self.registry
            )
            
            self.SALES_ANALYSIS_SUCCESS_COUNT = Counter(
                'sales_analysis_success_total',
                'Total number of successful sales analyses',
                ['analysis_type'],
                registry=self.registry
            )
            
            self.SALES_ANALYSIS_ERROR_COUNT = Counter(
                'sales_analysis_error_total',
                'Total number of sales analysis errors',
                ['error_type'],
                registry=self.registry
            )
            
            # Performance quality metrics
            self.PREDICTION_ACCURACY_HISTOGRAM = Histogram(
                'prediction_accuracy_distribution',
                'Distribution of prediction accuracy scores',
                ['prediction_type'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                registry=self.registry
            )
            
            self.CONFIDENCE_SCORE_HISTOGRAM = Histogram(
                'confidence_score_distribution',
                'Distribution of prediction confidence scores',
                ['operation_type'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                registry=self.registry
            )
            
            # Model training metrics
            self.MODEL_TRAINING_COUNT = Counter(
                'model_training_total',
                'Total number of model training operations',
                ['model_type'],
                registry=self.registry
            )
            
            self.MODEL_TRAINING_DURATION = Histogram(
                'model_training_duration_seconds',
                'Time spent on model training operations',
                ['model_type'],
                registry=self.registry
            )
            
            self.TRAINING_DATA_SIZE = Histogram(
                'training_data_size_samples',
                'Number of samples used for model training',
                ['model_type'],
                buckets=[10, 50, 100, 500, 1000, 5000, 10000],
                registry=self.registry
            )
            
            # Service health metrics
            self.AGENT_HEALTH = Gauge(
                'sales_agent_health',
                'Sales monitor agent health status (1=healthy, 0=unhealthy)',
                registry=self.registry
            )
            
            self.PREDICTOR_HEALTH = Gauge(
                'sales_predictor_health',
                'Sales predictor health status (1=healthy, 0=unhealthy)',
                registry=self.registry
            )
            
            self.EVENT_PUBLISH_COUNT = Counter(
                'event_publish_total',
                'Total number of events published',
                ['event_type'],
                registry=self.registry
            )
            
            self.EVENT_PUBLISH_ERROR_COUNT = Counter(
                'event_publish_error_total',
                'Total number of event publishing errors',
                ['event_type'],
                registry=self.registry
            )
            
            # HTTP metrics
            self.HTTP_REQUEST_COUNT = Counter(
                'http_requests_total',
                'Total number of HTTP requests',
                ['method', 'endpoint', 'status_code'],
                registry=self.registry
            )
            
            self.HTTP_REQUEST_DURATION = Histogram(
                'http_request_duration_seconds',
                'HTTP request duration in seconds',
                ['method', 'endpoint'],
                registry=self.registry
            )
            
        else:
            # Create mock metrics when Prometheus is not available
            self._create_mock_metrics()
    
    def _create_mock_metrics(self):
        """Create mock metrics when Prometheus is not available"""
        class MockMetric:
            def inc(self, *args, **kwargs): pass
            def dec(self, *args, **kwargs): pass
            def set(self, *args, **kwargs): pass
            def observe(self, *args, **kwargs): pass
            def time(self): 
                class MockTimer:
                    def __enter__(self): return self
                    def __exit__(self, *args): pass
                return MockTimer()
        
        self.SALES_MONITOR_COUNT = MockMetric()
        self.SALES_MONITOR_DURATION = MockMetric()
        self.SALES_MONITOR_SUCCESS_COUNT = MockMetric()
        self.SALES_MONITOR_ERROR_COUNT = MockMetric()
        self.SALES_PREDICTION_COUNT = MockMetric()
        self.SALES_PREDICTION_DURATION = MockMetric()
        self.SALES_PREDICTION_SUCCESS_COUNT = MockMetric()
        self.SALES_PREDICTION_ERROR_COUNT = MockMetric()
        self.SALES_ANALYSIS_COUNT = MockMetric()
        self.SALES_ANALYSIS_DURATION = MockMetric()
        self.SALES_ANALYSIS_SUCCESS_COUNT = MockMetric()
        self.SALES_ANALYSIS_ERROR_COUNT = MockMetric()
        self.PREDICTION_ACCURACY_HISTOGRAM = MockMetric()
        self.CONFIDENCE_SCORE_HISTOGRAM = MockMetric()
        self.MODEL_TRAINING_COUNT = MockMetric()
        self.MODEL_TRAINING_DURATION = MockMetric()
        self.TRAINING_DATA_SIZE = MockMetric()
        self.AGENT_HEALTH = MockMetric()
        self.PREDICTOR_HEALTH = MockMetric()
        self.EVENT_PUBLISH_COUNT = MockMetric()
        self.EVENT_PUBLISH_ERROR_COUNT = MockMetric()
        self.HTTP_REQUEST_COUNT = MockMetric()
        self.HTTP_REQUEST_DURATION = MockMetric()

# Global metrics instance
metrics = SalesMonitorMetrics()

def setup_monitoring(app: FastAPI):
    """Setup monitoring and metrics endpoints for the FastAPI application"""
    
    if PROMETHEUS_AVAILABLE and metrics.registry:
        # Add Prometheus metrics endpoint
        metrics_app = make_asgi_app(registry=metrics.registry)
        app.mount("/metrics", metrics_app)
        logger.info("Prometheus metrics endpoint mounted at /metrics")
    else:
        # Add mock metrics endpoint
        @app.get("/metrics")
        async def mock_metrics():
            return "# Metrics not available - prometheus_client not installed"
        logger.info("Mock metrics endpoint available at /metrics")
    
    # Add middleware for HTTP metrics
    if PROMETHEUS_AVAILABLE:
        @app.middleware("http")
        async def metrics_middleware(request, call_next):
            method = request.method
            path = request.url.path
            
            with metrics.HTTP_REQUEST_DURATION.labels(method=method, endpoint=path).time():
                response = await call_next(request)
            
            metrics.HTTP_REQUEST_COUNT.labels(
                method=method,
                endpoint=path,
                status_code=response.status_code
            ).inc()
            
            return response
    
    logger.info("Monitoring setup completed for Sales Monitor Service")

def record_prediction_metrics(prediction_type: str, accuracy: float, confidence: float):
    """Record metrics for a completed prediction"""
    metrics.PREDICTION_ACCURACY_HISTOGRAM.labels(prediction_type=prediction_type).observe(accuracy)
    metrics.CONFIDENCE_SCORE_HISTOGRAM.labels(operation_type="prediction").observe(confidence)

def record_analysis_metrics(analysis_type: str, confidence: float):
    """Record metrics for a completed analysis"""
    metrics.CONFIDENCE_SCORE_HISTOGRAM.labels(operation_type="analysis").observe(confidence)

def record_agent_health(is_healthy: bool):
    """Record agent health status"""
    metrics.AGENT_HEALTH.set(1 if is_healthy else 0)

def record_predictor_health(is_healthy: bool):
    """Record predictor health status"""
    metrics.PREDICTOR_HEALTH.set(1 if is_healthy else 0)

def record_model_training(model_type: str, training_samples: int, duration: float):
    """Record model training metrics"""
    metrics.MODEL_TRAINING_COUNT.labels(model_type=model_type).inc()
    metrics.TRAINING_DATA_SIZE.labels(model_type=model_type).observe(training_samples)

def record_event_publication(event_type: str, success: bool):
    """Record event publication metrics"""
    if success:
        metrics.EVENT_PUBLISH_COUNT.labels(event_type=event_type).inc()
    else:
        metrics.EVENT_PUBLISH_ERROR_COUNT.labels(event_type=event_type).inc()