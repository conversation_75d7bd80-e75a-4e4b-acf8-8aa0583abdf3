"""
Sales Monitor Service - Main FastAPI Application

Provides comprehensive sales monitoring, performance analysis, and predictive analytics
as a microservice using PydanticAI and ML models.
"""

import asyncio
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .sales_agent import SalesMonitorAgent, SalesReportResult, SalesAnalysisResult
from .sales_predictor import SalesPredictor, SalesPredictionResult
from .event_client import EventClient
from .service_registry_client import ServiceRegistryClient
from .security_manager import SecurityManager, verify_api_key
from .monitoring import setup_monitoring, metrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global service instances
sales_agent: Optional[SalesMonitorAgent] = None
sales_predictor: Optional[SalesPredictor] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None

# Request/Response Models
class SalesMonitorRequest(BaseModel):
    date_range: str = Field(default="last_30_days", description="Date range for analysis")
    include_page_reads: bool = Field(default=True, description="Include page read data")
    generate_insights: bool = Field(default=True, description="Generate AI insights")
    books_filter: Optional[List[str]] = Field(default=None, description="Filter by specific book IDs")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class SalesPredictionRequest(BaseModel):
    book_id: str = Field(..., description="Book ID to predict")
    target_price: float = Field(default=4.99, description="Target selling price")
    launch_date: Optional[str] = Field(default=None, description="Planned launch date")
    market_analysis: bool = Field(default=True, description="Include market analysis")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class SalesAnalysisRequest(BaseModel):
    book_ids: List[str] = Field(..., description="List of book IDs to analyze")
    analysis_type: str = Field(default="performance", description="Type of analysis")
    time_period: str = Field(default="last_90_days", description="Analysis time period")
    include_benchmarks: bool = Field(default=True, description="Include industry benchmarks")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class SalesMonitorResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[SalesReportResult] = Field(default=None, description="Sales monitoring result")
    message: str = Field(..., description="Status message")

class SalesPredictionResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    result: SalesPredictionResult = Field(..., description="Sales prediction result")

class SalesAnalysisResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    result: SalesAnalysisResult = Field(..., description="Sales analysis result")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service health status")
    timestamp: str = Field(..., description="Health check timestamp")
    services: Dict[str, str] = Field(..., description="Dependent service status")

# Store for async requests
async_requests: Dict[str, Dict[str, Any]] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    global sales_agent, sales_predictor, event_client, service_registry, security_manager
    
    logger.info("Starting Sales Monitor Service...")
    
    try:
        # Initialize security manager
        security_manager = SecurityManager()
        await security_manager.initialize()
        
        # Initialize Sales Monitor Agent
        sales_agent = SalesMonitorAgent()
        await sales_agent.initialize()
        
        # Initialize Sales Predictor
        sales_predictor = SalesPredictor()
        await sales_predictor.initialize()
        
        # Initialize Event Client
        event_client = EventClient()
        await event_client.initialize()
        
        # Initialize Service Registry
        service_registry = ServiceRegistryClient()
        await service_registry.register_service(
            service_name="sales-monitor-service",
            service_url=f"https://{os.getenv('SERVICE_HOST', '0.0.0.0')}:{os.getenv('SERVICE_PORT', '8084')}",
            health_check_url="/health",
            metadata={
                "version": "1.0.0",
                "tier": "tier-2",
                "capabilities": ["sales_monitoring", "performance_analysis", "sales_prediction", "analytics"]
            }
        )
        
        logger.info("Sales Monitor Service initialized successfully")
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Sales Monitor Service...")
        if service_registry:
            await service_registry.deregister_service("sales-monitor-service")
        if sales_agent:
            await sales_agent.cleanup()
        if sales_predictor:
            await sales_predictor.cleanup()
        if event_client:
            await event_client.cleanup()

# Create FastAPI app
app = FastAPI(
    title="Sales Monitor Service",
    description="AI-powered sales monitoring, analytics, and prediction service",
    version="1.0.0",
    lifespan=lifespan
)

# Setup monitoring
setup_monitoring(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    services_status = {
        "sales_agent": "healthy" if sales_agent and sales_agent.is_ready else "unhealthy",
        "sales_predictor": "healthy" if sales_predictor and sales_predictor.is_ready else "unhealthy",
        "event_client": "healthy" if event_client and event_client.is_connected else "unhealthy",
        "service_registry": "healthy" if service_registry and service_registry.is_registered else "unhealthy"
    }
    
    overall_status = "healthy" if all(status == "healthy" for status in services_status.values()) else "unhealthy"
    
    return HealthResponse(
        status=overall_status,
        timestamp=datetime.utcnow().isoformat(),
        services=services_status
    )

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    if not sales_agent or not sales_agent.is_ready:
        raise HTTPException(status_code=503, detail="Sales agent not ready")
    if not sales_predictor or not sales_predictor.is_ready:
        raise HTTPException(status_code=503, detail="Sales predictor not ready")
    return {"status": "ready"}

@app.post("/monitor", response_model=SalesMonitorResponse)
async def monitor_sales_async(
    request: SalesMonitorRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Start asynchronous sales monitoring"""
    request_id = str(uuid.uuid4())
    
    # Store request
    async_requests[request_id] = {
        "status": "processing",
        "request": request.model_dump(),
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Add background task
    background_tasks.add_task(
        process_monitor_request,
        request_id,
        request
    )
    
    return SalesMonitorResponse(
        request_id=request_id,
        status="accepted",
        message="Sales monitoring request accepted and processing"
    )

@app.get("/monitor/{request_id}", response_model=SalesMonitorResponse)
async def get_monitor_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Get sales monitoring status and results"""
    if request_id not in async_requests:
        raise HTTPException(status_code=404, detail="Request not found")
    
    request_data = async_requests[request_id]
    
    return SalesMonitorResponse(
        request_id=request_id,
        status=request_data["status"],
        result=request_data.get("result"),
        message=request_data.get("message", "Processing")
    )

@app.post("/monitor/sync", response_model=SalesMonitorResponse)
async def monitor_sales_sync(
    request: SalesMonitorRequest,
    api_key: str = Depends(verify_api_key)
):
    """Synchronous sales monitoring execution"""
    if not sales_agent or not sales_agent.is_ready:
        raise HTTPException(status_code=503, detail="Sales agent not ready")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Record metrics
        with metrics.SALES_MONITOR_DURATION.time():
            result = await sales_agent.monitor_sales(
                date_range=request.date_range,
                include_page_reads=request.include_page_reads,
                generate_insights=request.generate_insights,
                books_filter=request.books_filter,
                user_id=request.user_id
            )
        
        metrics.SALES_MONITOR_COUNT.inc()
        metrics.SALES_MONITOR_SUCCESS_COUNT.inc()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "sales_monitoring_completed",
                {
                    "request_id": request_id,
                    "date_range": request.date_range,
                    "books_analyzed": len(result.books_data),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return SalesMonitorResponse(
            request_id=request_id,
            status="completed",
            result=result,
            message="Sales monitoring completed successfully"
        )
        
    except Exception as e:
        metrics.SALES_MONITOR_ERROR_COUNT.inc()
        logger.error(f"Sales monitoring failed: {e}")
        raise HTTPException(status_code=500, detail=f"Sales monitoring failed: {str(e)}")

@app.post("/predict", response_model=SalesPredictionResponse)
async def predict_sales(
    request: SalesPredictionRequest,
    api_key: str = Depends(verify_api_key)
):
    """Predict book sales performance"""
    if not sales_predictor or not sales_predictor.is_ready:
        raise HTTPException(status_code=503, detail="Sales predictor not ready")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Record metrics
        with metrics.SALES_PREDICTION_DURATION.time():
            result = await sales_predictor.predict_book_performance(
                book_id=request.book_id,
                target_price=request.target_price,
                launch_date=request.launch_date,
                include_market_analysis=request.market_analysis,
                user_id=request.user_id
            )
        
        metrics.SALES_PREDICTION_COUNT.inc()
        metrics.SALES_PREDICTION_SUCCESS_COUNT.inc()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "sales_prediction_completed",
                {
                    "request_id": request_id,
                    "book_id": request.book_id,
                    "predicted_sales_30d": result.predictions.get("sales_30d", 0),
                    "success_probability": result.predictions.get("success_probability", 0),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return SalesPredictionResponse(
            request_id=request_id,
            result=result
        )
        
    except Exception as e:
        metrics.SALES_PREDICTION_ERROR_COUNT.inc()
        logger.error(f"Sales prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Sales prediction failed: {str(e)}")

@app.post("/analyze", response_model=SalesAnalysisResponse)
async def analyze_sales(
    request: SalesAnalysisRequest,
    api_key: str = Depends(verify_api_key)
):
    """Analyze sales performance across multiple books"""
    if not sales_agent or not sales_agent.is_ready:
        raise HTTPException(status_code=503, detail="Sales agent not ready")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Record metrics
        with metrics.SALES_ANALYSIS_DURATION.time():
            result = await sales_agent.analyze_sales_performance(
                book_ids=request.book_ids,
                analysis_type=request.analysis_type,
                time_period=request.time_period,
                include_benchmarks=request.include_benchmarks,
                user_id=request.user_id
            )
        
        metrics.SALES_ANALYSIS_COUNT.inc()
        metrics.SALES_ANALYSIS_SUCCESS_COUNT.inc()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "sales_analysis_completed",
                {
                    "request_id": request_id,
                    "books_analyzed": len(request.book_ids),
                    "analysis_type": request.analysis_type,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return SalesAnalysisResponse(
            request_id=request_id,
            result=result
        )
        
    except Exception as e:
        metrics.SALES_ANALYSIS_ERROR_COUNT.inc()
        logger.error(f"Sales analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Sales analysis failed: {str(e)}")

@app.post("/train-models")
async def train_prediction_models(
    retrain: bool = False,
    api_key: str = Depends(verify_api_key)
):
    """Train or retrain ML prediction models"""
    if not sales_predictor or not sales_predictor.is_ready:
        raise HTTPException(status_code=503, detail="Sales predictor not ready")
    
    try:
        result = await sales_predictor.train_models(retrain=retrain)
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "sales_models_trained",
                {
                    "status": result.get("status"),
                    "training_samples": result.get("training_samples", 0),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return result
        
    except Exception as e:
        logger.error(f"Model training failed: {e}")
        raise HTTPException(status_code=500, detail=f"Model training failed: {str(e)}")

async def process_monitor_request(request_id: str, request: SalesMonitorRequest):
    """Process sales monitoring request asynchronously"""
    try:
        async_requests[request_id]["status"] = "processing"
        
        result = await sales_agent.monitor_sales(
            date_range=request.date_range,
            include_page_reads=request.include_page_reads,
            generate_insights=request.generate_insights,
            books_filter=request.books_filter,
            user_id=request.user_id
        )
        
        async_requests[request_id].update({
            "status": "completed",
            "result": result,
            "message": "Sales monitoring completed successfully"
        })
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "sales_monitoring_completed",
                {
                    "request_id": request_id,
                    "date_range": request.date_range,
                    "books_analyzed": len(result.books_data),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        metrics.SALES_MONITOR_SUCCESS_COUNT.inc()
        
    except Exception as e:
        logger.error(f"Async sales monitoring failed for request {request_id}: {e}")
        async_requests[request_id].update({
            "status": "failed",
            "message": f"Sales monitoring failed: {str(e)}"
        })
        metrics.SALES_MONITOR_ERROR_COUNT.inc()

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail, "request_id": str(uuid.uuid4())}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "request_id": str(uuid.uuid4())}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("SERVICE_HOST", "0.0.0.0"),
        port=int(os.getenv("SERVICE_PORT", "8084")),
        reload=True
    )