"""
Sales Predictor - ML-based Sales Prediction Engine

Provides comprehensive sales prediction capabilities using machine learning models
and market analysis for accurate forecasting and business intelligence.
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

import numpy as np
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# Mock scikit-learn imports for demonstration
# In production, these would be actual imports
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_absolute_error, r2_score
    import joblib
    SKLEARN_AVAILABLE = True
except ImportError:
    logger.warning("scikit-learn not available, using mock implementations")
    SKLEARN_AVAILABLE = False
    # Mock classes
    class RandomForestRegressor:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X, y): pass
        def predict(self, X): return [50.0] * len(X)
    
    class RandomForestClassifier:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X, y): pass
        def predict_proba(self, X): return [[0.3, 0.7]] * len(X)
    
    def train_test_split(*args, **kwargs):
        return args[0][:10], args[0][10:], args[1][:10], args[1][10:]
    
    def mean_absolute_error(y_true, y_pred): return 10.0
    def r2_score(y_true, y_pred): return 0.8
    
    class joblib:
        @staticmethod
        def dump(obj, path): pass
        @staticmethod
        def load(path): return RandomForestRegressor()

# Result Models
class SalesPredictionResult(BaseModel):
    """Result model for sales prediction operations"""
    book_id: str = Field(description="Book ID that was analyzed")
    predictions: Dict[str, Any] = Field(description="Sales predictions")
    risk_assessment: Dict[str, Any] = Field(description="Risk analysis")
    recommendations: List[Dict[str, str]] = Field(description="Actionable recommendations")
    price_optimization: Dict[str, Any] = Field(description="Price optimization suggestions")
    feature_importance: Dict[str, float] = Field(description="Feature importance for prediction")
    market_analysis: Dict[str, Any] = Field(description="Market analysis data")
    confidence_score: float = Field(description="Prediction confidence score")
    timestamp: str = Field(description="Prediction generation timestamp")

class SalesPredictor:
    """
    ML-based sales prediction engine with comprehensive forecasting capabilities
    """
    
    def __init__(self):
        self._initialized = False
        
        # ML Models
        self.sales_model = None
        self.revenue_model = None
        self.success_model = None
        
        # Model metadata
        self.model_version = "1.0"
        self.last_trained = None
        self.feature_names = []
        
    @property
    def is_ready(self) -> bool:
        """Check if predictor is ready for operations"""
        return self._initialized
    
    async def initialize(self):
        """Initialize the sales predictor"""
        if self._initialized:
            return
        
        try:
            # Load existing models if available
            await self._load_models()
            
            # Initialize feature names
            if not self.feature_names:
                self.feature_names = [
                    'word_count_normalized', 'avg_sentence_length', 'vocabulary_richness',
                    'reading_ease', 'grade_level', 'title_power_words', 'actionable_content_score',
                    'structure_completeness', 'competition_density', 'market_opportunity_score',
                    'current_seasonal_factor', 'target_price', 'price_vs_market'
                ]
            
            self._initialized = True
            logger.info("Sales Predictor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Sales Predictor: {e}")
            raise
    
    async def predict_book_performance(
        self,
        book_id: str,
        target_price: float = 4.99,
        launch_date: Optional[str] = None,
        include_market_analysis: bool = True,
        user_id: Optional[str] = None
    ) -> SalesPredictionResult:
        """
        Predict book sales performance
        
        Args:
            book_id: Book ID to predict
            target_price: Target selling price
            launch_date: Planned launch date
            include_market_analysis: Include market analysis
            user_id: Optional user ID for tracking
            
        Returns:
            SalesPredictionResult with comprehensive predictions
        """
        if not self.is_ready:
            raise RuntimeError("Sales predictor not initialized")
        
        try:
            # Get book data and extract features
            book_data = await self._get_book_data(book_id)
            features = await self._extract_features(book_data, target_price)
            
            # Get market analysis if requested
            market_analysis = {}
            if include_market_analysis:
                market_analysis = await self._analyze_market_conditions(book_data.get("category", "general"))
                features.update(market_analysis)
            
            # Generate predictions
            predictions = await self._generate_predictions(features)
            
            # Calculate risk assessment
            risk_assessment = await self._assess_risks(features, predictions)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(features, predictions)
            
            # Price optimization
            price_optimization = await self._optimize_pricing(features, book_data.get("category", "general"))
            
            # Feature importance
            feature_importance = await self._get_feature_importance(features)
            
            result = SalesPredictionResult(
                book_id=book_id,
                predictions=predictions,
                risk_assessment=risk_assessment,
                recommendations=recommendations,
                price_optimization=price_optimization,
                feature_importance=feature_importance,
                market_analysis=market_analysis,
                confidence_score=predictions.get('confidence', 0.7),
                timestamp=datetime.utcnow().isoformat()
            )
            
            logger.info(f"Sales prediction completed for book: {book_id}")
            return result
            
        except Exception as e:
            logger.error(f"Sales prediction failed: {e}")
            raise
    
    async def train_models(self, retrain: bool = False) -> Dict[str, Any]:
        """Train ML models on historical data"""
        
        if not retrain and self.sales_model is not None:
            return {"status": "models_already_trained", "last_trained": self.last_trained}
        
        logger.info("Training sales prediction models...")
        
        # Get training data
        training_data = await self._get_training_data()
        
        if len(training_data) < 50:
            logger.warning(f"Insufficient training data: {len(training_data)} samples")
            return {"status": "insufficient_data", "samples": len(training_data)}
        
        # Prepare features and targets
        X, y_sales, y_revenue, y_success = await self._prepare_training_data(training_data)
        
        # Train models
        models_trained = {}
        
        # Sales prediction model
        self.sales_model = RandomForestRegressor(n_estimators=100, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y_sales, test_size=0.2, random_state=42)
        self.sales_model.fit(X_train, y_train)
        
        if SKLEARN_AVAILABLE:
            sales_predictions = self.sales_model.predict(X_test)
            sales_mae = mean_absolute_error(y_test, sales_predictions)
            sales_r2 = r2_score(y_test, sales_predictions)
            models_trained['sales'] = {'mae': sales_mae, 'r2': sales_r2}
        
        # Revenue prediction model
        self.revenue_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.revenue_model.fit(X_train, y_revenue[:len(X_train)])
        
        # Success classification model
        self.success_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.success_model.fit(X_train, y_success[:len(X_train)])
        
        # Save models
        await self._save_models()
        
        self.last_trained = datetime.now()
        
        logger.info(f"Models trained successfully on {len(training_data)} samples")
        
        return {
            "status": "success",
            "training_samples": len(training_data),
            "models_trained": models_trained,
            "last_trained": self.last_trained.isoformat() if self.last_trained else None
        }
    
    async def _get_book_data(self, book_id: str) -> Dict[str, Any]:
        """Get book data from database (mock implementation)"""
        # Mock book data - in production this would query the database
        return {
            "id": book_id,
            "title": "Sample Book Title",
            "author": "Sample Author",
            "category": "self-help",
            "description": "A comprehensive guide to personal development and success.",
            "word_count": 50000,
            "price": 4.99,
            "keywords": ["success", "personal development", "motivation"]
        }
    
    async def _extract_features(self, book_data: Dict[str, Any], target_price: float) -> Dict[str, float]:
        """Extract features from book data for ML models"""
        # Mock feature extraction - in production this would analyze actual content
        return {
            'word_count_normalized': min(book_data.get('word_count', 50000) / 100000, 1.0),
            'avg_sentence_length': 15.0 / 25.0,  # Normalized
            'vocabulary_richness': 0.7,
            'reading_ease': 0.65,
            'grade_level': 8.0 / 16.0,  # Normalized to 0-1
            'title_power_words': 0.6,
            'actionable_content_score': 0.75,
            'structure_completeness': 0.8,
            'competition_density': 0.4,
            'market_opportunity_score': 0.7,
            'current_seasonal_factor': 1.1,
            'target_price': target_price,
            'price_vs_market': target_price / 4.99
        }
    
    async def _analyze_market_conditions(self, category: str) -> Dict[str, Any]:
        """Analyze market conditions for the category"""
        # Mock market analysis
        return {
            'avg_price': 4.99,
            'market_saturation': 0.6,
            'growth_trend': 0.15,
            'seasonal_demand': 1.1,
            'competition_level': 0.7
        }
    
    async def _generate_predictions(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Generate ML-based predictions"""
        
        try:
            # Prepare feature vector
            feature_vector = self._prepare_feature_vector(features)
            
            if self.sales_model is None:
                # Use heuristic model if ML model not trained yet
                return await self._heuristic_predictions(features)
            
            # ML predictions
            sales_30d = max(0, int(self.sales_model.predict([feature_vector])[0]))
            revenue_30d = max(0, float(self.revenue_model.predict([feature_vector])[0]))
            
            if SKLEARN_AVAILABLE and hasattr(self.success_model, 'predict_proba'):
                success_prob = max(0, min(1, float(self.success_model.predict_proba([feature_vector])[0][1])))
            else:
                success_prob = 0.7
            
            # Extended predictions (90 days)
            sales_90d = int(sales_30d * 2.5)  # Typical growth pattern
            revenue_90d = revenue_30d * 2.5
            
            # Confidence calculation
            confidence = self._calculate_prediction_confidence(features)
            
            return {
                'sales_30d': sales_30d,
                'revenue_30d': revenue_30d,
                'sales_90d': sales_90d,
                'revenue_90d': revenue_90d,
                'success_probability': success_prob,
                'confidence': confidence,
                'peak_sales_period': self._predict_peak_period(features),
                'roi_projection': revenue_30d / 100  # Assuming $100 investment
            }
            
        except Exception as e:
            logger.error(f"ML prediction failed: {e}")
            return await self._heuristic_predictions(features)
    
    async def _heuristic_predictions(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Fallback heuristic predictions when ML models aren't available"""
        
        # Base prediction using feature scores
        base_score = (
            features.get('actionable_content_score', 0.5) * 0.3 +
            features.get('title_power_words', 0.5) * 0.2 +
            features.get('market_opportunity_score', 0.5) * 0.3 +
            features.get('reading_ease', 0.5) * 0.1 +
            features.get('structure_completeness', 0.5) * 0.1
        )
        
        # Market modifiers
        market_modifier = features.get('current_seasonal_factor', 1.0)
        competition_modifier = 1 - features.get('competition_density', 0.5) * 0.3
        
        # Final score
        final_score = base_score * market_modifier * competition_modifier
        
        # Convert to sales predictions
        sales_30d = int(final_score * 100)  # Scale to reasonable sales numbers
        revenue_30d = sales_30d * features.get('target_price', 4.99)
        
        return {
            'sales_30d': sales_30d,
            'revenue_30d': revenue_30d,
            'sales_90d': int(sales_30d * 2.2),
            'revenue_90d': revenue_30d * 2.2,
            'success_probability': min(final_score * 1.2, 1.0),
            'confidence': 0.6,  # Lower confidence for heuristic
            'peak_sales_period': 'first_week',
            'roi_projection': revenue_30d / 50  # Lower investment assumption
        }
    
    async def _assess_risks(
        self, 
        features: Dict[str, float], 
        predictions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Assess risks and potential issues"""
        
        risks = []
        risk_score = 0.0
        
        # Competition risk
        if features.get('competition_density', 0) > 0.7:
            risks.append("High competition in category")
            risk_score += 0.2
        
        # Market saturation risk
        if features.get('market_saturation', 0) > 0.8:
            risks.append("Market appears saturated")
            risk_score += 0.25
        
        # Content quality risk
        if features.get('reading_ease', 0.7) < 0.4:
            risks.append("Content may be too difficult to read")
            risk_score += 0.15
        
        # Title appeal risk
        if features.get('title_power_words', 0.5) < 0.3:
            risks.append("Title may lack market appeal")
            risk_score += 0.1
        
        # Seasonal risk
        if features.get('current_seasonal_factor', 1.0) < 0.8:
            risks.append("Launching in low-demand season")
            risk_score += 0.1
        
        # Price risk
        price_vs_market = features.get('price_vs_market', 1.0)
        if price_vs_market > 1.5:
            risks.append("Price significantly above market average")
            risk_score += 0.15
        elif price_vs_market < 0.5:
            risks.append("Price may signal low quality")
            risk_score += 0.1
        
        # Overall risk level
        if risk_score < 0.3:
            risk_level = "low"
        elif risk_score < 0.6:
            risk_level = "medium"
        else:
            risk_level = "high"
        
        return {
            'risk_level': risk_level,
            'risk_score': min(risk_score, 1.0),
            'identified_risks': risks,
            'mitigation_strategies': self._generate_mitigation_strategies(risks)
        }
    
    def _generate_mitigation_strategies(self, risks: List[str]) -> List[str]:
        """Generate risk mitigation strategies"""
        
        strategies = []
        
        for risk in risks:
            if "competition" in risk.lower():
                strategies.append("Consider unique positioning or niche targeting")
            elif "saturated" in risk.lower():
                strategies.append("Focus on unique angle or underserved subtopic")
            elif "difficult" in risk.lower():
                strategies.append("Simplify language and add more examples")
            elif "title" in risk.lower():
                strategies.append("A/B test different titles with power words")
            elif "season" in risk.lower():
                strategies.append("Consider delaying launch or seasonal marketing")
            elif "price" in risk.lower():
                strategies.append("Research optimal pricing for category")
        
        if not strategies:
            strategies.append("Monitor performance closely and optimize based on initial results")
        
        return strategies
    
    async def _generate_recommendations(
        self, 
        features: Dict[str, float], 
        predictions: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        # Content recommendations
        if features.get('actionable_content_score', 0.5) < 0.6:
            recommendations.append({
                'type': 'content',
                'priority': 'high',
                'title': 'Add More Actionable Content',
                'description': 'Include more practical tips, step-by-step guides, and actionable advice to increase reader value.'
            })
        
        # Title recommendations
        if features.get('title_power_words', 0.5) < 0.4:
            recommendations.append({
                'type': 'marketing',
                'priority': 'high',
                'title': 'Optimize Book Title',
                'description': 'Consider adding power words like "Ultimate", "Complete", "Proven", or "Step-by-Step" to increase appeal.'
            })
        
        # Market timing recommendations
        seasonal_factor = features.get('current_seasonal_factor', 1.0)
        if seasonal_factor < 0.8:
            recommendations.append({
                'type': 'timing',
                'priority': 'medium',
                'title': 'Consider Launch Timing',
                'description': f'Current season has {seasonal_factor:.0%} demand. Consider launching in high-demand periods for better performance.'
            })
        
        # Pricing recommendations
        if predictions.get('success_probability', 0.5) < 0.6:
            recommendations.append({
                'type': 'pricing',
                'priority': 'high',
                'title': 'Review Pricing Strategy',
                'description': 'Consider price testing or market research to optimize pricing for better success probability.'
            })
        
        return recommendations
    
    async def _optimize_pricing(self, features: Dict[str, float], category: str) -> Dict[str, Any]:
        """Optimize pricing based on market analysis"""
        
        market_avg = features.get('avg_price', 4.99)
        optimal_min = 2.99
        optimal_max = 7.99
        
        # Quality-based pricing adjustment
        quality_score = (
            features.get('actionable_content_score', 0.5) +
            features.get('reading_ease', 0.5) +
            features.get('structure_completeness', 0.5)
        ) / 3
        
        # Recommended price based on quality
        if quality_score > 0.8:
            recommended_price = optimal_max * 0.9  # Premium pricing
        elif quality_score > 0.6:
            recommended_price = (optimal_min + optimal_max) / 2  # Market average
        else:
            recommended_price = optimal_min * 1.1  # Competitive pricing
        
        # Price elasticity estimation
        price_elasticity = -1.2  # Typical for e-books (elastic)
        
        return {
            'recommended_price': round(recommended_price, 2),
            'market_average': market_avg,
            'optimal_range': {'min': optimal_min, 'max': optimal_max},
            'price_elasticity': price_elasticity,
            'pricing_strategy': self._suggest_pricing_strategy(quality_score),
            'test_prices': [
                round(recommended_price * 0.8, 2),
                round(recommended_price, 2),
                round(recommended_price * 1.2, 2)
            ]
        }
    
    def _suggest_pricing_strategy(self, quality_score: float) -> str:
        """Suggest pricing strategy based on quality"""
        
        if quality_score > 0.8:
            return "premium"  # High quality can command premium price
        elif quality_score > 0.6:
            return "competitive"  # Match market average
        else:
            return "penetration"  # Lower price to gain market share
    
    async def _get_feature_importance(self, features: Dict[str, float]) -> Dict[str, float]:
        """Get feature importance for this prediction"""
        
        if self.sales_model is None or not SKLEARN_AVAILABLE:
            # Return heuristic importance
            return {
                'content_quality': 0.25,
                'market_conditions': 0.20,
                'title_appeal': 0.15,
                'pricing': 0.15,
                'readability': 0.10,
                'structure': 0.10,
                'seasonal_timing': 0.05
            }
        
        # Get ML model feature importance
        if hasattr(self.sales_model, 'feature_importances_'):
            importances = self.sales_model.feature_importances_
            feature_importance = {}
            
            for i, importance in enumerate(importances):
                if i < len(self.feature_names):
                    feature_importance[self.feature_names[i]] = float(importance)
            
            return feature_importance
        
        return {}
    
    def _prepare_feature_vector(self, features: Dict[str, float]) -> List[float]:
        """Convert feature dict to vector for ML models"""
        
        # Create feature vector
        feature_vector = []
        for feature_name in self.feature_names:
            feature_vector.append(features.get(feature_name, 0.5))  # Default to 0.5
        
        return feature_vector
    
    def _calculate_prediction_confidence(self, features: Dict[str, float]) -> float:
        """Calculate confidence in predictions based on feature quality"""
        
        confidence_factors = []
        
        # Data completeness
        feature_completeness = sum(1 for v in features.values() if v > 0) / len(features)
        confidence_factors.append(feature_completeness)
        
        # Market data quality
        if features.get('market_opportunity_score', 0) > 0:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.4)
        
        # Content quality indicators
        content_quality = (
            features.get('reading_ease', 0.5) +
            features.get('structure_completeness', 0.5) +
            features.get('actionable_content_score', 0.5)
        ) / 3
        confidence_factors.append(content_quality)
        
        return sum(confidence_factors) / len(confidence_factors)
    
    def _predict_peak_period(self, features: Dict[str, float]) -> str:
        """Predict when sales will peak"""
        
        seasonal_factor = features.get('current_seasonal_factor', 1.0)
        
        if seasonal_factor > 1.2:
            return "immediate"  # High season
        elif seasonal_factor > 0.9:
            return "first_week"
        else:
            return "within_month"
    
    async def _get_training_data(self) -> List[Dict[str, Any]]:
        """Get historical data for training (mock implementation)"""
        # Mock training data
        return [
            {
                'book': {
                    'title': 'Mock Book 1',
                    'category': 'self-help',
                    'word_count': 45000
                },
                'sales_data': {
                    'sales_units': 150,
                    'revenue': 747.50
                }
            }
            # Would have many more entries in production
        ]
    
    async def _prepare_training_data(
        self, 
        training_data: List[Dict[str, Any]]
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Prepare training data for ML models"""
        
        # Mock implementation
        X = np.random.rand(len(training_data), len(self.feature_names))
        y_sales = np.random.randint(10, 200, len(training_data))
        y_revenue = y_sales * 4.99
        y_success = (y_sales > 50).astype(int)
        
        return X, y_sales, y_revenue, y_success
    
    async def _load_models(self):
        """Load pre-trained models if available"""
        
        model_dir = Path("storage/prediction_models")
        
        try:
            if (model_dir / "sales_model.pkl").exists() and SKLEARN_AVAILABLE:
                self.sales_model = joblib.load(model_dir / "sales_model.pkl")
            
            if (model_dir / "revenue_model.pkl").exists() and SKLEARN_AVAILABLE:
                self.revenue_model = joblib.load(model_dir / "revenue_model.pkl")
            
            if (model_dir / "success_model.pkl").exists() and SKLEARN_AVAILABLE:
                self.success_model = joblib.load(model_dir / "success_model.pkl")
            
            if (model_dir / "metadata.json").exists():
                with open(model_dir / "metadata.json", 'r') as f:
                    metadata = json.load(f)
                    self.last_trained = datetime.fromisoformat(metadata.get('last_trained', '2024-01-01'))
                    self.feature_names = metadata.get('feature_names', [])
                    
        except Exception as e:
            logger.warning(f"Failed to load models: {e}")
    
    async def _save_models(self):
        """Save trained models"""
        
        if not SKLEARN_AVAILABLE:
            return
        
        model_dir = Path("storage/prediction_models")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            if self.sales_model:
                joblib.dump(self.sales_model, model_dir / "sales_model.pkl")
            
            if self.revenue_model:
                joblib.dump(self.revenue_model, model_dir / "revenue_model.pkl")
            
            if self.success_model:
                joblib.dump(self.success_model, model_dir / "success_model.pkl")
            
            # Save metadata
            metadata = {
                'last_trained': self.last_trained.isoformat() if self.last_trained else datetime.now().isoformat(),
                'feature_names': self.feature_names,
                'model_version': self.model_version
            }
            
            with open(model_dir / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save models: {e}")
    
    async def cleanup(self):
        """Cleanup predictor resources"""
        if self._initialized:
            self.sales_model = None
            self.revenue_model = None
            self.success_model = None
            self._initialized = False
            logger.info("Sales Predictor cleaned up")