"""
Sales Monitor Agent - Standalone PydanticAI Implementation

Provides comprehensive sales monitoring, performance analysis, and insights
using PydanticAI for AI-powered analytics and reporting.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel

from .prompt_template import PromptTemplate

logger = logging.getLogger(__name__)

# Result Models
class SalesReportResult(BaseModel):
    """Result model for sales monitoring operations"""
    reporting_period: str = Field(description="The reporting period for the sales data")
    books_data: List[Dict[str, Any]] = Field(description="Sales data for each book")
    performance_metrics: Dict[str, Any] = Field(description="Key performance metrics summary")
    insights: Dict[str, Any] = Field(description="AI-generated insights and recommendations")
    total_revenue: float = Field(description="Total revenue for the period")
    total_units_sold: int = Field(description="Total units sold for the period")
    average_price: float = Field(description="Average selling price")
    top_performers: List[Dict[str, Any]] = Field(description="Top performing books")
    growth_metrics: Dict[str, Any] = Field(description="Growth and trend metrics")
    timestamp: str = Field(description="Report generation timestamp")

class SalesAnalysisResult(BaseModel):
    """Result model for sales analysis operations"""
    analysis_type: str = Field(description="Type of analysis performed")
    time_period: str = Field(description="Analysis time period")
    books_analyzed: int = Field(description="Number of books analyzed")
    performance_summary: Dict[str, Any] = Field(description="Overall performance summary")
    comparative_analysis: Dict[str, Any] = Field(description="Comparative performance analysis")
    benchmarks: Dict[str, Any] = Field(description="Industry benchmarks comparison")
    recommendations: List[Dict[str, str]] = Field(description="Actionable recommendations")
    risk_factors: List[str] = Field(description="Identified risk factors")
    opportunities: List[str] = Field(description="Identified opportunities")
    confidence_score: float = Field(description="Analysis confidence score")
    timestamp: str = Field(description="Analysis generation timestamp")

class SalesMonitorAgent:
    """
    Standalone Sales Monitor Agent for comprehensive sales analytics
    """
    
    def __init__(self):
        self._agent: Optional[TypedAgent] = None
        self._analysis_agent: Optional[TypedAgent] = None
        self._initialized = False
        
    @property
    def is_ready(self) -> bool:
        """Check if agent is ready for operations"""
        return self._initialized and self._agent is not None and self._analysis_agent is not None
    
    def _get_available_model(self) -> Optional[Model]:
        """Get available AI model based on configuration"""
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        # Fallback to any available model
        if os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_monitoring_prompt(self) -> str:
        """Get the sales monitoring system prompt"""
        return """
        You are an expert sales analyst and business intelligence specialist with extensive experience in 
        e-commerce, digital publishing, and performance analytics. Your expertise includes:

        - Sales data analysis and trend identification
        - Performance metrics calculation and interpretation
        - Market comparison and competitive analysis
        - Revenue optimization and pricing strategies
        - Customer behavior analysis and segmentation
        - Seasonal pattern recognition and forecasting
        - Risk assessment and opportunity identification

        For each sales monitoring request, you will provide comprehensive analysis that includes:
        1. Detailed sales performance metrics with context and insights
        2. Trend analysis with growth patterns and seasonal factors
        3. Comparative analysis against historical performance
        4. Revenue optimization opportunities and recommendations
        5. Risk factors and potential challenges identification
        6. Strategic recommendations for performance improvement

        Focus on actionable insights that drive business decisions and revenue growth.
        Provide clear explanations for all metrics and recommendations.
        """
    
    def _get_analysis_prompt(self) -> str:
        """Get the sales analysis system prompt"""
        return """
        You are a senior business analyst specializing in sales performance analytics and strategic
        business intelligence. Your expertise includes:

        - Multi-dimensional sales data analysis
        - Performance benchmarking and comparative studies
        - Market trend analysis and competitive positioning
        - Risk assessment and opportunity mapping
        - Strategic recommendation development
        - Business growth pattern recognition

        Provide data-driven insights with clear methodology, actionable recommendations,
        and strategic guidance for business optimization and growth acceleration.
        """
    
    async def initialize(self):
        """Initialize the sales monitor agent"""
        if self._initialized:
            return
        
        model = self._get_available_model()
        if not model:
            raise RuntimeError("No AI model available. Check API keys.")
        
        try:
            # Initialize sales monitoring agent
            self._agent = TypedAgent(
                model=model,
                result_type=SalesReportResult,
                system_prompt=self._get_monitoring_prompt()
            )
            
            # Initialize sales analysis agent
            self._analysis_agent = TypedAgent(
                model=model,
                result_type=SalesAnalysisResult,
                system_prompt=self._get_analysis_prompt()
            )
            
            self._initialized = True
            logger.info("Sales Monitor Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Sales Monitor Agent: {e}")
            raise
    
    async def monitor_sales(
        self,
        date_range: str = "last_30_days",
        include_page_reads: bool = True,
        generate_insights: bool = True,
        books_filter: Optional[List[str]] = None,
        user_id: Optional[str] = None
    ) -> SalesReportResult:
        """
        Monitor sales performance and generate comprehensive report
        
        Args:
            date_range: Date range for analysis
            include_page_reads: Include page read data
            generate_insights: Generate AI insights
            books_filter: Filter by specific book IDs
            user_id: Optional user ID for tracking
            
        Returns:
            SalesReportResult with comprehensive sales analysis
        """
        if not self.is_ready:
            raise RuntimeError("Sales monitor agent not initialized")
        
        try:
            # Get sales data (this would interface with the actual database)
            sales_data = await self._get_sales_data(date_range, books_filter, include_page_reads)
            
            # Prepare monitoring prompt
            prompt_template = PromptTemplate.from_string("""
            Analyze the following sales data and generate a comprehensive sales report:
            
            Date Range: {{ date_range }}
            Include Page Reads: {{ include_page_reads }}
            Generate Insights: {{ generate_insights }}
            
            Sales Data: {{ sales_data }}
            
            Provide detailed analysis including:
            - Performance metrics and trends
            - Revenue analysis and growth patterns
            - Top performing books and their success factors
            - Insights and recommendations for improvement
            - Risk factors and opportunities
            
            Focus on actionable insights that can drive business decisions.
            """)
            
            prompt = prompt_template.format(
                date_range=date_range,
                include_page_reads=include_page_reads,
                generate_insights=generate_insights,
                sales_data=self._format_sales_data(sales_data)
            )
            
            # Execute monitoring
            result = await self._agent.run(prompt)
            
            # Enhance result with metadata
            enhanced_result = result.data
            enhanced_result.timestamp = datetime.utcnow().isoformat()
            
            logger.info(f"Sales monitoring completed for period: {date_range}")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Sales monitoring failed: {e}")
            raise
    
    async def analyze_sales_performance(
        self,
        book_ids: List[str],
        analysis_type: str = "performance",
        time_period: str = "last_90_days",
        include_benchmarks: bool = True,
        user_id: Optional[str] = None
    ) -> SalesAnalysisResult:
        """
        Analyze sales performance across multiple books
        
        Args:
            book_ids: List of book IDs to analyze
            analysis_type: Type of analysis to perform
            time_period: Analysis time period
            include_benchmarks: Include industry benchmarks
            user_id: Optional user ID for tracking
            
        Returns:
            SalesAnalysisResult with comprehensive analysis
        """
        if not self.is_ready:
            raise RuntimeError("Sales analysis agent not initialized")
        
        try:
            # Get comprehensive data for analysis
            sales_data = await self._get_sales_data(time_period, book_ids, True)
            benchmarks = await self._get_benchmarks() if include_benchmarks else {}
            
            # Prepare analysis prompt
            prompt_template = PromptTemplate.from_string("""
            Perform comprehensive sales analysis for the following data:
            
            Analysis Type: {{ analysis_type }}
            Time Period: {{ time_period }}
            Books Analyzed: {{ book_count }}
            Include Benchmarks: {{ include_benchmarks }}
            
            Sales Data: {{ sales_data }}
            Industry Benchmarks: {{ benchmarks }}
            
            Provide detailed analysis including:
            - Performance summary and key metrics
            - Comparative analysis between books
            - Trend identification and pattern analysis
            - Benchmark comparison and competitive positioning
            - Risk factors and growth opportunities
            - Strategic recommendations for optimization
            
            Focus on actionable insights and strategic guidance.
            """)
            
            prompt = prompt_template.format(
                analysis_type=analysis_type,
                time_period=time_period,
                book_count=len(book_ids),
                include_benchmarks=include_benchmarks,
                sales_data=self._format_sales_data(sales_data),
                benchmarks=self._format_benchmarks(benchmarks)
            )
            
            # Execute analysis
            result = await self._analysis_agent.run(prompt)
            
            # Enhance result with metadata
            enhanced_result = result.data
            enhanced_result.timestamp = datetime.utcnow().isoformat()
            enhanced_result.books_analyzed = len(book_ids)
            
            logger.info(f"Sales analysis completed for {len(book_ids)} books")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Sales analysis failed: {e}")
            raise
    
    async def get_performance_insights(
        self,
        performance_data: Dict[str, Any],
        comparison_period: Optional[str] = None
    ) -> List[str]:
        """
        Get AI-powered insights from performance data
        
        Args:
            performance_data: Performance metrics data
            comparison_period: Optional comparison period
            
        Returns:
            List of insights and recommendations
        """
        insights = []
        
        # Revenue insights
        revenue = performance_data.get("total_revenue", 0)
        units_sold = performance_data.get("total_units_sold", 0)
        
        if revenue > 0 and units_sold > 0:
            avg_price = revenue / units_sold
            insights.append(f"Average selling price is ${avg_price:.2f}")
            
            if avg_price < 3.00:
                insights.append("Consider increasing prices to optimize revenue")
            elif avg_price > 8.00:
                insights.append("Monitor price sensitivity for potential volume impact")
        
        # Growth insights
        growth_rate = performance_data.get("growth_rate", 0)
        if growth_rate > 0.2:
            insights.append("Strong growth momentum - consider scaling marketing efforts")
        elif growth_rate < -0.1:
            insights.append("Declining performance - investigate causes and implement improvements")
        
        # Seasonal insights
        seasonal_factor = performance_data.get("seasonal_factor", 1.0)
        if seasonal_factor > 1.2:
            insights.append("Benefiting from seasonal demand - prepare for post-season strategy")
        elif seasonal_factor < 0.8:
            insights.append("Operating in low-demand season - focus on preparation for peak periods")
        
        # Competition insights
        market_share = performance_data.get("market_share", 0)
        if market_share > 0.1:
            insights.append("Strong market position - leverage for premium pricing or expansion")
        elif market_share < 0.05:
            insights.append("Limited market presence - focus on differentiation and visibility")
        
        return insights
    
    async def _get_sales_data(
        self,
        date_range: str,
        book_ids: Optional[List[str]] = None,
        include_page_reads: bool = True
    ) -> Dict[str, Any]:
        """
        Get sales data from database (mock implementation)
        In production, this would interface with the actual database
        """
        # This is a mock implementation
        # In production, this would query the actual Supabase database
        
        end_date = datetime.now()
        if date_range == "last_30_days":
            start_date = end_date - timedelta(days=30)
        elif date_range == "last_90_days":
            start_date = end_date - timedelta(days=90)
        elif date_range == "last_7_days":
            start_date = end_date - timedelta(days=7)
        else:
            start_date = end_date - timedelta(days=30)
        
        # Mock data structure
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": (end_date - start_date).days
            },
            "books": [
                {
                    "book_id": "book_1",
                    "title": "Sample Book 1",
                    "units_sold": 45,
                    "revenue": 224.55,
                    "page_reads": 1250 if include_page_reads else None,
                    "average_rating": 4.2
                },
                {
                    "book_id": "book_2", 
                    "title": "Sample Book 2",
                    "units_sold": 32,
                    "revenue": 159.68,
                    "page_reads": 890 if include_page_reads else None,
                    "average_rating": 4.0
                }
            ],
            "totals": {
                "total_revenue": 384.23,
                "total_units": 77,
                "total_page_reads": 2140 if include_page_reads else None
            }
        }
    
    async def _get_benchmarks(self) -> Dict[str, Any]:
        """Get industry benchmarks (mock implementation)"""
        return {
            "average_monthly_sales": 25,
            "average_revenue_per_book": 125.00,
            "average_price": 4.99,
            "industry_growth_rate": 0.15,
            "top_quartile_performance": {
                "monthly_sales": 100,
                "revenue": 500.00
            }
        }
    
    def _format_sales_data(self, sales_data: Dict[str, Any]) -> str:
        """Format sales data for prompt inclusion"""
        if not sales_data:
            return "No sales data available"
        
        formatted = []
        formatted.append(f"Period: {sales_data.get('period', {}).get('start_date')} to {sales_data.get('period', {}).get('end_date')}")
        
        if "totals" in sales_data:
            totals = sales_data["totals"]
            formatted.append(f"Total Revenue: ${totals.get('total_revenue', 0):.2f}")
            formatted.append(f"Total Units Sold: {totals.get('total_units', 0)}")
            if totals.get('total_page_reads'):
                formatted.append(f"Total Page Reads: {totals.get('total_page_reads', 0)}")
        
        if "books" in sales_data:
            formatted.append("\nBook Performance:")
            for book in sales_data["books"]:
                formatted.append(f"- {book.get('title', 'Unknown')}: {book.get('units_sold', 0)} units, ${book.get('revenue', 0):.2f} revenue")
        
        return "\n".join(formatted)
    
    def _format_benchmarks(self, benchmarks: Dict[str, Any]) -> str:
        """Format benchmark data for prompt inclusion"""
        if not benchmarks:
            return "No benchmark data available"
        
        formatted = []
        for key, value in benchmarks.items():
            if isinstance(value, dict):
                formatted.append(f"{key}: {str(value)}")
            else:
                formatted.append(f"{key}: {value}")
        
        return "\n".join(formatted)
    
    async def cleanup(self):
        """Cleanup agent resources"""
        if self._initialized:
            self._agent = None
            self._analysis_agent = None
            self._initialized = False
            logger.info("Sales Monitor Agent cleaned up")