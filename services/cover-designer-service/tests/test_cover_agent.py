"""
Tests for Cover Designer Agent
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.cover_agent import CoverDesignerAgent, CoverDesignResult, MarketAnalysisResult


class TestCoverDesignerAgent:
    """Test cases for Cover Designer Agent"""
    
    def test_cover_agent_initialization(self):
        """Test cover agent initialization"""
        agent = CoverDesignerAgent()
        
        assert agent._initialized is False
        assert agent._agent is None
        assert agent._market_agent is None
        assert agent.is_ready is False
    
    def test_get_available_model_openai(self):
        """Test getting OpenAI model"""
        agent = CoverDesignerAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'openai',
            'OPENAI_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_anthropic(self):
        """Test getting Anthropic model"""
        agent = CoverDesignerAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'anthropic',
            'ANTHROPIC_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_none(self):
        """Test when no model is available"""
        agent = CoverDesignerAgent()
        
        with patch.dict('os.environ', {}, clear=True):
            model = agent._get_available_model()
            assert model is None
    
    def test_design_prompt_content(self):
        """Test design prompt contains required elements"""
        agent = CoverDesignerAgent()
        prompt = agent._get_design_prompt()
        
        assert "book cover designer" in prompt.lower()
        assert "genre" in prompt.lower()
        assert "color palette" in prompt.lower()
        assert "typography" in prompt.lower()
        assert "market" in prompt.lower()
    
    def test_market_analysis_prompt_content(self):
        """Test market analysis prompt contains required elements"""
        agent = CoverDesignerAgent()
        prompt = agent._get_market_analysis_prompt()
        
        assert "market analyst" in prompt.lower()
        assert "design trends" in prompt.lower()
        assert "consumer behavior" in prompt.lower()
        assert "competitive" in prompt.lower()
    
    def test_format_style_preferences(self):
        """Test style preferences formatting"""
        agent = CoverDesignerAgent()
        
        preferences = {
            "color_scheme": "warm",
            "style": "modern",
            "mood": "professional"
        }
        
        formatted = agent._format_style_preferences(preferences)
        
        assert "color_scheme: warm" in formatted
        assert "style: modern" in formatted
        assert "mood: professional" in formatted
    
    def test_format_style_preferences_empty(self):
        """Test empty style preferences formatting"""
        agent = CoverDesignerAgent()
        
        formatted = agent._format_style_preferences({})
        assert "No specific style preferences provided" in formatted
    
    def test_format_market_data(self):
        """Test market data formatting"""
        agent = CoverDesignerAgent()
        
        market_data = {
            "trending_colors": ["blue", "gold"],
            "popular_styles": {"minimalist": 0.8},
            "genre_score": 0.85
        }
        
        formatted = agent._format_market_data(market_data)
        
        assert "trending_colors:" in formatted
        assert "popular_styles:" in formatted
        assert "genre_score: 0.85" in formatted
    
    def test_format_market_data_empty(self):
        """Test empty market data formatting"""
        agent = CoverDesignerAgent()
        
        formatted = agent._format_market_data({})
        assert "No specific market data provided" in formatted
    
    @pytest.mark.asyncio
    async def test_design_cover_not_initialized(self):
        """Test cover design when agent not initialized"""
        agent = CoverDesignerAgent()
        
        with pytest.raises(RuntimeError, match="Cover designer agent not initialized"):
            await agent.design_cover(
                book_title="Test Book",
                author_name="Test Author",
                genre="fiction"
            )
    
    @pytest.mark.asyncio
    async def test_analyze_market_trends_not_initialized(self):
        """Test market analysis when agent not initialized"""
        agent = CoverDesignerAgent()
        
        with pytest.raises(RuntimeError, match="Market analysis agent not initialized"):
            await agent.analyze_market_trends(
                genre="fiction",
                target_audience="young adults"
            )
    
    @pytest.mark.asyncio
    async def test_get_design_suggestions(self):
        """Test getting design suggestions"""
        agent = CoverDesignerAgent()
        
        suggestions = await agent.get_design_suggestions(
            genre="fiction",
            target_audience="young adults"
        )
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        # Should include genre-specific suggestions
        assert any("atmospheric" in suggestion.lower() or "imagery" in suggestion.lower() for suggestion in suggestions)
        # Should include audience-specific suggestions
        assert any("vibrant" in suggestion.lower() or "energetic" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_get_design_suggestions_mystery_genre(self):
        """Test design suggestions for mystery genre"""
        agent = CoverDesignerAgent()
        
        suggestions = await agent.get_design_suggestions(
            genre="mystery",
            target_audience="adults"
        )
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        # Should include mystery-specific suggestions
        assert any("dark" in suggestion.lower() or "shadow" in suggestion.lower() or "dramatic" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_get_design_suggestions_with_style_context(self):
        """Test design suggestions with style context"""
        agent = CoverDesignerAgent()
        
        suggestions = await agent.get_design_suggestions(
            genre="non-fiction",
            target_audience="professionals",
            style_context={"modern": True, "minimalist": True}
        )
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        # Should include style context suggestions
        assert any("contemporary" in suggestion.lower() or "modern" in suggestion.lower() for suggestion in suggestions)
        assert any("clean" in suggestion.lower() or "uncluttered" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test agent cleanup"""
        agent = CoverDesignerAgent()
        agent._initialized = True  # Simulate initialized state
        
        await agent.cleanup()
        
        assert agent._initialized is False
        assert agent._agent is None
        assert agent._market_agent is None
    
    def test_cover_design_result_model(self):
        """Test CoverDesignResult model"""
        result = CoverDesignResult(
            design_concept={"theme": "modern tech", "approach": "minimalist"},
            color_palette={"primary": "#2E86C1", "secondary": "#F7DC6F"},
            typography={"title_font": "Helvetica Bold", "author_font": "Arial"},
            layout={"title_position": "top", "author_position": "bottom"},
            image_prompts=["abstract technology background", "subtle geometric patterns"],
            design_score=0.87,
            market_alignment={"genre_match": 0.9, "trend_alignment": 0.85},
            design_rationale="Clean, professional design targeting tech enthusiasts",
            style_categories=["modern", "minimalist", "tech"],
            target_demographics=["professionals", "tech workers"],
            competitive_analysis={"market_position": "premium", "differentiation": "clean design"},
            timestamp="2024-01-01T00:00:00Z"
        )
        
        assert result.design_concept["theme"] == "modern tech"
        assert result.color_palette["primary"] == "#2E86C1"
        assert result.design_score == 0.87
        assert len(result.image_prompts) == 2
        assert len(result.style_categories) == 3
        assert result.market_alignment["genre_match"] == 0.9
    
    def test_market_analysis_result_model(self):
        """Test MarketAnalysisResult model"""
        analysis = MarketAnalysisResult(
            trending_styles=["minimalist", "vintage", "bold typography"],
            color_trends=["blue and gold", "dark themes", "monochrome"],
            typography_trends=["serif titles", "sans-serif subtitles", "handwritten accents"],
            genre_conventions={
                "fiction": {"colors": ["warm tones"], "styles": ["atmospheric"]},
                "non-fiction": {"colors": ["professional"], "styles": ["clean"]}
            },
            audience_preferences={
                "young_adults": {"prefer": ["vibrant colors", "modern fonts"]},
                "professionals": {"prefer": ["clean design", "readable fonts"]}
            },
            competitive_insights=[
                "Successful covers use high contrast",
                "Genre recognition is crucial",
                "Author name prominence varies by genre"
            ],
            recommendation_score=0.92
        )
        
        assert len(analysis.trending_styles) == 3
        assert len(analysis.color_trends) == 3
        assert "fiction" in analysis.genre_conventions
        assert "young_adults" in analysis.audience_preferences
        assert len(analysis.competitive_insights) == 3
        assert analysis.recommendation_score == 0.92