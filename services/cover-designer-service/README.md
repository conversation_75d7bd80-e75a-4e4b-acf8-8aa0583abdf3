# Cover Designer Service

A microservice implementation of the Cover Designer Agent, providing comprehensive book cover design capabilities with market analysis and design optimization.

## Overview

The Cover Designer Service is part of the Publish AI platform's microservices architecture migration. It extracts the cover design functionality from the monolithic system and provides it as a standalone, scalable service for AI-powered book cover creation and market analysis.

## Features

- **AI-Powered Cover Design**: Comprehensive cover design with PydanticAI agents
- **Market Analysis**: Real-time market trend analysis for design optimization
- **Design Optimization**: Multi-factor design scoring and competitive analysis
- **Style Recommendations**: Genre-specific and audience-targeted design guidance
- **Multiple AI Models**: Supports both OpenAI and Anthropic models with automatic fallback
- **Event-Driven**: Integrates with Event Bus for asynchronous communication
- **Service Discovery**: Automatic registration and health monitoring
- **Security**: API key authentication and mTLS support
- **Monitoring**: Prometheus metrics and health checks
- **Scalable**: Docker containerization with Kubernetes support

## API Endpoints

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### Cover Design Operations
- `POST /design` - Start asynchronous cover design
- `GET /design/{request_id}` - Get design status and results
- `POST /design/sync` - Synchronous cover design execution
- `POST /design/market-analysis` - Analyze market trends for design guidance

## Request/Response Models

### Cover Design Request
```json
{
  "book_title": "The Future of AI",
  "author_name": "John Smith",
  "genre": "non-fiction",
  "target_audience": "tech professionals",
  "style_preferences": {
    "color_scheme": "modern",
    "style": "minimalist",
    "mood": "professional"
  },
  "market_data": {
    "trending_styles": ["clean", "bold typography"],
    "competitor_analysis": "high contrast designs performing well"
  },
  "user_id": "user123"
}
```

### Cover Design Response
```json
{
  "request_id": "uuid-string",
  "status": "completed",
  "result": {
    "design_concept": {
      "theme": "modern technology",
      "approach": "minimalist professional"
    },
    "color_palette": {
      "primary": "#2E86C1",
      "secondary": "#F7DC6F",
      "accent": "#FFFFFF"
    },
    "typography": {
      "title_font": "Helvetica Bold",
      "author_font": "Arial Regular",
      "subtitle_font": "Helvetica Light"
    },
    "layout": {
      "title_position": "upper_third",
      "author_position": "bottom",
      "image_placement": "background"
    },
    "image_prompts": [
      "abstract technology background with subtle geometric patterns",
      "professional tech environment with soft lighting",
      "minimal circuit board patterns in the background"
    ],
    "design_score": 0.89,
    "market_alignment": {
      "genre_match": 0.92,
      "trend_alignment": 0.87,
      "audience_appeal": 0.90
    },
    "design_rationale": "Clean, professional design targeting tech professionals with high readability and genre recognition",
    "style_categories": ["modern", "minimalist", "professional"],
    "target_demographics": ["tech professionals", "business readers"],
    "competitive_analysis": {
      "market_position": "premium professional",
      "differentiation": "clean design with tech elements"
    },
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "message": "Cover design completed successfully"
}
```

### Market Analysis Request
```json
{
  "genre": "science fiction",
  "target_audience": "young adults",
  "analysis_depth": "comprehensive"
}
```

### Market Analysis Response
```json
{
  "analysis": {
    "trending_styles": ["futuristic", "neon colors", "space themes"],
    "color_trends": ["blue and purple", "dark themes with bright accents"],
    "typography_trends": ["bold sans-serif", "futuristic fonts"],
    "genre_conventions": {
      "science_fiction": {
        "colors": ["blues", "purples", "metallic"],
        "styles": ["futuristic", "space-themed", "tech-inspired"]
      }
    },
    "audience_preferences": {
      "young_adults": {
        "prefer": ["vibrant colors", "modern design", "series branding"]
      }
    },
    "competitive_insights": [
      "Successful sci-fi covers use high contrast",
      "Space imagery resonates with target audience",
      "Series consistency drives recognition"
    ],
    "recommendation_score": 0.94
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVICE_HOST` | Service host address | `0.0.0.0` |
| `SERVICE_PORT` | Service port | `8083` |
| `PREFERRED_MODEL` | AI model preference | `openai` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `ANTHROPIC_API_KEY` | Anthropic API key | Optional |
| `EVENT_BUS_URL` | Event Bus service URL | `http://event-bus:8080` |
| `SERVICE_DISCOVERY_URL` | Service Discovery URL | `http://service-discovery:8070` |
| `API_KEY` | Service API key | Required |

### Design Capabilities

- **Genre-Specific Design**: Conventions for fiction, non-fiction, mystery, romance, sci-fi, fantasy
- **Target Audience Optimization**: Young adult, professional, general audience customization
- **Style Categories**: Modern, minimalist, bold, vintage, professional, artistic
- **Market Analysis Depths**: Basic, standard, comprehensive analysis levels

## Development

### Local Development

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export API_KEY="your-service-api-key"
   ```

3. **Run Service**
   ```bash
   cd src
   python main.py
   ```

### Testing

Run all tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=src --cov-report=html
```

Run specific test category:
```bash
pytest tests/test_main_simple.py
pytest tests/test_cover_agent.py
pytest tests/test_monitoring.py
```

### Docker Development

1. **Build Image**
   ```bash
   docker build -t cover-designer-service .
   ```

2. **Run Container**
   ```bash
   docker run -p 8083:8083 \
     -e OPENAI_API_KEY="your-key" \
     -e API_KEY="your-api-key" \
     cover-designer-service
   ```

3. **Docker Compose**
   ```bash
   docker-compose up --build
   ```

## Architecture

### Components

- **Cover Designer Agent**: Core AI-powered cover design functionality
- **Market Analysis Agent**: Trend analysis and competitive intelligence
- **Event Client**: Communication with Event Bus service
- **Service Registry Client**: Registration with Service Discovery
- **Security Manager**: API key authentication and mTLS
- **Monitoring**: Prometheus metrics and health checks

### Dependencies

- **Infrastructure Services**: Event Bus, Service Discovery
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **Security**: mTLS certificates, API keys
- **Monitoring**: Prometheus metrics collection

### Data Flow

1. Client sends cover design request with authentication
2. Service validates request and API key
3. Market analysis performed (if not provided)
4. Cover Designer Agent processes request using AI model
5. Design optimization and scoring performed
6. Results published to Event Bus (async mode)
7. Metrics recorded for monitoring
8. Response returned to client

## Deployment

### Docker Compose
```yaml
services:
  cover-designer-service:
    build: .
    ports:
      - "8083:8083"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${COVER_DESIGNER_SERVICE_API_KEY}
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
    networks:
      - publish-ai-network
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cover-designer-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cover-designer-service
  template:
    metadata:
      labels:
        app: cover-designer-service
        component: microservice
        tier: tier-2
    spec:
      containers:
      - name: cover-designer-service
        image: cover-designer-service:latest
        ports:
        - containerPort: 8083
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
```

## Security

### Authentication
- API key authentication for all endpoints
- Service-to-service authentication via mTLS
- Security headers and CORS configuration

### mTLS Configuration
- Service certificates generated by internal CA
- Mutual authentication between services
- Encrypted inter-service communication

### Network Security
- Kubernetes NetworkPolicies for micro-segmentation
- Zero Trust networking principles
- Firewall rules for service isolation

## Monitoring

### Metrics
- HTTP request count and duration
- Cover design operation metrics
- Design effectiveness scores
- Market analysis counts
- Agent health status
- Event publication counts

### Health Checks
- `/health` - Basic health status
- `/ready` - Readiness for traffic
- Service Discovery integration

### Logging
- Structured logging with context
- Request tracing and correlation
- Error tracking and alerting

## Testing

### Test Coverage
- Unit tests for all components (18 tests)
- Integration tests for API endpoints (11 tests)
- Mock testing for external dependencies (11 tests)
- Performance and load testing capabilities

### Test Categories
- **Unit Tests**: Component-level testing
- **Integration Tests**: API endpoint testing
- **Contract Tests**: Service interface testing
- **Performance Tests**: Load and stress testing

**Total Test Coverage**: 40/40 tests passing (100%)

## Migration Status

✅ **Phase 2.2: Cover Designer Service Migration - COMPLETED**

- [x] Agent extraction and containerization
- [x] Dual-agent architecture (design + market analysis)
- [x] Event-driven communication integration
- [x] Service Discovery registration
- [x] Security implementation (API keys + mTLS)
- [x] Comprehensive testing suite (40 tests)
- [x] Docker containerization
- [x] Kubernetes deployment manifests
- [x] Monitoring and metrics
- [x] Documentation and deployment guides

**Service Metrics**: 40/40 tests passing, 7 API endpoints, production-ready deployment

## Cover Design Features

### Design Process
1. **Market Analysis**: Analyze current trends and competitive landscape
2. **Concept Development**: Create design concept based on genre and audience
3. **Color Palette**: Select colors based on psychology and market data
4. **Typography**: Choose fonts for optimal readability and genre appeal
5. **Layout Composition**: Arrange elements for visual impact and hierarchy
6. **Image Generation**: Create prompts for AI image generation
7. **Scoring & Optimization**: Evaluate design effectiveness and market alignment

### Supported Genres
- **Fiction**: Atmospheric, character-focused designs
- **Non-Fiction**: Professional, credible, value-focused designs
- **Mystery**: Dark, dramatic, suspenseful designs
- **Romance**: Warm, emotional, inviting designs
- **Science Fiction**: Futuristic, tech-inspired designs
- **Fantasy**: Magical, rich, atmospheric designs

### Target Audiences
- **Young Adults**: Vibrant, contemporary, digital-first designs
- **Professionals**: Sophisticated, credible, business-focused designs
- **General Readers**: Balanced, accessible, genre-appropriate designs

## Troubleshooting

### Common Issues

1. **AI Model Not Available**
   - Check API keys are set correctly
   - Verify model endpoints are accessible
   - Check rate limits and quotas

2. **Service Registration Failed**
   - Verify Service Discovery is running
   - Check network connectivity
   - Validate API key configuration

3. **Event Bus Connection Failed**
   - Check Event Bus service status
   - Verify network policies allow connection
   - Check authentication credentials

### Debug Commands

```bash
# Check service health
curl http://localhost:8083/health

# Verify readiness
curl http://localhost:8083/ready

# Get metrics
curl http://localhost:8083/metrics

# Test cover design endpoint
curl -X POST http://localhost:8083/design/sync \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "book_title": "Test Book",
    "author_name": "Test Author",
    "genre": "fiction",
    "target_audience": "young adults"
  }'

# Test market analysis
curl -X POST http://localhost:8083/design/market-analysis \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "genre": "science fiction",
    "target_audience": "young adults",
    "analysis_depth": "comprehensive"
  }'
```