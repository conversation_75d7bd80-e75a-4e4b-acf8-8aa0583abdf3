"""
Monitoring and Metrics for Cover Designer Service

Provides Prometheus metrics collection and health monitoring
specific to cover design operations and service performance.
"""

import logging
from typing import Optional

from fastapi import FastAPI

logger = logging.getLogger(__name__)

# Mock metrics classes when prometheus_client is not available
try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, make_asgi_app
    PROMETHEUS_AVAILABLE = True
except ImportError:
    logger.warning("prometheus_client not available, using mock metrics")
    Counter = Histogram = Gauge = CollectorRegistry = object
    PROMETHEUS_AVAILABLE = False
    
    def make_asgi_app(*args, **kwargs):
        """Mock ASGI app when prometheus not available"""
        async def mock_app(scope, receive, send):
            await send({
                'type': 'http.response.start',
                'status': 200,
                'headers': [[b'content-type', b'text/plain']],
            })
            await send({
                'type': 'http.response.body',
                'body': b'Metrics not available',
            })
        return mock_app

class CoverDesignerMetrics:
    """Metrics collection for Cover Designer Service operations"""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or (CollectorRegistry() if PROMETHEUS_AVAILABLE else None)
        
        if PROMETHEUS_AVAILABLE and self.registry:
            # Cover design operation metrics
            self.COVER_DESIGN_COUNT = Counter(
                'cover_design_total',
                'Total number of cover design requests',
                ['genre', 'target_audience'],
                registry=self.registry
            )
            
            self.COVER_DESIGN_DURATION = Histogram(
                'cover_design_duration_seconds',
                'Time spent on cover design operations',
                ['genre', 'operation_type'],
                registry=self.registry
            )
            
            self.COVER_DESIGN_SUCCESS_COUNT = Counter(
                'cover_design_success_total',
                'Total number of successful cover designs',
                ['genre'],
                registry=self.registry
            )
            
            self.COVER_DESIGN_ERROR_COUNT = Counter(
                'cover_design_error_total',
                'Total number of cover design errors',
                ['error_type'],
                registry=self.registry
            )
            
            # Market analysis metrics
            self.MARKET_ANALYSIS_COUNT = Counter(
                'market_analysis_total',
                'Total number of market analysis requests',
                ['genre', 'analysis_depth'],
                registry=self.registry
            )
            
            self.MARKET_ANALYSIS_DURATION = Histogram(
                'market_analysis_duration_seconds',
                'Time spent on market analysis operations',
                ['genre'],
                registry=self.registry
            )
            
            # Design quality metrics
            self.DESIGN_SCORE_HISTOGRAM = Histogram(
                'design_score_distribution',
                'Distribution of design effectiveness scores',
                ['genre'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                registry=self.registry
            )
            
            self.MARKET_ALIGNMENT_SCORE = Histogram(
                'market_alignment_score_distribution',
                'Distribution of market alignment scores',
                ['genre', 'target_audience'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                registry=self.registry
            )
            
            # Service health metrics
            self.AGENT_HEALTH = Gauge(
                'cover_agent_health',
                'Cover designer agent health status (1=healthy, 0=unhealthy)',
                registry=self.registry
            )
            
            self.EVENT_PUBLISH_COUNT = Counter(
                'event_publish_total',
                'Total number of events published',
                ['event_type'],
                registry=self.registry
            )
            
            self.EVENT_PUBLISH_ERROR_COUNT = Counter(
                'event_publish_error_total',
                'Total number of event publishing errors',
                ['event_type'],
                registry=self.registry
            )
            
            # HTTP metrics
            self.HTTP_REQUEST_COUNT = Counter(
                'http_requests_total',
                'Total number of HTTP requests',
                ['method', 'endpoint', 'status_code'],
                registry=self.registry
            )
            
            self.HTTP_REQUEST_DURATION = Histogram(
                'http_request_duration_seconds',
                'HTTP request duration in seconds',
                ['method', 'endpoint'],
                registry=self.registry
            )
            
        else:
            # Create mock metrics when Prometheus is not available
            self._create_mock_metrics()
    
    def _create_mock_metrics(self):
        """Create mock metrics when Prometheus is not available"""
        class MockMetric:
            def inc(self, *args, **kwargs): pass
            def dec(self, *args, **kwargs): pass
            def set(self, *args, **kwargs): pass
            def observe(self, *args, **kwargs): pass
            def time(self): 
                class MockTimer:
                    def __enter__(self): return self
                    def __exit__(self, *args): pass
                return MockTimer()
        
        self.COVER_DESIGN_COUNT = MockMetric()
        self.COVER_DESIGN_DURATION = MockMetric()
        self.COVER_DESIGN_SUCCESS_COUNT = MockMetric()
        self.COVER_DESIGN_ERROR_COUNT = MockMetric()
        self.MARKET_ANALYSIS_COUNT = MockMetric()
        self.MARKET_ANALYSIS_DURATION = MockMetric()
        self.DESIGN_SCORE_HISTOGRAM = MockMetric()
        self.MARKET_ALIGNMENT_SCORE = MockMetric()
        self.AGENT_HEALTH = MockMetric()
        self.EVENT_PUBLISH_COUNT = MockMetric()
        self.EVENT_PUBLISH_ERROR_COUNT = MockMetric()
        self.HTTP_REQUEST_COUNT = MockMetric()
        self.HTTP_REQUEST_DURATION = MockMetric()

# Global metrics instance
metrics = CoverDesignerMetrics()

def setup_monitoring(app: FastAPI):
    """Setup monitoring and metrics endpoints for the FastAPI application"""
    
    if PROMETHEUS_AVAILABLE and metrics.registry:
        # Add Prometheus metrics endpoint
        metrics_app = make_asgi_app(registry=metrics.registry)
        app.mount("/metrics", metrics_app)
        logger.info("Prometheus metrics endpoint mounted at /metrics")
    else:
        # Add mock metrics endpoint
        @app.get("/metrics")
        async def mock_metrics():
            return "# Metrics not available - prometheus_client not installed"
        logger.info("Mock metrics endpoint available at /metrics")
    
    # Add middleware for HTTP metrics
    if PROMETHEUS_AVAILABLE:
        @app.middleware("http")
        async def metrics_middleware(request, call_next):
            method = request.method
            path = request.url.path
            
            with metrics.HTTP_REQUEST_DURATION.labels(method=method, endpoint=path).time():
                response = await call_next(request)
            
            metrics.HTTP_REQUEST_COUNT.labels(
                method=method,
                endpoint=path,
                status_code=response.status_code
            ).inc()
            
            return response
    
    logger.info("Monitoring setup completed for Cover Designer Service")

def record_design_metrics(genre: str, design_score: float, market_alignment_score: float):
    """Record metrics for a completed cover design"""
    metrics.DESIGN_SCORE_HISTOGRAM.labels(genre=genre).observe(design_score)
    metrics.MARKET_ALIGNMENT_SCORE.labels(genre=genre, target_audience="general").observe(market_alignment_score)

def record_agent_health(is_healthy: bool):
    """Record agent health status"""
    metrics.AGENT_HEALTH.set(1 if is_healthy else 0)

def record_event_publication(event_type: str, success: bool):
    """Record event publication metrics"""
    if success:
        metrics.EVENT_PUBLISH_COUNT.labels(event_type=event_type).inc()
    else:
        metrics.EVENT_PUBLISH_ERROR_COUNT.labels(event_type=event_type).inc()