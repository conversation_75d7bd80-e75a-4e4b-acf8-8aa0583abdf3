"""
Cover Designer Agent - Standalone PydanticAI Implementation

Provides comprehensive book cover design capabilities with market analysis,
design optimization, and style recommendations.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel

from .prompt_template import PromptTemplate

logger = logging.getLogger(__name__)

# Result Models
class CoverDesignResult(BaseModel):
    """Result model for cover design operations"""
    design_concept: Dict[str, Any] = Field(description="The design concept and rationale")
    color_palette: Dict[str, Any] = Field(description="The color palette specification")
    typography: Dict[str, Any] = Field(description="The typography specifications")
    layout: Dict[str, Any] = Field(description="The layout and composition details")
    image_prompts: List[str] = Field(description="A list of prompts for generating images")
    design_score: float = Field(description="Design effectiveness score (0-1)")
    market_alignment: Dict[str, Any] = Field(description="Market trend alignment analysis")
    design_rationale: str = Field(description="Explanation of design choices")
    style_categories: List[str] = Field(description="Applied style categories")
    target_demographics: List[str] = Field(description="Aligned target demographics")
    competitive_analysis: Dict[str, Any] = Field(description="Competitive design analysis")
    timestamp: str = Field(description="Design creation timestamp")

class MarketAnalysisResult(BaseModel):
    """Result model for market analysis operations"""
    trending_styles: List[str] = Field(description="Currently trending design styles")
    color_trends: List[str] = Field(description="Popular color combinations")
    typography_trends: List[str] = Field(description="Popular typography styles")
    genre_conventions: Dict[str, Any] = Field(description="Genre-specific design conventions")
    audience_preferences: Dict[str, Any] = Field(description="Target audience preferences")
    competitive_insights: List[str] = Field(description="Competitive market insights")
    recommendation_score: float = Field(description="Market alignment confidence score")

class CoverDesignerAgent:
    """
    Standalone Cover Designer Agent for book cover design and market analysis
    """
    
    def __init__(self):
        self._agent: Optional[TypedAgent] = None
        self._market_agent: Optional[TypedAgent] = None
        self._initialized = False
        
    @property
    def is_ready(self) -> bool:
        """Check if agent is ready for operations"""
        return self._initialized and self._agent is not None and self._market_agent is not None
    
    def _get_available_model(self) -> Optional[Model]:
        """Get available AI model based on configuration"""
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        # Fallback to any available model
        if os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_design_prompt(self) -> str:
        """Get the cover design system prompt"""
        return """
        You are an expert book cover designer with extensive experience in publishing, 
        marketing, and visual design. Your expertise includes:

        - Genre-specific design conventions and market expectations
        - Color psychology and visual hierarchy principles
        - Typography selection for readability and genre appeal
        - Market trend analysis and competitive positioning
        - Target audience preferences and buying behavior
        - Print and digital format optimization

        For each cover design request, you will create a comprehensive design plan that includes:
        1. Design concept with clear rationale tied to genre and audience
        2. Color palette with psychological and market justification
        3. Typography specifications optimized for title hierarchy
        4. Layout composition ensuring visual impact and readability
        5. Image generation prompts for visual elements
        6. Market alignment analysis with competitive insights

        Focus on creating designs that are both visually striking and commercially viable,
        balancing artistic merit with market performance potential.
        """
    
    def _get_market_analysis_prompt(self) -> str:
        """Get the market analysis system prompt"""
        return """
        You are a book market analyst specializing in cover design trends and consumer behavior.
        Your expertise includes:

        - Current design trends across different genres and platforms
        - Consumer psychology and purchasing behavior analysis
        - Competitive landscape analysis and positioning strategies
        - Platform-specific design requirements (Amazon, bookstores, digital)
        - Demographic preference analysis and market segmentation
        - Historical trend analysis and future predictions

        Provide data-driven insights about design trends, audience preferences, and market
        opportunities that will inform effective cover design decisions.
        """
    
    async def initialize(self):
        """Initialize the cover designer agent"""
        if self._initialized:
            return
        
        model = self._get_available_model()
        if not model:
            raise RuntimeError("No AI model available. Check API keys.")
        
        try:
            # Initialize cover design agent
            self._agent = TypedAgent(
                model=model,
                result_type=CoverDesignResult,
                system_prompt=self._get_design_prompt()
            )
            
            # Initialize market analysis agent
            self._market_agent = TypedAgent(
                model=model,
                result_type=MarketAnalysisResult,
                system_prompt=self._get_market_analysis_prompt()
            )
            
            self._initialized = True
            logger.info("Cover Designer Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Cover Designer Agent: {e}")
            raise
    
    async def design_cover(
        self,
        book_title: str,
        author_name: str,
        genre: str,
        target_audience: str = "general adults",
        style_preferences: Optional[Dict[str, Any]] = None,
        market_data: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None
    ) -> CoverDesignResult:
        """
        Design a book cover with comprehensive market analysis
        
        Args:
            book_title: Title of the book
            author_name: Author's name
            genre: Book genre/category
            target_audience: Target audience description
            style_preferences: Optional style preferences
            market_data: Optional market analysis data
            user_id: Optional user ID for tracking
            
        Returns:
            CoverDesignResult with comprehensive design specifications
        """
        if not self.is_ready:
            raise RuntimeError("Cover designer agent not initialized")
        
        try:
            # Get market analysis if not provided
            if not market_data:
                market_analysis = await self.analyze_market_trends(
                    genre=genre,
                    target_audience=target_audience
                )
                market_data = market_analysis.model_dump()
            
            # Prepare design prompt
            prompt_template = PromptTemplate.from_string("""
            Design a book cover for:
            
            Title: {{ book_title }}
            Author: {{ author_name }}
            Genre: {{ genre }}
            Target Audience: {{ target_audience }}
            
            Style Preferences: {{ style_preferences }}
            Market Data: {{ market_data }}
            
            Create a comprehensive cover design that balances artistic appeal with commercial viability.
            Consider genre conventions, target audience preferences, and current market trends.
            Provide detailed specifications for all design elements and justify your choices.
            """)
            
            prompt = prompt_template.format(
                book_title=book_title,
                author_name=author_name,
                genre=genre,
                target_audience=target_audience,
                style_preferences=self._format_style_preferences(style_preferences or {}),
                market_data=self._format_market_data(market_data or {})
            )
            
            # Execute design
            result = await self._agent.run(prompt)
            
            # Enhance result with metadata
            enhanced_result = result.data
            enhanced_result.timestamp = datetime.utcnow().isoformat()
            
            logger.info(f"Cover design completed for '{book_title}' by {author_name}")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Cover design failed: {e}")
            raise
    
    async def analyze_market_trends(
        self,
        genre: str,
        target_audience: str = "general adults",
        analysis_depth: str = "standard"
    ) -> MarketAnalysisResult:
        """
        Analyze market trends for cover design guidance
        
        Args:
            genre: Book genre to analyze
            target_audience: Target audience description
            analysis_depth: Level of analysis (basic, standard, comprehensive)
            
        Returns:
            MarketAnalysisResult with trend insights
        """
        if not self.is_ready:
            raise RuntimeError("Market analysis agent not initialized")
        
        try:
            prompt_template = PromptTemplate.from_string("""
            Analyze current market trends for book cover design:
            
            Genre: {{ genre }}
            Target Audience: {{ target_audience }}
            Analysis Depth: {{ analysis_depth }}
            
            Provide comprehensive market analysis including:
            - Current design trends and popular styles
            - Color and typography preferences
            - Genre-specific conventions and expectations
            - Target audience behavior and preferences
            - Competitive landscape insights
            - Commercial viability factors
            
            Focus on actionable insights that will inform effective cover design decisions.
            """)
            
            prompt = prompt_template.format(
                genre=genre,
                target_audience=target_audience,
                analysis_depth=analysis_depth
            )
            
            result = await self._market_agent.run(prompt)
            
            logger.info(f"Market analysis completed for genre: {genre}")
            return result.data
            
        except Exception as e:
            logger.error(f"Market analysis failed: {e}")
            raise
    
    async def get_design_suggestions(
        self,
        genre: str,
        target_audience: str,
        style_context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Get design suggestions based on genre and audience
        
        Args:
            genre: Book genre
            target_audience: Target audience
            style_context: Optional style context
            
        Returns:
            List of design suggestions
        """
        suggestions = []
        
        # Genre-based suggestions
        genre_suggestions = {
            "fiction": [
                "Use atmospheric imagery that evokes the story's mood",
                "Consider character silhouettes or symbolic elements",
                "Balance title prominence with visual storytelling"
            ],
            "non-fiction": [
                "Emphasize credibility with clean, professional typography",
                "Use conceptual imagery that represents the book's value",
                "Ensure subtitle readability for topic clarity"
            ],
            "mystery": [
                "Incorporate dark, moody color palettes",
                "Use shadowing and contrast for dramatic effect",
                "Consider symbolic elements like keys, shadows, or silhouettes"
            ],
            "romance": [
                "Use warm, inviting color schemes",
                "Consider elegant typography with romantic flair",
                "Include imagery that suggests emotional connection"
            ],
            "science fiction": [
                "Use futuristic typography and color schemes",
                "Incorporate technological or space-themed imagery",
                "Balance innovation with genre recognition"
            ],
            "fantasy": [
                "Use rich, magical color palettes",
                "Consider mystical symbols and atmospheric imagery",
                "Balance fantastical elements with readability"
            ]
        }
        
        suggestions.extend(genre_suggestions.get(genre.lower(), [
            "Focus on clear hierarchy with title prominence",
            "Ensure genre conventions are respected",
            "Balance visual appeal with commercial viability"
        ]))
        
        # Audience-based suggestions
        if "young adult" in target_audience.lower():
            suggestions.extend([
                "Use vibrant, energetic color schemes",
                "Consider contemporary design trends",
                "Ensure appeal to digital-first audience"
            ])
        elif "professional" in target_audience.lower():
            suggestions.extend([
                "Emphasize credibility and expertise",
                "Use sophisticated color palettes",
                "Focus on clear value proposition"
            ])
        
        # Style context suggestions
        if style_context:
            if style_context.get("modern", False):
                suggestions.append("Incorporate contemporary design elements")
            if style_context.get("minimalist", False):
                suggestions.append("Focus on clean, uncluttered composition")
            if style_context.get("bold", False):
                suggestions.append("Use striking colors and dramatic typography")
        
        return suggestions
    
    def _format_style_preferences(self, preferences: Dict[str, Any]) -> str:
        """Format style preferences for prompt inclusion"""
        if not preferences:
            return "No specific style preferences provided"
        
        formatted = []
        for key, value in preferences.items():
            formatted.append(f"{key}: {value}")
        
        return "\n".join(formatted)
    
    def _format_market_data(self, market_data: Dict[str, Any]) -> str:
        """Format market data for prompt inclusion"""
        if not market_data:
            return "No specific market data provided"
        
        formatted = []
        for key, value in market_data.items():
            if isinstance(value, (dict, list)):
                formatted.append(f"{key}: {str(value)}")
            else:
                formatted.append(f"{key}: {value}")
        
        return "\n".join(formatted)
    
    async def cleanup(self):
        """Cleanup agent resources"""
        if self._initialized:
            self._agent = None
            self._market_agent = None
            self._initialized = False
            logger.info("Cover Designer Agent cleaned up")