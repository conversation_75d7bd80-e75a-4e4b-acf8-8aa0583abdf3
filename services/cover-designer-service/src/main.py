"""
Cover Designer Service - Main FastAPI Application

Provides book cover design capabilities as a microservice using PydanticAI.
Handles comprehensive cover design with market analysis and design optimization.
"""

import asyncio
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .cover_agent import CoverDesignerAgent, CoverDesignResult
from .event_client import EventClient
from .service_registry_client import ServiceRegistryClient
from .security_manager import SecurityManager, verify_api_key
from .monitoring import setup_monitoring, metrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global service instances
cover_agent: Optional[CoverDesignerAgent] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None

# Request/Response Models
class CoverDesignRequest(BaseModel):
    book_title: str = Field(..., description="Title of the book")
    author_name: str = Field(..., description="Name of the author")
    genre: str = Field(..., description="Book genre/category")
    target_audience: str = Field(default="general adults", description="Target audience")
    style_preferences: Optional[Dict[str, Any]] = Field(default=None, description="Design style preferences")
    market_data: Optional[Dict[str, Any]] = Field(default=None, description="Market analysis data")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class CoverDesignResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[CoverDesignResult] = Field(default=None, description="Cover design result")
    message: str = Field(..., description="Status message")

class CoverDesignSyncResponse(BaseModel):
    result: CoverDesignResult = Field(..., description="Cover design result")
    request_id: str = Field(..., description="Request identifier")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service health status")
    timestamp: str = Field(..., description="Health check timestamp")
    services: Dict[str, str] = Field(..., description="Dependent service status")

# Store for async requests
async_requests: Dict[str, Dict[str, Any]] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    global cover_agent, event_client, service_registry, security_manager
    
    logger.info("Starting Cover Designer Service...")
    
    try:
        # Initialize security manager
        security_manager = SecurityManager()
        await security_manager.initialize()
        
        # Initialize Cover Designer Agent
        cover_agent = CoverDesignerAgent()
        await cover_agent.initialize()
        
        # Initialize Event Client
        event_client = EventClient()
        await event_client.initialize()
        
        # Initialize Service Registry
        service_registry = ServiceRegistryClient()
        await service_registry.register_service(
            service_name="cover-designer-service",
            service_url=f"https://{os.getenv('SERVICE_HOST', '0.0.0.0')}:{os.getenv('SERVICE_PORT', '8083')}",
            health_check_url="/health",
            metadata={
                "version": "1.0.0",
                "tier": "tier-2",
                "capabilities": ["cover_design", "market_analysis", "design_optimization"]
            }
        )
        
        logger.info("Cover Designer Service initialized successfully")
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Cover Designer Service...")
        if service_registry:
            await service_registry.deregister_service("cover-designer-service")
        if cover_agent:
            await cover_agent.cleanup()
        if event_client:
            await event_client.cleanup()

# Create FastAPI app
app = FastAPI(
    title="Cover Designer Service",
    description="AI-powered book cover design service with market analysis",
    version="1.0.0",
    lifespan=lifespan
)

# Setup monitoring
setup_monitoring(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    services_status = {
        "cover_agent": "healthy" if cover_agent and cover_agent.is_ready else "unhealthy",
        "event_client": "healthy" if event_client and event_client.is_connected else "unhealthy",
        "service_registry": "healthy" if service_registry and service_registry.is_registered else "unhealthy"
    }
    
    overall_status = "healthy" if all(status == "healthy" for status in services_status.values()) else "unhealthy"
    
    return HealthResponse(
        status=overall_status,
        timestamp=datetime.utcnow().isoformat(),
        services=services_status
    )

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    if not cover_agent or not cover_agent.is_ready:
        raise HTTPException(status_code=503, detail="Cover agent not ready")
    return {"status": "ready"}

@app.post("/design", response_model=CoverDesignResponse)
async def design_cover_async(
    request: CoverDesignRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Start asynchronous cover design"""
    request_id = str(uuid.uuid4())
    
    # Store request
    async_requests[request_id] = {
        "status": "processing",
        "request": request.model_dump(),
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Add background task
    background_tasks.add_task(
        process_design_request,
        request_id,
        request
    )
    
    return CoverDesignResponse(
        request_id=request_id,
        status="accepted",
        message="Cover design request accepted and processing"
    )

@app.get("/design/{request_id}", response_model=CoverDesignResponse)
async def get_design_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Get cover design status and results"""
    if request_id not in async_requests:
        raise HTTPException(status_code=404, detail="Request not found")
    
    request_data = async_requests[request_id]
    
    return CoverDesignResponse(
        request_id=request_id,
        status=request_data["status"],
        result=request_data.get("result"),
        message=request_data.get("message", "Processing")
    )

@app.post("/design/sync", response_model=CoverDesignSyncResponse)
async def design_cover_sync(
    request: CoverDesignRequest,
    api_key: str = Depends(verify_api_key)
):
    """Synchronous cover design execution"""
    if not cover_agent or not cover_agent.is_ready:
        raise HTTPException(status_code=503, detail="Cover agent not ready")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Record metrics
        with metrics.COVER_DESIGN_DURATION.time():
            result = await cover_agent.design_cover(
                book_title=request.book_title,
                author_name=request.author_name,
                genre=request.genre,
                target_audience=request.target_audience,
                style_preferences=request.style_preferences,
                market_data=request.market_data,
                user_id=request.user_id
            )
        
        metrics.COVER_DESIGN_COUNT.inc()
        metrics.COVER_DESIGN_SUCCESS_COUNT.inc()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "cover_design_completed",
                {
                    "request_id": request_id,
                    "book_title": request.book_title,
                    "author_name": request.author_name,
                    "genre": request.genre,
                    "design_score": result.design_score,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return CoverDesignSyncResponse(
            result=result,
            request_id=request_id
        )
        
    except Exception as e:
        metrics.COVER_DESIGN_ERROR_COUNT.inc()
        logger.error(f"Cover design failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cover design failed: {str(e)}")

@app.post("/design/market-analysis")
async def analyze_market_for_design(
    request: Dict[str, Any],
    api_key: str = Depends(verify_api_key)
):
    """Analyze market trends for cover design"""
    if not cover_agent or not cover_agent.is_ready:
        raise HTTPException(status_code=503, detail="Cover agent not ready")
    
    try:
        analysis = await cover_agent.analyze_market_trends(
            genre=request.get("genre"),
            target_audience=request.get("target_audience", "general adults"),
            analysis_depth=request.get("analysis_depth", "standard")
        )
        
        return {"analysis": analysis, "timestamp": datetime.utcnow().isoformat()}
        
    except Exception as e:
        logger.error(f"Market analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Market analysis failed: {str(e)}")

async def process_design_request(request_id: str, request: CoverDesignRequest):
    """Process cover design request asynchronously"""
    try:
        async_requests[request_id]["status"] = "processing"
        
        result = await cover_agent.design_cover(
            book_title=request.book_title,
            author_name=request.author_name,
            genre=request.genre,
            target_audience=request.target_audience,
            style_preferences=request.style_preferences,
            market_data=request.market_data,
            user_id=request.user_id
        )
        
        async_requests[request_id].update({
            "status": "completed",
            "result": result,
            "message": "Cover design completed successfully"
        })
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "cover_design_completed",
                {
                    "request_id": request_id,
                    "book_title": request.book_title,
                    "author_name": request.author_name,
                    "genre": request.genre,
                    "design_score": result.design_score,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        metrics.COVER_DESIGN_SUCCESS_COUNT.inc()
        
    except Exception as e:
        logger.error(f"Async cover design failed for request {request_id}: {e}")
        async_requests[request_id].update({
            "status": "failed",
            "message": f"Cover design failed: {str(e)}"
        })
        metrics.COVER_DESIGN_ERROR_COUNT.inc()

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail, "request_id": str(uuid.uuid4())}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "request_id": str(uuid.uuid4())}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("SERVICE_HOST", "0.0.0.0"),
        port=int(os.getenv("SERVICE_PORT", "8083")),
        reload=True
    )