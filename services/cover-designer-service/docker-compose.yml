version: '3.8'

services:
  cover-designer-service:
    build: .
    ports:
      - "8083:8083"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8083
      - SERVICE_NAME=cover-designer-service
      - PREFERRED_MODEL=openai
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - API_KEY=${COVER_DESIGNER_SERVICE_API_KEY}
      - EVENT_BUS_URL=http://event-bus:8080
      - SERVICE_DISCOVERY_URL=http://service-discovery:8070
      - ENVIRONMENT=development
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
    networks:
      - publish-ai-network
    depends_on:
      - redis
      - event-bus
      - service-discovery
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - publish-ai-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  event-bus:
    image: publish-ai/event-bus:latest
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379
      - API_KEY=${EVENT_BUS_API_KEY}
    networks:
      - publish-ai-network
    depends_on:
      - redis
    restart: unless-stopped

  service-discovery:
    image: publish-ai/service-discovery:latest
    ports:
      - "8070:8070"
    environment:
      - REDIS_URL=redis://redis:6379
      - API_KEY=${SERVICE_DISCOVERY_API_KEY}
    networks:
      - publish-ai-network
    depends_on:
      - redis
    restart: unless-stopped

networks:
  publish-ai-network:
    external: true

volumes:
  redis_data: