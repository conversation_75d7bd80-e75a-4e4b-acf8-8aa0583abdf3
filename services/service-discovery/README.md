# Service Discovery Service

A production-ready service discovery and registry system for the Publish AI microservices architecture. This service provides dynamic service registration, health monitoring, and service lookup capabilities essential for a distributed system.

## Features

### Core Functionality
- **Dynamic Service Registration**: Automatic service registration with metadata
- **Health Monitoring**: Continuous health checks with configurable intervals
- **Service Lookup**: Fast service discovery with load balancing
- **Graceful Shutdown**: Clean deregistration on service termination
- **Failure Detection**: Automatic removal of unhealthy services

### Advanced Features
- **Service Metadata**: Tags, versions, and custom attributes
- **Load Balancing**: Round-robin and weighted routing strategies
- **Circuit Breaker Integration**: Health-aware routing decisions
- **Event Notifications**: Real-time service state changes
- **Admin Dashboard**: Web UI for service monitoring and management

### Production Features
- **Redis Backend**: Persistent service registry with clustering support
- **API Authentication**: Secure service registration with API keys
- **Monitoring**: Prometheus metrics and OpenTelemetry tracing
- **High Availability**: Multi-instance deployment with leader election
- **Configuration Management**: Environment-based configuration

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Agent Service │    │ Service Discovery│    │   Client Service│
│                 │    │                  │    │                 │
│ 1. Register     │───▶│ ┌──────────────┐ │◀───│ 3. Discover     │
│ 2. Heartbeat    │───▶│ │    Redis     │ │    │ 4. Health Check │
│ 4. Deregister   │───▶│ │   Registry   │ │    │                 │
│                 │    │ └──────────────┘ │    │                 │
└─────────────────┘    │                  │    └─────────────────┘
                       │ ┌──────────────┐ │
                       │ │ Health Monitor│ │
                       │ │ Background Job│ │
                       │ └──────────────┘ │
                       └──────────────────┘
```

## API Endpoints

### Service Registration
- `POST /services/register` - Register a new service
- `PUT /services/{service_id}/heartbeat` - Send heartbeat
- `DELETE /services/{service_id}` - Deregister service

### Service Discovery
- `GET /services` - List all services
- `GET /services/{service_name}` - Find services by name
- `GET /services/{service_name}/instances` - Get healthy instances
- `GET /services/{service_name}/health` - Check service health

### Administration
- `GET /admin/dashboard` - Web-based admin interface
- `GET /admin/stats` - Service registry statistics
- `POST /admin/services/{service_id}/force-remove` - Force remove service

### Monitoring
- `GET /health` - Service health check
- `GET /metrics` - Prometheus metrics
- `GET /ready` - Readiness probe

## Service Registration Model

```json
{
  "service_id": "content-generator-001",
  "service_name": "content-generator",
  "service_version": "1.0.0",
  "host": "content-generator.internal",
  "port": 8081,
  "protocol": "http",
  "health_check_url": "/health",
  "metadata": {
    "tier": "1",
    "capabilities": ["manuscript-generation", "ai-content"],
    "max_concurrent_requests": 10
  },
  "tags": ["ai-service", "content", "critical"]
}
```

## Configuration

### Environment Variables
```bash
# Service Configuration
SERVICE_DISCOVERY_PORT=8070
SERVICE_DISCOVERY_HOST=0.0.0.0
SERVICE_DISCOVERY_DEBUG=false

# Redis Configuration
SERVICE_DISCOVERY_REDIS_URL=redis://localhost:6379
SERVICE_DISCOVERY_REDIS_DB=2
SERVICE_DISCOVERY_REDIS_PASSWORD=

# Health Check Configuration
SERVICE_DISCOVERY_HEALTH_CHECK_INTERVAL=30
SERVICE_DISCOVERY_HEALTH_CHECK_TIMEOUT=10
SERVICE_DISCOVERY_HEALTH_CHECK_RETRIES=3
SERVICE_DISCOVERY_UNHEALTHY_THRESHOLD=3

# Security
SERVICE_DISCOVERY_API_KEYS=service1:key1,service2:key2
SERVICE_DISCOVERY_ADMIN_TOKEN=admin-secret-token

# Load Balancing
SERVICE_DISCOVERY_LB_STRATEGY=round_robin
SERVICE_DISCOVERY_LB_WEIGHTS_ENABLED=true
```

## Usage Examples

### Register a Service
```python
import httpx

# Register content generator service
registration_data = {
    "service_id": "content-generator-001",
    "service_name": "content-generator", 
    "host": "*************",
    "port": 8081,
    "health_check_url": "/health",
    "metadata": {"tier": "1", "max_requests": 10},
    "tags": ["ai-service", "critical"]
}

async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://service-discovery:8070/services/register",
        json=registration_data,
        headers={"Authorization": "Bearer service-api-key"}
    )
    print(f"Registered: {response.json()}")
```

### Discover Services
```python
# Find content generator instances
async with httpx.AsyncClient() as client:
    response = await client.get(
        "http://service-discovery:8070/services/content-generator/instances"
    )
    instances = response.json()["instances"]
    
    # Select healthy instance for load balancing
    if instances:
        selected = instances[0]  # Round-robin handled by service
        service_url = f"http://{selected['host']}:{selected['port']}"
        print(f"Using service: {service_url}")
```

### Send Heartbeat
```python
# Keep service alive with periodic heartbeats
import asyncio

async def heartbeat_loop():
    while True:
        try:
            async with httpx.AsyncClient() as client:
                await client.put(
                    "http://service-discovery:8070/services/content-generator-001/heartbeat",
                    headers={"Authorization": "Bearer service-api-key"}
                )
                print("Heartbeat sent")
        except Exception as e:
            print(f"Heartbeat failed: {e}")
        
        await asyncio.sleep(30)  # Send every 30 seconds
```

## Health Monitoring

The service discovery system continuously monitors registered services:

1. **Periodic Health Checks**: HTTP GET requests to service health endpoints
2. **Heartbeat Tracking**: Services must send periodic heartbeats
3. **Failure Detection**: Services failing health checks are marked unhealthy
4. **Automatic Cleanup**: Unhealthy services are removed after threshold

### Health Check Flow
```
1. Service registers with health_check_url
2. Service Discovery polls health_check_url every 30s
3. If health check fails 3 times consecutively:
   - Service marked as unhealthy
   - Service excluded from discovery results
   - After 5 minutes, service removed from registry
```

## Load Balancing

Built-in load balancing strategies:

### Round Robin (Default)
```python
# Automatically cycles through healthy instances
instances = await discover_service("content-generator")
# Returns instances in rotating order
```

### Weighted Round Robin
```python
# Register with weights in metadata
registration_data = {
    "metadata": {
        "weight": 10  # Higher weight = more traffic
    }
}
```

### Least Connections
```python
# Based on active connection tracking
# Automatically routes to instance with fewest active requests
```

## Integration with Event Bus

The Service Discovery service integrates with the Event Bus for real-time notifications:

```python
# Service state change events
{
    "event_type": "service.registered",
    "payload": {
        "service_id": "content-generator-001",
        "service_name": "content-generator",
        "timestamp": "2025-01-15T10:30:00Z"
    }
}

{
    "event_type": "service.health_changed", 
    "payload": {
        "service_id": "content-generator-001",
        "old_status": "healthy",
        "new_status": "unhealthy",
        "reason": "health_check_failed"
    }
}
```

## Deployment

### Docker Compose
```yaml
version: '3.8'
services:
  service-discovery:
    build: .
    ports:
      - "8070:8070"
    environment:
      - SERVICE_DISCOVERY_REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - event-bus
```

### Production Deployment
- Deploy multiple instances for high availability
- Use Redis Cluster for registry persistence
- Configure proper health check endpoints
- Set up monitoring and alerting
- Enable TLS for secure communication

## Monitoring and Observability

### Prometheus Metrics
- `service_registry_total` - Total registered services
- `service_health_checks_total` - Health check counters
- `service_discovery_requests_total` - API request metrics
- `service_registry_size` - Current registry size

### Structured Logging
- Service registration/deregistration events
- Health check results and failures
- Load balancing decisions
- Error conditions and recovery

This Service Discovery service provides the essential infrastructure for dynamic service management in the Publish AI microservices ecosystem.