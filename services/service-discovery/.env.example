# Service Discovery Configuration

# Service Configuration
SERVICE_DISCOVERY_HOST=0.0.0.0
SERVICE_DISCOVERY_PORT=8070
SERVICE_DISCOVERY_DEBUG=false
SERVICE_DISCOVERY_WORKERS=1

# Redis Configuration
SERVICE_DISCOVERY_REDIS_URL=redis://localhost:6379
SERVICE_DISCOVERY_REDIS_PASSWORD=
SERVICE_DISCOVERY_REDIS_DB=2
SERVICE_DISCOVERY_REDIS_MAX_CONNECTIONS=20

# Service Registry Configuration
SERVICE_DISCOVERY_REGISTRY_TTL_SECONDS=300
SERVICE_DISCOVERY_CLEANUP_INTERVAL_SECONDS=60
SERVICE_DISCOVERY_SERVICE_TIMEOUT_SECONDS=180

# Health Check Configuration
SERVICE_DISCOVERY_HEALTH_CHECK_INTERVAL=30
SERVICE_DISCOVERY_HEALTH_CHECK_TIMEOUT=10
SERVICE_DISCOVERY_HEALTH_CHECK_RETRIES=3
SERVICE_DISCOVERY_UNHEALTHY_THRESHOLD=3
SERVICE_DISCOVERY_HEALTH_CHECK_WORKERS=5

# Load Balancing Configuration
SERVICE_DISCOVERY_DEFAULT_LB_STRATEGY=round_robin
SERVICE_DISCOVERY_ENABLE_WEIGHTED_LB=true
SERVICE_DISCOVERY_CONNECTION_TRACKING=true

# Security Configuration
SERVICE_DISCOVERY_REQUIRE_AUTH=true
SERVICE_DISCOVERY_API_KEYS=event-bus:event-bus-key,trend-analyzer:trend-key,content-generator:content-key
SERVICE_DISCOVERY_ADMIN_TOKEN=admin-secret-token

# CORS Configuration
SERVICE_DISCOVERY_ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# Event Bus Integration
SERVICE_DISCOVERY_EVENT_BUS_ENABLED=true
SERVICE_DISCOVERY_EVENT_BUS_URL=http://event-bus:8080
SERVICE_DISCOVERY_EVENT_BUS_API_KEY=service-discovery-key

# Circuit Breaker Configuration
SERVICE_DISCOVERY_CIRCUIT_BREAKER_ENABLED=true
SERVICE_DISCOVERY_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
SERVICE_DISCOVERY_CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3
SERVICE_DISCOVERY_CIRCUIT_BREAKER_TIMEOUT_SECONDS=60

# Monitoring Configuration
SERVICE_DISCOVERY_METRICS_ENABLED=true
SERVICE_DISCOVERY_TRACING_ENABLED=true
SERVICE_DISCOVERY_STRUCTURED_LOGGING=true

# Admin Dashboard Configuration
SERVICE_DISCOVERY_ADMIN_DASHBOARD_ENABLED=true
SERVICE_DISCOVERY_ADMIN_DASHBOARD_PATH=/admin

# Performance Configuration
SERVICE_DISCOVERY_MAX_SERVICES_PER_NAME=100
SERVICE_DISCOVERY_REQUEST_TIMEOUT_SECONDS=30
SERVICE_DISCOVERY_BACKGROUND_TASK_TIMEOUT=300

# High Availability Configuration
SERVICE_DISCOVERY_LEADER_ELECTION_ENABLED=false
SERVICE_DISCOVERY_LEADER_ELECTION_TTL=30
SERVICE_DISCOVERY_LEADER_ELECTION_KEY=service-discovery:leader

# Data Retention Configuration
SERVICE_DISCOVERY_METRICS_RETENTION_DAYS=7
SERVICE_DISCOVERY_EVENT_LOG_RETENTION_DAYS=30
SERVICE_DISCOVERY_HEALTH_CHECK_HISTORY_HOURS=24