version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: service-discovery-redis
    ports:
      - "6380:6379"  # Use different port to avoid conflicts
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - service-discovery-network

  service-discovery:
    build: .
    container_name: service-discovery-service
    ports:
      - "8070:8070"
    environment:
      - SERVICE_DISCOVERY_REDIS_URL=redis://redis:6379
      - SERVICE_DISCOVERY_REDIS_PASSWORD=${REDIS_PASSWORD:-defaultpassword}
      - SERVICE_DISCOVERY_REDIS_DB=2
      - SERVICE_DISCOVERY_DEBUG=${DEBUG:-false}
      - SERVICE_DISCOVERY_EVENT_BUS_ENABLED=${EVENT_BUS_ENABLED:-true}
      - SERVICE_DISCOVERY_EVENT_BUS_URL=${EVENT_BUS_URL:-http://event-bus:8080}
      - SERVICE_DISCOVERY_EVENT_BUS_API_KEY=${EVENT_BUS_API_KEY}
      - SERVICE_DISCOVERY_ADMIN_TOKEN=${ADMIN_TOKEN:-admin-secret-token}
      - SERVICE_DISCOVERY_API_KEYS=${API_KEYS:-event-bus:event-bus-key,test-service:test-key}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8070/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - service-discovery-network
    volumes:
      - ./logs:/app/logs

  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: service-discovery-redis-exporter
    ports:
      - "9122:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-defaultpassword}
    depends_on:
      - redis
    networks:
      - service-discovery-network

volumes:
  redis_data:
    driver: local

networks:
  service-discovery-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

  # External network for connecting to Event Bus
  event-bus-network:
    external: true