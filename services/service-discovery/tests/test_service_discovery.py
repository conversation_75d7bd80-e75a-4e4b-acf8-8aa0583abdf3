"""
Service Discovery Tests
Basic test structure for the Service Discovery service
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi.testclient import TestClient

from src.main import create_app
from src.models import ServiceRegistration, ServiceStatus, ServiceProtocol
from src.config import Settings


@pytest.fixture
def test_settings():
    """Test configuration settings"""
    return Settings(
        debug=True,
        redis_url="redis://localhost:6379/3",  # Use test database
        require_auth=False,  # Disable auth for tests
        event_bus_enabled=False,  # Disable Event Bus for tests
        admin_dashboard_enabled=True
    )


@pytest.fixture
def client():
    """FastAPI test client"""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_registration():
    """Sample service registration"""
    return ServiceRegistration(
        service_id="test-service-001",
        service_name="test-service",
        service_version="1.0.0",
        host="127.0.0.1",
        port=8081,
        protocol=ServiceProtocol.HTTP,
        health_check_url="/health",
        metadata={"tier": "test"},
        tags=["test", "example"]
    )


class TestServiceDiscoveryAPI:
    """Test Service Discovery API endpoints"""
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_registry = AsyncMock()
            mock_registry.redis_client.ping.return_value = True
            mock_app.state.registry = mock_registry
            mock_app.state.settings.event_bus_enabled = False
            
            response = client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "service-discovery"
    
    def test_readiness_endpoint(self, client):
        """Test readiness check endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_registry = AsyncMock()
            mock_registry.redis_client.ping.return_value = True
            mock_app.state.registry = mock_registry
            
            response = client.get("/ready")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ready"
    
    def test_register_service_endpoint(self, client, sample_registration):
        """Test service registration endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_registry = AsyncMock()
            mock_event_bus = None
            mock_metrics = MagicMock()
            
            # Mock successful registration
            from src.models import ServiceInstance
            mock_instance = ServiceInstance(**sample_registration.model_dump())
            mock_registry.register_service.return_value = mock_instance
            
            mock_app.state.registry = mock_registry
            mock_app.state.event_bus = mock_event_bus
            mock_app.state.settings.event_bus_enabled = False
            
            with patch('src.main.get_metrics_collector', return_value=mock_metrics):
                response = client.post(
                    "/services/register",
                    json=sample_registration.model_dump()
                )
            
            assert response.status_code == 200
            data = response.json()
            assert data["service_id"] == sample_registration.service_id
            assert data["service_name"] == sample_registration.service_name
    
    def test_list_services_endpoint(self, client):
        """Test list all services endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_registry = AsyncMock()
            
            # Mock service instances
            from src.models import ServiceInstance
            mock_instances = [
                ServiceInstance(
                    service_id="test-1",
                    service_name="test-service",
                    service_version="1.0.0",
                    host="127.0.0.1",
                    port=8081,
                    protocol=ServiceProtocol.HTTP,
                    health_check_url="/health",
                    health_check_interval=30,
                    health_check_timeout=10,
                    metadata={},
                    tags=[],
                    weight=1,
                    max_connections=None,
                    status=ServiceStatus.HEALTHY
                )
            ]
            
            mock_registry.get_all_services.return_value = mock_instances
            mock_app.state.registry = mock_registry
            
            response = client.get("/services")
            
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["service_id"] == "test-1"
    
    def test_discover_service_endpoint(self, client):
        """Test service discovery endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_registry = AsyncMock()
            mock_metrics = MagicMock()
            
            # Mock discovery response
            from src.models import ServiceDiscoveryResponse, ServiceInstance, LoadBalancingStrategy
            mock_response = ServiceDiscoveryResponse(
                service_name="test-service",
                total_instances=1,
                healthy_instances=1,
                instances=[
                    ServiceInstance(
                        service_id="test-1",
                        service_name="test-service",
                        service_version="1.0.0",
                        host="127.0.0.1",
                        port=8081,
                        protocol=ServiceProtocol.HTTP,
                        health_check_url="/health",
                        health_check_interval=30,
                        health_check_timeout=10,
                        metadata={},
                        tags=[],
                        weight=1,
                        max_connections=None,
                        status=ServiceStatus.HEALTHY
                    )
                ],
                load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN
            )
            
            mock_registry.discover_services.return_value = mock_response
            mock_app.state.registry = mock_registry
            
            with patch('src.main.get_metrics_collector', return_value=mock_metrics):
                response = client.get("/services/test-service")
            
            assert response.status_code == 200
            data = response.json()
            assert data["service_name"] == "test-service"
            assert data["total_instances"] == 1


class TestServiceRegistration:
    """Test service registration model"""
    
    def test_valid_registration(self, sample_registration):
        """Test valid service registration"""
        assert sample_registration.service_id == "test-service-001"
        assert sample_registration.service_name == "test-service"
        assert sample_registration.host == "127.0.0.1"
        assert sample_registration.port == 8081
    
    def test_invalid_service_id(self):
        """Test invalid service ID validation"""
        with pytest.raises(ValueError):
            ServiceRegistration(
                service_id="ab",  # Too short
                service_name="test-service",
                host="127.0.0.1",
                port=8081
            )
    
    def test_invalid_port(self):
        """Test invalid port validation"""
        with pytest.raises(ValueError):
            ServiceRegistration(
                service_id="test-service-001",
                service_name="test-service",
                host="127.0.0.1",
                port=70000  # Too high
            )


class TestConfiguration:
    """Test configuration management"""
    
    def test_default_settings(self):
        """Test default configuration values"""
        settings = Settings()
        
        assert settings.host == "0.0.0.0"
        assert settings.port == 8070
        assert settings.redis_db == 2
        assert settings.health_check_interval == 30
        assert settings.require_auth is True
    
    def test_validation(self):
        """Test configuration validation"""
        # This should not raise any errors
        from src.config import validate_settings
        
        settings = Settings(
            redis_url="redis://localhost:6379",
            health_check_timeout=5,
            health_check_interval=30,
            unhealthy_threshold=3
        )
        
        # Mock the settings for validation
        with patch('src.config.settings', settings):
            validate_settings()  # Should not raise


class TestServiceRegistry:
    """Test service registry functionality"""
    
    @pytest.mark.asyncio
    async def test_registry_initialization(self, test_settings):
        """Test registry initialization"""
        from src.registry import ServiceRegistry
        
        registry = ServiceRegistry(test_settings)
        
        # Mock Redis client
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        
        with patch('redis.asyncio.from_url', return_value=mock_redis):
            await registry.initialize()
        
        assert registry.redis_client is not None
    
    @pytest.mark.asyncio
    async def test_service_registration(self, test_settings, sample_registration):
        """Test service registration"""
        from src.registry import ServiceRegistry
        
        registry = ServiceRegistry(test_settings)
        
        # Mock Redis operations
        mock_redis = AsyncMock()
        registry.redis_client = mock_redis
        
        instance = await registry.register_service(sample_registration)
        
        assert instance.service_id == sample_registration.service_id
        assert instance.service_name == sample_registration.service_name
        assert instance.status == ServiceStatus.STARTING


if __name__ == "__main__":
    pytest.main([__file__, "-v"])