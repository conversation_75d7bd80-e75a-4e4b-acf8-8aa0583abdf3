#!/bin/bash

# Service Discovery Service Startup Script

set -e

echo "Starting Service Discovery Service..."

# Check environment variables
if [ -z "$SERVICE_DISCOVERY_REDIS_URL" ]; then
    echo "Warning: SERVICE_DISCOVERY_REDIS_URL not set, using default"
    export SERVICE_DISCOVERY_REDIS_URL="redis://localhost:6379"
fi

if [ -z "$SERVICE_DISCOVERY_HOST" ]; then
    export SERVICE_DISCOVERY_HOST="0.0.0.0"
fi

if [ -z "$SERVICE_DISCOVERY_PORT" ]; then
    export SERVICE_DISCOVERY_PORT="8070"
fi

# Wait for Redis to be available
echo "Waiting for Redis..."
until redis-cli -u $SERVICE_DISCOVERY_REDIS_URL ping > /dev/null 2>&1; do
    echo "Waiting for Redis to start..."
    sleep 2
done
echo "Redis is ready!"

# Wait for Event Bus if enabled
if [ "$SERVICE_DISCOVERY_EVENT_BUS_ENABLED" = "true" ] && [ -n "$SERVICE_DISCOVERY_EVENT_BUS_URL" ]; then
    echo "Waiting for Event Bus..."
    until curl -f $SERVICE_DISCOVERY_EVENT_BUS_URL/health > /dev/null 2>&1; do
        echo "Waiting for Event Bus to start..."
        sleep 2
    done
    echo "Event Bus is ready!"
fi

# Initialize service registry if needed
echo "Initializing service registry..."
python -c "
import asyncio
from src.registry import ServiceRegistry
from src.config import Settings

async def setup():
    settings = Settings()
    registry = ServiceRegistry(settings)
    await registry.initialize()
    await registry.close()
    print('Service registry initialized')

asyncio.run(setup())
"

# Start the service
echo "Starting Service Discovery Service on port $SERVICE_DISCOVERY_PORT..."
exec uvicorn src.main:app \
    --host $SERVICE_DISCOVERY_HOST \
    --port $SERVICE_DISCOVERY_PORT \
    --workers ${SERVICE_DISCOVERY_WORKERS:-1} \
    --log-level info \
    --access-log