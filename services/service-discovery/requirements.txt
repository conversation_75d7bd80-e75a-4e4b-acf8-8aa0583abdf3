# Service Discovery Service Dependencies

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Redis Client
redis[hiredis]==5.0.1

# HTTP Client for Health Checks
httpx==0.25.2
aiohttp==3.9.1

# Background Tasks
celery==5.3.4
redis==5.0.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring and Observability
prometheus-client==0.19.0
structlog==23.2.0

# Optional: OpenTelemetry
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-instrumentation-httpx==0.42b0

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2
fakeredis==2.20.1

# Utilities
python-dateutil==2.8.2
asyncio-mqtt==0.16.1