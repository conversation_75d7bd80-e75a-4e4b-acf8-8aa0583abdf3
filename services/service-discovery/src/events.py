"""
Event Bus Integration
Client for publishing service lifecycle events to the Event Bus
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Optional
from uuid import uuid4

import httpx
import structlog

from .config import Settings

logger = structlog.get_logger(__name__)


class EventBusClient:
    """Client for integrating with the Event Bus service"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.event_bus_url = settings.event_bus_url
        self.api_key = settings.event_bus_api_key
        self.client: Optional[httpx.AsyncClient] = None
        
    async def initialize(self):
        """Initialize Event Bus client"""
        try:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
            
            # Test connectivity
            await self.health_check()
            logger.info("Event Bus client initialized")
            
        except Exception as e:
            logger.error("Failed to initialize Event Bus client", error=str(e))
            # Don't raise - service discovery should work without Event Bus
    
    async def close(self):
        """Close Event Bus client"""
        if self.client:
            await self.client.aclose()
            logger.info("Event Bus client closed")
    
    async def health_check(self) -> bool:
        """Check Event Bus connectivity"""
        try:
            if not self.client:
                return False
            
            response = await self.client.get(f"{self.event_bus_url}/health")
            return response.status_code == 200
            
        except Exception as e:
            logger.debug("Event Bus health check failed", error=str(e))
            return False
    
    async def publish_service_event(
        self,
        event_type: str,
        service_id: str,
        service_name: str,
        details: Dict
    ):
        """Publish a service lifecycle event"""
        try:
            if not self.client:
                logger.debug("Event Bus client not available, skipping event")
                return
            
            event_data = {
                "event_type": event_type,
                "source_service": "service-discovery",
                "payload": {
                    "service_id": service_id,
                    "service_name": service_name,
                    "timestamp": datetime.utcnow().isoformat(),
                    "details": details
                },
                "priority": "medium",
                "topic": "service-lifecycle"
            }
            
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            response = await self.client.post(
                f"{self.event_bus_url}/events/publish",
                json=event_data,
                headers=headers
            )
            
            if response.status_code == 200:
                logger.debug(
                    "Service event published",
                    event_type=event_type,
                    service_id=service_id
                )
            else:
                logger.warning(
                    "Failed to publish service event",
                    event_type=event_type,
                    status_code=response.status_code,
                    response=response.text
                )
                
        except Exception as e:
            logger.error(
                "Error publishing service event",
                event_type=event_type,
                service_id=service_id,
                error=str(e)
            )
    
    async def publish_health_change_event(
        self,
        service_id: str,
        service_name: str,
        old_status: str,
        new_status: str,
        reason: str
    ):
        """Publish service health change event"""
        await self.publish_service_event(
            "service.health_changed",
            service_id,
            service_name,
            {
                "old_status": old_status,
                "new_status": new_status,
                "reason": reason,
                "change_time": datetime.utcnow().isoformat()
            }
        )
    
    async def publish_registry_event(
        self,
        event_type: str,
        details: Dict
    ):
        """Publish registry-level event"""
        await self.publish_service_event(
            event_type,
            "service-discovery",
            "service-discovery",
            details
        )


class ServiceEventType:
    """Service event type constants"""
    
    # Service lifecycle events
    SERVICE_REGISTERED = "service.registered"
    SERVICE_DEREGISTERED = "service.deregistered"
    SERVICE_HEALTH_CHANGED = "service.health_changed"
    SERVICE_HEARTBEAT_MISSED = "service.heartbeat_missed"
    
    # Registry events
    REGISTRY_STARTED = "registry.started"
    REGISTRY_STOPPED = "registry.stopped"
    REGISTRY_CLEANUP = "registry.cleanup"
    
    # Load balancer events
    LOAD_BALANCER_UPDATED = "load_balancer.updated"
    CIRCUIT_BREAKER_OPENED = "circuit_breaker.opened"
    CIRCUIT_BREAKER_CLOSED = "circuit_breaker.closed"


class EventPublisher:
    """Helper class for publishing common service events"""
    
    def __init__(self, event_bus_client: EventBusClient):
        self.event_bus = event_bus_client
    
    async def service_registered(
        self,
        service_id: str,
        service_name: str,
        host: str,
        port: int,
        metadata: Dict = None
    ):
        """Publish service registration event"""
        await self.event_bus.publish_service_event(
            ServiceEventType.SERVICE_REGISTERED,
            service_id,
            service_name,
            {
                "host": host,
                "port": port,
                "metadata": metadata or {},
                "registration_time": datetime.utcnow().isoformat()
            }
        )
    
    async def service_deregistered(
        self,
        service_id: str,
        service_name: str,
        reason: str = "manual"
    ):
        """Publish service deregistration event"""
        await self.event_bus.publish_service_event(
            ServiceEventType.SERVICE_DEREGISTERED,
            service_id,
            service_name,
            {
                "reason": reason,
                "deregistration_time": datetime.utcnow().isoformat()
            }
        )
    
    async def service_health_changed(
        self,
        service_id: str,
        service_name: str,
        old_status: str,
        new_status: str,
        failure_count: int = 0,
        error_message: str = None
    ):
        """Publish service health change event"""
        await self.event_bus.publish_service_event(
            ServiceEventType.SERVICE_HEALTH_CHANGED,
            service_id,
            service_name,
            {
                "old_status": old_status,
                "new_status": new_status,
                "failure_count": failure_count,
                "error_message": error_message,
                "change_time": datetime.utcnow().isoformat()
            }
        )
    
    async def heartbeat_missed(
        self,
        service_id: str,
        service_name: str,
        last_heartbeat: datetime,
        missed_count: int
    ):
        """Publish heartbeat missed event"""
        await self.event_bus.publish_service_event(
            ServiceEventType.SERVICE_HEARTBEAT_MISSED,
            service_id,
            service_name,
            {
                "last_heartbeat": last_heartbeat.isoformat(),
                "missed_count": missed_count,
                "detection_time": datetime.utcnow().isoformat()
            }
        )
    
    async def load_balancer_updated(
        self,
        service_name: str,
        strategy: str,
        instance_count: int,
        healthy_instances: int
    ):
        """Publish load balancer update event"""
        await self.event_bus.publish_service_event(
            ServiceEventType.LOAD_BALANCER_UPDATED,
            "load-balancer",
            service_name,
            {
                "strategy": strategy,
                "instance_count": instance_count,
                "healthy_instances": healthy_instances,
                "update_time": datetime.utcnow().isoformat()
            }
        )
    
    async def circuit_breaker_state_changed(
        self,
        service_id: str,
        service_name: str,
        old_state: str,
        new_state: str,
        failure_count: int
    ):
        """Publish circuit breaker state change event"""
        event_type = (
            ServiceEventType.CIRCUIT_BREAKER_OPENED 
            if new_state == "open" 
            else ServiceEventType.CIRCUIT_BREAKER_CLOSED
        )
        
        await self.event_bus.publish_service_event(
            event_type,
            service_id,
            service_name,
            {
                "old_state": old_state,
                "new_state": new_state,
                "failure_count": failure_count,
                "change_time": datetime.utcnow().isoformat()
            }
        )
    
    async def registry_started(self, instance_count: int):
        """Publish registry started event"""
        await self.event_bus.publish_registry_event(
            ServiceEventType.REGISTRY_STARTED,
            {
                "initial_instance_count": instance_count,
                "startup_time": datetime.utcnow().isoformat()
            }
        )
    
    async def registry_cleanup(
        self,
        removed_services: int,
        total_services: int
    ):
        """Publish registry cleanup event"""
        await self.event_bus.publish_registry_event(
            ServiceEventType.REGISTRY_CLEANUP,
            {
                "removed_services": removed_services,
                "total_services": total_services,
                "cleanup_time": datetime.utcnow().isoformat()
            }
        )