"""
Service Discovery Security
Authentication and authorization for service registration
"""

from typing import Optional
from fastapi import HTTP<PERSON>xception, Security, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog

from .config import Settings, get_settings

logger = structlog.get_logger(__name__)
security = HTTPBearer()


async def verify_api_key(
    credentials: HTTPAuthorizationCredentials = Security(security),
    settings: Settings = Depends(get_settings)
) -> str:
    """Verify API key from Authorization header"""
    
    if not settings.require_auth:
        return "anonymous"
    
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    api_key = credentials.credentials
    
    # Check if API key is valid
    valid_keys = settings.api_keys
    service_name = None
    
    for service, key in valid_keys.items():
        if key == api_key:
            service_name = service
            break
    
    if not service_name:
        logger.warning("Invalid API key used", key_prefix=api_key[:8])
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    logger.debug("Service authenticated", service_name=service_name)
    return service_name


async def require_admin_auth(
    credentials: HTTPAuthorizationCredentials = Security(security),
    settings: Settings = Depends(get_settings)
) -> str:
    """Require admin authentication"""
    
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing admin token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    
    if not settings.admin_token or token != settings.admin_token:
        logger.warning("Invalid admin token used", token_prefix=token[:8])
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid admin token"
        )
    
    logger.debug("Admin authenticated")
    return "admin"


def generate_api_key() -> str:
    """Generate a new API key"""
    import secrets
    return secrets.token_urlsafe(32)


def setup_default_api_keys(settings: Settings) -> dict:
    """Setup default API keys for known services"""
    default_services = [
        "event-bus",
        "trend-analyzer", 
        "content-generator",
        "cover-designer",
        "kdp-uploader",
        "sales-monitor",
        "research-assistant",
        "personalization-engine",
        "multimodal-generator"
    ]
    
    api_keys = {}
    
    for service in default_services:
        if service not in settings.api_keys:
            api_key = generate_api_key()
            api_keys[service] = api_key
            
            logger.info(
                f"Generated API key for {service}",
                service=service,
                api_key=api_key  # Only log in development
            )
    
    return api_keys


class SecurityManager:
    """Manages security policies and API keys"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        
    def validate_service_registration(self, service_name: str, authenticated_service: str) -> bool:
        """Validate if a service can register with the given name"""
        # Allow services to register themselves
        if service_name == authenticated_service:
            return True
        
        # Allow admin to register any service
        if authenticated_service == "admin":
            return True
        
        # For now, be permissive - allow any authenticated service to register others
        # In production, you might want stricter policies
        return True
    
    def can_access_service_info(self, service_name: str, authenticated_service: str) -> bool:
        """Check if a service can access information about another service"""
        # Allow all authenticated services to discover others
        return True
    
    def can_modify_service(self, service_id: str, authenticated_service: str) -> bool:
        """Check if a service can modify another service's registration"""
        # Only allow services to modify their own registrations
        # or admin to modify any
        return authenticated_service == "admin" or service_id.startswith(authenticated_service)


def get_security_manager(settings: Settings = Depends(get_settings)) -> SecurityManager:
    """Get security manager instance"""
    return SecurityManager(settings)