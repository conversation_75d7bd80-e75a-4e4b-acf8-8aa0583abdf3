"""
Service Discovery Service - Main Application
Dynamic service registration and discovery for Publish AI microservices
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

from fastapi import FastAPI, HTTPException, Depends, Query, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from prometheus_client import make_asgi_app
import structlog

# Optional OpenTelemetry imports
try:
    from opentelemetry import trace
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    from opentelemetry.instrumentation.redis import RedisInstrumentor
    TELEMETRY_AVAILABLE = True
except ImportError:
    TELEMETRY_AVAILABLE = False
    trace = None

from .config import Settings, validate_settings
from .registry import ServiceRegistry
from .models import (
    ServiceRegistration, ServiceInstance, ServiceHeartbeat,
    ServiceDiscoveryQuery, ServiceDiscoveryResponse, ServiceHealthResponse,
    ServiceRegistryStats, ErrorResponse, ServiceStatus
)
from .security import verify_api_key, require_admin_auth
from .metrics import setup_metrics, get_metrics_collector
from .events import EventBusClient

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)
tracer = trace.get_tracer(__name__) if TELEMETRY_AVAILABLE else None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    logger.info("Starting Service Discovery Service")
    
    # Initialize service registry
    await app.state.registry.initialize()
    logger.info("Service registry initialized")
    
    # Initialize Event Bus client if enabled
    if app.state.settings.event_bus_enabled:
        await app.state.event_bus.initialize()
        logger.info("Event Bus client initialized")
    
    # Setup metrics
    setup_metrics()
    
    yield
    
    # Cleanup
    logger.info("Shutting down Service Discovery Service")
    if app.state.settings.event_bus_enabled:
        await app.state.event_bus.close()
    await app.state.registry.close()


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = Settings()
    validate_settings()
    
    app = FastAPI(
        title="Service Discovery Service",
        description="Dynamic service registration and discovery for Publish AI",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize components
    app.state.settings = settings
    app.state.registry = ServiceRegistry(settings)
    app.state.event_bus = EventBusClient(settings) if settings.event_bus_enabled else None
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    return app


app = create_app()

# Setup OpenTelemetry instrumentation if available
if TELEMETRY_AVAILABLE:
    FastAPIInstrumentor.instrument_app(app)
    RedisInstrumentor().instrument()


# Health and Status Endpoints
@app.get("/health", response_model=ServiceHealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis connectivity
        registry_healthy = await app.state.registry.redis_client.ping() if app.state.registry.redis_client else False
        
        # Check Event Bus connectivity if enabled
        event_bus_healthy = True
        if app.state.settings.event_bus_enabled and app.state.event_bus:
            event_bus_healthy = await app.state.event_bus.health_check()
        
        overall_healthy = registry_healthy and event_bus_healthy
        
        return ServiceHealthResponse(
            status="healthy" if overall_healthy else "unhealthy",
            service="service-discovery",
            version="1.0.0",
            dependencies={
                "redis": "healthy" if registry_healthy else "unhealthy",
                "event_bus": "healthy" if event_bus_healthy else "unhealthy"
            }
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/ready")
async def readiness_check():
    """Readiness probe endpoint"""
    try:
        # Check if registry is ready
        if not app.state.registry.redis_client:
            raise HTTPException(status_code=503, detail="Registry not ready")
        
        await app.state.registry.redis_client.ping()
        return {"status": "ready", "service": "service-discovery"}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service not ready")


# Service Registration Endpoints
@app.post("/services/register", response_model=ServiceInstance)
async def register_service(
    registration: ServiceRegistration,
    api_key: str = Depends(verify_api_key)
):
    """Register a new service instance"""
    try:
        instance = await app.state.registry.register_service(registration)
        
        # Publish event to Event Bus
        if app.state.event_bus:
            await app.state.event_bus.publish_service_event(
                "service.registered",
                instance.service_id,
                instance.service_name,
                {"host": instance.host, "port": instance.port}
            )
        
        # Update metrics
        metrics = get_metrics_collector()
        metrics.record_service_registered(instance.service_name)
        
        logger.info(
            "Service registered",
            service_id=instance.service_id,
            service_name=instance.service_name
        )
        
        return instance
        
    except Exception as e:
        logger.error("Failed to register service", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to register service: {str(e)}"
        )


@app.put("/services/{service_id}/heartbeat")
async def send_heartbeat(
    service_id: str,
    heartbeat: ServiceHeartbeat,
    api_key: str = Depends(verify_api_key)
):
    """Send heartbeat for a service instance"""
    try:
        success = await app.state.registry.update_heartbeat(
            service_id,
            heartbeat.metadata
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Service not found")
        
        # Update metrics
        metrics = get_metrics_collector()
        metrics.record_heartbeat(service_id)
        
        return {"success": True, "message": "Heartbeat updated"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update heartbeat", service_id=service_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update heartbeat: {str(e)}"
        )


@app.delete("/services/{service_id}")
async def deregister_service(
    service_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Deregister a service instance"""
    try:
        # Get service info before deregistration
        instance = await app.state.registry.get_service_instance(service_id)
        if not instance:
            raise HTTPException(status_code=404, detail="Service not found")
        
        success = await app.state.registry.deregister_service(service_id)
        
        if success:
            # Publish event to Event Bus
            if app.state.event_bus:
                await app.state.event_bus.publish_service_event(
                    "service.deregistered",
                    service_id,
                    instance.service_name,
                    {"reason": "manual_deregistration"}
                )
            
            # Update metrics
            metrics = get_metrics_collector()
            metrics.record_service_deregistered(instance.service_name)
            
            return {"success": True, "message": "Service deregistered"}
        else:
            raise HTTPException(status_code=404, detail="Service not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to deregister service", service_id=service_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to deregister service: {str(e)}"
        )


# Service Discovery Endpoints
@app.get("/services", response_model=List[ServiceInstance])
async def list_all_services(
    status_filter: Optional[ServiceStatus] = Query(None, description="Filter by service status"),
    tag_filter: Optional[str] = Query(None, description="Filter by tag"),
    limit: Optional[int] = Query(None, ge=1, le=1000, description="Limit number of results")
):
    """List all registered services"""
    try:
        instances = await app.state.registry.get_all_services()
        
        # Apply filters
        if status_filter:
            instances = [i for i in instances if i.status == status_filter]
        
        if tag_filter:
            instances = [i for i in instances if tag_filter in i.tags]
        
        # Apply limit
        if limit:
            instances = instances[:limit]
        
        return instances
        
    except Exception as e:
        logger.error("Failed to list services", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list services: {str(e)}"
        )


@app.get("/services/{service_name}", response_model=ServiceDiscoveryResponse)
async def discover_service(
    service_name: str,
    healthy_only: bool = Query(True, description="Return only healthy instances"),
    tags: Optional[str] = Query(None, description="Comma-separated required tags"),
    limit: Optional[int] = Query(None, ge=1, description="Maximum number of instances")
):
    """Discover instances of a specific service"""
    try:
        # Parse tags
        tag_list = tags.split(",") if tags else None
        
        query = ServiceDiscoveryQuery(
            service_name=service_name,
            tags=tag_list,
            healthy_only=healthy_only,
            limit=limit
        )
        
        response = await app.state.registry.discover_services(query)
        
        # Update metrics
        metrics = get_metrics_collector()
        metrics.record_service_discovery(service_name, len(response.instances))
        
        return response
        
    except Exception as e:
        logger.error("Failed to discover service", service_name=service_name, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to discover service: {str(e)}"
        )


@app.get("/services/{service_name}/instances", response_model=List[ServiceInstance])
async def get_service_instances(
    service_name: str,
    healthy_only: bool = Query(True, description="Return only healthy instances")
):
    """Get all instances for a specific service"""
    try:
        instances = await app.state.registry.get_service_instances(service_name)
        
        if healthy_only:
            instances = [i for i in instances if i.is_healthy]
        
        return instances
        
    except Exception as e:
        logger.error("Failed to get service instances", service_name=service_name, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get service instances: {str(e)}"
        )


@app.get("/services/{service_name}/health")
async def check_service_health(service_name: str):
    """Check health status of all instances for a service"""
    try:
        instances = await app.state.registry.get_service_instances(service_name)
        
        health_status = {
            "service_name": service_name,
            "total_instances": len(instances),
            "healthy_instances": len([i for i in instances if i.status == ServiceStatus.HEALTHY]),
            "unhealthy_instances": len([i for i in instances if i.status == ServiceStatus.UNHEALTHY]),
            "instances": [
                {
                    "service_id": i.service_id,
                    "host": i.host,
                    "port": i.port,
                    "status": i.status,
                    "last_health_check": i.last_health_check,
                    "health_check_failures": i.health_check_failures
                }
                for i in instances
            ]
        }
        
        return health_status
        
    except Exception as e:
        logger.error("Failed to check service health", service_name=service_name, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check service health: {str(e)}"
        )


# Administrative Endpoints
@app.get("/admin/stats", response_model=ServiceRegistryStats)
async def get_registry_stats(admin_token: str = Depends(require_admin_auth)):
    """Get service registry statistics"""
    try:
        instances = await app.state.registry.get_all_services()
        
        # Calculate statistics
        total_services = len(instances)
        healthy_services = len([i for i in instances if i.status == ServiceStatus.HEALTHY])
        unhealthy_services = len([i for i in instances if i.status == ServiceStatus.UNHEALTHY])
        
        services_by_name = {}
        services_by_status = {status: 0 for status in ServiceStatus}
        
        for instance in instances:
            # Count by name
            services_by_name[instance.service_name] = services_by_name.get(instance.service_name, 0) + 1
            
            # Count by status
            services_by_status[instance.status] = services_by_status.get(instance.status, 0) + 1
        
        # Get metrics
        metrics = get_metrics_collector()
        
        return ServiceRegistryStats(
            total_services=total_services,
            healthy_services=healthy_services,
            unhealthy_services=unhealthy_services,
            services_by_name=services_by_name,
            services_by_status=services_by_status,
            total_health_checks=metrics.total_health_checks,
            failed_health_checks=metrics.failed_health_checks,
            uptime_seconds=metrics.get_uptime()
        )
        
    except Exception as e:
        logger.error("Failed to get registry stats", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get stats: {str(e)}"
        )


@app.post("/admin/services/{service_id}/force-remove")
async def force_remove_service(
    service_id: str,
    admin_token: str = Depends(require_admin_auth)
):
    """Force remove a service (admin only)"""
    try:
        success = await app.state.registry.deregister_service(service_id)
        
        if success:
            logger.info("Service force removed by admin", service_id=service_id)
            return {"success": True, "message": "Service force removed"}
        else:
            raise HTTPException(status_code=404, detail="Service not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to force remove service", service_id=service_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to force remove service: {str(e)}"
        )


@app.get("/admin/dashboard", response_class=HTMLResponse)
async def admin_dashboard():
    """Simple admin dashboard"""
    if not app.state.settings.admin_dashboard_enabled:
        raise HTTPException(status_code=404, detail="Dashboard not enabled")
    
    # Simple HTML dashboard
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Service Discovery - Admin Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .service { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
            .healthy { border-left: 5px solid #4CAF50; }
            .unhealthy { border-left: 5px solid #f44336; }
            .unknown { border-left: 5px solid #ff9800; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: #f5f5f5; padding: 20px; border-radius: 5px; text-align: center; }
            .refresh-btn { background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        </style>
        <script>
            function refreshPage() { location.reload(); }
            setInterval(refreshPage, 30000); // Auto-refresh every 30 seconds
        </script>
    </head>
    <body>
        <div class="container">
            <h1>Service Discovery Dashboard</h1>
            <button class="refresh-btn" onclick="refreshPage()">Refresh</button>
            
            <div id="services-container">
                <p>Loading services...</p>
            </div>
        </div>
        
        <script>
            async function loadServices() {
                try {
                    const response = await fetch('/services');
                    const services = await response.json();
                    
                    const statsResponse = await fetch('/admin/stats');
                    const stats = await statsResponse.json();
                    
                    displayStats(stats);
                    displayServices(services);
                } catch (error) {
                    document.getElementById('services-container').innerHTML = 
                        '<p>Error loading services: ' + error.message + '</p>';
                }
            }
            
            function displayStats(stats) {
                const statsHtml = `
                    <div class="stats">
                        <div class="stat-card">
                            <h3>${stats.total_services}</h3>
                            <p>Total Services</p>
                        </div>
                        <div class="stat-card">
                            <h3>${stats.healthy_services}</h3>
                            <p>Healthy Services</p>
                        </div>
                        <div class="stat-card">
                            <h3>${stats.unhealthy_services}</h3>
                            <p>Unhealthy Services</p>
                        </div>
                        <div class="stat-card">
                            <h3>${Math.round(stats.uptime_seconds / 60)}</h3>
                            <p>Uptime (minutes)</p>
                        </div>
                    </div>
                `;
                
                document.getElementById('services-container').innerHTML = statsHtml + 
                    '<h2>Registered Services</h2>' + 
                    document.getElementById('services-container').innerHTML.split('<h2>')[1] || '';
            }
            
            function displayServices(services) {
                if (services.length === 0) {
                    document.getElementById('services-container').innerHTML += '<p>No services registered.</p>';
                    return;
                }
                
                const servicesHtml = services.map(service => `
                    <div class="service ${service.status}">
                        <h3>${service.service_name} (${service.service_id})</h3>
                        <p><strong>Status:</strong> ${service.status}</p>
                        <p><strong>Address:</strong> ${service.host}:${service.port}</p>
                        <p><strong>Version:</strong> ${service.service_version}</p>
                        <p><strong>Last Heartbeat:</strong> ${service.last_heartbeat || 'Never'}</p>
                        <p><strong>Health Check Failures:</strong> ${service.health_check_failures}</p>
                        <p><strong>Tags:</strong> ${service.tags.join(', ') || 'None'}</p>
                    </div>
                `).join('');
                
                document.getElementById('services-container').innerHTML = 
                    document.getElementById('services-container').innerHTML.split('<h2>')[0] + 
                    '<h2>Registered Services</h2>' + servicesHtml;
            }
            
            // Load services on page load
            loadServices();
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)


if __name__ == "__main__":
    import uvicorn
    
    settings = Settings()
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=settings.workers,
        log_level="info"
    )