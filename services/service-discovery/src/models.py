"""
Service Discovery Models
Pydantic models for service registration and discovery
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, validator
from uuid import UUID, uuid4


class ServiceStatus(str, Enum):
    """Service health status"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    STARTING = "starting"
    STOPPING = "stopping"


class ServiceProtocol(str, Enum):
    """Supported service protocols"""
    HTTP = "http"
    HTTPS = "https"
    GRPC = "grpc"
    TCP = "tcp"


class LoadBalancingStrategy(str, Enum):
    """Load balancing strategies"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RANDOM = "random"


class ServiceRegistration(BaseModel):
    """Service registration request model"""
    
    service_id: str = Field(..., description="Unique service instance identifier")
    service_name: str = Field(..., description="Service name for discovery")
    service_version: str = Field(default="1.0.0", description="Service version")
    
    # Network information
    host: str = Field(..., description="Service host address")
    port: int = Field(..., ge=1, le=65535, description="Service port number")
    protocol: ServiceProtocol = Field(default=ServiceProtocol.HTTP, description="Service protocol")
    
    # Health check configuration
    health_check_url: str = Field(default="/health", description="Health check endpoint")
    health_check_interval: int = Field(default=30, ge=5, description="Health check interval in seconds")
    health_check_timeout: int = Field(default=10, ge=1, description="Health check timeout in seconds")
    
    # Service metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Service metadata")
    tags: List[str] = Field(default_factory=list, description="Service tags")
    
    # Load balancing
    weight: int = Field(default=1, ge=1, le=100, description="Load balancing weight")
    max_connections: Optional[int] = Field(default=None, description="Maximum concurrent connections")
    
    @validator('service_id')
    def validate_service_id(cls, v):
        """Validate service ID format"""
        if not v or len(v) < 3:
            raise ValueError("Service ID must be at least 3 characters long")
        return v
    
    @validator('service_name')
    def validate_service_name(cls, v):
        """Validate service name format"""
        if not v or not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError("Service name must contain only alphanumeric characters, hyphens, and underscores")
        return v
    
    @validator('host')
    def validate_host(cls, v):
        """Validate host format"""
        if not v:
            raise ValueError("Host cannot be empty")
        return v


class ServiceInstance(BaseModel):
    """Service instance model with runtime information"""
    
    # Registration data
    service_id: str
    service_name: str
    service_version: str
    host: str
    port: int
    protocol: ServiceProtocol
    
    # Health check configuration
    health_check_url: str
    health_check_interval: int
    health_check_timeout: int
    
    # Service metadata
    metadata: Dict[str, Any]
    tags: List[str]
    weight: int
    max_connections: Optional[int]
    
    # Runtime information
    status: ServiceStatus = ServiceStatus.STARTING
    registered_at: datetime = Field(default_factory=datetime.utcnow)
    last_heartbeat: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    health_check_failures: int = 0
    
    # Performance metrics
    active_connections: int = 0
    total_requests: int = 0
    
    @property
    def service_url(self) -> str:
        """Get full service URL"""
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def health_check_full_url(self) -> str:
        """Get full health check URL"""
        return f"{self.service_url}{self.health_check_url}"
    
    @property
    def is_healthy(self) -> bool:
        """Check if service is considered healthy"""
        return self.status == ServiceStatus.HEALTHY


class ServiceHeartbeat(BaseModel):
    """Service heartbeat model"""
    
    service_id: str = Field(..., description="Service instance identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata updates")
    active_connections: Optional[int] = Field(default=None, description="Current active connections")


class ServiceDiscoveryQuery(BaseModel):
    """Service discovery query parameters"""
    
    service_name: str = Field(..., description="Service name to discover")
    tags: Optional[List[str]] = Field(default=None, description="Required tags filter")
    metadata_filters: Optional[Dict[str, Any]] = Field(default=None, description="Metadata filters")
    healthy_only: bool = Field(default=True, description="Return only healthy services")
    limit: Optional[int] = Field(default=None, ge=1, description="Maximum number of instances")


class ServiceDiscoveryResponse(BaseModel):
    """Service discovery response model"""
    
    service_name: str
    total_instances: int
    healthy_instances: int
    instances: List[ServiceInstance]
    load_balancing_strategy: LoadBalancingStrategy
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthCheckResult(BaseModel):
    """Health check result model"""
    
    service_id: str
    status: ServiceStatus
    response_time_ms: Optional[float] = None
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ServiceRegistryStats(BaseModel):
    """Service registry statistics"""
    
    total_services: int
    healthy_services: int
    unhealthy_services: int
    services_by_name: Dict[str, int]
    services_by_status: Dict[ServiceStatus, int]
    total_health_checks: int
    failed_health_checks: int
    uptime_seconds: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ServiceEvent(BaseModel):
    """Service lifecycle event model"""
    
    event_id: UUID = Field(default_factory=uuid4)
    event_type: str = Field(..., description="Event type (registered, deregistered, health_changed)")
    service_id: str
    service_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Dict[str, Any] = Field(default_factory=dict)


class AdminCommand(BaseModel):
    """Administrative command model"""
    
    command: str = Field(..., description="Command to execute")
    target_service_id: Optional[str] = Field(default=None, description="Target service ID")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Command parameters")


class ServiceHealthResponse(BaseModel):
    """Service health check response"""
    
    status: str = Field(..., description="Health status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    dependencies: Optional[Dict[str, str]] = Field(default=None, description="Dependency health status")
    metrics: Optional[Dict[str, Any]] = Field(default=None, description="Health metrics")


# Error response models
class ErrorResponse(BaseModel):
    """Standard error response"""
    
    success: bool = False
    error_code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ValidationErrorResponse(BaseModel):
    """Validation error response"""
    
    success: bool = False
    error_code: str = "validation_error"
    message: str = "Validation failed"
    validation_errors: List[Dict[str, Any]]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Load balancer models
class LoadBalancerState(BaseModel):
    """Load balancer state for a service"""
    
    service_name: str
    strategy: LoadBalancingStrategy
    instances: List[str]  # Service IDs
    current_index: int = 0
    connection_counts: Dict[str, int] = Field(default_factory=dict)
    last_updated: datetime = Field(default_factory=datetime.utcnow)


class CircuitBreakerState(BaseModel):
    """Circuit breaker state for service instances"""
    
    service_id: str
    state: str = Field(..., description="open, closed, half_open")
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    next_attempt_time: Optional[datetime] = None
    success_threshold: int = 5
    failure_threshold: int = 5
    timeout_seconds: int = 60