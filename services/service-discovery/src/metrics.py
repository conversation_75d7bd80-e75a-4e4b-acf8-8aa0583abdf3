"""
Service Discovery Metrics and Monitoring
Prometheus metrics for service registry operations
"""

import time
from typing import Dict
from prometheus_client import Counter, Histogram, Gauge, Info
import structlog

logger = structlog.get_logger(__name__)

# Prometheus metrics
services_registered_total = Counter(
    'service_discovery_registrations_total',
    'Total number of service registrations',
    ['service_name']
)

services_deregistered_total = Counter(
    'service_discovery_deregistrations_total',
    'Total number of service deregistrations',
    ['service_name']
)

service_discovery_requests_total = Counter(
    'service_discovery_requests_total',
    'Total number of service discovery requests',
    ['service_name', 'result']
)

heartbeats_received_total = Counter(
    'service_discovery_heartbeats_total',
    'Total number of heartbeats received',
    ['service_id']
)

health_checks_total = Counter(
    'service_discovery_health_checks_total',
    'Total number of health checks performed',
    ['service_name', 'status']
)

registry_size = Gauge(
    'service_discovery_registry_size',
    'Current number of registered services',
    ['status']
)

service_discovery_duration = Histogram(
    'service_discovery_request_duration_seconds',
    'Time spent processing discovery requests',
    ['operation']
)

active_services_by_name = Gauge(
    'service_discovery_active_services',
    'Number of active services by name',
    ['service_name']
)

load_balancer_selections = Counter(
    'service_discovery_load_balancer_selections_total',
    'Load balancer selections by strategy',
    ['service_name', 'strategy']
)

circuit_breaker_state = Gauge(
    'service_discovery_circuit_breaker_state',
    'Circuit breaker state (0=closed, 1=open, 2=half-open)',
    ['service_id']
)

admin_operations_total = Counter(
    'service_discovery_admin_operations_total',
    'Total admin operations performed',
    ['operation', 'result']
)

service_info = Info(
    'service_discovery_info',
    'Service Discovery service information'
)


class MetricsCollector:
    """Collects and manages Service Discovery metrics"""
    
    def __init__(self):
        self.start_time = time.time()
        self.total_health_checks = 0
        self.failed_health_checks = 0
        
    def record_service_registered(self, service_name: str):
        """Record a service registration"""
        services_registered_total.labels(service_name=service_name).inc()
        logger.debug("Metrics: Service registered", service_name=service_name)
    
    def record_service_deregistered(self, service_name: str):
        """Record a service deregistration"""
        services_deregistered_total.labels(service_name=service_name).inc()
        logger.debug("Metrics: Service deregistered", service_name=service_name)
    
    def record_service_discovery(self, service_name: str, instances_found: int):
        """Record a service discovery request"""
        result = "success" if instances_found > 0 else "not_found"
        service_discovery_requests_total.labels(
            service_name=service_name,
            result=result
        ).inc()
        logger.debug(
            "Metrics: Service discovery",
            service_name=service_name,
            instances_found=instances_found
        )
    
    def record_heartbeat(self, service_id: str):
        """Record a heartbeat"""
        heartbeats_received_total.labels(service_id=service_id).inc()
        logger.debug("Metrics: Heartbeat received", service_id=service_id)
    
    def record_health_check(self, service_name: str, success: bool):
        """Record a health check"""
        status = "success" if success else "failure"
        health_checks_total.labels(service_name=service_name, status=status).inc()
        
        self.total_health_checks += 1
        if not success:
            self.failed_health_checks += 1
        
        logger.debug(
            "Metrics: Health check",
            service_name=service_name,
            status=status
        )
    
    def update_registry_size(self, healthy_count: int, unhealthy_count: int, total_count: int):
        """Update registry size metrics"""
        registry_size.labels(status="healthy").set(healthy_count)
        registry_size.labels(status="unhealthy").set(unhealthy_count)
        registry_size.labels(status="total").set(total_count)
        
        logger.debug(
            "Metrics: Registry size updated",
            healthy=healthy_count,
            unhealthy=unhealthy_count,
            total=total_count
        )
    
    def update_services_by_name(self, service_counts: Dict[str, int]):
        """Update active services by name"""
        for service_name, count in service_counts.items():
            active_services_by_name.labels(service_name=service_name).set(count)
        
        logger.debug("Metrics: Services by name updated", counts=service_counts)
    
    def record_load_balancer_selection(self, service_name: str, strategy: str):
        """Record a load balancer selection"""
        load_balancer_selections.labels(
            service_name=service_name,
            strategy=strategy
        ).inc()
        
        logger.debug(
            "Metrics: Load balancer selection",
            service_name=service_name,
            strategy=strategy
        )
    
    def update_circuit_breaker_state(self, service_id: str, state: int):
        """Update circuit breaker state (0=closed, 1=open, 2=half-open)"""
        circuit_breaker_state.labels(service_id=service_id).set(state)
        
        logger.debug(
            "Metrics: Circuit breaker state",
            service_id=service_id,
            state=state
        )
    
    def record_admin_operation(self, operation: str, success: bool):
        """Record an admin operation"""
        result = "success" if success else "failure"
        admin_operations_total.labels(operation=operation, result=result).inc()
        
        logger.debug(
            "Metrics: Admin operation",
            operation=operation,
            result=result
        )
    
    def get_uptime(self) -> float:
        """Get service uptime in seconds"""
        return time.time() - self.start_time
    
    def record_operation_duration(self, operation: str, duration: float):
        """Record operation duration"""
        service_discovery_duration.labels(operation=operation).observe(duration)


# Global metrics collector instance
_metrics_collector: MetricsCollector = None


def get_metrics_collector() -> MetricsCollector:
    """Get or create metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def setup_metrics():
    """Setup Prometheus metrics"""
    try:
        # Initialize service info
        service_info.info({
            'service': 'service-discovery',
            'version': '1.0.0',
            'description': 'Dynamic service registration and discovery'
        })
        
        # Initialize metrics collector
        collector = get_metrics_collector()
        
        logger.info("Prometheus metrics initialized")
        
    except Exception as e:
        logger.error("Failed to setup metrics", error=str(e))
        raise


def timing_decorator(operation: str):
    """Decorator to measure operation execution time"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                collector = get_metrics_collector()
                collector.record_operation_duration(operation, duration)
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                collector = get_metrics_collector()
                collector.record_operation_duration(operation, duration)
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Registry monitoring helper
async def collect_registry_metrics(registry):
    """Collect comprehensive registry metrics"""
    try:
        collector = get_metrics_collector()
        
        # Get all services
        all_services = await registry.get_all_services()
        
        # Count by status
        healthy_count = len([s for s in all_services if s.status.value == "healthy"])
        unhealthy_count = len([s for s in all_services if s.status.value == "unhealthy"])
        total_count = len(all_services)
        
        # Update registry size
        collector.update_registry_size(healthy_count, unhealthy_count, total_count)
        
        # Count by service name
        service_counts = {}
        for service in all_services:
            service_counts[service.service_name] = service_counts.get(service.service_name, 0) + 1
        
        # Update services by name
        collector.update_services_by_name(service_counts)
        
        return {
            'total_services': total_count,
            'healthy_services': healthy_count,
            'unhealthy_services': unhealthy_count,
            'services_by_name': service_counts,
            'uptime': collector.get_uptime()
        }
        
    except Exception as e:
        logger.error("Failed to collect registry metrics", error=str(e))
        return {}


# Background metrics collection
async def start_metrics_collection(registry, interval: int = 60):
    """Start background metrics collection"""
    import asyncio
    
    async def metrics_loop():
        while True:
            try:
                await collect_registry_metrics(registry)
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in metrics collection loop", error=str(e))
                await asyncio.sleep(interval)
    
    return asyncio.create_task(metrics_loop())