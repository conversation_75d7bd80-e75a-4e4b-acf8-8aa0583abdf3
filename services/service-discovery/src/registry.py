"""
Service Registry Implementation
Core service registration and discovery functionality using Redis
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from uuid import uuid4

import redis.asyncio as redis
from redis.asyncio.retry import Retry
from redis.asyncio.backoff import ExponentialBackoff
import structlog

from .config import Settings, get_redis_config
from .models import (
    ServiceInstance, ServiceRegistration, ServiceStatus, 
    ServiceDiscoveryQuery, ServiceDiscoveryResponse,
    LoadBalancingStrategy, LoadBalancerState
)

logger = structlog.get_logger(__name__)


class ServiceRegistry:
    """Redis-based service registry with health monitoring and load balancing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.redis_client: Optional[redis.Redis] = None
        
        # Redis key patterns
        self.services_key = "services:registry"
        self.health_key = "services:health"
        self.metrics_key = "services:metrics"
        self.lb_state_key = "services:lb_state"
        self.events_key = "services:events"
        
        # Load balancer state
        self.lb_states: Dict[str, LoadBalancerState] = {}
        
        # Circuit breaker state
        self.circuit_breakers: Dict[str, dict] = {}
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize Redis connection and background tasks"""
        try:
            # Create Redis connection
            redis_config = get_redis_config()
            retry = Retry(ExponentialBackoff(), retries=3)
            
            self.redis_client = redis.from_url(
                redis_config["url"],
                password=redis_config.get("password"),
                db=redis_config["db"],
                max_connections=redis_config["max_connections"],
                retry=retry,
                health_check_interval=30,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Service registry Redis connection established")
            
            # Load existing load balancer states
            await self._load_lb_states()
            
            # Start background tasks
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            self._health_monitor_task = asyncio.create_task(self._health_monitor_loop())
            
            logger.info("Service registry initialized")
            
        except Exception as e:
            logger.error("Failed to initialize service registry", error=str(e))
            raise
    
    async def close(self):
        """Close Redis connection and cleanup"""
        # Cancel background tasks
        if self._cleanup_task:
            self._cleanup_task.cancel()
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
        
        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()
            
        logger.info("Service registry closed")
    
    async def register_service(self, registration: ServiceRegistration) -> ServiceInstance:
        """Register a new service instance"""
        try:
            # Create service instance
            instance = ServiceInstance(
                service_id=registration.service_id,
                service_name=registration.service_name,
                service_version=registration.service_version,
                host=registration.host,
                port=registration.port,
                protocol=registration.protocol,
                health_check_url=registration.health_check_url,
                health_check_interval=registration.health_check_interval,
                health_check_timeout=registration.health_check_timeout,
                metadata=registration.metadata,
                tags=registration.tags,
                weight=registration.weight,
                max_connections=registration.max_connections,
                status=ServiceStatus.STARTING
            )
            
            # Store in Redis
            await self._store_service_instance(instance)
            
            # Update load balancer state
            await self._update_load_balancer(instance.service_name)
            
            # Log event
            await self._log_service_event(
                "service.registered",
                instance.service_id,
                instance.service_name,
                {"registration_time": instance.registered_at.isoformat()}
            )
            
            logger.info(
                "Service registered",
                service_id=instance.service_id,
                service_name=instance.service_name,
                host=instance.host,
                port=instance.port
            )
            
            return instance
            
        except Exception as e:
            logger.error(
                "Failed to register service",
                service_id=registration.service_id,
                error=str(e)
            )
            raise
    
    async def deregister_service(self, service_id: str) -> bool:
        """Deregister a service instance"""
        try:
            # Get service instance
            instance = await self.get_service_instance(service_id)
            if not instance:
                return False
            
            # Remove from Redis
            await self.redis_client.hdel(self.services_key, service_id)
            await self.redis_client.hdel(self.health_key, service_id)
            await self.redis_client.hdel(self.metrics_key, service_id)
            
            # Update load balancer state
            await self._update_load_balancer(instance.service_name)
            
            # Log event
            await self._log_service_event(
                "service.deregistered",
                service_id,
                instance.service_name,
                {"deregistration_time": datetime.utcnow().isoformat()}
            )
            
            logger.info(
                "Service deregistered",
                service_id=service_id,
                service_name=instance.service_name
            )
            
            return True
            
        except Exception as e:
            logger.error("Failed to deregister service", service_id=service_id, error=str(e))
            return False
    
    async def update_heartbeat(self, service_id: str, metadata: Optional[Dict] = None) -> bool:
        """Update service heartbeat"""
        try:
            instance = await self.get_service_instance(service_id)
            if not instance:
                return False
            
            # Update heartbeat timestamp
            instance.last_heartbeat = datetime.utcnow()
            
            # Update metadata if provided
            if metadata:
                instance.metadata.update(metadata)
            
            # Update status if starting
            if instance.status == ServiceStatus.STARTING:
                instance.status = ServiceStatus.HEALTHY
            
            # Store updated instance
            await self._store_service_instance(instance)
            
            logger.debug("Heartbeat updated", service_id=service_id)
            return True
            
        except Exception as e:
            logger.error("Failed to update heartbeat", service_id=service_id, error=str(e))
            return False
    
    async def discover_services(self, query: ServiceDiscoveryQuery) -> ServiceDiscoveryResponse:
        """Discover services matching the query"""
        try:
            # Get all instances for the service name
            instances = await self.get_service_instances(query.service_name)
            
            # Apply filters
            filtered_instances = []
            for instance in instances:
                # Health filter
                if query.healthy_only and not instance.is_healthy:
                    continue
                
                # Tags filter
                if query.tags:
                    if not all(tag in instance.tags for tag in query.tags):
                        continue
                
                # Metadata filter
                if query.metadata_filters:
                    if not self._matches_metadata_filter(instance.metadata, query.metadata_filters):
                        continue
                
                filtered_instances.append(instance)
            
            # Apply limit
            if query.limit:
                filtered_instances = filtered_instances[:query.limit]
            
            # Apply load balancing
            balanced_instances = await self._apply_load_balancing(
                query.service_name, 
                filtered_instances
            )
            
            # Get load balancing strategy
            lb_state = self.lb_states.get(query.service_name)
            strategy = LoadBalancingStrategy(lb_state.strategy) if lb_state else LoadBalancingStrategy.ROUND_ROBIN
            
            return ServiceDiscoveryResponse(
                service_name=query.service_name,
                total_instances=len(instances),
                healthy_instances=len([i for i in instances if i.is_healthy]),
                instances=balanced_instances,
                load_balancing_strategy=strategy
            )
            
        except Exception as e:
            logger.error(
                "Failed to discover services",
                service_name=query.service_name,
                error=str(e)
            )
            raise
    
    async def get_service_instance(self, service_id: str) -> Optional[ServiceInstance]:
        """Get a specific service instance"""
        try:
            data = await self.redis_client.hget(self.services_key, service_id)
            if not data:
                return None
            
            instance_data = json.loads(data)
            return ServiceInstance(**instance_data)
            
        except Exception as e:
            logger.error("Failed to get service instance", service_id=service_id, error=str(e))
            return None
    
    async def get_service_instances(self, service_name: str) -> List[ServiceInstance]:
        """Get all instances for a service name"""
        try:
            # Get all service instances
            all_services = await self.redis_client.hgetall(self.services_key)
            
            instances = []
            for service_id, data in all_services.items():
                try:
                    instance_data = json.loads(data)
                    instance = ServiceInstance(**instance_data)
                    
                    if instance.service_name == service_name:
                        instances.append(instance)
                        
                except Exception as e:
                    logger.error(
                        "Failed to parse service instance",
                        service_id=service_id.decode() if isinstance(service_id, bytes) else service_id,
                        error=str(e)
                    )
            
            return instances
            
        except Exception as e:
            logger.error("Failed to get service instances", service_name=service_name, error=str(e))
            return []
    
    async def get_all_services(self) -> List[ServiceInstance]:
        """Get all registered service instances"""
        try:
            all_services = await self.redis_client.hgetall(self.services_key)
            
            instances = []
            for service_id, data in all_services.items():
                try:
                    instance_data = json.loads(data)
                    instances.append(ServiceInstance(**instance_data))
                except Exception as e:
                    logger.error(
                        "Failed to parse service instance",
                        service_id=service_id.decode() if isinstance(service_id, bytes) else service_id,
                        error=str(e)
                    )
            
            return instances
            
        except Exception as e:
            logger.error("Failed to get all services", error=str(e))
            return []
    
    async def update_service_health(self, service_id: str, status: ServiceStatus, details: Dict = None):
        """Update service health status"""
        try:
            instance = await self.get_service_instance(service_id)
            if not instance:
                return
            
            old_status = instance.status
            instance.status = status
            instance.last_health_check = datetime.utcnow()
            
            # Update failure count
            if status == ServiceStatus.UNHEALTHY:
                instance.health_check_failures += 1
            elif status == ServiceStatus.HEALTHY:
                instance.health_check_failures = 0
            
            # Store updated instance
            await self._store_service_instance(instance)
            
            # Log health change event
            if old_status != status:
                await self._log_service_event(
                    "service.health_changed",
                    service_id,
                    instance.service_name,
                    {
                        "old_status": old_status,
                        "new_status": status,
                        "details": details or {}
                    }
                )
                
                # Update load balancer if health changed
                await self._update_load_balancer(instance.service_name)
            
            logger.debug(
                "Service health updated",
                service_id=service_id,
                status=status,
                failures=instance.health_check_failures
            )
            
        except Exception as e:
            logger.error("Failed to update service health", service_id=service_id, error=str(e))
    
    async def _store_service_instance(self, instance: ServiceInstance):
        """Store service instance in Redis"""
        instance_data = instance.model_dump()
        
        # Convert datetime objects to ISO format
        for key, value in instance_data.items():
            if isinstance(value, datetime):
                instance_data[key] = value.isoformat()
        
        await self.redis_client.hset(
            self.services_key,
            instance.service_id,
            json.dumps(instance_data)
        )
        
        # Set TTL for service registration
        await self.redis_client.expire(
            f"service:{instance.service_id}",
            self.settings.registry_ttl_seconds
        )
    
    async def _update_load_balancer(self, service_name: str):
        """Update load balancer state for a service"""
        try:
            instances = await self.get_service_instances(service_name)
            healthy_instances = [i for i in instances if i.is_healthy]
            
            # Get or create load balancer state
            if service_name not in self.lb_states:
                self.lb_states[service_name] = LoadBalancerState(
                    service_name=service_name,
                    strategy=LoadBalancingStrategy(self.settings.default_lb_strategy),
                    instances=[]
                )
            
            lb_state = self.lb_states[service_name]
            lb_state.instances = [i.service_id for i in healthy_instances]
            lb_state.last_updated = datetime.utcnow()
            
            # Reset index if no instances or current index is out of bounds
            if not lb_state.instances or lb_state.current_index >= len(lb_state.instances):
                lb_state.current_index = 0
            
            # Store state in Redis
            await self.redis_client.hset(
                self.lb_state_key,
                service_name,
                json.dumps(lb_state.model_dump(), default=str)
            )
            
        except Exception as e:
            logger.error("Failed to update load balancer", service_name=service_name, error=str(e))
    
    async def _apply_load_balancing(self, service_name: str, instances: List[ServiceInstance]) -> List[ServiceInstance]:
        """Apply load balancing strategy to instances"""
        if not instances:
            return instances
        
        lb_state = self.lb_states.get(service_name)
        if not lb_state:
            return instances
        
        strategy = lb_state.strategy
        
        if strategy == LoadBalancingStrategy.ROUND_ROBIN:
            # Rotate instances based on current index
            if lb_state.current_index < len(instances):
                rotated = instances[lb_state.current_index:] + instances[:lb_state.current_index]
                lb_state.current_index = (lb_state.current_index + 1) % len(instances)
                return rotated
        
        elif strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            # Sort by weight (descending) for weighted distribution
            return sorted(instances, key=lambda x: x.weight, reverse=True)
        
        elif strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            # Sort by active connections (ascending)
            return sorted(instances, key=lambda x: x.active_connections)
        
        elif strategy == LoadBalancingStrategy.RANDOM:
            # Return shuffled instances
            import random
            shuffled = instances.copy()
            random.shuffle(shuffled)
            return shuffled
        
        return instances
    
    async def _load_lb_states(self):
        """Load load balancer states from Redis"""
        try:
            states = await self.redis_client.hgetall(self.lb_state_key)
            
            for service_name, data in states.items():
                try:
                    state_data = json.loads(data)
                    self.lb_states[service_name.decode()] = LoadBalancerState(**state_data)
                except Exception as e:
                    logger.error("Failed to load LB state", service_name=service_name, error=str(e))
                    
        except Exception as e:
            logger.error("Failed to load LB states", error=str(e))
    
    async def _cleanup_loop(self):
        """Background task to cleanup expired services"""
        while True:
            try:
                await asyncio.sleep(self.settings.cleanup_interval_seconds)
                
                current_time = datetime.utcnow()
                instances = await self.get_all_services()
                
                for instance in instances:
                    # Check if service has timed out
                    if instance.last_heartbeat:
                        time_since_heartbeat = (current_time - instance.last_heartbeat).total_seconds()
                        if time_since_heartbeat > self.settings.service_timeout_seconds:
                            logger.info(
                                "Removing expired service",
                                service_id=instance.service_id,
                                last_heartbeat=instance.last_heartbeat
                            )
                            await self.deregister_service(instance.service_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
    
    async def _health_monitor_loop(self):
        """Background task to monitor service health"""
        import httpx
        
        while True:
            try:
                await asyncio.sleep(self.settings.health_check_interval)
                
                instances = await self.get_all_services()
                
                # Create health check tasks
                tasks = []
                for instance in instances:
                    if instance.status not in [ServiceStatus.STOPPING]:
                        task = asyncio.create_task(
                            self._perform_health_check(instance)
                        )
                        tasks.append(task)
                
                # Execute health checks concurrently
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in health monitor loop", error=str(e))
    
    async def _perform_health_check(self, instance: ServiceInstance):
        """Perform health check for a service instance"""
        import httpx
        
        try:
            async with httpx.AsyncClient(timeout=instance.health_check_timeout) as client:
                response = await client.get(instance.health_check_full_url)
                
                if response.status_code == 200:
                    await self.update_service_health(instance.service_id, ServiceStatus.HEALTHY)
                else:
                    await self.update_service_health(
                        instance.service_id, 
                        ServiceStatus.UNHEALTHY,
                        {"status_code": response.status_code}
                    )
                    
        except Exception as e:
            await self.update_service_health(
                instance.service_id,
                ServiceStatus.UNHEALTHY,
                {"error": str(e)}
            )
    
    async def _log_service_event(self, event_type: str, service_id: str, service_name: str, details: Dict):
        """Log service lifecycle event"""
        try:
            event = {
                "event_id": str(uuid4()),
                "event_type": event_type,
                "service_id": service_id,
                "service_name": service_name,
                "timestamp": datetime.utcnow().isoformat(),
                "details": details
            }
            
            await self.redis_client.lpush(
                f"{self.events_key}:{service_name}",
                json.dumps(event)
            )
            
            # Keep only recent events
            await self.redis_client.ltrim(
                f"{self.events_key}:{service_name}",
                0, 99  # Keep last 100 events
            )
            
        except Exception as e:
            logger.error("Failed to log service event", error=str(e))
    
    def _matches_metadata_filter(self, metadata: Dict, filters: Dict) -> bool:
        """Check if metadata matches the filter criteria"""
        for key, expected_value in filters.items():
            if key not in metadata:
                return False
            
            actual_value = metadata[key]
            
            # Handle different comparison types
            if isinstance(expected_value, dict):
                # Range comparison: {"min": 1, "max": 10}
                if "min" in expected_value and actual_value < expected_value["min"]:
                    return False
                if "max" in expected_value and actual_value > expected_value["max"]:
                    return False
            elif isinstance(expected_value, list):
                # Must be one of the values
                if actual_value not in expected_value:
                    return False
            else:
                # Exact match
                if actual_value != expected_value:
                    return False
        
        return True