"""
Service Discovery Configuration
Centralized configuration management with environment variable support
"""

from typing import List, Dict, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Service Discovery configuration settings"""
    
    # Service Configuration
    service_name: str = "service-discovery"
    host: str = Field(default="0.0.0.0", description="Service host address")
    port: int = Field(default=8070, description="Service port number")
    debug: bool = Field(default=False, description="Enable debug mode")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", description="Redis connection URL")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_db: int = Field(default=2, description="Redis database number for service registry")
    redis_max_connections: int = Field(default=20, description="Maximum Redis connections")
    
    # Service Registry Configuration
    registry_ttl_seconds: int = Field(default=300, description="Service registration TTL in seconds")
    cleanup_interval_seconds: int = Field(default=60, description="Registry cleanup interval in seconds")
    service_timeout_seconds: int = Field(default=180, description="Service timeout before removal")
    
    # Health Check Configuration
    health_check_interval: int = Field(default=30, description="Default health check interval in seconds")
    health_check_timeout: int = Field(default=10, description="Health check timeout in seconds")
    health_check_retries: int = Field(default=3, description="Health check retry attempts")
    unhealthy_threshold: int = Field(default=3, description="Failed checks before marking unhealthy")
    health_check_workers: int = Field(default=5, description="Concurrent health check workers")
    
    # Load Balancing Configuration
    default_lb_strategy: str = Field(default="round_robin", description="Default load balancing strategy")
    enable_weighted_lb: bool = Field(default=True, description="Enable weighted load balancing")
    connection_tracking: bool = Field(default=True, description="Track active connections for LB")
    
    # Security Configuration
    api_keys: Dict[str, str] = Field(
        default_factory=dict,
        description="API keys for service authentication (service_name:api_key)"
    )
    admin_token: Optional[str] = Field(default=None, description="Admin authentication token")
    require_auth: bool = Field(default=True, description="Require API key authentication")
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="Allowed CORS origins"
    )
    
    # Event Bus Integration
    event_bus_enabled: bool = Field(default=True, description="Enable Event Bus integration")
    event_bus_url: str = Field(default="http://event-bus:8080", description="Event Bus service URL")
    event_bus_api_key: Optional[str] = Field(default=None, description="Event Bus API key")
    
    # Circuit Breaker Configuration
    circuit_breaker_enabled: bool = Field(default=True, description="Enable circuit breaker pattern")
    circuit_breaker_failure_threshold: int = Field(default=5, description="Failures before opening circuit")
    circuit_breaker_success_threshold: int = Field(default=3, description="Successes before closing circuit")
    circuit_breaker_timeout_seconds: int = Field(default=60, description="Circuit breaker timeout")
    
    # Monitoring Configuration
    metrics_enabled: bool = Field(default=True, description="Enable Prometheus metrics")
    tracing_enabled: bool = Field(default=True, description="Enable OpenTelemetry tracing")
    structured_logging: bool = Field(default=True, description="Enable structured logging")
    
    # Admin Dashboard Configuration
    admin_dashboard_enabled: bool = Field(default=True, description="Enable admin web dashboard")
    admin_dashboard_path: str = Field(default="/admin", description="Admin dashboard URL path")
    
    # Performance Configuration
    max_services_per_name: int = Field(default=100, description="Maximum instances per service name")
    request_timeout_seconds: int = Field(default=30, description="API request timeout")
    background_task_timeout: int = Field(default=300, description="Background task timeout")
    
    # High Availability Configuration
    leader_election_enabled: bool = Field(default=False, description="Enable leader election for HA")
    leader_election_ttl: int = Field(default=30, description="Leader election TTL in seconds")
    leader_election_key: str = Field(default="service-discovery:leader", description="Leader election Redis key")
    
    # Data Retention Configuration
    metrics_retention_days: int = Field(default=7, description="Metrics retention period in days")
    event_log_retention_days: int = Field(default=30, description="Event log retention period in days")
    health_check_history_hours: int = Field(default=24, description="Health check history retention in hours")
    
    class Config:
        env_prefix = "SERVICE_DISCOVERY_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Parse complex types from environment
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str):
            if field_name == 'api_keys':
                # Parse "service1:key1,service2:key2" format
                if not raw_val:
                    return {}
                pairs = raw_val.split(',')
                return dict(pair.split(':') for pair in pairs if ':' in pair)
            return cls.json_loads(raw_val)


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


def validate_settings():
    """Validate configuration settings"""
    errors = []
    
    # Validate Redis configuration
    if not settings.redis_url:
        errors.append("Redis URL is required")
    
    # Validate port range
    if not (1024 <= settings.port <= 65535):
        errors.append("Port must be between 1024 and 65535")
    
    # Validate health check configuration
    if settings.health_check_timeout >= settings.health_check_interval:
        errors.append("Health check timeout must be less than interval")
    
    # Validate thresholds
    if settings.unhealthy_threshold < 1:
        errors.append("Unhealthy threshold must be at least 1")
    
    if settings.circuit_breaker_failure_threshold < 1:
        errors.append("Circuit breaker failure threshold must be at least 1")
    
    # Validate Event Bus configuration
    if settings.event_bus_enabled and not settings.event_bus_url:
        errors.append("Event Bus URL is required when Event Bus is enabled")
    
    # Validate admin configuration
    if settings.admin_dashboard_enabled and settings.require_auth and not settings.admin_token:
        errors.append("Admin token is required when dashboard is enabled and auth is required")
    
    if errors:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")


def get_redis_config() -> Dict[str, any]:
    """Get Redis connection configuration"""
    config = {
        "url": settings.redis_url,
        "db": settings.redis_db,
        "max_connections": settings.redis_max_connections,
        "health_check_interval": 30,
        "socket_keepalive": True,
        "socket_keepalive_options": {},
        "retry_on_timeout": True,
        "retry_on_error": [ConnectionError, TimeoutError]
    }
    
    if settings.redis_password:
        config["password"] = settings.redis_password
    
    return config


def get_health_check_config() -> Dict[str, any]:
    """Get health check configuration"""
    return {
        "interval": settings.health_check_interval,
        "timeout": settings.health_check_timeout,
        "retries": settings.health_check_retries,
        "unhealthy_threshold": settings.unhealthy_threshold,
        "workers": settings.health_check_workers
    }


def get_load_balancer_config() -> Dict[str, any]:
    """Get load balancer configuration"""
    return {
        "strategy": settings.default_lb_strategy,
        "weighted_enabled": settings.enable_weighted_lb,
        "connection_tracking": settings.connection_tracking
    }


def get_circuit_breaker_config() -> Dict[str, any]:
    """Get circuit breaker configuration"""
    return {
        "enabled": settings.circuit_breaker_enabled,
        "failure_threshold": settings.circuit_breaker_failure_threshold,
        "success_threshold": settings.circuit_breaker_success_threshold,
        "timeout": settings.circuit_breaker_timeout_seconds
    }