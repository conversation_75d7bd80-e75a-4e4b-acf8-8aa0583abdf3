# Service Discovery Service Dockerfile
# Multi-stage build for optimized production image

FROM python:3.11-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Create non-root user for security
RUN groupadd -r serviceuser && useradd -r -g serviceuser serviceuser

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy installed packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY src/ ./src/
COPY scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/logs && \
    chown -R serviceuser:serviceuser /app

# Switch to non-root user
USER serviceuser

# Add user's local bin to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Set Python path
ENV PYTHONPATH=/app

# Expose service port
EXPOSE 8070

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8070/health || exit 1

# Set default environment
ENV SERVICE_DISCOVERY_HOST=0.0.0.0
ENV SERVICE_DISCOVERY_PORT=8070
ENV SERVICE_DISCOVERY_WORKERS=1

# Start the service
CMD ["sh", "scripts/start.sh"]