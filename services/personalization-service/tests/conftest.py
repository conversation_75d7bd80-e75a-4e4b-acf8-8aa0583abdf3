"""
Pytest configuration and fixtures for Personalization Service tests
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
import os


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
def mock_environment():
    """Mock environment variables for testing"""
    env_vars = {
        'OPENAI_API_KEY': 'test-openai-key',
        'ANTHROPIC_API_KEY': 'test-anthropic-key',
        'PREFERRED_MODEL': 'openai',
        'SERVICE_HOST': 'localhost',
        'SERVICE_PORT': '8082',
        'EVENT_BUS_URL': 'http://localhost:8080',
        'SERVICE_DISCOVERY_URL': 'http://localhost:8070',
        'API_KEY': 'test-api-key',
        'ENVIRONMENT': 'testing'
    }
    
    with patch.dict(os.environ, env_vars):
        yield


@pytest.fixture
def mock_prometheus():
    """Mock Prometheus metrics"""
    with patch('src.monitoring.PROMETHEUS_AVAILABLE', False):
        yield


@pytest.fixture
def sample_personalization_result():
    """Sample personalization result for testing"""
    from src.personalization_agent import PersonalizationResult
    
    return PersonalizationResult(
        personalized_content="This is personalized content tailored for tech enthusiasts with casual communication style.",
        personalization_score=0.87,
        applied_strategies=[
            "communication_style_adaptation",
            "interest_based_examples",
            "vocabulary_adjustment"
        ],
        target_audience="tech enthusiasts", 
        personalization_level="moderate",
        user_preferences_matched=[
            "casual_communication",
            "technology_interests",
            "intermediate_expertise"
        ],
        content_adjustments={
            "tone": "adjusted to casual style",
            "examples": "added technology-related examples",
            "vocabulary": "simplified technical terms"
        },
        timestamp="2024-01-01T12:00:00Z"
    )


@pytest.fixture
def sample_user_profile():
    """Sample user profile for testing"""
    return {
        "interests": ["artificial intelligence", "machine learning", "technology"],
        "communication_style": "casual",
        "expertise_level": "intermediate",
        "preferred_format": "article",
        "age_group": "25-35",
        "profession": "software engineer",
        "reading_preferences": ["technical blogs", "research papers"],
        "content_engagement": {
            "prefers_examples": True,
            "likes_visual_content": True,
            "attention_span": "medium"
        }
    }


@pytest.fixture
def sample_user_data():
    """Sample raw user data for analysis"""
    return {
        "age": 28,
        "location": "San Francisco",
        "job_title": "Software Engineer",
        "education": "Computer Science Degree",
        "browsing_history": [
            "AI research papers",
            "machine learning tutorials",
            "tech news articles"
        ],
        "social_media_activity": {
            "posts_about_tech": 45,
            "shares_technical_content": 23,
            "follows_tech_influencers": 12
        },
        "content_interactions": {
            "time_on_technical_articles": "15 minutes average",
            "saves_technical_content": True,
            "comments_on_tech_posts": "occasionally"
        }
    }