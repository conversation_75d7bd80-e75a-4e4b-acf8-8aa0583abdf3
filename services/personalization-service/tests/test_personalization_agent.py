"""
Tests for Personalization Agent
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.personalization_agent import PersonalizationAgent, PersonalizationResult, UserProfileAnalysis


class TestPersonalizationAgent:
    """Test cases for Personalization Agent"""
    
    def test_personalization_agent_initialization(self):
        """Test personalization agent initialization"""
        agent = PersonalizationAgent()
        
        assert agent._initialized is False
        assert agent._agent is None
        assert agent._profile_agent is None
        assert agent.is_ready is False
    
    def test_get_available_model_openai(self):
        """Test getting OpenAI model"""
        agent = PersonalizationAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'openai',
            'OPENAI_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_anthropic(self):
        """Test getting Anthropic model"""
        agent = PersonalizationAgent()
        
        with patch.dict('os.environ', {
            'PREFERRED_MODEL': 'anthropic',
            'ANTHROPIC_API_KEY': 'test-key'
        }):
            model = agent._get_available_model()
            assert model is not None
    
    def test_get_available_model_none(self):
        """Test when no model is available"""
        agent = PersonalizationAgent()
        
        with patch.dict('os.environ', {}, clear=True):
            model = agent._get_available_model()
            assert model is None
    
    def test_personalization_prompt_content(self):
        """Test personalization prompt contains required elements"""
        agent = PersonalizationAgent()
        prompt = agent._get_personalization_prompt()
        
        assert "personalization specialist" in prompt.lower()
        assert "user profile" in prompt.lower()
        assert "personalization level" in prompt.lower()
        assert "target audience" in prompt.lower()
    
    def test_profile_analysis_prompt_content(self):
        """Test profile analysis prompt contains required elements"""
        agent = PersonalizationAgent()
        prompt = agent._get_profile_analysis_prompt()
        
        assert "user behavior analyst" in prompt.lower()
        assert "user profile" in prompt.lower()
        assert "content personalization" in prompt.lower()
        assert "confidence" in prompt.lower()
    
    def test_format_user_profile(self):
        """Test user profile formatting"""
        agent = PersonalizationAgent()
        
        user_profile = {
            "interests": ["AI", "technology"],
            "age": 30,
            "communication_style": "casual"
        }
        
        formatted = agent._format_user_profile(user_profile)
        
        assert "interests: ['AI', 'technology']" in formatted
        assert "age: 30" in formatted
        assert "communication_style: casual" in formatted
    
    def test_format_user_data(self):
        """Test user data formatting"""
        agent = PersonalizationAgent()
        
        user_data = {
            "location": "San Francisco",
            "reading_habits": "technical blogs",
            "expertise": "intermediate"
        }
        
        formatted = agent._format_user_data(user_data)
        
        assert "location: San Francisco" in formatted
        assert "reading_habits: technical blogs" in formatted
        assert "expertise: intermediate" in formatted
    
    @pytest.mark.asyncio
    async def test_personalize_content_not_initialized(self):
        """Test personalization when agent not initialized"""
        agent = PersonalizationAgent()
        
        with pytest.raises(RuntimeError, match="Personalization agent not initialized"):
            await agent.personalize_content(
                content="Test content",
                user_profile={"interests": ["technology"]}
            )
    
    @pytest.mark.asyncio
    async def test_analyze_user_profile_not_initialized(self):
        """Test profile analysis when agent not initialized"""
        agent = PersonalizationAgent()
        
        with pytest.raises(RuntimeError, match="Profile analysis agent not initialized"):
            await agent.analyze_user_profile(
                user_data={"age": 25, "interests": ["tech"]}
            )
    
    @pytest.mark.asyncio
    async def test_get_personalization_suggestions(self):
        """Test getting personalization suggestions"""
        agent = PersonalizationAgent()
        
        user_profile = {
            "interests": ["AI", "machine learning"],
            "communication_style": "casual",
            "expertise_level": "beginner",
            "preferred_format": "visual"
        }
        
        suggestions = await agent.get_personalization_suggestions("article", user_profile)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        assert any("AI" in suggestion for suggestion in suggestions)
        assert any("casual" in suggestion.lower() for suggestion in suggestions)
        assert any("basic" in suggestion.lower() or "beginner" in suggestion.lower() for suggestion in suggestions)
        assert any("visual" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_get_personalization_suggestions_minimal_profile(self):
        """Test suggestions with minimal user profile"""
        agent = PersonalizationAgent()
        
        user_profile = {}
        suggestions = await agent.get_personalization_suggestions("blog", user_profile)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        # When profile is empty, should return default strategies
        assert any("general" in suggestion.lower() or "basic" in suggestion.lower() or "professional" in suggestion.lower() for suggestion in suggestions)
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test agent cleanup"""
        agent = PersonalizationAgent()
        agent._initialized = True  # Simulate initialized state
        
        await agent.cleanup()
        
        assert agent._initialized is False
    
    def test_personalization_result_model(self):
        """Test PersonalizationResult model"""
        result = PersonalizationResult(
            personalized_content="Personalized content here",
            personalization_score=0.85,
            applied_strategies=["tone_adjustment", "vocabulary_adaptation"],
            target_audience="tech professionals",
            personalization_level="moderate",
            user_preferences_matched=["technical_interests", "formal_tone"],
            content_adjustments={
                "tone": "more professional",
                "examples": "added technical examples"
            },
            timestamp="2024-01-01T00:00:00Z"
        )
        
        assert result.personalized_content == "Personalized content here"
        assert result.personalization_score == 0.85
        assert len(result.applied_strategies) == 2
        assert result.target_audience == "tech professionals"
        assert result.personalization_level == "moderate"
        assert len(result.user_preferences_matched) == 2
        assert "tone" in result.content_adjustments
    
    def test_user_profile_analysis_model(self):
        """Test UserProfileAnalysis model"""
        analysis = UserProfileAnalysis(
            profile={
                "persona": "tech-savvy professional",
                "interests": ["AI", "data science"],
                "communication_style": "formal"
            },
            recommendations=[
                "Use technical terminology",
                "Include data-driven examples",
                "Maintain professional tone"
            ],
            confidence_score=0.92,
            profile_strengths=[
                "clear technical interests",
                "consistent communication patterns"
            ],
            profile_gaps=[
                "missing demographic information",
                "no content format preferences"
            ]
        )
        
        assert analysis.profile["persona"] == "tech-savvy professional"
        assert len(analysis.recommendations) == 3
        assert analysis.confidence_score == 0.92
        assert len(analysis.profile_strengths) == 2
        assert len(analysis.profile_gaps) == 2