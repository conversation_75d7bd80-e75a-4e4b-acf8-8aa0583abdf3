"""
Simple Tests for Personalization Service main application
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient


def test_app_creation():
    """Test that the FastAPI app can be created"""
    with patch('src.main.personalization_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        assert app is not None
        assert app.title == "Personalization Service"


def test_health_endpoint():
    """Test health check endpoint"""
    with patch('src.main.personalization_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "personalization-service"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data


def test_readiness_endpoint_not_ready():
    """Test readiness endpoint when not ready"""
    with patch('src.main.personalization_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.get("/ready")
        
        assert response.status_code == 503
        assert "not ready" in response.json()["detail"]


def test_personalization_endpoint_no_auth():
    """Test personalization endpoint without authentication"""
    with patch('src.main.personalization_agent', None), \
         patch('src.main.event_client', None), \
         patch('src.main.service_registry', None), \
         patch('src.main.security_manager', None):
        
        from src.main import app
        client = TestClient(app)
        
        response = client.post(
            "/personalize",
            json={
                "content": "Test content",
                "user_profile": {"interests": ["technology"]}
            }
        )
        
        # Should fail due to missing authorization
        assert response.status_code == 403


def test_models_validation():
    """Test Pydantic models"""
    from src.main import PersonalizationRequest, PersonalizationResponse, HealthResponse, UserProfileAnalysisRequest
    
    # Test PersonalizationRequest
    request = PersonalizationRequest(
        content="Test content",
        user_profile={"interests": ["AI", "technology"]}
    )
    assert request.content == "Test content"
    assert request.target_audience == "general"  # default
    assert request.personalization_level == "moderate"  # default
    
    # Test PersonalizationResponse
    response = PersonalizationResponse(
        request_id="test-123",
        status="pending",
        message="Started"
    )
    assert response.request_id == "test-123"
    assert response.status == "pending"
    
    # Test UserProfileAnalysisRequest
    profile_request = UserProfileAnalysisRequest(
        user_data={"age": 25, "interests": ["tech"]}
    )
    assert profile_request.analysis_depth == "standard"  # default
    
    # Test HealthResponse
    health = HealthResponse(
        status="healthy",
        service="test-service",
        version="1.0.0",
        timestamp="2024-01-01T00:00:00Z"
    )
    assert health.status == "healthy"


def test_personalization_request_validation():
    """Test PersonalizationRequest validation"""
    from src.main import PersonalizationRequest
    
    # Valid request with all fields
    request = PersonalizationRequest(
        content="Test content for personalization",
        user_profile={
            "interests": ["AI", "machine learning"],
            "communication_style": "casual",
            "expertise_level": "intermediate"
        },
        target_audience="tech enthusiasts",
        personalization_level="heavy",
        user_id="user123"
    )
    
    assert request.content == "Test content for personalization"
    assert request.user_profile["interests"] == ["AI", "machine learning"]
    assert request.target_audience == "tech enthusiasts"
    assert request.personalization_level == "heavy"
    assert request.user_id == "user123"


def test_profile_analysis_models():
    """Test profile analysis models"""
    from src.main import UserProfileAnalysisRequest, UserProfileAnalysisResponse
    
    # Analysis request
    request = UserProfileAnalysisRequest(
        user_data={
            "age": 30,
            "location": "San Francisco",
            "interests": ["technology", "science"],
            "reading_habits": "technical blogs"
        },
        analysis_depth="comprehensive"
    )
    
    assert request.user_data["age"] == 30
    assert request.analysis_depth == "comprehensive"
    
    # Analysis response
    response = UserProfileAnalysisResponse(
        request_id="analysis-123",
        user_profile={
            "persona": "tech-savvy professional",
            "preferences": {"content_format": "detailed"}
        },
        recommendations=["Use technical examples", "Include data visualizations"],
        confidence_score=0.85
    )
    
    assert response.request_id == "analysis-123"
    assert response.confidence_score == 0.85
    assert len(response.recommendations) == 2