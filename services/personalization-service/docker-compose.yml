# Personalization Service Docker Compose Configuration
version: '3.8'

services:
  personalization-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    environment:
      # Service configuration
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8082
      - SERVICE_NAME=personalization-service
      
      # AI Model configuration
      - PREFERRED_MODEL=openai
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      
      # Infrastructure services
      - EVENT_BUS_URL=http://event-bus:8080
      - SERVICE_DISCOVERY_URL=http://service-discovery:8070
      
      # Security
      - API_KEY=${PERSONALIZATION_SERVICE_API_KEY}
      
      # Monitoring
      - ENVIRONMENT=development
      
    volumes:
      # Mount security certificates for mTLS
      - ../../security/certs:/etc/ssl/certs:ro
      
    networks:
      - publish-ai-network
    
    depends_on:
      - event-bus
      - service-discovery
    
    restart: unless-stopped
    
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.personalization-service.rule=PathPrefix(`/api/personalization`)"
      - "traefik.http.services.personalization-service.loadbalancer.server.port=8082"

networks:
  publish-ai-network:
    external: true