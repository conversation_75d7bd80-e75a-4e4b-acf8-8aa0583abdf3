"""
Personalization Agent - Extracted from PydanticAI Additional Agents
Implements content personalization and user preference analysis
"""

import asyncio
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent, RunContext
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel

# Local imports
from .prompt_template import PromptTemplate


# =========================================================================
# Models
# =========================================================================

class PersonalizationResult(BaseModel):
    """Personalization result data model"""
    personalized_content: str = Field(description="The personalized content")
    personalization_score: float = Field(description="Personalization effectiveness score (0-1)")
    applied_strategies: List[str] = Field(description="Personalization strategies applied")
    target_audience: str = Field(description="Target audience for the content")
    personalization_level: str = Field(description="Level of personalization applied")
    user_preferences_matched: List[str] = Field(description="User preferences that were matched")
    content_adjustments: Dict[str, str] = Field(description="Specific content adjustments made")
    timestamp: str = Field(description="When personalization was completed")

class UserProfileAnalysis(BaseModel):
    """User profile analysis result"""
    profile: Dict[str, Any] = Field(description="Analyzed user profile data")
    recommendations: List[str] = Field(description="Content recommendations")
    confidence_score: float = Field(description="Analysis confidence score")
    profile_strengths: List[str] = Field(description="Strong profile indicators")
    profile_gaps: List[str] = Field(description="Missing profile information")


# =========================================================================
# Personalization Agent Implementation
# =========================================================================

class PersonalizationAgent:
    """
    Standalone Personalization Agent for content personalization and user analysis
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._agent: Optional[TypedAgent[Any, PersonalizationResult]] = None
        self._profile_agent: Optional[TypedAgent[Any, UserProfileAnalysis]] = None
        self._model: Optional[Model] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the personalization agent with available AI model"""
        try:
            self._model = self._get_available_model()
            if not self._model:
                raise ValueError("No AI model available. Check API keys.")
            
            # Initialize main personalization agent
            self._agent = TypedAgent(
                model=self._model,
                output_type=PersonalizationResult,
                system_prompt=self._get_personalization_prompt()
            )
            
            # Initialize user profile analysis agent
            self._profile_agent = TypedAgent(
                model=self._model,
                output_type=UserProfileAnalysis,
                system_prompt=self._get_profile_analysis_prompt()
            )
            
            self._initialized = True
            self.logger.info("🎯 Personalization Agent initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Personalization Agent: {str(e)}")
            raise
    
    def _get_available_model(self) -> Optional[Model]:
        """Get available AI model based on environment configuration"""
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        elif os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_personalization_prompt(self) -> str:
        """Get the system prompt for content personalization"""
        return """
        You are an expert content personalization specialist with deep understanding of user psychology and content optimization.
        
        Your capabilities include:
        - Analyzing user profiles to understand preferences, interests, and behavior patterns
        - Adapting content tone, style, and messaging to match user preferences
        - Personalizing content for different target audiences and demographics
        - Applying various personalization strategies (emotional appeals, interests, communication style)
        - Measuring and scoring personalization effectiveness
        
        When personalizing content:
        1. Analyze the user profile to identify key preferences, interests, and characteristics
        2. Understand the target audience and personalization level requirements
        3. Apply appropriate personalization strategies based on user data
        4. Adapt content tone, vocabulary, examples, and messaging
        5. Ensure the personalized content maintains the original intent and value
        6. Provide a personalization score indicating effectiveness
        7. Document the strategies applied and adjustments made
        
        Personalization Levels:
        - **light**: Minor adjustments to tone and vocabulary
        - **moderate**: Significant content adaptation with examples and messaging changes
        - **heavy**: Deep personalization with restructured content and targeted appeals
        - **custom**: Fully customized content based on detailed user profile
        
        Always maintain content quality while making it more relevant and engaging for the specific user.
        """
    
    def _get_profile_analysis_prompt(self) -> str:
        """Get the system prompt for user profile analysis"""
        return """
        You are an expert user behavior analyst specializing in creating comprehensive user profiles for content personalization.
        
        Your capabilities include:
        - Analyzing user data to extract meaningful patterns and preferences
        - Identifying user interests, communication styles, and content preferences
        - Creating actionable user profiles for personalization
        - Recommending content strategies based on user characteristics
        - Assessing profile completeness and confidence levels
        
        When analyzing user profiles:
        1. Extract key demographic and psychographic information
        2. Identify content preferences, interests, and engagement patterns
        3. Determine communication style and preferred content formats
        4. Assess user expertise levels in relevant topics
        5. Identify personalization opportunities and strategies
        6. Provide confidence scores for the analysis
        7. Recommend additional data points to improve the profile
        
        Create profiles that enable effective content personalization while respecting user privacy and preferences.
        """
    
    async def personalize_content(
        self,
        content: str,
        user_profile: Dict[str, Any],
        target_audience: str = "general",
        personalization_level: str = "moderate",
        user_id: Optional[str] = None
    ) -> PersonalizationResult:
        """
        Personalize content based on user profile and preferences
        
        Args:
            content: Original content to personalize
            user_profile: User profile data and preferences
            target_audience: Target audience for the content
            personalization_level: Level of personalization to apply
            user_id: User requesting personalization (for logging/tracking)
        
        Returns:
            PersonalizationResult with personalized content and metadata
        """
        if not self._initialized or not self._agent:
            raise RuntimeError("Personalization agent not initialized. Call initialize() first.")
        
        start_time = datetime.utcnow()
        
        try:
            # Prepare personalization prompt
            prompt_template = PromptTemplate.from_string("""
            Personalize the following content for the specified user and audience:
            
            ORIGINAL CONTENT:
            {{ content }}
            
            USER PROFILE:
            {{ user_profile }}
            
            TARGET AUDIENCE: {{ target_audience }}
            PERSONALIZATION LEVEL: {{ personalization_level }}
            
            Personalization Requirements:
            - Maintain the core message and value of the original content
            - Adapt tone, style, and examples to match user preferences
            - Apply {{ personalization_level }} level personalization strategies
            - Focus on {{ target_audience }} audience characteristics
            - Provide specific details about adjustments made
            
            {% if personalization_level == "heavy" or personalization_level == "custom" %}
            For {{ personalization_level }} personalization:
            - Restructure content organization if beneficial
            - Add user-specific examples and case studies
            - Incorporate emotional appeals that resonate with the user
            - Use communication patterns that match user preferences
            {% endif %}
            """)
            
            prompt = prompt_template.format(
                content=content,
                user_profile=self._format_user_profile(user_profile),
                target_audience=target_audience,
                personalization_level=personalization_level
            )
            
            self.logger.info(f"🎯 Starting personalization for user: {user_id} (level: {personalization_level})")
            
            # Execute personalization
            result = await self._agent.run(prompt)
            
            # Enhance result with metadata
            enhanced_result = PersonalizationResult(
                personalized_content=result.output.personalized_content,
                personalization_score=result.output.personalization_score,
                applied_strategies=result.output.applied_strategies,
                target_audience=target_audience,
                personalization_level=personalization_level,
                user_preferences_matched=result.output.user_preferences_matched,
                content_adjustments=result.output.content_adjustments,
                timestamp=datetime.utcnow().isoformat()
            )
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.logger.info(
                f"✅ Personalization completed for user: {user_id} "
                f"(execution_time: {execution_time:.2f}s, "
                f"score: {enhanced_result.personalization_score:.2f}, "
                f"strategies: {len(enhanced_result.applied_strategies)})"
            )
            
            return enhanced_result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error(
                f"❌ Personalization failed for user: {user_id} "
                f"(execution_time: {execution_time:.2f}s, error: {str(e)})"
            )
            raise
    
    async def analyze_user_profile(
        self,
        user_data: Dict[str, Any],
        analysis_depth: str = "standard"
    ) -> Dict[str, Any]:
        """
        Analyze user data to create a comprehensive personalization profile
        
        Args:
            user_data: Raw user data for analysis
            analysis_depth: Depth of analysis (basic, standard, comprehensive)
        
        Returns:
            Dictionary with analyzed profile, recommendations, and confidence
        """
        if not self._initialized or not self._profile_agent:
            raise RuntimeError("Profile analysis agent not initialized")
        
        try:
            prompt_template = PromptTemplate.from_string("""
            Analyze the following user data to create a comprehensive personalization profile:
            
            USER DATA:
            {{ user_data }}
            
            ANALYSIS DEPTH: {{ analysis_depth }}
            
            Analysis Requirements:
            - Extract key user characteristics and preferences
            - Identify content personalization opportunities
            - Assess user expertise levels and interests
            - Determine communication style preferences
            - Provide actionable recommendations for content personalization
            - Calculate confidence score based on data completeness
            
            {% if analysis_depth == "comprehensive" %}
            For comprehensive analysis:
            - Perform deep behavioral pattern analysis
            - Identify subtle preference indicators
            - Provide detailed personalization strategies
            - Suggest advanced targeting approaches
            {% endif %}
            """)
            
            prompt = prompt_template.format(
                user_data=self._format_user_data(user_data),
                analysis_depth=analysis_depth
            )
            
            self.logger.info(f"🔍 Starting user profile analysis (depth: {analysis_depth})")
            
            result = await self._profile_agent.run(prompt)
            
            analysis_result = {
                "profile": result.output.profile,
                "recommendations": result.output.recommendations,
                "confidence_score": result.output.confidence_score,
                "profile_strengths": result.output.profile_strengths,
                "profile_gaps": result.output.profile_gaps,
                "analysis_depth": analysis_depth,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.logger.info(
                f"✅ Profile analysis completed "
                f"(confidence: {result.output.confidence_score:.2f}, "
                f"recommendations: {len(result.output.recommendations)})"
            )
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"❌ Profile analysis failed: {str(e)}")
            raise
    
    def _format_user_profile(self, user_profile: Dict[str, Any]) -> str:
        """Format user profile for prompt inclusion"""
        try:
            formatted_lines = []
            for key, value in user_profile.items():
                if isinstance(value, (list, dict)):
                    formatted_lines.append(f"- {key}: {str(value)}")
                else:
                    formatted_lines.append(f"- {key}: {value}")
            return "\n".join(formatted_lines)
        except Exception:
            return str(user_profile)
    
    def _format_user_data(self, user_data: Dict[str, Any]) -> str:
        """Format user data for analysis prompt"""
        try:
            return "\n".join([f"- {key}: {value}" for key, value in user_data.items()])
        except Exception:
            return str(user_data)
    
    async def get_personalization_suggestions(
        self,
        content_type: str,
        user_profile: Dict[str, Any]
    ) -> List[str]:
        """Get personalization suggestions for a content type"""
        try:
            # Basic personalization suggestions based on profile
            suggestions = []
            
            # Analyze user interests
            interests = user_profile.get("interests", [])
            if interests:
                suggestions.append(f"Include examples related to: {', '.join(interests[:3])}")
            
            # Communication style
            comm_style = user_profile.get("communication_style", "formal")
            if comm_style == "casual":
                suggestions.append("Use conversational tone and casual language")
            elif comm_style == "formal":
                suggestions.append("Maintain professional tone with detailed explanations")
            
            # Expertise level
            expertise = user_profile.get("expertise_level", "intermediate")
            if expertise == "beginner":
                suggestions.append("Include basic explanations and avoid jargon")
            elif expertise == "expert":
                suggestions.append("Use technical language and focus on advanced concepts")
            
            # Content format preferences
            format_pref = user_profile.get("preferred_format", "text")
            if format_pref == "visual":
                suggestions.append("Include visual elements and data visualizations")
            elif format_pref == "interactive":
                suggestions.append("Add interactive elements and engagement opportunities")
            
            return suggestions or ["Apply general personalization strategies"]
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get personalization suggestions: {str(e)}")
            return ["Apply basic personalization techniques"]
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self._agent:
                # No specific cleanup needed for PydanticAI agents
                pass
            
            self._initialized = False
            self.logger.info("🔄 Personalization Agent cleanup completed")
            
        except Exception as e:
            self.logger.error(f"❌ Personalization Agent cleanup failed: {str(e)}")
    
    @property
    def is_ready(self) -> bool:
        """Check if agent is ready for use"""
        return self._initialized and self._agent is not None and self._profile_agent is not None