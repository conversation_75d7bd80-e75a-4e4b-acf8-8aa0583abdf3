"""
Prompt Template utility for Research Service
Simple template engine for formatting prompts
"""

import re
from typing import Dict, Any, List


class PromptTemplate:
    """
    Simple template engine for formatting prompts with variable substitution
    """
    
    def __init__(self, template: str):
        self.template = template
        self._variables = self._extract_variables()
    
    @classmethod
    def from_string(cls, template: str) -> 'PromptTemplate':
        """Create template from string"""
        return cls(template)
    
    def _extract_variables(self) -> List[str]:
        """Extract variable names from template"""
        # Find all {{ variable }} patterns
        pattern = r'\{\{\s*(\w+)\s*\}\}'
        return list(set(re.findall(pattern, self.template)))
    
    def format(self, **kwargs) -> str:
        """Format template with provided variables"""
        result = self.template
        
        # Handle simple variable substitution {{ variable }}
        for var_name, value in kwargs.items():
            pattern = r'\{\{\s*' + re.escape(var_name) + r'\s*\}\}'
            result = re.sub(pattern, str(value), result)
        
        # Handle conditional blocks {% if variable %}...{% endif %}
        result = self._process_conditionals(result, kwargs)
        
        # Handle loops {% for item in list %}...{% endfor %}
        result = self._process_loops(result, kwargs)
        
        return result.strip()
    
    def _process_conditionals(self, text: str, variables: Dict[str, Any]) -> str:
        """Process conditional blocks"""
        # Pattern for {% if variable %}...{% endif %}
        pattern = r'\{\%\s*if\s+(\w+)\s*\%\}(.*?)\{\%\s*endif\s*\%\}'
        
        def replace_conditional(match):
            var_name = match.group(1)
            content = match.group(2)
            
            # Check if variable exists and is truthy
            if var_name in variables and variables[var_name]:
                return content
            else:
                return ""
        
        return re.sub(pattern, replace_conditional, text, flags=re.DOTALL)
    
    def _process_loops(self, text: str, variables: Dict[str, Any]) -> str:
        """Process loop blocks"""
        # Pattern for {% for item in list %}...{% endfor %}
        pattern = r'\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}(.*?)\{\%\s*endfor\s*\%\}'
        
        def replace_loop(match):
            item_name = match.group(1)
            list_name = match.group(2)
            content = match.group(3)
            
            if list_name not in variables:
                return ""
            
            items = variables[list_name]
            if not isinstance(items, (list, tuple)):
                return ""
            
            result_parts = []
            for item in items:
                # Replace {{ item }} in content
                item_content = content.replace(f'{{{{ {item_name} }}}}', str(item))
                result_parts.append(item_content)
            
            return '\n'.join(result_parts)
        
        return re.sub(pattern, replace_loop, text, flags=re.DOTALL)
    
    @property
    def variables(self) -> List[str]:
        """Get list of variables in template"""
        return self._variables.copy()
    
    def validate(self, **kwargs) -> List[str]:
        """Validate that all required variables are provided"""
        missing = []
        for var in self._variables:
            if var not in kwargs:
                missing.append(var)
        return missing


# Predefined templates for common research scenarios
RESEARCH_TEMPLATES = {
    "basic_research": PromptTemplate.from_string("""
        Conduct basic research on the topic: "{{ topic }}"
        
        Please provide:
        - 3-5 key findings about this topic
        - 2-3 reliable sources
        - A brief summary (2-3 sentences)
        
        Focus on factual, current information.
    """),
    
    "comprehensive_research": PromptTemplate.from_string("""
        Conduct comprehensive research on the topic: "{{ topic }}"
        
        Research Requirements:
        - Provide 8-12 detailed key findings
        - Include 5-8 diverse and credible sources
        - Cover multiple perspectives and aspects
        - Include current trends and statistics where relevant
        - Provide a comprehensive summary (1-2 paragraphs)
        
        {% if focus_areas %}
        Please focus specifically on these areas:
        {% for area in focus_areas %}
        - {{ area }}
        {% endfor %}
        {% endif %}
    """),
    
    "expert_research": PromptTemplate.from_string("""
        Conduct expert-level research on the topic: "{{ topic }}"
        
        Research Requirements:
        - Provide 12-15 detailed findings with technical depth
        - Include 8-12 authoritative sources (academic, industry, government)
        - Cover industry trends, technical specifications, and future outlook
        - Include competitive landscape analysis where applicable
        - Provide comprehensive executive summary (2-3 paragraphs)
        - Address potential challenges and opportunities
        
        {% if focus_areas %}
        Focus areas for expert analysis:
        {% for area in focus_areas %}
        - {{ area }}
        {% endfor %}
        {% endif %}
        
        Ensure technical accuracy and cite specific data points where available.
    """)
}


def get_research_template(research_depth: str) -> PromptTemplate:
    """Get appropriate template based on research depth"""
    template_map = {
        "basic": "basic_research",
        "moderate": "comprehensive_research", 
        "comprehensive": "comprehensive_research",
        "expert": "expert_research"
    }
    
    template_name = template_map.get(research_depth, "comprehensive_research")
    return RESEARCH_TEMPLATES[template_name]