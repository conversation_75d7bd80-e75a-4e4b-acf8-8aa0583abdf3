"""
Service Registry Client for Research Service
Handles registration and discovery with the Service Discovery Service
"""

import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime
import logging

import aiohttp
from pydantic import BaseModel


class ServiceRegistration(BaseModel):
    """Service registration model"""
    service_name: str
    service_version: str
    host: str
    port: int
    protocol: str = "http"
    health_check_path: str = "/health"
    metadata: Dict[str, Any] = {}


class ServiceRegistryClient:
    """
    Client for registering with and communicating with Service Discovery
    """
    
    def __init__(
        self,
        service_name: str,
        service_version: str,
        host: str,
        port: int,
        registry_url: Optional[str] = None,
        security_manager: Optional[Any] = None
    ):
        self.service_name = service_name
        self.service_version = service_version
        self.host = host
        self.port = port
        self.registry_url = registry_url or "http://service-discovery:8070"
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        self._registered = False
        self._service_id: Optional[str] = None
    
    async def register(self):
        """Register this service with Service Discovery"""
        try:
            headers = {}
            if self.security_manager:
                api_key = self.security_manager.get_api_key()
                if api_key:
                    headers["Authorization"] = f"Bearer {api_key}"
            
            self._session = aiohttp.ClientSession(headers=headers)
            
            registration = ServiceRegistration(
                service_name=self.service_name,
                service_version=self.service_version,
                host=self.host,
                port=self.port,
                protocol="http",
                health_check_path="/health",
                metadata={
                    "type": "ai-agent-service",
                    "agent_type": "research-assistant",
                    "capabilities": ["topic-research", "information-gathering"],
                    "startup_time": datetime.utcnow().isoformat()
                }
            )
            
            async with self._session.post(
                f"{self.registry_url}/services/register",
                json=registration.model_dump()
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    self._service_id = result.get("service_id")
                    self._registered = True
                    self.logger.info(
                        f"📋 Registered with Service Discovery: {self.service_name} "
                        f"(ID: {self._service_id})"
                    )
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Service registration failed: {response.status} - {error_text}")
                    
        except Exception as e:
            self.logger.warning(f"⚠️  Failed to register with Service Discovery: {str(e)}")
            # Don't fail startup if Service Discovery is not available
            self._registered = False
    
    async def deregister(self):
        """Deregister this service from Service Discovery"""
        if not self._registered or not self._service_id or not self._session:
            return
        
        try:
            async with self._session.delete(
                f"{self.registry_url}/services/{self._service_id}"
            ) as response:
                if response.status == 200:
                    self.logger.info(f"📋 Deregistered service: {self._service_id}")
                else:
                    self.logger.warning(f"⚠️  Deregistration failed: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Deregistration error: {str(e)}")
        
        finally:
            if self._session:
                await self._session.close()
                self._session = None
            
            self._registered = False
            self._service_id = None
    
    async def discover_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Discover another service"""
        if not self._session:
            return None
        
        try:
            async with self._session.get(
                f"{self.registry_url}/services/discover/{service_name}"
            ) as response:
                if response.status == 200:
                    service_info = await response.json()
                    self.logger.info(f"🔍 Discovered service: {service_name}")
                    return service_info
                else:
                    self.logger.warning(f"⚠️  Service discovery failed for {service_name}: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Service discovery error: {str(e)}")
            return None
    
    async def update_health_status(self, status: str, metadata: Optional[Dict[str, Any]] = None):
        """Update health status with Service Discovery"""
        if not self._registered or not self._service_id or not self._session:
            return
        
        try:
            payload = {
                "status": status,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            async with self._session.put(
                f"{self.registry_url}/services/{self._service_id}/health",
                json=payload
            ) as response:
                if response.status != 200:
                    self.logger.warning(f"⚠️  Health status update failed: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Health status update error: {str(e)}")
    
    @property
    def is_registered(self) -> bool:
        """Check if service is registered"""
        return self._registered
    
    @property
    def service_id(self) -> Optional[str]:
        """Get service ID"""
        return self._service_id