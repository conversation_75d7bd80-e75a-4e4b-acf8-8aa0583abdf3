"""
Monitoring and Metrics for Personalization Service
Implements Prometheus metrics and observability
"""

import time
from typing import Optional, Dict, Any
from functools import wraps
import logging

try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, REGISTRY
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Mock classes for when Prometheus is not available
    Counter = Histogram = Gauge = CollectorRegistry = object


class MetricsManager:
    """Manages Prometheus metrics for the Personalization Service"""
    
    def __init__(self, service_name: str = "personalization_service"):
        self.service_name = service_name
        self.logger = logging.getLogger(__name__)
        self._registry: Optional[CollectorRegistry] = None
        self._metrics: Dict[str, Any] = {}
        
        if PROMETHEUS_AVAILABLE:
            self._setup_metrics()
        else:
            self.logger.warning("⚠️  Prometheus client not available, metrics disabled")
    
    def _setup_metrics(self):
        """Setup Prometheus metrics"""
        try:
            self._registry = REGISTRY
            
            # Request metrics
            self._metrics['request_count'] = Counter(
                'personalization_requests_total',
                'Total number of personalization requests',
                ['method', 'endpoint', 'status'],
                registry=self._registry
            )
            
            self._metrics['request_duration'] = Histogram(
                'personalization_request_duration_seconds',
                'Personalization request duration in seconds',
                ['method', 'endpoint'],
                registry=self._registry
            )
            
            # Personalization-specific metrics
            self._metrics['personalization_count'] = Counter(
                'personalization_operations_total',
                'Total number of personalization operations',
                ['personalization_level', 'target_audience', 'status'],
                registry=self._registry
            )
            
            self._metrics['personalization_duration'] = Histogram(
                'personalization_operation_duration_seconds',
                'Personalization operation duration in seconds',
                ['personalization_level'],
                registry=self._registry
            )
            
            self._metrics['personalization_score'] = Histogram(
                'personalization_effectiveness_score',
                'Personalization effectiveness score (0-1)',
                ['personalization_level', 'target_audience'],
                registry=self._registry
            )
            
            # Profile analysis metrics
            self._metrics['profile_analysis_count'] = Counter(
                'profile_analysis_total',
                'Total number of profile analyses',
                ['analysis_depth', 'status'],
                registry=self._registry
            )
            
            # Agent metrics
            self._metrics['agent_status'] = Gauge(
                'personalization_agent_status',
                'Personalization agent status (1=ready, 0=not ready)',
                registry=self._registry
            )
            
            # Event Bus metrics
            self._metrics['event_count'] = Counter(
                'personalization_events_total',
                'Total number of events published',
                ['event_type', 'status'],
                registry=self._registry
            )
            
            self.logger.info("📊 Metrics setup completed")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup metrics: {str(e)}")
    
    def record_request(self, method: str, endpoint: str, status: str, duration: float):
        """Record HTTP request metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['request_count'].labels(
                method=method,
                endpoint=endpoint,
                status=status
            ).inc()
            
            self._metrics['request_duration'].labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record request metrics: {str(e)}")
    
    def record_personalization_operation(self, personalization_level: str, target_audience: str, status: str, duration: float, score: float = None):
        """Record personalization operation metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['personalization_count'].labels(
                personalization_level=personalization_level,
                target_audience=target_audience,
                status=status
            ).inc()
            
            self._metrics['personalization_duration'].labels(
                personalization_level=personalization_level
            ).observe(duration)
            
            if score is not None:
                self._metrics['personalization_score'].labels(
                    personalization_level=personalization_level,
                    target_audience=target_audience
                ).observe(score)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record personalization metrics: {str(e)}")
    
    def record_profile_analysis(self, analysis_depth: str, status: str):
        """Record profile analysis metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['profile_analysis_count'].labels(
                analysis_depth=analysis_depth,
                status=status
            ).inc()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record profile analysis metrics: {str(e)}")
    
    def set_agent_status(self, ready: bool):
        """Set agent status metric"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['agent_status'].set(1 if ready else 0)
        except Exception as e:
            self.logger.error(f"❌ Failed to set agent status: {str(e)}")
    
    def record_event(self, event_type: str, status: str):
        """Record event publication metrics"""
        if not PROMETHEUS_AVAILABLE or not self._metrics:
            return
        
        try:
            self._metrics['event_count'].labels(
                event_type=event_type,
                status=status
            ).inc()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record event metrics: {str(e)}")
    
    def get_registry(self) -> Optional[CollectorRegistry]:
        """Get the metrics registry"""
        return self._registry


# Global metrics manager instance
_metrics_manager: Optional[MetricsManager] = None


def setup_monitoring(service_name: str = "personalization_service"):
    """Setup monitoring for the service"""
    global _metrics_manager
    _metrics_manager = MetricsManager(service_name)
    return _metrics_manager


def get_metrics_manager() -> Optional[MetricsManager]:
    """Get the global metrics manager"""
    return _metrics_manager


def get_metrics_registry() -> Optional[CollectorRegistry]:
    """Get the metrics registry"""
    if _metrics_manager:
        return _metrics_manager.get_registry()
    return None


def monitor_request(func):
    """Decorator to monitor HTTP requests"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        status = "success"
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status = "error"
            raise
        finally:
            if _metrics_manager:
                duration = time.time() - start_time
                # Extract method and endpoint from function context if available
                method = getattr(func, '_method', 'unknown')
                endpoint = getattr(func, '_endpoint', func.__name__)
                _metrics_manager.record_request(method, endpoint, status, duration)
    
    return wrapper


def monitor_research_operation(func):
    """Decorator to monitor research operations"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        status = "success"
        research_depth = "unknown"
        
        try:
            # Try to extract research_depth from kwargs
            research_depth = kwargs.get('research_depth', 'comprehensive')
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status = "error"
            raise
        finally:
            if _metrics_manager:
                duration = time.time() - start_time
                _metrics_manager.record_research_operation(research_depth, status, duration)
    
    return wrapper