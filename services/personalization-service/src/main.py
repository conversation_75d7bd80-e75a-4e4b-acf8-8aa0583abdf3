"""
Personalization Service - Microservice for Personalization Engine Agent
Provides content personalization and user preference management capabilities
"""

import asyncio
import os
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from .personalization_agent import PersonalizationAgent, PersonalizationResult
from .event_client import EventClient
from .service_registry_client import ServiceRegistryClient
from .security_manager import SecurityManager
from .monitoring import setup_monitoring, get_metrics_registry


# =========================================================================
# Request/Response Models
# =========================================================================

class PersonalizationRequest(BaseModel):
    content: str = Field(..., description="Content to personalize")
    user_profile: Dict[str, Any] = Field(..., description="User profile data")
    target_audience: str = Field(default="general", description="Target audience")
    personalization_level: str = Field(default="moderate", description="Personalization level")
    user_id: Optional[str] = Field(default=None, description="User requesting personalization")

class PersonalizationResponse(BaseModel):
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[PersonalizationResult] = Field(default=None, description="Personalization results")
    message: str = Field(default="", description="Status message")

class UserProfileAnalysisRequest(BaseModel):
    user_data: Dict[str, Any] = Field(..., description="Raw user data for analysis")
    analysis_depth: str = Field(default="standard", description="Analysis depth level")

class UserProfileAnalysisResponse(BaseModel):
    request_id: str = Field(..., description="Request identifier")
    user_profile: Dict[str, Any] = Field(..., description="Analyzed user profile")
    recommendations: List[str] = Field(..., description="Profile-based recommendations")
    confidence_score: float = Field(..., description="Analysis confidence (0-1)")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service health status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    timestamp: str = Field(..., description="Health check timestamp")


# =========================================================================
# Global Components
# =========================================================================

personalization_agent: Optional[PersonalizationAgent] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None
security = HTTPBearer()


# =========================================================================
# Lifecycle Management
# =========================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage service lifecycle"""
    global personalization_agent, event_client, service_registry, security_manager
    
    # Startup
    print("🎯 Starting Personalization Service...")
    
    # Initialize monitoring
    setup_monitoring("personalization-service")
    
    # Initialize security
    security_manager = SecurityManager()
    
    # Initialize personalization agent
    personalization_agent = PersonalizationAgent()
    await personalization_agent.initialize()
    
    # Initialize event client
    event_client = EventClient(
        service_name="personalization-service",
        security_manager=security_manager
    )
    await event_client.connect()
    
    # Initialize service registry
    service_registry = ServiceRegistryClient(
        service_name="personalization-service",
        service_version="1.0.0",
        host=os.getenv("SERVICE_HOST", "localhost"),
        port=int(os.getenv("SERVICE_PORT", "8082")),
        security_manager=security_manager
    )
    await service_registry.register()
    
    print("✅ Personalization Service started successfully")
    
    yield
    
    # Shutdown
    print("🔄 Shutting down Personalization Service...")
    
    if service_registry:
        await service_registry.deregister()
    
    if event_client:
        await event_client.disconnect()
    
    if personalization_agent:
        await personalization_agent.cleanup()
    
    print("✅ Personalization Service shutdown complete")


# =========================================================================
# FastAPI Application
# =========================================================================

app = FastAPI(
    title="Personalization Service",
    description="Microservice for content personalization and user preference management",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# =========================================================================
# Security Dependencies
# =========================================================================

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify API key authentication"""
    if not security_manager:
        raise HTTPException(status_code=500, detail="Security manager not initialized")
    
    api_key = credentials.credentials
    if not security_manager.verify_api_key(api_key):
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return api_key


# =========================================================================
# API Endpoints
# =========================================================================

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    
    return HealthResponse(
        status="healthy",
        service="personalization-service",
        version="1.0.0",
        timestamp=datetime.utcnow().isoformat()
    )


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    if not personalization_agent or not event_client or not service_registry:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return {"status": "ready"}


@app.post("/personalize", response_model=PersonalizationResponse)
async def personalize_content(
    request: PersonalizationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Personalize content using the personalization engine agent"""
    if not personalization_agent:
        raise HTTPException(status_code=503, detail="Personalization agent not available")
    
    request_id = str(uuid.uuid4())
    
    # Start background personalization task
    background_tasks.add_task(
        execute_personalization_task,
        request_id,
        request,
        api_key
    )
    
    return PersonalizationResponse(
        request_id=request_id,
        status="started",
        message="Personalization task started successfully"
    )


@app.get("/personalize/{request_id}", response_model=PersonalizationResponse)
async def get_personalization_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Get personalization task status and results"""
    # In a real implementation, this would check task status in database/cache
    # For now, return a placeholder response
    return PersonalizationResponse(
        request_id=request_id,
        status="completed",
        message="Personalization results available"
    )


@app.post("/personalize/sync", response_model=PersonalizationResponse)
async def personalize_content_sync(
    request: PersonalizationRequest,
    api_key: str = Depends(verify_api_key)
):
    """Synchronous content personalization (for testing/small requests)"""
    if not personalization_agent:
        raise HTTPException(status_code=503, detail="Personalization agent not available")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Execute personalization synchronously
        result = await personalization_agent.personalize_content(
            content=request.content,
            user_profile=request.user_profile,
            target_audience=request.target_audience,
            personalization_level=request.personalization_level,
            user_id=request.user_id
        )
        
        return PersonalizationResponse(
            request_id=request_id,
            status="completed",
            result=result,
            message="Personalization completed successfully"
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Personalization failed: {str(e)}")


@app.post("/profile/analyze", response_model=UserProfileAnalysisResponse)
async def analyze_user_profile(
    request: UserProfileAnalysisRequest,
    api_key: str = Depends(verify_api_key)
):
    """Analyze user data to create a personalization profile"""
    if not personalization_agent:
        raise HTTPException(status_code=503, detail="Personalization agent not available")
    
    request_id = str(uuid.uuid4())
    
    try:
        # Analyze user profile
        analysis = await personalization_agent.analyze_user_profile(
            user_data=request.user_data,
            analysis_depth=request.analysis_depth
        )
        
        return UserProfileAnalysisResponse(
            request_id=request_id,
            user_profile=analysis["profile"],
            recommendations=analysis["recommendations"],
            confidence_score=analysis["confidence_score"]
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Profile analysis failed: {str(e)}")


@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    registry = get_metrics_registry()
    from prometheus_client import generate_latest
    return generate_latest(registry)


# =========================================================================
# Background Tasks
# =========================================================================

async def execute_personalization_task(
    request_id: str,
    request: PersonalizationRequest,
    api_key: str
):
    """Execute personalization task in background"""
    try:
        # Execute personalization
        result = await personalization_agent.personalize_content(
            content=request.content,
            user_profile=request.user_profile,
            target_audience=request.target_audience,
            personalization_level=request.personalization_level,
            user_id=request.user_id
        )
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                event_type="personalization.completed",
                data={
                    "request_id": request_id,
                    "target_audience": request.target_audience,
                    "personalization_level": request.personalization_level,
                    "result": result.model_dump()
                }
            )
        
        print(f"✅ Personalization task {request_id} completed successfully")
    
    except Exception as e:
        # Publish error event
        if event_client:
            await event_client.publish_event(
                event_type="personalization.failed",
                data={
                    "request_id": request_id,
                    "target_audience": request.target_audience,
                    "error": str(e)
                }
            )
        
        print(f"❌ Personalization task {request_id} failed: {str(e)}")


# =========================================================================
# Main Entry Point
# =========================================================================

def run_server():
    """Run the Personalization Service server"""
    port = int(os.getenv("SERVICE_PORT", "8082"))
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=os.getenv("ENVIRONMENT", "production") == "development",
        log_level="info"
    )


if __name__ == "__main__":
    run_server()