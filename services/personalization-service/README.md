# Personalization Service

A microservice implementation of the Personalization Engine Agent, providing comprehensive content personalization and user profile analysis capabilities.

## Overview

The Personalization Service is part of the Publish AI platform's microservices architecture migration. It extracts the personalization functionality from the monolithic system and provides it as a standalone, scalable service for content adaptation and user experience optimization.

## Features

- **Content Personalization**: Multi-level personalization (light, moderate, heavy, custom)
- **User Profile Analysis**: Deep analysis of user data to create actionable profiles
- **AI-Powered**: Supports both OpenAI and Anthropic models with automatic fallback
- **Event-Driven**: Integrates with Event Bus for asynchronous communication
- **Service Discovery**: Automatic registration and health monitoring
- **Security**: API key authentication and mTLS support
- **Monitoring**: Prometheus metrics and health checks
- **Scalable**: Docker containerization with Kubernetes support

## API Endpoints

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### Personalization Operations
- `POST /personalize` - Start asynchronous content personalization
- `GET /personalize/{request_id}` - Get personalization status and results
- `POST /personalize/sync` - Synchronous personalization execution
- `POST /profile/analyze` - Analyze user data to create personalization profile

## Request/Response Models

### Personalization Request
```json
{
  "content": "Original content to personalize",
  "user_profile": {
    "interests": ["AI", "technology"],
    "communication_style": "casual",
    "expertise_level": "intermediate",
    "preferred_format": "article"
  },
  "target_audience": "tech enthusiasts",
  "personalization_level": "moderate",
  "user_id": "user123"
}
```

### Personalization Response
```json
{
  "request_id": "uuid-string",
  "status": "completed",
  "result": {
    "personalized_content": "Content adapted for the user...",
    "personalization_score": 0.87,
    "applied_strategies": [
      "communication_style_adaptation",
      "interest_based_examples",
      "vocabulary_adjustment"
    ],
    "target_audience": "tech enthusiasts",
    "personalization_level": "moderate",
    "user_preferences_matched": [
      "casual_communication",
      "technology_interests"
    ],
    "content_adjustments": {
      "tone": "adjusted to casual style",
      "examples": "added technology examples"
    },
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "message": "Personalization completed successfully"
}
```

### User Profile Analysis Request
```json
{
  "user_data": {
    "age": 28,
    "job_title": "Software Engineer",
    "browsing_history": ["AI articles", "tech blogs"],
    "social_media_activity": {
      "posts_about_tech": 45,
      "shares_technical_content": 23
    }
  },
  "analysis_depth": "comprehensive"
}
```

### User Profile Analysis Response
```json
{
  "request_id": "analysis-uuid",
  "user_profile": {
    "persona": "tech-savvy professional",
    "interests": ["AI", "machine learning"],
    "communication_style": "casual",
    "expertise_level": "intermediate"
  },
  "recommendations": [
    "Use technical examples",
    "Maintain casual tone",
    "Include visual elements"
  ],
  "confidence_score": 0.89
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVICE_HOST` | Service host address | `0.0.0.0` |
| `SERVICE_PORT` | Service port | `8082` |
| `PREFERRED_MODEL` | AI model preference | `openai` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `ANTHROPIC_API_KEY` | Anthropic API key | Optional |
| `EVENT_BUS_URL` | Event Bus service URL | `http://event-bus:8080` |
| `SERVICE_DISCOVERY_URL` | Service Discovery URL | `http://service-discovery:8070` |
| `API_KEY` | Service API key | Required |

### Personalization Levels

- **light**: Minor tone and vocabulary adjustments
- **moderate**: Significant content adaptation with examples and messaging changes
- **heavy**: Deep personalization with restructured content and targeted appeals
- **custom**: Fully customized content based on detailed user profile

### Analysis Depths

- **basic**: Surface-level profile analysis
- **standard**: Comprehensive profile with recommendations
- **comprehensive**: Deep behavioral analysis with advanced strategies

## Development

### Local Development

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export API_KEY="your-service-api-key"
   ```

3. **Run Service**
   ```bash
   cd src
   python main.py
   ```

### Testing

Run all tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=src --cov-report=html
```

Run specific test category:
```bash
pytest tests/test_main_simple.py
pytest tests/test_personalization_agent.py
```

### Docker Development

1. **Build Image**
   ```bash
   docker build -t personalization-service .
   ```

2. **Run Container**
   ```bash
   docker run -p 8082:8082 \
     -e OPENAI_API_KEY="your-key" \
     -e API_KEY="your-api-key" \
     personalization-service
   ```

3. **Docker Compose**
   ```bash
   docker-compose up --build
   ```

## Architecture

### Components

- **Personalization Agent**: Core AI-powered personalization functionality
- **Profile Analysis Agent**: User data analysis and profile creation
- **Event Client**: Communication with Event Bus service
- **Service Registry Client**: Registration with Service Discovery
- **Security Manager**: API key authentication and mTLS
- **Monitoring**: Prometheus metrics and health checks

### Dependencies

- **Infrastructure Services**: Event Bus, Service Discovery
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **Security**: mTLS certificates, API keys
- **Monitoring**: Prometheus metrics collection

### Data Flow

1. Client sends personalization request with authentication
2. Service validates request and API key
3. Personalization Agent processes request using AI model
4. Results published to Event Bus (async mode)
5. Metrics recorded for monitoring
6. Response returned to client

## Deployment

### Docker Compose
```yaml
services:
  personalization-service:
    build: .
    ports:
      - "8082:8082"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${PERSONALIZATION_SERVICE_API_KEY}
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
    networks:
      - publish-ai-network
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: personalization-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: personalization-service
  template:
    metadata:
      labels:
        app: personalization-service
        component: microservice
    spec:
      containers:
      - name: personalization-service
        image: personalization-service:latest
        ports:
        - containerPort: 8082
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
```

## Security

### Authentication
- API key authentication for all endpoints
- Service-to-service authentication via mTLS
- Security headers and CORS configuration

### mTLS Configuration
- Service certificates generated by internal CA
- Mutual authentication between services
- Encrypted inter-service communication

### Network Security
- Kubernetes NetworkPolicies for micro-segmentation
- Zero Trust networking principles
- Firewall rules for service isolation

## Monitoring

### Metrics
- HTTP request count and duration
- Personalization operation metrics
- Personalization effectiveness scores
- Profile analysis counts
- Agent health status
- Event publication counts

### Health Checks
- `/health` - Basic health status
- `/ready` - Readiness for traffic
- Service Discovery integration

### Logging
- Structured logging with context
- Request tracing and correlation
- Error tracking and alerting

## Testing

### Test Coverage
- Unit tests for all components
- Integration tests for API endpoints
- Mock testing for external dependencies
- Performance and load testing

### Test Categories
- **Unit Tests**: Component-level testing (15 tests)
- **Integration Tests**: API endpoint testing (7 tests)
- **Contract Tests**: Service interface testing
- **Performance Tests**: Load and stress testing

**Total Test Coverage**: 22/22 tests passing (100%)

## Migration Status

✅ **Phase 2.1: Personalization Service Migration - COMPLETED**

- [x] Agent extraction and containerization
- [x] Event-driven communication integration
- [x] Service Discovery registration
- [x] Security implementation (API keys + mTLS)
- [x] Comprehensive testing suite
- [x] Docker containerization
- [x] Monitoring and metrics
- [x] Documentation and deployment guides

**Phase 2.1 Status**: COMPLETED - Both Research and Personalization services migrated

## Troubleshooting

### Common Issues

1. **AI Model Not Available**
   - Check API keys are set correctly
   - Verify model endpoints are accessible
   - Check rate limits and quotas

2. **Service Registration Failed**
   - Verify Service Discovery is running
   - Check network connectivity
   - Validate API key configuration

3. **Event Bus Connection Failed**
   - Check Event Bus service status
   - Verify network policies allow connection
   - Check authentication credentials

### Debug Commands

```bash
# Check service health
curl http://localhost:8082/health

# Verify readiness
curl http://localhost:8082/ready

# Get metrics
curl http://localhost:8082/metrics

# Test personalization endpoint
curl -X POST http://localhost:8082/personalize/sync \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Test content",
    "user_profile": {"interests": ["technology"]}
  }'

# Test profile analysis
curl -X POST http://localhost:8082/profile/analyze \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "user_data": {"age": 25, "interests": ["tech"]},
    "analysis_depth": "comprehensive"
  }'
```