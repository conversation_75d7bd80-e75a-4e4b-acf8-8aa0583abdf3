[tool.poetry]
name = "business-intelligence-service"
version = "1.0.0"
description = "Business Intelligence Service for Publish AI - Advanced analytics, revenue optimization, and data-driven insights"
authors = ["Publish AI Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.4.0"
httpx = "^0.25.0"
asyncio = "^3.4.3"

# Database and Storage
sqlalchemy = "^2.0.0"
asyncpg = "^0.29.0"
alembic = "^1.12.0"
redis = "^5.0.0"

# Data Processing and Analytics
pandas = "^2.1.0"
numpy = "^1.24.0"
scipy = "^1.11.0"
scikit-learn = "^1.3.0"
statsmodels = "^0.14.0"
plotly = "^5.17.0"
dash = "^2.14.0"
dash-bootstrap-components = "^1.5.0"

# Time Series Analysis
prophet = "^1.1.4"
pmdarima = "^2.0.4"
seasonal-decompose = "^0.2.0"

# Data Visualization
matplotlib = "^3.8.0"
seaborn = "^0.13.0"
altair = "^5.1.0"
bokeh = "^3.3.0"

# Report Generation
reportlab = "^4.0.0"
weasyprint = "^60.0"
jinja2 = "^3.1.0"
xlsxwriter = "^3.1.0"

# Business Intelligence
apache-superset = "^3.0.0"
metabase-api = "^0.3.0"

# Monitoring and Logging
prometheus-client = "^0.19.0"
structlog = "^23.2.0"
opentelemetry-api = "^1.21.0"
opentelemetry-sdk = "^1.21.0"
opentelemetry-instrumentation-fastapi = "^0.42b0"
opentelemetry-instrumentation-sqlalchemy = "^0.42b0"
opentelemetry-exporter-jaeger = "^1.21.0"

# Configuration and Environment
pydantic-settings = "^2.0.0"
python-dotenv = "^1.0.0"
click = "^8.1.0"

# Security
cryptography = "^41.0.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}

# Background Tasks
celery = "^5.3.0"
flower = "^2.0.0"

# Utilities
aiofiles = "^23.2.0"
psutil = "^5.9.0"
python-dateutil = "^2.8.0"
pytz = "^2023.3"
cachetools = "^5.3.0"

# External APIs
stripe = "^7.0.0"
shopify-python-api = "^12.0.0"
google-analytics-data = "^0.17.0"
facebook-business = "^18.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^23.9.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"
jupyter = "^1.0.0"
ipykernel = "^6.25.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-branch",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "analytics: marks tests for analytics functionality",
    "reports: marks tests for report generation",
]
asyncio_mode = "auto"