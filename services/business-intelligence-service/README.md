# Business Intelligence Service

Advanced analytics, revenue optimization, and data-driven insights for Publish AI platform. This service provides comprehensive business intelligence capabilities including real-time analytics dashboards, revenue tracking and forecasting, user behavior analysis, and performance optimization recommendations.

## 🚀 Features

### Core Analytics Capabilities
- **Real-time Event Tracking** - Track user interactions and system events
- **User Behavior Analysis** - Comprehensive user journey and engagement metrics
- **Content Performance Analytics** - Track content views, engagement, and conversion
- **Conversion Funnel Analysis** - Identify bottlenecks and optimization opportunities
- **Cohort Analysis** - User retention and revenue cohort tracking
- **A/B Testing Support** - Statistical analysis of experiments

### Revenue Intelligence
- **Revenue Tracking** - Real-time revenue monitoring and analysis
- **Subscription Analytics** - MRR, ARR, churn, and lifetime value calculations
- **Payment Processing Analytics** - Transaction success rates and payment insights
- **Pricing Optimization** - Price elasticity analysis and recommendations
- **Churn Analysis** - Predict and prevent customer churn
- **Financial Forecasting** - Revenue predictions and business planning

### Advanced Reporting
- **Interactive Dashboards** - Real-time visualization and monitoring
- **Automated Reports** - Scheduled PDF and Excel report generation
- **Custom Analytics** - Flexible querying and custom metric creation
- **Executive Summaries** - High-level business performance insights
- **Anomaly Detection** - Automatic identification of unusual patterns
- **Alerting System** - Proactive notifications for key metrics

### Data Intelligence
- **Predictive Analytics** - Machine learning-powered insights
- **Statistical Analysis** - Advanced statistical modeling and testing
- **Market Intelligence** - Competitive analysis and market trends
- **Customer Segmentation** - AI-powered user classification
- **Performance Optimization** - Data-driven recommendations
- **Business Intelligence** - Strategic insights and decision support

## 🏗️ Architecture

### Service Components

```
┌─────────────────────────────────────────────────────────────────┐
│                Business Intelligence Service                    │
├─────────────────────────────────────────────────────────────────┤
│  API Layer                                                      │
│  ├── Analytics Endpoints   ├── Revenue Endpoints               │
│  ├── Dashboard Endpoints   ├── Reporting Endpoints             │
│  └── Health & Status       └── Export & Integration            │
├─────────────────────────────────────────────────────────────────┤
│  Core Analytics Engine                                          │
│  ├── Analytics Service    ├── Revenue Service                  │
│  ├── Forecasting Service  ├── Reporting Service                │
│  └── Dashboard Service    └── Data Processing Engine           │
├─────────────────────────────────────────────────────────────────┤
│  Data Processing Layer                                          │
│  ├── Event Processing     ├── Metrics Aggregation              │
│  ├── Statistical Analysis ├── Predictive Modeling              │
│  └── Report Generation    └── Cache Management                 │
├─────────────────────────────────────────────────────────────────┤
│  Storage & Integration                                          │
│  ├── Time Series Database ├── Data Warehouse                   │
│  ├── Real-time Streaming  ├── External APIs                    │
│  └── File Storage         └── Notification Systems             │
└─────────────────────────────────────────────────────────────────┘
```

### Data Flow Architecture

```
┌──────────────┐    ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
│  Data        │    │ Real-time    │    │ Analytics    │    │ Business     │
│  Collection  │───▶│ Processing   │───▶│ Engine       │───▶│ Intelligence │
│              │    │              │    │              │    │ Dashboard    │
└──────────────┘    └──────────────┘    └──────────────┘    └──────────────┘
       │                     │                   │                   │
       ▼                     ▼                   ▼                   ▼
┌──────────────┐    ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
│ Event        │    │ Stream       │    │ Statistical  │    │ Reports &    │
│ Ingestion    │    │ Processing   │    │ Analysis     │    │ Alerts       │
└──────────────┘    └──────────────┘    └──────────────┘    └──────────────┘
```

## 🛠️ Technology Stack

### Core Framework
- **FastAPI** - High-performance API framework
- **Uvicorn** - ASGI server for production
- **Pydantic** - Data validation and serialization
- **Asyncio** - Asynchronous programming support

### Analytics & Data Science
- **Pandas** - Data manipulation and analysis
- **NumPy** - Numerical computing
- **SciPy** - Scientific computing and statistics
- **Scikit-learn** - Machine learning algorithms
- **Statsmodels** - Statistical modeling
- **Prophet** - Time series forecasting

### Visualization & Dashboards
- **Plotly** - Interactive visualizations
- **Dash** - Web-based dashboards
- **Matplotlib** - Static plotting
- **Seaborn** - Statistical data visualization
- **Altair** - Grammar of graphics visualization
- **Bokeh** - Interactive web plots

### Data Storage & Processing
- **PostgreSQL** - Primary database with Supabase
- **Redis** - Caching and real-time data
- **Time Series Database** - High-frequency metrics storage
- **Data Warehouse** - Historical data analysis
- **Apache Kafka** - Real-time data streaming

### Business Intelligence Tools
- **Apache Superset** - Modern data exploration platform
- **Metabase** - Business intelligence dashboard
- **Custom Dashboards** - Tailored visualization interfaces

### Report Generation
- **ReportLab** - PDF report generation
- **WeasyPrint** - HTML to PDF conversion
- **Jinja2** - Template engine for reports
- **XlsxWriter** - Excel file generation

## 📊 API Endpoints

### Analytics Endpoints
```bash
# Event Tracking
POST /api/analytics/events/track          # Track single event
POST /api/analytics/events/batch          # Track multiple events
GET  /api/analytics/events/export         # Export event data

# User Analytics
GET  /api/analytics/users/{user_id}/metrics    # Individual user metrics
GET  /api/analytics/users/metrics              # Aggregate user metrics
GET  /api/analytics/insights/users             # User behavior insights

# Content Analytics
GET  /api/analytics/content/{content_id}/metrics    # Content performance
GET  /api/analytics/content/metrics                 # All content metrics

# Advanced Analytics
POST /api/analytics/funnel/analyze        # Conversion funnel analysis
GET  /api/analytics/cohort/analyze        # Cohort retention analysis
GET  /api/analytics/summary               # Analytics summary
GET  /api/analytics/metrics/realtime      # Real-time metrics
```

### Revenue Endpoints
```bash
# Revenue Tracking
POST /api/revenue/payments/track          # Track payment events
GET  /api/revenue/metrics                 # Revenue metrics
GET  /api/revenue/summary                 # Revenue summary

# Subscription Analytics
GET  /api/revenue/subscriptions/metrics   # Subscription analytics
GET  /api/revenue/subscriptions/churn     # Churn analysis
GET  /api/revenue/subscriptions/forecast  # Revenue forecasting

# Pricing Analytics
GET  /api/revenue/pricing/analysis        # Pricing optimization
GET  /api/revenue/pricing/elasticity      # Price elasticity analysis
GET  /api/revenue/pricing/recommendations # Pricing recommendations

# Financial Reports
GET  /api/revenue/reports/monthly         # Monthly financial reports
GET  /api/revenue/reports/quarterly       # Quarterly business review
GET  /api/revenue/forecasts               # Revenue forecasts
```

### Dashboard Endpoints
```bash
# Interactive Dashboards
GET  /api/dashboards/executive            # Executive dashboard
GET  /api/dashboards/analytics            # Analytics dashboard
GET  /api/dashboards/revenue              # Revenue dashboard
GET  /api/dashboards/users                # User behavior dashboard

# Custom Dashboards
POST /api/dashboards/custom               # Create custom dashboard
GET  /api/dashboards/custom/{id}          # Get custom dashboard
PUT  /api/dashboards/custom/{id}          # Update custom dashboard
DELETE /api/dashboards/custom/{id}        # Delete custom dashboard

# Dashboard Sharing
POST /api/dashboards/{id}/share           # Share dashboard
GET  /api/dashboards/shared/{token}       # Access shared dashboard
```

### Reporting Endpoints
```bash
# Report Generation
POST /api/reports/generate                # Generate custom report
GET  /api/reports/{report_id}             # Get report status/download
GET  /api/reports/templates               # Available report templates

# Scheduled Reports
POST /api/reports/schedules               # Create report schedule
GET  /api/reports/schedules               # List scheduled reports
PUT  /api/reports/schedules/{id}          # Update schedule
DELETE /api/reports/schedules/{id}        # Delete schedule

# Report Management
GET  /api/reports/history                 # Report generation history
POST /api/reports/email                  # Email report
GET  /api/reports/exports                 # Export reports
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL database (via Supabase)
- Redis server
- Docker and Docker Compose

### Environment Setup

1. **Clone and navigate to service:**
```bash
cd publish-ai/services/business-intelligence-service
```

2. **Create environment file:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Install dependencies:**
```bash
poetry install
```

### Configuration

Key environment variables:

```bash
# Service Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development
LOG_LEVEL=INFO

# Database
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
DATABASE_URL=postgresql://user:pass@host:port/db

# Redis
REDIS_URL=redis://localhost:6379

# Analytics Configuration
ANALYTICS_ENABLED=true
REVENUE_TRACKING_ENABLED=true
FORECASTING_ENABLED=true
REAL_TIME_PROCESSING=true

# External Integrations
STRIPE_API_KEY=your-stripe-key
GOOGLE_ANALYTICS_KEY=your-ga-key
MIXPANEL_TOKEN=your-mixpanel-token

# Reporting
REPORT_STORAGE_PATH=/app/reports
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=your-email
EMAIL_PASSWORD=your-password

# Dashboard Configuration
SUPERSET_URL=http://superset:8088
METABASE_URL=http://metabase:3000
CUSTOM_DASHBOARD_ENABLED=true
```

### Docker Deployment

1. **Build the image:**
```bash
docker build -t business-intelligence-service .
```

2. **Run with Docker Compose:**
```bash
docker-compose up -d
```

3. **Check service health:**
```bash
curl http://localhost:8000/health
```

### Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check pod status
kubectl get pods -l app=business-intelligence-service

# View logs
kubectl logs -f deployment/business-intelligence-service
```

## 📋 Usage Examples

### Track Analytics Event

```python
import httpx

# Track user interaction
event_data = {
    "event_name": "book_generated",
    "user_id": "user_123",
    "session_id": "session_456",
    "properties": {
        "book_title": "AI and the Future",
        "genre": "technology",
        "generation_time": 45.2,
        "word_count": 25000
    },
    "timestamp": "2024-01-15T10:30:00Z"
}

async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://localhost:8000/api/analytics/events/track",
        json=event_data
    )
    result = response.json()
    print(f"Event tracked: {result['event_id']}")
```

### Get User Analytics

```python
# Get user metrics for last 30 days
async with httpx.AsyncClient() as client:
    response = await client.get(
        "http://localhost:8000/api/analytics/users/user_123/metrics",
        params={
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        }
    )
    metrics = response.json()
    print(f"User generated {metrics['books_generated']} books")
    print(f"Conversion rate: {metrics['conversion_rate']:.2%}")
```

### Analyze Conversion Funnel

```python
# Analyze book creation funnel
funnel_steps = [
    "page_view",
    "signup",
    "book_started",
    "book_generated",
    "book_published"
]

async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://localhost:8000/api/analytics/funnel/analyze",
        json=funnel_steps,
        params={
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        }
    )
    funnel = response.json()
    print(f"Overall conversion rate: {funnel['overall_conversion_rate']:.2%}")
```

### Track Revenue Event

```python
# Track subscription payment
payment_data = {
    "user_id": "user_123",
    "subscription_id": "sub_456",
    "amount": 29.99,
    "currency": "USD",
    "payment_method": "card",
    "processor": "stripe",
    "transaction_id": "txn_789",
    "status": "completed"
}

async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://localhost:8000/api/revenue/payments/track",
        json=payment_data
    )
    result = response.json()
    print(f"Payment tracked: {result['payment_id']}")
```

### Generate Revenue Forecast

```python
# Generate 12-month revenue forecast
async with httpx.AsyncClient() as client:
    response = await client.get(
        "http://localhost:8000/api/revenue/forecasts",
        params={
            "forecast_periods": 12,
            "forecast_type": "monthly"
        }
    )
    forecast = response.json()
    print(f"Next month predicted revenue: ${forecast['forecasts']['ensemble'][0]:.2f}")
```

## 📈 Dashboard Configuration

### Executive Dashboard

The executive dashboard provides high-level business metrics:

```python
# Configure executive dashboard
dashboard_config = {
    "name": "Executive Dashboard",
    "refresh_interval": 300,  # 5 minutes
    "widgets": [
        {
            "type": "kpi_card",
            "title": "Monthly Recurring Revenue",
            "metric": "mrr",
            "format": "currency"
        },
        {
            "type": "chart",
            "title": "Revenue Trend",
            "chart_type": "line",
            "data_source": "revenue_monthly",
            "time_range": "12_months"
        },
        {
            "type": "funnel",
            "title": "Conversion Funnel",
            "steps": ["signup", "book_created", "subscription"]
        }
    ]
}
```

### Analytics Dashboard

Detailed analytics for product and marketing teams:

```python
# Analytics dashboard configuration
analytics_dashboard = {
    "name": "Product Analytics",
    "widgets": [
        {
            "type": "cohort_chart",
            "title": "User Retention",
            "cohort_type": "weekly",
            "periods": 12
        },
        {
            "type": "segmentation",
            "title": "User Segments",
            "segment_by": "behavior",
            "metrics": ["engagement", "ltv"]
        },
        {
            "type": "feature_usage",
            "title": "Feature Adoption",
            "features": ["ai_writing", "templates", "collaboration"]
        }
    ]
}
```

## 🔧 Advanced Configuration

### Custom Metrics

Define custom business metrics:

```python
# Custom metric definitions
custom_metrics = {
    "content_quality_score": {
        "name": "Content Quality Score",
        "calculation": "avg(rating * engagement_time / 100)",
        "filters": ["content_type", "date_range"],
        "aggregation": "daily"
    },
    "user_engagement_index": {
        "name": "User Engagement Index",
        "calculation": "session_duration * page_views * return_rate",
        "normalization": "z_score",
        "segments": ["user_type", "acquisition_channel"]
    }
}
```

### Alerting Rules

Configure automated alerts:

```python
# Alert configuration
alert_rules = {
    "revenue_drop": {
        "name": "Daily Revenue Drop",
        "condition": "daily_revenue < previous_day_revenue * 0.8",
        "notification": ["email", "slack"],
        "priority": "high"
    },
    "churn_spike": {
        "name": "Churn Rate Spike",
        "condition": "weekly_churn_rate > historical_avg * 1.5",
        "notification": ["email"],
        "priority": "medium"
    }
}
```

### Data Export Configuration

Set up automated data exports:

```python
# Export schedules
export_schedules = {
    "daily_metrics": {
        "frequency": "daily",
        "time": "09:00",
        "format": "csv",
        "destination": "s3://bucket/daily-metrics/",
        "metrics": ["dau", "revenue", "conversions"]
    },
    "weekly_cohorts": {
        "frequency": "weekly",
        "day": "monday",
        "format": "excel",
        "destination": "email:<EMAIL>",
        "data": "cohort_analysis"
    }
}
```

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests
poetry run pytest tests/unit/

# With coverage
poetry run pytest tests/unit/ --cov=src --cov-report=html
```

### Integration Tests
```bash
# Run integration tests
poetry run pytest tests/integration/

# Analytics-specific tests
poetry run pytest tests/integration/ -m analytics
```

### Load Testing
```bash
# Test event ingestion performance
poetry run pytest tests/load/test_event_ingestion.py

# Test dashboard response times
poetry run pytest tests/load/test_dashboard_performance.py
```

## 🔒 Security

### Data Privacy
- **PII Protection** - Automatic detection and masking of personal data
- **GDPR Compliance** - Right to deletion and data portability
- **Data Retention** - Configurable retention policies
- **Access Controls** - Role-based access to sensitive metrics

### Security Features
- **API Authentication** - JWT token-based authentication
- **Rate Limiting** - Protection against abuse
- **Data Encryption** - Encryption at rest and in transit
- **Audit Logging** - Comprehensive access and modification logs

## 🚀 Production Deployment

### Scaling Configuration

**Horizontal Scaling:**
```yaml
# Kubernetes HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: business-intelligence-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: business-intelligence-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

**Database Scaling:**
- Read replicas for analytics queries
- Time-series database for high-frequency metrics
- Data partitioning by date ranges
- Automated data archival

### Performance Optimization

1. **Query Optimization:**
   - Materialized views for common aggregations
   - Indexed columns for frequent filters
   - Query result caching
   - Database connection pooling

2. **Real-time Processing:**
   - Event streaming with Apache Kafka
   - In-memory processing with Redis
   - Batch processing for historical data
   - Incremental data updates

3. **Caching Strategy:**
   - Application-level caching
   - CDN for static dashboard assets
   - Database query result caching
   - Pre-computed metric aggregations

## 📚 Documentation

- [Analytics API Reference](docs/analytics-api.md) - Complete API documentation
- [Dashboard Guide](docs/dashboards.md) - Dashboard creation and customization
- [Revenue Analytics](docs/revenue.md) - Revenue tracking and optimization
- [Forecasting Models](docs/forecasting.md) - Predictive analytics documentation
- [Deployment Guide](docs/deployment.md) - Production deployment instructions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement analytics features with tests
4. Update documentation
5. Submit a pull request

### Development Guidelines

- Follow data science best practices
- Include statistical significance tests
- Validate data quality and integrity
- Document metric definitions clearly
- Test with realistic data volumes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Business Intelligence Service** - Transforming data into actionable business insights 📊