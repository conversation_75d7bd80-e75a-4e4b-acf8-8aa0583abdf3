"""
Business Intelligence Service - Advanced Analytics and Revenue Optimization

This service provides comprehensive business intelligence capabilities including:
- Real-time analytics dashboards
- Revenue tracking and forecasting
- User behavior analysis
- Market trend insights
- Performance optimization recommendations
- Automated reporting and alerts
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from prometheus_client import make_asgi_app
import structlog

from core.config import get_settings
from core.logging_config import setup_logging
from core.monitoring import setup_monitoring, AnalyticsMetrics
from core.exceptions import AnalyticsException, handle_analytics_exception
from api.routes import analytics, revenue, reports, dashboards, health
from services.analytics_service import AnalyticsService
from services.revenue_service import RevenueService
from services.forecasting_service import ForecastingService
from services.reporting_service import ReportingService
from services.dashboard_service import DashboardService
from database.database import get_database_client
from database.supabase_client import get_supabase_client


# Global service instances
analytics_service: AnalyticsService = None
revenue_service: RevenueService = None
forecasting_service: ForecastingService = None
reporting_service: ReportingService = None
dashboard_service: DashboardService = None
analytics_metrics: AnalyticsMetrics = None

# Shutdown event
shutdown_event = asyncio.Event()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global analytics_service, revenue_service, forecasting_service
    global reporting_service, dashboard_service, analytics_metrics
    
    settings = get_settings()
    logger = structlog.get_logger()
    
    try:
        # Setup monitoring
        analytics_metrics = setup_monitoring()
        logger.info("Monitoring system initialized")
        
        # Initialize database connections
        database_client = get_database_client()
        supabase_client = get_supabase_client()
        logger.info("Database connections established")
        
        # Initialize services
        analytics_service = AnalyticsService(
            database_client=database_client,
            supabase_client=supabase_client,
            metrics=analytics_metrics
        )
        
        revenue_service = RevenueService(
            database_client=database_client,
            supabase_client=supabase_client,
            metrics=analytics_metrics
        )
        
        forecasting_service = ForecastingService(
            database_client=database_client,
            analytics_service=analytics_service,
            revenue_service=revenue_service,
            metrics=analytics_metrics
        )
        
        reporting_service = ReportingService(
            database_client=database_client,
            analytics_service=analytics_service,
            revenue_service=revenue_service,
            forecasting_service=forecasting_service,
            metrics=analytics_metrics
        )
        
        dashboard_service = DashboardService(
            analytics_service=analytics_service,
            revenue_service=revenue_service,
            forecasting_service=forecasting_service,
            metrics=analytics_metrics
        )
        
        # Start background services
        await analytics_service.start()
        await revenue_service.start()
        await forecasting_service.start()
        await reporting_service.start()
        await dashboard_service.start()
        
        logger.info("Business Intelligence Service started successfully")
        
        # Register signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown")
            shutdown_event.set()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Business Intelligence Service: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Business Intelligence Service")
        
        if analytics_service:
            await analytics_service.stop()
        if revenue_service:
            await revenue_service.stop()
        if forecasting_service:
            await forecasting_service.stop()
        if reporting_service:
            await reporting_service.stop()
        if dashboard_service:
            await dashboard_service.stop()
        
        logger.info("Business Intelligence Service shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    # Setup logging
    setup_logging(settings.log_level)
    logger = structlog.get_logger()
    
    # Create FastAPI app
    app = FastAPI(
        title="Business Intelligence Service",
        description="Advanced analytics, revenue optimization, and data-driven insights for Publish AI",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Add exception handlers
    app.add_exception_handler(AnalyticsException, handle_analytics_exception)
    app.add_exception_handler(Exception, handle_general_exception)
    
    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["Health"])
    app.include_router(analytics.router, prefix="/api/analytics", tags=["Analytics"])
    app.include_router(revenue.router, prefix="/api/revenue", tags=["Revenue"])
    app.include_router(reports.router, prefix="/api/reports", tags=["Reports"])
    app.include_router(dashboards.router, prefix="/api/dashboards", tags=["Dashboards"])
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # Root endpoint
    @app.get("/", response_class=JSONResponse)
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "Business Intelligence Service",
            "version": "1.0.0",
            "status": "operational",
            "description": "Advanced analytics, revenue optimization, and data-driven insights",
            "features": [
                "Real-time analytics dashboards",
                "Revenue tracking and forecasting",
                "User behavior analysis",
                "Market trend insights",
                "Performance optimization",
                "Automated reporting",
                "Interactive visualizations",
                "Predictive analytics"
            ],
            "endpoints": {
                "health": "/health",
                "analytics": "/api/analytics",
                "revenue": "/api/revenue",
                "reports": "/api/reports",
                "dashboards": "/api/dashboards",
                "metrics": "/metrics",
                "docs": "/docs" if settings.environment != "production" else None
            }
        }
    
    # Global service access endpoints
    @app.get("/api/services/status")
    async def get_services_status():
        """Get status of all internal services."""
        status = {}
        
        if analytics_service:
            status["analytics"] = await analytics_service.get_status()
        if revenue_service:
            status["revenue"] = await revenue_service.get_status()
        if forecasting_service:
            status["forecasting"] = await forecasting_service.get_status()
        if reporting_service:
            status["reporting"] = await reporting_service.get_status()
        if dashboard_service:
            status["dashboard"] = await dashboard_service.get_status()
        
        return status
    
    # Business metrics endpoint
    @app.get("/api/business/overview")
    async def get_business_overview():
        """Get comprehensive business overview."""
        try:
            overview = {
                "revenue": await revenue_service.get_revenue_summary(),
                "analytics": await analytics_service.get_analytics_summary(),
                "forecasting": await forecasting_service.get_forecast_summary(),
                "key_metrics": await analytics_service.get_key_metrics(),
                "growth_trends": await revenue_service.get_growth_trends(),
                "user_insights": await analytics_service.get_user_insights()
            }
            
            return overview
            
        except Exception as e:
            logger.error(f"Failed to get business overview: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate business overview")
    
    return app


async def handle_general_exception(request, exc):
    """Handle general exceptions."""
    logger = structlog.get_logger()
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "type": "InternalServerError"
        }
    )


def get_analytics_service() -> AnalyticsService:
    """Get the analytics service instance."""
    if analytics_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Analytics service not available"
        )
    return analytics_service


def get_revenue_service() -> RevenueService:
    """Get the revenue service instance."""
    if revenue_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Revenue service not available"
        )
    return revenue_service


def get_forecasting_service() -> ForecastingService:
    """Get the forecasting service instance."""
    if forecasting_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Forecasting service not available"
        )
    return forecasting_service


def get_reporting_service() -> ReportingService:
    """Get the reporting service instance."""
    if reporting_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Reporting service not available"
        )
    return reporting_service


def get_dashboard_service() -> DashboardService:
    """Get the dashboard service instance."""
    if dashboard_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Dashboard service not available"
        )
    return dashboard_service


def get_analytics_metrics() -> AnalyticsMetrics:
    """Get the analytics metrics instance."""
    if analytics_metrics is None:
        raise HTTPException(
            status_code=503, 
            detail="Analytics metrics not available"
        )
    return analytics_metrics


# Create the app instance
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    # Configure uvicorn
    uvicorn_config = {
        "host": settings.host,
        "port": settings.port,
        "log_level": settings.log_level.lower(),
        "access_log": True,
        "reload": settings.environment == "development",
        "workers": 1 if settings.environment == "development" else 4,
    }
    
    # Add SSL for production
    if settings.environment == "production" and settings.ssl_cert_path:
        uvicorn_config.update({
            "ssl_keyfile": settings.ssl_key_path,
            "ssl_certfile": settings.ssl_cert_path,
        })
    
    logger = structlog.get_logger()
    logger.info(
        "Starting Business Intelligence Service",
        host=settings.host,
        port=settings.port,
        environment=settings.environment,
        analytics_enabled=settings.analytics_enabled,
        forecasting_enabled=settings.forecasting_enabled,
    )
    
    uvicorn.run("main:app", **uvicorn_config)