"""
Analytics API routes for Business Intelligence Service.

Provides endpoints for:
- Event tracking
- User analytics
- Content performance
- Conversion funnel analysis
- Cohort analysis
- Real-time metrics
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
import structlog

from models.analytics_schemas import (
    AnalyticsEvent, UserMetrics, ContentMetrics,
    ConversionFunnel, CohortAnalysis, AnalyticsSummary,
    EventTrackingResponse
)
from models.common_schemas import DateRange
from services.analytics_service import AnalyticsService
from core.exceptions import AnalyticsException
from main import get_analytics_service
from utils.date_utils import parse_date_range


router = APIRouter()
logger = structlog.get_logger(__name__)


@router.post("/events/track", response_model=EventTrackingResponse)
async def track_event(
    event: AnalyticsEvent,
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Track an analytics event.
    
    Args:
        event: Analytics event data
        analytics_service: Analytics service instance
    
    Returns:
        Event tracking confirmation
    """
    try:
        logger.info(
            "Tracking analytics event",
            event_name=event.event_name,
            user_id=event.user_id,
            session_id=event.session_id
        )
        
        result = await analytics_service.track_event(event)
        
        return EventTrackingResponse(
            event_id=result["event_id"],
            status=result["status"],
            timestamp=datetime.utcnow(),
            message="Event tracked successfully"
        )
        
    except AnalyticsException as e:
        logger.error(f"Analytics error in event tracking: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in event tracking: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/users/{user_id}/metrics", response_model=UserMetrics)
async def get_user_metrics(
    user_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for analysis"),
    end_date: Optional[datetime] = Query(None, description="End date for analysis"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get comprehensive user metrics for a specific user.
    
    Args:
        user_id: User identifier
        start_date: Start date for analysis period
        end_date: End date for analysis period
        analytics_service: Analytics service instance
    
    Returns:
        Detailed user metrics
    """
    try:
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        metrics = await analytics_service.get_user_metrics(
            user_id=user_id,
            date_range=date_range
        )
        
        logger.info(
            "Retrieved user metrics",
            user_id=user_id,
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            total_events=metrics.total_events
        )
        
        return metrics
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting user metrics: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting user metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/users/metrics", response_model=UserMetrics)
async def get_aggregate_user_metrics(
    start_date: Optional[datetime] = Query(None, description="Start date for analysis"),
    end_date: Optional[datetime] = Query(None, description="End date for analysis"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get aggregate user metrics across all users.
    
    Args:
        start_date: Start date for analysis period
        end_date: End date for analysis period
        analytics_service: Analytics service instance
    
    Returns:
        Aggregate user metrics
    """
    try:
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        # Get aggregate metrics (no specific user_id)
        metrics = await analytics_service.get_user_metrics(
            user_id=None,
            date_range=date_range
        )
        
        logger.info(
            "Retrieved aggregate user metrics",
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            total_events=metrics.total_events
        )
        
        return metrics
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting aggregate user metrics: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting aggregate user metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/content/{content_id}/metrics", response_model=ContentMetrics)
async def get_content_metrics(
    content_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for analysis"),
    end_date: Optional[datetime] = Query(None, description="End date for analysis"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get performance metrics for specific content.
    
    Args:
        content_id: Content identifier
        start_date: Start date for analysis period
        end_date: End date for analysis period
        analytics_service: Analytics service instance
    
    Returns:
        Content performance metrics
    """
    try:
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        metrics = await analytics_service.get_content_metrics(
            content_id=content_id,
            date_range=date_range
        )
        
        logger.info(
            "Retrieved content metrics",
            content_id=content_id,
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            total_views=metrics.total_views
        )
        
        return metrics
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting content metrics: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting content metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/content/metrics", response_model=List[ContentMetrics])
async def get_all_content_metrics(
    start_date: Optional[datetime] = Query(None, description="Start date for analysis"),
    end_date: Optional[datetime] = Query(None, description="End date for analysis"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of content items"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get performance metrics for all content.
    
    Args:
        start_date: Start date for analysis period
        end_date: End date for analysis period
        limit: Maximum number of content items to return
        analytics_service: Analytics service instance
    
    Returns:
        List of content performance metrics
    """
    try:
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        metrics_list = await analytics_service.get_content_metrics(
            content_id=None,
            date_range=date_range
        )
        
        # Apply limit
        if isinstance(metrics_list, list):
            metrics_list = metrics_list[:limit]
        else:
            metrics_list = [metrics_list]
        
        logger.info(
            "Retrieved all content metrics",
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            content_count=len(metrics_list)
        )
        
        return metrics_list
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting all content metrics: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting all content metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/funnel/analyze", response_model=ConversionFunnel)
async def analyze_conversion_funnel(
    funnel_steps: List[str],
    start_date: Optional[datetime] = Query(None, description="Start date for analysis"),
    end_date: Optional[datetime] = Query(None, description="End date for analysis"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Analyze conversion funnel with specified steps.
    
    Args:
        funnel_steps: List of event names representing funnel steps
        start_date: Start date for analysis period
        end_date: End date for analysis period
        analytics_service: Analytics service instance
    
    Returns:
        Conversion funnel analysis
    """
    try:
        if len(funnel_steps) < 2:
            raise HTTPException(
                status_code=400,
                detail="Funnel must have at least 2 steps"
            )
        
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        funnel_analysis = await analytics_service.analyze_conversion_funnel(
            funnel_steps=funnel_steps,
            date_range=date_range
        )
        
        logger.info(
            "Analyzed conversion funnel",
            funnel_steps=funnel_steps,
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            overall_conversion_rate=funnel_analysis.overall_conversion_rate
        )
        
        return funnel_analysis
        
    except AnalyticsException as e:
        logger.error(f"Analytics error analyzing funnel: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error analyzing funnel: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/cohort/analyze", response_model=CohortAnalysis)
async def perform_cohort_analysis(
    cohort_type: str = Query("weekly", description="Cohort type: daily, weekly, monthly"),
    metric: str = Query("retention", description="Metric to analyze: retention, revenue"),
    periods: int = Query(12, ge=1, le=24, description="Number of periods to analyze"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Perform cohort analysis for user retention or revenue.
    
    Args:
        cohort_type: Type of cohort grouping (daily, weekly, monthly)
        metric: Metric to analyze (retention, revenue)
        periods: Number of periods to analyze
        analytics_service: Analytics service instance
    
    Returns:
        Cohort analysis results
    """
    try:
        if cohort_type not in ["daily", "weekly", "monthly"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid cohort type. Must be daily, weekly, or monthly"
            )
        
        if metric not in ["retention", "revenue"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid metric. Must be retention or revenue"
            )
        
        cohort_analysis = await analytics_service.perform_cohort_analysis(
            cohort_type=cohort_type,
            metric=metric,
            periods=periods
        )
        
        logger.info(
            "Performed cohort analysis",
            cohort_type=cohort_type,
            metric=metric,
            periods=periods,
            cohort_count=len(cohort_analysis.cohort_table)
        )
        
        return cohort_analysis
        
    except AnalyticsException as e:
        logger.error(f"Analytics error in cohort analysis: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in cohort analysis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/summary", response_model=AnalyticsSummary)
async def get_analytics_summary(
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get comprehensive analytics summary.
    
    Args:
        analytics_service: Analytics service instance
    
    Returns:
        Analytics summary with key metrics and trends
    """
    try:
        summary = await analytics_service.get_analytics_summary()
        
        logger.info(
            "Retrieved analytics summary",
            total_users=summary.total_users,
            active_users=summary.active_users,
            conversion_rate=summary.conversion_rate
        )
        
        return summary
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting summary: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/metrics/realtime")
async def get_realtime_metrics(
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get real-time analytics metrics.
    
    Args:
        analytics_service: Analytics service instance
    
    Returns:
        Real-time metrics and KPIs
    """
    try:
        metrics = await analytics_service.get_key_metrics()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "metrics": metrics,
            "status": "success"
        }
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting realtime metrics: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting realtime metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/insights/users")
async def get_user_insights(
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get user behavior insights and segmentation.
    
    Args:
        analytics_service: Analytics service instance
    
    Returns:
        User insights and behavioral patterns
    """
    try:
        insights = await analytics_service.get_user_insights()
        
        logger.info(
            "Retrieved user insights",
            segment_count=len(insights.get("user_segments", [])),
            device_count=len(insights.get("device_breakdown", []))
        )
        
        return insights
        
    except AnalyticsException as e:
        logger.error(f"Analytics error getting user insights: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting user insights: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/events/export")
async def export_events(
    start_date: Optional[datetime] = Query(None, description="Start date for export"),
    end_date: Optional[datetime] = Query(None, description="End date for export"),
    event_names: Optional[List[str]] = Query(None, description="Specific events to export"),
    format: str = Query("json", description="Export format: json, csv"),
    limit: int = Query(10000, ge=1, le=50000, description="Maximum events to export"),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Export analytics events for external analysis.
    
    Args:
        start_date: Start date for export period
        end_date: End date for export period
        event_names: Specific event names to filter
        format: Export format (json or csv)
        limit: Maximum number of events to export
        analytics_service: Analytics service instance
    
    Returns:
        Exported events data
    """
    try:
        # Parse date range
        date_range = parse_date_range(start_date, end_date)
        
        # For now, return a placeholder response
        # In a real implementation, you would query the events and format them
        export_data = {
            "export_info": {
                "date_range": {
                    "start": date_range.start_date.isoformat(),
                    "end": date_range.end_date.isoformat()
                },
                "event_names": event_names,
                "format": format,
                "limit": limit,
                "exported_at": datetime.utcnow().isoformat()
            },
            "events": []  # Would contain actual event data
        }
        
        logger.info(
            "Exported analytics events",
            date_range=f"{date_range.start_date} to {date_range.end_date}",
            format=format,
            limit=limit
        )
        
        return export_data
        
    except AnalyticsException as e:
        logger.error(f"Analytics error exporting events: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error exporting events: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/events/batch")
async def track_events_batch(
    events: List[AnalyticsEvent],
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Track multiple analytics events in batch.
    
    Args:
        events: List of analytics events
        analytics_service: Analytics service instance
    
    Returns:
        Batch tracking results
    """
    try:
        if len(events) > 100:
            raise HTTPException(
                status_code=400,
                detail="Batch size cannot exceed 100 events"
            )
        
        results = []
        successful = 0
        failed = 0
        
        for event in events:
            try:
                result = await analytics_service.track_event(event)
                results.append({
                    "event_id": result["event_id"],
                    "status": "success"
                })
                successful += 1
            except Exception as e:
                results.append({
                    "event_name": event.event_name,
                    "status": "failed",
                    "error": str(e)
                })
                failed += 1
        
        logger.info(
            "Batch event tracking completed",
            total_events=len(events),
            successful=successful,
            failed=failed
        )
        
        return {
            "total_events": len(events),
            "successful": successful,
            "failed": failed,
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in batch tracking: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")