"""
Analytics Service - Core analytics engine for business intelligence.

Provides comprehensive analytics capabilities including:
- User behavior analysis
- Content performance metrics
- Platform usage statistics
- Conversion funnel analysis
- Cohort analysis
- Real-time event tracking
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
import uuid

import pandas as pd
import numpy as np
from scipy import stats
import structlog

from core.config import get_settings
from core.monitoring import AnalyticsMetrics
from core.exceptions import AnalyticsException
from models.analytics_schemas import (
    AnalyticsEvent, UserMetrics, ContentMetrics, 
    ConversionFunnel, CohortAnalysis, AnalyticsSummary
)
from utils.date_utils import DateRange, get_date_ranges
from utils.statistical_utils import calculate_significance, calculate_confidence_interval


class AnalyticsService:
    """
    Core analytics service that processes and analyzes user behavior,
    content performance, and business metrics.
    """
    
    def __init__(self, database_client, supabase_client, metrics: AnalyticsMetrics):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = metrics
        
        # Analytics state
        self.event_buffer = []
        self.processing_queue = asyncio.Queue()
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
        # Cache for frequently accessed data
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def start(self):
        """Start the analytics service and background tasks."""
        self.logger.info("Starting Analytics Service")
        
        try:
            # Initialize analytics tables if needed
            await self._initialize_analytics_tables()
            
            # Start background tasks
            if self.settings.analytics_enabled:
                self.background_tasks.append(
                    asyncio.create_task(self._event_processor())
                )
                
                self.background_tasks.append(
                    asyncio.create_task(self._metrics_aggregator())
                )
                
                self.background_tasks.append(
                    asyncio.create_task(self._cache_manager())
                )
            
            self.is_running = True
            self.logger.info("Analytics Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start analytics service: {e}")
            raise AnalyticsException(f"Service startup failed: {e}")
    
    async def stop(self):
        """Stop the analytics service gracefully."""
        self.logger.info("Stopping Analytics Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # Process remaining events
        await self._flush_event_buffer()
        
        self.logger.info("Analytics Service stopped")
    
    async def track_event(self, event: AnalyticsEvent) -> Dict[str, Any]:
        """Track an analytics event."""
        try:
            # Validate event
            if not event.user_id and not event.session_id:
                raise AnalyticsException("Event must have either user_id or session_id")
            
            # Add to buffer for batch processing
            event_data = {
                "id": str(uuid.uuid4()),
                "event_name": event.event_name,
                "user_id": event.user_id,
                "session_id": event.session_id,
                "properties": event.properties,
                "timestamp": event.timestamp or datetime.utcnow(),
                "ip_address": event.ip_address,
                "user_agent": event.user_agent,
                "referrer": event.referrer,
                "page_url": event.page_url
            }
            
            # Add to processing queue
            await self.processing_queue.put(event_data)
            
            # Update metrics
            self.metrics.events_tracked.inc()
            
            self.logger.debug(
                "Event tracked",
                event_name=event.event_name,
                user_id=event.user_id,
                session_id=event.session_id
            )
            
            return {"event_id": event_data["id"], "status": "tracked"}
            
        except Exception as e:
            self.logger.error(f"Failed to track event: {e}")
            self.metrics.events_failed.inc()
            raise AnalyticsException(f"Failed to track event: {e}")
    
    async def get_user_metrics(
        self, 
        user_id: Optional[str] = None,
        date_range: Optional[DateRange] = None
    ) -> UserMetrics:
        """Get comprehensive user metrics."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=30),
                    end_date=datetime.utcnow()
                )
            
            # Check cache
            cache_key = f"user_metrics_{user_id}_{date_range.start_date}_{date_range.end_date}"
            if cache_key in self.cache:
                return self.cache[cache_key]
            
            # Query user events
            query = """
            SELECT 
                COUNT(*) as total_events,
                COUNT(DISTINCT session_id) as sessions,
                COUNT(DISTINCT DATE(timestamp)) as active_days,
                AVG(EXTRACT(EPOCH FROM (
                    SELECT MAX(timestamp) - MIN(timestamp) 
                    FROM analytics_events e2 
                    WHERE e2.session_id = e1.session_id
                ))) as avg_session_duration,
                COUNT(CASE WHEN event_name = 'book_generated' THEN 1 END) as books_generated,
                COUNT(CASE WHEN event_name = 'book_published' THEN 1 END) as books_published,
                COUNT(CASE WHEN event_name = 'purchase' THEN 1 END) as purchases
            FROM analytics_events e1
            WHERE timestamp >= %s AND timestamp <= %s
            """
            
            params = [date_range.start_date, date_range.end_date]
            if user_id:
                query += " AND user_id = %s"
                params.append(user_id)
            
            result = await self.database.fetch_one(query, params)
            
            # Calculate derived metrics
            metrics = UserMetrics(
                user_id=user_id,
                date_range=date_range,
                total_events=result["total_events"] or 0,
                sessions=result["sessions"] or 0,
                active_days=result["active_days"] or 0,
                avg_session_duration=result["avg_session_duration"] or 0,
                books_generated=result["books_generated"] or 0,
                books_published=result["books_published"] or 0,
                purchases=result["purchases"] or 0,
                conversion_rate=self._calculate_conversion_rate(result),
                engagement_score=self._calculate_engagement_score(result),
                lifetime_value=await self._calculate_lifetime_value(user_id, date_range)
            )
            
            # Cache result
            self.cache[cache_key] = metrics
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to get user metrics: {e}")
            raise AnalyticsException(f"Failed to get user metrics: {e}")
    
    async def get_content_metrics(
        self, 
        content_id: Optional[str] = None,
        date_range: Optional[DateRange] = None
    ) -> ContentMetrics:
        """Get content performance metrics."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=30),
                    end_date=datetime.utcnow()
                )
            
            # Query content events
            query = """
            SELECT 
                properties->>'content_id' as content_id,
                COUNT(*) as total_views,
                COUNT(DISTINCT user_id) as unique_viewers,
                COUNT(CASE WHEN event_name = 'content_shared' THEN 1 END) as shares,
                COUNT(CASE WHEN event_name = 'content_liked' THEN 1 END) as likes,
                COUNT(CASE WHEN event_name = 'content_downloaded' THEN 1 END) as downloads,
                AVG(CAST(properties->>'engagement_time' AS FLOAT)) as avg_engagement_time,
                COUNT(CASE WHEN event_name = 'purchase' THEN 1 END) as conversions
            FROM analytics_events
            WHERE timestamp >= %s AND timestamp <= %s
            AND properties->>'content_id' IS NOT NULL
            """
            
            params = [date_range.start_date, date_range.end_date]
            if content_id:
                query += " AND properties->>'content_id' = %s"
                params.append(content_id)
            
            query += " GROUP BY properties->>'content_id'"
            
            results = await self.database.fetch_all(query, params)
            
            # Process results
            content_metrics = []
            for result in results:
                metrics = ContentMetrics(
                    content_id=result["content_id"],
                    date_range=date_range,
                    total_views=result["total_views"] or 0,
                    unique_viewers=result["unique_viewers"] or 0,
                    shares=result["shares"] or 0,
                    likes=result["likes"] or 0,
                    downloads=result["downloads"] or 0,
                    avg_engagement_time=result["avg_engagement_time"] or 0,
                    conversions=result["conversions"] or 0,
                    conversion_rate=self._calculate_content_conversion_rate(result),
                    engagement_rate=self._calculate_engagement_rate(result),
                    virality_score=self._calculate_virality_score(result)
                )
                content_metrics.append(metrics)
            
            if content_id:
                return content_metrics[0] if content_metrics else ContentMetrics(
                    content_id=content_id, date_range=date_range
                )
            
            return content_metrics
            
        except Exception as e:
            self.logger.error(f"Failed to get content metrics: {e}")
            raise AnalyticsException(f"Failed to get content metrics: {e}")
    
    async def analyze_conversion_funnel(
        self, 
        funnel_steps: List[str],
        date_range: Optional[DateRange] = None
    ) -> ConversionFunnel:
        """Analyze conversion funnel with drop-off rates."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=30),
                    end_date=datetime.utcnow()
                )
            
            # Query funnel events
            funnel_data = {}
            
            for step in funnel_steps:
                query = """
                SELECT COUNT(DISTINCT user_id) as users
                FROM analytics_events
                WHERE event_name = %s
                AND timestamp >= %s AND timestamp <= %s
                """
                
                result = await self.database.fetch_one(
                    query, [step, date_range.start_date, date_range.end_date]
                )
                
                funnel_data[step] = result["users"] or 0
            
            # Calculate conversion rates
            step_data = []
            previous_count = None
            
            for i, step in enumerate(funnel_steps):
                count = funnel_data[step]
                
                if i == 0:
                    conversion_rate = 1.0
                    drop_off_rate = 0.0
                else:
                    conversion_rate = count / funnel_data[funnel_steps[0]] if funnel_data[funnel_steps[0]] > 0 else 0
                    drop_off_rate = (previous_count - count) / previous_count if previous_count > 0 else 0
                
                step_data.append({
                    "step": step,
                    "users": count,
                    "conversion_rate": conversion_rate,
                    "drop_off_rate": drop_off_rate
                })
                
                previous_count = count
            
            return ConversionFunnel(
                funnel_name="_".join(funnel_steps),
                date_range=date_range,
                steps=step_data,
                overall_conversion_rate=step_data[-1]["conversion_rate"] if step_data else 0,
                total_drop_offs=sum(step["drop_off_rate"] for step in step_data)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze conversion funnel: {e}")
            raise AnalyticsException(f"Failed to analyze conversion funnel: {e}")
    
    async def perform_cohort_analysis(
        self,
        cohort_type: str = "weekly",
        metric: str = "retention",
        periods: int = 12
    ) -> CohortAnalysis:
        """Perform cohort analysis for user retention or revenue."""
        try:
            # Determine cohort period
            if cohort_type == "daily":
                period_format = "YYYY-MM-DD"
                period_interval = "1 day"
            elif cohort_type == "weekly":
                period_format = "YYYY-\"W\"WW"
                period_interval = "1 week"
            elif cohort_type == "monthly":
                period_format = "YYYY-MM"
                period_interval = "1 month"
            else:
                raise AnalyticsException(f"Invalid cohort type: {cohort_type}")
            
            # Query cohort data
            query = f"""
            WITH user_cohorts AS (
                SELECT 
                    user_id,
                    TO_CHAR(MIN(timestamp), '{period_format}') as cohort_period,
                    MIN(timestamp) as first_seen
                FROM analytics_events
                WHERE user_id IS NOT NULL
                GROUP BY user_id
            ),
            user_periods AS (
                SELECT 
                    e.user_id,
                    uc.cohort_period,
                    TO_CHAR(e.timestamp, '{period_format}') as period,
                    EXTRACT(EPOCH FROM (
                        DATE_TRUNC('{cohort_type}', e.timestamp) - 
                        DATE_TRUNC('{cohort_type}', uc.first_seen)
                    )) / EXTRACT(EPOCH FROM INTERVAL '{period_interval}') as period_number
                FROM analytics_events e
                JOIN user_cohorts uc ON e.user_id = uc.user_id
                WHERE e.timestamp >= uc.first_seen
            )
            SELECT 
                cohort_period,
                period_number,
                COUNT(DISTINCT user_id) as users
            FROM user_periods
            WHERE period_number <= {periods}
            GROUP BY cohort_period, period_number
            ORDER BY cohort_period, period_number
            """
            
            results = await self.database.fetch_all(query)
            
            # Process cohort data
            cohort_data = defaultdict(dict)
            cohort_sizes = {}
            
            for result in results:
                cohort = result["cohort_period"]
                period = int(result["period_number"])
                users = result["users"]
                
                if period == 0:
                    cohort_sizes[cohort] = users
                
                cohort_data[cohort][period] = users
            
            # Calculate retention rates
            cohort_table = []
            for cohort in sorted(cohort_data.keys()):
                row = {"cohort": cohort, "size": cohort_sizes.get(cohort, 0)}
                
                for period in range(periods + 1):
                    if period in cohort_data[cohort]:
                        retention_rate = cohort_data[cohort][period] / cohort_sizes[cohort]
                        row[f"period_{period}"] = retention_rate
                    else:
                        row[f"period_{period}"] = 0.0
                
                cohort_table.append(row)
            
            return CohortAnalysis(
                cohort_type=cohort_type,
                metric=metric,
                periods=periods,
                cohort_table=cohort_table,
                avg_retention_rates=self._calculate_avg_retention_rates(cohort_table, periods)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to perform cohort analysis: {e}")
            raise AnalyticsException(f"Failed to perform cohort analysis: {e}")
    
    async def get_analytics_summary(self) -> AnalyticsSummary:
        """Get comprehensive analytics summary."""
        try:
            current_period = DateRange(
                start_date=datetime.utcnow() - timedelta(days=30),
                end_date=datetime.utcnow()
            )
            
            previous_period = DateRange(
                start_date=datetime.utcnow() - timedelta(days=60),
                end_date=datetime.utcnow() - timedelta(days=30)
            )
            
            # Get key metrics for both periods
            current_metrics = await self._get_period_metrics(current_period)
            previous_metrics = await self._get_period_metrics(previous_period)
            
            # Calculate growth rates
            growth_rates = {}
            for key in current_metrics:
                if key in previous_metrics and previous_metrics[key] > 0:
                    growth_rates[key] = (
                        (current_metrics[key] - previous_metrics[key]) / previous_metrics[key]
                    ) * 100
                else:
                    growth_rates[key] = 0.0
            
            return AnalyticsSummary(
                period=current_period,
                total_users=current_metrics.get("total_users", 0),
                active_users=current_metrics.get("active_users", 0),
                new_users=current_metrics.get("new_users", 0),
                total_sessions=current_metrics.get("total_sessions", 0),
                avg_session_duration=current_metrics.get("avg_session_duration", 0),
                page_views=current_metrics.get("page_views", 0),
                bounce_rate=current_metrics.get("bounce_rate", 0),
                conversion_rate=current_metrics.get("conversion_rate", 0),
                growth_rates=growth_rates,
                top_content=await self._get_top_content(current_period),
                user_acquisition_channels=await self._get_acquisition_channels(current_period)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get analytics summary: {e}")
            raise AnalyticsException(f"Failed to get analytics summary: {e}")
    
    async def get_key_metrics(self) -> Dict[str, Any]:
        """Get key business metrics for dashboards."""
        try:
            # Real-time metrics
            today = datetime.utcnow().date()
            
            query = """
            SELECT 
                COUNT(CASE WHEN DATE(timestamp) = %s THEN 1 END) as events_today,
                COUNT(DISTINCT CASE WHEN DATE(timestamp) = %s THEN user_id END) as active_users_today,
                COUNT(DISTINCT CASE WHEN DATE(timestamp) = %s THEN session_id END) as sessions_today,
                COUNT(CASE WHEN event_name = 'book_generated' AND DATE(timestamp) = %s THEN 1 END) as books_generated_today,
                COUNT(CASE WHEN event_name = 'purchase' AND DATE(timestamp) = %s THEN 1 END) as purchases_today
            FROM analytics_events
            WHERE timestamp >= %s - INTERVAL '7 days'
            """
            
            params = [today] * 5 + [today]
            result = await self.database.fetch_one(query, params)
            
            return {
                "events_today": result["events_today"] or 0,
                "active_users_today": result["active_users_today"] or 0,
                "sessions_today": result["sessions_today"] or 0,
                "books_generated_today": result["books_generated_today"] or 0,
                "purchases_today": result["purchases_today"] or 0,
                "system_health": "healthy",
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get key metrics: {e}")
            raise AnalyticsException(f"Failed to get key metrics: {e}")
    
    async def get_user_insights(self) -> Dict[str, Any]:
        """Get user behavior insights."""
        try:
            # User segmentation and behavior patterns
            query = """
            SELECT 
                properties->>'user_segment' as segment,
                COUNT(DISTINCT user_id) as users,
                AVG(CAST(properties->>'engagement_score' AS FLOAT)) as avg_engagement,
                COUNT(*) as total_events
            FROM analytics_events
            WHERE timestamp >= NOW() - INTERVAL '30 days'
            AND properties->>'user_segment' IS NOT NULL
            GROUP BY properties->>'user_segment'
            ORDER BY users DESC
            """
            
            segments = await self.database.fetch_all(query)
            
            # Device and platform insights
            device_query = """
            SELECT 
                properties->>'device_type' as device_type,
                COUNT(DISTINCT user_id) as users,
                COUNT(*) as events
            FROM analytics_events
            WHERE timestamp >= NOW() - INTERVAL '30 days'
            AND properties->>'device_type' IS NOT NULL
            GROUP BY properties->>'device_type'
            ORDER BY users DESC
            """
            
            devices = await self.database.fetch_all(device_query)
            
            return {
                "user_segments": [dict(row) for row in segments],
                "device_breakdown": [dict(row) for row in devices],
                "insights": await self._generate_user_insights(segments, devices)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get user insights: {e}")
            raise AnalyticsException(f"Failed to get user insights: {e}")
    
    # Private helper methods
    
    async def _event_processor(self):
        """Background task to process analytics events."""
        self.logger.info("Starting event processor")
        
        batch_size = 100
        events_batch = []
        
        while self.is_running:
            try:
                # Collect events into batch
                while len(events_batch) < batch_size:
                    try:
                        event = await asyncio.wait_for(
                            self.processing_queue.get(), timeout=1.0
                        )
                        events_batch.append(event)
                    except asyncio.TimeoutError:
                        break
                
                # Process batch if we have events
                if events_batch:
                    await self._store_events_batch(events_batch)
                    events_batch.clear()
                
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in event processor: {e}")
                await asyncio.sleep(5)
    
    async def _store_events_batch(self, events: List[Dict[str, Any]]):
        """Store a batch of events in the database."""
        try:
            # Prepare bulk insert
            values = []
            for event in events:
                values.append((
                    event["id"],
                    event["event_name"],
                    event["user_id"],
                    event["session_id"],
                    json.dumps(event["properties"]),
                    event["timestamp"],
                    event["ip_address"],
                    event["user_agent"],
                    event["referrer"],
                    event["page_url"]
                ))
            
            # Bulk insert
            query = """
            INSERT INTO analytics_events 
            (id, event_name, user_id, session_id, properties, timestamp, 
             ip_address, user_agent, referrer, page_url)
            VALUES %s
            ON CONFLICT (id) DO NOTHING
            """
            
            await self.database.execute_many(query, values)
            
            # Update metrics
            self.metrics.events_processed.inc(len(events))
            
            self.logger.debug(f"Stored {len(events)} events")
            
        except Exception as e:
            self.logger.error(f"Failed to store events batch: {e}")
            self.metrics.events_failed.inc(len(events))
    
    async def _metrics_aggregator(self):
        """Background task to aggregate metrics periodically."""
        self.logger.info("Starting metrics aggregator")
        
        while self.is_running:
            try:
                # Aggregate hourly metrics
                await self._aggregate_hourly_metrics()
                
                # Wait for next aggregation cycle
                await asyncio.sleep(3600)  # Run every hour
                
            except Exception as e:
                self.logger.error(f"Error in metrics aggregator: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _cache_manager(self):
        """Background task to manage cache expiration."""
        while self.is_running:
            try:
                current_time = time.time()
                expired_keys = []
                
                for key, (data, timestamp) in self.cache.items():
                    if current_time - timestamp > self.cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.cache[key]
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in cache manager: {e}")
                await asyncio.sleep(60)
    
    def _calculate_conversion_rate(self, metrics: Dict[str, Any]) -> float:
        """Calculate conversion rate from metrics."""
        if metrics["sessions"] and metrics["purchases"]:
            return metrics["purchases"] / metrics["sessions"]
        return 0.0
    
    def _calculate_engagement_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate engagement score from metrics."""
        # Simplified engagement score calculation
        score = 0.0
        
        if metrics["sessions"]:
            score += min(metrics["active_days"] / 30, 1.0) * 0.3  # Frequency
            score += min(metrics["avg_session_duration"] / 600, 1.0) * 0.3  # Duration
            score += min(metrics["total_events"] / metrics["sessions"] / 10, 1.0) * 0.4  # Depth
        
        return score
    
    async def _calculate_lifetime_value(self, user_id: str, date_range: DateRange) -> float:
        """Calculate user lifetime value."""
        if not user_id:
            return 0.0
        
        # Query purchase events for the user
        query = """
        SELECT SUM(CAST(properties->>'amount' AS FLOAT)) as total_spent
        FROM analytics_events
        WHERE user_id = %s 
        AND event_name = 'purchase'
        AND timestamp >= %s AND timestamp <= %s
        """
        
        result = await self.database.fetch_one(
            query, [user_id, date_range.start_date, date_range.end_date]
        )
        
        return result["total_spent"] or 0.0
    
    async def get_status(self) -> Dict[str, Any]:
        """Get analytics service status."""
        return {
            "is_running": self.is_running,
            "queue_size": self.processing_queue.qsize(),
            "cache_size": len(self.cache),
            "background_tasks": len(self.background_tasks),
            "events_processed_today": await self._get_events_processed_today()
        }
    
    async def _get_events_processed_today(self) -> int:
        """Get number of events processed today."""
        try:
            query = """
            SELECT COUNT(*) as count
            FROM analytics_events
            WHERE DATE(timestamp) = CURRENT_DATE
            """
            
            result = await self.database.fetch_one(query)
            return result["count"] or 0
            
        except Exception:
            return 0