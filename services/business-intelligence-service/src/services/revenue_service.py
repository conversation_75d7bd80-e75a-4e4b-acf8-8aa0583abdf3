"""
Revenue Service - Comprehensive revenue tracking and optimization.

Provides revenue analytics capabilities including:
- Revenue tracking and forecasting
- Subscription management
- Payment processing analytics
- Pricing optimization
- Financial reporting
- Churn analysis
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import uuid

import pandas as pd
import numpy as np
from scipy import stats
import structlog

from core.config import get_settings
from core.monitoring import AnalyticsMetrics
from core.exceptions import AnalyticsException
from models.revenue_schemas import (
    RevenueMetrics, SubscriptionMetrics, PaymentEvent,
    PricingAnalysis, ChurnAnalysis, RevenueForecast
)
from utils.date_utils import DateRange, get_date_ranges
from utils.financial_utils import calculate_mrr, calculate_arr, calculate_ltv


class RevenueService:
    """
    Revenue service that handles all revenue-related analytics including
    subscription management, payment tracking, and financial forecasting.
    """
    
    def __init__(self, database_client, supabase_client, metrics: AnalyticsMetrics):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = metrics
        
        # Revenue state
        self.revenue_cache = {}
        self.subscription_cache = {}
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
        # Payment processors
        self.payment_processors = {}
        
    async def start(self):
        """Start the revenue service and background tasks."""
        self.logger.info("Starting Revenue Service")
        
        try:
            # Initialize revenue tables if needed
            await self._initialize_revenue_tables()
            
            # Initialize payment processors
            await self._initialize_payment_processors()
            
            # Start background tasks
            if self.settings.revenue_tracking_enabled:
                self.background_tasks.append(
                    asyncio.create_task(self._revenue_aggregator())
                )
                
                self.background_tasks.append(
                    asyncio.create_task(self._subscription_monitor())
                )
                
                self.background_tasks.append(
                    asyncio.create_task(self._churn_analyzer())
                )
            
            self.is_running = True
            self.logger.info("Revenue Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start revenue service: {e}")
            raise AnalyticsException(f"Service startup failed: {e}")
    
    async def stop(self):
        """Stop the revenue service gracefully."""
        self.logger.info("Stopping Revenue Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("Revenue Service stopped")
    
    async def track_payment(self, payment_event: PaymentEvent) -> Dict[str, Any]:
        """Track a payment event."""
        try:
            # Validate payment event
            if not payment_event.amount or payment_event.amount <= 0:
                raise AnalyticsException("Payment amount must be positive")
            
            # Store payment event
            payment_data = {
                "id": str(uuid.uuid4()),
                "user_id": payment_event.user_id,
                "subscription_id": payment_event.subscription_id,
                "amount": float(payment_event.amount),
                "currency": payment_event.currency,
                "payment_method": payment_event.payment_method,
                "processor": payment_event.processor,
                "transaction_id": payment_event.transaction_id,
                "status": payment_event.status,
                "metadata": payment_event.metadata,
                "timestamp": payment_event.timestamp or datetime.utcnow()
            }
            
            # Insert into database
            result = await self.supabase.table("payment_events").insert(payment_data).execute()
            
            if not result.data:
                raise AnalyticsException("Failed to store payment event")
            
            # Update revenue metrics
            await self._update_revenue_metrics(payment_data)
            
            # Update subscription if applicable
            if payment_event.subscription_id:
                await self._update_subscription_metrics(payment_event.subscription_id, payment_data)
            
            # Update metrics
            self.metrics.payments_processed.inc()
            self.metrics.revenue_tracked.inc(payment_event.amount)
            
            self.logger.info(
                "Payment tracked",
                payment_id=payment_data["id"],
                user_id=payment_event.user_id,
                amount=payment_event.amount,
                currency=payment_event.currency
            )
            
            return {"payment_id": payment_data["id"], "status": "tracked"}
            
        except Exception as e:
            self.logger.error(f"Failed to track payment: {e}")
            self.metrics.payments_failed.inc()
            raise AnalyticsException(f"Failed to track payment: {e}")
    
    async def get_revenue_metrics(
        self, 
        date_range: Optional[DateRange] = None,
        currency: str = "USD"
    ) -> RevenueMetrics:
        """Get comprehensive revenue metrics."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=30),
                    end_date=datetime.utcnow()
                )
            
            # Query revenue data
            query = """
            SELECT 
                SUM(amount) as total_revenue,
                COUNT(*) as total_transactions,
                COUNT(DISTINCT user_id) as paying_customers,
                AVG(amount) as avg_transaction_value,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_revenue,
                SUM(CASE WHEN status = 'refunded' THEN amount ELSE 0 END) as refunded_amount,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions
            FROM payment_events
            WHERE timestamp >= %s AND timestamp <= %s
            AND currency = %s
            """
            
            result = await self.database.fetch_one(
                query, [date_range.start_date, date_range.end_date, currency]
            )
            
            # Calculate additional metrics
            mrr = await self._calculate_mrr(date_range, currency)
            arr = await self._calculate_arr(date_range, currency)
            growth_rate = await self._calculate_revenue_growth_rate(date_range, currency)
            
            return RevenueMetrics(
                date_range=date_range,
                currency=currency,
                total_revenue=Decimal(str(result["total_revenue"] or 0)),
                completed_revenue=Decimal(str(result["completed_revenue"] or 0)),
                refunded_amount=Decimal(str(result["refunded_amount"] or 0)),
                total_transactions=result["total_transactions"] or 0,
                failed_transactions=result["failed_transactions"] or 0,
                paying_customers=result["paying_customers"] or 0,
                avg_transaction_value=Decimal(str(result["avg_transaction_value"] or 0)),
                mrr=mrr,
                arr=arr,
                growth_rate=growth_rate,
                churn_rate=await self._calculate_churn_rate(date_range),
                customer_lifetime_value=await self._calculate_customer_ltv(date_range, currency)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get revenue metrics: {e}")
            raise AnalyticsException(f"Failed to get revenue metrics: {e}")
    
    async def get_subscription_metrics(
        self, 
        date_range: Optional[DateRange] = None
    ) -> SubscriptionMetrics:
        """Get subscription-specific metrics."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=30),
                    end_date=datetime.utcnow()
                )
            
            # Query subscription data
            query = """
            SELECT 
                COUNT(*) as total_subscriptions,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_subscriptions,
                COUNT(CASE WHEN created_at >= %s THEN 1 END) as new_subscriptions,
                AVG(CASE WHEN status = 'active' THEN monthly_value ELSE NULL END) as avg_subscription_value,
                SUM(CASE WHEN status = 'active' THEN monthly_value ELSE 0 END) as total_mrr
            FROM subscriptions
            WHERE created_at <= %s
            """
            
            result = await self.database.fetch_one(
                query, [date_range.start_date, date_range.end_date]
            )
            
            # Query plan distribution
            plan_query = """
            SELECT 
                plan_type,
                COUNT(*) as count,
                SUM(monthly_value) as revenue
            FROM subscriptions
            WHERE status = 'active'
            AND created_at <= %s
            GROUP BY plan_type
            ORDER BY revenue DESC
            """
            
            plan_results = await self.database.fetch_all(plan_query, [date_range.end_date])
            
            # Calculate subscription health metrics
            cohort_retention = await self._calculate_subscription_retention(date_range)
            upgrade_rate = await self._calculate_upgrade_rate(date_range)
            downgrade_rate = await self._calculate_downgrade_rate(date_range)
            
            return SubscriptionMetrics(
                date_range=date_range,
                total_subscriptions=result["total_subscriptions"] or 0,
                active_subscriptions=result["active_subscriptions"] or 0,
                cancelled_subscriptions=result["cancelled_subscriptions"] or 0,
                new_subscriptions=result["new_subscriptions"] or 0,
                avg_subscription_value=Decimal(str(result["avg_subscription_value"] or 0)),
                total_mrr=Decimal(str(result["total_mrr"] or 0)),
                plan_distribution=[dict(row) for row in plan_results],
                cohort_retention=cohort_retention,
                upgrade_rate=upgrade_rate,
                downgrade_rate=downgrade_rate,
                churn_rate=await self._calculate_subscription_churn_rate(date_range)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get subscription metrics: {e}")
            raise AnalyticsException(f"Failed to get subscription metrics: {e}")
    
    async def analyze_pricing_optimization(
        self, 
        product_id: Optional[str] = None
    ) -> PricingAnalysis:
        """Analyze pricing optimization opportunities."""
        try:
            # Price sensitivity analysis
            price_points = await self._get_price_points(product_id)
            
            # Demand elasticity calculation
            elasticity_data = []
            for i in range(len(price_points) - 1):
                current_price = price_points[i]
                next_price = price_points[i + 1]
                
                current_demand = await self._get_demand_at_price(current_price["price"], product_id)
                next_demand = await self._get_demand_at_price(next_price["price"], product_id)
                
                if current_demand > 0 and current_price["price"] != next_price["price"]:
                    price_change = (next_price["price"] - current_price["price"]) / current_price["price"]
                    demand_change = (next_demand - current_demand) / current_demand
                    
                    elasticity = demand_change / price_change if price_change != 0 else 0
                    
                    elasticity_data.append({
                        "price_from": current_price["price"],
                        "price_to": next_price["price"],
                        "elasticity": elasticity,
                        "revenue_impact": self._calculate_revenue_impact(
                            current_price["price"], next_price["price"], 
                            current_demand, next_demand
                        )
                    })
            
            # Optimal pricing recommendations
            optimal_prices = await self._calculate_optimal_prices(elasticity_data, product_id)
            
            # Competitive analysis
            competitive_data = await self._get_competitive_pricing_data(product_id)
            
            return PricingAnalysis(
                product_id=product_id,
                current_pricing=price_points,
                elasticity_analysis=elasticity_data,
                optimal_prices=optimal_prices,
                competitive_analysis=competitive_data,
                recommendations=await self._generate_pricing_recommendations(
                    elasticity_data, optimal_prices, competitive_data
                )
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze pricing optimization: {e}")
            raise AnalyticsException(f"Failed to analyze pricing optimization: {e}")
    
    async def analyze_churn(self, date_range: Optional[DateRange] = None) -> ChurnAnalysis:
        """Perform comprehensive churn analysis."""
        try:
            if not date_range:
                date_range = DateRange(
                    start_date=datetime.utcnow() - timedelta(days=90),
                    end_date=datetime.utcnow()
                )
            
            # Query churn data
            churn_query = """
            SELECT 
                user_id,
                subscription_id,
                plan_type,
                monthly_value,
                created_at,
                cancelled_at,
                cancellation_reason,
                EXTRACT(DAYS FROM (cancelled_at - created_at)) as subscription_lifetime_days
            FROM subscriptions
            WHERE cancelled_at >= %s AND cancelled_at <= %s
            """
            
            churned_subscriptions = await self.database.fetch_all(
                churn_query, [date_range.start_date, date_range.end_date]
            )
            
            # Churn rate by segment
            segment_churn = {}
            for subscription in churned_subscriptions:
                plan_type = subscription["plan_type"]
                if plan_type not in segment_churn:
                    segment_churn[plan_type] = {"churned": 0, "total": 0}
                segment_churn[plan_type]["churned"] += 1
            
            # Get total subscriptions by segment for churn rate calculation
            for plan_type in segment_churn:
                total_query = """
                SELECT COUNT(*) as total
                FROM subscriptions
                WHERE plan_type = %s
                AND created_at <= %s
                """
                
                result = await self.database.fetch_one(total_query, [plan_type, date_range.end_date])
                segment_churn[plan_type]["total"] = result["total"] or 0
                
                if segment_churn[plan_type]["total"] > 0:
                    segment_churn[plan_type]["churn_rate"] = (
                        segment_churn[plan_type]["churned"] / segment_churn[plan_type]["total"]
                    )
                else:
                    segment_churn[plan_type]["churn_rate"] = 0
            
            # Churn prediction model inputs
            churn_predictors = await self._analyze_churn_predictors(date_range)
            
            # Revenue impact of churn
            churned_revenue = sum(
                float(sub["monthly_value"]) * 12 for sub in churned_subscriptions
            )
            
            return ChurnAnalysis(
                date_range=date_range,
                total_churned_customers=len(churned_subscriptions),
                churn_rate=await self._calculate_overall_churn_rate(date_range),
                churn_by_segment=segment_churn,
                avg_customer_lifetime=np.mean([
                    sub["subscription_lifetime_days"] for sub in churned_subscriptions
                ]) if churned_subscriptions else 0,
                churn_reasons=self._analyze_churn_reasons(churned_subscriptions),
                revenue_impact=Decimal(str(churned_revenue)),
                predictive_factors=churn_predictors,
                retention_recommendations=await self._generate_retention_recommendations(
                    segment_churn, churn_predictors
                )
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze churn: {e}")
            raise AnalyticsException(f"Failed to analyze churn: {e}")
    
    async def forecast_revenue(
        self, 
        forecast_periods: int = 12,
        forecast_type: str = "monthly"
    ) -> RevenueForecast:
        """Generate revenue forecast using statistical models."""
        try:
            # Get historical revenue data
            historical_data = await self._get_historical_revenue_data(forecast_periods * 3)
            
            if len(historical_data) < 6:
                raise AnalyticsException("Insufficient historical data for forecasting")
            
            # Prepare time series data
            df = pd.DataFrame(historical_data)
            df["date"] = pd.to_datetime(df["date"])
            df = df.set_index("date").sort_index()
            
            # Apply multiple forecasting models
            forecasts = {}
            
            # Linear trend model
            forecasts["linear"] = self._linear_trend_forecast(df, forecast_periods)
            
            # Moving average model
            forecasts["moving_average"] = self._moving_average_forecast(df, forecast_periods)
            
            # Exponential smoothing
            forecasts["exponential_smoothing"] = self._exponential_smoothing_forecast(
                df, forecast_periods
            )
            
            # Seasonal decomposition (if enough data)
            if len(df) >= 24:
                forecasts["seasonal"] = self._seasonal_forecast(df, forecast_periods)
            
            # Ensemble forecast (weighted average)
            ensemble_forecast = self._create_ensemble_forecast(forecasts, forecast_periods)
            
            # Calculate confidence intervals
            confidence_intervals = self._calculate_forecast_confidence_intervals(
                df, ensemble_forecast, forecast_periods
            )
            
            # Business assumptions and adjustments
            adjusted_forecast = await self._apply_business_adjustments(
                ensemble_forecast, forecast_periods
            )
            
            return RevenueForecast(
                forecast_type=forecast_type,
                periods=forecast_periods,
                historical_data=historical_data,
                forecasts=forecasts,
                ensemble_forecast=ensemble_forecast,
                adjusted_forecast=adjusted_forecast,
                confidence_intervals=confidence_intervals,
                model_accuracy=self._calculate_model_accuracy(df, forecasts),
                assumptions=await self._get_forecast_assumptions()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to forecast revenue: {e}")
            raise AnalyticsException(f"Failed to forecast revenue: {e}")
    
    async def get_revenue_summary(self) -> Dict[str, Any]:
        """Get revenue summary for dashboard."""
        try:
            # Current month metrics
            current_month = DateRange(
                start_date=datetime.utcnow().replace(day=1),
                end_date=datetime.utcnow()
            )
            
            # Previous month for comparison
            previous_month_start = (current_month.start_date - timedelta(days=1)).replace(day=1)
            previous_month = DateRange(
                start_date=previous_month_start,
                end_date=current_month.start_date - timedelta(days=1)
            )
            
            # Get metrics for both periods
            current_metrics = await self.get_revenue_metrics(current_month)
            previous_metrics = await self.get_revenue_metrics(previous_month)
            
            # Calculate growth
            revenue_growth = 0.0
            if float(previous_metrics.total_revenue) > 0:
                revenue_growth = (
                    (float(current_metrics.total_revenue) - float(previous_metrics.total_revenue)) /
                    float(previous_metrics.total_revenue)
                ) * 100
            
            return {
                "current_month_revenue": float(current_metrics.total_revenue),
                "previous_month_revenue": float(previous_metrics.total_revenue),
                "revenue_growth": revenue_growth,
                "mrr": float(current_metrics.mrr),
                "arr": float(current_metrics.arr),
                "paying_customers": current_metrics.paying_customers,
                "avg_transaction_value": float(current_metrics.avg_transaction_value),
                "churn_rate": current_metrics.churn_rate,
                "customer_lifetime_value": float(current_metrics.customer_lifetime_value)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get revenue summary: {e}")
            raise AnalyticsException(f"Failed to get revenue summary: {e}")
    
    async def get_growth_trends(self) -> Dict[str, Any]:
        """Get revenue growth trends."""
        try:
            # Get last 12 months of data
            monthly_revenue = []
            
            for i in range(12):
                month_start = (datetime.utcnow().replace(day=1) - timedelta(days=32*i)).replace(day=1)
                month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
                
                query = """
                SELECT 
                    SUM(amount) as revenue,
                    COUNT(DISTINCT user_id) as customers
                FROM payment_events
                WHERE timestamp >= %s AND timestamp <= %s
                AND status = 'completed'
                """
                
                result = await self.database.fetch_one(query, [month_start, month_end])
                
                monthly_revenue.append({
                    "month": month_start.strftime("%Y-%m"),
                    "revenue": float(result["revenue"] or 0),
                    "customers": result["customers"] or 0
                })
            
            # Calculate trends
            revenues = [item["revenue"] for item in monthly_revenue]
            if len(revenues) >= 2:
                growth_rate = ((revenues[0] - revenues[-1]) / revenues[-1]) * 100 if revenues[-1] > 0 else 0
                trend = "increasing" if growth_rate > 5 else "decreasing" if growth_rate < -5 else "stable"
            else:
                growth_rate = 0
                trend = "stable"
            
            return {
                "monthly_revenue": list(reversed(monthly_revenue)),
                "growth_rate": growth_rate,
                "trend": trend,
                "revenue_volatility": np.std(revenues) if revenues else 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get growth trends: {e}")
            raise AnalyticsException(f"Failed to get growth trends: {e}")
    
    # Private helper methods
    
    async def _calculate_mrr(self, date_range: DateRange, currency: str) -> Decimal:
        """Calculate Monthly Recurring Revenue."""
        query = """
        SELECT SUM(monthly_value) as mrr
        FROM subscriptions
        WHERE status = 'active'
        AND currency = %s
        AND created_at <= %s
        """
        
        result = await self.database.fetch_one(query, [currency, date_range.end_date])
        return Decimal(str(result["mrr"] or 0))
    
    async def _calculate_arr(self, date_range: DateRange, currency: str) -> Decimal:
        """Calculate Annual Recurring Revenue."""
        mrr = await self._calculate_mrr(date_range, currency)
        return mrr * 12
    
    async def _calculate_churn_rate(self, date_range: DateRange) -> float:
        """Calculate customer churn rate."""
        # Get churned customers in period
        churn_query = """
        SELECT COUNT(DISTINCT user_id) as churned
        FROM subscriptions
        WHERE cancelled_at >= %s AND cancelled_at <= %s
        """
        
        churn_result = await self.database.fetch_one(
            churn_query, [date_range.start_date, date_range.end_date]
        )
        
        # Get total active customers at start of period
        active_query = """
        SELECT COUNT(DISTINCT user_id) as active
        FROM subscriptions
        WHERE status = 'active'
        AND created_at <= %s
        """
        
        active_result = await self.database.fetch_one(active_query, [date_range.start_date])
        
        churned = churn_result["churned"] or 0
        active = active_result["active"] or 0
        
        return (churned / active) if active > 0 else 0.0
    
    async def get_status(self) -> Dict[str, Any]:
        """Get revenue service status."""
        return {
            "is_running": self.is_running,
            "background_tasks": len(self.background_tasks),
            "revenue_cache_size": len(self.revenue_cache),
            "subscription_cache_size": len(self.subscription_cache),
            "payment_processors": list(self.payment_processors.keys())
        }