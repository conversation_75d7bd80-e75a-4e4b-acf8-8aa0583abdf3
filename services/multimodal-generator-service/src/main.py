"""
Multimodal Generator Service - Main Application

Provides AI-powered multimodal content generation capabilities including text, image, audio, 
and video content creation with cross-modal integration and style adaptation.
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field

from multimodal_generator import MultimodalGenerator
from content_processor import ContentProcessor
from shared.event_client import EventClient
from shared.service_registry_client import ServiceRegistryClient
from shared.security_manager import SecurityManager, verify_api_key
from shared.monitoring import setup_monitoring, get_metrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Multimodal Generator Service",
    description="AI-powered multimodal content generation and cross-modal integration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global service instances
multimodal_generator: Optional[MultimodalGenerator] = None
content_processor: Optional[ContentProcessor] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: SecurityManager = SecurityManager()

# Setup monitoring
setup_monitoring(app, service_name="multimodal-generator")

# =============================================================================
# Request/Response Models
# =============================================================================

class ServiceResponse(BaseModel):
    """Standard service response format"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    task_id: Optional[str] = None
    execution_time: Optional[float] = None

class MultimodalGenerationRequest(BaseModel):
    """Request for multimodal content generation"""
    content_brief: str = Field(..., description="Brief description of content to generate")
    modalities: List[str] = Field(default=["text"], description="Content modalities to generate")
    style_guide: Optional[Dict[str, Any]] = Field(default=None, description="Style preferences")
    target_format: str = Field(default="web", description="Target output format")
    content_length: Optional[str] = Field(default="medium", description="Content length preference")
    target_audience: str = Field(default="general", description="Target audience")
    tone: str = Field(default="professional", description="Content tone")
    include_metadata: bool = Field(default=True, description="Include generation metadata")

class ContentAdaptationRequest(BaseModel):
    """Request for content adaptation between modalities"""
    source_content: str = Field(..., description="Source content to adapt")
    source_modality: str = Field(..., description="Source content modality")
    target_modalities: List[str] = Field(..., description="Target modalities for adaptation")
    adaptation_style: str = Field(default="faithful", description="Adaptation style")
    quality_level: str = Field(default="high", description="Quality level for adaptation")

class ContentEnhancementRequest(BaseModel):
    """Request for content enhancement"""
    content: str = Field(..., description="Content to enhance")
    modality: str = Field(..., description="Content modality")
    enhancement_types: List[str] = Field(..., description="Types of enhancements to apply")
    quality_threshold: float = Field(default=0.8, description="Quality threshold for enhancements")

class BatchGenerationRequest(BaseModel):
    """Request for batch content generation"""
    generation_requests: List[MultimodalGenerationRequest] = Field(..., description="List of generation requests")
    batch_priority: str = Field(default="normal", description="Batch processing priority")
    parallel_processing: bool = Field(default=True, description="Enable parallel processing")

# =============================================================================
# Startup/Shutdown Events
# =============================================================================

@app.on_event("startup")
async def startup_event():
    """Initialize service components on startup"""
    global multimodal_generator, content_processor, event_client, service_registry
    
    try:
        logger.info("Starting Multimodal Generator Service...")
        
        # Initialize multimodal generator
        multimodal_generator = MultimodalGenerator()
        await multimodal_generator.initialize()
        
        # Initialize content processor
        content_processor = ContentProcessor()
        await content_processor.initialize()
        
        # Initialize event client
        event_client = EventClient(
            service_name="multimodal-generator-service",
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379")
        )
        await event_client.initialize()
        
        # Initialize service registry
        service_registry = ServiceRegistryClient(
            service_name="multimodal-generator-service",
            service_host=os.getenv("SERVICE_HOST", "localhost"),
            service_port=int(os.getenv("SERVICE_PORT", "8088")),
            discovery_url=os.getenv("SERVICE_DISCOVERY_URL", "http://localhost:8070")
        )
        await service_registry.register()
        
        logger.info("Multimodal Generator Service started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup service components on shutdown"""
    global multimodal_generator, content_processor, event_client, service_registry
    
    try:
        logger.info("Shutting down Multimodal Generator Service...")
        
        if service_registry:
            await service_registry.deregister()
        
        if event_client:
            await event_client.cleanup()
        
        if content_processor:
            await content_processor.cleanup()
        
        if multimodal_generator:
            await multimodal_generator.cleanup()
        
        logger.info("Multimodal Generator Service shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# =============================================================================
# API Endpoints
# =============================================================================

@app.post("/generate", response_model=ServiceResponse)
async def generate_multimodal_content(
    request: MultimodalGenerationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate multimodal content asynchronously"""
    try:
        if not multimodal_generator or not multimodal_generator.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Multimodal generator is not ready"
            )
        
        task_id = str(uuid.uuid4())
        
        # Start background task
        background_tasks.add_task(
            _generate_multimodal_content_task,
            task_id,
            request.model_dump()
        )
        
        return ServiceResponse(
            success=True,
            message="Multimodal generation started",
            task_id=task_id
        )
        
    except Exception as e:
        logger.error(f"Failed to start multimodal generation: {e}")
        return ServiceResponse(
            success=False,
            message=f"Failed to start generation: {str(e)}"
        )

@app.post("/generate/sync", response_model=ServiceResponse)
async def generate_multimodal_content_sync(
    request: MultimodalGenerationRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate multimodal content synchronously"""
    try:
        if not multimodal_generator or not multimodal_generator.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Multimodal generator is not ready"
            )
        
        start_time = datetime.now()
        
        result = await multimodal_generator.generate_multimodal_content(
            content_brief=request.content_brief,
            modalities=request.modalities,
            style_guide=request.style_guide,
            target_format=request.target_format,
            content_length=request.content_length,
            target_audience=request.target_audience,
            tone=request.tone,
            include_metadata=request.include_metadata
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return ServiceResponse(
            success=True,
            message="Multimodal content generated successfully",
            data=result.model_dump(),
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"Failed to generate multimodal content: {e}")
        return ServiceResponse(
            success=False,
            message=f"Generation failed: {str(e)}"
        )

@app.post("/adapt", response_model=ServiceResponse)
async def adapt_content(
    request: ContentAdaptationRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Adapt content between different modalities"""
    try:
        if not content_processor or not content_processor.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Content processor is not ready"
            )
        
        start_time = datetime.now()
        
        result = await content_processor.adapt_content(
            source_content=request.source_content,
            source_modality=request.source_modality,
            target_modalities=request.target_modalities,
            adaptation_style=request.adaptation_style,
            quality_level=request.quality_level
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return ServiceResponse(
            success=True,
            message="Content adapted successfully",
            data=result,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"Failed to adapt content: {e}")
        return ServiceResponse(
            success=False,
            message=f"Adaptation failed: {str(e)}"
        )

@app.post("/enhance", response_model=ServiceResponse)
async def enhance_content(
    request: ContentEnhancementRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Enhance content quality and engagement"""
    try:
        if not content_processor or not content_processor.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Content processor is not ready"
            )
        
        start_time = datetime.now()
        
        result = await content_processor.enhance_content(
            content=request.content,
            modality=request.modality,
            enhancement_types=request.enhancement_types,
            quality_threshold=request.quality_threshold
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return ServiceResponse(
            success=True,
            message="Content enhanced successfully",
            data=result,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"Failed to enhance content: {e}")
        return ServiceResponse(
            success=False,
            message=f"Enhancement failed: {str(e)}"
        )

@app.post("/batch/generate", response_model=ServiceResponse)
async def batch_generate_content(
    request: BatchGenerationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate multiple content pieces in batch"""
    try:
        if not multimodal_generator or not multimodal_generator.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Multimodal generator is not ready"
            )
        
        batch_id = str(uuid.uuid4())
        
        # Start background batch task
        background_tasks.add_task(
            _batch_generate_content_task,
            batch_id,
            request.model_dump()
        )
        
        return ServiceResponse(
            success=True,
            message="Batch generation started",
            task_id=batch_id
        )
        
    except Exception as e:
        logger.error(f"Failed to start batch generation: {e}")
        return ServiceResponse(
            success=False,
            message=f"Batch generation failed: {str(e)}"
        )

@app.get("/batch/status/{batch_id}", response_model=ServiceResponse)
async def get_batch_status(
    batch_id: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Get batch generation status"""
    try:
        if not content_processor:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service not ready"
            )
        
        status_info = await content_processor.get_batch_status(batch_id)
        
        return ServiceResponse(
            success=True,
            message="Batch status retrieved",
            data=status_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get batch status: {e}")
        return ServiceResponse(
            success=False,
            message=f"Failed to get status: {str(e)}"
        )

@app.get("/task/{task_id}", response_model=ServiceResponse)
async def get_task_status(
    task_id: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Get generation task status and results"""
    try:
        if not content_processor:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service not ready"
            )
        
        task_info = await content_processor.get_task_status(task_id)
        
        return ServiceResponse(
            success=True,
            message="Task status retrieved",
            data=task_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get task status: {e}")
        return ServiceResponse(
            success=False,
            message=f"Failed to get task status: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """Service health check endpoint"""
    try:
        health_status = {
            "service": "multimodal-generator",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "multimodal_generator": multimodal_generator.is_ready if multimodal_generator else False,
                "content_processor": content_processor.is_ready if content_processor else False,
                "event_client": event_client.is_connected if event_client else False,
                "service_registry": service_registry.is_registered if service_registry else False
            }
        }
        
        # Check if all critical components are ready
        critical_components = ["multimodal_generator", "content_processor"]
        all_ready = all(health_status["components"].get(comp, False) for comp in critical_components)
        
        if not all_ready:
            health_status["status"] = "degraded"
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content=health_status
            )
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "service": "multimodal-generator",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/ready")
async def readiness_check():
    """Service readiness check endpoint"""
    try:
        if not multimodal_generator or not multimodal_generator.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Multimodal generator not ready"
            )
        
        if not content_processor or not content_processor.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Content processor not ready"
            )
        
        return {
            "service": "multimodal-generator",
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )

@app.get("/status")
async def get_service_status():
    """Get detailed service status and metrics"""
    try:
        metrics = get_metrics()
        
        status_info = {
            "service": "multimodal-generator",
            "uptime": metrics.get("uptime", 0),
            "requests_processed": metrics.get("requests_total", 0),
            "active_tasks": content_processor.get_active_task_count() if content_processor else 0,
            "error_rate": metrics.get("error_rate", 0),
            "average_response_time": metrics.get("avg_response_time", 0),
            "timestamp": datetime.now().isoformat()
        }
        
        return status_info
        
    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get status: {str(e)}"
        )

# =============================================================================
# Background Task Functions
# =============================================================================

async def _generate_multimodal_content_task(task_id: str, request_data: Dict[str, Any]):
    """Background task for multimodal content generation"""
    try:
        if not multimodal_generator:
            return
        
        # Publish task started event
        if event_client:
            await event_client.publish_event(
                "multimodal.generation.started",
                {"task_id": task_id, "request": request_data}
            )
        
        # Generate content
        result = await multimodal_generator.generate_multimodal_content(
            content_brief=request_data["content_brief"],
            modalities=request_data.get("modalities", ["text"]),
            style_guide=request_data.get("style_guide"),
            target_format=request_data.get("target_format", "web"),
            content_length=request_data.get("content_length", "medium"),
            target_audience=request_data.get("target_audience", "general"),
            tone=request_data.get("tone", "professional"),
            include_metadata=request_data.get("include_metadata", True)
        )
        
        # Store result and publish completion event
        if content_processor:
            await content_processor.store_task_result(task_id, result.model_dump())
        
        if event_client:
            await event_client.publish_event(
                "multimodal.generation.completed",
                {"task_id": task_id, "result": result.model_dump()}
            )
        
    except Exception as e:
        logger.error(f"Multimodal generation task {task_id} failed: {e}")
        
        if event_client:
            await event_client.publish_event(
                "multimodal.generation.failed",
                {"task_id": task_id, "error": str(e)}
            )

async def _batch_generate_content_task(batch_id: str, request_data: Dict[str, Any]):
    """Background task for batch content generation"""
    try:
        if not multimodal_generator or not content_processor:
            return
        
        # Publish batch started event
        if event_client:
            await event_client.publish_event(
                "multimodal.batch.started",
                {"batch_id": batch_id, "request": request_data}
            )
        
        # Process batch
        results = await content_processor.process_batch_generation(
            batch_id=batch_id,
            generation_requests=request_data["generation_requests"],
            batch_priority=request_data.get("batch_priority", "normal"),
            parallel_processing=request_data.get("parallel_processing", True)
        )
        
        # Publish batch completion event
        if event_client:
            await event_client.publish_event(
                "multimodal.batch.completed",
                {"batch_id": batch_id, "results": results}
            )
        
    except Exception as e:
        logger.error(f"Batch generation {batch_id} failed: {e}")
        
        if event_client:
            await event_client.publish_event(
                "multimodal.batch.failed",
                {"batch_id": batch_id, "error": str(e)}
            )

# =============================================================================
# Application Entry Point
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("SERVICE_PORT", "8088"))
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )