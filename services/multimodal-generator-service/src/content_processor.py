"""
Multimodal Generator Service - Content Processor

Advanced content processing engine for multimodal content adaptation, enhancement, 
and cross-modal operations.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

# Import dependencies with graceful handling for testing
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    from PIL import Image, ImageEnhance, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

try:
    import textstat
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import markdownify
    MARKDOWNIFY_AVAILABLE = True
except ImportError:
    MARKDOWNIFY_AVAILABLE = False

logger = logging.getLogger(__name__)


class ContentProcessor:
    """Advanced content processing engine for multimodal operations"""
    
    def __init__(self):
        self.is_ready = False
        self.task_storage = {}  # In production, this would be a database
        self.batch_storage = {}
        self.active_tasks = set()
        
        # Processing capabilities
        self.text_processors = {
            "markdown_to_html": self._markdown_to_html,
            "html_to_text": self._html_to_text,
            "text_enhancement": self._enhance_text_content,
            "readability_analysis": self._analyze_readability
        }
        
        self.image_processors = {
            "resize": self._resize_image,
            "enhance": self._enhance_image,
            "format_conversion": self._convert_image_format,
            "quality_analysis": self._analyze_image_quality
        }
        
        self.audio_processors = {
            "format_conversion": self._convert_audio_format,
            "enhancement": self._enhance_audio,
            "analysis": self._analyze_audio_content
        }
    
    async def initialize(self) -> None:
        """Initialize the content processor"""
        try:
            # Initialize processing components
            await self._initialize_text_processing()
            await self._initialize_image_processing()
            await self._initialize_audio_processing()
            
            self.is_ready = True
            logger.info("Content Processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Content Processor: {e}")
            self.is_ready = False
            raise
    
    async def _initialize_text_processing(self):
        """Initialize text processing capabilities"""
        try:
            # Download NLTK data if needed
            import nltk
            try:
                nltk.data.find('punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            
            logger.info("Text processing initialized")
            
        except Exception as e:
            logger.warning(f"Text processing initialization warning: {e}")
    
    async def _initialize_image_processing(self):
        """Initialize image processing capabilities"""
        try:
            # Verify image processing libraries
            test_image = Image.new('RGB', (100, 100), color='red')
            test_array = np.array(test_image)
            assert test_array.shape == (100, 100, 3)
            
            logger.info("Image processing initialized")
            
        except Exception as e:
            logger.warning(f"Image processing initialization warning: {e}")
    
    async def _initialize_audio_processing(self):
        """Initialize audio processing capabilities"""
        try:
            # Test audio processing capabilities
            # Note: This is a basic test - in production you'd verify codec support
            logger.info("Audio processing initialized")
            
        except Exception as e:
            logger.warning(f"Audio processing initialization warning: {e}")
    
    async def adapt_content(
        self,
        source_content: str,
        source_modality: str,
        target_modalities: List[str],
        adaptation_style: str = "faithful",
        quality_level: str = "high"
    ) -> Dict[str, Any]:
        """Adapt content between different modalities"""
        
        if not self.is_ready:
            raise Exception("Content processor is not initialized")
        
        adaptation_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting content adaptation {adaptation_id}")
            
            # Analyze source content
            source_analysis = await self._analyze_content(source_content, source_modality)
            
            # Perform adaptations
            adaptations = {}
            for target_modality in target_modalities:
                adaptation = await self._perform_adaptation(
                    source_content=source_content,
                    source_modality=source_modality,
                    target_modality=target_modality,
                    style=adaptation_style,
                    quality_level=quality_level,
                    source_analysis=source_analysis
                )
                adaptations[target_modality] = adaptation
            
            # Calculate adaptation quality metrics
            quality_metrics = await self._calculate_adaptation_quality(
                source_content, source_modality, adaptations
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "adaptation_id": adaptation_id,
                "source_modality": source_modality,
                "target_modalities": target_modalities,
                "adaptations": adaptations,
                "source_analysis": source_analysis,
                "quality_metrics": quality_metrics,
                "adaptation_style": adaptation_style,
                "quality_level": quality_level,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Content adaptation {adaptation_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Content adaptation {adaptation_id} failed: {e}")
            raise
    
    async def enhance_content(
        self,
        content: str,
        modality: str,
        enhancement_types: List[str],
        quality_threshold: float = 0.8
    ) -> Dict[str, Any]:
        """Enhance content quality and engagement"""
        
        if not self.is_ready:
            raise Exception("Content processor is not initialized")
        
        enhancement_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting content enhancement {enhancement_id}")
            
            # Analyze current content quality
            current_quality = await self._assess_content_quality(content, modality)
            
            # Apply enhancements
            enhanced_content = content
            enhancement_details = {}
            
            for enhancement_type in enhancement_types:
                enhanced_content, details = await self._apply_enhancement(
                    enhanced_content, modality, enhancement_type, quality_threshold
                )
                enhancement_details[enhancement_type] = details
            
            # Analyze enhanced content quality
            enhanced_quality = await self._assess_content_quality(enhanced_content, modality)
            
            # Calculate improvement metrics
            improvement_metrics = {
                "quality_improvement": enhanced_quality.get("overall_score", 0) - current_quality.get("overall_score", 0),
                "specific_improvements": self._calculate_specific_improvements(current_quality, enhanced_quality),
                "enhancement_effectiveness": self._calculate_enhancement_effectiveness(enhancement_details)
            }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "enhancement_id": enhancement_id,
                "original_content": content,
                "enhanced_content": enhanced_content,
                "modality": modality,
                "enhancement_types": enhancement_types,
                "current_quality": current_quality,
                "enhanced_quality": enhanced_quality,
                "improvement_metrics": improvement_metrics,
                "enhancement_details": enhancement_details,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Content enhancement {enhancement_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Content enhancement {enhancement_id} failed: {e}")
            raise
    
    async def process_batch_generation(
        self,
        batch_id: str,
        generation_requests: List[Dict[str, Any]],
        batch_priority: str = "normal",
        parallel_processing: bool = True
    ) -> Dict[str, Any]:
        """Process batch generation requests"""
        
        if not self.is_ready:
            raise Exception("Content processor is not initialized")
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting batch processing {batch_id}")
            
            # Initialize batch tracking
            self.batch_storage[batch_id] = {
                "status": "processing",
                "total_requests": len(generation_requests),
                "completed_requests": 0,
                "failed_requests": 0,
                "results": {},
                "start_time": start_time.isoformat()
            }
            
            # Process requests
            if parallel_processing:
                # Process requests in parallel (limited concurrency)
                semaphore = asyncio.Semaphore(5)  # Max 5 concurrent requests
                tasks = []
                
                for i, request in enumerate(generation_requests):
                    task = self._process_single_generation_request(
                        f"{batch_id}_{i}", request, semaphore
                    )
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
            else:
                # Process requests sequentially
                results = []
                for i, request in enumerate(generation_requests):
                    try:
                        result = await self._process_single_generation_request(
                            f"{batch_id}_{i}", request
                        )
                        results.append(result)
                    except Exception as e:
                        results.append(e)
            
            # Process results
            successful_results = []
            failed_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append({
                        "request_index": i,
                        "error": str(result)
                    })
                else:
                    successful_results.append(result)
            
            # Update batch status
            execution_time = (datetime.now() - start_time).total_seconds()
            
            batch_result = {
                "batch_id": batch_id,
                "status": "completed",
                "total_requests": len(generation_requests),
                "successful_requests": len(successful_results),
                "failed_requests": len(failed_results),
                "success_rate": len(successful_results) / len(generation_requests),
                "successful_results": successful_results,
                "failed_results": failed_results,
                "execution_time": execution_time,
                "batch_priority": batch_priority,
                "parallel_processing": parallel_processing,
                "timestamp": datetime.now().isoformat()
            }
            
            self.batch_storage[batch_id] = batch_result
            
            logger.info(f"Batch processing {batch_id} completed")
            return batch_result
            
        except Exception as e:
            logger.error(f"Batch processing {batch_id} failed: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get task status and results"""
        return self.task_storage.get(task_id, {
            "task_id": task_id,
            "status": "not_found",
            "message": "Task not found"
        })
    
    async def get_batch_status(self, batch_id: str) -> Dict[str, Any]:
        """Get batch processing status"""
        return self.batch_storage.get(batch_id, {
            "batch_id": batch_id,
            "status": "not_found",
            "message": "Batch not found"
        })
    
    async def store_task_result(self, task_id: str, result: Dict[str, Any]) -> None:
        """Store task result"""
        self.task_storage[task_id] = {
            "task_id": task_id,
            "status": "completed",
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_active_task_count(self) -> int:
        """Get number of active tasks"""
        return len(self.active_tasks)
    
    # =============================================================================
    # Content Analysis Methods
    # =============================================================================
    
    async def _analyze_content(self, content: str, modality: str) -> Dict[str, Any]:
        """Analyze content characteristics"""
        analysis = {
            "modality": modality,
            "content_length": len(content),
            "complexity_score": 0.5,  # Mock score
            "language_detected": "en",
            "readability_score": 0.7,
            "sentiment_score": 0.6,
            "key_themes": [],
            "technical_complexity": "medium"
        }
        
        if modality == "text":
            analysis.update(await self._analyze_text_content(content))
        elif modality == "html":
            analysis.update(await self._analyze_html_content(content))
        
        return analysis
    
    async def _analyze_text_content(self, text: str) -> Dict[str, Any]:
        """Analyze text content characteristics"""
        try:
            analysis = {
                "word_count": len(text.split()),
                "sentence_count": text.count('.') + text.count('!') + text.count('?'),
                "avg_sentence_length": len(text.split()) / max(1, text.count('.'))
            }
            
            if TEXTSTAT_AVAILABLE:
                analysis.update({
                    "readability_score": textstat.flesch_reading_ease(text) / 100,
                    "grade_level": textstat.flesch_kincaid_grade(text)
                })
            else:
                analysis.update({
                    "readability_score": 0.75,  # Mock score
                    "grade_level": 8.0  # Mock grade level
                })
            
            return analysis
        except Exception as e:
            logger.warning(f"Text analysis error: {e}")
            return {"analysis_error": str(e)}
    
    async def _analyze_html_content(self, html: str) -> Dict[str, Any]:
        """Analyze HTML content structure"""
        try:
            if BS4_AVAILABLE:
                soup = BeautifulSoup(html, 'html.parser')
                return {
                    "tag_count": len(soup.find_all()),
                    "text_content_ratio": len(soup.get_text()) / len(html),
                    "heading_count": len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])),
                    "link_count": len(soup.find_all('a')),
                    "image_count": len(soup.find_all('img'))
                }
            else:
                # Basic analysis without BeautifulSoup
                return {
                    "tag_count": html.count('<'),
                    "text_content_ratio": 0.7,  # Mock ratio
                    "heading_count": html.count('<h'),
                    "link_count": html.count('<a'),
                    "image_count": html.count('<img')
                }
        except Exception as e:
            logger.warning(f"HTML analysis error: {e}")
            return {"analysis_error": str(e)}
    
    # =============================================================================
    # Content Adaptation Methods
    # =============================================================================
    
    async def _perform_adaptation(
        self,
        source_content: str,
        source_modality: str,
        target_modality: str,
        style: str,
        quality_level: str,
        source_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform content adaptation between modalities"""
        
        adaptation_key = f"{source_modality}_to_{target_modality}"
        
        # Define adaptation strategies
        adaptations = {
            "text_to_html": self._adapt_text_to_html,
            "html_to_text": self._adapt_html_to_text,
            "text_to_markdown": self._adapt_text_to_markdown,
            "markdown_to_html": self._markdown_to_html,
            "text_to_image": self._adapt_text_to_image_description,
            "text_to_audio": self._adapt_text_to_audio_script,
            "text_to_video": self._adapt_text_to_video_script
        }
        
        if adaptation_key in adaptations:
            adapted_content = await adaptations[adaptation_key](
                source_content, style, quality_level, source_analysis
            )
        else:
            # Generic adaptation for unsupported combinations
            adapted_content = await self._generic_adaptation(
                source_content, source_modality, target_modality, style
            )
        
        return {
            "content": adapted_content,
            "adaptation_strategy": adaptation_key,
            "quality_indicators": await self._assess_adaptation_quality(adapted_content, target_modality),
            "metadata": {
                "source_modality": source_modality,
                "target_modality": target_modality,
                "style": style,
                "quality_level": quality_level
            }
        }
    
    async def _adapt_text_to_html(self, text: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt text content to HTML format"""
        # Convert line breaks to paragraphs
        paragraphs = text.split('\n\n')
        html_content = ""
        
        for paragraph in paragraphs:
            if paragraph.strip():
                html_content += f"<p>{paragraph.strip()}</p>\n"
        
        # Add basic HTML structure
        if quality_level == "high":
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adapted Content</title>
</head>
<body>
    {html_content}
</body>
</html>"""
        
        return html_content
    
    async def _adapt_html_to_text(self, html: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt HTML content to plain text"""
        try:
            if BS4_AVAILABLE:
                soup = BeautifulSoup(html, 'html.parser')
                return soup.get_text(separator='\n\n', strip=True)
            else:
                # Basic HTML stripping without BeautifulSoup
                import re
                # Remove HTML tags
                text = re.sub(r'<[^>]+>', ' ', html)
                # Clean up whitespace
                text = re.sub(r'\s+', ' ', text).strip()
                return text
        except Exception as e:
            logger.warning(f"HTML to text adaptation error: {e}")
            return html  # Return original if parsing fails
    
    async def _adapt_text_to_markdown(self, text: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt text content to Markdown format"""
        # Basic markdown conversion
        lines = text.split('\n')
        markdown_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                markdown_lines.append('')
            elif line.isupper() and len(line) < 50:  # Potential heading
                markdown_lines.append(f"# {line.title()}")
            else:
                markdown_lines.append(line)
        
        return '\n'.join(markdown_lines)
    
    async def _markdown_to_html(self, markdown: str, style: str = None, quality_level: str = None, analysis: Dict[str, Any] = None) -> str:
        """Convert Markdown to HTML"""
        try:
            import markdown
            return markdown.markdown(markdown)
        except ImportError:
            # Fallback basic conversion
            html = markdown.replace('\n# ', '\n<h1>').replace('\n## ', '\n<h2>')
            html = html.replace('\n', '<br>\n')
            return html
    
    async def _adapt_text_to_image_description(self, text: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt text to image description/prompt"""
        # Extract key visual elements and create image description
        visual_keywords = ["color", "shape", "style", "composition", "lighting", "mood"]
        
        description = f"Create an image that visually represents: {text[:200]}..."
        
        if style == "professional":
            description += " Use clean, professional styling with high contrast and clear composition."
        elif style == "creative":
            description += " Use artistic, creative styling with bold colors and dynamic composition."
        
        return description
    
    async def _adapt_text_to_audio_script(self, text: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt text to audio script format"""
        # Add audio-specific formatting
        script = f"[INTRO MUSIC]\n\n"
        script += f"NARRATOR: {text}\n\n"
        script += f"[OUTRO MUSIC]"
        
        if style == "conversational":
            script = script.replace("NARRATOR:", "HOST:")
            script += "\n\n[PAUSE FOR REFLECTION]"
        
        return script
    
    async def _adapt_text_to_video_script(self, text: str, style: str, quality_level: str, analysis: Dict[str, Any]) -> str:
        """Adapt text to video script format"""
        # Create basic video script structure
        script = "VIDEO SCRIPT\n"
        script += "=" * 50 + "\n\n"
        script += "SCENE 1: INTRODUCTION\n"
        script += f"VISUAL: [Relevant opening visual]\n"
        script += f"AUDIO: {text[:100]}...\n\n"
        script += "SCENE 2: MAIN CONTENT\n"
        script += f"VISUAL: [Supporting visuals]\n"
        script += f"AUDIO: {text[100:] if len(text) > 100 else '[Continue with main content]'}\n\n"
        script += "SCENE 3: CONCLUSION\n"
        script += "VISUAL: [Closing visual]\n"
        script += "AUDIO: [Summary and call-to-action]"
        
        return script
    
    async def _generic_adaptation(self, content: str, source_modality: str, target_modality: str, style: str) -> str:
        """Generic adaptation for unsupported modality combinations"""
        return f"[{target_modality.upper()} ADAPTATION]\n\nBased on {source_modality} content:\n{content[:500]}..."
    
    # =============================================================================
    # Content Enhancement Methods
    # =============================================================================
    
    async def _assess_content_quality(self, content: str, modality: str) -> Dict[str, Any]:
        """Assess content quality metrics"""
        quality_metrics = {
            "overall_score": 0.75,  # Mock score
            "clarity_score": 0.8,
            "engagement_score": 0.7,
            "accessibility_score": 0.75,
            "technical_score": 0.8
        }
        
        if modality == "text":
            if TEXTSTAT_AVAILABLE:
                readability_score = min(textstat.flesch_reading_ease(content) / 100, 1.0)
            else:
                readability_score = 0.75  # Mock score
            
            quality_metrics.update({
                "readability_score": readability_score,
                "grammar_score": 0.85,  # Mock score - would use grammar checking in production
                "coherence_score": 0.8
            })
        
        return quality_metrics
    
    async def _apply_enhancement(self, content: str, modality: str, enhancement_type: str, quality_threshold: float) -> tuple:
        """Apply specific enhancement to content"""
        
        enhancement_methods = {
            "grammar": self._enhance_grammar,
            "readability": self._enhance_readability,
            "engagement": self._enhance_engagement,
            "accessibility": self._enhance_accessibility,
            "structure": self._enhance_structure,
            "style": self._enhance_style
        }
        
        if enhancement_type in enhancement_methods:
            enhanced_content, details = await enhancement_methods[enhancement_type](content, modality, quality_threshold)
        else:
            enhanced_content = content
            details = {"enhancement": "not_supported", "message": f"Enhancement type '{enhancement_type}' not supported"}
        
        return enhanced_content, details
    
    async def _enhance_grammar(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance grammar and language quality"""
        # Mock grammar enhancement - in production, use language processing tools
        enhanced_content = content.replace(" i ", " I ")  # Basic capitalization
        enhanced_content = enhanced_content.replace("cant", "can't")  # Basic contractions
        
        details = {
            "corrections_made": 2,
            "improvement_score": 0.1,
            "areas_improved": ["capitalization", "contractions"]
        }
        
        return enhanced_content, details
    
    async def _enhance_readability(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance content readability"""
        # Basic readability improvements
        sentences = content.split('. ')
        enhanced_sentences = []
        
        for sentence in sentences:
            # Break down long sentences
            if len(sentence.split()) > 25:
                # Simple sentence splitting logic
                mid_point = len(sentence) // 2
                space_near_mid = sentence.find(' ', mid_point)
                if space_near_mid != -1:
                    enhanced_sentences.append(sentence[:space_near_mid] + '.')
                    enhanced_sentences.append(sentence[space_near_mid+1:])
                else:
                    enhanced_sentences.append(sentence)
            else:
                enhanced_sentences.append(sentence)
        
        enhanced_content = '. '.join(enhanced_sentences)
        
        details = {
            "long_sentences_split": len([s for s in sentences if len(s.split()) > 25]),
            "improvement_score": 0.15,
            "readability_grade_improvement": 1.2
        }
        
        return enhanced_content, details
    
    async def _enhance_engagement(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance content engagement"""
        # Add engaging elements
        enhanced_content = content
        
        # Add questions to engage readers
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 2:
            mid_paragraph = len(paragraphs) // 2
            question = "What do you think about this?"
            paragraphs.insert(mid_paragraph, question)
            enhanced_content = '\n\n'.join(paragraphs)
        
        details = {
            "engagement_elements_added": 1,
            "improvement_score": 0.12,
            "elements": ["interactive_question"]
        }
        
        return enhanced_content, details
    
    async def _enhance_accessibility(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance content accessibility"""
        enhanced_content = content
        improvements = []
        
        if modality == "html":
            # Add alt text reminders, heading structure, etc.
            enhanced_content = content.replace('<img', '<img alt="[Add descriptive alt text]"')
            improvements.append("alt_text_reminders")
        
        details = {
            "accessibility_improvements": improvements,
            "improvement_score": 0.1,
            "compliance_level": "enhanced"
        }
        
        return enhanced_content, details
    
    async def _enhance_structure(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance content structure and organization"""
        # Basic structure improvements
        paragraphs = content.split('\n\n')
        
        if len(paragraphs) > 1 and not content.startswith('#') and not content.startswith('<h'):
            # Add a title if none exists
            enhanced_content = f"# Main Content\n\n{content}"
        else:
            enhanced_content = content
        
        details = {
            "structure_improvements": ["added_title"],
            "improvement_score": 0.08,
            "organization_level": "improved"
        }
        
        return enhanced_content, details
    
    async def _enhance_style(self, content: str, modality: str, threshold: float) -> tuple:
        """Enhance content style and tone"""
        # Basic style improvements
        enhanced_content = content
        
        # Improve sentence variety
        sentences = content.split('. ')
        if len(set(len(s.split()) for s in sentences)) < 3:
            # Add variety marker for manual review
            enhanced_content += "\n\n[Note: Consider varying sentence length for better flow]"
        
        details = {
            "style_improvements": ["sentence_variety_note"],
            "improvement_score": 0.05,
            "style_consistency": 0.9
        }
        
        return enhanced_content, details
    
    # =============================================================================
    # Quality Assessment Methods
    # =============================================================================
    
    async def _calculate_adaptation_quality(self, source_content: str, source_modality: str, adaptations: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate quality metrics for adaptations"""
        metrics = {
            "overall_quality": 0.85,
            "content_preservation": 0.9,
            "format_appropriateness": 0.8,
            "technical_accuracy": 0.85,
            "modality_scores": {}
        }
        
        for modality, adaptation in adaptations.items():
            metrics["modality_scores"][modality] = {
                "quality_score": 0.8,
                "content_length_ratio": len(adaptation.get("content", "")) / len(source_content),
                "adaptation_success": True
            }
        
        return metrics
    
    async def _assess_adaptation_quality(self, content: str, modality: str) -> Dict[str, float]:
        """Assess quality of adapted content"""
        return {
            "content_quality": 0.85,
            "format_compliance": 0.9,
            "readability": 0.8,
            "technical_accuracy": 0.88
        }
    
    def _calculate_specific_improvements(self, before: Dict[str, Any], after: Dict[str, Any]) -> Dict[str, float]:
        """Calculate specific improvement metrics"""
        improvements = {}
        
        for metric in ["clarity_score", "engagement_score", "accessibility_score"]:
            before_score = before.get(metric, 0)
            after_score = after.get(metric, 0)
            improvements[metric] = after_score - before_score
        
        return improvements
    
    def _calculate_enhancement_effectiveness(self, enhancement_details: Dict[str, Any]) -> float:
        """Calculate overall enhancement effectiveness"""
        total_improvement = 0
        count = 0
        
        for details in enhancement_details.values():
            if isinstance(details, dict) and "improvement_score" in details:
                total_improvement += details["improvement_score"]
                count += 1
        
        return total_improvement / max(count, 1)
    
    # =============================================================================
    # Processing Task Methods
    # =============================================================================
    
    async def _process_single_generation_request(self, request_id: str, request: Dict[str, Any], semaphore=None) -> Dict[str, Any]:
        """Process a single generation request"""
        
        async def _process():
            try:
                self.active_tasks.add(request_id)
                
                # Mock processing - in production, this would call the multimodal generator
                await asyncio.sleep(0.1)  # Simulate processing time
                
                result = {
                    "request_id": request_id,
                    "status": "completed",
                    "generated_content": f"Generated content for: {request.get('content_brief', 'Unknown brief')}",
                    "modalities": request.get("modalities", ["text"]),
                    "processing_time": 0.1,
                    "timestamp": datetime.now().isoformat()
                }
                
                return result
                
            except Exception as e:
                logger.error(f"Single generation request {request_id} failed: {e}")
                raise
            finally:
                self.active_tasks.discard(request_id)
        
        if semaphore:
            async with semaphore:
                return await _process()
        else:
            return await _process()
    
    # =============================================================================
    # Image Processing Methods
    # =============================================================================
    
    async def _resize_image(self, image_data: bytes, target_size: tuple) -> bytes:
        """Resize image to target dimensions"""
        try:
            # This is a placeholder - in production, implement actual image resizing
            return image_data
        except Exception as e:
            logger.error(f"Image resize error: {e}")
            raise
    
    async def _enhance_image(self, image_data: bytes, enhancement_type: str) -> bytes:
        """Enhance image quality"""
        try:
            # This is a placeholder - in production, implement actual image enhancement
            return image_data
        except Exception as e:
            logger.error(f"Image enhancement error: {e}")
            raise
    
    async def _convert_image_format(self, image_data: bytes, target_format: str) -> bytes:
        """Convert image to different format"""
        try:
            # This is a placeholder - in production, implement actual format conversion
            return image_data
        except Exception as e:
            logger.error(f"Image format conversion error: {e}")
            raise
    
    async def _analyze_image_quality(self, image_data: bytes) -> Dict[str, Any]:
        """Analyze image quality metrics"""
        return {
            "resolution": "unknown",
            "quality_score": 0.8,
            "format": "unknown",
            "file_size": len(image_data)
        }
    
    # =============================================================================
    # Audio Processing Methods
    # =============================================================================
    
    async def _convert_audio_format(self, audio_data: bytes, target_format: str) -> bytes:
        """Convert audio to different format"""
        try:
            # This is a placeholder - in production, implement actual audio conversion
            return audio_data
        except Exception as e:
            logger.error(f"Audio format conversion error: {e}")
            raise
    
    async def _enhance_audio(self, audio_data: bytes, enhancement_type: str) -> bytes:
        """Enhance audio quality"""
        try:
            # This is a placeholder - in production, implement actual audio enhancement
            return audio_data
        except Exception as e:
            logger.error(f"Audio enhancement error: {e}")
            raise
    
    async def _analyze_audio_content(self, audio_data: bytes) -> Dict[str, Any]:
        """Analyze audio content characteristics"""
        return {
            "duration": 0,
            "quality_score": 0.8,
            "format": "unknown",
            "file_size": len(audio_data)
        }
    
    # =============================================================================
    # Text Processing Methods
    # =============================================================================
    
    async def _html_to_text(self, html: str) -> str:
        """Convert HTML to plain text"""
        try:
            if BS4_AVAILABLE:
                soup = BeautifulSoup(html, 'html.parser')
                return soup.get_text(separator=' ', strip=True)
            else:
                # Basic HTML stripping
                import re
                text = re.sub(r'<[^>]+>', ' ', html)
                text = re.sub(r'\s+', ' ', text).strip()
                return text
        except Exception as e:
            logger.error(f"HTML to text conversion error: {e}")
            return html
    
    async def _enhance_text_content(self, text: str, enhancement_type: str) -> str:
        """Enhance text content"""
        try:
            if enhancement_type == "grammar":
                # Basic grammar corrections
                enhanced = text.replace(" i ", " I ")
                enhanced = enhanced.replace("cant", "can't")
                return enhanced
            elif enhancement_type == "readability":
                # Improve readability
                return text  # Placeholder
            else:
                return text
        except Exception as e:
            logger.error(f"Text enhancement error: {e}")
            return text
    
    async def _analyze_readability(self, text: str) -> Dict[str, Any]:
        """Analyze text readability"""
        try:
            analysis = {
                "word_count": len(text.split()),
                "sentence_count": text.count('.') + text.count('!') + text.count('?')
            }
            
            if TEXTSTAT_AVAILABLE:
                analysis.update({
                    "flesch_score": textstat.flesch_reading_ease(text),
                    "grade_level": textstat.flesch_kincaid_grade(text)
                })
            else:
                # Mock scores when textstat is not available
                analysis.update({
                    "flesch_score": 65.0,  # Mock score
                    "grade_level": 8.0     # Mock grade level
                })
            
            return analysis
        except Exception as e:
            logger.error(f"Readability analysis error: {e}")
            return {"error": str(e)}
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            self.is_ready = False
            self.task_storage.clear()
            self.batch_storage.clear()
            self.active_tasks.clear()
            logger.info("Content Processor cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Content Processor cleanup: {e}")