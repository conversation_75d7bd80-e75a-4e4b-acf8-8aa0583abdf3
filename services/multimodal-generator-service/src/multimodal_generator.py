"""
Multimodal Generator Service - PydanticAI Agent

AI-powered multimodal content generation with cross-modal integration and style adaptation.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from pydantic import BaseModel, Field
from pydantic_ai import Agent

logger = logging.getLogger(__name__)


# =============================================================================
# Response Models
# =============================================================================

class MultimodalContentResult(BaseModel):
    """Result model for multimodal content generation"""
    content_id: str = Field(description="Unique identifier for generated content")
    content_brief: str = Field(description="Original content brief")
    generated_content: Dict[str, Any] = Field(description="Generated content by modality")
    metadata: Dict[str, Any] = Field(description="Generation metadata and analytics")
    quality_scores: Dict[str, float] = Field(description="Quality scores by modality")
    style_analysis: Dict[str, Any] = Field(description="Style analysis and consistency metrics")
    cross_modal_coherence: float = Field(description="Cross-modal coherence score")
    generation_time: float = Field(description="Total generation time in seconds")
    model_used: str = Field(description="AI model used for generation")
    recommendations: List[str] = Field(description="Improvement recommendations")


# =============================================================================
# Multimodal Generator Agent
# =============================================================================

class MultimodalGenerator:
    """AI-powered multimodal content generation agent"""
    
    def __init__(self):
        self.agent: Optional[Agent] = None
        self.is_ready = False
        self.supported_modalities = [
            "text", "image", "audio", "video", "interactive", "structured"
        ]
        self.supported_formats = [
            "web", "mobile", "print", "social", "presentation", "document"
        ]
        
    async def initialize(self) -> None:
        """Initialize the multimodal generator agent"""
        try:
            # Determine which AI model to use
            model = self._get_ai_model()
            if not model:
                raise Exception("No AI model available - check API keys")
            
            # Initialize PydanticAI agent
            self.agent = Agent(
                model=model,
                result_type=MultimodalContentResult,
                system_prompt=self._get_system_prompt()
            )
            
            self.is_ready = True
            logger.info("Multimodal Generator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Multimodal Generator: {e}")
            self.is_ready = False
            raise
    
    def _get_ai_model(self):
        """Get available AI model based on API keys"""
        from pydantic_ai.models.openai import OpenAIModel
        from pydantic_ai.models.anthropic import AnthropicModel
        
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        elif os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for multimodal content generation"""
        return """You are an expert multimodal content creator with deep expertise in:

1. CONTENT MODALITIES:
   - Text: Articles, blogs, scripts, copy, technical documentation
   - Image: Visual concepts, image descriptions, graphic design specifications
   - Audio: Podcast scripts, voiceover content, sound design concepts
   - Video: Storyboards, video scripts, visual sequences
   - Interactive: UI/UX content, interactive elements, user flows
   - Structured: Data formats, schema definitions, API specifications

2. CROSS-MODAL INTEGRATION:
   - Ensure coherent narrative across all modalities
   - Maintain consistent tone, style, and messaging
   - Create complementary content that enhances the overall experience
   - Adapt core messages for optimal expression in each modality

3. STYLE ADAPTATION:
   - Professional: Formal, authoritative, data-driven
   - Casual: Conversational, approachable, relatable
   - Creative: Artistic, experimental, innovative
   - Educational: Clear, structured, informative
   - Persuasive: Compelling, action-oriented, benefits-focused

4. TARGET FORMATS:
   - Web: Responsive design, accessibility, SEO optimization
   - Mobile: Touch-friendly, concise, vertical layouts
   - Print: High-resolution, CMYK considerations, typography
   - Social: Platform-specific optimization, engagement-focused
   - Presentation: Clear hierarchy, visual emphasis, audience flow
   - Document: Professional formatting, readability, structure

5. QUALITY STANDARDS:
   - Accuracy: Factually correct, properly researched
   - Clarity: Easy to understand, well-structured
   - Engagement: Compelling, audience-appropriate
   - Consistency: Unified voice and visual identity
   - Accessibility: Inclusive design principles
   - Performance: Optimized for target platforms

Your task is to create comprehensive multimodal content that:
- Fulfills the specified content brief completely
- Maintains high quality across all requested modalities
- Ensures cross-modal coherence and consistency
- Adapts appropriately to the target format and audience
- Provides actionable implementation guidance
- Includes optimization recommendations

Always provide detailed specifications for each modality, including technical requirements, 
creative direction, and quality metrics. Consider user experience, accessibility, and 
performance optimization in all recommendations."""

    async def generate_multimodal_content(
        self,
        content_brief: str,
        modalities: List[str] = None,
        style_guide: Optional[Dict[str, Any]] = None,
        target_format: str = "web",
        content_length: str = "medium",
        target_audience: str = "general",
        tone: str = "professional",
        include_metadata: bool = True,
        progress_callback: Optional[Callable[[str], None]] = None
    ) -> MultimodalContentResult:
        """Generate comprehensive multimodal content"""
        
        if not self.is_ready or not self.agent:
            raise Exception("Multimodal generator is not initialized")
        
        start_time = datetime.now()
        
        # Validate modalities
        modalities = modalities or ["text"]
        invalid_modalities = [m for m in modalities if m not in self.supported_modalities]
        if invalid_modalities:
            raise ValueError(f"Unsupported modalities: {invalid_modalities}")
        
        # Validate target format
        if target_format not in self.supported_formats:
            raise ValueError(f"Unsupported target format: {target_format}")
        
        try:
            if progress_callback:
                progress_callback("Preparing content generation...")
            
            # Prepare generation prompt
            prompt = self._prepare_generation_prompt(
                content_brief=content_brief,
                modalities=modalities,
                style_guide=style_guide,
                target_format=target_format,
                content_length=content_length,
                target_audience=target_audience,
                tone=tone
            )
            
            if progress_callback:
                progress_callback("Generating multimodal content...")
            
            # Generate content using AI agent
            result = await self.agent.run(prompt)
            
            if progress_callback:
                progress_callback("Processing and validating content...")
            
            # Process and enhance the result
            processed_result = self._process_generation_result(
                result=result,
                content_brief=content_brief,
                modalities=modalities,
                target_format=target_format,
                style_guide=style_guide,
                generation_time=(datetime.now() - start_time).total_seconds(),
                include_metadata=include_metadata
            )
            
            if progress_callback:
                progress_callback("Content generation completed!")
            
            return processed_result
            
        except Exception as e:
            logger.error(f"Failed to generate multimodal content: {e}")
            raise
    
    def _prepare_generation_prompt(
        self,
        content_brief: str,
        modalities: List[str],
        style_guide: Optional[Dict[str, Any]],
        target_format: str,
        content_length: str,
        target_audience: str,
        tone: str
    ) -> str:
        """Prepare the generation prompt for the AI agent"""
        
        prompt = f"""
Generate comprehensive multimodal content based on the following requirements:

CONTENT BRIEF:
{content_brief}

GENERATION PARAMETERS:
- Target Modalities: {', '.join(modalities)}
- Target Format: {target_format}
- Content Length: {content_length}
- Target Audience: {target_audience}
- Tone: {tone}

STYLE GUIDE:
{style_guide or 'No specific style guide provided - use best practices for the target audience and format'}

REQUIREMENTS:
1. Create content for each specified modality that serves the content brief
2. Ensure all modalities work together cohesively
3. Adapt content appropriately for the target format
4. Maintain consistent tone and messaging across modalities
5. Consider accessibility and user experience
6. Provide technical specifications for implementation
7. Include quality metrics and optimization recommendations

For each modality, provide:
- Main content/concept
- Technical specifications
- Implementation guidance
- Quality considerations
- Cross-modal integration notes

Generate a unique content_id and provide comprehensive metadata about the generation process.
"""
        
        return prompt
    
    def _process_generation_result(
        self,
        result: Any,
        content_brief: str,
        modalities: List[str],
        target_format: str,
        style_guide: Optional[Dict[str, Any]],
        generation_time: float,
        include_metadata: bool
    ) -> MultimodalContentResult:
        """Process and enhance the generation result"""
        
        try:
            # Extract the generated content
            generated_data = result.data if hasattr(result, 'data') else result
            
            # Calculate quality scores
            quality_scores = self._calculate_quality_scores(generated_data, modalities)
            
            # Analyze style consistency
            style_analysis = self._analyze_style_consistency(generated_data, style_guide)
            
            # Calculate cross-modal coherence
            coherence_score = self._calculate_cross_modal_coherence(generated_data, modalities)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                generated_data, quality_scores, style_analysis, coherence_score
            )
            
            # Prepare metadata
            metadata = {}
            if include_metadata:
                metadata = {
                    "generation_timestamp": datetime.now().isoformat(),
                    "modalities_count": len(modalities),
                    "target_format": target_format,
                    "content_length": self._estimate_content_length(generated_data),
                    "complexity_score": self._calculate_complexity_score(generated_data),
                    "style_guide_adherence": style_analysis.get("adherence_score", 0),
                    "technical_requirements": self._extract_technical_requirements(generated_data)
                }
            
            return MultimodalContentResult(
                content_id=f"multimodal_{int(datetime.now().timestamp())}",
                content_brief=content_brief,
                generated_content=generated_data,
                metadata=metadata,
                quality_scores=quality_scores,
                style_analysis=style_analysis,
                cross_modal_coherence=coherence_score,
                generation_time=generation_time,
                model_used=self._get_model_name(),
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Failed to process generation result: {e}")
            # Return a basic result with error information
            return MultimodalContentResult(
                content_id=f"error_{int(datetime.now().timestamp())}",
                content_brief=content_brief,
                generated_content={"error": str(e)},
                metadata={"error": True, "processing_failed": True},
                quality_scores={modality: 0.0 for modality in modalities},
                style_analysis={"error": True, "adherence_score": 0.0},
                cross_modal_coherence=0.0,
                generation_time=generation_time,
                model_used=self._get_model_name(),
                recommendations=["Address processing errors before using content"]
            )
    
    def _calculate_quality_scores(self, content: Dict[str, Any], modalities: List[str]) -> Dict[str, float]:
        """Calculate quality scores for each modality"""
        scores = {}
        
        for modality in modalities:
            # Basic quality scoring based on content analysis
            if modality in content:
                modality_content = content[modality]
                if isinstance(modality_content, str):
                    # Text-based quality scoring
                    scores[modality] = min(0.8 + (len(modality_content) / 1000) * 0.2, 1.0)
                elif isinstance(modality_content, dict):
                    # Structure-based quality scoring
                    scores[modality] = min(0.7 + (len(modality_content) / 10) * 0.3, 1.0)
                else:
                    scores[modality] = 0.75  # Default score
            else:
                scores[modality] = 0.0  # Missing content
        
        return scores
    
    def _analyze_style_consistency(self, content: Dict[str, Any], style_guide: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze style consistency across modalities"""
        analysis = {
            "adherence_score": 0.85,  # Mock score - in production, this would use NLP analysis
            "consistency_across_modalities": 0.90,
            "style_elements_detected": [],
            "recommendations": []
        }
        
        if style_guide:
            # In production, this would analyze adherence to the style guide
            analysis["style_guide_provided"] = True
            analysis["adherence_score"] = 0.88
        
        return analysis
    
    def _calculate_cross_modal_coherence(self, content: Dict[str, Any], modalities: List[str]) -> float:
        """Calculate cross-modal coherence score"""
        # In production, this would use advanced NLP and content analysis
        if len(modalities) == 1:
            return 1.0  # Perfect coherence for single modality
        
        # Mock coherence calculation
        base_score = 0.85
        modality_penalty = max(0, (len(modalities) - 2) * 0.02)
        return max(0.7, base_score - modality_penalty)
    
    def _generate_recommendations(
        self,
        content: Dict[str, Any],
        quality_scores: Dict[str, float],
        style_analysis: Dict[str, Any],
        coherence_score: float
    ) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        # Quality-based recommendations
        low_quality_modalities = [mod for mod, score in quality_scores.items() if score < 0.7]
        if low_quality_modalities:
            recommendations.append(f"Improve content quality for: {', '.join(low_quality_modalities)}")
        
        # Style consistency recommendations
        if style_analysis.get("adherence_score", 0) < 0.8:
            recommendations.append("Enhance adherence to style guide requirements")
        
        # Cross-modal coherence recommendations
        if coherence_score < 0.8:
            recommendations.append("Improve narrative consistency across modalities")
        
        # General recommendations
        recommendations.extend([
            "Test content with target audience for feedback",
            "Consider A/B testing for optimal engagement",
            "Optimize for accessibility across all modalities",
            "Review technical implementation requirements"
        ])
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _estimate_content_length(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate content length across modalities"""
        lengths = {}
        
        for modality, modality_content in content.items():
            if isinstance(modality_content, str):
                lengths[modality] = {
                    "characters": len(modality_content),
                    "words": len(modality_content.split()),
                    "estimated_reading_time": len(modality_content.split()) / 200  # 200 WPM
                }
            elif isinstance(modality_content, dict):
                lengths[modality] = {
                    "elements": len(modality_content),
                    "complexity": "medium"
                }
        
        return lengths
    
    def _calculate_complexity_score(self, content: Dict[str, Any]) -> float:
        """Calculate content complexity score"""
        total_elements = sum(
            len(str(content_item)) for content_item in content.values()
        )
        
        # Normalize complexity score (0.0 to 1.0)
        return min(total_elements / 5000, 1.0)
    
    def _extract_technical_requirements(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Extract technical implementation requirements"""
        requirements = {
            "recommended_platforms": ["web", "mobile"],
            "accessibility_considerations": ["alt_text", "color_contrast", "keyboard_navigation"],
            "performance_optimization": ["image_compression", "content_minification", "lazy_loading"],
            "browser_compatibility": ["modern_browsers", "mobile_responsive"]
        }
        
        return requirements
    
    def _get_model_name(self) -> str:
        """Get the name of the AI model being used"""
        if not self.agent:
            return "unknown"
        
        # Check both the class name and the string representation
        model_type = str(type(self.agent.model))
        model_class_name = self.agent.model.__class__.__name__
        
        if "openai" in model_type.lower() or "openai" in model_class_name.lower():
            return "gpt-4"
        elif "anthropic" in model_type.lower() or "anthropic" in model_class_name.lower():
            return "claude-3-sonnet"
        else:
            return "unknown"
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            self.is_ready = False
            self.agent = None
            logger.info("Multimodal Generator cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Multimodal Generator cleanup: {e}")