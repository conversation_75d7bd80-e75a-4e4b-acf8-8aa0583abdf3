# Multimodal Generator Service

AI-powered multimodal content generation microservice that creates cohesive content across text, image, audio, video, interactive, and structured formats with cross-modal integration and style adaptation.

## Overview

The Multimodal Generator Service is a Tier 2 microservice that extracts the multimodal content generation capabilities from the monolithic Publish AI platform. It provides comprehensive AI-powered content creation with advanced cross-modal coherence, style consistency, and format adaptation capabilities.

## Features

### Core Capabilities
- **Multimodal Content Generation**: Create content across 6 modalities (text, image, audio, video, interactive, structured)
- **Cross-Modal Integration**: Ensure narrative coherence and consistent messaging across all modalities
- **Style Adaptation**: Professional, casual, creative, educational, and persuasive style adaptations
- **Format Optimization**: Target-specific optimization for web, mobile, print, social, presentation, and document formats
- **Content Enhancement**: Grammar, readability, engagement, accessibility, and structure improvements
- **Content Adaptation**: Convert content between different modalities with quality preservation

### Technical Features
- **PydanticAI Integration**: Advanced AI agent with multi-model support (OpenAI GPT-4, Anthropic Claude)
- **Advanced Content Processing**: Multi-format conversion, enhancement pipelines, and quality assessment
- **Batch Processing**: Parallel generation with priority queuing and progress tracking
- **Event-Driven Architecture**: Redis Streams for asynchronous workflows and real-time updates
- **Quality Analytics**: Comprehensive scoring with readability, coherence, and originality metrics
- **Multimedia Support**: Image, audio, and video processing capabilities with OpenCV, Pillow, and FFmpeg

## API Endpoints

### Content Generation
- `POST /generate` - Generate multimodal content asynchronously
- `POST /generate/sync` - Generate multimodal content synchronously
- `GET /task/{task_id}` - Get generation task status and results

### Content Processing
- `POST /adapt` - Adapt content between different modalities
- `POST /enhance` - Enhance content quality and engagement

### Batch Operations
- `POST /batch/generate` - Generate multiple content pieces in batch
- `GET /batch/status/{batch_id}` - Get batch generation status

### Service Management
- `GET /health` - Service health check
- `GET /ready` - Service readiness check
- `GET /status` - Service status and metrics

## Architecture

### Core Components

#### MultimodalGenerator (PydanticAI Agent)
AI-powered content generation agent that orchestrates multimodal content creation:
- Market-informed content generation with trend integration
- Progressive generation with real-time progress tracking
- Quality assurance with built-in content scoring
- Cross-modal coherence validation and optimization
- AI-powered analytics and performance predictions

#### ContentProcessor (Processing Engine)
Advanced content processing engine for multimodal operations:
- Multi-format conversion (Markdown, HTML, DOCX, PDF, EPUB, plain text)
- Content enhancement pipeline with grammar, style, and structure optimization
- Quality assessment with comprehensive metrics and recommendations
- Batch processing with parallel execution and priority queuing
- Cross-modal adaptation with quality preservation

### Supported Modalities

#### Text Modalities
- **Plain Text**: Articles, blogs, copy, documentation
- **Structured Text**: Markdown, HTML, rich text formats
- **Technical Content**: Code documentation, API specifications

#### Visual Modalities
- **Image Descriptions**: Detailed visual concept specifications
- **Graphic Design**: Layout, typography, and composition guidelines
- **Visual Prompts**: AI image generation prompts and parameters

#### Audio Modalities
- **Audio Scripts**: Podcast scripts, voiceover content
- **Sound Design**: Audio concept and specification documents
- **Music Concepts**: Musical composition and arrangement ideas

#### Video Modalities
- **Video Scripts**: Storyboards, scene descriptions, shot lists
- **Visual Sequences**: Video editing and post-production guidelines
- **Animation Concepts**: Motion graphics and animation specifications

#### Interactive Modalities
- **UI/UX Content**: Interface copy, user flow descriptions
- **Interactive Elements**: Button text, form labels, navigation content
- **User Experience**: Interaction patterns and usability guidelines

#### Structured Modalities
- **Data Formats**: JSON, XML, CSV structure definitions
- **API Specifications**: REST API documentation and examples
- **Schema Definitions**: Database schemas and data models

### Target Formats

#### Web Optimization
- Responsive design considerations
- SEO optimization guidelines
- Accessibility compliance (WCAG 2.1)
- Performance optimization recommendations

#### Mobile Optimization
- Touch-friendly interface specifications
- Vertical layout optimization
- Reduced bandwidth considerations
- Native app integration guidelines

#### Print Optimization
- High-resolution specifications
- CMYK color considerations
- Typography and layout for print media
- Bleed and margin specifications

#### Social Media Optimization
- Platform-specific formatting
- Character limits and constraints
- Engagement optimization
- Visual hierarchy for social feeds

#### Presentation Optimization
- Slide-specific formatting
- Visual emphasis and hierarchy
- Audience engagement techniques
- Presenter notes and guidelines

#### Document Optimization
- Professional document formatting
- Reading flow optimization
- Reference and citation formatting
- Version control considerations

## Configuration

### Environment Variables

#### Service Configuration
- `SERVICE_HOST` - Service bind host (default: 0.0.0.0)
- `SERVICE_PORT` - Service port (default: 8088)
- `LOG_LEVEL` - Logging level (default: info)

#### AI Model Configuration
- `OPENAI_API_KEY` - OpenAI API key for GPT models
- `ANTHROPIC_API_KEY` - Anthropic API key for Claude models
- `PREFERRED_MODEL` - Preferred AI model (openai/anthropic)

#### Infrastructure
- `EVENT_BUS_URL` - Event Bus service URL
- `SERVICE_DISCOVERY_URL` - Service Discovery URL
- `REDIS_URL` - Redis URL for caching and queuing

### Content Generation Parameters

#### Style Adaptations
- **Professional**: Formal, authoritative, data-driven approach
- **Casual**: Conversational, approachable, relatable tone
- **Creative**: Artistic, experimental, innovative expression
- **Educational**: Clear, structured, informative presentation
- **Persuasive**: Compelling, action-oriented, benefits-focused

#### Content Length Options
- **Short**: Brief, concise content (500-1000 words)
- **Medium**: Standard length content (1000-3000 words)
- **Long**: Comprehensive content (3000-8000 words)
- **Custom**: User-specified length requirements

#### Quality Levels
- **High**: Premium quality with extensive optimization
- **Standard**: Good quality with standard optimization
- **Fast**: Quick generation with basic optimization

## Deployment

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"

# Run the service
python src/main.py
```

### Docker Deployment

```bash
# Build image
docker build -t multimodal-generator-service:latest .

# Run container
docker run -p 8088:8088 \
  -e OPENAI_API_KEY="your-key" \
  -e ANTHROPIC_API_KEY="your-key" \
  multimodal-generator-service:latest
```

### Kubernetes Deployment

```bash
# Apply Kubernetes configuration
kubectl apply -f k8s/deployment.yaml

# Check deployment status
kubectl get pods -n publish-ai -l app=multimodal-generator-service

# View logs
kubectl logs -n publish-ai -l app=multimodal-generator-service
```

## Dependencies

### Core Dependencies
- **FastAPI**: Web framework for API endpoints
- **PydanticAI**: AI agent framework with multi-model support
- **Uvicorn**: ASGI server for production deployment

### Multimedia Processing
- **OpenCV**: Computer vision and image processing
- **Pillow**: Image manipulation and enhancement
- **PyDub**: Audio processing and conversion
- **LibROSA**: Audio analysis and feature extraction
- **MoviePy**: Video processing and editing

### Text Processing
- **TextStat**: Readability analysis and metrics
- **NLTK**: Natural language processing
- **BeautifulSoup**: HTML parsing and manipulation
- **Markdownify**: HTML to Markdown conversion

### Infrastructure
- **Redis**: Caching and background task queuing
- **Prometheus**: Metrics collection and monitoring

## Testing

### Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run simple tests only
python -m pytest tests/test_simple.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

### Test Coverage
- MultimodalGenerator agent functionality (21 tests)
- ContentProcessor engine operations (15 tests)
- Content adaptation and enhancement
- Quality assessment and analytics
- Batch processing and task management
- API endpoint responses and error handling

### Test Results
- **Total Tests**: 36/36 passing (100% success rate)
- **MultimodalGenerator Tests**: 21/21 passing
- **ContentProcessor Tests**: 15/15 passing
- **Coverage**: Comprehensive testing of core functionality

## Monitoring

### Health Checks
- `/health` - Comprehensive health status with component checks
- `/ready` - Service readiness including AI model availability
- Kubernetes liveness and readiness probes

### Metrics
- Content generation success rates by modality
- Cross-modal coherence scores
- Processing times and performance analytics
- Error rates and failure patterns
- Resource utilization and scaling metrics

### Logging
- Structured JSON logging with correlation IDs
- Content generation workflow tracking
- Quality assessment and improvement logging
- Error and exception tracking with context

## Security

### Authentication
- API key-based authentication
- Kubernetes secrets for credentials
- Service-to-service mTLS communication

### Data Security
- Input validation and sanitization
- Content filtering and safety checks
- Secure temporary file handling
- Memory-safe processing operations

### Network Security
- Network policies for pod-to-pod communication
- Egress restrictions for external APIs only
- TLS encryption for all external communications

## Performance

### Optimization Features
- Connection pooling for AI model APIs
- Intelligent caching of generated content
- Parallel processing for batch operations
- Memory-efficient content processing
- Progressive generation with streaming responses

### Scaling
- Horizontal Pod Autoscaler (HPA) configuration
- CPU and memory-based auto-scaling (2-8 replicas)
- Pod Disruption Budget for high availability
- Load balancing across service instances

### Resource Requirements
- **CPU**: 500m request, 2000m limit
- **Memory**: 1Gi request, 4Gi limit
- **Storage**: 1Gi temporary storage
- **Network**: External API access for AI models

## Quality Assurance

### Content Quality Metrics
- **Readability Scores**: Flesch Reading Ease, grade level analysis
- **Grammar Quality**: Automated grammar checking and correction
- **Coherence Scores**: Cross-modal narrative consistency
- **Engagement Metrics**: Content engagement potential assessment
- **Accessibility Scores**: Compliance with accessibility guidelines

### Quality Improvement Features
- Real-time quality feedback during generation
- Automatic content enhancement suggestions
- Style consistency validation across modalities
- Performance optimization recommendations
- A/B testing support for content variations

## Error Handling

### Content Generation Errors
- AI model API failures and automatic retry logic
- Content quality threshold failures with iterative improvement
- Cross-modal coherence validation and correction
- Format adaptation errors with fallback strategies

### Processing Errors
- Multimedia processing failures with graceful degradation
- File format conversion errors with alternative approaches
- Content enhancement failures with original content preservation
- Batch processing errors with individual item recovery

### Recovery Strategies
- Automatic retry with exponential backoff
- Circuit breaker patterns for external services
- Graceful degradation for non-critical features
- Manual intervention alerts for critical failures

## Migration Notes

This service was extracted from the monolithic system with the following enhancements:
- Converted from basic content generation to comprehensive multimodal capabilities
- Implemented advanced cross-modal coherence validation
- Added sophisticated content processing and enhancement pipelines
- Enhanced security and monitoring with production-grade observability
- Containerized for Kubernetes deployment with auto-scaling

## Contributing

1. Follow established code patterns and architecture
2. Add comprehensive tests for all new features
3. Update documentation for API changes
4. Ensure multimedia processing functionality works correctly
5. Test content quality and cross-modal coherence thoroughly

## License

Internal Publish AI platform component - All rights reserved.