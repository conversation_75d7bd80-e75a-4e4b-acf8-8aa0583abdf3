# Multimodal Generator Service Dockerfile
FROM python:3.11-slim

# Install system dependencies for multimedia processing
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsm6 \
    libxext6 \
    libfontconfig1 \
    libxrender1 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY shared/ ./shared/
COPY tests/ ./tests/

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app

# Create directories for temporary files
RUN mkdir -p /tmp/multimodal-generator \
    && chown -R appuser:appuser /tmp/multimodal-generator

# Switch to non-root user
USER appuser

# Set environment variables
ENV PYTHONPATH=/app/src:/app/shared
ENV SERVICE_HOST=0.0.0.0
ENV SERVICE_PORT=8088

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8088/health || exit 1

# Expose port
EXPOSE 8088

# Run the application
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8088"]