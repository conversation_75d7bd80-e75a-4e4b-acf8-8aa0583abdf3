"""
Test configuration for Multimodal Generator Service
"""

import pytest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up test environment"""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    
    yield
    
    # Clean up after tests
    if "TESTING" in os.environ:
        del os.environ["TESTING"]