"""
Simple tests for Multimodal Generator Service
"""

import pytest
import sys
import os
from unittest.mock import Async<PERSON>ock, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestSimpleMultimodalGenerator:
    """Simple tests for MultimodalGenerator"""
    
    @pytest.mark.asyncio
    async def test_initialization_no_api_keys(self):
        """Test initialization with no API keys"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {}, clear=True):
            generator = MultimodalGenerator()
            
            with pytest.raises(Exception, match="No AI model available"):
                await generator.initialize()
    
    @pytest.mark.asyncio
    async def test_initialization_with_openai(self):
        """Test initialization with OpenAI API key"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = MultimodalGenerator()
            await generator.initialize()
            
            assert generator.is_ready
            assert generator.agent is not None
    
    @pytest.mark.asyncio
    async def test_initialization_with_anthropic(self):
        """Test initialization with Anthropic API key"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key', 'PREFERRED_MODEL': 'anthropic'}):
            generator = MultimodalGenerator()
            await generator.initialize()
            
            assert generator.is_ready
            assert generator.agent is not None
    
    def test_supported_modalities(self):
        """Test supported modalities list"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        expected_modalities = ["text", "image", "audio", "video", "interactive", "structured"]
        assert generator.supported_modalities == expected_modalities
    
    def test_supported_formats(self):
        """Test supported formats list"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        expected_formats = ["web", "mobile", "print", "social", "presentation", "document"]
        assert generator.supported_formats == expected_formats
    
    @pytest.mark.asyncio
    async def test_generate_multimodal_content_not_ready(self):
        """Test content generation when not ready"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        # Don't initialize to test not ready state
        
        with pytest.raises(Exception, match="not initialized"):
            await generator.generate_multimodal_content("Test content brief")
    
    @pytest.mark.asyncio
    async def test_generate_multimodal_content_invalid_modality(self):
        """Test content generation with invalid modality"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = MultimodalGenerator()
            await generator.initialize()
            
            with pytest.raises(ValueError, match="Unsupported modalities"):
                await generator.generate_multimodal_content(
                    "Test content",
                    modalities=["invalid_modality"]
                )
    
    @pytest.mark.asyncio
    async def test_generate_multimodal_content_invalid_format(self):
        """Test content generation with invalid format"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = MultimodalGenerator()
            await generator.initialize()
            
            with pytest.raises(ValueError, match="Unsupported target format"):
                await generator.generate_multimodal_content(
                    "Test content",
                    target_format="invalid_format"
                )
    
    def test_get_ai_model_openai(self):
        """Test AI model selection for OpenAI"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key', 'PREFERRED_MODEL': 'openai'}):
            generator = MultimodalGenerator()
            model = generator._get_ai_model()
            
            assert model is not None
            assert str(type(model)).lower().find('openai') != -1
    
    def test_get_ai_model_anthropic(self):
        """Test AI model selection for Anthropic"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key', 'PREFERRED_MODEL': 'anthropic'}):
            generator = MultimodalGenerator()
            model = generator._get_ai_model()
            
            assert model is not None
            assert str(type(model)).lower().find('anthropic') != -1
    
    def test_get_ai_model_no_keys(self):
        """Test AI model selection with no API keys"""
        from multimodal_generator import MultimodalGenerator
        
        with patch.dict(os.environ, {}, clear=True):
            generator = MultimodalGenerator()
            model = generator._get_ai_model()
            
            assert model is None
    
    def test_get_system_prompt(self):
        """Test system prompt generation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        prompt = generator._get_system_prompt()
        
        assert isinstance(prompt, str)
        assert len(prompt) > 100
        assert "multimodal" in prompt.lower()
        assert "content" in prompt.lower()
    
    def test_prepare_generation_prompt(self):
        """Test generation prompt preparation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        prompt = generator._prepare_generation_prompt(
            content_brief="Test content",
            modalities=["text", "image"],
            style_guide={"tone": "professional"},
            target_format="web",
            content_length="medium",
            target_audience="general",
            tone="professional"
        )
        
        assert isinstance(prompt, str)
        assert "Test content" in prompt
        assert "text, image" in prompt
        assert "professional" in prompt
    
    def test_calculate_quality_scores(self):
        """Test quality score calculation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        content = {
            "text": "This is a test text content with reasonable length",
            "image": {"description": "test image", "format": "jpg"}
        }
        modalities = ["text", "image", "missing_modality"]
        
        scores = generator._calculate_quality_scores(content, modalities)
        
        assert isinstance(scores, dict)
        assert "text" in scores
        assert "image" in scores
        assert "missing_modality" in scores
        assert scores["text"] > 0
        assert scores["image"] > 0
        assert scores["missing_modality"] == 0.0
    
    def test_analyze_style_consistency(self):
        """Test style consistency analysis"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        content = {"text": "Test content", "image": "Test image"}
        style_guide = {"tone": "professional", "style": "modern"}
        
        analysis = generator._analyze_style_consistency(content, style_guide)
        
        assert isinstance(analysis, dict)
        assert "adherence_score" in analysis
        assert "consistency_across_modalities" in analysis
        assert 0 <= analysis["adherence_score"] <= 1
        assert 0 <= analysis["consistency_across_modalities"] <= 1
    
    def test_calculate_cross_modal_coherence(self):
        """Test cross-modal coherence calculation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        content = {"text": "Test", "image": "Test"}
        
        # Single modality should have perfect coherence
        coherence_single = generator._calculate_cross_modal_coherence(content, ["text"])
        assert coherence_single == 1.0
        
        # Multiple modalities should have reasonable coherence
        coherence_multi = generator._calculate_cross_modal_coherence(content, ["text", "image"])
        assert 0.7 <= coherence_multi <= 1.0
    
    def test_generate_recommendations(self):
        """Test recommendation generation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        content = {"text": "Test content"}
        quality_scores = {"text": 0.6}  # Low quality
        style_analysis = {"adherence_score": 0.7}  # Low adherence
        coherence_score = 0.75
        
        recommendations = generator._generate_recommendations(
            content, quality_scores, style_analysis, coherence_score
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 5
        assert any("quality" in rec.lower() for rec in recommendations)
    
    def test_estimate_content_length(self):
        """Test content length estimation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        content = {
            "text": "This is a test text with multiple words for testing",
            "structured": {"key1": "value1", "key2": "value2"}
        }
        
        lengths = generator._estimate_content_length(content)
        
        assert isinstance(lengths, dict)
        assert "text" in lengths
        assert "structured" in lengths
        assert "words" in lengths["text"]
        assert "characters" in lengths["text"]
        assert "elements" in lengths["structured"]
    
    def test_calculate_complexity_score(self):
        """Test complexity score calculation"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        simple_content = {"text": "Short"}
        complex_content = {"text": "A" * 10000, "image": "B" * 5000}
        
        simple_score = generator._calculate_complexity_score(simple_content)
        complex_score = generator._calculate_complexity_score(complex_content)
        
        assert 0 <= simple_score <= 1
        assert 0 <= complex_score <= 1
        assert complex_score >= simple_score
    
    def test_get_model_name(self):
        """Test model name extraction"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        
        # Test with no agent
        assert generator._get_model_name() == "unknown"
        
        # Test with mock agent
        mock_agent = MagicMock()
        mock_model = MagicMock()
        mock_model.__class__.__name__ = "OpenAIModel"
        mock_agent.model = mock_model
        generator.agent = mock_agent
        
        model_name = generator._get_model_name()
        assert model_name == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test cleanup process"""
        from multimodal_generator import MultimodalGenerator
        
        generator = MultimodalGenerator()
        generator.is_ready = True
        generator.agent = MagicMock()
        
        await generator.cleanup()
        
        assert not generator.is_ready
        assert generator.agent is None


class TestSimpleContentProcessor:
    """Simple tests for ContentProcessor"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """Test content processor initialization"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        assert processor.is_ready
        assert len(processor.text_processors) > 0
        assert len(processor.image_processors) > 0
        assert len(processor.audio_processors) > 0
    
    @pytest.mark.asyncio
    async def test_adapt_content_not_ready(self):
        """Test content adaptation when not ready"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        # Don't initialize
        
        with pytest.raises(Exception, match="not initialized"):
            await processor.adapt_content(
                "test content", "text", ["html"]
            )
    
    @pytest.mark.asyncio
    async def test_adapt_content_text_to_html(self):
        """Test text to HTML adaptation"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        result = await processor.adapt_content(
            "Test paragraph\n\nSecond paragraph",
            "text",
            ["html"]
        )
        
        assert result["source_modality"] == "text"
        assert "html" in result["adaptations"]
        assert "adaptation_id" in result
        assert result["execution_time"] > 0
    
    @pytest.mark.asyncio
    async def test_adapt_content_html_to_text(self):
        """Test HTML to text adaptation"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        html_content = "<p>Test paragraph</p><p>Second paragraph</p>"
        
        result = await processor.adapt_content(
            html_content,
            "html",
            ["text"]
        )
        
        assert result["source_modality"] == "html"
        assert "text" in result["adaptations"]
        assert "Test paragraph" in result["adaptations"]["text"]["content"]
    
    @pytest.mark.asyncio
    async def test_enhance_content_not_ready(self):
        """Test content enhancement when not ready"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        # Don't initialize
        
        with pytest.raises(Exception, match="not initialized"):
            await processor.enhance_content(
                "test content", "text", ["grammar"]
            )
    
    @pytest.mark.asyncio
    async def test_enhance_content_grammar(self):
        """Test grammar enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        text_with_errors = "this is a test with i and cant"
        
        result = await processor.enhance_content(
            text_with_errors,
            "text",
            ["grammar"]
        )
        
        assert result["modality"] == "text"
        assert "enhanced_content" in result
        assert "I" in result["enhanced_content"]  # Should fix capitalization
        assert "can't" in result["enhanced_content"]  # Should fix contraction
    
    @pytest.mark.asyncio
    async def test_enhance_content_readability(self):
        """Test readability enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        # Create a long sentence to test splitting
        long_sentence = "This is a very long sentence " * 10 + "that should be split."
        
        result = await processor.enhance_content(
            long_sentence,
            "text",
            ["readability"]
        )
        
        assert result["modality"] == "text"
        assert "enhanced_content" in result
        assert "enhancement_details" in result
    
    @pytest.mark.asyncio
    async def test_process_batch_generation(self):
        """Test batch generation processing"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        requests = [
            {"content_brief": "Test content 1", "modalities": ["text"]},
            {"content_brief": "Test content 2", "modalities": ["text", "image"]}
        ]
        
        result = await processor.process_batch_generation(
            "test_batch",
            requests,
            parallel_processing=False  # Use sequential for simpler testing
        )
        
        assert result["batch_id"] == "test_batch"
        assert result["total_requests"] == 2
        assert result["status"] == "completed"
        assert "successful_results" in result
        assert "execution_time" in result
    
    @pytest.mark.asyncio
    async def test_get_task_status_not_found(self):
        """Test getting status for non-existent task"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        status = await processor.get_task_status("non_existent_task")
        
        assert status["status"] == "not_found"
        assert "task_id" in status
    
    @pytest.mark.asyncio
    async def test_store_and_get_task_result(self):
        """Test storing and retrieving task results"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        task_id = "test_task"
        result_data = {"content": "test result", "quality": 0.8}
        
        await processor.store_task_result(task_id, result_data)
        status = await processor.get_task_status(task_id)
        
        assert status["status"] == "completed"
        assert status["result"] == result_data
    
    def test_get_active_task_count(self):
        """Test active task count tracking"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        assert processor.get_active_task_count() == 0
        
        processor.active_tasks.add("task1")
        processor.active_tasks.add("task2")
        
        assert processor.get_active_task_count() == 2
    
    @pytest.mark.asyncio
    async def test_analyze_text_content(self):
        """Test text content analysis"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        text = "This is a test sentence. This is another sentence!"
        analysis = await processor._analyze_text_content(text)
        
        assert "word_count" in analysis
        assert "sentence_count" in analysis
        assert analysis["word_count"] > 0
        assert analysis["sentence_count"] > 0
    
    @pytest.mark.asyncio
    async def test_analyze_html_content(self):
        """Test HTML content analysis"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        html = "<h1>Title</h1><p>Content</p><a href='#'>Link</a><img src='test.jpg'>"
        analysis = await processor._analyze_html_content(html)
        
        assert "tag_count" in analysis
        assert "heading_count" in analysis
        assert "link_count" in analysis
        assert "image_count" in analysis
        assert analysis["tag_count"] > 0
    
    @pytest.mark.asyncio
    async def test_assess_content_quality(self):
        """Test content quality assessment"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        quality = await processor._assess_content_quality("Test content", "text")
        
        assert "overall_score" in quality
        assert "clarity_score" in quality
        assert 0 <= quality["overall_score"] <= 1
        assert 0 <= quality["clarity_score"] <= 1
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test cleanup process"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        processor.is_ready = True
        processor.task_storage["test"] = "data"
        processor.active_tasks.add("task1")
        
        await processor.cleanup()
        
        assert not processor.is_ready
        assert len(processor.task_storage) == 0
        assert len(processor.active_tasks) == 0


if __name__ == "__main__":
    pytest.main([__file__])