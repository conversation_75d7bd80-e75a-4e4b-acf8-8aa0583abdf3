#!/bin/bash

# Event Bus Service Startup Script

set -e

echo "Starting Event Bus Service..."

# Check environment variables
if [ -z "$EVENT_BUS_REDIS_URL" ]; then
    echo "Warning: EVENT_BUS_REDIS_URL not set, using default"
    export EVENT_BUS_REDIS_URL="redis://localhost:6379"
fi

if [ -z "$EVENT_BUS_ENCRYPTION_KEY" ]; then
    echo "Warning: EVENT_BUS_ENCRYPTION_KEY not set, encryption disabled"
fi

# Wait for Redis to be available
echo "Waiting for Redis..."
until redis-cli -u $EVENT_BUS_REDIS_URL ping > /dev/null 2>&1; do
    echo "Waiting for Redis to start..."
    sleep 2
done
echo "Redis is ready!"

# Run database migrations if needed
echo "Setting up consumer groups..."
python -c "
import asyncio
from src.event_bus import EventBus
from src.config import Settings

async def setup():
    settings = Settings()
    bus = EventBus(settings)
    await bus.initialize()
    await bus.close()
    print('Consumer groups initialized')

asyncio.run(setup())
"

# Start the service
echo "Starting Event Bus Service on port 8080..."
exec uvicorn src.main:app \
    --host 0.0.0.0 \
    --port 8080 \
    --workers 1 \
    --log-level info \
    --access-log