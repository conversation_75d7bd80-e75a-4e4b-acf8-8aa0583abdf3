"""
Event Bus Service Security
"""

import hashlib
import hmac
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from cryptography.fernet import Fernet
import structlog

from .config import Settings

logger = structlog.get_logger(__name__)
security = HTTPBearer()

# In-memory API key store (in production, use Redis or database)
_api_keys = {
    "trend-analyzer-service": {
        "key_hash": "hashed_key_1",
        "service_id": "trend-analyzer",
        "permissions": ["publish", "subscribe"],
        "created_at": datetime.utcnow(),
        "last_used": None
    },
    "content-generator-service": {
        "key_hash": "hashed_key_2", 
        "service_id": "content-generator",
        "permissions": ["publish", "subscribe"],
        "created_at": datetime.utcnow(),
        "last_used": None
    }
}


class SecurityManager:
    """Manages security for the event bus"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.encryption_key = settings.encryption_key
        if self.encryption_key:
            self.cipher_suite = Fernet(self.encryption_key.encode())
        else:
            self.cipher_suite = None
    
    def generate_api_key(self) -> str:
        """Generate a new API key"""
        return secrets.token_urlsafe(32)
    
    def hash_api_key(self, api_key: str) -> str:
        """Hash an API key for storage"""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def verify_api_key(self, api_key: str) -> Optional[dict]:
        """Verify an API key and return service info"""
        key_hash = self.hash_api_key(api_key)
        
        for service_name, service_info in _api_keys.items():
            if service_info["key_hash"] == key_hash:
                # Update last used timestamp
                service_info["last_used"] = datetime.utcnow()
                return service_info
        
        return None
    
    def encrypt_payload(self, payload: str) -> str:
        """Encrypt event payload"""
        if not self.cipher_suite:
            return payload
        
        try:
            encrypted = self.cipher_suite.encrypt(payload.encode())
            return encrypted.decode()
        except Exception as e:
            logger.error("Failed to encrypt payload", error=str(e))
            raise
    
    def decrypt_payload(self, encrypted_payload: str) -> str:
        """Decrypt event payload"""
        if not self.cipher_suite:
            return encrypted_payload
        
        try:
            decrypted = self.cipher_suite.decrypt(encrypted_payload.encode())
            return decrypted.decode()
        except Exception as e:
            logger.error("Failed to decrypt payload", error=str(e))
            raise
    
    def check_permissions(self, service_info: dict, required_permission: str) -> bool:
        """Check if service has required permission"""
        return required_permission in service_info.get("permissions", [])


# Global security manager instance
_security_manager: Optional[SecurityManager] = None


def get_security_manager() -> SecurityManager:
    """Get or create security manager instance"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager(Settings())
    return _security_manager


async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> dict:
    """Verify API key from Authorization header"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    security_manager = get_security_manager()
    service_info = security_manager.verify_api_key(credentials.credentials)
    
    if not service_info:
        logger.warning("Invalid API key used", key_prefix=credentials.credentials[:8])
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    logger.info("Service authenticated", service_id=service_info["service_id"])
    return service_info


async def get_current_service(service_info: dict = Security(verify_api_key)) -> str:
    """Get current service ID from authenticated request"""
    return service_info["service_id"]


def require_permission(permission: str):
    """Decorator to require specific permission"""
    def decorator(func):
        async def wrapper(*args, service_info: dict = Security(verify_api_key), **kwargs):
            security_manager = get_security_manager()
            
            if not security_manager.check_permissions(service_info, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission}' required"
                )
            
            return await func(*args, service_info=service_info, **kwargs)
        return wrapper
    return decorator


class MessageSigner:
    """Signs and verifies message integrity"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key.encode()
    
    def sign_message(self, message: str) -> str:
        """Create HMAC signature for message"""
        signature = hmac.new(
            self.secret_key,
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def verify_signature(self, message: str, signature: str) -> bool:
        """Verify message signature"""
        expected_signature = self.sign_message(message)
        return hmac.compare_digest(expected_signature, signature)


def setup_service_keys():
    """Setup initial service API keys (for development)"""
    services = [
        "trend-analyzer",
        "content-generator", 
        "cover-designer",
        "kdp-uploader",
        "sales-monitor",
        "research-assistant",
        "personalization-engine",
        "multimodal-generator"
    ]
    
    security_manager = get_security_manager()
    
    for service in services:
        api_key = security_manager.generate_api_key()
        key_hash = security_manager.hash_api_key(api_key)
        
        service_name = f"{service}-service"
        _api_keys[service_name] = {
            "key_hash": key_hash,
            "service_id": service,
            "permissions": ["publish", "subscribe"],
            "created_at": datetime.utcnow(),
            "last_used": None
        }
        
        logger.info(
            f"Generated API key for {service}",
            service_id=service,
            api_key=api_key  # Only log in development
        )


def rotate_api_key(service_id: str) -> str:
    """Rotate API key for a service"""
    security_manager = get_security_manager()
    new_key = security_manager.generate_api_key()
    new_hash = security_manager.hash_api_key(new_key)
    
    service_name = f"{service_id}-service"
    if service_name in _api_keys:
        _api_keys[service_name]["key_hash"] = new_hash
        _api_keys[service_name]["created_at"] = datetime.utcnow()
        _api_keys[service_name]["last_used"] = None
        
        logger.info("API key rotated", service_id=service_id)
        return new_key
    
    raise ValueError(f"Service {service_id} not found")