"""
Event Bus Core Implementation
Handles Redis Streams, consumer groups, and event routing
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import AsyncGenerator, Dict, List, Optional, Any
from uuid import UUID, uuid4

import redis.asyncio as redis
from redis.asyncio.retry import Retry
from redis.asyncio.backoff import ExponentialBackoff
import structlog

from .config import Settings
from .models import (
    EventMessage, DeadLetterMessage, ConsumerInfo, 
    StreamInfo, EventMetrics, EventType
)
from .security import get_security_manager

logger = structlog.get_logger(__name__)


class EventBus:
    """Core event bus implementation using Redis Streams"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.redis_client: Optional[redis.Redis] = None
        self.security_manager = get_security_manager()
        
        # Stream naming convention
        self.event_stream = "events:main"
        self.dead_letter_stream = "events:dead_letter"
        
        # Consumer group configuration
        self.consumer_group = settings.consumer_group_name
        self.consumer_id = f"event-bus-{uuid4().hex[:8]}"
        
        # Metrics tracking
        self.metrics = {
            "events_published": 0,
            "events_consumed": 0,
            "events_failed": 0,
            "last_event_time": None
        }
    
    async def initialize(self):
        """Initialize Redis connection and consumer groups"""
        try:
            # Create Redis connection with retry configuration
            retry = Retry(ExponentialBackoff(), retries=3)
            
            self.redis_client = redis.from_url(
                self.settings.redis_url,
                password=self.settings.redis_password,
                db=self.settings.redis_db,
                max_connections=self.settings.redis_max_connections,
                retry=retry,
                health_check_interval=30,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Create consumer groups
            await self._create_consumer_groups()
            
            logger.info(
                "Event bus initialized",
                consumer_id=self.consumer_id,
                consumer_group=self.consumer_group
            )
            
        except Exception as e:
            logger.error("Failed to initialize event bus", error=str(e))
            raise
    
    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Event bus connection closed")
    
    async def health_check(self) -> bool:
        """Check Redis connectivity"""
        try:
            if not self.redis_client:
                return False
            
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return False
    
    async def publish(self, event: EventMessage, source_service: str) -> UUID:
        """Publish event to the event bus"""
        try:
            # Set source service
            event.source_service = source_service
            
            # Encrypt payload if encryption is enabled
            payload_data = event.payload
            if self.security_manager.cipher_suite:
                encrypted_payload = self.security_manager.encrypt_payload(
                    json.dumps(payload_data)
                )
                payload_data = {"encrypted": True, "data": encrypted_payload}
            
            # Prepare stream data
            stream_data = {
                "event_id": str(event.event_id),
                "event_type": event.event_type.value,
                "source_service": event.source_service,
                "timestamp": event.timestamp.isoformat(),
                "payload": json.dumps(payload_data),
                "correlation_id": str(event.correlation_id) if event.correlation_id else "",
                "user_id": event.user_id or "",
                "priority": event.priority.value,
                "ttl_seconds": str(event.ttl_seconds) if event.ttl_seconds else "",
                "retry_count": str(event.retry_count),
                "target_services": json.dumps(event.target_services or []),
                "topic": event.topic or ""
            }
            
            # Add to main event stream
            message_id = await self.redis_client.xadd(
                self.event_stream,
                stream_data,
                maxlen=self.settings.max_stream_length,
                approximate=True
            )
            
            # Update metrics
            self.metrics["events_published"] += 1
            self.metrics["last_event_time"] = datetime.utcnow()
            
            # If event has target services, route to specific topics
            if event.target_services:
                for service in event.target_services:
                    topic_stream = f"events:topic:{service}"
                    await self.redis_client.xadd(
                        topic_stream,
                        stream_data,
                        maxlen=self.settings.max_stream_length,
                        approximate=True
                    )
            
            # If event has topic, route to topic stream
            if event.topic:
                topic_stream = f"events:topic:{event.topic}"
                await self.redis_client.xadd(
                    topic_stream,
                    stream_data,
                    maxlen=self.settings.max_stream_length,
                    approximate=True
                )
            
            logger.info(
                "Event published to stream",
                event_id=event.event_id,
                message_id=message_id,
                stream=self.event_stream
            )
            
            return event.event_id
            
        except Exception as e:
            logger.error(
                "Failed to publish event",
                event_id=event.event_id,
                error=str(e)
            )
            self.metrics["events_failed"] += 1
            raise
    
    async def subscribe(self, topic: str, service_id: str):
        """Subscribe a service to a topic"""
        try:
            topic_stream = f"events:topic:{topic}"
            group_name = f"{self.consumer_group}:{service_id}"
            
            # Create consumer group for the topic stream
            try:
                await self.redis_client.xgroup_create(
                    topic_stream,
                    group_name,
                    id="0",
                    mkstream=True
                )
                logger.info(
                    "Consumer group created",
                    stream=topic_stream,
                    group=group_name
                )
            except redis.ResponseError as e:
                if "BUSYGROUP" in str(e):
                    logger.debug("Consumer group already exists", group=group_name)
                else:
                    raise
            
            # Store subscription information
            subscription_key = f"subscriptions:{service_id}"
            await self.redis_client.sadd(subscription_key, topic)
            await self.redis_client.expire(subscription_key, 86400)  # 24 hours
            
        except Exception as e:
            logger.error(
                "Failed to subscribe to topic",
                topic=topic,
                service_id=service_id,
                error=str(e)
            )
            raise
    
    async def stream_events(
        self,
        topic: str,
        service_id: str,
        consumer_id: Optional[str] = None
    ) -> AsyncGenerator[EventMessage, None]:
        """Stream events from a topic"""
        if not consumer_id:
            consumer_id = f"{service_id}-{uuid4().hex[:8]}"
        
        topic_stream = f"events:topic:{topic}"
        group_name = f"{self.consumer_group}:{service_id}"
        
        try:
            while True:
                try:
                    # Read from stream
                    messages = await self.redis_client.xreadgroup(
                        group_name,
                        consumer_id,
                        {topic_stream: ">"},
                        count=1,
                        block=self.settings.stream_block_time_ms
                    )
                    
                    for stream, stream_messages in messages:
                        for message_id, fields in stream_messages:
                            try:
                                # Parse event from stream data
                                event = await self._parse_stream_message(fields)
                                
                                # Acknowledge message
                                await self.redis_client.xack(
                                    topic_stream,
                                    group_name,
                                    message_id
                                )
                                
                                self.metrics["events_consumed"] += 1
                                yield event
                                
                            except Exception as e:
                                logger.error(
                                    "Failed to process stream message",
                                    message_id=message_id,
                                    error=str(e)
                                )
                                
                                # Move to dead letter queue
                                await self._move_to_dead_letter(
                                    topic_stream,
                                    message_id,
                                    fields,
                                    str(e)
                                )
                
                except redis.ResponseError as e:
                    if "NOGROUP" in str(e):
                        # Consumer group doesn't exist, create it
                        await self.subscribe(topic, service_id)
                        continue
                    else:
                        raise
                        
                except asyncio.CancelledError:
                    logger.info("Event streaming cancelled", topic=topic)
                    break
                    
        except Exception as e:
            logger.error(
                "Event streaming failed",
                topic=topic,
                service_id=service_id,
                error=str(e)
            )
            raise
    
    async def get_dead_letter_messages(self) -> List[DeadLetterMessage]:
        """Get messages from dead letter queue"""
        try:
            messages = await self.redis_client.xrange(
                self.dead_letter_stream,
                count=100
            )
            
            dead_letter_messages = []
            for message_id, fields in messages:
                try:
                    # Parse dead letter message
                    original_event = await self._parse_stream_message(
                        json.loads(fields[b"original_event"].decode())
                    )
                    
                    dead_letter_msg = DeadLetterMessage(
                        message_id=message_id,
                        original_event=original_event,
                        failure_reason=fields[b"failure_reason"].decode(),
                        failed_at=datetime.fromisoformat(
                            fields[b"failed_at"].decode()
                        ),
                        retry_count=int(fields[b"retry_count"]),
                        last_error=fields.get(b"last_error", b"").decode()
                    )
                    
                    dead_letter_messages.append(dead_letter_msg)
                    
                except Exception as e:
                    logger.error(
                        "Failed to parse dead letter message",
                        message_id=message_id,
                        error=str(e)
                    )
            
            return dead_letter_messages
            
        except Exception as e:
            logger.error("Failed to get dead letter messages", error=str(e))
            raise
    
    async def delete_dead_letter_message(self, message_id: str) -> bool:
        """Delete a message from dead letter queue"""
        try:
            result = await self.redis_client.xdel(
                self.dead_letter_stream,
                message_id
            )
            return result > 0
            
        except Exception as e:
            logger.error(
                "Failed to delete dead letter message",
                message_id=message_id,
                error=str(e)
            )
            return False
    
    async def get_metrics(self) -> EventMetrics:
        """Get event bus metrics"""
        try:
            # Get stream info
            stream_info = await self.redis_client.xinfo_stream(self.event_stream)
            dead_letter_info = await self.redis_client.xinfo_stream(
                self.dead_letter_stream
            )
            
            # Calculate events per second
            events_per_second = 0.0
            if self.metrics["last_event_time"]:
                time_diff = (datetime.utcnow() - self.metrics["last_event_time"]).total_seconds()
                if time_diff > 0:
                    events_per_second = self.metrics["events_published"] / time_diff
            
            # Calculate error rate
            total_events = self.metrics["events_published"]
            error_rate = 0.0
            if total_events > 0:
                error_rate = (self.metrics["events_failed"] / total_events) * 100
            
            return EventMetrics(
                total_events_published=self.metrics["events_published"],
                total_events_consumed=self.metrics["events_consumed"],
                events_in_dead_letter=dead_letter_info["length"],
                active_consumers=len(await self._get_active_consumers()),
                events_per_second=events_per_second,
                error_rate=error_rate
            )
            
        except Exception as e:
            logger.error("Failed to get metrics", error=str(e))
            return EventMetrics()
    
    async def _create_consumer_groups(self):
        """Create Redis consumer groups"""
        streams = [self.event_stream, self.dead_letter_stream]
        
        for stream in streams:
            try:
                await self.redis_client.xgroup_create(
                    stream,
                    self.consumer_group,
                    id="0",
                    mkstream=True
                )
                logger.info("Consumer group created", stream=stream)
            except redis.ResponseError as e:
                if "BUSYGROUP" in str(e):
                    logger.debug("Consumer group already exists", stream=stream)
                else:
                    logger.error(
                        "Failed to create consumer group",
                        stream=stream,
                        error=str(e)
                    )
                    raise
    
    async def _parse_stream_message(self, fields: Dict) -> EventMessage:
        """Parse Redis stream message back to EventMessage"""
        try:
            # Handle both bytes and string field keys
            def get_field(key: str) -> str:
                if isinstance(fields, dict):
                    # Try both string and bytes keys
                    value = fields.get(key) or fields.get(key.encode())
                    if isinstance(value, bytes):
                        return value.decode()
                    return str(value) if value is not None else ""
                return ""
            
            # Parse payload
            payload_str = get_field("payload")
            payload_data = json.loads(payload_str) if payload_str else {}
            
            # Decrypt if encrypted
            if payload_data.get("encrypted"):
                decrypted_payload = self.security_manager.decrypt_payload(
                    payload_data["data"]
                )
                payload_data = json.loads(decrypted_payload)
            
            # Parse other fields
            correlation_id = get_field("correlation_id")
            user_id = get_field("user_id")
            ttl_seconds = get_field("ttl_seconds")
            target_services = get_field("target_services")
            topic = get_field("topic")
            
            return EventMessage(
                event_id=UUID(get_field("event_id")),
                event_type=EventType(get_field("event_type")),
                source_service=get_field("source_service"),
                timestamp=datetime.fromisoformat(get_field("timestamp")),
                payload=payload_data,
                correlation_id=UUID(correlation_id) if correlation_id else None,
                user_id=user_id if user_id else None,
                retry_count=int(get_field("retry_count") or "0"),
                ttl_seconds=int(ttl_seconds) if ttl_seconds else None,
                target_services=json.loads(target_services) if target_services else None,
                topic=topic if topic else None
            )
            
        except Exception as e:
            logger.error("Failed to parse stream message", error=str(e))
            raise
    
    async def _move_to_dead_letter(
        self,
        stream: str,
        message_id: str,
        fields: Dict,
        error_message: str
    ):
        """Move failed message to dead letter queue"""
        try:
            dead_letter_data = {
                "message_id": message_id,
                "original_stream": stream,
                "original_event": json.dumps(fields),
                "failure_reason": error_message,
                "failed_at": datetime.utcnow().isoformat(),
                "retry_count": fields.get("retry_count", "0"),
                "last_error": error_message
            }
            
            await self.redis_client.xadd(
                self.dead_letter_stream,
                dead_letter_data,
                maxlen=self.settings.max_stream_length,
                approximate=True
            )
            
            logger.warning(
                "Message moved to dead letter queue",
                message_id=message_id,
                stream=stream,
                error=error_message
            )
            
        except Exception as e:
            logger.error(
                "Failed to move message to dead letter queue",
                message_id=message_id,
                error=str(e)
            )
    
    async def _get_active_consumers(self) -> List[ConsumerInfo]:
        """Get list of active consumers"""
        try:
            consumers = []
            
            # Get consumer groups for main stream
            groups = await self.redis_client.xinfo_groups(self.event_stream)
            
            for group in groups:
                group_consumers = await self.redis_client.xinfo_consumers(
                    self.event_stream,
                    group["name"]
                )
                
                for consumer in group_consumers:
                    consumers.append(ConsumerInfo(
                        consumer_id=consumer["name"],
                        service_id="unknown",  # Extract from consumer name if needed
                        group_name=group["name"],
                        last_seen=datetime.utcnow(),  # Redis doesn't track this directly
                        messages_processed=consumer["pending"],
                        current_lag=group["lag"] if "lag" in group else 0
                    ))
            
            return consumers
            
        except Exception as e:
            logger.error("Failed to get active consumers", error=str(e))
            return []