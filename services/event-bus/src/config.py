"""
Event Bus Service Configuration
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Event Bus Service configuration settings"""
    
    # Service configuration
    service_name: str = "event-bus"
    debug: bool = False
    
    # Redis configuration
    redis_url: str = Field(default="redis://localhost:6379", description="Redis connection URL")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_db: int = Field(default=0, description="Redis database number")
    redis_max_connections: int = Field(default=20, description="Maximum Redis connections")
    
    # Event configuration
    event_retention_hours: int = Field(default=168, description="Event retention in hours (7 days)")
    max_retry_attempts: int = Field(default=3, description="Maximum retry attempts for failed events")
    dead_letter_queue_ttl: int = Field(default=604800, description="Dead letter queue TTL in seconds (7 days)")
    
    # Security
    api_key_header: str = Field(default="X-API-Key", description="API key header name")
    encryption_key: Optional[str] = Field(default=None, description="AES encryption key for event payloads")
    
    # CORS
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="Allowed CORS origins"
    )
    
    # Monitoring
    metrics_enabled: bool = Field(default=True, description="Enable Prometheus metrics")
    tracing_enabled: bool = Field(default=True, description="Enable OpenTelemetry tracing")
    
    # Consumer group configuration
    consumer_group_name: str = Field(default="event-bus-consumers", description="Redis consumer group name")
    consumer_timeout_ms: int = Field(default=30000, description="Consumer timeout in milliseconds")
    
    # Stream configuration
    max_stream_length: int = Field(default=10000, description="Maximum stream length")
    stream_block_time_ms: int = Field(default=1000, description="Stream blocking time in milliseconds")
    
    class Config:
        env_prefix = "EVENT_BUS_"
        env_file = ".env"
        env_file_encoding = "utf-8"