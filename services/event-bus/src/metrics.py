"""
Event Bus Metrics and Monitoring
Prometheus metrics and performance monitoring
"""

import time
import asyncio
from typing import Dict, Any
from prometheus_client import Counter, Histogram, Gauge, Info
import structlog

logger = structlog.get_logger(__name__)

# Prometheus metrics
events_published_total = Counter(
    'event_bus_events_published_total',
    'Total number of events published',
    ['event_type', 'source_service']
)

events_consumed_total = Counter(
    'event_bus_events_consumed_total', 
    'Total number of events consumed',
    ['event_type', 'consumer_service']
)

events_failed_total = Counter(
    'event_bus_events_failed_total',
    'Total number of failed events',
    ['event_type', 'failure_reason']
)

event_processing_duration = Histogram(
    'event_bus_processing_duration_seconds',
    'Time spent processing events',
    ['event_type', 'operation']
)

active_streams = Gauge(
    'event_bus_active_streams',
    'Number of active Redis streams'
)

active_consumers = Gauge(
    'event_bus_active_consumers',
    'Number of active consumers'
)

stream_length = Gauge(
    'event_bus_stream_length',
    'Length of Redis streams',
    ['stream_name']
)

dead_letter_queue_size = Gauge(
    'event_bus_dead_letter_queue_size',
    'Number of messages in dead letter queue'
)

redis_connection_status = Gauge(
    'event_bus_redis_connection_status',
    'Redis connection status (1=connected, 0=disconnected)'
)

service_info = Info(
    'event_bus_service_info',
    'Event Bus service information'
)


class MetricsCollector:
    """Collects and manages Event Bus metrics"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        
    def record_event_published(self, event_type: str, source_service: str):
        """Record an event publication"""
        events_published_total.labels(
            event_type=event_type,
            source_service=source_service
        ).inc()
        
        logger.debug(
            "Metrics: Event published",
            event_type=event_type,
            source_service=source_service
        )
    
    def record_event_consumed(self, event_type: str, consumer_service: str):
        """Record an event consumption"""
        events_consumed_total.labels(
            event_type=event_type,
            consumer_service=consumer_service
        ).inc()
        
        logger.debug(
            "Metrics: Event consumed",
            event_type=event_type,
            consumer_service=consumer_service
        )
    
    def record_event_failed(self, event_type: str, failure_reason: str):
        """Record a failed event"""
        events_failed_total.labels(
            event_type=event_type,
            failure_reason=failure_reason
        ).inc()
        
        logger.debug(
            "Metrics: Event failed",
            event_type=event_type,
            failure_reason=failure_reason
        )
    
    def record_processing_time(self, event_type: str, operation: str, duration: float):
        """Record event processing time"""
        event_processing_duration.labels(
            event_type=event_type,
            operation=operation
        ).observe(duration)
        
        logger.debug(
            "Metrics: Processing time recorded",
            event_type=event_type,
            operation=operation,
            duration=duration
        )
    
    def update_stream_metrics(self, stream_stats: Dict[str, Any]):
        """Update stream-related metrics"""
        # Update active streams count
        active_streams.set(stream_stats.get('active_streams', 0))
        
        # Update individual stream lengths
        for stream_name, length in stream_stats.get('stream_lengths', {}).items():
            stream_length.labels(stream_name=stream_name).set(length)
        
        # Update dead letter queue size
        dead_letter_queue_size.set(stream_stats.get('dead_letter_size', 0))
        
        logger.debug("Metrics: Stream metrics updated", **stream_stats)
    
    def update_consumer_metrics(self, consumer_count: int):
        """Update consumer-related metrics"""
        active_consumers.set(consumer_count)
        
        logger.debug("Metrics: Consumer metrics updated", consumer_count=consumer_count)
    
    def update_redis_status(self, connected: bool):
        """Update Redis connection status"""
        redis_connection_status.set(1 if connected else 0)
        
        logger.debug("Metrics: Redis status updated", connected=connected)
    
    def get_uptime(self) -> float:
        """Get service uptime in seconds"""
        return time.time() - self.start_time


# Global metrics collector instance
_metrics_collector: MetricsCollector = None


def get_metrics_collector() -> MetricsCollector:
    """Get or create metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def setup_metrics():
    """Setup Prometheus metrics"""
    try:
        # Initialize service info
        service_info.info({
            'service': 'event-bus',
            'version': '1.0.0',
            'description': 'Central message routing and event handling'
        })
        
        # Initialize metrics collector
        collector = get_metrics_collector()
        
        logger.info("Prometheus metrics initialized")
        
    except Exception as e:
        logger.error("Failed to setup metrics", error=str(e))
        raise


class MetricsMiddleware:
    """FastAPI middleware for automatic metrics collection"""
    
    def __init__(self, app):
        self.app = app
        self.collector = get_metrics_collector()
    
    async def __call__(self, scope, receive, send):
        """Process request and collect metrics"""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = time.time()
        
        try:
            await self.app(scope, receive, send)
        finally:
            # Record request duration
            duration = time.time() - start_time
            method = scope["method"]
            path = scope["path"]
            
            # Record HTTP request metrics
            event_processing_duration.labels(
                event_type="http_request",
                operation=f"{method}_{path}"
            ).observe(duration)


def timing_decorator(event_type: str, operation: str):
    """Decorator to measure function execution time"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                collector = get_metrics_collector()
                collector.record_processing_time(event_type, operation, duration)
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                collector = get_metrics_collector()
                collector.record_processing_time(event_type, operation, duration)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Health check metrics
health_check_total = Counter(
    'event_bus_health_checks_total',
    'Total number of health checks performed',
    ['status']
)

health_check_duration = Histogram(
    'event_bus_health_check_duration_seconds',
    'Time spent on health checks'
)


def record_health_check(status: str, duration: float):
    """Record health check metrics"""
    health_check_total.labels(status=status).inc()
    health_check_duration.observe(duration)


# Circuit breaker metrics for external dependencies
circuit_breaker_state = Gauge(
    'event_bus_circuit_breaker_state',
    'Circuit breaker state (0=closed, 1=open, 2=half-open)',
    ['dependency']
)

circuit_breaker_failures = Counter(
    'event_bus_circuit_breaker_failures_total',
    'Total circuit breaker failures',
    ['dependency']
)


def update_circuit_breaker_state(dependency: str, state: int):
    """Update circuit breaker state metrics"""
    circuit_breaker_state.labels(dependency=dependency).set(state)


def record_circuit_breaker_failure(dependency: str):
    """Record circuit breaker failure"""
    circuit_breaker_failures.labels(dependency=dependency).inc()


# Performance monitoring
async def collect_performance_metrics(event_bus) -> Dict[str, Any]:
    """Collect comprehensive performance metrics"""
    try:
        collector = get_metrics_collector()
        
        # Get event bus metrics
        event_metrics = await event_bus.get_metrics()
        
        # Update Prometheus metrics
        collector.update_stream_metrics({
            'active_streams': 1,  # Main event stream
            'stream_lengths': {
                'main': event_metrics.total_events_published,
                'dead_letter': event_metrics.events_in_dead_letter
            },
            'dead_letter_size': event_metrics.events_in_dead_letter
        })
        
        collector.update_consumer_metrics(event_metrics.active_consumers)
        
        # Check Redis connectivity
        redis_connected = await event_bus.health_check()
        collector.update_redis_status(redis_connected)
        
        return {
            'uptime': collector.get_uptime(),
            'redis_connected': redis_connected,
            'event_metrics': event_metrics.dict(),
            'performance': {
                'events_per_second': event_metrics.events_per_second,
                'error_rate': event_metrics.error_rate,
                'average_processing_time': event_metrics.average_processing_time_ms
            }
        }
        
    except Exception as e:
        logger.error("Failed to collect performance metrics", error=str(e))
        return {}