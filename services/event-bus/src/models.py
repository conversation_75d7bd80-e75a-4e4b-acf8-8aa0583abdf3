"""
Event Bus Service Data Models
"""

from datetime import datetime
from typing import Any, Dict, Optional, List
from uuid import UUID, uuid4
from pydantic import BaseModel, Field, validator
from enum import Enum


class EventType(str, Enum):
    """Supported event types"""
    # Command events (imperative)
    CONTENT_GENERATION_REQUESTED = "content.generation.requested"
    COVER_DESIGN_REQUESTED = "cover.design.requested"
    PUBLISHING_INITIATED = "publishing.initiated"
    TREND_ANALYSIS_REQUESTED = "trend.analysis.requested"
    RESEARCH_REQUESTED = "research.requested"
    
    # Domain events (declarative)
    MANUSCRIPT_GENERATED = "manuscript.generated"
    COVER_DESIGNED = "cover.designed"
    BOOK_PUBLISHED = "book.published"
    SALES_DATA_UPDATED = "sales.data.updated"
    TREND_ANALYSIS_COMPLETED = "trend.analysis.completed"
    RESEARCH_COMPLETED = "research.completed"
    
    # System events
    SERVICE_STARTED = "service.started"
    SERVICE_STOPPED = "service.stopped"
    HEALTH_CHECK_FAILED = "health.check.failed"


class EventPriority(str, Enum):
    """Event priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class EventMessage(BaseModel):
    """Event message structure"""
    
    # Event metadata
    event_id: UUID = Field(default_factory=uuid4, description="Unique event identifier")
    event_type: EventType = Field(..., description="Type of event")
    source_service: str = Field(..., description="Service that generated the event")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    
    # Event content
    payload: Dict[str, Any] = Field(..., description="Event payload data")
    correlation_id: Optional[UUID] = Field(default=None, description="Correlation ID for tracking")
    user_id: Optional[str] = Field(default=None, description="User ID associated with event")
    
    # Event properties
    priority: EventPriority = Field(default=EventPriority.MEDIUM, description="Event priority")
    ttl_seconds: Optional[int] = Field(default=None, description="Time to live in seconds")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    
    # Routing
    target_services: Optional[List[str]] = Field(default=None, description="Target services for directed events")
    topic: Optional[str] = Field(default=None, description="Topic for pub/sub routing")
    
    @validator('payload')
    def validate_payload(cls, v):
        """Validate payload is serializable"""
        if not isinstance(v, dict):
            raise ValueError("Payload must be a dictionary")
        return v
    
    @validator('event_type')
    def validate_event_type(cls, v):
        """Ensure event type is valid"""
        if isinstance(v, str):
            try:
                return EventType(v)
            except ValueError:
                raise ValueError(f"Invalid event type: {v}")
        return v


class EventResponse(BaseModel):
    """Response from event operations"""
    success: bool = Field(..., description="Operation success status")
    event_id: Optional[UUID] = Field(default=None, description="Event ID if applicable")
    message: str = Field(..., description="Response message")
    error_code: Optional[str] = Field(default=None, description="Error code if failed")


class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Service health status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    redis_connected: bool = Field(..., description="Redis connection status")
    active_streams: Optional[int] = Field(default=None, description="Number of active streams")
    consumer_groups: Optional[int] = Field(default=None, description="Number of consumer groups")


class DeadLetterMessage(BaseModel):
    """Dead letter queue message"""
    message_id: str = Field(..., description="Message identifier")
    original_event: EventMessage = Field(..., description="Original event message")
    failure_reason: str = Field(..., description="Reason for failure")
    failed_at: datetime = Field(..., description="Failure timestamp")
    retry_count: int = Field(..., description="Number of retry attempts")
    last_error: Optional[str] = Field(default=None, description="Last error message")


class ConsumerInfo(BaseModel):
    """Consumer information"""
    consumer_id: str = Field(..., description="Consumer identifier")
    service_id: str = Field(..., description="Service identifier")
    group_name: str = Field(..., description="Consumer group name")
    last_seen: datetime = Field(..., description="Last activity timestamp")
    messages_processed: int = Field(default=0, description="Total messages processed")
    current_lag: int = Field(default=0, description="Current message lag")


class StreamInfo(BaseModel):
    """Stream information"""
    stream_name: str = Field(..., description="Stream name")
    length: int = Field(..., description="Number of messages in stream")
    first_entry_id: Optional[str] = Field(default=None, description="ID of first message")
    last_entry_id: Optional[str] = Field(default=None, description="ID of last message")
    consumer_groups: List[str] = Field(default_factory=list, description="Associated consumer groups")
    max_deleted_entry_id: Optional[str] = Field(default=None, description="Last deleted entry ID")


class EventMetrics(BaseModel):
    """Event processing metrics"""
    total_events_published: int = Field(default=0, description="Total events published")
    total_events_consumed: int = Field(default=0, description="Total events consumed")
    events_in_dead_letter: int = Field(default=0, description="Events in dead letter queue")
    active_consumers: int = Field(default=0, description="Number of active consumers")
    average_processing_time_ms: float = Field(default=0.0, description="Average processing time")
    events_per_second: float = Field(default=0.0, description="Current events per second")
    error_rate: float = Field(default=0.0, description="Error rate percentage")


# Event payload schemas for specific event types
class ContentGenerationPayload(BaseModel):
    """Payload for content generation events"""
    book_id: str
    user_id: str
    title: str
    category: str
    target_audience: str
    writing_style: str
    target_length: int
    ai_provider: str = "openai"


class CoverDesignPayload(BaseModel):
    """Payload for cover design events"""
    book_id: str
    user_id: str
    title: str
    author_name: str
    genre: str
    target_audience: str
    style_preferences: Optional[Dict[str, Any]] = None


class PublishingPayload(BaseModel):
    """Payload for publishing events"""
    book_id: str
    user_id: str
    manuscript_path: str
    cover_paths: List[str]
    pricing_config: Dict[str, Any]
    auto_publish: bool = False


class TrendAnalysisPayload(BaseModel):
    """Payload for trend analysis events"""
    user_id: str
    categories: List[str]
    keywords: Optional[List[str]] = None
    analysis_type: str = "comprehensive"
    max_results: int = 50