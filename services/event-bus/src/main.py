"""
Event Bus Service - Main Application
Central message routing and event handling for Publish AI microservices
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
# Optional OpenTelemetry imports
try:
    from opentelemetry import trace
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    from opentelemetry.instrumentation.redis import RedisInstrumentor
    TELEMETRY_AVAILABLE = True
except ImportError:
    TELEMETRY_AVAILABLE = False
    trace = None
    FastAPIInstrumentor = None
    RedisInstrumentor = None
from prometheus_client import make_asgi_app
import structlog

from .config import Settings
from .event_bus import EventBus
from .models import EventMessage, EventResponse, HealthResponse
from .security import verify_api_key, get_current_service
from .metrics import setup_metrics

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)
tracer = trace.get_tracer(__name__) if TELEMETRY_AVAILABLE else None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    logger.info("Starting Event Bus Service")
    
    # Initialize event bus
    await app.state.event_bus.initialize()
    logger.info("Event bus initialized")
    
    # Setup metrics
    setup_metrics()
    
    yield
    
    # Cleanup
    logger.info("Shutting down Event Bus Service")
    await app.state.event_bus.close()


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = Settings()
    
    app = FastAPI(
        title="Event Bus Service",
        description="Central message routing and event handling for Publish AI",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize event bus
    app.state.event_bus = EventBus(settings)
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    return app


app = create_app()

# Setup OpenTelemetry instrumentation if available
if TELEMETRY_AVAILABLE:
    FastAPIInstrumentor.instrument_app(app)
    RedisInstrumentor().instrument()


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis connectivity
        redis_healthy = await app.state.event_bus.health_check()
        
        status = "healthy" if redis_healthy else "unhealthy"
        return HealthResponse(
            status=status,
            service="event-bus",
            version="1.0.0",
            redis_connected=redis_healthy
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/events/publish", response_model=EventResponse)
async def publish_event(
    event: EventMessage,
    service_id: str = Depends(get_current_service)
):
    """Publish an event to the event bus"""
    try:
        event_id = await app.state.event_bus.publish(event, service_id)
        
        logger.info(
            "Event published",
            event_id=event_id,
            event_type=event.event_type,
            service_id=service_id
        )
        
        return EventResponse(
            success=True,
            event_id=event_id,
            message="Event published successfully"
        )
        
    except Exception as e:
        logger.error(
            "Failed to publish event",
            error=str(e),
            event_type=event.event_type,
            service_id=service_id
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to publish event: {str(e)}"
        )


@app.post("/events/subscribe/{topic}")
async def subscribe_to_topic(
    topic: str,
    service_id: str = Depends(get_current_service)
):
    """Subscribe a service to a topic"""
    try:
        await app.state.event_bus.subscribe(topic, service_id)
        
        logger.info(
            "Service subscribed to topic",
            topic=topic,
            service_id=service_id
        )
        
        return {"success": True, "message": f"Subscribed to topic: {topic}"}
        
    except Exception as e:
        logger.error(
            "Failed to subscribe to topic",
            error=str(e),
            topic=topic,
            service_id=service_id
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to subscribe: {str(e)}"
        )


@app.get("/events/stream/{topic}")
async def stream_events(
    topic: str,
    service_id: str = Depends(get_current_service)
):
    """Stream events from a topic (Server-Sent Events)"""
    from fastapi.responses import StreamingResponse
    
    async def event_generator():
        """Generate events for SSE"""
        try:
            async for event in app.state.event_bus.stream_events(topic, service_id):
                yield f"data: {event.model_dump_json()}\n\n"
        except Exception as e:
            logger.error("Event streaming failed", error=str(e))
            yield f"data: {{'error': '{str(e)}'}}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


@app.get("/events/dead-letter")
async def get_dead_letter_queue(
    service_id: str = Depends(get_current_service)
):
    """Get messages from dead letter queue"""
    try:
        messages = await app.state.event_bus.get_dead_letter_messages()
        return {
            "success": True,
            "messages": messages,
            "count": len(messages)
        }
    except Exception as e:
        logger.error("Failed to get dead letter messages", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get dead letter messages: {str(e)}"
        )


@app.delete("/events/dead-letter/{message_id}")
async def delete_dead_letter_message(
    message_id: str,
    service_id: str = Depends(get_current_service)
):
    """Delete a message from dead letter queue"""
    try:
        success = await app.state.event_bus.delete_dead_letter_message(message_id)
        
        if success:
            return {"success": True, "message": "Message deleted"}
        else:
            raise HTTPException(status_code=404, detail="Message not found")
            
    except Exception as e:
        logger.error("Failed to delete dead letter message", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete message: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.debug,
        log_level="info"
    )