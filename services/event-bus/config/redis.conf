# Redis Configuration for Event Bus Service

# Basic Configuration
port 6379
bind 0.0.0.0
timeout 300
tcp-keepalive 60

# Memory Configuration
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence Configuration
save 900 1
save 300 10
save 60 10000

# Append Only File
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Security
# requirepass will be set via command line

# Logging
loglevel notice
logfile ""

# Client Configuration
maxclients 1000

# Network
tcp-backlog 511

# Performance
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Streams Configuration (Redis 5.0+)
stream-node-max-bytes 4096
stream-node-max-entries 100

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Event Notification
notify-keyspace-events ""

# Advanced Configuration
hz 10
dynamic-hz yes
rdbcompression yes
rdbchecksum yes