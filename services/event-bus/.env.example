# Event Bus Service Configuration

# Redis Configuration
EVENT_BUS_REDIS_URL=redis://localhost:6379
EVENT_BUS_REDIS_PASSWORD=
EVENT_BUS_REDIS_DB=0

# Security
EVENT_BUS_ENCRYPTION_KEY=  # Generate with: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"

# Service Configuration
EVENT_BUS_DEBUG=false
EVENT_BUS_SERVICE_NAME=event-bus

# Event Configuration
EVENT_BUS_EVENT_RETENTION_HOURS=168  # 7 days
EVENT_BUS_MAX_RETRY_ATTEMPTS=3
EVENT_BUS_DEAD_LETTER_QUEUE_TTL=604800  # 7 days

# CORS
EVENT_BUS_ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# Monitoring
EVENT_BUS_METRICS_ENABLED=true
EVENT_BUS_TRACING_ENABLED=true

# Consumer Configuration
EVENT_BUS_CONSUMER_GROUP_NAME=event-bus-consumers
EVENT_BUS_CONSUMER_TIMEOUT_MS=30000

# Stream Configuration
EVENT_BUS_MAX_STREAM_LENGTH=10000
EVENT_BUS_STREAM_BLOCK_TIME_MS=1000