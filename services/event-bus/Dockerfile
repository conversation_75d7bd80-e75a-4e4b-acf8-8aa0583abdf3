FROM python:3.11-slim

LABEL maintainer="Publish AI Team"
LABEL version="1.0.0"
LABEL description="Event Bus Service for Publish AI microservices"

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY config/ ./config/

# Create non-root user
RUN groupadd -r eventbus && useradd -r -g eventbus eventbus
RUN chown -R eventbus:eventbus /app
USER eventbus

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Run the application
CMD ["python", "-m", "src.main"]