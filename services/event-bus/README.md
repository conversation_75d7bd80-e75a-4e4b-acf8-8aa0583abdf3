# Event Bus Service

Central message routing and event handling service for the Publish AI microservices architecture.

## 🎯 Overview

The Event Bus Service provides reliable, scalable event-driven communication between microservices using Redis Streams and pub/sub patterns.

## 🏗️ Architecture

### Core Components
- **Redis Streams**: Persistent event logs with consumer groups
- **Pub/Sub**: Real-time event broadcasting
- **Dead Letter Queues**: Failed message handling
- **Event Schema Registry**: Pydantic model validation

### Event Flow
```
Producer Service → Event Bus → [Routing] → Consumer Services
                     ↓
               [Dead Letter Queue]
```

## 📨 Event Types

### Command Events (Imperative)
- `ContentGenerationRequested`
- `CoverDesignRequested`
- `PublishingInitiated`

### Domain Events (Declarative)
- `ManuscriptGenerated`
- `CoverDesigned`
- `BookPublished`
- `SalesDataUpdated`

## 🔐 Security Features

- **Event Encryption**: AES-256 payload encryption
- **Authentication**: Redis AUTH + API key validation
- **Authorization**: Topic-based access control
- **Audit Logging**: Complete event trail

## 🚀 Deployment

### Docker Compose
```bash
cd services/event-bus
docker-compose up -d
```

### Kubernetes
```bash
kubectl apply -f k8s/
```

## 📊 Monitoring

- **Health Endpoint**: `/health`
- **Metrics**: Prometheus format on `/metrics`
- **Tracing**: OpenTelemetry integration

## 🔧 Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |
| `REDIS_PASSWORD` | Redis authentication | `None` |
| `EVENT_RETENTION_HOURS` | Event log retention | `168` (7 days) |
| `MAX_RETRY_ATTEMPTS` | Failed message retries | `3` |
| `DEAD_LETTER_QUEUE_TTL` | DLQ message TTL | `604800` (7 days) |