version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: event-bus-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - event-bus-network

  event-bus:
    build: .
    container_name: event-bus-service
    ports:
      - "8080:8080"
    environment:
      - EVENT_BUS_REDIS_URL=redis://redis:6379
      - EVENT_BUS_REDIS_PASSWORD=${REDIS_PASSWORD:-defaultpassword}
      - EVENT_BUS_DEBUG=${DEBUG:-false}
      - EVENT_BUS_ENCRYPTION_KEY=${ENCRYPTION_KEY}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - event-bus-network
    volumes:
      - ./logs:/app/logs

  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-defaultpassword}
    depends_on:
      - redis
    networks:
      - event-bus-network

volumes:
  redis_data:
    driver: local

networks:
  event-bus-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16