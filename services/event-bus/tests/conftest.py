"""
Pytest configuration for Event Bus Service tests
"""

import asyncio
import pytest
import redis.asyncio as redis
from typing import Generator


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def redis_server():
    """Ensure Redis server is available for testing"""
    try:
        client = redis.from_url("redis://localhost:6379/1")
        await client.ping()
        await client.close()
        yield "redis://localhost:6379/1"
    except Exception as e:
        pytest.skip(f"Redis server not available: {e}")


@pytest.fixture(autouse=True)
async def clean_redis():
    """Clean Redis test database before each test"""
    try:
        client = redis.from_url("redis://localhost:6379/1")
        await client.flushdb()
        await client.close()
    except Exception:
        pass  # Redis not available, skip cleanup