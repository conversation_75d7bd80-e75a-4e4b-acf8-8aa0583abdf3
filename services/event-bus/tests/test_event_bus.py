"""
Event Bus Service Tests
Comprehensive test suite for the Event Bus Service
"""

import asyncio
import json
import pytest
from datetime import datetime
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi.testclient import TestClient
import redis.asyncio as redis

from src.main import create_app
from src.event_bus import EventBus
from src.models import EventMessage, EventType, EventPriority
from src.config import Settings


@pytest.fixture
def test_settings():
    """Test configuration settings"""
    return Settings(
        debug=True,
        redis_url="redis://localhost:6379/1",  # Use test database
        event_retention_hours=1,
        max_retry_attempts=2,
        encryption_key=None  # Disable encryption for tests
    )


@pytest.fixture
async def redis_client():
    """Redis client for testing"""
    client = redis.from_url("redis://localhost:6379/1")
    
    # Clear test database
    await client.flushdb()
    
    yield client
    
    # Cleanup
    await client.flushdb()
    await client.close()


@pytest.fixture
async def event_bus(test_settings, redis_client):
    """Event bus instance for testing"""
    bus = EventBus(test_settings)
    bus.redis_client = redis_client
    await bus._create_consumer_groups()
    
    yield bus
    
    await bus.close()


@pytest.fixture
def test_event():
    """Sample event for testing"""
    return EventMessage(
        event_type=EventType.CONTENT_GENERATION_REQUESTED,
        source_service="test-service",
        payload={
            "book_id": "test-book-123",
            "user_id": "test-user-456",
            "title": "Test Book",
            "category": "fiction"
        },
        priority=EventPriority.MEDIUM,
        user_id="test-user-456"
    )


@pytest.fixture
def client():
    """FastAPI test client"""
    app = create_app()
    return TestClient(app)


class TestEventBus:
    """Test Event Bus core functionality"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, test_settings):
        """Test event bus initialization"""
        bus = EventBus(test_settings)
        
        # Mock Redis client
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        
        with patch('redis.asyncio.from_url', return_value=mock_redis):
            await bus.initialize()
            
        assert bus.redis_client is not None
        mock_redis.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_health_check(self, event_bus):
        """Test health check functionality"""
        # Mock successful ping
        event_bus.redis_client.ping = AsyncMock(return_value=True)
        
        health = await event_bus.health_check()
        assert health is True
        
        # Mock failed ping
        event_bus.redis_client.ping = AsyncMock(side_effect=Exception("Connection failed"))
        
        health = await event_bus.health_check()
        assert health is False
    
    @pytest.mark.asyncio
    async def test_publish_event(self, event_bus, test_event):
        """Test event publishing"""
        # Mock Redis xadd
        event_bus.redis_client.xadd = AsyncMock(return_value="**********-0")
        
        event_id = await event_bus.publish(test_event, "test-service")
        
        assert event_id == test_event.event_id
        assert event_bus.metrics["events_published"] == 1
        
        # Verify Redis calls
        event_bus.redis_client.xadd.assert_called()
    
    @pytest.mark.asyncio
    async def test_subscribe_to_topic(self, event_bus):
        """Test topic subscription"""
        # Mock Redis operations
        event_bus.redis_client.xgroup_create = AsyncMock()
        event_bus.redis_client.sadd = AsyncMock()
        event_bus.redis_client.expire = AsyncMock()
        
        await event_bus.subscribe("test-topic", "test-service")
        
        # Verify subscription calls
        event_bus.redis_client.xgroup_create.assert_called()
        event_bus.redis_client.sadd.assert_called()
        event_bus.redis_client.expire.assert_called()
    
    @pytest.mark.asyncio
    async def test_event_streaming(self, event_bus, test_event):
        """Test event streaming functionality"""
        # Mock Redis stream reading
        mock_messages = [
            ("events:topic:test", [
                ("**********-0", {
                    b"event_id": str(test_event.event_id).encode(),
                    b"event_type": test_event.event_type.value.encode(),
                    b"source_service": test_event.source_service.encode(),
                    b"timestamp": test_event.timestamp.isoformat().encode(),
                    b"payload": json.dumps(test_event.payload).encode(),
                    b"correlation_id": b"",
                    b"user_id": test_event.user_id.encode(),
                    b"priority": test_event.priority.value.encode(),
                    b"ttl_seconds": b"",
                    b"retry_count": b"0",
                    b"target_services": b"[]",
                    b"topic": b""
                })
            ])
        ]
        
        event_bus.redis_client.xreadgroup = AsyncMock(return_value=mock_messages)
        event_bus.redis_client.xack = AsyncMock()
        
        # Test streaming (get first event then break)
        events = []
        async for event in event_bus.stream_events("test", "test-service"):
            events.append(event)
            if len(events) >= 1:
                break
        
        assert len(events) == 1
        assert events[0].event_type == test_event.event_type
        assert events[0].payload == test_event.payload
    
    @pytest.mark.asyncio
    async def test_dead_letter_queue(self, event_bus):
        """Test dead letter queue functionality"""
        # Mock Redis operations
        mock_messages = [
            ("dlq-msg-1", {
                b"message_id": b"**********-0",
                b"original_event": json.dumps({
                    "event_id": str(uuid4()),
                    "event_type": "test.event",
                    "source_service": "test",
                    "timestamp": datetime.utcnow().isoformat(),
                    "payload": "{}",
                    "correlation_id": "",
                    "user_id": "",
                    "priority": "medium",
                    "ttl_seconds": "",
                    "retry_count": "0",
                    "target_services": "[]",
                    "topic": ""
                }).encode(),
                b"failure_reason": b"Test failure",
                b"failed_at": datetime.utcnow().isoformat().encode(),
                b"retry_count": b"1",
                b"last_error": b"Test error"
            })
        ]
        
        event_bus.redis_client.xrange = AsyncMock(return_value=mock_messages)
        
        dead_letters = await event_bus.get_dead_letter_messages()
        
        assert len(dead_letters) == 1
        assert dead_letters[0].failure_reason == "Test failure"
        assert dead_letters[0].retry_count == 1
    
    @pytest.mark.asyncio
    async def test_metrics_collection(self, event_bus):
        """Test metrics collection"""
        # Mock Redis stream info
        mock_stream_info = {
            "length": 10,
            "first-entry": "**********-0",
            "last-entry": "**********-9"
        }
        
        mock_dead_letter_info = {
            "length": 2
        }
        
        event_bus.redis_client.xinfo_stream = AsyncMock(
            side_effect=[mock_stream_info, mock_dead_letter_info]
        )
        event_bus.redis_client.xinfo_groups = AsyncMock(return_value=[])
        
        # Set some test metrics
        event_bus.metrics["events_published"] = 100
        event_bus.metrics["events_consumed"] = 95
        event_bus.metrics["events_failed"] = 5
        
        metrics = await event_bus.get_metrics()
        
        assert metrics.total_events_published == 100
        assert metrics.total_events_consumed == 95
        assert metrics.events_in_dead_letter == 2
        assert metrics.error_rate == 5.0


class TestEventBusAPI:
    """Test Event Bus API endpoints"""
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        with patch.object(TestClient, 'app') as mock_app:
            mock_event_bus = AsyncMock()
            mock_event_bus.health_check.return_value = True
            mock_app.state.event_bus = mock_event_bus
            
            response = client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "event-bus"
    
    def test_publish_event_endpoint(self, client):
        """Test event publishing endpoint"""
        event_data = {
            "event_type": "content.generation.requested",
            "source_service": "test-service",
            "payload": {
                "book_id": "test-book-123",
                "title": "Test Book"
            },
            "priority": "medium"
        }
        
        # Mock authentication
        with patch('src.security.verify_api_key') as mock_auth:
            mock_auth.return_value = {"service_id": "test-service"}
            
            with patch.object(TestClient, 'app') as mock_app:
                mock_event_bus = AsyncMock()
                mock_event_bus.publish.return_value = uuid4()
                mock_app.state.event_bus = mock_event_bus
                
                response = client.post(
                    "/events/publish",
                    json=event_data,
                    headers={"Authorization": "Bearer test-key"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "event_id" in data
    
    def test_subscribe_endpoint(self, client):
        """Test topic subscription endpoint"""
        # Mock authentication
        with patch('src.security.verify_api_key') as mock_auth:
            mock_auth.return_value = {"service_id": "test-service"}
            
            with patch.object(TestClient, 'app') as mock_app:
                mock_event_bus = AsyncMock()
                mock_event_bus.subscribe.return_value = None
                mock_app.state.event_bus = mock_event_bus
                
                response = client.post(
                    "/events/subscribe/test-topic",
                    headers={"Authorization": "Bearer test-key"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "test-topic" in data["message"]
    
    def test_dead_letter_queue_endpoint(self, client):
        """Test dead letter queue endpoint"""
        # Mock authentication
        with patch('src.security.verify_api_key') as mock_auth:
            mock_auth.return_value = {"service_id": "test-service"}
            
            with patch.object(TestClient, 'app') as mock_app:
                mock_event_bus = AsyncMock()
                mock_event_bus.get_dead_letter_messages.return_value = []
                mock_app.state.event_bus = mock_event_bus
                
                response = client.get(
                    "/events/dead-letter",
                    headers={"Authorization": "Bearer test-key"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["count"] == 0
                assert data["messages"] == []


class TestSecurity:
    """Test security functionality"""
    
    def test_api_key_generation(self):
        """Test API key generation"""
        from src.security import SecurityManager
        
        settings = Settings()
        security_manager = SecurityManager(settings)
        
        api_key = security_manager.generate_api_key()
        
        assert len(api_key) > 20  # URL-safe base64 should be reasonably long
        assert isinstance(api_key, str)
    
    def test_api_key_hashing(self):
        """Test API key hashing"""
        from src.security import SecurityManager
        
        settings = Settings()
        security_manager = SecurityManager(settings)
        
        api_key = "test-api-key"
        hash1 = security_manager.hash_api_key(api_key)
        hash2 = security_manager.hash_api_key(api_key)
        
        assert hash1 == hash2  # Same input should produce same hash
        assert len(hash1) == 64  # SHA256 produces 64 character hex string
    
    def test_encryption_disabled(self):
        """Test encryption when disabled"""
        from src.security import SecurityManager
        
        settings = Settings(encryption_key=None)
        security_manager = SecurityManager(settings)
        
        payload = "test payload"
        encrypted = security_manager.encrypt_payload(payload)
        decrypted = security_manager.decrypt_payload(encrypted)
        
        assert encrypted == payload  # No encryption when key is None
        assert decrypted == payload


class TestModels:
    """Test Pydantic models"""
    
    def test_event_message_creation(self):
        """Test EventMessage model creation"""
        event = EventMessage(
            event_type=EventType.CONTENT_GENERATION_REQUESTED,
            source_service="test-service",
            payload={"test": "data"},
            user_id="test-user"
        )
        
        assert event.event_type == EventType.CONTENT_GENERATION_REQUESTED
        assert event.source_service == "test-service"
        assert event.payload == {"test": "data"}
        assert event.user_id == "test-user"
        assert event.priority == EventPriority.MEDIUM  # Default
        assert event.retry_count == 0  # Default
    
    def test_event_message_validation(self):
        """Test EventMessage validation"""
        # Test invalid event type
        with pytest.raises(ValueError):
            EventMessage(
                event_type="invalid.event.type",
                source_service="test-service",
                payload={"test": "data"}
            )
        
        # Test invalid payload type
        with pytest.raises(ValueError):
            EventMessage(
                event_type=EventType.CONTENT_GENERATION_REQUESTED,
                source_service="test-service",
                payload="not a dict"  # Should be dict
            )


@pytest.mark.integration
class TestIntegration:
    """Integration tests requiring real Redis"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_event_flow(self, redis_client):
        """Test complete event flow from publish to consume"""
        settings = Settings(redis_url="redis://localhost:6379/1")
        event_bus = EventBus(settings)
        event_bus.redis_client = redis_client
        
        try:
            await event_bus._create_consumer_groups()
            
            # Create test event
            test_event = EventMessage(
                event_type=EventType.CONTENT_GENERATION_REQUESTED,
                source_service="test-service",
                payload={"book_id": "test-123"},
                topic="content-generation"
            )
            
            # Publish event
            await event_bus.publish(test_event, "test-service")
            
            # Subscribe to topic
            await event_bus.subscribe("content-generation", "consumer-service")
            
            # Consume event
            events = []
            async for event in event_bus.stream_events("content-generation", "consumer-service"):
                events.append(event)
                if len(events) >= 1:
                    break
            
            assert len(events) == 1
            consumed_event = events[0]
            assert consumed_event.event_type == test_event.event_type
            assert consumed_event.payload == test_event.payload
            
        finally:
            await event_bus.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])