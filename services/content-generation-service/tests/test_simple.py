"""
Simple tests for Content Generation Service
"""

import pytest
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestSimpleContentProcessor:
    """Simple tests for ContentProcessor"""
    
    @pytest.mark.asyncio
    async def test_basic_initialization(self):
        """Test basic ContentProcessor initialization"""
        from content_processor import ContentProcessor
        
        with patch('content_processor.spacy.load') as mock_spacy:
            mock_spacy.side_effect = OSError("Model not found")
            
            processor = ContentProcessor()
            await processor.initialize()
            
            assert processor.is_ready
            assert processor.nlp is None
    
    @pytest.mark.asyncio
    async def test_text_stats_analysis(self):
        """Test text statistics analysis"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "This is a test. It has two sentences."
        stats = await processor._analyze_text_stats(content)
        
        assert stats["word_count"] == 8
        assert stats["sentence_count"] >= 2
        assert "readability_score" in stats
    
    @pytest.mark.asyncio
    async def test_grammar_enhancement(self):
        """Test grammar enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "This  has   multiple    spaces."
        enhanced = await processor._enhance_grammar(content)
        
        assert "   " not in enhanced
        assert "This has multiple spaces." == enhanced
    
    @pytest.mark.asyncio
    async def test_normalize_content(self):
        """Test content normalization"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        html_content = "<h1>Title</h1><p>Paragraph</p>"
        normalized = await processor._normalize_content(html_content, "html")
        
        assert "#1 Title" in normalized
        assert "Paragraph" in normalized
        assert "<h1>" not in normalized


class TestSimpleManuscriptGenerator:
    """Simple tests for ManuscriptGenerator"""
    
    @pytest.mark.asyncio
    async def test_initialization_no_api_keys(self):
        """Test initialization with no API keys"""
        from manuscript_generator import ManuscriptGenerator
        
        with patch.dict(os.environ, {}, clear=True):
            generator = ManuscriptGenerator()
            
            with pytest.raises(Exception, match="No AI model available"):
                await generator.initialize()
    
    def test_create_generation_prompt(self):
        """Test generation prompt creation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        prompt = generator._create_generation_prompt(
            topic="Test Topic",
            style="professional",
            target_audience="general adults",
            target_length=8000,
            chapter_count=8
        )
        
        assert "Test Topic" in prompt
        assert "professional" in prompt
        assert "8000 words" in prompt
    
    def test_calculate_quality_score(self):
        """Test quality score calculation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        good_content = """
        # Chapter 1
        
        This is well-structured content with proper headings.
        Each paragraph flows well and maintains readability.
        """
        
        score = generator._calculate_quality_score(good_content, 1000, 1000)
        assert 0.5 <= score <= 1.0
    
    def test_estimate_readability(self):
        """Test readability estimation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        # Good readability
        good_text = "This is a sentence with good readability. It flows well."
        score = generator._estimate_readability(good_text)
        assert score >= 0.4
        
        # Poor readability - very long sentence
        poor_text = "This is an extremely long sentence that goes on and on without proper breaks making it difficult to read."
        score = generator._estimate_readability(poor_text)
        assert score >= 0.0


class TestIntegration:
    """Simple integration tests"""
    
    @pytest.mark.asyncio
    async def test_content_processor_full_workflow(self):
        """Test content processor complete workflow"""
        from content_processor import ContentProcessor
        
        with patch('content_processor.spacy.load') as mock_spacy:
            mock_spacy.side_effect = OSError("Model not found")
            
            processor = ContentProcessor()
            await processor.initialize()
            
            # Test complete workflow
            content = "# Test Document\n\nThis is **test content**."
            
            # Test normalization
            normalized = await processor._normalize_content(content, "markdown")
            assert "Test Document" in normalized
            
            # Test enhancement
            enhanced = await processor._enhance_grammar(normalized)
            assert len(enhanced) > 0
            
            # Test analysis
            stats = await processor._analyze_text_stats(enhanced)
            assert stats["word_count"] > 0
            
            await processor.cleanup()
            assert not processor.is_ready


if __name__ == "__main__":
    pytest.main([__file__])