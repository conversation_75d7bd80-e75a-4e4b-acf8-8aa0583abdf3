"""
Tests for Content Generation Service Main Application
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient

# Mock all dependencies before importing main
def mock_manuscript_generator():
    mock = MagicMock()
    mock.initialize = AsyncMock()
    mock.cleanup = AsyncMock()
    mock.is_ready = True
    mock.generate_manuscript = AsyncMock()
    return mock

def mock_content_processor():
    mock = MagicMock()
    mock.initialize = AsyncMock()
    mock.cleanup = AsyncMock()
    mock.is_ready = True
    mock.enhance_content = AsyncMock()
    mock.convert_content = AsyncMock()
    mock.assess_quality = AsyncMock()
    return mock

def mock_event_client():
    mock = MagicMock()
    mock.connect = AsyncMock()
    mock.disconnect = AsyncMock()
    mock.is_connected = True
    mock.publish_event = AsyncMock()
    return mock

def mock_service_registry():
    mock = MagicMock()
    mock.register_service = AsyncMock()
    mock.unregister_service = AsyncMock()
    return mock

def mock_security_manager():
    return MagicMock()

def mock_metrics_collector():
    mock = MagicMock()
    mock.get_metrics = MagicMock(return_value={"test": "metrics"})
    mock.record_generation_request = MagicMock()
    mock.record_generation_completed = MagicMock()
    mock.record_generation_error = MagicMock()
    mock.record_enhancement_completed = MagicMock()
    mock.record_enhancement_error = MagicMock()
    mock.record_conversion_completed = MagicMock()
    mock.record_conversion_error = MagicMock()
    mock.record_quality_assessment_completed = MagicMock()
    mock.record_quality_assessment_error = MagicMock()
    mock.record_batch_generation_request = MagicMock()
    mock.record_batch_generation_error = MagicMock()
    return mock

def verify_api_key_mock():
    return "test-api-key"

# Import everything first, then create the test client
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Mock all the dependencies at module level
with patch('manuscript_generator.ManuscriptGenerator', mock_manuscript_generator), \
     patch('content_processor.ContentProcessor', mock_content_processor), \
     patch('event_client.EventClient', mock_event_client), \
     patch('service_registry_client.ServiceRegistryClient', mock_service_registry), \
     patch('security_manager.SecurityManager', mock_security_manager), \
     patch('monitoring.MetricsCollector', mock_metrics_collector), \
     patch('security_manager.verify_api_key', verify_api_key_mock), \
     patch('monitoring.setup_logging'):
    
    from main import app

client = TestClient(app)


class TestContentGenerationAPI:
    """Test Content Generation Service API endpoints"""
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["service"] == "content-generation-service"
        assert "status" in data
        assert "components" in data
    
    def test_readiness_check(self):
        """Test readiness check endpoint"""
        response = client.get("/ready")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ready"
        assert data["service"] == "content-generation-service"
    
    def test_metrics_endpoint(self):
        """Test metrics endpoint"""
        response = client.get("/metrics")
        assert response.status_code == 200
        data = response.json()
        assert "test" in data
    
    @patch('main.manuscript_generator')
    def test_generate_manuscript_sync(self, mock_generator):
        """Test synchronous manuscript generation"""
        # Mock the generator
        mock_result = MagicMock()
        mock_result.content = "Test manuscript content"
        mock_result.word_count = 1000
        mock_result.quality_score = 0.8
        mock_result.model_dump.return_value = {
            "title": "Test Book",
            "content": "Test manuscript content",
            "word_count": 1000,
            "quality_score": 0.8
        }
        
        mock_generator.generate_manuscript = AsyncMock(return_value=mock_result)
        mock_generator.is_ready = True
        
        request_data = {
            "topic": "Test Topic",
            "style": "professional",
            "target_audience": "general adults",
            "target_length": 8000,
            "chapter_count": 8,
            "include_outline": True,
            "quality_threshold": 0.7,
            "output_formats": ["markdown"]
        }
        
        response = client.post(
            "/generate/sync",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "result" in data
        assert "execution_time" in data
    
    def test_generate_manuscript_async(self):
        """Test asynchronous manuscript generation"""
        request_data = {
            "topic": "Test Topic",
            "style": "professional",
            "target_audience": "general adults",
            "target_length": 8000,
            "chapter_count": 8,
            "include_outline": True,
            "quality_threshold": 0.7,
            "output_formats": ["markdown"]
        }
        
        response = client.post(
            "/generate",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "accepted"
        assert "request_id" in data
    
    def test_get_generation_status_not_found(self):
        """Test getting status for non-existent generation job"""
        response = client.get(
            "/generate/nonexistent-id",
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 404
    
    @patch('main.content_processor')
    def test_enhance_content(self, mock_processor):
        """Test content enhancement"""
        mock_processor.enhance_content = AsyncMock(return_value={
            "enhanced_content": "Enhanced content",
            "improvement_metrics": {"readability_improvement": 0.1}
        })
        mock_processor.is_ready = True
        
        request_data = {
            "content": "Original content",
            "enhancement_type": "grammar",
            "target_audience": "general",
            "style_guide": "formal"
        }
        
        response = client.post(
            "/enhance",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "result" in data
    
    @patch('main.content_processor')
    def test_convert_document(self, mock_processor):
        """Test document conversion"""
        mock_processor.convert_content = AsyncMock(return_value={
            "docx": {"content": "converted", "mime_type": "application/docx"}
        })
        mock_processor.is_ready = True
        
        request_data = {
            "content": "Markdown content",
            "source_format": "markdown",
            "target_formats": ["docx"],
            "document_title": "Test Document",
            "author_name": "Test Author"
        }
        
        response = client.post(
            "/convert",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "result" in data
    
    @patch('main.content_processor')
    def test_assess_quality(self, mock_processor):
        """Test content quality assessment"""
        mock_processor.assess_quality = AsyncMock(return_value={
            "overall_quality": 0.8,
            "readability_score": 0.85,
            "grammar_score": 0.9,
            "recommendations": ["Great content!"]
        })
        mock_processor.is_ready = True
        
        response = client.post(
            "/assess-quality",
            params={"content": "Test content"},
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "result" in data
    
    def test_batch_generate_manuscripts(self):
        """Test batch manuscript generation"""
        request_data = {
            "topics": ["Topic 1", "Topic 2"],
            "template_config": {
                "topic": "Template",
                "style": "professional",
                "target_audience": "general adults",
                "target_length": 8000,
                "chapter_count": 8,
                "include_outline": True,
                "quality_threshold": 0.7,
                "output_formats": ["markdown"]
            },
            "priority": "normal"
        }
        
        response = client.post(
            "/batch-generate",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "accepted"
        assert "request_id" in data
    
    def test_unauthorized_access(self):
        """Test API access without authentication"""
        response = client.get("/generate/test-id")
        assert response.status_code == 422  # FastAPI validation error for missing dependency


class TestRequestModels:
    """Test request/response models"""
    
    def test_manuscript_generation_request_validation(self):
        """Test manuscript generation request validation"""
        # Valid request
        valid_data = {
            "topic": "Test Topic",
            "style": "professional",
            "target_audience": "general adults",
            "target_length": 8000,
            "chapter_count": 8,
            "include_outline": True,
            "quality_threshold": 0.7,
            "output_formats": ["markdown"]
        }
        
        response = client.post(
            "/generate",
            json=valid_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        
        # Invalid request - missing required field
        invalid_data = {
            "style": "professional",
            "target_audience": "general adults"
        }
        
        response = client.post(
            "/generate",
            json=invalid_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_content_enhancement_request_validation(self):
        """Test content enhancement request validation"""
        # Valid request
        valid_data = {
            "content": "Test content",
            "enhancement_type": "grammar"
        }
        
        response = client.post(
            "/enhance",
            json=valid_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 200
        
        # Invalid request - missing required fields
        invalid_data = {
            "enhancement_type": "grammar"
        }
        
        response = client.post(
            "/enhance",
            json=invalid_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 422  # Validation error


class TestErrorHandling:
    """Test error handling scenarios"""
    
    @patch('main.manuscript_generator')
    def test_generator_not_available(self, mock_generator):
        """Test handling when manuscript generator is not available"""
        mock_generator.is_ready = False
        
        request_data = {
            "topic": "Test Topic",
            "style": "professional",
            "target_audience": "general adults",
            "target_length": 8000,
            "chapter_count": 8,
            "include_outline": True,
            "quality_threshold": 0.7,
            "output_formats": ["markdown"]
        }
        
        response = client.post(
            "/generate/sync",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 503
    
    @patch('main.content_processor')
    def test_processor_not_available(self, mock_processor):
        """Test handling when content processor is not available"""
        mock_processor.is_ready = False
        
        request_data = {
            "content": "Test content",
            "enhancement_type": "grammar"
        }
        
        response = client.post(
            "/enhance",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 503
    
    @patch('main.manuscript_generator')
    def test_generation_exception(self, mock_generator):
        """Test handling of generation exceptions"""
        mock_generator.is_ready = True
        mock_generator.generate_manuscript = AsyncMock(side_effect=Exception("Generation failed"))
        
        request_data = {
            "topic": "Test Topic",
            "style": "professional",
            "target_audience": "general adults",
            "target_length": 8000,
            "chapter_count": 8,
            "include_outline": True,
            "quality_threshold": 0.7,
            "output_formats": ["markdown"]
        }
        
        response = client.post(
            "/generate/sync",
            json=request_data,
            headers={"X-API-Key": "test-key"}
        )
        
        assert response.status_code == 500


if __name__ == "__main__":
    pytest.main([__file__])