"""
Test configuration and fixtures for Content Generation Service
"""

import pytest
import sys
import os
from unittest.mock import MagicMock, AsyncMock

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture
def mock_openai_model():
    """Mock OpenAI model"""
    mock = MagicMock()
    mock.return_value = MagicMock()
    return mock


@pytest.fixture
def mock_anthropic_model():
    """Mock Anthropic model"""
    mock = MagicMock()
    mock.return_value = MagicMock()
    return mock


@pytest.fixture
def mock_pydantic_agent():
    """Mock PydanticAI TypedAgent"""
    mock_agent = MagicMock()
    mock_agent.run = AsyncMock()
    
    mock_class = MagicMock()
    mock_class.return_value = mock_agent
    
    return mock_class, mock_agent


@pytest.fixture
def mock_event_client():
    """Mock event client"""
    mock = MagicMock()
    mock.connect = AsyncMock()
    mock.disconnect = AsyncMock()
    mock.publish_event = AsyncMock()
    mock.is_connected = True
    return mock


@pytest.fixture
def mock_service_registry():
    """Mock service registry client"""
    mock = MagicMock()
    mock.register_service = AsyncMock()
    mock.unregister_service = AsyncMock()
    return mock


@pytest.fixture
def mock_security_manager():
    """Mock security manager"""
    mock = MagicMock()
    mock.generate_api_key = MagicMock(return_value="test-api-key")
    mock.verify_api_key = MagicMock(return_value=True)
    return mock


@pytest.fixture
def mock_metrics():
    """Mock metrics collector"""
    mock = MagicMock()
    mock.record_manuscript_generation = MagicMock()
    mock.record_manuscript_error = MagicMock()
    mock.record_enhancement_completed = MagicMock()
    mock.record_enhancement_error = MagicMock()
    mock.record_conversion_completed = MagicMock()
    mock.record_conversion_error = MagicMock()
    mock.record_quality_assessment_completed = MagicMock()
    mock.record_quality_assessment_error = MagicMock()
    mock.record_batch_generation_request = MagicMock()
    mock.record_batch_generation_error = MagicMock()
    mock.get_metrics = MagicMock(return_value={"test": "metrics"})
    return mock


@pytest.fixture
def sample_manuscript_content():
    """Sample manuscript content for testing"""
    return """
    # The Complete Guide to AI and Machine Learning
    
    ## Chapter 1: Introduction to Artificial Intelligence
    
    Artificial Intelligence (AI) represents one of the most transformative 
    technologies of our time. This chapter provides a comprehensive overview 
    of AI fundamentals and applications.
    
    ### What is AI?
    
    AI refers to computer systems that can perform tasks typically requiring 
    human intelligence. These include learning, reasoning, perception, and 
    language understanding.
    
    ## Chapter 2: Machine Learning Basics
    
    Machine Learning is a subset of AI that enables computers to learn and 
    improve from experience without being explicitly programmed.
    
    ### Types of Machine Learning
    
    1. Supervised Learning
    2. Unsupervised Learning  
    3. Reinforcement Learning
    
    ## Chapter 3: Deep Learning and Neural Networks
    
    Deep Learning uses neural networks with multiple layers to model and 
    understand complex patterns in data.
    
    ## Conclusion
    
    This guide has covered the essential concepts of AI and Machine Learning, 
    providing readers with a solid foundation for further exploration.
    """


@pytest.fixture
def sample_trend_data():
    """Sample trend data for testing"""
    return {
        "search_trends": ["artificial intelligence", "machine learning", "AI automation"],
        "keywords": ["AI", "ML", "neural networks", "deep learning"],
        "market_insights": "Growing interest in AI applications across industries",
        "competitor_analysis": "High competition in AI education content",
        "popularity_score": 85,
        "growth_rate": 25.5
    }


@pytest.fixture
def sample_enhancement_request():
    """Sample content enhancement request"""
    return {
        "content": "This is sample content that needs enhancement.",
        "enhancement_type": "grammar",
        "target_audience": "general adults",
        "style_guide": "formal"
    }


@pytest.fixture
def sample_conversion_request():
    """Sample document conversion request"""
    return {
        "content": "# Sample Document\n\nThis is **sample content** for conversion.",
        "source_format": "markdown",
        "target_formats": ["html", "docx", "pdf"],
        "document_title": "Sample Document",
        "author_name": "Test Author"
    }


@pytest.fixture
def sample_generation_request():
    """Sample manuscript generation request"""
    return {
        "topic": "The Future of Artificial Intelligence",
        "trend_data": {
            "search_trends": ["AI", "automation"],
            "keywords": ["artificial intelligence", "machine learning"]
        },
        "style": "professional",
        "target_audience": "tech professionals",
        "target_length": 10000,
        "chapter_count": 8,
        "include_outline": True,
        "quality_threshold": 0.8,
        "output_formats": ["markdown", "docx", "pdf"]
    }


@pytest.fixture
def sample_manuscript_result():
    """Sample manuscript generation result"""
    from unittest.mock import MagicMock
    
    result = MagicMock()
    result.title = "The Future of Artificial Intelligence"
    result.content = "# The Future of Artificial Intelligence\n\nAI is transforming our world..."
    result.outline = "1. Introduction\n2. Current State\n3. Future Prospects"
    result.chapters = [
        {"title": "Introduction", "content": "AI introduction content"},
        {"title": "Current State", "content": "Current AI state content"},
        {"title": "Future Prospects", "content": "AI future content"}
    ]
    result.word_count = 8500
    result.chapter_count = 3
    result.quality_score = 0.85
    result.generation_metadata = {
        "generated_at": "2024-01-01T12:00:00Z",
        "topic": "The Future of Artificial Intelligence",
        "target_length": 10000,
        "actual_length": 8500
    }
    result.model_dump.return_value = {
        "title": result.title,
        "content": result.content,
        "outline": result.outline,
        "chapters": result.chapters,
        "word_count": result.word_count,
        "chapter_count": result.chapter_count,
        "quality_score": result.quality_score,
        "generation_metadata": result.generation_metadata
    }
    
    return result


# Global test configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )


# Skip slow tests by default
def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle markers"""
    try:
        if config.getoption("--runslow"):
            # --runslow given in cli: do not skip slow tests
            return
    except ValueError:
        # --runslow option not available, skip slow tests
        pass
    
    skip_slow = pytest.mark.skip(reason="need --runslow option to run")
    for item in items:
        if "slow" in item.keywords:
            item.add_marker(skip_slow)