"""
Tests for Content Processor Component
"""

import base64
import io
import pytest
from unittest.mock import MagicMock, patch


class TestContentProcessor:
    """Test ContentProcessor class"""
    
    @pytest.mark.asyncio
    async def test_initialization_success(self):
        """Test successful initialization"""
        from content_processor import ContentProcessor
        
        with patch('content_processor.spacy.load') as mock_spacy:
            mock_nlp = MagicMock()
            mock_spacy.return_value = mock_nlp
            
            processor = ContentProcessor()
            await processor.initialize()
            
            assert processor.is_ready
            assert processor.nlp == mock_nlp
    
    @pytest.mark.asyncio
    async def test_initialization_no_spacy_model(self):
        """Test initialization when spaCy model is missing"""
        from content_processor import ContentProcessor
        
        with patch('content_processor.spacy.load') as mock_spacy:
            mock_spacy.side_effect = OSError("Model not found")
            
            processor = ContentProcessor()
            await processor.initialize()
            
            assert processor.is_ready
            assert processor.nlp is None
    
    @pytest.mark.asyncio
    async def test_convert_content_markdown_to_html(self):
        """Test content conversion from Markdown to HTML"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "# Test Header\n\nThis is **bold** text."
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["html"]
        )
        
        assert "html" in result
        assert "text/html" in result["html"]["mime_type"]
        assert "<h1>Test Header</h1>" in result["html"]["content"]
        assert "<strong>bold</strong>" in result["html"]["content"]
    
    @pytest.mark.asyncio
    async def test_convert_content_markdown_to_docx(self):
        """Test content conversion from Markdown to DOCX"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "# Test Document\n\nThis is test content."
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["docx"],
            document_title="Test Title",
            author_name="Test Author"
        )
        
        assert "docx" in result
        assert result["docx"]["mime_type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        assert result["docx"]["file_extension"] == ".docx"
        assert result["docx"]["encoding"] == "base64"
        assert "content" in result["docx"]
    
    @pytest.mark.asyncio
    async def test_convert_content_markdown_to_pdf(self):
        """Test content conversion from Markdown to PDF"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "# Test Document\n\nThis is test content for PDF."
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["pdf"],
            document_title="Test PDF",
            author_name="Test Author"
        )
        
        assert "pdf" in result
        assert result["pdf"]["mime_type"] == "application/pdf"
        assert result["pdf"]["file_extension"] == ".pdf"
        assert result["pdf"]["encoding"] == "base64"
        
        # Verify it's valid base64
        try:
            base64.b64decode(result["pdf"]["content"])
        except Exception:
            pytest.fail("Invalid base64 content")
    
    @pytest.mark.asyncio
    async def test_convert_content_markdown_to_epub(self):
        """Test content conversion from Markdown to EPUB"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = """
        # Chapter 1: Introduction
        
        This is the first chapter.
        
        # Chapter 2: Content
        
        This is the second chapter.
        """
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["epub"],
            document_title="Test Book",
            author_name="Test Author"
        )
        
        assert "epub" in result
        assert result["epub"]["mime_type"] == "application/epub+zip"
        assert result["epub"]["file_extension"] == ".epub"
        assert result["epub"]["encoding"] == "base64"
    
    @pytest.mark.asyncio
    async def test_convert_content_to_plain_text(self):
        """Test content conversion to plain text"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "# Test Header\n\nThis is **bold** and *italic* text."
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["plain_text"]
        )
        
        assert "plain_text" in result
        assert result["plain_text"]["mime_type"] == "text/plain"
        assert "Test Header" in result["plain_text"]["content"]
        assert "**" not in result["plain_text"]["content"]  # Bold formatting removed
        assert "*" not in result["plain_text"]["content"]   # Italic formatting removed
    
    @pytest.mark.asyncio
    async def test_convert_content_multiple_formats(self):
        """Test converting content to multiple formats"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "# Test\n\nMultiple format test."
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["html", "plain_text", "markdown"]
        )
        
        assert len(result) == 3
        assert "html" in result
        assert "plain_text" in result
        assert "markdown" in result
    
    @pytest.mark.asyncio
    async def test_convert_content_unsupported_format(self):
        """Test conversion with unsupported format"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "Test content"
        
        result = await processor.convert_content(
            content=content,
            source_format="markdown",
            target_formats=["unsupported_format"]
        )
        
        assert "unsupported_format" in result
        assert "error" in result["unsupported_format"]
    
    @pytest.mark.asyncio
    async def test_enhance_content_grammar(self):
        """Test grammar enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "This  is   poorly   formatted    text."
        
        result = await processor.enhance_content(
            content=content,
            enhancement_type="grammar"
        )
        
        assert result["enhancement_type"] == "grammar"
        assert "grammar_correction" in result["enhancements_applied"]
        assert "enhanced_content" in result
        # Should fix multiple spaces
        assert "   " not in result["enhanced_content"]
    
    @pytest.mark.asyncio
    async def test_enhance_content_style_formal(self):
        """Test style enhancement to formal"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "Don't worry, you can't go wrong with this."
        
        result = await processor.enhance_content(
            content=content,
            enhancement_type="style",
            style_guide="formal"
        )
        
        assert result["enhancement_type"] == "style"
        assert "style_improvement" in result["enhancements_applied"]
        enhanced = result["enhanced_content"]
        assert "do not" in enhanced
        assert "cannot" in enhanced
    
    @pytest.mark.asyncio
    async def test_enhance_content_style_casual(self):
        """Test style enhancement to casual"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "You do not need to worry. You cannot make mistakes."
        
        result = await processor.enhance_content(
            content=content,
            enhancement_type="style",
            style_guide="casual"
        )
        
        assert result["enhancement_type"] == "style"
        enhanced = result["enhanced_content"]
        assert "don't" in enhanced
        assert "can't" in enhanced
    
    @pytest.mark.asyncio
    async def test_enhance_content_structure(self):
        """Test structure enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "Introduction:\nThis is the intro.\n\nMain Content:\nThis is the main part."
        
        result = await processor.enhance_content(
            content=content,
            enhancement_type="structure"
        )
        
        assert result["enhancement_type"] == "structure"
        assert "structure_optimization" in result["enhancements_applied"]
        enhanced = result["enhanced_content"]
        assert "## Introduction" in enhanced
        assert "## Main Content" in enhanced
    
    @pytest.mark.asyncio
    async def test_enhance_content_readability(self):
        """Test readability enhancement"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = ("This is an extremely long sentence that goes on and on without proper breaks "
                  "and contains many complex ideas that should be split into shorter sentences "
                  "but unfortunately it just keeps going making it hard to read.")
        
        result = await processor.enhance_content(
            content=content,
            enhancement_type="readability"
        )
        
        assert result["enhancement_type"] == "readability"
        assert "readability_optimization" in result["enhancements_applied"]
        # Should break up long sentences
        enhanced = result["enhanced_content"]
        sentences = enhanced.split('. ')
        assert len(sentences) > 1
    
    @pytest.mark.asyncio
    async def test_enhance_content_invalid_type(self):
        """Test enhancement with invalid type"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        with pytest.raises(ValueError, match="Unknown enhancement type"):
            await processor.enhance_content(
                content="Test content",
                enhancement_type="invalid_type"
            )
    
    @pytest.mark.asyncio
    async def test_assess_quality_good_content(self):
        """Test quality assessment of good content"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = """
        # Introduction
        
        This is well-structured content with proper headings and paragraphs.
        Each sentence is clear and concise. The content flows logically.
        
        ## Main Points
        
        Here are the key points discussed in this section.
        Each point builds upon the previous one naturally.
        
        ## Conclusion
        
        This concludes our discussion with a clear summary.
        """
        
        result = await processor.assess_quality(content)
        
        assert "overall_quality" in result
        assert "readability_score" in result
        assert "grammar_score" in result
        assert "coherence_score" in result
        assert "originality_score" in result
        assert "recommendations" in result
        assert "text_statistics" in result
        
        # Should have decent quality scores
        assert result["overall_quality"] > 0.5
        assert isinstance(result["recommendations"], list)
    
    @pytest.mark.asyncio
    async def test_assess_quality_poor_content(self):
        """Test quality assessment of poor content"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        content = "bad content.no structure.very short."
        
        result = await processor.assess_quality(content)
        
        assert result["overall_quality"] < 0.7
        assert len(result["recommendations"]) > 0
        
        # Should have recommendations for improvement
        recommendations_text = " ".join(result["recommendations"])
        assert any(word in recommendations_text.lower() for word in 
                  ["improve", "review", "add", "increase", "consider"])
    
    @pytest.mark.asyncio
    async def test_analyze_text_stats(self):
        """Test text statistics analysis"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "This is a test. It has two sentences.\n\nAnd two paragraphs."
        
        stats = await processor._analyze_text_stats(content)
        
        assert stats["sentence_count"] == 3  # Including the third sentence
        assert stats["paragraph_count"] == 2
        assert stats["word_count"] == len(content.split())
        assert stats["character_count"] == len(content)
        assert "readability_score" in stats
    
    @pytest.mark.asyncio
    async def test_normalize_content_html(self):
        """Test HTML content normalization"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        html_content = "<h1>Title</h1><p>This is a paragraph.</p><br><strong>Bold text</strong>"
        
        normalized = await processor._normalize_content(html_content, "html")
        
        assert "#1 Title" in normalized
        assert "This is a paragraph." in normalized
        assert "<h1>" not in normalized
        assert "<p>" not in normalized
    
    @pytest.mark.asyncio
    async def test_normalize_content_plain_text(self):
        """Test plain text normalization"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "Line 1\n\n\n\nLine 2"
        
        normalized = await processor._normalize_content(content, "plain_text")
        
        assert "\n\n\n" not in normalized
        assert "Line 1" in normalized
        assert "Line 2" in normalized
    
    @pytest.mark.asyncio
    async def test_estimate_grammar_score(self):
        """Test grammar score estimation"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        # Good grammar
        good_text = "This is well formatted. Each sentence is proper."
        score = await processor._estimate_grammar_score(good_text)
        assert score >= 0.9
        
        # Poor grammar
        poor_text = "This is bad.No space after period .Space before period."
        score = await processor._estimate_grammar_score(poor_text)
        assert score < 0.9
    
    @pytest.mark.asyncio
    async def test_estimate_coherence_score(self):
        """Test coherence score estimation"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        # Good coherence with transitions
        good_text = "First point. However, there is another view. Therefore, we conclude."
        score = await processor._estimate_coherence_score(good_text)
        assert score > 0.5
        
        # Poor coherence
        poor_text = "Random sentence. Unrelated thought."
        score = await processor._estimate_coherence_score(poor_text)
        assert score <= 0.7
    
    @pytest.mark.asyncio
    async def test_estimate_originality_score(self):
        """Test originality score estimation"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        # Diverse content
        diverse_text = "This unique sentence contains various different words expressing multiple concepts."
        score = await processor._estimate_originality_score(diverse_text)
        assert score > 0.7
        
        # Repetitive content
        repetitive_text = "test test test test test test test test"
        score = await processor._estimate_originality_score(repetitive_text)
        assert score < 0.5
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test cleanup functionality"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        await processor.initialize()
        
        assert processor.is_ready
        
        await processor.cleanup()
        
        assert not processor.is_ready


class TestDocumentConversion:
    """Test specific document conversion methods"""
    
    @pytest.mark.asyncio
    async def test_convert_to_docx_with_headings(self):
        """Test DOCX conversion with headings"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "# Main Title\n## Subtitle\n### Sub-subtitle\nRegular paragraph."
        
        result = await processor._convert_to_docx(content, "Test Document", "Test Author")
        
        assert result["mime_type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        assert result["file_extension"] == ".docx"
        assert result["encoding"] == "base64"
        
        # Should be valid base64
        try:
            base64.b64decode(result["content"])
        except Exception:
            pytest.fail("Invalid base64 DOCX content")
    
    @pytest.mark.asyncio
    async def test_convert_to_pdf_complete(self):
        """Test complete PDF conversion"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "# Document Title\n\nThis is the content of the document.\n\n## Section 1\n\nMore content here."
        
        result = await processor._convert_to_pdf(content, "Test PDF", "Test Author")
        
        assert result["mime_type"] == "application/pdf"
        assert result["file_extension"] == ".pdf"
        assert result["encoding"] == "base64"
    
    @pytest.mark.asyncio
    async def test_convert_to_epub_with_chapters(self):
        """Test EPUB conversion with multiple chapters"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = """
        # Chapter 1: Beginning
        
        This is the first chapter content.
        
        # Chapter 2: Middle
        
        This is the second chapter content.
        
        # Chapter 3: End
        
        This is the final chapter content.
        """
        
        result = await processor._convert_to_epub(content, "Test Book", "Test Author")
        
        assert result["mime_type"] == "application/epub+zip"
        assert result["file_extension"] == ".epub"
        assert result["encoding"] == "base64"


class TestEnhancementMethods:
    """Test specific enhancement methods"""
    
    @pytest.mark.asyncio
    async def test_enhance_grammar_multiple_spaces(self):
        """Test grammar enhancement removes multiple spaces"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "This  has    multiple     spaces."
        enhanced = await processor._enhance_grammar(content)
        
        assert "  " not in enhanced
        assert "This has multiple spaces." == enhanced
    
    @pytest.mark.asyncio
    async def test_enhance_structure_headers(self):
        """Test structure enhancement adds headers"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        content = "Introduction:\nContent here.\n\nConclusion:\nFinal thoughts."
        enhanced = await processor._enhance_structure(content)
        
        assert "## Introduction" in enhanced
        assert "## Conclusion" in enhanced
    
    @pytest.mark.asyncio
    async def test_enhance_readability_long_sentences(self):
        """Test readability enhancement breaks long sentences"""
        from content_processor import ContentProcessor
        
        processor = ContentProcessor()
        
        # Create a very long sentence with 'and' conjunction
        long_sentence = " ".join(["word"] * 15) + " and " + " ".join(["word"] * 15) + "."
        
        enhanced = await processor._enhance_readability(long_sentence)
        
        # Should be split at 'and'
        assert enhanced.count('.') >= 2


if __name__ == "__main__":
    pytest.main([__file__])