"""
Tests for Manuscript Generator Component
"""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import os

# Mock dependencies
@pytest.fixture
def mock_openai_model():
    with patch('manuscript_generator.OpenAIModel') as mock:
        yield mock

@pytest.fixture
def mock_anthropic_model():
    with patch('manuscript_generator.AnthropicModel') as mock:
        yield mock

@pytest.fixture
def mock_typed_agent():
    with patch('manuscript_generator.TypedAgent') as mock:
        agent_instance = MagicMock()
        agent_instance.run = AsyncMock()
        mock.return_value = agent_instance
        yield mock, agent_instance

@pytest.fixture
def mock_event_client():
    mock = MagicMock()
    mock.publish_event = AsyncMock()
    return mock

@pytest.fixture
def mock_metrics():
    mock = MagicMock()
    mock.record_manuscript_generation = MagicMock()
    mock.record_manuscript_error = MagicMock()
    return mock


class TestManuscriptGenerator:
    """Test ManuscriptGenerator class"""
    
    @pytest.mark.asyncio
    async def test_initialization_with_openai(self, mock_openai_model, mock_typed_agent):
        """Test initialization with OpenAI model"""
        from manuscript_generator import ManuscriptGenerator
        
        # Mock environment variables
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key', 'PREFERRED_MODEL': 'openai'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            assert generator.is_ready
            assert generator.agent is not None
            mock_openai_model.assert_called_once_with("gpt-4")
    
    @pytest.mark.asyncio
    async def test_initialization_with_anthropic(self, mock_anthropic_model, mock_typed_agent):
        """Test initialization with Anthropic model"""
        from manuscript_generator import ManuscriptGenerator
        
        # Mock environment variables
        with patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key', 'PREFERRED_MODEL': 'anthropic'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            assert generator.is_ready
            assert generator.agent is not None
            mock_anthropic_model.assert_called_once_with("claude-3-sonnet-20240229")
    
    @pytest.mark.asyncio
    async def test_initialization_no_api_keys(self, mock_typed_agent):
        """Test initialization failure when no API keys available"""
        from manuscript_generator import ManuscriptGenerator
        
        # Mock environment with no API keys
        with patch.dict(os.environ, {}, clear=True):
            generator = ManuscriptGenerator()
            
            with pytest.raises(Exception, match="No AI model available"):
                await generator.initialize()
            
            assert not generator.is_ready
    
    @pytest.mark.asyncio
    async def test_initialization_fallback_to_openai(self, mock_openai_model, mock_typed_agent):
        """Test fallback to OpenAI when preferred model unavailable"""
        from manuscript_generator import ManuscriptGenerator
        
        # Mock environment with only OpenAI key but prefer Anthropic
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key', 'PREFERRED_MODEL': 'anthropic'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            assert generator.is_ready
            mock_openai_model.assert_called_once_with("gpt-4")
    
    @pytest.mark.asyncio
    async def test_generate_manuscript_success(self, mock_typed_agent):
        """Test successful manuscript generation"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        # Setup mocks
        mock_agent_class, mock_agent_instance = mock_typed_agent
        
        # Mock the result
        mock_result_data = ManuscriptGenerationResult(
            title="Test Manuscript",
            content="This is test content with multiple chapters.",
            word_count=2000,
            chapter_count=5,
            quality_score=0.85,
            chapters=[
                {"title": "Chapter 1", "content": "Chapter 1 content"},
                {"title": "Chapter 2", "content": "Chapter 2 content"}
            ]
        )
        
        mock_run_result = MagicMock()
        mock_run_result.data = mock_result_data
        mock_agent_instance.run.return_value = mock_run_result
        
        # Initialize generator
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            # Generate manuscript
            result = await generator.generate_manuscript(
                topic="Test Topic",
                style="professional",
                target_audience="general adults",
                target_length=8000,
                chapter_count=8
            )
            
            assert result.title == "Test Manuscript"
            assert result.word_count == 2000
            assert result.quality_score == 0.85
            assert len(result.chapters) == 2
            mock_agent_instance.run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_manuscript_not_initialized(self):
        """Test manuscript generation when not initialized"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        # Don't initialize
        
        with pytest.raises(Exception, match="Manuscript Generator not initialized"):
            await generator.generate_manuscript(
                topic="Test Topic",
                style="professional"
            )
    
    @pytest.mark.asyncio
    async def test_generate_manuscript_with_progress_callback(self, mock_typed_agent):
        """Test manuscript generation with progress callback"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        # Setup mocks
        mock_agent_class, mock_agent_instance = mock_typed_agent
        
        mock_result_data = ManuscriptGenerationResult(
            title="Test Manuscript",
            content="Test content",
            word_count=1000,
            chapter_count=3,
            quality_score=0.8,
            chapters=[]
        )
        
        mock_run_result = MagicMock()
        mock_run_result.data = mock_result_data
        mock_agent_instance.run.return_value = mock_run_result
        
        # Initialize generator
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            # Track progress calls
            progress_calls = []
            def progress_callback(progress, chapter):
                progress_calls.append((progress, chapter))
            
            # Generate manuscript with progress callback
            result = await generator.generate_manuscript(
                topic="Test Topic",
                progress_callback=progress_callback
            )
            
            assert len(progress_calls) > 0
            assert progress_calls[-1][0] == 100  # Final progress should be 100%
            assert result.word_count == 1000
    
    @pytest.mark.asyncio
    async def test_generate_manuscript_with_trend_data(self, mock_typed_agent):
        """Test manuscript generation with trend data"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        # Setup mocks
        mock_agent_class, mock_agent_instance = mock_typed_agent
        
        mock_result_data = ManuscriptGenerationResult(
            title="Trending Topic Book",
            content="Content based on trends",
            word_count=5000,
            chapter_count=10,
            quality_score=0.9,
            chapters=[]
        )
        
        mock_run_result = MagicMock()
        mock_run_result.data = mock_result_data
        mock_agent_instance.run.return_value = mock_run_result
        
        # Initialize generator
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            # Trend data
            trend_data = {
                "search_trends": ["AI", "machine learning"],
                "keywords": ["artificial intelligence", "automation"],
                "market_insights": "Growing interest in AI"
            }
            
            # Generate manuscript
            result = await generator.generate_manuscript(
                topic="AI and Machine Learning",
                trend_data=trend_data,
                target_length=10000
            )
            
            assert result.title == "Trending Topic Book"
            assert result.word_count == 5000
            
            # Verify the prompt included trend data
            call_args = mock_agent_instance.run.call_args[0][0]
            assert "MARKET TREND DATA" in call_args
            assert "artificial intelligence" in call_args
    
    @pytest.mark.asyncio
    async def test_generation_exception_handling(self, mock_typed_agent):
        """Test exception handling during generation"""
        from manuscript_generator import ManuscriptGenerator
        
        # Setup mocks
        mock_agent_class, mock_agent_instance = mock_typed_agent
        mock_agent_instance.run = AsyncMock(side_effect=Exception("AI model error"))
        
        # Initialize generator
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            generator = ManuscriptGenerator()
            await generator.initialize()
            
            # Attempt generation - should raise exception
            with pytest.raises(Exception, match="AI model error"):
                await generator.generate_manuscript(
                    topic="Test Topic"
                )
    
    def test_create_generation_prompt(self):
        """Test generation prompt creation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        prompt = generator._create_generation_prompt(
            topic="Test Topic",
            style="professional",
            target_audience="general adults",
            target_length=8000,
            chapter_count=8
        )
        
        assert "Test Topic" in prompt
        assert "professional" in prompt
        assert "general adults" in prompt
        assert "8000 words" in prompt
        assert "8" in prompt
    
    def test_create_generation_prompt_with_trend_data(self):
        """Test generation prompt creation with trend data"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        trend_data = {
            "search_trends": ["AI", "automation"],
            "keywords": ["machine learning", "neural networks"]
        }
        
        prompt = generator._create_generation_prompt(
            topic="AI Technology",
            trend_data=trend_data
        )
        
        assert "AI Technology" in prompt
        assert "MARKET TREND DATA" in prompt
        assert "machine learning" in prompt
        assert "neural networks" in prompt
    
    def test_calculate_quality_score(self):
        """Test quality score calculation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        # Test with good content
        good_content = """
        # Chapter 1: Introduction
        
        This is a well-structured chapter with multiple paragraphs.
        
        ## Section 1.1
        
        Here we discuss important concepts. The content flows well and maintains 
        readability throughout the chapter.
        
        ## Section 1.2
        
        Additional content that provides value to readers. Each section builds
        upon the previous one logically.
        """
        
        score = generator._calculate_quality_score(good_content, 1000, 1000)
        assert 0.6 <= score <= 1.0
        
        # Test with poor content
        poor_content = "Short content."
        
        score = generator._calculate_quality_score(poor_content, 10, 1000)
        assert score < 0.6
    
    def test_estimate_readability(self):
        """Test readability estimation"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        # Good readability - sentences around 15-20 words
        good_text = "This is a sentence with good readability. It has the right length. Each sentence flows well."
        score = generator._estimate_readability(good_text)
        assert score >= 0.8
        
        # Poor readability - very long sentences
        poor_text = "This is an extremely long sentence that goes on and on without proper breaks or punctuation making it very difficult to read and understand for most readers who expect shorter more digestible content."
        score = generator._estimate_readability(poor_text)
        assert score < 0.8
    
    def test_generate_title_from_topic(self):
        """Test title generation from topic"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        
        # Short topic
        title = generator._generate_title_from_topic("AI")
        assert title == "Ai"
        
        # Long topic
        title = generator._generate_title_from_topic("artificial intelligence and machine learning")
        assert "Complete Guide" in title
    
    @pytest.mark.asyncio
    async def test_cleanup(self, mock_event_client):
        """Test cleanup functionality"""
        from manuscript_generator import ManuscriptGenerator
        
        generator = ManuscriptGenerator()
        generator.event_client = mock_event_client
        generator.is_ready = True
        
        await generator.cleanup()
        
        assert not generator.is_ready
        mock_event_client.disconnect.assert_called_once()


class TestPostProcessing:
    """Test post-processing functionality"""
    
    @pytest.mark.asyncio
    async def test_post_process_result_with_missing_title(self):
        """Test post-processing when title is missing"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        generator = ManuscriptGenerator()
        
        # Result with missing title
        raw_result = ManuscriptGenerationResult(
            title="",
            content="Test content",
            word_count=0,
            chapter_count=0,
            quality_score=0,
            chapters=[]
        )
        
        processed = await generator._post_process_result(
            raw_result, "test topic", 1000, 0.7
        )
        
        assert processed.title != ""
        assert "test topic" in processed.title.lower()
    
    @pytest.mark.asyncio
    async def test_post_process_result_calculates_word_count(self):
        """Test post-processing calculates word count when missing"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        generator = ManuscriptGenerator()
        
        content = "This is test content with exactly ten words total here."
        
        raw_result = ManuscriptGenerationResult(
            title="Test Title",
            content=content,
            word_count=0,  # Missing word count
            chapter_count=0,
            quality_score=0,
            chapters=[]
        )
        
        processed = await generator._post_process_result(
            raw_result, "test topic", 1000, 0.7
        )
        
        assert processed.word_count == 10
    
    @pytest.mark.asyncio
    async def test_post_process_result_adds_metadata(self):
        """Test post-processing adds generation metadata"""
        from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
        
        generator = ManuscriptGenerator()
        
        raw_result = ManuscriptGenerationResult(
            title="Test Title",
            content="Test content",
            word_count=100,
            chapter_count=1,
            quality_score=0.8,
            chapters=[]
        )
        
        processed = await generator._post_process_result(
            raw_result, "test topic", 1000, 0.7
        )
        
        assert "generated_at" in processed.generation_metadata
        assert processed.generation_metadata["topic"] == "test topic"
        assert processed.generation_metadata["target_length"] == 1000
        assert processed.generation_metadata["actual_length"] == 100
        assert "quality_meets_threshold" in processed.generation_metadata


if __name__ == "__main__":
    pytest.main([__file__])