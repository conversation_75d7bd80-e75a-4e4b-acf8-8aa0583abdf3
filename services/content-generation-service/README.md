# Content Generation Service

A comprehensive microservice providing AI-powered manuscript generation, content creation, and document processing capabilities for the Publish AI platform.

## Features

### 🚀 Manuscript Generation
- **AI-Powered Content Creation**: Uses OpenAI GPT-4 and Anthropic Claude for high-quality manuscript generation
- **Market-Informed Writing**: Incorporates trend data and market insights into content
- **Customizable Styles**: Supports multiple writing styles (professional, casual, academic, creative)
- **Target Audience Optimization**: Tailors content for specific demographics
- **Quality Assurance**: Built-in quality scoring and threshold enforcement

### 📝 Content Enhancement
- **Grammar Optimization**: Automated grammar correction and improvement
- **Style Adaptation**: Formal/casual style transformation
- **Structure Enhancement**: Improved heading organization and flow
- **Readability Optimization**: Sentence length and complexity adjustment
- **Clarity Improvement**: Content simplification for target audiences

### 🔄 Document Conversion
- **Multi-Format Support**: Convert between Markdown, HTML, DOCX, PDF, EPUB, and plain text
- **Professional Formatting**: Maintains proper styling and structure
- **Metadata Handling**: Preserves author information and document titles
- **Base64 Encoding**: Secure binary file handling

### 📊 Quality Assessment
- **Comprehensive Scoring**: Readability, grammar, coherence, and originality metrics
- **Actionable Recommendations**: Specific improvement suggestions
- **Text Statistics**: Word count, sentence analysis, and structure metrics
- **Industry Standards**: Quality thresholds aligned with publishing requirements

### ⚡ Batch Processing
- **Bulk Generation**: Process multiple manuscripts simultaneously
- **Priority Queuing**: Support for different processing priorities
- **Progress Tracking**: Real-time status updates for long-running operations
- **Failure Handling**: Robust error recovery and partial completion support

## Architecture

### Core Components

#### ManuscriptGenerator
- **PydanticAI Agent**: AI-powered manuscript creation
- **Multi-Model Support**: OpenAI and Anthropic model integration
- **Progressive Generation**: Chapter-by-chapter content creation
- **Quality Validation**: Built-in content quality assessment

#### ContentProcessor
- **Document Conversion Engine**: Multi-format transformation
- **Enhancement Pipeline**: Content improvement workflows
- **Text Analysis**: Statistical and quality metrics
- **Format Optimization**: Target-specific formatting

### Technology Stack
- **Framework**: FastAPI with async/await support
- **AI Models**: OpenAI GPT-4, Anthropic Claude 3 Sonnet
- **Document Processing**: python-docx, reportlab, ebooklib
- **Text Analysis**: spaCy, textstat, NLTK
- **Microservices**: Event-driven architecture with Redis Streams

## API Endpoints

### Manuscript Generation
```
POST   /generate           # Async manuscript generation
POST   /generate/sync      # Synchronous generation
GET    /generate/{id}      # Check generation status
POST   /batch-generate     # Bulk manuscript generation
```

### Content Enhancement
```
POST   /enhance           # Content improvement
```

### Document Conversion
```
POST   /convert           # Format conversion
```

### Quality Assessment
```
POST   /assess-quality    # Content analysis
```

### Health & Monitoring
```
GET    /health           # Service health
GET    /ready            # Readiness check
GET    /metrics          # Prometheus metrics
```

## Request/Response Examples

### Manuscript Generation
```json
{
  "topic": "The Future of Artificial Intelligence",
  "trend_data": {
    "search_trends": ["AI automation", "machine learning"],
    "keywords": ["artificial intelligence", "neural networks"]
  },
  "style": "professional",
  "target_audience": "tech professionals",
  "target_length": 10000,
  "chapter_count": 8,
  "quality_threshold": 0.8,
  "output_formats": ["markdown", "docx", "pdf"]
}
```

### Content Enhancement
```json
{
  "content": "Original content text",
  "enhancement_type": "grammar",
  "target_audience": "general adults",
  "style_guide": "formal"
}
```

### Document Conversion
```json
{
  "content": "# Sample Document\n\nThis is **sample content**.",
  "source_format": "markdown",
  "target_formats": ["html", "docx", "pdf"],
  "document_title": "Sample Document",
  "author_name": "Author Name"
}
```

## Configuration

### Environment Variables
```bash
# Service Configuration
SERVICE_PORT=8086
SERVICE_HOST=0.0.0.0
EVENT_BUS_URL=http://event-bus:8080
SERVICE_DISCOVERY_URL=http://service-discovery:8070

# AI Model Configuration
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
PREFERRED_MODEL=openai  # or anthropic

# Dependencies
REDIS_URL=redis://redis:6379
LOG_LEVEL=info
```

### AI Model Selection
The service automatically selects the best available AI model:
1. Uses preferred model if available
2. Falls back to any available model
3. Supports both OpenAI and Anthropic APIs

## Development

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Install spaCy model (optional)
python -m spacy download en_core_web_sm

# Run service
python -m uvicorn src.main:app --reload --port 8086
```

### Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src tests/

# Run specific test categories
pytest -m unit        # Unit tests only
pytest -m integration # Integration tests only
```

### Docker Development
```bash
# Build image
docker build -t content-generation-service .

# Run container
docker run -p 8086:8086 \
  -e OPENAI_API_KEY=your_key \
  content-generation-service
```

## Deployment

### Kubernetes
```bash
# Deploy to cluster
kubectl apply -f k8s/deployment.yaml

# Check status
kubectl get pods -l app=content-generation-service

# View logs
kubectl logs -l app=content-generation-service --tail=100
```

### Service Discovery
The service automatically registers with the Service Discovery system:
- **Name**: content-generation-service
- **Port**: 8086
- **Capabilities**: manuscript_generation, content_enhancement, document_conversion

## Monitoring

### Health Checks
- **Liveness**: `/health` - Overall service health
- **Readiness**: `/ready` - Ready to accept requests

### Metrics
- **Prometheus**: `/metrics` endpoint
- **Custom Metrics**: Generation success rates, processing times, error rates
- **Resource Usage**: Memory, CPU, and request volume tracking

### Logging
- **Structured Logging**: JSON format with request tracing
- **Log Levels**: Configurable via LOG_LEVEL environment variable
- **Request Correlation**: Unique request IDs for tracking

## Performance

### Optimization Features
- **Async Processing**: Non-blocking I/O operations
- **Background Tasks**: Long-running operations in background
- **Connection Pooling**: Efficient resource utilization
- **Circuit Breakers**: AI API failure protection
- **Rate Limiting**: Request throttling for stability

### Scaling
- **Horizontal Scaling**: Multiple service instances
- **Auto-scaling**: CPU/Memory based scaling
- **Load Balancing**: Round-robin request distribution
- **Resource Limits**: Memory: 4Gi, CPU: 2000m

## Security

### Authentication
- **API Key Authentication**: Required for all endpoints
- **Service-to-Service**: mTLS communication
- **Network Policies**: Restricted pod communication

### Data Protection
- **Input Validation**: Pydantic schema validation
- **Content Sanitization**: HTML/script tag removal
- **Secrets Management**: Kubernetes secrets for API keys

## Quality Standards

### Content Quality
- **Readability**: Flesch Reading Ease scoring
- **Grammar**: Automated grammar validation
- **Coherence**: Logical flow assessment
- **Originality**: Content uniqueness scoring

### Service Quality
- **99.9% Uptime**: High availability target
- **< 30s Response**: Generation completion SLA
- **Error Rate < 1%**: Reliability target
- **Auto-recovery**: Self-healing capabilities

## Integration

### Event-Driven Architecture
- **Event Publishing**: Generation completion events
- **Event Consumption**: Market trend updates
- **Event Types**: manuscript_generated, content_enhanced, document_converted

### Service Dependencies
- **Event Bus**: Redis Streams messaging
- **Service Discovery**: Service registration
- **AI APIs**: OpenAI/Anthropic integration
- **Storage**: Temporary file handling

## Troubleshooting

### Common Issues
1. **AI API Failures**: Check API keys and rate limits
2. **Memory Issues**: Increase resource limits for large documents
3. **Timeout Errors**: Adjust timeouts for complex generations
4. **Format Errors**: Verify input format specifications

### Debug Commands
```bash
# Check service health
curl http://localhost:8086/health

# Test generation
curl -X POST http://localhost:8086/generate/sync \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-key" \
  -d '{"topic": "Test", "target_length": 1000}'

# View metrics
curl http://localhost:8086/metrics
```

## Contributing

1. Follow existing code patterns and structure
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure security best practices
5. Test with both AI model providers

## License

This service is part of the Publish AI platform and follows the main project licensing.