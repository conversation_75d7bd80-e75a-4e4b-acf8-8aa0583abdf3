"""
Content Generation Service - Content Processor

Document processing, format conversion, and content enhancement utilities.
"""

import asyncio
import base64
import io
import logging
import re
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import markdown
from docx import Document
from docx.shared import Inches
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from ebooklib import epub
import spacy
import textstat

logger = logging.getLogger(__name__)


# ============================================================================
# Enums and Constants
# ============================================================================

class DocumentFormat(str, Enum):
    MARKDOWN = "markdown"
    HTML = "html"
    DOCX = "docx"
    PDF = "pdf"
    EPUB = "epub"
    PLAIN_TEXT = "plain_text"


class EnhancementType(str, Enum):
    GRAMMAR = "grammar"
    STYLE = "style"
    STRUCTURE = "structure"
    CLARITY = "clarity"
    READABILITY = "readability"


# ============================================================================
# Content Processor
# ============================================================================

class ContentProcessor:
    """Content processing and format conversion engine"""
    
    def __init__(self):
        self.is_ready = False
        self.nlp = None
        
    async def initialize(self):
        """Initialize the content processor"""
        try:
            # Load spaCy model for text processing
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy English model not found. Some features will be limited.")
                self.nlp = None
            
            self.is_ready = True
            logger.info("Content Processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Content Processor: {e}")
            self.is_ready = False
            raise
    
    async def convert_content(
        self,
        content: str,
        source_format: str,
        target_formats: List[str],
        document_title: Optional[str] = None,
        author_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Convert content between different formats"""
        
        if not self.is_ready:
            raise Exception("Content Processor not initialized")
        
        results = {}
        
        try:
            # Normalize source content
            normalized_content = await self._normalize_content(content, source_format)
            
            for target_format in target_formats:
                try:
                    converted = await self._convert_to_format(
                        normalized_content,
                        target_format,
                        document_title,
                        author_name
                    )
                    results[target_format] = converted
                    
                except Exception as e:
                    logger.error(f"Failed to convert to {target_format}: {e}")
                    results[target_format] = {"error": str(e)}
            
            logger.info(f"Content conversion completed for {len(target_formats)} formats")
            return results
            
        except Exception as e:
            logger.error(f"Content conversion failed: {e}")
            raise
    
    async def enhance_content(
        self,
        content: str,
        enhancement_type: str,
        target_audience: Optional[str] = None,
        style_guide: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhance content based on specified enhancement type"""
        
        if not self.is_ready:
            raise Exception("Content Processor not initialized")
        
        try:
            enhanced_content = content
            enhancements_applied = []
            
            if enhancement_type == EnhancementType.GRAMMAR:
                enhanced_content = await self._enhance_grammar(enhanced_content)
                enhancements_applied.append("grammar_correction")
            
            elif enhancement_type == EnhancementType.STYLE:
                enhanced_content = await self._enhance_style(enhanced_content, style_guide)
                enhancements_applied.append("style_improvement")
            
            elif enhancement_type == EnhancementType.STRUCTURE:
                enhanced_content = await self._enhance_structure(enhanced_content)
                enhancements_applied.append("structure_optimization")
            
            elif enhancement_type == EnhancementType.CLARITY:
                enhanced_content = await self._enhance_clarity(enhanced_content, target_audience)
                enhancements_applied.append("clarity_improvement")
            
            elif enhancement_type == EnhancementType.READABILITY:
                enhanced_content = await self._enhance_readability(enhanced_content)
                enhancements_applied.append("readability_optimization")
            
            else:
                raise ValueError(f"Unknown enhancement type: {enhancement_type}")
            
            # Calculate improvement metrics
            original_stats = await self._analyze_text_stats(content)
            enhanced_stats = await self._analyze_text_stats(enhanced_content)
            
            result = {
                "original_content": content,
                "enhanced_content": enhanced_content,
                "enhancement_type": enhancement_type,
                "enhancements_applied": enhancements_applied,
                "improvement_metrics": {
                    "original_stats": original_stats,
                    "enhanced_stats": enhanced_stats,
                    "readability_improvement": enhanced_stats["readability_score"] - original_stats["readability_score"]
                },
                "word_count_change": enhanced_stats["word_count"] - original_stats["word_count"],
                "enhanced_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Content enhancement completed: {enhancement_type}")
            return result
            
        except Exception as e:
            logger.error(f"Content enhancement failed: {e}")
            raise
    
    async def assess_quality(self, content: str) -> Dict[str, Any]:
        """Assess content quality and provide recommendations"""
        
        if not self.is_ready:
            raise Exception("Content Processor not initialized")
        
        try:
            # Analyze text statistics
            stats = await self._analyze_text_stats(content)
            
            # Calculate quality scores
            readability_score = min(stats["readability_score"] / 100, 1.0)
            grammar_score = await self._estimate_grammar_score(content)
            coherence_score = await self._estimate_coherence_score(content)
            originality_score = await self._estimate_originality_score(content)
            
            # Calculate overall quality
            overall_quality = (
                readability_score * 0.25 +
                grammar_score * 0.25 +
                coherence_score * 0.25 +
                originality_score * 0.25
            )
            
            # Generate recommendations
            recommendations = await self._generate_quality_recommendations(
                content, readability_score, grammar_score, coherence_score, originality_score
            )
            
            quality_metrics = {
                "readability_score": readability_score,
                "grammar_score": grammar_score,
                "coherence_score": coherence_score,
                "originality_score": originality_score,
                "overall_quality": overall_quality,
                "recommendations": recommendations,
                "text_statistics": stats,
                "assessed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Content quality assessment completed. Overall score: {overall_quality:.2f}")
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Content quality assessment failed: {e}")
            raise
    
    # ========================================================================
    # Private Methods - Content Conversion
    # ========================================================================
    
    async def _normalize_content(self, content: str, source_format: str) -> str:
        """Normalize content from source format to a standard format"""
        
        if source_format == DocumentFormat.HTML:
            # Strip HTML tags and convert to markdown-like format
            content = re.sub(r'<h([1-6])>(.*?)</h[1-6]>', r'#\1 \2', content)
            content = re.sub(r'<p>(.*?)</p>', r'\1\n\n', content)
            content = re.sub(r'<br\s*/?>', '\n', content)
            content = re.sub(r'<[^>]+>', '', content)  # Remove remaining HTML tags
        
        elif source_format == DocumentFormat.MARKDOWN:
            # Content is already in a good format
            pass
        
        elif source_format == DocumentFormat.PLAIN_TEXT:
            # Basic formatting for plain text
            content = content.replace('\n\n\n', '\n\n')  # Normalize line breaks
        
        return content.strip()
    
    async def _convert_to_format(
        self,
        content: str,
        target_format: str,
        document_title: Optional[str] = None,
        author_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Convert content to specific format"""
        
        if target_format == DocumentFormat.HTML:
            html_content = markdown.markdown(content)
            return {
                "content": html_content,
                "mime_type": "text/html",
                "file_extension": ".html"
            }
        
        elif target_format == DocumentFormat.DOCX:
            return await self._convert_to_docx(content, document_title, author_name)
        
        elif target_format == DocumentFormat.PDF:
            return await self._convert_to_pdf(content, document_title, author_name)
        
        elif target_format == DocumentFormat.EPUB:
            return await self._convert_to_epub(content, document_title, author_name)
        
        elif target_format == DocumentFormat.MARKDOWN:
            return {
                "content": content,
                "mime_type": "text/markdown",
                "file_extension": ".md"
            }
        
        elif target_format == DocumentFormat.PLAIN_TEXT:
            # Strip markdown formatting
            plain_text = re.sub(r'#+\s*', '', content)  # Remove headers
            plain_text = re.sub(r'\*\*(.*?)\*\*', r'\1', plain_text)  # Remove bold
            plain_text = re.sub(r'\*(.*?)\*', r'\1', plain_text)  # Remove italic
            
            return {
                "content": plain_text,
                "mime_type": "text/plain",
                "file_extension": ".txt"
            }
        
        else:
            raise ValueError(f"Unsupported target format: {target_format}")
    
    async def _convert_to_docx(self, content: str, title: Optional[str], author: Optional[str]) -> Dict[str, Any]:
        """Convert content to DOCX format"""
        
        doc = Document()
        
        # Add title if provided
        if title:
            title_paragraph = doc.add_heading(title, level=1)
            title_paragraph.alignment = 1  # Center alignment
        
        # Add author if provided
        if author:
            author_paragraph = doc.add_paragraph(f"By {author}")
            author_paragraph.alignment = 1  # Center alignment
            doc.add_paragraph()  # Add space
        
        # Process content
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                doc.add_paragraph()  # Empty paragraph for spacing
            elif line.startswith('# '):
                doc.add_heading(line[2:], level=1)
            elif line.startswith('## '):
                doc.add_heading(line[3:], level=2)
            elif line.startswith('### '):
                doc.add_heading(line[4:], level=3)
            else:
                doc.add_paragraph(line)
        
        # Save to bytes
        doc_io = io.BytesIO()
        doc.save(doc_io)
        doc_bytes = doc_io.getvalue()
        
        return {
            "content": base64.b64encode(doc_bytes).decode('utf-8'),
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "file_extension": ".docx",
            "encoding": "base64"
        }
    
    async def _convert_to_pdf(self, content: str, title: Optional[str], author: Optional[str]) -> Dict[str, Any]:
        """Convert content to PDF format"""
        
        pdf_io = io.BytesIO()
        doc = SimpleDocTemplate(pdf_io, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Add title
        if title:
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center
            )
            story.append(Paragraph(title, title_style))
        
        # Add author
        if author:
            author_style = ParagraphStyle(
                'Author',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=30,
                alignment=1  # Center
            )
            story.append(Paragraph(f"By {author}", author_style))
        
        # Process content
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                story.append(Spacer(1, 12))
            elif line.startswith('# '):
                story.append(Paragraph(line[2:], styles['Heading1']))
            elif line.startswith('## '):
                story.append(Paragraph(line[3:], styles['Heading2']))
            elif line.startswith('### '):
                story.append(Paragraph(line[4:], styles['Heading3']))
            else:
                story.append(Paragraph(line, styles['Normal']))
        
        doc.build(story)
        pdf_bytes = pdf_io.getvalue()
        
        return {
            "content": base64.b64encode(pdf_bytes).decode('utf-8'),
            "mime_type": "application/pdf",
            "file_extension": ".pdf",
            "encoding": "base64"
        }
    
    async def _convert_to_epub(self, content: str, title: Optional[str], author: Optional[str]) -> Dict[str, Any]:
        """Convert content to EPUB format"""
        
        book = epub.EpubBook()
        
        # Set metadata
        book.set_identifier('generated-book-' + str(datetime.now().timestamp()))
        book.set_title(title or 'Generated Book')
        book.set_language('en')
        
        if author:
            book.add_author(author)
        
        # Create chapters from content
        chapters = []
        current_chapter = []
        chapter_count = 1
        
        lines = content.split('\n')
        for line in lines:
            if line.startswith('# ') and current_chapter:
                # Save previous chapter
                chapter_content = '\n'.join(current_chapter)
                chapter = epub.EpubHtml(
                    title=f'Chapter {chapter_count}',
                    file_name=f'chap_{chapter_count:02d}.xhtml',
                    lang='en'
                )
                chapter.content = f'<html><body>{markdown.markdown(chapter_content)}</body></html>'
                chapters.append(chapter)
                book.add_item(chapter)
                
                current_chapter = [line]
                chapter_count += 1
            else:
                current_chapter.append(line)
        
        # Add final chapter
        if current_chapter:
            chapter_content = '\n'.join(current_chapter)
            chapter = epub.EpubHtml(
                title=f'Chapter {chapter_count}',
                file_name=f'chap_{chapter_count:02d}.xhtml',
                lang='en'
            )
            chapter.content = f'<html><body>{markdown.markdown(chapter_content)}</body></html>'
            chapters.append(chapter)
            book.add_item(chapter)
        
        # Define Table of Contents
        book.toc = [(epub.Link(chapter.file_name, chapter.title, chapter.id), []) for chapter in chapters]
        
        # Add navigation files
        book.add_item(epub.EpubNcx())
        book.add_item(epub.EpubNav())
        
        # Define CSS style
        style = '''
        body { font-family: Times, serif; }
        h1 { color: #333; }
        p { text-align: justify; }
        '''
        nav_css = epub.EpubItem(
            uid="nav_css",
            file_name="style/nav.css",
            media_type="text/css",
            content=style
        )
        book.add_item(nav_css)
        
        # Create spine
        book.spine = ['nav'] + chapters
        
        # Generate EPUB
        epub_io = io.BytesIO()
        epub.write_epub(epub_io, book, {})
        epub_bytes = epub_io.getvalue()
        
        return {
            "content": base64.b64encode(epub_bytes).decode('utf-8'),
            "mime_type": "application/epub+zip",
            "file_extension": ".epub",
            "encoding": "base64"
        }
    
    # ========================================================================
    # Private Methods - Content Enhancement
    # ========================================================================
    
    async def _enhance_grammar(self, content: str) -> str:
        """Enhance content grammar (basic implementation)"""
        # Basic grammar corrections
        enhanced = content
        
        # Fix common issues
        enhanced = re.sub(r'\s+', ' ', enhanced)  # Multiple spaces
        enhanced = re.sub(r'([.!?])\s*([a-z])', r'\1 \2', enhanced)  # Space after punctuation
        enhanced = re.sub(r'([a-z])([.!?])', r'\1\2', enhanced)  # No space before punctuation
        
        return enhanced
    
    async def _enhance_style(self, content: str, style_guide: Optional[str]) -> str:
        """Enhance content style"""
        enhanced = content
        
        # Apply basic style improvements
        if style_guide == "formal":
            # Make more formal
            enhanced = enhanced.replace("don't", "do not")
            enhanced = enhanced.replace("can't", "cannot")
            enhanced = enhanced.replace("won't", "will not")
        elif style_guide == "casual":
            # Make more casual
            enhanced = enhanced.replace("do not", "don't")
            enhanced = enhanced.replace("cannot", "can't")
            enhanced = enhanced.replace("will not", "won't")
        
        return enhanced
    
    async def _enhance_structure(self, content: str) -> str:
        """Enhance content structure"""
        lines = content.split('\n')
        enhanced_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # Ensure proper heading format
                if line.endswith(':') and len(line.split()) <= 6:
                    if not line.startswith('#'):
                        line = f"## {line[:-1]}"
                enhanced_lines.append(line)
            else:
                enhanced_lines.append('')
        
        return '\n'.join(enhanced_lines)
    
    async def _enhance_clarity(self, content: str, target_audience: Optional[str]) -> str:
        """Enhance content clarity"""
        enhanced = content
        
        # Simplify based on target audience
        if target_audience and "beginner" in target_audience.lower():
            # Add explanations for complex terms
            # This is a simplified implementation
            pass
        
        return enhanced
    
    async def _enhance_readability(self, content: str) -> str:
        """Enhance content readability"""
        enhanced = content
        
        # Break up long sentences
        sentences = enhanced.split('. ')
        improved_sentences = []
        
        for sentence in sentences:
            words = sentence.split()
            if len(words) > 25:  # Long sentence
                # Simple approach: split at conjunctions
                for conjunction in [' and ', ' but ', ' or ', ' because ']:
                    if conjunction in sentence:
                        parts = sentence.split(conjunction, 1)
                        improved_sentences.append(parts[0] + '.')
                        improved_sentences.append(parts[1].strip().capitalize())
                        break
                else:
                    improved_sentences.append(sentence)
            else:
                improved_sentences.append(sentence)
        
        enhanced = '. '.join(improved_sentences)
        return enhanced
    
    # ========================================================================
    # Private Methods - Analysis
    # ========================================================================
    
    async def _analyze_text_stats(self, content: str) -> Dict[str, Any]:
        """Analyze text statistics"""
        
        words = content.split()
        sentences = content.split('.')
        paragraphs = content.split('\n\n')
        
        stats = {
            "word_count": len(words),
            "sentence_count": len([s for s in sentences if s.strip()]),
            "paragraph_count": len([p for p in paragraphs if p.strip()]),
            "character_count": len(content),
            "character_count_no_spaces": len(content.replace(' ', '')),
            "average_words_per_sentence": len(words) / max(len(sentences), 1),
            "average_sentences_per_paragraph": len(sentences) / max(len(paragraphs), 1),
            "readability_score": textstat.flesch_reading_ease(content) if content else 0
        }
        
        return stats
    
    async def _estimate_grammar_score(self, content: str) -> float:
        """Estimate grammar quality score"""
        # Basic grammar checks
        score = 1.0
        
        # Check for basic punctuation errors
        if re.search(r'[a-z][.!?][a-z]', content):  # Missing space after punctuation
            score -= 0.1
        
        if re.search(r'\s[.!?]', content):  # Space before punctuation
            score -= 0.1
        
        if re.search(r'\s{2,}', content):  # Multiple spaces
            score -= 0.05
        
        return max(score, 0.0)
    
    async def _estimate_coherence_score(self, content: str) -> float:
        """Estimate content coherence score"""
        # Simple coherence estimation
        sentences = [s.strip() for s in content.split('.') if s.strip()]
        
        if len(sentences) < 2:
            return 0.5
        
        # Check for transition words
        transition_words = ['however', 'therefore', 'furthermore', 'moreover', 'additionally', 'consequently']
        transition_count = sum(1 for sentence in sentences for word in transition_words if word in sentence.lower())
        
        transition_ratio = transition_count / len(sentences)
        coherence_score = min(0.5 + (transition_ratio * 2), 1.0)
        
        return coherence_score
    
    async def _estimate_originality_score(self, content: str) -> float:
        """Estimate content originality score"""
        # Simple originality check based on content diversity
        words = content.lower().split()
        unique_words = set(words)
        
        if len(words) == 0:
            return 0.0
        
        diversity_ratio = len(unique_words) / len(words)
        originality_score = min(diversity_ratio * 3, 1.0)  # Scale up diversity
        
        return originality_score
    
    async def _generate_quality_recommendations(
        self,
        content: str,
        readability_score: float,
        grammar_score: float,
        coherence_score: float,
        originality_score: float
    ) -> List[str]:
        """Generate quality improvement recommendations"""
        
        recommendations = []
        
        if readability_score < 0.6:
            recommendations.append("Improve readability by using shorter sentences and simpler vocabulary")
        
        if grammar_score < 0.8:
            recommendations.append("Review and correct grammar errors, particularly punctuation and spacing")
        
        if coherence_score < 0.6:
            recommendations.append("Add transition words and improve logical flow between sentences")
        
        if originality_score < 0.7:
            recommendations.append("Increase content diversity and avoid repetitive language")
        
        stats = await self._analyze_text_stats(content)
        
        if stats["average_words_per_sentence"] > 25:
            recommendations.append("Break up long sentences for better readability")
        
        if stats["word_count"] < 500:
            recommendations.append("Consider expanding content for more comprehensive coverage")
        
        if not recommendations:
            recommendations.append("Content quality is good! Consider minor style refinements")
        
        return recommendations
    
    async def cleanup(self):
        """Cleanup resources"""
        self.is_ready = False
        logger.info("Content Processor cleaned up successfully")