"""
Content Generation Service - Main Application

A comprehensive microservice providing AI-powered manuscript generation, content creation,
and document processing capabilities for the Publish AI platform.
"""

import asyncio
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import Fast<PERSON>I, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field

from manuscript_generator import ManuscriptGenerator, ManuscriptGenerationResult
from content_processor import ContentProcessor, DocumentFormat
from event_client import EventClient
from service_registry_client import ServiceRegistryClient
from security_manager import SecurityManager, verify_api_key
from monitoring import MetricsCollector, setup_logging


# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

# Global instances
manuscript_generator: Optional[ManuscriptGenerator] = None
content_processor: Optional[ContentProcessor] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None
metrics: Optional[MetricsCollector] = None

# ============================================================================
# Request/Response Models
# ============================================================================

class ManuscriptGenerationRequest(BaseModel):
    """Request model for manuscript generation"""
    topic: str = Field(..., description="Main topic for the manuscript")
    trend_data: Optional[Dict[str, Any]] = Field(default=None, description="Market trend data to inform content")
    style: str = Field(default="professional", description="Writing style: professional, casual, academic, creative")
    target_audience: str = Field(default="general adults", description="Target audience demographic")
    target_length: int = Field(default=8000, description="Target word count for the manuscript")
    chapter_count: int = Field(default=8, description="Number of chapters to generate")
    include_outline: bool = Field(default=True, description="Include detailed outline before generation")
    quality_threshold: float = Field(default=0.7, description="Minimum quality score (0-1)")
    output_formats: List[str] = Field(default=["markdown", "docx"], description="Desired output formats")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class ContentEnhancementRequest(BaseModel):
    """Request model for content enhancement"""
    content: str = Field(..., description="Content to enhance")
    enhancement_type: str = Field(..., description="Type of enhancement: grammar, style, structure, clarity")
    target_audience: Optional[str] = Field(default=None, description="Target audience for optimization")
    style_guide: Optional[str] = Field(default=None, description="Style guide to follow")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class DocumentConversionRequest(BaseModel):
    """Request model for document format conversion"""
    content: str = Field(..., description="Content to convert")
    source_format: str = Field(..., description="Source format: markdown, html, plain_text")
    target_formats: List[str] = Field(..., description="Target formats: docx, pdf, epub, html")
    document_title: Optional[str] = Field(default=None, description="Document title for metadata")
    author_name: Optional[str] = Field(default=None, description="Author name for metadata")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class BatchGenerationRequest(BaseModel):
    """Request model for batch manuscript generation"""
    topics: List[str] = Field(..., description="List of topics to generate manuscripts for")
    template_config: ManuscriptGenerationRequest = Field(..., description="Template configuration for all manuscripts")
    priority: str = Field(default="normal", description="Processing priority: low, normal, high")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class ServiceResponse(BaseModel):
    """Standard service response model"""
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Generation results")
    message: str = Field(default="", description="Response message")
    execution_time: Optional[float] = Field(default=None, description="Execution time in seconds")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())

class GenerationStatus(BaseModel):
    """Model for generation job status"""
    request_id: str
    status: str  # pending, generating, processing, completed, failed
    progress: Optional[int] = None  # 0-100
    current_chapter: Optional[int] = None
    total_chapters: Optional[int] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: str
    updated_at: str

class ContentQualityMetrics(BaseModel):
    """Model for content quality assessment"""
    readability_score: float = Field(..., description="Readability score (0-1)")
    grammar_score: float = Field(..., description="Grammar quality score (0-1)")
    coherence_score: float = Field(..., description="Content coherence score (0-1)")
    originality_score: float = Field(..., description="Content originality score (0-1)")
    overall_quality: float = Field(..., description="Overall quality score (0-1)")
    recommendations: List[str] = Field(..., description="Quality improvement recommendations")

# ============================================================================
# Startup/Shutdown
# ============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Content Generation Service")
    
    global manuscript_generator, content_processor, event_client
    global service_registry, security_manager, metrics
    
    try:
        # Initialize components
        metrics = MetricsCollector()
        security_manager = SecurityManager()
        
        # Initialize manuscript generator
        manuscript_generator = ManuscriptGenerator()
        await manuscript_generator.initialize()
        
        # Initialize content processor
        content_processor = ContentProcessor()
        await content_processor.initialize()
        
        # Initialize event client
        event_bus_url = os.getenv("EVENT_BUS_URL", "http://event-bus:8080")
        event_client = EventClient(event_bus_url)
        await event_client.connect()
        
        # Initialize service registry
        service_discovery_url = os.getenv("SERVICE_DISCOVERY_URL", "http://service-discovery:8070")
        service_registry = ServiceRegistryClient(service_discovery_url)
        
        # Register service
        service_info = {
            "name": "content-generation-service",
            "host": os.getenv("SERVICE_HOST", "0.0.0.0"),
            "port": int(os.getenv("SERVICE_PORT", "8086")),
            "health_endpoint": "/health",
            "capabilities": [
                "manuscript_generation",
                "content_enhancement", 
                "document_conversion",
                "batch_processing",
                "quality_assessment"
            ],
            "version": "1.0.0"
        }
        
        await service_registry.register_service(service_info)
        logger.info("Content Generation Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Content Generation Service: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down Content Generation Service")
    
    try:
        if manuscript_generator:
            await manuscript_generator.cleanup()
        if content_processor:
            await content_processor.cleanup()
        if event_client:
            await event_client.disconnect()
        if service_registry:
            await service_registry.unregister_service("content-generation-service")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# ============================================================================
# FastAPI Application
# ============================================================================

app = FastAPI(
    title="Content Generation Service",
    description="AI-powered content generation and manuscript creation microservice",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for async jobs (in production, use Redis or database)
generation_jobs: Dict[str, GenerationStatus] = {}

# ============================================================================
# Health & Monitoring Endpoints
# ============================================================================

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check component availability
        generator_status = "healthy" if manuscript_generator and manuscript_generator.is_ready else "unhealthy"
        processor_status = "healthy" if content_processor and content_processor.is_ready else "unhealthy"
        
        # Check external dependencies
        event_status = "healthy" if event_client and event_client.is_connected else "unhealthy"
        
        overall_status = "healthy" if all([
            generator_status == "healthy",
            processor_status == "healthy",
            event_status == "healthy"
        ]) else "unhealthy"
        
        return {
            "status": overall_status,
            "service": "content-generation-service",
            "version": "1.0.0",
            "components": {
                "manuscript_generator": generator_status,
                "content_processor": processor_status,
                "event_client": event_status,
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    try:
        if not manuscript_generator or not manuscript_generator.is_ready:
            raise HTTPException(status_code=503, detail="Manuscript Generator not ready")
        
        if not content_processor or not content_processor.is_ready:
            raise HTTPException(status_code=503, detail="Content Processor not ready")
        
        return {"status": "ready", "service": "content-generation-service"}
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    try:
        if metrics:
            return metrics.get_metrics()
        return {"message": "Metrics not available"}
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        return {"error": str(e)}

# ============================================================================
# Manuscript Generation Endpoints
# ============================================================================

@app.post("/generate", response_model=ServiceResponse)
async def generate_manuscript(
    request: ManuscriptGenerationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate manuscript asynchronously"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting manuscript generation for topic: {request.topic}")
        
        # Create generation job
        job = GenerationStatus(
            request_id=request_id,
            status="pending",
            progress=0,
            total_chapters=request.chapter_count,
            created_at=start_time.isoformat(),
            updated_at=start_time.isoformat()
        )
        generation_jobs[request_id] = job
        
        # Start background task
        background_tasks.add_task(_execute_manuscript_generation, request_id, request)
        
        # Record metrics
        if metrics:
            metrics.record_generation_request(request.topic, request.style)
        
        return ServiceResponse(
            request_id=request_id,
            status="accepted",
            message=f"Manuscript generation started for topic: {request.topic}",
            timestamp=start_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Manuscript generation request failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_generation_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Manuscript generation failed: {str(e)}"
        )

@app.get("/generate/{request_id}", response_model=ServiceResponse)
async def get_generation_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Get manuscript generation status and results"""
    try:
        if request_id not in generation_jobs:
            raise HTTPException(status_code=404, detail="Generation job not found")
        
        job = generation_jobs[request_id]
        
        return ServiceResponse(
            request_id=request_id,
            status=job.status,
            result=job.result,
            message=job.error_message if job.status == "failed" else "Generation status retrieved",
            timestamp=job.updated_at
        )
        
    except Exception as e:
        logger.error(f"Failed to get generation status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate/sync", response_model=ServiceResponse)
async def generate_manuscript_sync(
    request: ManuscriptGenerationRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate manuscript synchronously"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting synchronous manuscript generation for topic: {request.topic}")
        
        if not manuscript_generator:
            raise HTTPException(status_code=503, detail="Manuscript Generator not available")
        
        # Execute generation
        result = await manuscript_generator.generate_manuscript(
            topic=request.topic,
            trend_data=request.trend_data,
            style=request.style,
            target_audience=request.target_audience,
            target_length=request.target_length,
            chapter_count=request.chapter_count,
            include_outline=request.include_outline,
            quality_threshold=request.quality_threshold
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Process output formats if requested
        if request.output_formats and content_processor:
            processed_content = await content_processor.convert_content(
                content=result.content,
                source_format="markdown",
                target_formats=request.output_formats,
                document_title=result.title,
                author_name="AI Assistant"
            )
            result.formatted_content = processed_content
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "manuscript_generated",
                {
                    "request_id": request_id,
                    "topic": request.topic,
                    "word_count": result.word_count,
                    "quality_score": result.quality_score,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_generation_completed(request.topic, execution_time, result.word_count)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result.model_dump(),
            message="Manuscript generated successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Synchronous manuscript generation failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_generation_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Manuscript generation failed: {str(e)}"
        )

# ============================================================================
# Content Enhancement Endpoints
# ============================================================================

@app.post("/enhance", response_model=ServiceResponse)
async def enhance_content(
    request: ContentEnhancementRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Enhance existing content"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting content enhancement: {request.enhancement_type}")
        
        if not content_processor:
            raise HTTPException(status_code=503, detail="Content Processor not available")
        
        # Execute enhancement
        enhanced_content = await content_processor.enhance_content(
            content=request.content,
            enhancement_type=request.enhancement_type,
            target_audience=request.target_audience,
            style_guide=request.style_guide
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "content_enhanced",
                {
                    "request_id": request_id,
                    "enhancement_type": request.enhancement_type,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_enhancement_completed(request.enhancement_type, execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=enhanced_content,
            message="Content enhanced successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Content enhancement failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_enhancement_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Content enhancement failed: {str(e)}"
        )

# ============================================================================
# Document Conversion Endpoints
# ============================================================================

@app.post("/convert", response_model=ServiceResponse)
async def convert_document(
    request: DocumentConversionRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Convert document between formats"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting document conversion: {request.source_format} -> {request.target_formats}")
        
        if not content_processor:
            raise HTTPException(status_code=503, detail="Content Processor not available")
        
        # Execute conversion
        converted_content = await content_processor.convert_content(
            content=request.content,
            source_format=request.source_format,
            target_formats=request.target_formats,
            document_title=request.document_title,
            author_name=request.author_name
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "document_converted",
                {
                    "request_id": request_id,
                    "source_format": request.source_format,
                    "target_formats": request.target_formats,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_conversion_completed(request.source_format, request.target_formats, execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=converted_content,
            message="Document converted successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Document conversion failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_conversion_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document conversion failed: {str(e)}"
        )

# ============================================================================
# Quality Assessment Endpoints
# ============================================================================

@app.post("/assess-quality", response_model=ServiceResponse)
async def assess_content_quality(
    content: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Assess content quality and provide recommendations"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting content quality assessment")
        
        if not content_processor:
            raise HTTPException(status_code=503, detail="Content Processor not available")
        
        # Execute quality assessment
        quality_metrics = await content_processor.assess_quality(content)
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Record metrics
        if metrics:
            metrics.record_quality_assessment_completed(execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=quality_metrics,
            message="Content quality assessed successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Content quality assessment failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_quality_assessment_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Content quality assessment failed: {str(e)}"
        )

# ============================================================================
# Batch Processing Endpoints
# ============================================================================

@app.post("/batch-generate", response_model=ServiceResponse)
async def batch_generate_manuscripts(
    request: BatchGenerationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Generate multiple manuscripts in batch"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting batch manuscript generation for {len(request.topics)} topics")
        
        # Create batch job
        job = GenerationStatus(
            request_id=request_id,
            status="pending",
            progress=0,
            total_chapters=len(request.topics),
            created_at=start_time.isoformat(),
            updated_at=start_time.isoformat()
        )
        generation_jobs[request_id] = job
        
        # Start background task
        background_tasks.add_task(_execute_batch_generation, request_id, request)
        
        # Record metrics
        if metrics:
            metrics.record_batch_generation_request(len(request.topics))
        
        return ServiceResponse(
            request_id=request_id,
            status="accepted",
            message=f"Batch generation started for {len(request.topics)} topics",
            timestamp=start_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Batch generation request failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_batch_generation_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch generation failed: {str(e)}"
        )

# ============================================================================
# Background Task Functions
# ============================================================================

async def _execute_manuscript_generation(request_id: str, request: ManuscriptGenerationRequest):
    """Execute manuscript generation in background"""
    try:
        # Update job status
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "generating"
            generation_jobs[request_id].progress = 10
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        if not manuscript_generator:
            raise Exception("Manuscript Generator not available")
        
        # Execute generation with progress updates
        result = await manuscript_generator.generate_manuscript(
            topic=request.topic,
            trend_data=request.trend_data,
            style=request.style,
            target_audience=request.target_audience,
            target_length=request.target_length,
            chapter_count=request.chapter_count,
            include_outline=request.include_outline,
            quality_threshold=request.quality_threshold,
            progress_callback=lambda progress, chapter: _update_generation_progress(
                request_id, progress, chapter
            )
        )
        
        # Process output formats if requested
        if request.output_formats and content_processor:
            if request_id in generation_jobs:
                generation_jobs[request_id].status = "processing"
                generation_jobs[request_id].progress = 90
                generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
            
            processed_content = await content_processor.convert_content(
                content=result.content,
                source_format="markdown",
                target_formats=request.output_formats,
                document_title=result.title,
                author_name="AI Assistant"
            )
            result.formatted_content = processed_content
        
        # Update job status - completed
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "completed"
            generation_jobs[request_id].progress = 100
            generation_jobs[request_id].result = result.model_dump()
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "manuscript_generated",
                {
                    "request_id": request_id,
                    "topic": request.topic,
                    "word_count": result.word_count,
                    "quality_score": result.quality_score,
                    "user_id": request.user_id,
                    "status": "completed"
                }
            )
        
    except Exception as e:
        logger.error(f"Background manuscript generation failed: {e}")
        
        # Update job status - failed
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "failed"
            generation_jobs[request_id].error_message = str(e)
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish failure event
        if event_client:
            await event_client.publish_event(
                "manuscript_generation_failed",
                {
                    "request_id": request_id,
                    "topic": request.topic,
                    "user_id": request.user_id,
                    "error": str(e)
                }
            )

async def _execute_batch_generation(request_id: str, request: BatchGenerationRequest):
    """Execute batch manuscript generation in background"""
    try:
        if not manuscript_generator:
            raise Exception("Manuscript Generator not available")
        
        # Update job status
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "generating"
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        results = []
        total_topics = len(request.topics)
        
        for i, topic in enumerate(request.topics):
            try:
                # Create individual request
                individual_request = ManuscriptGenerationRequest(
                    topic=topic,
                    trend_data=request.template_config.trend_data,
                    style=request.template_config.style,
                    target_audience=request.template_config.target_audience,
                    target_length=request.template_config.target_length,
                    chapter_count=request.template_config.chapter_count,
                    include_outline=request.template_config.include_outline,
                    quality_threshold=request.template_config.quality_threshold,
                    output_formats=request.template_config.output_formats,
                    user_id=request.user_id
                )
                
                # Generate manuscript
                result = await manuscript_generator.generate_manuscript(
                    topic=individual_request.topic,
                    trend_data=individual_request.trend_data,
                    style=individual_request.style,
                    target_audience=individual_request.target_audience,
                    target_length=individual_request.target_length,
                    chapter_count=individual_request.chapter_count,
                    include_outline=individual_request.include_outline,
                    quality_threshold=individual_request.quality_threshold
                )
                
                results.append({
                    "topic": topic,
                    "success": True,
                    "result": result.model_dump()
                })
                
                # Update progress
                progress = int(((i + 1) / total_topics) * 100)
                if request_id in generation_jobs:
                    generation_jobs[request_id].progress = progress
                    generation_jobs[request_id].current_chapter = i + 1
                    generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
                
            except Exception as e:
                logger.error(f"Failed to generate manuscript for topic {topic}: {e}")
                results.append({
                    "topic": topic,
                    "success": False,
                    "error": str(e)
                })
        
        # Update job status - completed
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "completed"
            generation_jobs[request_id].progress = 100
            generation_jobs[request_id].result = {
                "batch_results": results,
                "total_topics": total_topics,
                "successful": len([r for r in results if r["success"]]),
                "failed": len([r for r in results if not r["success"]])
            }
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "batch_generation_completed",
                {
                    "request_id": request_id,
                    "total_topics": total_topics,
                    "successful": len([r for r in results if r["success"]]),
                    "failed": len([r for r in results if not r["success"]]),
                    "user_id": request.user_id
                }
            )
        
    except Exception as e:
        logger.error(f"Batch generation failed: {e}")
        
        # Update job status - failed
        if request_id in generation_jobs:
            generation_jobs[request_id].status = "failed"
            generation_jobs[request_id].error_message = str(e)
            generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()

def _update_generation_progress(request_id: str, progress: int, current_chapter: int):
    """Update generation progress"""
    if request_id in generation_jobs:
        generation_jobs[request_id].progress = progress
        generation_jobs[request_id].current_chapter = current_chapter
        generation_jobs[request_id].updated_at = datetime.utcnow().isoformat()

# ============================================================================
# Application Entry Point
# ============================================================================

if __name__ == "__main__":
    port = int(os.getenv("SERVICE_PORT", "8086"))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )