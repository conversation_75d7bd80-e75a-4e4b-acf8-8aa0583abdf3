"""
Content Generation Service - Manuscript Generator

PydanticAI agent for generating comprehensive manuscripts with AI-powered content creation.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Callable

from pydantic import BaseModel, Field
from pydantic_ai import Agent as TypedAgent, RunContext

from event_client import EventClient
from monitoring import MetricsManager

logger = logging.getLogger(__name__)


# ============================================================================
# Result Models
# ============================================================================

class ManuscriptGenerationResult(BaseModel):
    """Result model for manuscript generation"""
    title: str = Field(description="Generated manuscript title")
    content: str = Field(description="Generated manuscript content")
    outline: Optional[str] = Field(default=None, description="Book outline")
    chapters: List[Dict[str, Any]] = Field(default=[], description="Individual chapters")
    word_count: int = Field(description="Total word count")
    chapter_count: int = Field(description="Number of chapters")
    quality_score: float = Field(description="Content quality score (0-1)")
    generation_metadata: Dict[str, Any] = Field(default={}, description="Generation metadata")
    formatted_content: Optional[Dict[str, Any]] = Field(default=None, description="Formatted content in multiple formats")


# ============================================================================
# Manuscript Generator Agent
# ============================================================================

class ManuscriptGenerator:
    """AI-powered manuscript generation agent"""
    
    def __init__(self):
        self.agent: Optional[TypedAgent] = None
        self.is_ready = False
        self.event_client: Optional[EventClient] = None
        self.metrics = MetricsManager()
        
    async def initialize(self):
        """Initialize the manuscript generator agent"""
        try:
            # Import AI models here to avoid circular imports
            from pydantic_ai.models.openai import OpenAIModel
            from pydantic_ai.models.anthropic import AnthropicModel
            
            # Select model based on environment
            model = None
            preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
            
            if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
                model = OpenAIModel("gpt-4")
                logger.info("Initialized with OpenAI GPT-4 model")
            elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
                model = AnthropicModel("claude-3-sonnet-20240229")
                logger.info("Initialized with Anthropic Claude 3 Sonnet model")
            else:
                # Fallback to any available model
                if os.getenv("OPENAI_API_KEY"):
                    model = OpenAIModel("gpt-4")
                    logger.info("Fallback: Initialized with OpenAI GPT-4 model")
                elif os.getenv("ANTHROPIC_API_KEY"):
                    model = AnthropicModel("claude-3-sonnet-20240229")
                    logger.info("Fallback: Initialized with Anthropic Claude 3 Sonnet model")
            
            if not model:
                raise Exception("No AI model available. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY")
            
            # Create agent with comprehensive system prompt
            system_prompt = self._get_system_prompt()
            
            self.agent = TypedAgent(
                model=model,
                result_type=ManuscriptGenerationResult,
                system_prompt=system_prompt
            )
            
            self.is_ready = True
            logger.info("Manuscript Generator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Manuscript Generator: {e}")
            self.is_ready = False
            raise
    
    def _get_system_prompt(self) -> str:
        """Get comprehensive system prompt for manuscript generation"""
        return """
        You are an expert AI manuscript generator specializing in creating comprehensive, 
        high-quality books across multiple genres and topics. Your expertise includes:

        CORE CAPABILITIES:
        - Creating detailed outlines with logical chapter flow
        - Writing engaging, well-structured content
        - Adapting writing style to target audience
        - Incorporating market trends and data
        - Ensuring content originality and quality
        - Maintaining consistency across chapters

        WRITING PRINCIPLES:
        - Start with compelling hooks in each chapter
        - Use clear, accessible language appropriate to audience
        - Include practical examples and actionable insights
        - Structure content with proper headings and flow
        - End chapters with engaging transitions
        - Maintain consistent tone and voice throughout

        QUALITY STANDARDS:
        - Minimum word count requirements per chapter
        - Proper grammar and readability
        - Logical progression of ideas
        - Balanced depth vs. accessibility
        - Engaging and valuable content

        OUTPUT REQUIREMENTS:
        - Generate complete manuscript with all chapters
        - Include detailed outline
        - Provide accurate word counts
        - Calculate quality score based on readability, coherence, and value
        - Include metadata about generation process

        Always prioritize creating valuable, original content that provides genuine insight 
        and utility to readers while meeting commercial publishing standards.
        """
    
    async def generate_manuscript(
        self,
        topic: str,
        trend_data: Optional[Dict[str, Any]] = None,
        style: str = "professional",
        target_audience: str = "general adults",
        target_length: int = 8000,
        chapter_count: int = 8,
        include_outline: bool = True,
        quality_threshold: float = 0.7,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> ManuscriptGenerationResult:
        """Generate a complete manuscript"""
        
        if not self.is_ready or not self.agent:
            raise Exception("Manuscript Generator not initialized")
        
        start_time = datetime.utcnow()
        
        try:
            # Update progress
            if progress_callback:
                progress_callback(5, 0)
            
            # Prepare generation prompt
            prompt = self._create_generation_prompt(
                topic=topic,
                trend_data=trend_data,
                style=style,
                target_audience=target_audience,
                target_length=target_length,
                chapter_count=chapter_count,
                include_outline=include_outline
            )
            
            logger.info(f"Starting manuscript generation for topic: {topic}")
            
            # Update progress
            if progress_callback:
                progress_callback(15, 0)
            
            # Generate manuscript using AI agent
            result = await self.agent.run(prompt)
            
            # Update progress
            if progress_callback:
                progress_callback(80, chapter_count)
            
            # Post-process the result
            processed_result = await self._post_process_result(
                result.data,
                topic,
                target_length,
                quality_threshold,
                progress_callback
            )
            
            # Update progress
            if progress_callback:
                progress_callback(95, chapter_count)
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Record metrics
            self.metrics.record_manuscript_generation(
                topic=topic,
                word_count=processed_result.word_count,
                quality_score=processed_result.quality_score,
                execution_time=execution_time
            )
            
            # Publish event
            if self.event_client:
                await self.event_client.publish_event(
                    "manuscript_generated",
                    {
                        "topic": topic,
                        "word_count": processed_result.word_count,
                        "quality_score": processed_result.quality_score,
                        "chapter_count": processed_result.chapter_count,
                        "execution_time": execution_time
                    }
                )
            
            # Final progress update
            if progress_callback:
                progress_callback(100, chapter_count)
            
            logger.info(f"Manuscript generation completed successfully for topic: {topic}")
            return processed_result
            
        except Exception as e:
            logger.error(f"Manuscript generation failed for topic {topic}: {e}")
            
            # Record error metrics
            self.metrics.record_manuscript_error(str(e))
            
            # Publish error event
            if self.event_client:
                await self.event_client.publish_event(
                    "manuscript_generation_failed",
                    {
                        "topic": topic,
                        "error": str(e),
                        "execution_time": (datetime.utcnow() - start_time).total_seconds()
                    }
                )
            
            raise
    
    def _create_generation_prompt(
        self,
        topic: str,
        trend_data: Optional[Dict[str, Any]] = None,
        style: str = "professional",
        target_audience: str = "general adults",
        target_length: int = 8000,
        chapter_count: int = 8,
        include_outline: bool = True
    ) -> str:
        """Create a detailed generation prompt"""
        
        words_per_chapter = target_length // chapter_count
        
        prompt_parts = [
            f"Generate a comprehensive manuscript on the topic: '{topic}'",
            f"",
            f"REQUIREMENTS:",
            f"- Target audience: {target_audience}",
            f"- Writing style: {style}",
            f"- Total target length: {target_length} words",
            f"- Number of chapters: {chapter_count}",
            f"- Approximate words per chapter: {words_per_chapter}",
            f"- Include detailed outline: {'Yes' if include_outline else 'No'}",
        ]
        
        if trend_data:
            prompt_parts.extend([
                f"",
                f"MARKET TREND DATA TO INCORPORATE:",
                f"{self._format_trend_data(trend_data)}"
            ])
        
        prompt_parts.extend([
            f"",
            f"STRUCTURE REQUIREMENTS:",
            f"1. Create an engaging title that captures the topic essence",
            f"2. Generate a detailed outline with chapter titles and descriptions",
            f"3. Write complete chapters with proper structure:",
            f"   - Compelling chapter openings",
            f"   - Clear section headings",
            f"   - Practical examples and insights",
            f"   - Smooth transitions between ideas",
            f"   - Engaging chapter conclusions",
            f"4. Ensure logical flow between chapters",
            f"5. Maintain consistent voice and style throughout",
            f"",
            f"QUALITY STANDARDS:",
            f"- Each chapter must meet minimum word count",
            f"- Content must be original and valuable",
            f"- Include actionable insights and practical advice",
            f"- Maintain readability appropriate for target audience",
            f"- Ensure proper grammar and structure",
            f"",
            f"Please generate the complete manuscript with all required components."
        ])
        
        return "\n".join(prompt_parts)
    
    def _format_trend_data(self, trend_data: Dict[str, Any]) -> str:
        """Format trend data for inclusion in prompt"""
        formatted_parts = []
        
        if "search_trends" in trend_data:
            formatted_parts.append(f"Search Trends: {trend_data['search_trends']}")
        
        if "keywords" in trend_data:
            formatted_parts.append(f"Popular Keywords: {', '.join(trend_data['keywords'])}")
        
        if "market_insights" in trend_data:
            formatted_parts.append(f"Market Insights: {trend_data['market_insights']}")
        
        if "competitor_analysis" in trend_data:
            formatted_parts.append(f"Competitive Landscape: {trend_data['competitor_analysis']}")
        
        return "\n".join(formatted_parts) if formatted_parts else "No specific trend data provided"
    
    async def _post_process_result(
        self,
        raw_result: ManuscriptGenerationResult,
        topic: str,
        target_length: int,
        quality_threshold: float,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> ManuscriptGenerationResult:
        """Post-process the generated manuscript"""
        
        # Validate and enhance the result
        processed_result = raw_result.model_copy()
        
        # Ensure title exists
        if not processed_result.title or processed_result.title == "":
            processed_result.title = self._generate_title_from_topic(topic)
        
        # Calculate actual word count if not provided
        if processed_result.word_count == 0:
            processed_result.word_count = len(processed_result.content.split())
        
        # Validate chapter count
        if processed_result.chapter_count == 0:
            processed_result.chapter_count = len(processed_result.chapters)
        
        # Calculate quality score if not provided
        if processed_result.quality_score == 0:
            processed_result.quality_score = self._calculate_quality_score(
                processed_result.content,
                processed_result.word_count,
                target_length
            )
        
        # Add generation metadata
        processed_result.generation_metadata = {
            "generated_at": datetime.utcnow().isoformat(),
            "topic": topic,
            "target_length": target_length,
            "actual_length": processed_result.word_count,
            "length_variance": abs(processed_result.word_count - target_length) / target_length,
            "quality_meets_threshold": processed_result.quality_score >= quality_threshold,
            "generator_version": "1.0.0"
        }
        
        # Update progress if callback provided
        if progress_callback:
            progress_callback(90, processed_result.chapter_count)
        
        return processed_result
    
    def _generate_title_from_topic(self, topic: str) -> str:
        """Generate a title from the topic if none provided"""
        # Simple title generation logic
        words = topic.split()
        if len(words) <= 3:
            return topic.title()
        else:
            return f"The Complete Guide to {topic.title()}"
    
    def _calculate_quality_score(self, content: str, word_count: int, target_length: int) -> float:
        """Calculate a quality score for the content"""
        score = 0.0
        
        # Length factor (0.3 weight)
        length_ratio = min(word_count / target_length, 1.0)
        length_score = 0.7 + (0.3 * length_ratio)  # Minimum 0.7, max 1.0
        score += length_score * 0.3
        
        # Content structure factor (0.4 weight)
        structure_score = 0.5  # Base score
        
        # Check for headings
        if "##" in content or "#" in content:
            structure_score += 0.2
        
        # Check for paragraphs
        paragraphs = content.split("\n\n")
        if len(paragraphs) >= 5:
            structure_score += 0.2
        
        # Check for sufficient content diversity
        unique_words = len(set(content.lower().split()))
        word_diversity = min(unique_words / (word_count * 0.3), 1.0)  # Target 30% unique words
        structure_score += word_diversity * 0.1
        
        score += min(structure_score, 1.0) * 0.4
        
        # Readability factor (0.3 weight)
        readability_score = self._estimate_readability(content)
        score += readability_score * 0.3
        
        return min(score, 1.0)
    
    def _estimate_readability(self, content: str) -> float:
        """Estimate readability score"""
        words = content.split()
        sentences = content.split('.')
        
        if len(sentences) == 0 or len(words) == 0:
            return 0.5
        
        avg_words_per_sentence = len(words) / len(sentences)
        
        # Optimal range is 15-20 words per sentence
        if 15 <= avg_words_per_sentence <= 20:
            return 1.0
        elif 10 <= avg_words_per_sentence <= 25:
            return 0.8
        elif 8 <= avg_words_per_sentence <= 30:
            return 0.6
        else:
            return 0.4
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.event_client:
                await self.event_client.disconnect()
            
            self.is_ready = False
            logger.info("Manuscript Generator cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Manuscript Generator cleanup: {e}")