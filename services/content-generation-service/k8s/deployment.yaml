apiVersion: apps/v1
kind: Deployment
metadata:
  name: content-generation-service
  namespace: publish-ai
  labels:
    app: content-generation-service
    tier: tier1
    component: generation
spec:
  replicas: 2
  selector:
    matchLabels:
      app: content-generation-service
  template:
    metadata:
      labels:
        app: content-generation-service
        tier: tier1
        component: generation
    spec:
      containers:
      - name: content-generation-service
        image: content-generation-service:latest
        ports:
        - containerPort: 8086
          name: http
        env:
        - name: SERVICE_PORT
          value: "8086"
        - name: SERVICE_HOST
          value: "0.0.0.0"
        - name: EVENT_BUS_URL
          value: "http://event-bus:8080"
        - name: SERVICE_DISCOVERY_URL
          value: "http://service-discovery:8070"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: anthropic-api-key
        - name: PREFERRED_MODEL
          value: "openai"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: LOG_LEVEL
          value: "info"
        volumeMounts:
        - name: tls-certs
          mountPath: /etc/ssl/certs/service
          readOnly: true
        - name: ca-cert
          mountPath: /etc/ssl/certs/ca
          readOnly: true
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8086
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8086
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: tls-certs
        secret:
          secretName: content-generation-service-tls
      - name: ca-cert
        secret:
          secretName: ca-certificate
      securityContext:
        fsGroup: 1000
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: content-generation-service
  namespace: publish-ai
  labels:
    app: content-generation-service
    tier: tier1
spec:
  selector:
    app: content-generation-service
  ports:
  - name: http
    port: 8086
    targetPort: 8086
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: content-generation-service-tls
  namespace: publish-ai
type: kubernetes.io/tls
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCi4uLiAoYmFzZTY0IGVuY29kZWQgY2VydGlmaWNhdGUpCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCi4uLiAoYmFzZTY0IGVuY29kZWQgcHJpdmF0ZSBrZXkpCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0K
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: content-generation-service-network-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: content-generation-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: api-gateway
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8086
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8080
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8070
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS for AI APIs
    - protocol: TCP
      port: 80   # HTTP for external APIs
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: content-generation-service-pdb
  namespace: publish-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: content-generation-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: content-generation-service-hpa
  namespace: publish-ai
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: content-generation-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60