# API Gateway Service Dependencies

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Data Validation
pydantic==2.5.0

# HTTP Client & Proxy
httpx==0.25.2
requests==2.31.0

# Rate Limiting & Caching
slowapi==0.1.9
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.8

# Service Discovery & Health Checks
consul==1.1.0

# Load Balancing
haproxy-stats==2.3.0

# Monitoring & Metrics
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# Circuit Breaker
pybreaker==0.8.0

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging
structlog==23.2.0

# API Documentation
python-multipart==0.0.6

# Testing Dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1