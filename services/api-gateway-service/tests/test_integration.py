"""
Integration tests for API Gateway Service
"""

import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../src"))

from main import app


class TestAPIGatewayIntegration:
    """Integration test cases for API Gateway"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)

    @pytest.fixture
    def mock_all_services(self):
        """Mock all external services and components"""
        with patch.multiple(
            'main',
            gateway_router=MagicMock(),
            service_registry=MagicMock(),
            load_balancer=MagicMock(),
            rate_limiter=MagicMock(),
            circuit_breaker_manager=MagicMock(),
            auth_manager=MagicMock(),
            middleware_manager=MagicMock()
        ) as mocks:
            
            # Configure basic readiness
            for mock in mocks.values():
                if hasattr(mock, 'is_ready'):
                    mock.is_ready = True
                if hasattr(mock, 'is_connected'):
                    mock.is_connected = True
            
            yield mocks

    def test_complete_request_flow(self, client, mock_all_services):
        """Test complete request flow through gateway"""
        # Mock successful service response
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        mock_response.content = b'{"result": "success"}'
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request through gateway
        response = client.get("/api/research/analyze")
        
        # Verify the request was processed
        mock_all_services['gateway_router'].route_request.assert_called_once()

    def test_authentication_flow(self, client, mock_all_services):
        """Test authentication flow"""
        # Configure auth manager to require authentication
        mock_all_services['auth_manager'].authenticate_request = AsyncMock()
        
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make authenticated request
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/api/research/analyze", headers=headers)
        
        # Verify authentication was checked
        assert mock_all_services['gateway_router'].route_request.called

    def test_rate_limiting_flow(self, client, mock_all_services):
        """Test rate limiting flow"""
        # Configure rate limiter
        mock_all_services['rate_limiter'].check_rate_limit = AsyncMock()
        
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request
        response = client.get("/api/research/analyze")
        
        # Verify rate limiting was checked
        assert mock_all_services['gateway_router'].route_request.called

    def test_circuit_breaker_flow(self, client, mock_all_services):
        """Test circuit breaker flow"""
        # Configure circuit breaker
        mock_all_services['circuit_breaker_manager'].execute = AsyncMock()
        
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request
        response = client.get("/api/research/analyze")
        
        # Verify circuit breaker was involved
        assert mock_all_services['gateway_router'].route_request.called

    def test_service_discovery_flow(self, client, mock_all_services):
        """Test service discovery flow"""
        # Configure service registry
        mock_all_services['service_registry'].get_service = AsyncMock(return_value={
            "name": "research-service",
            "endpoint": "http://research:8001"
        })
        
        # Configure load balancer
        mock_all_services['load_balancer'].get_service_endpoint = AsyncMock(
            return_value="http://research:8001"
        )
        
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request
        response = client.get("/api/research/analyze")
        
        # Verify service discovery was used
        assert mock_all_services['gateway_router'].route_request.called

    def test_middleware_chain(self, client, mock_all_services):
        """Test middleware chain execution"""
        # Configure middleware manager
        mock_all_services['middleware_manager'].apply_pre_routing_middleware = AsyncMock()
        mock_all_services['middleware_manager'].apply_post_routing_middleware = AsyncMock()
        
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request
        response = client.get("/api/research/analyze")
        
        # Verify middleware was applied
        assert mock_all_services['gateway_router'].route_request.called

    def test_error_handling_chain(self, client, mock_all_services):
        """Test error handling through the chain"""
        # Configure gateway router to raise exception
        mock_all_services['gateway_router'].route_request = AsyncMock(
            side_effect=Exception("Service error")
        )
        
        # Make request
        response = client.get("/api/research/analyze")
        
        # Should get 500 error with proper error response
        assert response.status_code == 500
        data = response.json()
        assert data["success"] == False
        assert "Internal gateway error" in data["message"]

    def test_health_check_integration(self, client, mock_all_services):
        """Test health check integration"""
        response = client.get("/health")
        
        # Should get health status
        assert response.status_code == 200
        data = response.json()
        assert data["service"] == "api-gateway"
        assert "components" in data

    def test_metrics_integration(self, client, mock_all_services):
        """Test metrics integration"""
        with patch('main.get_metrics') as mock_get_metrics:
            mock_get_metrics.return_value = {
                "requests_total": 100,
                "error_rate": 1.5,
                "avg_response_time": 0.25
            }
            
            response = client.get("/metrics")
            
            assert response.status_code == 200
            data = response.json()
            assert "total_requests" in data
            assert "error_rate" in data

    def test_service_management_integration(self, client, mock_all_services):
        """Test service management integration"""
        # Mock service registry operations
        mock_all_services['service_registry'].get_all_services = AsyncMock(return_value=[
            {"name": "test-service", "endpoint": "http://test:8000"}
        ])
        mock_all_services['service_registry'].check_service_health = AsyncMock(
            return_value={"status": "healthy"}
        )
        
        # Test services health endpoint
        response = client.get("/services")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["service_name"] == "test-service"

    def test_service_reload_integration(self, client, mock_all_services):
        """Test service reload integration"""
        mock_all_services['service_registry'].reload_service = AsyncMock(return_value=True)
        
        response = client.post("/services/test-service/reload")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] == True

    def test_concurrent_requests(self, client, mock_all_services):
        """Test handling concurrent requests"""
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make multiple concurrent requests
        responses = []
        for i in range(5):
            response = client.get(f"/api/research/analyze?request={i}")
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200

    def test_different_http_methods(self, client, mock_all_services):
        """Test different HTTP methods"""
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Test different methods
        methods = [
            ("GET", client.get),
            ("POST", client.post),
            ("PUT", client.put),
            ("DELETE", client.delete),
            ("PATCH", client.patch)
        ]
        
        for method_name, method_func in methods:
            response = method_func("/api/research/analyze")
            # All should be processed (success depends on service mock)
            assert response.status_code in [200, 422]  # 422 for methods expecting body

    def test_request_with_body(self, client, mock_all_services):
        """Test request with body data"""
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make POST request with JSON body
        response = client.post(
            "/api/content/generate",
            json={"topic": "AI in healthcare", "length": 1000}
        )
        
        # Should be processed
        assert mock_all_services['gateway_router'].route_request.called

    def test_request_with_query_parameters(self, client, mock_all_services):
        """Test request with query parameters"""
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request with query parameters
        response = client.get("/api/research/analyze?topic=ai&limit=10")
        
        # Should be processed
        assert mock_all_services['gateway_router'].route_request.called

    def test_request_with_headers(self, client, mock_all_services):
        """Test request with custom headers"""
        # Mock successful routing
        mock_response = MagicMock()
        mock_response.headers = {}
        mock_response.status_code = 200
        
        mock_all_services['gateway_router'].route_request = AsyncMock(return_value=mock_response)
        
        # Make request with custom headers
        headers = {
            "X-API-Key": "test-key",
            "X-Request-Source": "integration-test",
            "Content-Type": "application/json"
        }
        
        response = client.get("/api/research/analyze", headers=headers)
        
        # Should be processed
        assert mock_all_services['gateway_router'].route_request.called


if __name__ == "__main__":
    pytest.main([__file__])