"""
Tests for API Gateway Authentication Manager
"""

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta
import jwt
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../src"))

from auth_manager import AuthManager, UserInfo
from fastapi import HTTPException, Request


class TestAuthManager:
    """Test cases for Authentication Manager"""

    @pytest.fixture
    def auth_manager(self):
        """Create auth manager instance"""
        manager = AuthManager(secret_key="test-secret-key")
        manager.is_ready = True
        return manager

    @pytest.fixture
    def mock_request(self):
        """Create mock request"""
        request = MagicMock(spec=Request)
        request.headers = {}
        request.state = MagicMock()
        return request

    @pytest.mark.asyncio
    async def test_initialize_success(self):
        """Test successful initialization"""
        manager = AuthManager(secret_key="test-secret")
        await manager.initialize()
        
        assert manager.is_ready == True
        assert len(manager.api_keys) > 0  # Should have default API keys

    @pytest.mark.asyncio
    async def test_cleanup(self, auth_manager):
        """Test cleanup"""
        await auth_manager.cleanup()
        assert auth_manager.is_ready == False

    @pytest.mark.asyncio
    async def test_authenticate_jwt_success(self, auth_manager, mock_request):
        """Test successful JWT authentication"""
        # Create valid JWT token
        token = await auth_manager.create_jwt_token(
            user_id="user123",
            email="<EMAIL>",
            role="user"
        )
        
        mock_request.headers = {"Authorization": f"Bearer {token}"}
        
        user_info = await auth_manager.authenticate_request(mock_request)
        
        assert user_info.user_id == "user123"
        assert user_info.email == "<EMAIL>"
        assert user_info.role == "user"
        assert mock_request.state.user_id == "user123"

    @pytest.mark.asyncio
    async def test_authenticate_jwt_invalid_token(self, auth_manager, mock_request):
        """Test JWT authentication with invalid token"""
        mock_request.headers = {"Authorization": "Bearer invalid-token"}
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_authenticate_jwt_expired_token(self, auth_manager, mock_request):
        """Test JWT authentication with expired token"""
        # Create expired token
        past_time = datetime.utcnow() - timedelta(hours=1)
        payload = {
            "sub": "user123",
            "exp": past_time
        }
        
        expired_token = jwt.encode(payload, auth_manager.secret_key, algorithm=auth_manager.algorithm)
        mock_request.headers = {"Authorization": f"Bearer {expired_token}"}
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_authenticate_api_key_success(self, auth_manager, mock_request):
        """Test successful API key authentication"""
        # Use default admin API key
        admin_key = "pk_admin_gateway_key_12345678901234567890"
        mock_request.headers = {"X-API-Key": admin_key}
        
        user_info = await auth_manager.authenticate_request(mock_request)
        
        assert user_info.user_id == "admin"
        assert user_info.role == "admin"
        assert mock_request.state.user_id == "admin"

    @pytest.mark.asyncio
    async def test_authenticate_api_key_invalid(self, auth_manager, mock_request):
        """Test API key authentication with invalid key"""
        mock_request.headers = {"X-API-Key": "invalid-api-key"}
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_authenticate_no_credentials(self, auth_manager, mock_request):
        """Test authentication with no credentials"""
        mock_request.headers = {}
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_authorize_service_access_admin(self, auth_manager):
        """Test service authorization for admin user"""
        user_info = UserInfo(user_id="admin", role="admin", permissions=[])
        
        # Admin should have access to everything
        result = await auth_manager.authorize_service_access(
            user_info, "research-service", "read"
        )
        assert result == True
        
        result = await auth_manager.authorize_service_access(
            user_info, "publishing-service", "write"
        )
        assert result == True

    @pytest.mark.asyncio
    async def test_authorize_service_access_user_with_permission(self, auth_manager):
        """Test service authorization for user with specific permission"""
        user_info = UserInfo(
            user_id="user123",
            role="user",
            permissions=["read:research", "write:content"]
        )
        
        # Should have access to research service for read
        result = await auth_manager.authorize_service_access(
            user_info, "research-service", "read"
        )
        assert result == True
        
        # Should not have access to research service for write
        result = await auth_manager.authorize_service_access(
            user_info, "research-service", "write"
        )
        assert result == False

    @pytest.mark.asyncio
    async def test_authorize_service_access_user_no_permission(self, auth_manager):
        """Test service authorization for user without permission"""
        user_info = UserInfo(user_id="user123", role="user", permissions=[])
        
        result = await auth_manager.authorize_service_access(
            user_info, "publishing-service", "write"
        )
        assert result == False

    @pytest.mark.asyncio
    async def test_create_jwt_token(self, auth_manager):
        """Test JWT token creation"""
        token = await auth_manager.create_jwt_token(
            user_id="user123",
            email="<EMAIL>",
            role="manager",
            permissions=["read:all"],
            expires_in=3600
        )
        
        # Decode and verify token
        payload = jwt.decode(
            token,
            auth_manager.secret_key,
            algorithms=[auth_manager.algorithm]
        )
        
        assert payload["sub"] == "user123"
        assert payload["email"] == "<EMAIL>"
        assert payload["role"] == "manager"
        assert payload["permissions"] == ["read:all"]

    @pytest.mark.asyncio
    async def test_create_jwt_token_error(self, auth_manager):
        """Test JWT token creation error"""
        with patch('jwt.encode', side_effect=Exception("Encoding failed")):
            with pytest.raises(ValueError):
                await auth_manager.create_jwt_token(user_id="user123")

    @pytest.mark.asyncio
    async def test_create_api_key(self, auth_manager):
        """Test API key creation"""
        api_key = await auth_manager.create_api_key(
            user_id="user123",
            role="user",
            permissions=["read:own"],
            description="Test API key"
        )
        
        assert api_key.startswith("pk_")
        assert api_key in auth_manager.api_keys
        
        key_info = auth_manager.api_keys[api_key]
        assert key_info["user_id"] == "user123"
        assert key_info["role"] == "user"
        assert key_info["active"] == True

    @pytest.mark.asyncio
    async def test_create_api_key_with_expiration(self, auth_manager):
        """Test API key creation with expiration"""
        api_key = await auth_manager.create_api_key(
            user_id="user123",
            expires_in=3600
        )
        
        key_info = auth_manager.api_keys[api_key]
        assert key_info["expires_at"] is not None
        assert key_info["expires_at"] > datetime.now()

    @pytest.mark.asyncio
    async def test_revoke_api_key(self, auth_manager):
        """Test API key revocation"""
        # Create API key first
        api_key = await auth_manager.create_api_key(user_id="user123")
        
        # Revoke it
        result = await auth_manager.revoke_api_key(api_key)
        assert result == True
        
        # Check it's marked as inactive
        key_info = auth_manager.api_keys[api_key]
        assert key_info["active"] == False

    @pytest.mark.asyncio
    async def test_revoke_nonexistent_api_key(self, auth_manager):
        """Test revoking nonexistent API key"""
        result = await auth_manager.revoke_api_key("nonexistent-key")
        assert result == False

    def test_get_api_keys_info(self, auth_manager):
        """Test getting API keys information"""
        info = auth_manager.get_api_keys_info()
        
        assert isinstance(info, dict)
        assert len(info) > 0  # Should have default keys
        
        # Check info structure
        first_key_info = next(iter(info.values()))
        assert "user_id" in first_key_info
        assert "role" in first_key_info
        assert "active" in first_key_info

    def test_get_role_permissions(self, auth_manager):
        """Test getting role permissions"""
        permissions = auth_manager.get_role_permissions()
        
        assert isinstance(permissions, dict)
        assert "admin" in permissions
        assert "user" in permissions
        assert len(permissions["admin"]) > len(permissions["user"])

    def test_get_service_permissions(self, auth_manager):
        """Test getting service permissions"""
        permissions = auth_manager.get_service_permissions()
        
        assert isinstance(permissions, dict)
        assert "research-service" in permissions
        assert "publishing-service" in permissions

    @pytest.mark.asyncio
    async def test_update_role_permissions(self, auth_manager):
        """Test updating role permissions"""
        new_permissions = ["read:test", "write:test"]
        
        result = await auth_manager.update_role_permissions("test_role", new_permissions)
        assert result == True
        
        # Verify update
        assert auth_manager.role_permissions["test_role"] == new_permissions

    @pytest.mark.asyncio
    async def test_add_service_permissions(self, auth_manager):
        """Test adding service permissions"""
        new_permissions = ["read:test", "write:test"]
        
        result = await auth_manager.add_service_permissions("test-service", new_permissions)
        assert result == True
        
        # Verify addition
        assert auth_manager.service_permissions["test-service"] == new_permissions

    @pytest.mark.asyncio
    async def test_authenticate_jwt_no_bearer_prefix(self, auth_manager, mock_request):
        """Test JWT authentication without Bearer prefix"""
        token = await auth_manager.create_jwt_token(user_id="user123")
        mock_request.headers = {"Authorization": token}  # Missing "Bearer "
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_authenticate_jwt_no_sub_claim(self, auth_manager, mock_request):
        """Test JWT authentication with missing sub claim"""
        payload = {"role": "user", "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(payload, auth_manager.secret_key, algorithm=auth_manager.algorithm)
        
        mock_request.headers = {"Authorization": f"Bearer {token}"}
        
        with pytest.raises(HTTPException) as exc_info:
            await auth_manager.authenticate_request(mock_request)
        
        assert exc_info.value.status_code == 401


if __name__ == "__main__":
    pytest.main([__file__])