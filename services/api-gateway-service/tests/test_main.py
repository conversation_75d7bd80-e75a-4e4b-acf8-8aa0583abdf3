"""
Tests for API Gateway Service main application
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../src"))

from main import app
from gateway_router import GatewayRouter
from service_registry import ServiceRegistry
from load_balancer import LoadBalancer
from rate_limiter import RateLimiter
from circuit_breaker import CircuitBreakerManager
from auth_manager import AuthManager
from middleware_manager import MiddlewareManager


class TestAPIGatewayMain:
    """Test cases for API Gateway main application"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)

    @pytest.fixture
    def mock_components(self):
        """Mock all gateway components"""
        with patch('main.gateway_router') as mock_router, \
             patch('main.service_registry') as mock_registry, \
             patch('main.load_balancer') as mock_balancer, \
             patch('main.rate_limiter') as mock_rate_limiter, \
             patch('main.circuit_breaker_manager') as mock_circuit_breaker, \
             patch('main.auth_manager') as mock_auth, \
             patch('main.middleware_manager') as mock_middleware:
            
            # Configure mocks
            mock_router.is_ready = True
            mock_registry.is_connected = True
            mock_balancer.is_ready = True
            mock_rate_limiter.is_connected = True
            mock_circuit_breaker.is_ready = True
            mock_auth.is_ready = True
            
            yield {
                'router': mock_router,
                'registry': mock_registry,
                'balancer': mock_balancer,
                'rate_limiter': mock_rate_limiter,
                'circuit_breaker': mock_circuit_breaker,
                'auth': mock_auth,
                'middleware': mock_middleware
            }

    def test_health_check_healthy(self, client, mock_components):
        """Test health check when all components are healthy"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["service"] == "api-gateway"
        assert data["status"] == "healthy"
        assert "components" in data
        assert data["components"]["gateway_router"] == True

    def test_health_check_degraded(self, client):
        """Test health check when components are not ready"""
        with patch('main.gateway_router', None):
            response = client.get("/health")
            assert response.status_code == 503
            
            data = response.json()
            assert data["status"] == "degraded"

    def test_readiness_check_ready(self, client, mock_components):
        """Test readiness check when gateway is ready"""
        response = client.get("/ready")
        assert response.status_code == 200
        
        data = response.json()
        assert data["service"] == "api-gateway"
        assert data["status"] == "ready"

    def test_readiness_check_not_ready(self, client):
        """Test readiness check when gateway is not ready"""
        with patch('main.gateway_router', None):
            response = client.get("/ready")
            assert response.status_code == 503

    def test_gateway_status(self, client, mock_components):
        """Test gateway status endpoint"""
        # Mock metrics
        with patch('main.get_metrics') as mock_get_metrics, \
             patch('main.service_registry') as mock_registry:
            
            mock_get_metrics.return_value = {
                "requests_total": 100,
                "error_rate": 2.5,
                "avg_response_time": 0.15,
                "active_connections": 5
            }
            
            mock_registry.get_all_services = AsyncMock(return_value=[
                {"name": "test-service", "endpoint": "http://test:8000"}
            ])
            mock_registry.check_service_health = AsyncMock(return_value={"status": "healthy"})
            
            response = client.get("/status")
            assert response.status_code == 200
            
            data = response.json()
            assert data["gateway_status"] == "healthy"
            assert data["total_services"] == 1
            assert data["healthy_services"] == 1
            assert data["request_count"] == 100

    def test_services_health(self, client, mock_components):
        """Test services health endpoint"""
        with patch('main.service_registry') as mock_registry:
            mock_registry.get_all_services = AsyncMock(return_value=[
                {"name": "test-service", "endpoint": "http://test:8000"}
            ])
            mock_registry.check_service_health = AsyncMock(return_value={"status": "healthy"})
            
            response = client.get("/services")
            assert response.status_code == 200
            
            data = response.json()
            assert len(data) == 1
            assert data[0]["service_name"] == "test-service"
            assert data[0]["status"] == "healthy"

    def test_reload_service(self, client, mock_components):
        """Test service reload endpoint"""
        with patch('main.service_registry') as mock_registry:
            mock_registry.reload_service = AsyncMock(return_value=True)
            
            response = client.post("/services/test-service/reload")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] == True
            assert "reloaded successfully" in data["message"]

    def test_reload_service_not_found(self, client, mock_components):
        """Test service reload when service not found"""
        with patch('main.service_registry') as mock_registry:
            mock_registry.reload_service = AsyncMock(return_value=False)
            
            response = client.post("/services/nonexistent-service/reload")
            assert response.status_code == 404

    def test_gateway_metrics(self, client, mock_components):
        """Test gateway metrics endpoint"""
        with patch('main.get_metrics') as mock_get_metrics, \
             patch('main.circuit_breaker_manager') as mock_cb, \
             patch('main.rate_limiter') as mock_rl:
            
            mock_get_metrics.return_value = {
                "uptime": 3600,
                "requests_total": 1000,
                "error_rate": 1.5,
                "avg_response_time": 0.2,
                "active_connections": 10
            }
            mock_cb.get_trip_count = MagicMock(return_value=2)
            mock_rl.get_limit_hits = MagicMock(return_value=5)
            
            response = client.get("/metrics")
            assert response.status_code == 200
            
            data = response.json()
            assert data["gateway_version"] == "1.0.0"
            assert data["uptime"] == 3600
            assert data["total_requests"] == 1000
            assert data["circuit_breaker_trips"] == 2
            assert data["rate_limit_hits"] == 5

    def test_proxy_request_404(self, client):
        """Test catch-all proxy returns 404 for unhandled paths"""
        response = client.get("/unknown/path")
        assert response.status_code == 404
        
        data = response.json()
        assert data["success"] == False
        assert "Service not found" in data["message"]

    def test_middleware_request_processing(self, client, mock_components):
        """Test request processing middleware"""
        with patch('main.gateway_router') as mock_router:
            # Mock successful routing
            mock_response = MagicMock()
            mock_response.headers = {}
            mock_router.route_request = AsyncMock(return_value=mock_response)
            
            response = client.get("/api/test")
            
            # Check that router was called
            mock_router.route_request.assert_called_once()
            
            # Check middleware headers were added
            assert "X-Request-ID" in mock_response.headers
            assert "X-Gateway-Time" in mock_response.headers

    def test_middleware_gateway_not_ready(self, client):
        """Test middleware when gateway is not ready"""
        with patch('main.gateway_router', None):
            response = client.get("/api/test")
            assert response.status_code == 503
            
            data = response.json()
            assert data["success"] == False
            assert "Gateway not ready" in data["message"]

    def test_middleware_exception_handling(self, client, mock_components):
        """Test middleware exception handling"""
        with patch('main.gateway_router') as mock_router:
            # Mock router raising exception
            mock_router.route_request = AsyncMock(side_effect=Exception("Test error"))
            
            response = client.get("/api/test")
            assert response.status_code == 500
            
            data = response.json()
            assert data["success"] == False
            assert "Internal gateway error" in data["message"]

    def test_admin_endpoints_bypass_gateway(self, client):
        """Test that admin endpoints bypass gateway processing"""
        # Health endpoint should work even without gateway components
        response = client.get("/health")
        # Should not return 503 even with no components
        assert response.status_code in [200, 503]  # May be degraded but not gateway error
        
        # Docs endpoint should be accessible
        response = client.get("/docs")
        assert response.status_code == 200

    def test_cors_headers(self, client):
        """Test CORS headers are present"""
        response = client.get("/health")
        
        # Check CORS headers from middleware
        assert "Access-Control-Allow-Origin" in response.headers

    def test_security_headers(self, client):
        """Test security headers are present"""
        response = client.get("/health")
        
        # Check for security headers (these might be added by middleware)
        # Note: Actual headers depend on middleware configuration
        assert response.status_code in [200, 503]  # Just ensure response is valid

    def test_rate_limiting_headers(self, client):
        """Test rate limiting integration"""
        # This would require actual rate limiting setup
        # For now, just test that endpoints are accessible
        response = client.get("/health")
        assert response.status_code in [200, 503]

    @pytest.mark.asyncio
    async def test_startup_event(self):
        """Test startup event initialization"""
        # This is more of an integration test
        # In real scenario, would mock all external dependencies
        pass

    @pytest.mark.asyncio
    async def test_shutdown_event(self):
        """Test shutdown event cleanup"""
        # This is more of an integration test
        # In real scenario, would verify cleanup calls
        pass


# Additional test utilities

def test_environment_variable_defaults():
    """Test environment variable defaults"""
    # Test default port
    default_port = int(os.getenv("SERVICE_PORT", "8080"))
    assert default_port == 8080
    
    # Test default host
    default_host = os.getenv("SERVICE_HOST", "0.0.0.0")
    assert default_host == "0.0.0.0"

def test_service_configuration():
    """Test service configuration values"""
    # Test that configuration values are reasonable
    assert True  # Placeholder for configuration tests


if __name__ == "__main__":
    pytest.main([__file__])