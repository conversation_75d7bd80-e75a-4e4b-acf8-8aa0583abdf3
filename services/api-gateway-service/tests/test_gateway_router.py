"""
Tests for API Gateway Router
"""

import pytest
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch
import httpx
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, Response, status
from fastapi.testclient import TestClient
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../src"))

from gateway_router import GatewayRouter


class TestGatewayRouter:
    """Test cases for Gateway Router"""

    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies"""
        service_registry = AsyncMock()
        load_balancer = AsyncMock()
        rate_limiter = AsyncMock()
        circuit_breaker_manager = AsyncMock()
        auth_manager = AsyncMock()
        middleware_manager = AsyncMock()
        
        return {
            'service_registry': service_registry,
            'load_balancer': load_balancer,
            'rate_limiter': rate_limiter,
            'circuit_breaker_manager': circuit_breaker_manager,
            'auth_manager': auth_manager,
            'middleware_manager': middleware_manager
        }

    @pytest.fixture
    def gateway_router(self, mock_dependencies):
        """Create gateway router with mocked dependencies"""
        router = GatewayRouter(**mock_dependencies)
        router.is_ready = True
        return router

    @pytest.fixture
    def mock_request(self):
        """Create mock request"""
        request = MagicMock(spec=Request)
        request.url.path = "/api/research/analyze"
        request.url.query = "param=value"
        request.method = "GET"
        request.headers = {"Authorization": "Bearer token123"}
        request.client.host = "127.0.0.1"
        request.state = MagicMock()
        return request

    @pytest.mark.asyncio
    async def test_initialize_success(self, mock_dependencies):
        """Test successful initialization"""
        router = GatewayRouter(**mock_dependencies)
        
        with patch('httpx.AsyncClient') as mock_client:
            await router.initialize()
            assert router.is_ready == True

    @pytest.mark.asyncio
    async def test_initialize_failure(self, mock_dependencies):
        """Test initialization failure"""
        router = GatewayRouter(**mock_dependencies)
        
        with patch('httpx.AsyncClient', side_effect=Exception("Connection failed")):
            with pytest.raises(Exception):
                await router.initialize()
            assert router.is_ready == False

    @pytest.mark.asyncio
    async def test_cleanup(self, gateway_router):
        """Test cleanup"""
        gateway_router.http_client = AsyncMock()
        
        await gateway_router.cleanup()
        
        gateway_router.http_client.aclose.assert_called_once()
        assert gateway_router.is_ready == False

    def test_match_service_success(self, gateway_router):
        """Test successful service matching"""
        # Test exact match
        service = gateway_router._match_service("/api/research/analyze")
        assert service == "research-service"
        
        # Test partial match
        service = gateway_router._match_service("/api/content/generate")
        assert service == "content-generation-service"

    def test_match_service_no_match(self, gateway_router):
        """Test no service match"""
        service = gateway_router._match_service("/unknown/path")
        assert service is None

    def test_requires_authentication_public_paths(self, gateway_router):
        """Test authentication requirements for public paths"""
        public_paths = ["/health", "/ready", "/docs", "/redoc", "/openapi.json"]
        
        for path in public_paths:
            assert gateway_router._requires_authentication(path) == False

    def test_requires_authentication_api_paths(self, gateway_router):
        """Test authentication requirements for API paths"""
        api_paths = ["/api/research", "/api/content", "/api/publishing"]
        
        for path in api_paths:
            assert gateway_router._requires_authentication(path) == True

    def test_build_target_url(self, gateway_router):
        """Test target URL building"""
        service_endpoint = "http://research-service:8001"
        path = "/api/research/analyze"
        query = "param=value"
        
        target_url = gateway_router._build_target_url(service_endpoint, path, query)
        
        assert target_url == "http://research-service:8001/api/research/analyze?param=value"

    def test_build_target_url_no_query(self, gateway_router):
        """Test target URL building without query"""
        service_endpoint = "http://research-service:8001"
        path = "/api/research/analyze"
        query = ""
        
        target_url = gateway_router._build_target_url(service_endpoint, path, query)
        
        assert target_url == "http://research-service:8001/api/research/analyze"

    def test_clean_headers(self, gateway_router):
        """Test header cleaning"""
        headers = {
            "Authorization": "Bearer token",
            "Content-Type": "application/json",
            "Connection": "keep-alive",  # Should be removed
            "X-Forwarded-For": "127.0.0.1",  # Should be removed
            "Custom-Header": "value"
        }
        
        clean_headers = gateway_router._clean_headers(headers)
        
        assert "Authorization" in clean_headers
        assert "Content-Type" in clean_headers
        assert "Custom-Header" in clean_headers
        assert "Connection" not in clean_headers
        assert "X-Forwarded-For" not in clean_headers
        assert clean_headers["X-Forwarded-By"] == "api-gateway"

    @pytest.mark.asyncio
    async def test_route_request_success(self, gateway_router, mock_request):
        """Test successful request routing"""
        # Setup mocks
        gateway_router.middleware_manager.apply_pre_routing_middleware = AsyncMock(return_value=mock_request)
        gateway_router.middleware_manager.apply_post_routing_middleware = AsyncMock()
        gateway_router.auth_manager.authenticate_request = AsyncMock()
        gateway_router.rate_limiter.check_rate_limit = AsyncMock()
        gateway_router.load_balancer.get_service_endpoint = AsyncMock(return_value="http://research:8001")
        
        # Mock circuit breaker execution
        mock_response = Response(content="success", status_code=200)
        mock_response.headers = {}
        gateway_router.circuit_breaker_manager.execute = AsyncMock(return_value=mock_response)
        
        result = await gateway_router.route_request(mock_request)
        
        assert result == mock_response
        assert "X-Service-Name" in result.headers
        assert "X-Service-Endpoint" in result.headers

    @pytest.mark.asyncio
    async def test_route_request_no_service(self, gateway_router, mock_request):
        """Test routing when no service matches"""
        mock_request.url.path = "/unknown/path"
        
        with pytest.raises(HTTPException) as exc_info:
            await gateway_router.route_request(mock_request)
        
        assert exc_info.value.status_code == 404

    @pytest.mark.asyncio
    async def test_route_request_service_unavailable(self, gateway_router, mock_request):
        """Test routing when service is unavailable"""
        # Setup mocks
        gateway_router.middleware_manager.apply_pre_routing_middleware = AsyncMock(return_value=mock_request)
        gateway_router.auth_manager.authenticate_request = AsyncMock()
        gateway_router.rate_limiter.check_rate_limit = AsyncMock()
        gateway_router.load_balancer.get_service_endpoint = AsyncMock(return_value=None)
        
        with pytest.raises(HTTPException) as exc_info:
            await gateway_router.route_request(mock_request)
        
        assert exc_info.value.status_code == 503

    @pytest.mark.asyncio
    async def test_make_service_request_success(self, gateway_router):
        """Test successful service request"""
        gateway_router.http_client = AsyncMock()
        
        # Mock httpx response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b'{"result": "success"}'
        mock_response.headers = {"content-type": "application/json"}
        
        gateway_router.http_client.__aenter__ = AsyncMock(return_value=gateway_router.http_client)
        gateway_router.http_client.__aexit__ = AsyncMock(return_value=None)
        gateway_router.http_client.request = AsyncMock(return_value=mock_response)
        
        mock_request = MagicMock()
        mock_request.body = AsyncMock(return_value=b'{"data": "test"}')
        
        result = await gateway_router._make_service_request(
            "http://test:8000/api/test",
            "POST",
            {"Content-Type": "application/json"},
            mock_request
        )
        
        assert result.status_code == 200
        assert result.content == b'{"result": "success"}'

    @pytest.mark.asyncio
    async def test_make_service_request_timeout(self, gateway_router):
        """Test service request timeout"""
        gateway_router.http_client = AsyncMock()
        gateway_router.http_client.__aenter__ = AsyncMock(return_value=gateway_router.http_client)
        gateway_router.http_client.__aexit__ = AsyncMock(return_value=None)
        gateway_router.http_client.request = AsyncMock(side_effect=httpx.TimeoutException("Timeout"))
        
        mock_request = MagicMock()
        mock_request.body = AsyncMock(return_value=b'')
        
        with pytest.raises(HTTPException) as exc_info:
            await gateway_router._make_service_request(
                "http://test:8000/api/test",
                "GET",
                {},
                mock_request
            )
        
        assert exc_info.value.status_code == 504

    @pytest.mark.asyncio
    async def test_make_service_request_connection_error(self, gateway_router):
        """Test service request connection error"""
        gateway_router.http_client = AsyncMock()
        gateway_router.http_client.__aenter__ = AsyncMock(return_value=gateway_router.http_client)
        gateway_router.http_client.__aexit__ = AsyncMock(return_value=None)
        gateway_router.http_client.request = AsyncMock(side_effect=httpx.ConnectError("Connection failed"))
        
        mock_request = MagicMock()
        mock_request.body = AsyncMock(return_value=b'')
        
        with pytest.raises(HTTPException) as exc_info:
            await gateway_router._make_service_request(
                "http://test:8000/api/test",
                "GET",
                {},
                mock_request
            )
        
        assert exc_info.value.status_code == 503

    @pytest.mark.asyncio
    async def test_add_route_pattern(self, gateway_router):
        """Test adding route pattern"""
        initial_count = len(gateway_router.route_patterns)
        
        await gateway_router.add_route_pattern("/api/new", "new-service")
        
        assert len(gateway_router.route_patterns) == initial_count + 1
        assert gateway_router.route_patterns["/api/new"] == "new-service"

    @pytest.mark.asyncio
    async def test_remove_route_pattern(self, gateway_router):
        """Test removing route pattern"""
        # Add a pattern first
        await gateway_router.add_route_pattern("/api/temp", "temp-service")
        initial_count = len(gateway_router.route_patterns)
        
        await gateway_router.remove_route_pattern("/api/temp")
        
        assert len(gateway_router.route_patterns) == initial_count - 1
        assert "/api/temp" not in gateway_router.route_patterns

    def test_get_route_patterns(self, gateway_router):
        """Test getting route patterns"""
        patterns = gateway_router.get_route_patterns()
        
        assert isinstance(patterns, dict)
        assert "/api/research" in patterns
        assert patterns["/api/research"] == "research-service"

    @pytest.mark.asyncio
    async def test_health_check_services(self, gateway_router):
        """Test health check of all services"""
        gateway_router.http_client = AsyncMock()
        gateway_router.load_balancer.get_service_endpoint = AsyncMock(return_value="http://test:8000")
        
        # Mock successful health check
        mock_response = MagicMock()
        mock_response.status_code = 200
        
        gateway_router.http_client.__aenter__ = AsyncMock(return_value=gateway_router.http_client)
        gateway_router.http_client.__aexit__ = AsyncMock(return_value=None)
        gateway_router.http_client.get = AsyncMock(return_value=mock_response)
        
        result = await gateway_router.health_check_services()
        
        assert isinstance(result, dict)
        # Should have results for each service in route patterns
        assert len(result) > 0
        
        # Check first service result
        first_service = next(iter(result.values()))
        assert "status" in first_service
        assert "response_time" in first_service

    @pytest.mark.asyncio
    async def test_health_check_services_unavailable(self, gateway_router):
        """Test health check when service is unavailable"""
        gateway_router.load_balancer.get_service_endpoint = AsyncMock(return_value=None)
        
        result = await gateway_router.health_check_services()
        
        assert isinstance(result, dict)
        # Should have results indicating services are unavailable
        first_service = next(iter(result.values()))
        assert first_service["status"] == "unavailable"


if __name__ == "__main__":
    pytest.main([__file__])