"""
API Gateway Load Balancer

Load balancing and service selection logic for the API Gateway.
"""

import asyncio
import logging
import random
import time
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class LoadBalancer:
    """Load balancer for distributing requests across service instances"""
    
    def __init__(self, service_registry):
        self.service_registry = service_registry
        self.is_ready = False
        
        # Load balancing algorithms
        self.algorithms = {
            "round_robin": self._round_robin,
            "random": self._random_selection,
            "least_connections": self._least_connections,
            "weighted_random": self._weighted_random
        }
        
        # Default algorithm
        self.default_algorithm = "round_robin"
        
        # State tracking for round robin
        self.round_robin_state = {}
        
        # Connection tracking for least connections
        self.connection_counts = {}
        
        # Health tracking
        self.unhealthy_instances = set()
        self.health_check_interval = 30  # seconds
        
    async def initialize(self):
        """Initialize the load balancer"""
        try:
            # Start background health checking
            asyncio.create_task(self._health_check_loop())
            
            self.is_ready = True
            logger.info("Load Balancer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Load Balancer: {e}")
            self.is_ready = False
            raise
    
    async def cleanup(self):
        """Cleanup load balancer resources"""
        try:
            self.is_ready = False
            logger.info("Load Balancer cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Load Balancer cleanup: {e}")
    
    async def get_service_endpoint(self, service_name: str, algorithm: Optional[str] = None) -> Optional[str]:
        """Get an endpoint for the specified service using load balancing"""
        try:
            # Get service instances
            instances = await self.service_registry.get_service_instances(service_name)
            if not instances:
                # Fallback to single service endpoint
                service = await self.service_registry.get_service(service_name)
                if service and service.get("endpoint"):
                    return service["endpoint"]
                return None
            
            # Filter out unhealthy instances
            healthy_instances = [
                instance for instance in instances
                if self._get_instance_key(instance) not in self.unhealthy_instances
            ]
            
            if not healthy_instances:
                logger.warning(f"No healthy instances for service {service_name}")
                return None
            
            # Select algorithm
            algo = algorithm or self.default_algorithm
            selector = self.algorithms.get(algo, self.algorithms[self.default_algorithm])
            
            # Select instance
            selected_instance = await selector(service_name, healthy_instances)
            
            if selected_instance:
                endpoint = selected_instance.get("endpoint")
                if endpoint:
                    # Track connection
                    self._track_connection(selected_instance)
                    return endpoint
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get service endpoint for {service_name}: {e}")
            return None
    
    async def _round_robin(self, service_name: str, instances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Round robin load balancing algorithm"""
        if not instances:
            return None
        
        # Get current position for this service
        current_pos = self.round_robin_state.get(service_name, 0)
        
        # Select instance
        selected_instance = instances[current_pos % len(instances)]
        
        # Update position
        self.round_robin_state[service_name] = (current_pos + 1) % len(instances)
        
        return selected_instance
    
    async def _random_selection(self, service_name: str, instances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Random selection load balancing algorithm"""
        if not instances:
            return None
        
        return random.choice(instances)
    
    async def _least_connections(self, service_name: str, instances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Least connections load balancing algorithm"""
        if not instances:
            return None
        
        # Find instance with least connections
        min_connections = float('inf')
        selected_instance = None
        
        for instance in instances:
            instance_key = self._get_instance_key(instance)
            connections = self.connection_counts.get(instance_key, 0)
            
            if connections < min_connections:
                min_connections = connections
                selected_instance = instance
        
        return selected_instance or instances[0]
    
    async def _weighted_random(self, service_name: str, instances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Weighted random selection based on instance weights"""
        if not instances:
            return None
        
        # Calculate weights (could be based on CPU, memory, response time, etc.)
        weights = []
        for instance in instances:
            # Default weight of 1, but could be calculated based on metrics
            weight = instance.get("weight", 1)
            # Adjust weight based on health and performance
            instance_key = self._get_instance_key(instance)
            connections = self.connection_counts.get(instance_key, 0)
            
            # Reduce weight for heavily loaded instances
            if connections > 10:
                weight = weight * 0.5
            
            weights.append(weight)
        
        # Weighted random selection
        if sum(weights) == 0:
            return random.choice(instances)
        
        total_weight = sum(weights)
        random_weight = random.uniform(0, total_weight)
        
        current_weight = 0
        for i, weight in enumerate(weights):
            current_weight += weight
            if random_weight <= current_weight:
                return instances[i]
        
        return instances[-1]  # Fallback
    
    def _get_instance_key(self, instance: Dict[str, Any]) -> str:
        """Get unique key for an instance"""
        return f"{instance.get('host', 'unknown')}:{instance.get('port', 'unknown')}"
    
    def _track_connection(self, instance: Dict[str, Any]):
        """Track connection to an instance"""
        instance_key = self._get_instance_key(instance)
        self.connection_counts[instance_key] = self.connection_counts.get(instance_key, 0) + 1
    
    def release_connection(self, instance: Dict[str, Any]):
        """Release connection from an instance"""
        instance_key = self._get_instance_key(instance)
        if instance_key in self.connection_counts:
            self.connection_counts[instance_key] = max(0, self.connection_counts[instance_key] - 1)
    
    def mark_instance_unhealthy(self, instance: Dict[str, Any]):
        """Mark an instance as unhealthy"""
        instance_key = self._get_instance_key(instance)
        self.unhealthy_instances.add(instance_key)
        logger.warning(f"Marked instance {instance_key} as unhealthy")
    
    def mark_instance_healthy(self, instance: Dict[str, Any]):
        """Mark an instance as healthy"""
        instance_key = self._get_instance_key(instance)
        self.unhealthy_instances.discard(instance_key)
        logger.info(f"Marked instance {instance_key} as healthy")
    
    async def _health_check_loop(self):
        """Background loop for health checking instances"""
        while self.is_ready:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(5)  # Short delay before retrying
    
    async def _perform_health_checks(self):
        """Perform health checks on all known instances"""
        try:
            services = await self.service_registry.get_all_services()
            
            for service in services:
                service_name = service.get("name")
                if not service_name:
                    continue
                
                instances = await self.service_registry.get_service_instances(service_name)
                
                for instance in instances:
                    instance_key = self._get_instance_key(instance)
                    
                    # Perform health check
                    is_healthy = await self._check_instance_health(instance)
                    
                    if is_healthy:
                        if instance_key in self.unhealthy_instances:
                            self.mark_instance_healthy(instance)
                    else:
                        if instance_key not in self.unhealthy_instances:
                            self.mark_instance_unhealthy(instance)
                            
        except Exception as e:
            logger.error(f"Health check error: {e}")
    
    async def _check_instance_health(self, instance: Dict[str, Any]) -> bool:
        """Check health of a specific instance"""
        try:
            endpoint = instance.get("endpoint")
            if not endpoint:
                return False
            
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{endpoint}/health")
                return response.status_code == 200
                
        except Exception:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get load balancer statistics"""
        return {
            "algorithm": self.default_algorithm,
            "round_robin_state": self.round_robin_state.copy(),
            "connection_counts": self.connection_counts.copy(),
            "unhealthy_instances": list(self.unhealthy_instances),
            "health_check_interval": self.health_check_interval
        }
    
    def set_algorithm(self, algorithm: str):
        """Set the default load balancing algorithm"""
        if algorithm in self.algorithms:
            self.default_algorithm = algorithm
            logger.info(f"Load balancing algorithm set to: {algorithm}")
        else:
            logger.error(f"Unknown algorithm: {algorithm}")
            raise ValueError(f"Unknown algorithm: {algorithm}")
    
    def get_available_algorithms(self) -> List[str]:
        """Get list of available load balancing algorithms"""
        return list(self.algorithms.keys())