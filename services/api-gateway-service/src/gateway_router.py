"""
API Gateway Router

Core routing logic for the API Gateway that handles request routing,
load balancing, and service discovery integration.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import httpx
from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)


class GatewayRouter:
    """Core gateway router for handling request routing and service communication"""
    
    def __init__(
        self,
        service_registry,
        load_balancer,
        rate_limiter,
        circuit_breaker_manager,
        auth_manager,
        middleware_manager
    ):
        self.service_registry = service_registry
        self.load_balancer = load_balancer
        self.rate_limiter = rate_limiter
        self.circuit_breaker_manager = circuit_breaker_manager
        self.auth_manager = auth_manager
        self.middleware_manager = middleware_manager
        
        self.is_ready = False
        self.http_client: Optional[httpx.AsyncClient] = None
        
        # Route patterns for service matching
        self.route_patterns = {
            "/api/research": "research-service",
            "/api/personalization": "personalization-service", 
            "/api/cover": "cover-designer-service",
            "/api/sales": "sales-monitor-service",
            "/api/market": "market-intelligence-service",
            "/api/content": "content-generation-service",
            "/api/publish": "publishing-service",
            "/api/multimodal": "multimodal-generator-service",
            "/api/events": "event-bus",
            "/api/discovery": "service-discovery"
        }
        
        # Default timeouts and retry settings
        self.default_timeout = 30.0
        self.max_retries = 3
        self.retry_delay = 1.0
    
    async def initialize(self):
        """Initialize the gateway router"""
        try:
            # Initialize HTTP client with connection pooling
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.default_timeout),
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
                follow_redirects=True
            )
            
            self.is_ready = True
            logger.info("Gateway Router initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gateway Router: {e}")
            self.is_ready = False
            raise
    
    async def cleanup(self):
        """Cleanup router resources"""
        try:
            if self.http_client:
                await self.http_client.aclose()
            
            self.is_ready = False
            logger.info("Gateway Router cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Gateway Router cleanup: {e}")
    
    async def route_request(self, request: Request) -> Response:
        """Route incoming request to appropriate microservice"""
        start_time = time.time()
        
        try:
            # Extract request details
            path = request.url.path
            method = request.method
            headers = dict(request.headers)
            
            # Find target service
            service_name = self._match_service(path)
            if not service_name:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No service found for path: {path}"
                )
            
            # Apply pre-routing middleware
            await self.middleware_manager.apply_pre_routing_middleware(request)
            
            # Check authentication if required
            if self._requires_authentication(path):
                await self.auth_manager.authenticate_request(request)
            
            # Apply rate limiting
            await self.rate_limiter.check_rate_limit(request)
            
            # Get service endpoint through load balancer
            service_endpoint = await self.load_balancer.get_service_endpoint(service_name)
            if not service_endpoint:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Service {service_name} not available"
                )
            
            # Build target URL
            target_url = self._build_target_url(service_endpoint, path, request.url.query)
            
            # Execute request with circuit breaker
            response = await self.circuit_breaker_manager.execute(
                service_name,
                self._make_service_request,
                target_url,
                method,
                headers,
                request
            )
            
            # Apply post-routing middleware
            response = await self.middleware_manager.apply_post_routing_middleware(response)
            
            # Add routing headers
            response.headers["X-Service-Name"] = service_name
            response.headers["X-Service-Endpoint"] = service_endpoint
            response.headers["X-Routing-Time"] = str(time.time() - start_time)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Routing error for {method} {path}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal routing error"
            )
    
    def _match_service(self, path: str) -> Optional[str]:
        """Match request path to service name"""
        for pattern, service_name in self.route_patterns.items():
            if path.startswith(pattern):
                return service_name
        return None
    
    def _requires_authentication(self, path: str) -> bool:
        """Check if path requires authentication"""
        # Public endpoints that don't require authentication
        public_paths = ["/health", "/ready", "/docs", "/redoc", "/openapi.json"]
        
        for public_path in public_paths:
            if path.startswith(public_path):
                return False
        
        # All API endpoints require authentication by default
        return path.startswith("/api")
    
    def _build_target_url(self, service_endpoint: str, path: str, query: str) -> str:
        """Build the target URL for the service request"""
        # Remove service prefix from path for internal routing
        for pattern in self.route_patterns.keys():
            if path.startswith(pattern):
                # Keep the path but ensure proper formatting
                internal_path = path
                break
        else:
            internal_path = path
        
        # Build full URL
        target_url = urljoin(service_endpoint, internal_path.lstrip('/'))
        
        if query:
            target_url += f"?{query}"
        
        return target_url
    
    async def _make_service_request(
        self,
        target_url: str,
        method: str,
        headers: Dict[str, str],
        request: Request
    ) -> Response:
        """Make the actual request to the target service"""
        try:
            # Prepare headers (remove hop-by-hop headers)
            clean_headers = self._clean_headers(headers)
            
            # Get request body if present
            body = None
            if method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
            
            # Make the request
            async with self.http_client as client:
                response = await client.request(
                    method=method,
                    url=target_url,
                    headers=clean_headers,
                    content=body
                )
                
                # Create FastAPI response
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type=response.headers.get("content-type", "application/json")
                )
                
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="Service request timeout"
            )
        except httpx.ConnectError:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service connection failed"
            )
        except Exception as e:
            logger.error(f"Service request error: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Service request failed"
            )
    
    def _clean_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Clean headers for forwarding (remove hop-by-hop headers)"""
        hop_by_hop_headers = {
            "connection", "keep-alive", "proxy-authenticate",
            "proxy-authorization", "te", "trailers", "transfer-encoding", "upgrade"
        }
        
        clean_headers = {}
        for key, value in headers.items():
            if key.lower() not in hop_by_hop_headers and not key.lower().startswith("x-forwarded"):
                clean_headers[key] = value
        
        # Add gateway identification
        clean_headers["X-Forwarded-By"] = "api-gateway"
        clean_headers["X-Gateway-Version"] = "1.0.0"
        
        return clean_headers
    
    async def add_route_pattern(self, pattern: str, service_name: str):
        """Dynamically add a new route pattern"""
        self.route_patterns[pattern] = service_name
        logger.info(f"Added route pattern: {pattern} -> {service_name}")
    
    async def remove_route_pattern(self, pattern: str):
        """Dynamically remove a route pattern"""
        if pattern in self.route_patterns:
            del self.route_patterns[pattern]
            logger.info(f"Removed route pattern: {pattern}")
    
    def get_route_patterns(self) -> Dict[str, str]:
        """Get current route patterns"""
        return self.route_patterns.copy()
    
    async def health_check_services(self) -> Dict[str, Any]:
        """Perform health check on all routed services"""
        health_results = {}
        
        for pattern, service_name in self.route_patterns.items():
            try:
                service_endpoint = await self.load_balancer.get_service_endpoint(service_name)
                if service_endpoint:
                    health_url = urljoin(service_endpoint, "health")
                    
                    start_time = time.time()
                    async with self.http_client as client:
                        response = await client.get(
                            health_url,
                            timeout=5.0  # Short timeout for health checks
                        )
                    
                    response_time = time.time() - start_time
                    
                    health_results[service_name] = {
                        "status": "healthy" if response.status_code == 200 else "unhealthy",
                        "response_time": response_time,
                        "endpoint": service_endpoint,
                        "pattern": pattern
                    }
                else:
                    health_results[service_name] = {
                        "status": "unavailable",
                        "endpoint": None,
                        "pattern": pattern
                    }
                    
            except Exception as e:
                health_results[service_name] = {
                    "status": "error",
                    "error": str(e),
                    "pattern": pattern
                }
        
        return health_results