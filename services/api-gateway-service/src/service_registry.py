"""
API Gateway Service Registry

Service discovery and registry integration for the API Gateway.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

import httpx

logger = logging.getLogger(__name__)


class ServiceRegistry:
    """Service registry client for service discovery and health monitoring"""
    
    def __init__(self, discovery_url: str):
        self.discovery_url = discovery_url
        self.is_connected = False
        self.http_client: Optional[httpx.AsyncClient] = None
        self.services_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 30  # Cache TTL in seconds
        self.last_cache_update = 0
        
    async def initialize(self):
        """Initialize the service registry client"""
        try:
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(10.0),
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )
            
            # Test connection to service discovery
            await self._test_connection()
            
            # Initial services fetch
            await self._update_services_cache()
            
            self.is_connected = True
            logger.info("Service Registry initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Service Registry: {e}")
            self.is_connected = False
            raise
    
    async def cleanup(self):
        """Cleanup service registry resources"""
        try:
            if self.http_client:
                await self.http_client.aclose()
            
            self.is_connected = False
            logger.info("Service Registry cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Service Registry cleanup: {e}")
    
    async def _test_connection(self):
        """Test connection to service discovery"""
        try:
            response = await self.http_client.get(f"{self.discovery_url}/health")
            if response.status_code != 200:
                raise Exception(f"Service discovery unhealthy: {response.status_code}")
        except Exception as e:
            raise Exception(f"Cannot connect to service discovery: {e}")
    
    async def get_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get service information by name"""
        try:
            await self._ensure_cache_fresh()
            return self.services_cache.get(service_name)
            
        except Exception as e:
            logger.error(f"Failed to get service {service_name}: {e}")
            return None
    
    async def get_all_services(self) -> List[Dict[str, Any]]:
        """Get all registered services"""
        try:
            await self._ensure_cache_fresh()
            return list(self.services_cache.values())
            
        except Exception as e:
            logger.error(f"Failed to get all services: {e}")
            return []
    
    async def get_service_instances(self, service_name: str) -> List[Dict[str, Any]]:
        """Get all instances of a specific service"""
        try:
            response = await self.http_client.get(
                f"{self.discovery_url}/services/{service_name}/instances"
            )
            
            if response.status_code == 200:
                return response.json().get("instances", [])
            else:
                logger.warning(f"Failed to get instances for {service_name}: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to get service instances for {service_name}: {e}")
            return []
    
    async def check_service_health(self, service_name: str) -> Dict[str, Any]:
        """Check health of a specific service"""
        try:
            service = await self.get_service(service_name)
            if not service:
                return {"status": "not_found"}
            
            # Try to get health from service discovery first
            try:
                response = await self.http_client.get(
                    f"{self.discovery_url}/services/{service_name}/health",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    return response.json()
            except:
                pass
            
            # Fallback to direct health check
            endpoint = service.get("endpoint")
            if endpoint:
                health_url = f"{endpoint}/health"
                response = await self.http_client.get(health_url, timeout=5.0)
                
                if response.status_code == 200:
                    return {"status": "healthy", "endpoint": endpoint}
                else:
                    return {"status": "unhealthy", "endpoint": endpoint}
            
            return {"status": "no_endpoint"}
            
        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {e}")
            return {"status": "error", "error": str(e)}
    
    async def register_service(self, service_info: Dict[str, Any]) -> bool:
        """Register a service with the discovery service"""
        try:
            response = await self.http_client.post(
                f"{self.discovery_url}/services/register",
                json=service_info
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Successfully registered service: {service_info.get('name')}")
                await self._update_services_cache()  # Refresh cache
                return True
            else:
                logger.error(f"Failed to register service: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Service registration failed: {e}")
            return False
    
    async def deregister_service(self, service_name: str) -> bool:
        """Deregister a service from the discovery service"""
        try:
            response = await self.http_client.delete(
                f"{self.discovery_url}/services/{service_name}"
            )
            
            if response.status_code in [200, 204]:
                logger.info(f"Successfully deregistered service: {service_name}")
                await self._update_services_cache()  # Refresh cache
                return True
            else:
                logger.error(f"Failed to deregister service: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Service deregistration failed: {e}")
            return False
    
    async def reload_service(self, service_name: str) -> bool:
        """Reload service configuration"""
        try:
            response = await self.http_client.post(
                f"{self.discovery_url}/services/{service_name}/reload"
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully reloaded service: {service_name}")
                await self._update_services_cache()  # Refresh cache
                return True
            else:
                logger.error(f"Failed to reload service: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Service reload failed: {e}")
            return False
    
    async def _ensure_cache_fresh(self):
        """Ensure services cache is fresh"""
        current_time = time.time()
        if current_time - self.last_cache_update > self.cache_ttl:
            await self._update_services_cache()
    
    async def _update_services_cache(self):
        """Update the services cache from service discovery"""
        try:
            response = await self.http_client.get(f"{self.discovery_url}/services")
            
            if response.status_code == 200:
                services_data = response.json()
                services = services_data.get("services", [])
                
                # Update cache
                self.services_cache.clear()
                for service in services:
                    service_name = service.get("name")
                    if service_name:
                        self.services_cache[service_name] = service
                
                self.last_cache_update = time.time()
                logger.debug(f"Updated services cache with {len(self.services_cache)} services")
            else:
                logger.warning(f"Failed to fetch services: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to update services cache: {e}")
    
    def get_cached_services(self) -> Dict[str, Dict[str, Any]]:
        """Get cached services (for debugging/monitoring)"""
        return self.services_cache.copy()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cached_services": len(self.services_cache),
            "last_update": self.last_cache_update,
            "cache_age": time.time() - self.last_cache_update,
            "cache_ttl": self.cache_ttl
        }