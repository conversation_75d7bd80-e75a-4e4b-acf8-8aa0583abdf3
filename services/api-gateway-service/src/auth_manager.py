"""
API Gateway Authentication Manager

Authentication and authorization functionality for the API Gateway
supporting JWT tokens, API keys, and role-based access control.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import jwt
from fastapi import HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class UserInfo(BaseModel):
    """User information extracted from authentication"""
    user_id: str
    email: Optional[str] = None
    role: str = "user"
    permissions: List[str] = []
    expires_at: Optional[datetime] = None


class AuthManager:
    """Authentication manager for API Gateway"""
    
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.is_ready = False
        
        # API key storage (in production, use database)
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        
        # Security scheme
        self.security = HTTPBearer(auto_error=False)
        
        # Default permissions for roles
        self.role_permissions = {
            "admin": [
                "read:all", "write:all", "delete:all",
                "manage:users", "manage:services", "manage:gateway"
            ],
            "manager": [
                "read:all", "write:books", "write:content",
                "read:analytics", "manage:publishing"
            ],
            "user": [
                "read:own", "write:own", "create:books", "publish:books"
            ],
            "readonly": [
                "read:own"
            ]
        }
        
        # Service-specific permissions
        self.service_permissions = {
            "research-service": ["read:research", "write:research"],
            "personalization-service": ["read:personalization", "write:personalization"],
            "cover-designer-service": ["read:covers", "write:covers"],
            "sales-monitor-service": ["read:sales", "read:analytics"],
            "market-intelligence-service": ["read:market", "read:trends"],
            "content-generation-service": ["write:content", "read:content"],
            "publishing-service": ["write:publishing", "read:publishing"],
            "multimodal-generator-service": ["write:multimodal", "read:multimodal"]
        }
        
    async def initialize(self):
        """Initialize authentication manager"""
        try:
            # Load default API keys (in production, load from database)
            await self._load_default_api_keys()
            
            self.is_ready = True
            logger.info("Authentication Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Authentication Manager: {e}")
            self.is_ready = False
            raise
    
    async def cleanup(self):
        """Cleanup authentication manager"""
        try:
            self.is_ready = False
            logger.info("Authentication Manager cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Authentication Manager cleanup: {e}")
    
    async def authenticate_request(self, request: Request) -> UserInfo:
        """Authenticate incoming request"""
        try:
            # Try JWT token first
            user_info = await self._authenticate_jwt(request)
            if user_info:
                # Store user info in request state
                request.state.user_id = user_info.user_id
                request.state.user_role = user_info.role
                request.state.user_permissions = user_info.permissions
                return user_info
            
            # Try API key authentication
            user_info = await self._authenticate_api_key(request)
            if user_info:
                # Store user info in request state
                request.state.user_id = user_info.user_id
                request.state.user_role = user_info.role
                request.state.user_permissions = user_info.permissions
                return user_info
            
            # No valid authentication found
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )
    
    async def _authenticate_jwt(self, request: Request) -> Optional[UserInfo]:
        """Authenticate using JWT token"""
        try:
            # Get token from Authorization header
            authorization = request.headers.get("Authorization")
            if not authorization or not authorization.startswith("Bearer "):
                return None
            
            token = authorization.split("Bearer ")[1]
            
            # Decode JWT token
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            # Extract user information
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            email = payload.get("email")
            role = payload.get("role", "user")
            permissions = payload.get("permissions", [])
            
            # Add role-based permissions
            role_perms = self.role_permissions.get(role, [])
            all_permissions = list(set(permissions + role_perms))
            
            # Check token expiration
            exp = payload.get("exp")
            expires_at = datetime.fromtimestamp(exp) if exp else None
            
            return UserInfo(
                user_id=user_id,
                email=email,
                role=role,
                permissions=all_permissions,
                expires_at=expires_at
            )
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {e}")
            return None
        except Exception as e:
            logger.error(f"JWT authentication error: {e}")
            return None
    
    async def _authenticate_api_key(self, request: Request) -> Optional[UserInfo]:
        """Authenticate using API key"""
        try:
            # Get API key from header
            api_key = request.headers.get("X-API-Key")
            if not api_key:
                return None
            
            # Validate API key
            key_info = self.api_keys.get(api_key)
            if not key_info:
                return None
            
            # Check if key is active
            if not key_info.get("active", True):
                return None
            
            # Check expiration
            expires_at = key_info.get("expires_at")
            if expires_at and datetime.now() > expires_at:
                return None
            
            # Extract user information
            user_id = key_info.get("user_id", f"api_key:{api_key[:8]}")
            role = key_info.get("role", "user")
            permissions = key_info.get("permissions", [])
            
            # Add role-based permissions
            role_perms = self.role_permissions.get(role, [])
            all_permissions = list(set(permissions + role_perms))
            
            return UserInfo(
                user_id=user_id,
                role=role,
                permissions=all_permissions,
                expires_at=expires_at
            )
            
        except Exception as e:
            logger.error(f"API key authentication error: {e}")
            return None
    
    async def authorize_service_access(
        self,
        user_info: UserInfo,
        service_name: str,
        action: str = "read"
    ) -> bool:
        """Authorize user access to specific service"""
        try:
            # Admin can access everything
            if user_info.role == "admin":
                return True
            
            # Check if user has general permission for the action
            general_permissions = [f"{action}:all", f"{action}:own"]
            if any(perm in user_info.permissions for perm in general_permissions):
                return True
            
            # Check service-specific permissions
            required_permissions = self.service_permissions.get(service_name, [])
            for required_perm in required_permissions:
                if required_perm.startswith(action) and required_perm in user_info.permissions:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Authorization error for {service_name}: {e}")
            return False
    
    async def create_jwt_token(
        self,
        user_id: str,
        email: Optional[str] = None,
        role: str = "user",
        permissions: Optional[List[str]] = None,
        expires_in: int = 3600
    ) -> str:
        """Create JWT token for user"""
        try:
            now = datetime.utcnow()
            expires_at = now + timedelta(seconds=expires_in)
            
            payload = {
                "sub": user_id,
                "email": email,
                "role": role,
                "permissions": permissions or [],
                "iat": now,
                "exp": expires_at
            }
            
            token = jwt.encode(
                payload,
                self.secret_key,
                algorithm=self.algorithm
            )
            
            return token
            
        except Exception as e:
            logger.error(f"Error creating JWT token: {e}")
            raise ValueError(f"Failed to create JWT token: {e}")
    
    async def create_api_key(
        self,
        user_id: str,
        role: str = "user",
        permissions: Optional[List[str]] = None,
        expires_in: Optional[int] = None,
        description: Optional[str] = None
    ) -> str:
        """Create API key for user"""
        try:
            import secrets
            
            # Generate secure API key
            api_key = f"pk_{secrets.token_urlsafe(32)}"
            
            expires_at = None
            if expires_in:
                expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            key_info = {
                "user_id": user_id,
                "role": role,
                "permissions": permissions or [],
                "created_at": datetime.now(),
                "expires_at": expires_at,
                "active": True,
                "description": description
            }
            
            self.api_keys[api_key] = key_info
            
            logger.info(f"Created API key for user {user_id}: {api_key[:10]}...")
            return api_key
            
        except Exception as e:
            logger.error(f"Error creating API key: {e}")
            raise ValueError(f"Failed to create API key: {e}")
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """Revoke API key"""
        try:
            if api_key in self.api_keys:
                self.api_keys[api_key]["active"] = False
                logger.info(f"Revoked API key: {api_key[:10]}...")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error revoking API key: {e}")
            return False
    
    async def _load_default_api_keys(self):
        """Load default API keys for testing"""
        # Admin API key
        admin_key = "pk_admin_gateway_key_12345678901234567890"
        self.api_keys[admin_key] = {
            "user_id": "admin",
            "role": "admin",
            "permissions": [],
            "created_at": datetime.now(),
            "expires_at": None,
            "active": True,
            "description": "Default admin API key"
        }
        
        # Manager API key
        manager_key = "pk_manager_gateway_key_12345678901234567890"
        self.api_keys[manager_key] = {
            "user_id": "manager",
            "role": "manager",
            "permissions": [],
            "created_at": datetime.now(),
            "expires_at": None,
            "active": True,
            "description": "Default manager API key"
        }
        
        # User API key
        user_key = "pk_user_gateway_key_12345678901234567890"
        self.api_keys[user_key] = {
            "user_id": "user_123",
            "role": "user",
            "permissions": [],
            "created_at": datetime.now(),
            "expires_at": None,
            "active": True,
            "description": "Default user API key"
        }
        
        logger.info("Loaded default API keys")
    
    def get_api_keys_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all API keys (without the actual keys)"""
        return {
            key[:10] + "...": {
                "user_id": info["user_id"],
                "role": info["role"],
                "created_at": info["created_at"].isoformat(),
                "expires_at": info["expires_at"].isoformat() if info["expires_at"] else None,
                "active": info["active"],
                "description": info["description"]
            }
            for key, info in self.api_keys.items()
        }
    
    def get_role_permissions(self) -> Dict[str, List[str]]:
        """Get all role permissions"""
        return self.role_permissions.copy()
    
    def get_service_permissions(self) -> Dict[str, List[str]]:
        """Get all service permissions"""
        return self.service_permissions.copy()
    
    async def update_role_permissions(self, role: str, permissions: List[str]) -> bool:
        """Update permissions for a role"""
        try:
            self.role_permissions[role] = permissions
            logger.info(f"Updated permissions for role {role}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating role permissions: {e}")
            return False
    
    async def add_service_permissions(self, service_name: str, permissions: List[str]) -> bool:
        """Add permissions for a service"""
        try:
            self.service_permissions[service_name] = permissions
            logger.info(f"Added permissions for service {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding service permissions: {e}")
            return False