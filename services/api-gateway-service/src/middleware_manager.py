"""
API Gateway Middleware Manager

Manages request/response transformation middleware for the API Gateway
including logging, security headers, request/response modification.
"""

import asyncio
import gzip
import json
import logging
import time
import uuid
from typing import Any, Callable, Dict, List, Optional

from fastapi import Request, Response
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)


class Middleware:
    """Base middleware class"""
    
    def __init__(self, name: str, priority: int = 100):
        self.name = name
        self.priority = priority
        self.enabled = True
    
    async def process_request(self, request: Request) -> Optional[Request]:
        """Process incoming request"""
        return request
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Process outgoing response"""
        return response


class LoggingMiddleware(Middleware):
    """Request/response logging middleware"""
    
    def __init__(self):
        super().__init__("logging", priority=10)
        self.log_body = False  # Set to True to log request/response bodies
    
    async def process_request(self, request: Request) -> Optional[Request]:
        """Log incoming request"""
        try:
            request_id = getattr(request.state, 'request_id', str(uuid.uuid4()))
            
            log_data = {
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "headers": dict(request.headers),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown"),
                "timestamp": time.time()
            }
            
            # Log request body if enabled and present
            if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        # Try to parse as JSON for better logging
                        try:
                            log_data["body"] = json.loads(body.decode())
                        except:
                            log_data["body"] = body.decode()[:1000]  # Truncate large bodies
                except:
                    pass
            
            logger.info(f"Incoming request: {json.dumps(log_data, default=str)}")
            
            # Store start time for response logging
            request.state.start_time = time.time()
            request.state.request_id = request_id
            
        except Exception as e:
            logger.error(f"Error in logging middleware (request): {e}")
        
        return request
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Log outgoing response"""
        try:
            request_id = getattr(response, 'request_id', 'unknown')
            start_time = getattr(response, 'start_time', time.time())
            duration = time.time() - start_time
            
            log_data = {
                "request_id": request_id,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "duration_ms": round(duration * 1000, 2),
                "timestamp": time.time()
            }
            
            # Log response body if enabled
            if self.log_body and hasattr(response, 'body'):
                try:
                    if len(response.body) < 1000:  # Only log small responses
                        log_data["body"] = response.body.decode()
                except:
                    pass
            
            logger.info(f"Outgoing response: {json.dumps(log_data, default=str)}")
            
        except Exception as e:
            logger.error(f"Error in logging middleware (response): {e}")
        
        return response


class SecurityHeadersMiddleware(Middleware):
    """Security headers middleware"""
    
    def __init__(self):
        super().__init__("security_headers", priority=20)
        
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Add security headers to response"""
        try:
            for header, value in self.security_headers.items():
                response.headers[header] = value
            
            # Add custom gateway headers
            response.headers["X-Gateway-Version"] = "1.0.0"
            response.headers["X-Powered-By"] = "API-Gateway"
            
        except Exception as e:
            logger.error(f"Error in security headers middleware: {e}")
        
        return response


class CompressionMiddleware(Middleware):
    """Response compression middleware"""
    
    def __init__(self):
        super().__init__("compression", priority=90)
        self.min_size = 1024  # Minimum response size to compress
        self.compression_level = 6
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Compress response if applicable"""
        try:
            # Check if compression is requested
            accept_encoding = response.headers.get("accept-encoding", "")
            if "gzip" not in accept_encoding.lower():
                return response
            
            # Check response size
            if hasattr(response, 'body') and len(response.body) < self.min_size:
                return response
            
            # Check content type
            content_type = response.headers.get("content-type", "")
            compressible_types = [
                "application/json",
                "application/xml",
                "text/html",
                "text/plain",
                "text/css",
                "text/javascript",
                "application/javascript"
            ]
            
            if not any(ct in content_type for ct in compressible_types):
                return response
            
            # Compress response body
            if hasattr(response, 'body'):
                compressed_body = gzip.compress(
                    response.body,
                    compresslevel=self.compression_level
                )
                
                response.body = compressed_body
                response.headers["Content-Encoding"] = "gzip"
                response.headers["Content-Length"] = str(len(compressed_body))
            
        except Exception as e:
            logger.error(f"Error in compression middleware: {e}")
        
        return response


class RequestIdMiddleware(Middleware):
    """Request ID tracking middleware"""
    
    def __init__(self):
        super().__init__("request_id", priority=5)
    
    async def process_request(self, request: Request) -> Optional[Request]:
        """Add request ID to request"""
        try:
            # Get existing request ID or create new one
            request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
            request.state.request_id = request_id
            
        except Exception as e:
            logger.error(f"Error in request ID middleware: {e}")
        
        return request
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Add request ID to response"""
        try:
            if hasattr(response, 'request_id'):
                response.headers["X-Request-ID"] = response.request_id
            
        except Exception as e:
            logger.error(f"Error in request ID middleware (response): {e}")
        
        return response


class CorsMiddleware(Middleware):
    """CORS handling middleware"""
    
    def __init__(self):
        super().__init__("cors", priority=15)
        
        self.allowed_origins = ["*"]  # Configure for production
        self.allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
        self.allowed_headers = ["*"]
        self.allow_credentials = True
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Add CORS headers to response"""
        try:
            response.headers["Access-Control-Allow-Origin"] = ", ".join(self.allowed_origins)
            response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allowed_methods)
            response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allowed_headers)
            
            if self.allow_credentials:
                response.headers["Access-Control-Allow-Credentials"] = "true"
            
        except Exception as e:
            logger.error(f"Error in CORS middleware: {e}")
        
        return response


class RequestTransformationMiddleware(Middleware):
    """Request transformation middleware"""
    
    def __init__(self):
        super().__init__("request_transformation", priority=50)
    
    async def process_request(self, request: Request) -> Optional[Request]:
        """Transform incoming request"""
        try:
            # Add standard headers for downstream services
            request.headers.__dict__.setdefault("X-Forwarded-By", "api-gateway")
            request.headers.__dict__.setdefault("X-Gateway-Version", "1.0.0")
            request.headers.__dict__.setdefault("X-Request-Time", str(time.time()))
            
            # Add user context if available
            if hasattr(request.state, 'user_id'):
                request.headers.__dict__.setdefault("X-User-ID", request.state.user_id)
                request.headers.__dict__.setdefault("X-User-Role", getattr(request.state, 'user_role', 'unknown'))
            
        except Exception as e:
            logger.error(f"Error in request transformation middleware: {e}")
        
        return request


class ResponseTransformationMiddleware(Middleware):
    """Response transformation middleware"""
    
    def __init__(self):
        super().__init__("response_transformation", priority=80)
    
    async def process_response(self, response: Response) -> Optional[Response]:
        """Transform outgoing response"""
        try:
            # Add standard response headers
            response.headers["X-Response-Time"] = str(time.time())
            
            # Add performance headers if available
            if hasattr(response, 'start_time'):
                duration = time.time() - response.start_time
                response.headers["X-Response-Duration"] = f"{duration:.3f}s"
            
        except Exception as e:
            logger.error(f"Error in response transformation middleware: {e}")
        
        return response


class MiddlewareManager:
    """Manager for request/response middleware"""
    
    def __init__(self):
        self.middlewares: List[Middleware] = []
        self.is_ready = False
    
    async def initialize(self):
        """Initialize middleware manager"""
        try:
            # Register default middlewares
            self.middlewares = [
                RequestIdMiddleware(),
                LoggingMiddleware(),
                CorsMiddleware(),
                SecurityHeadersMiddleware(),
                RequestTransformationMiddleware(),
                ResponseTransformationMiddleware(),
                CompressionMiddleware()
            ]
            
            # Sort by priority
            self.middlewares.sort(key=lambda m: m.priority)
            
            self.is_ready = True
            logger.info(f"Middleware Manager initialized with {len(self.middlewares)} middlewares")
            
        except Exception as e:
            logger.error(f"Failed to initialize Middleware Manager: {e}")
            self.is_ready = False
            raise
    
    async def cleanup(self):
        """Cleanup middleware manager"""
        try:
            self.middlewares.clear()
            self.is_ready = False
            logger.info("Middleware Manager cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Middleware Manager cleanup: {e}")
    
    async def apply_pre_routing_middleware(self, request: Request) -> Request:
        """Apply middleware before routing"""
        try:
            for middleware in self.middlewares:
                if middleware.enabled:
                    processed_request = await middleware.process_request(request)
                    if processed_request:
                        request = processed_request
            
            return request
            
        except Exception as e:
            logger.error(f"Error applying pre-routing middleware: {e}")
            return request
    
    async def apply_post_routing_middleware(self, response: Response) -> Response:
        """Apply middleware after routing"""
        try:
            # Apply middlewares in reverse order for response processing
            for middleware in reversed(self.middlewares):
                if middleware.enabled:
                    processed_response = await middleware.process_response(response)
                    if processed_response:
                        response = processed_response
            
            return response
            
        except Exception as e:
            logger.error(f"Error applying post-routing middleware: {e}")
            return response
    
    def add_middleware(self, middleware: Middleware):
        """Add custom middleware"""
        self.middlewares.append(middleware)
        self.middlewares.sort(key=lambda m: m.priority)
        logger.info(f"Added middleware: {middleware.name}")
    
    def remove_middleware(self, name: str) -> bool:
        """Remove middleware by name"""
        for i, middleware in enumerate(self.middlewares):
            if middleware.name == name:
                del self.middlewares[i]
                logger.info(f"Removed middleware: {name}")
                return True
        return False
    
    def enable_middleware(self, name: str) -> bool:
        """Enable middleware by name"""
        for middleware in self.middlewares:
            if middleware.name == name:
                middleware.enabled = True
                logger.info(f"Enabled middleware: {name}")
                return True
        return False
    
    def disable_middleware(self, name: str) -> bool:
        """Disable middleware by name"""
        for middleware in self.middlewares:
            if middleware.name == name:
                middleware.enabled = False
                logger.info(f"Disabled middleware: {name}")
                return True
        return False
    
    def get_middleware_info(self) -> List[Dict[str, Any]]:
        """Get information about all middlewares"""
        return [
            {
                "name": m.name,
                "priority": m.priority,
                "enabled": m.enabled,
                "class": m.__class__.__name__
            }
            for m in self.middlewares
        ]
    
    def get_enabled_middlewares(self) -> List[str]:
        """Get list of enabled middleware names"""
        return [m.name for m in self.middlewares if m.enabled]
    
    def get_disabled_middlewares(self) -> List[str]:
        """Get list of disabled middleware names"""
        return [m.name for m in self.middlewares if not m.enabled]