"""
API Gateway Rate Limiter

Rate limiting functionality for the API Gateway using Redis for distributed
rate limiting across multiple gateway instances.
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional

import redis.asyncio as redis
from fastapi import HTTPException, Request, status

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter for controlling request rates to services"""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.is_connected = False
        
        # Default rate limits (requests per minute)
        self.default_limits = {
            "global": 1000,      # Global rate limit per IP
            "per_service": 100,  # Per service rate limit per IP
            "authenticated": 500, # Higher limit for authenticated users
            "admin": 10000       # Very high limit for admin users
        }
        
        # Rate limit windows (in seconds)
        self.windows = {
            "minute": 60,
            "hour": 3600,
            "day": 86400
        }
        
        # Rate limit hit counter
        self.limit_hits = 0
        
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self.is_connected = True
            logger.info("Rate Limiter initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Rate Limiter: {e}")
            self.is_connected = False
            raise
    
    async def cleanup(self):
        """Cleanup Redis connection"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            self.is_connected = False
            logger.info("Rate Limiter cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Rate Limiter cleanup: {e}")
    
    async def check_rate_limit(self, request: Request, service_name: Optional[str] = None) -> bool:
        """Check if request is within rate limits"""
        try:
            if not self.is_connected:
                logger.warning("Rate limiter not connected, allowing request")
                return True
            
            # Extract client identifier
            client_id = self._get_client_id(request)
            
            # Get user role for rate limit determination
            user_role = await self._get_user_role(request)
            
            # Check multiple rate limit tiers
            await self._check_global_rate_limit(client_id, user_role)
            
            if service_name:
                await self._check_service_rate_limit(client_id, service_name, user_role)
            
            return True
            
        except HTTPException:
            self.limit_hits += 1
            raise
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            # Fail open - allow request if rate limiter fails
            return True
    
    async def _check_global_rate_limit(self, client_id: str, user_role: str):
        """Check global rate limit for client"""
        limit = self._get_rate_limit(user_role, "global")
        key = f"rate_limit:global:{client_id}"
        
        current_count = await self._increment_counter(key, self.windows["minute"])
        
        if current_count > limit:
            logger.warning(f"Global rate limit exceeded for {client_id}: {current_count}/{limit}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "limit": limit,
                    "window": "1 minute",
                    "retry_after": await self._get_retry_after(key)
                }
            )
    
    async def _check_service_rate_limit(self, client_id: str, service_name: str, user_role: str):
        """Check service-specific rate limit for client"""
        limit = self._get_rate_limit(user_role, "per_service")
        key = f"rate_limit:service:{service_name}:{client_id}"
        
        current_count = await self._increment_counter(key, self.windows["minute"])
        
        if current_count > limit:
            logger.warning(f"Service rate limit exceeded for {client_id} on {service_name}: {current_count}/{limit}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Service rate limit exceeded",
                    "service": service_name,
                    "limit": limit,
                    "window": "1 minute",
                    "retry_after": await self._get_retry_after(key)
                }
            )
    
    async def _increment_counter(self, key: str, window: int) -> int:
        """Increment rate limit counter using sliding window"""
        try:
            pipeline = self.redis_client.pipeline()
            
            # Use sliding window log approach
            current_time = time.time()
            window_start = current_time - window
            
            # Remove old entries
            pipeline.zremrangebyscore(key, 0, window_start)
            
            # Add current request
            pipeline.zadd(key, {str(current_time): current_time})
            
            # Count current requests in window
            pipeline.zcard(key)
            
            # Set expiration
            pipeline.expire(key, window)
            
            results = await pipeline.execute()
            
            return results[2]  # Count result
            
        except Exception as e:
            logger.error(f"Error incrementing counter for {key}: {e}")
            return 0
    
    async def _get_retry_after(self, key: str) -> int:
        """Get retry-after time in seconds"""
        try:
            # Get oldest entry in current window
            oldest_entries = await self.redis_client.zrange(key, 0, 0, withscores=True)
            
            if oldest_entries:
                oldest_time = oldest_entries[0][1]
                current_time = time.time()
                retry_after = int(self.windows["minute"] - (current_time - oldest_time))
                return max(1, retry_after)
            
            return 60  # Default retry after 1 minute
            
        except Exception as e:
            logger.error(f"Error calculating retry-after for {key}: {e}")
            return 60
    
    def _get_client_id(self, request: Request) -> str:
        """Extract client identifier from request"""
        # Check for authenticated user first
        if hasattr(request.state, 'user_id'):
            return f"user:{request.state.user_id}"
        
        # Check for API key
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key[:10]}"
        
        # Fallback to IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Get first IP in X-Forwarded-For chain
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"
        
        return f"ip:{client_ip}"
    
    async def _get_user_role(self, request: Request) -> str:
        """Get user role for rate limit determination"""
        # Check if user is authenticated and get role
        if hasattr(request.state, 'user_role'):
            return request.state.user_role
        
        # Check for admin API key
        api_key = request.headers.get("X-API-Key")
        if api_key and await self._is_admin_api_key(api_key):
            return "admin"
        
        # Check for authenticated user
        if hasattr(request.state, 'user_id'):
            return "authenticated"
        
        return "anonymous"
    
    async def _is_admin_api_key(self, api_key: str) -> bool:
        """Check if API key belongs to admin user"""
        try:
            # Check Redis cache for admin API keys
            is_admin = await self.redis_client.sismember("admin_api_keys", api_key)
            return bool(is_admin)
            
        except Exception as e:
            logger.error(f"Error checking admin API key: {e}")
            return False
    
    def _get_rate_limit(self, user_role: str, limit_type: str) -> int:
        """Get rate limit based on user role and limit type"""
        role_multipliers = {
            "admin": 10.0,
            "authenticated": 2.0,
            "anonymous": 1.0
        }
        
        base_limit = self.default_limits.get(limit_type, self.default_limits["global"])
        multiplier = role_multipliers.get(user_role, 1.0)
        
        return int(base_limit * multiplier)
    
    async def add_admin_api_key(self, api_key: str) -> bool:
        """Add API key to admin list"""
        try:
            if not self.is_connected:
                return False
            
            await self.redis_client.sadd("admin_api_keys", api_key)
            logger.info(f"Added admin API key: {api_key[:10]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error adding admin API key: {e}")
            return False
    
    async def remove_admin_api_key(self, api_key: str) -> bool:
        """Remove API key from admin list"""
        try:
            if not self.is_connected:
                return False
            
            await self.redis_client.srem("admin_api_keys", api_key)
            logger.info(f"Removed admin API key: {api_key[:10]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error removing admin API key: {e}")
            return False
    
    async def get_client_stats(self, client_id: str) -> Dict[str, Any]:
        """Get rate limit statistics for a client"""
        try:
            stats = {}
            
            # Global stats
            global_key = f"rate_limit:global:{client_id}"
            global_count = await self.redis_client.zcard(global_key)
            stats["global_requests"] = global_count
            
            # Service-specific stats
            pattern = f"rate_limit:service:*:{client_id}"
            service_keys = await self.redis_client.keys(pattern)
            
            service_stats = {}
            for key in service_keys:
                service_name = key.split(":")[2]
                count = await self.redis_client.zcard(key)
                service_stats[service_name] = count
            
            stats["service_requests"] = service_stats
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting client stats for {client_id}: {e}")
            return {}
    
    async def reset_client_limits(self, client_id: str) -> bool:
        """Reset rate limits for a specific client"""
        try:
            if not self.is_connected:
                return False
            
            # Find all keys for this client
            patterns = [
                f"rate_limit:global:{client_id}",
                f"rate_limit:service:*:{client_id}"
            ]
            
            keys_to_delete = []
            for pattern in patterns:
                keys = await self.redis_client.keys(pattern)
                keys_to_delete.extend(keys)
            
            if keys_to_delete:
                await self.redis_client.delete(*keys_to_delete)
                logger.info(f"Reset rate limits for {client_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error resetting limits for {client_id}: {e}")
            return False
    
    def get_limit_hits(self) -> int:
        """Get total number of rate limit hits"""
        return self.limit_hits
    
    def get_configuration(self) -> Dict[str, Any]:
        """Get current rate limiter configuration"""
        return {
            "redis_url": self.redis_url,
            "is_connected": self.is_connected,
            "default_limits": self.default_limits,
            "windows": self.windows,
            "total_limit_hits": self.limit_hits
        }
    
    async def update_rate_limits(self, new_limits: Dict[str, int]) -> bool:
        """Update rate limit configuration"""
        try:
            for limit_type, value in new_limits.items():
                if limit_type in self.default_limits:
                    self.default_limits[limit_type] = value
                    logger.info(f"Updated {limit_type} rate limit to {value}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating rate limits: {e}")
            return False