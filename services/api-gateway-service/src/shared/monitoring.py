"""
Shared monitoring utilities for API Gateway Service
"""

import logging
import time
from typing import Any, Dict

logger = logging.getLogger(__name__)

# Global metrics storage (in production, use proper metrics system)
metrics_store = {
    "requests_total": 0,
    "errors_total": 0,
    "response_times": [],
    "start_time": time.time()
}


def setup_monitoring(app, service_name: str):
    """Setup monitoring for the service"""
    logger.info(f"Monitoring setup for {service_name}")
    
    @app.middleware("http")
    async def metrics_middleware(request, call_next):
        start_time = time.time()
        
        # Increment request counter
        metrics_store["requests_total"] += 1
        
        try:
            response = await call_next(request)
            
            # Record response time
            response_time = time.time() - start_time
            metrics_store["response_times"].append(response_time)
            
            # Keep only last 1000 response times
            if len(metrics_store["response_times"]) > 1000:
                metrics_store["response_times"] = metrics_store["response_times"][-1000:]
            
            # Count errors
            if response.status_code >= 400:
                metrics_store["errors_total"] += 1
            
            return response
            
        except Exception as e:
            # Count exceptions as errors
            metrics_store["errors_total"] += 1
            raise


def get_metrics() -> Dict[str, Any]:
    """Get current metrics"""
    uptime = time.time() - metrics_store["start_time"]
    
    response_times = metrics_store["response_times"]
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0
    
    error_rate = 0
    if metrics_store["requests_total"] > 0:
        error_rate = (metrics_store["errors_total"] / metrics_store["requests_total"]) * 100
    
    return {
        "uptime": uptime,
        "requests_total": metrics_store["requests_total"],
        "errors_total": metrics_store["errors_total"],
        "error_rate": error_rate,
        "avg_response_time": avg_response_time,
        "active_connections": 0  # Placeholder
    }