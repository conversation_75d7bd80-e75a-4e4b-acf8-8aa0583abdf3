"""
API Gateway Service - Main Application

Provides centralized routing, security, rate limiting, load balancing, and 
request/response transformation for all microservices in the Publish AI platform.
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from fastapi import FastAP<PERSON>, HTTPException, Request, Response, status, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address

from gateway_router import GatewayRouter
from service_registry import ServiceRegistry
from load_balancer import LoadBalancer
from rate_limiter import RateLimiter
from circuit_breaker import CircuitBreakerManager
from auth_manager import AuthManager
from middleware_manager import MiddlewareManager
from shared.monitoring import setup_monitoring, get_metrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="API Gateway Service",
    description="Centralized API gateway for microservices routing and management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Rate limiting setup
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Global service instances
gateway_router: Optional[GatewayRouter] = None
service_registry: Optional[ServiceRegistry] = None
load_balancer: Optional[LoadBalancer] = None
rate_limiter: Optional[RateLimiter] = None
circuit_breaker_manager: Optional[CircuitBreakerManager] = None
auth_manager: Optional[AuthManager] = None
middleware_manager: Optional[MiddlewareManager] = None

# Setup monitoring
setup_monitoring(app, service_name="api-gateway")

# =============================================================================
# Request/Response Models
# =============================================================================

class GatewayResponse(BaseModel):
    """Standard gateway response format"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    request_id: Optional[str] = None
    timestamp: Optional[str] = None
    execution_time: Optional[float] = None

class ServiceHealth(BaseModel):
    """Service health status"""
    service_name: str
    status: str
    endpoint: str
    response_time: Optional[float] = None
    last_check: str

class GatewayStatus(BaseModel):
    """Gateway status information"""
    gateway_status: str
    total_services: int
    healthy_services: int
    request_count: int
    error_rate: float
    average_response_time: float
    timestamp: str

# =============================================================================
# Startup/Shutdown Events
# =============================================================================

@app.on_event("startup")
async def startup_event():
    """Initialize gateway components on startup"""
    global gateway_router, service_registry, load_balancer, rate_limiter
    global circuit_breaker_manager, auth_manager, middleware_manager
    
    try:
        logger.info("Starting API Gateway Service...")
        
        # Initialize service registry
        service_registry = ServiceRegistry(
            discovery_url=os.getenv("SERVICE_DISCOVERY_URL", "http://localhost:8070")
        )
        await service_registry.initialize()
        
        # Initialize load balancer
        load_balancer = LoadBalancer(service_registry)
        await load_balancer.initialize()
        
        # Initialize rate limiter
        rate_limiter = RateLimiter(
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379")
        )
        await rate_limiter.initialize()
        
        # Initialize circuit breaker manager
        circuit_breaker_manager = CircuitBreakerManager()
        await circuit_breaker_manager.initialize()
        
        # Initialize authentication manager
        auth_manager = AuthManager(
            secret_key=os.getenv("JWT_SECRET_KEY", "your-secret-key"),
            algorithm="HS256"
        )
        await auth_manager.initialize()
        
        # Initialize middleware manager
        middleware_manager = MiddlewareManager()
        await middleware_manager.initialize()
        
        # Initialize gateway router
        gateway_router = GatewayRouter(
            service_registry=service_registry,
            load_balancer=load_balancer,
            rate_limiter=rate_limiter,
            circuit_breaker_manager=circuit_breaker_manager,
            auth_manager=auth_manager,
            middleware_manager=middleware_manager
        )
        await gateway_router.initialize()
        
        logger.info("API Gateway Service started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start API Gateway Service: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup gateway components on shutdown"""
    global gateway_router, service_registry, load_balancer, rate_limiter
    global circuit_breaker_manager, auth_manager, middleware_manager
    
    try:
        logger.info("Shutting down API Gateway Service...")
        
        if gateway_router:
            await gateway_router.cleanup()
        
        if middleware_manager:
            await middleware_manager.cleanup()
        
        if auth_manager:
            await auth_manager.cleanup()
        
        if circuit_breaker_manager:
            await circuit_breaker_manager.cleanup()
        
        if rate_limiter:
            await rate_limiter.cleanup()
        
        if load_balancer:
            await load_balancer.cleanup()
        
        if service_registry:
            await service_registry.cleanup()
        
        logger.info("API Gateway Service shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# =============================================================================
# Request Processing Middleware
# =============================================================================

@app.middleware("http")
async def request_processing_middleware(request: Request, call_next):
    """Process all incoming requests through the gateway"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # Add request ID to headers
    request.state.request_id = request_id
    
    # Skip gateway processing for admin endpoints
    if request.url.path.startswith(("/health", "/ready", "/status", "/docs", "/redoc", "/openapi.json")):
        response = await call_next(request)
        return response
    
    try:
        if not gateway_router:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gateway not ready"
            )
        
        # Process request through gateway router
        response = await gateway_router.route_request(request)
        
        # Add response headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Gateway-Time"] = str(time.time() - start_time)
        
        return response
        
    except HTTPException as e:
        # Handle HTTP exceptions
        return JSONResponse(
            status_code=e.status_code,
            content={
                "success": False,
                "message": e.detail,
                "request_id": request_id,
                "timestamp": datetime.now().isoformat()
            }
        )
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Gateway error for request {request_id}: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": "Internal gateway error",
                "request_id": request_id,
                "timestamp": datetime.now().isoformat()
            }
        )

# =============================================================================
# Gateway Management Endpoints
# =============================================================================

@app.get("/health")
async def health_check():
    """Gateway health check endpoint"""
    try:
        health_status = {
            "service": "api-gateway",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "gateway_router": gateway_router.is_ready if gateway_router else False,
                "service_registry": service_registry.is_connected if service_registry else False,
                "load_balancer": load_balancer.is_ready if load_balancer else False,
                "rate_limiter": rate_limiter.is_connected if rate_limiter else False,
                "circuit_breaker": circuit_breaker_manager.is_ready if circuit_breaker_manager else False,
                "auth_manager": auth_manager.is_ready if auth_manager else False
            }
        }
        
        # Check if all critical components are ready
        critical_components = ["gateway_router", "service_registry", "load_balancer"]
        all_ready = all(health_status["components"].get(comp, False) for comp in critical_components)
        
        if not all_ready:
            health_status["status"] = "degraded"
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content=health_status
            )
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "service": "api-gateway",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/ready")
async def readiness_check():
    """Gateway readiness check endpoint"""
    try:
        if not gateway_router or not gateway_router.is_ready:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gateway router not ready"
            )
        
        if not service_registry or not service_registry.is_connected:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service registry not connected"
            )
        
        return {
            "service": "api-gateway",
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Gateway not ready: {str(e)}"
        )

@app.get("/status", response_model=GatewayStatus)
async def get_gateway_status():
    """Get detailed gateway status and metrics"""
    try:
        metrics = get_metrics()
        
        # Get service registry information
        total_services = 0
        healthy_services = 0
        
        if service_registry:
            services = await service_registry.get_all_services()
            total_services = len(services)
            
            # Check health of each service
            for service in services:
                try:
                    health_check = await service_registry.check_service_health(service["name"])
                    if health_check.get("status") == "healthy":
                        healthy_services += 1
                except:
                    pass  # Service unhealthy
        
        status_info = GatewayStatus(
            gateway_status="healthy" if gateway_router and gateway_router.is_ready else "unhealthy",
            total_services=total_services,
            healthy_services=healthy_services,
            request_count=metrics.get("requests_total", 0),
            error_rate=metrics.get("error_rate", 0),
            average_response_time=metrics.get("avg_response_time", 0),
            timestamp=datetime.now().isoformat()
        )
        
        return status_info
        
    except Exception as e:
        logger.error(f"Failed to get gateway status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get status: {str(e)}"
        )

@app.get("/services", response_model=List[ServiceHealth])
async def get_services_health():
    """Get health status of all registered services"""
    try:
        if not service_registry:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service registry not available"
            )
        
        services = await service_registry.get_all_services()
        health_results = []
        
        for service in services:
            try:
                start_time = time.time()
                health_check = await service_registry.check_service_health(service["name"])
                response_time = time.time() - start_time
                
                health_results.append(ServiceHealth(
                    service_name=service["name"],
                    status=health_check.get("status", "unknown"),
                    endpoint=service.get("endpoint", "unknown"),
                    response_time=response_time,
                    last_check=datetime.now().isoformat()
                ))
                
            except Exception as e:
                health_results.append(ServiceHealth(
                    service_name=service["name"],
                    status="unhealthy",
                    endpoint=service.get("endpoint", "unknown"),
                    last_check=datetime.now().isoformat()
                ))
        
        return health_results
        
    except Exception as e:
        logger.error(f"Failed to get services health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get services health: {str(e)}"
        )

@app.post("/services/{service_name}/reload")
@limiter.limit("5/minute")
async def reload_service(service_name: str, request: Request):
    """Reload a specific service configuration"""
    try:
        if not service_registry:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service registry not available"
            )
        
        success = await service_registry.reload_service(service_name)
        
        if success:
            return GatewayResponse(
                success=True,
                message=f"Service {service_name} reloaded successfully",
                timestamp=datetime.now().isoformat()
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service {service_name} not found or reload failed"
            )
        
    except Exception as e:
        logger.error(f"Failed to reload service {service_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reload service: {str(e)}"
        )

@app.get("/metrics")
async def get_gateway_metrics():
    """Get gateway performance metrics"""
    try:
        metrics = get_metrics()
        
        # Add gateway-specific metrics
        gateway_metrics = {
            "gateway_version": "1.0.0",
            "uptime": metrics.get("uptime", 0),
            "total_requests": metrics.get("requests_total", 0),
            "error_rate": metrics.get("error_rate", 0),
            "average_response_time": metrics.get("avg_response_time", 0),
            "active_connections": metrics.get("active_connections", 0),
            "circuit_breaker_trips": circuit_breaker_manager.get_trip_count() if circuit_breaker_manager else 0,
            "rate_limit_hits": rate_limiter.get_limit_hits() if rate_limiter else 0
        }
        
        return gateway_metrics
        
    except Exception as e:
        logger.error(f"Failed to get gateway metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )

# =============================================================================
# Catch-all route for service proxying
# =============================================================================

@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"])
async def proxy_request(path: str, request: Request):
    """Catch-all route that proxies requests to appropriate microservices"""
    # This is handled by the middleware, but we need this route to exist
    # The actual routing logic is in the gateway_router
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            "success": False,
            "message": "Service not found or not available",
            "path": path,
            "timestamp": datetime.now().isoformat()
        }
    )

# =============================================================================
# Application Entry Point
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("SERVICE_PORT", "8080"))
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )