"""
API Gateway Circuit Breaker

Circuit breaker pattern implementation for the API Gateway to handle
service failures gracefully and prevent cascade failures.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Any, Callable, Dict, Optional

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"       # Normal operation
    OPEN = "open"          # Blocking requests
    HALF_OPEN = "half_open" # Testing recovery


class CircuitBreaker:
    """Individual circuit breaker for a service"""
    
    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception
    ):
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.last_success_time = time.time()
        
        # Statistics
        self.total_requests = 0
        self.total_failures = 0
        self.total_timeouts = 0
        self.total_trips = 0
        
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        self.total_requests += 1
        
        # Check if circuit breaker should be opened
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info(f"Circuit breaker for {self.service_name} moved to HALF_OPEN")
            else:
                logger.warning(f"Circuit breaker for {self.service_name} is OPEN, request blocked")
                raise CircuitBreakerException(
                    f"Circuit breaker for {self.service_name} is OPEN"
                )
        
        try:
            # Execute the function
            result = await func(*args, **kwargs)
            
            # Success - reset failure count
            await self._on_success()
            return result
            
        except self.expected_exception as e:
            # Expected failure - increment failure count
            await self._on_failure()
            raise e
        except Exception as e:
            # Unexpected exception - treat as failure
            await self._on_failure()
            raise e
    
    async def _on_success(self):
        """Handle successful request"""
        self.failure_count = 0
        self.last_success_time = time.time()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.CLOSED
            logger.info(f"Circuit breaker for {self.service_name} moved to CLOSED")
    
    async def _on_failure(self):
        """Handle failed request"""
        self.failure_count += 1
        self.total_failures += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            if self.state != CircuitBreakerState.OPEN:
                self.state = CircuitBreakerState.OPEN
                self.total_trips += 1
                logger.warning(
                    f"Circuit breaker for {self.service_name} OPENED "
                    f"(failures: {self.failure_count}/{self.failure_threshold})"
                )
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        
        return (time.time() - self.last_failure_time) >= self.recovery_timeout
    
    def get_state(self) -> str:
        """Get current circuit breaker state"""
        return self.state.value
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics"""
        return {
            "service_name": self.service_name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "recovery_timeout": self.recovery_timeout,
            "last_failure_time": self.last_failure_time,
            "last_success_time": self.last_success_time,
            "total_requests": self.total_requests,
            "total_failures": self.total_failures,
            "total_timeouts": self.total_timeouts,
            "total_trips": self.total_trips,
            "success_rate": self._calculate_success_rate()
        }
    
    def _calculate_success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 100.0
        
        successful_requests = self.total_requests - self.total_failures
        return (successful_requests / self.total_requests) * 100.0
    
    def reset(self):
        """Manually reset circuit breaker"""
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        logger.info(f"Circuit breaker for {self.service_name} manually reset")


class CircuitBreakerException(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class CircuitBreakerManager:
    """Manager for multiple circuit breakers"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.is_ready = False
        
        # Default configuration
        self.default_config = {
            "failure_threshold": 5,
            "recovery_timeout": 60,
            "expected_exception": Exception
        }
        
        # Service-specific configurations
        self.service_configs = {
            "research-service": {
                "failure_threshold": 3,
                "recovery_timeout": 30
            },
            "personalization-service": {
                "failure_threshold": 3,
                "recovery_timeout": 30
            },
            "cover-designer-service": {
                "failure_threshold": 5,
                "recovery_timeout": 60
            },
            "sales-monitor-service": {
                "failure_threshold": 3,
                "recovery_timeout": 45
            },
            "market-intelligence-service": {
                "failure_threshold": 3,
                "recovery_timeout": 30
            },
            "content-generation-service": {
                "failure_threshold": 5,
                "recovery_timeout": 90
            },
            "publishing-service": {
                "failure_threshold": 3,
                "recovery_timeout": 120
            },
            "multimodal-generator-service": {
                "failure_threshold": 5,
                "recovery_timeout": 90
            }
        }
    
    async def initialize(self):
        """Initialize circuit breaker manager"""
        try:
            # Pre-create circuit breakers for known services
            for service_name in self.service_configs.keys():
                await self._create_circuit_breaker(service_name)
            
            self.is_ready = True
            logger.info("Circuit Breaker Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Circuit Breaker Manager: {e}")
            self.is_ready = False
            raise
    
    async def cleanup(self):
        """Cleanup circuit breaker manager"""
        try:
            self.circuit_breakers.clear()
            self.is_ready = False
            logger.info("Circuit Breaker Manager cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during Circuit Breaker Manager cleanup: {e}")
    
    async def execute(
        self,
        service_name: str,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """Execute function with circuit breaker protection"""
        circuit_breaker = await self._get_circuit_breaker(service_name)
        return await circuit_breaker.call(func, *args, **kwargs)
    
    async def _get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            await self._create_circuit_breaker(service_name)
        
        return self.circuit_breakers[service_name]
    
    async def _create_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Create new circuit breaker for service"""
        # Get service-specific config or use defaults
        config = self.service_configs.get(service_name, {})
        merged_config = {**self.default_config, **config}
        
        circuit_breaker = CircuitBreaker(
            service_name=service_name,
            **merged_config
        )
        
        self.circuit_breakers[service_name] = circuit_breaker
        logger.info(f"Created circuit breaker for {service_name}")
        
        return circuit_breaker
    
    def get_circuit_breaker_state(self, service_name: str) -> Optional[str]:
        """Get state of specific circuit breaker"""
        circuit_breaker = self.circuit_breakers.get(service_name)
        return circuit_breaker.get_state() if circuit_breaker else None
    
    def get_all_states(self) -> Dict[str, str]:
        """Get states of all circuit breakers"""
        return {
            service: cb.get_state()
            for service, cb in self.circuit_breakers.items()
        }
    
    def get_circuit_breaker_stats(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get statistics for specific circuit breaker"""
        circuit_breaker = self.circuit_breakers.get(service_name)
        return circuit_breaker.get_stats() if circuit_breaker else None
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all circuit breakers"""
        return {
            service: cb.get_stats()
            for service, cb in self.circuit_breakers.items()
        }
    
    def reset_circuit_breaker(self, service_name: str) -> bool:
        """Reset specific circuit breaker"""
        circuit_breaker = self.circuit_breakers.get(service_name)
        if circuit_breaker:
            circuit_breaker.reset()
            return True
        return False
    
    def reset_all_circuit_breakers(self):
        """Reset all circuit breakers"""
        for circuit_breaker in self.circuit_breakers.values():
            circuit_breaker.reset()
        logger.info("All circuit breakers reset")
    
    def get_trip_count(self) -> int:
        """Get total number of circuit breaker trips"""
        return sum(
            cb.total_trips
            for cb in self.circuit_breakers.values()
        )
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get overall health summary"""
        total_services = len(self.circuit_breakers)
        open_breakers = sum(
            1 for cb in self.circuit_breakers.values()
            if cb.get_state() == CircuitBreakerState.OPEN.value
        )
        half_open_breakers = sum(
            1 for cb in self.circuit_breakers.values()
            if cb.get_state() == CircuitBreakerState.HALF_OPEN.value
        )
        
        return {
            "total_services": total_services,
            "healthy_services": total_services - open_breakers - half_open_breakers,
            "open_breakers": open_breakers,
            "half_open_breakers": half_open_breakers,
            "total_trips": self.get_trip_count(),
            "overall_health": "healthy" if open_breakers == 0 else "degraded"
        }
    
    async def update_service_config(
        self,
        service_name: str,
        config: Dict[str, Any]
    ) -> bool:
        """Update configuration for a service"""
        try:
            if service_name in self.service_configs:
                self.service_configs[service_name].update(config)
            else:
                self.service_configs[service_name] = config
            
            # If circuit breaker exists, recreate with new config
            if service_name in self.circuit_breakers:
                await self._create_circuit_breaker(service_name)
            
            logger.info(f"Updated configuration for {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating config for {service_name}: {e}")
            return False
    
    def get_service_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all service configurations"""
        return self.service_configs.copy()
    
    async def health_check_services(self) -> Dict[str, Any]:
        """Perform health check on circuit breaker protected services"""
        health_results = {}
        
        for service_name, circuit_breaker in self.circuit_breakers.items():
            stats = circuit_breaker.get_stats()
            
            health_results[service_name] = {
                "state": stats["state"],
                "success_rate": stats["success_rate"],
                "failure_count": stats["failure_count"],
                "total_requests": stats["total_requests"],
                "is_healthy": stats["state"] == CircuitBreakerState.CLOSED.value
            }
        
        return health_results