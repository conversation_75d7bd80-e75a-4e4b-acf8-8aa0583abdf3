apiVersion: v1
kind: Namespace
metadata:
  name: api-gateway
  labels:
    name: api-gateway
    security.istio.io/tlsMode: istio

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
  namespace: api-gateway
data:
  SERVICE_NAME: "api-gateway"
  SERVICE_PORT: "8080"
  SERVICE_HOST: "0.0.0.0"
  LOG_LEVEL: "info"
  ENVIRONMENT: "production"
  # Service Discovery
  SERVICE_DISCOVERY_URL: "http://service-discovery.infrastructure:8070"
  # Redis for rate limiting
  REDIS_URL: "redis://redis.infrastructure:6379"
  # Security
  JWT_ALGORITHM: "HS256"
  # Monitoring
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-secrets
  namespace: api-gateway
type: Opaque
data:
  # JWT secret key (base64 encoded)
  JWT_SECRET_KEY: "eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1mb3ItcHJvZHVjdGlvbi11c2U="
  # Admin API key (base64 encoded)
  ADMIN_API_KEY: "cGtfYWRtaW5fZ2F0ZXdheV9rZXlfMTIzNDU2Nzg5MDEyMzQ1Njc4OTA="

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app: api-gateway
    version: v1
    component: infrastructure
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1
        component: infrastructure
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: api-gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: api-gateway
        image: api-gateway:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        env:
        - name: SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: SERVICE_NAME
        - name: SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: SERVICE_PORT
        - name: SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: SERVICE_HOST
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: LOG_LEVEL
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: ENVIRONMENT
        - name: SERVICE_DISCOVERY_URL
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: SERVICE_DISCOVERY_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: REDIS_URL
        - name: JWT_ALGORITHM
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: JWT_ALGORITHM
        - name: METRICS_ENABLED
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: METRICS_ENABLED
        - name: TRACING_ENABLED
          valueFrom:
            configMapKeyRef:
              name: api-gateway-config
              key: TRACING_ENABLED
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: api-gateway-secrets
              key: JWT_SECRET_KEY
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-gateway-secrets
              key: ADMIN_API_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 6
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-run
          mountPath: /var/run
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-run
        emptyDir: {}
      nodeSelector:
        node-type: worker
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "worker"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app: api-gateway
    component: infrastructure
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  - name: https
    port: 443
    targetPort: 8080
    protocol: TCP
  selector:
    app: api-gateway
  sessionAffinity: None

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-internal
  namespace: api-gateway
  labels:
    app: api-gateway
    component: infrastructure
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: api-gateway

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app: api-gateway

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: api-gateway
  labels:
    app: api-gateway
rules:
- apiGroups: [""]
  resources: ["services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: api-gateway
  labels:
    app: api-gateway
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: api-gateway
subjects:
- kind: ServiceAccount
  name: api-gateway
  namespace: api-gateway

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: api-gateway
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: api-gateway

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-network-policy
  namespace: api-gateway
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 8080
  - from: []
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: infrastructure
    ports:
    - protocol: TCP
      port: 8070  # Service Discovery
    - protocol: TCP
      port: 6379  # Redis
  - to:
    - namespaceSelector:
        matchLabels:
          name: tier1-services
    - namespaceSelector:
        matchLabels:
          name: tier2-services
    - namespaceSelector:
        matchLabels:
          name: tier3-services
    ports:
    - protocol: TCP
      port: 8001  # Service ports
    - protocol: TCP
      port: 8002
    - protocol: TCP
      port: 8003
    - protocol: TCP
      port: 8004
    - protocol: TCP
      port: 8005
    - protocol: TCP
      port: 8006
    - protocol: TCP
      port: 8007
    - protocol: TCP
      port: 8008
  - to: []
    ports:
    - protocol: TCP
      port: 53  # DNS
    - protocol: UDP
      port: 53  # DNS
    - protocol: TCP
      port: 443  # HTTPS

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: api-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-nginx
  namespace: api-gateway
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream api_gateway {
            server api-gateway-internal:8080;
        }
        
        server {
            listen 80;
            
            location / {
                proxy_pass http://api_gateway;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Timeouts
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
                
                # Buffer settings
                proxy_buffering on;
                proxy_buffer_size 4k;
                proxy_buffers 8 4k;
                
                # Rate limiting
                limit_req_status 429;
            }
            
            location /health {
                proxy_pass http://api_gateway/health;
                access_log off;
            }
        }
    }