# API Gateway Service

A production-ready API Gateway service providing centralized routing, security, rate limiting, load balancing, and request/response transformation for all microservices in the Publish AI platform.

## Features

### Core Gateway Functionality
- **Centralized Routing**: Routes requests to appropriate microservices based on URL patterns
- **Load Balancing**: Multiple algorithms (round-robin, random, least connections, weighted random)
- **Service Discovery**: Integration with service registry for dynamic service lookup
- **Circuit Breaker**: Fault tolerance and cascade failure prevention
- **Rate Limiting**: Distributed rate limiting using Redis with multiple tiers
- **Authentication & Authorization**: JWT tokens and API key support with role-based access control

### Advanced Features
- **Request/Response Middleware**: Logging, security headers, compression, CORS
- **Health Monitoring**: Comprehensive health checks for all services
- **Metrics & Monitoring**: Performance metrics and observability
- **Security**: Production-grade security headers and input validation
- **High Availability**: Kubernetes-ready with auto-scaling and fault tolerance

## Architecture

### Components
- **Gateway Router**: Core routing logic and service communication
- **Service Registry**: Service discovery and health monitoring client
- **Load Balancer**: Service instance selection and health tracking
- **Rate Limiter**: Redis-based distributed rate limiting
- **Circuit Breaker Manager**: Fault tolerance pattern implementation
- **Authentication Manager**: JWT and API key authentication
- **Middleware Manager**: Request/response transformation pipeline

### Service Routing
```
Client Request → API Gateway → Service Discovery → Load Balancer → Target Service
                     ↓
            Rate Limiter + Circuit Breaker + Auth + Middleware
```

## Installation

### Development Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export SERVICE_DISCOVERY_URL="http://localhost:8070"
   export REDIS_URL="redis://localhost:6379"
   export JWT_SECRET_KEY="your-secret-key"
   ```

3. **Run the Service**
   ```bash
   python src/main.py
   ```

### Docker Deployment

1. **Build Image**
   ```bash
   docker build -t api-gateway:latest .
   ```

2. **Run Container**
   ```bash
   docker run -p 8080:8080 \
     -e SERVICE_DISCOVERY_URL="http://service-discovery:8070" \
     -e REDIS_URL="redis://redis:6379" \
     -e JWT_SECRET_KEY="your-secret-key" \
     api-gateway:latest
   ```

### Kubernetes Deployment

1. **Deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s-deployment.yaml
   ```

2. **Verify Deployment**
   ```bash
   kubectl get pods -n api-gateway
   kubectl get services -n api-gateway
   ```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVICE_PORT` | `8080` | Service port |
| `SERVICE_HOST` | `0.0.0.0` | Service host |
| `SERVICE_DISCOVERY_URL` | `http://localhost:8070` | Service discovery endpoint |
| `REDIS_URL` | `redis://localhost:6379` | Redis URL for rate limiting |
| `JWT_SECRET_KEY` | Required | JWT signing secret |
| `JWT_ALGORITHM` | `HS256` | JWT algorithm |
| `LOG_LEVEL` | `info` | Logging level |
| `ENVIRONMENT` | `development` | Environment name |

### Rate Limiting Configuration

Default rate limits (requests per minute):
- **Global**: 1000 per IP
- **Per Service**: 100 per IP per service
- **Authenticated Users**: 500 per user
- **Admin Users**: 10000 per user

### Circuit Breaker Configuration

Service-specific thresholds:
- **Research Service**: 3 failures, 30s recovery
- **Content Generation**: 5 failures, 90s recovery
- **Publishing Service**: 3 failures, 120s recovery

## API Endpoints

### Gateway Management

#### Health Check
```http
GET /health
```
Returns gateway health status and component readiness.

#### Readiness Check
```http
GET /ready
```
Returns readiness status for load balancer health checks.

#### Gateway Status
```http
GET /status
```
Returns detailed gateway status and metrics.

#### Services Health
```http
GET /services
```
Returns health status of all registered services.

#### Metrics
```http
GET /metrics
```
Returns gateway performance metrics.

#### Service Management
```http
POST /services/{service_name}/reload
```
Reload specific service configuration.

### Service Routing

The gateway automatically routes requests based on URL patterns:

| Pattern | Target Service |
|---------|----------------|
| `/api/research` | research-service |
| `/api/personalization` | personalization-service |
| `/api/cover` | cover-designer-service |
| `/api/sales` | sales-monitor-service |
| `/api/market` | market-intelligence-service |
| `/api/content` | content-generation-service |
| `/api/publish` | publishing-service |
| `/api/multimodal` | multimodal-generator-service |

## Authentication

### JWT Authentication
```http
Authorization: Bearer <jwt-token>
```

Create JWT tokens using the `/auth/token` endpoint or use the AuthManager programmatically.

### API Key Authentication
```http
X-API-Key: <api-key>
```

Default API keys for testing:
- **Admin**: `pk_admin_gateway_key_12345678901234567890`
- **Manager**: `pk_manager_gateway_key_12345678901234567890`
- **User**: `pk_user_gateway_key_12345678901234567890`

### Role-Based Access Control

Roles and permissions:
- **admin**: Full access to all services and management
- **manager**: Read/write access to business services
- **user**: Limited access to own resources
- **readonly**: Read-only access

## Monitoring & Observability

### Health Checks
- **Liveness Probe**: `/health` endpoint
- **Readiness Probe**: `/ready` endpoint
- **Startup Probe**: Initial health verification

### Metrics
- Request count and error rates
- Response time percentiles
- Circuit breaker trip counts
- Rate limit hit counts
- Service health status

### Logging
Structured logging with request tracing:
- Request/response logging
- Error tracking
- Performance monitoring
- Security event logging

## Security

### Security Headers
- Content Security Policy (CSP)
- Strict Transport Security (HSTS)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection

### Input Validation
- Request size limits
- Header validation
- Path traversal protection
- SQL injection prevention

### Network Security
- Network policies for pod-to-pod communication
- TLS encryption for inter-service communication
- Service account isolation

## Testing

### Run Tests
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_main.py

# Run with coverage
pytest --cov=src tests/
```

### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end request flow testing
- **Load Tests**: Performance and scalability testing

### Test Coverage
- Main application: 95%+
- Gateway router: 90%+
- Authentication: 95%+
- Circuit breaker: 90%+

## Development

### Code Structure
```
src/
├── main.py                 # FastAPI application
├── gateway_router.py       # Core routing logic
├── service_registry.py     # Service discovery client
├── load_balancer.py        # Load balancing algorithms
├── rate_limiter.py         # Rate limiting with Redis
├── circuit_breaker.py      # Circuit breaker pattern
├── auth_manager.py         # Authentication & authorization
├── middleware_manager.py   # Request/response middleware
└── shared/
    └── monitoring.py       # Monitoring utilities
```

### Adding New Services
1. Add route pattern to `gateway_router.py`
2. Configure circuit breaker thresholds
3. Set up authentication requirements
4. Update Kubernetes network policies

### Custom Middleware
```python
from middleware_manager import Middleware

class CustomMiddleware(Middleware):
    def __init__(self):
        super().__init__("custom", priority=50)
    
    async def process_request(self, request):
        # Custom request processing
        return request
    
    async def process_response(self, response):
        # Custom response processing
        return response
```

## Deployment

### Production Checklist
- [ ] Update JWT secret key
- [ ] Configure Redis cluster
- [ ] Set up monitoring
- [ ] Configure log aggregation
- [ ] Enable TLS/mTLS
- [ ] Set resource limits
- [ ] Configure auto-scaling
- [ ] Set up backup procedures

### Scaling Considerations
- **Horizontal Scaling**: 3-10 replicas based on load
- **Resource Requirements**: 256Mi-512Mi memory, 250m-500m CPU
- **Redis**: Use Redis cluster for high availability
- **Load Balancer**: Use external load balancer for production

### Monitoring Setup
1. Deploy Prometheus for metrics collection
2. Set up Grafana dashboards
3. Configure alerting rules
4. Set up log aggregation (ELK stack)

## Troubleshooting

### Common Issues

#### Service Discovery Connection Failed
```bash
# Check service discovery health
curl http://service-discovery:8070/health

# Verify network connectivity
kubectl exec -it api-gateway-pod -- curl service-discovery:8070/health
```

#### Redis Connection Issues
```bash
# Check Redis connectivity
kubectl exec -it api-gateway-pod -- redis-cli -h redis ping

# Verify Redis configuration
kubectl get configmap api-gateway-config -o yaml
```

#### Authentication Failures
```bash
# Verify JWT configuration
kubectl get secret api-gateway-secrets -o yaml

# Check API key configuration
curl -H "X-API-Key: your-key" http://gateway/api/test
```

### Performance Issues

#### High Response Times
1. Check service health: `GET /services`
2. Monitor circuit breaker status: `GET /metrics`
3. Review rate limiting: Check Redis metrics
4. Scale up replicas if needed

#### Memory/CPU Issues
1. Monitor resource usage: `kubectl top pods`
2. Check for memory leaks in logs
3. Adjust resource limits
4. Scale horizontally

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Create GitHub issue
- Check documentation
- Review logs and metrics
- Contact development team

---

**Note**: This is a production-ready API Gateway service designed for high availability and scalability. Ensure proper security configuration before deploying to production.