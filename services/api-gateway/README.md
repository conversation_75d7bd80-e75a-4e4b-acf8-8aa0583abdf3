# API Gateway Service

A high-performance, production-ready API Gateway for the Publish AI microservices architecture. This service provides centralized routing, authentication, rate limiting, and observability for all microservice communications.

## Features

### Core Gateway Functionality
- **Intelligent Routing**: Dynamic route discovery via Service Discovery integration
- **Load Balancing**: Multiple strategies with health-aware routing
- **Request/Response Transformation**: Header manipulation, payload transformation
- **Protocol Translation**: HTTP/HTTPS, WebSocket support
- **Service Mesh Integration**: Ready for Istio/Linkerd integration

### Security & Authentication
- **Multi-layer Authentication**: API keys, JWT tokens, OAuth2/OIDC
- **Authorization**: Role-based access control (RBAC) and policy enforcement
- **Rate Limiting**: Per-client, per-endpoint, and global rate limiting
- **CORS Management**: Configurable cross-origin resource sharing
- **Request Validation**: Schema validation and input sanitization

### Performance & Resilience
- **Circuit Breaker**: Automatic failure detection and recovery
- **Timeout Management**: Configurable request and service timeouts
- **Connection Pooling**: Efficient connection reuse and management
- **Caching**: Response caching with TTL and invalidation policies
- **Compression**: Automatic response compression (gzip, brotli)

### Observability & Monitoring
- **Distributed Tracing**: OpenTelemetry integration with correlation IDs
- **Metrics Collection**: Comprehensive Prometheus metrics
- **Access Logging**: Structured request/response logging
- **Health Monitoring**: Upstream service health tracking
- **Real-time Analytics**: Traffic patterns and performance insights

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │    │   API Gateway    │    │  Microservices  │
│                 │    │                  │    │                 │
│ 1. Request      │───▶│ ┌──────────────┐ │    │ ┌─────────────┐ │
│ 2. Auth Check   │    │ │  Auth Layer  │ │    │ │ Agent Service│ │
│ 3. Rate Limit   │    │ │              │ │    │ │             │ │
│ 4. Route        │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌──────────────┐ │◀───│ ┌─────────────┐ │
│                 │    │ │ Route Engine │ │    │ │Event Bus Svc│ │
│                 │    │ │              │ │    │ │             │ │
│                 │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │◀───│ ┌──────────────┐ │    │ ┌─────────────┐ │
│                 │    │ │ Load Balancer│ │    │ │Service Disc │ │
│                 │    │ │              │ │    │ │             │ │
│                 │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Routing Configuration

### Route Discovery
The API Gateway automatically discovers routes through Service Discovery integration:

```yaml
# Automatic route generation example
services:
  - name: content-generator
    prefix: /api/content
    upstream: content-generator-service
    methods: [GET, POST, PUT, DELETE]
    auth_required: true
    rate_limit: 100/minute
    
  - name: trend-analyzer  
    prefix: /api/trends
    upstream: trend-analyzer-service
    methods: [GET, POST]
    auth_required: true
    rate_limit: 50/minute
```

### Manual Route Configuration
```json
{
  "routes": [
    {
      "id": "content-generation",
      "path": "/api/content/*",
      "methods": ["GET", "POST", "PUT", "DELETE"],
      "upstream": {
        "service_name": "content-generator",
        "load_balancing": "round_robin",
        "health_check": true,
        "timeout": 30
      },
      "middleware": [
        {
          "type": "auth",
          "config": {"required": true, "type": "api_key"}
        },
        {
          "type": "rate_limit", 
          "config": {"requests": 100, "window": "1m"}
        },
        {
          "type": "cors",
          "config": {"origins": ["*"], "methods": ["GET", "POST"]}
        }
      ]
    }
  ]
}
```

## Authentication & Authorization

### Supported Authentication Methods
1. **API Key Authentication**
   ```http
   GET /api/content/generate
   Authorization: Bearer your-api-key-here
   X-API-Key: your-api-key-here
   ```

2. **JWT Token Authentication**
   ```http
   GET /api/trends/analyze
   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

3. **OAuth2/OIDC Integration**
   ```http
   GET /api/user/profile
   Authorization: Bearer oauth2-access-token
   ```

### Role-Based Access Control (RBAC)
```json
{
  "roles": {
    "admin": {
      "permissions": ["*"],
      "routes": ["*"]
    },
    "user": {
      "permissions": ["read", "write:own"],
      "routes": ["/api/content/*", "/api/trends/analyze"]
    },
    "service": {
      "permissions": ["service:*"],
      "routes": ["/api/internal/*"]
    }
  }
}
```

## Rate Limiting

### Configuration Options
```json
{
  "rate_limiting": {
    "global": {
      "requests": 10000,
      "window": "1h"
    },
    "per_client": {
      "requests": 1000,
      "window": "1h"
    },
    "per_endpoint": {
      "/api/content/generate": {
        "requests": 100,
        "window": "1m"
      },
      "/api/trends/analyze": {
        "requests": 50,
        "window": "1m"
      }
    }
  }
}
```

### Rate Limit Headers
```http
HTTP/1.1 200 OK
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 85
X-RateLimit-Reset: **********
X-RateLimit-Window: 60
```

## Load Balancing

### Supported Strategies
1. **Round Robin** (Default)
2. **Weighted Round Robin**
3. **Least Connections**
4. **IP Hash**
5. **Random**
6. **Health-aware routing**

### Health Check Configuration
```json
{
  "health_checks": {
    "interval": 30,
    "timeout": 10,
    "healthy_threshold": 2,
    "unhealthy_threshold": 3,
    "path": "/health"
  }
}
```

## Circuit Breaker

### Configuration
```json
{
  "circuit_breaker": {
    "failure_threshold": 5,
    "success_threshold": 3,
    "timeout": 60,
    "half_open_max_calls": 3
  }
}
```

### Circuit Breaker States
- **Closed**: Normal operation, requests pass through
- **Open**: Circuit is open, requests fail fast
- **Half-Open**: Testing if service has recovered

## Caching

### Response Caching
```json
{
  "caching": {
    "enabled": true,
    "default_ttl": 300,
    "cache_keys": ["method", "path", "query"],
    "cacheable_methods": ["GET"],
    "cacheable_status_codes": [200, 301, 302]
  }
}
```

### Cache Headers
```http
HTTP/1.1 200 OK
Cache-Control: public, max-age=300
ETag: "abc123"
X-Cache: HIT
X-Cache-TTL: 245
```

## API Endpoints

### Gateway Management
- `GET /gateway/health` - Gateway health status
- `GET /gateway/metrics` - Prometheus metrics
- `GET /gateway/routes` - List configured routes
- `POST /gateway/routes` - Add new route
- `PUT /gateway/routes/{id}` - Update route
- `DELETE /gateway/routes/{id}` - Remove route

### Administrative
- `GET /admin/dashboard` - Web-based admin interface
- `GET /admin/stats` - Traffic and performance statistics
- `POST /admin/cache/clear` - Clear response cache
- `GET /admin/services` - Upstream service status

### Traffic Routing
- `/*` - All application traffic (dynamically routed)

## Configuration

### Environment Variables
```bash
# Service Configuration
API_GATEWAY_HOST=0.0.0.0
API_GATEWAY_PORT=8080
API_GATEWAY_DEBUG=false
API_GATEWAY_WORKERS=4

# Service Discovery Integration
API_GATEWAY_SERVICE_DISCOVERY_URL=http://service-discovery:8070
API_GATEWAY_SERVICE_DISCOVERY_API_KEY=gateway-service-key

# Authentication
API_GATEWAY_JWT_SECRET=your-jwt-secret
API_GATEWAY_API_KEY_HEADER=X-API-Key
API_GATEWAY_ENABLE_OAUTH=true

# Rate Limiting
API_GATEWAY_REDIS_URL=redis://localhost:6379
API_GATEWAY_RATE_LIMIT_ENABLED=true
API_GATEWAY_GLOBAL_RATE_LIMIT=10000/hour

# Circuit Breaker
API_GATEWAY_CIRCUIT_BREAKER_ENABLED=true
API_GATEWAY_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5

# Caching
API_GATEWAY_CACHE_ENABLED=true
API_GATEWAY_CACHE_TTL=300
API_GATEWAY_CACHE_REDIS_DB=1

# Monitoring
API_GATEWAY_METRICS_ENABLED=true
API_GATEWAY_TRACING_ENABLED=true
API_GATEWAY_ACCESS_LOG_ENABLED=true
```

## Usage Examples

### Client Request Flow
```python
import httpx

# Client request to generate content
async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://api-gateway:8080/api/content/generate",
        headers={
            "Authorization": "Bearer your-api-key",
            "Content-Type": "application/json"
        },
        json={
            "title": "AI and the Future",
            "category": "technology",
            "length": 5000
        }
    )
    
    print(f"Status: {response.status_code}")
    print(f"Rate Limit: {response.headers.get('X-RateLimit-Remaining')}")
    print(f"Response: {response.json()}")
```

### Service Registration
```python
# Services automatically register routes via Service Discovery
# The API Gateway discovers them and creates routes

# Manual route registration example
route_config = {
    "id": "custom-service",
    "path": "/api/custom/*",
    "methods": ["GET", "POST"],
    "upstream": {
        "service_name": "custom-service",
        "load_balancing": "least_connections"
    },
    "middleware": [
        {"type": "auth", "config": {"required": True}},
        {"type": "rate_limit", "config": {"requests": 50, "window": "1m"}}
    ]
}

async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://api-gateway:8080/gateway/routes",
        json=route_config,
        headers={"Authorization": "Bearer admin-token"}
    )
```

## Deployment

### Docker Compose
```yaml
version: '3.8'
services:
  api-gateway:
    build: .
    ports:
      - "8080:8080"
    environment:
      - API_GATEWAY_SERVICE_DISCOVERY_URL=http://service-discovery:8070
      - API_GATEWAY_REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - service-discovery
```

### Production Deployment
- Deploy behind a load balancer (HAProxy, NGINX)
- Configure SSL/TLS termination
- Set up monitoring and alerting
- Configure log aggregation
- Enable distributed tracing

## Monitoring and Observability

### Prometheus Metrics
- `api_gateway_requests_total` - Total requests processed
- `api_gateway_request_duration_seconds` - Request processing time
- `api_gateway_upstream_requests_total` - Upstream service requests
- `api_gateway_circuit_breaker_state` - Circuit breaker states
- `api_gateway_rate_limit_exceeded_total` - Rate limit violations

### Tracing
- Request correlation IDs
- Distributed tracing spans
- Service dependency mapping
- Performance bottleneck identification

### Logging
- Structured JSON logging
- Request/response logging
- Error tracking and analysis
- Security event logging

This API Gateway provides enterprise-grade capabilities for managing microservice communication while maintaining high performance and reliability standards essential for the Publish AI platform.