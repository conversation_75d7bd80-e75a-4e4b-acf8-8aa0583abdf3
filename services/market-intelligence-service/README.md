# Market Intelligence Service

A comprehensive microservice implementation providing AI-powered market intelligence, trend analysis, and competitive research capabilities for the Publish AI platform.

## Overview

The Market Intelligence Service is the first Tier 1 microservice in the Publish AI platform's microservices architecture migration. It combines advanced market analysis, trend identification, and competitive intelligence into a standalone, scalable service for comprehensive business intelligence and opportunity discovery.

## Features

- **AI-Powered Market Analysis**: Comprehensive market analysis using OpenAI/Anthropic models
- **Google Trends Integration**: Real-time trend analysis with PyTrends integration
- **Competitive Intelligence**: Automated competitor analysis and market positioning
- **Opportunity Discovery**: AI-driven opportunity identification and scoring
- **Market Forecasting**: Predictive market analysis and trend forecasting
- **Industry-Specific Analysis**: Specialized analysis for health, wealth, beauty, technology sectors
- **Multiple AI Models**: Supports both OpenAI and Anthropic models with automatic fallback
- **Real-Time Data**: Integration with Google Trends and market data sources
- **Event-Driven**: Integrates with Event Bus for asynchronous communication
- **Service Discovery**: Automatic registration and health monitoring
- **Security**: API key authentication and mTLS support
- **Monitoring**: Comprehensive Prometheus metrics and health checks
- **Scalable**: Docker containerization with Kubernetes support

## API Endpoints

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### Market Analysis
- `POST /analyze` - Start asynchronous market analysis
- `GET /analyze/{request_id}` - Get market analysis status and results
- `POST /analyze/sync` - Synchronous market analysis execution

### Trend Analysis
- `POST /trends` - Perform comprehensive trend analysis

### Opportunity Analysis
- `POST /opportunities` - Analyze market opportunities across industries

### Competitive Intelligence
- `POST /competitors` - Analyze competitors in a category

## Request/Response Models

### Market Analysis Request
```json
{
  "category": "self-help",
  "analysis_depth": "comprehensive",
  "include_competitors": true,
  "include_trends": true,
  "include_pricing": true,
  "force_refresh": false,
  "user_id": "user123"
}
```

### Market Analysis Response
```json
{
  "request_id": "uuid-string",
  "status": "completed",
  "result": {
    "category": "self-help",
    "analysis_date": "2024-01-01T12:00:00Z",
    "market_size": {
      "estimated_size": "Large - $500M+ annual",
      "growth_rate": 0.15
    },
    "competition_density": 0.7,
    "saturation_level": 0.6,
    "opportunities": [
      {
        "category": "self-help",
        "opportunity_score": 0.85,
        "market_size": "Large",
        "competition_level": "medium",
        "recommended_approach": "Focus on AI productivity niche"
      }
    ],
    "trending_topics": [
      {
        "trend_name": "AI productivity",
        "trend_score": 0.9,
        "growth_trajectory": "rising",
        "market_relevance": "high",
        "opportunity_window": "immediate"
      }
    ],
    "recommendations": [
      {
        "type": "market_entry",
        "priority": "high",
        "title": "AI Productivity Opportunity",
        "description": "Strong growth in AI productivity market with low competition"
      }
    ],
    "confidence_score": 0.88
  },
  "execution_time": 15.2,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Trend Analysis Request
```json
{
  "categories": ["business", "self-help"],
  "keywords": ["productivity", "ai tools"],
  "timeframe": "today 3-m",
  "geo": "US",
  "analysis_type": "comprehensive",
  "max_results": 50,
  "user_id": "user123"
}
```

### Trend Analysis Response
```json
{
  "request_id": "trend-uuid",
  "status": "completed",
  "result": {
    "analysis_date": "2024-01-01T12:00:00Z",
    "timeframe": "today 3-m",
    "geographic_region": "US",
    "trending_keywords": [
      {
        "keyword": "ai productivity",
        "interest_score": 85,
        "growth_rate": 25.5,
        "regional_interest": {"US": 85}
      }
    ],
    "rising_trends": ["ai productivity", "automation tools"],
    "declining_trends": ["manual processes"],
    "stable_trends": ["project management"],
    "opportunities": [
      {
        "keyword": "ai productivity",
        "opportunity_score": 0.88,
        "market_potential": "High - Strong interest with positive growth",
        "competition_level": "Medium",
        "recommended_timing": "Immediate - Strong growth trend",
        "content_suggestions": [
          "Complete Guide to AI Productivity",
          "AI Tools for Business Automation",
          "Productivity Hacks with AI"
        ]
      }
    ],
    "immediate_opportunities": ["ai productivity", "automation tools"],
    "content_gaps": [
      "ai productivity - High - Strong interest with positive growth"
    ],
    "confidence_score": 0.92
  }
}
```

### Opportunity Analysis Request
```json
{
  "industry_focus": ["health", "wealth", "technology"],
  "target_audience": "entrepreneurs",
  "budget_range": {"min": 1000, "max": 10000},
  "risk_tolerance": "medium",
  "time_horizon": "3_months",
  "user_id": "user123"
}
```

### Competitive Analysis Request
```json
{
  "category": "business",
  "target_keywords": ["productivity", "automation"],
  "analysis_depth": "comprehensive",
  "include_pricing": true,
  "include_performance": true,
  "user_id": "user123"
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVICE_HOST` | Service host address | `0.0.0.0` |
| `SERVICE_PORT` | Service port | `8085` |
| `PREFERRED_MODEL` | AI model preference | `openai` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `ANTHROPIC_API_KEY` | Anthropic API key | Optional |
| `EVENT_BUS_URL` | Event Bus service URL | `http://event-bus:8080` |
| `SERVICE_DISCOVERY_URL` | Service Discovery URL | `http://service-discovery:8070` |
| `API_KEY` | Service API key | Required |

### Analysis Capabilities

- **Market Categories**: business, self-help, health, technology, finance, lifestyle
- **Analysis Depths**: basic, standard, comprehensive
- **Timeframes**: today 7-d, today 3-m, today 12-m, custom ranges
- **Geographic Regions**: US, UK, CA, AU, DE, FR, global
- **Industry Focus**: health, wealth, beauty, technology, business, fitness, nutrition
- **Risk Tolerance**: low, medium, high
- **Time Horizons**: 1_month, 3_months, 6_months, 1_year

## Development

### Local Development

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export API_KEY="your-service-api-key"
   ```

3. **Run Service**
   ```bash
   cd src
   python main.py
   ```

### Testing

Run all tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=src --cov-report=html
```

Run specific test category:
```bash
pytest tests/test_main_simple.py
pytest tests/test_market_intelligence_agent.py
pytest tests/test_trend_analyzer.py
```

### Docker Development

1. **Build Image**
   ```bash
   docker build -t market-intelligence-service .
   ```

2. **Run Container**
   ```bash
   docker run -p 8085:8085 \
     -e OPENAI_API_KEY="your-key" \
     -e API_KEY="your-api-key" \
     market-intelligence-service
   ```

## Architecture

### Components

- **Market Intelligence Agent**: AI-powered market analysis and opportunity identification
- **Trend Analyzer**: Google Trends integration and trend analysis engine
- **Event Client**: Communication with Event Bus service
- **Service Registry Client**: Registration with Service Discovery
- **Security Manager**: API key authentication and mTLS
- **Monitoring**: Comprehensive Prometheus metrics and health checks

### Dependencies

- **Infrastructure Services**: Event Bus, Service Discovery
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **External APIs**: Google Trends (PyTrends), Web scraping
- **Security**: mTLS certificates, API keys
- **Storage**: Persistent volume for trend cache
- **Monitoring**: Prometheus metrics collection

### Data Flow

1. Client sends analysis request with authentication
2. Service validates request and API key
3. Market Intelligence Agent or Trend Analyzer processes request
4. External data sources (Google Trends) provide real-time data
5. AI models analyze data and generate insights
6. Results published to Event Bus (async mode)
7. Metrics recorded for monitoring
8. Response returned to client

## AI Model Integration

### Market Intelligence Agent
- **Primary Model**: OpenAI GPT-4 or Anthropic Claude-3
- **Capabilities**: Market analysis, opportunity scoring, competitive intelligence
- **Prompts**: Specialized prompts for market analysis, trend analysis, competitor analysis
- **Output**: Structured analysis with confidence scores and recommendations

### Trend Analysis Engine
- **Data Source**: Google Trends via PyTrends
- **Processing**: Real-time trend data analysis and opportunity identification
- **Features**: Keyword analysis, growth rate calculation, competition assessment
- **Intelligence**: AI-powered content suggestions and timing recommendations

### Dual-Model Architecture
- **Primary**: Preferred model (OpenAI/Anthropic) for main analysis
- **Fallback**: Automatic failover to secondary model if primary unavailable
- **Optimization**: Model selection based on request type and performance

## Deployment

### Docker Compose
```yaml
services:
  market-intelligence-service:
    build: .
    ports:
      - "8085:8085"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${MARKET_INTELLIGENCE_SERVICE_API_KEY}
    volumes:
      - ../../security/certs:/etc/ssl/certs:ro
      - trend_cache:/app/trend_cache
    networks:
      - publish-ai-network

volumes:
  trend_cache:
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: market-intelligence-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: market-intelligence-service
  template:
    metadata:
      labels:
        app: market-intelligence-service
        component: microservice
        tier: tier-1
    spec:
      containers:
      - name: market-intelligence-service
        image: market-intelligence-service:latest
        ports:
        - containerPort: 8085
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-api-key
        volumeMounts:
        - name: trend-cache
          mountPath: /app/trend_cache
      volumes:
      - name: trend-cache
        persistentVolumeClaim:
          claimName: trend-cache-pvc
```

## Security

### Authentication
- API key authentication for all endpoints
- Service-to-service authentication via mTLS
- Security headers and CORS configuration

### Data Protection
- Encrypted inter-service communication
- Secure trend data caching with persistent volumes
- Row-level security for sensitive market data

### Network Security
- Kubernetes NetworkPolicies for micro-segmentation
- Zero Trust networking principles
- Firewall rules for service isolation

## Monitoring

### Metrics
- Market analysis operation metrics (count, duration, success rate)
- Trend analysis performance and accuracy tracking
- AI model usage and response time metrics
- Google Trends API usage and rate limiting
- Business KPIs (opportunity discovery, recommendation effectiveness)
- Agent health status and availability
- Event publication counts and errors

### Health Checks
- `/health` - Comprehensive health status including AI models and external APIs
- `/ready` - Readiness for traffic including trend data availability
- Service Discovery integration with automatic registration

### Logging
- Structured logging with correlation IDs
- Market analysis and trend analysis audit logs
- Business event tracking for analytics
- AI model performance logging
- External API interaction logs

## Testing

### Test Coverage
- Unit tests for all components (16 tests)
- Market Intelligence Agent tests (26 tests)
- Trend Analyzer tests (29 tests)
- Integration tests for AI model interactions
- Performance and load testing capabilities

### Test Categories
- **Unit Tests**: Component-level testing (Main: 16 tests, Agent: 26 tests, Trends: 29 tests)
- **Integration Tests**: AI model and external API testing
- **Contract Tests**: Service interface testing
- **Performance Tests**: Load testing and scalability validation

**Total Test Coverage**: 69/71 tests passing (97.2% success rate)

## Migration Status

✅ **Phase 2.3: Market Intelligence Service Migration - COMPLETED**

- [x] Dual-component architecture (market intelligence + trend analysis)
- [x] AI-powered market analysis with PydanticAI
- [x] Google Trends integration with PyTrends
- [x] Event-driven communication integration
- [x] Service Discovery registration
- [x] Security implementation (API keys + mTLS)
- [x] Comprehensive testing suite (69/71 tests passing)
- [x] Docker containerization with persistent storage
- [x] Kubernetes deployment manifests
- [x] Monitoring and metrics
- [x] Documentation and deployment guides

**Service Metrics**: 69/71 tests passing, 9 API endpoints, AI + Trends integration

## Market Intelligence Features

### Analysis Capabilities
1. **Market Size Assessment**: Comprehensive market size estimation and growth analysis
2. **Competition Analysis**: Detailed competitor profiling and market positioning
3. **Opportunity Scoring**: AI-powered opportunity identification and ranking
4. **Trend Forecasting**: Predictive trend analysis with confidence intervals
5. **Risk Assessment**: Comprehensive risk analysis with mitigation strategies

### Industry Specialization
1. **Health & Wellness**: Fitness, nutrition, mental health market analysis
2. **Wealth & Finance**: Business, investing, entrepreneurship opportunities
3. **Beauty & Lifestyle**: Skincare, fashion, lifestyle trend analysis
4. **Technology**: AI, software, digital transformation markets
5. **Education**: Learning, skills development, training opportunities

### Intelligence Features
1. **Real-Time Data**: Live Google Trends integration with 2-hour cache
2. **AI-Powered Insights**: Advanced pattern recognition and opportunity identification
3. **Competitive Intelligence**: Automated competitor tracking and analysis
4. **Market Timing**: Optimal timing recommendations based on seasonal patterns
5. **Content Strategy**: AI-generated content suggestions and gap analysis

## Troubleshooting

### Common Issues

1. **Google Trends API Rate Limiting**
   - Check rate limiting status: Monitor PyTrends connection
   - Verify cache hit rates and adjust cache duration
   - Implement exponential backoff for API calls

2. **AI Model Unavailability**
   - Check API key validity and quotas
   - Verify model fallback mechanism is working
   - Monitor model response times and error rates

3. **Service Registration Failed**
   - Verify Service Discovery is running
   - Check network connectivity and API keys
   - Validate service health endpoints

### Debug Commands

```bash
# Check service health
curl http://localhost:8085/health

# Verify readiness
curl http://localhost:8085/ready

# Get metrics
curl http://localhost:8085/metrics

# Test market analysis
curl -X POST http://localhost:8085/analyze/sync \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "business",
    "analysis_depth": "comprehensive",
    "include_trends": true
  }'

# Test trend analysis
curl -X POST http://localhost:8085/trends \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "categories": ["business"],
    "timeframe": "today 3-m",
    "analysis_type": "comprehensive"
  }'

# Test opportunity analysis
curl -X POST http://localhost:8085/opportunities \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "industry_focus": ["health", "wealth"],
    "risk_tolerance": "medium",
    "time_horizon": "3_months"
  }'
```

## Performance Optimization

### Caching Strategy
- Google Trends data caching with 2-hour TTL
- Market analysis result caching with 6-hour TTL
- AI model response caching for repeated queries
- Persistent trend cache with volume mounting

### Scaling Considerations
- Horizontal scaling with stateless analysis services
- Persistent storage for trend cache and analysis results
- Background job processing for long-running analyses
- Resource allocation based on analysis workload
- Load balancing for multiple AI model providers

### AI Model Optimization
- Model selection based on request complexity
- Prompt optimization for consistent high-quality results
- Response streaming for long-running analyses
- Batch processing for multiple analysis requests