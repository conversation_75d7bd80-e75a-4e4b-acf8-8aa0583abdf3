"""
Simple tests for Market Intelligence Service main application
These tests avoid complex dependencies and focus on basic functionality.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestMarketIntelligenceServiceBasic:
    """Basic tests for market intelligence service without full app initialization"""
    
    def test_service_configuration(self):
        """Test service configuration constants"""
        # Test that we can import the configuration
        assert True  # Basic import test
    
    def test_request_models_creation(self):
        """Test that request models can be created"""
        from main import MarketAnalysisRequest, TrendAnalysisRequest
        
        # Test MarketAnalysisRequest
        request = MarketAnalysisRequest(category="business")
        assert request.category == "business"
        assert request.analysis_depth == "standard"
        assert request.include_competitors is True
        assert request.include_trends is True
        assert request.include_pricing is True
        assert request.force_refresh is False
        
        # Test TrendAnalysisRequest
        trend_request = TrendAnalysisRequest()
        assert trend_request.timeframe == "today 3-m"
        assert trend_request.geo == "US"
        assert trend_request.analysis_type == "comprehensive"
        assert trend_request.max_results == 50
    
    def test_response_models_creation(self):
        """Test that response models can be created"""
        from main import ServiceResponse, AsyncJobStatus
        
        # Test ServiceResponse
        response = ServiceResponse(
            request_id="test-123",
            status="completed",
            message="Test completed"
        )
        assert response.request_id == "test-123"
        assert response.status == "completed"
        assert response.message == "Test completed"
        assert response.result is None
        
        # Test AsyncJobStatus
        job = AsyncJobStatus(
            request_id="job-123",
            status="pending",
            created_at=datetime.utcnow().isoformat(),
            updated_at=datetime.utcnow().isoformat()
        )
        assert job.request_id == "job-123"
        assert job.status == "pending"
    
    def test_opportunity_analysis_request(self):
        """Test opportunity analysis request model"""
        from main import OpportunityAnalysisRequest
        
        request = OpportunityAnalysisRequest(
            industry_focus=["health", "wealth", "beauty"],
            target_audience="entrepreneurs",
            risk_tolerance="medium",
            time_horizon="3_months"
        )
        
        assert request.industry_focus == ["health", "wealth", "beauty"]
        assert request.target_audience == "entrepreneurs"
        assert request.risk_tolerance == "medium"
        assert request.time_horizon == "3_months"
    
    def test_competitor_analysis_request(self):
        """Test competitor analysis request model"""
        from main import CompetitorAnalysisRequest
        
        request = CompetitorAnalysisRequest(
            category="self-help",
            target_keywords=["productivity", "time management"],
            analysis_depth="comprehensive",
            include_pricing=True,
            include_performance=True
        )
        
        assert request.category == "self-help"
        assert request.target_keywords == ["productivity", "time management"]
        assert request.analysis_depth == "comprehensive"
        assert request.include_pricing is True
        assert request.include_performance is True
    
    @pytest.mark.asyncio
    async def test_health_check_basic(self):
        """Test basic health check logic"""
        # Mock the health check components
        with patch('main.market_intelligence_agent') as mock_agent, \
             patch('main.trend_analyzer') as mock_trend, \
             patch('main.event_client') as mock_event:
            
            # Configure mocks
            mock_agent.is_ready = True
            mock_trend.is_ready = True
            mock_event.is_connected = True
            
            # Import after mocking
            from main import health_check
            
            result = await health_check()
            
            assert result["status"] == "healthy"
            assert result["service"] == "market-intelligence-service"
            assert result["version"] == "1.0.0"
            assert "components" in result
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self):
        """Test health check when components are unhealthy"""
        with patch('main.market_intelligence_agent') as mock_agent, \
             patch('main.trend_analyzer') as mock_trend, \
             patch('main.event_client') as mock_event:
            
            # Configure mocks as unhealthy
            mock_agent.is_ready = False
            mock_trend.is_ready = False
            mock_event.is_connected = False
            
            from main import health_check
            
            result = await health_check()
            
            assert result["status"] == "unhealthy"
            assert result["components"]["market_intelligence_agent"] == "unhealthy"
            assert result["components"]["trend_analyzer"] == "unhealthy"
            assert result["components"]["event_client"] == "unhealthy"
    
    @pytest.mark.asyncio
    async def test_readiness_check_ready(self):
        """Test readiness check when service is ready"""
        with patch('main.market_intelligence_agent') as mock_agent, \
             patch('main.trend_analyzer') as mock_trend:
            
            # Configure mocks as ready
            mock_agent.is_ready = True
            mock_trend.is_ready = True
            
            from main import readiness_check
            
            result = await readiness_check()
            
            assert result["status"] == "ready"
            assert result["service"] == "market-intelligence-service"
    
    @pytest.mark.asyncio
    async def test_readiness_check_not_ready(self):
        """Test readiness check when service is not ready"""
        with patch('main.market_intelligence_agent') as mock_agent, \
             patch('main.trend_analyzer') as mock_trend:
            
            # Configure mocks as not ready
            mock_agent = None
            mock_trend = None
            
            from main import readiness_check
            
            with pytest.raises(Exception):  # Should raise HTTPException
                await readiness_check()
    
    def test_metrics_endpoint_basic(self):
        """Test basic metrics endpoint"""
        with patch('main.metrics') as mock_metrics:
            mock_metrics.get_metrics.return_value = {"test_metric": 1}
            
            from main import get_metrics
            
            # This is a sync function, not async
            import asyncio
            result = asyncio.run(get_metrics())
            
            assert "test_metric" in result or "message" in result
    
    def test_async_job_management(self):
        """Test async job management functionality"""
        from main import async_jobs, AsyncJobStatus
        
        # Test adding job to storage
        job_id = "test-job-123"
        job = AsyncJobStatus(
            request_id=job_id,
            status="pending",
            progress=0,
            created_at=datetime.utcnow().isoformat(),
            updated_at=datetime.utcnow().isoformat()
        )
        
        async_jobs[job_id] = job
        
        # Test retrieval
        retrieved_job = async_jobs.get(job_id)
        assert retrieved_job is not None
        assert retrieved_job.request_id == job_id
        assert retrieved_job.status == "pending"
        
        # Clean up
        del async_jobs[job_id]
    
    def test_keyword_preparation_logic(self):
        """Test keyword preparation logic"""
        # Test the keyword preparation would work
        categories = ["business", "self-help"]
        keywords = ["productivity", "efficiency"]
        
        # Simple test that we can combine these
        combined = []
        if categories:
            combined.extend(categories)
        if keywords:
            combined.extend(keywords)
        
        assert len(combined) == 4
        assert "business" in combined
        assert "productivity" in combined
    
    def test_industry_mapping_logic(self):
        """Test industry to category mapping logic"""
        industry_mapping = {
            "health": ["health", "fitness", "nutrition", "wellness"],
            "wealth": ["business", "finance", "investing", "entrepreneurship"],
            "beauty": ["lifestyle", "self-help", "wellness"],
            "technology": ["technology", "business", "programming"]
        }
        
        # Test mapping functionality
        assert "health" in industry_mapping
        assert "fitness" in industry_mapping["health"]
        assert "business" in industry_mapping["wealth"]
        assert len(industry_mapping["technology"]) == 3
    
    def test_opportunity_scoring_logic(self):
        """Test opportunity scoring logic"""
        def calculate_basic_score(interest_level, growth_rate, competition):
            base_score = 0.5
            
            # Interest factor
            if interest_level > 70:
                base_score += 0.2
            elif interest_level > 50:
                base_score += 0.1
            
            # Growth factor
            if growth_rate > 20:
                base_score += 0.2
            elif growth_rate > 10:
                base_score += 0.1
            
            # Competition factor
            if competition == "low":
                base_score += 0.1
            elif competition == "high":
                base_score -= 0.1
            
            return min(max(base_score, 0.0), 1.0)
        
        # Test scoring
        high_score = calculate_basic_score(80, 25, "low")
        assert high_score > 0.8
        
        medium_score = calculate_basic_score(60, 15, "medium")
        assert 0.6 <= medium_score <= 0.8
        
        low_score = calculate_basic_score(30, -5, "high")
        assert low_score < 0.6
    
    def test_trend_categorization_logic(self):
        """Test trend categorization logic"""
        def categorize_trend(growth_rate):
            if growth_rate > 10:
                return "rising"
            elif growth_rate < -10:
                return "declining"
            else:
                return "stable"
        
        assert categorize_trend(25) == "rising"
        assert categorize_trend(-15) == "declining"
        assert categorize_trend(5) == "stable"
        assert categorize_trend(-5) == "stable"
    
    def test_timing_recommendation_logic(self):
        """Test timing recommendation logic"""
        def recommend_timing(growth_rate, interest_level):
            if growth_rate > 20:
                return "Immediate - Strong growth trend"
            elif growth_rate > 10:
                return "Short-term - Positive growth momentum"
            elif interest_level > 60:
                return "Medium-term - Stable market interest"
            else:
                return "Long-term - Monitor for growth signals"
        
        assert "Immediate" in recommend_timing(25, 50)
        assert "Short-term" in recommend_timing(15, 45)
        assert "Medium-term" in recommend_timing(5, 70)
        assert "Long-term" in recommend_timing(-5, 30)


if __name__ == "__main__":
    pytest.main([__file__])