# Market Intelligence Service Dependencies

# Web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Async support
httpx==0.25.0
aiofiles==23.2.1

# PydanticAI for agent functionality
pydantic-ai==0.0.13
pydantic==2.5.0

# AI model providers
openai==1.3.0
anthropic==0.7.7

# Google Trends integration
pytrends==4.9.2

# Web scraping and parsing
beautifulsoup4==4.12.2
lxml==4.9.3
selenium==4.15.2
scrapy==2.11.0

# Data processing
numpy==1.24.3
pandas==2.1.3
scipy==1.11.4

# Caching and storage
redis==5.0.1
joblib==1.3.2

# Service communication
httpx==0.25.0
websockets==12.0

# Monitoring and observability
prometheus-client==0.19.0
structlog==23.2.0

# Security
cryptography==41.0.7
python-jose[cryptography]==3.3.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
requests-mock==1.11.0

# Type checking
mypy==1.7.1
types-redis==*******
types-requests==*********

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0