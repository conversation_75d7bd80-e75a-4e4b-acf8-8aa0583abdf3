"""
Security Manager for Research Service
Handles API key authentication and mTLS configuration
"""

import os
import hashlib
import json
from typing import Optional, Dict, Any
import logging
from pathlib import Path
from fastapi import HTTP<PERSON>x<PERSON>, Header


class SecurityManager:
    """
    Security manager for handling API keys and mTLS configuration
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._api_key: Optional[str] = None
        self._service_name = "research-service"
        self._load_security_config()
    
    def _load_security_config(self):
        """Load security configuration"""
        try:
            # Load API key from environment or file
            self._api_key = os.getenv("API_KEY")
            
            if not self._api_key:
                # Try to load from security directory
                api_key_file = Path(__file__).parent.parent.parent.parent / "security" / "api-keys" / "keys" / f"{self._service_name}.env"
                if api_key_file.exists():
                    with open(api_key_file, 'r') as f:
                        for line in f:
                            if line.startswith('API_KEY='):
                                self._api_key = line.split('=', 1)[1].strip()
                                break
            
            if self._api_key:
                self.logger.info("🔑 API key loaded successfully")
            else:
                self.logger.warning("⚠️  No API key found, authentication disabled")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to load security configuration: {str(e)}")
    
    def get_api_key(self) -> Optional[str]:
        """Get the API key for this service"""
        return self._api_key
    
    def verify_api_key(self, provided_key: str) -> bool:
        """Verify an API key"""
        if not self._api_key:
            # If no API key is configured, allow access (development mode)
            return True
        
        try:
            # Hash both keys for comparison
            provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()
            expected_hash = hashlib.sha256(self._api_key.encode()).hexdigest()
            
            return provided_hash == expected_hash
            
        except Exception as e:
            self.logger.error(f"❌ API key verification failed: {str(e)}")
            return False
    
    def get_tls_config(self) -> Dict[str, str]:
        """Get TLS configuration for mTLS"""
        try:
            security_dir = Path(__file__).parent.parent.parent.parent / "security"
            service_certs_dir = security_dir / "certs" / "services" / self._service_name
            
            tls_config = {
                "cert_file": str(service_certs_dir / f"{self._service_name}.cert.pem"),
                "key_file": str(service_certs_dir / f"{self._service_name}.key.pem"),
                "ca_file": str(security_dir / "certs" / "ca-chain.pem"),
                "verify_mode": "required"
            }
            
            # Check if files exist
            for key, file_path in tls_config.items():
                if key.endswith('_file') and not Path(file_path).exists():
                    self.logger.warning(f"⚠️  TLS file not found: {file_path}")
                    return {}
            
            self.logger.info("🔒 TLS configuration loaded successfully")
            return tls_config
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load TLS configuration: {str(e)}")
            return {}
    
    def is_tls_enabled(self) -> bool:
        """Check if TLS is enabled"""
        return bool(self.get_tls_config())
    
    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP requests"""
        headers = {
            "X-Service-Name": self._service_name,
            "X-Service-Version": "1.0.0"
        }
        
        if self._api_key:
            headers["Authorization"] = f"Bearer {self._api_key}"
        
        return headers


# Global security manager instance
_security_manager = SecurityManager()


def verify_api_key(authorization: str = Header(None)) -> str:
    """
    FastAPI dependency for API key verification
    This function is used with FastAPI's Depends() decorator
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header is required")
    
    # Extract API key from Bearer token
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header format")
    
    api_key = authorization.split("Bearer ", 1)[1]
    
    if not _security_manager.verify_api_key(api_key):
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return api_key