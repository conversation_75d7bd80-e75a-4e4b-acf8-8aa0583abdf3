"""
Market Intelligence Service - Main Application

A comprehensive microservice providing AI-powered market intelligence, trend analysis,
and competitive research capabilities for the Publish AI platform.
"""

import asyncio
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import Fast<PERSON>I, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from market_intelligence_agent import MarketIntelligenceAgent, MarketAnalysisResult
from trend_analyzer import TrendAnalyzer, TrendAnalysisResult
from event_client import EventClient
from service_registry_client import ServiceRegistryClient
from security_manager import SecurityManager, verify_api_key
from monitoring import MetricsCollector, setup_logging


# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

# Global instances
market_intelligence_agent: Optional[MarketIntelligenceAgent] = None
trend_analyzer: Optional[TrendAnalyzer] = None
event_client: Optional[EventClient] = None
service_registry: Optional[ServiceRegistryClient] = None
security_manager: Optional[SecurityManager] = None
metrics: Optional[MetricsCollector] = None

# ============================================================================
# Request/Response Models
# ============================================================================

class MarketAnalysisRequest(BaseModel):
    """Request model for market analysis"""
    category: str = Field(..., description="Market category to analyze (e.g., 'self-help', 'business')")
    analysis_depth: str = Field(default="standard", description="Analysis depth: basic, standard, comprehensive")
    include_competitors: bool = Field(default=True, description="Include competitor analysis")
    include_trends: bool = Field(default=True, description="Include trend analysis")
    include_pricing: bool = Field(default=True, description="Include pricing analysis")
    force_refresh: bool = Field(default=False, description="Force refresh of cached data")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class TrendAnalysisRequest(BaseModel):
    """Request model for trend analysis"""
    categories: Optional[List[str]] = Field(default=None, description="Categories to analyze")
    keywords: Optional[List[str]] = Field(default=None, description="Specific keywords to analyze")
    timeframe: str = Field(default="today 3-m", description="Timeframe for trend analysis")
    geo: str = Field(default="US", description="Geographic region for analysis")
    analysis_type: str = Field(default="comprehensive", description="Type of analysis to perform")
    max_results: int = Field(default=50, description="Maximum number of results")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class OpportunityAnalysisRequest(BaseModel):
    """Request model for opportunity analysis"""
    industry_focus: List[str] = Field(..., description="Industries to focus on")
    target_audience: Optional[str] = Field(default=None, description="Target audience segment")
    budget_range: Optional[Dict[str, float]] = Field(default=None, description="Budget constraints")
    risk_tolerance: str = Field(default="medium", description="Risk tolerance: low, medium, high")
    time_horizon: str = Field(default="3_months", description="Time horizon for opportunities")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class CompetitorAnalysisRequest(BaseModel):
    """Request model for competitor analysis"""
    category: str = Field(..., description="Category to analyze")
    target_keywords: Optional[List[str]] = Field(default=None, description="Keywords to focus on")
    analysis_depth: str = Field(default="standard", description="Analysis depth")
    include_pricing: bool = Field(default=True, description="Include pricing analysis")
    include_performance: bool = Field(default=True, description="Include performance metrics")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")

class ServiceResponse(BaseModel):
    """Standard service response model"""
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Request status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Analysis results")
    message: str = Field(default="", description="Response message")
    execution_time: Optional[float] = Field(default=None, description="Execution time in seconds")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())

class AsyncJobStatus(BaseModel):
    """Model for async job status"""
    request_id: str
    status: str  # pending, running, completed, failed
    progress: Optional[int] = None  # 0-100
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: str
    updated_at: str

# ============================================================================
# Startup/Shutdown
# ============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Market Intelligence Service")
    
    global market_intelligence_agent, trend_analyzer, event_client
    global service_registry, security_manager, metrics
    
    try:
        # Initialize components
        metrics = MetricsCollector()
        security_manager = SecurityManager()
        
        # Initialize agents
        market_intelligence_agent = MarketIntelligenceAgent()
        await market_intelligence_agent.initialize()
        
        trend_analyzer = TrendAnalyzer()
        await trend_analyzer.initialize()
        
        # Initialize event client
        event_bus_url = os.getenv("EVENT_BUS_URL", "http://event-bus:8080")
        event_client = EventClient(event_bus_url)
        await event_client.connect()
        
        # Initialize service registry
        service_discovery_url = os.getenv("SERVICE_DISCOVERY_URL", "http://service-discovery:8070")
        service_registry = ServiceRegistryClient(service_discovery_url)
        
        # Register service
        service_info = {
            "name": "market-intelligence-service",
            "host": os.getenv("SERVICE_HOST", "0.0.0.0"),
            "port": int(os.getenv("SERVICE_PORT", "8085")),
            "health_endpoint": "/health",
            "capabilities": [
                "market_analysis",
                "trend_analysis", 
                "competitor_analysis",
                "opportunity_analysis"
            ],
            "version": "1.0.0"
        }
        
        await service_registry.register_service(service_info)
        logger.info("Market Intelligence Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start Market Intelligence Service: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down Market Intelligence Service")
    
    try:
        if market_intelligence_agent:
            await market_intelligence_agent.cleanup()
        if trend_analyzer:
            await trend_analyzer.cleanup()
        if event_client:
            await event_client.disconnect()
        if service_registry:
            await service_registry.unregister_service("market-intelligence-service")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# ============================================================================
# FastAPI Application
# ============================================================================

app = FastAPI(
    title="Market Intelligence Service",
    description="AI-powered market intelligence and trend analysis microservice",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for async jobs (in production, use Redis or database)
async_jobs: Dict[str, AsyncJobStatus] = {}

# ============================================================================
# Health & Monitoring Endpoints
# ============================================================================

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check agent availability
        agent_status = "healthy" if market_intelligence_agent and market_intelligence_agent.is_ready else "unhealthy"
        trend_status = "healthy" if trend_analyzer and trend_analyzer.is_ready else "unhealthy"
        
        # Check external dependencies
        event_status = "healthy" if event_client and event_client.is_connected else "unhealthy"
        
        overall_status = "healthy" if all([
            agent_status == "healthy",
            trend_status == "healthy",
            event_status == "healthy"
        ]) else "unhealthy"
        
        return {
            "status": overall_status,
            "service": "market-intelligence-service",
            "version": "1.0.0",
            "components": {
                "market_intelligence_agent": agent_status,
                "trend_analyzer": trend_status,
                "event_client": event_status,
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    try:
        if not market_intelligence_agent or not market_intelligence_agent.is_ready:
            raise HTTPException(status_code=503, detail="Market Intelligence Agent not ready")
        
        if not trend_analyzer or not trend_analyzer.is_ready:
            raise HTTPException(status_code=503, detail="Trend Analyzer not ready")
        
        return {"status": "ready", "service": "market-intelligence-service"}
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    try:
        if metrics:
            return metrics.get_metrics()
        return {"message": "Metrics not available"}
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        return {"error": str(e)}

# ============================================================================
# Market Analysis Endpoints
# ============================================================================

@app.post("/analyze", response_model=ServiceResponse)
async def analyze_market(
    request: MarketAnalysisRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Perform asynchronous market analysis"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting market analysis for category: {request.category}")
        
        # Create async job
        job = AsyncJobStatus(
            request_id=request_id,
            status="pending",
            progress=0,
            created_at=start_time.isoformat(),
            updated_at=start_time.isoformat()
        )
        async_jobs[request_id] = job
        
        # Start background task
        asyncio.create_task(_execute_market_analysis(request_id, request))
        
        # Record metrics
        if metrics:
            metrics.record_market_analysis_request(request.category, request.analysis_depth)
        
        return ServiceResponse(
            request_id=request_id,
            status="accepted",
            message=f"Market analysis started for category: {request.category}",
            timestamp=start_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Market analysis request failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_market_analysis_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Market analysis failed: {str(e)}"
        )

@app.get("/analyze/{request_id}", response_model=ServiceResponse)
async def get_analysis_status(
    request_id: str,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Get market analysis status and results"""
    try:
        if request_id not in async_jobs:
            raise HTTPException(status_code=404, detail="Request not found")
        
        job = async_jobs[request_id]
        
        return ServiceResponse(
            request_id=request_id,
            status=job.status,
            result=job.result,
            message=job.error_message if job.status == "failed" else "Analysis status retrieved",
            timestamp=job.updated_at
        )
        
    except Exception as e:
        logger.error(f"Failed to get analysis status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze/sync", response_model=ServiceResponse)
async def analyze_market_sync(
    request: MarketAnalysisRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Perform synchronous market analysis"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting synchronous market analysis for category: {request.category}")
        
        if not market_intelligence_agent:
            raise HTTPException(status_code=503, detail="Market Intelligence Agent not available")
        
        # Execute analysis
        result = await market_intelligence_agent.analyze_market(
            category=request.category,
            analysis_depth=request.analysis_depth,
            include_competitors=request.include_competitors,
            include_trends=request.include_trends,
            include_pricing=request.include_pricing,
            force_refresh=request.force_refresh
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "market_analysis_completed",
                {
                    "request_id": request_id,
                    "category": request.category,
                    "analysis_depth": request.analysis_depth,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_market_analysis_completed(request.category, execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result.model_dump() if result else None,
            message="Market analysis completed successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Synchronous market analysis failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_market_analysis_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Market analysis failed: {str(e)}"
        )

# ============================================================================
# Trend Analysis Endpoints
# ============================================================================

@app.post("/trends", response_model=ServiceResponse)
async def analyze_trends(
    request: TrendAnalysisRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Perform trend analysis"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting trend analysis for categories: {request.categories}")
        
        if not trend_analyzer:
            raise HTTPException(status_code=503, detail="Trend Analyzer not available")
        
        # Execute trend analysis
        result = await trend_analyzer.analyze_trends(
            categories=request.categories,
            keywords=request.keywords,
            timeframe=request.timeframe,
            geo=request.geo,
            analysis_type=request.analysis_type,
            max_results=request.max_results
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "trend_analysis_completed",
                {
                    "request_id": request_id,
                    "categories": request.categories,
                    "keywords": request.keywords,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_trend_analysis_completed(execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result.model_dump() if result else None,
            message="Trend analysis completed successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Trend analysis failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_trend_analysis_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Trend analysis failed: {str(e)}"
        )

# ============================================================================
# Opportunity Analysis Endpoints
# ============================================================================

@app.post("/opportunities", response_model=ServiceResponse)
async def analyze_opportunities(
    request: OpportunityAnalysisRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Analyze market opportunities"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting opportunity analysis for industries: {request.industry_focus}")
        
        if not market_intelligence_agent:
            raise HTTPException(status_code=503, detail="Market Intelligence Agent not available")
        
        # Execute opportunity analysis
        result = await market_intelligence_agent.analyze_opportunities(
            industry_focus=request.industry_focus,
            target_audience=request.target_audience,
            budget_range=request.budget_range,
            risk_tolerance=request.risk_tolerance,
            time_horizon=request.time_horizon
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "opportunity_analysis_completed",
                {
                    "request_id": request_id,
                    "industry_focus": request.industry_focus,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_opportunity_analysis_completed(execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result,
            message="Opportunity analysis completed successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Opportunity analysis failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_opportunity_analysis_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Opportunity analysis failed: {str(e)}"
        )

# ============================================================================
# Competitor Analysis Endpoints
# ============================================================================

@app.post("/competitors", response_model=ServiceResponse)
async def analyze_competitors(
    request: CompetitorAnalysisRequest,
    api_key: str = Depends(verify_api_key)
) -> ServiceResponse:
    """Analyze competitors in a category"""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()
    
    try:
        logger.info(f"Starting competitor analysis for category: {request.category}")
        
        if not market_intelligence_agent:
            raise HTTPException(status_code=503, detail="Market Intelligence Agent not available")
        
        # Execute competitor analysis
        result = await market_intelligence_agent.analyze_competitors(
            category=request.category,
            target_keywords=request.target_keywords,
            analysis_depth=request.analysis_depth,
            include_pricing=request.include_pricing,
            include_performance=request.include_performance
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Publish event
        if event_client:
            await event_client.publish_event(
                "competitor_analysis_completed",
                {
                    "request_id": request_id,
                    "category": request.category,
                    "execution_time": execution_time,
                    "user_id": request.user_id
                }
            )
        
        # Record metrics
        if metrics:
            metrics.record_competitor_analysis_completed(request.category, execution_time)
        
        return ServiceResponse(
            request_id=request_id,
            status="completed",
            result=result,
            message="Competitor analysis completed successfully",
            execution_time=execution_time,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Competitor analysis failed: {e}")
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        if metrics:
            metrics.record_competitor_analysis_error(str(e))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Competitor analysis failed: {str(e)}"
        )

# ============================================================================
# Background Task Functions
# ============================================================================

async def _execute_market_analysis(request_id: str, request: MarketAnalysisRequest):
    """Execute market analysis in background"""
    try:
        # Update job status
        if request_id in async_jobs:
            async_jobs[request_id].status = "running"
            async_jobs[request_id].progress = 0
            async_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        if not market_intelligence_agent:
            raise Exception("Market Intelligence Agent not available")
        
        # Execute analysis with progress updates
        result = await market_intelligence_agent.analyze_market(
            category=request.category,
            analysis_depth=request.analysis_depth,
            include_competitors=request.include_competitors,
            include_trends=request.include_trends,
            include_pricing=request.include_pricing,
            force_refresh=request.force_refresh
        )
        
        # Update job status - completed
        if request_id in async_jobs:
            async_jobs[request_id].status = "completed"
            async_jobs[request_id].progress = 100
            async_jobs[request_id].result = result.model_dump() if result else None
            async_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish completion event
        if event_client:
            await event_client.publish_event(
                "market_analysis_completed",
                {
                    "request_id": request_id,
                    "category": request.category,
                    "user_id": request.user_id,
                    "status": "completed"
                }
            )
        
    except Exception as e:
        logger.error(f"Background market analysis failed: {e}")
        
        # Update job status - failed
        if request_id in async_jobs:
            async_jobs[request_id].status = "failed"
            async_jobs[request_id].error_message = str(e)
            async_jobs[request_id].updated_at = datetime.utcnow().isoformat()
        
        # Publish failure event
        if event_client:
            await event_client.publish_event(
                "market_analysis_failed",
                {
                    "request_id": request_id,
                    "category": request.category,
                    "user_id": request.user_id,
                    "error": str(e)
                }
            )

# ============================================================================
# Application Entry Point
# ============================================================================

if __name__ == "__main__":
    port = int(os.getenv("SERVICE_PORT", "8085"))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )