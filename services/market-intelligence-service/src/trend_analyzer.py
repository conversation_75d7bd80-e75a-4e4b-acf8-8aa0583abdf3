"""
Trend Analyzer

Google Trends integration and trend analysis for market intelligence.
Provides real-time trend data and analysis for publishing opportunities.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import httpx
from pydantic import BaseModel, Field
from pytrends.request import TrendReq

logger = logging.getLogger(__name__)

# ============================================================================
# Response Models
# ============================================================================

class TrendMetric(BaseModel):
    """Individual trend metric"""
    keyword: str = Field(..., description="Trending keyword")
    interest_score: int = Field(..., description="Interest score (0-100)")
    growth_rate: float = Field(..., description="Growth rate percentage")
    peak_date: Optional[str] = Field(default=None, description="Peak interest date")
    regional_interest: Dict[str, int] = Field(default_factory=dict, description="Regional interest breakdown")

class TrendOpportunity(BaseModel):
    """Trend-based opportunity"""
    keyword: str = Field(..., description="Opportunity keyword")
    opportunity_score: float = Field(..., description="Opportunity score (0-1)")
    market_potential: str = Field(..., description="Market potential assessment")
    competition_level: str = Field(..., description="Competition level")
    recommended_timing: str = Field(..., description="Recommended timing for entry")
    content_suggestions: List[str] = Field(..., description="Content creation suggestions")

class TrendAnalysisResult(BaseModel):
    """Complete trend analysis result"""
    analysis_date: str = Field(..., description="Analysis completion date")
    timeframe: str = Field(..., description="Analysis timeframe")
    geographic_region: str = Field(..., description="Geographic region analyzed")
    
    # Trend Metrics
    trending_keywords: List[TrendMetric] = Field(..., description="Trending keywords analysis")
    rising_trends: List[str] = Field(..., description="Rising trend keywords")
    declining_trends: List[str] = Field(..., description="Declining trend keywords")
    stable_trends: List[str] = Field(..., description="Stable trend keywords")
    
    # Opportunities
    opportunities: List[TrendOpportunity] = Field(..., description="Identified opportunities")
    niche_opportunities: List[str] = Field(..., description="Niche market opportunities")
    
    # Market Intelligence
    seasonal_patterns: Dict[str, Any] = Field(..., description="Seasonal pattern analysis")
    competitive_landscape: Dict[str, Any] = Field(..., description="Competitive analysis")
    
    # Recommendations
    immediate_opportunities: List[str] = Field(..., description="Immediate action opportunities")
    watchlist: List[str] = Field(..., description="Trends to monitor")
    content_gaps: List[str] = Field(..., description="Content gap opportunities")
    
    # Metadata
    confidence_score: float = Field(..., description="Analysis confidence (0-1)")
    data_sources: List[str] = Field(..., description="Data sources used")

# ============================================================================
# Trend Analyzer
# ============================================================================

class TrendAnalyzer:
    """
    Google Trends integration and trend analysis engine for market intelligence.
    Provides real-time trend data and identifies publishing opportunities.
    """
    
    def __init__(self):
        self.pytrends: Optional[TrendReq] = None
        self._initialized = False
        self.cache_duration = timedelta(hours=2)  # Cache trends for 2 hours
        self._cache: Dict[str, Any] = {}
        
        # Industry-specific keyword mappings
        self.industry_keywords = {
            "health": ["wellness", "fitness", "nutrition", "mental health", "healthcare", "medical"],
            "wealth": ["finance", "investing", "money", "business", "entrepreneurship", "passive income"],
            "beauty": ["skincare", "cosmetics", "beauty tips", "fashion", "style", "appearance"],
            "technology": ["ai", "software", "programming", "tech", "digital", "innovation"],
            "business": ["marketing", "sales", "leadership", "management", "startup", "growth"],
            "fitness": ["workout", "exercise", "training", "bodybuilding", "yoga", "sports"],
            "nutrition": ["diet", "healthy eating", "supplements", "weight loss", "recipes", "food"],
            "self-help": ["motivation", "productivity", "personal growth", "mindset", "success", "habits"],
            "finance": ["investing", "trading", "cryptocurrency", "real estate", "retirement", "budgeting"],
            "real estate": ["property", "housing", "investment", "rental", "mortgage", "flipping"],
            "marketing": ["digital marketing", "social media", "advertising", "branding", "seo", "content"],
            "education": ["learning", "skills", "training", "courses", "teaching", "knowledge"],
            "relationships": ["dating", "marriage", "communication", "love", "family", "social"],
            "lifestyle": ["travel", "home", "organization", "minimalism", "happiness", "work-life"],
            "travel": ["destinations", "adventure", "culture", "hotels", "vacation", "backpacking"],
            "food": ["cooking", "recipes", "restaurant", "cuisine", "baking", "gourmet"],
            "fashion": ["style", "clothing", "trends", "accessories", "designer", "wardrobe"],
            "entertainment": ["movies", "music", "gaming", "tv shows", "celebrities", "streaming"],
            "sports": ["football", "basketball", "soccer", "baseball", "athletics", "competition"],
            "science": ["research", "discovery", "physics", "biology", "chemistry", "innovation"]
        }
        
        # Book category mappings
        self.book_categories = {
            "self-help": ["self improvement", "personal development", "productivity", "mindfulness", "motivation"],
            "business": ["entrepreneurship", "leadership", "marketing", "finance", "startup"],
            "health": ["fitness", "nutrition", "wellness", "mental health", "weight loss"],
            "technology": ["artificial intelligence", "programming", "tech career", "coding", "software"],
            "romance": ["romance novel", "dating advice", "relationships", "love stories"],
            "mystery": ["mystery novel", "crime fiction", "detective stories", "thriller books"],
            "finance": ["investing", "financial planning", "money management", "trading", "cryptocurrency"],
            "cooking": ["recipes", "culinary skills", "food", "cooking techniques", "baking"],
            "travel": ["travel guide", "destinations", "travel tips", "adventure", "culture"],
            "parenting": ["child development", "parenting tips", "family", "kids", "education"],
            "history": ["historical events", "biography", "historical fiction", "ancient history"],
            "science": ["popular science", "research", "discovery", "physics", "biology"]
        }
    
    @property
    def is_ready(self) -> bool:
        """Check if analyzer is ready for operations"""
        return self._initialized and self.pytrends is not None
    
    async def initialize(self):
        """Initialize the trend analyzer"""
        try:
            logger.info("Initializing Trend Analyzer")
            
            # Initialize PyTrends with retry mechanism
            await self._initialize_pytrends()
            
            self._initialized = True
            logger.info("Trend Analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Trend Analyzer: {e}")
            raise
    
    async def _initialize_pytrends(self, retries: int = 3):
        """Initialize PyTrends with retry mechanism"""
        for attempt in range(retries):
            try:
                self.pytrends = TrendReq(hl='en-US', tz=360, timeout=(10, 25), retries=2, backoff_factor=0.1)
                # Test connection with a simple query
                await asyncio.to_thread(self._test_pytrends_connection)
                return
            except Exception as e:
                logger.warning(f"PyTrends initialization attempt {attempt + 1} failed: {e}")
                if attempt == retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
    
    def _test_pytrends_connection(self):
        """Test PyTrends connection"""
        try:
            # Simple test query
            self.pytrends.build_payload(['test'], cat=0, timeframe='today 7-d', geo='US', gprop='')
            return True
        except Exception as e:
            raise Exception(f"PyTrends connection test failed: {e}")
    
    async def analyze_trends(
        self,
        categories: Optional[List[str]] = None,
        keywords: Optional[List[str]] = None,
        timeframe: str = "today 3-m",
        geo: str = "US",
        analysis_type: str = "comprehensive",
        max_results: int = 50
    ) -> TrendAnalysisResult:
        """
        Analyze trends for specified categories and keywords
        
        Args:
            categories: Book categories to analyze
            keywords: Specific keywords to analyze
            timeframe: Time period for analysis
            geo: Geographic region
            analysis_type: Type of analysis (basic, standard, comprehensive)
            max_results: Maximum number of results
        """
        if not self.is_ready:
            raise RuntimeError("Trend Analyzer not initialized")
        
        cache_key = f"trends_{hash(str(categories))}_{hash(str(keywords))}_{timeframe}_{geo}"
        
        # Check cache first
        if cache_key in self._cache:
            cached_result = self._cache[cache_key]
            cache_time = cached_result.get("timestamp")
            if cache_time and datetime.fromisoformat(cache_time) > datetime.utcnow() - self.cache_duration:
                logger.info("Returning cached trend analysis")
                return TrendAnalysisResult(**cached_result["data"])
        
        try:
            logger.info(f"Analyzing trends for categories: {categories}, keywords: {keywords}")
            
            # Prepare keywords for analysis
            analysis_keywords = self._prepare_keywords(categories, keywords)
            
            # Get trend data
            trend_data = await self._get_trend_data(analysis_keywords, timeframe, geo)
            
            # Analyze opportunities
            opportunities = await self._analyze_opportunities(trend_data, categories)
            
            # Build result
            result = await self._build_trend_result(
                trend_data=trend_data,
                opportunities=opportunities,
                timeframe=timeframe,
                geo=geo,
                analysis_type=analysis_type
            )
            
            # Cache result
            self._cache[cache_key] = {
                "data": result.model_dump(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            raise
    
    def _prepare_keywords(
        self,
        categories: Optional[List[str]],
        keywords: Optional[List[str]]
    ) -> List[str]:
        """Prepare keywords for trend analysis"""
        analysis_keywords = []
        
        # Add category-specific keywords
        if categories:
            for category in categories:
                category_keywords = self.book_categories.get(category.lower(), [])
                analysis_keywords.extend(category_keywords[:3])  # Top 3 from each category
        
        # Add explicit keywords
        if keywords:
            analysis_keywords.extend(keywords)
        
        # Default keywords if none provided
        if not analysis_keywords:
            analysis_keywords = ["self improvement", "business", "health", "productivity", "finance"]
        
        # Remove duplicates and limit to 5 keywords (PyTrends limit)
        unique_keywords = list(dict.fromkeys(analysis_keywords))
        return unique_keywords[:5]
    
    async def _get_trend_data(
        self,
        keywords: List[str],
        timeframe: str,
        geo: str
    ) -> Dict[str, Any]:
        """Get trend data from Google Trends"""
        try:
            # Build payload and get data in thread pool
            trend_data = await asyncio.to_thread(
                self._execute_pytrends_query, keywords, timeframe, geo
            )
            
            return trend_data
            
        except Exception as e:
            logger.error(f"Failed to get trend data: {e}")
            # Return mock data as fallback
            return self._get_mock_trend_data(keywords, timeframe, geo)
    
    def _execute_pytrends_query(
        self,
        keywords: List[str],
        timeframe: str,
        geo: str
    ) -> Dict[str, Any]:
        """Execute PyTrends query (runs in thread pool)"""
        try:
            # Build payload
            self.pytrends.build_payload(keywords, cat=0, timeframe=timeframe, geo=geo, gprop='')
            
            # Get interest over time
            interest_over_time = self.pytrends.interest_over_time()
            
            # Get related queries
            related_queries = self.pytrends.related_queries()
            
            # Get trending searches
            try:
                trending_searches = self.pytrends.trending_searches(pn='united_states')
            except:
                trending_searches = None
            
            # Format data
            trend_data = {
                "keywords": keywords,
                "timeframe": timeframe,
                "geo": geo,
                "interest_over_time": (
                    interest_over_time.to_dict() if not interest_over_time.empty else {}
                ),
                "related_queries": related_queries,
                "trending_searches": (
                    trending_searches.head(20).values.flatten().tolist()
                    if trending_searches is not None
                    else []
                ),
                "peak_popularity": {},
                "average_interest": {},
                "growth_rates": {}
            }
            
            # Calculate metrics
            if not interest_over_time.empty:
                for keyword in keywords:
                    if keyword in interest_over_time.columns:
                        series = interest_over_time[keyword]
                        trend_data["peak_popularity"][keyword] = int(series.max())
                        trend_data["average_interest"][keyword] = float(series.mean())
                        
                        # Calculate growth rate (last month vs previous month)
                        if len(series) >= 8:  # At least 8 weeks of data
                            recent = series[-4:].mean()  # Last 4 weeks
                            previous = series[-8:-4].mean()  # Previous 4 weeks
                            if previous > 0:
                                growth_rate = ((recent - previous) / previous) * 100
                                trend_data["growth_rates"][keyword] = round(growth_rate, 2)
            
            return trend_data
            
        except Exception as e:
            logger.error(f"PyTrends query failed: {e}")
            raise
    
    def _get_mock_trend_data(self, keywords: List[str], timeframe: str, geo: str) -> Dict[str, Any]:
        """Get mock trend data as fallback"""
        return {
            "keywords": keywords,
            "timeframe": timeframe,
            "geo": geo,
            "interest_over_time": {},
            "related_queries": {},
            "trending_searches": [
                "AI productivity tools",
                "remote work tips",
                "cryptocurrency investing",
                "healthy meal prep",
                "side hustle ideas"
            ],
            "peak_popularity": {keyword: 75 for keyword in keywords},
            "average_interest": {keyword: 65 for keyword in keywords},
            "growth_rates": {keyword: 15.5 for keyword in keywords}
        }
    
    async def _analyze_opportunities(
        self,
        trend_data: Dict[str, Any],
        categories: Optional[List[str]]
    ) -> List[TrendOpportunity]:
        """Analyze trend data for opportunities"""
        opportunities = []
        
        # Analyze trending searches
        trending_searches = trend_data.get("trending_searches", [])
        for search_term in trending_searches[:10]:  # Top 10
            opportunity_score = self._calculate_opportunity_score(search_term, trend_data)
            
            if opportunity_score > 0.5:  # Only include viable opportunities
                opportunity = TrendOpportunity(
                    keyword=search_term,
                    opportunity_score=opportunity_score,
                    market_potential=self._assess_market_potential(search_term, trend_data),
                    competition_level=self._assess_competition_level(search_term),
                    recommended_timing=self._recommend_timing(search_term, trend_data),
                    content_suggestions=self._generate_content_suggestions(search_term, categories)
                )
                opportunities.append(opportunity)
        
        # Analyze main keywords
        for keyword in trend_data.get("keywords", []):
            opportunity_score = self._calculate_opportunity_score(keyword, trend_data)
            
            if opportunity_score > 0.4:  # Lower threshold for main keywords
                opportunity = TrendOpportunity(
                    keyword=keyword,
                    opportunity_score=opportunity_score,
                    market_potential=self._assess_market_potential(keyword, trend_data),
                    competition_level=self._assess_competition_level(keyword),
                    recommended_timing=self._recommend_timing(keyword, trend_data),
                    content_suggestions=self._generate_content_suggestions(keyword, categories)
                )
                opportunities.append(opportunity)
        
        # Sort by opportunity score
        opportunities.sort(key=lambda x: x.opportunity_score, reverse=True)
        
        return opportunities[:15]  # Top 15 opportunities
    
    def _calculate_opportunity_score(self, keyword: str, trend_data: Dict[str, Any]) -> float:
        """Calculate opportunity score for a keyword"""
        score = 0.5  # Base score
        
        # Interest level factor
        avg_interest = trend_data.get("average_interest", {}).get(keyword, 50)
        interest_factor = min(avg_interest / 100, 1.0)
        score += interest_factor * 0.3
        
        # Growth rate factor
        growth_rate = trend_data.get("growth_rates", {}).get(keyword, 0)
        if growth_rate > 20:
            score += 0.2
        elif growth_rate > 10:
            score += 0.1
        elif growth_rate < -10:
            score -= 0.1
        
        # Peak popularity factor
        peak = trend_data.get("peak_popularity", {}).get(keyword, 50)
        if peak > 80:
            score += 0.15
        elif peak > 60:
            score += 0.1
        
        # Keyword quality factors
        if len(keyword.split()) > 1:  # Multi-word keywords often better
            score += 0.05
        
        if any(word in keyword.lower() for word in ["how to", "guide", "tips", "strategies"]):
            score += 0.1  # Instructional content performs well
        
        return min(max(score, 0.0), 1.0)
    
    def _assess_market_potential(self, keyword: str, trend_data: Dict[str, Any]) -> str:
        """Assess market potential for a keyword"""
        avg_interest = trend_data.get("average_interest", {}).get(keyword, 50)
        growth_rate = trend_data.get("growth_rates", {}).get(keyword, 0)
        
        if avg_interest > 70 and growth_rate > 15:
            return "High - Strong interest with positive growth"
        elif avg_interest > 50 and growth_rate > 0:
            return "Medium - Moderate interest with stable growth"
        elif avg_interest > 30:
            return "Medium - Decent interest level"
        else:
            return "Low - Limited market interest"
    
    def _assess_competition_level(self, keyword: str) -> str:
        """Assess competition level for a keyword"""
        # Simple heuristics - could be enhanced with actual competition data
        common_high_competition = ["business", "finance", "marketing", "health", "fitness"]
        common_medium_competition = ["productivity", "self-help", "cooking", "travel"]
        
        keyword_lower = keyword.lower()
        
        if any(term in keyword_lower for term in common_high_competition):
            return "High"
        elif any(term in keyword_lower for term in common_medium_competition):
            return "Medium"
        else:
            return "Low"
    
    def _recommend_timing(self, keyword: str, trend_data: Dict[str, Any]) -> str:
        """Recommend timing for entering the market"""
        growth_rate = trend_data.get("growth_rates", {}).get(keyword, 0)
        avg_interest = trend_data.get("average_interest", {}).get(keyword, 50)
        
        if growth_rate > 20:
            return "Immediate - Strong growth trend"
        elif growth_rate > 10:
            return "Short-term - Positive growth momentum"
        elif avg_interest > 60:
            return "Medium-term - Stable market interest"
        else:
            return "Long-term - Monitor for growth signals"
    
    def _generate_content_suggestions(
        self,
        keyword: str,
        categories: Optional[List[str]]
    ) -> List[str]:
        """Generate content suggestions for a keyword"""
        suggestions = []
        
        # Generic suggestions based on keyword
        suggestions.extend([
            f"Beginner's Guide to {keyword.title()}",
            f"Advanced {keyword.title()} Strategies",
            f"Common {keyword.title()} Mistakes to Avoid"
        ])
        
        # Category-specific suggestions
        if categories:
            for category in categories:
                if category.lower() == "self-help":
                    suggestions.append(f"Personal Development Through {keyword.title()}")
                elif category.lower() == "business":
                    suggestions.append(f"Business Applications of {keyword.title()}")
                elif category.lower() == "health":
                    suggestions.append(f"Health Benefits of {keyword.title()}")
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    async def _build_trend_result(
        self,
        trend_data: Dict[str, Any],
        opportunities: List[TrendOpportunity],
        timeframe: str,
        geo: str,
        analysis_type: str
    ) -> TrendAnalysisResult:
        """Build comprehensive trend analysis result"""
        
        # Build trend metrics
        trending_keywords = []
        rising_trends = []
        declining_trends = []
        stable_trends = []
        
        for keyword in trend_data.get("keywords", []):
            growth_rate = trend_data.get("growth_rates", {}).get(keyword, 0)
            interest_score = trend_data.get("average_interest", {}).get(keyword, 50)
            peak_date = None  # Could be extracted from time series data
            
            # Categorize trends
            if growth_rate > 10:
                rising_trends.append(keyword)
            elif growth_rate < -10:
                declining_trends.append(keyword)
            else:
                stable_trends.append(keyword)
            
            # Create trend metric
            trend_metric = TrendMetric(
                keyword=keyword,
                interest_score=int(interest_score),
                growth_rate=growth_rate,
                peak_date=peak_date,
                regional_interest={geo: int(interest_score)}
            )
            trending_keywords.append(trend_metric)
        
        # Extract immediate opportunities and watchlist
        immediate_opportunities = [
            opp.keyword for opp in opportunities 
            if opp.recommended_timing.startswith("Immediate")
        ]
        
        watchlist = [
            opp.keyword for opp in opportunities 
            if "monitor" in opp.recommended_timing.lower()
        ]
        
        # Identify content gaps
        content_gaps = []
        for opp in opportunities:
            if opp.competition_level == "Low" and opp.opportunity_score > 0.6:
                content_gaps.append(f"{opp.keyword} - {opp.market_potential}")
        
        # Build result
        result = TrendAnalysisResult(
            analysis_date=datetime.utcnow().isoformat(),
            timeframe=timeframe,
            geographic_region=geo,
            trending_keywords=trending_keywords,
            rising_trends=rising_trends,
            declining_trends=declining_trends,
            stable_trends=stable_trends,
            opportunities=opportunities,
            niche_opportunities=trend_data.get("trending_searches", [])[:5],
            seasonal_patterns={"current_season": "standard"},  # Could be enhanced
            competitive_landscape={"analysis": "based_on_trend_data"},  # Could be enhanced
            immediate_opportunities=immediate_opportunities,
            watchlist=watchlist,
            content_gaps=content_gaps,
            confidence_score=0.85,  # Could be calculated based on data quality
            data_sources=["Google Trends", "PyTrends API"]
        )
        
        return result
    
    async def cleanup(self):
        """Cleanup analyzer resources"""
        try:
            logger.info("Cleaning up Trend Analyzer")
            self._initialized = False
            self.pytrends = None
            self._cache.clear()
            logger.info("Trend Analyzer cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")