"""
Market Intelligence Agent

PydanticAI-powered agent for comprehensive market analysis, trend identification,
and competitive intelligence for the publishing industry.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel

logger = logging.getLogger(__name__)

# ============================================================================
# Response Models
# ============================================================================

class MarketOpportunity(BaseModel):
    """Market opportunity identification"""
    category: str = Field(..., description="Market category")
    opportunity_score: float = Field(..., description="Opportunity score (0-1)")
    market_size: str = Field(..., description="Estimated market size")
    competition_level: str = Field(..., description="Competition level: low, medium, high")
    entry_barriers: List[str] = Field(..., description="Barriers to entry")
    growth_potential: str = Field(..., description="Growth potential assessment")
    recommended_approach: str = Field(..., description="Recommended market entry approach")

class CompetitorAnalysis(BaseModel):
    """Competitor analysis results"""
    category: str = Field(..., description="Market category analyzed")
    top_competitors: List[Dict[str, Any]] = Field(..., description="Top competitors identified")
    market_leaders: List[Dict[str, Any]] = Field(..., description="Market leaders analysis")
    pricing_analysis: Dict[str, Any] = Field(..., description="Pricing landscape analysis")
    content_gaps: List[str] = Field(..., description="Identified content gaps")
    differentiation_opportunities: List[str] = Field(..., description="Differentiation opportunities")

class TrendInsight(BaseModel):
    """Trend analysis insight"""
    trend_name: str = Field(..., description="Name of the trend")
    trend_score: float = Field(..., description="Trend strength score (0-1)")
    growth_trajectory: str = Field(..., description="Growth trajectory: rising, stable, declining")
    market_relevance: str = Field(..., description="Relevance to publishing market")
    opportunity_window: str = Field(..., description="Opportunity time window")
    recommended_action: str = Field(..., description="Recommended action")

class MarketAnalysisResult(BaseModel):
    """Complete market analysis result"""
    category: str = Field(..., description="Analyzed category")
    analysis_date: str = Field(..., description="Analysis completion date")
    
    # Market Overview
    market_size: Dict[str, Any] = Field(..., description="Market size analysis")
    growth_rate: float = Field(..., description="Market growth rate")
    saturation_level: float = Field(..., description="Market saturation (0-1)")
    
    # Competition Analysis
    competition_density: float = Field(..., description="Competition density (0-1)")
    market_leaders: List[Dict[str, Any]] = Field(..., description="Market leaders")
    entry_barriers: Dict[str, Any] = Field(..., description="Entry barrier analysis")
    
    # Opportunity Analysis
    opportunities: List[MarketOpportunity] = Field(..., description="Identified opportunities")
    threat_level: float = Field(..., description="Overall threat level (0-1)")
    
    # Trend Analysis
    trending_topics: List[TrendInsight] = Field(..., description="Trending topics")
    emerging_niches: List[str] = Field(..., description="Emerging niche opportunities")
    declining_areas: List[str] = Field(..., description="Declining market areas")
    
    # Pricing Intelligence
    pricing_landscape: Dict[str, Any] = Field(..., description="Pricing analysis")
    optimal_pricing: Dict[str, Any] = Field(..., description="Optimal pricing recommendations")
    
    # Strategic Recommendations
    recommendations: List[Dict[str, Any]] = Field(..., description="Strategic recommendations")
    risk_assessment: Dict[str, Any] = Field(..., description="Risk assessment")
    
    # Metadata
    confidence_score: float = Field(..., description="Analysis confidence (0-1)")
    data_sources: List[str] = Field(..., description="Data sources used")
    analysis_depth: str = Field(..., description="Analysis depth performed")

# ============================================================================
# Market Intelligence Agent
# ============================================================================

class MarketIntelligenceAgent:
    """
    AI-powered market intelligence agent providing comprehensive market analysis,
    trend identification, and competitive research for publishing opportunities.
    """
    
    def __init__(self):
        self.agent: Optional[Agent] = None
        self.trend_agent: Optional[Agent] = None
        self.competitor_agent: Optional[Agent] = None
        self._initialized = False
        self.cache_duration = timedelta(hours=6)
        self._cache: Dict[str, Any] = {}
        
    @property
    def is_ready(self) -> bool:
        """Check if agent is ready for operations"""
        return self._initialized and self.agent is not None
    
    async def initialize(self):
        """Initialize the market intelligence agent"""
        try:
            logger.info("Initializing Market Intelligence Agent")
            
            # Get the preferred model
            model = self._get_available_model()
            if not model:
                raise Exception("No AI model available for Market Intelligence Agent")
            
            # Initialize main market analysis agent
            self.agent = Agent(
                model=model,
                result_type=MarketAnalysisResult,
                system_prompt=self._get_market_analysis_prompt()
            )
            
            # Initialize trend analysis agent
            self.trend_agent = Agent(
                model=model,
                result_type=List[TrendInsight],
                system_prompt=self._get_trend_analysis_prompt()
            )
            
            # Initialize competitor analysis agent
            self.competitor_agent = Agent(
                model=model,
                result_type=CompetitorAnalysis,
                system_prompt=self._get_competitor_analysis_prompt()
            )
            
            self._initialized = True
            logger.info("Market Intelligence Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Market Intelligence Agent: {e}")
            raise
    
    def _get_available_model(self) -> Optional[Union[OpenAIModel, AnthropicModel]]:
        """Get available AI model based on configuration"""
        preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
        
        if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        elif os.getenv("OPENAI_API_KEY"):
            return OpenAIModel("gpt-4")
        elif os.getenv("ANTHROPIC_API_KEY"):
            return AnthropicModel("claude-3-sonnet-20240229")
        
        return None
    
    def _get_market_analysis_prompt(self) -> str:
        """Get system prompt for market analysis"""
        return """
        You are an expert market intelligence analyst specializing in the publishing industry,
        particularly digital books, e-books, and online content markets.
        
        Your expertise includes:
        - Market size estimation and growth analysis
        - Competition density assessment
        - Opportunity identification and scoring
        - Trend analysis and forecasting
        - Pricing strategy and optimization
        - Risk assessment and mitigation
        - Strategic recommendations
        
        When analyzing markets, provide:
        1. Comprehensive market overview with size and growth metrics
        2. Detailed competition analysis including key players and market share
        3. Opportunity scoring based on market potential, competition, and entry barriers
        4. Trend analysis highlighting emerging and declining areas
        5. Pricing intelligence with optimal pricing recommendations
        6. Strategic recommendations with risk assessment
        7. Confidence scoring for your analysis
        
        Focus on actionable insights that can help publishers and content creators
        make informed decisions about market entry, positioning, and strategy.
        
        Always provide specific, data-driven recommendations with clear rationale.
        """
    
    def _get_trend_analysis_prompt(self) -> str:
        """Get system prompt for trend analysis"""
        return """
        You are an expert trend analyst specializing in content and publishing markets.
        
        Your role is to identify and analyze trends that impact book publishing,
        content creation, and digital media markets.
        
        When analyzing trends, evaluate:
        1. Trend strength and momentum
        2. Market relevance and impact potential
        3. Growth trajectory and sustainability
        4. Opportunity windows and timing
        5. Content creation implications
        6. Monetization potential
        7. Competition and saturation levels
        
        Provide actionable insights about:
        - Which trends to capitalize on immediately
        - Which trends to monitor for future opportunities
        - Which trends are declining or oversaturated
        - How to position content to benefit from trends
        
        Focus on trends that can translate into profitable content opportunities.
        """
    
    def _get_competitor_analysis_prompt(self) -> str:
        """Get system prompt for competitor analysis"""
        return """
        You are an expert competitive intelligence analyst for the publishing industry.
        
        Your expertise covers:
        - Competitor identification and profiling
        - Market positioning analysis
        - Content strategy assessment
        - Pricing analysis and optimization
        - Performance benchmarking
        - Gap analysis and opportunity identification
        - Differentiation strategy development
        
        When analyzing competitors, provide:
        1. Comprehensive competitor profiles with strengths and weaknesses
        2. Market positioning and strategy analysis
        3. Content gaps and underserved segments
        4. Pricing strategy and competitive positioning
        5. Performance benchmarks and success factors
        6. Differentiation opportunities and recommendations
        
        Focus on actionable competitive intelligence that enables strategic advantage.
        """
    
    async def analyze_market(
        self,
        category: str,
        analysis_depth: str = "standard",
        include_competitors: bool = True,
        include_trends: bool = True,
        include_pricing: bool = True,
        force_refresh: bool = False
    ) -> MarketAnalysisResult:
        """
        Perform comprehensive market analysis
        
        Args:
            category: Market category to analyze
            analysis_depth: Analysis depth (basic, standard, comprehensive)
            include_competitors: Include competitor analysis
            include_trends: Include trend analysis
            include_pricing: Include pricing analysis
            force_refresh: Force refresh of cached data
        """
        if not self.is_ready:
            raise RuntimeError("Market Intelligence Agent not initialized")
        
        cache_key = f"market_analysis_{category}_{analysis_depth}"
        
        # Check cache first
        if not force_refresh and cache_key in self._cache:
            cached_result = self._cache[cache_key]
            cache_time = cached_result.get("timestamp")
            if cache_time and datetime.fromisoformat(cache_time) > datetime.utcnow() - self.cache_duration:
                logger.info(f"Returning cached market analysis for {category}")
                return MarketAnalysisResult(**cached_result["data"])
        
        try:
            logger.info(f"Performing market analysis for category: {category}")
            
            # Gather market data
            market_data = await self._gather_market_data(category, analysis_depth)
            
            # Build analysis prompt
            prompt = self._build_market_analysis_prompt(
                category=category,
                market_data=market_data,
                analysis_depth=analysis_depth,
                include_competitors=include_competitors,
                include_trends=include_trends,
                include_pricing=include_pricing
            )
            
            # Execute analysis
            result = await self.agent.run(prompt)
            
            # Cache result
            self._cache[cache_key] = {
                "data": result.data.model_dump(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            return result.data
            
        except Exception as e:
            logger.error(f"Market analysis failed for {category}: {e}")
            raise
    
    async def analyze_trends(
        self,
        categories: Optional[List[str]] = None,
        timeframe: str = "3_months",
        focus_areas: Optional[List[str]] = None
    ) -> List[TrendInsight]:
        """Analyze market trends"""
        if not self.is_ready:
            raise RuntimeError("Market Intelligence Agent not initialized")
        
        try:
            logger.info(f"Analyzing trends for categories: {categories}")
            
            # Gather trend data
            trend_data = await self._gather_trend_data(categories, timeframe, focus_areas)
            
            # Build trend analysis prompt
            prompt = self._build_trend_analysis_prompt(
                categories=categories,
                trend_data=trend_data,
                timeframe=timeframe,
                focus_areas=focus_areas
            )
            
            # Execute analysis
            result = await self.trend_agent.run(prompt)
            
            return result.data
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            raise
    
    async def analyze_competitors(
        self,
        category: str,
        target_keywords: Optional[List[str]] = None,
        analysis_depth: str = "standard",
        include_pricing: bool = True,
        include_performance: bool = True
    ) -> Dict[str, Any]:
        """Analyze competitors in a category"""
        if not self.is_ready:
            raise RuntimeError("Market Intelligence Agent not initialized")
        
        try:
            logger.info(f"Analyzing competitors for category: {category}")
            
            # Gather competitor data
            competitor_data = await self._gather_competitor_data(
                category, target_keywords, analysis_depth
            )
            
            # Build competitor analysis prompt
            prompt = self._build_competitor_analysis_prompt(
                category=category,
                competitor_data=competitor_data,
                target_keywords=target_keywords,
                include_pricing=include_pricing,
                include_performance=include_performance
            )
            
            # Execute analysis
            result = await self.competitor_agent.run(prompt)
            
            return result.data.model_dump()
            
        except Exception as e:
            logger.error(f"Competitor analysis failed for {category}: {e}")
            raise
    
    async def analyze_opportunities(
        self,
        industry_focus: List[str],
        target_audience: Optional[str] = None,
        budget_range: Optional[Dict[str, float]] = None,
        risk_tolerance: str = "medium",
        time_horizon: str = "3_months"
    ) -> Dict[str, Any]:
        """Analyze market opportunities"""
        if not self.is_ready:
            raise RuntimeError("Market Intelligence Agent not initialized")
        
        try:
            logger.info(f"Analyzing opportunities for industries: {industry_focus}")
            
            # Analyze each industry focus area
            opportunities = []
            
            for industry in industry_focus:
                # Map industry to relevant categories
                categories = self._map_industry_to_categories(industry)
                
                for category in categories:
                    # Perform market analysis
                    market_analysis = await self.analyze_market(
                        category=category,
                        analysis_depth="standard",
                        include_competitors=True,
                        include_trends=True,
                        include_pricing=True
                    )
                    
                    # Extract opportunities
                    category_opportunities = self._extract_opportunities(
                        market_analysis,
                        industry,
                        target_audience,
                        budget_range,
                        risk_tolerance,
                        time_horizon
                    )
                    
                    opportunities.extend(category_opportunities)
            
            # Rank and filter opportunities
            ranked_opportunities = self._rank_opportunities(opportunities, risk_tolerance)
            
            return {
                "industry_focus": industry_focus,
                "target_audience": target_audience,
                "time_horizon": time_horizon,
                "risk_tolerance": risk_tolerance,
                "total_opportunities": len(ranked_opportunities),
                "opportunities": ranked_opportunities[:10],  # Top 10
                "analysis_date": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Opportunity analysis failed: {e}")
            raise
    
    # ========================================================================
    # Helper Methods
    # ========================================================================
    
    async def _gather_market_data(self, category: str, analysis_depth: str) -> Dict[str, Any]:
        """Gather market data for analysis"""
        # This would integrate with various data sources
        # For now, return mock comprehensive data
        
        return {
            "category": category,
            "analysis_depth": analysis_depth,
            "market_metrics": {
                "estimated_size": "Large - $500M+ annual",
                "growth_rate": 0.15,
                "competition_count": 150,
                "avg_book_price": 4.99,
                "top_seller_revenue": 50000
            },
            "recent_trends": [
                "AI and automation content",
                "Personal development focus",
                "Quick-start guides popularity",
                "Visual learning materials"
            ],
            "competitor_snapshot": {
                "market_leaders": ["Author A", "Author B", "Author C"],
                "avg_rating": 4.2,
                "avg_reviews": 85,
                "price_range": {"min": 2.99, "max": 9.99}
            },
            "seasonal_data": {
                "peak_months": ["January", "September"],
                "slow_months": ["June", "July"],
                "current_factor": 1.1
            }
        }
    
    async def _gather_trend_data(
        self,
        categories: Optional[List[str]],
        timeframe: str,
        focus_areas: Optional[List[str]]
    ) -> Dict[str, Any]:
        """Gather trend data for analysis"""
        # This would integrate with Google Trends, social media APIs, etc.
        
        return {
            "categories": categories or ["business", "self-help", "technology"],
            "timeframe": timeframe,
            "focus_areas": focus_areas or [],
            "trending_keywords": [
                "AI productivity",
                "remote work",
                "digital nomad",
                "passive income",
                "crypto trading"
            ],
            "growth_metrics": {
                "ai_productivity": {"score": 0.85, "growth": "rising"},
                "remote_work": {"score": 0.75, "growth": "stable"},
                "crypto_trading": {"score": 0.60, "growth": "declining"}
            },
            "market_opportunities": [
                "AI tools for productivity",
                "Remote work management",
                "Digital business automation"
            ]
        }
    
    async def _gather_competitor_data(
        self,
        category: str,
        target_keywords: Optional[List[str]],
        analysis_depth: str
    ) -> Dict[str, Any]:
        """Gather competitor data for analysis"""
        # This would integrate with Amazon API, web scraping, etc.
        
        return {
            "category": category,
            "target_keywords": target_keywords or [],
            "analysis_depth": analysis_depth,
            "top_performers": [
                {
                    "title": "AI Productivity Mastery",
                    "author": "John Expert",
                    "price": 4.99,
                    "rating": 4.5,
                    "reviews": 120,
                    "sales_rank": 15
                },
                {
                    "title": "Remote Work Revolution",
                    "author": "Jane Leader",
                    "price": 6.99,
                    "rating": 4.3,
                    "reviews": 89,
                    "sales_rank": 28
                }
            ],
            "pricing_analysis": {
                "avg_price": 5.49,
                "price_range": {"min": 2.99, "max": 9.99},
                "optimal_range": {"min": 4.99, "max": 7.99}
            },
            "content_gaps": [
                "Beginner-friendly AI guides",
                "Industry-specific automation",
                "Visual learning materials"
            ]
        }
    
    def _build_market_analysis_prompt(
        self,
        category: str,
        market_data: Dict[str, Any],
        analysis_depth: str,
        include_competitors: bool,
        include_trends: bool,
        include_pricing: bool
    ) -> str:
        """Build market analysis prompt"""
        
        prompt = f"""
        Perform a {analysis_depth} market analysis for the "{category}" category.
        
        Market Data:
        {market_data}
        
        Analysis Requirements:
        - Analysis Depth: {analysis_depth}
        - Include Competitors: {include_competitors}
        - Include Trends: {include_trends}
        - Include Pricing: {include_pricing}
        
        Provide a comprehensive market analysis including:
        1. Market size and growth assessment
        2. Competition density and saturation analysis
        3. Opportunity identification and scoring
        4. Risk assessment and threat analysis
        5. Strategic recommendations with confidence scores
        
        Focus on actionable insights for content creators and publishers.
        """
        
        return prompt.strip()
    
    def _build_trend_analysis_prompt(
        self,
        categories: Optional[List[str]],
        trend_data: Dict[str, Any],
        timeframe: str,
        focus_areas: Optional[List[str]]
    ) -> str:
        """Build trend analysis prompt"""
        
        prompt = f"""
        Analyze market trends for content publishing opportunities.
        
        Categories: {categories or 'All major categories'}
        Timeframe: {timeframe}
        Focus Areas: {focus_areas or 'General market trends'}
        
        Trend Data:
        {trend_data}
        
        Identify and analyze trends that represent content opportunities, including:
        1. Trend strength and momentum scoring
        2. Market relevance and impact assessment
        3. Opportunity windows and timing recommendations
        4. Content creation and monetization potential
        5. Competition and saturation analysis
        
        Provide specific recommendations for capitalizing on identified trends.
        """
        
        return prompt.strip()
    
    def _build_competitor_analysis_prompt(
        self,
        category: str,
        competitor_data: Dict[str, Any],
        target_keywords: Optional[List[str]],
        include_pricing: bool,
        include_performance: bool
    ) -> str:
        """Build competitor analysis prompt"""
        
        prompt = f"""
        Analyze competitors in the "{category}" category.
        
        Target Keywords: {target_keywords or 'Category-relevant keywords'}
        
        Competitor Data:
        {competitor_data}
        
        Analysis Requirements:
        - Include Pricing Analysis: {include_pricing}
        - Include Performance Analysis: {include_performance}
        
        Provide comprehensive competitor analysis including:
        1. Top competitor profiles and positioning
        2. Market leader analysis and success factors
        3. Content gaps and underserved segments
        4. Pricing strategy and competitive positioning
        5. Differentiation opportunities and recommendations
        
        Focus on actionable competitive intelligence.
        """
        
        return prompt.strip()
    
    def _map_industry_to_categories(self, industry: str) -> List[str]:
        """Map industry focus to relevant book categories"""
        industry_mapping = {
            "health": ["health", "fitness", "nutrition", "wellness"],
            "wealth": ["business", "finance", "investing", "entrepreneurship"],
            "beauty": ["lifestyle", "self-help", "wellness"],
            "technology": ["technology", "business", "programming"],
            "business": ["business", "entrepreneurship", "marketing", "leadership"],
            "fitness": ["health", "fitness", "sports"],
            "nutrition": ["health", "nutrition", "cooking"],
            "self-help": ["self-help", "personal development", "psychology"],
            "finance": ["finance", "investing", "business"],
            "real estate": ["business", "finance", "investing"],
            "marketing": ["business", "marketing", "entrepreneurship"],
            "education": ["education", "reference", "self-help"]
        }
        
        return industry_mapping.get(industry.lower(), ["business", "self-help"])
    
    def _extract_opportunities(
        self,
        market_analysis: MarketAnalysisResult,
        industry: str,
        target_audience: Optional[str],
        budget_range: Optional[Dict[str, float]],
        risk_tolerance: str,
        time_horizon: str
    ) -> List[Dict[str, Any]]:
        """Extract opportunities from market analysis"""
        opportunities = []
        
        for opportunity in market_analysis.opportunities:
            # Score opportunity based on criteria
            score = self._score_opportunity(
                opportunity, risk_tolerance, time_horizon, budget_range
            )
            
            opportunities.append({
                "industry": industry,
                "category": opportunity.category,
                "opportunity_score": score,
                "market_size": opportunity.market_size,
                "competition_level": opportunity.competition_level,
                "entry_barriers": opportunity.entry_barriers,
                "recommended_approach": opportunity.recommended_approach,
                "target_audience_match": self._assess_audience_match(
                    opportunity, target_audience
                ),
                "risk_assessment": {
                    "level": opportunity.competition_level,
                    "factors": opportunity.entry_barriers
                }
            })
        
        return opportunities
    
    def _score_opportunity(
        self,
        opportunity: MarketOpportunity,
        risk_tolerance: str,
        time_horizon: str,
        budget_range: Optional[Dict[str, float]]
    ) -> float:
        """Score opportunity based on criteria"""
        base_score = opportunity.opportunity_score
        
        # Adjust for risk tolerance
        risk_multiplier = {
            "low": 0.8,
            "medium": 1.0,
            "high": 1.2
        }.get(risk_tolerance, 1.0)
        
        # Adjust for competition level
        competition_adjustment = {
            "low": 1.2,
            "medium": 1.0,
            "high": 0.8
        }.get(opportunity.competition_level, 1.0)
        
        # Adjust for time horizon
        time_adjustment = 1.0  # Could be enhanced based on growth potential
        
        final_score = base_score * risk_multiplier * competition_adjustment * time_adjustment
        
        return min(max(final_score, 0.0), 1.0)
    
    def _assess_audience_match(
        self,
        opportunity: MarketOpportunity,
        target_audience: Optional[str]
    ) -> float:
        """Assess how well opportunity matches target audience"""
        if not target_audience:
            return 0.8  # Default neutral score
        
        # Simple keyword matching - could be enhanced with NLP
        audience_keywords = target_audience.lower().split()
        opportunity_text = f"{opportunity.category} {opportunity.recommended_approach}".lower()
        
        matches = sum(1 for keyword in audience_keywords if keyword in opportunity_text)
        return min(matches / len(audience_keywords), 1.0) if audience_keywords else 0.5
    
    def _rank_opportunities(
        self,
        opportunities: List[Dict[str, Any]],
        risk_tolerance: str
    ) -> List[Dict[str, Any]]:
        """Rank opportunities by score and relevance"""
        # Sort by opportunity score descending
        ranked = sorted(opportunities, key=lambda x: x["opportunity_score"], reverse=True)
        
        # Filter by risk tolerance if needed
        if risk_tolerance == "low":
            ranked = [opp for opp in ranked if opp["competition_level"] != "high"]
        
        return ranked
    
    async def cleanup(self):
        """Cleanup agent resources"""
        try:
            logger.info("Cleaning up Market Intelligence Agent")
            self._initialized = False
            self.agent = None
            self.trend_agent = None
            self.competitor_agent = None
            self._cache.clear()
            logger.info("Market Intelligence Agent cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")