"""
API Ecosystem Service - Main Application

Comprehensive public API platform enabling third-party integrations with:
- RESTful API with versioning
- GraphQL endpoint
- WebSocket support
- Developer portal
- API key management
- Rate limiting and quotas
- Webhook management
- SDK generation
- Analytics and monitoring
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import structlog
import uvicorn

from core.config import get_settings
from core.exceptions import APIEcosystemException
from core.logging import setup_logging
from core.monitoring import setup_monitoring, APIEcosystemMetrics
from core.security import SecurityMiddleware
from database.connection import DatabaseManager
from services.api_management_service import APIManagementService
from services.developer_portal_service import DeveloperPortalService
from services.webhook_service import WebhookService
from services.analytics_service import AnalyticsService
from services.sdk_generator_service import SDKGeneratorService
from api.v1 import router as v1_router
from api.v2 import router as v2_router
from api.graphql import graphql_router
from api.websocket import websocket_router
from api.admin import admin_router


# Global state
app_state: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    logger = structlog.get_logger(__name__)
    
    try:
        logger.info("Starting API Ecosystem Service")
        
        # Initialize database
        database_manager = DatabaseManager()
        await database_manager.initialize()
        app_state["database"] = database_manager
        
        # Initialize services
        api_management = APIManagementService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await api_management.start()
        app_state["api_management"] = api_management
        
        developer_portal = DeveloperPortalService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await developer_portal.start()
        app_state["developer_portal"] = developer_portal
        
        webhook_service = WebhookService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await webhook_service.start()
        app_state["webhook_service"] = webhook_service
        
        analytics_service = AnalyticsService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await analytics_service.start()
        app_state["analytics_service"] = analytics_service
        
        sdk_generator = SDKGeneratorService(
            database_manager.get_client(),
            database_manager.get_supabase_client()
        )
        await sdk_generator.start()
        app_state["sdk_generator"] = sdk_generator
        
        # Initialize metrics
        metrics = APIEcosystemMetrics()
        app_state["metrics"] = metrics
        
        logger.info("API Ecosystem Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start API Ecosystem Service: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down API Ecosystem Service")
        
        # Stop services
        if "api_management" in app_state:
            await app_state["api_management"].stop()
        if "developer_portal" in app_state:
            await app_state["developer_portal"].stop()
        if "webhook_service" in app_state:
            await app_state["webhook_service"].stop()
        if "analytics_service" in app_state:
            await app_state["analytics_service"].stop()
        if "sdk_generator" in app_state:
            await app_state["sdk_generator"].stop()
        
        # Close database connections
        if "database" in app_state:
            await app_state["database"].close()
        
        logger.info("API Ecosystem Service stopped")


# Create FastAPI application
def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    # Setup logging
    setup_logging()
    logger = structlog.get_logger(__name__)
    
    # Create app
    app = FastAPI(
        title="API Ecosystem Platform",
        description="Comprehensive public API platform for third-party integrations",
        version="1.0.0",
        docs_url="/docs" if settings.enable_docs else None,
        redoc_url="/redoc" if settings.enable_docs else None,
        openapi_url="/openapi.json" if settings.enable_openapi else None,
        lifespan=lifespan
    )
    
    # Setup monitoring
    setup_monitoring(app)
    
    # Initialize rate limiter
    limiter = Limiter(key_func=get_remote_address)
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    
    # Security middleware
    app.add_middleware(SecurityMiddleware)
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
        expose_headers=["X-Total-Count", "X-Page", "X-Per-Page", "X-Rate-Limit-*"]
    )
    
    # Trusted host middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts
        )
    
    # Static files
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # Exception handlers
    @app.exception_handler(APIEcosystemException)
    async def api_exception_handler(request: Request, exc: APIEcosystemException):
        logger = structlog.get_logger(__name__)
        logger.error(f"API error: {exc}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_type,
                "message": str(exc),
                "details": exc.details
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger = structlog.get_logger(__name__)
        logger.warning(f"HTTP error: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "http_error",
                "message": exc.detail
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger = structlog.get_logger(__name__)
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "internal_error",
                "message": "An unexpected error occurred"
            }
        )
    
    # Include API routers
    app.include_router(v1_router, prefix="/api/v1", tags=["API v1"])
    app.include_router(v2_router, prefix="/api/v2", tags=["API v2"])
    app.include_router(graphql_router, prefix="/graphql", tags=["GraphQL"])
    app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])
    app.include_router(admin_router, prefix="/admin", tags=["Admin"])
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "API Ecosystem Platform",
            "version": "1.0.0",
            "status": "operational",
            "features": [
                "RESTful API v1 & v2",
                "GraphQL API",
                "WebSocket Support",
                "Developer Portal",
                "API Key Management",
                "Rate Limiting & Quotas",
                "Webhook Management",
                "SDK Generation",
                "Analytics & Monitoring",
                "OAuth 2.0 Integration",
                "OpenAPI Documentation",
                "Multi-language SDKs"
            ],
            "documentation": {
                "api_docs": "/docs",
                "redoc": "/redoc",
                "openapi": "/openapi.json",
                "developer_portal": "/portal",
                "api_reference": "/reference"
            }
        }
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        try:
            # Check database connection
            if "database" in app_state:
                await app_state["database"].health_check()
            
            # Check services
            services_healthy = all([
                app_state.get("api_management", {}).get("is_running", False),
                app_state.get("developer_portal", {}).get("is_running", False),
                app_state.get("webhook_service", {}).get("is_running", False)
            ])
            
            if services_healthy:
                return {
                    "status": "healthy",
                    "services": {
                        "api_management": "running",
                        "developer_portal": "running",
                        "webhook_service": "running",
                        "analytics": "running",
                        "sdk_generator": "running"
                    }
                }
            else:
                raise HTTPException(status_code=503, detail="Some services are not running")
                
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            raise HTTPException(status_code=503, detail="Service unhealthy")
    
    return app


# Create app instance
app = create_app()


# Dependency injection functions
def get_api_management() -> APIManagementService:
    """Get API management service instance."""
    if "api_management" not in app_state:
        raise HTTPException(status_code=503, detail="API management service not available")
    return app_state["api_management"]


def get_developer_portal() -> DeveloperPortalService:
    """Get developer portal service instance."""
    if "developer_portal" not in app_state:
        raise HTTPException(status_code=503, detail="Developer portal service not available")
    return app_state["developer_portal"]


def get_webhook_service() -> WebhookService:
    """Get webhook service instance."""
    if "webhook_service" not in app_state:
        raise HTTPException(status_code=503, detail="Webhook service not available")
    return app_state["webhook_service"]


def get_analytics_service() -> AnalyticsService:
    """Get analytics service instance."""
    if "analytics_service" not in app_state:
        raise HTTPException(status_code=503, detail="Analytics service not available")
    return app_state["analytics_service"]


def get_sdk_generator() -> SDKGeneratorService:
    """Get SDK generator service instance."""
    if "sdk_generator" not in app_state:
        raise HTTPException(status_code=503, detail="SDK generator service not available")
    return app_state["sdk_generator"]


def get_metrics() -> APIEcosystemMetrics:
    """Get metrics instance."""
    if "metrics" not in app_state:
        raise HTTPException(status_code=503, detail="Metrics service not available")
    return app_state["metrics"]


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        workers=1 if settings.environment == "development" else settings.workers,
        log_config=None,  # Use our custom logging
        access_log=False  # Disable default access log
    )