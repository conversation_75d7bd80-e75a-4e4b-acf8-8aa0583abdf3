"""
API Version 1 Routes

REST API endpoints for the API Ecosystem Platform v1.
"""

from fastapi import APIRouter

from .api_keys import router as api_keys_router
from .webhooks import router as webhooks_router
from .developers import router as developers_router
from .applications import router as applications_router
from .documentation import router as documentation_router
from .analytics import router as analytics_router

# Create main v1 router
router = APIRouter()

# Include sub-routers
router.include_router(api_keys_router, prefix="/keys", tags=["API Keys"])
router.include_router(webhooks_router, prefix="/webhooks", tags=["Webhooks"])
router.include_router(developers_router, prefix="/developers", tags=["Developers"])
router.include_router(applications_router, prefix="/applications", tags=["Applications"])
router.include_router(documentation_router, prefix="/docs", tags=["Documentation"])
router.include_router(analytics_router, prefix="/analytics", tags=["Analytics"])

__all__ = ["router"]