"""
Webhooks Endpoints - Version 1

RESTful endpoints for webhook management including registration,
event subscriptions, delivery tracking, and debugging.
"""

from datetime import datetime, timedelta
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from slowapi import Limiter
from slowapi.util import get_remote_address

from main import get_webhook_service, get_metrics
from models.webhook_schemas import (
    Webhook, WebhookCreate, WebhookUpdate, WebhookListResponse,
    WebhookTestRequest, WebhookTestResponse, EventReplayRequest, EventReplayResponse,
    WebhookDeliveryReport, WebhookStats, WebhookSecurityInfo,
    EventType, WebhookStatus, DeliveryStatus, EventPriority
)
from core.security import JWTBearer
from core.exceptions import APIEcosystemException

router = APIRouter()
security = JWTBearer()
limiter = Limiter(key_func=get_remote_address)


@router.post("/", response_model=Webhook, status_code=201)
@limiter.limit("10/minute")
async def create_webhook(
    webhook_data: WebhookCreate,
    application_id: str = Query(..., description="Application ID"),
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Register a new webhook endpoint.
    
    Creates a new webhook that will receive events for the specified application.
    The webhook URL must be accessible and return 2xx status codes for successful delivery.
    """
    try:
        webhook = await webhook_service.register_webhook(
            application_id=application_id,
            url=str(webhook_data.url),
            events=webhook_data.events,
            secret=webhook_data.secret,
            description=webhook_data.description
        )
        
        return webhook
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=WebhookListResponse)
@limiter.limit("60/minute")
async def list_webhooks(
    application_id: Optional[str] = Query(None, description="Filter by application ID"),
    status: Optional[WebhookStatus] = Query(None, description="Filter by webhook status"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    List webhooks for the authenticated user.
    
    Returns a paginated list of webhooks with their configuration and status.
    """
    try:
        # Get user's applications
        # Filter webhooks by user's applications
        # This would be implemented with proper authorization
        
        webhooks = []  # Placeholder
        total = 0  # Placeholder
        
        return WebhookListResponse(
            webhooks=webhooks,
            total=total,
            page=page,
            per_page=per_page,
            has_next=(page * per_page) < total
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{webhook_id}", response_model=Webhook)
@limiter.limit("100/minute")
async def get_webhook(
    webhook_id: str,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Get details of a specific webhook.
    
    Returns the webhook configuration and status.
    The webhook secret is not included for security reasons.
    """
    try:
        # Verify webhook belongs to user
        # Get webhook details
        raise HTTPException(status_code=404, detail="Webhook not found")
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{webhook_id}", response_model=Webhook)
@limiter.limit("10/minute")
async def update_webhook(
    webhook_id: str,
    update_data: WebhookUpdate,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Update a webhook configuration.
    
    Updates the webhook URL, event subscriptions, or status.
    """
    try:
        webhook = await webhook_service.update_webhook(
            webhook_id=webhook_id,
            url=str(update_data.url) if update_data.url else None,
            events=update_data.events,
            status=update_data.status
        )
        
        return webhook
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{webhook_id}", status_code=204)
@limiter.limit("5/minute")
async def delete_webhook(
    webhook_id: str,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Delete a webhook.
    
    Permanently removes the webhook and stops event delivery.
    This action cannot be undone.
    """
    try:
        success = await webhook_service.delete_webhook(webhook_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Webhook not found")
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{webhook_id}/test", response_model=WebhookTestResponse)
@limiter.limit("10/minute")
async def test_webhook(
    webhook_id: str,
    test_data: Optional[WebhookTestRequest] = None,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Test a webhook endpoint.
    
    Sends a test event to the webhook to verify it's working correctly.
    """
    try:
        # Get webhook
        webhook = await webhook_service._get_webhook(webhook_id)
        if not webhook:
            raise HTTPException(status_code=404, detail="Webhook not found")
        
        # Create test event
        from models.webhook_schemas import WebhookEvent
        test_event = WebhookEvent(
            id="test-" + webhook_id,
            type=EventType.WEBHOOK_TEST,
            payload=test_data.custom_payload if test_data and test_data.custom_payload else {
                "message": "This is a test webhook delivery",
                "timestamp": datetime.utcnow().isoformat()
            },
            application_id=webhook.application_id,
            created_at=datetime.utcnow(),
            priority=EventPriority.LOW
        )
        
        # Deliver test webhook
        attempt = await webhook_service.deliver_webhook(webhook, test_event)
        
        return WebhookTestResponse(
            delivery_attempt=attempt,
            success=attempt.status.value == "delivered",
            message="Test webhook delivered successfully" if attempt.status.value == "delivered" else f"Test failed: {attempt.error_message}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{webhook_id}/events")
@limiter.limit("100/minute")
async def get_webhook_events(
    webhook_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for events"),
    end_date: Optional[datetime] = Query(None, description="End date for events"),
    status: Optional[DeliveryStatus] = Query(None, description="Filter by delivery status"),
    event_type: Optional[EventType] = Query(None, description="Filter by event type"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(50, ge=1, le=100, description="Items per page"),
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Get webhook event history.
    
    Returns a list of events sent to this webhook with their delivery status.
    """
    try:
        # Set default date range
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=7)
        
        events = await webhook_service.get_webhook_events(
            webhook_id=webhook_id,
            start_date=start_date,
            end_date=end_date,
            limit=per_page
        )
        
        return {
            "events": events,
            "total": len(events),
            "page": page,
            "per_page": per_page,
            "has_next": len(events) == per_page
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/events/{event_id}/replay", response_model=EventReplayResponse)
@limiter.limit("5/minute")
async def replay_event(
    event_id: str,
    replay_data: EventReplayRequest,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Replay a webhook event.
    
    Resends a specific event to one or more webhooks.
    Useful for debugging or recovering from delivery failures.
    """
    try:
        delivery_ids = await webhook_service.replay_event(
            event_id=event_id,
            webhook_ids=replay_data.webhook_ids
        )
        
        return EventReplayResponse(
            event_id=event_id,
            delivery_attempts=delivery_ids,
            replayed_to=len(delivery_ids),
            message=f"Event replayed to {len(delivery_ids)} webhooks"
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{webhook_id}/delivery-report", response_model=WebhookDeliveryReport)
@limiter.limit("20/minute")
async def get_delivery_report(
    webhook_id: str,
    start_date: Optional[datetime] = Query(None, description="Report start date"),
    end_date: Optional[datetime] = Query(None, description="Report end date"),
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Get webhook delivery report.
    
    Returns statistics about webhook delivery performance including
    success rates, response times, and failure analysis.
    """
    try:
        # Set default date range
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # This would be implemented in the webhook service
        raise HTTPException(status_code=501, detail="Delivery reports not implemented")
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{webhook_id}/security", response_model=WebhookSecurityInfo)
@limiter.limit("60/minute")
async def get_webhook_security_info(
    webhook_id: str,
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Get webhook security information.
    
    Returns information about how to verify webhook signatures
    and secure webhook endpoints.
    """
    try:
        return WebhookSecurityInfo(
            webhook_id=webhook_id,
            signature_method="HMAC-SHA256",
            signature_header="X-Webhook-Signature",
            timestamp_header="X-Webhook-Timestamp",
            verification_guide="""
To verify webhook signatures:

1. Extract the signature from the X-Webhook-Signature header
2. Extract the timestamp from the X-Webhook-Timestamp header
3. Create the signed payload: timestamp + "." + request_body
4. Compute HMAC-SHA256 using your webhook secret
5. Compare the computed signature with the received signature

Example in Python:
```python
import hmac
import hashlib

def verify_webhook(signature, timestamp, body, secret):
    expected = hmac.new(
        secret.encode(),
        f"{timestamp}.{body}".encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f"sha256={expected}", signature)
```

Always verify the timestamp to prevent replay attacks.
"""
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/event-types", response_model=List[dict])
@limiter.limit("100/minute")
async def get_event_types():
    """
    Get available webhook event types.
    
    Returns a list of all available event types that can be subscribed to.
    """
    try:
        event_types = []
        
        for event_type in EventType:
            category = event_type.value.split('.')[0]
            event_types.append({
                "type": event_type.value,
                "name": event_type.value.replace('_', ' ').replace('.', ' - ').title(),
                "category": category.title(),
                "description": f"Triggered when {event_type.value.replace('_', ' ').replace('.', ' ')} occurs"
            })
        
        # Group by category
        categories = {}
        for event in event_types:
            cat = event["category"]
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(event)
        
        return [
            {
                "category": cat,
                "events": events
            }
            for cat, events in categories.items()
        ]
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/stats", response_model=WebhookStats)
@limiter.limit("30/minute")
async def get_webhook_stats(
    application_id: Optional[str] = Query(None, description="Filter by application ID"),
    current_user: str = Depends(security),
    webhook_service = Depends(get_webhook_service)
):
    """
    Get webhook statistics.
    
    Returns aggregate statistics about webhook usage and performance.
    """
    try:
        # This would be implemented in the webhook service
        # with proper user authorization
        
        return WebhookStats(
            total_webhooks=0,
            active_webhooks=0,
            total_events_sent=0,
            successful_deliveries=0,
            failed_deliveries=0,
            average_success_rate=0.0,
            most_popular_events=[]
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))