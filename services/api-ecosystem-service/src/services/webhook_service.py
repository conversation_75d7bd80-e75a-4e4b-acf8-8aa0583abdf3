"""
Webhook Service

Comprehensive webhook management including:
- Webhook registration and validation
- Event subscription management
- Reliable event delivery with retries
- Webhook security (signatures, verification)
- Event replay and debugging
- Real-time event streaming
"""

import asyncio
import json
import hmac
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
import uuid
from urllib.parse import urlparse

import httpx
import structlog
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding, rsa

from core.config import get_settings
from core.exceptions import APIEcosystemException
from core.monitoring import APIEcosystemMetrics
from models.webhook_schemas import (
    Webhook, WebhookEvent, EventType, DeliveryAttempt,
    WebhookSubscription, EventPayload, WebhookStatus
)


class DeliveryStatus(Enum):
    """Webhook delivery status."""
    PENDING = "pending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"


class EventPriority(Enum):
    """Event priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class WebhookService:
    """
    Comprehensive webhook service for event delivery and management.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = APIEcosystemMetrics()
        
        # HTTP client for webhook delivery
        self.http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            follow_redirects=True,
            limits=httpx.Limits(max_keepalive_connections=20)
        )
        
        # Event queue
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        self.priority_queue: asyncio.Queue = asyncio.PriorityQueue(maxsize=1000)
        
        # Retry configuration
        self.max_retries = 5
        self.retry_delays = [1, 5, 30, 300, 1800]  # seconds
        
        # Active webhooks cache
        self.active_webhooks: Dict[str, List[Webhook]] = {}
        self.webhook_cache_ttl = timedelta(minutes=5)
        self.cache_updated_at = datetime.utcnow()
        
        # Event subscribers (for real-time streaming)
        self.event_subscribers: Dict[str, Set[asyncio.Queue]] = {}
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the webhook service."""
        self.logger.info("Starting Webhook Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Load active webhooks
            await self._load_active_webhooks()
            
            # Start background tasks
            self.background_tasks.extend([
                asyncio.create_task(self._event_dispatcher()),
                asyncio.create_task(self._priority_dispatcher()),
                asyncio.create_task(self._retry_processor()),
                asyncio.create_task(self._health_checker()),
                asyncio.create_task(self._metrics_aggregator())
            ])
            
            self.is_running = True
            self.logger.info("Webhook Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start webhook service: {e}")
            raise APIEcosystemException(f"Webhook service startup failed: {e}")
    
    async def stop(self):
        """Stop the webhook service."""
        self.logger.info("Stopping Webhook Service")
        
        self.is_running = False
        
        # Process remaining events
        await self._flush_event_queues()
        
        # Close HTTP client
        await self.http_client.aclose()
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("Webhook Service stopped")
    
    async def register_webhook(
        self,
        application_id: str,
        url: str,
        events: List[EventType],
        secret: Optional[str] = None,
        description: Optional[str] = None
    ) -> Webhook:
        """
        Register a new webhook.
        
        Args:
            application_id: Application identifier
            url: Webhook endpoint URL
            events: List of event types to subscribe to
            secret: Webhook secret for signature verification
            description: Webhook description
        
        Returns:
            Webhook object
        """
        try:
            # Validate URL
            parsed_url = urlparse(url)
            if parsed_url.scheme not in ["https", "http"]:
                raise APIEcosystemException("Invalid webhook URL scheme")
            
            # Validate HTTPS in production
            if self.settings.environment == "production" and parsed_url.scheme != "https":
                raise APIEcosystemException("HTTPS required for webhooks in production")
            
            webhook_id = str(uuid.uuid4())
            
            # Generate secret if not provided
            if not secret:
                secret = self._generate_webhook_secret()
            
            webhook_data = {
                "id": webhook_id,
                "application_id": application_id,
                "url": url,
                "secret_hash": self._hash_secret(secret),
                "events": [event.value for event in events],
                "description": description,
                "status": WebhookStatus.ACTIVE.value,
                "created_at": datetime.utcnow().isoformat(),
                "last_triggered_at": None,
                "failure_count": 0,
                "metadata": {
                    "headers": {},
                    "retry_policy": "exponential",
                    "timeout_seconds": 30
                }
            }
            
            # Store in database
            await self.supabase.table("webhooks").insert(webhook_data).execute()
            
            # Create webhook object
            webhook = Webhook(
                id=webhook_id,
                application_id=application_id,
                url=url,
                secret=secret,  # Return unhashed secret only on creation
                events=events,
                description=description,
                status=WebhookStatus.ACTIVE,
                created_at=datetime.utcnow(),
                last_triggered_at=None,
                failure_count=0,
                metadata=webhook_data["metadata"]
            )
            
            # Update cache
            await self._update_webhook_cache()
            
            # Test webhook
            await self._test_webhook(webhook)
            
            self.logger.info(f"Webhook registered: {url}", webhook_id=webhook_id)
            
            # Update metrics
            self.metrics.webhooks_registered_total.inc()
            
            return webhook
            
        except Exception as e:
            self.logger.error(f"Failed to register webhook: {e}")
            raise APIEcosystemException(f"Webhook registration failed: {e}")
    
    async def trigger_event(
        self,
        event_type: EventType,
        payload: Dict[str, Any],
        application_id: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL
    ) -> str:
        """
        Trigger a webhook event.
        
        Args:
            event_type: Type of event
            payload: Event payload data
            application_id: Optional application filter
            priority: Event priority
        
        Returns:
            Event ID
        """
        try:
            event_id = str(uuid.uuid4())
            
            # Create event
            event = WebhookEvent(
                id=event_id,
                type=event_type,
                payload=payload,
                application_id=application_id,
                created_at=datetime.utcnow(),
                priority=priority
            )
            
            # Store event
            event_data = {
                "id": event_id,
                "type": event_type.value,
                "payload": payload,
                "application_id": application_id,
                "created_at": event.created_at.isoformat(),
                "priority": priority.value
            }
            
            await self.supabase.table("webhook_events").insert(event_data).execute()
            
            # Queue for delivery
            if priority in [EventPriority.HIGH, EventPriority.CRITICAL]:
                await self.priority_queue.put((
                    self._get_priority_score(priority),
                    event
                ))
            else:
                await self.event_queue.put(event)
            
            # Notify real-time subscribers
            await self._notify_subscribers(event)
            
            self.logger.info(
                f"Event triggered: {event_type.value}",
                event_id=event_id,
                priority=priority.value
            )
            
            # Update metrics
            self.metrics.webhook_events_triggered_total.labels(
                event_type=event_type.value,
                priority=priority.value
            ).inc()
            
            return event_id
            
        except Exception as e:
            self.logger.error(f"Failed to trigger event: {e}")
            raise APIEcosystemException(f"Event trigger failed: {e}")
    
    async def deliver_webhook(
        self,
        webhook: Webhook,
        event: WebhookEvent,
        attempt_number: int = 1
    ) -> DeliveryAttempt:
        """
        Deliver webhook event to endpoint.
        
        Args:
            webhook: Webhook configuration
            event: Event to deliver
            attempt_number: Delivery attempt number
        
        Returns:
            Delivery attempt result
        """
        try:
            # Prepare payload
            payload = {
                "id": event.id,
                "type": event.type.value,
                "created_at": event.created_at.isoformat(),
                "data": event.payload
            }
            
            # Generate signature
            signature = self._generate_signature(webhook.secret, payload)
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "X-Webhook-Event-Id": event.id,
                "X-Webhook-Event-Type": event.type.value,
                "X-Webhook-Signature": signature,
                "X-Webhook-Timestamp": str(int(event.created_at.timestamp())),
                "User-Agent": f"APIEcosystem-Webhook/1.0"
            }
            
            # Add custom headers from webhook metadata
            if webhook.metadata.get("headers"):
                headers.update(webhook.metadata["headers"])
            
            # Deliver webhook
            start_time = datetime.utcnow()
            
            try:
                response = await self.http_client.post(
                    webhook.url,
                    json=payload,
                    headers=headers,
                    timeout=webhook.metadata.get("timeout_seconds", 30)
                )
                
                response_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
                
                # Create delivery attempt
                attempt = DeliveryAttempt(
                    id=str(uuid.uuid4()),
                    webhook_id=webhook.id,
                    event_id=event.id,
                    attempt_number=attempt_number,
                    status=DeliveryStatus.DELIVERED if response.status_code < 400 else DeliveryStatus.FAILED,
                    status_code=response.status_code,
                    response_body=response.text[:1000],  # Limit response size
                    response_time_ms=response_time_ms,
                    attempted_at=start_time,
                    error_message=None if response.status_code < 400 else f"HTTP {response.status_code}"
                )
                
                # Log delivery
                await self._log_delivery_attempt(attempt)
                
                # Update metrics
                self.metrics.webhook_deliveries_total.labels(
                    status="success" if response.status_code < 400 else "failure",
                    status_code=str(response.status_code)
                ).inc()
                
                self.metrics.webhook_delivery_duration.labels(
                    webhook_id=webhook.id
                ).observe(response_time_ms / 1000.0)
                
                # Handle failure
                if response.status_code >= 400:
                    await self._handle_delivery_failure(webhook, event, attempt)
                else:
                    # Reset failure count on success
                    await self._reset_webhook_failures(webhook.id)
                
                return attempt
                
            except httpx.TimeoutException:
                error_message = "Request timeout"
                response_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
                
            except httpx.RequestError as e:
                error_message = f"Request error: {str(e)}"
                response_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
                
            except Exception as e:
                error_message = f"Unexpected error: {str(e)}"
                response_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Create failed delivery attempt
            attempt = DeliveryAttempt(
                id=str(uuid.uuid4()),
                webhook_id=webhook.id,
                event_id=event.id,
                attempt_number=attempt_number,
                status=DeliveryStatus.FAILED,
                status_code=None,
                response_body=None,
                response_time_ms=response_time_ms,
                attempted_at=start_time,
                error_message=error_message
            )
            
            # Log delivery
            await self._log_delivery_attempt(attempt)
            
            # Update metrics
            self.metrics.webhook_deliveries_total.labels(
                status="error",
                status_code="0"
            ).inc()
            
            # Handle failure
            await self._handle_delivery_failure(webhook, event, attempt)
            
            return attempt
            
        except Exception as e:
            self.logger.error(f"Webhook delivery failed: {e}")
            raise APIEcosystemException(f"Webhook delivery failed: {e}")
    
    async def update_webhook(
        self,
        webhook_id: str,
        url: Optional[str] = None,
        events: Optional[List[EventType]] = None,
        status: Optional[WebhookStatus] = None
    ) -> Webhook:
        """
        Update webhook configuration.
        
        Args:
            webhook_id: Webhook identifier
            url: New webhook URL
            events: New event subscriptions
            status: New webhook status
        
        Returns:
            Updated webhook object
        """
        try:
            # Get existing webhook
            result = await self.supabase.table("webhooks").select("*").eq(
                "id", webhook_id
            ).single().execute()
            
            if not result.data:
                raise APIEcosystemException("Webhook not found")
            
            # Prepare update data
            update_data = {
                "updated_at": datetime.utcnow().isoformat()
            }
            
            if url:
                update_data["url"] = url
            
            if events:
                update_data["events"] = [event.value for event in events]
            
            if status:
                update_data["status"] = status.value
            
            # Update webhook
            await self.supabase.table("webhooks").update(update_data).eq(
                "id", webhook_id
            ).execute()
            
            # Get updated webhook
            updated = await self.supabase.table("webhooks").select("*").eq(
                "id", webhook_id
            ).single().execute()
            
            # Create webhook object
            webhook = Webhook(
                id=updated.data["id"],
                application_id=updated.data["application_id"],
                url=updated.data["url"],
                secret=None,  # Don't expose secret
                events=[EventType(e) for e in updated.data["events"]],
                description=updated.data.get("description"),
                status=WebhookStatus(updated.data["status"]),
                created_at=datetime.fromisoformat(updated.data["created_at"]),
                last_triggered_at=datetime.fromisoformat(updated.data["last_triggered_at"]) if updated.data.get("last_triggered_at") else None,
                failure_count=updated.data["failure_count"],
                metadata=updated.data.get("metadata", {})
            )
            
            # Update cache
            await self._update_webhook_cache()
            
            self.logger.info(f"Webhook updated: {webhook_id}")
            
            return webhook
            
        except Exception as e:
            self.logger.error(f"Failed to update webhook: {e}")
            raise APIEcosystemException(f"Webhook update failed: {e}")
    
    async def delete_webhook(self, webhook_id: str) -> bool:
        """
        Delete a webhook.
        
        Args:
            webhook_id: Webhook identifier
        
        Returns:
            True if deleted successfully
        """
        try:
            # Soft delete by updating status
            await self.supabase.table("webhooks").update({
                "status": WebhookStatus.DELETED.value,
                "deleted_at": datetime.utcnow().isoformat()
            }).eq("id", webhook_id).execute()
            
            # Update cache
            await self._update_webhook_cache()
            
            self.logger.info(f"Webhook deleted: {webhook_id}")
            
            # Update metrics
            self.metrics.webhooks_deleted_total.inc()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete webhook: {e}")
            raise APIEcosystemException(f"Webhook deletion failed: {e}")
    
    async def get_webhook_events(
        self,
        webhook_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get webhook event history.
        
        Args:
            webhook_id: Webhook identifier
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum events to return
        
        Returns:
            List of webhook events with delivery status
        """
        try:
            # Get delivery attempts
            query = self.supabase.table("webhook_delivery_attempts").select(
                "*, webhook_events(*)"
            ).eq("webhook_id", webhook_id)
            
            if start_date:
                query = query.gte("attempted_at", start_date.isoformat())
            
            if end_date:
                query = query.lte("attempted_at", end_date.isoformat())
            
            result = await query.order("attempted_at", desc=True).limit(limit).execute()
            
            events = []
            for row in result.data:
                events.append({
                    "event": row["webhook_events"],
                    "delivery": {
                        "attempt_number": row["attempt_number"],
                        "status": row["status"],
                        "status_code": row.get("status_code"),
                        "response_time_ms": row.get("response_time_ms"),
                        "attempted_at": row["attempted_at"],
                        "error_message": row.get("error_message")
                    }
                })
            
            return events
            
        except Exception as e:
            self.logger.error(f"Failed to get webhook events: {e}")
            raise APIEcosystemException(f"Webhook events retrieval failed: {e}")
    
    async def replay_event(self, event_id: str, webhook_ids: Optional[List[str]] = None) -> List[str]:
        """
        Replay a webhook event.
        
        Args:
            event_id: Event identifier to replay
            webhook_ids: Optional specific webhooks to replay to
        
        Returns:
            List of delivery attempt IDs
        """
        try:
            # Get event
            event_result = await self.supabase.table("webhook_events").select("*").eq(
                "id", event_id
            ).single().execute()
            
            if not event_result.data:
                raise APIEcosystemException("Event not found")
            
            # Create event object
            event = WebhookEvent(
                id=event_result.data["id"],
                type=EventType(event_result.data["type"]),
                payload=event_result.data["payload"],
                application_id=event_result.data.get("application_id"),
                created_at=datetime.fromisoformat(event_result.data["created_at"]),
                priority=EventPriority(event_result.data.get("priority", "normal"))
            )
            
            # Get webhooks to replay to
            if webhook_ids:
                webhooks = []
                for webhook_id in webhook_ids:
                    webhook = await self._get_webhook(webhook_id)
                    if webhook:
                        webhooks.append(webhook)
            else:
                # Get all webhooks subscribed to this event type
                webhooks = await self._get_webhooks_for_event(event.type, event.application_id)
            
            # Deliver to each webhook
            delivery_ids = []
            for webhook in webhooks:
                attempt = await self.deliver_webhook(webhook, event)
                delivery_ids.append(attempt.id)
            
            self.logger.info(f"Event replayed: {event_id}", delivery_count=len(delivery_ids))
            
            # Update metrics
            self.metrics.webhook_events_replayed_total.inc()
            
            return delivery_ids
            
        except Exception as e:
            self.logger.error(f"Failed to replay event: {e}")
            raise APIEcosystemException(f"Event replay failed: {e}")
    
    async def subscribe_to_events(
        self,
        event_types: List[EventType],
        application_id: Optional[str] = None
    ) -> asyncio.Queue:
        """
        Subscribe to real-time events (for WebSocket connections).
        
        Args:
            event_types: Event types to subscribe to
            application_id: Optional application filter
        
        Returns:
            Queue for receiving events
        """
        try:
            # Create subscriber queue
            queue = asyncio.Queue(maxsize=100)
            
            # Register subscriber
            for event_type in event_types:
                key = f"{event_type.value}:{application_id or '*'}"
                if key not in self.event_subscribers:
                    self.event_subscribers[key] = set()
                self.event_subscribers[key].add(queue)
            
            self.logger.info(
                "Event subscription created",
                event_types=[e.value for e in event_types],
                application_id=application_id
            )
            
            return queue
            
        except Exception as e:
            self.logger.error(f"Failed to subscribe to events: {e}")
            raise APIEcosystemException(f"Event subscription failed: {e}")
    
    async def unsubscribe_from_events(self, queue: asyncio.Queue):
        """
        Unsubscribe from real-time events.
        
        Args:
            queue: Subscriber queue to remove
        """
        try:
            # Remove from all subscriptions
            for subscribers in self.event_subscribers.values():
                subscribers.discard(queue)
            
            # Clean up empty subscription keys
            self.event_subscribers = {
                k: v for k, v in self.event_subscribers.items() if v
            }
            
            self.logger.info("Event subscription removed")
            
        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from events: {e}")
    
    # Private helper methods
    
    def _generate_webhook_secret(self) -> str:
        """Generate secure webhook secret."""
        import secrets
        return f"whsec_{secrets.token_urlsafe(32)}"
    
    def _hash_secret(self, secret: str) -> str:
        """Hash webhook secret for storage."""
        return hashlib.sha256(secret.encode()).hexdigest()
    
    def _generate_signature(self, secret: str, payload: Dict[str, Any]) -> str:
        """Generate webhook signature."""
        payload_str = json.dumps(payload, sort_keys=True, separators=(',', ':'))
        signature = hmac.new(
            secret.encode(),
            payload_str.encode(),
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"
    
    def _get_priority_score(self, priority: EventPriority) -> int:
        """Get priority score for queue ordering (lower is higher priority)."""
        scores = {
            EventPriority.CRITICAL: 0,
            EventPriority.HIGH: 1,
            EventPriority.NORMAL: 2,
            EventPriority.LOW: 3
        }
        return scores.get(priority, 2)
    
    async def _test_webhook(self, webhook: Webhook):
        """Test webhook with a ping event."""
        try:
            test_event = WebhookEvent(
                id=str(uuid.uuid4()),
                type=EventType.WEBHOOK_TEST,
                payload={"message": "Webhook registered successfully"},
                application_id=webhook.application_id,
                created_at=datetime.utcnow(),
                priority=EventPriority.LOW
            )
            
            await self.deliver_webhook(webhook, test_event)
            
        except Exception as e:
            self.logger.warning(f"Webhook test failed: {e}")
    
    async def _load_active_webhooks(self):
        """Load active webhooks into cache."""
        try:
            result = await self.supabase.table("webhooks").select("*").eq(
                "status", WebhookStatus.ACTIVE.value
            ).execute()
            
            self.active_webhooks.clear()
            
            for row in result.data:
                webhook = Webhook(
                    id=row["id"],
                    application_id=row["application_id"],
                    url=row["url"],
                    secret=None,  # Don't load secret into cache
                    events=[EventType(e) for e in row["events"]],
                    description=row.get("description"),
                    status=WebhookStatus(row["status"]),
                    created_at=datetime.fromisoformat(row["created_at"]),
                    last_triggered_at=datetime.fromisoformat(row["last_triggered_at"]) if row.get("last_triggered_at") else None,
                    failure_count=row["failure_count"],
                    metadata=row.get("metadata", {})
                )
                
                # Group by event type
                for event_type in webhook.events:
                    key = f"{event_type.value}:{webhook.application_id}"
                    if key not in self.active_webhooks:
                        self.active_webhooks[key] = []
                    self.active_webhooks[key].append(webhook)
            
            self.cache_updated_at = datetime.utcnow()
            self.logger.info(f"Loaded {len(result.data)} active webhooks")
            
        except Exception as e:
            self.logger.error(f"Failed to load active webhooks: {e}")
    
    async def _update_webhook_cache(self):
        """Update webhook cache."""
        await self._load_active_webhooks()
    
    async def _get_webhooks_for_event(
        self,
        event_type: EventType,
        application_id: Optional[str] = None
    ) -> List[Webhook]:
        """Get webhooks subscribed to an event type."""
        # Check cache validity
        if datetime.utcnow() - self.cache_updated_at > self.webhook_cache_ttl:
            await self._update_webhook_cache()
        
        webhooks = []
        
        # Get webhooks for specific application
        if application_id:
            key = f"{event_type.value}:{application_id}"
            webhooks.extend(self.active_webhooks.get(key, []))
        
        # Get webhooks for all applications (if event supports it)
        key = f"{event_type.value}:*"
        webhooks.extend(self.active_webhooks.get(key, []))
        
        return webhooks
    
    async def _get_webhook(self, webhook_id: str) -> Optional[Webhook]:
        """Get webhook by ID."""
        try:
            result = await self.supabase.table("webhooks").select("*").eq(
                "id", webhook_id
            ).single().execute()
            
            if not result.data:
                return None
            
            row = result.data
            return Webhook(
                id=row["id"],
                application_id=row["application_id"],
                url=row["url"],
                secret=None,  # Don't expose secret
                events=[EventType(e) for e in row["events"]],
                description=row.get("description"),
                status=WebhookStatus(row["status"]),
                created_at=datetime.fromisoformat(row["created_at"]),
                last_triggered_at=datetime.fromisoformat(row["last_triggered_at"]) if row.get("last_triggered_at") else None,
                failure_count=row["failure_count"],
                metadata=row.get("metadata", {})
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get webhook: {e}")
            return None
    
    async def _log_delivery_attempt(self, attempt: DeliveryAttempt):
        """Log webhook delivery attempt."""
        try:
            attempt_data = {
                "id": attempt.id,
                "webhook_id": attempt.webhook_id,
                "event_id": attempt.event_id,
                "attempt_number": attempt.attempt_number,
                "status": attempt.status.value,
                "status_code": attempt.status_code,
                "response_body": attempt.response_body,
                "response_time_ms": attempt.response_time_ms,
                "attempted_at": attempt.attempted_at.isoformat(),
                "error_message": attempt.error_message
            }
            
            await self.supabase.table("webhook_delivery_attempts").insert(attempt_data).execute()
            
        except Exception as e:
            self.logger.error(f"Failed to log delivery attempt: {e}")
    
    async def _handle_delivery_failure(
        self,
        webhook: Webhook,
        event: WebhookEvent,
        attempt: DeliveryAttempt
    ):
        """Handle webhook delivery failure."""
        try:
            # Increment failure count
            new_failure_count = webhook.failure_count + 1
            
            await self.supabase.table("webhooks").update({
                "failure_count": new_failure_count,
                "last_failure_at": datetime.utcnow().isoformat()
            }).eq("id", webhook.id).execute()
            
            # Check if should retry
            if attempt.attempt_number < self.max_retries:
                # Schedule retry
                retry_delay = self.retry_delays[min(attempt.attempt_number - 1, len(self.retry_delays) - 1)]
                
                await self._schedule_retry(webhook, event, attempt.attempt_number + 1, retry_delay)
            else:
                # Max retries reached
                self.logger.warning(
                    f"Webhook delivery failed after {self.max_retries} attempts",
                    webhook_id=webhook.id,
                    event_id=event.id
                )
                
                # Disable webhook if too many failures
                if new_failure_count >= 100:
                    await self._disable_webhook(webhook.id, "Too many delivery failures")
            
        except Exception as e:
            self.logger.error(f"Failed to handle delivery failure: {e}")
    
    async def _reset_webhook_failures(self, webhook_id: str):
        """Reset webhook failure count after successful delivery."""
        try:
            await self.supabase.table("webhooks").update({
                "failure_count": 0,
                "last_triggered_at": datetime.utcnow().isoformat()
            }).eq("id", webhook_id).execute()
            
        except Exception as e:
            self.logger.error(f"Failed to reset webhook failures: {e}")
    
    async def _disable_webhook(self, webhook_id: str, reason: str):
        """Disable a webhook due to failures."""
        try:
            await self.supabase.table("webhooks").update({
                "status": WebhookStatus.DISABLED.value,
                "disabled_at": datetime.utcnow().isoformat(),
                "disabled_reason": reason
            }).eq("id", webhook_id).execute()
            
            # Update cache
            await self._update_webhook_cache()
            
            self.logger.warning(f"Webhook disabled: {webhook_id}", reason=reason)
            
            # Update metrics
            self.metrics.webhooks_disabled_total.inc()
            
        except Exception as e:
            self.logger.error(f"Failed to disable webhook: {e}")
    
    async def _schedule_retry(
        self,
        webhook: Webhook,
        event: WebhookEvent,
        attempt_number: int,
        delay_seconds: int
    ):
        """Schedule webhook retry."""
        try:
            retry_data = {
                "id": str(uuid.uuid4()),
                "webhook_id": webhook.id,
                "event_id": event.id,
                "attempt_number": attempt_number,
                "scheduled_at": (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat(),
                "status": "pending"
            }
            
            await self.supabase.table("webhook_retries").insert(retry_data).execute()
            
            self.logger.info(
                f"Webhook retry scheduled",
                webhook_id=webhook.id,
                event_id=event.id,
                attempt_number=attempt_number,
                delay_seconds=delay_seconds
            )
            
        except Exception as e:
            self.logger.error(f"Failed to schedule retry: {e}")
    
    async def _notify_subscribers(self, event: WebhookEvent):
        """Notify real-time subscribers about event."""
        try:
            # Notify specific application subscribers
            if event.application_id:
                key = f"{event.type.value}:{event.application_id}"
                subscribers = self.event_subscribers.get(key, set())
                for queue in subscribers:
                    try:
                        await queue.put(event)
                    except asyncio.QueueFull:
                        pass  # Skip if subscriber queue is full
            
            # Notify wildcard subscribers
            key = f"{event.type.value}:*"
            subscribers = self.event_subscribers.get(key, set())
            for queue in subscribers:
                try:
                    await queue.put(event)
                except asyncio.QueueFull:
                    pass
            
        except Exception as e:
            self.logger.error(f"Failed to notify subscribers: {e}")
    
    async def _initialize_tables(self):
        """Initialize database tables."""
        # Tables are created via Supabase migrations
        pass
    
    async def _flush_event_queues(self):
        """Flush remaining events in queues."""
        try:
            # Process remaining normal events
            while not self.event_queue.empty():
                event = await self.event_queue.get()
                webhooks = await self._get_webhooks_for_event(event.type, event.application_id)
                for webhook in webhooks:
                    await self.deliver_webhook(webhook, event)
            
            # Process remaining priority events
            while not self.priority_queue.empty():
                _, event = await self.priority_queue.get()
                webhooks = await self._get_webhooks_for_event(event.type, event.application_id)
                for webhook in webhooks:
                    await self.deliver_webhook(webhook, event)
            
        except Exception as e:
            self.logger.error(f"Failed to flush event queues: {e}")
    
    async def _event_dispatcher(self):
        """Background task to dispatch events to webhooks."""
        while self.is_running:
            try:
                # Get event from queue
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                
                # Get webhooks for this event
                webhooks = await self._get_webhooks_for_event(event.type, event.application_id)
                
                # Deliver to each webhook
                delivery_tasks = []
                for webhook in webhooks:
                    task = asyncio.create_task(self.deliver_webhook(webhook, event))
                    delivery_tasks.append(task)
                
                # Wait for all deliveries
                if delivery_tasks:
                    await asyncio.gather(*delivery_tasks, return_exceptions=True)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Event dispatcher error: {e}")
                await asyncio.sleep(1)
    
    async def _priority_dispatcher(self):
        """Background task to dispatch priority events."""
        while self.is_running:
            try:
                # Get priority event from queue
                _, event = await asyncio.wait_for(self.priority_queue.get(), timeout=1.0)
                
                # Get webhooks for this event
                webhooks = await self._get_webhooks_for_event(event.type, event.application_id)
                
                # Deliver to each webhook immediately
                for webhook in webhooks:
                    await self.deliver_webhook(webhook, event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Priority dispatcher error: {e}")
                await asyncio.sleep(1)
    
    async def _retry_processor(self):
        """Background task to process webhook retries."""
        while self.is_running:
            try:
                # Get pending retries
                result = await self.supabase.table("webhook_retries").select("*").eq(
                    "status", "pending"
                ).lte("scheduled_at", datetime.utcnow().isoformat()).limit(10).execute()
                
                for retry in result.data:
                    try:
                        # Get webhook and event
                        webhook = await self._get_webhook(retry["webhook_id"])
                        
                        event_result = await self.supabase.table("webhook_events").select("*").eq(
                            "id", retry["event_id"]
                        ).single().execute()
                        
                        if webhook and event_result.data:
                            # Create event object
                            event = WebhookEvent(
                                id=event_result.data["id"],
                                type=EventType(event_result.data["type"]),
                                payload=event_result.data["payload"],
                                application_id=event_result.data.get("application_id"),
                                created_at=datetime.fromisoformat(event_result.data["created_at"]),
                                priority=EventPriority(event_result.data.get("priority", "normal"))
                            )
                            
                            # Deliver webhook
                            await self.deliver_webhook(webhook, event, retry["attempt_number"])
                        
                        # Mark retry as processed
                        await self.supabase.table("webhook_retries").update({
                            "status": "processed",
                            "processed_at": datetime.utcnow().isoformat()
                        }).eq("id", retry["id"]).execute()
                        
                    except Exception as e:
                        self.logger.error(f"Failed to process retry: {e}")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Retry processor error: {e}")
                await asyncio.sleep(30)
    
    async def _health_checker(self):
        """Background task to check webhook health."""
        while self.is_running:
            try:
                # Check webhook health
                # This could ping webhooks or analyze failure rates
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                self.logger.error(f"Health checker error: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_aggregator(self):
        """Background task to aggregate webhook metrics."""
        while self.is_running:
            try:
                # Aggregate delivery metrics
                # Calculate success rates, response times, etc.
                
                await asyncio.sleep(60)  # 1 minute
                
            except Exception as e:
                self.logger.error(f"Metrics aggregator error: {e}")
                await asyncio.sleep(30)