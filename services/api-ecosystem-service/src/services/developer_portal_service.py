"""
Developer Portal Service

Comprehensive developer portal providing:
- Interactive API documentation
- API testing playground
- Code examples and tutorials
- SDK downloads
- Developer onboarding
- Community features
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid
from pathlib import Path

import structlog
import markdown
from jinja2 import Environment, FileSystemLoader

from core.config import get_settings
from core.exceptions import APIEcosystemException
from core.monitoring import APIEcosystemMetrics
from models.developer_schemas import (
    Developer, DeveloperTier, Application, Documentation,
    Tutorial, CodeExample, SDKVersion, ForumPost, SupportTicket
)


class DeveloperStatus(Enum):
    """Developer account status."""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"


class DocumentationType(Enum):
    """Types of documentation."""
    API_REFERENCE = "api_reference"
    GUIDE = "guide"
    TUTORIAL = "tutorial"
    EXAMPLE = "example"
    CHANGELOG = "changelog"
    FAQ = "faq"


class DeveloperPortalService:
    """
    Comprehensive developer portal service providing documentation,
    tools, and community features.
    """
    
    def __init__(self, database_client, supabase_client):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.database = database_client
        self.supabase = supabase_client
        self.metrics = APIEcosystemMetrics()
        
        # Template engine for documentation
        self.template_env = Environment(
            loader=FileSystemLoader("templates"),
            autoescape=True
        )
        
        # Markdown processor
        self.markdown_processor = markdown.Markdown(
            extensions=['extra', 'codehilite', 'toc', 'tables']
        )
        
        # Cache for documentation
        self.doc_cache: Dict[str, Any] = {}
        self.cache_ttl = timedelta(hours=1)
        
        # Forum and support
        self.active_tickets: Dict[str, SupportTicket] = {}
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start(self):
        """Start the developer portal service."""
        self.logger.info("Starting Developer Portal Service")
        
        try:
            # Initialize database tables
            await self._initialize_tables()
            
            # Load documentation
            await self._load_documentation()
            
            # Start background tasks
            self.background_tasks.extend([
                asyncio.create_task(self._documentation_updater()),
                asyncio.create_task(self._metrics_collector()),
                asyncio.create_task(self._support_monitor()),
                asyncio.create_task(self._community_moderator())
            ])
            
            self.is_running = True
            self.logger.info("Developer Portal Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start developer portal service: {e}")
            raise APIEcosystemException(f"Developer portal startup failed: {e}")
    
    async def stop(self):
        """Stop the developer portal service."""
        self.logger.info("Stopping Developer Portal Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.logger.info("Developer Portal Service stopped")
    
    async def register_developer(
        self,
        email: str,
        company: Optional[str] = None,
        use_case: Optional[str] = None
    ) -> Developer:
        """
        Register a new developer account.
        
        Args:
            email: Developer email
            company: Company name
            use_case: Intended use case
        
        Returns:
            Developer account object
        """
        try:
            developer_id = str(uuid.uuid4())
            
            developer_data = {
                "id": developer_id,
                "email": email,
                "company": company,
                "use_case": use_case,
                "tier": DeveloperTier.FREE.value,
                "status": DeveloperStatus.PENDING.value,
                "created_at": datetime.utcnow().isoformat(),
                "verified_at": None,
                "settings": {
                    "notifications": True,
                    "newsletter": True,
                    "public_profile": False
                }
            }
            
            # Store in database
            await self.supabase.table("developers").insert(developer_data).execute()
            
            # Create developer object
            developer = Developer(
                id=developer_id,
                email=email,
                company=company,
                use_case=use_case,
                tier=DeveloperTier.FREE,
                status=DeveloperStatus.PENDING,
                created_at=datetime.utcnow(),
                verified_at=None,
                settings=developer_data["settings"]
            )
            
            # Send verification email
            await self._send_verification_email(developer)
            
            self.logger.info(f"Developer registered: {email}", developer_id=developer_id)
            
            # Update metrics
            self.metrics.developers_registered_total.inc()
            
            return developer
            
        except Exception as e:
            self.logger.error(f"Failed to register developer: {e}")
            raise APIEcosystemException(f"Developer registration failed: {e}")
    
    async def create_application(
        self,
        developer_id: str,
        name: str,
        description: str,
        redirect_uris: List[str] = None,
        webhook_url: Optional[str] = None
    ) -> Application:
        """
        Create a new application for a developer.
        
        Args:
            developer_id: Developer identifier
            name: Application name
            description: Application description
            redirect_uris: OAuth redirect URIs
            webhook_url: Webhook URL for events
        
        Returns:
            Application object
        """
        try:
            app_id = str(uuid.uuid4())
            client_id = f"app_{uuid.uuid4().hex}"
            client_secret = f"secret_{uuid.uuid4().hex}"
            
            app_data = {
                "id": app_id,
                "developer_id": developer_id,
                "name": name,
                "description": description,
                "client_id": client_id,
                "client_secret_hash": self._hash_secret(client_secret),
                "redirect_uris": redirect_uris or [],
                "webhook_url": webhook_url,
                "status": "active",
                "created_at": datetime.utcnow().isoformat(),
                "settings": {
                    "rate_limit_tier": "standard",
                    "allowed_scopes": ["read", "write"],
                    "ip_whitelist": []
                }
            }
            
            # Store in database
            await self.supabase.table("applications").insert(app_data).execute()
            
            # Create application object
            application = Application(
                id=app_id,
                developer_id=developer_id,
                name=name,
                description=description,
                client_id=client_id,
                client_secret=client_secret,  # Return unhashed secret only on creation
                redirect_uris=redirect_uris or [],
                webhook_url=webhook_url,
                status="active",
                created_at=datetime.utcnow(),
                settings=app_data["settings"]
            )
            
            self.logger.info(f"Application created: {name}", app_id=app_id)
            
            # Update metrics
            self.metrics.applications_created_total.inc()
            
            return application
            
        except Exception as e:
            self.logger.error(f"Failed to create application: {e}")
            raise APIEcosystemException(f"Application creation failed: {e}")
    
    async def get_documentation(
        self,
        doc_type: DocumentationType,
        version: str = "latest",
        language: str = "en"
    ) -> Documentation:
        """
        Get documentation by type and version.
        
        Args:
            doc_type: Type of documentation
            version: API version
            language: Language code
        
        Returns:
            Documentation object
        """
        try:
            cache_key = f"{doc_type.value}:{version}:{language}"
            
            # Check cache
            if cache_key in self.doc_cache:
                cached_doc = self.doc_cache[cache_key]
                if cached_doc["expires_at"] > datetime.utcnow():
                    return cached_doc["content"]
            
            # Query database
            result = await self.supabase.table("documentation").select("*").eq(
                "type", doc_type.value
            ).eq("version", version).eq("language", language).single().execute()
            
            if not result.data:
                raise APIEcosystemException(f"Documentation not found: {doc_type.value}")
            
            doc_data = result.data
            
            # Process content (Markdown to HTML)
            processed_content = self._process_documentation(doc_data["content"])
            
            # Create documentation object
            documentation = Documentation(
                id=doc_data["id"],
                type=doc_type,
                title=doc_data["title"],
                content=processed_content,
                raw_content=doc_data["content"],
                version=doc_data["version"],
                language=doc_data["language"],
                tags=doc_data.get("tags", []),
                created_at=datetime.fromisoformat(doc_data["created_at"]),
                updated_at=datetime.fromisoformat(doc_data["updated_at"])
            )
            
            # Cache the documentation
            self.doc_cache[cache_key] = {
                "content": documentation,
                "expires_at": datetime.utcnow() + self.cache_ttl
            }
            
            # Track view
            await self._track_documentation_view(doc_data["id"])
            
            return documentation
            
        except Exception as e:
            self.logger.error(f"Failed to get documentation: {e}")
            raise APIEcosystemException(f"Documentation retrieval failed: {e}")
    
    async def get_code_examples(
        self,
        language: str,
        feature: Optional[str] = None,
        limit: int = 10
    ) -> List[CodeExample]:
        """
        Get code examples for a programming language.
        
        Args:
            language: Programming language
            feature: Specific feature/endpoint
            limit: Maximum examples to return
        
        Returns:
            List of code examples
        """
        try:
            query = self.supabase.table("code_examples").select("*").eq(
                "language", language
            )
            
            if feature:
                query = query.eq("feature", feature)
            
            result = await query.limit(limit).execute()
            
            examples = []
            for row in result.data:
                example = CodeExample(
                    id=row["id"],
                    title=row["title"],
                    description=row["description"],
                    language=row["language"],
                    feature=row["feature"],
                    code=row["code"],
                    dependencies=row.get("dependencies", []),
                    output_example=row.get("output_example"),
                    tags=row.get("tags", []),
                    created_at=datetime.fromisoformat(row["created_at"])
                )
                examples.append(example)
            
            return examples
            
        except Exception as e:
            self.logger.error(f"Failed to get code examples: {e}")
            raise APIEcosystemException(f"Code examples retrieval failed: {e}")
    
    async def create_support_ticket(
        self,
        developer_id: str,
        subject: str,
        description: str,
        category: str,
        priority: str = "normal"
    ) -> SupportTicket:
        """
        Create a support ticket.
        
        Args:
            developer_id: Developer identifier
            subject: Ticket subject
            description: Issue description
            category: Ticket category
            priority: Ticket priority
        
        Returns:
            Support ticket object
        """
        try:
            ticket_id = str(uuid.uuid4())
            ticket_number = f"TICK-{datetime.utcnow().strftime('%Y%m%d')}-{ticket_id[:8]}"
            
            ticket_data = {
                "id": ticket_id,
                "ticket_number": ticket_number,
                "developer_id": developer_id,
                "subject": subject,
                "description": description,
                "category": category,
                "priority": priority,
                "status": "open",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "messages": [{
                    "id": str(uuid.uuid4()),
                    "author_id": developer_id,
                    "content": description,
                    "timestamp": datetime.utcnow().isoformat(),
                    "is_staff": False
                }]
            }
            
            # Store in database
            await self.supabase.table("support_tickets").insert(ticket_data).execute()
            
            # Create ticket object
            ticket = SupportTicket(
                id=ticket_id,
                ticket_number=ticket_number,
                developer_id=developer_id,
                subject=subject,
                description=description,
                category=category,
                priority=priority,
                status="open",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                messages=ticket_data["messages"]
            )
            
            # Add to active tickets
            self.active_tickets[ticket_id] = ticket
            
            self.logger.info(f"Support ticket created: {ticket_number}", ticket_id=ticket_id)
            
            # Update metrics
            self.metrics.support_tickets_created_total.labels(
                category=category,
                priority=priority
            ).inc()
            
            # Notify support team
            await self._notify_support_team(ticket)
            
            return ticket
            
        except Exception as e:
            self.logger.error(f"Failed to create support ticket: {e}")
            raise APIEcosystemException(f"Support ticket creation failed: {e}")
    
    async def create_forum_post(
        self,
        developer_id: str,
        category: str,
        title: str,
        content: str,
        tags: List[str] = None
    ) -> ForumPost:
        """
        Create a forum post.
        
        Args:
            developer_id: Developer identifier
            category: Forum category
            title: Post title
            content: Post content
            tags: Post tags
        
        Returns:
            Forum post object
        """
        try:
            post_id = str(uuid.uuid4())
            
            post_data = {
                "id": post_id,
                "developer_id": developer_id,
                "category": category,
                "title": title,
                "content": content,
                "content_html": self.markdown_processor.convert(content),
                "tags": tags or [],
                "status": "published",
                "views": 0,
                "likes": 0,
                "replies": 0,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Store in database
            await self.supabase.table("forum_posts").insert(post_data).execute()
            
            # Create post object
            post = ForumPost(
                id=post_id,
                developer_id=developer_id,
                category=category,
                title=title,
                content=content,
                content_html=post_data["content_html"],
                tags=tags or [],
                status="published",
                views=0,
                likes=0,
                replies=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.logger.info(f"Forum post created: {title}", post_id=post_id)
            
            # Update metrics
            self.metrics.forum_posts_created_total.labels(category=category).inc()
            
            # Notify subscribers
            await self._notify_forum_subscribers(post)
            
            return post
            
        except Exception as e:
            self.logger.error(f"Failed to create forum post: {e}")
            raise APIEcosystemException(f"Forum post creation failed: {e}")
    
    async def get_tutorials(
        self,
        difficulty: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Tutorial]:
        """
        Get tutorials with optional filtering.
        
        Args:
            difficulty: Filter by difficulty level
            tags: Filter by tags
            limit: Maximum tutorials to return
        
        Returns:
            List of tutorials
        """
        try:
            query = self.supabase.table("tutorials").select("*")
            
            if difficulty:
                query = query.eq("difficulty", difficulty)
            
            if tags:
                # Filter by tags (assuming PostgreSQL array operations)
                query = query.contains("tags", tags)
            
            result = await query.order("created_at", desc=True).limit(limit).execute()
            
            tutorials = []
            for row in result.data:
                tutorial = Tutorial(
                    id=row["id"],
                    title=row["title"],
                    description=row["description"],
                    content=row["content"],
                    difficulty=row["difficulty"],
                    estimated_time_minutes=row["estimated_time_minutes"],
                    prerequisites=row.get("prerequisites", []),
                    tags=row.get("tags", []),
                    author_id=row["author_id"],
                    views=row["views"],
                    completed_count=row["completed_count"],
                    rating=row.get("rating"),
                    created_at=datetime.fromisoformat(row["created_at"]),
                    updated_at=datetime.fromisoformat(row["updated_at"])
                )
                tutorials.append(tutorial)
            
            return tutorials
            
        except Exception as e:
            self.logger.error(f"Failed to get tutorials: {e}")
            raise APIEcosystemException(f"Tutorials retrieval failed: {e}")
    
    async def get_sdk_versions(
        self,
        language: str
    ) -> List[SDKVersion]:
        """
        Get available SDK versions for a language.
        
        Args:
            language: Programming language
        
        Returns:
            List of SDK versions
        """
        try:
            result = await self.supabase.table("sdk_versions").select("*").eq(
                "language", language
            ).order("released_at", desc=True).execute()
            
            versions = []
            for row in result.data:
                version = SDKVersion(
                    id=row["id"],
                    language=row["language"],
                    version=row["version"],
                    release_notes=row["release_notes"],
                    download_url=row["download_url"],
                    documentation_url=row["documentation_url"],
                    min_language_version=row.get("min_language_version"),
                    dependencies=row.get("dependencies", {}),
                    checksum=row["checksum"],
                    size_bytes=row["size_bytes"],
                    released_at=datetime.fromisoformat(row["released_at"]),
                    deprecated=row.get("deprecated", False)
                )
                versions.append(version)
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Failed to get SDK versions: {e}")
            raise APIEcosystemException(f"SDK versions retrieval failed: {e}")
    
    async def track_api_test(
        self,
        developer_id: str,
        endpoint: str,
        method: str,
        status_code: int,
        response_time_ms: float
    ):
        """
        Track API test from playground.
        
        Args:
            developer_id: Developer identifier
            endpoint: API endpoint tested
            method: HTTP method
            status_code: Response status code
            response_time_ms: Response time in milliseconds
        """
        try:
            test_data = {
                "id": str(uuid.uuid4()),
                "developer_id": developer_id,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time_ms": response_time_ms,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.supabase.table("api_playground_tests").insert(test_data).execute()
            
            # Update metrics
            self.metrics.api_playground_tests_total.labels(
                endpoint=endpoint,
                method=method,
                status_code=str(status_code)
            ).inc()
            
            self.metrics.api_playground_response_time.labels(
                endpoint=endpoint,
                method=method
            ).observe(response_time_ms / 1000.0)  # Convert to seconds
            
        except Exception as e:
            self.logger.error(f"Failed to track API test: {e}")
    
    async def get_developer_stats(self, developer_id: str) -> Dict[str, Any]:
        """
        Get developer statistics and usage.
        
        Args:
            developer_id: Developer identifier
        
        Returns:
            Developer statistics
        """
        try:
            # Get developer info
            dev_result = await self.supabase.table("developers").select("*").eq(
                "id", developer_id
            ).single().execute()
            
            if not dev_result.data:
                raise APIEcosystemException("Developer not found")
            
            # Get applications
            apps_result = await self.supabase.table("applications").select("id").eq(
                "developer_id", developer_id
            ).execute()
            
            # Get API usage
            usage_result = await self.supabase.rpc("get_developer_api_usage", {
                "p_developer_id": developer_id,
                "p_days": 30
            }).execute()
            
            # Get forum activity
            forum_result = await self.supabase.table("forum_posts").select("id").eq(
                "developer_id", developer_id
            ).execute()
            
            return {
                "developer": {
                    "id": developer_id,
                    "tier": dev_result.data["tier"],
                    "status": dev_result.data["status"],
                    "created_at": dev_result.data["created_at"]
                },
                "applications": {
                    "total": len(apps_result.data),
                    "active": len([app for app in apps_result.data if app.get("status") == "active"])
                },
                "api_usage": {
                    "last_30_days": usage_result.data.get("total_requests", 0),
                    "daily_average": usage_result.data.get("daily_average", 0),
                    "top_endpoints": usage_result.data.get("top_endpoints", [])
                },
                "community": {
                    "forum_posts": len(forum_result.data),
                    "reputation": dev_result.data.get("reputation", 0)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get developer stats: {e}")
            raise APIEcosystemException(f"Developer stats retrieval failed: {e}")
    
    # Private helper methods
    
    def _hash_secret(self, secret: str) -> str:
        """Hash a secret for storage."""
        import hashlib
        return hashlib.sha256(secret.encode()).hexdigest()
    
    def _process_documentation(self, content: str) -> str:
        """Process documentation content (Markdown to HTML)."""
        try:
            # Convert Markdown to HTML
            html_content = self.markdown_processor.convert(content)
            
            # Add syntax highlighting classes
            html_content = html_content.replace('<pre>', '<pre class="highlight">')
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"Failed to process documentation: {e}")
            return content  # Return raw content on error
    
    async def _send_verification_email(self, developer: Developer):
        """Send verification email to developer."""
        try:
            # Generate verification token
            token = str(uuid.uuid4())
            
            # Store token
            await self.supabase.table("verification_tokens").insert({
                "token": token,
                "developer_id": developer.id,
                "expires_at": (datetime.utcnow() + timedelta(hours=24)).isoformat()
            }).execute()
            
            # Send email (placeholder - would integrate with email service)
            self.logger.info(f"Verification email sent to {developer.email}")
            
        except Exception as e:
            self.logger.error(f"Failed to send verification email: {e}")
    
    async def _track_documentation_view(self, doc_id: str):
        """Track documentation view."""
        try:
            await self.supabase.rpc("increment_doc_views", {"doc_id": doc_id}).execute()
        except Exception as e:
            self.logger.error(f"Failed to track documentation view: {e}")
    
    async def _notify_support_team(self, ticket: SupportTicket):
        """Notify support team about new ticket."""
        try:
            # Send notification to support team
            # This would integrate with Slack, email, etc.
            self.logger.info(f"Support team notified about ticket {ticket.ticket_number}")
            
        except Exception as e:
            self.logger.error(f"Failed to notify support team: {e}")
    
    async def _notify_forum_subscribers(self, post: ForumPost):
        """Notify forum subscribers about new post."""
        try:
            # Get subscribers for the category
            subscribers = await self.supabase.table("forum_subscriptions").select(
                "developer_id"
            ).eq("category", post.category).execute()
            
            # Send notifications
            for sub in subscribers.data:
                # Queue notification
                pass
            
        except Exception as e:
            self.logger.error(f"Failed to notify forum subscribers: {e}")
    
    async def _initialize_tables(self):
        """Initialize database tables."""
        # Tables are created via Supabase migrations
        pass
    
    async def _load_documentation(self):
        """Load initial documentation."""
        # Load base documentation into cache
        pass
    
    async def _documentation_updater(self):
        """Background task to update documentation."""
        while self.is_running:
            try:
                # Check for documentation updates
                # Regenerate processed content if needed
                
                await asyncio.sleep(3600)  # 1 hour
                
            except Exception as e:
                self.logger.error(f"Documentation update failed: {e}")
                await asyncio.sleep(300)
    
    async def _metrics_collector(self):
        """Background task to collect portal metrics."""
        while self.is_running:
            try:
                # Collect and aggregate metrics
                # Update dashboards
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                self.logger.error(f"Metrics collection failed: {e}")
                await asyncio.sleep(60)
    
    async def _support_monitor(self):
        """Background task to monitor support tickets."""
        while self.is_running:
            try:
                # Check for SLA violations
                # Auto-escalate high priority tickets
                # Send reminders for pending tickets
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                self.logger.error(f"Support monitoring failed: {e}")
                await asyncio.sleep(60)
    
    async def _community_moderator(self):
        """Background task for community moderation."""
        while self.is_running:
            try:
                # Check for spam or inappropriate content
                # Update reputation scores
                # Handle flagged content
                
                await asyncio.sleep(600)  # 10 minutes
                
            except Exception as e:
                self.logger.error(f"Community moderation failed: {e}")
                await asyncio.sleep(300)