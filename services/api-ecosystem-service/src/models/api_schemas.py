"""
API Management Schemas

Pydantic models for API management, keys, and usage tracking.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid

from pydantic import BaseModel, Field, validator


class APIKeyType(Enum):
    """Types of API keys."""
    PUBLIC = "public"
    STANDARD = "standard"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"
    INTERNAL = "internal"


class APIKeyStatus(Enum):
    """API key status."""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REVOKED = "revoked"
    EXPIRED = "expired"


class QuotaPeriod(Enum):
    """Quota period types."""
    HOURLY = "hourly"
    DAILY = "daily"
    MONTHLY = "monthly"
    YEARLY = "yearly"


class APIKeyPermissions(BaseModel):
    """API key permissions model."""
    read: List[str] = Field(default_factory=list, description="Read permissions")
    write: List[str] = Field(default_factory=list, description="Write permissions")
    delete: List[str] = Field(default_factory=list, description="Delete permissions")
    admin: List[str] = Field(default_factory=list, description="Admin permissions")


class RateLimitConfig(BaseModel):
    """Rate limit configuration model."""
    requests: int = Field(..., description="Number of requests allowed")
    window_seconds: int = Field(..., description="Time window in seconds")
    burst_multiplier: float = Field(default=1.5, description="Burst multiplier for short peaks")


class UsageQuota(BaseModel):
    """Usage quota model."""
    resource_type: str = Field(..., description="Type of resource")
    limit: int = Field(..., description="Quota limit")
    period: QuotaPeriod = Field(..., description="Quota period")
    current_usage: int = Field(default=0, description="Current usage")
    reset_at: datetime = Field(..., description="When quota resets")


class APIKeyCreate(BaseModel):
    """API key creation request model."""
    name: str = Field(..., min_length=1, max_length=100, description="API key name")
    key_type: APIKeyType = Field(default=APIKeyType.STANDARD, description="API key type")
    permissions: Optional[APIKeyPermissions] = Field(default=None, description="Custom permissions")
    expires_at: Optional[datetime] = Field(default=None, description="Expiration date")
    description: Optional[str] = Field(default=None, max_length=500, description="Key description")
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()


class APIKey(BaseModel):
    """API key model."""
    id: str = Field(..., description="API key ID")
    user_id: str = Field(..., description="User ID")
    name: str = Field(..., description="API key name")
    key_value: Optional[str] = Field(default=None, description="API key value (only on creation)")
    key_prefix: str = Field(..., description="Key prefix")
    key_type: APIKeyType = Field(..., description="API key type")
    permissions: Dict[str, Any] = Field(default_factory=dict, description="Key permissions")
    status: APIKeyStatus = Field(..., description="Key status")
    created_at: datetime = Field(..., description="Creation timestamp")
    expires_at: Optional[datetime] = Field(default=None, description="Expiration timestamp")
    last_used_at: Optional[datetime] = Field(default=None, description="Last used timestamp")
    usage_count: int = Field(default=0, description="Total usage count")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class APIKeyUpdate(BaseModel):
    """API key update request model."""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100)
    status: Optional[APIKeyStatus] = Field(default=None)
    permissions: Optional[APIKeyPermissions] = Field(default=None)
    expires_at: Optional[datetime] = Field(default=None)


class APIVersion(BaseModel):
    """API version model."""
    version: str = Field(..., description="Version number")
    status: str = Field(..., description="Version status")
    release_date: datetime = Field(..., description="Release date")
    deprecation_date: Optional[datetime] = Field(default=None, description="Deprecation date")
    endpoints: List[str] = Field(default_factory=list, description="Available endpoints")
    changelog: str = Field(default="", description="Version changelog")


class AccessLog(BaseModel):
    """API access log model."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Log ID")
    api_key_id: str = Field(..., description="API key ID")
    endpoint: str = Field(..., description="API endpoint")
    method: str = Field(..., description="HTTP method")
    status_code: int = Field(..., description="Response status code")
    response_time_ms: float = Field(..., description="Response time in milliseconds")
    request_size_bytes: Optional[int] = Field(default=None, description="Request size")
    response_size_bytes: Optional[int] = Field(default=None, description="Response size")
    ip_address: str = Field(..., description="Client IP address")
    user_agent: Optional[str] = Field(default=None, description="User agent")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Access timestamp")
    error_message: Optional[str] = Field(default=None, description="Error message if any")


class RateLimitInfo(BaseModel):
    """Rate limit information model."""
    limit: int = Field(..., description="Rate limit")
    remaining: int = Field(..., description="Remaining requests")
    reset_at: datetime = Field(..., description="When limit resets")
    window_seconds: int = Field(..., description="Window duration")


class QuotaInfo(BaseModel):
    """Quota information model."""
    resource_type: str = Field(..., description="Resource type")
    limit: int = Field(..., description="Quota limit")
    remaining: int = Field(..., description="Remaining quota")
    period: QuotaPeriod = Field(..., description="Quota period")
    reset_at: datetime = Field(..., description="When quota resets")


class UsageStats(BaseModel):
    """Usage statistics model."""
    total_requests: int = Field(..., description="Total requests")
    successful_requests: int = Field(..., description="Successful requests")
    failed_requests: int = Field(..., description="Failed requests")
    average_response_time_ms: float = Field(..., description="Average response time")
    top_endpoints: List[Dict[str, Any]] = Field(default_factory=list, description="Top endpoints")
    usage_by_hour: Dict[str, int] = Field(default_factory=dict, description="Hourly usage")
    usage_by_day: Dict[str, int] = Field(default_factory=dict, description="Daily usage")


class APIKeyValidationResponse(BaseModel):
    """API key validation response model."""
    valid: bool = Field(..., description="Whether key is valid")
    api_key: Optional[APIKey] = Field(default=None, description="API key details")
    rate_limit: Optional[RateLimitInfo] = Field(default=None, description="Rate limit info")
    quotas: List[QuotaInfo] = Field(default_factory=list, description="Quota information")
    permissions: List[str] = Field(default_factory=list, description="Granted permissions")


class APIKeyListResponse(BaseModel):
    """API key list response model."""
    keys: List[APIKey] = Field(..., description="List of API keys")
    total: int = Field(..., description="Total number of keys")
    page: int = Field(..., description="Current page")
    per_page: int = Field(..., description="Items per page")
    has_next: bool = Field(..., description="Whether there are more pages")


class UsageReport(BaseModel):
    """Usage report model."""
    api_key_id: str = Field(..., description="API key ID")
    period_start: datetime = Field(..., description="Report period start")
    period_end: datetime = Field(..., description="Report period end")
    total_requests: int = Field(..., description="Total requests in period")
    successful_requests: int = Field(..., description="Successful requests")
    failed_requests: int = Field(..., description="Failed requests")
    data_transfer_mb: float = Field(..., description="Data transfer in MB")
    top_endpoints: List[Dict[str, Any]] = Field(default_factory=list)
    hourly_breakdown: Dict[str, int] = Field(default_factory=dict)
    error_breakdown: Dict[str, int] = Field(default_factory=dict)


class APIHealthCheck(BaseModel):
    """API health check model."""
    status: str = Field(..., description="Health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(..., description="API version")
    uptime_seconds: float = Field(..., description="Uptime in seconds")
    database_status: str = Field(..., description="Database connection status")
    redis_status: str = Field(..., description="Redis connection status")
    external_services: Dict[str, str] = Field(default_factory=dict, description="External service status")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(default=None, description="Request ID for tracking")