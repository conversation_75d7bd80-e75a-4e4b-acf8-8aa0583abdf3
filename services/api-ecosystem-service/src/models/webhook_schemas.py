"""
Webhook Schemas

Pydantic models for webhook management and event delivery.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import uuid

from pydantic import BaseModel, Field, validator, HttpUrl


class EventType(Enum):
    """Types of webhook events."""
    # API Events
    API_KEY_CREATED = "api.key.created"
    API_KEY_REVOKED = "api.key.revoked"
    API_KEY_ROTATED = "api.key.rotated"
    
    # Application Events
    APPLICATION_CREATED = "application.created"
    APPLICATION_UPDATED = "application.updated"
    APPLICATION_DELETED = "application.deleted"
    
    # Usage Events
    QUOTA_EXCEEDED = "usage.quota.exceeded"
    RATE_LIMIT_EXCEEDED = "usage.rate_limit.exceeded"
    USAGE_THRESHOLD_REACHED = "usage.threshold.reached"
    
    # Developer Events
    DEVELOPER_REGISTERED = "developer.registered"
    DEVELOPER_VERIFIED = "developer.verified"
    DEVELOPER_TIER_CHANGED = "developer.tier.changed"
    
    # Support Events
    TICKET_CREATED = "support.ticket.created"
    TICKET_UPDATED = "support.ticket.updated"
    TICKET_RESOLVED = "support.ticket.resolved"
    
    # Forum Events
    POST_CREATED = "forum.post.created"
    POST_LIKED = "forum.post.liked"
    REPLY_CREATED = "forum.reply.created"
    
    # System Events
    MAINTENANCE_SCHEDULED = "system.maintenance.scheduled"
    MAINTENANCE_STARTED = "system.maintenance.started"
    MAINTENANCE_COMPLETED = "system.maintenance.completed"
    
    # Security Events
    SECURITY_ALERT = "security.alert"
    SUSPICIOUS_ACTIVITY = "security.suspicious_activity"
    
    # Test Events
    WEBHOOK_TEST = "webhook.test"


class EventPriority(Enum):
    """Event priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class WebhookStatus(Enum):
    """Webhook status."""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"
    DELETED = "deleted"


class DeliveryStatus(Enum):
    """Webhook delivery status."""
    PENDING = "pending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"


class WebhookCreate(BaseModel):
    """Webhook creation request model."""
    url: HttpUrl = Field(..., description="Webhook endpoint URL")
    events: List[EventType] = Field(..., min_items=1, description="Event types to subscribe to")
    secret: Optional[str] = Field(default=None, min_length=8, description="Webhook secret")
    description: Optional[str] = Field(default=None, max_length=500, description="Webhook description")
    headers: Optional[Dict[str, str]] = Field(default=None, description="Custom headers")
    timeout_seconds: Optional[int] = Field(default=30, ge=1, le=120, description="Request timeout")
    
    @validator('events')
    def validate_events(cls, v):
        if not v:
            raise ValueError('At least one event type must be specified')
        return v


class Webhook(BaseModel):
    """Webhook model."""
    id: str = Field(..., description="Webhook ID")
    application_id: str = Field(..., description="Application ID")
    url: str = Field(..., description="Webhook endpoint URL")
    secret: Optional[str] = Field(default=None, description="Webhook secret (only on creation)")
    events: List[EventType] = Field(..., description="Subscribed event types")
    description: Optional[str] = Field(default=None, description="Webhook description")
    status: WebhookStatus = Field(..., description="Webhook status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Update timestamp")
    last_triggered_at: Optional[datetime] = Field(default=None, description="Last trigger timestamp")
    failure_count: int = Field(default=0, description="Consecutive failure count")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class WebhookUpdate(BaseModel):
    """Webhook update request model."""
    url: Optional[HttpUrl] = Field(default=None, description="New webhook URL")
    events: Optional[List[EventType]] = Field(default=None, description="New event subscriptions")
    status: Optional[WebhookStatus] = Field(default=None, description="New webhook status")
    description: Optional[str] = Field(default=None, max_length=500, description="New description")
    headers: Optional[Dict[str, str]] = Field(default=None, description="Custom headers")
    timeout_seconds: Optional[int] = Field(default=None, ge=1, le=120, description="Request timeout")


class EventPayload(BaseModel):
    """Base event payload model."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Event ID")
    type: EventType = Field(..., description="Event type")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    data: Dict[str, Any] = Field(..., description="Event data")
    application_id: Optional[str] = Field(default=None, description="Application ID")
    user_id: Optional[str] = Field(default=None, description="User ID")


class WebhookEvent(BaseModel):
    """Webhook event model."""
    id: str = Field(..., description="Event ID")
    type: EventType = Field(..., description="Event type")
    payload: Dict[str, Any] = Field(..., description="Event payload")
    application_id: Optional[str] = Field(default=None, description="Application ID")
    created_at: datetime = Field(..., description="Event timestamp")
    priority: EventPriority = Field(default=EventPriority.NORMAL, description="Event priority")


class DeliveryAttempt(BaseModel):
    """Webhook delivery attempt model."""
    id: str = Field(..., description="Attempt ID")
    webhook_id: str = Field(..., description="Webhook ID")
    event_id: str = Field(..., description="Event ID")
    attempt_number: int = Field(..., description="Attempt number")
    status: DeliveryStatus = Field(..., description="Delivery status")
    status_code: Optional[int] = Field(default=None, description="HTTP status code")
    response_body: Optional[str] = Field(default=None, description="Response body")
    response_time_ms: Optional[float] = Field(default=None, description="Response time in milliseconds")
    attempted_at: datetime = Field(..., description="Attempt timestamp")
    error_message: Optional[str] = Field(default=None, description="Error message")


class WebhookSubscription(BaseModel):
    """Webhook subscription model."""
    webhook_id: str = Field(..., description="Webhook ID")
    event_type: EventType = Field(..., description="Event type")
    active: bool = Field(default=True, description="Whether subscription is active")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Event filters")


class WebhookDeliveryReport(BaseModel):
    """Webhook delivery report model."""
    webhook_id: str = Field(..., description="Webhook ID")
    period_start: datetime = Field(..., description="Report period start")
    period_end: datetime = Field(..., description="Report period end")
    total_events: int = Field(..., description="Total events sent")
    successful_deliveries: int = Field(..., description="Successful deliveries")
    failed_deliveries: int = Field(..., description="Failed deliveries")
    average_response_time_ms: float = Field(..., description="Average response time")
    success_rate: float = Field(..., description="Success rate percentage")
    failure_reasons: Dict[str, int] = Field(default_factory=dict, description="Failure reasons breakdown")


class WebhookTestRequest(BaseModel):
    """Webhook test request model."""
    webhook_id: str = Field(..., description="Webhook ID to test")
    event_type: Optional[EventType] = Field(default=EventType.WEBHOOK_TEST, description="Event type to send")
    custom_payload: Optional[Dict[str, Any]] = Field(default=None, description="Custom test payload")


class WebhookTestResponse(BaseModel):
    """Webhook test response model."""
    delivery_attempt: DeliveryAttempt = Field(..., description="Delivery attempt details")
    success: bool = Field(..., description="Whether test was successful")
    message: str = Field(..., description="Test result message")


class EventReplayRequest(BaseModel):
    """Event replay request model."""
    event_id: str = Field(..., description="Event ID to replay")
    webhook_ids: Optional[List[str]] = Field(default=None, description="Specific webhooks to replay to")
    force: bool = Field(default=False, description="Force replay even if already delivered")


class EventReplayResponse(BaseModel):
    """Event replay response model."""
    event_id: str = Field(..., description="Event ID")
    delivery_attempts: List[str] = Field(..., description="Delivery attempt IDs")
    replayed_to: int = Field(..., description="Number of webhooks replayed to")
    message: str = Field(..., description="Replay result message")


class WebhookSecurityInfo(BaseModel):
    """Webhook security information model."""
    webhook_id: str = Field(..., description="Webhook ID")
    signature_method: str = Field(default="HMAC-SHA256", description="Signature method")
    signature_header: str = Field(default="X-Webhook-Signature", description="Signature header name")
    timestamp_header: str = Field(default="X-Webhook-Timestamp", description="Timestamp header name")
    verification_guide: str = Field(..., description="Signature verification guide")


class WebhookEventFilter(BaseModel):
    """Webhook event filter model."""
    event_types: Optional[List[EventType]] = Field(default=None, description="Filter by event types")
    application_id: Optional[str] = Field(default=None, description="Filter by application")
    start_date: Optional[datetime] = Field(default=None, description="Filter by start date")
    end_date: Optional[datetime] = Field(default=None, description="Filter by end date")
    status: Optional[DeliveryStatus] = Field(default=None, description="Filter by delivery status")
    limit: int = Field(default=50, ge=1, le=1000, description="Maximum results")
    offset: int = Field(default=0, ge=0, description="Results offset")


class WebhookListResponse(BaseModel):
    """Webhook list response model."""
    webhooks: List[Webhook] = Field(..., description="List of webhooks")
    total: int = Field(..., description="Total number of webhooks")
    page: int = Field(..., description="Current page")
    per_page: int = Field(..., description="Items per page")
    has_next: bool = Field(..., description="Whether there are more pages")


class EventListResponse(BaseModel):
    """Event list response model."""
    events: List[Dict[str, Any]] = Field(..., description="List of events with delivery info")
    total: int = Field(..., description="Total number of events")
    page: int = Field(..., description="Current page")
    per_page: int = Field(..., description="Items per page")
    has_next: bool = Field(..., description="Whether there are more pages")


class WebhookStats(BaseModel):
    """Webhook statistics model."""
    total_webhooks: int = Field(..., description="Total number of webhooks")
    active_webhooks: int = Field(..., description="Number of active webhooks")
    total_events_sent: int = Field(..., description="Total events sent")
    successful_deliveries: int = Field(..., description="Successful deliveries")
    failed_deliveries: int = Field(..., description="Failed deliveries")
    average_success_rate: float = Field(..., description="Average success rate")
    most_popular_events: List[Dict[str, Any]] = Field(default_factory=list, description="Most popular event types")


# Event-specific payload models

class APIKeyEventData(BaseModel):
    """API key event data."""
    api_key_id: str = Field(..., description="API key ID")
    key_type: str = Field(..., description="API key type")
    user_id: str = Field(..., description="User ID")
    application_id: Optional[str] = Field(default=None, description="Application ID")


class ApplicationEventData(BaseModel):
    """Application event data."""
    application_id: str = Field(..., description="Application ID")
    name: str = Field(..., description="Application name")
    developer_id: str = Field(..., description="Developer ID")


class UsageEventData(BaseModel):
    """Usage event data."""
    api_key_id: str = Field(..., description="API key ID")
    resource_type: str = Field(..., description="Resource type")
    usage_amount: int = Field(..., description="Usage amount")
    limit: int = Field(..., description="Usage limit")
    percentage: float = Field(..., description="Usage percentage")


class DeveloperEventData(BaseModel):
    """Developer event data."""
    developer_id: str = Field(..., description="Developer ID")
    email: str = Field(..., description="Developer email")
    tier: Optional[str] = Field(default=None, description="Developer tier")
    previous_tier: Optional[str] = Field(default=None, description="Previous tier")


class SupportTicketEventData(BaseModel):
    """Support ticket event data."""
    ticket_id: str = Field(..., description="Ticket ID")
    ticket_number: str = Field(..., description="Ticket number")
    developer_id: str = Field(..., description="Developer ID")
    subject: str = Field(..., description="Ticket subject")
    priority: str = Field(..., description="Ticket priority")
    status: str = Field(..., description="Ticket status")


class ForumEventData(BaseModel):
    """Forum event data."""
    post_id: str = Field(..., description="Post ID")
    title: str = Field(..., description="Post title")
    category: str = Field(..., description="Post category")
    author_id: str = Field(..., description="Author ID")


class SecurityEventData(BaseModel):
    """Security event data."""
    alert_type: str = Field(..., description="Alert type")
    severity: str = Field(..., description="Alert severity")
    description: str = Field(..., description="Alert description")
    source_ip: Optional[str] = Field(default=None, description="Source IP address")
    affected_resources: List[str] = Field(default_factory=list, description="Affected resources")