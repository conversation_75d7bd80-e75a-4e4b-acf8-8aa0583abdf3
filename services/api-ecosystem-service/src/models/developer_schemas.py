"""
Developer Portal Schemas

Pydantic models for developer portal, documentation, and community features.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import uuid

from pydantic import BaseModel, Field, validator, EmailStr, HttpUrl


class DeveloperTier(Enum):
    """Developer tier levels."""
    FREE = "free"
    BASIC = "basic"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class DeveloperStatus(Enum):
    """Developer account status."""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"


class DocumentationType(Enum):
    """Types of documentation."""
    API_REFERENCE = "api_reference"
    GUIDE = "guide"
    TUTORIAL = "tutorial"
    EXAMPLE = "example"
    CHANGELOG = "changelog"
    FAQ = "faq"


class TutorialDifficulty(Enum):
    """Tutorial difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class SupportTicketStatus(Enum):
    """Support ticket status."""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    WAITING_FOR_CUSTOMER = "waiting_for_customer"
    RESOLVED = "resolved"
    CLOSED = "closed"


class SupportTicketPriority(Enum):
    """Support ticket priority."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class ForumPostStatus(Enum):
    """Forum post status."""
    DRAFT = "draft"
    PUBLISHED = "published"
    HIDDEN = "hidden"
    DELETED = "deleted"


class DeveloperCreate(BaseModel):
    """Developer registration request model."""
    email: EmailStr = Field(..., description="Developer email address")
    company: Optional[str] = Field(default=None, max_length=200, description="Company name")
    use_case: Optional[str] = Field(default=None, max_length=1000, description="Intended use case")
    first_name: Optional[str] = Field(default=None, max_length=100, description="First name")
    last_name: Optional[str] = Field(default=None, max_length=100, description="Last name")
    website: Optional[HttpUrl] = Field(default=None, description="Company/personal website")
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower().strip()


class Developer(BaseModel):
    """Developer model."""
    id: str = Field(..., description="Developer ID")
    email: str = Field(..., description="Email address")
    company: Optional[str] = Field(default=None, description="Company name")
    use_case: Optional[str] = Field(default=None, description="Use case description")
    first_name: Optional[str] = Field(default=None, description="First name")
    last_name: Optional[str] = Field(default=None, description="Last name")
    website: Optional[str] = Field(default=None, description="Website URL")
    tier: DeveloperTier = Field(..., description="Developer tier")
    status: DeveloperStatus = Field(..., description="Account status")
    created_at: datetime = Field(..., description="Registration timestamp")
    verified_at: Optional[datetime] = Field(default=None, description="Email verification timestamp")
    last_login_at: Optional[datetime] = Field(default=None, description="Last login timestamp")
    reputation: int = Field(default=0, description="Community reputation score")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Developer settings")


class DeveloperUpdate(BaseModel):
    """Developer update request model."""
    company: Optional[str] = Field(default=None, max_length=200)
    use_case: Optional[str] = Field(default=None, max_length=1000)
    first_name: Optional[str] = Field(default=None, max_length=100)
    last_name: Optional[str] = Field(default=None, max_length=100)
    website: Optional[HttpUrl] = Field(default=None)
    settings: Optional[Dict[str, Any]] = Field(default=None)


class ApplicationCreate(BaseModel):
    """Application creation request model."""
    name: str = Field(..., min_length=1, max_length=100, description="Application name")
    description: str = Field(..., min_length=1, max_length=1000, description="Application description")
    redirect_uris: Optional[List[HttpUrl]] = Field(default=None, description="OAuth redirect URIs")
    webhook_url: Optional[HttpUrl] = Field(default=None, description="Webhook URL")
    logo_url: Optional[HttpUrl] = Field(default=None, description="Application logo URL")
    privacy_policy_url: Optional[HttpUrl] = Field(default=None, description="Privacy policy URL")
    terms_of_service_url: Optional[HttpUrl] = Field(default=None, description="Terms of service URL")


class Application(BaseModel):
    """Application model."""
    id: str = Field(..., description="Application ID")
    developer_id: str = Field(..., description="Developer ID")
    name: str = Field(..., description="Application name")
    description: str = Field(..., description="Application description")
    client_id: str = Field(..., description="OAuth client ID")
    client_secret: Optional[str] = Field(default=None, description="OAuth client secret (only on creation)")
    redirect_uris: List[str] = Field(default_factory=list, description="OAuth redirect URIs")
    webhook_url: Optional[str] = Field(default=None, description="Webhook URL")
    logo_url: Optional[str] = Field(default=None, description="Logo URL")
    privacy_policy_url: Optional[str] = Field(default=None, description="Privacy policy URL")
    terms_of_service_url: Optional[str] = Field(default=None, description="Terms of service URL")
    status: str = Field(..., description="Application status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Update timestamp")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Application settings")


class ApplicationUpdate(BaseModel):
    """Application update request model."""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, min_length=1, max_length=1000)
    redirect_uris: Optional[List[HttpUrl]] = Field(default=None)
    webhook_url: Optional[HttpUrl] = Field(default=None)
    logo_url: Optional[HttpUrl] = Field(default=None)
    privacy_policy_url: Optional[HttpUrl] = Field(default=None)
    terms_of_service_url: Optional[HttpUrl] = Field(default=None)
    settings: Optional[Dict[str, Any]] = Field(default=None)


class Documentation(BaseModel):
    """Documentation model."""
    id: str = Field(..., description="Documentation ID")
    type: DocumentationType = Field(..., description="Documentation type")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Processed content (HTML)")
    raw_content: str = Field(..., description="Raw content (Markdown)")
    version: str = Field(..., description="API version")
    language: str = Field(default="en", description="Language code")
    tags: List[str] = Field(default_factory=list, description="Document tags")
    author_id: Optional[str] = Field(default=None, description="Author ID")
    views: int = Field(default=0, description="View count")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")


class DocumentationCreate(BaseModel):
    """Documentation creation request model."""
    type: DocumentationType = Field(..., description="Documentation type")
    title: str = Field(..., min_length=1, max_length=200, description="Document title")
    content: str = Field(..., min_length=1, description="Document content (Markdown)")
    version: str = Field(default="latest", description="API version")
    language: str = Field(default="en", description="Language code")
    tags: Optional[List[str]] = Field(default=None, description="Document tags")


class CodeExample(BaseModel):
    """Code example model."""
    id: str = Field(..., description="Example ID")
    title: str = Field(..., description="Example title")
    description: str = Field(..., description="Example description")
    language: str = Field(..., description="Programming language")
    feature: str = Field(..., description="Feature/endpoint demonstrated")
    code: str = Field(..., description="Code content")
    dependencies: List[str] = Field(default_factory=list, description="Required dependencies")
    output_example: Optional[str] = Field(default=None, description="Expected output")
    tags: List[str] = Field(default_factory=list, description="Example tags")
    author_id: str = Field(..., description="Author ID")
    views: int = Field(default=0, description="View count")
    likes: int = Field(default=0, description="Like count")
    created_at: datetime = Field(..., description="Creation timestamp")


class CodeExampleCreate(BaseModel):
    """Code example creation request model."""
    title: str = Field(..., min_length=1, max_length=200, description="Example title")
    description: str = Field(..., min_length=1, max_length=1000, description="Example description")
    language: str = Field(..., description="Programming language")
    feature: str = Field(..., description="Feature/endpoint demonstrated")
    code: str = Field(..., min_length=1, description="Code content")
    dependencies: Optional[List[str]] = Field(default=None, description="Required dependencies")
    output_example: Optional[str] = Field(default=None, description="Expected output")
    tags: Optional[List[str]] = Field(default=None, description="Example tags")


class Tutorial(BaseModel):
    """Tutorial model."""
    id: str = Field(..., description="Tutorial ID")
    title: str = Field(..., description="Tutorial title")
    description: str = Field(..., description="Tutorial description")
    content: str = Field(..., description="Tutorial content")
    difficulty: TutorialDifficulty = Field(..., description="Difficulty level")
    estimated_time_minutes: int = Field(..., description="Estimated completion time")
    prerequisites: List[str] = Field(default_factory=list, description="Prerequisites")
    tags: List[str] = Field(default_factory=list, description="Tutorial tags")
    author_id: str = Field(..., description="Author ID")
    views: int = Field(default=0, description="View count")
    completed_count: int = Field(default=0, description="Completion count")
    rating: Optional[float] = Field(default=None, description="Average rating")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")


class TutorialCreate(BaseModel):
    """Tutorial creation request model."""
    title: str = Field(..., min_length=1, max_length=200, description="Tutorial title")
    description: str = Field(..., min_length=1, max_length=1000, description="Tutorial description")
    content: str = Field(..., min_length=1, description="Tutorial content")
    difficulty: TutorialDifficulty = Field(..., description="Difficulty level")
    estimated_time_minutes: int = Field(..., gt=0, description="Estimated completion time")
    prerequisites: Optional[List[str]] = Field(default=None, description="Prerequisites")
    tags: Optional[List[str]] = Field(default=None, description="Tutorial tags")


class SDKVersion(BaseModel):
    """SDK version model."""
    id: str = Field(..., description="SDK version ID")
    language: str = Field(..., description="Programming language")
    version: str = Field(..., description="Version number")
    release_notes: str = Field(..., description="Release notes")
    download_url: str = Field(..., description="Download URL")
    documentation_url: str = Field(..., description="Documentation URL")
    min_language_version: Optional[str] = Field(default=None, description="Minimum language version")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="Dependencies")
    checksum: str = Field(..., description="File checksum")
    size_bytes: int = Field(..., description="File size in bytes")
    released_at: datetime = Field(..., description="Release timestamp")
    deprecated: bool = Field(default=False, description="Whether version is deprecated")


class SupportTicketCreate(BaseModel):
    """Support ticket creation request model."""
    subject: str = Field(..., min_length=1, max_length=200, description="Ticket subject")
    description: str = Field(..., min_length=1, max_length=5000, description="Issue description")
    category: str = Field(..., description="Ticket category")
    priority: SupportTicketPriority = Field(default=SupportTicketPriority.NORMAL, description="Ticket priority")
    api_key_id: Optional[str] = Field(default=None, description="Related API key ID")
    application_id: Optional[str] = Field(default=None, description="Related application ID")


class SupportTicket(BaseModel):
    """Support ticket model."""
    id: str = Field(..., description="Ticket ID")
    ticket_number: str = Field(..., description="Ticket number")
    developer_id: str = Field(..., description="Developer ID")
    subject: str = Field(..., description="Ticket subject")
    description: str = Field(..., description="Issue description")
    category: str = Field(..., description="Ticket category")
    priority: SupportTicketPriority = Field(..., description="Ticket priority")
    status: SupportTicketStatus = Field(..., description="Ticket status")
    assigned_to: Optional[str] = Field(default=None, description="Assigned staff member")
    api_key_id: Optional[str] = Field(default=None, description="Related API key ID")
    application_id: Optional[str] = Field(default=None, description="Related application ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")
    resolved_at: Optional[datetime] = Field(default=None, description="Resolution timestamp")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="Ticket messages")


class SupportMessage(BaseModel):
    """Support ticket message model."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Message ID")
    ticket_id: str = Field(..., description="Ticket ID")
    author_id: str = Field(..., description="Message author ID")
    content: str = Field(..., min_length=1, max_length=5000, description="Message content")
    is_staff: bool = Field(default=False, description="Whether author is staff")
    attachments: List[str] = Field(default_factory=list, description="Attachment URLs")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class ForumPostCreate(BaseModel):
    """Forum post creation request model."""
    category: str = Field(..., description="Forum category")
    title: str = Field(..., min_length=1, max_length=200, description="Post title")
    content: str = Field(..., min_length=1, max_length=10000, description="Post content")
    tags: Optional[List[str]] = Field(default=None, description="Post tags")


class ForumPost(BaseModel):
    """Forum post model."""
    id: str = Field(..., description="Post ID")
    developer_id: str = Field(..., description="Author ID")
    category: str = Field(..., description="Forum category")
    title: str = Field(..., description="Post title")
    content: str = Field(..., description="Post content (Markdown)")
    content_html: str = Field(..., description="Post content (HTML)")
    tags: List[str] = Field(default_factory=list, description="Post tags")
    status: ForumPostStatus = Field(..., description="Post status")
    views: int = Field(default=0, description="View count")
    likes: int = Field(default=0, description="Like count")
    replies: int = Field(default=0, description="Reply count")
    last_reply_at: Optional[datetime] = Field(default=None, description="Last reply timestamp")
    pinned: bool = Field(default=False, description="Whether post is pinned")
    locked: bool = Field(default=False, description="Whether post is locked")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")


class ForumReply(BaseModel):
    """Forum reply model."""
    id: str = Field(..., description="Reply ID")
    post_id: str = Field(..., description="Parent post ID")
    developer_id: str = Field(..., description="Author ID")
    content: str = Field(..., description="Reply content (Markdown)")
    content_html: str = Field(..., description="Reply content (HTML)")
    likes: int = Field(default=0, description="Like count")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")


class DeveloperStats(BaseModel):
    """Developer statistics model."""
    developer_id: str = Field(..., description="Developer ID")
    tier: DeveloperTier = Field(..., description="Developer tier")
    status: DeveloperStatus = Field(..., description="Account status")
    joined_at: datetime = Field(..., description="Registration date")
    applications: Dict[str, int] = Field(..., description="Application statistics")
    api_usage: Dict[str, Any] = Field(..., description="API usage statistics")
    community: Dict[str, int] = Field(..., description="Community participation")
    support: Dict[str, int] = Field(..., description="Support interaction statistics")


class DeveloperDashboard(BaseModel):
    """Developer dashboard data model."""
    developer: Developer = Field(..., description="Developer information")
    applications: List[Application] = Field(..., description="Developer applications")
    recent_api_usage: Dict[str, Any] = Field(..., description="Recent API usage")
    notifications: List[Dict[str, Any]] = Field(default_factory=list, description="Recent notifications")
    quick_actions: List[Dict[str, Any]] = Field(default_factory=list, description="Quick action items")


class APIPlaygroundTest(BaseModel):
    """API playground test model."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Test ID")
    developer_id: str = Field(..., description="Developer ID")
    endpoint: str = Field(..., description="API endpoint")
    method: str = Field(..., description="HTTP method")
    headers: Dict[str, str] = Field(default_factory=dict, description="Request headers")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Request parameters")
    body: Optional[str] = Field(default=None, description="Request body")
    status_code: Optional[int] = Field(default=None, description="Response status code")
    response_body: Optional[str] = Field(default=None, description="Response body")
    response_time_ms: Optional[float] = Field(default=None, description="Response time")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Test timestamp")


class DeveloperNotification(BaseModel):
    """Developer notification model."""
    id: str = Field(..., description="Notification ID")
    developer_id: str = Field(..., description="Developer ID")
    type: str = Field(..., description="Notification type")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    read: bool = Field(default=False, description="Whether notification is read")
    action_url: Optional[str] = Field(default=None, description="Action URL")
    created_at: datetime = Field(..., description="Creation timestamp")
    expires_at: Optional[datetime] = Field(default=None, description="Expiration timestamp")