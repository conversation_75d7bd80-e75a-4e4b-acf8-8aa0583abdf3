#!/bin/bash

# AI Agent Execution Service Startup Script

set -e

echo "Starting AI Agent Execution Service..."

# Check Redis connectivity
echo "Testing Redis connection..."
python -c "
import redis
import os
client = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379'))
client.ping()
print('Redis connection successful')
"

# Check database connectivity
echo "Testing database connection..."
python -c "
import asyncio
import os
from supabase import create_client
url = os.getenv('SUPABASE_URL')
key = os.getenv('SUPABASE_SERVICE_KEY')
if url and key:
    client = create_client(url, key)
    print('Database connection successful')
else:
    print('Warning: Database credentials not found')
"

# Check AI API keys
echo "Checking AI API availability..."
python -c "
import os
openai_key = os.getenv('OPENAI_API_KEY')
anthropic_key = os.getenv('ANTHROPIC_API_KEY')
if openai_key:
    print('OpenAI API key found')
if anthropic_key:
    print('Anthropic API key found')
if not openai_key and not anthropic_key:
    print('Warning: No AI API keys found - agents will run in mock mode')
"

# Set resource limits
export PYTHONPATH="/app:$PYTHONPATH"
export AGENT_POOL_SIZE=${AGENT_POOL_SIZE:-10}
export MAX_CONCURRENT_AGENTS=${MAX_CONCURRENT_AGENTS:-5}

# Start the service
echo "Starting AI Agent Execution Service on port 8002..."
exec python -m uvicorn main:app --host 0.0.0.0 --port 8002 --workers 1