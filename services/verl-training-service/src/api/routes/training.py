"""
Training API routes for VERL Training Service.

Provides endpoints for:
- Submitting training jobs
- Monitoring training progress
- Managing training configurations
- Retrieving training results and metrics
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse
import structlog

from models.schemas import (
    TrainingRequest, TrainingResponse, TrainingStatus,
    TrainingJobInfo, TrainingMetricsResponse, ModelMetrics,
    TrainingConfig, PPOConfig, VERLConfig
)
from services.training_service import VERLTrainingService
from core.exceptions import TrainingException, VERLException
from main import get_training_service, get_training_metrics


router = APIRouter()
logger = structlog.get_logger(__name__)


@router.post("/submit", response_model=TrainingResponse)
async def submit_training_job(
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Submit a new training job to the VERL training pipeline.
    
    Args:
        request: Training job configuration
        background_tasks: FastAPI background tasks
        training_service: VERL training service instance
    
    Returns:
        TrainingResponse with job ID and status
    """
    try:
        logger.info(
            "Received training job submission",
            model_name=request.model_name,
            training_type=request.training_type,
            priority=request.priority
        )
        
        # Submit training job
        response = await training_service.submit_training_job(request)
        
        logger.info(
            "Training job submitted successfully",
            training_id=response.training_id,
            status=response.status
        )
        
        return response
        
    except TrainingException as e:
        logger.error(f"Training submission failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in training submission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/jobs/{training_id}", response_model=TrainingJobInfo)
async def get_training_job(
    training_id: str,
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Get detailed information about a specific training job.
    
    Args:
        training_id: Unique training job identifier
        training_service: VERL training service instance
    
    Returns:
        Detailed training job information
    """
    try:
        job_data = await training_service.get_training_status(training_id)
        
        return TrainingJobInfo(
            id=job_data["id"],
            model_name=job_data.get("model_name"),
            training_type=job_data.get("training_type"),
            status=TrainingStatus(job_data.get("status", "unknown")),
            config=job_data.get("config", {}),
            created_at=job_data.get("created_at"),
            started_at=job_data.get("started_at"),
            completed_at=job_data.get("completed_at"),
            failed_at=job_data.get("failed_at"),
            error_message=job_data.get("error_message"),
            progress=job_data.get("progress", 0.0),
            current_epoch=job_data.get("current_epoch", 0),
            current_step=job_data.get("current_step", 0),
            estimated_duration=job_data.get("estimated_duration"),
            actual_duration=job_data.get("actual_duration"),
            resource_usage=job_data.get("resource_usage", {}),
            priority=job_data.get("priority", 1)
        )
        
    except TrainingException as e:
        logger.error(f"Failed to get training job: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting training job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/jobs", response_model=List[TrainingJobInfo])
async def list_training_jobs(
    status: Optional[TrainingStatus] = Query(None, description="Filter by training status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of jobs to return"),
    offset: int = Query(0, ge=0, description="Number of jobs to skip"),
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    List training jobs with optional filtering and pagination.
    
    Args:
        status: Optional status filter
        limit: Maximum number of jobs to return
        offset: Number of jobs to skip for pagination
        training_service: VERL training service instance
    
    Returns:
        List of training job information
    """
    try:
        jobs_data = await training_service.list_training_jobs(
            status=status, limit=limit, offset=offset
        )
        
        jobs = []
        for job_data in jobs_data:
            jobs.append(TrainingJobInfo(
                id=job_data["id"],
                model_name=job_data.get("model_name"),
                training_type=job_data.get("training_type"),
                status=TrainingStatus(job_data.get("status", "unknown")),
                config=job_data.get("config", {}),
                created_at=job_data.get("created_at"),
                started_at=job_data.get("started_at"),
                completed_at=job_data.get("completed_at"),
                failed_at=job_data.get("failed_at"),
                error_message=job_data.get("error_message"),
                progress=job_data.get("progress", 0.0),
                current_epoch=job_data.get("current_epoch", 0),
                current_step=job_data.get("current_step", 0),
                estimated_duration=job_data.get("estimated_duration"),
                actual_duration=job_data.get("actual_duration"),
                resource_usage=job_data.get("resource_usage", {}),
                priority=job_data.get("priority", 1)
            ))
        
        return jobs
        
    except Exception as e:
        logger.error(f"Failed to list training jobs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/jobs/{training_id}/cancel")
async def cancel_training_job(
    training_id: str,
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Cancel a running or queued training job.
    
    Args:
        training_id: Unique training job identifier
        training_service: VERL training service instance
    
    Returns:
        Cancellation confirmation
    """
    try:
        result = await training_service.cancel_training(training_id)
        
        logger.info("Training job cancelled", training_id=training_id)
        
        return {
            "training_id": training_id,
            "status": "cancelled",
            "message": "Training job cancelled successfully",
            "cancelled_at": result.get("ended_at")
        }
        
    except TrainingException as e:
        logger.error(f"Failed to cancel training: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error cancelling training: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/jobs/{training_id}/metrics", response_model=TrainingMetricsResponse)
async def get_training_metrics(
    training_id: str,
    include_history: bool = Query(False, description="Include full training history"),
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Get detailed training metrics for a specific job.
    
    Args:
        training_id: Unique training job identifier
        include_history: Whether to include full training history
        training_service: VERL training service instance
    
    Returns:
        Comprehensive training metrics
    """
    try:
        metrics = await training_service.get_training_metrics(training_id)
        
        # Get additional metrics if requested
        history = []
        if include_history:
            # This would fetch historical metrics from database
            # For now, return empty list
            history = []
        
        return TrainingMetricsResponse(
            training_id=training_id,
            current_metrics=metrics,
            history=history,
            summary={
                "total_steps": metrics.step,
                "current_epoch": metrics.epoch,
                "best_loss": metrics.loss,
                "training_time": metrics.training_time,
                "gpu_utilization": metrics.memory_usage
            }
        )
        
    except TrainingException as e:
        logger.error(f"Failed to get training metrics: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting training metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/jobs/{training_id}/logs")
async def get_training_logs(
    training_id: str,
    lines: int = Query(100, ge=1, le=1000, description="Number of log lines to return"),
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Get training logs for a specific job.
    
    Args:
        training_id: Unique training job identifier
        lines: Number of recent log lines to return
        training_service: VERL training service instance
    
    Returns:
        Training logs
    """
    try:
        # In a real implementation, you would fetch logs from a logging system
        # For now, return placeholder logs
        logs = [
            f"[{datetime.utcnow().isoformat()}] INFO: Training step {i}/1000"
            for i in range(max(0, 1000 - lines), 1000)
        ]
        
        return {
            "training_id": training_id,
            "logs": logs,
            "total_lines": len(logs),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get training logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/jobs/{training_id}/restart")
async def restart_training_job(
    training_id: str,
    from_checkpoint: bool = Query(True, description="Restart from last checkpoint"),
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Restart a failed or cancelled training job.
    
    Args:
        training_id: Unique training job identifier
        from_checkpoint: Whether to restart from last checkpoint
        training_service: VERL training service instance
    
    Returns:
        Restart confirmation
    """
    try:
        # Get original job data
        original_job = await training_service.get_training_status(training_id)
        
        # Create new training request based on original
        request = TrainingRequest(
            model_name=original_job["model_name"],
            training_type=original_job["training_type"],
            config=TrainingConfig(**original_job.get("config", {})),
            priority=original_job.get("priority", 1),
            restart_from_checkpoint=from_checkpoint,
            parent_training_id=training_id
        )
        
        # Submit new training job
        response = await training_service.submit_training_job(request)
        
        logger.info(
            "Training job restarted",
            original_id=training_id,
            new_id=response.training_id,
            from_checkpoint=from_checkpoint
        )
        
        return {
            "original_training_id": training_id,
            "new_training_id": response.training_id,
            "status": "restarted",
            "from_checkpoint": from_checkpoint,
            "message": "Training job restarted successfully"
        }
        
    except TrainingException as e:
        logger.error(f"Failed to restart training: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error restarting training: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/queue/status")
async def get_queue_status(
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Get current training queue status.
    
    Args:
        training_service: VERL training service instance
    
    Returns:
        Queue status information
    """
    try:
        status = await training_service.get_status()
        
        return {
            "queue_size": status["queue_size"],
            "active_trainings": status["active_trainings"],
            "gpu_available": status["gpu_available"],
            "gpu_memory_usage": status["gpu_memory_usage"],
            "service_status": "running" if status["is_running"] else "stopped",
            "background_tasks": status["background_tasks"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get queue status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/config/validate")
async def validate_training_config(config: TrainingConfig):
    """
    Validate a training configuration without submitting a job.
    
    Args:
        config: Training configuration to validate
    
    Returns:
        Validation results
    """
    try:
        # Perform validation checks
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "recommendations": []
        }
        
        # Check batch size
        if config.batch_size and config.batch_size > 32:
            validation_results["warnings"].append(
                "Large batch size may cause out-of-memory errors"
            )
        
        # Check learning rate
        if config.learning_rate and (config.learning_rate > 1e-3 or config.learning_rate < 1e-7):
            validation_results["warnings"].append(
                "Learning rate is outside typical range (1e-7 to 1e-3)"
            )
        
        # Check epochs
        if config.max_epochs and config.max_epochs > 10:
            validation_results["warnings"].append(
                "High number of epochs may lead to overfitting"
            )
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Failed to validate config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/templates")
async def get_training_templates():
    """
    Get predefined training configuration templates.
    
    Returns:
        Available training templates
    """
    templates = {
        "quick_finetune": {
            "name": "Quick Fine-tune",
            "description": "Fast fine-tuning for small models",
            "config": {
                "max_epochs": 3,
                "batch_size": 8,
                "learning_rate": 5e-5,
                "warmup_steps": 100,
                "use_lora": True,
                "lora_r": 8,
                "lora_alpha": 32
            }
        },
        "deep_training": {
            "name": "Deep Training",
            "description": "Comprehensive training with multiple phases",
            "config": {
                "max_epochs": 10,
                "batch_size": 4,
                "learning_rate": 1e-5,
                "warmup_steps": 500,
                "use_lora": True,
                "lora_r": 16,
                "lora_alpha": 64,
                "gradient_accumulation_steps": 4
            }
        },
        "verl_standard": {
            "name": "VERL Standard",
            "description": "Standard VERL training with all phases",
            "config": {
                "training_type": "verl",
                "sft_epochs": 3,
                "reward_epochs": 2,
                "ppo_epochs": 4,
                "batch_size": 8,
                "learning_rate": 1e-5,
                "kl_penalty": 0.1,
                "use_lora": True
            }
        },
        "ppo_only": {
            "name": "PPO Only",
            "description": "PPO training without reward model training",
            "config": {
                "training_type": "ppo",
                "ppo_epochs": 6,
                "batch_size": 8,
                "learning_rate": 1e-6,
                "clip_range": 0.2,
                "kl_penalty": 0.1
            }
        }
    }
    
    return {"templates": templates}


@router.post("/batch/submit")
async def submit_batch_training(
    requests: List[TrainingRequest],
    training_service: VERLTrainingService = Depends(get_training_service)
):
    """
    Submit multiple training jobs as a batch.
    
    Args:
        requests: List of training requests
        training_service: VERL training service instance
    
    Returns:
        Batch submission results
    """
    try:
        results = []
        
        for request in requests:
            try:
                response = await training_service.submit_training_job(request)
                results.append({
                    "model_name": request.model_name,
                    "training_id": response.training_id,
                    "status": "submitted",
                    "message": response.message
                })
            except Exception as e:
                results.append({
                    "model_name": request.model_name,
                    "training_id": None,
                    "status": "failed",
                    "message": str(e)
                })
        
        successful = len([r for r in results if r["status"] == "submitted"])
        failed = len([r for r in results if r["status"] == "failed"])
        
        return {
            "total_jobs": len(requests),
            "successful": successful,
            "failed": failed,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Failed to submit batch training: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")