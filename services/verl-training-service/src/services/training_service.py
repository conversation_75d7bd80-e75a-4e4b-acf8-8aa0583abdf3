"""
VERL Training Service - Core training orchestration and management.

This module implements the main training service that coordinates all aspects
of the VERL training pipeline including data preparation, model training,
evaluation, and deployment.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import uuid

import torch
import torch.distributed as dist
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    TrainingArguments, 
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset
import numpy as np
from peft import LoraConfig, get_peft_model, TaskType
import structlog

from core.config import get_settings
from core.monitoring import TrainingMetrics
from core.exceptions import VERLException, TrainingException, ModelException
from models.schemas import (
    TrainingRequest, TrainingResponse, TrainingStatus, 
    ModelMetrics, TrainingConfig, PPOConfig
)
from services.data_service import TrainingDataService
from services.model_service import ModelManagementService
from services.evaluation_service import EvaluationService
from training.verl_trainer import VER<PERSON>rainer
from training.ppo_trainer import <PERSON><PERSON>rainer
from training.reward_model import <PERSON>wardModel
from training.data_processor import TrainingDataProcessor
from utils.gpu_utils import GPUManager
from utils.checkpoint_utils import CheckpointManager


class VERLTrainingService:
    """
    Main training service that orchestrates the VERL training pipeline.
    
    Handles:
    - Training job scheduling and management
    - Model loading and optimization
    - Data preparation and validation
    - Distributed training coordination
    - Performance monitoring and metrics
    - Checkpoint management and recovery
    """
    
    def __init__(
        self,
        supabase_client,
        data_service: TrainingDataService,
        model_service: ModelManagementService,
        evaluation_service: EvaluationService,
        metrics: TrainingMetrics
    ):
        self.settings = get_settings()
        self.logger = structlog.get_logger(__name__)
        self.supabase = supabase_client
        self.data_service = data_service
        self.model_service = model_service
        self.evaluation_service = evaluation_service
        self.metrics = metrics
        
        # Training state
        self.active_trainings: Dict[str, Dict[str, Any]] = {}
        self.training_queue: asyncio.Queue = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=self.settings.max_concurrent_trainings)
        
        # GPU and resource management
        self.gpu_manager = GPUManager()
        self.checkpoint_manager = CheckpointManager(self.settings.checkpoint_dir)
        
        # Training components
        self.verl_trainer: Optional[VERLTrainer] = None
        self.ppo_trainer: Optional[PPOTrainer] = None
        self.reward_model: Optional[RewardModel] = None
        self.data_processor: Optional[TrainingDataProcessor] = None
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
    async def start(self):
        """Start the training service and background tasks."""
        self.logger.info("Starting VERL Training Service")
        
        try:
            # Initialize components
            await self._initialize_components()
            
            # Start background tasks
            if self.settings.training_schedule_enabled:
                self.background_tasks.append(
                    asyncio.create_task(self._training_scheduler())
                )
            
            self.background_tasks.append(
                asyncio.create_task(self._training_worker())
            )
            
            self.background_tasks.append(
                asyncio.create_task(self._metrics_collector())
            )
            
            self.is_running = True
            self.logger.info("VERL Training Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start training service: {e}")
            raise VERLException(f"Service startup failed: {e}")
    
    async def stop(self):
        """Stop the training service gracefully."""
        self.logger.info("Stopping VERL Training Service")
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # Stop active trainings
        for training_id in list(self.active_trainings.keys()):
            await self._stop_training(training_id)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        self.logger.info("VERL Training Service stopped")
    
    async def _initialize_components(self):
        """Initialize training components."""
        self.logger.info("Initializing training components")
        
        # Initialize data processor
        self.data_processor = TrainingDataProcessor(
            tokenizer_name="microsoft/DialoGPT-medium",  # Default tokenizer
            max_length=self.settings.max_sequence_length,
            validation_enabled=self.settings.data_validation_enabled
        )
        
        # Initialize reward model if enabled
        if self.settings.verl_enabled:
            self.reward_model = RewardModel(
                model_name="microsoft/DialoGPT-medium",
                device_map=self.settings.device_map,
                torch_dtype=self.settings.torch_dtype
            )
            await self.reward_model.initialize()
        
        # Initialize trainers
        self.verl_trainer = VERLTrainer(
            settings=self.settings,
            metrics=self.metrics,
            gpu_manager=self.gpu_manager,
            checkpoint_manager=self.checkpoint_manager
        )
        
        if self.settings.ppo_enabled:
            self.ppo_trainer = PPOTrainer(
                settings=self.settings,
                metrics=self.metrics,
                reward_model=self.reward_model
            )
        
        self.logger.info("Training components initialized")
    
    async def submit_training_job(
        self, 
        request: TrainingRequest
    ) -> TrainingResponse:
        """Submit a new training job."""
        training_id = str(uuid.uuid4())
        
        self.logger.info(
            "Submitting training job",
            training_id=training_id,
            model_name=request.model_name,
            training_type=request.training_type
        )
        
        try:
            # Validate request
            await self._validate_training_request(request)
            
            # Create training job record
            training_job = {
                "id": training_id,
                "model_name": request.model_name,
                "training_type": request.training_type,
                "config": request.config.dict() if request.config else {},
                "status": TrainingStatus.QUEUED,
                "created_at": datetime.utcnow().isoformat(),
                "priority": request.priority,
                "estimated_duration": await self._estimate_training_duration(request),
                "resource_requirements": await self._calculate_resource_requirements(request)
            }
            
            # Store in database
            result = self.supabase.table("verl_training_jobs").insert(training_job).execute()
            
            if not result.data:
                raise TrainingException("Failed to create training job record")
            
            # Add to queue
            await self.training_queue.put({
                "training_id": training_id,
                "request": request,
                "job_data": training_job
            })
            
            # Update metrics
            self.metrics.training_jobs_submitted.inc()
            
            self.logger.info("Training job submitted successfully", training_id=training_id)
            
            return TrainingResponse(
                training_id=training_id,
                status=TrainingStatus.QUEUED,
                message="Training job submitted successfully",
                estimated_duration=training_job["estimated_duration"]
            )
            
        except Exception as e:
            self.logger.error(f"Failed to submit training job: {e}")
            self.metrics.training_jobs_failed.inc()
            raise TrainingException(f"Failed to submit training job: {e}")
    
    async def get_training_status(self, training_id: str) -> Dict[str, Any]:
        """Get the status of a training job."""
        try:
            # Check active trainings first
            if training_id in self.active_trainings:
                return self.active_trainings[training_id]
            
            # Query database
            result = self.supabase.table("verl_training_jobs").select("*").eq("id", training_id).execute()
            
            if not result.data:
                raise TrainingException(f"Training job {training_id} not found")
            
            return result.data[0]
            
        except Exception as e:
            self.logger.error(f"Failed to get training status: {e}")
            raise TrainingException(f"Failed to get training status: {e}")
    
    async def cancel_training(self, training_id: str) -> Dict[str, Any]:
        """Cancel a training job."""
        self.logger.info("Cancelling training job", training_id=training_id)
        
        try:
            # Check if training is active
            if training_id in self.active_trainings:
                await self._stop_training(training_id)
            
            # Update database
            result = self.supabase.table("verl_training_jobs").update({
                "status": TrainingStatus.CANCELLED,
                "ended_at": datetime.utcnow().isoformat(),
                "cancellation_reason": "User requested cancellation"
            }).eq("id", training_id).execute()
            
            if not result.data:
                raise TrainingException(f"Training job {training_id} not found")
            
            self.metrics.training_jobs_cancelled.inc()
            
            self.logger.info("Training job cancelled successfully", training_id=training_id)
            
            return result.data[0]
            
        except Exception as e:
            self.logger.error(f"Failed to cancel training: {e}")
            raise TrainingException(f"Failed to cancel training: {e}")
    
    async def list_training_jobs(
        self, 
        status: Optional[TrainingStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """List training jobs with optional filtering."""
        try:
            query = self.supabase.table("verl_training_jobs").select("*")
            
            if status:
                query = query.eq("status", status.value)
            
            result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
            
            return result.data or []
            
        except Exception as e:
            self.logger.error(f"Failed to list training jobs: {e}")
            raise TrainingException(f"Failed to list training jobs: {e}")
    
    async def get_training_metrics(self, training_id: str) -> ModelMetrics:
        """Get detailed metrics for a training job."""
        try:
            # Query training metrics
            result = self.supabase.table("verl_training_metrics").select("*").eq("training_id", training_id).execute()
            
            if not result.data:
                raise TrainingException(f"No metrics found for training {training_id}")
            
            metrics_data = result.data[-1]  # Get latest metrics
            
            return ModelMetrics(
                training_id=training_id,
                epoch=metrics_data.get("epoch", 0),
                step=metrics_data.get("step", 0),
                loss=metrics_data.get("loss", 0.0),
                learning_rate=metrics_data.get("learning_rate", 0.0),
                eval_loss=metrics_data.get("eval_loss"),
                eval_accuracy=metrics_data.get("eval_accuracy"),
                reward_score=metrics_data.get("reward_score"),
                kl_divergence=metrics_data.get("kl_divergence"),
                gradient_norm=metrics_data.get("gradient_norm"),
                memory_usage=metrics_data.get("memory_usage"),
                training_time=metrics_data.get("training_time"),
                timestamp=metrics_data.get("timestamp", datetime.utcnow())
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get training metrics: {e}")
            raise TrainingException(f"Failed to get training metrics: {e}")
    
    async def _training_scheduler(self):
        """Background task to schedule automatic training."""
        self.logger.info("Starting training scheduler")
        
        while self.is_running:
            try:
                # Check if automatic training should be triggered
                should_train = await self._check_training_trigger()
                
                if should_train:
                    # Create automatic training request
                    request = await self._create_auto_training_request()
                    await self.submit_training_job(request)
                
                # Wait for next check
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                self.logger.error(f"Error in training scheduler: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _training_worker(self):
        """Background worker to process training jobs."""
        self.logger.info("Starting training worker")
        
        while self.is_running:
            try:
                # Get next training job
                job = await asyncio.wait_for(self.training_queue.get(), timeout=1.0)
                
                # Process training job
                await self._process_training_job(job)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in training worker: {e}")
                await asyncio.sleep(10)
    
    async def _process_training_job(self, job: Dict[str, Any]):
        """Process a single training job."""
        training_id = job["training_id"]
        request = job["request"]
        
        self.logger.info("Processing training job", training_id=training_id)
        
        try:
            # Update status to running
            await self._update_training_status(training_id, TrainingStatus.RUNNING)
            
            # Add to active trainings
            self.active_trainings[training_id] = {
                "id": training_id,
                "status": TrainingStatus.RUNNING,
                "started_at": datetime.utcnow(),
                "progress": 0.0,
                "current_epoch": 0,
                "current_step": 0
            }
            
            # Execute training in thread pool
            future = self.executor.submit(self._run_training, training_id, request)
            result = await asyncio.get_event_loop().run_in_executor(None, future.result)
            
            # Update status to completed
            await self._update_training_status(training_id, TrainingStatus.COMPLETED)
            
            # Remove from active trainings
            self.active_trainings.pop(training_id, None)
            
            self.metrics.training_jobs_completed.inc()
            
            self.logger.info("Training job completed successfully", training_id=training_id)
            
        except Exception as e:
            self.logger.error(f"Training job failed: {e}", training_id=training_id)
            
            # Update status to failed
            await self._update_training_status(training_id, TrainingStatus.FAILED, str(e))
            
            # Remove from active trainings
            self.active_trainings.pop(training_id, None)
            
            self.metrics.training_jobs_failed.inc()
    
    def _run_training(self, training_id: str, request: TrainingRequest) -> Dict[str, Any]:
        """Run the actual training process (in thread pool)."""
        try:
            if request.training_type == "verl":
                return self.verl_trainer.train(training_id, request)
            elif request.training_type == "ppo":
                return self.ppo_trainer.train(training_id, request)
            else:
                raise TrainingException(f"Unknown training type: {request.training_type}")
            
        except Exception as e:
            self.logger.error(f"Training execution failed: {e}")
            raise
    
    async def _metrics_collector(self):
        """Background task to collect and store training metrics."""
        self.logger.info("Starting metrics collector")
        
        while self.is_running:
            try:
                # Collect metrics for active trainings
                for training_id, training_data in self.active_trainings.items():
                    await self._collect_training_metrics(training_id, training_data)
                
                # Wait before next collection
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in metrics collector: {e}")
                await asyncio.sleep(10)
    
    async def _collect_training_metrics(self, training_id: str, training_data: Dict[str, Any]):
        """Collect metrics for a specific training job."""
        try:
            # Get current metrics from trainer
            if training_id in self.active_trainings:
                metrics_data = {
                    "training_id": training_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "epoch": training_data.get("current_epoch", 0),
                    "step": training_data.get("current_step", 0),
                    "progress": training_data.get("progress", 0.0),
                    "memory_usage": self.gpu_manager.get_memory_usage(),
                    "gpu_utilization": self.gpu_manager.get_gpu_utilization()
                }
                
                # Store metrics in database
                self.supabase.table("verl_training_metrics").insert(metrics_data).execute()
                
        except Exception as e:
            self.logger.error(f"Failed to collect metrics: {e}")
    
    async def _validate_training_request(self, request: TrainingRequest):
        """Validate training request parameters."""
        if not request.model_name:
            raise TrainingException("Model name is required")
        
        if request.training_type not in ["verl", "ppo", "fine_tune"]:
            raise TrainingException(f"Invalid training type: {request.training_type}")
        
        # Check if sufficient data is available
        data_count = await self.data_service.get_training_data_count()
        if data_count < self.settings.min_training_examples:
            raise TrainingException(
                f"Insufficient training data: {data_count} < {self.settings.min_training_examples}"
            )
        
        # Check GPU availability
        if not self.gpu_manager.is_available():
            raise TrainingException("No GPU available for training")
    
    async def _estimate_training_duration(self, request: TrainingRequest) -> int:
        """Estimate training duration in seconds."""
        # Base estimation logic (can be improved with historical data)
        base_time = 3600  # 1 hour base
        
        if request.training_type == "verl":
            multiplier = 2.0
        elif request.training_type == "ppo":
            multiplier = 1.5
        else:
            multiplier = 1.0
        
        return int(base_time * multiplier)
    
    async def _calculate_resource_requirements(self, request: TrainingRequest) -> Dict[str, Any]:
        """Calculate resource requirements for training."""
        return {
            "gpu_memory": "8GB",
            "system_memory": "16GB",
            "storage": "50GB",
            "gpu_count": 1
        }
    
    async def _update_training_status(
        self, 
        training_id: str, 
        status: TrainingStatus, 
        error_message: Optional[str] = None
    ):
        """Update training job status in database."""
        update_data = {
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        if status == TrainingStatus.COMPLETED:
            update_data["completed_at"] = datetime.utcnow().isoformat()
        elif status == TrainingStatus.FAILED:
            update_data["failed_at"] = datetime.utcnow().isoformat()
            if error_message:
                update_data["error_message"] = error_message
        
        self.supabase.table("verl_training_jobs").update(update_data).eq("id", training_id).execute()
    
    async def _stop_training(self, training_id: str):
        """Stop an active training job."""
        if training_id in self.active_trainings:
            # Implementation depends on specific trainer
            # For now, just mark as stopped
            self.active_trainings[training_id]["status"] = TrainingStatus.CANCELLED
    
    async def _check_training_trigger(self) -> bool:
        """Check if automatic training should be triggered."""
        # Get recent feedback data
        recent_feedback = await self.data_service.get_recent_feedback_count()
        
        # Check if threshold is met
        return recent_feedback >= self.settings.min_training_examples * self.settings.auto_training_threshold
    
    async def _create_auto_training_request(self) -> TrainingRequest:
        """Create automatic training request."""
        return TrainingRequest(
            model_name="auto-training-model",
            training_type="verl",
            config=TrainingConfig(
                max_epochs=2,
                batch_size=self.settings.training_batch_size,
                learning_rate=self.settings.learning_rate
            ),
            priority=1,
            automated=True
        )
    
    async def get_status(self) -> Dict[str, Any]:
        """Get overall service status."""
        return {
            "is_running": self.is_running,
            "active_trainings": len(self.active_trainings),
            "queue_size": self.training_queue.qsize(),
            "gpu_available": self.gpu_manager.is_available(),
            "gpu_memory_usage": self.gpu_manager.get_memory_usage(),
            "background_tasks": len(self.background_tasks)
        }