"""Configuration management for VERL Training Service."""

import os
from functools import lru_cache
from typing import List, Optional, Dict, Any
from pathlib import Path

import torch
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class VERLSettings(BaseSettings):
    """VERL Training Service configuration settings."""
    
    # Service configuration
    service_name: str = "verl-training-service"
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    environment: str = Field(default="development", env="ENVIRONMENT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security
    secret_key: str = Field(env="SECRET_KEY")
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    
    # SSL Configuration
    ssl_cert_path: Optional[str] = Field(default=None, env="SSL_CERT_PATH")
    ssl_key_path: Optional[str] = Field(default=None, env="SSL_KEY_PATH")
    
    # Database Configuration
    supabase_url: str = Field(env="SUPABASE_URL")
    supabase_key: str = Field(env="SUPABASE_ANON_KEY")
    supabase_service_key: str = Field(env="SUPABASE_SERVICE_KEY")
    database_url: str = Field(env="DATABASE_URL")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    redis_max_connections: int = Field(default=10, env="REDIS_MAX_CONNECTIONS")
    
    # Storage Configuration
    minio_endpoint: str = Field(env="MINIO_ENDPOINT")
    minio_access_key: str = Field(env="MINIO_ACCESS_KEY")
    minio_secret_key: str = Field(env="MINIO_SECRET_KEY")
    minio_bucket: str = Field(default="verl-models", env="MINIO_BUCKET")
    minio_secure: bool = Field(default=True, env="MINIO_SECURE")
    
    # AI Model Configuration
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    huggingface_token: Optional[str] = Field(default=None, env="HUGGINGFACE_TOKEN")
    
    # GPU and CUDA Configuration
    cuda_available: bool = Field(default_factory=lambda: torch.cuda.is_available())
    cuda_device_count: int = Field(default_factory=lambda: torch.cuda.device_count())
    gpu_memory_limit: Optional[float] = Field(default=None, env="GPU_MEMORY_LIMIT")
    mixed_precision: bool = Field(default=True, env="MIXED_PRECISION")
    
    # VERL Training Configuration
    verl_enabled: bool = Field(default=True, env="VERL_ENABLED")
    min_training_examples: int = Field(default=100, env="MIN_TRAINING_EXAMPLES")
    max_training_examples: int = Field(default=10000, env="MAX_TRAINING_EXAMPLES")
    training_batch_size: int = Field(default=8, env="TRAINING_BATCH_SIZE")
    eval_batch_size: int = Field(default=16, env="EVAL_BATCH_SIZE")
    learning_rate: float = Field(default=1e-5, env="LEARNING_RATE")
    max_epochs: int = Field(default=3, env="MAX_EPOCHS")
    warmup_steps: int = Field(default=100, env="WARMUP_STEPS")
    
    # PPO Configuration
    ppo_enabled: bool = Field(default=True, env="PPO_ENABLED")
    ppo_epochs: int = Field(default=4, env="PPO_EPOCHS")
    ppo_clip_range: float = Field(default=0.2, env="PPO_CLIP_RANGE")
    ppo_kl_penalty: float = Field(default=0.1, env="PPO_KL_PENALTY")
    ppo_value_loss_coef: float = Field(default=0.5, env="PPO_VALUE_LOSS_COEF")
    ppo_entropy_coef: float = Field(default=0.01, env="PPO_ENTROPY_COEF")
    
    # Model Management
    model_cache_dir: Path = Field(default=Path("/app/models"), env="MODEL_CACHE_DIR")
    checkpoint_dir: Path = Field(default=Path("/app/checkpoints"), env="CHECKPOINT_DIR")
    max_model_cache_size: int = Field(default=10, env="MAX_MODEL_CACHE_SIZE")
    model_save_interval: int = Field(default=1000, env="MODEL_SAVE_INTERVAL")
    
    # Training Scheduling
    training_schedule_enabled: bool = Field(default=True, env="TRAINING_SCHEDULE_ENABLED")
    training_interval_hours: int = Field(default=24, env="TRAINING_INTERVAL_HOURS")
    auto_training_threshold: float = Field(default=0.1, env="AUTO_TRAINING_THRESHOLD")
    max_concurrent_trainings: int = Field(default=2, env="MAX_CONCURRENT_TRAININGS")
    
    # Evaluation and Metrics
    evaluation_enabled: bool = Field(default=True, env="EVALUATION_ENABLED")
    evaluation_interval_steps: int = Field(default=500, env="EVALUATION_INTERVAL_STEPS")
    metric_collection_enabled: bool = Field(default=True, env="METRIC_COLLECTION_ENABLED")
    wandb_enabled: bool = Field(default=False, env="WANDB_ENABLED")
    wandb_project: str = Field(default="publish-ai-verl", env="WANDB_PROJECT")
    wandb_api_key: Optional[str] = Field(default=None, env="WANDB_API_KEY")
    
    # Monitoring and Observability
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    jaeger_enabled: bool = Field(default=True, env="JAEGER_ENABLED")
    jaeger_endpoint: str = Field(
        default="http://jaeger:14268/api/traces", 
        env="JAEGER_ENDPOINT"
    )
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    # Data Processing
    data_preprocessing_workers: int = Field(default=4, env="DATA_PREPROCESSING_WORKERS")
    data_validation_enabled: bool = Field(default=True, env="DATA_VALIDATION_ENABLED")
    data_augmentation_enabled: bool = Field(default=False, env="DATA_AUGMENTATION_ENABLED")
    max_sequence_length: int = Field(default=2048, env="MAX_SEQUENCE_LENGTH")
    
    # Performance and Resource Management
    memory_optimization: bool = Field(default=True, env="MEMORY_OPTIMIZATION")
    gradient_checkpointing: bool = Field(default=True, env="GRADIENT_CHECKPOINTING")
    dataloader_num_workers: int = Field(default=4, env="DATALOADER_NUM_WORKERS")
    pin_memory: bool = Field(default=True, env="PIN_MEMORY")
    
    # Distributed Training
    distributed_training: bool = Field(default=False, env="DISTRIBUTED_TRAINING")
    world_size: int = Field(default=1, env="WORLD_SIZE")
    rank: int = Field(default=0, env="RANK")
    master_addr: str = Field(default="localhost", env="MASTER_ADDR")
    master_port: str = Field(default="12355", env="MASTER_PORT")
    
    # Safety and Validation
    safety_checks_enabled: bool = Field(default=True, env="SAFETY_CHECKS_ENABLED")
    model_validation_enabled: bool = Field(default=True, env="MODEL_VALIDATION_ENABLED")
    content_filter_enabled: bool = Field(default=True, env="CONTENT_FILTER_ENABLED")
    bias_detection_enabled: bool = Field(default=True, env="BIAS_DETECTION_ENABLED")
    
    # External Service Timeouts
    api_timeout: int = Field(default=300, env="API_TIMEOUT")
    database_timeout: int = Field(default=30, env="DATABASE_TIMEOUT")
    storage_timeout: int = Field(default=60, env="STORAGE_TIMEOUT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("cors_origins", pre=True)
    def validate_cors_origins(cls, v):
        """Validate and parse CORS origins."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("model_cache_dir", "checkpoint_dir", pre=True)
    def validate_paths(cls, v):
        """Validate and create directory paths."""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @validator("training_batch_size", "eval_batch_size")
    def validate_batch_sizes(cls, v):
        """Validate batch sizes are positive."""
        if v <= 0:
            raise ValueError("Batch size must be positive")
        return v
    
    @validator("learning_rate")
    def validate_learning_rate(cls, v):
        """Validate learning rate is in reasonable range."""
        if not 1e-8 <= v <= 1e-1:
            raise ValueError("Learning rate must be between 1e-8 and 1e-1")
        return v
    
    @validator("ppo_clip_range")
    def validate_ppo_clip_range(cls, v):
        """Validate PPO clip range."""
        if not 0.1 <= v <= 0.5:
            raise ValueError("PPO clip range must be between 0.1 and 0.5")
        return v
    
    @property
    def effective_batch_size(self) -> int:
        """Calculate effective batch size for distributed training."""
        return self.training_batch_size * max(1, self.world_size)
    
    @property
    def device_map(self) -> Dict[str, Any]:
        """Get device mapping for model loading."""
        if self.cuda_available and self.cuda_device_count > 1:
            return "auto"
        elif self.cuda_available:
            return {"": 0}
        else:
            return {"": "cpu"}
    
    @property
    def torch_dtype(self):
        """Get appropriate torch dtype for training."""
        if self.mixed_precision and self.cuda_available:
            return torch.float16
        return torch.float32
    
    def get_training_config(self) -> Dict[str, Any]:
        """Get training-specific configuration."""
        return {
            "batch_size": self.training_batch_size,
            "eval_batch_size": self.eval_batch_size,
            "learning_rate": self.learning_rate,
            "max_epochs": self.max_epochs,
            "warmup_steps": self.warmup_steps,
            "max_sequence_length": self.max_sequence_length,
            "mixed_precision": self.mixed_precision,
            "gradient_checkpointing": self.gradient_checkpointing,
            "dataloader_num_workers": self.dataloader_num_workers,
            "pin_memory": self.pin_memory,
        }
    
    def get_ppo_config(self) -> Dict[str, Any]:
        """Get PPO-specific configuration."""
        return {
            "epochs": self.ppo_epochs,
            "clip_range": self.ppo_clip_range,
            "kl_penalty": self.ppo_kl_penalty,
            "value_loss_coef": self.ppo_value_loss_coef,
            "entropy_coef": self.ppo_entropy_coef,
        }
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model-specific configuration."""
        return {
            "device_map": self.device_map,
            "torch_dtype": self.torch_dtype,
            "cache_dir": str(self.model_cache_dir),
            "max_cache_size": self.max_model_cache_size,
        }


@lru_cache()
def get_settings() -> VERLSettings:
    """Get cached settings instance."""
    return VERLSettings()