"""
VERL Trainer - Core implementation of Versatile and Efficient Reinforcement Learning.

This module implements the main VERL training algorithm that combines:
- Reinforcement Learning from Human Feedback (RLHF)
- Proximal Policy Optimization (PPO)
- Advanced reward modeling
- Efficient training optimizations
"""

import json
import time
import math
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    get_linear_schedule_with_warmup
)
from accelerate import Accelerator
from peft import LoraConfig, get_peft_model, TaskType
import numpy as np
from tqdm import tqdm
import structlog

from core.config import get_settings
from core.monitoring import TrainingMetrics
from core.exceptions import TrainingException, ModelException
from models.schemas import TrainingRequest, TrainingConfig
from training.reward_model import RewardModel
from training.ppo_trainer import PPOTrainer
from utils.gpu_utils import GPUManager
from utils.checkpoint_utils import CheckpointManager


class VERLTrainer:
    """
    Main VERL trainer implementation.
    
    Combines multiple training techniques:
    1. Supervised Fine-Tuning (SFT) on human demonstrations
    2. Reward Model training on human preferences
    3. PPO optimization against the reward model
    4. Iterative improvement with online feedback
    """
    
    def __init__(
        self,
        settings,
        metrics: TrainingMetrics,
        gpu_manager: GPUManager,
        checkpoint_manager: CheckpointManager
    ):
        self.settings = settings
        self.metrics = metrics
        self.gpu_manager = gpu_manager
        self.checkpoint_manager = checkpoint_manager
        self.logger = structlog.get_logger(__name__)
        
        # Training components
        self.accelerator = None
        self.tokenizer = None
        self.model = None
        self.reward_model = None
        self.ppo_trainer = None
        
        # Training state
        self.current_epoch = 0
        self.current_step = 0
        self.best_reward_score = float('-inf')
        self.training_history = []
        
        # Device management
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def train(self, training_id: str, request: TrainingRequest) -> Dict[str, Any]:
        """
        Main training entry point for VERL.
        
        Args:
            training_id: Unique identifier for this training run
            request: Training configuration and parameters
            
        Returns:
            Training results and metrics
        """
        self.logger.info("Starting VERL training", training_id=training_id)
        
        try:
            # Initialize training
            training_config = self._prepare_training_config(request)
            self._initialize_training(training_config)
            
            # Load and prepare data
            train_dataset, eval_dataset = self._prepare_datasets(training_id)
            
            # Phase 1: Supervised Fine-Tuning
            sft_results = self._supervised_fine_tuning(
                train_dataset, eval_dataset, training_config
            )
            
            # Phase 2: Reward Model Training
            reward_results = self._train_reward_model(
                train_dataset, training_config
            )
            
            # Phase 3: PPO Optimization
            ppo_results = self._ppo_optimization(
                train_dataset, eval_dataset, training_config
            )
            
            # Phase 4: Final Evaluation
            final_metrics = self._final_evaluation(eval_dataset)
            
            # Save final model
            model_path = self._save_final_model(training_id)
            
            # Compile results
            results = {
                "training_id": training_id,
                "status": "completed",
                "phases": {
                    "sft": sft_results,
                    "reward_model": reward_results,
                    "ppo": ppo_results
                },
                "final_metrics": final_metrics,
                "model_path": str(model_path),
                "training_duration": time.time() - self.training_start_time,
                "total_steps": self.current_step,
                "best_reward_score": self.best_reward_score
            }
            
            self.logger.info("VERL training completed successfully", training_id=training_id)
            return results
            
        except Exception as e:
            self.logger.error(f"VERL training failed: {e}", training_id=training_id)
            raise TrainingException(f"VERL training failed: {e}")
        finally:
            self._cleanup_training()
    
    def _prepare_training_config(self, request: TrainingRequest) -> Dict[str, Any]:
        """Prepare comprehensive training configuration."""
        config = self.settings.get_training_config()
        
        # Override with request-specific config
        if request.config:
            config.update(request.config.dict(exclude_unset=True))
        
        # Add VERL-specific parameters
        config.update({
            "training_id": request.model_name,
            "sft_epochs": config.get("sft_epochs", 3),
            "reward_epochs": config.get("reward_epochs", 2),
            "ppo_epochs": config.get("ppo_epochs", 4),
            "kl_penalty": config.get("kl_penalty", 0.1),
            "reward_weight": config.get("reward_weight", 1.0),
            "temperature": config.get("temperature", 0.8),
            "top_p": config.get("top_p", 0.9),
            "max_new_tokens": config.get("max_new_tokens", 256)
        })
        
        return config
    
    def _initialize_training(self, config: Dict[str, Any]):
        """Initialize training components and setup."""
        self.logger.info("Initializing VERL training components")
        
        self.training_start_time = time.time()
        
        # Initialize accelerator for distributed training
        self.accelerator = Accelerator(
            gradient_accumulation_steps=config.get("gradient_accumulation_steps", 1),
            mixed_precision="fp16" if self.settings.mixed_precision else "no",
            log_with=["tensorboard", "wandb"] if self.settings.wandb_enabled else ["tensorboard"],
            project_dir=str(self.settings.checkpoint_dir)
        )
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            config.get("base_model", "microsoft/DialoGPT-medium"),
            cache_dir=str(self.settings.model_cache_dir),
            use_fast=True
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Load base model
        self.model = AutoModelForCausalLM.from_pretrained(
            config.get("base_model", "microsoft/DialoGPT-medium"),
            cache_dir=str(self.settings.model_cache_dir),
            device_map=self.settings.device_map,
            torch_dtype=self.settings.torch_dtype,
            load_in_8bit=config.get("load_in_8bit", False),
            load_in_4bit=config.get("load_in_4bit", False)
        )
        
        # Apply PEFT (LoRA) if enabled
        if config.get("use_lora", True):
            lora_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                inference_mode=False,
                r=config.get("lora_r", 8),
                lora_alpha=config.get("lora_alpha", 32),
                lora_dropout=config.get("lora_dropout", 0.1),
                target_modules=config.get("lora_target_modules", ["q_proj", "v_proj"])
            )
            self.model = get_peft_model(self.model, lora_config)
            self.model.print_trainable_parameters()
        
        # Initialize reward model
        self.reward_model = RewardModel(
            model_name=config.get("reward_model", "microsoft/DialoGPT-medium"),
            device_map=self.settings.device_map,
            torch_dtype=self.settings.torch_dtype
        )
        
        # Initialize PPO trainer
        self.ppo_trainer = PPOTrainer(
            settings=self.settings,
            metrics=self.metrics,
            reward_model=self.reward_model
        )
        
        self.logger.info("VERL training components initialized")
    
    def _prepare_datasets(self, training_id: str) -> Tuple[Any, Any]:
        """Prepare training and evaluation datasets."""
        self.logger.info("Preparing datasets for VERL training")
        
        # This would typically load from your data service
        # For now, we'll create placeholder datasets
        
        # In a real implementation, you would:
        # 1. Load human demonstrations for SFT
        # 2. Load preference pairs for reward model training
        # 3. Load prompts for PPO optimization
        # 4. Apply data preprocessing and validation
        
        train_dataset = self._create_placeholder_dataset("train", 1000)
        eval_dataset = self._create_placeholder_dataset("eval", 200)
        
        return train_dataset, eval_dataset
    
    def _create_placeholder_dataset(self, split: str, size: int) -> List[Dict[str, Any]]:
        """Create placeholder dataset for testing."""
        return [
            {
                "input_text": f"Sample input {i} for {split}",
                "target_text": f"Sample target {i} for {split}",
                "reward_score": np.random.uniform(0, 1),
                "preference_label": np.random.choice([0, 1])
            }
            for i in range(size)
        ]
    
    def _supervised_fine_tuning(
        self, 
        train_dataset: List[Dict[str, Any]], 
        eval_dataset: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Phase 1: Supervised Fine-Tuning on human demonstrations."""
        self.logger.info("Starting Supervised Fine-Tuning phase")
        
        sft_start_time = time.time()
        
        # Prepare SFT data
        sft_train_data = self._prepare_sft_data(train_dataset)
        sft_eval_data = self._prepare_sft_data(eval_dataset)
        
        # Create data loaders
        train_loader = DataLoader(
            sft_train_data,
            batch_size=config["batch_size"],
            shuffle=True,
            num_workers=config.get("dataloader_num_workers", 4),
            pin_memory=config.get("pin_memory", True)
        )
        
        eval_loader = DataLoader(
            sft_eval_data,
            batch_size=config["eval_batch_size"],
            shuffle=False,
            num_workers=config.get("dataloader_num_workers", 4),
            pin_memory=config.get("pin_memory", True)
        )
        
        # Setup optimizer and scheduler
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=config["learning_rate"],
            weight_decay=config.get("weight_decay", 0.01)
        )
        
        total_steps = len(train_loader) * config["sft_epochs"]
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=config.get("warmup_steps", 100),
            num_training_steps=total_steps
        )
        
        # Prepare for accelerated training
        self.model, optimizer, train_loader, eval_loader, scheduler = self.accelerator.prepare(
            self.model, optimizer, train_loader, eval_loader, scheduler
        )
        
        # Training loop
        best_eval_loss = float('inf')
        sft_metrics = {"train_losses": [], "eval_losses": []}
        
        for epoch in range(config["sft_epochs"]):
            self.current_epoch = epoch
            
            # Training
            train_loss = self._train_epoch(train_loader, optimizer, scheduler, "SFT")
            sft_metrics["train_losses"].append(train_loss)
            
            # Evaluation
            eval_loss = self._evaluate_epoch(eval_loader, "SFT")
            sft_metrics["eval_losses"].append(eval_loss)
            
            # Save best model
            if eval_loss < best_eval_loss:
                best_eval_loss = eval_loss
                self._save_checkpoint("sft_best")
            
            self.logger.info(
                "SFT Epoch completed",
                epoch=epoch,
                train_loss=train_loss,
                eval_loss=eval_loss
            )
        
        sft_duration = time.time() - sft_start_time
        
        return {
            "duration": sft_duration,
            "epochs": config["sft_epochs"],
            "best_eval_loss": best_eval_loss,
            "final_train_loss": sft_metrics["train_losses"][-1],
            "metrics": sft_metrics
        }
    
    def _train_reward_model(
        self, 
        train_dataset: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Phase 2: Train reward model on preference data."""
        self.logger.info("Starting Reward Model training phase")
        
        reward_start_time = time.time()
        
        # Prepare preference data
        preference_data = self._prepare_preference_data(train_dataset)
        
        # Train reward model
        reward_metrics = self.reward_model.train(
            preference_data=preference_data,
            epochs=config["reward_epochs"],
            batch_size=config["batch_size"],
            learning_rate=config.get("reward_learning_rate", 1e-5)
        )
        
        reward_duration = time.time() - reward_start_time
        
        return {
            "duration": reward_duration,
            "epochs": config["reward_epochs"],
            "metrics": reward_metrics
        }
    
    def _ppo_optimization(
        self,
        train_dataset: List[Dict[str, Any]],
        eval_dataset: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Phase 3: PPO optimization against reward model."""
        self.logger.info("Starting PPO optimization phase")
        
        ppo_start_time = time.time()
        
        # Prepare PPO data (prompts for generation)
        ppo_prompts = self._prepare_ppo_prompts(train_dataset)
        
        # PPO training configuration
        ppo_config = {
            "model": self.model,
            "tokenizer": self.tokenizer,
            "reward_model": self.reward_model,
            "prompts": ppo_prompts,
            "epochs": config["ppo_epochs"],
            "batch_size": config["batch_size"],
            "learning_rate": config.get("ppo_learning_rate", 1e-6),
            "kl_penalty": config["kl_penalty"],
            "clip_range": config.get("clip_range", 0.2),
            "value_loss_coef": config.get("value_loss_coef", 0.5),
            "entropy_coef": config.get("entropy_coef", 0.01)
        }
        
        # Run PPO training
        ppo_metrics = self.ppo_trainer.train(ppo_config)
        
        ppo_duration = time.time() - ppo_start_time
        
        return {
            "duration": ppo_duration,
            "epochs": config["ppo_epochs"],
            "metrics": ppo_metrics
        }
    
    def _final_evaluation(self, eval_dataset: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Phase 4: Final comprehensive evaluation."""
        self.logger.info("Starting final evaluation")
        
        eval_start_time = time.time()
        
        # Evaluate on various metrics
        metrics = {
            "perplexity": self._calculate_perplexity(eval_dataset),
            "reward_score": self._calculate_average_reward(eval_dataset),
            "generation_quality": self._evaluate_generation_quality(eval_dataset),
            "coherence_score": self._calculate_coherence_score(eval_dataset),
            "diversity_score": self._calculate_diversity_score(eval_dataset)
        }
        
        eval_duration = time.time() - eval_start_time
        metrics["evaluation_duration"] = eval_duration
        
        return metrics
    
    def _train_epoch(
        self, 
        train_loader: DataLoader, 
        optimizer: torch.optim.Optimizer,
        scheduler: Any,
        phase: str
    ) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f"{phase} Training")
        
        for batch in progress_bar:
            with self.accelerator.accumulate(self.model):
                # Forward pass
                outputs = self.model(**batch)
                loss = outputs.loss
                
                # Backward pass
                self.accelerator.backward(loss)
                
                # Gradient clipping
                if self.accelerator.sync_gradients:
                    self.accelerator.clip_grad_norm_(self.model.parameters(), 1.0)
                
                optimizer.step()
                scheduler.step()
                optimizer.zero_grad()
                
                # Update metrics
                total_loss += loss.item()
                num_batches += 1
                self.current_step += 1
                
                # Update progress bar
                progress_bar.set_postfix({
                    "loss": f"{loss.item():.4f}",
                    "avg_loss": f"{total_loss / num_batches:.4f}",
                    "lr": f"{scheduler.get_last_lr()[0]:.2e}"
                })
                
                # Log to metrics
                self.metrics.training_loss.observe(loss.item())
                self.metrics.learning_rate.set(scheduler.get_last_lr()[0])
        
        return total_loss / num_batches
    
    def _evaluate_epoch(self, eval_loader: DataLoader, phase: str) -> float:
        """Evaluate for one epoch."""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            progress_bar = tqdm(eval_loader, desc=f"{phase} Evaluation")
            
            for batch in progress_bar:
                outputs = self.model(**batch)
                loss = outputs.loss
                
                total_loss += loss.item()
                num_batches += 1
                
                progress_bar.set_postfix({
                    "eval_loss": f"{loss.item():.4f}",
                    "avg_eval_loss": f"{total_loss / num_batches:.4f}"
                })
        
        avg_loss = total_loss / num_batches
        self.metrics.validation_loss.observe(avg_loss)
        
        return avg_loss
    
    def _prepare_sft_data(self, dataset: List[Dict[str, Any]]) -> List[Dict[str, torch.Tensor]]:
        """Prepare data for supervised fine-tuning."""
        processed_data = []
        
        for item in dataset:
            # Tokenize input and target
            input_text = item["input_text"]
            target_text = item["target_text"]
            
            # Combine input and target for causal LM
            full_text = input_text + " " + target_text
            
            # Tokenize
            encoding = self.tokenizer(
                full_text,
                truncation=True,
                padding="max_length",
                max_length=self.settings.max_sequence_length,
                return_tensors="pt"
            )
            
            processed_data.append({
                "input_ids": encoding["input_ids"].squeeze(),
                "attention_mask": encoding["attention_mask"].squeeze(),
                "labels": encoding["input_ids"].squeeze().clone()
            })
        
        return processed_data
    
    def _prepare_preference_data(self, dataset: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare preference pairs for reward model training."""
        preference_pairs = []
        
        # Group by input and create preference pairs
        # This is a simplified version - in practice, you'd have actual preference data
        for i in range(0, len(dataset), 2):
            if i + 1 < len(dataset):
                item1 = dataset[i]
                item2 = dataset[i + 1]
                
                # Create preference pair based on reward scores
                if item1["reward_score"] > item2["reward_score"]:
                    preferred = item1["target_text"]
                    rejected = item2["target_text"]
                else:
                    preferred = item2["target_text"]
                    rejected = item1["target_text"]
                
                preference_pairs.append({
                    "prompt": item1["input_text"],
                    "preferred": preferred,
                    "rejected": rejected
                })
        
        return preference_pairs
    
    def _prepare_ppo_prompts(self, dataset: List[Dict[str, Any]]) -> List[str]:
        """Prepare prompts for PPO training."""
        return [item["input_text"] for item in dataset]
    
    def _calculate_perplexity(self, dataset: List[Dict[str, Any]]) -> float:
        """Calculate perplexity on evaluation dataset."""
        # Simplified perplexity calculation
        total_loss = 0.0
        num_tokens = 0
        
        self.model.eval()
        with torch.no_grad():
            for item in dataset:
                # Tokenize and compute loss
                encoding = self.tokenizer(
                    item["input_text"] + " " + item["target_text"],
                    return_tensors="pt",
                    truncation=True,
                    max_length=self.settings.max_sequence_length
                ).to(self.device)
                
                outputs = self.model(**encoding, labels=encoding["input_ids"])
                total_loss += outputs.loss.item() * encoding["input_ids"].size(1)
                num_tokens += encoding["input_ids"].size(1)
        
        avg_loss = total_loss / num_tokens
        return math.exp(avg_loss)
    
    def _calculate_average_reward(self, dataset: List[Dict[str, Any]]) -> float:
        """Calculate average reward score."""
        if not hasattr(self, 'reward_model') or self.reward_model is None:
            return 0.0
        
        total_reward = 0.0
        num_samples = 0
        
        for item in dataset:
            # Get reward from reward model
            reward = self.reward_model.get_reward(
                item["input_text"], 
                item["target_text"]
            )
            total_reward += reward
            num_samples += 1
        
        return total_reward / num_samples if num_samples > 0 else 0.0
    
    def _evaluate_generation_quality(self, dataset: List[Dict[str, Any]]) -> Dict[str, float]:
        """Evaluate generation quality with multiple metrics."""
        # Placeholder implementation
        return {
            "bleu_score": 0.75,
            "rouge_l": 0.68,
            "bert_score": 0.82
        }
    
    def _calculate_coherence_score(self, dataset: List[Dict[str, Any]]) -> float:
        """Calculate coherence score for generated text."""
        # Placeholder implementation
        return 0.78
    
    def _calculate_diversity_score(self, dataset: List[Dict[str, Any]]) -> float:
        """Calculate diversity score for generated text."""
        # Placeholder implementation
        return 0.71
    
    def _save_checkpoint(self, checkpoint_name: str):
        """Save model checkpoint."""
        checkpoint_path = self.settings.checkpoint_dir / f"{checkpoint_name}_{self.current_step}.pt"
        
        self.accelerator.save_state(str(checkpoint_path))
        
        self.logger.info(
            "Checkpoint saved",
            checkpoint_name=checkpoint_name,
            step=self.current_step,
            path=str(checkpoint_path)
        )
    
    def _save_final_model(self, training_id: str) -> Path:
        """Save final trained model."""
        model_path = self.settings.model_cache_dir / f"verl_model_{training_id}"
        model_path.mkdir(exist_ok=True)
        
        # Save model and tokenizer
        self.accelerator.unwrap_model(self.model).save_pretrained(str(model_path))
        self.tokenizer.save_pretrained(str(model_path))
        
        # Save training metadata
        metadata = {
            "training_id": training_id,
            "model_type": "verl",
            "training_steps": self.current_step,
            "best_reward_score": self.best_reward_score,
            "created_at": datetime.utcnow().isoformat()
        }
        
        with open(model_path / "training_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
        
        self.logger.info(
            "Final model saved",
            training_id=training_id,
            path=str(model_path)
        )
        
        return model_path
    
    def _cleanup_training(self):
        """Clean up training resources."""
        if hasattr(self, 'accelerator') and self.accelerator is not None:
            self.accelerator.end_training()
        
        # Clear GPU cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("Training cleanup completed")