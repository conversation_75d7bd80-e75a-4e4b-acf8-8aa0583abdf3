"""
VERL Training Service - Advanced AI Model Optimization and Training Pipeline

This service implements the VERL (Versatile and Efficient Reinforcement Learning) 
framework for continuous model improvement based on user feedback and performance metrics.

Key Features:
- Reinforcement Learning from Human Feedback (RLHF)
- Proximal Policy Optimization (PPO) training
- Model performance monitoring and evaluation
- Distributed training across multiple GPUs
- Integration with Supabase for data management
- Real-time training metrics and monitoring
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app
import structlog

from core.config import get_settings
from core.logging_config import setup_logging
from core.monitoring import setup_monitoring, TrainingMetrics
from core.exceptions import VERLException, handle_verl_exception
from api.routes import training, models, evaluation, health
from services.training_service import VERLTrainingService
from services.model_service import ModelManagementService
from services.data_service import TrainingDataService
from services.evaluation_service import EvaluationService
from database.supabase_client import get_supabase_client


# Global service instances
training_service: VERLTrainingService = None
model_service: ModelManagementService = None
data_service: TrainingDataService = None
evaluation_service: EvaluationService = None
training_metrics: TrainingMetrics = None

# Shutdown event
shutdown_event = asyncio.Event()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global training_service, model_service, data_service, evaluation_service, training_metrics
    
    settings = get_settings()
    logger = structlog.get_logger()
    
    try:
        # Setup monitoring
        training_metrics = setup_monitoring()
        logger.info("Monitoring system initialized")
        
        # Initialize database connection
        supabase_client = get_supabase_client()
        logger.info("Database connection established")
        
        # Initialize services
        data_service = TrainingDataService(supabase_client)
        model_service = ModelManagementService(supabase_client)
        evaluation_service = EvaluationService(supabase_client)
        training_service = VERLTrainingService(
            supabase_client=supabase_client,
            data_service=data_service,
            model_service=model_service,
            evaluation_service=evaluation_service,
            metrics=training_metrics
        )
        
        # Start background services
        await training_service.start()
        await model_service.start()
        await data_service.start()
        await evaluation_service.start()
        
        logger.info("VERL Training Service started successfully")
        
        # Register signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown")
            shutdown_event.set()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start VERL Training Service: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down VERL Training Service")
        
        if training_service:
            await training_service.stop()
        if model_service:
            await model_service.stop()
        if data_service:
            await data_service.stop()
        if evaluation_service:
            await evaluation_service.stop()
        
        logger.info("VERL Training Service shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    # Setup logging
    setup_logging(settings.log_level)
    logger = structlog.get_logger()
    
    # Create FastAPI app
    app = FastAPI(
        title="VERL Training Service",
        description="Advanced AI model optimization and training pipeline using VERL framework",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Add exception handlers
    app.add_exception_handler(VERLException, handle_verl_exception)
    app.add_exception_handler(Exception, handle_general_exception)
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["Health"])
    app.include_router(training.router, prefix="/api/training", tags=["Training"])
    app.include_router(models.router, prefix="/api/models", tags=["Models"])
    app.include_router(evaluation.router, prefix="/api/evaluation", tags=["Evaluation"])
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # Root endpoint
    @app.get("/", response_class=JSONResponse)
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "VERL Training Service",
            "version": "1.0.0",
            "status": "operational",
            "description": "Advanced AI model optimization and training pipeline",
            "features": [
                "RLHF (Reinforcement Learning from Human Feedback)",
                "PPO (Proximal Policy Optimization) training",
                "Model performance monitoring",
                "Distributed training support",
                "Real-time metrics and evaluation"
            ],
            "endpoints": {
                "health": "/health",
                "training": "/api/training",
                "models": "/api/models",
                "evaluation": "/api/evaluation",
                "metrics": "/metrics",
                "docs": "/docs" if settings.environment != "production" else None
            }
        }
    
    # Global service access endpoints
    @app.get("/api/services/status")
    async def get_services_status():
        """Get status of all internal services."""
        status = {}
        
        if training_service:
            status["training"] = await training_service.get_status()
        if model_service:
            status["models"] = await model_service.get_status()
        if data_service:
            status["data"] = await data_service.get_status()
        if evaluation_service:
            status["evaluation"] = await evaluation_service.get_status()
        
        return status
    
    return app


async def handle_general_exception(request, exc):
    """Handle general exceptions."""
    logger = structlog.get_logger()
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "type": "InternalServerError"
        }
    )


def get_training_service() -> VERLTrainingService:
    """Get the training service instance."""
    if training_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Training service not available"
        )
    return training_service


def get_model_service() -> ModelManagementService:
    """Get the model service instance."""
    if model_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Model service not available"
        )
    return model_service


def get_data_service() -> TrainingDataService:
    """Get the data service instance."""
    if data_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Data service not available"
        )
    return data_service


def get_evaluation_service() -> EvaluationService:
    """Get the evaluation service instance."""
    if evaluation_service is None:
        raise HTTPException(
            status_code=503, 
            detail="Evaluation service not available"
        )
    return evaluation_service


def get_training_metrics() -> TrainingMetrics:
    """Get the training metrics instance."""
    if training_metrics is None:
        raise HTTPException(
            status_code=503, 
            detail="Training metrics not available"
        )
    return training_metrics


# Create the app instance
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    # Configure uvicorn
    uvicorn_config = {
        "host": settings.host,
        "port": settings.port,
        "log_level": settings.log_level.lower(),
        "access_log": True,
        "reload": settings.environment == "development",
        "workers": 1,  # Single worker for GPU usage
    }
    
    # Add SSL for production
    if settings.environment == "production" and settings.ssl_cert_path:
        uvicorn_config.update({
            "ssl_keyfile": settings.ssl_key_path,
            "ssl_certfile": settings.ssl_cert_path,
        })
    
    logger = structlog.get_logger()
    logger.info(
        "Starting VERL Training Service",
        host=settings.host,
        port=settings.port,
        environment=settings.environment,
        gpu_enabled=settings.cuda_available,
    )
    
    uvicorn.run("main:app", **uvicorn_config)