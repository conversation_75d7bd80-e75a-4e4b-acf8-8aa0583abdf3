# Multi-stage Docker build for VERL Training Service
# Optimized for GPU support and ML training workloads

# Build stage
FROM python:3.11-slim as builder

# Install system dependencies for ML and compilation
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    cmake \
    pkg-config \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Configure Poetry
RUN poetry config virtualenvs.create false \
    && poetry config virtualenvs.in-project false

# Install dependencies
RUN poetry install --no-dev --no-interaction --no-ansi

# Production stage
FROM nvidia/cuda:12.1-runtime-ubuntu22.04

# Install Python 3.11 and system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3-pip \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links for python
RUN ln -s /usr/bin/python3.11 /usr/bin/python \
    && ln -s /usr/bin/python3.11 /usr/bin/python3

# Install pip for Python 3.11
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11

# Copy Python dependencies from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Create non-root user for security
RUN groupadd -r verl && useradd -r -g verl verl

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /app/models /app/data /app/logs /app/checkpoints \
    && chown -R verl:verl /app

# Copy application code
COPY --chown=verl:verl src/ ./src/
COPY --chown=verl:verl models/ ./models/
COPY --chown=verl:verl training_configs/ ./training_configs/
COPY --chown=verl:verl scripts/ ./scripts/

# Make scripts executable
RUN chmod +x scripts/*.sh

# Switch to non-root user
USER verl

# Expose ports
EXPOSE 8000 8080 6006

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Set environment variables
ENV PYTHONPATH=/app/src
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Default command
CMD ["python", "src/main.py"]