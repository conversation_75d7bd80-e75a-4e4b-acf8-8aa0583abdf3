# VERL Training Service

Advanced AI model optimization and training pipeline using the VERL (Versatile and Efficient Reinforcement Learning) framework for continuous model improvement based on user feedback and performance metrics.

## 🚀 Features

### Core Training Capabilities
- **Reinforcement Learning from Human Feedback (RLHF)** - Train models to align with human preferences
- **Proximal Policy Optimization (PPO)** - State-of-the-art policy gradient method
- **Supervised Fine-Tuning (SFT)** - Initial model training on human demonstrations
- **Reward Model Training** - Learn to predict human preferences
- **Multi-Phase Training Pipeline** - Integrated SFT → Reward → PPO workflow

### Advanced AI Features
- **LoRA (Low-Rank Adaptation)** - Efficient fine-tuning with minimal parameters
- **Mixed Precision Training** - FP16 support for faster training and lower memory usage
- **Gradient Checkpointing** - Memory-efficient training for large models
- **Distributed Training** - Multi-GPU and multi-node training support
- **Model Parallelism** - Handle large models across multiple devices

### Production-Grade Infrastructure
- **GPU Resource Management** - Intelligent GPU allocation and monitoring
- **Checkpoint Management** - Automatic saving and recovery from failures
- **Training Queue System** - Scalable job scheduling and prioritization
- **Real-time Monitoring** - Comprehensive metrics and progress tracking
- **Automatic Scaling** - Dynamic resource allocation based on demand

### Data Management
- **Training Data Pipeline** - Automated data preprocessing and validation
- **Preference Data Handling** - Support for human preference datasets
- **Data Augmentation** - Optional data enhancement techniques
- **Quality Validation** - Automated data quality checks and filtering

### Model Management
- **Model Versioning** - Track and manage different model versions
- **A/B Testing Support** - Compare model performance across versions
- **Model Registry** - Centralized model storage and metadata management
- **Deployment Integration** - Seamless model deployment workflows

## 🏗️ Architecture

### Service Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    VERL Training Service                        │
├─────────────────────────────────────────────────────────────────┤
│  API Layer                                                      │
│  ├── Training Endpoints    ├── Model Management                │
│  ├── Metrics & Monitoring  ├── Configuration                   │
│  └── Health & Status       └── Batch Operations                │
├─────────────────────────────────────────────────────────────────┤
│  Core Training Engine                                           │
│  ├── VERL Trainer         ├── PPO Trainer                      │
│  ├── Reward Model         ├── Data Processor                   │
│  └── Evaluation Engine    └── Checkpoint Manager               │
├─────────────────────────────────────────────────────────────────┤
│  Infrastructure Layer                                           │
│  ├── GPU Management       ├── Storage Management               │
│  ├── Queue System         ├── Monitoring & Metrics             │
│  └── Database Integration └── External APIs                    │
└─────────────────────────────────────────────────────────────────┘
```

### Training Pipeline

```
┌──────────────┐    ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
│   Phase 1    │    │   Phase 2    │    │   Phase 3    │    │   Phase 4    │
│     SFT      │───▶│ Reward Model │───▶│     PPO      │───▶│  Evaluation  │
│  Training    │    │  Training    │    │ Optimization │    │ & Deployment │
└──────────────┘    └──────────────┘    └──────────────┘    └──────────────┘
```

## 🛠️ Technology Stack

### Core ML Framework
- **PyTorch** - Deep learning framework
- **Transformers** - HuggingFace transformers library
- **Accelerate** - Distributed training acceleration
- **PEFT** - Parameter-Efficient Fine-Tuning
- **Datasets** - Data loading and processing

### VERL Framework
- **VERL** - Core reinforcement learning framework
- **RLHF** - Human feedback integration
- **PPO Implementation** - Policy optimization algorithms
- **Reward Modeling** - Preference learning components

### Infrastructure
- **FastAPI** - High-performance API framework
- **Uvicorn** - ASGI server for production
- **Supabase** - Database and real-time features
- **Redis** - Caching and job queuing
- **MinIO** - Object storage for models and data

### Monitoring & Observability
- **Prometheus** - Metrics collection and alerting
- **Grafana** - Visualization and dashboards
- **Jaeger** - Distributed tracing
- **Weights & Biases** - Experiment tracking
- **TensorBoard** - Training visualization

## 📊 API Endpoints

### Training Management
```bash
# Submit training job
POST /api/training/submit

# Get training status
GET /api/training/jobs/{training_id}

# List training jobs
GET /api/training/jobs?status=running&limit=50

# Cancel training
POST /api/training/jobs/{training_id}/cancel

# Restart training
POST /api/training/jobs/{training_id}/restart
```

### Monitoring & Metrics
```bash
# Get training metrics
GET /api/training/jobs/{training_id}/metrics

# Get training logs
GET /api/training/jobs/{training_id}/logs

# Get queue status
GET /api/training/queue/status

# Get system metrics
GET /metrics
```

### Configuration & Templates
```bash
# Validate training config
POST /api/training/config/validate

# Get training templates
GET /api/training/templates

# Batch training submission
POST /api/training/batch/submit
```

### Model Management
```bash
# List models
GET /api/models

# Get model details
GET /api/models/{model_id}

# Deploy model
POST /api/models/{model_id}/deploy

# Get model metrics
GET /api/models/{model_id}/metrics
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- CUDA-capable GPU (recommended)
- Docker and Docker Compose
- Supabase account and project
- MinIO or compatible S3 storage

### Environment Setup

1. **Clone the repository:**
```bash
git clone <repository-url>
cd publish-ai/services/verl-training-service
```

2. **Create environment file:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Install dependencies:**
```bash
poetry install
# For GPU support:
poetry install --with gpu
```

### Configuration

Key environment variables:

```bash
# Service Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development
LOG_LEVEL=INFO

# Database
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
DATABASE_URL=postgresql://user:pass@host:port/db

# Storage
MINIO_ENDPOINT=your-minio-endpoint
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key

# AI APIs
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
HUGGINGFACE_TOKEN=your-hf-token

# Training Configuration
VERL_ENABLED=true
MIN_TRAINING_EXAMPLES=100
TRAINING_BATCH_SIZE=8
LEARNING_RATE=1e-5
MAX_EPOCHS=3

# GPU Configuration
CUDA_VISIBLE_DEVICES=0
MIXED_PRECISION=true
GPU_MEMORY_LIMIT=8000

# Monitoring
WANDB_ENABLED=true
WANDB_PROJECT=publish-ai-verl
WANDB_API_KEY=your-wandb-key
```

### Docker Deployment

1. **Build the image:**
```bash
docker build -t verl-training-service .
```

2. **Run with Docker Compose:**
```bash
docker-compose up -d
```

3. **Check service health:**
```bash
curl http://localhost:8000/health
```

### Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check pod status
kubectl get pods -l app=verl-training-service

# View logs
kubectl logs -f deployment/verl-training-service
```

## 📋 Usage Examples

### Submit a Training Job

```python
import httpx

# Training configuration
training_request = {
    "model_name": "gpt-3.5-turbo-finetune",
    "training_type": "verl",
    "config": {
        "max_epochs": 3,
        "batch_size": 8,
        "learning_rate": 1e-5,
        "use_lora": True,
        "lora_r": 8,
        "lora_alpha": 32
    },
    "priority": 1,
    "automated": False
}

# Submit training job
async with httpx.AsyncClient() as client:
    response = await client.post(
        "http://localhost:8000/api/training/submit",
        json=training_request
    )
    result = response.json()
    training_id = result["training_id"]
    print(f"Training job submitted: {training_id}")
```

### Monitor Training Progress

```python
# Get training status
async with httpx.AsyncClient() as client:
    response = await client.get(
        f"http://localhost:8000/api/training/jobs/{training_id}"
    )
    status = response.json()
    print(f"Status: {status['status']}")
    print(f"Progress: {status['progress']}%")
    print(f"Current Epoch: {status['current_epoch']}")
```

### Get Training Metrics

```python
# Get detailed training metrics
async with httpx.AsyncClient() as client:
    response = await client.get(
        f"http://localhost:8000/api/training/jobs/{training_id}/metrics?include_history=true"
    )
    metrics = response.json()
    print(f"Loss: {metrics['current_metrics']['loss']}")
    print(f"Reward Score: {metrics['current_metrics']['reward_score']}")
```

## 🔧 Configuration Options

### Training Configuration

```python
from models.schemas import TrainingConfig

config = TrainingConfig(
    # Basic settings
    max_epochs=3,
    batch_size=8,
    eval_batch_size=16,
    learning_rate=1e-5,
    warmup_steps=100,
    
    # LoRA configuration
    use_lora=True,
    lora_r=8,
    lora_alpha=32,
    lora_dropout=0.1,
    
    # Training optimization
    gradient_accumulation_steps=1,
    max_grad_norm=1.0,
    weight_decay=0.01,
    
    # Memory optimization
    gradient_checkpointing=True,
    load_in_8bit=False,
    load_in_4bit=False,
    
    # Advanced settings
    dataloader_num_workers=4,
    pin_memory=True,
    save_steps=500,
    eval_steps=250,
    logging_steps=10
)
```

### PPO Configuration

```python
from models.schemas import PPOConfig

ppo_config = PPOConfig(
    # PPO hyperparameters
    epochs=4,
    clip_range=0.2,
    kl_penalty=0.1,
    value_loss_coef=0.5,
    entropy_coef=0.01,
    
    # Generation settings
    temperature=0.8,
    top_p=0.9,
    max_new_tokens=256,
    do_sample=True,
    
    # Training settings
    batch_size=8,
    mini_batch_size=2,
    gradient_accumulation_steps=1,
    learning_rate=1e-6,
    
    # Regularization
    target_kl=0.1,
    kl_threshold=4.0,
    adaptive_kl=True
)
```

## 📈 Monitoring and Metrics

### Key Metrics

- **Training Loss** - Model training progress
- **Validation Loss** - Generalization performance
- **Reward Score** - Human preference alignment
- **KL Divergence** - Policy deviation from reference
- **GPU Utilization** - Resource usage efficiency
- **Memory Usage** - Memory consumption tracking
- **Training Speed** - Steps per second
- **Queue Length** - Pending training jobs

### Grafana Dashboards

The service includes pre-configured Grafana dashboards for:

1. **Training Overview** - High-level training metrics
2. **Resource Utilization** - GPU and memory usage
3. **Job Queue Status** - Training job pipeline
4. **Model Performance** - Model quality metrics
5. **System Health** - Service availability and errors

### Alerts and Notifications

Automated alerts for:
- Training job failures
- GPU memory exhaustion
- High queue backlog
- Model performance degradation
- Service unavailability

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests
poetry run pytest tests/unit/

# With coverage
poetry run pytest tests/unit/ --cov=src --cov-report=html
```

### Integration Tests
```bash
# Run integration tests
poetry run pytest tests/integration/

# GPU tests (requires GPU)
poetry run pytest tests/integration/ -m gpu
```

### Training Tests
```bash
# Run training pipeline tests
poetry run pytest tests/training/ -m training --slow
```

### Load Testing
```bash
# Run load tests
poetry run pytest tests/load/

# With Locust
locust -f tests/load/locustfile.py --host=http://localhost:8000
```

## 🔒 Security

### Authentication & Authorization
- JWT token-based authentication
- Role-based access control (RBAC)
- API key management
- Service-to-service authentication

### Data Security
- Encryption at rest and in transit
- Secure model storage
- Data privacy compliance
- Audit logging

### Infrastructure Security
- Container security scanning
- Vulnerability management
- Network security policies
- Secrets management

## 🚀 Production Deployment

### Resource Requirements

**Minimum:**
- 4 CPU cores
- 16 GB RAM
- 1 GPU (8GB VRAM)
- 100 GB storage

**Recommended:**
- 8 CPU cores
- 32 GB RAM
- 2-4 GPUs (16GB+ VRAM each)
- 500 GB SSD storage

### Scaling Configuration

```yaml
# Kubernetes HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: verl-training-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: verl-training-service
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Performance Optimization

1. **GPU Optimization:**
   - Use appropriate batch sizes for your GPU memory
   - Enable mixed precision training
   - Optimize data loading pipelines

2. **Memory Management:**
   - Enable gradient checkpointing for large models
   - Use LoRA for parameter-efficient training
   - Implement model sharding for very large models

3. **Training Efficiency:**
   - Use appropriate learning rate schedules
   - Implement early stopping
   - Optimize data preprocessing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

### Development Setup

```bash
# Install development dependencies
poetry install --with dev

# Install pre-commit hooks
pre-commit install

# Run code formatting
black src/ tests/
isort src/ tests/

# Run linting
flake8 src/ tests/
mypy src/
```

## 📚 Documentation

- [API Documentation](http://localhost:8000/docs) - Interactive API docs
- [Architecture Guide](docs/architecture.md) - System architecture
- [Training Guide](docs/training.md) - Training best practices
- [Deployment Guide](docs/deployment.md) - Production deployment
- [Troubleshooting](docs/troubleshooting.md) - Common issues and solutions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Transformers](https://github.com/huggingface/transformers) - HuggingFace Transformers
- [PEFT](https://github.com/huggingface/peft) - Parameter-Efficient Fine-Tuning
- [Accelerate](https://github.com/huggingface/accelerate) - Distributed training
- [FastAPI](https://github.com/tiangolo/fastapi) - Modern web framework
- [PyTorch](https://pytorch.org/) - Deep learning framework

---

**VERL Training Service** - Advancing AI through human feedback and reinforcement learning 🚀