{
  "recommendations": [
    // Core Development
    "ms-python.python",                    // Python support
    "ms-python.pylint",                    // Python linting
    "ms-python.black-formatter",           // Code formatting
    "ms-python.isort",                     // Import sorting

    // Git & GitHub
    "github.vscode-pull-request-github",   // GitHub integration
    "eamodio.gitlens",                     // Enhanced Git capabilities
    "donjayamanne.githistory",             // Git history visualization
    "github.codespaces",                   // GitHub Codespaces

    // FastAPI & Web Development
    "ms-vscode.vscode-json",               // JSON support
    "redhat.vscode-yaml",                  // YAML support
    "ms-vscode-remote.remote-containers",  // Docker containers
    "ms-azuretools.vscode-docker",         // Docker support

    // VERL/ML Specific
    "ms-toolsai.jupyter",                  // Jupyter notebooks
    "ms-python.flake8",                    // Additional Python linting
    "kevinrose.vsc-python-indent",         // Better Python indentation
    "njpwerner.autodocstring",             // Auto-generate docstrings

    // Database & Analytics
    "mtxr.sqltools",                       // SQL support
    "alexcvzz.vscode-sqlite",              // SQLite support
    "ms-mssql.mssql",                      // SQL Server support

    // Productivity
    "streetsidesoftware.code-spell-checker", // Spell checker
    "aaron-bond.better-comments",          // Enhanced comments
    "gruntfuggly.todo-tree",              // TODO tracking
    "formulahendry.auto-rename-tag",       // Auto rename tags

    // API Development
    "humao.rest-client",                   // REST API testing
    "rangav.vscode-thunder-client",        // API client
    "42crunch.vscode-openapi",             // OpenAPI support

    // Environment & Config
    "mikestead.dotenv",                    // .env file support
    "redhat.vscode-xml",                   // XML support
    "tamasfe.even-better-toml",            // TOML support

    // Code Quality
    "ms-python.mypy-type-checker",         // Type checking
    "charliermarsh.ruff",                  // Fast Python linter
    "ms-python.autopep8",                  // Code formatting

    // VERL/PyTorch Specific
    "pytorch.pytorch-snippets",           // PyTorch code snippets
    "ms-toolsai.vscode-ai",               // AI development tools
    "continue.continue",                   // AI code assistant

    // Documentation
    "shd101wyy.markdown-preview-enhanced", // Enhanced Markdown
    "yzhang.markdown-all-in-one",         // Markdown tools
    "davidanson.vscode-markdownlint"      // Markdown linting
  ]
}