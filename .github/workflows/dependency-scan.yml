name: Dependency Vulnerability Scan

on:
  push:
    branches: [ main, develop ]
    paths: [ 'pyproject.toml', 'poetry.lock' ]
  pull_request:
    branches: [ main, develop ]
    paths: [ 'pyproject.toml', 'poetry.lock' ]
  schedule:
    # Run weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'

env:
  PYTHON_VERSION: '3.11'

jobs:
  dependency-audit:
    name: Dependency Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Export requirements for multiple tools
      run: |
        poetry export -f requirements.txt --output requirements.txt --without-hashes
        poetry export -f requirements.txt --output requirements-dev.txt --with dev --without-hashes
        poetry show --tree > dependency-tree.txt
        poetry show --outdated > outdated-packages.txt || true
    
    - name: Run pip-audit for vulnerability scanning
      run: |
        pip install pip-audit
        pip-audit --format=json --output=pip-audit-report.json || true
        pip-audit --format=cyclonedx-json --output=pip-audit-sbom.json || true
        pip-audit --desc --output=pip-audit-detailed.txt || true
    
    - name: Run safety check with detailed output
      run: |
        poetry run safety check --json --output safety-detailed.json || true
        poetry run safety check --full-report --output safety-full-report.txt || true
        
        # Check specific dependency files
        poetry run safety check -r requirements.txt --json --output safety-prod.json || true
        poetry run safety check -r requirements-dev.txt --json --output safety-dev.json || true
    
    - name: Analyze dependency licenses
      run: |
        pip install pip-licenses
        pip-licenses --format=json --output-file=licenses-report.json || true
        pip-licenses --format=csv --output-file=licenses-report.csv || true
        
        # Check for potentially problematic licenses
        pip-licenses --format=plain-vertical --output-file=licenses-detailed.txt || true
    
    - name: Check for known malicious packages
      run: |
        # Create a simple script to check against known malicious package patterns
        cat > check_malicious.py << 'EOF'
import json
import re
import sys

# Known malicious package patterns (simplified list)
SUSPICIOUS_PATTERNS = [
    r'.*discord.*token.*',
    r'.*password.*stealer.*',
    r'.*crypto.*miner.*',
    r'.*keylogger.*',
    r'.*backdoor.*',
    r'.*malware.*',
    r'.*trojan.*',
    r'.*phishing.*'
]

def check_package_name(name):
    """Check if package name matches suspicious patterns."""
    for pattern in SUSPICIOUS_PATTERNS:
        if re.match(pattern, name.lower()):
            return True
    return False

def analyze_dependencies():
    """Analyze installed packages for suspicious names."""
    suspicious_packages = []
    
    try:
        with open('requirements.txt', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    package_name = line.split('==')[0].split('>=')[0].split('<=')[0].split('~=')[0].split('>')[0].split('<')[0]
                    if check_package_name(package_name):
                        suspicious_packages.append(package_name)
    except FileNotFoundError:
        print("requirements.txt not found")
    
    report = {
        "suspicious_packages": suspicious_packages,
        "total_checked": 0,
        "scan_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    }
    
    with open('malicious-check.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    if suspicious_packages:
        print(f"WARNING: Found {len(suspicious_packages)} potentially suspicious packages:")
        for pkg in suspicious_packages:
            print(f"  - {pkg}")
        sys.exit(1)
    else:
        print("No obviously suspicious packages detected.")

if __name__ == "__main__":
    analyze_dependencies()
EOF
        
        python check_malicious.py || true
    
    - name: Generate dependency security report
      run: |
        cat > generate_report.py << 'EOF'
import json
import os
from datetime import datetime

def load_json_report(filename):
    """Load JSON report file safely."""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def generate_security_report():
    """Generate comprehensive dependency security report."""
    
    # Load all reports
    pip_audit = load_json_report('pip-audit-report.json')
    safety_detailed = load_json_report('safety-detailed.json')
    safety_prod = load_json_report('safety-prod.json')
    safety_dev = load_json_report('safety-dev.json')
    licenses_report = load_json_report('licenses-report.json')
    malicious_check = load_json_report('malicious-check.json')
    
    # Extract vulnerabilities
    pip_audit_vulns = pip_audit.get('vulnerabilities', [])
    safety_vulns = safety_detailed.get('vulnerabilities', [])
    
    # Count unique vulnerabilities
    all_vulns = []
    vuln_ids = set()
    
    for vuln in pip_audit_vulns:
        vuln_id = f"{vuln.get('package', '')}-{vuln.get('id', '')}"
        if vuln_id not in vuln_ids:
            all_vulns.append({
                'source': 'pip-audit',
                'package': vuln.get('package', ''),
                'version': vuln.get('version', ''),
                'id': vuln.get('id', ''),
                'aliases': vuln.get('aliases', []),
                'summary': vuln.get('summary', ''),
                'severity': 'unknown'
            })
            vuln_ids.add(vuln_id)
    
    for vuln in safety_vulns:
        vuln_id = f"{vuln.get('package_name', '')}-{vuln.get('vulnerability_id', '')}"
        if vuln_id not in vuln_ids:
            all_vulns.append({
                'source': 'safety',
                'package': vuln.get('package_name', ''),
                'version': vuln.get('installed_version', ''),
                'id': vuln.get('vulnerability_id', ''),
                'advisory': vuln.get('advisory', ''),
                'vulnerable_spec': vuln.get('vulnerable_spec', ''),
                'severity': 'unknown'
            })
            vuln_ids.add(vuln_id)
    
    # Analyze licenses
    problematic_licenses = []
    if licenses_report:
        PROBLEMATIC_LICENSES = ['GPL-3.0', 'AGPL-3.0', 'SSPL-1.0', 'Commons Clause']
        for pkg in licenses_report:
            license_name = pkg.get('License', '')
            if any(prob_license in license_name for prob_license in PROBLEMATIC_LICENSES):
                problematic_licenses.append({
                    'package': pkg.get('Name', ''),
                    'version': pkg.get('Version', ''),
                    'license': license_name
                })
    
    # Generate summary report
    report = {
        'scan_metadata': {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'tools_used': ['pip-audit', 'safety', 'pip-licenses'],
            'python_version': os.environ.get('PYTHON_VERSION', '3.11')
        },
        'vulnerability_summary': {
            'total_vulnerabilities': len(all_vulns),
            'unique_packages_affected': len(set(v['package'] for v in all_vulns)),
            'sources': {
                'pip-audit': len(pip_audit_vulns),
                'safety': len(safety_vulns)
            }
        },
        'vulnerabilities': all_vulns[:20],  # Top 20 for summary
        'license_analysis': {
            'total_packages_scanned': len(licenses_report) if licenses_report else 0,
            'problematic_licenses_found': len(problematic_licenses),
            'problematic_packages': problematic_licenses
        },
        'security_recommendations': [],
        'malicious_package_check': malicious_check
    }
    
    # Generate recommendations
    if len(all_vulns) > 0:
        report['security_recommendations'].append({
            'type': 'vulnerability_remediation',
            'priority': 'high',
            'description': f'Found {len(all_vulns)} security vulnerabilities in dependencies',
            'action': 'Update affected packages to safe versions immediately'
        })
    
    if len(problematic_licenses) > 0:
        report['security_recommendations'].append({
            'type': 'license_compliance',
            'priority': 'medium',
            'description': f'Found {len(problematic_licenses)} packages with potentially problematic licenses',
            'action': 'Review license compatibility with your project requirements'
        })
    
    if malicious_check.get('suspicious_packages'):
        report['security_recommendations'].append({
            'type': 'malicious_package_detection',
            'priority': 'critical',
            'description': 'Potentially suspicious packages detected',
            'action': 'Investigate and remove suspicious packages immediately'
        })
    
    # Write comprehensive report
    with open('dependency-security-report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Generate human-readable summary
    with open('dependency-security-summary.md', 'w') as f:
        f.write('# Dependency Security Report\n\n')
        f.write(f"**Generated:** {report['scan_metadata']['timestamp']}\n\n")
        
        f.write('## Summary\n\n')
        f.write(f"- **Total Vulnerabilities:** {report['vulnerability_summary']['total_vulnerabilities']}\n")
        f.write(f"- **Packages Affected:** {report['vulnerability_summary']['unique_packages_affected']}\n")
        f.write(f"- **Packages Scanned:** {report['license_analysis']['total_packages_scanned']}\n")
        f.write(f"- **License Issues:** {report['license_analysis']['problematic_licenses_found']}\n\n")
        
        if report['vulnerabilities']:
            f.write('## Top Vulnerabilities\n\n')
            for i, vuln in enumerate(report['vulnerabilities'][:10], 1):
                f.write(f"{i}. **{vuln['package']}** {vuln['version']}\n")
                f.write(f"   - ID: {vuln['id']}\n")
                if 'summary' in vuln:
                    f.write(f"   - Summary: {vuln['summary']}\n")
                if 'advisory' in vuln:
                    f.write(f"   - Advisory: {vuln['advisory']}\n")
                f.write(f"   - Source: {vuln['source']}\n\n")
        
        if report['license_analysis']['problematic_packages']:
            f.write('## License Issues\n\n')
            for pkg in report['license_analysis']['problematic_packages']:
                f.write(f"- **{pkg['package']}** {pkg['version']}: {pkg['license']}\n")
            f.write('\n')
        
        if report['security_recommendations']:
            f.write('## Recommendations\n\n')
            for rec in report['security_recommendations']:
                f.write(f"### {rec['type'].replace('_', ' ').title()} ({rec['priority'].upper()} Priority)\n")
                f.write(f"{rec['description']}\n\n")
                f.write(f"**Action:** {rec['action']}\n\n")
    
    print(f"Generated security report with {len(all_vulns)} vulnerabilities")
    return len(all_vulns)

if __name__ == "__main__":
    vuln_count = generate_security_report()
    exit(1 if vuln_count > 0 else 0)
EOF
        
        python generate_report.py || true
    
    - name: Upload dependency reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-security-reports
        path: |
          pip-audit-report.json
          pip-audit-sbom.json
          pip-audit-detailed.txt
          safety-detailed.json
          safety-full-report.txt
          safety-prod.json
          safety-dev.json
          licenses-report.json
          licenses-report.csv
          licenses-detailed.txt
          malicious-check.json
          dependency-security-report.json
          dependency-security-summary.md
          dependency-tree.txt
          outdated-packages.txt
          requirements.txt
          requirements-dev.txt
    
    - name: Comment PR with dependency analysis
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          try {
            const report = JSON.parse(fs.readFileSync('dependency-security-report.json', 'utf8'));
            
            let comment = `## 📦 Dependency Security Analysis\n\n`;
            comment += `**Scan completed:** ${report.scan_metadata.timestamp}\n\n`;
            
            comment += `### Summary\n`;
            comment += `- **Vulnerabilities Found:** ${report.vulnerability_summary.total_vulnerabilities}\n`;
            comment += `- **Packages Affected:** ${report.vulnerability_summary.unique_packages_affected}\n`;
            comment += `- **License Issues:** ${report.license_analysis.problematic_licenses_found}\n`;
            comment += `- **Packages Scanned:** ${report.license_analysis.total_packages_scanned}\n\n`;
            
            if (report.vulnerability_summary.total_vulnerabilities > 0) {
              comment += `### 🚨 Critical Vulnerabilities\n\n`;
              
              const topVulns = report.vulnerabilities.slice(0, 5);
              topVulns.forEach((vuln, index) => {
                comment += `${index + 1}. **${vuln.package}** ${vuln.version}\n`;
                comment += `   - Vulnerability ID: ${vuln.id}\n`;
                if (vuln.summary) {
                  comment += `   - ${vuln.summary}\n`;
                }
                if (vuln.advisory) {
                  comment += `   - ${vuln.advisory}\n`;
                }
                comment += `   - Detected by: ${vuln.source}\n\n`;
              });
              
              if (report.vulnerabilities.length > 5) {
                comment += `... and ${report.vulnerabilities.length - 5} more vulnerabilities.\n\n`;
              }
            }
            
            if (report.license_analysis.problematic_licenses_found > 0) {
              comment += `### ⚠️ License Compliance Issues\n\n`;
              
              report.license_analysis.problematic_packages.forEach((pkg, index) => {
                comment += `${index + 1}. **${pkg.package}** ${pkg.version}: ${pkg.license}\n`;
              });
              comment += `\n`;
            }
            
            if (report.malicious_package_check.suspicious_packages && 
                report.malicious_package_check.suspicious_packages.length > 0) {
              comment += `### 🛡️ Potentially Suspicious Packages\n\n`;
              report.malicious_package_check.suspicious_packages.forEach(pkg => {
                comment += `- ⚠️ **${pkg}**\n`;
              });
              comment += `\n`;
            }
            
            if (report.security_recommendations.length > 0) {
              comment += `### 🔧 Recommendations\n\n`;
              report.security_recommendations.forEach(rec => {
                const emoji = rec.priority === 'critical' ? '🚨' : 
                             rec.priority === 'high' ? '⚠️' : 
                             rec.priority === 'medium' ? '📋' : 'ℹ️';
                comment += `${emoji} **${rec.type.replace(/_/g, ' ').toUpperCase()}** (${rec.priority})\n`;
                comment += `${rec.description}\n`;
                comment += `*Action:* ${rec.action}\n\n`;
              });
            }
            
            if (report.vulnerability_summary.total_vulnerabilities === 0 && 
                report.license_analysis.problematic_licenses_found === 0) {
              comment += `✅ **All clear!** No security vulnerabilities or license issues found.\n\n`;
            }
            
            comment += `📋 Detailed dependency reports are available in the workflow artifacts.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.error('Error processing dependency report:', error);
          }
    
    - name: Check for critical vulnerabilities
      run: |
        if [ -f "dependency-security-report.json" ]; then
          critical_vulns=$(python3 -c "
import json
with open('dependency-security-report.json', 'r') as f:
    data = json.load(f)
    print(data['vulnerability_summary']['total_vulnerabilities'])
")
          
          malicious_packages=$(python3 -c "
import json
with open('dependency-security-report.json', 'r') as f:
    data = json.load(f)
    suspicious = data.get('malicious_package_check', {}).get('suspicious_packages', [])
    print(len(suspicious))
")
          
          echo "Vulnerabilities found: $critical_vulns"
          echo "Suspicious packages: $malicious_packages"
          
          if [ "$critical_vulns" -gt "0" ] || [ "$malicious_packages" -gt "0" ]; then
            echo "::error::Critical security issues found in dependencies!"
            echo "::error::Vulnerabilities: $critical_vulns, Suspicious packages: $malicious_packages"
            exit 1
          fi
        fi