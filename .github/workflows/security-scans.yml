name: Security Scans

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'

jobs:
  bandit-security-scan:
    name: Bandit Security Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run Bandit security scan
      run: |
        poetry run bandit -r app/ -f json -o bandit-report.json || true
        poetry run bandit -r app/ -f txt -o bandit-report.txt || true
        
        # Also run with high severity only for stricter checking
        poetry run bandit -r app/ -ll -f json -o bandit-high-severity.json || true
    
    - name: Upload Bandit results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-security-reports
        path: |
          bandit-report.json
          bandit-report.txt
          bandit-high-severity.json
    
    - name: Comment PR with Bandit results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          try {
            const report = JSON.parse(fs.readFileSync('bandit-report.json', 'utf8'));
            const highSeverity = JSON.parse(fs.readFileSync('bandit-high-severity.json', 'utf8'));
            
            const totalIssues = report.results ? report.results.length : 0;
            const highSeverityIssues = highSeverity.results ? highSeverity.results.length : 0;
            
            let comment = `## 🔒 Bandit Security Scan Results\n\n`;
            comment += `- **Total Issues Found:** ${totalIssues}\n`;
            comment += `- **High Severity Issues:** ${highSeverityIssues}\n\n`;
            
            if (highSeverityIssues > 0) {
              comment += `⚠️ **High severity security issues detected!** Please review and fix before merging.\n\n`;
              comment += `### High Severity Issues:\n`;
              
              highSeverity.results.slice(0, 5).forEach((issue, index) => {
                comment += `${index + 1}. **${issue.test_name}** in \`${issue.filename}:${issue.line_number}\`\n`;
                comment += `   - ${issue.issue_text}\n`;
                comment += `   - Confidence: ${issue.issue_confidence}\n\n`;
              });
              
              if (highSeverity.results.length > 5) {
                comment += `... and ${highSeverity.results.length - 5} more issues. Check the full report for details.\n\n`;
              }
            } else {
              comment += `✅ No high severity security issues found!\n\n`;
            }
            
            comment += `📋 Full security reports are available in the workflow artifacts.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.error('Error processing Bandit report:', error);
          }

  safety-dependency-scan:
    name: Safety Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: poetry install --no-interaction --no-root
    
    - name: Export requirements.txt
      run: poetry export -f requirements.txt --output requirements.txt --without-hashes
    
    - name: Run Safety vulnerability scan
      run: |
        poetry run safety check --json --output safety-report.json || true
        poetry run safety check --output safety-report.txt || true
        
        # Also check requirements.txt specifically
        poetry run safety check -r requirements.txt --json --output safety-requirements.json || true
    
    - name: Upload Safety results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: safety-vulnerability-reports
        path: |
          safety-report.json
          safety-report.txt
          safety-requirements.json
          requirements.txt
    
    - name: Parse Safety results and comment
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          try {
            let vulnerabilities = [];
            
            // Try to parse JSON report
            try {
              const report = JSON.parse(fs.readFileSync('safety-report.json', 'utf8'));
              vulnerabilities = report.vulnerabilities || [];
            } catch (e) {
              console.log('Could not parse JSON report, checking text report...');
            }
            
            let comment = `## 🛡️ Safety Dependency Vulnerability Scan\n\n`;
            comment += `- **Vulnerabilities Found:** ${vulnerabilities.length}\n\n`;
            
            if (vulnerabilities.length > 0) {
              comment += `⚠️ **Security vulnerabilities detected in dependencies!**\n\n`;
              comment += `### Critical Vulnerabilities:\n`;
              
              vulnerabilities.slice(0, 5).forEach((vuln, index) => {
                comment += `${index + 1}. **${vuln.package_name}** ${vuln.installed_version}\n`;
                comment += `   - Advisory: ${vuln.advisory}\n`;
                comment += `   - Vulnerable: ${vuln.vulnerable_spec}\n`;
                if (vuln.more_info_url) {
                  comment += `   - [More Info](${vuln.more_info_url})\n`;
                }
                comment += `\n`;
              });
              
              if (vulnerabilities.length > 5) {
                comment += `... and ${vulnerabilities.length - 5} more vulnerabilities.\n\n`;
              }
              
              comment += `🔧 **Recommended Actions:**\n`;
              comment += `- Update vulnerable packages to safe versions\n`;
              comment += `- Review the full safety report in workflow artifacts\n`;
              comment += `- Consider using \`poetry update\` to update dependencies\n\n`;
            } else {
              comment += `✅ No known security vulnerabilities found in dependencies!\n\n`;
            }
            
            comment += `📋 Full vulnerability reports are available in the workflow artifacts.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.error('Error processing Safety report:', error);
          }

  semgrep-sast:
    name: Semgrep SAST Analysis
    runs-on: ubuntu-latest
    
    container:
      image: returntocorp/semgrep:latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Semgrep security analysis
      run: |
        semgrep --config=auto --json --output=semgrep-report.json app/ || true
        semgrep --config=auto --text --output=semgrep-report.txt app/ || true
        
        # Run specific rulesets
        semgrep --config=p/python --json --output=semgrep-python.json app/ || true
        semgrep --config=p/security-audit --json --output=semgrep-security.json app/ || true
        semgrep --config=p/owasp-top-ten --json --output=semgrep-owasp.json app/ || true
    
    - name: Upload Semgrep results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: semgrep-sast-reports
        path: |
          semgrep-report.json
          semgrep-report.txt
          semgrep-python.json
          semgrep-security.json
          semgrep-owasp.json
    
    - name: Comment PR with Semgrep results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          try {
            const report = JSON.parse(fs.readFileSync('semgrep-report.json', 'utf8'));
            const findings = report.results || [];
            
            const errorFindings = findings.filter(f => f.extra.severity === 'ERROR');
            const warningFindings = findings.filter(f => f.extra.severity === 'WARNING');
            
            let comment = `## 🔍 Semgrep SAST Analysis Results\n\n`;
            comment += `- **Total Findings:** ${findings.length}\n`;
            comment += `- **Errors (High Severity):** ${errorFindings.length}\n`;
            comment += `- **Warnings (Medium Severity):** ${warningFindings.length}\n\n`;
            
            if (errorFindings.length > 0) {
              comment += `🚨 **High Severity Issues (Errors):**\n\n`;
              
              errorFindings.slice(0, 5).forEach((finding, index) => {
                comment += `${index + 1}. **${finding.check_id}**\n`;
                comment += `   - File: \`${finding.path}:${finding.start.line}\`\n`;
                comment += `   - Message: ${finding.extra.message}\n`;
                if (finding.extra.metadata && finding.extra.metadata.owasp) {
                  comment += `   - OWASP: ${finding.extra.metadata.owasp.join(', ')}\n`;
                }
                comment += `\n`;
              });
              
              if (errorFindings.length > 5) {
                comment += `... and ${errorFindings.length - 5} more high severity issues.\n\n`;
              }
            }
            
            if (warningFindings.length > 0 && errorFindings.length === 0) {
              comment += `⚠️ **Medium Severity Issues (Warnings):**\n\n`;
              
              warningFindings.slice(0, 3).forEach((finding, index) => {
                comment += `${index + 1}. **${finding.check_id}**\n`;
                comment += `   - File: \`${finding.path}:${finding.start.line}\`\n`;
                comment += `   - Message: ${finding.extra.message}\n\n`;
              });
              
              if (warningFindings.length > 3) {
                comment += `... and ${warningFindings.length - 3} more warnings.\n\n`;
              }
            }
            
            if (findings.length === 0) {
              comment += `✅ No security issues found by Semgrep analysis!\n\n`;
            }
            
            comment += `📋 Full SAST analysis reports are available in the workflow artifacts.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.error('Error processing Semgrep report:', error);
          }

  security-reporting:
    name: Security Report Summary
    runs-on: ubuntu-latest
    needs: [bandit-security-scan, safety-dependency-scan, semgrep-sast]
    if: always()
    
    steps:
    - name: Download all security reports
      uses: actions/download-artifact@v3
    
    - name: Create security summary report
      run: |
        echo "# Security Scan Summary Report" > security-summary.md
        echo "Generated on: $(date)" >> security-summary.md
        echo "" >> security-summary.md
        
        echo "## Scan Results Overview" >> security-summary.md
        echo "" >> security-summary.md
        
        # Bandit summary
        if [ -f "bandit-security-reports/bandit-report.json" ]; then
          echo "### 🔒 Bandit Static Analysis" >> security-summary.md
          python3 -c "
import json
with open('bandit-security-reports/bandit-report.json', 'r') as f:
    data = json.load(f)
    total = len(data.get('results', []))
    print(f'- Total issues found: {total}')
    
    if 'metrics' in data:
        print(f'- Files scanned: {data[\"metrics\"].get(\"_totals\", {}).get(\"SLOC\", \"N/A\")} lines')
        print(f'- Confidence levels: High={data[\"metrics\"].get(\"_totals\", {}).get(\"CONFIDENCE.HIGH\", 0)}, Medium={data[\"metrics\"].get(\"_totals\", {}).get(\"CONFIDENCE.MEDIUM\", 0)}, Low={data[\"metrics\"].get(\"_totals\", {}).get(\"CONFIDENCE.LOW\", 0)}')
" >> security-summary.md
        else
          echo "- Bandit scan failed or no report available" >> security-summary.md
        fi
        echo "" >> security-summary.md
        
        # Safety summary
        if [ -f "safety-vulnerability-reports/safety-report.json" ]; then
          echo "### 🛡️ Safety Dependency Scan" >> security-summary.md
          python3 -c "
import json
try:
    with open('safety-vulnerability-reports/safety-report.json', 'r') as f:
        data = json.load(f)
        vulns = data.get('vulnerabilities', [])
        print(f'- Vulnerabilities found: {len(vulns)}')
        if vulns:
            packages = set(v['package_name'] for v in vulns)
            print(f'- Affected packages: {len(packages)}')
except:
    print('- Safety scan results unavailable')
" >> security-summary.md
        else
          echo "- Safety scan failed or no report available" >> security-summary.md
        fi
        echo "" >> security-summary.md
        
        # Semgrep summary
        if [ -f "semgrep-sast-reports/semgrep-report.json" ]; then
          echo "### 🔍 Semgrep SAST Analysis" >> security-summary.md
          python3 -c "
import json
with open('semgrep-sast-reports/semgrep-report.json', 'r') as f:
    data = json.load(f)
    findings = data.get('results', [])
    errors = [f for f in findings if f.get('extra', {}).get('severity') == 'ERROR']
    warnings = [f for f in findings if f.get('extra', {}).get('severity') == 'WARNING']
    print(f'- Total findings: {len(findings)}')
    print(f'- High severity (errors): {len(errors)}')
    print(f'- Medium severity (warnings): {len(warnings)}')
" >> security-summary.md
        else
          echo "- Semgrep scan failed or no report available" >> security-summary.md
        fi
        echo "" >> security-summary.md
        
        echo "## Recommendations" >> security-summary.md
        echo "" >> security-summary.md
        echo "1. Review all high severity security issues immediately" >> security-summary.md
        echo "2. Update vulnerable dependencies to safe versions" >> security-summary.md
        echo "3. Address SAST findings based on severity" >> security-summary.md
        echo "4. Integrate security scanning into pre-commit hooks" >> security-summary.md
        echo "5. Schedule regular security audits" >> security-summary.md
        echo "" >> security-summary.md
        echo "📋 Detailed reports are available in the workflow artifacts." >> security-summary.md
    
    - name: Upload security summary
      uses: actions/upload-artifact@v3
      with:
        name: security-summary-report
        path: security-summary.md
    
    - name: Check for critical security issues
      run: |
        # Check if any critical issues were found
        critical_issues=0
        
        # Check Bandit high severity
        if [ -f "bandit-security-reports/bandit-high-severity.json" ]; then
          bandit_high=$(python3 -c "
import json
with open('bandit-security-reports/bandit-high-severity.json', 'r') as f:
    data = json.load(f)
    print(len(data.get('results', [])))")
          critical_issues=$((critical_issues + bandit_high))
        fi
        
        # Check Safety vulnerabilities
        if [ -f "safety-vulnerability-reports/safety-report.json" ]; then
          safety_vulns=$(python3 -c "
import json
try:
    with open('safety-vulnerability-reports/safety-report.json', 'r') as f:
        data = json.load(f)
        print(len(data.get('vulnerabilities', [])))
except:
    print(0)")
          critical_issues=$((critical_issues + safety_vulns))
        fi
        
        # Check Semgrep errors
        if [ -f "semgrep-sast-reports/semgrep-report.json" ]; then
          semgrep_errors=$(python3 -c "
import json
with open('semgrep-sast-reports/semgrep-report.json', 'r') as f:
    data = json.load(f)
    findings = data.get('results', [])
    errors = [f for f in findings if f.get('extra', {}).get('severity') == 'ERROR']
    print(len(errors))")
          critical_issues=$((critical_issues + semgrep_errors))
        fi
        
        echo "Total critical security issues: $critical_issues"
        
        if [ $critical_issues -gt 0 ]; then
          echo "::error::Critical security issues found! Review and fix before proceeding."
          exit 1
        else
          echo "::notice::No critical security issues found."
        fi