name: Security Reporting

on:
  schedule:
    # Generate weekly security reports on Mondays at 6 AM UTC
    - cron: '0 6 * * 1'
  workflow_dispatch:
    inputs:
      report_type:
        description: 'Type of security report to generate'
        required: true
        default: 'comprehensive'
        type: choice
        options:
          - comprehensive
          - vulnerability-only
          - compliance-only
          - quick-scan
      include_recommendations:
        description: 'Include security recommendations'
        required: false
        default: true
        type: boolean

env:
  PYTHON_VERSION: '3.11'

jobs:
  generate-security-report:
    name: Generate Comprehensive Security Report
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: |
        poetry install --no-interaction --no-root
        poetry install --no-interaction
    
    - name: Install additional security tools
      run: |
        pip install pip-audit detect-secrets semgrep bandit safety pip-licenses
        # Install GitHub CLI for repository analysis
        curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
        sudo apt update
        sudo apt install gh
    
    - name: Run comprehensive security scans
      run: |
        mkdir -p security-reports
        
        echo "Running Bandit security scan..."
        poetry run bandit -r app/ -f json -o security-reports/bandit-full.json || true
        poetry run bandit -r app/ -ll -f json -o security-reports/bandit-high-severity.json || true
        
        echo "Running Safety dependency scan..."
        poetry run safety check --json --output security-reports/safety-vulnerabilities.json || true
        poetry run safety check --full-report --output security-reports/safety-full-report.txt || true
        
        echo "Running pip-audit..."
        pip-audit --format=json --output=security-reports/pip-audit.json || true
        pip-audit --format=cyclonedx-json --output=security-reports/pip-audit-sbom.json || true
        
        echo "Running Semgrep SAST..."
        semgrep --config=auto --json --output=security-reports/semgrep-auto.json app/ || true
        semgrep --config=p/security-audit --json --output=security-reports/semgrep-security.json app/ || true
        semgrep --config=p/owasp-top-ten --json --output=security-reports/semgrep-owasp.json app/ || true
        
        echo "Running secrets detection..."
        detect-secrets scan --all-files --baseline .secrets.baseline --output security-reports/secrets-scan.json || true
        
        echo "Analyzing licenses..."
        pip-licenses --format=json --output-file=security-reports/licenses.json || true
        
        echo "Generating dependency tree..."
        poetry show --tree > security-reports/dependency-tree.txt
        poetry show --outdated > security-reports/outdated-packages.txt || true
    
    - name: Analyze repository security configuration
      run: |
        cat > analyze_repo_security.py << 'EOF'
import json
import os
import subprocess
from datetime import datetime, timedelta

def run_command(cmd):
    """Run command and return output."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip()
    except Exception as e:
        return f"Error: {e}"

def analyze_repository_security():
    """Analyze repository security configuration."""
    
    security_config = {
        'branch_protection': {},
        'workflow_security': {},
        'file_security': {},
        'commit_security': {},
        'metadata': {
            'analysis_timestamp': datetime.utcnow().isoformat() + 'Z',
            'repository': os.environ.get('GITHUB_REPOSITORY', 'unknown'),
            'ref': os.environ.get('GITHUB_REF', 'unknown')
        }
    }
    
    # Check for security-related files
    security_files = {
        '.github/workflows/security-scans.yml': 'Security scanning workflow',
        '.github/workflows/dependency-scan.yml': 'Dependency vulnerability scanning',
        '.pre-commit-config.yaml': 'Pre-commit hooks configuration',
        '.secrets.baseline': 'Secrets detection baseline',
        'pyproject.toml': 'Python project configuration',
        'poetry.lock': 'Dependency lock file',
        'Dockerfile': 'Container configuration',
        'docker-compose.yml': 'Docker compose configuration',
        '.env.example': 'Environment variables template',
        'SECURITY.md': 'Security policy documentation'
    }
    
    found_files = {}
    for file_path, description in security_files.items():
        if os.path.exists(file_path):
            stat_info = os.stat(file_path)
            found_files[file_path] = {
                'description': description,
                'size_bytes': stat_info.st_size,
                'last_modified': datetime.fromtimestamp(stat_info.st_mtime).isoformat()
            }
    
    security_config['file_security']['found_files'] = found_files
    security_config['file_security']['missing_files'] = [
        f for f in security_files.keys() if f not in found_files
    ]
    
    # Analyze Git configuration
    git_config = {}
    git_config['commit_signing_enabled'] = 'true' in run_command('git config --get commit.gpgsign').lower()
    git_config['user_email'] = run_command('git config --get user.email')
    git_config['user_name'] = run_command('git config --get user.name')
    
    # Check recent commits for security patterns
    recent_commits = run_command('git log --oneline -20')
    security_commits = []
    for line in recent_commits.split('\n'):
        if any(keyword in line.lower() for keyword in ['security', 'fix', 'vulnerability', 'patch']):
            security_commits.append(line.strip())
    
    git_config['recent_security_commits'] = security_commits[:5]
    security_config['commit_security'] = git_config
    
    # Workflow security analysis
    workflow_dir = '.github/workflows'
    if os.path.exists(workflow_dir):
        workflows = []
        for workflow_file in os.listdir(workflow_dir):
            if workflow_file.endswith('.yml') or workflow_file.endswith('.yaml'):
                workflow_path = os.path.join(workflow_dir, workflow_file)
                with open(workflow_path, 'r') as f:
                    content = f.read()
                
                # Basic security checks
                security_features = {
                    'uses_secrets': '${{ secrets.' in content,
                    'has_permissions': 'permissions:' in content,
                    'uses_third_party_actions': 'uses:' in content and '@' in content,
                    'has_security_scans': any(tool in content.lower() for tool in ['bandit', 'safety', 'semgrep', 'snyk']),
                    'pins_action_versions': '@v' in content or '@sha' in content
                }
                
                workflows.append({
                    'name': workflow_file,
                    'security_features': security_features,
                    'triggers': [line.strip() for line in content.split('\n') if line.strip().startswith('- ')][:5]
                })
        
        security_config['workflow_security']['workflows'] = workflows
    
    # Save analysis
    with open('security-reports/repository-security-analysis.json', 'w') as f:
        json.dump(security_config, f, indent=2)
    
    return security_config

if __name__ == "__main__":
    analyze_repository_security()
EOF
        
        python analyze_repo_security.py
    
    - name: Generate comprehensive security report
      run: |
        cat > generate_security_report.py << 'EOF'
import json
import os
from datetime import datetime
from pathlib import Path

def load_json_file(filepath):
    """Safely load JSON file."""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError, Exception):
        return {}

def calculate_security_score(report_data):
    """Calculate overall security score based on findings."""
    score = 100
    
    # Deduct points for vulnerabilities
    vulnerabilities = report_data.get('vulnerability_summary', {})
    total_vulns = vulnerabilities.get('total_vulnerabilities', 0)
    critical_vulns = vulnerabilities.get('critical_vulnerabilities', 0)
    high_vulns = vulnerabilities.get('high_vulnerabilities', 0)
    
    score -= critical_vulns * 20  # 20 points per critical
    score -= high_vulns * 10      # 10 points per high
    score -= (total_vulns - critical_vulns - high_vulns) * 2  # 2 points per other
    
    # Deduct points for missing security features
    security_config = report_data.get('repository_security', {})
    missing_files = security_config.get('file_security', {}).get('missing_files', [])
    score -= len(missing_files) * 5  # 5 points per missing security file
    
    # Deduct points for SAST findings
    sast_summary = report_data.get('sast_summary', {})
    sast_errors = sast_summary.get('total_errors', 0)
    score -= sast_errors * 3  # 3 points per SAST error
    
    return max(0, min(100, score))

def generate_comprehensive_report():
    """Generate comprehensive security report."""
    
    report_data = {
        'metadata': {
            'report_type': os.environ.get('INPUT_REPORT_TYPE', 'comprehensive'),
            'generated_at': datetime.utcnow().isoformat() + 'Z',
            'repository': os.environ.get('GITHUB_REPOSITORY', 'unknown'),
            'ref': os.environ.get('GITHUB_REF', 'unknown'),
            'workflow_run_id': os.environ.get('GITHUB_RUN_ID', 'unknown')
        }
    }
    
    # Load all security scan results
    bandit_full = load_json_file('security-reports/bandit-full.json')
    bandit_high = load_json_file('security-reports/bandit-high-severity.json')
    safety_data = load_json_file('security-reports/safety-vulnerabilities.json')
    pip_audit_data = load_json_file('security-reports/pip-audit.json')
    semgrep_auto = load_json_file('security-reports/semgrep-auto.json')
    semgrep_security = load_json_file('security-reports/semgrep-security.json')
    semgrep_owasp = load_json_file('security-reports/semgrep-owasp.json')
    secrets_scan = load_json_file('security-reports/secrets-scan.json')
    licenses_data = load_json_file('security-reports/licenses.json')
    repo_security = load_json_file('security-reports/repository-security-analysis.json')
    
    # Vulnerability Analysis
    all_vulnerabilities = []
    vulnerability_sources = {}
    
    # Safety vulnerabilities
    safety_vulns = safety_data.get('vulnerabilities', [])
    for vuln in safety_vulns:
        all_vulnerabilities.append({
            'source': 'safety',
            'package': vuln.get('package_name', ''),
            'version': vuln.get('installed_version', ''),
            'vulnerability_id': vuln.get('vulnerability_id', ''),
            'advisory': vuln.get('advisory', ''),
            'severity': 'unknown'
        })
    vulnerability_sources['safety'] = len(safety_vulns)
    
    # pip-audit vulnerabilities
    pip_audit_vulns = pip_audit_data.get('vulnerabilities', [])
    for vuln in pip_audit_vulns:
        all_vulnerabilities.append({
            'source': 'pip-audit',
            'package': vuln.get('package', ''),
            'version': vuln.get('version', ''),
            'vulnerability_id': vuln.get('id', ''),
            'summary': vuln.get('summary', ''),
            'severity': 'unknown'
        })
    vulnerability_sources['pip_audit'] = len(pip_audit_vulns)
    
    # SAST Analysis
    all_sast_findings = []
    sast_sources = {}
    
    # Bandit findings
    bandit_issues = bandit_full.get('results', [])
    bandit_high_issues = bandit_high.get('results', [])
    
    for issue in bandit_issues:
        all_sast_findings.append({
            'source': 'bandit',
            'filename': issue.get('filename', ''),
            'line_number': issue.get('line_number', 0),
            'test_name': issue.get('test_name', ''),
            'issue_text': issue.get('issue_text', ''),
            'issue_confidence': issue.get('issue_confidence', ''),
            'issue_severity': issue.get('issue_severity', ''),
            'severity': 'high' if issue in bandit_high_issues else 'medium'
        })
    sast_sources['bandit'] = len(bandit_issues)
    
    # Semgrep findings
    semgrep_findings = (semgrep_auto.get('results', []) + 
                       semgrep_security.get('results', []) + 
                       semgrep_owasp.get('results', []))
    
    for finding in semgrep_findings:
        severity = finding.get('extra', {}).get('severity', 'INFO').lower()
        all_sast_findings.append({
            'source': 'semgrep',
            'filename': finding.get('path', ''),
            'line_number': finding.get('start', {}).get('line', 0),
            'check_id': finding.get('check_id', ''),
            'message': finding.get('extra', {}).get('message', ''),
            'severity': 'high' if severity == 'error' else 'medium' if severity == 'warning' else 'low'
        })
    sast_sources['semgrep'] = len(semgrep_findings)
    
    # Secrets Analysis
    secrets_found = secrets_scan.get('results', {})
    total_secrets = sum(len(file_secrets) for file_secrets in secrets_found.values())
    
    # License Analysis
    license_issues = []
    problematic_licenses = ['GPL-3.0', 'AGPL-3.0', 'SSPL-1.0', 'Commons Clause']
    
    for package in licenses_data:
        license_name = package.get('License', '')
        if any(prob in license_name for prob in problematic_licenses):
            license_issues.append({
                'package': package.get('Name', ''),
                'version': package.get('Version', ''),
                'license': license_name
            })
    
    # Build comprehensive report
    report_data.update({
        'vulnerability_summary': {
            'total_vulnerabilities': len(all_vulnerabilities),
            'critical_vulnerabilities': 0,  # Would need CVE scoring
            'high_vulnerabilities': 0,      # Would need CVE scoring
            'unique_packages_affected': len(set(v['package'] for v in all_vulnerabilities)),
            'sources': vulnerability_sources
        },
        'sast_summary': {
            'total_findings': len(all_sast_findings),
            'total_errors': len([f for f in all_sast_findings if f['severity'] == 'high']),
            'total_warnings': len([f for f in all_sast_findings if f['severity'] == 'medium']),
            'sources': sast_sources
        },
        'secrets_summary': {
            'total_secrets_found': total_secrets,
            'files_with_secrets': len(secrets_found),
            'baseline_exists': os.path.exists('.secrets.baseline')
        },
        'license_summary': {
            'total_packages': len(licenses_data),
            'problematic_licenses': len(license_issues),
            'license_issues': license_issues
        },
        'repository_security': repo_security,
        'top_vulnerabilities': all_vulnerabilities[:10],
        'top_sast_findings': [f for f in all_sast_findings if f['severity'] == 'high'][:10],
        'security_recommendations': []
    })
    
    # Calculate security score
    security_score = calculate_security_score(report_data)
    report_data['security_score'] = security_score
    
    # Generate recommendations
    recommendations = []
    
    if len(all_vulnerabilities) > 0:
        recommendations.append({
            'category': 'vulnerability_management',
            'priority': 'critical' if len(all_vulnerabilities) > 10 else 'high',
            'title': 'Address dependency vulnerabilities',
            'description': f'Found {len(all_vulnerabilities)} vulnerabilities in dependencies',
            'action': 'Update vulnerable packages to safe versions using poetry update'
        })
    
    if len(all_sast_findings) > 0:
        high_severity_sast = [f for f in all_sast_findings if f['severity'] == 'high']
        if high_severity_sast:
            recommendations.append({
                'category': 'static_analysis',
                'priority': 'high',
                'title': 'Fix high-severity SAST findings',
                'description': f'Found {len(high_severity_sast)} high-severity static analysis issues',
                'action': 'Review and fix security issues identified by Bandit and Semgrep'
            })
    
    if total_secrets > 0:
        recommendations.append({
            'category': 'secrets_management',
            'priority': 'critical',
            'title': 'Address exposed secrets',
            'description': f'Found {total_secrets} potential secrets in code',
            'action': 'Remove hardcoded secrets and use environment variables or secret management'
        })
    
    if len(license_issues) > 0:
        recommendations.append({
            'category': 'license_compliance',
            'priority': 'medium',
            'title': 'Review license compliance',
            'description': f'Found {len(license_issues)} packages with potentially problematic licenses',
            'action': 'Review license compatibility and consider alternative packages'
        })
    
    missing_security_files = repo_security.get('file_security', {}).get('missing_files', [])
    if missing_security_files:
        recommendations.append({
            'category': 'security_configuration',
            'priority': 'medium',
            'title': 'Improve security configuration',
            'description': f'Missing {len(missing_security_files)} security configuration files',
            'action': 'Add missing security files: ' + ', '.join(missing_security_files[:3])
        })
    
    if security_score < 70:
        recommendations.append({
            'category': 'overall_security',
            'priority': 'high',
            'title': 'Improve overall security posture',
            'description': f'Current security score: {security_score}/100',
            'action': 'Address high-priority security issues to improve overall security score'
        })
    
    report_data['security_recommendations'] = recommendations
    
    # Write comprehensive report
    with open('security-reports/comprehensive-security-report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    # Generate executive summary
    generate_executive_summary(report_data)
    
    return report_data

def generate_executive_summary(report_data):
    """Generate executive summary in markdown format."""
    
    summary = f"""# Security Report Executive Summary
    
**Generated:** {report_data['metadata']['generated_at']}  
**Repository:** {report_data['metadata']['repository']}  
**Security Score:** {report_data['security_score']}/100

## Overview

This comprehensive security assessment analyzes vulnerabilities, static code analysis findings, secrets exposure, license compliance, and repository security configuration.

## Key Findings

### 🔍 Vulnerability Analysis
- **Total Vulnerabilities:** {report_data['vulnerability_summary']['total_vulnerabilities']}
- **Packages Affected:** {report_data['vulnerability_summary']['unique_packages_affected']}
- **Critical/High Severity:** {report_data['vulnerability_summary']['critical_vulnerabilities'] + report_data['vulnerability_summary']['high_vulnerabilities']}

### 🛡️ Static Analysis (SAST)
- **Total Findings:** {report_data['sast_summary']['total_findings']}
- **High Severity:** {report_data['sast_summary']['total_errors']}
- **Medium Severity:** {report_data['sast_summary']['total_warnings']}

### 🔐 Secrets Exposure
- **Potential Secrets Found:** {report_data['secrets_summary']['total_secrets_found']}
- **Files Affected:** {report_data['secrets_summary']['files_with_secrets']}

### 📋 License Compliance
- **Total Packages:** {report_data['license_summary']['total_packages']}
- **License Issues:** {report_data['license_summary']['problematic_licenses']}

## Priority Recommendations

"""
    
    high_priority_recs = [r for r in report_data['security_recommendations'] if r['priority'] in ['critical', 'high']]
    
    for i, rec in enumerate(high_priority_recs[:5], 1):
        priority_emoji = '🚨' if rec['priority'] == 'critical' else '⚠️'
        summary += f"{i}. {priority_emoji} **{rec['title']}** ({rec['priority'].upper()})\n"
        summary += f"   - {rec['description']}\n"
        summary += f"   - *Action:* {rec['action']}\n\n"
    
    if report_data['security_score'] >= 80:
        summary += "## ✅ Security Status: GOOD\n\n"
        summary += "The repository demonstrates strong security practices with minimal issues.\n\n"
    elif report_data['security_score'] >= 60:
        summary += "## ⚠️ Security Status: NEEDS IMPROVEMENT\n\n"
        summary += "The repository has moderate security issues that should be addressed.\n\n"
    else:
        summary += "## 🚨 Security Status: CRITICAL\n\n"
        summary += "The repository has significant security issues requiring immediate attention.\n\n"
    
    summary += f"""## Next Steps

1. **Immediate Actions:** Address critical and high-priority security issues
2. **Short-term:** Implement missing security configurations and processes
3. **Long-term:** Establish regular security scanning and monitoring

## Detailed Reports

📊 Full analysis data is available in the workflow artifacts:
- `comprehensive-security-report.json` - Complete findings and data
- Individual scan reports from Bandit, Safety, Semgrep, and other tools

---
*Report generated automatically by Security Reporting workflow*
"""
    
    with open('security-reports/executive-summary.md', 'w') as f:
        f.write(summary)

if __name__ == "__main__":
    generate_comprehensive_report()
    print("Comprehensive security report generated successfully")
EOF
        
        python generate_security_report.py
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: comprehensive-security-reports
        path: security-reports/
    
    - name: Create security summary comment
      if: github.event_name == 'workflow_dispatch'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          try {
            const summary = fs.readFileSync('security-reports/executive-summary.md', 'utf8');
            
            // Create or update issue with security report
            const title = `🔒 Weekly Security Report - ${new Date().toISOString().split('T')[0]}`;
            
            const { data: issues } = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['security-report'],
              state: 'open'
            });
            
            if (issues.length > 0) {
              // Update existing issue
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issues[0].number,
                body: summary
              });
            } else {
              // Create new issue
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: summary,
                labels: ['security-report', 'security']
              });
            }
          } catch (error) {
            console.error('Error creating security summary:', error);
          }
    
    - name: Check security thresholds
      run: |
        if [ -f "security-reports/comprehensive-security-report.json" ]; then
          security_score=$(python3 -c "
import json
with open('security-reports/comprehensive-security-report.json', 'r') as f:
    data = json.load(f)
    print(data['security_score'])
")
          
          critical_vulns=$(python3 -c "
import json
with open('security-reports/comprehensive-security-report.json', 'r') as f:
    data = json.load(f)
    print(data['vulnerability_summary']['critical_vulnerabilities'])
")
          
          high_sast=$(python3 -c "
import json
with open('security-reports/comprehensive-security-report.json', 'r') as f:
    data = json.load(f)
    print(data['sast_summary']['total_errors'])
")
          
          secrets_found=$(python3 -c "
import json
with open('security-reports/comprehensive-security-report.json', 'r') as f:
    data = json.load(f)
    print(data['secrets_summary']['total_secrets_found'])
")
          
          echo "Security Score: $security_score/100"
          echo "Critical Vulnerabilities: $critical_vulns"
          echo "High-Severity SAST Findings: $high_sast"
          echo "Secrets Found: $secrets_found"
          
          # Set thresholds
          MIN_SECURITY_SCORE=60
          MAX_CRITICAL_VULNS=0
          MAX_HIGH_SAST=5
          MAX_SECRETS=0
          
          if [ "$security_score" -lt "$MIN_SECURITY_SCORE" ]; then
            echo "::error::Security score ($security_score) below minimum threshold ($MIN_SECURITY_SCORE)"
            exit 1
          fi
          
          if [ "$critical_vulns" -gt "$MAX_CRITICAL_VULNS" ]; then
            echo "::error::Critical vulnerabilities ($critical_vulns) exceed maximum ($MAX_CRITICAL_VULNS)"
            exit 1
          fi
          
          if [ "$high_sast" -gt "$MAX_HIGH_SAST" ]; then
            echo "::error::High-severity SAST findings ($high_sast) exceed maximum ($MAX_HIGH_SAST)"
            exit 1
          fi
          
          if [ "$secrets_found" -gt "$MAX_SECRETS" ]; then
            echo "::error::Secrets found ($secrets_found) exceed maximum ($MAX_SECRETS)"
            exit 1
          fi
          
          echo "::notice::All security thresholds passed!"
        fi