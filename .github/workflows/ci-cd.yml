# GitHub Actions CI/CD Pipeline for Publish AI
# Alternative to GitLab CI/CD with comprehensive testing and deployment

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  COVERAGE_THRESHOLD: 80
  PYTEST_OPTS: "--tb=short --strict-markers --disable-warnings"

jobs:
  # ============================================
  # VALIDATION JOBS
  # ============================================
  
  validate-code:
    name: Code Quality Validation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
        
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
        
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
      
    - name: Run code style checks
      run: |
        poetry run black --check --diff app/ tests/
        poetry run isort --check-only --diff app/ tests/
        poetry run flake8 app/ tests/
        poetry run mypy app/ --ignore-missing-imports

  validate-docker:
    name: Docker Validation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Validate Dockerfiles
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: services/*/Dockerfile
        recursive: true

  validate-kubernetes:
    name: Kubernetes Validation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.4'
        
    - name: Validate Kubernetes manifests
      run: |
        find infrastructure/ -name "*.yaml" -exec kubectl apply --dry-run=client -f {} \;
        find infrastructure/ -name "*.yml" -exec kubectl apply --dry-run=client -f {} \;

  # ============================================
  # TESTING JOBS
  # ============================================
  
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_publishai
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      
    - name: Install dependencies
      run: poetry install --no-interaction
      
    - name: Run unit tests
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_publishai
        REDIS_URL: redis://localhost:6379
        ENVIRONMENT: test
        TESTING: true
        MOCK_EXTERNAL_APIS: true
        OPENAI_API_KEY: test-key-openai
        ANTHROPIC_API_KEY: test-key-anthropic
      run: |
        poetry run pytest tests/unit/ ${{ env.PYTEST_OPTS }} \
          --cov=app --cov-branch --cov-report=xml --cov-report=html \
          --junit-xml=test-results/unit-test-results.xml
        
    - name: Check coverage threshold
      run: |
        poetry run coverage report --fail-under=${{ env.COVERAGE_THRESHOLD }}
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: |
          test-results/
          htmlcov/

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_publishai
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      
    - name: Install dependencies
      run: poetry install --no-interaction
      
    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_publishai
        REDIS_URL: redis://localhost:6379
        ENVIRONMENT: test
        TESTING: true
        MOCK_EXTERNAL_APIS: true
      run: |
        poetry run pytest tests/integration/ ${{ env.PYTEST_OPTS }} \
          --junit-xml=test-results/integration-test-results.xml
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: test-results/

  agent-tests:
    name: PydanticAI Agent Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_publishai
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      
    - name: Install dependencies
      run: poetry install --no-interaction
      
    - name: Run agent tests
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_publishai
        REDIS_URL: redis://localhost:6379
        ENVIRONMENT: test
        TESTING: true
        MOCK_EXTERNAL_APIS: true
        OPENAI_API_KEY: test-key-openai
        ANTHROPIC_API_KEY: test-key-anthropic
      run: |
        if [ -f tests/test_agents/run_agent_tests.py ]; then
          python tests/test_agents/run_agent_tests.py --suite all --mock-only
        else
          poetry run pytest tests/test_agents/ ${{ env.PYTEST_OPTS }} \
            --junit-xml=test-results/agent-test-results.xml
        fi
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: agent-test-results
        path: test-results/

  # ============================================
  # SECURITY SCANNING
  # ============================================
  
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Bandit Security Linter
      uses: securecodewarrior/github-action-bandit@v1.0.1
      with:
        path: app/
        
    - name: Run Safety Check
      uses: pyupio/safety@2.3.5
      with:
        api-key: ${{ secrets.SAFETY_API_KEY }}
        
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/python

  # ============================================
  # BUILD JOBS
  # ============================================
  
  build-images:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: [validate-code, validate-docker, unit-tests, integration-tests]
    
    strategy:
      matrix:
        service: 
          - api-gateway
          - content-generation
          - market-intelligence
          - publishing-service
          - cover-designer
          - sales-monitor
          - personalization
          - research
          - multimodal-generator
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: services/${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          SERVICE_NAME=${{ matrix.service }}
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}

  # ============================================
  # DEPLOYMENT JOBS
  # ============================================
  
  deploy-development:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.4'
        
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG_DEV }}" | base64 -d > ~/.kube/config
        
    - name: Deploy to development
      run: |
        chmod +x ci-cd/scripts/deploy-to-k8s.sh
        ci-cd/scripts/deploy-to-k8s.sh \
          --environment development \
          --image-tag ${{ github.sha }} \
          --registry ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

  integration-test-dev:
    name: Integration Test Development
    runs-on: ubuntu-latest
    needs: [deploy-development]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install test dependencies
      run: |
        pip install requests pytest pytest-html
        
    - name: Run integration tests against development
      env:
        API_GATEWAY_URL: ${{ secrets.DEV_API_URL }}
      run: |
        chmod +x ci-cd/scripts/run-tests.sh
        ci-cd/scripts/run-tests.sh --suite integration --fast
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dev-integration-test-results
        path: test-results/

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [integration-test-dev]
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.4'
        
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG_STAGING }}" | base64 -d > ~/.kube/config
        
    - name: Deploy to staging
      run: |
        chmod +x ci-cd/scripts/deploy-to-k8s.sh
        ci-cd/scripts/deploy-to-k8s.sh \
          --environment staging \
          --image-tag ${{ github.sha }} \
          --registry ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }} \
          --strategy blue-green

  e2e-test-staging:
    name: E2E Test Staging
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Playwright
      run: |
        pip install playwright pytest-playwright
        playwright install chromium
        
    - name: Run E2E tests against staging
      env:
        API_GATEWAY_URL: ${{ secrets.STAGING_API_URL }}
        FRONTEND_URL: ${{ secrets.STAGING_FRONTEND_URL }}
      run: |
        pytest tests/e2e/ --html=e2e-report.html --self-contained-html
        
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: |
          e2e-report.html
          test-results/

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [e2e-test-staging]
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.4'
        
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > ~/.kube/config
        
    - name: Run production readiness check
      run: |
        curl -fsSL https://raw.githubusercontent.com/publishai/infrastructure/main/production/scripts/production-readiness-check.sh | bash -s -- --severity critical
        
    - name: Deploy to production with canary strategy
      run: |
        chmod +x ci-cd/scripts/deploy-to-k8s.sh
        ci-cd/scripts/deploy-to-k8s.sh \
          --environment production \
          --image-tag ${{ github.sha }} \
          --registry ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }} \
          --strategy canary

  post-deploy-verification:
    name: Post-Deploy Verification
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.4'
        
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > ~/.kube/config
        
    - name: Run post-deployment verification
      run: |
        curl -fsSL https://raw.githubusercontent.com/publishai/infrastructure/main/production/scripts/verify-production-deployment.sh | bash
        
    - name: Send deployment notification
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: |
          🚀 Publish AI deployed to production!
          Commit: ${{ github.sha }}
          Release: ${{ github.event.release.tag_name }}
          Status: ${{ job.status }}

  # ============================================
  # CLEANUP JOBS
  # ============================================
  
  cleanup-registry:
    name: Cleanup Container Registry
    runs-on: ubuntu-latest
    needs: [post-deploy-verification]
    if: github.event_name == 'release'
    
    steps:
    - name: Delete old container images
      uses: actions/delete-package-versions@v4
      with:
        package-name: ${{ env.IMAGE_NAME }}
        package-type: container
        min-versions-to-keep: 10
        delete-only-untagged-versions: false