# 🚀 Supabase Migration Guide

This guide will help you migrate from the SQLAlchemy-based system to a fully Supabase-powered backend.

## 📋 Overview

### What Changed
- **Database**: SQLAlchemy + SQLite/PostgreSQL → Supabase PostgreSQL
- **Authentication**: Custom JWT → Supabase Auth
- **Storage**: Local files → Supabase Storage
- **Real-time**: None → Supabase Realtime
- **API**: SQLAlchemy models → Supabase client

### Benefits of Migration
- ✅ **Scalable**: Auto-scaling PostgreSQL database
- ✅ **Secure**: Built-in RLS (Row Level Security)
- ✅ **Real-time**: Live subscriptions and updates
- ✅ **Storage**: Integrated file storage for covers/manuscripts
- ✅ **Authentication**: Production-ready auth with social providers
- ✅ **Dashboard**: Built-in admin dashboard
- ✅ **Backup**: Automatic daily backups

## 🛠️ Migration Steps

### Step 1: Create Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Create a new project
3. Choose your region (closest to your users)
4. Set a strong database password
5. Wait for project initialization (~2 minutes)

### Step 2: Get Credentials

In your Supabase dashboard, go to **Settings > API**:

```bash
# Project URL
SUPABASE_URL=https://your-project.supabase.co

# API Keys
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Go to **Settings > Database** for the connection string:

```bash
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### Step 3: Run Schema Migration

1. Copy the SQL from `supabase_schema.sql`
2. Go to **SQL Editor** in your Supabase dashboard
3. Paste and run the entire script
4. Verify all tables were created (should see 7 tables)

### Step 4: Configure Environment

1. Copy `.env.supabase.example` to `.env`
2. Fill in your Supabase credentials:

```bash
# Copy the example
cp .env.supabase.example .env

# Edit with your credentials
nano .env
```

Required variables:
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### Step 5: Update Dependencies

Ensure you have the Supabase dependencies:

```bash
# Install/update dependencies
poetry install

# Or with pip
pip install supabase asyncpg
```

### Step 6: Start New Application

```bash
# Start the Supabase-powered version
python app/main_supabase.py

# Or with uvicorn
uvicorn app.main_supabase:app --reload --host 0.0.0.0 --port 8000
```

### Step 7: Verify Migration

1. Visit `http://localhost:8000/health` - should show Supabase status
2. Visit `http://localhost:8000/api/setup/validate` - validate setup
3. Test user registration: `POST /api/auth/register`
4. Check Supabase dashboard for new user

## 📊 Database Schema

The new schema includes:

### Core Tables
- **users** - User profiles and preferences
- **books** - Book manuscripts and metadata
- **publications** - KDP publications and status
- **sales_data** - Sales performance tracking
- **feedback_metrics** - VERL training data
- **trend_analyses** - Market trend analysis
- **verl_training_jobs** - ML training jobs

### Views
- **user_analytics** - Aggregated user statistics
- **book_performance** - Book performance metrics

### Features
- **UUID primary keys** for better scalability
- **JSON columns** for flexible metadata
- **Computed columns** for derived values
- **Row Level Security** for data isolation
- **Triggers** for automatic timestamps
- **Indexes** for optimal performance

## 🔐 Authentication Changes

### Old System (JWT)
```python
# Manual token creation
token = create_access_token({"sub": user.email})
```

### New System (Supabase Auth)
```python
# Automatic token management
result = await auth_service.login_user(email, password)
access_token = result["access_token"]
```

### Benefits
- ✅ Email verification
- ✅ Password reset flows
- ✅ Social authentication ready
- ✅ Session management
- ✅ Security best practices

## 📡 API Changes

### Endpoint Structure Preserved
All existing endpoints work the same:
- `GET /api/books` - List books
- `POST /api/books` - Create book
- `POST /api/auth/login` - Login user

### Enhanced Responses
```json
{
  "id": "uuid-v4-format",
  "user_id": "uuid-v4-format", 
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### New Endpoints
- `GET /api/setup/validate` - Validate setup
- `POST /api/setup/migrate-schema` - Schema migration
- `GET /api/verl/stats` - Enhanced VERL stats

## 🎯 VERL Integration

VERL (reinforcement learning) is fully preserved and enhanced:

### Data Collection
```python
# Automatic feedback collection
await feedback_model.record_feedback({
    "user_id": user_id,
    "book_id": book_id,
    "metric_type": "user_approval",
    "metric_value": 1.0,
    "approved": True
})
```

### Training Data
- Stored in `feedback_metrics` table
- Real-time aggregation with views
- Enhanced reward calculation
- Better performance tracking

## 🔧 Development Workflow

### Local Development
```bash
# Start development server
python app/main_supabase.py

# Run tests (updated for Supabase)
pytest tests/

# Check database health
curl http://localhost:8000/health
```

### Production Deployment
```bash
# Set production environment
export ENVIRONMENT=production

# Use production Supabase project
export SUPABASE_URL=https://your-prod-project.supabase.co

# Deploy with your preferred method
```

## 📈 Performance Improvements

### Database Performance
- **Connection pooling** with asyncpg
- **Prepared statements** for common queries
- **Indexes** on frequently queried columns
- **Views** for complex aggregations

### Scalability
- **Auto-scaling** PostgreSQL database
- **CDN** for file storage
- **Real-time** subscriptions
- **Geographic distribution**

## 🚨 Troubleshooting

### Common Issues

#### 1. Connection Failed
```bash
# Check credentials
curl https://your-project.supabase.co/rest/v1/ \
  -H "apikey: your-anon-key"
```

#### 2. Schema Missing
```bash
# Validate schema
curl http://localhost:8000/api/setup/validate
```

#### 3. Authentication Issues
```bash
# Test user creation in Supabase dashboard
# Auth > Users > "Add user"
```

### Error Messages

#### "Supabase client not initialized"
- Check `SUPABASE_URL` and `SUPABASE_SERVICE_KEY`
- Verify credentials are correct

#### "Schema validation failed"
- Run the SQL migration script
- Check table creation in Supabase dashboard

#### "Authentication failed"
- Verify user exists in Supabase Auth
- Check token format and expiration

## 🔄 Rollback Plan

If you need to rollback to the old system:

```bash
# Use the original main.py
python app/main.py

# Restore original database
# (if you backed up your SQLite/PostgreSQL data)
```

## 📝 Migration Checklist

- [ ] Create Supabase project
- [ ] Get API credentials
- [ ] Run schema migration
- [ ] Configure environment variables
- [ ] Test database connection
- [ ] Test user registration
- [ ] Test book creation
- [ ] Verify VERL integration
- [ ] Run health checks
- [ ] Update frontend (if needed)
- [ ] Deploy to production
- [ ] Monitor performance

## 🎉 Next Steps

After successful migration:

1. **Enable RLS**: Set up Row Level Security policies
2. **Configure Storage**: Set up buckets for covers/manuscripts
3. **Set up Realtime**: Enable live subscriptions
4. **Add Social Auth**: Configure OAuth providers
5. **Monitor Usage**: Set up alerts and monitoring
6. **Scale**: Configure auto-scaling policies

## 📞 Support

If you encounter issues:

1. Check the [Supabase Documentation](https://supabase.com/docs)
2. Visit the health endpoint: `/health`
3. Check application logs
4. Validate setup: `/api/setup/validate`

---

**🎯 You're now running on Supabase! Welcome to the next generation of your AI e-book platform!**