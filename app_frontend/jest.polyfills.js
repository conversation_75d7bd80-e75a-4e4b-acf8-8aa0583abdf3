// Polyfills for jsdom environment

// TextEncoder/TextDecoder polyfill
const { TextEncoder, TextDecoder } = require('util')

Object.assign(global, { TextDecoder, TextEncoder })

// Fetch polyfill
global.fetch = jest.fn()

// FormData polyfill
global.FormData = class FormData {
  constructor() {
    this.data = new Map()
  }
  
  append(key, value) {
    if (!this.data.has(key)) {
      this.data.set(key, [])
    }
    this.data.get(key).push(value)
  }
  
  get(key) {
    const values = this.data.get(key)
    return values ? values[0] : null
  }
  
  getAll(key) {
    return this.data.get(key) || []
  }
  
  has(key) {
    return this.data.has(key)
  }
  
  delete(key) {
    this.data.delete(key)
  }
  
  entries() {
    return this.data.entries()
  }
  
  keys() {
    return this.data.keys()
  }
  
  values() {
    return this.data.values()
  }
}

// File polyfill
global.File = class File {
  constructor(bits, name, options = {}) {
    this.bits = bits
    this.name = name
    this.type = options.type || ''
    this.lastModified = options.lastModified || Date.now()
    this.size = bits.reduce((acc, bit) => acc + bit.length, 0)
  }
}

// Blob polyfill
global.Blob = class Blob {
  constructor(blobParts = [], options = {}) {
    this.size = blobParts.reduce((acc, part) => acc + part.length, 0)
    this.type = options.type || ''
  }
}

// URL polyfill
global.URL = class URL {
  constructor(url, base) {
    this.href = url
    this.origin = 'http://localhost:3000'
    this.protocol = 'http:'
    this.host = 'localhost:3000'
    this.hostname = 'localhost'
    this.port = '3000'
    this.pathname = '/'
    this.search = ''
    this.hash = ''
  }
  
  static createObjectURL() {
    return 'blob:mock-url'
  }
  
  static revokeObjectURL() {
    return undefined
  }
}

// MutationObserver polyfill
global.MutationObserver = class MutationObserver {
  constructor(callback) {
    this.callback = callback
  }
  
  observe() {}
  disconnect() {}
  takeRecords() {
    return []
  }
}

// getComputedStyle polyfill
global.getComputedStyle = (element) => ({
  getPropertyValue: () => '',
  color: 'rgb(0, 0, 0)',
  backgroundColor: 'rgb(255, 255, 255)',
})

// Selection API polyfill
global.getSelection = () => ({
  toString: () => '',
  addRange: () => {},
  removeAllRanges: () => {},
})

// Range API polyfill
global.Range = class Range {
  createContextualFragment() {
    const fragment = document.createDocumentFragment()
    return fragment
  }
}

// localStorage/sessionStorage polyfill (enhanced)
const createStorage = () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  key: jest.fn(),
  length: 0,
})

Object.defineProperty(window, 'localStorage', {
  value: createStorage(),
  writable: true,
})

Object.defineProperty(window, 'sessionStorage', {
  value: createStorage(),
  writable: true,
})

// Navigator polyfill
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn(() => Promise.resolve()),
    readText: jest.fn(() => Promise.resolve('')),
  },
  writable: true,
})

Object.defineProperty(navigator, 'userAgent', {
  value: 'Mozilla/5.0 (Node.js jsdom)',
  writable: true,
})

// CustomEvent polyfill
global.CustomEvent = class CustomEvent extends Event {
  constructor(type, options = {}) {
    super(type, options)
    this.detail = options.detail
  }
}

// DOMRect polyfill
global.DOMRect = class DOMRect {
  constructor(x = 0, y = 0, width = 0, height = 0) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.top = y
    this.right = x + width
    this.bottom = y + height
    this.left = x
  }
}

// Touch events polyfill
global.TouchEvent = class TouchEvent extends Event {
  constructor(type, options = {}) {
    super(type, options)
    this.touches = options.touches || []
    this.targetTouches = options.targetTouches || []
    this.changedTouches = options.changedTouches || []
  }
}