#!/bin/bash

# ============================================================================
# Next.js 15 + React 19 Dependencies Installation Script
# ============================================================================
# This script installs all required dependencies for the Publish AI frontend
# Compatible with Next.js 15.3.4 and React 19.1.0
# 
# Usage:
#   ./install-dependencies.sh          # Normal installation
#   ./install-dependencies.sh --clean  # Clean install (removes node_modules first)
# ============================================================================

set -e  # Exit on any error

echo "🚀 Starting Next.js 15 + React 19 Dependencies Installation..."
echo "=================================================="

# Check for --clean flag
if [[ "$1" == "--clean" ]]; then
    echo ""
    echo "🧹 Clean installation requested..."
    rm -rf node_modules package-lock.json
    echo "✅ Removed node_modules and package-lock.json"
    echo ""
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from your Next.js project root."
    exit 1
fi

# Function to clean node_modules if installation fails
clean_node_modules() {
    print_warning "Cleaning node_modules directory due to installation issues..."
    rm -rf node_modules package-lock.json
    print_success "Cleaned node_modules and package-lock.json"
}

# Function to install with retry on failure
install_with_retry() {
    local packages="$1"
    local description="$2"
    
    if ! npm install $packages --legacy-peer-deps 2>/dev/null; then
        print_warning "Installation failed, cleaning and retrying..."
        clean_node_modules
        npm install $packages --legacy-peer-deps
    fi
}

print_status "Installing core Next.js 15 and React 19 dependencies..."

# ============================================================================
# Phase 1: Core Framework Dependencies (Install if missing)
# ============================================================================
print_status "Phase 1: Checking and installing core framework dependencies..."

# Verify existing Next.js and React versions
if npm list next >/dev/null 2>&1; then
    NEXT_VERSION=$(npm list next --depth=0 2>/dev/null | grep next@ | cut -d'@' -f2 | head -1)
    print_success "Found existing Next.js version: $NEXT_VERSION"
else
    print_warning "Next.js not found in package.json - installing latest versions..."
    install_with_retry "next@latest react@latest react-dom@latest" "Next.js, React, and React DOM"
    print_success "Installed Next.js, React, and React DOM (latest versions)"
fi

if npm list react >/dev/null 2>&1; then
    REACT_VERSION=$(npm list react --depth=0 2>/dev/null | grep react@ | cut -d'@' -f2 | head -1)
    print_success "Found existing React version: $REACT_VERSION"
else
    print_warning "React not found in package.json - installing latest versions..."
    install_with_retry "react@latest react-dom@latest" "React and React DOM"
    print_success "Installed React and React DOM (latest versions)"
fi

# ============================================================================
# Phase 2: TypeScript and Development Dependencies
# ============================================================================
print_status "Phase 2: Installing TypeScript and development tools..."

npm install --save-dev \
  typescript@latest \
  @types/node@latest \
  @types/react@latest \
  @types/react-dom@latest \
  eslint@latest \
  eslint-config-next@latest \
  --legacy-peer-deps

print_success "TypeScript and dev tools installed!"

# ============================================================================
# Phase 3: Authentication Dependencies
# ============================================================================
print_status "Phase 3: Installing authentication dependencies..."

npm install \
  next-auth@latest \
  @auth/prisma-adapter@latest \
  --legacy-peer-deps

print_success "Authentication dependencies installed!"

# ============================================================================
# Phase 4: UI Framework Dependencies (Radix UI + Tailwind)
# ============================================================================
print_status "Phase 4: Installing UI framework dependencies..."

npm install \
  @radix-ui/react-alert-dialog@latest \
  @radix-ui/react-avatar@latest \
  @radix-ui/react-checkbox@latest \
  @radix-ui/react-dialog@latest \
  @radix-ui/react-dropdown-menu@latest \
  @radix-ui/react-label@latest \
  @radix-ui/react-progress@latest \
  @radix-ui/react-scroll-area@latest \
  @radix-ui/react-select@latest \
  @radix-ui/react-separator@latest \
  @radix-ui/react-slider@latest \
  @radix-ui/react-slot@latest \
  @radix-ui/react-switch@latest \
  @radix-ui/react-tabs@latest \
  @radix-ui/react-toast@latest \
  --legacy-peer-deps

print_success "Radix UI components installed!"

# ============================================================================
# Phase 5: Tailwind CSS v4 Dependencies
# ============================================================================
print_status "Phase 5: Installing Tailwind CSS v4..."

npm install \
  tailwindcss@latest \
  @tailwindcss/postcss@latest \
  postcss@latest \
  @tailwindcss/forms@latest \
  @tailwindcss/typography@latest \
  tailwind-merge@latest \
  tailwindcss-animate@latest \
  --legacy-peer-deps

print_success "Tailwind CSS v4 installed!"

# ============================================================================
# Phase 6: Form and Validation Dependencies
# ============================================================================
print_status "Phase 6: Installing form and validation libraries..."

npm install \
  react-hook-form@latest \
  @hookform/resolvers@latest \
  zod@latest \
  --legacy-peer-deps

print_success "Form libraries installed!"

# ============================================================================
# Phase 7: State Management and Data Fetching
# ============================================================================
print_status "Phase 7: Installing state management and data fetching..."

npm install \
  @tanstack/react-query@latest \
  @tanstack/react-query-devtools@latest \
  axios@latest \
  --legacy-peer-deps

print_success "State management libraries installed!"

# ============================================================================
# Phase 8: UI Utilities and Icons
# ============================================================================
print_status "Phase 8: Installing UI utilities and icons..."

npm install \
  lucide-react@latest \
  clsx@latest \
  class-variance-authority@latest \
  cmdk@latest \
  react-hot-toast@latest \
  --legacy-peer-deps

print_success "UI utilities installed!"

# ============================================================================
# Phase 9: Charts and Data Visualization
# ============================================================================
print_status "Phase 9: Installing charts and visualization..."

npm install \
  recharts@latest \
  date-fns@latest \
  --legacy-peer-deps

print_success "Chart libraries installed!"

# ============================================================================
# Phase 10: Additional UI Libraries
# ============================================================================
print_status "Phase 10: Installing additional UI libraries..."

npm install \
  @headlessui/react@latest \
  --legacy-peer-deps

print_success "Additional UI libraries installed!"

# ============================================================================
# Phase 11: Testing Dependencies (Optional)
# ============================================================================
print_status "Phase 11: Installing testing dependencies..."

npm install --save-dev \
  @testing-library/react@latest \
  @testing-library/jest-dom@latest \
  @testing-library/user-event@latest \
  jest@latest \
  jest-environment-jsdom@latest \
  @types/jest@latest \
  --legacy-peer-deps

print_success "Testing libraries installed!"

# ============================================================================
# Phase 12: E2E Testing (Optional)
# ============================================================================
print_status "Phase 12: Installing E2E testing dependencies..."

npm install --save-dev \
  @playwright/test@latest \
  --legacy-peer-deps

print_success "E2E testing installed!"

# ============================================================================
# Phase 13: Build and Performance Tools
# ============================================================================
print_status "Phase 13: Installing build and performance tools..."

npm install --save-dev \
  webpack-bundle-analyzer@latest \
  --legacy-peer-deps

npm install \
  web-vitals@latest \
  --legacy-peer-deps

print_success "Build tools installed!"

# ============================================================================
# Phase 14: Security and Authentication Utilities
# ============================================================================
print_status "Phase 14: Installing security utilities..."

npm install \
  bcryptjs@latest \
  @types/bcryptjs@latest \
  --legacy-peer-deps

print_success "Security utilities installed!"

# ============================================================================
# Phase 15: CSS Processing and Optimization
# ============================================================================
print_status "Phase 15: Installing CSS processing tools..."

npm install \
  critters@latest \
  --legacy-peer-deps

print_success "CSS tools installed!"

# ============================================================================
# Final Setup and Verification
# ============================================================================
print_status "Running final setup and verification..."

# Clear any potential cache issues
npm cache clean --force 2>/dev/null || true

# Verify installation
print_status "Verifying installation..."

if npm list next >/dev/null 2>&1; then
    NEXT_VERSION=$(npm list next --depth=0 2>/dev/null | grep next@ | cut -d'@' -f2)
    print_success "Next.js version: $NEXT_VERSION"
else
    print_error "Next.js installation verification failed"
    exit 1
fi

if npm list react >/dev/null 2>&1; then
    REACT_VERSION=$(npm list react --depth=0 2>/dev/null | grep react@ | cut -d'@' -f2)
    print_success "React version: $REACT_VERSION"
else
    print_error "React installation verification failed"
    exit 1
fi

# Check for common conflicts
print_status "Checking for dependency conflicts..."

if npm ls >/dev/null 2>&1; then
    print_success "No dependency conflicts detected!"
else
    print_warning "Some peer dependency warnings detected (this is normal with --legacy-peer-deps)"
fi

# ============================================================================
# Generate helpful configuration files
# ============================================================================
print_status "Generating configuration files..."

# Create minimal tailwind.config.ts for v4 (optional - v4 has zero config by default)
if [ ! -f "tailwind.config.ts" ]; then
    cat > tailwind.config.ts << 'EOF'
import type { Config } from "tailwindcss"

// Tailwind CSS v4 requires minimal configuration
// Most configuration is now done directly in CSS
const config: Config = {
  // Content detection is automatic in v4, but you can still override if needed
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  // Theme customization is now done in CSS using @theme directive
  theme: {
    extend: {},
  },
  plugins: [],
}

export default config
EOF
    print_success "Generated tailwind.config.ts (v4 minimal config)"
fi

# Create postcss.config.mjs for Tailwind CSS v4
if [ ! -f "postcss.config.mjs" ]; then
    cat > postcss.config.mjs << 'EOF'
/** @type {import('postcss-load-config').Config} */
export default {
  plugins: {
    '@tailwindcss/postcss': {},
  },
}
EOF
    print_success "Generated postcss.config.mjs (Tailwind CSS v4)"
fi

# Create next.config.js if it doesn't exist
if [ ! -f "next.config.js" ]; then
    cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-tabs',
      'recharts',
    ],
  },
  images: {
    formats: ['image/webp', 'image/avif'],
  },
}

module.exports = nextConfig
EOF
    print_success "Generated next.config.js"
fi

# Create components.json for shadcn/ui compatibility
if [ ! -f "components.json" ]; then
    cat > components.json << 'EOF'
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/app/globals.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
EOF
    print_success "Generated components.json"
fi

# Create or update globals.css for Tailwind CSS v4
if [ ! -f "src/app/globals.css" ]; then
    mkdir -p src/app
    cat > src/app/globals.css << 'EOF'
@import "tailwindcss";

/* Tailwind CSS v4 - All configuration can be done here in CSS */

/* Custom CSS variables for design system */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
EOF
    print_success "Generated src/app/globals.css for Tailwind CSS v4"
else
    print_warning "src/app/globals.css already exists - you may need to update it manually for v4"
    echo "          Add '@import \"tailwindcss\";' at the top of your globals.css"
fi

# ============================================================================
# Success Summary
# ============================================================================
echo ""
echo "🎉 INSTALLATION COMPLETE!"
echo "=================================================="
print_success "All dependencies have been successfully installed!"
echo ""
echo "📋 What was installed:"
echo "   ✅ Next.js and React (installed if missing)"
echo "   ✅ TypeScript with latest types"
echo "   ✅ NextAuth.js for authentication"
echo "   ✅ Radix UI components"
echo "   ✅ Tailwind CSS v4 with PostCSS plugin"
echo "   ✅ React Hook Form + Zod validation"
echo "   ✅ TanStack Query for data fetching"
echo "   ✅ Lucide React icons"
echo "   ✅ Testing libraries (Jest, Testing Library, Playwright)"
echo "   ✅ Charts and visualization (Recharts)"
echo "   ✅ Security utilities"
echo "   ✅ Development and build tools"
echo ""
echo "📁 Configuration files created:"
echo "   ✅ tailwind.config.ts (v4 minimal config)"
echo "   ✅ postcss.config.mjs (v4 PostCSS plugin)"
echo "   ✅ next.config.js"
echo "   ✅ components.json"
echo "   ✅ src/app/globals.css (v4 CSS-first config)"
echo ""
echo "🚀 Next steps:"
echo "   1. If globals.css exists, add '@import \"tailwindcss\";' at the top"
echo "   2. Run 'npm run dev --turbo' to start development with Turbopack"
echo "   3. Run 'npm run build' to test production build"
echo "   4. Set up your environment variables (.env.local)"
echo "   5. Start building your components!"
echo ""
echo "📖 Useful commands:"
echo "   npm run dev --turbo    # Development with Turbopack (faster)"
echo "   npm run dev            # Standard development"
echo "   npm run build          # Production build"
echo "   npm run lint           # Run ESLint"
echo "   npm test               # Run tests"
echo ""
print_success "Happy coding! 🎉"