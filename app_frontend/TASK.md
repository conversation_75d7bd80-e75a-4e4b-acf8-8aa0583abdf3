# Bridges Market Trends Page - Implementation Task

## 🎯 Project Overview

Build a comprehensive **Trends page** for Bridges Market - a real estate tokenization platform. The page will display market analytics, AI forecasts, and interactive visualizations for token trading data.

## ✅ Requirements

### Core Features
- **Market Data Display**: Token trading volume, average price, holding duration
- **Geographic Visualization**: Interactive heatmap showing regional activity
- **AI Forecasts**: Token price predictions, rental yields, sentiment analysis
- **Interactive Charts**: Volume trends, price charts, yield curves
- **Filtering System**: Asset type, region, time period filters
- **AI Commentary**: Narrative insights validated via PydanticAI schema
- **Export Options**: CSV, JSON, PDF snapshot capabilities
- **Alert System**: User-configurable alerts for volume spikes, price changes
- **Real-time Updates**: WebSocket integration for live data

### Technical Constraints
- **Frontend**: React + Next.js 15 + TypeScript
- **Styling**: Tailwind CSS v4
- **Charts**: Recharts for all visualizations
- **Data**: Supabase database + Redis caching
- **Architecture**: Modular, reusable components

## 📋 Implementation Plan

### 1. Component Architecture

```typescript
// Main page structure
<TrendsPage>
  <TrendsHeader />           // Title, refresh, actions
  <TrendsFilters />          // Asset, region, time filters  
  <TrendsMetrics />          // Key metrics cards
  <TrendsCharts />           // Interactive chart grid
  <TrendsHeatmap />          // Geographic visualization
  <TrendsForecasts />        // AI predictions panel
  <TrendsActions />          // Export, alerts, share
</TrendsPage>
```

### 2. Data Flow Architecture

```
Supabase DB → Redis Cache → Next.js API → React Query → Components
     ↓              ↓            ↓            ↓           ↓
Real-time     5min cache    REST endpoints   State    UI Updates
WebSocket     invalidation  /api/trends/*   management
```

### 3. API Endpoints

```
/api/trends/metrics       - Volume, price, duration metrics
/api/trends/charts        - Chart data (volume, price, yield)
/api/trends/heatmap       - Geographic data points
/api/trends/forecasts     - AI predictions and sentiment
/api/trends/commentary    - PydanticAI generated insights
/api/trends/export        - Data export in multiple formats
/api/trends/alerts        - Alert configuration and triggers
/api/webhooks/alerts      - Webhook endpoints for notifications
```

## 🧩 Component Specifications

### TrendsHeader
```typescript
interface TrendsHeaderProps {
  lastUpdated: Date;
  isLoading: boolean;
  onRefresh: () => void;
  onExport: (format: 'csv' | 'json' | 'pdf') => void;
  onConfigureAlerts: () => void;
}
```

### TrendsFilters
```typescript
interface TrendsFiltersProps {
  filters: {
    assetTypes: string[];      // ['residential', 'commercial', 'land']
    regions: string[];         // ['north-america', 'europe', 'asia']
    timePeriod: TimePeriod;    // '1D' | '7D' | '30D' | '3M' | '1Y' | 'custom'
    customDateRange?: [Date, Date];
  };
  onFiltersChange: (filters: Partial<TrendsFilters>) => void;
  availableAssetTypes: AssetType[];
  availableRegions: Region[];
  presets: FilterPreset[];     // "Hot Markets", "New Listings"
}
```

### TrendsMetrics
```typescript
interface TrendsMetricsProps {
  metrics: {
    totalVolume: number;
    avgPrice: number;
    avgHoldingDuration: number;  // in months
    volumeChange: number;        // percentage
    priceChange: number;         // percentage
    durationChange: number;      // percentage
  };
  loading: boolean;
  timeframe: string;
}
```

### TrendsCharts
```typescript
interface TrendsChartsProps {
  volumeData: ChartDataPoint[];
  priceData: ChartDataPoint[];
  yieldData: YieldCurvePoint[];
  onChartInteraction: (event: ChartEvent) => void;
  selectedTimeRange: [Date, Date];
}

interface ChartDataPoint {
  timestamp: string;
  value: number;
  volume?: number;
  assetType?: string;
}

interface YieldCurvePoint {
  duration: number;           // months
  historicalYield: number;    // percentage
  forecastYield: number;      // percentage
  confidenceInterval: [number, number];
}
```

### TrendsHeatmap
```typescript
interface TrendsHeatmapProps {
  data: GeographicDataPoint[];
  metric: 'volume' | 'price' | 'sentiment';
  onRegionClick: (region: string) => void;
  onMetricChange: (metric: string) => void;
  colorScale: ColorScale;
}

interface GeographicDataPoint {
  region: string;
  lat: number;
  lng: number;
  volume: number;
  avgPrice: number;
  sentiment: number;         // -100 to 100
  count: number;            // number of transactions
}
```

### TrendsForecasts
```typescript
interface TrendsForecastsProps {
  forecasts: {
    priceForecasts: PriceForecast[];
    yieldForecasts: YieldForecast[];
    sentimentIndex: SentimentData;
  };
  commentary: AICommentary;
  confidenceLevel: number;
  onCommentaryRefresh: () => void;
}

interface PriceForecast {
  assetType: string;
  currentPrice: number;
  predictedPrice: number;
  confidenceInterval: [number, number];
  timeHorizon: string;      // '1M', '3M', '6M', '1Y'
}

interface AICommentary {
  content: string;
  sources: string[];
  confidence: number;
  lastUpdated: Date;
  validatedSchema: boolean;  // PydanticAI validation
}
```

## 📊 Visual Layout Design

```
┌─────────────────────────────────────────────────────────────────────┐
│ BRIDGES MARKET TRENDS                    [🔄] [📊] [🔔] [⚙️]       │
├─────────────────────────────────────────────────────────────────────┤
│ Filters: [Asset Type ▼] [Region ▼] [Time ▼] [Hot Markets] [Apply]   │
├───────────────┬───────────────┬───────────────┬─────────────────────┤
│   Volume      │  Avg Price    │ Hold Duration │   Last Updated      │
│  $2.4M ↑12%   │ $245K ↓3%     │  18m ↑5%      │   2 mins ago       │
├───────────────┴───────────────┴───────────────┴─────────────────────┤
│ ┌─────────────────────┐  ┌───────────────────────────────────────┐  │
│ │   Trading Volume    │  │        Geographic Heatmap             │  │
│ │                     │  │                                       │  │
│ │  [Line Chart with   │  │    [World Map with Color Coding]     │  │
│ │   hover tooltips]   │  │    Layers: Volume | Price | Sentiment│  │
│ │                     │  │                                       │  │
│ └─────────────────────┘  └───────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐  ┌───────────────────────────────────────┐  │
│ │   Price Trends      │  │         Yield Curve                   │  │
│ │                     │  │                                       │  │
│ │ [Multi-asset lines  │  │  [Area chart: Historical vs Forecast]│  │
│ │  with legend]       │  │   Confidence intervals shown         │  │
│ │                     │  │                                       │  │
│ └─────────────────────┘  └───────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────┤
│                           AI FORECASTS                              │
│ ┌──────────────┐ ┌──────────────┐ ┌─────────────────────────────┐   │
│ │Price Forecast│ │Yield Forecast│ │      Sentiment Gauge        │   │
│ │             │ │              │ │                             │   │
│ │   +15% ±3%   │ │   6.2% ±0.5% │ │    [Circular Gauge]         │   │
│ │  (Next 3M)   │ │  (12M Avg)   │ │      Bullish 75%            │   │
│ └──────────────┘ └──────────────┘ └─────────────────────────────┘   │
│                                                                     │
│ 📝 AI Commentary: "Market momentum strengthening in urban areas.    │
│    Rising institutional interest driving volume increases..."        │
├─────────────────────────────────────────────────────────────────────┤
│ [🔔 Set Alert] [📤 Export CSV] [📊 Export JSON] [📄 PDF] [🔗 API]  │
└─────────────────────────────────────────────────────────────────────┘
```

## 🛠️ Technical Implementation

### Chart Components (Recharts)

```typescript
// Volume Chart with hover interactions
const VolumeChart: React.FC<VolumeChartProps> = ({ data, onInteraction }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data} onMouseMove={onInteraction}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={(value) => format(new Date(value), 'MMM dd')}
        />
        <YAxis 
          tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`}
        />
        <Tooltip 
          content={<CustomTooltip />}
          cursor={{ stroke: '#FF385C', strokeWidth: 1 }}
        />
        <Line 
          type="monotone" 
          dataKey="volume" 
          stroke="#FF385C" 
          strokeWidth={2}
          dot={{ fill: '#FF385C', strokeWidth: 0, r: 4 }}
          activeDot={{ r: 6, stroke: '#FF385C', strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Sentiment Gauge with custom SVG
const SentimentGauge: React.FC<SentimentGaugeProps> = ({ value, trend }) => {
  const normalizedValue = (value + 100) / 200; // Convert -100,100 to 0,1
  const rotation = normalizedValue * 180 - 90; // Convert to degrees
  
  return (
    <div className="relative w-48 h-32">
      <svg viewBox="0 0 200 120" className="w-full h-full">
        {/* Background arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke="#e5e7eb"
          strokeWidth="8"
          strokeLinecap="round"
        />
        {/* Colored progress arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke={getSentimentColor(value)}
          strokeWidth="8"
          strokeLinecap="round"
          strokeDasharray={`${normalizedValue * 251.2} 251.2`}
        />
        {/* Needle */}
        <line
          x1="100"
          y1="100"
          x2={100 + 70 * Math.cos((rotation * Math.PI) / 180)}
          y2={100 + 70 * Math.sin((rotation * Math.PI) / 180)}
          stroke="#374151"
          strokeWidth="2"
        />
        <circle cx="100" cy="100" r="4" fill="#374151" />
      </svg>
      
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center">
        <div className="text-2xl font-bold">{value > 0 ? '+' : ''}{value}</div>
        <div className="text-sm text-gray-600 flex items-center">
          <TrendIndicator direction={trend} />
          <span className="ml-1">
            {value > 20 ? 'Bullish' : value < -20 ? 'Bearish' : 'Neutral'}
          </span>
        </div>
      </div>
    </div>
  );
};
```

### Data Fetching Hooks

```typescript
// Custom hooks for data fetching
export const useTrendsMetrics = (filters: TrendsFilters) => {
  return useQuery({
    queryKey: ['trends', 'metrics', filters],
    queryFn: () => fetchTrendsMetrics(filters),
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchInterval: 1000 * 60 * 5, // 5 minutes
  });
};

export const useTrendsChartData = (filters: TrendsFilters) => {
  return useQuery({
    queryKey: ['trends', 'charts', filters],
    queryFn: () => fetchTrendsChartData(filters),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useAICommentary = (filters: TrendsFilters) => {
  return useQuery({
    queryKey: ['trends', 'commentary', filters],
    queryFn: () => fetchAICommentary(filters),
    staleTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
  });
};
```

### Export Service

```typescript
export class ExportService {
  static async exportToCSV(data: TrendsData): Promise<Blob> {
    const csvData = [
      ['Timestamp', 'Volume', 'Price', 'Region', 'Asset Type'],
      ...data.chartData.map(point => [
        point.timestamp,
        point.volume,
        point.price,
        point.region,
        point.assetType
      ])
    ];
    
    const csv = csvData.map(row => row.join(',')).join('\n');
    return new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  }

  static async exportToJSON(data: TrendsData): Promise<Blob> {
    const exportData = {
      exportedAt: new Date().toISOString(),
      filters: data.filters,
      metrics: data.metrics,
      chartData: data.chartData,
      forecasts: data.forecasts
    };
    
    return new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
  }

  static async exportToPDF(elementRef: React.RefObject<HTMLElement>): Promise<Blob> {
    const element = elementRef.current;
    if (!element) throw new Error('Element not found');

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true
    });

    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = 297; // A4 landscape width
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    return pdf.output('blob');
  }
}
```

## 🔧 API Implementation

### Supabase + Redis Caching

```typescript
export async function getTrendsMetrics(filters: TrendsFilters) {
  const cacheKey = `trends:metrics:${hashFilters(filters)}`;
  
  // Try Redis cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Query Supabase
  const { data, error } = await supabase
    .from('market_metrics')
    .select(`
      timestamp,
      total_volume,
      avg_price,
      avg_holding_duration,
      asset_type,
      region
    `)
    .gte('timestamp', filters.startDate)
    .lte('timestamp', filters.endDate)
    .in('asset_type', filters.assetTypes)
    .in('region', filters.regions);

  if (error) throw error;

  // Process and cache for 5 minutes
  const processed = processMetricsData(data);
  await redis.setex(cacheKey, 300, JSON.stringify(processed));
  
  return processed;
}
```

### Webhook Alert System

```typescript
// Alert configuration
export async function createVolumeAlert(userId: string, config: AlertConfig) {
  const { data, error } = await supabase
    .from('user_alerts')
    .insert({
      user_id: userId,
      alert_type: 'volume_spike',
      threshold: config.threshold,
      asset_types: config.assetTypes,
      regions: config.regions,
      webhook_url: `/api/webhooks/alerts/${userId}`,
      is_active: true
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Webhook endpoint
export async function POST(
  request: Request,
  { params }: { params: { userId: string } }
) {
  const { alertId, currentValue, threshold, assetType } = await request.json();
  
  // Validate webhook signature
  const signature = request.headers.get('x-webhook-signature');
  if (!validateWebhookSignature(signature, request.body)) {
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
  }

  // Send notification
  await sendNotification({
    userId: params.userId,
    title: 'Volume Alert Triggered',
    message: `${assetType} volume spike: $${currentValue}M exceeds threshold of $${threshold}M`,
    type: 'volume_alert',
    data: { alertId, currentValue, threshold }
  });

  return NextResponse.json({ success: true });
}
```

## ⚠️ Error Handling & Edge Cases

### Error Boundaries

```typescript
const TrendsErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ErrorBoundary
    FallbackComponent={({ error, resetErrorBoundary }) => (
      <div className="rounded-lg border border-red-200 bg-red-50 p-6 m-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
          <h3 className="text-lg font-medium text-red-800">
            Unable to load trends data
          </h3>
        </div>
        <p className="mt-2 text-sm text-red-600">
          {error.message || 'An unexpected error occurred'}
        </p>
        <div className="mt-4 flex space-x-3">
          <Button 
            onClick={resetErrorBoundary} 
            variant="outline" 
            size="sm"
          >
            Try Again
          </Button>
          <Button 
            onClick={() => window.location.reload()} 
            variant="ghost" 
            size="sm"
          >
            Refresh Page
          </Button>
        </div>
      </div>
    )}
    onError={(error, errorInfo) => {
      console.error('Trends page error:', error, errorInfo);
      // Send to monitoring service
      sendErrorToMonitoring(error, errorInfo);
    }}
  >
    {children}
  </ErrorBoundary>
);
```

### No Data States

```typescript
const EmptyState: React.FC<{ type: 'metrics' | 'chart' | 'heatmap' }> = ({ type }) => {
  const config = {
    metrics: {
      icon: BarChart3,
      title: 'No metrics available',
      description: 'No trading data found for the selected filters'
    },
    chart: {
      icon: LineChart,
      title: 'No chart data',
      description: 'Try expanding your date range or adjusting filters'
    },
    heatmap: {
      icon: Map,
      title: 'No geographic data',
      description: 'No trading activity found in the selected regions'
    }
  }[type];

  const Icon = config.icon;

  return (
    <div className="flex flex-col items-center justify-center h-64 text-gray-500">
      <Icon className="h-12 w-12 mb-4 text-gray-400" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {config.title}
      </h3>
      <p className="text-sm text-center max-w-sm">
        {config.description}
      </p>
      <Button 
        onClick={() => window.location.reload()} 
        variant="outline" 
        className="mt-4"
        size="sm"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Refresh Data
      </Button>
    </div>
  );
};
```

### Loading States

```typescript
const LoadingSkeleton: React.FC<{ type: 'metrics' | 'chart' | 'heatmap' }> = ({ type }) => {
  if (type === 'metrics') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="bg-white p-6 rounded-lg border">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'chart') {
    return (
      <div className="bg-white p-6 rounded-lg border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Heatmap skeleton
  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div className="h-96 bg-gray-200 rounded"></div>
      </div>
    </div>
  );
};
```

## 📱 Responsive Design

```typescript
const ResponsiveLayout: React.FC = ({ children }) => (
  <div className="min-h-screen bg-gray-50">
    {/* Mobile-first responsive grid */}
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters - sidebar on desktop, top on mobile */}
        <div className="lg:col-span-1 order-1 lg:order-1">
          <TrendsFilters />
        </div>
        
        {/* Main content */}
        <div className="lg:col-span-3 order-2 lg:order-2 space-y-6">
          {/* Metrics - stacked on mobile, grid on desktop */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <TrendsMetrics />
          </div>
          
          {/* Charts - stacked on mobile, side-by-side on desktop */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <TrendsCharts />
          </div>
          
          {/* Heatmap - full width */}
          <div className="col-span-full">
            <TrendsHeatmap />
          </div>
          
          {/* Forecasts - responsive grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <TrendsForecasts />
          </div>
        </div>
      </div>
    </div>
  </div>
);
```

## 📂 File Structure

```
/app_frontend/
├── src/
│   ├── app/
│   │   └── trends/
│   │       ├── page.tsx                 # Main trends page
│   │       └── loading.tsx              # Loading UI
│   ├── components/
│   │   └── trends/
│   │       ├── TrendsPage.tsx           # Main layout
│   │       ├── TrendsHeader.tsx         # Header with actions
│   │       ├── TrendsFilters.tsx        # Filter controls
│   │       ├── TrendsMetrics.tsx        # Metrics cards
│   │       ├── TrendsCharts.tsx         # Chart components
│   │       ├── TrendsHeatmap.tsx        # Geographic heatmap
│   │       ├── TrendsForecasts.tsx      # AI predictions
│   │       ├── TrendsActions.tsx        # Export/alert actions
│   │       └── index.ts                 # Barrel exports
│   ├── hooks/
│   │   └── api/
│   │       ├── use-trends.ts            # Data fetching hooks
│   │       └── use-trends-exports.ts    # Export functionality
│   ├── lib/
│   │   ├── trends/
│   │   │   ├── export-service.ts        # Export utilities
│   │   │   ├── chart-utils.ts           # Chart helpers
│   │   │   └── data-processing.ts       # Data transformations
│   │   └── api/
│   │       └── trends.ts                # API client functions
│   └── types/
│       └── trends.ts                    # TypeScript definitions
├── TASK.md                              # This task documentation
└── README.md                            # Project documentation
```

## 🚀 Implementation Priority

### Phase 1: Core Foundation (High Priority)
1. Create basic page structure and routing
2. Implement data fetching hooks
3. Build metrics display cards
4. Add filter controls

### Phase 2: Visualizations (High Priority)
1. Implement volume and price charts
2. Create geographic heatmap
3. Add interactive features (zoom, hover)
4. Implement responsive design

### Phase 3: Advanced Features (Medium Priority)
1. Add AI forecasts and commentary
2. Implement sentiment gauge
3. Create export functionality
4. Add alert system

### Phase 4: Polish & Optimization (Low Priority)
1. Comprehensive error handling
2. Performance optimization
3. Accessibility improvements
4. Analytics and monitoring

## 📊 Success Metrics

- **Performance**: Page loads in < 2 seconds
- **Usability**: Intuitive filter and export workflows
- **Reliability**: < 1% error rate on data fetching
- **Responsiveness**: Works seamlessly on mobile and desktop
- **Real-time**: Data updates within 30 seconds of changes

---

**Ready to implement a world-class trends dashboard for Bridges Market! 🚀**