// Progressive Web App utilities

export interface PWAPromptEvent extends Event {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>
}

export interface PWAInstallState {
  isInstallable: boolean
  isInstalled: boolean
  canInstall: boolean
  showPrompt: boolean
  installPrompt: PWAPromptEvent | null
}

export class PWAManager {
  private static instance: PWAManager
  private installPrompt: PWAPromptEvent | null = null
  private isInstalled = false
  private callbacks: Array<(state: PWAInstallState) => void> = []

  private constructor() {
    this.init()
  }

  static getInstance(): PWAManager {
    if (!PWAManager.instance) {
      PWAManager.instance = new PWAManager()
    }
    return PWAManager.instance
  }

  private init() {
    if (typeof window === 'undefined') return

    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      this.installPrompt = e as PWAPromptEvent
      this.notifyStateChange()
    })

    // Listen for appinstalled event
    window.addEventListener('appinstalled', () => {
      this.isInstalled = true
      this.installPrompt = null
      this.notifyStateChange()
    })

    // Check if app is already installed
    this.checkInstallationStatus()
  }

  private checkInstallationStatus() {
    // Check if running in standalone mode (installed)
    if (window.matchMedia('(display-mode: standalone)').matches) {
      this.isInstalled = true
    }

    // Check if running as PWA on iOS
    if ((navigator as any).standalone === true) {
      this.isInstalled = true
    }

    this.notifyStateChange()
  }

  private notifyStateChange() {
    const state = this.getState()
    this.callbacks.forEach(callback => callback(state))
  }

  getState(): PWAInstallState {
    return {
      isInstallable: !!this.installPrompt,
      isInstalled: this.isInstalled,
      canInstall: !!this.installPrompt && !this.isInstalled,
      showPrompt: !!this.installPrompt && !this.isInstalled,
      installPrompt: this.installPrompt
    }
  }

  async install(): Promise<{ outcome: 'accepted' | 'dismissed'; platform: string } | null> {
    if (!this.installPrompt) {
      return null
    }

    try {
      await this.installPrompt.prompt()
      const choiceResult = await this.installPrompt.userChoice

      if (choiceResult.outcome === 'accepted') {
        this.isInstalled = true
        this.installPrompt = null
        this.notifyStateChange()
      }

      return choiceResult
    } catch (error) {
      console.error('Error during PWA installation:', error)
      return null
    }
  }

  onStateChange(callback: (state: PWAInstallState) => void) {
    this.callbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      this.callbacks = this.callbacks.filter(cb => cb !== callback)
    }
  }

  // iOS specific installation instructions
  getIOSInstallInstructions(): string[] {
    const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && 
                       /Safari/.test(navigator.userAgent) && 
                       !(window as any).MSStream

    if (!isIOSSafari) return []

    return [
      'Tap the Share button',
      'Scroll down and tap "Add to Home Screen"',
      'Tap "Add" to install the app'
    ]
  }
}

// Service Worker management
export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager
  private registration: ServiceWorkerRegistration | null = null
  private updateAvailable = false
  private callbacks: Array<(hasUpdate: boolean) => void> = []

  private constructor() {
    this.init()
  }

  static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager()
    }
    return ServiceWorkerManager.instance
  }

  private async init() {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      // Check for updates
      this.registration.addEventListener('updatefound', () => {
        const newWorker = this.registration!.installing

        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.updateAvailable = true
              this.notifyUpdateAvailable()
            }
          })
        }
      })

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'SW_UPDATE_AVAILABLE') {
          this.updateAvailable = true
          this.notifyUpdateAvailable()
        }
      })

      // Check for immediate update
      await this.checkForUpdate()

    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }

  private notifyUpdateAvailable() {
    this.callbacks.forEach(callback => callback(this.updateAvailable))
  }

  async checkForUpdate(): Promise<boolean> {
    if (!this.registration) return false

    try {
      await this.registration.update()
      return this.updateAvailable
    } catch (error) {
      console.error('Service Worker update check failed:', error)
      return false
    }
  }

  async applyUpdate(): Promise<void> {
    if (!this.registration || !this.updateAvailable) return

    const newWorker = this.registration.waiting || this.registration.installing

    if (newWorker) {
      newWorker.postMessage({ type: 'SKIP_WAITING' })
      
      // Reload page after service worker takes control
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload()
      })
    }
  }

  onUpdateAvailable(callback: (hasUpdate: boolean) => void) {
    this.callbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      this.callbacks = this.callbacks.filter(cb => cb !== callback)
    }
  }

  hasUpdateAvailable(): boolean {
    return this.updateAvailable
  }
}

// Offline status management
export class OfflineManager {
  private static instance: OfflineManager
  private isOnline = navigator.onLine
  private callbacks: Array<(isOnline: boolean) => void> = []

  private constructor() {
    this.init()
  }

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager()
    }
    return OfflineManager.instance
  }

  private init() {
    if (typeof window === 'undefined') return

    window.addEventListener('online', () => {
      this.isOnline = true
      this.notifyStatusChange()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.notifyStatusChange()
    })
  }

  private notifyStatusChange() {
    this.callbacks.forEach(callback => callback(this.isOnline))
  }

  getStatus(): boolean {
    return this.isOnline
  }

  onStatusChange(callback: (isOnline: boolean) => void) {
    this.callbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      this.callbacks = this.callbacks.filter(cb => cb !== callback)
    }
  }
}

// Push notification management
export class NotificationManager {
  private static instance: NotificationManager

  private constructor() {}

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager()
    }
    return NotificationManager.instance
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied'
    }

    if (Notification.permission === 'default') {
      return await Notification.requestPermission()
    }

    return Notification.permission
  }

  async showNotification(
    title: string,
    options: NotificationOptions = {}
  ): Promise<void> {
    const permission = await this.requestPermission()
    
    if (permission !== 'granted') {
      return
    }

    const defaultOptions: NotificationOptions = {
      badge: '/icons/icon-96x96.png',
      icon: '/icons/icon-192x192.png',
      vibrate: [200, 100, 200],
      requireInteraction: false,
      ...options
    }

    // If service worker is available, use it for notifications
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'SHOW_NOTIFICATION',
        title,
        options: defaultOptions
      })
    } else {
      new Notification(title, defaultOptions)
    }
  }

  async subscribeToPush(): Promise<PushSubscription | null> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      return null
    }

    try {
      const registration = await navigator.serviceWorker.ready
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || ''
        )
      })

      return subscription
    } catch (error) {
      console.error('Push subscription failed:', error)
      return null
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    
    return outputArray
  }
}

// Background sync management
export class BackgroundSyncManager {
  private static instance: BackgroundSyncManager
  private pendingActions: Array<{
    id: string
    action: string
    data: any
    timestamp: number
  }> = []

  private constructor() {
    this.loadPendingActions()
  }

  static getInstance(): BackgroundSyncManager {
    if (!BackgroundSyncManager.instance) {
      BackgroundSyncManager.instance = new BackgroundSyncManager()
    }
    return BackgroundSyncManager.instance
  }

  private loadPendingActions() {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('pwa_pending_actions')
      if (stored) {
        this.pendingActions = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Failed to load pending actions:', error)
    }
  }

  private savePendingActions() {
    try {
      localStorage.setItem('pwa_pending_actions', JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Failed to save pending actions:', error)
    }
  }

  addPendingAction(action: string, data: any): string {
    const id = `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    this.pendingActions.push({
      id,
      action,
      data,
      timestamp: Date.now()
    })

    this.savePendingActions()

    // Try to register background sync if available
    this.registerBackgroundSync(action)

    return id
  }

  private async registerBackgroundSync(tag: string) {
    if (!('serviceWorker' in navigator) || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      return
    }

    try {
      const registration = await navigator.serviceWorker.ready
      await registration.sync.register(tag)
    } catch (error) {
      console.error('Background sync registration failed:', error)
    }
  }

  removePendingAction(id: string) {
    this.pendingActions = this.pendingActions.filter(action => action.id !== id)
    this.savePendingActions()
  }

  getPendingActions(): Array<{
    id: string
    action: string
    data: any
    timestamp: number
  }> {
    return [...this.pendingActions]
  }

  async processPendingActions() {
    const actions = [...this.pendingActions]
    
    for (const action of actions) {
      try {
        // Process the action (implement based on your needs)
        await this.processAction(action)
        this.removePendingAction(action.id)
      } catch (error) {
        console.error('Failed to process pending action:', action, error)
      }
    }
  }

  private async processAction(action: {
    id: string
    action: string
    data: any
    timestamp: number
  }) {
    // Implement action processing logic based on action type
    switch (action.action) {
      case 'sync_book_data':
        // Sync book data to server
        break
      case 'upload_analytics':
        // Upload analytics data
        break
      default:
        console.warn('Unknown action type:', action.action)
    }
  }
}

// Main PWA hook for React components
export function usePWA() {
  const [installState, setInstallState] = React.useState<PWAInstallState>({
    isInstallable: false,
    isInstalled: false,
    canInstall: false,
    showPrompt: false,
    installPrompt: null
  })

  const [hasUpdate, setHasUpdate] = React.useState(false)
  const [isOnline, setIsOnline] = React.useState(true)

  React.useEffect(() => {
    const pwaManager = PWAManager.getInstance()
    const swManager = ServiceWorkerManager.getInstance()
    const offlineManager = OfflineManager.getInstance()

    // Set initial states
    setInstallState(pwaManager.getState())
    setHasUpdate(swManager.hasUpdateAvailable())
    setIsOnline(offlineManager.getStatus())

    // Subscribe to state changes
    const unsubscribePWA = pwaManager.onStateChange(setInstallState)
    const unsubscribeSW = swManager.onUpdateAvailable(setHasUpdate)
    const unsubscribeOffline = offlineManager.onStatusChange(setIsOnline)

    return () => {
      unsubscribePWA()
      unsubscribeSW()
      unsubscribeOffline()
    }
  }, [])

  const installApp = async () => {
    const pwaManager = PWAManager.getInstance()
    return await pwaManager.install()
  }

  const updateApp = async () => {
    const swManager = ServiceWorkerManager.getInstance()
    await swManager.applyUpdate()
  }

  const showNotification = async (title: string, options?: NotificationOptions) => {
    const notificationManager = NotificationManager.getInstance()
    await notificationManager.showNotification(title, options)
  }

  return {
    // Installation state
    ...installState,
    installApp,
    
    // Update state
    hasUpdate,
    updateApp,
    
    // Offline state
    isOnline,
    
    // Notifications
    showNotification,
    
    // Utility functions
    getIOSInstructions: () => PWAManager.getInstance().getIOSInstallInstructions()
  }
}

// Utility to import React for the hook
import React from 'react'

export default {
  PWAManager,
  ServiceWorkerManager,
  OfflineManager,
  NotificationManager,
  BackgroundSyncManager,
  usePWA
}