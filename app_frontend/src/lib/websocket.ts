import React from 'react'
import { queryClient } from './react-query'
import { queryKeys } from './react-query'
import { toast } from 'react-hot-toast'

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

export interface BookGenerationUpdate {
  bookId: string
  status: 'generating' | 'completed' | 'failed'
  progress: number
  currentStep: string
  error?: string
}

export interface SalesUpdate {
  bookId: string
  salesCount: number
  revenue: number
  timestamp: string
}

export interface RealTimeAnalytics {
  activeUsers: number
  currentSales: number
  todayRevenue: number
  popularBooks: Array<{
    id: string
    title: string
    currentViews: number
  }>
}

class WebSocketClient {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnected = false
  private heartbeatInterval: NodeJS.Timeout | null = null
  private subscriptions = new Set<string>()

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws'
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
      
      this.ws = new WebSocket(`${wsUrl}?token=${token}`)

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.isConnected = true
        this.reconnectAttempts = 0
        this.startHeartbeat()
        resolve()
      }

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        this.isConnected = false
        this.stopHeartbeat()
        
        // Reconnect if not a clean close
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect()
        }
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        reject(error)
      }
    })
  }

  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'book_generation_update':
        this.handleBookGenerationUpdate(message.data as BookGenerationUpdate)
        break
      
      case 'sales_update':
        this.handleSalesUpdate(message.data as SalesUpdate)
        break
      
      case 'realtime_analytics':
        this.handleRealTimeAnalytics(message.data as RealTimeAnalytics)
        break
      
      case 'error':
        this.handleError(message.data)
        break
      
      default:
        console.log('Unknown WebSocket message type:', message.type)
    }
  }

  private handleBookGenerationUpdate(update: BookGenerationUpdate) {
    // Update book status in cache
    queryClient.setQueryData(
      queryKeys.books.detail(update.bookId),
      (old: any) => {
        if (!old) return old
        return {
          ...old,
          status: update.status,
          generation_progress: update.progress,
          current_step: update.currentStep,
        }
      }
    )

    // Update in lists as well
    queryClient.setQueriesData(
      { queryKey: queryKeys.books.lists() },
      (old: any) => {
        if (!old?.books) return old
        return {
          ...old,
          books: old.books.map((book: any) =>
            book.id === update.bookId
              ? {
                  ...book,
                  status: update.status,
                  generation_progress: update.progress,
                  current_step: update.currentStep,
                }
              : book
          ),
        }
      }
    )

    // Show notifications
    if (update.status === 'completed') {
      toast.success('Book generation completed!')
    } else if (update.status === 'failed') {
      toast.error(update.error || 'Book generation failed')
    }
  }

  private handleSalesUpdate(update: SalesUpdate) {
    // Update book sales data
    queryClient.setQueryData(
      queryKeys.books.detail(update.bookId),
      (old: any) => {
        if (!old) return old
        return {
          ...old,
          sales: update.salesCount,
          revenue: update.revenue,
        }
      }
    )

    // Invalidate analytics to refresh charts
    queryClient.invalidateQueries({
      queryKey: queryKeys.analytics.all(),
    })

    // Show success notification
    toast.success('New sale recorded!')
  }

  private handleRealTimeAnalytics(analytics: RealTimeAnalytics) {
    // Update real-time analytics cache
    queryClient.setQueryData(
      [...queryKeys.analytics.all(), 'realtime'],
      analytics
    )
  }

  private handleError(error: any) {
    console.error('WebSocket error:', error)
    toast.error(error.message || 'Real-time connection error')
  }

  private scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting to reconnect in ${delay}ms... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect().catch(console.error)
    }, delay)
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // Ping every 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  subscribe(channel: string) {
    this.subscriptions.add(channel)
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'subscribe',
        channel,
      }))
    }
  }

  unsubscribe(channel: string) {
    this.subscriptions.delete(channel)
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'unsubscribe',
        channel,
      }))
    }
  }

  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    this.isConnected = false
    this.subscriptions.clear()
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions),
    }
  }
}

export const wsClient = new WebSocketClient()

// React hook for WebSocket connection
export function useWebSocket() {
  const connect = () => wsClient.connect()
  const disconnect = () => wsClient.disconnect()
  const subscribe = (channel: string) => wsClient.subscribe(channel)
  const unsubscribe = (channel: string) => wsClient.unsubscribe(channel)
  const status = wsClient.getConnectionStatus()

  return {
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    status,
  }
}

// Hook for book generation updates
export function useBookGenerationUpdates(bookId: string) {
  const { subscribe, unsubscribe } = useWebSocket()

  React.useEffect(() => {
    if (bookId) {
      const channel = `book_generation:${bookId}`
      subscribe(channel)
      return () => unsubscribe(channel)
    }
  }, [bookId, subscribe, unsubscribe])
}

// Hook for real-time analytics
export function useRealTimeUpdates(enabled = true) {
  const { subscribe, unsubscribe } = useWebSocket()

  React.useEffect(() => {
    if (enabled) {
      subscribe('realtime_analytics')
      subscribe('sales_updates')
      return () => {
        unsubscribe('realtime_analytics')
        unsubscribe('sales_updates')
      }
    }
  }, [enabled, subscribe, unsubscribe])
}

export default wsClient