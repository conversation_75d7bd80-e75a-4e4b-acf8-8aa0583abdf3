import { useEffect, useRef, useCallback, useState } from 'react'

// Accessibility utilities and hooks
export interface AccessibilityOptions {
  announcePageChanges?: boolean
  focusManagement?: boolean
  keyboardNavigation?: boolean
  colorContrastChecking?: boolean
  motionReduction?: boolean
}

// ARIA live region announcer
class LiveAnnouncer {
  private politeElement: HTMLElement | null = null
  private assertiveElement: HTMLElement | null = null

  constructor() {
    this.createLiveRegions()
  }

  private createLiveRegions() {
    if (typeof document === 'undefined') return

    // Polite announcements (non-interrupting)
    this.politeElement = document.createElement('div')
    this.politeElement.setAttribute('aria-live', 'polite')
    this.politeElement.setAttribute('aria-atomic', 'true')
    this.politeElement.className = 'sr-only'
    this.politeElement.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `
    document.body.appendChild(this.politeElement)

    // Assertive announcements (interrupting)
    this.assertiveElement = document.createElement('div')
    this.assertiveElement.setAttribute('aria-live', 'assertive')
    this.assertiveElement.setAttribute('aria-atomic', 'true')
    this.assertiveElement.className = 'sr-only'
    this.assertiveElement.style.cssText = this.politeElement.style.cssText
    document.body.appendChild(this.assertiveElement)
  }

  announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const element = priority === 'assertive' ? this.assertiveElement : this.politeElement
    
    if (element) {
      // Clear and then set the message to ensure it's announced
      element.textContent = ''
      setTimeout(() => {
        element.textContent = message
      }, 100)
    }
  }

  clear() {
    if (this.politeElement) this.politeElement.textContent = ''
    if (this.assertiveElement) this.assertiveElement.textContent = ''
  }

  destroy() {
    if (this.politeElement) {
      document.body.removeChild(this.politeElement)
      this.politeElement = null
    }
    if (this.assertiveElement) {
      document.body.removeChild(this.assertiveElement)
      this.assertiveElement = null
    }
  }
}

export const liveAnnouncer = new LiveAnnouncer()

// Focus management utilities
export class FocusManager {
  private focusHistory: HTMLElement[] = []
  private trapStack: HTMLElement[] = []

  // Store current focus for restoration
  storeFocus() {
    const activeElement = document.activeElement as HTMLElement
    if (activeElement && activeElement !== document.body) {
      this.focusHistory.push(activeElement)
    }
  }

  // Restore previously stored focus
  restoreFocus() {
    const element = this.focusHistory.pop()
    if (element && element.isConnected) {
      element.focus()
      return true
    }
    return false
  }

  // Focus first interactive element in container
  focusFirst(container: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
      return true
    }
    return false
  }

  // Focus last interactive element in container
  focusLast(container: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus()
      return true
    }
    return false
  }

  // Get all focusable elements in container
  getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ')

    const elements = Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[]
    
    return elements.filter(element => {
      // Check if element is visible and not hidden
      const style = window.getComputedStyle(element)
      return style.display !== 'none' && 
             style.visibility !== 'hidden' && 
             element.offsetParent !== null
    })
  }

  // Trap focus within container
  trapFocus(container: HTMLElement) {
    this.trapStack.push(container)
    
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      const focusableElements = this.getFocusableElements(container)
      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement?.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    container.setAttribute('data-focus-trap', 'true')

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
      container.removeAttribute('data-focus-trap')
      const index = this.trapStack.indexOf(container)
      if (index > -1) {
        this.trapStack.splice(index, 1)
      }
    }
  }
}

export const focusManager = new FocusManager()

// Keyboard navigation utilities
export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  handler: (event: KeyboardEvent) => void
  description: string
}

export class KeyboardManager {
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private isListening = false

  constructor() {
    this.startListening()
  }

  private getShortcutKey(shortcut: Omit<KeyboardShortcut, 'handler' | 'description'>): string {
    const modifiers = []
    if (shortcut.ctrlKey) modifiers.push('ctrl')
    if (shortcut.altKey) modifiers.push('alt')
    if (shortcut.shiftKey) modifiers.push('shift')
    if (shortcut.metaKey) modifiers.push('meta')
    return [...modifiers, shortcut.key.toLowerCase()].join('+')
  }

  private handleKeyDown = (event: KeyboardEvent) => {
    const key = this.getShortcutKey({
      key: event.key,
      ctrlKey: event.ctrlKey,
      altKey: event.altKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey,
    })

    const shortcut = this.shortcuts.get(key)
    if (shortcut) {
      event.preventDefault()
      shortcut.handler(event)
    }
  }

  addShortcut(shortcut: KeyboardShortcut) {
    const key = this.getShortcutKey(shortcut)
    this.shortcuts.set(key, shortcut)
  }

  removeShortcut(key: string | Omit<KeyboardShortcut, 'handler' | 'description'>) {
    const shortcutKey = typeof key === 'string' ? key : this.getShortcutKey(key)
    this.shortcuts.delete(shortcutKey)
  }

  getAllShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  startListening() {
    if (!this.isListening && typeof window !== 'undefined') {
      window.addEventListener('keydown', this.handleKeyDown)
      this.isListening = true
    }
  }

  stopListening() {
    if (this.isListening && typeof window !== 'undefined') {
      window.removeEventListener('keydown', this.handleKeyDown)
      this.isListening = false
    }
  }

  destroy() {
    this.stopListening()
    this.shortcuts.clear()
  }
}

export const keyboardManager = new KeyboardManager()

// React hooks for accessibility
export function useAnnouncements() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    liveAnnouncer.announce(message, priority)
  }, [])

  const clear = useCallback(() => {
    liveAnnouncer.clear()
  }, [])

  return { announce, clear }
}

export function useFocusManagement() {
  const storeFocus = useCallback(() => focusManager.storeFocus(), [])
  const restoreFocus = useCallback(() => focusManager.restoreFocus(), [])
  const focusFirst = useCallback((container: HTMLElement) => focusManager.focusFirst(container), [])
  const focusLast = useCallback((container: HTMLElement) => focusManager.focusLast(container), [])

  return { storeFocus, restoreFocus, focusFirst, focusLast }
}

export function useFocusTrap(enabled: boolean = true) {
  const containerRef = useRef<HTMLElement>(null)
  const cleanupRef = useRef<(() => void) | null>(null)

  useEffect(() => {
    if (enabled && containerRef.current) {
      cleanupRef.current = focusManager.trapFocus(containerRef.current)
    }

    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
        cleanupRef.current = null
      }
    }
  }, [enabled])

  return containerRef
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  useEffect(() => {
    shortcuts.forEach(shortcut => keyboardManager.addShortcut(shortcut))

    return () => {
      shortcuts.forEach(shortcut => {
        const key = keyboardManager['getShortcutKey'](shortcut)
        keyboardManager.removeShortcut(key)
      })
    }
  }, [shortcuts])
}

export function useSkipLink() {
  const skipLinkRef = useRef<HTMLAnchorElement>(null)

  const handleSkipToContent = useCallback(() => {
    const mainContent = document.querySelector('main, #main-content, [role="main"]') as HTMLElement
    if (mainContent) {
      mainContent.focus()
      mainContent.scrollIntoView()
    }
  }, [])

  return { skipLinkRef, handleSkipToContent }
}

// Reduced motion preference
export function usePrefersReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handler = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    mediaQuery.addEventListener('change', handler)
    return () => mediaQuery.removeEventListener('change', handler)
  }, [])

  return prefersReducedMotion
}

// High contrast preference
export function usePrefersHighContrast(): boolean {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setPrefersHighContrast(mediaQuery.matches)

    const handler = (event: MediaQueryListEvent) => {
      setPrefersHighContrast(event.matches)
    }

    mediaQuery.addEventListener('change', handler)
    return () => mediaQuery.removeEventListener('change', handler)
  }, [])

  return prefersHighContrast
}

// ARIA attributes helper
export function getAriaAttributes(props: {
  label?: string
  labelledBy?: string
  describedBy?: string
  expanded?: boolean
  selected?: boolean
  checked?: boolean
  disabled?: boolean
  required?: boolean
  invalid?: boolean
  hidden?: boolean
  live?: 'polite' | 'assertive' | 'off'
  role?: string
}) {
  const aria: Record<string, any> = {}

  if (props.label) aria['aria-label'] = props.label
  if (props.labelledBy) aria['aria-labelledby'] = props.labelledBy
  if (props.describedBy) aria['aria-describedby'] = props.describedBy
  if (props.expanded !== undefined) aria['aria-expanded'] = props.expanded
  if (props.selected !== undefined) aria['aria-selected'] = props.selected
  if (props.checked !== undefined) aria['aria-checked'] = props.checked
  if (props.disabled !== undefined) aria['aria-disabled'] = props.disabled
  if (props.required !== undefined) aria['aria-required'] = props.required
  if (props.invalid !== undefined) aria['aria-invalid'] = props.invalid
  if (props.hidden !== undefined) aria['aria-hidden'] = props.hidden
  if (props.live) aria['aria-live'] = props.live
  if (props.role) aria['role'] = props.role

  return aria
}

// Initialize global accessibility features
export function initializeAccessibility(options: AccessibilityOptions = {}) {
  // Add skip link if not present
  if (typeof document !== 'undefined') {
    let skipLink = document.querySelector('#skip-link') as HTMLAnchorElement
    if (!skipLink) {
      skipLink = document.createElement('a')
      skipLink.id = 'skip-link'
      skipLink.href = '#main-content'
      skipLink.textContent = 'Skip to main content'
      skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded'
      
      skipLink.addEventListener('click', (e) => {
        e.preventDefault()
        const main = document.querySelector('main, #main-content, [role="main"]') as HTMLElement
        if (main) {
          main.focus()
          main.scrollIntoView()
        }
      })
      
      document.body.insertBefore(skipLink, document.body.firstChild)
    }
  }

  // Set up default keyboard shortcuts
  if (options.keyboardNavigation !== false) {
    keyboardManager.addShortcut({
      key: '/',
      handler: (e) => {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement
        if (searchInput) {
          e.preventDefault()
          searchInput.focus()
        }
      },
      description: 'Focus search input',
    })

    keyboardManager.addShortcut({
      key: 'Escape',
      handler: () => {
        // Close any open modals or dropdowns
        const modal = document.querySelector('[role="dialog"][aria-hidden="false"]') as HTMLElement
        if (modal) {
          const closeButton = modal.querySelector('[aria-label*="close" i], .close, [data-close]') as HTMLElement
          closeButton?.click()
        }
      },
      description: 'Close modal or dropdown',
    })
  }

  // Announce page changes for SPAs
  if (options.announcePageChanges !== false) {
    let lastUrl = location.href
    const observer = new MutationObserver(() => {
      if (location.href !== lastUrl) {
        lastUrl = location.href
        const pageTitle = document.title
        liveAnnouncer.announce(`Navigated to ${pageTitle}`, 'polite')
      }
    })
    observer.observe(document.body, { childList: true, subtree: true })
  }

  console.log('Accessibility features initialized')
}