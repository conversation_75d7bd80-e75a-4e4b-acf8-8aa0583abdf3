import { create } from 'zustand'
import { createJSONStorage, persist, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// Theme State
interface ThemeState {
  theme: 'light' | 'dark' | 'system'
  setTheme: (theme: 'light' | 'dark' | 'system') => void
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      theme: 'system',
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: 'publish-ai-theme',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

// UI State
interface UIState {
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void
  
  activeModal: string | null
  setActiveModal: (modal: string | null) => void
  
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    timestamp: number
    read: boolean
  }>
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void
  markNotificationRead: (id: string) => void
  removeNotification: (id: string) => void
  clearAllNotifications: () => void
  
  recentSearches: string[]
  addRecentSearch: (search: string) => void
  clearRecentSearches: () => void
}

export const useUIStore = create<UIState>()(
  persist(
    subscribeWithSelector(
      immer((set, get) => ({
        sidebarCollapsed: false,
        setSidebarCollapsed: (collapsed) => set((state) => {
          state.sidebarCollapsed = collapsed
        }),
        
        activeModal: null,
        setActiveModal: (modal) => set((state) => {
          state.activeModal = modal
        }),
        
        notifications: [],
        addNotification: (notification) => set((state) => {
          state.notifications.unshift({
            ...notification,
            id: crypto.randomUUID(),
            timestamp: Date.now(),
            read: false,
          })
          
          // Keep only last 50 notifications
          if (state.notifications.length > 50) {
            state.notifications = state.notifications.slice(0, 50)
          }
        }),
        markNotificationRead: (id) => set((state) => {
          const notification = state.notifications.find(n => n.id === id)
          if (notification) {
            notification.read = true
          }
        }),
        removeNotification: (id) => set((state) => {
          state.notifications = state.notifications.filter(n => n.id !== id)
        }),
        clearAllNotifications: () => set((state) => {
          state.notifications = []
        }),
        
        recentSearches: [],
        addRecentSearch: (search) => set((state) => {
          // Remove if already exists
          state.recentSearches = state.recentSearches.filter(s => s !== search)
          // Add to beginning
          state.recentSearches.unshift(search)
          // Keep only last 10
          if (state.recentSearches.length > 10) {
            state.recentSearches = state.recentSearches.slice(0, 10)
          }
        }),
        clearRecentSearches: () => set((state) => {
          state.recentSearches = []
        }),
      }))
    ),
    {
      name: 'publish-ai-ui',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        recentSearches: state.recentSearches,
        // Don't persist notifications and activeModal
      }),
    }
  )
)

// Form State Management
interface FormState {
  drafts: Record<string, any>
  saveDraft: (formId: string, data: any) => void
  getDraft: (formId: string) => any
  removeDraft: (formId: string) => void
  clearAllDrafts: () => void
  
  validationErrors: Record<string, Record<string, string[]>>
  setValidationErrors: (formId: string, errors: Record<string, string[]>) => void
  clearValidationErrors: (formId: string) => void
}

export const useFormStore = create<FormState>()(
  persist(
    immer((set, get) => ({
      drafts: {},
      saveDraft: (formId, data) => set((state) => {
        state.drafts[formId] = {
          ...data,
          _timestamp: Date.now(),
        }
      }),
      getDraft: (formId) => {
        const draft = get().drafts[formId]
        if (!draft) return null
        
        // Check if draft is older than 24 hours
        const dayAgo = Date.now() - 24 * 60 * 60 * 1000
        if (draft._timestamp < dayAgo) {
          get().removeDraft(formId)
          return null
        }
        
        const { _timestamp, ...data } = draft
        return data
      },
      removeDraft: (formId) => set((state) => {
        delete state.drafts[formId]
      }),
      clearAllDrafts: () => set((state) => {
        state.drafts = {}
      }),
      
      validationErrors: {},
      setValidationErrors: (formId, errors) => set((state) => {
        state.validationErrors[formId] = errors
      }),
      clearValidationErrors: (formId) => set((state) => {
        delete state.validationErrors[formId]
      }),
    })),
    {
      name: 'publish-ai-forms',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        drafts: state.drafts,
        // Don't persist validation errors
      }),
    }
  )
)

// Publishing Workflow State
interface WorkflowState {
  currentStep: number
  totalSteps: number
  stepData: Record<number, any>
  isComplete: boolean
  
  setCurrentStep: (step: number) => void
  setStepData: (step: number, data: any) => void
  nextStep: () => void
  previousStep: () => void
  resetWorkflow: () => void
  completeWorkflow: () => void
}

export const useWorkflowStore = create<WorkflowState>()(
  immer((set, get) => ({
    currentStep: 1,
    totalSteps: 6,
    stepData: {},
    isComplete: false,
    
    setCurrentStep: (step) => set((state) => {
      state.currentStep = Math.max(1, Math.min(step, state.totalSteps))
    }),
    setStepData: (step, data) => set((state) => {
      state.stepData[step] = { ...state.stepData[step], ...data }
    }),
    nextStep: () => set((state) => {
      if (state.currentStep < state.totalSteps) {
        state.currentStep += 1
      }
    }),
    previousStep: () => set((state) => {
      if (state.currentStep > 1) {
        state.currentStep -= 1
      }
    }),
    resetWorkflow: () => set((state) => {
      state.currentStep = 1
      state.stepData = {}
      state.isComplete = false
    }),
    completeWorkflow: () => set((state) => {
      state.isComplete = true
    }),
  }))
)

// Analytics State
interface AnalyticsState {
  selectedDateRange: {
    from: string
    to: string
  }
  setDateRange: (range: { from: string; to: string }) => void
  
  activeTab: string
  setActiveTab: (tab: string) => void
  
  chartType: 'line' | 'bar' | 'area'
  setChartType: (type: 'line' | 'bar' | 'area') => void
  
  filters: Record<string, any>
  setFilter: (key: string, value: any) => void
  clearFilters: () => void
}

export const useAnalyticsStore = create<AnalyticsState>()(
  persist(
    immer((set) => ({
      selectedDateRange: {
        from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
        to: new Date().toISOString().split('T')[0], // today
      },
      setDateRange: (range) => set((state) => {
        state.selectedDateRange = range
      }),
      
      activeTab: 'overview',
      setActiveTab: (tab) => set((state) => {
        state.activeTab = tab
      }),
      
      chartType: 'line',
      setChartType: (type) => set((state) => {
        state.chartType = type
      }),
      
      filters: {},
      setFilter: (key, value) => set((state) => {
        state.filters[key] = value
      }),
      clearFilters: () => set((state) => {
        state.filters = {}
      }),
    })),
    {
      name: 'publish-ai-analytics',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

// Book Management State
interface BookState {
  selectedBooks: string[]
  setSelectedBooks: (books: string[]) => void
  toggleBookSelection: (bookId: string) => void
  clearSelection: () => void
  
  viewMode: 'grid' | 'list'
  setViewMode: (mode: 'grid' | 'list') => void
  
  sortBy: string
  sortOrder: 'asc' | 'desc'
  setSorting: (sortBy: string, order: 'asc' | 'desc') => void
  
  filters: Record<string, any>
  setFilter: (key: string, value: any) => void
  clearFilters: () => void
}

export const useBookStore = create<BookState>()(
  persist(
    immer((set, get) => ({
      selectedBooks: [],
      setSelectedBooks: (books) => set((state) => {
        state.selectedBooks = books
      }),
      toggleBookSelection: (bookId) => set((state) => {
        const index = state.selectedBooks.indexOf(bookId)
        if (index >= 0) {
          state.selectedBooks.splice(index, 1)
        } else {
          state.selectedBooks.push(bookId)
        }
      }),
      clearSelection: () => set((state) => {
        state.selectedBooks = []
      }),
      
      viewMode: 'grid',
      setViewMode: (mode) => set((state) => {
        state.viewMode = mode
      }),
      
      sortBy: 'created_at',
      sortOrder: 'desc',
      setSorting: (sortBy, order) => set((state) => {
        state.sortBy = sortBy
        state.sortOrder = order
      }),
      
      filters: {},
      setFilter: (key, value) => set((state) => {
        if (value === null || value === undefined || value === '') {
          delete state.filters[key]
        } else {
          state.filters[key] = value
        }
      }),
      clearFilters: () => set((state) => {
        state.filters = {}
      }),
    })),
    {
      name: 'publish-ai-books',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        viewMode: state.viewMode,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        filters: state.filters,
        // Don't persist selectedBooks
      }),
    }
  )
)

// Offline State Management
interface OfflineState {
  isOnline: boolean
  setOnlineStatus: (online: boolean) => void
  
  pendingActions: Array<{
    id: string
    type: string
    data: any
    timestamp: number
    retryCount: number
  }>
  addPendingAction: (action: Omit<OfflineState['pendingActions'][0], 'id' | 'timestamp' | 'retryCount'>) => void
  removePendingAction: (id: string) => void
  incrementRetryCount: (id: string) => void
  clearPendingActions: () => void
  
  offlineData: Record<string, any>
  setOfflineData: (key: string, data: any) => void
  getOfflineData: (key: string) => any
  removeOfflineData: (key: string) => void
}

export const useOfflineStore = create<OfflineState>()(
  persist(
    immer((set, get) => ({
      isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
      setOnlineStatus: (online) => set((state) => {
        state.isOnline = online
      }),
      
      pendingActions: [],
      addPendingAction: (action) => set((state) => {
        state.pendingActions.push({
          ...action,
          id: crypto.randomUUID(),
          timestamp: Date.now(),
          retryCount: 0,
        })
      }),
      removePendingAction: (id) => set((state) => {
        state.pendingActions = state.pendingActions.filter(a => a.id !== id)
      }),
      incrementRetryCount: (id) => set((state) => {
        const action = state.pendingActions.find(a => a.id === id)
        if (action) {
          action.retryCount += 1
        }
      }),
      clearPendingActions: () => set((state) => {
        state.pendingActions = []
      }),
      
      offlineData: {},
      setOfflineData: (key, data) => set((state) => {
        state.offlineData[key] = data
      }),
      getOfflineData: (key) => get().offlineData[key],
      removeOfflineData: (key) => set((state) => {
        delete state.offlineData[key]
      }),
    })),
    {
      name: 'publish-ai-offline',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

// Store debugging utilities
export const storeDebug = {
  logAllStores: () => {
    console.group('Zustand Store State')
    console.log('Theme:', useThemeStore.getState())
    console.log('UI:', useUIStore.getState())
    console.log('Form:', useFormStore.getState())
    console.log('Workflow:', useWorkflowStore.getState())
    console.log('Analytics:', useAnalyticsStore.getState())
    console.log('Books:', useBookStore.getState())
    console.log('Offline:', useOfflineStore.getState())
    console.groupEnd()
  },
  
  resetAllStores: () => {
    localStorage.removeItem('publish-ai-theme')
    localStorage.removeItem('publish-ai-ui')
    localStorage.removeItem('publish-ai-forms')
    localStorage.removeItem('publish-ai-analytics')
    localStorage.removeItem('publish-ai-books')
    localStorage.removeItem('publish-ai-offline')
    window.location.reload()
  },
}

// Make debug available in development
if (process.env.NODE_ENV === 'development') {
  ;(window as any).storeDebug = storeDebug
}