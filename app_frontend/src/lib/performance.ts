import { useEffect, useCallback, useState } from 'react'

// Performance monitoring utilities
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
  
  // Custom metrics
  componentMountTime?: number
  routeChangeTime?: number
  apiCallTime?: number
  
  // Memory metrics
  jsHeapSizeLimit?: number
  totalJSHeapSize?: number
  usedJSHeapSize?: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {}
  private observers: PerformanceObserver[] = []
  private startTimes: Map<string, number> = new Map()

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    if (typeof window === 'undefined') return

    try {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as PerformanceEntry & { renderTime: number; loadTime: number }
        this.metrics.lcp = lastEntry.renderTime || lastEntry.loadTime
        this.reportMetric('lcp', this.metrics.lcp)
      })
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })
      this.observers.push(lcpObserver)

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const firstInput = list.getEntries()[0] as PerformanceEntry & { processingStart: number; startTime: number }
        this.metrics.fid = firstInput.processingStart - firstInput.startTime
        this.reportMetric('fid', this.metrics.fid)
      })
      fidObserver.observe({ type: 'first-input', buffered: true })
      this.observers.push(fidObserver)

      // Cumulative Layout Shift
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const layoutShift = entry as PerformanceEntry & { value: number; hadRecentInput: boolean }
          if (!layoutShift.hadRecentInput) {
            clsValue += layoutShift.value
          }
        }
        this.metrics.cls = clsValue
        this.reportMetric('cls', this.metrics.cls)
      })
      clsObserver.observe({ type: 'layout-shift', buffered: true })
      this.observers.push(clsObserver)

      // Navigation timing
      this.measureNavigationTiming()

      // Memory usage (if available)
      this.measureMemoryUsage()
    } catch (error) {
      console.warn('Performance observers not supported:', error)
    }
  }

  private measureNavigationTiming() {
    if (typeof window === 'undefined' || !window.performance?.timing) return

    const timing = window.performance.timing
    const navigationStart = timing.navigationStart

    this.metrics.ttfb = timing.responseStart - navigationStart
    this.metrics.fcp = timing.domContentLoadedEventEnd - navigationStart

    this.reportMetric('ttfb', this.metrics.ttfb)
    this.reportMetric('fcp', this.metrics.fcp)
  }

  private measureMemoryUsage() {
    if (typeof window === 'undefined') return

    const memory = (window.performance as any).memory
    if (memory) {
      this.metrics.jsHeapSizeLimit = memory.jsHeapSizeLimit
      this.metrics.totalJSHeapSize = memory.totalJSHeapSize
      this.metrics.usedJSHeapSize = memory.usedJSHeapSize
    }
  }

  private reportMetric(name: string, value: number) {
    // Send to analytics service in production
    if (process.env.NODE_ENV === 'production') {
      console.log(`Performance metric: ${name} = ${value}`)
    }
  }

  // Manual timing utilities
  startTiming(label: string): void {
    this.startTimes.set(label, performance.now())
  }

  endTiming(label: string): number {
    const startTime = this.startTimes.get(label)
    if (!startTime) {
      console.warn(`No start time found for label: ${label}`)
      return 0
    }

    const duration = performance.now() - startTime
    this.startTimes.delete(label)
    
    // Store in metrics
    if (label.includes('component')) {
      this.metrics.componentMountTime = duration
    } else if (label.includes('route')) {
      this.metrics.routeChangeTime = duration
    } else if (label.includes('api')) {
      this.metrics.apiCallTime = duration
    }

    this.reportMetric(label, duration)
    return duration
  }

  // Get current metrics
  getMetrics(): PerformanceMetrics {
    this.measureMemoryUsage() // Update memory metrics
    return { ...this.metrics }
  }

  // Cleanup
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.startTimes.clear()
  }
}

export const performanceMonitor = new PerformanceMonitor()

// React hooks for performance monitoring
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})

  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(performanceMonitor.getMetrics())
    }

    updateMetrics()
    const interval = setInterval(updateMetrics, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return metrics
}

export function useComponentPerformance(componentName: string) {
  useEffect(() => {
    performanceMonitor.startTiming(`component-${componentName}`)
    
    return () => {
      performanceMonitor.endTiming(`component-${componentName}`)
    }
  }, [componentName])
}

export function useRoutePerformance(routeName: string) {
  useEffect(() => {
    performanceMonitor.startTiming(`route-${routeName}`)
    
    return () => {
      performanceMonitor.endTiming(`route-${routeName}`)
    }
  }, [routeName])
}

// Image optimization utilities
export function optimizeImageUrl(
  src: string,
  width: number,
  height?: number,
  options: { quality?: number; format?: 'webp' | 'avif' | 'jpeg' | 'png' } = {}
): string {
  if (!src) return ''

  // If using Next.js Image optimization
  if (process.env.NODE_ENV === 'production') {
    const params = new URLSearchParams({
      url: src,
      w: width.toString(),
      ...(height && { h: height.toString() }),
      ...(options.quality && { q: options.quality.toString() }),
      ...(options.format && { f: options.format }),
    })
    
    return `/_next/image?${params.toString()}`
  }

  return src
}

// Resource preloading utilities
export function preloadResource(href: string, as: string, type?: string): void {
  if (typeof document === 'undefined') return

  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = href
  link.as = as
  if (type) link.type = type
  
  document.head.appendChild(link)
}

export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

// Intersection Observer for lazy loading
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
): [React.RefCallback<Element>, boolean] {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [observer, setObserver] = useState<IntersectionObserver | null>(null)

  const ref = useCallback((element: Element | null) => {
    if (observer) {
      observer.disconnect()
    }

    if (element) {
      const newObserver = new IntersectionObserver(
        ([entry]) => {
          setIsIntersecting(entry.isIntersecting)
        },
        {
          threshold: 0.1,
          rootMargin: '50px',
          ...options,
        }
      )

      newObserver.observe(element)
      setObserver(newObserver)
    }
  }, [observer, options])

  useEffect(() => {
    return () => {
      if (observer) {
        observer.disconnect()
      }
    }
  }, [observer])

  return [ref, isIntersecting]
}

// Critical resource hints
export function addResourceHints(): void {
  if (typeof document === 'undefined') return

  // DNS prefetch for external domains
  const dnsPrefetch = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
  ]

  dnsPrefetch.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = domain
    document.head.appendChild(link)
  })

  // Preconnect to critical origins
  const preconnect = [
    process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  ]

  preconnect.forEach(origin => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = origin
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

// Initialize performance monitoring on app start
if (typeof window !== 'undefined') {
  addResourceHints()
}