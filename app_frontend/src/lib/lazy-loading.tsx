import React, { Suspense, ComponentType, ReactElement } from "react";
import { ErrorBoundary } from "@/components/layout/error-boundary";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import Link from "next/link";
import { useRouter } from "next/navigation";

// Lazy loading wrapper for client components
export function withLazyLoading<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: {
    fallback?: ReactElement;
    errorFallback?: ComponentType<{ error: Error; reset: () => void }>;
    delay?: number;
    timeout?: number;
  } = {}
) {
  const LazyComponent = React.lazy(() => {
    const componentPromise = factory();

    if (options.delay) {
      return new Promise<{ default: T }>((resolve) => {
        setTimeout(() => {
          componentPromise.then((module) => resolve(module));
        }, options.delay);
      });
    }

    if (options.timeout) {
      return Promise.race([
        componentPromise,
        new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(
              new Error(`Lazy loading timeout after ${options.timeout}ms`)
            );
          }, options.timeout);
        }),
      ]);
    }

    return componentPromise;
  });

  const WrappedComponent = (props: React.ComponentProps<T>) => {
    const fallback = options.fallback || (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
      </div>
    );

    return (
      <ErrorBoundary fallback={options.errorFallback}>
        <Suspense fallback={fallback}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withLazyLoading(Component)`;
  return WrappedComponent;
}

// Preload-friendly link component
export function PreloadLink({
  href,
  children,
  ...props
}: React.ComponentProps<typeof Link>) {
  return (
    <Link href={href} prefetch {...props}>
      {children}
    </Link>
  );
}

// Lazy client components
export const LazyNewBookWizard = withLazyLoading(
  () =>
    import("@/components/books/new-book-wizard").then((module) => ({
      default: module.NewBookWizard,
    })),
  { fallback: <NewBookWizardSkeleton /> }
);

export const LazyChartWrapper = withLazyLoading(
  () =>
    import("@/components/ui/chart-wrapper").then((module) => ({
      default: module.ChartWrapper,
    })),
  { fallback: <ChartSkeleton /> }
);

export const LazyVirtualList = withLazyLoading(
  () => import("@/components/ui/virtual-list"),
  { fallback: <VirtualListSkeleton /> }
);

// Skeleton components
function NewBookWizardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="h-8 bg-gray-200 rounded animate-pulse" />
      <div className="h-64 bg-gray-100 rounded animate-pulse" />
      <div className="flex justify-between">
        <div className="h-10 w-20 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 w-20 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
}

function ChartSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-6 bg-gray-200 rounded animate-pulse" />
      <div className="h-64 bg-gray-100 rounded animate-pulse" />
    </div>
  );
}

function VirtualListSkeleton() {
  return (
    <div className="space-y-2">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="h-12 bg-gray-100 rounded animate-pulse" />
      ))}
    </div>
  );
}

// Dynamic import manager (optional, unchanged if you still want it)
export class DynamicImportManager {
  private cache = new Map<string, Promise<any>>();

  import<T>(key: string, factory: () => Promise<T>): Promise<T> {
    if (!this.cache.has(key)) {
      this.cache.set(key, factory());
    }
    return this.cache.get(key)!;
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export const dynamicImportManager = new DynamicImportManager();

// React hook for app route preloading (via router)
export function useRoutePreloading() {
  const router = useRouter();

  const preloadRoute = React.useCallback(
    (href: string) => {
      try {
        router.prefetch(href);
      } catch (error) {
        // Ignore prefetch errors
      }
    },
    [router]
  );

  const preloadOnHover = React.useCallback(
    (href: string) => {
      return {
        onMouseEnter: () => preloadRoute(href),
        onFocus: () => preloadRoute(href),
      };
    },
    [preloadRoute]
  );

  return { preloadRoute, preloadOnHover };
}

