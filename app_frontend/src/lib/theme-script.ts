// Theme script that runs before React hydration to prevent flash
export const themeScript = `
(function() {
  function getStoredTheme() {
    try {
      return localStorage.getItem('ui-theme') || 'system';
    } catch (e) {
      return 'system';
    }
  }

  function getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  function applyTheme(theme) {
    const actualTheme = theme === 'system' ? getSystemTheme() : theme;
    const root = document.documentElement;
    
    // Remove previous theme classes
    root.classList.remove('light', 'dark');
    
    // Add current theme class
    root.classList.add(actualTheme);

    // Update CSS custom properties for theme
    if (actualTheme === 'dark') {
      root.style.setProperty('--background', '222.2% 84% 4.9%');
      root.style.setProperty('--foreground', '210% 40% 98%');
      root.style.setProperty('--card', '222.2% 84% 4.9%');
      root.style.setProperty('--card-foreground', '210% 40% 98%');
      root.style.setProperty('--primary', '210% 40% 98%');
      root.style.setProperty('--primary-foreground', '222.2% 84% 4.9%');
      root.style.setProperty('--secondary', '217.2% 32.6% 17.5%');
      root.style.setProperty('--secondary-foreground', '210% 40% 98%');
      root.style.setProperty('--muted', '217.2% 32.6% 17.5%');
      root.style.setProperty('--muted-foreground', '215% 20.2% 65.1%');
      root.style.setProperty('--accent', '217.2% 32.6% 17.5%');
      root.style.setProperty('--accent-foreground', '210% 40% 98%');
      root.style.setProperty('--border', '217.2% 32.6% 17.5%');
      root.style.setProperty('--input', '217.2% 32.6% 17.5%');
      root.style.setProperty('--ring', '212.7% 26.8% 83.9%');
    } else {
      root.style.setProperty('--background', '0 0% 100%');
      root.style.setProperty('--foreground', '222.2% 84% 4.9%');
      root.style.setProperty('--card', '0 0% 100%');
      root.style.setProperty('--card-foreground', '222.2% 84% 4.9%');
      root.style.setProperty('--primary', '222.2% 47.4% 11.2%');
      root.style.setProperty('--primary-foreground', '210% 40% 98%');
      root.style.setProperty('--secondary', '210% 40% 96%');
      root.style.setProperty('--secondary-foreground', '222.2% 84% 4.9%');
      root.style.setProperty('--muted', '210% 40% 96%');
      root.style.setProperty('--muted-foreground', '215.4% 16.3% 46.9%');
      root.style.setProperty('--accent', '210% 40% 96%');
      root.style.setProperty('--accent-foreground', '222.2% 84% 4.9%');
      root.style.setProperty('--border', '214.3% 31.8% 91.4%');
      root.style.setProperty('--input', '214.3% 31.8% 91.4%');
      root.style.setProperty('--ring', '222.2% 84% 4.9%');
    }
  }

  // Apply theme immediately
  const storedTheme = getStoredTheme();
  applyTheme(storedTheme);

  // Listen for system theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function() {
    const currentTheme = getStoredTheme();
    if (currentTheme === 'system') {
      applyTheme('system');
    }
  });
})();
`