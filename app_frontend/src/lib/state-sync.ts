import { useEffect, useCallback } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useOfflineStore } from './store'
import { apiClient } from './api'
import { toast } from 'react-hot-toast'

// Action types for offline synchronization
export interface SyncAction {
  id: string
  type: 'CREATE' | 'UPDATE' | 'DELETE'
  entity: string
  entityId?: string
  data: any
  timestamp: number
  retryCount: number
  maxRetries?: number
}

export interface SyncResult {
  success: boolean
  actionId: string
  error?: Error
}

// State synchronization manager
class StateSyncManager {
  private queryClient: ReturnType<typeof useQueryClient> | null = null
  private isProcessing = false
  private processingQueue = new Set<string>()

  setQueryClient(client: ReturnType<typeof useQueryClient>) {
    this.queryClient = client
  }

  // Queue action for offline execution
  queueAction(action: Omit<SyncAction, 'id' | 'timestamp' | 'retryCount'>) {
    const syncAction: SyncAction = {
      ...action,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: action.maxRetries || 3,
    }

    useOfflineStore.getState().addPendingAction(syncAction)
    return syncAction.id
  }

  // Process pending actions when online
  async processPendingActions(): Promise<SyncResult[]> {
    if (this.isProcessing || !this.queryClient) {
      return []
    }

    this.isProcessing = true
    const { pendingActions } = useOfflineStore.getState()
    const results: SyncResult[] = []

    try {
      // Process actions in chronological order
      const sortedActions = [...pendingActions].sort((a, b) => a.timestamp - b.timestamp)

      for (const action of sortedActions) {
        if (this.processingQueue.has(action.id)) {
          continue // Skip if already processing
        }

        this.processingQueue.add(action.id)
        
        try {
          const result = await this.executeAction(action)
          results.push(result)

          if (result.success) {
            useOfflineStore.getState().removePendingAction(action.id)
            this.processingQueue.delete(action.id)
          } else {
            // Increment retry count
            useOfflineStore.getState().incrementRetryCount(action.id)
            this.processingQueue.delete(action.id)

            // Remove if max retries exceeded
            if (action.retryCount >= (action.maxRetries || 3)) {
              useOfflineStore.getState().removePendingAction(action.id)
              toast.error(`Failed to sync ${action.entity} after ${action.maxRetries} attempts`)
            }
          }
        } catch (error) {
          console.error('Error processing action:', error)
          this.processingQueue.delete(action.id)
          
          results.push({
            success: false,
            actionId: action.id,
            error: error as Error,
          })
        }
      }
    } finally {
      this.isProcessing = false
    }

    return results
  }

  private async executeAction(action: SyncAction): Promise<SyncResult> {
    try {
      let result: any

      switch (action.type) {
        case 'CREATE':
          result = await this.executeCreate(action)
          break
        case 'UPDATE':
          result = await this.executeUpdate(action)
          break
        case 'DELETE':
          result = await this.executeDelete(action)
          break
        default:
          throw new Error(`Unknown action type: ${action.type}`)
      }

      // Update query cache with result
      if (result && this.queryClient) {
        this.updateQueryCache(action, result)
      }

      return {
        success: true,
        actionId: action.id,
      }
    } catch (error) {
      return {
        success: false,
        actionId: action.id,
        error: error as Error,
      }
    }
  }

  private async executeCreate(action: SyncAction) {
    const endpoint = this.getEndpoint(action.entity)
    return await apiClient.post(endpoint, action.data)
  }

  private async executeUpdate(action: SyncAction) {
    if (!action.entityId) {
      throw new Error('Entity ID required for update action')
    }
    const endpoint = this.getEndpoint(action.entity, action.entityId)
    return await apiClient.put(endpoint, action.data)
  }

  private async executeDelete(action: SyncAction) {
    if (!action.entityId) {
      throw new Error('Entity ID required for delete action')
    }
    const endpoint = this.getEndpoint(action.entity, action.entityId)
    return await apiClient.delete(endpoint)
  }

  private getEndpoint(entity: string, id?: string): string {
    const baseEndpoints: Record<string, string> = {
      books: '/api/books',
      users: '/api/users',
      analytics: '/api/analytics',
      settings: '/api/settings',
    }

    const base = baseEndpoints[entity]
    if (!base) {
      throw new Error(`Unknown entity: ${entity}`)
    }

    return id ? `${base}/${id}` : base
  }

  private updateQueryCache(action: SyncAction, result: any) {
    if (!this.queryClient) return

    const queryKey = this.getQueryKey(action.entity)

    switch (action.type) {
      case 'CREATE':
        // Add to list queries
        this.queryClient.setQueriesData(
          { queryKey: [...queryKey, 'list'] },
          (old: any) => {
            if (old?.data) {
              return {
                ...old,
                data: [result, ...old.data],
                total: old.total + 1,
              }
            }
            return old
          }
        )
        // Set individual item
        this.queryClient.setQueryData([...queryKey, 'detail', result.id], result)
        break

      case 'UPDATE':
        // Update in list queries
        this.queryClient.setQueriesData(
          { queryKey: [...queryKey, 'list'] },
          (old: any) => {
            if (old?.data) {
              return {
                ...old,
                data: old.data.map((item: any) =>
                  item.id === action.entityId ? result : item
                ),
              }
            }
            return old
          }
        )
        // Update individual item
        this.queryClient.setQueryData([...queryKey, 'detail', action.entityId], result)
        break

      case 'DELETE':
        // Remove from list queries
        this.queryClient.setQueriesData(
          { queryKey: [...queryKey, 'list'] },
          (old: any) => {
            if (old?.data) {
              return {
                ...old,
                data: old.data.filter((item: any) => item.id !== action.entityId),
                total: Math.max(0, old.total - 1),
              }
            }
            return old
          }
        )
        // Remove individual item
        this.queryClient.removeQueries({
          queryKey: [...queryKey, 'detail', action.entityId],
        })
        break
    }
  }

  private getQueryKey(entity: string): string[] {
    const queryKeys: Record<string, string[]> = {
      books: ['books'],
      users: ['users'],
      analytics: ['analytics'],
      settings: ['settings'],
    }

    return queryKeys[entity] || [entity]
  }

  // Get sync status
  getSyncStatus() {
    const { pendingActions } = useOfflineStore.getState()
    return {
      hasPendingActions: pendingActions.length > 0,
      pendingCount: pendingActions.length,
      isProcessing: this.isProcessing,
      actionsByType: pendingActions.reduce((acc, action) => {
        acc[action.type] = (acc[action.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
    }
  }
}

export const stateSyncManager = new StateSyncManager()

// React hook for state synchronization
export function useStateSync() {
  const queryClient = useQueryClient()
  const { isOnline, pendingActions } = useOfflineStore()

  // Set query client on mount
  useEffect(() => {
    stateSyncManager.setQueryClient(queryClient)
  }, [queryClient])

  // Process pending actions when coming online
  useEffect(() => {
    if (isOnline && pendingActions.length > 0) {
      console.log('Processing pending actions after coming online...')
      stateSyncManager.processPendingActions()
        .then(results => {
          const successful = results.filter(r => r.success).length
          const failed = results.filter(r => !r.success).length
          
          if (successful > 0) {
            toast.success(`Synced ${successful} pending changes`)
          }
          if (failed > 0) {
            toast.error(`Failed to sync ${failed} changes`)
          }
        })
        .catch(error => {
          console.error('Error processing pending actions:', error)
          toast.error('Error syncing pending changes')
        })
    }
  }, [isOnline, pendingActions.length])

  const queueAction = useCallback(
    (action: Omit<SyncAction, 'id' | 'timestamp' | 'retryCount'>) => {
      return stateSyncManager.queueAction(action)
    },
    []
  )

  const processPendingActions = useCallback(() => {
    return stateSyncManager.processPendingActions()
  }, [])

  const getSyncStatus = useCallback(() => {
    return stateSyncManager.getSyncStatus()
  }, [])

  return {
    queueAction,
    processPendingActions,
    getSyncStatus,
    isOnline,
    pendingActions,
  }
}

// Hook for offline-first CRUD operations
export function useOfflineFirstMutation<T = any>(
  entity: string,
  options: {
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
    enableOptimisticUpdates?: boolean
  } = {}
) {
  const { queueAction, isOnline } = useStateSync()
  const queryClient = useQueryClient()

  const createMutation = useCallback(
    async (data: any): Promise<T> => {
      if (isOnline) {
        try {
          const result = await apiClient.post(`/api/${entity}`, data)
          options.onSuccess?.(result)
          return result
        } catch (error) {
          options.onError?.(error as Error)
          throw error
        }
      } else {
        // Queue for offline processing
        const actionId = queueAction({
          type: 'CREATE',
          entity,
          data,
        })

        if (options.enableOptimisticUpdates) {
          // Apply optimistic update
          const optimisticData = {
            id: actionId,
            ...data,
            _pending: true,
          }

          const queryKey = [entity, 'list']
          queryClient.setQueriesData({ queryKey }, (old: any) => {
            if (old?.data) {
              return {
                ...old,
                data: [optimisticData, ...old.data],
                total: old.total + 1,
              }
            }
            return old
          })

          options.onSuccess?.(optimisticData)
          return optimisticData
        }

        toast.info('Changes saved locally. Will sync when online.')
        return { id: actionId, ...data } as T
      }
    },
    [entity, isOnline, queueAction, options, queryClient]
  )

  const updateMutation = useCallback(
    async (id: string, data: any): Promise<T> => {
      if (isOnline) {
        try {
          const result = await apiClient.put(`/api/${entity}/${id}`, data)
          options.onSuccess?.(result)
          return result
        } catch (error) {
          options.onError?.(error as Error)
          throw error
        }
      } else {
        queueAction({
          type: 'UPDATE',
          entity,
          entityId: id,
          data,
        })

        if (options.enableOptimisticUpdates) {
          // Apply optimistic update
          const queryKey = [entity, 'detail', id]
          queryClient.setQueryData(queryKey, (old: any) => ({
            ...old,
            ...data,
            _pending: true,
          }))
        }

        toast.info('Changes saved locally. Will sync when online.')
        return { id, ...data } as T
      }
    },
    [entity, isOnline, queueAction, options, queryClient]
  )

  const deleteMutation = useCallback(
    async (id: string): Promise<void> => {
      if (isOnline) {
        try {
          await apiClient.delete(`/api/${entity}/${id}`)
          options.onSuccess?.(undefined as any)
        } catch (error) {
          options.onError?.(error as Error)
          throw error
        }
      } else {
        queueAction({
          type: 'DELETE',
          entity,
          entityId: id,
          data: {},
        })

        if (options.enableOptimisticUpdates) {
          // Apply optimistic delete
          const listQueryKey = [entity, 'list']
          queryClient.setQueriesData({ queryKey: listQueryKey }, (old: any) => {
            if (old?.data) {
              return {
                ...old,
                data: old.data.filter((item: any) => item.id !== id),
                total: Math.max(0, old.total - 1),
              }
            }
            return old
          })

          // Mark detail as deleted
          const detailQueryKey = [entity, 'detail', id]
          queryClient.setQueryData(detailQueryKey, (old: any) => ({
            ...old,
            _deleted: true,
            _pending: true,
          }))
        }

        toast.info('Changes saved locally. Will sync when online.')
      }
    },
    [entity, isOnline, queueAction, options, queryClient]
  )

  return {
    create: createMutation,
    update: updateMutation,
    delete: deleteMutation,
  }
}