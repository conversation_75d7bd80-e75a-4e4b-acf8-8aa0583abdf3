/**
 * Permission checking utilities for role-based access control
 * Handles role validation, permission levels, and resource-based access control
 */

export type UserRole = "admin" | "editor" | "author" | "user" | "guest"
export type Permission = "read" | "write" | "delete" | "admin" | "manage_users" | "manage_content" | "publish" | "edit_all" | "view_analytics"

// Role hierarchy mapping (higher number = higher privilege)
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  guest: 0,
  user: 1,
  author: 2,
  editor: 3,
  admin: 4,
}

// Permission mapping by role
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  guest: ["read"],
  user: ["read"],
  author: ["read", "write", "publish"],
  editor: ["read", "write", "publish", "manage_content", "edit_all", "view_analytics"],
  admin: ["read", "write", "delete", "admin", "manage_users", "manage_content", "publish", "edit_all", "view_analytics"],
}

// Resource-based permissions (for future extensibility)
export interface ResourcePermission {
  resource: string
  action: Permission
  conditions?: Record<string, any>
}

export interface User {
  id: string
  email: string
  role: UserRole
  permissions?: Permission[]
  customPermissions?: ResourcePermission[]
}

/**
 * Checks if a user has a specific role or higher
 * @param userRole - The user's current role
 * @param requiredRole - The minimum required role
 * @returns True if user has the required role or higher
 */
export function hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
  const userLevel = ROLE_HIERARCHY[userRole] || 0
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0
  return userLevel >= requiredLevel
}

/**
 * Checks if a user has any of the specified roles
 * @param userRole - The user's current role
 * @param allowedRoles - Array of allowed roles
 * @returns True if user has any of the allowed roles
 */
export function hasAnyRole(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole)
}

/**
 * Checks if a user has all of the specified roles (role hierarchy check)
 * @param userRole - The user's current role
 * @param requiredRoles - Array of required roles
 * @returns True if user has a role that satisfies all requirements
 */
export function hasAllRoles(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  const userLevel = ROLE_HIERARCHY[userRole] || 0
  const maxRequiredLevel = Math.max(...requiredRoles.map(role => ROLE_HIERARCHY[role] || 0))
  return userLevel >= maxRequiredLevel
}

/**
 * Checks if a user is explicitly denied a role
 * @param userRole - The user's current role
 * @param deniedRoles - Array of denied roles
 * @returns True if user role is in the denied list
 */
export function isRoleDenied(userRole: UserRole, deniedRoles: UserRole[]): boolean {
  return deniedRoles.includes(userRole)
}

/**
 * Gets all permissions for a specific role
 * @param role - The role to get permissions for
 * @returns Array of permissions for the role
 */
export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || []
}

/**
 * Checks if a user has a specific permission
 * @param user - The user object or role string
 * @param permission - The permission to check
 * @returns True if user has the permission
 */
export function hasPermission(user: User | UserRole, permission: Permission): boolean {
  const userRole = typeof user === "string" ? user : user.role
  const userPermissions = getRolePermissions(userRole)
  
  // Check role-based permissions
  if (userPermissions.includes(permission)) {
    return true
  }
  
  // Check custom permissions if user object is provided
  if (typeof user === "object" && user.permissions) {
    return user.permissions.includes(permission)
  }
  
  return false
}

/**
 * Checks if a user has any of the specified permissions
 * @param user - The user object or role string
 * @param permissions - Array of permissions to check
 * @returns True if user has any of the permissions
 */
export function hasAnyPermission(user: User | UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(user, permission))
}

/**
 * Checks if a user has all of the specified permissions
 * @param user - The user object or role string
 * @param permissions - Array of permissions to check
 * @returns True if user has all permissions
 */
export function hasAllPermissions(user: User | UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(user, permission))
}

/**
 * Checks if a user can perform an action on a specific resource
 * @param user - The user object
 * @param resource - The resource identifier
 * @param action - The action/permission to check
 * @param conditions - Additional conditions to check
 * @returns True if user can perform the action
 */
export function canAccessResource(
  user: User,
  resource: string,
  action: Permission,
  conditions?: Record<string, any>
): boolean {
  // Check role-based permissions first
  if (hasPermission(user, action)) {
    return true
  }
  
  // Check custom resource permissions
  if (user.customPermissions) {
    return user.customPermissions.some(perm => 
      perm.resource === resource && 
      perm.action === action &&
      (!conditions || matchesConditions(perm.conditions || {}, conditions))
    )
  }
  
  return false
}

/**
 * Helper function to match conditions for resource-based permissions
 * @param permissionConditions - Conditions from the permission
 * @param requestConditions - Conditions from the request
 * @returns True if conditions match
 */
function matchesConditions(
  permissionConditions: Record<string, any>,
  requestConditions: Record<string, any>
): boolean {
  for (const [key, value] of Object.entries(requestConditions)) {
    if (permissionConditions[key] !== value) {
      return false
    }
  }
  return true
}

/**
 * Gets the highest role from an array of roles
 * @param roles - Array of roles
 * @returns The role with the highest privilege level
 */
export function getHighestRole(roles: UserRole[]): UserRole {
  if (roles.length === 0) return "guest"
  
  return roles.reduce((highest, current) => {
    const highestLevel = ROLE_HIERARCHY[highest] || 0
    const currentLevel = ROLE_HIERARCHY[current] || 0
    return currentLevel > highestLevel ? current : highest
  })
}

/**
 * Checks if a role is valid
 * @param role - The role to validate
 * @returns True if the role is valid
 */
export function isValidRole(role: string): role is UserRole {
  return Object.keys(ROLE_HIERARCHY).includes(role)
}

/**
 * Checks if a permission is valid
 * @param permission - The permission to validate
 * @returns True if the permission is valid
 */
export function isValidPermission(permission: string): permission is Permission {
  const allPermissions = Object.values(ROLE_PERMISSIONS).flat()
  return allPermissions.includes(permission as Permission)
}

/**
 * Creates a permission checker function for a specific user
 * @param user - The user to create a checker for
 * @returns Function that checks permissions for the user
 */
export function createPermissionChecker(user: User | UserRole) {
  return {
    hasRole: (requiredRole: UserRole) => {
      const userRole = typeof user === "string" ? user : user.role
      return hasRole(userRole, requiredRole)
    },
    hasAnyRole: (allowedRoles: UserRole[]) => {
      const userRole = typeof user === "string" ? user : user.role
      return hasAnyRole(userRole, allowedRoles)
    },
    hasPermission: (permission: Permission) => hasPermission(user, permission),
    hasAnyPermission: (permissions: Permission[]) => hasAnyPermission(user, permissions),
    hasAllPermissions: (permissions: Permission[]) => hasAllPermissions(user, permissions),
    canAccess: (resource: string, action: Permission, conditions?: Record<string, any>) => {
      if (typeof user === "string") return false
      return canAccessResource(user, resource, action, conditions)
    }
  }
}

/**
 * Role-based component access control utility
 * @param userRole - The user's current role
 * @param config - Access control configuration
 * @returns True if access should be granted
 */
export interface AccessControlConfig {
  requiredRole?: UserRole
  requiredRoles?: UserRole[]
  allowedRoles?: UserRole[]
  deniedRoles?: UserRole[]
  requiredPermissions?: Permission[]
  customCheck?: (userRole: UserRole) => boolean
}

export function checkAccess(userRole: UserRole, config: AccessControlConfig): boolean {
  // Check denied roles first (explicit deny)
  if (config.deniedRoles && isRoleDenied(userRole, config.deniedRoles)) {
    return false
  }
  
  // Check custom access function
  if (config.customCheck && !config.customCheck(userRole)) {
    return false
  }
  
  // Check allowed roles (explicit allow)
  if (config.allowedRoles && config.allowedRoles.length > 0) {
    return hasAnyRole(userRole, config.allowedRoles)
  }
  
  // Check required role (minimum level)
  if (config.requiredRole && !hasRole(userRole, config.requiredRole)) {
    return false
  }
  
  // Check multiple required roles (all must be satisfied)
  if (config.requiredRoles && !hasAllRoles(userRole, config.requiredRoles)) {
    return false
  }
  
  // Check required permissions
  if (config.requiredPermissions && !hasAllPermissions(userRole, config.requiredPermissions)) {
    return false
  }
  
  return true
}

/**
 * Middleware-style permission checker
 * @param user - The user object
 * @param requirements - Permission requirements
 * @returns Object with access status and reason
 */
export interface PermissionCheckResult {
  allowed: boolean
  reason?: string
  missingPermissions?: Permission[]
  suggestedRole?: UserRole
}

export function checkPermissions(
  user: User | UserRole,
  requirements: {
    roles?: UserRole[]
    permissions?: Permission[]
    resource?: string
    action?: Permission
    conditions?: Record<string, any>
  }
): PermissionCheckResult {
  const userRole = typeof user === "string" ? user : user.role
  
  // Check roles
  if (requirements.roles && !hasAnyRole(userRole, requirements.roles)) {
    return {
      allowed: false,
      reason: `Requires one of these roles: ${requirements.roles.join(", ")}`,
      suggestedRole: getHighestRole(requirements.roles)
    }
  }
  
  // Check permissions
  if (requirements.permissions) {
    const missingPermissions = requirements.permissions.filter(
      perm => !hasPermission(user, perm)
    )
    
    if (missingPermissions.length > 0) {
      return {
        allowed: false,
        reason: `Missing required permissions: ${missingPermissions.join(", ")}`,
        missingPermissions
      }
    }
  }
  
  // Check resource access
  if (requirements.resource && requirements.action && typeof user === "object") {
    if (!canAccessResource(user, requirements.resource, requirements.action, requirements.conditions)) {
      return {
        allowed: false,
        reason: `Cannot perform '${requirements.action}' on resource '${requirements.resource}'`
      }
    }
  }
  
  return { allowed: true }
}

/**
 * Utility to create role-based navigation items
 * @param items - Navigation items with role requirements
 * @param userRole - Current user role
 * @returns Filtered navigation items the user can access
 */
export interface NavigationItem {
  label: string
  href: string
  icon?: string
  requiredRole?: UserRole
  requiredPermissions?: Permission[]
  allowedRoles?: UserRole[]
}

export function filterNavigationByRole(
  items: NavigationItem[],
  userRole: UserRole
): NavigationItem[] {
  return items.filter(item => {
    if (item.requiredRole && !hasRole(userRole, item.requiredRole)) {
      return false
    }
    
    if (item.allowedRoles && !hasAnyRole(userRole, item.allowedRoles)) {
      return false
    }
    
    if (item.requiredPermissions && !hasAllPermissions(userRole, item.requiredPermissions)) {
      return false
    }
    
    return true
  })
}