/**
 * Redirect management utilities for authentication flows
 * Handles safe redirects, URL preservation, and prevents malicious redirects
 */

// List of allowed redirect patterns for security
const ALLOWED_REDIRECT_PATTERNS = [
  /^\/dashboard/, // Dashboard pages
  /^\/auth/, // Auth pages
  /^\/profile/, // Profile pages
  /^\/settings/, // Settings pages
  /^\/books/, // Books pages
  /^\/analytics/, // Analytics pages
  /^\/trends/, // Trends pages
  /^\/$/, // Home page
]

// Dangerous redirect patterns to block
const BLOCKED_REDIRECT_PATTERNS = [
  /^javascript:/, // JavaScript protocol
  /^data:/, // Data protocol
  /^vbscript:/, // VBScript protocol
  /^file:/, // File protocol
  /^ftp:/, // FTP protocol
  /^mailto:/, // Email protocol
  /^tel:/, // Phone protocol
]

/**
 * Validates if a URL is safe for redirection
 * @param url - The URL to validate
 * @returns True if the URL is safe to redirect to
 */
export function isValidRedirectUrl(url: string): boolean {
  if (!url || typeof url !== "string") {
    return false
  }

  // Remove leading/trailing whitespace
  const cleanUrl = url.trim()

  // Block empty URLs
  if (!cleanUrl) {
    return false
  }

  // Block dangerous protocols
  if (BLOCKED_REDIRECT_PATTERNS.some(pattern => pattern.test(cleanUrl))) {
    return false
  }

  // If it's an absolute URL, ensure it's same-origin
  if (cleanUrl.startsWith("http://") || cleanUrl.startsWith("https://")) {
    try {
      const urlObj = new URL(cleanUrl)
      // Only allow same-origin URLs
      return urlObj.origin === window.location.origin
    } catch {
      return false
    }
  }

  // For relative URLs, check against allowed patterns
  if (cleanUrl.startsWith("/")) {
    return ALLOWED_REDIRECT_PATTERNS.some(pattern => pattern.test(cleanUrl))
  }

  // Block relative URLs without leading slash (potential security risk)
  return false
}

/**
 * Sanitizes a redirect URL and returns a safe version
 * @param url - The URL to sanitize
 * @param fallback - Fallback URL if the original is unsafe (default: "/dashboard")
 * @returns A safe redirect URL
 */
export function sanitizeRedirectUrl(url: string, fallback = "/dashboard"): string {
  if (!url || !isValidRedirectUrl(url)) {
    return fallback
  }

  // Remove any fragments that might be used for XSS
  try {
    const urlObj = new URL(url, window.location.origin)
    // Preserve pathname and search, but remove hash for security
    return urlObj.pathname + urlObj.search
  } catch {
    // If URL parsing fails, return fallback
    return fallback
  }
}

/**
 * Gets the current URL for use as a callback URL
 * @param includeSearch - Whether to include search parameters (default: true)
 * @param includeHash - Whether to include hash fragment (default: false)
 * @returns The current URL path
 */
export function getCurrentUrl(includeSearch = true, includeHash = false): string {
  if (typeof window === "undefined") {
    return "/"
  }

  let url = window.location.pathname

  if (includeSearch && window.location.search) {
    url += window.location.search
  }

  if (includeHash && window.location.hash) {
    url += window.location.hash
  }

  return url
}

/**
 * Creates a login URL with callback parameter
 * @param loginPath - The login page path (default: "/auth/login")
 * @param callbackUrl - The URL to redirect to after login
 * @returns Complete login URL with callback
 */
export function createLoginUrl(loginPath = "/auth/login", callbackUrl?: string): string {
  const callback = callbackUrl || getCurrentUrl()
  const safeCallback = sanitizeRedirectUrl(callback)
  
  // Create URL with callback parameter
  const url = new URL(loginPath, window.location.origin)
  url.searchParams.set("callbackUrl", safeCallback)
  
  return url.pathname + url.search
}

/**
 * Extracts callback URL from current URL search parameters
 * @param paramName - The parameter name to look for (default: "callbackUrl")
 * @param fallback - Fallback URL if no callback is found
 * @returns The callback URL or fallback
 */
export function getCallbackUrl(paramName = "callbackUrl", fallback = "/dashboard"): string {
  if (typeof window === "undefined") {
    return fallback
  }

  const urlParams = new URLSearchParams(window.location.search)
  const callback = urlParams.get(paramName)
  
  return sanitizeRedirectUrl(callback || "", fallback)
}

/**
 * Removes callback URL from current URL search parameters
 * @param paramName - The parameter name to remove (default: "callbackUrl")
 */
export function clearCallbackUrl(paramName = "callbackUrl"): void {
  if (typeof window === "undefined") {
    return
  }

  const url = new URL(window.location.href)
  url.searchParams.delete(paramName)
  
  // Update URL without triggering a page reload
  window.history.replaceState({}, "", url.pathname + url.search)
}

/**
 * Performs a safe redirect to the specified URL
 * @param url - The URL to redirect to
 * @param fallback - Fallback URL if the original is unsafe
 * @param replace - Whether to replace the current history entry (default: false)
 */
export function safeRedirect(url: string, fallback = "/dashboard", replace = false): void {
  const safeUrl = sanitizeRedirectUrl(url, fallback)
  
  if (replace) {
    window.location.replace(safeUrl)
  } else {
    window.location.href = safeUrl
  }
}

/**
 * Creates a redirect function that can be used with Next.js router
 * @param router - Next.js router instance
 * @param fallback - Fallback URL for unsafe redirects
 * @returns A safe redirect function
 */
export function createSafeRedirect(router: any, fallback = "/dashboard") {
  return (url: string, replace = false) => {
    const safeUrl = sanitizeRedirectUrl(url, fallback)
    
    if (replace) {
      router.replace(safeUrl)
    } else {
      router.push(safeUrl)
    }
  }
}

/**
 * Detects potential redirect loops and prevents them
 * @param targetUrl - The URL being redirected to
 * @param maxRedirects - Maximum allowed redirects (default: 3)
 * @returns True if redirect is safe, false if it would cause a loop
 */
export function detectRedirectLoop(targetUrl: string, maxRedirects = 3): boolean {
  const REDIRECT_HISTORY_KEY = "__auth_redirect_history"
  
  if (typeof window === "undefined") {
    return true
  }

  // Get redirect history from sessionStorage
  const historyJson = sessionStorage.getItem(REDIRECT_HISTORY_KEY)
  const history: string[] = historyJson ? JSON.parse(historyJson) : []
  
  // Count occurrences of target URL in recent history
  const recentRedirects = history.slice(-maxRedirects)
  const redirectCount = recentRedirects.filter(url => url === targetUrl).length
  
  // If we've seen this URL too many times recently, it's likely a loop
  if (redirectCount >= maxRedirects) {
    // Clear the history to prevent permanent lockout
    sessionStorage.removeItem(REDIRECT_HISTORY_KEY)
    return false
  }
  
  // Add current redirect to history
  history.push(targetUrl)
  
  // Keep only recent history (last 10 redirects)
  const recentHistory = history.slice(-10)
  sessionStorage.setItem(REDIRECT_HISTORY_KEY, JSON.stringify(recentHistory))
  
  return true
}

/**
 * Gets the appropriate redirect URL based on user authentication state
 * @param isAuthenticated - Whether the user is authenticated
 * @param requestedUrl - The originally requested URL
 * @param userRole - The user's role (optional)
 * @returns The appropriate redirect URL
 */
export function getAuthRedirectUrl(
  isAuthenticated: boolean,
  requestedUrl?: string,
  userRole?: string
): string {
  // If not authenticated, go to login
  if (!isAuthenticated) {
    return createLoginUrl("/auth/login", requestedUrl)
  }

  // If authenticated but no requested URL, go to appropriate dashboard
  if (!requestedUrl) {
    return getRoleBasedDashboard(userRole)
  }

  // Return the sanitized requested URL
  return sanitizeRedirectUrl(requestedUrl)
}

/**
 * Gets the appropriate dashboard URL based on user role
 * @param userRole - The user's role
 * @returns The dashboard URL for the user's role
 */
export function getRoleBasedDashboard(userRole?: string): string {
  switch (userRole) {
    case "admin":
      return "/dashboard/admin"
    case "editor":
      return "/dashboard/editor"
    case "author":
      return "/dashboard/author"
    default:
      return "/dashboard"
  }
}

/**
 * Utility for handling post-authentication redirects
 * @param router - Next.js router instance
 * @param userRole - The user's role
 * @param fallback - Fallback URL
 */
export function handlePostAuthRedirect(
  router: any,
  userRole?: string,
  fallback?: string
): void {
  const callbackUrl = getCallbackUrl()
  const finalUrl = callbackUrl || getRoleBasedDashboard(userRole) || fallback || "/dashboard"
  
  // Check for redirect loops
  if (!detectRedirectLoop(finalUrl)) {
    console.warn("Redirect loop detected, using fallback")
    router.replace("/dashboard")
    return
  }
  
  // Clear the callback URL from the URL bar
  clearCallbackUrl()
  
  // Perform the redirect
  router.replace(finalUrl)
}

/**
 * Preserves the current URL as a return URL in sessionStorage
 * @param key - Storage key (default: "auth_return_url")
 */
export function preserveReturnUrl(key = "auth_return_url"): void {
  if (typeof window === "undefined") {
    return
  }
  
  const currentUrl = getCurrentUrl()
  sessionStorage.setItem(key, currentUrl)
}

/**
 * Retrieves and clears the preserved return URL
 * @param key - Storage key (default: "auth_return_url")
 * @param fallback - Fallback URL if no return URL is found
 * @returns The return URL or fallback
 */
export function getAndClearReturnUrl(key = "auth_return_url", fallback = "/dashboard"): string {
  if (typeof window === "undefined") {
    return fallback
  }
  
  const returnUrl = sessionStorage.getItem(key)
  sessionStorage.removeItem(key)
  
  return sanitizeRedirectUrl(returnUrl || "", fallback)
}