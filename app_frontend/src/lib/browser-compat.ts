// Browser compatibility utilities

// Browser detection
export interface BrowserInfo {
  name: string
  version: string
  engine: string
  platform: string
  mobile: boolean
  touch: boolean
}

export function getBrowserInfo(): BrowserInfo {
  if (typeof window === 'undefined') {
    return {
      name: 'unknown',
      version: '0',
      engine: 'unknown',
      platform: 'unknown',
      mobile: false,
      touch: false,
    }
  }

  const userAgent = navigator.userAgent
  const platform = navigator.platform
  
  // Browser detection
  let name = 'unknown'
  let version = '0'
  let engine = 'unknown'

  if (userAgent.includes('Chrome')) {
    name = 'chrome'
    const match = userAgent.match(/Chrome\/(\d+)/)
    version = match ? match[1] : '0'
    engine = 'blink'
  } else if (userAgent.includes('Firefox')) {
    name = 'firefox'
    const match = userAgent.match(/Firefox\/(\d+)/)
    version = match ? match[1] : '0'
    engine = 'gecko'
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    name = 'safari'
    const match = userAgent.match(/Version\/(\d+)/)
    version = match ? match[1] : '0'
    engine = 'webkit'
  } else if (userAgent.includes('Edge')) {
    name = 'edge'
    const match = userAgent.match(/Edge\/(\d+)/)
    version = match ? match[1] : '0'
    engine = 'edgehtml'
  } else if (userAgent.includes('Edg/')) {
    name = 'edge-chromium'
    const match = userAgent.match(/Edg\/(\d+)/)
    version = match ? match[1] : '0'
    engine = 'blink'
  }

  // Mobile detection
  const mobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  
  // Touch capability
  const touch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  return {
    name,
    version,
    engine,
    platform,
    mobile,
    touch,
  }
}

// Feature detection
export interface FeatureSupport {
  flexbox: boolean
  grid: boolean
  customProperties: boolean
  intersectionObserver: boolean
  webp: boolean
  avif: boolean
  webGL: boolean
  serviceWorker: boolean
  webAssembly: boolean
  modules: boolean
  asyncAwait: boolean
  fetch: boolean
  promises: boolean
  localStorage: boolean
  sessionStorage: boolean
  indexedDB: boolean
  webSockets: boolean
  geolocation: boolean
  notifications: boolean
  clipboardAPI: boolean
  fileAPI: boolean
  dragAndDrop: boolean
  fullscreen: boolean
  pointerEvents: boolean
  touchEvents: boolean
  mediaQueries: boolean
  viewport: boolean
  darkMode: boolean
  reducedMotion: boolean
  highContrast: boolean
}

export function detectFeatureSupport(): FeatureSupport {
  if (typeof window === 'undefined') {
    // Return minimal support for SSR
    return {
      flexbox: true,
      grid: true,
      customProperties: true,
      intersectionObserver: false,
      webp: false,
      avif: false,
      webGL: false,
      serviceWorker: false,
      webAssembly: false,
      modules: true,
      asyncAwait: true,
      fetch: false,
      promises: true,
      localStorage: false,
      sessionStorage: false,
      indexedDB: false,
      webSockets: false,
      geolocation: false,
      notifications: false,
      clipboardAPI: false,
      fileAPI: false,
      dragAndDrop: false,
      fullscreen: false,
      pointerEvents: false,
      touchEvents: false,
      mediaQueries: true,
      viewport: true,
      darkMode: false,
      reducedMotion: false,
      highContrast: false,
    }
  }

  const features: FeatureSupport = {
    // CSS Features
    flexbox: CSS.supports('display', 'flex'),
    grid: CSS.supports('display', 'grid'),
    customProperties: CSS.supports('--custom', 'property'),
    
    // Web APIs
    intersectionObserver: 'IntersectionObserver' in window,
    serviceWorker: 'serviceWorker' in navigator,
    webAssembly: 'WebAssembly' in window,
    modules: 'noModule' in HTMLScriptElement.prototype,
    fetch: 'fetch' in window,
    promises: 'Promise' in window,
    
    // Storage APIs
    localStorage: (() => {
      try {
        const test = '__test__'
        localStorage.setItem(test, test)
        localStorage.removeItem(test)
        return true
      } catch {
        return false
      }
    })(),
    sessionStorage: (() => {
      try {
        const test = '__test__'
        sessionStorage.setItem(test, test)
        sessionStorage.removeItem(test)
        return true
      } catch {
        return false
      }
    })(),
    indexedDB: 'indexedDB' in window,
    
    // Network APIs
    webSockets: 'WebSocket' in window,
    
    // Device APIs
    geolocation: 'geolocation' in navigator,
    notifications: 'Notification' in window,
    clipboardAPI: 'clipboard' in navigator,
    
    // File APIs
    fileAPI: 'File' in window && 'FileReader' in window,
    dragAndDrop: 'draggable' in document.createElement('div'),
    
    // Display APIs
    fullscreen: 'requestFullscreen' in document.createElement('div'),
    
    // Input APIs
    pointerEvents: 'PointerEvent' in window,
    touchEvents: 'TouchEvent' in window,
    
    // Media queries
    mediaQueries: 'matchMedia' in window,
    viewport: 'visualViewport' in window,
    
    // Accessibility preferences
    darkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
    reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    highContrast: window.matchMedia('(prefers-contrast: high)').matches,
    
    // Image formats
    webp: (() => {
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
    })(),
    avif: (() => {
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
    })(),
    
    // WebGL
    webGL: (() => {
      try {
        const canvas = document.createElement('canvas')
        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      } catch {
        return false
      }
    })(),
    
    // JavaScript features
    asyncAwait: (() => {
      try {
        return (async () => {})().constructor === Promise
      } catch {
        return false
      }
    })(),
  }

  return features
}

// Polyfill loader
export class PolyfillLoader {
  private static loaded = new Set<string>()

  static async loadPolyfill(name: string, condition: boolean, url: string): Promise<void> {
    if (condition || this.loaded.has(name)) {
      return
    }

    try {
      await this.loadScript(url)
      this.loaded.add(name)
      console.log(`✅ Loaded polyfill: ${name}`)
    } catch (error) {
      console.warn(`⚠️ Failed to load polyfill: ${name}`, error)
    }
  }

  private static loadScript(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.onload = () => resolve()
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`))
      document.head.appendChild(script)
    })
  }

  static async loadEssentialPolyfills(): Promise<void> {
    const features = detectFeatureSupport()
    
    const polyfills = [
      {
        name: 'intersection-observer',
        condition: features.intersectionObserver,
        url: 'https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver',
      },
      {
        name: 'fetch',
        condition: features.fetch,
        url: 'https://polyfill.io/v3/polyfill.min.js?features=fetch',
      },
      {
        name: 'custom-properties',
        condition: features.customProperties,
        url: 'https://polyfill.io/v3/polyfill.min.js?features=CSS.supports',
      },
    ]

    await Promise.all(
      polyfills.map(({ name, condition, url }) =>
        this.loadPolyfill(name, condition, url)
      )
    )
  }
}

// CSS compatibility helpers
export const cssCompatibility = {
  // Add vendor prefixes
  addVendorPrefixes: (property: string, value: string): Record<string, string> => {
    const prefixes = ['-webkit-', '-moz-', '-ms-', '-o-', '']
    const result: Record<string, string> = {}
    
    prefixes.forEach(prefix => {
      result[`${prefix}${property}`] = value
    })
    
    return result
  },

  // Check CSS support
  supports: (property: string, value: string): boolean => {
    if (typeof CSS === 'undefined' || !CSS.supports) {
      return false
    }
    return CSS.supports(property, value)
  },

  // Generate fallback styles
  generateFallbacks: (styles: Record<string, string>): Record<string, string> => {
    const fallbacks: Record<string, string> = { ...styles }

    // Grid fallbacks
    if (styles.display === 'grid') {
      fallbacks.display = 'flex'
      fallbacks.flexWrap = 'wrap'
    }

    // Custom properties fallbacks
    Object.entries(styles).forEach(([property, value]) => {
      if (value.includes('var(')) {
        // Extract fallback value from var()
        const match = value.match(/var\([^,]+,\s*([^)]+)\)/)
        if (match) {
          fallbacks[property] = match[1].trim()
        }
      }
    })

    return fallbacks
  },
}

// Responsive design helpers
export interface Breakpoint {
  name: string
  min: number
  max?: number
}

export const breakpoints: Breakpoint[] = [
  { name: 'mobile', min: 0, max: 767 },
  { name: 'tablet', min: 768, max: 1023 },
  { name: 'desktop', min: 1024, max: 1439 },
  { name: 'wide', min: 1440 },
]

export function getCurrentBreakpoint(): string {
  if (typeof window === 'undefined') {
    return 'desktop'
  }

  const width = window.innerWidth
  return breakpoints.find(bp => 
    width >= bp.min && (bp.max === undefined || width <= bp.max)
  )?.name || 'desktop'
}

export function createMediaQuery(breakpoint: string): string {
  const bp = breakpoints.find(b => b.name === breakpoint)
  if (!bp) return ''

  if (bp.max) {
    return `(min-width: ${bp.min}px) and (max-width: ${bp.max}px)`
  }
  return `(min-width: ${bp.min}px)`
}

// Performance optimization for older browsers
export const performanceOptimizations = {
  // Debounce resize events
  debounceResize: (callback: () => void, delay = 100): (() => void) => {
    let timeoutId: NodeJS.Timeout
    
    return () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(callback, delay)
    }
  },

  // Throttle scroll events
  throttleScroll: (callback: () => void, delay = 16): (() => void) => {
    let isThrottled = false
    
    return () => {
      if (isThrottled) return
      
      isThrottled = true
      requestAnimationFrame(() => {
        callback()
        isThrottled = false
      })
    }
  },

  // Optimize images for different browsers
  getOptimalImageFormat: (): string => {
    const features = detectFeatureSupport()
    
    if (features.avif) return 'avif'
    if (features.webp) return 'webp'
    return 'jpg'
  },

  // Lazy load with IntersectionObserver fallback
  lazyLoad: (element: HTMLElement, callback: () => void): (() => void) => {
    const features = detectFeatureSupport()
    
    if (features.intersectionObserver) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            callback()
            observer.unobserve(element)
          }
        })
      })
      
      observer.observe(element)
      return () => observer.disconnect()
    } else {
      // Fallback for older browsers
      const checkVisibility = () => {
        const rect = element.getBoundingClientRect()
        const isVisible = rect.top < window.innerHeight && rect.bottom > 0
        
        if (isVisible) {
          callback()
          window.removeEventListener('scroll', checkVisibility)
          window.removeEventListener('resize', checkVisibility)
        }
      }
      
      window.addEventListener('scroll', checkVisibility)
      window.addEventListener('resize', checkVisibility)
      checkVisibility() // Check immediately
      
      return () => {
        window.removeEventListener('scroll', checkVisibility)
        window.removeEventListener('resize', checkVisibility)
      }
    }
  },
}

// Browser compatibility warnings
export function checkBrowserCompatibility(): {
  compatible: boolean
  warnings: string[]
  critical: string[]
} {
  const browser = getBrowserInfo()
  const features = detectFeatureSupport()
  
  const warnings: string[] = []
  const critical: string[] = []

  // Check for outdated browsers
  if (browser.name === 'chrome' && parseInt(browser.version) < 80) {
    warnings.push('Chrome browser is outdated. Please update for the best experience.')
  }
  
  if (browser.name === 'firefox' && parseInt(browser.version) < 75) {
    warnings.push('Firefox browser is outdated. Please update for the best experience.')
  }
  
  if (browser.name === 'safari' && parseInt(browser.version) < 13) {
    warnings.push('Safari browser is outdated. Please update for the best experience.')
  }

  // Check for critical features
  if (!features.fetch) {
    critical.push('This browser does not support the Fetch API.')
  }
  
  if (!features.promises) {
    critical.push('This browser does not support Promises.')
  }
  
  if (!features.localStorage) {
    warnings.push('Local storage is not available. Some features may not work properly.')
  }

  // Check for modern CSS features
  if (!features.flexbox) {
    critical.push('This browser does not support CSS Flexbox.')
  }
  
  if (!features.grid) {
    warnings.push('This browser does not support CSS Grid. Layout may appear different.')
  }

  return {
    compatible: critical.length === 0,
    warnings,
    critical,
  }
}

export default {
  getBrowserInfo,
  detectFeatureSupport,
  PolyfillLoader,
  cssCompatibility,
  breakpoints,
  getCurrentBreakpoint,
  createMediaQuery,
  performanceOptimizations,
  checkBrowserCompatibility,
}