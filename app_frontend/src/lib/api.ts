import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from "axios";
import { toast } from "react-hot-toast";
import { getSession } from "next-auth/react";

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface LoadingState {
  isLoading: boolean;
  error: ApiError | null;
}

// Cache configuration
interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class ApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig = {
    ttl: 5 * 60 * 1000, // 5 minutes default
    maxSize: 100,
  };

  set<T>(key: string, data: T, ttl?: number): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (!firstKey) return;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

// Retry configuration
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

class ApiClient {
  private client: AxiosInstance;
  private cache = new ApiCache();
  private loadingStates = new Map<string, boolean>();
  private retryConfig: RetryConfig = {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error: AxiosError) => {
      // Retry on network errors and 5xx status codes
      return (
        !error.response ||
        (error.response.status >= 500 && error.response.status < 600)
      );
    },
  };

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 15000, // Increased timeout for backend processing
      maxRedirects: 5, // Follow redirects automatically
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        // Add auth token if available - use NextAuth session instead of localStorage
        if (typeof window !== "undefined") {
          try {
            const session = await getSession();
            
            // Check for NextAuth session first
            if (session?.accessToken) {
              config.headers.Authorization = `Bearer ${session.accessToken}`;
            } else {
              // Fallback to localStorage for backward compatibility
              const token = localStorage.getItem("auth_token");
              if (token) {
                config.headers.Authorization = `Bearer ${token}`;
              }
            }
          } catch (error) {
            // If getSession fails, try localStorage as fallback
            const token = localStorage.getItem("auth_token");
            if (token) {
              config.headers.Authorization = `Bearer ${token}`;
            }
          }
        }

        // Add request ID for tracking
        config.headers["X-Request-ID"] = crypto.randomUUID();

        // Set loading state
        const requestKey = this.getRequestKey(config);
        this.setLoading(requestKey, true);

        // Security: Never log requests containing sensitive data
        if (process.env.NODE_ENV === "development") {
          const hasCredentials =
            config.url?.includes("/auth/") ||
            config.data?.password ||
            config.data?.token;
          if (!hasCredentials) {
            // Only log non-sensitive requests in development
          }
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor with retry logic
    this.client.interceptors.response.use(
      (response) => {
        // Clear loading state
        const requestKey = this.getRequestKey(response.config);
        this.setLoading(requestKey, false);
        return response;
      },
      async (error: AxiosError) => {
        const config = error.config as AxiosRequestConfig & {
          _retryCount?: number;
        };
        const requestKey = this.getRequestKey(config);
        this.setLoading(requestKey, false);

        // Retry logic
        if (this.shouldRetry(error, config)) {
          config._retryCount = (config._retryCount || 0) + 1;

          // Exponential backoff
          const delay =
            this.retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);
          await new Promise((resolve) => setTimeout(resolve, delay));

          return this.client(config);
        }

        const apiError: ApiError = {
          message: error.message || "An unexpected error occurred",
          status: error.response?.status,
          details: error.response?.data,
        };

        if (error.response?.data) {
          const errorData = error.response.data as any;
          apiError.message =
            errorData.detail || errorData.message || apiError.message;
          apiError.code = errorData.code;
        }

        // Handle different error types
        this.handleError(apiError);

        return Promise.reject(apiError);
      }
    );
  }

  private shouldRetry(
    error: AxiosError,
    config: AxiosRequestConfig & { _retryCount?: number }
  ): boolean {
    const retryCount = config._retryCount || 0;
    return (
      retryCount < this.retryConfig.retries &&
      config.method?.toLowerCase() === "get" && // Only retry GET requests
      this.retryConfig.retryCondition?.(error) === true
    );
  }

  private handleError(error: ApiError): void {
    // Handle connection errors (backend not running)
    if (
      !error.status ||
      error.message.includes("Network Error") ||
      error.message.includes("ECONNREFUSED")
    ) {
      // Don't show error toast for connection issues when backend is expected to be down
      return;
    }

    // Handle auth errors
    if (error.status === 401) {
      if (typeof window !== "undefined") {
        localStorage.removeItem("auth_token");
        toast.error("Session expired. Please login again.");
        window.location.href = "/auth/login";
      }
      return;
    }

    // Handle other errors with toast notifications
    if (error.status && error.status >= 400) {
      if (error.status >= 500) {
        toast.error("Server error. Please try again later.");
      } else if (error.status === 404) {
        toast.error("Resource not found");
      } else {
        toast.error(error.message);
      }
    }
  }

  private getRequestKey(config: AxiosRequestConfig): string {
    return `${config.method?.toUpperCase()}_${config.url}`;
  }

  private setLoading(key: string, loading: boolean): void {
    if (loading) {
      this.loadingStates.set(key, true);
    } else {
      this.loadingStates.delete(key);
    }
  }

  // Generic request methods with caching
  async get<T>(url: string, params?: any, useCache = true): Promise<T> {
    const cacheKey = `GET_${url}_${JSON.stringify(params || {})}`;

    // Check cache first
    if (useCache) {
      const cached = this.cache.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const response = await this.client.get(url, { params });
    const data = response.data;

    // Cache successful GET requests
    if (useCache && response.status === 200) {
      this.cache.set(cacheKey, data);
    }

    return data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    if (!process.env.NEXT_PUBLIC_API_URL) {
      throw new Error("Backend not available");
    }

    const response = await this.client.post(url, data);

    // Invalidate related cache entries
    this.invalidateCache(url);

    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.put(url, data);

    // Invalidate related cache entries
    this.invalidateCache(url);

    return response.data;
  }

  async patch<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.patch(url, data);

    // Invalidate related cache entries
    this.invalidateCache(url);

    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete(url);

    // Invalidate related cache entries
    this.invalidateCache(url);

    return response.data;
  }

  // File upload with progress
  async uploadFile<T>(
    url: string,
    file: File | Blob,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await this.client.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  // Optimistic updates
  async optimisticUpdate<T>(
    url: string,
    data: any,
    optimisticData: T,
    cacheKey?: string
  ): Promise<T> {
    // Set optimistic data immediately
    if (cacheKey) {
      this.cache.set(cacheKey, optimisticData);
    }

    try {
      const result = await this.put<T>(url, data);

      // Update cache with real data
      if (cacheKey) {
        this.cache.set(cacheKey, result);
      }

      return result;
    } catch (error) {
      // Revert optimistic update on error
      if (cacheKey) {
        this.cache.delete(cacheKey);
      }
      throw error;
    }
  }

  // Cache management
  private invalidateCache(url: string): void {
    // Remove cache entries that might be affected by this change
    const keysToDelete: string[] = [];

    // Simple pattern matching - could be more sophisticated
    const baseUrl = url.split("/").slice(0, -1).join("/");

    for (const key of Array.from(this.cache["cache"].keys())) {
      if (key.includes(baseUrl) || key.includes(url)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  // Loading state management
  isLoading(url: string, method = "GET"): boolean {
    const key = `${method.toUpperCase()}_${url}`;
    return this.loadingStates.has(key);
  }

  // Utility methods
  setAuthToken(token: string) {
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  }

  removeAuthToken() {
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  }

  // Cache utilities
  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache["cache"].size,
      keys: Array.from(this.cache["cache"].keys()),
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.get("/health", {}, false); // Don't cache health checks
      return true;
    } catch {
      return false;
    }
  }
}

export const apiClient = new ApiClient();
export default apiClient;
