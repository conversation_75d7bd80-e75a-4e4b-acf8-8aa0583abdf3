import React from 'react'

// Circuit breaker pattern for API resilience
export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface CircuitBreakerConfig {
  failureThreshold: number
  recoveryTimeout: number
  monitoringWindow: number
  expectedFailureRate: number
}

export interface CircuitBreakerMetrics {
  state: CircuitState
  failureCount: number
  successCount: number
  totalRequests: number
  lastFailureTime?: number
  nextAttemptTime?: number
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED
  private failureCount = 0
  private successCount = 0
  private totalRequests = 0
  private lastFailureTime?: number
  private nextAttemptTime?: number
  private config: CircuitBreakerConfig

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringWindow: 300000, // 5 minutes
      expectedFailureRate: 0.5, // 50%
      ...config,
    }
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN
      } else {
        throw new Error('Circuit breaker is OPEN - operation rejected')
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess(): void {
    this.successCount++
    this.totalRequests++
    
    if (this.state === CircuitState.HALF_OPEN) {
      // Reset circuit breaker after successful operation in half-open state
      this.reset()
    }
  }

  private onFailure(): void {
    this.failureCount++
    this.totalRequests++
    this.lastFailureTime = Date.now()

    if (this.shouldTrip()) {
      this.trip()
    }
  }

  private shouldTrip(): boolean {
    if (this.failureCount >= this.config.failureThreshold) {
      return true
    }

    const failureRate = this.failureCount / this.totalRequests
    return failureRate > this.config.expectedFailureRate && this.totalRequests >= this.config.failureThreshold
  }

  private shouldAttemptReset(): boolean {
    return (
      this.nextAttemptTime !== undefined &&
      Date.now() >= this.nextAttemptTime
    )
  }

  private trip(): void {
    this.state = CircuitState.OPEN
    this.nextAttemptTime = Date.now() + this.config.recoveryTimeout
  }

  private reset(): void {
    this.state = CircuitState.CLOSED
    this.failureCount = 0
    this.successCount = 0
    this.totalRequests = 0
    this.lastFailureTime = undefined
    this.nextAttemptTime = undefined
  }

  getMetrics(): CircuitBreakerMetrics {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      totalRequests: this.totalRequests,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime,
    }
  }

  forceOpen(): void {
    this.state = CircuitState.OPEN
    this.nextAttemptTime = Date.now() + this.config.recoveryTimeout
  }

  forceClose(): void {
    this.reset()
  }
}

// Circuit breaker manager for different services
class CircuitBreakerManager {
  private breakers = new Map<string, CircuitBreaker>()

  getBreaker(serviceName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.breakers.has(serviceName)) {
      this.breakers.set(serviceName, new CircuitBreaker(config))
    }
    return this.breakers.get(serviceName)!
  }

  getAllMetrics(): Record<string, CircuitBreakerMetrics> {
    const metrics: Record<string, CircuitBreakerMetrics> = {}
    for (const [serviceName, breaker] of this.breakers) {
      metrics[serviceName] = breaker.getMetrics()
    }
    return metrics
  }

  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.forceClose()
    }
  }
}

export const circuitBreakerManager = new CircuitBreakerManager()

// Enhanced API client with circuit breaker
export class ResilientApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string, defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders,
    }
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {},
    serviceName = 'default'
  ): Promise<T> {
    const circuitBreaker = circuitBreakerManager.getBreaker(serviceName, {
      failureThreshold: 3,
      recoveryTimeout: 30000, // 30 seconds
      expectedFailureRate: 0.5,
    })

    return circuitBreaker.execute(async () => {
      const url = `${this.baseURL}${endpoint}`
      const config: RequestInit = {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
      }

      // Add auth token if available
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          }
        }
      }

      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          errorData.message || 
          errorData.detail || 
          `HTTP ${response.status}: ${response.statusText}`
        )
      }

      return response.json()
    })
  }

  async get<T>(endpoint: string, serviceName?: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' }, serviceName)
  }

  async post<T>(
    endpoint: string,
    data?: any,
    serviceName?: string
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      serviceName
    )
  }

  async put<T>(
    endpoint: string,
    data?: any,
    serviceName?: string
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      serviceName
    )
  }

  async delete<T>(endpoint: string, serviceName?: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' }, serviceName)
  }
}

// Health check utilities
export class HealthChecker {
  private intervals = new Map<string, NodeJS.Timeout>()

  startHealthCheck(
    serviceName: string,
    healthEndpoint: string,
    interval = 60000 // 1 minute
  ): void {
    // Clear existing interval
    this.stopHealthCheck(serviceName)

    const check = async () => {
      try {
        const client = new ResilientApiClient(
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        )
        await client.get(healthEndpoint, `${serviceName}-health`)
        console.log(`Health check passed for ${serviceName}`)
      } catch (error) {
        console.error(`Health check failed for ${serviceName}:`, error)
        
        // Force open circuit breaker if health check fails
        const breaker = circuitBreakerManager.getBreaker(serviceName)
        breaker.forceOpen()
      }
    }

    // Initial check
    check()

    // Schedule periodic checks
    const intervalId = setInterval(check, interval)
    this.intervals.set(serviceName, intervalId)
  }

  stopHealthCheck(serviceName: string): void {
    const intervalId = this.intervals.get(serviceName)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(serviceName)
    }
  }

  stopAllHealthChecks(): void {
    for (const serviceName of this.intervals.keys()) {
      this.stopHealthCheck(serviceName)
    }
  }
}

export const healthChecker = new HealthChecker()

// React hooks for circuit breaker monitoring
export function useCircuitBreakerMetrics(serviceName?: string) {
  const [metrics, setMetrics] = React.useState<Record<string, CircuitBreakerMetrics>>({})

  React.useEffect(() => {
    const updateMetrics = () => {
      if (serviceName) {
        const breaker = circuitBreakerManager.getBreaker(serviceName)
        setMetrics({ [serviceName]: breaker.getMetrics() })
      } else {
        setMetrics(circuitBreakerManager.getAllMetrics())
      }
    }

    updateMetrics()
    const interval = setInterval(updateMetrics, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [serviceName])

  return metrics
}

export function useServiceHealth(serviceName: string, healthEndpoint: string) {
  const [isHealthy, setIsHealthy] = React.useState(true)
  const [lastCheck, setLastCheck] = React.useState<Date>()

  React.useEffect(() => {
    const checkHealth = async () => {
      try {
        const client = new ResilientApiClient(
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        )
        await client.get(healthEndpoint, `${serviceName}-health`)
        setIsHealthy(true)
      } catch (error) {
        setIsHealthy(false)
      } finally {
        setLastCheck(new Date())
      }
    }

    checkHealth()
    const interval = setInterval(checkHealth, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [serviceName, healthEndpoint])

  return { isHealthy, lastCheck }
}