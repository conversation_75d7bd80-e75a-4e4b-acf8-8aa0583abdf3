// Responsive design utilities

import { useEffect, useState } from 'react'

// Breakpoint definitions
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof breakpoints

// Screen size utilities
export function getScreenSize(): {
  width: number
  height: number
  availWidth: number
  availHeight: number
} {
  if (typeof window === 'undefined') {
    return {
      width: 1920,
      height: 1080,
      availWidth: 1920,
      availHeight: 1080,
    }
  }

  return {
    width: window.innerWidth,
    height: window.innerHeight,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
  }
}

export function getCurrentBreakpoint(): Breakpoint | null {
  const { width } = getScreenSize()
  
  if (width >= breakpoints['2xl']) return '2xl'
  if (width >= breakpoints.xl) return 'xl'
  if (width >= breakpoints.lg) return 'lg'
  if (width >= breakpoints.md) return 'md'
  if (width >= breakpoints.sm) return 'sm'
  
  return null
}

export function isBreakpoint(breakpoint: Breakpoint): boolean {
  const { width } = getScreenSize()
  return width >= breakpoints[breakpoint]
}

export function isBetweenBreakpoints(min: Breakpoint, max: Breakpoint): boolean {
  const { width } = getScreenSize()
  return width >= breakpoints[min] && width < breakpoints[max]
}

// React hooks for responsive design
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState(getScreenSize)

  useEffect(() => {
    const handleResize = () => {
      setScreenSize(getScreenSize())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return screenSize
}

export function useBreakpoint() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint | null>(getCurrentBreakpoint)

  useEffect(() => {
    const handleResize = () => {
      setCurrentBreakpoint(getCurrentBreakpoint())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isAbove = (breakpoint: Breakpoint): boolean => {
    if (!currentBreakpoint) return false
    return breakpoints[currentBreakpoint] >= breakpoints[breakpoint]
  }

  const isBelow = (breakpoint: Breakpoint): boolean => {
    if (!currentBreakpoint) return true
    return breakpoints[currentBreakpoint] < breakpoints[breakpoint]
  }

  const isExactly = (breakpoint: Breakpoint): boolean => {
    return currentBreakpoint === breakpoint
  }

  return {
    current: currentBreakpoint,
    isAbove,
    isBelow,
    isExactly,
    isMobile: !currentBreakpoint || currentBreakpoint === 'sm',
    isTablet: currentBreakpoint === 'md' || currentBreakpoint === 'lg',
    isDesktop: currentBreakpoint === 'xl' || currentBreakpoint === '2xl',
  }
}

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia(query)
    setMatches(mediaQuery.matches)

    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [query])

  return matches
}

// Device detection
export function useDeviceType() {
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
    const isTablet = /ipad|android(?!.*mobile)|tablet/i.test(userAgent)
    
    if (isMobile) {
      setDeviceType('mobile')
    } else if (isTablet) {
      setDeviceType('tablet')
    } else {
      setDeviceType('desktop')
    }
  }, [])

  return deviceType
}

// Orientation detection
export function useOrientation() {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait')

  useEffect(() => {
    const handleOrientationChange = () => {
      const { width, height } = getScreenSize()
      setOrientation(width > height ? 'landscape' : 'portrait')
    }

    handleOrientationChange() // Set initial value
    window.addEventListener('resize', handleOrientationChange)
    window.addEventListener('orientationchange', handleOrientationChange)

    return () => {
      window.removeEventListener('resize', handleOrientationChange)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [])

  return orientation
}

// Touch capability detection
export function useTouchCapability() {
  const [hasTouch, setHasTouch] = useState(false)
  const [isPrimaryTouch, setIsPrimaryTouch] = useState(false)

  useEffect(() => {
    const touchCapable = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    setHasTouch(touchCapable)

    // Detect if touch is the primary input method
    const primaryTouch = window.matchMedia('(pointer: coarse)').matches
    setIsPrimaryTouch(primaryTouch)
  }, [])

  return {
    hasTouch,
    isPrimaryTouch,
    hasHover: !isPrimaryTouch,
  }
}

// Safe area insets (for mobile devices with notches, etc.)
export function useSafeAreaInsets() {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  })

  useEffect(() => {
    const updateInsets = () => {
      const style = getComputedStyle(document.documentElement)
      
      setInsets({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      })
    }

    updateInsets()
    window.addEventListener('resize', updateInsets)
    window.addEventListener('orientationchange', updateInsets)

    return () => {
      window.removeEventListener('resize', updateInsets)
      window.removeEventListener('orientationchange', updateInsets)
    }
  }, [])

  return insets
}

// Container query simulation
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerSize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        })
      }
    })

    resizeObserver.observe(containerRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [containerRef])

  const isContainerAbove = (width: number): boolean => containerSize.width >= width
  const isContainerBelow = (width: number): boolean => containerSize.width < width

  return {
    containerSize,
    isContainerAbove,
    isContainerBelow,
  }
}

// Responsive value helper
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint | 'base', T>>): T | undefined {
  const { current } = useBreakpoint()
  
  // Return the most specific value available
  if (current && values[current] !== undefined) {
    return values[current]
  }
  
  // Fallback through breakpoints in descending order
  const fallbackOrder: (Breakpoint | 'base')[] = ['xl', 'lg', 'md', 'sm', 'base']
  
  for (const bp of fallbackOrder) {
    if (values[bp] !== undefined) {
      return values[bp]
    }
  }
  
  return undefined
}

// Aspect ratio utilities
export function calculateAspectRatio(width: number, height: number): string {
  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
  const divisor = gcd(width, height)
  return `${width / divisor}/${height / divisor}`
}

export function useAspectRatio(targetRatio: string) {
  const [ratio, width, height] = targetRatio.split('/').map(Number)
  const actualRatio = width / height

  const calculateDimensions = (containerWidth: number, containerHeight: number) => {
    const containerRatio = containerWidth / containerHeight

    if (containerRatio > actualRatio) {
      // Container is wider than target ratio
      return {
        width: containerHeight * actualRatio,
        height: containerHeight,
      }
    } else {
      // Container is taller than target ratio
      return {
        width: containerWidth,
        height: containerWidth / actualRatio,
      }
    }
  }

  return { calculateDimensions, targetRatio: actualRatio }
}

// Print media detection
export function usePrintMedia() {
  const [isPrint, setIsPrint] = useState(false)

  useEffect(() => {
    const printMediaQuery = window.matchMedia('print')
    
    const handleChange = (event: MediaQueryListEvent) => {
      setIsPrint(event.matches)
    }

    setIsPrint(printMediaQuery.matches)
    printMediaQuery.addEventListener('change', handleChange)

    return () => {
      printMediaQuery.removeEventListener('change', handleChange)
    }
  }, [])

  return isPrint
}

// Reduced motion preference
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    
    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    setPrefersReducedMotion(mediaQuery.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [])

  return prefersReducedMotion
}

// High contrast preference
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    
    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersHighContrast(event.matches)
    }

    setPrefersHighContrast(mediaQuery.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [])

  return prefersHighContrast
}

// Network information (experimental)
export function useNetworkInfo() {
  const [networkInfo, setNetworkInfo] = useState<{
    effectiveType: string
    downlink: number
    rtt: number
  } | null>(null)

  useEffect(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      const updateNetworkInfo = () => {
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
        })
      }

      updateNetworkInfo()
      connection.addEventListener('change', updateNetworkInfo)

      return () => {
        connection.removeEventListener('change', updateNetworkInfo)
      }
    }
  }, [])

  return networkInfo
}

export default {
  breakpoints,
  getScreenSize,
  getCurrentBreakpoint,
  isBreakpoint,
  isBetweenBreakpoints,
  useScreenSize,
  useBreakpoint,
  useMediaQuery,
  useDeviceType,
  useOrientation,
  useTouchCapability,
  useSafeAreaInsets,
  useContainerQuery,
  useResponsiveValue,
  calculateAspectRatio,
  useAspectRatio,
  usePrintMedia,
  useReducedMotion,
  useHighContrast,
  useNetworkInfo,
}