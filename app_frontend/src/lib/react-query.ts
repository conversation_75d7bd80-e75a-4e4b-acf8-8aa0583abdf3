import { QueryClient, DefaultOptions } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'

// Default query options
const defaultOptions: DefaultOptions = {
  queries: {
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false
      }
      // Retry up to 3 times for other errors
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  mutations: {
    retry: false, // Don't retry mutations by default
    onError: (error: any) => {
      // Global error handling for mutations
      if (error?.message && error.status !== 401) {
        toast.error(error.message)
      }
    },
  },
}

// Create query client
export const queryClient = new QueryClient({
  defaultOptions,
})

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth
  auth: {
    user: () => ['auth', 'user'] as const,
    session: () => ['auth', 'session'] as const,
  },
  
  // Books
  books: {
    all: () => ['books'] as const,
    lists: () => [...queryKeys.books.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.books.lists(), filters] as const,
    details: () => [...queryKeys.books.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.books.details(), id] as const,
    analytics: (id: string) => [...queryKeys.books.detail(id), 'analytics'] as const,
  },
  
  // Analytics
  analytics: {
    all: () => ['analytics'] as const,
    overview: (dateRange?: { from: string; to: string }) => 
      [...queryKeys.analytics.all(), 'overview', dateRange] as const,
    revenue: (dateRange?: { from: string; to: string }) => 
      [...queryKeys.analytics.all(), 'revenue', dateRange] as const,
    audience: (dateRange?: { from: string; to: string }) => 
      [...queryKeys.analytics.all(), 'audience', dateRange] as const,
  },
  
  // Trends
  trends: {
    all: () => ['trends'] as const,
    lists: () => [...queryKeys.trends.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.trends.lists(), filters] as const,
    details: () => [...queryKeys.trends.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.trends.details(), id] as const,
    current: () => [...queryKeys.trends.all(), 'current'] as const,
    categories: () => [...queryKeys.trends.all(), 'categories'] as const,
    market: (category: string) => [...queryKeys.trends.all(), 'market', category] as const,
    predictions: (category?: string, timeframe?: string) => 
      [...queryKeys.trends.all(), 'predictions', category, timeframe] as const,
    realtime: () => [...queryKeys.trends.all(), 'realtime'] as const,
    search: (query: string) => [...queryKeys.trends.all(), 'search', query] as const,
  },
  
  // Publications
  publications: {
    all: () => ['publications'] as const,
    lists: () => [...queryKeys.publications.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.publications.lists(), filters] as const,
    details: () => [...queryKeys.publications.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.publications.details(), id] as const,
    metrics: (id: string) => [...queryKeys.publications.detail(id), 'metrics'] as const,
    status: (id: string) => [...queryKeys.publications.detail(id), 'status'] as const,
    statistics: () => [...queryKeys.publications.all(), 'statistics'] as const,
  },
  
  // Settings
  settings: {
    all: () => ['settings'] as const,
    profile: () => [...queryKeys.settings.all(), 'profile'] as const,
    preferences: () => [...queryKeys.settings.all(), 'preferences'] as const,
    publishing: () => [...queryKeys.settings.all(), 'publishing'] as const,
    integrations: () => [...queryKeys.settings.all(), 'integrations'] as const,
    security: () => [...queryKeys.settings.all(), 'security'] as const,
    sessions: () => [...queryKeys.settings.all(), 'sessions'] as const,
    apiKeys: () => [...queryKeys.settings.all(), 'api-keys'] as const,
  },
  
  // Monitoring
  monitoring: {
    all: () => ['monitoring'] as const,
    verl: () => [...queryKeys.monitoring.all(), 'verl'] as const,
    system: () => [...queryKeys.monitoring.all(), 'system'] as const,
    performance: (timeRange?: string) => [...queryKeys.monitoring.all(), 'performance', timeRange] as const,
    alerts: () => [...queryKeys.monitoring.all(), 'alerts'] as const,
    realtime: (type: string) => [...queryKeys.monitoring.all(), type, 'realtime'] as const,
  },
  
  // Agents
  agents: {
    all: () => ['agents'] as const,
    tasks: () => [...queryKeys.agents.all(), 'tasks'] as const,
    tasksList: (filters: Record<string, any>) => [...queryKeys.agents.tasks(), 'list', filters] as const,
    task: (id: string) => [...queryKeys.agents.all(), 'task', id] as const,
    taskStatus: (id: string) => [...queryKeys.agents.task(id), 'status'] as const,
    statistics: () => [...queryKeys.agents.all(), 'statistics'] as const,
    realtime: (taskIds: string[]) => [...queryKeys.agents.all(), 'realtime', taskIds] as const,
  },
} as const

// Utility functions for query management
export const queryUtils = {
  // Invalidate all queries for a specific entity
  invalidateBooks: () => queryClient.invalidateQueries({ queryKey: queryKeys.books.all() }),
  invalidateAnalytics: () => queryClient.invalidateQueries({ queryKey: queryKeys.analytics.all() }),
  invalidateTrends: () => queryClient.invalidateQueries({ queryKey: queryKeys.trends.all() }),
  invalidatePublications: () => queryClient.invalidateQueries({ queryKey: queryKeys.publications.all() }),
  invalidateSettings: () => queryClient.invalidateQueries({ queryKey: queryKeys.settings.all() }),
  invalidateMonitoring: () => queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.all() }),
  invalidateAgents: () => queryClient.invalidateQueries({ queryKey: queryKeys.agents.all() }),
  
  // Remove specific queries
  removeBook: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.books.detail(id) }),
  removePublication: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.publications.detail(id) }),
  removeAgentTask: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.agents.task(id) }),
  
  // Set query data (for optimistic updates)
  setBookData: (id: string, data: any) => 
    queryClient.setQueryData(queryKeys.books.detail(id), data),
  setPublicationData: (id: string, data: any) => 
    queryClient.setQueryData(queryKeys.publications.detail(id), data),
  setAgentTaskData: (id: string, data: any) => 
    queryClient.setQueryData(queryKeys.agents.task(id), data),
  
  // Update query data
  updateBookData: (id: string, updater: (old: any) => any) => 
    queryClient.setQueryData(queryKeys.books.detail(id), updater),
  updatePublicationData: (id: string, updater: (old: any) => any) => 
    queryClient.setQueryData(queryKeys.publications.detail(id), updater),
  updateAgentTaskData: (id: string, updater: (old: any) => any) => 
    queryClient.setQueryData(queryKeys.agents.task(id), updater),
  
  // Prefetch queries
  prefetchBook: (id: string) => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.books.detail(id),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get(`/api/books/${id}`)),
      staleTime: 30 * 1000, // 30 seconds
    }),
  prefetchPublication: (id: string) => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.publications.detail(id),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get(`/api/publications/${id}`)),
      staleTime: 30 * 1000, // 30 seconds
    }),
  prefetchVerlMonitoring: () => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.monitoring.verl(),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get('/api/monitoring/verl')),
      staleTime: 30 * 1000, // 30 seconds
    }),
  prefetchSystemMonitoring: () => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.monitoring.system(),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get('/api/monitoring/system')),
      staleTime: 60 * 1000, // 1 minute
    }),
  prefetchAgentTask: (id: string) => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.agents.task(id),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get(`/api/agents/tasks/${id}`)),
      staleTime: 10 * 1000, // 10 seconds
    }),
  prefetchAgentStatistics: () => 
    queryClient.prefetchQuery({
      queryKey: queryKeys.agents.statistics(),
      queryFn: () => import('./api').then(({ apiClient }) => apiClient.get('/api/agents/statistics')),
      staleTime: 5 * 60 * 1000, // 5 minutes
    }),
}

// Error boundary for query errors
export class QueryErrorBoundary extends Error {
  constructor(
    message: string,
    public queryKey: unknown[],
    public originalError: unknown
  ) {
    super(message)
    this.name = 'QueryErrorBoundary'
  }
}

// Global error handler for React Query
export const handleQueryError = (error: unknown, queryKey: unknown[]) => {
  console.error('Query error:', { error, queryKey })
  
  // Don't show toast for auth errors (handled by API client)
  if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
    return
  }
  
  // Log error to monitoring service in production
  if (process.env.NODE_ENV === 'production') {
    // TODO: Send to error monitoring service (Sentry, etc.)
  }
  
  throw new QueryErrorBoundary(
    'An error occurred while fetching data',
    queryKey as unknown[],
    error
  )
}

// Optimistic update helpers
export const optimisticUpdateHelpers = {
  // Add item to list
  addToList: <T extends { id: string }>(
    queryKey: unknown[],
    newItem: T,
    addToStart = true
  ) => {
    queryClient.setQueryData(queryKey, (old: T[] = []) => 
      addToStart ? [newItem, ...old] : [...old, newItem]
    )
  },
  
  // Update item in list
  updateInList: <T extends { id: string }>(
    queryKey: unknown[],
    itemId: string,
    updater: (old: T) => T
  ) => {
    queryClient.setQueryData(queryKey, (old: T[] = []) =>
      old.map(item => item.id === itemId ? updater(item) : item)
    )
  },
  
  // Remove item from list
  removeFromList: <T extends { id: string }>(
    queryKey: unknown[],
    itemId: string
  ) => {
    queryClient.setQueryData(queryKey, (old: T[] = []) =>
      old.filter(item => item.id !== itemId)
    )
  },
}

export default queryClient