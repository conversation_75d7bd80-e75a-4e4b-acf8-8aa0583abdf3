// Security utilities for credential protection

/**
 * Sanitize objects to remove sensitive information before logging
 */
export function sanitizeForLogging(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }

  const sensitiveKeys = [
    'password',
    'token',
    'secret',
    'key',
    'auth',
    'authorization',
    'credential',
    'access_token',
    'refresh_token',
    'jwt'
  ]

  const sanitized = { ...obj }
  
  for (const key in sanitized) {
    if (sensitiveKeys.some(sensitiveKey => 
      key.toLowerCase().includes(sensitiveKey.toLowerCase())
    )) {
      sanitized[key] = '[REDACTED]'
    } else if (typeof sanitized[key] === 'object') {
      sanitized[key] = sanitizeForLogging(sanitized[key])
    }
  }

  return sanitized
}

/**
 * Secure console wrapper that automatically sanitizes sensitive data
 */
export const secureConsole = {
  log: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(...args.map(sanitizeForLogging))
    }
  },
  error: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(...args.map(sanitizeForLogging))
    }
  },
  warn: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(...args.map(sanitizeForLogging))
    }
  }
}

/**
 * Clear sensitive URL parameters
 */
export function clearSensitiveUrlParams(): void {
  if (typeof window === 'undefined') return

  const url = new URL(window.location.href)
  const sensitiveParams = ['password', 'token', 'secret', 'key', 'auth']
  
  let hasChanges = false
  sensitiveParams.forEach(param => {
    if (url.searchParams.has(param)) {
      url.searchParams.delete(param)
      hasChanges = true
    }
  })

  // Also check for email if accompanied by password-like params
  if (url.searchParams.has('email') && 
      (url.search.includes('password') || url.search.includes('token'))) {
    url.searchParams.delete('email')
    hasChanges = true
  }

  if (hasChanges) {
    window.history.replaceState({}, '', url.toString())
  }
}

/**
 * Disable development tools in production
 */
export function disableDevToolsInProduction(): void {
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // Disable right-click context menu
    document.addEventListener('contextmenu', (e) => e.preventDefault())
    
    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    document.addEventListener('keydown', (e) => {
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
        (e.ctrlKey && e.key === 'U')
      ) {
        e.preventDefault()
      }
    })
  }
}