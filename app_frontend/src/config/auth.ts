/**
 * Centralized authentication configuration
 * Contains route patterns, role definitions, redirect URLs, and permission configurations
 */

import type { UserRole, Permission } from "@/lib/auth/permissionHelpers"

// Authentication routes configuration
export const AUTH_ROUTES = {
  LOGIN: "/auth/login",
  REGISTER: "/auth/register",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  VERIFY_EMAIL: "/auth/verify-email",
  ERROR: "/auth/error",
} as const

// Protected route patterns (require authentication)
export const PROTECTED_ROUTE_PATTERNS = [
  /^\/dashboard/, // All dashboard routes
  /^\/profile/, // Profile pages
  /^\/settings/, // Settings pages
  /^\/books\/(?!public)/, // Book pages (except public)
  /^\/analytics/, // Analytics pages
  /^\/trends/, // Trends pages
  /^\/admin/, // Admin routes
] as const

// Public route patterns (no authentication required)
export const PUBLIC_ROUTE_PATTERNS = [
  /^\/$/, // Home page
  /^\/auth/, // All auth pages
  /^\/about/, // About page
  /^\/contact/, // Contact page
  /^\/privacy/, // Privacy policy
  /^\/terms/, // Terms of service
  /^\/help/, // Help pages
  /^\/books\/public/, // Public book pages
  /^\/api\/public/, // Public API routes
] as const

// Role-specific route patterns
export const ROLE_PROTECTED_ROUTES = {
  admin: [
    /^\/admin/, // Admin dashboard
    /^\/dashboard\/admin/, // Admin sections
    /^\/users/, // User management
    /^\/settings\/system/, // System settings
  ],
  editor: [
    /^\/dashboard\/editor/, // Editor dashboard
    /^\/content\/manage/, // Content management
    /^\/books\/review/, // Book review
  ],
  author: [
    /^\/dashboard\/author/, // Author dashboard
    /^\/books\/create/, // Create books
    /^\/books\/edit/, // Edit own books
  ],
} as const

// Default redirect URLs by role
export const DEFAULT_REDIRECTS = {
  afterLogin: {
    admin: "/dashboard/admin",
    editor: "/dashboard/editor", 
    author: "/dashboard/author",
    user: "/dashboard",
    guest: "/",
  },
  afterLogout: "/",
  unauthenticated: "/auth/login",
  unauthorized: "/auth/error?error=AccessDenied",
} as const

// Session configuration
export const SESSION_CONFIG = {
  maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
  updateAge: 24 * 60 * 60, // 24 hours in seconds
  cookieName: "next-auth.session-token",
  secureCookies: process.env.NODE_ENV === "production",
  sameSite: "lax" as const,
} as const

// Role hierarchy and permissions (matches permissionHelpers.ts)
export const ROLES = {
  GUEST: "guest" as UserRole,
  USER: "user" as UserRole,
  AUTHOR: "author" as UserRole,
  EDITOR: "editor" as UserRole,
  ADMIN: "admin" as UserRole,
} as const

export const PERMISSIONS = {
  READ: "read" as Permission,
  WRITE: "write" as Permission,
  DELETE: "delete" as Permission,
  ADMIN: "admin" as Permission,
  MANAGE_USERS: "manage_users" as Permission,
  MANAGE_CONTENT: "manage_content" as Permission,
  PUBLISH: "publish" as Permission,
  EDIT_ALL: "edit_all" as Permission,
  VIEW_ANALYTICS: "view_analytics" as Permission,
} as const

// Feature flags for authentication features
export const AUTH_FEATURES = {
  // OAuth providers
  GOOGLE_LOGIN: true,
  APPLE_LOGIN: true,
  GITHUB_LOGIN: false, // Disabled per requirements
  FACEBOOK_LOGIN: false, // Disabled per requirements
  
  // Authentication features
  EMAIL_VERIFICATION: true,
  PASSWORD_RESET: true,
  REMEMBER_ME: true,
  TWO_FACTOR_AUTH: false, // Future feature
  
  // Security features
  RATE_LIMITING: true,
  SESSION_TIMEOUT: true,
  CONCURRENT_SESSIONS: false, // Allow multiple sessions
  
  // Role-based features
  ROLE_INHERITANCE: true,
  CUSTOM_PERMISSIONS: true,
  RESOURCE_BASED_ACCESS: true,
} as const

// Rate limiting configuration
export const RATE_LIMITS = {
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 5,
    blockDuration: 30 * 60 * 1000, // 30 minutes
  },
  registration: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 3,
    blockDuration: 60 * 60 * 1000, // 1 hour
  },
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 3,
    blockDuration: 60 * 60 * 1000, // 1 hour
  },
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 100,
  },
} as const

// Navigation configuration based on roles
export const NAVIGATION_CONFIG = {
  dashboard: {
    admin: [
      { label: "Dashboard", href: "/dashboard", icon: "LayoutDashboard" },
      { label: "Users", href: "/dashboard/users", icon: "Users" },
      { label: "Content", href: "/dashboard/content", icon: "FileText" },
      { label: "Analytics", href: "/dashboard/analytics", icon: "BarChart" },
      { label: "Settings", href: "/dashboard/settings", icon: "Settings" },
      { label: "System", href: "/dashboard/system", icon: "Server" },
    ],
    editor: [
      { label: "Dashboard", href: "/dashboard", icon: "LayoutDashboard" },
      { label: "Content", href: "/dashboard/content", icon: "FileText" },
      { label: "Reviews", href: "/dashboard/reviews", icon: "CheckCircle" },
      { label: "Analytics", href: "/dashboard/analytics", icon: "BarChart" },
      { label: "Settings", href: "/dashboard/settings", icon: "Settings" },
    ],
    author: [
      { label: "Dashboard", href: "/dashboard", icon: "LayoutDashboard" },
      { label: "My Books", href: "/dashboard/books", icon: "Book" },
      { label: "Create", href: "/dashboard/books/new", icon: "Plus" },
      { label: "Analytics", href: "/dashboard/analytics", icon: "BarChart" },
      { label: "Settings", href: "/dashboard/settings", icon: "Settings" },
    ],
    user: [
      { label: "Dashboard", href: "/dashboard", icon: "LayoutDashboard" },
      { label: "Library", href: "/dashboard/library", icon: "BookOpen" },
      { label: "Settings", href: "/dashboard/settings", icon: "Settings" },
    ],
  },
} as const

// Error messages for authentication
export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: "Invalid email or password",
  USER_NOT_FOUND: "No account found with this email",
  EMAIL_ALREADY_EXISTS: "An account with this email already exists",
  WEAK_PASSWORD: "Password must be at least 8 characters with uppercase, lowercase, and numbers",
  INVALID_EMAIL: "Please enter a valid email address",
  NETWORK_ERROR: "Network error. Please check your connection and try again",
  SERVER_ERROR: "Server error. Please try again later",
  SESSION_EXPIRED: "Your session has expired. Please log in again",
  ACCESS_DENIED: "You don't have permission to access this resource",
  RATE_LIMIT_EXCEEDED: "Too many attempts. Please try again later",
  EMAIL_NOT_VERIFIED: "Please verify your email address before continuing",
  ACCOUNT_LOCKED: "Your account has been temporarily locked",
  OAUTH_ERROR: "Authentication failed. Please try again",
  PROVIDER_NOT_FOUND: "Authentication provider not found",
  CALLBACK_ERROR: "Authentication callback failed",
} as const

// Loading messages for different authentication states
export const LOADING_MESSAGES = {
  AUTHENTICATING: "Authenticating...",
  CHECKING_SESSION: "Checking your session...",
  LOGGING_IN: "Logging you in...",
  LOGGING_OUT: "Logging you out...",
  REDIRECTING: "Redirecting...",
  LOADING_PROFILE: "Loading your profile...",
  VERIFYING_EMAIL: "Verifying your email...",
  RESETTING_PASSWORD: "Resetting your password...",
} as const

// Security configuration
export const SECURITY_CONFIG = {
  passwordRequirements: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  sessionSecurity: {
    regenerateOnLogin: true,
    invalidateOnRoleChange: true,
    checkOrigin: true,
    secureHeaders: true,
  },
  csrfProtection: {
    enabled: true,
    cookieName: "__Host-next-auth.csrf-token",
    sameSite: "lax",
  },
} as const

// API endpoints configuration
export const API_ENDPOINTS = {
  auth: {
    login: "/api/auth/signin",
    logout: "/api/auth/signout",
    session: "/api/auth/session",
    csrf: "/api/auth/csrf",
    providers: "/api/auth/providers",
    callback: "/api/auth/callback",
  },
  user: {
    profile: "/api/user/profile",
    preferences: "/api/user/preferences", 
    sessions: "/api/user/sessions",
  },
} as const

// Utility functions for configuration
export function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTE_PATTERNS.some(pattern => pattern.test(pathname))
}

export function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTE_PATTERNS.some(pattern => pattern.test(pathname))
}

export function requiresRole(pathname: string, role: UserRole): boolean {
  const roleRoutes = ROLE_PROTECTED_ROUTES[role]
  if (!roleRoutes) return false
  return roleRoutes.some(pattern => pattern.test(pathname))
}

export function getDefaultRedirect(role: UserRole, context: "afterLogin" | "afterLogout" = "afterLogin"): string {
  if (context === "afterLogout") {
    return DEFAULT_REDIRECTS.afterLogout
  }
  return DEFAULT_REDIRECTS.afterLogin[role] || DEFAULT_REDIRECTS.afterLogin.user
}

export function getNavigationForRole(role: UserRole) {
  return NAVIGATION_CONFIG.dashboard[role] || NAVIGATION_CONFIG.dashboard.user
}

export function isFeatureEnabled(feature: keyof typeof AUTH_FEATURES): boolean {
  return AUTH_FEATURES[feature]
}

// Environment-specific configuration
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  isTest: process.env.NODE_ENV === "test",
  apiUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
  frontendUrl: process.env.NEXTAUTH_URL || "http://localhost:3000",
  debugMode: process.env.NODE_ENV === "development" && process.env.DEBUG_AUTH === "true",
} as const

// Export all configurations as a single object for convenience
export const AUTH_CONFIG = {
  routes: AUTH_ROUTES,
  protectedPatterns: PROTECTED_ROUTE_PATTERNS,
  publicPatterns: PUBLIC_ROUTE_PATTERNS,
  roleProtectedRoutes: ROLE_PROTECTED_ROUTES,
  defaultRedirects: DEFAULT_REDIRECTS,
  session: SESSION_CONFIG,
  roles: ROLES,
  permissions: PERMISSIONS,
  features: AUTH_FEATURES,
  rateLimits: RATE_LIMITS,
  navigation: NAVIGATION_CONFIG,
  errors: AUTH_ERRORS,
  loadingMessages: LOADING_MESSAGES,
  security: SECURITY_CONFIG,
  api: API_ENDPOINTS,
  env: ENV_CONFIG,
} as const

export type AuthConfig = typeof AUTH_CONFIG