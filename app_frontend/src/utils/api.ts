// API utility functions for the publishing platform

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

class ApiClient {
  private baseURL: string;
  
  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const config: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      };
      
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      return {
        data,
        success: true,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false,
      };
    }
  }
  
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }
  
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create default API client instance
export const api = new ApiClient();

// Export types for use in components
export type { ApiResponse };

// Specific API functions
export const apiClient = {
  // Books
  getBooks: () => api.get('/api/books'),
  createBook: (data: any) => api.post('/api/books', data),
  updateBook: (id: string, data: any) => api.put(`/api/books/${id}`, data),
  deleteBook: (id: string) => api.delete(`/api/books/${id}`),
  
  // Analytics
  getAnalytics: () => api.get('/api/analytics'),
  getUserAnalytics: (userId: string) => api.get(`/api/analytics/users/${userId}`),
  
  // Trends
  getTrends: () => api.get('/api/trends'),
  createTrend: (data: any) => api.post('/api/trends', data),
  
  // Publications
  getPublications: () => api.get('/api/publications'),
  publishBook: (data: any) => api.post('/api/publications', data),
  
  // Health check
  healthCheck: () => api.get('/health'),
};

export default api;