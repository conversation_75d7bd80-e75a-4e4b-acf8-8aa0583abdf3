import NextAuth, { DefaultSession } from "next-auth"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      email: string;
      full_name?: string | null;
      avatar_url?: string | null;
      role?: string;
      subscription_tier?: string;
      preferences?: {
        theme?: string;
        notifications?: {
          email?: boolean;
          push?: boolean;
          marketing?: boolean;
        };
        publishing?: {
          autoPublish?: boolean;
          defaultCategory?: string;
          targetAudience?: string;
        };
      };
      created_at?: string;
      updated_at?: string;
      // Keep NextAuth defaults
      name?: string | null;
      image?: string | null;
    } & DefaultSession["user"];
    accessToken?: string;
    error?: string;
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id: string;
    // Add other properties from DefaultUser if needed
  }
}

// If you are using JWTs for sessions, you may need to add `id` to the `JWT` type as well
import { JWT } from "next-auth/jwt"

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    id: string;
    accessToken?: string;
    // Add other properties from DefaultJWT if needed
  }
}