import { useState, useCallback, useRef, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'

// Optimistic state management hook
export function useOptimisticState<T, P = any>(
  initialState: T,
  mutationFn: (params: P) => Promise<T>,
  options: {
    queryKey?: unknown[]
    onSuccess?: (data: T, params: P) => void
    onError?: (error: unknown, params: P, previousState: T) => void
    onSettled?: (data: T | undefined, error: unknown | null, params: P) => void
    enableToast?: boolean
    successMessage?: string
    errorMessage?: string
  } = {}
) {
  const [optimisticState, setOptimisticState] = useState<T>(initialState)
  const previousStateRef = useRef<T>(initialState)
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn,
    onMutate: (params: P) => {
      // Store previous state for rollback
      previousStateRef.current = optimisticState
      
      // Cancel any outgoing refetches
      if (options.queryKey) {
        queryClient.cancelQueries({ queryKey: options.queryKey })
      }
      
      return { previousState: optimisticState }
    },
    onSuccess: (data, params) => {
      setOptimisticState(data)
      
      if (options.enableToast && options.successMessage) {
        toast.success(options.successMessage)
      }
      
      options.onSuccess?.(data, params)
    },
    onError: (error, params, context) => {
      // Rollback to previous state
      if (context?.previousState !== undefined) {
        setOptimisticState(context.previousState)
      }
      
      if (options.enableToast && options.errorMessage) {
        toast.error(options.errorMessage)
      }
      
      options.onError?.(error, params, context?.previousState || initialState)
    },
    onSettled: (data, error, params) => {
      // Invalidate queries if needed
      if (options.queryKey) {
        queryClient.invalidateQueries({ queryKey: options.queryKey })
      }
      
      options.onSettled?.(data, error, params)
    },
  })

  const updateOptimistically = useCallback((newState: T | ((prev: T) => T)) => {
    setOptimisticState(prev => 
      typeof newState === 'function' ? (newState as (prev: T) => T)(prev) : newState
    )
  }, [])

  const mutateOptimistically = useCallback((params: P, optimisticUpdate?: T | ((prev: T) => T)) => {
    // Apply optimistic update immediately if provided
    if (optimisticUpdate !== undefined) {
      updateOptimistically(optimisticUpdate)
    }
    
    return mutation.mutate(params)
  }, [mutation.mutate, updateOptimistically])

  return {
    state: optimisticState,
    setState: setOptimisticState,
    updateOptimistically,
    mutateOptimistically,
    mutation,
    isLoading: mutation.isPending,
    error: mutation.error,
  }
}

// Debounced state management
export function useDebouncedState<T>(
  initialValue: T,
  delay: number = 500
): [T, T, (value: T) => void] {
  const [immediateValue, setImmediateValue] = useState<T>(initialValue)
  const [debouncedValue, setDebouncedValue] = useState<T>(initialValue)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(immediateValue)
    }, delay)

    return () => clearTimeout(timer)
  }, [immediateValue, delay])

  return [immediateValue, debouncedValue, setImmediateValue]
}

// Persisted state hook
export function usePersistedState<T>(
  key: string,
  defaultValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  })

  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(state) : value
      setState(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, state])

  return [state, setValue]
}

// Async state management with error handling
export function useAsyncState<T>(
  asyncFn: () => Promise<T>,
  deps: React.DependencyList = []
) {
  const [state, setState] = useState<{
    data: T | null
    loading: boolean
    error: Error | null
  }>({
    data: null,
    loading: true,
    error: null,
  })

  useEffect(() => {
    let cancelled = false

    setState(prev => ({ ...prev, loading: true, error: null }))

    asyncFn()
      .then(data => {
        if (!cancelled) {
          setState({ data, loading: false, error: null })
        }
      })
      .catch(error => {
        if (!cancelled) {
          setState(prev => ({ ...prev, loading: false, error }))
        }
      })

    return () => {
      cancelled = true
    }
  }, deps)

  const refetch = useCallback(() => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    asyncFn()
      .then(data => setState({ data, loading: false, error: null }))
      .catch(error => setState(prev => ({ ...prev, loading: false, error })))
  }, [asyncFn])

  return { ...state, refetch }
}

// Form state management with validation
export function useFormState<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: (values: T) => Record<keyof T, string[]>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>)
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const setValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: [] }))
    }
  }, [errors])

  const setFieldTouched = useCallback((name: keyof T, touched: boolean = true) => {
    setTouched(prev => ({ ...prev, [name]: touched }))
  }, [])

  const validate = useCallback(() => {
    if (!validationSchema) return true

    const newErrors = validationSchema(values)
    setErrors(newErrors)

    return Object.values(newErrors).every(fieldErrors => fieldErrors.length === 0)
  }, [values, validationSchema])

  const handleSubmit = useCallback(async (
    onSubmit: (values: T) => Promise<void> | void
  ) => {
    setIsSubmitting(true)
    
    // Mark all fields as touched
    const allTouched = Object.keys(values).reduce((acc, key) => ({
      ...acc,
      [key]: true,
    }), {} as Record<keyof T, boolean>)
    setTouched(allTouched)

    try {
      const isValid = validate()
      if (!isValid) {
        return false
      }

      await onSubmit(values)
      return true
    } catch (error) {
      console.error('Form submission error:', error)
      return false
    } finally {
      setIsSubmitting(false)
    }
  }, [values, validate])

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({} as Record<keyof T, string[]>)
    setTouched({} as Record<keyof T, boolean>)
    setIsSubmitting(false)
  }, [initialValues])

  const getFieldProps = useCallback((name: keyof T) => ({
    value: values[name],
    onChange: (value: any) => setValue(name, value),
    onBlur: () => setFieldTouched(name),
    error: touched[name] ? errors[name] : [],
  }), [values, setValue, setFieldTouched, touched, errors])

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setFieldTouched,
    validate,
    handleSubmit,
    reset,
    getFieldProps,
    isValid: Object.values(errors).every(fieldErrors => fieldErrors.length === 0),
    isDirty: JSON.stringify(values) !== JSON.stringify(initialValues),
  }
}

// Undo/Redo state management
export function useUndoRedo<T>(initialState: T, maxHistorySize: number = 10) {
  const [history, setHistory] = useState<T[]>([initialState])
  const [currentIndex, setCurrentIndex] = useState(0)

  const currentState = history[currentIndex]

  const setState = useCallback((newState: T | ((prev: T) => T)) => {
    setHistory(prev => {
      const state = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prev[currentIndex])
        : newState

      const newHistory = prev.slice(0, currentIndex + 1)
      newHistory.push(state)
      
      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift()
        setCurrentIndex(currentIndex)
      } else {
        setCurrentIndex(newHistory.length - 1)
      }
      
      return newHistory
    })
  }, [currentIndex, maxHistorySize])

  const undo = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }, [currentIndex])

  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }, [currentIndex, history.length])

  const canUndo = currentIndex > 0
  const canRedo = currentIndex < history.length - 1

  const reset = useCallback(() => {
    setHistory([initialState])
    setCurrentIndex(0)
  }, [initialState])

  return {
    state: currentState,
    setState,
    undo,
    redo,
    canUndo,
    canRedo,
    reset,
    historyLength: history.length,
    currentIndex,
  }
}