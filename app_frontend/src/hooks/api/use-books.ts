import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from "@/lib/react-query";
import { toast } from 'react-hot-toast'

// Types
export interface Book {
  id: string;
  title: string;
  description: string;
  category: string;
  status:
    | "draft"
    | "published"
    | "generating"
    | "failed"
    | "in-review"
    | "archived";
  cover_url?: string;
  created_at: string;
  updated_at: string;
  word_count: number;
  revenue: number;
  sales: number;
  rating: number;
}

export interface BookFilters {
  status?: string
  category?: string
  search?: string
  sort?: string
  limit?: number
  offset?: number
}

export interface CreateBookRequest {
  title: string
  description: string
  category: string
  niche?: string
  target_audience?: string
  keywords?: string[]
}

export interface UpdateBookRequest {
  title?: string
  description?: string
  category?: string
  status?: string
}

// Hooks
export function useBooks(filters?: BookFilters) {
  return useQuery({
    queryKey: queryKeys.books.list(filters || {}),
    queryFn: async () => {
      try {
        const params: Record<string, string> = {};

        if (filters?.status) params.status = filters.status;
        if (filters?.category) params.category = filters.category;
        if (filters?.search) params.search = filters.search;
        if (filters?.sort) params.sort = filters.sort;
        if (filters?.limit) params.limit = filters.limit.toString();
        if (filters?.offset) params.offset = filters.offset.toString();

        const response = await apiClient.get<{
          books: Book[];
          total: number;
          page?: number;
          limit?: number;
        }>("/api/books", params);

        return response;
      } catch (error) {
        console.error("Failed to fetch books:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

export function useBook(id: string) {
  return useQuery({
    queryKey: queryKeys.books.detail(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get<Book>(`/api/books/${id}`);
        return response;
      } catch (error) {
        console.error(`Failed to fetch book ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

export function useBookAnalytics(id: string) {
  return useQuery({
    queryKey: queryKeys.books.analytics(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get(`/api/books/${id}/analytics`);
        return response;
      } catch (error) {
        console.error(`Failed to fetch analytics for book ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

export function useCreateBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateBookRequest) => apiClient.post<Book>('/api/books', data),
    onMutate: async (newBook) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })
      
      // Snapshot the previous value
      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())
      
      // Optimistically update to the new value
      const optimisticBook: Book = {
        id: crypto.randomUUID(),
        ...newBook,
        status: 'generating' as const,
        cover_url: undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        word_count: 0,
        revenue: 0,
        sales: 0,
        rating: 0,
      }
      
      // Add to all book lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: [optimisticBook, ...old.books],
              total: old.total + 1,
            }
          }
          return old
        }
      )
      
      return { previousBooks, optimisticBook }
    },
    onError: (err, newBook, context) => {
      // Revert the optimistic update
      if (context?.previousBooks) {
        queryClient.setQueriesData(
          { queryKey: queryKeys.books.lists() },
          context.previousBooks
        )
      }
      toast.error('Failed to create book')
    },
    onSuccess: (data, variables, context) => {
      // Replace optimistic book with real data
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books && context?.optimisticBook) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === context.optimisticBook.id ? data : book
              ),
            }
          }
          return old
        }
      )
      
      // Set individual book data
      queryClient.setQueryData(queryKeys.books.detail(data.id), data)
      
      toast.success('Book created successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function useUpdateBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBookRequest }) =>
      apiClient.patch<Book>(`/api/books/${id}`, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.detail(id) })
      
      // Snapshot the previous value
      const previousBook = queryClient.getQueryData(queryKeys.books.detail(id))
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        ...data,
        updated_at: new Date().toISOString(),
      }))
      
      // Update in lists as well
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === id ? { ...book, ...data, updated_at: new Date().toISOString() } : book
              ),
            }
          }
          return old
        }
      )
      
      return { previousBook }
    },
    onError: (err, { id }, context) => {
      // Revert the optimistic update
      if (context?.previousBook) {
        queryClient.setQueryData(queryKeys.books.detail(id), context.previousBook)
      }
      toast.error('Failed to update book')
    },
    onSuccess: (data) => {
      toast.success('Book updated successfully!')
    },
    onSettled: (data, error, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function useDeleteBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/api/books/${id}`),
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })
      
      // Snapshot the previous value
      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())
      
      // Optimistically remove from lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.filter((book: Book) => book.id !== id),
              total: Math.max(0, old.total - 1),
            }
          }
          return old
        }
      )
      
      return { previousBooks }
    },
    onError: (err, id, context) => {
      // Revert the optimistic update
      if (context?.previousBooks) {
        queryClient.setQueriesData(
          { queryKey: queryKeys.books.lists() },
          context.previousBooks
        )
      }
      toast.error('Failed to delete book')
    },
    onSuccess: (data, id) => {
      // Remove individual book data
      queryClient.removeQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.removeQueries({ queryKey: queryKeys.books.analytics(id) })
      
      toast.success('Book deleted successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function usePublishBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiClient.post(`/api/books/${id}/publish`),
    onMutate: async (id) => {
      // Optimistically update status
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        status: 'published' as const,
      }))
      
      // Update in lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === id ? { ...book, status: 'published' as const } : book
              ),
            }
          }
          return old
        }
      )
    },
    onError: (err, id) => {
      // Revert status on error
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        status: 'draft' as const,
      }))
      toast.error('Failed to publish book')
    },
    onSuccess: () => {
      toast.success('Book published successfully!')
    },
    onSettled: (data, error, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}