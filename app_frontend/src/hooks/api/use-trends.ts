'use client'

import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { TrendsFilters, TrendsData } from '@/types/trends'

interface UseTrendsDataResult {
  data: TrendsData | undefined
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

export function useTrendsData(filters?: TrendsFilters): UseTrendsDataResult {
  const queryResult = useQuery({
    queryKey: queryKeys.trends.list(filters),
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        
        if (filters?.timeRange) params.append('timeRange', filters.timeRange)
        if (filters?.region) params.append('region', filters.region)
        if (filters?.category) params.append('category', filters.category)
        if (filters?.sortBy) params.append('sortBy', filters.sortBy)
        if (filters?.orderBy) params.append('orderBy', filters.orderBy)
        
        const response = await apiClient.get<TrendsData>(`/api/trends?${params.toString()}`)
        return response
      } catch (error) {
        console.error('Failed to fetch trends data:', error)
        throw error
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - trends data changes less frequently
    retry: 2,
  })

  return {
    data: queryResult.data,
    isLoading: queryResult.isLoading,
    error: queryResult.error,
    refetch: queryResult.refetch,
  }
}

// Hook for specific trend analysis
export function useTrendAnalysis(trendId: string) {
  return useQuery({
    queryKey: queryKeys.trends.detail(trendId),
    queryFn: async () => {
      try {
        const response = await apiClient.get(`/api/trends/${trendId}/analysis`)
        return response
      } catch (error) {
        console.error(`Failed to fetch trend analysis for ${trendId}:`, error)
        throw error
      }
    },
    enabled: !!trendId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

// Hook for trend predictions
export function useTrendPredictions(category?: string, timeframe?: string) {
  return useQuery({
    queryKey: queryKeys.trends.predictions(category, timeframe),
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        if (category) params.append('category', category)
        if (timeframe) params.append('timeframe', timeframe)
        
        const response = await apiClient.get(`/api/trends/predictions?${params.toString()}`)
        return response
      } catch (error) {
        console.error('Failed to fetch trend predictions:', error)
        throw error
      }
    },
    staleTime: 30 * 60 * 1000, // 30 minutes - predictions change less frequently
    retry: 2,
  })
}

// Hook for real-time trend monitoring
export function useRealTimeTrends(enabled = false) {
  return useQuery({
    queryKey: queryKeys.trends.realtime(),
    queryFn: async () => {
      try {
        const response = await apiClient.get('/api/trends/realtime')
        return response
      } catch (error) {
        console.error('Failed to fetch real-time trends:', error)
        throw error
      }
    },
    enabled,
    refetchInterval: enabled ? 60 * 1000 : false, // Poll every minute when enabled
    staleTime: 0, // Always fresh for real-time data
  })
}

// Hook for trend search
export function useTrendSearch(query: string) {
  return useQuery({
    queryKey: queryKeys.trends.search(query),
    queryFn: async () => {
      try {
        const response = await apiClient.get(`/api/trends/search?q=${encodeURIComponent(query)}`)
        return response
      } catch (error) {
        console.error(`Failed to search trends for "${query}":`, error)
        throw error
      }
    },
    enabled: !!query && query.length > 2, // Only search when query is meaningful
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  })
}

export default useTrendsData