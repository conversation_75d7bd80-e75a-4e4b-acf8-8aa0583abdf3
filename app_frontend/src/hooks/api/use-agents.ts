'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// Types
export interface AgentTask {
  id: string;
  task_id: string;
  user_id: string;
  task_type: 'manuscript' | 'trend_analysis' | 'cover_design' | 'publishing' | 'research';
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  current_step: string;
  task_data: {
    topic?: string;
    title?: string;
    genre?: string;
    target_audience?: string;
    length?: string;
    language?: string;
    style?: string;
    [key: string]: any;
  };
  result?: {
    content?: string;
    metadata?: Record<string, any>;
    files?: Array<{
      name: string;
      path: string;
      size: number;
      type: string;
    }>;
    analytics?: {
      words_generated: number;
      chapters_completed: number;
      time_taken: number;
      quality_score: number;
    };
  };
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface ManuscriptGenerationRequest {
  topic?: string;
  title?: string;
  genre?: string;
  target_audience?: string;
  length?: 'short' | 'medium' | 'long';
  language?: string;
  style?: string;
  outline?: string;
  additional_instructions?: string;
}

export interface TrendAnalysisRequest {
  topic?: string;
  category?: string;
  timeframe?: string;
  region?: string;
  depth?: 'basic' | 'detailed' | 'comprehensive';
}

export interface CoverDesignRequest {
  title: string;
  genre: string;
  style?: string;
  color_scheme?: string;
  mood?: string;
  target_audience?: string;
  additional_requirements?: string;
}

export interface AgentTaskStatus {
  task_id: string;
  status: string;
  progress: number;
  current_step: string;
  estimated_completion?: string;
  steps_completed: number;
  total_steps: number;
  step_details: Array<{
    step_name: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    start_time?: string;
    end_time?: string;
    duration?: number;
    details?: string;
  }>;
}

// Hooks
export function useAgentTasks(filters?: {
  status?: string;
  task_type?: string;
  limit?: number;
  offset?: number;
}) {
  return useQuery({
    queryKey: ['agents', 'tasks', filters],
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        
        if (filters?.status) params.append('status', filters.status)
        if (filters?.task_type) params.append('task_type', filters.task_type)
        if (filters?.limit) params.append('limit', filters.limit.toString())
        if (filters?.offset) params.append('offset', filters.offset.toString())
        
        const response = await apiClient.get<{
          tasks: AgentTask[]
          total: number
          page?: number
          limit?: number
        }>(`/api/agents/tasks?${params.toString()}`)
        
        return response
      } catch (error) {
        console.error('Failed to fetch agent tasks:', error)
        throw error
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Poll every 30 seconds for active tasks
    retry: 2,
  })
}

export function useAgentTask(taskId: string) {
  return useQuery({
    queryKey: ['agents', 'task', taskId],
    queryFn: async () => {
      try {
        const response = await apiClient.get<AgentTask>(`/api/agents/tasks/${taskId}`)
        return response
      } catch (error) {
        console.error(`Failed to fetch agent task ${taskId}:`, error)
        throw error
      }
    },
    enabled: !!taskId,
    staleTime: 10 * 1000, // 10 seconds
    refetchInterval: 10 * 1000, // Poll every 10 seconds for task updates
    retry: 2,
  })
}

export function useAgentTaskStatus(taskId: string) {
  return useQuery({
    queryKey: ['agents', 'task', taskId, 'status'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<AgentTaskStatus>(`/api/agents/tasks/${taskId}/status`)
        return response
      } catch (error) {
        console.error(`Failed to fetch task status for ${taskId}:`, error)
        throw error
      }
    },
    enabled: !!taskId,
    staleTime: 5 * 1000, // 5 seconds
    refetchInterval: 5 * 1000, // Poll every 5 seconds for real-time status
    retry: 2,
  })
}

export function useGenerateManuscript() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: ManuscriptGenerationRequest) => 
      apiClient.post<{ task_id: string; message: string }>('/api/agents/generate-manuscript', data),
    onSuccess: (data) => {
      toast.success('Manuscript generation started! You can track progress in the tasks section.')
      // Invalidate tasks list to show new task
      queryClient.invalidateQueries({ queryKey: ['agents', 'tasks'] })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to start manuscript generation')
    },
  })
}

export function useAnalyzeTrends() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: TrendAnalysisRequest) => 
      apiClient.post<{ task_id: string; message: string }>('/api/agents/analyze-trends', data),
    onSuccess: (data) => {
      toast.success('Trend analysis started! Check back soon for insights.')
      queryClient.invalidateQueries({ queryKey: ['agents', 'tasks'] })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to start trend analysis')
    },
  })
}

export function useGenerateCover() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CoverDesignRequest) => 
      apiClient.post<{ task_id: string; message: string }>('/api/agents/generate-cover', data),
    onSuccess: (data) => {
      toast.success('Cover design generation started!')
      queryClient.invalidateQueries({ queryKey: ['agents', 'tasks'] })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to start cover generation')
    },
  })
}

export function useCancelAgentTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (taskId: string) => 
      apiClient.patch(`/api/agents/tasks/${taskId}/cancel`),
    onSuccess: (_, taskId) => {
      toast.success('Task cancelled successfully')
      queryClient.invalidateQueries({ queryKey: ['agents', 'task', taskId] })
      queryClient.invalidateQueries({ queryKey: ['agents', 'tasks'] })
    },
    onError: () => {
      toast.error('Failed to cancel task')
    },
  })
}

export function useRetryAgentTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (taskId: string) => 
      apiClient.patch(`/api/agents/tasks/${taskId}/retry`),
    onSuccess: (_, taskId) => {
      toast.success('Task retry initiated')
      queryClient.invalidateQueries({ queryKey: ['agents', 'task', taskId] })
      queryClient.invalidateQueries({ queryKey: ['agents', 'tasks'] })
    },
    onError: () => {
      toast.error('Failed to retry task')
    },
  })
}

// Hook for real-time task monitoring
export function useRealTimeTaskMonitoring(taskIds: string[]) {
  return useQuery({
    queryKey: ['agents', 'tasks', 'realtime', taskIds],
    queryFn: async () => {
      try {
        const response = await apiClient.post<{
          tasks: AgentTask[]
          status_updates: Record<string, AgentTaskStatus>
        }>('/api/agents/tasks/batch-status', { task_ids: taskIds })
        
        return response
      } catch (error) {
        console.error('Failed to fetch real-time task updates:', error)
        throw error
      }
    },
    enabled: taskIds.length > 0,
    staleTime: 0, // Always fresh for real-time
    refetchInterval: 3 * 1000, // Poll every 3 seconds
    retry: 1,
  })
}

export function useAgentStatistics() {
  return useQuery({
    queryKey: ['agents', 'statistics'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<{
          total_tasks: number;
          completed_tasks: number;
          failed_tasks: number;
          in_progress_tasks: number;
          avg_completion_time: number;
          success_rate: number;
          task_types: Array<{
            type: string;
            count: number;
            success_rate: number;
            avg_duration: number;
          }>;
          recent_completions: Array<{
            task_id: string;
            task_type: string;
            completed_at: string;
            duration: number;
            success: boolean;
          }>;
        }>('/api/agents/statistics')
        
        return response
      } catch (error) {
        console.error('Failed to fetch agent statistics:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export default useAgentTasks