'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSession } from 'next-auth/react'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// ================================
// TYPES (MATCHING BACKEND SCHEMAS)
// ================================

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  subscription_tier: string;
  created_at: string;
  updated_at?: string;
}

export interface UserPreferences {
  user_id: string;
  theme: 'light' | 'dark';
  language: string;
  timezone: string;
  email_notifications: boolean;
  push_notifications: boolean;
  marketing_emails: boolean;
  content_preferences: Record<string, any>;
  accessibility: Record<string, any>;
  privacy_level: string;
  updated_at?: string;
}

export interface PublishingSettings {
  user_id: string;
  preferred_ai_provider: string;
  default_writing_style: string;
  default_target_audience: string;
  auto_publish_enabled: boolean;
  quality_threshold: number;
  content_preferences: Record<string, any>;
  publishing_defaults: Record<string, any>;
  updated_at?: string;
}

export interface PlatformIntegration {
  enabled: boolean;
  account_id?: string;
  username?: string;
  publication_url?: string;
  site_url?: string;
  auto_publish?: boolean;
  last_sync?: string;
}

export interface PlatformIntegrations {
  user_id: string;
  amazon_kdp: PlatformIntegration;
  medium: PlatformIntegration;
  substack: PlatformIntegration;
  wordpress: PlatformIntegration;
  updated_at?: string;
}

export interface SecuritySettings {
  user_id: string;
  two_factor_enabled: boolean;
  login_notifications: boolean;
  session_timeout: number;
  ip_whitelist: string[];
  device_tracking: boolean;
  api_key_access: boolean;
  data_export_enabled: boolean;
  account_deletion_protection: boolean;
  last_password_change?: string;
  updated_at?: string;
}

export interface ActiveSession {
  session_id: string;
  device_type: string;
  browser: string;
  ip_address: string;
  location: string;
  created_at: string;
  last_activity: string;
  is_current: boolean;
}

export interface ApiKey {
  key_id: string;
  name: string;
  key_prefix: string;
  permissions: string[];
  usage_count: number;
  last_used?: string;
  created_at: string;
  expires_at?: string;
  is_active: boolean;
}

// ================================
// USER PROFILE HOOKS
// ================================

export function useUserProfile() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.profile(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<UserProfile>('/api/users/profile')
        return response
      } catch (error) {
        console.error('Failed to fetch user profile:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useUpdateUserProfile() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (profileData: Partial<UserProfile>) => {
      try {
        const response = await apiClient.patch<UserProfile>('/api/users/profile', profileData)
        return response
      } catch (error) {
        console.error('Failed to update user profile:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.settings.profile(), data)
      toast.success('Profile updated successfully!')
    },
    onError: (error) => {
      console.error('Profile update failed:', error)
      toast.error('Failed to update profile')
    },
  })
}

// ================================
// USER PREFERENCES HOOKS
// ================================

export function useUserPreferences() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated' && !!session
  
  return useQuery({
    queryKey: queryKeys.settings.preferences(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<UserPreferences>('/api/users/preferences')
        return response
      } catch (error) {
        console.error('Failed to fetch user preferences:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useUpdateUserPreferences() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (preferences: Partial<UserPreferences>) => {
      try {
        const response = await apiClient.patch<UserPreferences>('/api/users/preferences', preferences)
        return response
      } catch (error) {
        console.error('Failed to update user preferences:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.settings.preferences(), data)
      toast.success('Preferences updated successfully!')
    },
    onError: (error) => {
      console.error('Preferences update failed:', error)
      toast.error('Failed to update preferences')
    },
  })
}

// ================================
// PUBLISHING SETTINGS HOOKS
// ================================

export function usePublishingSettings() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.publishing(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<PublishingSettings>('/api/users/publishing-settings')
        return response
      } catch (error) {
        console.error('Failed to fetch publishing settings:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useUpdatePublishingSettings() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (settings: Partial<PublishingSettings>) => {
      try {
        const response = await apiClient.patch<PublishingSettings>('/api/users/publishing-settings', settings)
        return response
      } catch (error) {
        console.error('Failed to update publishing settings:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.settings.publishing(), data)
      toast.success('Publishing settings updated successfully!')
    },
    onError: (error) => {
      console.error('Publishing settings update failed:', error)
      toast.error('Failed to update publishing settings')
    },
  })
}

// ================================
// PLATFORM INTEGRATIONS HOOKS
// ================================

export function usePlatformIntegrations() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.integrations(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<PlatformIntegrations>('/api/users/integrations')
        return response
      } catch (error) {
        console.error('Failed to fetch platform integrations:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useUpdatePlatformIntegration() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ platform, data }: { platform: string, data: Partial<PlatformIntegration> }) => {
      try {
        const response = await apiClient.patch<PlatformIntegrations>(`/api/users/integrations/${platform}`, data)
        return response
      } catch (error) {
        console.error(`Failed to update ${platform} integration:`, error)
        throw error
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.settings.integrations(), data)
      toast.success('Integration updated successfully!')
    },
    onError: (error) => {
      console.error('Integration update failed:', error)
      toast.error('Failed to update integration')
    },
  })
}

// ================================
// SECURITY SETTINGS HOOKS
// ================================

export function useSecuritySettings() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.security(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<SecuritySettings>('/api/users/security')
        return response
      } catch (error) {
        console.error('Failed to fetch security settings:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useUpdateSecuritySettings() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (settings: Partial<SecuritySettings>) => {
      try {
        const response = await apiClient.patch<SecuritySettings>('/api/users/security', settings)
        return response
      } catch (error) {
        console.error('Failed to update security settings:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.settings.security(), data)
      toast.success('Security settings updated successfully!')
    },
    onError: (error) => {
      console.error('Security settings update failed:', error)
      toast.error('Failed to update security settings')
    },
  })
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async ({ oldPassword, newPassword }: { oldPassword: string, newPassword: string }) => {
      try {
        const response = await apiClient.patch('/api/users/change-password', {
          old_password: oldPassword,
          new_password: newPassword
        })
        return response
      } catch (error) {
        console.error('Failed to change password:', error)
        throw error
      }
    },
    onSuccess: () => {
      toast.success('Password changed successfully!')
    },
    onError: (error) => {
      console.error('Password change failed:', error)
      toast.error('Failed to change password')
    },
  })
}

// ================================
// ACTIVE SESSIONS HOOKS
// ================================

export function useActiveSessions() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.sessions(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ActiveSession[]>('/api/users/sessions')
        return response
      } catch (error) {
        console.error('Failed to fetch active sessions:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
  })
}

export function useTerminateSession() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (sessionId: string) => {
      try {
        const response = await apiClient.delete(`/api/users/sessions/${sessionId}`)
        return response
      } catch (error) {
        console.error('Failed to terminate session:', error)
        throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.sessions() })
      toast.success('Session terminated successfully!')
    },
    onError: (error) => {
      console.error('Session termination failed:', error)
      toast.error('Failed to terminate session')
    },
  })
}

// ================================
// API KEYS HOOKS
// ================================

export function useApiKeys() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === 'authenticated'
  
  return useQuery({
    queryKey: queryKeys.settings.apiKeys(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ApiKey[]>('/api/users/api-keys')
        return response
      } catch (error) {
        console.error('Failed to fetch API keys:', error)
        throw error
      }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function useCreateApiKey() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (keyData: { name: string, permissions: string[], usage_limit?: number, expires_at?: string }) => {
      try {
        const response = await apiClient.post('/api/users/api-keys', keyData)
        return response
      } catch (error) {
        console.error('Failed to create API key:', error)
        throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.apiKeys() })
      toast.success('API key created successfully!')
    },
    onError: (error) => {
      console.error('API key creation failed:', error)
      toast.error('Failed to create API key')
    },
  })
}

export function useRegenerateApiKey() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (keyId: string) => {
      try {
        const response = await apiClient.post(`/api/users/api-keys/${keyId}/regenerate`)
        return response
      } catch (error) {
        console.error('Failed to regenerate API key:', error)
        throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.apiKeys() })
      toast.success('API key regenerated successfully!')
    },
    onError: (error) => {
      console.error('API key regeneration failed:', error)
      toast.error('Failed to regenerate API key')
    },
  })
}