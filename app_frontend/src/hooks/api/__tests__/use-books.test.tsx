import { renderHook } from "@testing-library/react";
import { waitFor } from "@testing-library/dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactNode } from "react";
import { toast } from "react-hot-toast";
import {
  useBooks,
  useBook,
  useCreateBook,
  useUpdateBook,
  useDeleteBook,
  usePublishBook,
  Book,
  CreateBookRequest,
  UpdateBookRequest,
} from "../use-books";
import { apiClient } from "@/lib/api";

// Mock dependencies
jest.mock("@/lib/api");
jest.mock("react-hot-toast");

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockedToast = toast as jest.Mocked<typeof toast>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        retryDelay: 0,
      },
      mutations: {
        retry: false,
        retryDelay: 0,
      },
    },
  });

  const TestWrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  TestWrapper.displayName = "TestWrapper";

  return TestWrapper;
};

// Mock data
const mockBook: Book = {
  id: "1",
  title: "Test Book",
  description: "A test book",
  category: "Fiction",
  status: "draft",
  cover_url: "/covers/test.jpg",
  created_at: "2023-01-01T00:00:00Z",
  updated_at: "2023-01-01T00:00:00Z",
  word_count: 5000,
  revenue: 0,
  sales: 0,
  rating: 0,
};

const mockBooksData = {
  books: [mockBook],
  total: 1,
};

describe("useBooks", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should fetch books successfully", async () => {
    mockedApiClient.get.mockResolvedValue(mockBooksData);

    const { result } = renderHook(() => useBooks(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockBooksData);
    expect(mockedApiClient.get).toHaveBeenCalledWith("/api/books", {});
  });

  it("should handle filters", async () => {
    const filters = { status: "published", category: "Fiction" };
    mockedApiClient.get.mockResolvedValue(mockBooksData);

    const { result } = renderHook(() => useBooks(filters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(mockedApiClient.get).toHaveBeenCalledWith("/api/books", filters);
  });

  it("should handle error states", async () => {
    const error = new Error("Failed to fetch books");
    mockedApiClient.get.mockRejectedValue(error);

    const { result } = renderHook(() => useBooks(), {
      wrapper: createWrapper(),
    });

    await waitFor(
      () => {
        expect(result.current.isError).toBe(true);
      },
      { timeout: 5000 }
    );

    expect(result.current.error).toEqual(error);
  });
});

describe("useBook", () => {
  it("should fetch a single book", async () => {
    mockedApiClient.get.mockResolvedValue(mockBook);

    const { result } = renderHook(() => useBook("1"), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockBook);
    expect(mockedApiClient.get).toHaveBeenCalledWith("/api/books/1");
  });

  it("should not fetch when id is not provided", () => {
    const { result } = renderHook(() => useBook(""), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(mockedApiClient.get).not.toHaveBeenCalled();
  });
});

describe("useCreateBook", () => {
  it("should create a book successfully", async () => {
    const newBookData: CreateBookRequest = {
      title: "New Book",
      description: "A new test book",
      category: "Non-fiction",
    };

    const createdBook: Book = {
      ...mockBook,
      ...newBookData,
      id: "2",
    };

    mockedApiClient.post.mockResolvedValue(createdBook);

    const { result } = renderHook(() => useCreateBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate(newBookData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(createdBook);
    expect(mockedApiClient.post).toHaveBeenCalledWith(
      "/api/books",
      newBookData
    );
    expect(mockedToast.success).toHaveBeenCalledWith(
      "Book created successfully!"
    );
  });

  it("should handle creation errors", async () => {
    const error = new Error("Failed to create book");
    mockedApiClient.post.mockRejectedValue(error);

    const { result } = renderHook(() => useCreateBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate({
      title: "New Book",
      description: "A new test book",
      category: "Non-fiction",
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockedToast.error).toHaveBeenCalledWith("Failed to create book");
  });
});

describe("useUpdateBook", () => {
  it("should update a book successfully", async () => {
    const updateData: UpdateBookRequest = {
      title: "Updated Book Title",
    };

    const updatedBook: Book = {
      ...mockBook,
      title: updateData.title!,
    };

    mockedApiClient.patch.mockResolvedValue(updatedBook);

    const { result } = renderHook(() => useUpdateBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate({ id: "1", data: updateData });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(updatedBook);
    expect(mockedApiClient.patch).toHaveBeenCalledWith(
      "/api/books/1",
      updateData
    );
    expect(mockedToast.success).toHaveBeenCalledWith(
      "Book updated successfully!"
    );
  });

  it("should handle update errors", async () => {
    const error = new Error("Failed to update book");
    mockedApiClient.patch.mockRejectedValue(error);

    const { result } = renderHook(() => useUpdateBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate({
      id: "1",
      data: { title: "Updated Title" },
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockedToast.error).toHaveBeenCalledWith("Failed to update book");
  });
});

describe("useDeleteBook", () => {
  it("should delete a book successfully", async () => {
    mockedApiClient.delete.mockResolvedValue({ success: true });

    const { result } = renderHook(() => useDeleteBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate("1");

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(mockedApiClient.delete).toHaveBeenCalledWith("/api/books/1");
    expect(mockedToast.success).toHaveBeenCalledWith(
      "Book deleted successfully!"
    );
  });

  it("should handle deletion errors", async () => {
    const error = new Error("Failed to delete book");
    mockedApiClient.delete.mockRejectedValue(error);

    const { result } = renderHook(() => useDeleteBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate("1");

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockedToast.error).toHaveBeenCalledWith("Failed to delete book");
  });
});

describe("usePublishBook", () => {
  it("should publish a book successfully", async () => {
    const publishedBook: Book = {
      ...mockBook,
      status: "published",
    };

    mockedApiClient.post.mockResolvedValue(publishedBook);

    const { result } = renderHook(() => usePublishBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate("1");

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(mockedApiClient.post).toHaveBeenCalledWith("/api/books/1/publish");
    expect(mockedToast.success).toHaveBeenCalledWith(
      "Book published successfully!"
    );
  });

  it("should handle publish errors", async () => {
    const error = new Error("Failed to publish book");
    mockedApiClient.post.mockRejectedValue(error);

    const { result } = renderHook(() => usePublishBook(), {
      wrapper: createWrapper(),
    });

    result.current.mutate("1");

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockedToast.error).toHaveBeenCalledWith("Failed to publish book");
  });
});
