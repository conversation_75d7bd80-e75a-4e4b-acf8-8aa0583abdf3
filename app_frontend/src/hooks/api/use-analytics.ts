import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// Types
export interface DateRange {
  from: string
  to: string
}

export interface AnalyticsOverview {
  totalRevenue: number
  totalSales: number
  activeBooks: number
  averageRating: number
  revenueChange: number
  salesChange: number
  booksChange: number
  ratingChange?: number
  salesHistory?: Array<{
    month: string
    sales: number
    revenue: number
  }>
}

export interface AnalyticsDetails {
  salesHistory: Array<{
    month: string
    sales: number
    revenue: number
    books?: number
  }>
  topBooks?: Array<{
    id: string
    title: string
    revenue: number
    sales: number
    rating?: number
  }>
  demographics?: {
    countries?: Array<{
      country: string
      percentage: number
      sales: number
    }>
    ageGroups?: Array<{
      range: string
      percentage: number
    }>
  }
}

export interface RevenueAnalytics {
  totalRevenue: number
  monthlyRevenue: Array<{
    month: string
    revenue: number
    sales: number
  }>
  revenueByCategory: Array<{
    category: string
    revenue: number
    percentage: number
  }>
  topPerformers: Array<{
    id: string
    title: string
    revenue: number
    sales: number
  }>
}

export interface BookAnalytics {
  totalBooks: number
  booksByStatus: Array<{
    status: string
    count: number
    percentage: number
  }>
  booksByCategory: Array<{
    category: string
    count: number
    averageRevenue: number
  }>
  performanceMetrics: {
    averageWordCount: number
    averageRating: number
    successRate: number
  }
}

export interface AudienceAnalytics {
  totalReaders: number
  demographics: {
    ageGroups: Array<{
      range: string
      percentage: number
    }>
    regions: Array<{
      country: string
      percentage: number
      revenue: number
    }>
  }
  engagement: {
    averageReadTime: number
    completionRate: number
    returnReaderRate: number
  }
  preferences: {
    topCategories: Array<{
      category: string
      popularity: number
    }>
    readingPatterns: Array<{
      timeOfDay: string
      activity: number
    }>
  }
}

export interface BookPerformance {
  id: string
  title: string
  views: number
  sales: number
  revenue: number
  rating: number
  conversionRate: number
  dailyStats: Array<{
    date: string
    views: number
    sales: number
    revenue: number
  }>
}

// Hooks
export function useAnalyticsOverview(dateRange?: DateRange) {
  return useQuery({
    queryKey: queryKeys.analytics.overview(dateRange),
    queryFn: async () => {
      try {
        const params = dateRange ? {
          from: dateRange.from,
          to: dateRange.to
        } : {}
        
        const response = await apiClient.get<AnalyticsOverview>('/api/analytics/dashboard', { params })
        return response
      } catch (error) {
        console.error('Failed to fetch analytics overview:', error)
        // Return empty data structure on error
        return {
          totalRevenue: 0,
          totalSales: 0,
          activeBooks: 0,
          averageRating: 0,
          revenueChange: 0,
          salesChange: 0,
          booksChange: 0,
          ratingChange: 0,
        } as AnalyticsOverview
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  })
}

export function useAnalyticsDetails(params?: { timeRange?: string }) {
  return useQuery({
    queryKey: queryKeys.analytics.details(params),
    queryFn: async () => {
      try {
        // Use dashboard endpoint for now since details endpoint doesn't exist
        const response = await apiClient.get<AnalyticsDetails>('/api/analytics/dashboard', { params })
        return response
      } catch (error) {
        console.error('Failed to fetch analytics details:', error)
        // Return empty data structure
        return {
          salesHistory: [],
          topBooks: [],
          demographics: {
            countries: [],
            ageGroups: []
          }
        } as AnalyticsDetails
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

export function useRevenueAnalytics(dateRange?: DateRange) {
  return useQuery({
    queryKey: queryKeys.analytics.revenue(dateRange),
    queryFn: async () => {
      try {
        const params = dateRange ? {
          from: dateRange.from,
          to: dateRange.to
        } : {}
        
        const response = await apiClient.get<RevenueAnalytics>('/api/analytics/dashboard', { params })
        return response
      } catch (error) {
        console.error('Failed to fetch revenue analytics:', error)
        // Return empty structure
        return {
          totalRevenue: 0,
          monthlyRevenue: [],
          revenueByCategory: [],
          topPerformers: [],
        } as RevenueAnalytics
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

export function useBookAnalytics(dateRange?: DateRange) {
  return useQuery({
    queryKey: queryKeys.analytics.books(dateRange),
    queryFn: async () => {
      try {
        const params = dateRange ? {
          from: dateRange.from,
          to: dateRange.to
        } : {}
        
        const response = await apiClient.get<BookAnalytics>('/api/analytics/dashboard', { params })
        return response
      } catch (error) {
        console.error('Failed to fetch book analytics:', error)
        // Return empty structure
        return {
          totalBooks: 0,
          booksByStatus: [],
          booksByCategory: [],
          performanceMetrics: {
            averageWordCount: 0,
            averageRating: 0,
            successRate: 0,
          },
        } as BookAnalytics
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

export function useAudienceAnalytics(dateRange?: DateRange) {
  return useQuery({
    queryKey: queryKeys.analytics.audience(dateRange),
    queryFn: async () => {
      try {
        const params = dateRange ? {
          from: dateRange.from,
          to: dateRange.to
        } : {}
        
        const response = await apiClient.get<AudienceAnalytics>('/api/analytics/dashboard', { params })
        return response
      } catch (error) {
        console.error('Failed to fetch audience analytics:', error)
        // Return empty structure
        return {
          totalReaders: 0,
          demographics: {
            ageGroups: [],
            regions: [],
          },
          engagement: {
            averageReadTime: 0,
            completionRate: 0,
            returnReaderRate: 0,
          },
          preferences: {
            topCategories: [],
            readingPatterns: [],
          },
        } as AudienceAnalytics
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

export function useBookPerformance(bookId: string, dateRange?: DateRange) {
  return useQuery({
    queryKey: queryKeys.analytics.bookPerformance(bookId, dateRange),
    queryFn: async () => {
      try {
        const params = dateRange ? {
          from: dateRange.from,
          to: dateRange.to
        } : {}
        
        const response = await apiClient.get<BookPerformance>(`/api/books/${bookId}/analytics`, { params })
        return response
      } catch (error) {
        console.error('Failed to fetch book performance:', error)
        // Return empty structure
        return {
          id: bookId,
          title: 'Unknown Book',
          views: 0,
          sales: 0,
          revenue: 0,
          rating: 0,
          conversionRate: 0,
          dailyStats: [],
        } as BookPerformance
      }
    },
    enabled: !!bookId,
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

// Export analytics data
export function useExportAnalytics() {
  const handleExport = async (
    type: 'overview' | 'revenue' | 'books' | 'audience',
    dateRange?: DateRange,
    format: 'csv' | 'xlsx' = 'csv'
  ) => {
    try {
      const params = {
        ...dateRange,
        format
      }
      
      // Export functionality not implemented yet, return mock response
      const response = await apiClient.get(`/api/analytics/dashboard`, {
        params,
        responseType: 'blob'
      })
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response as any]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `analytics-${type}-${new Date().toISOString().split('T')[0]}.${format}`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      toast.success(`${type} analytics exported successfully`)
      return { success: true }
    } catch (error) {
      console.error(`Failed to export ${type} analytics:`, error)
      toast.error(`Failed to export ${type} analytics`)
      return { success: false, error }
    }
  }
  
  return {
    exportOverview: (dateRange?: DateRange, format?: 'csv' | 'xlsx') => 
      handleExport('overview', dateRange, format),
    
    exportRevenue: (dateRange?: DateRange, format?: 'csv' | 'xlsx') => 
      handleExport('revenue', dateRange, format),
    
    exportBooks: (dateRange?: DateRange, format?: 'csv' | 'xlsx') => 
      handleExport('books', dateRange, format),
    
    exportAudience: (dateRange?: DateRange, format?: 'csv' | 'xlsx') => 
      handleExport('audience', dateRange, format),
  }
}

// Real-time analytics (using polling)
export function useRealTimeAnalytics(enabled = false) {
  return useQuery({
    queryKey: queryKeys.analytics.realtime(),
    queryFn: async () => {
      try {
        const response = await apiClient.get('/api/analytics/dashboard')
        return response
      } catch (error) {
        console.error('Failed to fetch real-time analytics:', error)
        return {
          currentViews: 0,
          activeSessions: 0,
          recentSales: 0,
          lastUpdate: new Date().toISOString(),
        }
      }
    },
    enabled,
    refetchInterval: enabled ? 30 * 1000 : false, // Poll every 30 seconds when enabled
    staleTime: 0, // Always fresh for real-time data
  })
}

// Analytics comparisons
export function useAnalyticsComparison(
  dateRange: DateRange,
  comparisonRange: DateRange
) {
  return useQuery({
    queryKey: queryKeys.analytics.comparison(dateRange, comparisonRange),
    queryFn: async () => {
      try {
        const response = await apiClient.get('/api/analytics/dashboard', {
          params: {
            currentFrom: dateRange.from,
            currentTo: dateRange.to,
            previousFrom: comparisonRange.from,
            previousTo: comparisonRange.to,
          }
        })
        return response
      } catch (error) {
        console.error('Failed to fetch analytics comparison:', error)
        return {
          current: {
            revenue: 0,
            sales: 0,
            views: 0,
          },
          previous: {
            revenue: 0,
            sales: 0,
            views: 0,
          },
          changes: {
            revenue: 0,
            sales: 0,
            views: 0,
          },
        }
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 1,
  })
}

// Analytics predictions
export function useAnalyticsPredictions(bookId?: string) {
  return useQuery({
    queryKey: queryKeys.analytics.predictions(bookId),
    queryFn: async () => {
      try {
        const url = bookId 
          ? `/api/analytics/dashboard`
          : '/api/analytics/dashboard'
        
        const response = await apiClient.get(url)
        return response
      } catch (error) {
        console.error('Failed to fetch analytics predictions:', error)
        return {
          nextMonth: {
            predictedRevenue: 0,
            predictedSales: 0,
            confidence: 0,
          },
          trends: [],
          recommendations: [],
        }
      }
    },
    staleTime: 60 * 60 * 1000, // 1 hour for predictions
    retry: 1,
  })
}