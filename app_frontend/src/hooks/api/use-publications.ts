import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// Types
export interface Publication {
  id: string;
  book_id: string;
  title: string;
  description?: string;
  platform: string;
  platform_id?: string;
  status: 'draft' | 'published' | 'pending' | 'failed' | 'archived';
  published_date?: string;
  revenue: number;
  sales: number;
  rating?: number;
  review_count?: number;
  metadata?: {
    isbn?: string;
    price?: number;
    currency?: string;
    pages?: number;
    language?: string;
    categories?: string[];
  };
  created_at: string;
  updated_at: string;
}

export interface PublicationMetrics {
  id: string;
  publication_id: string;
  views: number;
  downloads: number;
  conversion_rate: number;
  avg_rating: number;
  total_reviews: number;
  revenue_today: number;
  revenue_week: number;
  revenue_month: number;
  sales_today: number;
  sales_week: number;
  sales_month: number;
  updated_at: string;
}

export interface PublicationFilters {
  status?: string;
  platform?: string;
  sort?: string;
  limit?: number;
  offset?: number;
}

export interface CreatePublicationRequest {
  book_id: string;
  platform: string;
  title?: string;
  description?: string;
  metadata?: {
    price?: number;
    currency?: string;
    categories?: string[];
    language?: string;
  };
}

export interface UpdatePublicationRequest {
  title?: string;
  description?: string;
  status?: string;
  metadata?: Record<string, any>;
}

// Hooks
export function usePublications(filters?: PublicationFilters) {
  return useQuery({
    queryKey: queryKeys.publications.list(filters || {}),
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        
        if (filters?.status) params.append('status', filters.status)
        if (filters?.platform) params.append('platform', filters.platform)
        if (filters?.sort) params.append('sort', filters.sort)
        if (filters?.limit) params.append('limit', filters.limit.toString())
        if (filters?.offset) params.append('offset', filters.offset.toString())
        
        const response = await apiClient.get<{
          publications: Publication[]
          total: number
          page?: number
          limit?: number
        }>(`/api/publications?${params.toString()}`)
        
        return response
      } catch (error) {
        console.error('Failed to fetch publications:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function usePublication(id: string) {
  return useQuery({
    queryKey: queryKeys.publications.detail(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get<Publication>(`/api/publications/${id}`)
        return response
      } catch (error) {
        console.error(`Failed to fetch publication ${id}:`, error)
        throw error
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export function usePublicationMetrics(id: string) {
  return useQuery({
    queryKey: queryKeys.publications.metrics(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get<PublicationMetrics>(`/api/publications/${id}/metrics`)
        return response
      } catch (error) {
        console.error(`Failed to fetch metrics for publication ${id}:`, error)
        throw error
      }
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes - metrics change more frequently
    retry: 2,
  })
}

export function usePublicationStatistics() {
  return useQuery({
    queryKey: queryKeys.publications.statistics(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<{
          totalPublications: number;
          activePublications: number;
          totalRevenue: number;
          totalSales: number;
          avgRating: number;
          platforms: Array<{
            platform: string;
            count: number;
            revenue: number;
          }>;
          recentActivity: Array<{
            id: string;
            type: string;
            title: string;
            timestamp: string;
          }>;
        }>('/api/publications/summary/statistics')
        
        return response
      } catch (error) {
        console.error('Failed to fetch publication statistics:', error)
        throw error
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

export function useCreatePublication() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreatePublicationRequest) => 
      apiClient.post<Publication>('/api/publications', data),
    onMutate: async (newPublication) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.publications.lists() })
      
      // Snapshot the previous value
      const previousPublications = queryClient.getQueryData(queryKeys.publications.lists())
      
      // Return a context object with the snapshotted value
      return { previousPublications }
    },
    onError: (err, newPublication, context) => {
      // Revert the optimistic update
      if (context?.previousPublications) {
        queryClient.setQueriesData(
          { queryKey: queryKeys.publications.lists() },
          context.previousPublications
        )
      }
      toast.error('Failed to create publication')
    },
    onSuccess: (data) => {
      toast.success('Publication created successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.publications.lists() })
      queryClient.invalidateQueries({ queryKey: queryKeys.publications.statistics() })
    },
  })
}

export function useUpdatePublication() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePublicationRequest }) =>
      apiClient.patch<Publication>(`/api/publications/${id}`, data),
    onSuccess: (data) => {
      // Update the cache
      queryClient.setQueryData(queryKeys.publications.detail(data.id), data)
      toast.success('Publication updated successfully!')
    },
    onError: () => {
      toast.error('Failed to update publication')
    },
    onSettled: (data) => {
      if (data) {
        queryClient.invalidateQueries({ queryKey: queryKeys.publications.detail(data.id) })
        queryClient.invalidateQueries({ queryKey: queryKeys.publications.lists() })
        queryClient.invalidateQueries({ queryKey: queryKeys.publications.statistics() })
      }
    },
  })
}

export function useDeletePublication() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/api/publications/${id}`),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.publications.detail(id) })
      queryClient.removeQueries({ queryKey: queryKeys.publications.metrics(id) })
      toast.success('Publication deleted successfully!')
    },
    onError: () => {
      toast.error('Failed to delete publication')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.publications.lists() })
      queryClient.invalidateQueries({ queryKey: queryKeys.publications.statistics() })
    },
  })
}

export function usePublicationStatus(id: string) {
  return useQuery({
    queryKey: queryKeys.publications.status(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get<{
          status: string;
          message?: string;
          progress?: number;
          details?: Record<string, any>;
        }>(`/api/publications/${id}/status`)
        
        return response
      } catch (error) {
        console.error(`Failed to fetch status for publication ${id}:`, error)
        throw error
      }
    },
    enabled: !!id,
    staleTime: 30 * 1000, // 30 seconds - status changes frequently
    refetchInterval: 5 * 1000, // Poll every 5 seconds for status updates
    retry: 2,
  })
}