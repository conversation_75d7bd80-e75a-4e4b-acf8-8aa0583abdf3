'use client'

import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'

// Types
export interface VerlMonitoringData {
  training_status: 'idle' | 'training' | 'evaluating' | 'completed' | 'failed';
  current_epoch?: number;
  total_epochs?: number;
  training_loss?: number;
  validation_loss?: number;
  accuracy?: number;
  estimated_completion?: string;
  last_training_session?: {
    id: string;
    start_time: string;
    end_time?: string;
    duration?: number;
    status: string;
    metrics?: {
      loss: number;
      accuracy: number;
      samples_processed: number;
    };
  };
  metrics: {
    total_training_sessions: number;
    successful_sessions: number;
    failed_sessions: number;
    avg_training_time: number;
    model_improvements: number;
    performance_score: number;
  };
  active_jobs: Array<{
    id: string;
    type: string;
    status: string;
    progress: number;
    started_at: string;
    estimated_completion?: string;
  }>;
}

export interface SystemMonitoringData {
  system_health: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    last_check: string;
    components: Array<{
      name: string;
      status: 'healthy' | 'warning' | 'critical';
      response_time?: number;
      error_rate?: number;
      last_check: string;
    }>;
  };
  api_metrics: {
    total_requests: number;
    requests_per_minute: number;
    avg_response_time: number;
    error_rate: number;
    active_connections: number;
    rate_limit_hits: number;
  };
  database_metrics: {
    connections: number;
    queries_per_second: number;
    avg_query_time: number;
    slow_queries: number;
    cache_hit_rate: number;
    storage_usage: number;
  };
  ai_services: {
    openai: {
      status: 'healthy' | 'warning' | 'critical';
      requests_today: number;
      avg_response_time: number;
      error_rate: number;
      quota_usage: number;
    };
    anthropic: {
      status: 'healthy' | 'warning' | 'critical';
      requests_today: number;
      avg_response_time: number;
      error_rate: number;
      quota_usage: number;
    };
  };
  recent_errors: Array<{
    timestamp: string;
    type: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    resolved: boolean;
  }>;
}

export interface PerformanceMetrics {
  response_times: Array<{
    endpoint: string;
    avg_time: number;
    min_time: number;
    max_time: number;
    requests_count: number;
  }>;
  error_rates: Array<{
    endpoint: string;
    error_rate: number;
    total_requests: number;
    error_count: number;
  }>;
  throughput: {
    requests_per_second: number;
    peak_rps: number;
    avg_rps: number;
  };
}

// Hooks
export function useVerlMonitoring() {
  return useQuery({
    queryKey: ['monitoring', 'verl'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<VerlMonitoringData>('/api/monitoring/verl')
        return response
      } catch (error) {
        console.error('Failed to fetch VERL monitoring data:', error)
        throw error
      }
    },
    staleTime: 30 * 1000, // 30 seconds - training data changes frequently
    refetchInterval: 30 * 1000, // Poll every 30 seconds
    retry: 2,
  })
}

export function useSystemMonitoring() {
  return useQuery({
    queryKey: ['monitoring', 'system'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<SystemMonitoringData>('/api/monitoring/system')
        return response
      } catch (error) {
        console.error('Failed to fetch system monitoring data:', error)
        throw error
      }
    },
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000, // Poll every minute
    retry: 2,
  })
}

export function usePerformanceMetrics(timeRange?: string) {
  return useQuery({
    queryKey: ['monitoring', 'performance', timeRange],
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        if (timeRange) params.append('timeRange', timeRange)
        
        const response = await apiClient.get<PerformanceMetrics>(
          `/api/monitoring/performance?${params.toString()}`
        )
        return response
      } catch (error) {
        console.error('Failed to fetch performance metrics:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Real-time monitoring hook with configurable polling
export function useRealTimeMonitoring(
  enabled = false,
  pollingInterval = 10000 // 10 seconds
) {
  const verlQuery = useQuery({
    queryKey: ['monitoring', 'verl', 'realtime'],
    queryFn: async () => {
      const response = await apiClient.get<VerlMonitoringData>('/api/monitoring/verl')
      return response
    },
    enabled,
    refetchInterval: enabled ? pollingInterval : false,
    staleTime: 0, // Always fresh for real-time
    retry: 1,
  })

  const systemQuery = useQuery({
    queryKey: ['monitoring', 'system', 'realtime'],
    queryFn: async () => {
      const response = await apiClient.get<SystemMonitoringData>('/api/monitoring/system')
      return response
    },
    enabled,
    refetchInterval: enabled ? pollingInterval : false,
    staleTime: 0,
    retry: 1,
  })

  return {
    verl: verlQuery,
    system: systemQuery,
    isLoading: verlQuery.isLoading || systemQuery.isLoading,
    hasError: verlQuery.error || systemQuery.error,
  }
}

// Hook for monitoring alerts and notifications
export function useMonitoringAlerts() {
  return useQuery({
    queryKey: ['monitoring', 'alerts'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<{
          alerts: Array<{
            id: string;
            type: 'warning' | 'error' | 'info';
            title: string;
            message: string;
            timestamp: string;
            resolved: boolean;
            severity: 'low' | 'medium' | 'high' | 'critical';
          }>;
          unread_count: number;
        }>('/api/monitoring/alerts')
        return response
      } catch (error) {
        console.error('Failed to fetch monitoring alerts:', error)
        throw error
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 2 * 60 * 1000, // Poll every 2 minutes
    retry: 2,
  })
}

export default useVerlMonitoring