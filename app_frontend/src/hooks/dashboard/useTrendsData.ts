'use client'

import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'

export interface TrendsFilters {
  timeRange?: '24h' | '7d' | '30d' | '90d' | '1y'
  region?: string
  category?: string
  sortBy?: 'volume' | 'growth' | 'sentiment'
  orderBy?: 'asc' | 'desc'
}

export interface TrendsData {
  metrics: {
    totalVolume: number
    avgPrice: number
    avgHoldingDuration: number
    volumeChange: number
    priceChange: number
    durationChange: number
  }
  chartData: Array<{
    timestamp: string
    value: number
    volume: number
  }>
  heatmapData: Array<{
    region: string
    lat: number
    lng: number
    volume: number
    avgPrice: number
    sentiment: number
    count: number
  }>
  forecasts: Array<{
    category: string
    confidence: number
    prediction: string
    timeframe: string
    impact: 'high' | 'medium' | 'low'
  }>
  sentiments: Array<{
    keyword: string
    sentiment: number
    mentions: number
    change: number
  }>
}

export function useTrendsData(filters: TrendsFilters = {}) {
  return useQuery({
    queryKey: queryKeys.trends.dashboard(filters),
    queryFn: async () => {
      try {
        const params = new URLSearchParams()
        
        if (filters.timeRange) params.append('timeRange', filters.timeRange)
        if (filters.region) params.append('region', filters.region)
        if (filters.category) params.append('category', filters.category)
        if (filters.sortBy) params.append('sortBy', filters.sortBy)
        if (filters.orderBy) params.append('orderBy', filters.orderBy)
        
        const response = await apiClient.get<TrendsData>(`/api/trends/dashboard?${params.toString()}`)
        return response
      } catch (error) {
        console.error('Failed to fetch trends dashboard data:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Hook for trend alerts
export function useTrendAlerts() {
  return useQuery({
    queryKey: queryKeys.trends.alerts(),
    queryFn: async () => {
      try {
        const response = await apiClient.get('/api/trends/alerts')
        return response
      } catch (error) {
        console.error('Failed to fetch trend alerts:', error)
        throw error
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes - alerts need to be more frequent
    retry: 2,
  })
}

// Hook for trending topics
export function useTrendingTopics(limit = 10) {
  return useQuery({
    queryKey: queryKeys.trends.topics(limit),
    queryFn: async () => {
      try {
        const response = await apiClient.get(`/api/trends/topics?limit=${limit}`)
        return response
      } catch (error) {
        console.error('Failed to fetch trending topics:', error)
        throw error
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

// Hook for market sentiment
export function useMarketSentiment() {
  return useQuery({
    queryKey: queryKeys.trends.sentiment(),
    queryFn: async () => {
      try {
        const response = await apiClient.get('/api/trends/sentiment')
        return response
      } catch (error) {
        console.error('Failed to fetch market sentiment:', error)
        throw error
      }
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

export default useTrendsData