"use client"

import { requireAuth } from "@/hoc/withAuth"
import { requireAdmin, requireAuthorOrAbove, requirePermissions } from "@/hoc/withRole"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Container } from "@/components/ui/container"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Heading, Text } from "@/components/ui/typography"
import { Shield, User, Edit, Settings } from "lucide-react"

// Example 1: Basic authentication required
function BasicProtectedComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Basic Protected Content
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Text>This content requires any authenticated user.</Text>
        <Button className="mt-4">Perform Action</Button>
      </CardContent>
    </Card>
  )
}

// Example 2: Admin-only component
function AdminOnlyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Admin Only Content
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Text>This content is only visible to administrators.</Text>
        <div className="mt-4 space-y-2">
          <Button variant="destructive">Delete All Data</Button>
          <Button variant="outline">Manage Users</Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Example 3: Author and above component
function AuthorContentComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit className="h-5 w-5" />
          Author Content Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Text>This content is available to authors, editors, and admins.</Text>
        <div className="mt-4 space-y-2">
          <Button>Create New Content</Button>
          <Button variant="outline">Edit Existing</Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Example 4: Permission-based component
function PermissionBasedComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Content Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Text>This content requires specific permissions.</Text>
        <div className="mt-4 space-y-2">
          <Button>Manage Content</Button>
          <Button variant="outline">View Analytics</Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Apply HOCs to protect components
const ProtectedBasic = requireAuth(BasicProtectedComponent)
const ProtectedAdmin = requireAdmin(AdminOnlyComponent)
const ProtectedAuthor = requireAuthorOrAbove(AuthorContentComponent)
const ProtectedPermissions = requirePermissions(["manage_content"])(PermissionBasedComponent)

// Main page component demonstrating different protection levels
function ExamplePage() {
  return (
    <DashboardLayout>
      <Container maxWidth="7xl" className="py-8">
        <div className="space-y-8">
          <div className="space-y-2">
            <Heading as="h1" variant="h2">
              HOC Protection Examples
            </Heading>
            <Text variant="lead" className="text-muted-foreground">
              Examples of different authentication and authorization patterns using HOCs
            </Text>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ProtectedBasic />
            <ProtectedAdmin />
            <ProtectedAuthor />
            <ProtectedPermissions />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>HOC Usage Examples</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Text className="font-medium">Basic Authentication:</Text>
                  <Text variant="small" className="text-muted-foreground font-mono">
                    const ProtectedComponent = requireAuth(MyComponent)
                  </Text>
                </div>
                
                <div>
                  <Text className="font-medium">Admin Only:</Text>
                  <Text variant="small" className="text-muted-foreground font-mono">
                    const AdminComponent = requireAdmin(MyComponent)
                  </Text>
                </div>
                
                <div>
                  <Text className="font-medium">Role-based (Author+):</Text>
                  <Text variant="small" className="text-muted-foreground font-mono">
                    const AuthorComponent = requireAuthorOrAbove(MyComponent)
                  </Text>
                </div>
                
                <div>
                  <Text className="font-medium">Permission-based:</Text>
                  <Text variant="small" className="text-muted-foreground font-mono">
                    const PermissionComponent = requirePermissions(["manage_content"])(MyComponent)
                  </Text>
                </div>
                
                <div>
                  <Text className="font-medium">Custom Role Configuration:</Text>
                  <Text variant="small" className="text-muted-foreground font-mono">
                    const CustomComponent = withRole(MyComponent, {`{`} allowedRoles: ["editor", "admin"] {`}`})
                  </Text>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Container>
    </DashboardLayout>
  )
}

// Protect the entire page with basic authentication
export default requireAuth(ExamplePage)