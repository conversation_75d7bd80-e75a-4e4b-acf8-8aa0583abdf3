import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Container } from "@/components/ui/container"
import { Heading, Text } from "@/components/ui/typography"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  Sparkles, 
  TrendingUp, 
  DollarSign, 
  BarChart3, 
  Upload,
  CheckCircle,
  ArrowRight,
  Star,
  Users,
  Zap
} from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <Container>
          <div className="flex h-16 items-center justify-between">
            <Link href="/" className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              <span className="font-display text-xl font-semibold">
                Publish AI
              </span>
            </Link>

            <div className="flex items-center gap-4">
              <Link href="/auth/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/register">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </Container>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <Badge className="mb-4" variant="secondary">
              <Sparkles className="h-3 w-3 mr-1" />
              AI-Powered Publishing Platform
            </Badge>

            <Heading as="h1" variant="h1" className="mb-6">
              Create, Publish, and Monetize Books
              <span className="text-primary"> with AI</span>
            </Heading>

            <Text
              variant="lead"
              className="mb-8 text-muted-foreground max-w-2xl mx-auto"
            >
              Transform your ideas into published books in minutes. Our AI
              agents handle everything from manuscript generation to Amazon KDP
              publishing, while you focus on growing your publishing business.
            </Text>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="gap-2">
                  Start Publishing Now
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="#features">
                <Button size="lg" variant="outline">
                  See How It Works
                </Button>
              </Link>
            </div>
          </div>

          {/* Hero Image - Placeholder */}
          <div className="mt-16 relative">
            <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent z-10" />
            <div className="w-full h-[600px] bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl shadow-2xl border flex items-center justify-center">
              <div className="text-center">
                <BookOpen className="h-24 w-24 text-primary/50 mx-auto mb-4" />
                <p className="text-muted-foreground">AI Book Publishing Dashboard</p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-surface">
        <Container>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {[
              { label: "Books Published", value: "10,000+", icon: BookOpen },
              { label: "Active Authors", value: "2,500+", icon: Users },
              { label: "Revenue Generated", value: "$1M+", icon: DollarSign },
              { label: "AI Models", value: "13", icon: Zap },
            ].map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index}>
                  <Icon className="h-8 w-8 text-primary mx-auto mb-2" />
                  <div className="text-3xl font-bold">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </Container>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24">
        <Container>
          <div className="text-center mb-16">
            <Heading as="h2" variant="h2" className="mb-4">
              Everything You Need to Succeed
            </Heading>
            <Text
              variant="lead"
              className="text-muted-foreground max-w-2xl mx-auto"
            >
              Our platform combines cutting-edge AI with proven publishing
              strategies to help you build a successful publishing business.
            </Text>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Sparkles,
                title: "AI-Powered Writing",
                description:
                  "Generate high-quality manuscripts with our advanced AI models. From fiction to non-fiction, create compelling content in any genre.",
              },
              {
                icon: TrendingUp,
                title: "Trend Analysis",
                description:
                  "Discover profitable niches and trending topics. Our AI analyzes market data to help you publish books that sell.",
              },
              {
                icon: Upload,
                title: "Automated Publishing",
                description:
                  "One-click publishing to Amazon KDP and other platforms. We handle formatting, metadata, and all technical details.",
              },
              {
                icon: BarChart3,
                title: "Sales Analytics",
                description:
                  "Track your book performance with detailed analytics. Monitor sales, revenue, and reader engagement in real-time.",
              },
              {
                icon: DollarSign,
                title: "Revenue Optimization",
                description:
                  "AI-driven pricing strategies and marketing recommendations to maximize your book sales and royalties.",
              },
              {
                icon: Users,
                title: "Reader Insights",
                description:
                  "Understand your audience with AI-powered reader behavior analysis and personalized content recommendations.",
              },
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="border-0 shadow-lg">
                  <CardHeader>
                    <Icon className="h-10 w-10 text-primary mb-4" />
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </Container>
      </section>

      {/* How It Works Section */}
      <section className="py-24 bg-surface">
        <Container>
          <div className="text-center mb-16">
            <Heading as="h2" variant="h2" className="mb-4">
              How It Works
            </Heading>
            <Text
              variant="lead"
              className="text-muted-foreground max-w-2xl mx-auto"
            >
              From idea to published book in three simple steps
            </Text>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                step: "1",
                title: "Choose Your Topic",
                description:
                  "Select a topic or let our AI suggest trending niches based on market data.",
              },
              {
                step: "2",
                title: "Generate Content",
                description:
                  "Our AI creates a complete manuscript, cover design, and optimized metadata.",
              },
              {
                step: "3",
                title: "Publish & Earn",
                description:
                  "Review, approve, and publish directly to Amazon KDP and start earning royalties.",
              },
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <Heading as="h3" variant="h5" className="mb-2">
                  {step.title}
                </Heading>
                <Text variant="muted">{step.description}</Text>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/auth/register">
              <Button size="lg" className="gap-2">
                Start Your Publishing Journey
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section className="py-24">
        <Container>
          <div className="text-center mb-16">
            <Heading as="h2" variant="h2" className="mb-4">
              Authors Love Publish AI
            </Heading>
            <Text
              variant="lead"
              className="text-muted-foreground max-w-2xl mx-auto"
            >
              Join thousands of successful authors using AI to build their
              publishing empire
            </Text>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "Fiction Author",
                content:
                  "I've published 15 books in 3 months! The AI-generated content is amazing and the automated publishing saves me hours.",
                rating: 5,
              },
              {
                name: "Michael Chen",
                role: "Business Writer",
                content:
                  "The trend analysis feature helped me find profitable niches I never would have discovered. My revenue has tripled!",
                rating: 5,
              },
              {
                name: "Emma Davis",
                role: "Self-Help Author",
                content:
                  "From someone who struggled to write, I now have a catalog of 20+ books. This platform changed my life!",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex gap-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-primary text-primary"
                      />
                    ))}
                  </div>
                  <CardTitle className="text-base">
                    {testimonial.name}
                  </CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Text variant="muted" className="italic">
                    "{testimonial.content}"
                  </Text>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-primary text-primary-foreground">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <Heading
              as="h2"
              variant="h2"
              className="mb-4 text-primary-foreground"
            >
              Ready to Start Your Publishing Journey?
            </Heading>
            <Text variant="lead" className="mb-8 text-primary-foreground/90">
              Join thousands of authors who are building passive income streams
              with AI-powered publishing. Start your free trial today.
            </Text>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" variant="secondary" className="gap-2">
                  Get Started Free
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-transparent text-primary-foreground border-primary-foreground hover:bg-primary-foreground/10"
                >
                  View Pricing
                </Button>
              </Link>
            </div>
            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>No credit card required</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>14-day free trial</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t">
        <Container>
          <div className="text-center text-sm text-muted-foreground">
            © {new Date().getFullYear()} Publish AI. All rights reserved.
          </div>
        </Container>
      </footer>
    </div>
  );
}