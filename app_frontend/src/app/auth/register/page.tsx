"use client"

import { useState } from "react"
import { signIn } from "next-auth/react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import Link from "next/link"
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Container } from "@/components/ui/container"
import { Heading, Text } from "@/components/ui/typography"
import { ErrorMessage } from "@/components/ui/error-message"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { BookOpen, CheckCircle, ArrowLeft } from "lucide-react"
import { apiClient } from "@/lib/api"
import { OAuthProviderGrid } from "@/components/auth/oauth-provider-grid"

const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, "You must accept the terms and conditions"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const password = watch("password")

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      // Register user via API
      await apiClient.post("/api/auth/register", {
        name: data.name,
        email: data.email,
        password: data.password,
      });

      setSuccess(true)
      
      // Auto-login after successful registration
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        setError("Registration successful, but failed to log in. Please try logging in manually.")
      } else {
        router.push("/dashboard")
      }
    } catch (error: any) {
      setError(error.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOAuthSignIn = async (provider: string) => {
    try {
      await signIn(provider, { callbackUrl: "/dashboard" })
    } catch (error) {
      setError(`Failed to sign in with ${provider}`)
    }
  }

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: "" }
    
    let strength = 0
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    if (/[^a-zA-Z\d]/.test(password)) strength++

    const labels = ["Very Weak", "Weak", "Fair", "Good", "Strong"]
    return { strength, label: labels[strength - 1] || "" }
  }

  const passwordStrength = getPasswordStrength(password || "")

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary text-primary-foreground flex-col justify-center p-12">
        <Link href="/" className="flex items-center gap-2 mb-8">
          <BookOpen className="h-8 w-8" />
          <span className="font-display text-2xl font-semibold">
            Publish AI
          </span>
        </Link>

        <div className="space-y-6">
          <Heading as="h1" variant="h2" className="text-primary-foreground">
            Start your publishing journey today
          </Heading>

          <Text variant="lead" className="text-primary-foreground/90">
            Join thousands of authors who are building successful publishing
            businesses with AI.
          </Text>

          <div className="space-y-4">
            {[
              "Generate high-quality manuscripts in minutes",
              "Automate your Amazon KDP publishing",
              "Track sales and optimize performance",
              "Access trending topics and market insights",
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-primary-foreground" />
                <Text className="text-primary-foreground/90">{feature}</Text>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Side - Register Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <Container maxWidth="sm">
          <div className="w-full max-w-md mx-auto">
            {/* Back to Home Button */}
            <div className="mb-6">
              <Link 
                href="/" 
                className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Home
              </Link>
            </div>
            
            <div className="text-center mb-8">
              <Link
                href="/"
                className="flex items-center gap-2 justify-center mb-6 lg:hidden"
              >
                <BookOpen className="h-6 w-6 text-primary" />
                <span className="font-display text-xl font-semibold">
                  Publish AI
                </span>
              </Link>

              <Heading as="h1" variant="h3" className="mb-2">
                Create your account
              </Heading>
              <Text variant="muted">
                Get started with your free account today.
              </Text>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Sign Up</CardTitle>
                <CardDescription>
                  Create your account to start publishing with AI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {error && <ErrorMessage message={error} />}

                {success && (
                  <div className="rounded-lg border border-success/50 bg-success/10 p-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-success" />
                      <Text variant="small" className="text-success">
                        Account created successfully! Redirecting to
                        dashboard...
                      </Text>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your full name"
                      {...register("name")}
                      className={errors.name ? "border-destructive" : ""}
                    />
                    {errors.name && (
                      <Text variant="caption" className="text-destructive">
                        {errors.name.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      {...register("email")}
                      className={errors.email ? "border-destructive" : ""}
                    />
                    {errors.email && (
                      <Text variant="caption" className="text-destructive">
                        {errors.email.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Create a strong password"
                      {...register("password")}
                      className={errors.password ? "border-destructive" : ""}
                    />
                    {password && (
                      <div className="space-y-1">
                        <div className="flex gap-1">
                          {[...Array(5)].map((_, i) => (
                            <div
                              key={i}
                              className={`h-1 flex-1 rounded ${
                                i < passwordStrength.strength
                                  ? passwordStrength.strength <= 2
                                    ? "bg-destructive"
                                    : passwordStrength.strength <= 3
                                    ? "bg-warning"
                                    : "bg-success"
                                  : "bg-muted"
                              }`}
                            />
                          ))}
                        </div>
                        {passwordStrength.label && (
                          <Text
                            variant="caption"
                            className="text-muted-foreground"
                          >
                            Password strength: {passwordStrength.label}
                          </Text>
                        )}
                      </div>
                    )}
                    {errors.password && (
                      <Text variant="caption" className="text-destructive">
                        {errors.password.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Confirm your password"
                      {...register("confirmPassword")}
                      className={
                        errors.confirmPassword ? "border-destructive" : ""
                      }
                    />
                    {errors.confirmPassword && (
                      <Text variant="caption" className="text-destructive">
                        {errors.confirmPassword.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Controller
                        name="acceptTerms"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <Checkbox
                            id="acceptTerms"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        )}
                      />
                      <Label htmlFor="acceptTerms" className="text-sm">
                        I agree to the{" "}
                        <Link
                          href="/terms"
                          className="text-primary hover:underline"
                        >
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link
                          href="/privacy"
                          className="text-primary hover:underline"
                        >
                          Privacy Policy
                        </Link>
                      </Label>
                    </div>
                    {errors.acceptTerms && (
                      <Text variant="caption" className="text-destructive">
                        {errors.acceptTerms.message}
                      </Text>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Creating account...
                      </>
                    ) : (
                      "Create account"
                    )}
                  </Button>
                </form>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with
                    </span>
                  </div>
                </div>

                <OAuthProviderGrid 
                  onSignIn={handleOAuthSignIn}
                  columns={2}
                  variant="outline"
                  providers={["google", "apple"]}
                />
                <Text variant="caption" className="text-muted-foreground text-center">
                  Sign up with Google or Apple for quick registration
                </Text>

                <div className="text-center">
                  <Text variant="small" className="text-muted-foreground">
                    Already have an account?{" "}
                    <Link
                      href="/auth/login"
                      className="text-primary hover:underline font-medium"
                    >
                      Sign in
                    </Link>
                  </Text>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </div>
    </div>
  );
}