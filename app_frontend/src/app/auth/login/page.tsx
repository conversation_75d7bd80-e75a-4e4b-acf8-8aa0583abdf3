"use client"

import { useState, useEffect } from "react"
import { signIn, getSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Container } from "@/components/ui/container"
import { Heading, Text } from "@/components/ui/typography"
import { ErrorMessage } from "@/components/ui/error-message"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { BookOpen, ArrowLeft } from "lucide-react"
import { clearSensitiveUrlParams } from "@/lib/security"
import { OAuthProviderGrid } from "@/components/auth/oauth-provider-grid"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Security: Clear any sensitive URL parameters on component mount
  useEffect(() => {
    clearSensitiveUrlParams()
  }, [])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      // Security: Ensure credentials are never exposed in URLs or logs
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        setError("Invalid email or password")
      } else if (result?.ok) {
        // Security: Use router.replace to prevent credentials in browser history
        router.replace("/dashboard")
        // Fallback redirect after short delay
        setTimeout(() => {
          if (window.location.pathname !== "/dashboard") {
            window.location.href = "/dashboard"
          }
        }, 1000)
      } else {
        setError("Authentication failed. Please try again.")
      }
    } catch (error) {
      setError("Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOAuthSignIn = async (provider: string) => {
    try {
      await signIn(provider, { callbackUrl: "/dashboard" })
    } catch (error) {
      setError(`Failed to sign in with ${provider}`)
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary text-primary-foreground flex-col justify-center p-12">
        <Link href="/" className="flex items-center gap-2 mb-8">
          <BookOpen className="h-8 w-8" />
          <span className="font-display text-2xl font-semibold">Publish AI</span>
        </Link>
        
        <div className="space-y-6">
          <Heading as="h1" variant="h2" className="text-primary-foreground">
            Welcome back to your publishing journey
          </Heading>
          
          <Text variant="lead" className="text-primary-foreground/90">
            Sign in to access your dashboard, manage your books, and track your success.
          </Text>
          
          <div className="space-y-4">
            {[
              "Access your AI-generated manuscripts",
              "Track sales and analytics",
              "Manage your publishing portfolio",
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                <Text className="text-primary-foreground/90">{feature}</Text>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <Container maxWidth="sm">
          <div className="w-full max-w-md mx-auto">
            {/* Back to Home Button */}
            <div className="mb-6">
              <Link 
                href="/" 
                className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Home
              </Link>
            </div>
            
            <div className="text-center mb-8">
              <Link href="/" className="flex items-center gap-2 justify-center mb-6 lg:hidden">
                <BookOpen className="h-6 w-6 text-primary" />
                <span className="font-display text-xl font-semibold">Publish AI</span>
              </Link>
              
              <Heading as="h1" variant="h3" className="mb-2">
                Sign in to your account
              </Heading>
              <Text variant="muted">
                Welcome back! Please enter your details.
              </Text>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Login</CardTitle>
                <CardDescription>
                  Enter your email and password to access your account
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {error && (
                  <ErrorMessage message={error} />
                )}

                <form onSubmit={handleSubmit(onSubmit)} method="post" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      autoComplete="email"
                      {...register("email")}
                      className={errors.email ? "border-destructive" : ""}
                    />
                    {errors.email && (
                      <Text variant="caption" className="text-destructive">
                        {errors.email.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Password</Label>
                      <Link
                        href="/auth/forgot-password"
                        className="text-sm text-primary hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      autoComplete="current-password"
                      {...register("password")}
                      className={errors.password ? "border-destructive" : ""}
                    />
                    {errors.password && (
                      <Text variant="caption" className="text-destructive">
                        {errors.password.message}
                      </Text>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Signing in...
                      </>
                    ) : (
                      "Sign in"
                    )}
                  </Button>
                </form>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <OAuthProviderGrid 
                    onSignIn={handleOAuthSignIn}
                    columns={2}
                    variant="outline"
                    providers={["google", "apple"]}
                  />
                  <Text variant="caption" className="text-muted-foreground text-center">
                    Sign in with Google or Apple for quick access
                  </Text>
                </div>

                <div className="text-center">
                  <Text variant="small" className="text-muted-foreground">
                    Don't have an account?{" "}
                    <Link
                      href="/auth/register"
                      className="text-primary hover:underline font-medium"
                    >
                      Sign up
                    </Link>
                  </Text>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </div>
    </div>
  )
}