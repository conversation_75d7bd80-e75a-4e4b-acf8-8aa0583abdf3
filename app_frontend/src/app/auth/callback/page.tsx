"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { signIn } from "next-auth/react"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Container } from "@/components/ui/container"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ErrorMessage } from "@/components/ui/error-message"
import { apiClient } from "@/lib/api"

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const handleCallback = async () => {
      // Check for Supabase auth hash parameters
      const hashParams = new URLSearchParams(window.location.hash.substring(1))
      const accessToken = hashParams.get('access_token') || searchParams.get('access_token')
      const refreshToken = hashParams.get('refresh_token') || searchParams.get('refresh_token')
      
      // OAuth error handling
      const errorParam = searchParams.get('error') || hashParams.get('error')
      const errorDescription = searchParams.get('error_description') || hashParams.get('error_description')

      if (errorParam) {
        setError(errorDescription || errorParam)
        // Redirect to error page after a short delay
        setTimeout(() => {
          router.push(`/auth/error?error=${encodeURIComponent(errorDescription || errorParam)}`)
        }, 2000)
        return
      }

      if (accessToken && refreshToken) {
        try {
          // Handle OAuth callback with tokens
          const response = await apiClient.post('/api/auth/oauth/callback', {
            access_token: accessToken,
            refresh_token: refreshToken,
          })

          if (response.access_token) {
            // Store the backend token
            apiClient.setAuthToken(response.access_token)
            
            // Sign in with NextAuth using the backend response
            const result = await signIn('credentials', {
              redirect: false,
              email: response.user.email,
              password: 'oauth-authenticated', // Special password for OAuth users
            })

            if (result?.ok) {
              router.push('/dashboard')
            } else {
              setError('Authentication failed')
              setTimeout(() => {
                router.push('/auth/error?error=auth_failed')
              }, 2000)
            }
          }
        } catch (error: any) {
          console.error('OAuth callback error:', error)
          setError(error.message || 'Authentication failed')
          setTimeout(() => {
            router.push('/auth/error?error=callback_failed')
          }, 2000)
        }
      } else {
        // No tokens provided, check if this is a regular NextAuth callback
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        
        if (code) {
          // This might be a standard OAuth flow handled by NextAuth
          // Let NextAuth handle it by redirecting to the appropriate page
          router.push('/dashboard')
        } else {
          // No valid callback parameters, redirect to login
          router.push('/auth/login')
        }
      }
    }

    handleCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center p-8">
      <Container maxWidth="sm">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle>Authenticating...</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            {error ? (
              <>
                <ErrorMessage message={error} className="mb-4" />
                <p className="text-sm text-muted-foreground">
                  Redirecting you back to login...
                </p>
              </>
            ) : (
              <>
                <LoadingSpinner size="lg" className="mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">
                  Please wait while we complete your authentication.
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </Container>
    </div>
  )
}