"use client"

import { AdminOnly } from "@/components/auth/RoleGuard"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Container } from "@/components/ui/container"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Heading, Text } from "@/components/ui/typography"
import { Users, Settings, BarChart, Shield, Database, Activity } from "lucide-react"

export default function AdminDashboardPage() {
  return (
    <AdminOnly>
      <DashboardLayout>
        <Container maxWidth="7xl" className="py-8">
          <div className="space-y-8">
            {/* Header */}
            <div className="space-y-2">
              <Heading as="h1" variant="h2" className="flex items-center gap-2">
                <Shield className="h-8 w-8 text-primary" />
                Admin Dashboard
              </Heading>
              <Text variant="lead" className="text-muted-foreground">
                System administration and management controls
              </Text>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,234</div>
                  <p className="text-xs text-muted-foreground">+12% from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Health</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">99.9%</div>
                  <p className="text-xs text-muted-foreground">Uptime this month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">68%</div>
                  <p className="text-xs text-muted-foreground">Of total capacity</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">API Requests</CardTitle>
                  <BarChart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">45.2K</div>
                  <p className="text-xs text-muted-foreground">Last 24 hours</p>
                </CardContent>
              </Card>
            </div>

            {/* Admin Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    User Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Text variant="small" className="text-muted-foreground">
                    Manage user accounts, roles, and permissions
                  </Text>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start">
                      View All Users
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Role Assignments
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Permissions Matrix
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    System Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Text variant="small" className="text-muted-foreground">
                    Configure system-wide settings and preferences
                  </Text>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start">
                      General Settings
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Security Settings
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      API Configuration
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart className="h-5 w-5" />
                    Analytics & Reports
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Text variant="small" className="text-muted-foreground">
                    View detailed analytics and generate reports
                  </Text>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start">
                      Usage Analytics
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Performance Reports
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Export Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent System Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { action: "User registration", user: "<EMAIL>", time: "2 minutes ago" },
                    { action: "Role updated", user: "Admin", time: "5 minutes ago" },
                    { action: "System backup", user: "System", time: "1 hour ago" },
                    { action: "Security scan", user: "System", time: "2 hours ago" },
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b last:border-0">
                      <div>
                        <Text className="font-medium">{activity.action}</Text>
                        <Text variant="small" className="text-muted-foreground">{activity.user}</Text>
                      </div>
                      <Text variant="small" className="text-muted-foreground">{activity.time}</Text>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </DashboardLayout>
    </AdminOnly>
  )
}