"use client"

import React, { useState } from "react"
import { useSession } from "next-auth/react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Button } from "@/components/ui/button"
import { useTheme } from "@/contexts/theme-context"
import {
  useUserProfile,
  useUserPreferences,
  usePublishingSettings,
  usePlatformIntegrations,
  useSecuritySettings,
  useActiveSessions,
  useUpdateProfile,
  useUpdatePreferences,
  useUpdatePublishingSettings,
  useTogglePlatformIntegration,
  useUpdateSecuritySettings,
  useRegenerateApiKey,
  useTerminateSession
} from "@/hooks/api/use-settings"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { StatsCard } from "@/components/ui/stats-card"
import {
  User,
  Bell,
  CreditCard,
  Shield,
  BookOpen,
  Globe,
  Zap,
  Settings,
  Save,
  Upload,
  Download,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Trash2,
  Plus
} from "lucide-react"

interface UserSettings {
  profile: {
    name: string
    email: string
    bio: string
    website: string
    location: string
    timezone: string
  }
  preferences: {
    language: string
    currency: string
    theme: string
    emailNotifications: boolean
    pushNotifications: boolean
    marketingEmails: boolean
  }
  publishing: {
    defaultGenre: string
    authorPseudonym: string
    publisherName: string
    defaultPrice: number
    autoPublish: boolean
    qualityCheck: boolean
  }
  integrations: {
    amazonKDP: boolean
    barnesNoble: boolean
    appleBooks: boolean
    googlePlay: boolean
  }
  security: {
    twoFactorEnabled: boolean
    lastPasswordChange: string
    activeDevices: number
  }
}

const TIMEZONES = [
  "UTC",
  "America/New_York",
  "America/Chicago", 
  "America/Denver",
  "America/Los_Angeles",
  "Europe/London",
  "Europe/Paris",
  "Europe/Berlin",
  "Asia/Tokyo",
  "Asia/Shanghai",
  "Australia/Sydney"
]

const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "it", name: "Italian" },
  { code: "pt", name: "Portuguese" },
  { code: "ja", name: "Japanese" },
  { code: "zh", name: "Chinese" }
]

const CURRENCIES = [
  { code: "USD", symbol: "$", name: "US Dollar" },
  { code: "EUR", symbol: "€", name: "Euro" },
  { code: "GBP", symbol: "£", name: "British Pound" },
  { code: "JPY", symbol: "¥", name: "Japanese Yen" },
  { code: "CAD", symbol: "C$", name: "Canadian Dollar" },
  { code: "AUD", symbol: "A$", name: "Australian Dollar" }
]

const GENRES = [
  "Business & Economics",
  "Technology & Engineering", 
  "Self-Help & Personal Development",
  "Health & Fitness",
  "Education & Reference",
  "Fiction & Literature",
  "History & Biography",
  "Science & Nature"
]

export default function SettingsPage() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState("profile")
  const [showApiKey, setShowApiKey] = useState(false)
  const { theme, setTheme } = useTheme()

  // API hooks
  const { data: profile, isLoading: profileLoading, error: profileError } = useUserProfile()
  const { data: preferences, isLoading: preferencesLoading, error: preferencesError } = useUserPreferences()
  const { data: publishingSettings, isLoading: publishingLoading, error: publishingError } = usePublishingSettings()
  const { data: integrations, isLoading: integrationsLoading, error: integrationsError } = usePlatformIntegrations()
  const { data: security, isLoading: securityLoading, error: securityError } = useSecuritySettings()
  const { data: sessions, isLoading: sessionsLoading, error: sessionsError } = useActiveSessions()

  // Mutation hooks
  const updateProfile = useUpdateProfile()
  const updatePreferences = useUpdatePreferences()
  const updatePublishingSettings = useUpdatePublishingSettings()
  const toggleIntegration = useTogglePlatformIntegration()
  const updateSecurity = useUpdateSecuritySettings()
  const regenerateApiKey = useRegenerateApiKey()
  const terminateSession = useTerminateSession()

  // Loading state
  const isLoading = profileLoading || preferencesLoading || publishingLoading || 
                   integrationsLoading || securityLoading || sessionsLoading

  // Error state  
  const hasError = profileError || preferencesError || publishingError || 
                   integrationsError || securityError || sessionsError

  const isSaving = updateProfile.isPending || updatePreferences.isPending || 
                   updatePublishingSettings.isPending || toggleIntegration.isPending ||
                   updateSecurity.isPending

  // Local state for form data
  const [formData, setFormData] = useState({
    profile: {
      name: "",
      bio: "",
      website: "", 
      location: "",
      timezone: "UTC"
    },
    preferences: {
      language: "en",
      currency: "USD",
      emailNotifications: true,
      pushNotifications: true,
      marketingEmails: false
    },
    publishing: {
      defaultGenre: "Business & Economics",
      authorPseudonym: "",
      publisherName: "",
      defaultPrice: 9.99,
      autoPublish: false,
      qualityCheck: true
    }
  })

  // Update form data when API data loads
  React.useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        profile: {
          name: profile.name || "",
          bio: profile.bio || "",
          website: profile.website || "",
          location: profile.location || "",
          timezone: profile.timezone || "UTC"
        }
      }))
    }
  }, [profile])

  React.useEffect(() => {
    if (preferences) {
      setFormData(prev => ({
        ...prev,
        preferences: {
          language: preferences.language || "en",
          currency: preferences.currency || "USD",
          emailNotifications: preferences.email_notifications ?? true,
          pushNotifications: preferences.push_notifications ?? true,
          marketingEmails: preferences.marketing_emails ?? false
        }
      }))
    }
  }, [preferences])

  React.useEffect(() => {
    if (publishingSettings) {
      setFormData(prev => ({
        ...prev,
        publishing: {
          defaultGenre: publishingSettings.default_genre || "Business & Economics",
          authorPseudonym: publishingSettings.author_pseudonym || "",
          publisherName: publishingSettings.publisher_name || "",
          defaultPrice: publishingSettings.default_price || 9.99,
          autoPublish: publishingSettings.auto_publish ?? false,
          qualityCheck: publishingSettings.quality_check ?? true
        }
      }))
    }
  }, [publishingSettings])

  const handleSaveProfile = () => {
    updateProfile.mutate(formData.profile)
  }

  const handleSavePreferences = () => {
    updatePreferences.mutate({
      ...formData.preferences,
      email_notifications: formData.preferences.emailNotifications,
      push_notifications: formData.preferences.pushNotifications,
      marketing_emails: formData.preferences.marketingEmails
    })
  }

  const handleSavePublishingSettings = () => {
    updatePublishingSettings.mutate({
      default_genre: formData.publishing.defaultGenre,
      author_pseudonym: formData.publishing.authorPseudonym,
      publisher_name: formData.publishing.publisherName,
      default_price: formData.publishing.defaultPrice,
      auto_publish: formData.publishing.autoPublish,
      quality_check: formData.publishing.qualityCheck
    })
  }

  const handleIntegrationToggle = (platform: string) => {
    const currentIntegration = integrations?.integrations.find(i => i.platform === platform)
    toggleIntegration.mutate({ 
      platform, 
      enabled: !currentIntegration?.is_connected 
    })
  }

  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  if (hasError) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="p-6">
            <ErrorMessage
              title="Failed to load settings"
              message="Please try refreshing the page or contact support if the problem persists."
            />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title="Settings"
            description="Manage your account, preferences, and publishing configuration"
            actions={
              <div className="flex gap-2">
                {activeTab === 'profile' && (
                  <Button onClick={handleSaveProfile} disabled={isSaving} className="gap-2">
                    {updateProfile.isPending ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        Save Profile
                      </>
                    )}
                  </Button>
                )}
                {activeTab === 'preferences' && (
                  <Button onClick={handleSavePreferences} disabled={isSaving} className="gap-2">
                    {updatePreferences.isPending ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        Save Preferences
                      </>
                    )}
                  </Button>
                )}
                {activeTab === 'publishing' && (
                  <Button onClick={handleSavePublishingSettings} disabled={isSaving} className="gap-2">
                    {updatePublishingSettings.isPending ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        Save Publishing
                      </>
                    )}
                  </Button>
                )}
              </div>
            }
          />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="profile" className="gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="preferences" className="gap-2">
                <Settings className="h-4 w-4" />
                Preferences
              </TabsTrigger>
              <TabsTrigger value="publishing" className="gap-2">
                <BookOpen className="h-4 w-4" />
                Publishing
              </TabsTrigger>
              <TabsTrigger value="integrations" className="gap-2">
                <Globe className="h-4 w-4" />
                Integrations
              </TabsTrigger>
              <TabsTrigger value="security" className="gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your personal information and public profile details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={formData.profile.name}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          profile: { ...prev.profile, name: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile?.email || ""}
                        disabled
                        className="bg-muted"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      placeholder="Tell readers about yourself..."
                      value={formData.profile.bio}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        profile: { ...prev.profile, bio: e.target.value }
                      }))}
                      rows={4}
                    />
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        placeholder="https://yourwebsite.com"
                        value={formData.profile.website}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          profile: { ...prev.profile, website: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        placeholder="City, Country"
                        value={formData.profile.location}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          profile: { ...prev.profile, location: e.target.value }
                        }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select
                      value={formData.profile.timezone}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        profile: { ...prev.profile, timezone: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {TIMEZONES.map((tz) => (
                          <SelectItem key={tz} value={tz}>
                            {tz}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preferences" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Language & Region</CardTitle>
                  <CardDescription>
                    Configure your language, currency, and regional preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label>Language</Label>
                      <Select
                        value={formData.preferences.language}
                        onValueChange={(value) => setFormData(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, language: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LANGUAGES.map((lang) => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Currency</Label>
                      <Select
                        value={formData.preferences.currency}
                        onValueChange={(value) => setFormData(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, currency: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CURRENCIES.map((currency) => (
                            <SelectItem key={currency.code} value={currency.code}>
                              {currency.symbol} {currency.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select
                        value={theme}
                        onValueChange={(value) => setTheme(value as 'light' | 'dark' | 'system')}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>
                    Choose how you want to be notified about account activity
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications about book generation and sales
                        </p>
                      </div>
                      <Switch
                        checked={formData.preferences.emailNotifications}
                        onCheckedChange={(checked) => setFormData(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, emailNotifications: checked }
                        }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Push Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Get real-time updates in your browser
                        </p>
                      </div>
                      <Switch
                        checked={formData.preferences.pushNotifications}
                        onCheckedChange={(checked) => setFormData(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, pushNotifications: checked }
                        }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Marketing Emails</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive tips, updates, and promotional content
                        </p>
                      </div>
                      <Switch
                        checked={formData.preferences.marketingEmails}
                        onCheckedChange={(checked) => setFormData(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, marketingEmails: checked }
                        }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="publishing" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Publishing Defaults</CardTitle>
                  <CardDescription>
                    Set default values for new book generation and publishing
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Default Genre</Label>
                      <Select
                        value={formData.publishing.defaultGenre}
                        onValueChange={(value) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, defaultGenre: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {GENRES.map((genre) => (
                            <SelectItem key={genre} value={genre}>
                              {genre}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Default Price (USD)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.99"
                        value={formData.publishing.defaultPrice}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, defaultPrice: parseFloat(e.target.value) }
                        }))}
                      />
                    </div>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Author Pseudonym (Optional)</Label>
                      <Input
                        placeholder="Pen name for publishing"
                        value={formData.publishing.authorPseudonym}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, authorPseudonym: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Publisher Name (Optional)</Label>
                      <Input
                        placeholder="Your publishing imprint"
                        value={formData.publishing.publisherName}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, publisherName: e.target.value }
                        }))}
                      />
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Auto-Publish</Label>
                        <p className="text-sm text-muted-foreground">
                          Automatically submit books to selected platforms after generation
                        </p>
                      </div>
                      <Switch
                        checked={formData.publishing.autoPublish}
                        onCheckedChange={(checked) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, autoPublish: checked }
                        }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Quality Check</Label>
                        <p className="text-sm text-muted-foreground">
                          Run additional quality checks before publishing
                        </p>
                      </div>
                      <Switch
                        checked={formData.publishing.qualityCheck}
                        onCheckedChange={(checked) => setFormData(prev => ({
                          ...prev,
                          publishing: { ...prev.publishing, qualityCheck: checked }
                        }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="integrations" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <StatsCard
                  value={integrations?.connected_count?.toString() || "0"}
                  label="Connected Platforms"
                  description="Active integrations"
                  icon={<Globe className="h-5 w-5" />}
                  size="sm"
                />
                <StatsCard
                  value={integrations?.total_platforms?.toString() || "4"}
                  label="Available Platforms"
                  description="Total options"
                  icon={<Zap className="h-5 w-5" />}
                  size="sm"
                  variant="info"
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Publishing Platforms</CardTitle>
                  <CardDescription>
                    Connect your accounts to enable automated publishing
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    {integrations?.integrations?.map((integration) => {
                      const platformInfo = {
                        amazon_kdp: {
                          name: "Amazon KDP",
                          description: "Publish to Amazon Kindle Direct Publishing",
                          color: "orange",
                          bgColor: "bg-orange-100",
                          textColor: "text-orange-600"
                        },
                        barnes_noble: {
                          name: "Barnes & Noble Press",
                          description: "Reach Barnes & Noble bookstore customers",
                          color: "green",
                          bgColor: "bg-green-100",
                          textColor: "text-green-600"
                        },
                        apple_books: {
                          name: "Apple Books",
                          description: "Distribute to iOS and Mac users",
                          color: "gray",
                          bgColor: "bg-gray-100",
                          textColor: "text-gray-600"
                        },
                        google_play: {
                          name: "Google Play Books",
                          description: "Reach Android and web readers",
                          color: "blue",
                          bgColor: "bg-blue-100",
                          textColor: "text-blue-600"
                        }
                      }[integration.platform] || {
                        name: integration.platform,
                        description: "Publishing platform integration",
                        color: "gray",
                        bgColor: "bg-gray-100",
                        textColor: "text-gray-600"
                      }

                      return (
                        <div key={integration.platform} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-4">
                            <div className={`w-10 h-10 ${platformInfo.bgColor} rounded-lg flex items-center justify-center`}>
                              <BookOpen className={`h-5 w-5 ${platformInfo.textColor}`} />
                            </div>
                            <div>
                              <div className="font-medium">{platformInfo.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {platformInfo.description}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {integration.is_connected && (
                              <Badge variant="default">Connected</Badge>
                            )}
                            {integration.status === 'error' && (
                              <Badge variant="destructive">Error</Badge>
                            )}
                            <Switch
                              checked={integration.is_connected}
                              onCheckedChange={() => handleIntegrationToggle(integration.platform)}
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <p className="text-sm text-yellow-800">
                    Publishing platform connections require additional verification and may take 1-2 business days to activate.
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <StatsCard
                  value={security?.two_factor_enabled ? "Enabled" : "Disabled"}
                  label="Two-Factor Auth"
                  description={security?.two_factor_enabled ? "Account secured" : "Not configured"}
                  icon={<Shield className="h-5 w-5" />}
                  variant={security?.two_factor_enabled ? "success" : "warning"}
                  size="sm"
                />
                <StatsCard
                  value={sessions?.total_sessions?.toString() || "0"}
                  label="Active Devices"
                  description="Currently signed in"
                  icon={<User className="h-5 w-5" />}
                  size="sm"
                />
                <StatsCard
                  value={security?.last_password_change ? new Date(security.last_password_change).toLocaleDateString() : "Unknown"}
                  label="Last Password Change"
                  description={security?.last_password_change ? `${Math.floor((Date.now() - new Date(security.last_password_change).getTime()) / (1000 * 60 * 60 * 24))} days ago` : "Never"}
                  icon={<RefreshCw className="h-5 w-5" />}
                  size="sm"
                  variant="info"
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Password & Authentication</CardTitle>
                  <CardDescription>
                    Manage your account security and authentication methods
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Two-Factor Authentication</Label>
                        <p className="text-sm text-muted-foreground">
                          Add an extra layer of security to your account
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {security?.two_factor_enabled && (
                          <Badge variant="default">Enabled</Badge>
                        )}
                        <Switch
                          checked={security?.two_factor_enabled || false}
                          onCheckedChange={(checked) => updateSecurity.mutate({ two_factor_enabled: checked })}
                        />
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Password</Label>
                          <p className="text-sm text-muted-foreground">
                            Last changed on {security?.last_password_change ? new Date(security.last_password_change).toLocaleDateString() : 'Never'}
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Change Password
                        </Button>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>API Access Key</Label>
                          <p className="text-sm text-muted-foreground">
                            For programmatic access to your account
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-2">
                            <Input
                              type={showApiKey ? "text" : "password"}
                              value={security?.api_key || "pk_live_xxxxxxxxxxxxxxxx"}
                              readOnly
                              className="w-48"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setShowApiKey(!showApiKey)}
                            >
                              {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => regenerateApiKey.mutate()}
                            disabled={regenerateApiKey.isPending}
                          >
                            {regenerateApiKey.isPending ? 'Regenerating...' : 'Regenerate'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Active Sessions</CardTitle>
                  <CardDescription>
                    Devices currently signed in to your account
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {sessions?.sessions?.map((session) => (
                      <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                            <User className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">{session.device_name}</div>
                            <div className="text-sm text-muted-foreground">{session.location}</div>
                            <div className="text-xs text-muted-foreground">
                              Last active: {new Date(session.last_activity).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {session.is_current && <Badge variant="default">Current</Badge>}
                          {!session.is_current && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => terminateSession.mutate(session.id)}
                              disabled={terminateSession.isPending}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    )) || (
                      <div className="text-center text-muted-foreground py-8">
                        <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No active sessions found</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}