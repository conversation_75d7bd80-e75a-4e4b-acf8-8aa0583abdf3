"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { useAnalyticsOverview, useAnalyticsDetails } from "@/hooks/api/use-analytics"
import { useBooks } from "@/hooks/api/use-books"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { StatsCard } from "@/components/ui/stats-card"
import { ChartWrapper, ChartPlaceholder } from "@/components/ui/chart-wrapper"
import { ActivityTimeline, type TimelineItem } from "@/components/ui/activity-timeline"
import { BookCard, type BookData } from "@/components/ui/book-card"
import { QuickActions, type QuickActionItem } from "@/components/ui/quick-actions"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  Download, 
  Filter, 
  RefreshCw,
  DollarSign,
  BookOpen,
  TrendingUp,
  Users,
  Target,
  Calendar,
  Eye,
  Star,
  Globe,
  PieChart,
  LineChart,
  Activity,
  Edit
} from "lucide-react"

export default function AnalyticsPage() {
  const { data: session } = useSession()
  const [timeRange, setTimeRange] = useState("6months")
  
  // API hooks for real data
  const { data: analytics, isLoading: analyticsLoading, error: analyticsError } = useAnalyticsOverview()
  const { data: analyticsDetails, isLoading: detailsLoading } = useAnalyticsDetails({ timeRange })
  const { data: booksData, isLoading: booksLoading } = useBooks({ sortBy: 'performance' })
  
  // Loading state
  if (analyticsLoading || detailsLoading || booksLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }
  
  // Error state
  if (analyticsError) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="p-6">
            <ErrorMessage 
              message="Failed to load analytics data"
              description="Please try refreshing the page or contact support if the problem persists."
              action={
                <Button onClick={() => window.location.reload()}>
                  Refresh Page
                </Button>
              }
            />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }
  
  // Extract data with fallbacks
  const books = booksData?.books || []
  const overview = analytics || {
    totalRevenue: 0,
    totalSales: 0,
    activeBooks: books.length,
    averageRating: 0,
    revenueChange: 0,
    salesChange: 0,
    booksChange: 0
  }
  
  // Generate activity from real data
  const recentActivity: TimelineItem[] = []
  
  // Add published books activity
  books.filter(book => book.status === 'published').slice(0, 3).forEach(book => {
    recentActivity.push({
      id: `book-${book.id}`,
      title: `"${book.title}" Published`,
      description: `Your book is now available for purchase`,
      timestamp: new Date(book.updated_at).toLocaleDateString(),
      status: "completed"
    })
  })
  
  // Add placeholder activities for new users
  if (recentActivity.length === 0) {
    recentActivity.push({
      id: "welcome",
      title: "Welcome to Analytics",
      description: "Publish your first book to start seeing analytics data",
      timestamp: "Getting started",
      status: "pending"
    })
  }
  
  // Sales data from API or empty state
  const salesData = analyticsDetails?.salesHistory || [
    { month: "Jan", sales: 0, revenue: 0, books: 0 },
    { month: "Feb", sales: 0, revenue: 0, books: 0 },
    { month: "Mar", sales: 0, revenue: 0, books: 0 },
    { month: "Apr", sales: 0, revenue: 0, books: 0 },
    { month: "May", sales: 0, revenue: 0, books: 0 },
    { month: "Jun", sales: 0, revenue: 0, books: 0 }
  ]
  
  const user = session?.user
    ? {
        name: session.user.name || "User",
        email: session.user.email || "",
        avatar: session.user.image || undefined,
      }
    : undefined

  const quickActions: QuickActionItem[] = [
    {
      id: "1",
      title: "Export Report",
      description: "Download detailed analytics report",
      icon: <Download className="h-5 w-5" />,
      onClick: () => console.log('Export analytics')
    },
    {
      id: "2",
      title: "Schedule Report",
      description: "Set up automated reporting",
      icon: <Calendar className="h-5 w-5" />,
      onClick: () => console.log('Schedule report')
    },
    {
      id: "3",
      title: "Set Goals",
      description: "Configure performance targets",
      icon: <Target className="h-5 w-5" />,
      onClick: () => console.log('Set goals')
    },
    {
      id: "4",
      title: "View Trends",
      description: "Analyze market trends",
      icon: <TrendingUp className="h-5 w-5" />,
      onClick: () => console.log('View trends')
    }
  ]

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title="Analytics"
            description="Comprehensive insights into your publishing performance and revenue"
            actions={
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            }
          />

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="books">Books</TabsTrigger>
              <TabsTrigger value="audience">Audience</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Key Metrics */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <StatsCard
                  value={`$${overview.totalRevenue?.toFixed(2) || '0.00'}`}
                  label="Total Revenue"
                  description="All-time earnings"
                  icon={<DollarSign className="h-5 w-5" />}
                  trend={overview.revenueChange ? {
                    direction: overview.revenueChange > 0 ? "up" : overview.revenueChange < 0 ? "down" : "neutral",
                    value: Math.abs(overview.revenueChange),
                    label: "vs last month"
                  }}
                  variant="success"
                />
                <StatsCard
                  value={overview.totalSales?.toString() || '0'}
                  label="Total Sales"
                  description="Books sold"
                  icon={<BookOpen className="h-5 w-5" />}
                  trend={overview.salesChange ? {
                    direction: overview.salesChange > 0 ? "up" : overview.salesChange < 0 ? "down" : "neutral",
                    value: Math.abs(overview.salesChange),
                    label: "vs last month"
                  } : undefined}
                />
                <StatsCard
                  value={overview.activeBooks?.toString() || books.length.toString()}
                  label="Active Books"
                  description="Published titles"
                  icon={<TrendingUp className="h-5 w-5" />}
                  trend={overview.booksChange ? {
                    direction: overview.booksChange > 0 ? "up" : overview.booksChange < 0 ? "down" : "neutral",
                    value: Math.abs(overview.booksChange),
                    label: "new this month"
                  } : undefined}
                  variant="info"
                />
                <StatsCard
                  value={overview.averageRating?.toFixed(1) || '0.0'}
                  label="Average Rating"
                  description="Across all books"
                  icon={<Star className="h-5 w-5" />}
                  trend={overview.ratingChange ? {
                    direction: overview.ratingChange > 0 ? "up" : overview.ratingChange < 0 ? "down" : "neutral",
                    value: Math.abs(overview.ratingChange),
                    label: "vs last month"
                  } : undefined}
                  variant="warning"
                />
              </div>

              {/* Charts Section */}
              <div className="grid gap-6 lg:grid-cols-2">
                <ChartWrapper
                  title="Revenue & Sales Trends"
                  description="Monthly performance over the last 6 months"
                  height="lg"
                  actions={
                    <div className="flex gap-2">
                      <Badge variant={timeRange === "6months" ? "default" : "secondary"}>
                        6M
                      </Badge>
                      <Badge variant={timeRange === "1year" ? "default" : "secondary"}>
                        1Y
                      </Badge>
                    </div>
                  }
                  footer={
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Last updated: 1 hour ago</span>
                      <span>Peak month: May ({salesData[4]?.sales || 0} sales)</span>
                    </div>
                  }
                >
                  <ChartPlaceholder type="area" data={salesData} />
                </ChartWrapper>

                <ChartWrapper
                  title="Top Performing Books"
                  description="Revenue breakdown by book"
                  height="lg"
                  variant="elevated"
                >
                  <ChartPlaceholder 
                    type="pie" 
                    data={analyticsDetails?.topBooks?.map(book => ({
                      name: book.title,
                      value: book.revenue
                    })) || []} 
                  />
                </ChartWrapper>
              </div>

              {/* Quick Actions */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
                <QuickActions items={quickActions} columns={4} />
              </div>

              {/* Activity Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest updates and milestones</CardDescription>
                </CardHeader>
                <CardContent>
                  <ActivityTimeline 
                    items={recentActivity} 
                    compact 
                    size="sm"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="revenue" className="space-y-6">
              <div className="grid gap-6">
                {/* Revenue Metrics */}
                <div className="grid gap-4 md:grid-cols-3">
                  <StatsCard
                    value={`$${overview.totalSales > 0 ? (overview.totalRevenue / overview.totalSales).toFixed(2) : '0.00'}`}
                    label="Revenue per Sale"
                    description="Average book price"
                    icon={<DollarSign className="h-5 w-5" />}
                    variant="success"
                  />
                  <StatsCard
                    value={`$${salesData[4]?.revenue || 0}`}
                    label="This Month"
                    description="Current month revenue"
                    icon={<Calendar className="h-5 w-5" />}
                  />
                  <StatsCard
                    value={`$${(overview.totalRevenue * 0.7).toFixed(2)}`}
                    label="Net Revenue"
                    description="After platform fees"
                    icon={<Target className="h-5 w-5" />}
                    variant="info"
                  />
                </div>

                {/* Revenue Chart */}
                <ChartWrapper
                  title="Monthly Revenue Breakdown"
                  description="Detailed revenue analysis over time"
                  height="xl"
                  actions={
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export Data
                    </Button>
                  }
                >
                  <ChartPlaceholder type="bar" data={salesData} />
                </ChartWrapper>

                {/* Top Earning Books */}
                <Card>
                  <CardHeader>
                    <CardTitle>Top Earning Books</CardTitle>
                    <CardDescription>Your most profitable titles</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {(analyticsDetails?.topBooks || []).map((book, index) => (
                        <div key={book.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-semibold">
                              {index + 1}
                            </div>
                            <div>
                              <h4 className="font-medium">{book.title}</h4>
                              <p className="text-sm text-muted-foreground">{book.sales} sales • {book.rating}★</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-green-600">${book.revenue.toFixed(2)}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              {book.trend === "up" ? (
                                <TrendingUp className="h-3 w-3 text-green-500" />
                              ) : (
                                <TrendingUp className="h-3 w-3 text-red-500 rotate-180" />
                              )}
                              <span>{book.trend === "up" ? "Growing" : "Declining"}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="books" className="space-y-6">
              <div className="grid gap-6">
                {/* Book Metrics */}
                <div className="grid gap-4 md:grid-cols-4">
                  <StatsCard
                    value={overview.activeBooks?.toString() || '0'}
                    label="Published Books"
                    description="Live on platforms"
                    icon={<BookOpen className="h-5 w-5" />}
                  />
                  <StatsCard
                    value="3"
                    label="Drafts"
                    description="In progress"
                    icon={<Edit className="h-5 w-5" />}
                    variant="warning"
                  />
                  <StatsCard
                    value="2"
                    label="In Review"
                    description="Pending approval"
                    icon={<Eye className="h-5 w-5" />}
                    variant="info"
                  />
                  <StatsCard
                    value={overview.averageRating?.toFixed(1) || '0.0'}
                    label="Avg Rating"
                    description="All books"
                    icon={<Star className="h-5 w-5" />}
                    variant="success"
                  />
                </div>

                {/* Books Performance Chart */}
                <ChartWrapper
                  title="Books Performance"
                  description="Sales comparison across your book portfolio"
                  height="lg"
                >
                  <ChartPlaceholder 
                    type="bar" 
                    data={(analyticsDetails?.topBooks || []).map(book => ({
                      title: book.title,
                      sales: book.sales,
                      revenue: book.revenue
                    }))} 
                  />
                </ChartWrapper>

                {/* Genre Performance */}
                <div className="grid gap-6 lg:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Genre Performance</CardTitle>
                      <CardDescription>Sales by book category</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { genre: "Technology", sales: 156, percentage: 54 },
                          { genre: "Business", sales: 89, percentage: 31 },
                          { genre: "Education", sales: 42, percentage: 15 }
                        ].map((item) => (
                          <div key={item.genre} className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">{item.genre}</span>
                              <span className="text-sm text-muted-foreground">{item.sales} sales</span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div 
                                className="bg-primary h-2 rounded-full" 
                                style={{width: `${item.percentage}%`}}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Publishing Velocity</CardTitle>
                      <CardDescription>Books published over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ChartPlaceholder 
                        type="line" 
                        data={salesData.map(d => ({
                          month: d.month,
                          books: d.books || 0
                        }))} 
                      />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="audience" className="space-y-6">
              <div className="grid gap-6">
                {/* Audience Metrics */}
                <div className="grid gap-4 md:grid-cols-4">
                  <StatsCard
                    value="34"
                    label="Avg Age"
                    description="Primary demographic"
                    icon={<Users className="h-5 w-5" />}
                  />
                  <StatsCard
                    value="127"
                    label="Repeat Customers"
                    description="Multiple purchases"
                    icon={<Target className="h-5 w-5" />}
                    variant="success"
                  />
                  <StatsCard
                    value="5"
                    label="Countries"
                    description="Global reach"
                    icon={<Globe className="h-5 w-5" />}
                    variant="info"
                  />
                  <StatsCard
                    value="68%"
                    label="Mobile Readers"
                    description="Reading on mobile"
                    icon={<Activity className="h-5 w-5" />}
                  />
                </div>

                {/* Geographic Distribution */}
                <div className="grid gap-6 lg:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Geographic Distribution</CardTitle>
                      <CardDescription>Sales by country</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {(analyticsDetails?.demographics?.countries || []).map((country) => (
                          <div key={country.country} className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">{country.country}</span>
                              <span className="text-sm text-muted-foreground">{country.sales} sales</span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div 
                                className="bg-primary h-2 rounded-full" 
                                style={{width: `${country.percentage}%`}}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Age Demographics</CardTitle>
                      <CardDescription>Reader age distribution</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ChartPlaceholder 
                        type="pie" 
                        data={(analyticsDetails?.demographics?.ageGroups || []).map(group => ({
                          name: group.range,
                          value: group.percentage
                        }))} 
                      />
                    </CardContent>
                  </Card>
                </div>

                {/* Reader Behavior */}
                <Card>
                  <CardHeader>
                    <CardTitle>Reader Behavior Insights</CardTitle>
                    <CardDescription>Understanding your audience preferences</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      <div className="text-center">
                        <div className="text-2xl font-bold">156</div>
                        <div className="text-sm text-muted-foreground">Avg. Pages Read</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">4.2</div>
                        <div className="text-sm text-muted-foreground">Completion Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">23%</div>
                        <div className="text-sm text-muted-foreground">Review Rate</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}