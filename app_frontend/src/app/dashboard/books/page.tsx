"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { useBooks } from "@/hooks/api/use-books";
import { ErrorMessage } from "@/components/ui/error-message";
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BookCard } from "@/components/books/book-card"
import { StatsCards } from "@/components/books/stats-cards"
import { EmptyState } from "@/components/ui/empty-state"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Grid } from "@/components/ui/grid"
import { PlusCircle, Search, Filter, BookOpen } from "lucide-react"


export default function BooksPage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGenre, setSelectedGenre] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  // API hooks for real data
  const { data: booksData, isLoading, error, refetch } = useBooks();

  const user = session?.user
    ? {
        name: session.user.name || "User",
        email: session.user.email || "",
        avatar: session.user.image || undefined,
      }
    : undefined;

  // Extract books from API response
  const books = booksData?.books || [];

  // Filter books based on search and filters
  const filteredBooks = books.filter((book) => {
    const matchesSearch =
      book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      book.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesGenre =
      selectedGenre === "all" || book.category?.toLowerCase() === selectedGenre;
    const matchesStatus =
      selectedStatus === "all" || book.status === selectedStatus;

    return matchesSearch && matchesGenre && matchesStatus;
  });

  const handleCreateBook = () => {
    window.location.href = "/dashboard/books/new";
  };

  const handleBookAction = (bookId: string, action: string) => {
    switch (action) {
      case "view":
        window.location.href = `/dashboard/books/${bookId}`;
        break;
      case "edit":
        window.location.href = `/dashboard/books/${bookId}/edit`;
        break;
      case "delete":
        // TODO: Implement delete confirmation dialog
        break;
      default:
        console.log(`${action} book ${bookId}`);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Error state
  if (error) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="p-6">
            <div className="space-y-4">
              <ErrorMessage
                title="Failed to load books"
                message="Please try refreshing the page or contact support if the problem persists."
              />
              <Button onClick={() => refetch()}>Retry</Button>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  const genres = [
    "all",
    "business",
    "marketing",
    "lifestyle",
    "finance",
    "technology",
    "self-help",
  ];
  const statuses = ["all", "draft", "generating", "review", "published"];

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title="My Books"
            description="Manage your book collection, track performance, and create new publications."
            actions={
              <Button onClick={handleCreateBook} className="gap-2">
                <PlusCircle className="h-4 w-4" />
                Create New Book
              </Button>
            }
          />

          {/* Stats Cards */}
          <StatsCards books={books} />

          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search books by title or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={selectedGenre} onValueChange={setSelectedGenre}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Genre" />
                </SelectTrigger>
                <SelectContent>
                  {genres.map((genre) => (
                    <SelectItem key={genre} value={genre}>
                      {genre === "all"
                        ? "All Genres"
                        : genre.charAt(0).toUpperCase() + genre.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status === "all"
                        ? "All Status"
                        : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tabs for different views */}
          <Tabs defaultValue="grid" className="space-y-6">
            <TabsList>
              <TabsTrigger value="grid">Grid View</TabsTrigger>
              <TabsTrigger value="list">List View</TabsTrigger>
            </TabsList>

            <TabsContent value="grid" className="space-y-6">
              {isLoading ? (
                <div className="flex justify-center py-12">
                  <LoadingSpinner size="lg" />
                </div>
              ) : filteredBooks.length > 0 ? (
                <Grid
                  cols={3}
                  gap={6}
                  className="grid-cols-1 md:grid-cols-2 xl:grid-cols-3"
                >
                  {filteredBooks.map((book) => (
                    <BookCard
                      key={book.id}
                      book={book}
                      onAction={handleBookAction}
                    />
                  ))}
                </Grid>
              ) : (
                <EmptyState
                  icon={BookOpen}
                  title={
                    searchQuery ||
                    selectedGenre !== "all" ||
                    selectedStatus !== "all"
                      ? "No books found"
                      : "No books yet"
                  }
                  description={
                    searchQuery ||
                    selectedGenre !== "all" ||
                    selectedStatus !== "all"
                      ? "Try adjusting your search criteria or filters."
                      : "Get started by creating your first AI-generated book."
                  }
                  action={
                    searchQuery ||
                    selectedGenre !== "all" ||
                    selectedStatus !== "all"
                      ? undefined
                      : {
                          label: "Create Your First Book",
                          onClick: handleCreateBook,
                        }
                  }
                />
              )}
            </TabsContent>

            <TabsContent value="list" className="space-y-4">
              {/* List view would go here - simpler table format */}
              <div className="text-center py-12 text-muted-foreground">
                List view coming soon...
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}