"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { NewBookWizard } from "@/components/books/new-book-wizard"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NewBookPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)

  const user = session?.user
    ? {
        name: session.user.name || "User",
        email: session.user.email || "",
        avatar: session.user.image || undefined,
      }
    : undefined

  const handleBookCreated = (bookId: string) => {
    // Navigate to the newly created book
    router.push(`/dashboard/books/${bookId}`)
  }

  const handleCancel = () => {
    router.push("/dashboard/books")
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title="Create New Book"
            description="Use AI to generate your next bestseller. Follow the steps below to create and publish your book."
            breadcrumb={true}
            actions={
              <Link href="/dashboard/books">
                <Button variant="outline" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Books
                </Button>
              </Link>
            }
          />

          <div className="max-w-4xl mx-auto">
            <NewBookWizard
              onComplete={handleBookCreated}
              onCancel={handleCancel}
            />
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}