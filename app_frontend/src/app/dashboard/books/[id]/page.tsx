"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { StatusIndicator } from "@/components/ui/status-indicator"
import { Heading, Text } from "@/components/ui/typography"
import { StatsCard } from "@/components/ui/stats-card"
import { ChartWrapper, ChartPlaceholder } from "@/components/ui/chart-wrapper"
import { ActivityTimeline, type TimelineItem } from "@/components/ui/activity-timeline"
import {
  Edit,
  Upload,
  Download,
  Share2,
  Calendar,
  FileText,
  DollarSign,
  Star,
  Eye,
  ArrowLeft,
  BarChart3,
  TrendingUp,
  Users,
  BookOpen,
  Clock,
  Target
} from "lucide-react"

// Mock data - in real app, this would come from API
const mockBook = {
  id: "1",
  title: "The Future of AI in Business",
  description: "A comprehensive guide to implementing artificial intelligence in modern business operations, covering everything from basic concepts to advanced implementation strategies.",
  status: "published" as "draft" | "generating" | "review" | "published",
  genre: "Business",
  pages: 245,
  chapters: 12,
  createdAt: "2024-01-15",
  updatedAt: "2024-01-20",
  publishedAt: "2024-01-25",
  coverUrl: "/images/book-covers/ai-business.jpg",
  revenue: 1250.50,
  sales: 89,
  rating: 4.2,
  reviews: 23,
  isbn: "978-1-234567-89-0",
  language: "English",
  marketplaces: ["Amazon KDP", "Barnes & Noble"],
  salesData: [
    { month: "Jan", sales: 12, revenue: 156 },
    { month: "Feb", sales: 18, revenue: 234 },
    { month: "Mar", sales: 25, revenue: 325 },
    { month: "Apr", sales: 15, revenue: 195 },
    { month: "May", sales: 19, revenue: 247 },
    { month: "Jun", sales: 0, revenue: 0 }
  ],
  recentActivity: [
    {
      id: "1",
      title: "Book published successfully",
      description: "Your book is now live on Amazon KDP and available for purchase.",
      timestamp: "2 days ago",
      status: "completed" as const
    },
    {
      id: "2",
      title: "Cover design completed",
      description: "AI generated a professional cover design for your book.",
      timestamp: "1 week ago",
      status: "completed" as const
    },
    {
      id: "3",
      title: "Manuscript generated",
      description: "AI completed the full manuscript with 12 chapters.",
      timestamp: "2 weeks ago",
      status: "completed" as const
    },
    {
      id: "4",
      title: "Content outline created",
      description: "Initial book outline and chapter structure defined.",
      timestamp: "3 weeks ago",
      status: "completed" as const
    }
  ],
  outline: [
    "Introduction to AI in Business",
    "Understanding Machine Learning",
    "AI Strategy Development",
    "Implementation Planning",
    "Data Management",
    "Automation Opportunities",
    "ROI Measurement",
    "Case Studies",
    "Future Trends",
    "Best Practices",
    "Common Pitfalls",
    "Conclusion and Next Steps",
  ],
}

const statusConfig = {
  draft: { label: "Draft", variant: "secondary" as const, indicator: "default" as const },
  generating: { label: "Generating", variant: "default" as const, indicator: "info" as const, pulse: true },
  review: { label: "Review", variant: "outline" as const, indicator: "warning" as const },
  published: { label: "Published", variant: "default" as const, indicator: "success" as const },
}

export default function BookDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const [activeTab, setActiveTab] = useState("overview")

  const user = session?.user
    ? {
        name: session.user.name || "User",
        email: session.user.email || "",
        avatar: session.user.image || undefined,
      }
    : undefined

  const book = mockBook // In real app: fetch book by params.id
  const status = statusConfig[book.status]
  const isPublished = book.status === "published"

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title={book.title}
            description={`${book.genre} • ${book.pages} pages • ${book.chapters} chapters`}
            breadcrumb={true}
            actions={
              <div className="flex gap-2">
                <Link href="/dashboard/books">
                  <Button variant="outline" size="sm" className="gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Back to Books
                  </Button>
                </Link>
                <Button variant="outline" size="sm" className="gap-2">
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
                {(book.status === "draft" || book.status === "review") && (
                  <Button size="sm" className="gap-2">
                    <Edit className="h-4 w-4" />
                    Edit
                  </Button>
                )}
                {book.status === "review" && (
                  <Button size="sm" className="gap-2">
                    <Upload className="h-4 w-4" />
                    Publish
                  </Button>
                )}
                {isPublished && (
                  <Button size="sm" className="gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Analytics
                  </Button>
                )}
              </div>
            }
          />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Book Cover and Status */}
            <div className="space-y-6">
              {/* Book Cover */}
              <Card>
                <CardContent className="p-6">
                  <div className="aspect-[3/4] relative bg-gradient-to-br from-primary/10 to-primary/20 rounded-lg overflow-hidden mb-4">
                    {book.coverUrl ? (
                      <Image
                        src={book.coverUrl}
                        alt={book.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <FileText className="h-24 w-24 text-primary/40" />
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <StatusIndicator
                        status={status.indicator}
                        label={status.label}
                        pulse={'pulse' in status ? status.pulse : false}
                      />
                      <Badge variant="secondary">{book.genre}</Badge>
                    </div>
                    
                    {isPublished && (
                      <div className="grid grid-cols-2 gap-4 pt-3 border-t">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            ${book.revenue.toFixed(2)}
                          </div>
                          <div className="text-xs text-muted-foreground">Revenue</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{book.sales}</div>
                          <div className="text-xs text-muted-foreground">Sales</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                    <Download className="h-4 w-4" />
                    Download PDF
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                    <Edit className="h-4 w-4" />
                    Edit Content
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                    <Eye className="h-4 w-4" />
                    Preview
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Book Details */}
            <div className="lg:col-span-2">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  {/* Description */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Description</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Text>{book.description}</Text>
                    </CardContent>
                  </Card>

                  {/* Book Details */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Book Details</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Text variant="small" className="text-muted-foreground">Pages</Text>
                          <Text className="font-medium">{book.pages}</Text>
                        </div>
                        <div>
                          <Text variant="small" className="text-muted-foreground">Chapters</Text>
                          <Text className="font-medium">{book.chapters}</Text>
                        </div>
                        <div>
                          <Text variant="small" className="text-muted-foreground">Language</Text>
                          <Text className="font-medium">{book.language}</Text>
                        </div>
                        <div>
                          <Text variant="small" className="text-muted-foreground">ISBN</Text>
                          <Text className="font-medium">{book.isbn}</Text>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <Text variant="small" className="text-muted-foreground">Marketplaces</Text>
                        <div className="flex gap-2">
                          {book.marketplaces.map((marketplace) => (
                            <Badge key={marketplace} variant="outline">
                              {marketplace}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <Separator />

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Created {formatDate(book.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Updated {formatDate(book.updatedAt)}</span>
                        </div>
                        {isPublished && (
                          <div className="flex items-center gap-2 col-span-2">
                            <Upload className="h-4 w-4 text-muted-foreground" />
                            <span>Published {formatDate(book.publishedAt!)}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="content" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Table of Contents</CardTitle>
                      <CardDescription>
                        {book.chapters} chapters covering the complete guide
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {book.outline.map((chapter, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-semibold">
                                {index + 1}
                              </div>
                              <span className="font-medium">{chapter}</span>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="performance" className="space-y-6">
                  {isPublished ? (
                    <>
                      {/* Performance Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <StatsCard
                          value={`$${book.revenue.toFixed(2)}`}
                          label="Total Revenue"
                          description="Lifetime earnings"
                          icon={<DollarSign className="h-5 w-5" />}
                          trend={{
                            direction: "up",
                            value: 18.5,
                            label: "vs last month"
                          }}
                          variant="success"
                        />
                        <StatsCard
                          value={book.sales.toString()}
                          label="Total Sales"
                          description="Books sold"
                          icon={<BookOpen className="h-5 w-5" />}
                          trend={{
                            direction: "up",
                            value: 12.3,
                            label: "vs last month"
                          }}
                        />
                        <StatsCard
                          value={book.rating.toString()}
                          label="Average Rating"
                          description={`${book.reviews} reviews`}
                          icon={<Star className="h-5 w-5" />}
                          variant="warning"
                        />
                        <StatsCard
                          value="156"
                          label="Page Views"
                          description="This month"
                          icon={<Eye className="h-5 w-5" />}
                          trend={{
                            direction: "up",
                            value: 24.1,
                            label: "vs last month"
                          }}
                          variant="info"
                        />
                      </div>

                      {/* Sales Chart */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <ChartWrapper
                          title="Sales Overview"
                          description="Monthly sales performance over time"
                          height="sm"
                          actions={
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4 mr-2" />
                              Export
                            </Button>
                          }
                          footer={
                            <div className="flex justify-between text-sm text-muted-foreground">
                              <span>Last updated: 2 hours ago</span>
                              <span>Total: {book.sales} sales</span>
                            </div>
                          }
                        >
                          <ChartPlaceholder type="line" data={book.salesData} />
                        </ChartWrapper>

                        <ChartWrapper
                          title="Revenue Breakdown"
                          description="Revenue by marketplace"
                          height="sm"
                          variant="elevated"
                        >
                          <ChartPlaceholder type="pie" data={[
                            { name: "Amazon KDP", value: book.revenue * 0.8 },
                            { name: "Barnes & Noble", value: book.revenue * 0.2 }
                          ]} />
                        </ChartWrapper>
                      </div>

                      {/* Performance Metrics */}
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <TrendingUp className="h-5 w-5" />
                              Growth Metrics
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Monthly Growth</span>
                              <span className="font-medium text-green-600">+18.5%</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Revenue/Sale</span>
                              <span className="font-medium">${(book.revenue / book.sales).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Conversion Rate</span>
                              <span className="font-medium">2.4%</span>
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Users className="h-5 w-5" />
                              Audience Insights
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Primary Market</span>
                              <span className="font-medium">United States</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Age Group</span>
                              <span className="font-medium">25-45</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Return Readers</span>
                              <span className="font-medium">34%</span>
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Target className="h-5 w-5" />
                              Goals & Targets
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-muted-foreground">Monthly Target</span>
                                <span className="font-medium">100 sales</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{width: `${(book.sales / 100) * 100}%`}}></div>
                              </div>
                              <span className="text-xs text-muted-foreground">{book.sales}/100 sales ({((book.sales / 100) * 100).toFixed(0)}%)</span>
                            </div>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-muted-foreground">Revenue Goal</span>
                                <span className="font-medium">$2,000</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2">
                                <div className="bg-green-500 h-2 rounded-full" style={{width: `${(book.revenue / 2000) * 100}%`}}></div>
                              </div>
                              <span className="text-xs text-muted-foreground">${book.revenue.toFixed(2)}/$2,000 ({((book.revenue / 2000) * 100).toFixed(0)}%)</span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Activity Timeline */}
                      <Card>
                        <CardHeader>
                          <CardTitle>Recent Activity</CardTitle>
                          <CardDescription>Timeline of key events for this book</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ActivityTimeline 
                            items={book.recentActivity} 
                            compact 
                            size="sm"
                          />
                        </CardContent>
                      </Card>
                    </>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <Heading as="h3" variant="h5" className="mb-2">
                          Performance data unavailable
                        </Heading>
                        <Text variant="muted">
                          Performance analytics will be available once your book is published.
                        </Text>
                        <div className="mt-6">
                          <Button onClick={() => console.log('Publish book')}>
                            <Upload className="h-4 w-4 mr-2" />
                            Publish Book to View Analytics
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="settings">
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Text variant="muted">Book settings coming soon...</Text>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}