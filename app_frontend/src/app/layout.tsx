import type { Metadata } from 'next'
import { Inter, Nunito_Sans } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/components/providers/auth-provider'
import { QueryProvider } from "@/components/providers/query-provider"
import { ThemeProvider } from '@/contexts/theme-context'
import { themeScript } from '@/lib/theme-script'

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const nunitoSans = Nunito_Sans({
  subsets: ["latin"],
  variable: "--font-nunito-sans",
});

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
  ),
  title: "Publish AI - AI-Powered Book Publishing Platform",
  description:
    "Create, publish, and monetize books with the power of AI. From manuscript generation to Amazon KDP publishing.",
  keywords: [
    "AI",
    "book publishing",
    "ebook",
    "KDP",
    "manuscript",
    "writing",
    "automation",
  ],
  authors: [{ name: "Publish AI Team" }],
  openGraph: {
    title: "Publish AI - AI-Powered Book Publishing Platform",
    description: "Create, publish, and monetize books with the power of AI",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${nunitoSans.variable}`}>
      <head>
        <script dangerouslySetInnerHTML={{ __html: themeScript }} />
      </head>
      <body className="font-sans antialiased">
        <QueryProvider>
          <AuthProvider>
            <ThemeProvider>
              {children}
            </ThemeProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
