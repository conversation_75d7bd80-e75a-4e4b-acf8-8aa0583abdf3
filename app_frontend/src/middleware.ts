import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"
import { 
  isProtectedRoute, 
  isPublicRoute, 
  requiresRole, 
  getDefaultRedirect,
  AUTH_ROUTES,
  DEFAULT_REDIRECTS 
} from "@/config/auth"
import type { UserRole } from "@/lib/auth/permissionHelpers"

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes - handle auth separately)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     * - auth pages (handle separately to prevent conflicts)
     * - .well-known (for Chrome dev tools)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public|auth|\\.well-known).*)",
  ],
}

export async function middleware(request: NextRequest) {
  const { pathname, origin } = request.nextUrl
  
  // In development, be more permissive to avoid blocking auth flows
  if (process.env.NODE_ENV === "development") {
    return developmentMiddleware(request)
  }
  
  // Get the session token
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET 
  })
  
  const isAuthenticated = !!token
  const userRole = (token?.role as UserRole) || "guest"
  
  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next()
  }
  
  // Handle authentication for protected routes
  if (isProtectedRoute(pathname)) {
    // Redirect unauthenticated users to login
    if (!isAuthenticated) {
      const loginUrl = new URL(AUTH_ROUTES.LOGIN, origin)
      loginUrl.searchParams.set("callbackUrl", request.url)
      return NextResponse.redirect(loginUrl)
    }
    
    // Check role-based access
    const hasRoleAccess = checkRoleAccess(pathname, userRole)
    if (!hasRoleAccess) {
      // Redirect to appropriate dashboard or access denied page
      const redirectUrl = new URL(DEFAULT_REDIRECTS.unauthorized, origin)
      return NextResponse.redirect(redirectUrl)
    }
  }
  
  // Handle auth pages when user is already authenticated
  if (pathname.startsWith("/auth/")) {
    if (isAuthenticated) {
      // Redirect authenticated users away from auth pages
      const dashboardUrl = new URL(getDefaultRedirect(userRole), origin)
      return NextResponse.redirect(dashboardUrl)
    }
  }
  
  // Add security headers
  const response = NextResponse.next()
  addSecurityHeaders(response)
  
  return response
}

/**
 * Development-specific middleware that's more permissive
 */
function developmentMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // In development, only protect obvious admin routes
  if (pathname.startsWith("/admin") || pathname.startsWith("/dashboard/admin")) {
    // Let the client-side guards handle it
    console.log(`[Dev Middleware] Allowing access to ${pathname} (client-side guards will handle)`)
  }
  
  // Add minimal security headers for development
  const response = NextResponse.next()
  response.headers.set("X-Frame-Options", "DENY")
  
  return response
}

/**
 * Checks if user has role-based access to a specific route
 */
function checkRoleAccess(pathname: string, userRole: UserRole): boolean {
  // Admin routes
  if (pathname.startsWith("/admin") || pathname.startsWith("/dashboard/admin")) {
    return userRole === "admin"
  }
  
  // Editor routes
  if (pathname.startsWith("/dashboard/editor")) {
    return ["admin", "editor"].includes(userRole)
  }
  
  // Author routes
  if (pathname.startsWith("/dashboard/author")) {
    return ["admin", "editor", "author"].includes(userRole)
  }
  
  // User management (admin only)
  if (pathname.startsWith("/dashboard/users") || pathname.startsWith("/users")) {
    return userRole === "admin"
  }
  
  // System settings (admin only)
  if (pathname.startsWith("/dashboard/system") || pathname.startsWith("/settings/system")) {
    return userRole === "admin"
  }
  
  // Content management (editor and above)
  if (pathname.startsWith("/content/manage")) {
    return ["admin", "editor"].includes(userRole)
  }
  
  // Book creation (author and above)
  if (pathname.startsWith("/books/create") || pathname.startsWith("/dashboard/books/new")) {
    return ["admin", "editor", "author"].includes(userRole)
  }
  
  // Analytics (author and above)
  if (pathname.startsWith("/analytics") || pathname.startsWith("/dashboard/analytics")) {
    return ["admin", "editor", "author"].includes(userRole)
  }
  
  // Default: allow access for basic dashboard routes
  return true
}

/**
 * Adds security headers to the response
 */
function addSecurityHeaders(response: NextResponse) {
  // Prevent clickjacking
  response.headers.set("X-Frame-Options", "DENY")
  
  // Prevent MIME type sniffing
  response.headers.set("X-Content-Type-Options", "nosniff")
  
  // Enable XSS protection
  response.headers.set("X-XSS-Protection", "1; mode=block")
  
  // Referrer policy
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
  
  // Content Security Policy (basic)
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Next.js requires these
    "style-src 'self' 'unsafe-inline'", // CSS-in-JS requires unsafe-inline
    "img-src 'self' data: blob: https:",
    "font-src 'self' data:",
    "connect-src 'self' https://api.anthropic.com https://api.openai.com",
    "frame-ancestors 'none'",
  ].join("; ")
  
  response.headers.set("Content-Security-Policy", csp)
  
  // Permissions Policy (formerly Feature Policy)
  response.headers.set("Permissions-Policy", [
    "camera=()",
    "microphone=()",
    "geolocation=()",
    "payment=()",
    "usb=()",
  ].join(", "))
  
  return response
}

/**
 * Enhanced middleware with rate limiting (for production)
 */
export async function enhancedMiddleware(request: NextRequest) {
  const { pathname, origin } = request.nextUrl
  const clientIp = getClientIP(request)
  
  // Basic rate limiting check
  if (await isRateLimited(clientIp, pathname)) {
    return new NextResponse("Too Many Requests", { status: 429 })
  }
  
  // Continue with normal middleware
  return middleware(request)
}

/**
 * Gets client IP address from request
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for")
  const realIP = request.headers.get("x-real-ip")
  
  if (forwarded) {
    return forwarded.split(",")[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return "unknown"
}

/**
 * Simple in-memory rate limiting (use Redis in production)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

async function isRateLimited(clientIP: string, pathname: string): Promise<boolean> {
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15 minutes
  const maxRequests = pathname.startsWith("/auth/") ? 10 : 100 // Lower limit for auth routes
  
  const key = `${clientIP}:${pathname.startsWith("/auth/") ? "auth" : "general"}`
  const current = rateLimitMap.get(key)
  
  if (!current || now > current.resetTime) {
    // Reset or create new rate limit entry
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return false
  }
  
  if (current.count >= maxRequests) {
    return true // Rate limited
  }
  
  // Increment count
  current.count++
  return false
}

/**
 * Middleware specifically for API routes
 */
export async function apiMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip authentication for public API routes
  if (pathname.startsWith("/api/public/")) {
    return NextResponse.next()
  }
  
  // Skip authentication for NextAuth routes
  if (pathname.startsWith("/api/auth/")) {
    return NextResponse.next()
  }
  
  // Check authentication for protected API routes
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET 
  })
  
  if (!token) {
    return new NextResponse("Unauthorized", { status: 401 })
  }
  
  // Add user info to headers for API routes
  const response = NextResponse.next()
  response.headers.set("X-User-ID", token.sub || "")
  response.headers.set("X-User-Role", (token.role as string) || "user")
  
  return response
}

/**
 * Development-only middleware that logs requests
 */
export function devMiddleware(request: NextRequest) {
  if (process.env.NODE_ENV === "development") {
    console.log(`[Middleware] ${request.method} ${request.nextUrl.pathname}`)
  }
  return middleware(request)
}

// Use the appropriate middleware based on environment
export default process.env.NODE_ENV === "development" 
  ? devMiddleware 
  : middleware