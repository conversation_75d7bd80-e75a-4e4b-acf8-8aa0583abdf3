import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"
import { Heading, Text } from "@/components/ui/typography"

const meta = {
  title: "UI/Typography",
  component: Heading,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Heading>

export default meta
type Story = StoryObj<typeof meta>

export const Headings: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading variant="h1">Heading 1 - Build your dreams</Heading>
      <Heading variant="h2">Heading 2 - Publish with AI</Heading>
      <Heading variant="h3">Heading 3 - Create amazing content</Heading>
      <Heading variant="h4">Heading 4 - Analytics dashboard</Heading>
      <Heading variant="h5">Heading 5 - User settings</Heading>
      <Heading variant="h6">Heading 6 - Small title</Heading>
    </div>
  ),
}

export const TextVariants: Story = {
  render: () => (
    <div className="space-y-4 max-w-2xl">
      <Text variant="lead">
        Lead text - This is a lead paragraph that stands out from regular body text. 
        It's typically used for introductions or key points.
      </Text>
      <Text variant="body">
        Body text - This is regular body text. It forms the main content of your 
        pages and should be easily readable. The font size and line height are 
        optimized for comfortable reading.
      </Text>
      <Text variant="small">
        Small text - This is smaller text that can be used for secondary information 
        or fine print. It's still readable but less prominent.
      </Text>
      <Text variant="muted">
        Muted text - This is muted text with lower contrast. It's useful for 
        supplementary information that shouldn't compete with primary content.
      </Text>
      <Text variant="caption">
        Caption text - This is caption text, typically used for image captions, 
        timestamps, or other metadata.
      </Text>
    </div>
  ),
}