import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta = {
  title: 'Design System/Design Tokens',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Airbnb-inspired design tokens that form the foundation of our design system.',
      },
    },
  },
};

export default meta;

export const Colors: StoryObj = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Primary Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="space-y-2">
              <div
                className={`h-16 w-full rounded-lg bg-primary-${shade} border border-gray-200`}
                style={{ backgroundColor: getColorValue('primary', shade) }}
              />
              <div className="text-sm">
                <div className="font-medium">primary-{shade}</div>
                <div className="text-gray-500">{getColorValue('primary', shade)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Secondary Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="space-y-2">
              <div
                className={`h-16 w-full rounded-lg bg-secondary-${shade} border border-gray-200`}
                style={{ backgroundColor: getColorValue('secondary', shade) }}
              />
              <div className="text-sm">
                <div className="font-medium">secondary-{shade}</div>
                <div className="text-gray-500">{getColorValue('secondary', shade)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Accent Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="space-y-2">
              <div
                className={`h-16 w-full rounded-lg bg-accent-${shade} border border-gray-200`}
                style={{ backgroundColor: getColorValue('accent', shade) }}
              />
              <div className="text-sm">
                <div className="font-medium">accent-{shade}</div>
                <div className="text-gray-500">{getColorValue('accent', shade)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Surface Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="space-y-2">
              <div
                className={`h-16 w-full rounded-lg bg-surface-${shade} border border-gray-200`}
                style={{ backgroundColor: getColorValue('surface', shade) }}
              />
              <div className="text-sm">
                <div className="font-medium">surface-{shade}</div>
                <div className="text-gray-500">{getColorValue('surface', shade)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Semantic Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { name: 'Success', color: '#10B981', bg: 'bg-success' },
            { name: 'Warning', color: '#F59E0B', bg: 'bg-warning' },
            { name: 'Error', color: '#EF4444', bg: 'bg-error' },
            { name: 'Info', color: '#3B82F6', bg: 'bg-info' },
          ].map((item) => (
            <div key={item.name} className="space-y-2">
              <div className={`h-16 w-full rounded-lg ${item.bg} border border-gray-200`} />
              <div className="text-sm">
                <div className="font-medium">{item.name.toLowerCase()}</div>
                <div className="text-gray-500">{item.color}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Our color palette follows Airbnb\'s design principles with the signature coral red (#FF385C) as the primary color.',
      },
    },
  },
};

export const Typography: StoryObj = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Font Sizes</h2>
        <div className="space-y-4">
          {[
            { size: 'text-xs', label: 'Extra Small (12px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-sm', label: 'Small (14px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-base', label: 'Base (16px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-lg', label: 'Large (18px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-xl', label: 'Extra Large (20px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-2xl', label: '2X Large (24px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-3xl', label: '3X Large (30px)', example: 'The quick brown fox jumps over the lazy dog' },
            { size: 'text-4xl', label: '4X Large (36px)', example: 'The quick brown fox jumps over the lazy dog' },
          ].map((item) => (
            <div key={item.size} className="flex items-center space-x-4">
              <div className="w-32 text-sm text-gray-500">{item.label}</div>
              <div className={`${item.size} font-sans`}>{item.example}</div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Font Weights</h2>
        <div className="space-y-2">
          <div className="font-normal">Normal - Regular text for body content</div>
          <div className="font-medium">Medium - Slightly emphasized text</div>
          <div className="font-semibold">Semibold - Headings and important labels</div>
          <div className="font-bold">Bold - Strong emphasis</div>
        </div>
      </div>
    </div>
  ),
};

export const Spacing: StoryObj = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Spacing Scale (4pt Grid)</h2>
        <div className="space-y-4">
          {[
            { class: 'p-1', value: '4px', name: 'p-1' },
            { class: 'p-2', value: '8px', name: 'p-2' },
            { class: 'p-3', value: '12px', name: 'p-3' },
            { class: 'p-4', value: '16px', name: 'p-4' },
            { class: 'p-5', value: '20px', name: 'p-5' },
            { class: 'p-6', value: '24px', name: 'p-6' },
            { class: 'p-8', value: '32px', name: 'p-8' },
            { class: 'p-10', value: '40px', name: 'p-10' },
            { class: 'p-12', value: '48px', name: 'p-12' },
            { class: 'p-16', value: '64px', name: 'p-16' },
          ].map((item) => (
            <div key={item.name} className="flex items-center space-x-4">
              <div className="w-20 text-sm text-gray-500">{item.name}</div>
              <div className="w-16 text-sm text-gray-500">{item.value}</div>
              <div className={`bg-primary-100 ${item.class} text-primary-800 text-sm rounded`}>
                Example
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
};

export const BorderRadius: StoryObj = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Border Radius</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { class: 'rounded-none', value: '0px', name: 'none' },
            { class: 'rounded-sm', value: '4px', name: 'sm' },
            { class: 'rounded', value: '8px', name: 'default' },
            { class: 'rounded-md', value: '12px', name: 'md' },
            { class: 'rounded-lg', value: '16px', name: 'lg' },
            { class: 'rounded-xl', value: '24px', name: 'xl' },
            { class: 'rounded-2xl', value: '32px', name: '2xl' },
            { class: 'rounded-3xl', value: '48px', name: '3xl' },
          ].map((item) => (
            <div key={item.name} className="text-center space-y-2">
              <div className={`h-16 w-full bg-primary-100 ${item.class} border border-primary-200`} />
              <div className="text-sm">
                <div className="font-medium">{item.name}</div>
                <div className="text-gray-500">{item.value}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
};

export const Shadows: StoryObj = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Box Shadows</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {[
            { class: 'shadow-sm', name: 'Small' },
            { class: 'shadow', name: 'Default' },
            { class: 'shadow-md', name: 'Medium' },
            { class: 'shadow-lg', name: 'Large' },
            { class: 'shadow-xl', name: 'Extra Large' },
            { class: 'shadow-2xl', name: '2X Large' },
          ].map((item) => (
            <div key={item.name} className="text-center space-y-3">
              <div className={`h-24 w-full bg-white ${item.class} rounded-lg flex items-center justify-center`}>
                <span className="text-gray-600">{item.name}</span>
              </div>
              <div className="text-sm font-medium">{item.class}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
};

// Helper function to get color values (simplified for demo)
function getColorValue(color: string, shade: number): string {
  const colors = {
    primary: {
      50: '#FFF0F3',
      100: '#FFE1E7',
      200: '#FFC2CF',
      300: '#FF94AB',
      400: '#FF5A7A',
      500: '#FF385C',
      600: '#E61E4D',
      700: '#CC0036',
      800: '#A60029',
      900: '#800020',
      950: '#590016',
    },
    secondary: {
      50: '#F7F7F7',
      100: '#EBEBEB',
      200: '#D4D4D4',
      300: '#B0B0B0',
      400: '#888888',
      500: '#717171',
      600: '#5A5A5A',
      700: '#484848',
      800: '#3C3C3C',
      900: '#343434',
      950: '#222222',
    },
    accent: {
      50: '#F6F6F6',
      100: '#E7E7E7',
      200: '#D1D1D1',
      300: '#B0B0B0',
      400: '#888888',
      500: '#6F6F6F',
      600: '#5D5D5D',
      700: '#4C4C4C',
      800: '#222222',
      900: '#191919',
      950: '#0D0D0D',
    },
    surface: {
      50: '#FFFFFF',
      100: '#FCFCFC',
      200: '#F7F7F7',
      300: '#EFEFEF',
      400: '#E7E7E7',
      500: '#DDDDDD',
      600: '#C4C4C4',
      700: '#A8A8A8',
      800: '#8C8C8C',
      900: '#717171',
      950: '#595959',
    },
  };
  
  return colors[color as keyof typeof colors]?.[shade as keyof typeof colors['primary']] || '#000000';
}