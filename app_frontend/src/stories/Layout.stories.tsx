import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { <PERSON>Header } from "@/components/layout/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const meta = {
  title: "Layout/Dashboard Layout",
  component: DashboardLayout,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof DashboardLayout>

export default meta
type Story = StoryObj<typeof meta>

const mockUser = {
  name: "<PERSON>",
  email: "<EMAIL>",
  avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=John",
}

export const Default: Story = {
  args: {
    user: mockUser,
    children: (
      <div className="p-6">
        <PageHeader
          title="Dashboard"
          description="Welcome back! Here's an overview of your publishing activity."
          actions={
            <Button>Create New Book</Button>
          }
        />
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[
            { title: "Total Books", value: "24", description: "+2 from last month" },
            { title: "Published", value: "18", description: "75% of total" },
            { title: "Revenue", value: "$12,345", description: "+15% from last month" },
            { title: "Active Readers", value: "1,234", description: "+201 since last week" },
          ].map((stat, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    ),
  },
}

export const WithFooter: Story = {
  args: {
    user: mockUser,
    showFooter: true,
    children: (
      <div className="p-6 min-h-[calc(100vh-4rem)]">
        <PageHeader
          title="Settings"
          description="Manage your account settings and preferences."
        />
        <Card>
          <CardHeader>
            <CardTitle>Account Settings</CardTitle>
            <CardDescription>
              Update your personal information and preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Settings content would go here...
            </p>
          </CardContent>
        </Card>
      </div>
    ),
  },
}

export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
  args: {
    user: mockUser,
    children: (
      <div className="p-4">
        <PageHeader
          title="My Books"
          description="Manage your book collection"
          actions={
            <Button size="sm">Add Book</Button>
          }
        />
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <CardTitle className="text-lg">Book Title {i}</CardTitle>
                <CardDescription>Science Fiction</CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    ),
  },
}