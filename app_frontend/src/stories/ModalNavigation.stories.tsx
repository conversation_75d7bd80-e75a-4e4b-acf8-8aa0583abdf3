import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  ConfirmationModal, 
  FilterModal, 
  SuccessModal, 
  LoadingModal, 
  ImagePreviewModal 
} from '@/components/ui/modal';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
} from '@/components/ui/breadcrumb';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { AdvancedPagination } from '@/components/ui/pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FormField } from '@/components/ui/form-field';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  BookOpen, 
  BarChart3, 
  Settings, 
  Users, 
  Filter, 
  Home, 
  ChevronRight,
  TrendingUp,
  FileText,
  Image as ImageIcon 
} from 'lucide-react';

const meta: Meta = {
  title: 'UI/Modal & Navigation Components',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Comprehensive modal system and navigation components built with Airbnb-inspired design tokens. Includes Dialogs, specialized modals, Breadcrumbs, Tabs, and Pagination.',
      },
    },
  },
};

export default meta;

// Dialog variants component
const DialogVariantsComponent = () => {
  const [open, setOpen] = useState(false);
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Small Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Small Dialog</Button>
            </DialogTrigger>
            <DialogContent size="sm">
              <DialogHeader>
                <DialogTitle>Small Dialog</DialogTitle>
                <DialogDescription>Perfect for simple confirmations</DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button>Confirm</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Default Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Default Dialog</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Book Details</DialogTitle>
                <DialogDescription>Make changes to your book information</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <FormField label="Title" id="title">
                  <Input placeholder="Enter book title" />
                </FormField>
                <FormField label="Description" id="description">
                  <Input placeholder="Enter description" />
                </FormField>
              </div>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button>Save Changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Large Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Large Dialog</Button>
            </DialogTrigger>
            <DialogContent size="lg">
              <DialogHeader>
                <DialogTitle>Advanced Settings</DialogTitle>
                <DialogDescription>Configure your publishing preferences</DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-4 py-4">
                <FormField label="Genre" id="genre">
                  <Input placeholder="Fiction" />
                </FormField>
                <FormField label="Target Audience" id="audience">
                  <Input placeholder="Adults" />
                </FormField>
                <FormField label="Price" id="price">
                  <Input placeholder="$9.99" />
                </FormField>
                <FormField label="Language" id="language">
                  <Input placeholder="English" />
                </FormField>
              </div>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button>Save Settings</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Fullscreen Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Fullscreen Dialog</Button>
            </DialogTrigger>
            <DialogContent variant="fullscreen">
              <DialogHeader>
                <DialogTitle>Book Preview</DialogTitle>
                <DialogDescription>Full preview of your generated book</DialogDescription>
              </DialogHeader>
              <div className="flex-1 bg-surface-50 rounded-lg p-8 my-4">
                <div className="max-w-4xl mx-auto space-y-6">
                  <h1 className="text-3xl font-bold">Chapter 1: The Beginning</h1>
                  <p className="text-lg leading-relaxed">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
                    tempor incididunt ut labore et dolore magna aliqua...
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline">Edit</Button>
                <Button>Publish</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Drawer Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Drawer Dialog</Button>
            </DialogTrigger>
            <DialogContent variant="drawer">
              <DialogHeader>
                <DialogTitle>Quick Actions</DialogTitle>
                <DialogDescription>Common tasks for your books</DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-3 py-4">
                <Button variant="outline" className="h-16 flex-col gap-2">
                  <BookOpen className="h-5 w-5" />
                  Generate Book
                </Button>
                <Button variant="outline" className="h-16 flex-col gap-2">
                  <BarChart3 className="h-5 w-5" />
                  View Analytics
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    );
};

export const DialogVariants: StoryObj = {
  render: () => <DialogVariantsComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Dialog component with different sizes and variants for various use cases.',
      },
    },
  },
};

// Specialized modals component
const SpecializedModalsComponent = () => {
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [successOpen, setSuccessOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [loadingOpen, setLoadingOpen] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [imageOpen, setImageOpen] = useState(false);

    const handleStartLoading = () => {
      setLoadingOpen(true);
      setLoadingProgress(0);
      
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setLoadingOpen(false);
            setSuccessOpen(true);
            return 100;
          }
          return prev + 10;
        });
      }, 500);
    };

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Confirmation Modal */}
          <div>
            <Button onClick={() => setConfirmOpen(true)} variant="destructive">
              Delete Book
            </Button>
            <ConfirmationModal
              open={confirmOpen}
              onOpenChange={setConfirmOpen}
              title="Delete Book"
              description="Are you sure you want to delete this book? This action cannot be undone."
              variant="destructive"
              confirmText="Delete"
              onConfirm={() => {
                console.log('Book deleted');
                setConfirmOpen(false);
              }}
            />
          </div>

          {/* Warning Modal */}
          <ConfirmationModal
            title="Publish Warning"
            description="This book has not been reviewed. Are you sure you want to publish?"
            variant="warning"
            confirmText="Publish Anyway"
            onConfirm={() => console.log('Published with warning')}
          >
            <Button variant="outline">Publish Book</Button>
          </ConfirmationModal>

          {/* Filter Modal */}
          <div>
            <Button onClick={() => setFilterOpen(true)} variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter Analytics
            </Button>
            <FilterModal
              open={filterOpen}
              onOpenChange={setFilterOpen}
              title="Analytics Filters"
              onApplyFilters={(filters) => {
                console.log('Applied filters:', filters);
                setFilterOpen(false);
              }}
              onResetFilters={() => console.log('Reset filters')}
            >
              <div className="space-y-4">
                <FormField label="Date Range" id="dateRange">
                  <Input type="date" />
                </FormField>
                <FormField label="Book Status" id="status">
                  <Input placeholder="Published, Draft, etc." />
                </FormField>
                <FormField label="Genre" id="genre">
                  <Input placeholder="Fiction, Non-fiction, etc." />
                </FormField>
              </div>
            </FilterModal>
          </div>

          {/* Loading Modal */}
          <div>
            <Button onClick={handleStartLoading} variant="outline">
              Start Generation
            </Button>
            <LoadingModal
              open={loadingOpen}
              title="Generating Book..."
              description="AI is creating your book content"
              progress={loadingProgress}
              showProgress={true}
            />
          </div>

          {/* Success Modal */}
          <SuccessModal
            open={successOpen}
            onOpenChange={setSuccessOpen}
            title="Book Generated Successfully!"
            description="Your book has been created and is ready for review."
            actionText="View Book"
            onAction={() => console.log('Viewing book')}
          />

          {/* Image Preview Modal */}
          <div>
            <Button onClick={() => setImageOpen(true)} variant="outline">
              <ImageIcon className="h-4 w-4 mr-2" />
              Preview Cover
            </Button>
            <ImagePreviewModal
              open={imageOpen}
              onOpenChange={setImageOpen}
              src="https://via.placeholder.com/400x600/FF385C/FFFFFF?text=Book+Cover"
              alt="Book Cover Preview"
              title="Book Cover"
            />
          </div>
        </div>
      </div>
    );
};

export const SpecializedModals: StoryObj = {
  render: () => <SpecializedModalsComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Specialized modal components for different use cases like confirmations, filters, and loading states.',
      },
    },
  },
};

export const BreadcrumbNavigation: StoryObj = {
  render: () => (
    <div className="space-y-8">
      {/* Basic Breadcrumb */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Basic Breadcrumb</h3>
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">
                <Home className="h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/books">Books</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Book Details</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Breadcrumb with Ellipsis */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Breadcrumb with Ellipsis</h3>
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbEllipsis />
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/books/category">Category</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Current Page</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Custom Separator */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Custom Separator</h3>
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink href="/analytics">Analytics</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage>Sales Report</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Breadcrumb navigation component for showing current page location in the hierarchy.',
      },
    },
  },
};

export const TabNavigation: StoryObj = {
  render: () => (
    <div className="space-y-8">
      {/* Default Tabs */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Default Tabs</h3>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview" icon={<BarChart3 className="h-4 w-4" />}>
              Overview
            </TabsTrigger>
            <TabsTrigger value="books" icon={<BookOpen className="h-4 w-4" />} badge={<Badge variant="secondary" size="sm">12</Badge>}>
              Books
            </TabsTrigger>
            <TabsTrigger value="analytics" icon={<TrendingUp className="h-4 w-4" />}>
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" icon={<Settings className="h-4 w-4" />}>
              Settings
            </TabsTrigger>
          </TabsList>
          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <CardTitle>Overview</CardTitle>
                <CardDescription>Dashboard overview with key metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Your publishing dashboard overview content here.</p>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="books">
            <Card>
              <CardHeader>
                <CardTitle>Books</CardTitle>
                <CardDescription>Manage your published books</CardDescription>
              </CardHeader>
              <CardContent>
                <p>List of your books and management tools.</p>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
                <CardDescription>Sales and performance analytics</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Analytics charts and reports go here.</p>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
                <CardDescription>Account and publication settings</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Settings configuration options.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Underline Tabs */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Underline Tabs</h3>
        <Tabs defaultValue="drafts" className="w-full">
          <TabsList variant="underline">
            <TabsTrigger variant="underline" value="drafts" icon={<FileText className="h-4 w-4" />}>
              Drafts
            </TabsTrigger>
            <TabsTrigger variant="underline" value="published" icon={<BookOpen className="h-4 w-4" />}>
              Published
            </TabsTrigger>
            <TabsTrigger variant="underline" value="archived" icon={<Users className="h-4 w-4" />}>
              Archived
            </TabsTrigger>
          </TabsList>
          <TabsContent value="drafts" className="mt-6">
            <p>Draft books that are being worked on.</p>
          </TabsContent>
          <TabsContent value="published" className="mt-6">
            <p>Books that have been published and are live.</p>
          </TabsContent>
          <TabsContent value="archived" className="mt-6">
            <p>Archived books that are no longer active.</p>
          </TabsContent>
        </Tabs>
      </div>

      {/* Pill Tabs */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Pill Tabs</h3>
        <Tabs defaultValue="all" className="w-full">
          <TabsList variant="pills">
            <TabsTrigger variant="pills" value="all">
              All Books
            </TabsTrigger>
            <TabsTrigger variant="pills" value="fiction">
              Fiction
            </TabsTrigger>
            <TabsTrigger variant="pills" value="nonfiction">
              Non-Fiction
            </TabsTrigger>
            <TabsTrigger variant="pills" value="mystery">
              Mystery
            </TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="mt-6">
            <p>All books in your library.</p>
          </TabsContent>
          <TabsContent value="fiction" className="mt-6">
            <p>Fiction books only.</p>
          </TabsContent>
          <TabsContent value="nonfiction" className="mt-6">
            <p>Non-fiction books only.</p>
          </TabsContent>
          <TabsContent value="mystery" className="mt-6">
            <p>Mystery genre books.</p>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Tab navigation component with different variants, icons, and badges.',
      },
    },
  },
};

// Pagination component with state
const PaginationDemoComponent = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPage2, setCurrentPage2] = useState(5);
    
    return (
      <div className="space-y-8">
        {/* Basic Pagination */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Basic Pagination</h3>
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={10}
            onPageChange={setCurrentPage}
            maxVisiblePages={5}
          />
          <p className="mt-2 text-sm text-muted-foreground">
            Current page: {currentPage} of 10
          </p>
        </div>

        {/* Large Pagination */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Large Pagination with Many Pages</h3>
          <AdvancedPagination
            currentPage={currentPage2}
            totalPages={50}
            onPageChange={setCurrentPage2}
            maxVisiblePages={7}
            size="lg"
          />
          <p className="mt-2 text-sm text-muted-foreground">
            Current page: {currentPage2} of 50
          </p>
        </div>

        {/* Compact Pagination */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Compact Pagination</h3>
          <AdvancedPagination
            currentPage={3}
            totalPages={25}
            onPageChange={(page) => console.log('Page changed to:', page)}
            showFirstLast={false}
            maxVisiblePages={3}
            size="sm"
          />
        </div>

        {/* Disabled Pagination */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Disabled Pagination</h3>
          <AdvancedPagination
            currentPage={1}
            totalPages={5}
            onPageChange={(page) => console.log('Page changed to:', page)}
            disabled={true}
          />
        </div>
      </div>
    );
};

export const PaginationComponent: StoryObj = {
  render: () => <PaginationDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Advanced pagination component with customizable options and responsive design.',
      },
    },
  },
};