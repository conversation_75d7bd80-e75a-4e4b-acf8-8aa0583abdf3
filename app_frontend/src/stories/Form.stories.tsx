import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FormField } from '@/components/ui/form-field';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, Mail, Lock, User, Calendar } from 'lucide-react';

const meta: Meta = {
  title: 'UI/Form Components',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Comprehensive form components built with Airbnb-inspired design tokens. Includes Input, Textarea, Select, and FormField wrapper.',
      },
    },
  },
};

export default meta;

export const InputVariants: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">Input Variants</h3>
        <div className="space-y-4">
          <Input placeholder="Default input" />
          <Input placeholder="Error state" error="This field is required" />
          <Input placeholder="Success state" success="Email is available" />
          <Input placeholder="Warning state" warning="Consider using a stronger password" />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Input component with different validation states.',
      },
    },
  },
};

export const InputSizes: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">Input Sizes</h3>
        <div className="space-y-4">
          <Input placeholder="Small input" size="sm" />
          <Input placeholder="Default input" size="default" />
          <Input placeholder="Large input" size="lg" />
        </div>
      </div>
    </div>
  ),
};

export const InputWithIcons: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">Input with Icons</h3>
        <div className="space-y-4">
          <Input 
            placeholder="Search..." 
            leftIcon={<Search className="h-4 w-4" />}
          />
          <Input 
            placeholder="Enter email" 
            type="email"
            leftIcon={<Mail className="h-4 w-4" />}
          />
          <Input 
            placeholder="Password" 
            type="password"
            leftIcon={<Lock className="h-4 w-4" />}
          />
          <Input 
            placeholder="Username" 
            leftIcon={<User className="h-4 w-4" />}
            rightIcon={<Calendar className="h-4 w-4" />}
          />
        </div>
      </div>
    </div>
  ),
};

export const TextareaVariants: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">Textarea Variants</h3>
        <div className="space-y-4">
          <Textarea placeholder="Default textarea" />
          <Textarea 
            placeholder="Textarea with character count" 
            maxLength={200}
            showCharCount
          />
          <Textarea 
            placeholder="Error state textarea" 
            error="Description is required"
          />
          <Textarea 
            placeholder="Large textarea" 
            size="lg"
            helperText="Tell us about your book idea"
          />
        </div>
      </div>
    </div>
  ),
};

export const SelectComponent: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">Select Component</h3>
        <div className="space-y-4">
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select a genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fiction">Fiction</SelectItem>
              <SelectItem value="non-fiction">Non-Fiction</SelectItem>
              <SelectItem value="mystery">Mystery</SelectItem>
              <SelectItem value="romance">Romance</SelectItem>
              <SelectItem value="sci-fi">Science Fiction</SelectItem>
            </SelectContent>
          </Select>

          <Select>
            <SelectTrigger variant="error">
              <SelectValue placeholder="Error state select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
            </SelectContent>
          </Select>

          <Select>
            <SelectTrigger size="lg">
              <SelectValue placeholder="Large select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  ),
};

export const FormFieldWrapper: StoryObj = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <div>
        <h3 className="text-lg font-semibold mb-3">FormField Wrapper</h3>
        <div className="space-y-6">
          <FormField 
            label="Email Address" 
            required 
            id="email"
            helperText="We'll never share your email"
          >
            <Input 
              type="email" 
              placeholder="Enter your email"
              leftIcon={<Mail className="h-4 w-4" />}
            />
          </FormField>

          <FormField 
            label="Password" 
            required 
            id="password"
            error="Password must be at least 8 characters"
          >
            <Input 
              type="password" 
              placeholder="Enter password"
              leftIcon={<Lock className="h-4 w-4" />}
            />
          </FormField>

          <FormField 
            label="Book Genre" 
            required 
            id="genre"
          >
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select a genre" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fiction">Fiction</SelectItem>
                <SelectItem value="non-fiction">Non-Fiction</SelectItem>
                <SelectItem value="mystery">Mystery</SelectItem>
              </SelectContent>
            </Select>
          </FormField>

          <FormField 
            label="Book Description" 
            id="description"
            helperText="Describe your book in a few sentences"
          >
            <Textarea 
              placeholder="Enter book description"
              maxLength={500}
              showCharCount
            />
          </FormField>
        </div>
      </div>
    </div>
  ),
};

// Complete form component
const CompleteFormComponent = () => {
  const [formData, setFormData] = useState({
    title: '',
    email: '',
    genre: '',
    description: '',
    targetAudience: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: Record<string, string> = {};
    
    if (!formData.title) newErrors.title = 'Title is required';
    if (!formData.email) newErrors.email = 'Email is required';
    if (!formData.genre) newErrors.genre = 'Please select a genre';
    if (!formData.description) newErrors.description = 'Description is required';
    
    setErrors(newErrors);
    
    if (Object.keys(newErrors).length === 0) {
      alert('Form submitted successfully!');
    }
  };

  return (
      <div className="max-w-md mx-auto">
        <div className="bg-white p-6 rounded-xl shadow-lg border">
          <h2 className="text-2xl font-semibold mb-6 text-center">Create New Book</h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormField 
              label="Book Title" 
              required 
              id="title"
              error={errors.title}
            >
              <Input 
                placeholder="Enter book title"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
              />
            </FormField>

            <FormField 
              label="Author Email" 
              required 
              id="email"
              error={errors.email}
            >
              <Input 
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="h-4 w-4" />}
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
              />
            </FormField>

            <FormField 
              label="Genre" 
              required 
              id="genre"
              error={errors.genre}
            >
              <Select value={formData.genre} onValueChange={(value) => setFormData({...formData, genre: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a genre" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fiction">Fiction</SelectItem>
                  <SelectItem value="non-fiction">Non-Fiction</SelectItem>
                  <SelectItem value="mystery">Mystery</SelectItem>
                  <SelectItem value="romance">Romance</SelectItem>
                  <SelectItem value="sci-fi">Science Fiction</SelectItem>
                  <SelectItem value="biography">Biography</SelectItem>
                </SelectContent>
              </Select>
            </FormField>

            <FormField 
              label="Target Audience" 
              id="audience"
            >
              <Select value={formData.targetAudience} onValueChange={(value) => setFormData({...formData, targetAudience: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target audience" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="children">Children (0-12)</SelectItem>
                  <SelectItem value="teens">Teenagers (13-17)</SelectItem>
                  <SelectItem value="young-adults">Young Adults (18-25)</SelectItem>
                  <SelectItem value="adults">Adults (26-64)</SelectItem>
                  <SelectItem value="seniors">Seniors (65+)</SelectItem>
                </SelectContent>
              </Select>
            </FormField>

            <FormField 
              label="Description" 
              required 
              id="description"
              error={errors.description}
              helperText="Provide a brief description of your book"
            >
              <Textarea 
                placeholder="Describe your book..."
                maxLength={500}
                showCharCount
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </FormField>

            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" className="flex-1">
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Create Book
              </Button>
            </div>
          </form>
        </div>
      </div>
    );
};

export const CompleteForm: StoryObj = {
  render: () => <CompleteFormComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Complete form example showing all components working together with validation.',
      },
    },
  },
};