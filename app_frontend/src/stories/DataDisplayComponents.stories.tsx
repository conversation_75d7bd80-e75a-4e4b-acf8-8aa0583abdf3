import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ToastProvider, ToastViewport, ToastWithIcon } from '@/components/ui/toast';
import { Progress, Spinner, CircularProgress } from '@/components/ui/progress';
import { Skeleton, SkeletonText, SkeletonCard, SkeletonList } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { CheckCircle, Star, Clock, AlertTriangle, BookOpen, TrendingUp } from 'lucide-react';

const meta: Meta = {
  title: 'UI/Data Display & Feedback Components',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Comprehensive data display and feedback components built with Airbnb-inspired design tokens. Includes Cards, Badges, Toast notifications, Progress indicators, and Skeleton loaders.',
      },
    },
  },
};

export default meta;

export const CardVariants: StoryObj = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl">
      {/* Default Card */}
      <Card>
        <CardHeader>
          <CardTitle>Default Card</CardTitle>
          <CardDescription>A standard card with basic styling</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">Card content goes here.</p>
        </CardContent>
      </Card>

      {/* Elevated Card */}
      <Card variant="elevated" hoverable>
        <CardHeader>
          <CardTitle>Elevated Card</CardTitle>
          <CardDescription>Higher shadow with hover effect</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-500" />
            <span className="text-sm">Interactive card</span>
          </div>
        </CardContent>
      </Card>

      {/* Success Card */}
      <Card variant="success">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Success Card
          </CardTitle>
          <CardDescription>Used for positive states</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm">Book published successfully!</p>
        </CardContent>
      </Card>

      {/* Warning Card */}
      <Card variant="warning">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            Warning Card
          </CardTitle>
          <CardDescription>Attention required</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm">Review needed before publishing</p>
        </CardContent>
      </Card>

      {/* Large Card with Footer */}
      <Card size="lg" className="md:col-span-2">
        <CardHeader>
          <CardTitle>Book Analytics Dashboard</CardTitle>
          <CardDescription>Overview of your publishing performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">12</div>
              <div className="text-sm text-muted-foreground">Books Published</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">$2,456</div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">89%</div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full">
            View Detailed Report
          </Button>
        </CardFooter>
      </Card>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Card component with different variants, sizes, and interactive states.',
      },
    },
  },
};

export const BadgeShowcase: StoryObj = {
  render: () => (
    <div className="space-y-8 max-w-4xl">
      {/* Status Badges */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Status Badges</h3>
        <div className="flex flex-wrap gap-3">
          <Badge variant="default">Draft</Badge>
          <Badge variant="primary">In Progress</Badge>
          <Badge variant="success" icon={<CheckCircle className="h-3 w-3" />}>Published</Badge>
          <Badge variant="warning" icon={<Clock className="h-3 w-3" />}>Pending Review</Badge>
          <Badge variant="error">Failed</Badge>
          <Badge variant="info">New</Badge>
          <Badge variant="secondary">Archive</Badge>
        </div>
      </div>

      {/* Dot Indicators */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Dot Indicators</h3>
        <div className="flex flex-wrap gap-3">
          <Badge variant="success" dot>Online</Badge>
          <Badge variant="error" dot>Offline</Badge>
          <Badge variant="warning" dot>Maintenance</Badge>
          <Badge variant="info" dot>Beta</Badge>
        </div>
      </div>

      {/* Different Sizes */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Sizes</h3>
        <div className="flex items-center gap-3">
          <Badge size="sm" variant="primary">Small</Badge>
          <Badge size="default" variant="primary">Default</Badge>
          <Badge size="lg" variant="primary">Large</Badge>
        </div>
      </div>

      {/* Interactive Badges */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Interactive Badges</h3>
        <div className="flex flex-wrap gap-3">
          <Badge variant="outline" onClick={() => alert('Badge clicked!')}>
            Clickable
          </Badge>
          <Badge variant="secondary">
            Category Tag
          </Badge>
          <Badge 
            variant="primary" 
            icon={<BookOpen className="h-3 w-3" />}
          >
            Fiction
          </Badge>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Badge component with status colors, icons, dots, and interactive features.',
      },
    },
  },
};

// Progress component with state management
const ProgressComponent = () => {
  const [progress, setProgress] = useState(65);
  
  return (
    <div className="space-y-8 max-w-2xl">
      {/* Linear Progress */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Linear Progress</h3>
        <div className="space-y-6">
          <Progress value={progress} showPercentage label="Book Generation Progress" />
          <Progress value={progress} variant="success" showPercentage labelPosition="inline" />
          <Progress value={85} variant="warning" size="lg" showPercentage />
          <Progress value={30} variant="error" size="sm" label="Upload Progress" />
        </div>
        
        <div className="flex gap-2 mt-4">
          <Button onClick={() => setProgress(Math.max(0, progress - 10))} size="sm">
            Decrease
          </Button>
          <Button onClick={() => setProgress(Math.min(100, progress + 10))} size="sm">
            Increase
          </Button>
        </div>
      </div>

      {/* Circular Progress */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Circular Progress</h3>
        <div className="flex gap-6 items-center">
          <CircularProgress value={progress} showPercentage />
          <CircularProgress value={75} variant="success" size={80} showPercentage />
          <CircularProgress value={40} variant="warning" size={60} strokeWidth={6}>
            <TrendingUp className="h-4 w-4" />
          </CircularProgress>
        </div>
      </div>

      {/* Spinners */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading Spinners</h3>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Spinner size="sm" />
            <Spinner size="default" />
            <Spinner size="lg" />
            <Spinner size="xl" />
          </div>
          <div className="space-y-3">
            <Spinner text="Generating book content..." />
            <Spinner text="Publishing to KDP..." variant="success" />
            <Spinner text="Processing..." textPosition="bottom" variant="info" />
          </div>
        </div>
      </div>
    </div>
  );
};

export const ProgressIndicators: StoryObj = {
  render: () => <ProgressComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Progress bars, circular progress, and loading spinners with different variants and sizes.',
      },
    },
  },
};

export const SkeletonLoaders: StoryObj = {
  render: () => (
    <div className="space-y-8 max-w-4xl">
      {/* Basic Skeletons */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Basic Skeletons</h3>
        <div className="space-y-3">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-12 w-12 rounded-full" />
          <Skeleton variant="shimmer" className="h-20 w-full rounded-lg" />
        </div>
      </div>

      {/* Text Skeletons */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Text Skeletons</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <SkeletonText lines={4} />
          <SkeletonText lines={3} variant="shimmer" lineHeights={["h-6", "h-4", "h-4"]} />
        </div>
      </div>

      {/* Card Skeletons */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Card Skeletons</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <SkeletonCard />
          <SkeletonCard showAvatar textLines={2} imageAspect="square" />
          <SkeletonCard showImage={false} showAvatar textLines={4} />
        </div>
      </div>

      {/* List Skeletons */}
      <div>
        <h3 className="text-lg font-semibold mb-4">List Skeletons</h3>
        <SkeletonList items={5} />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Skeleton loading states for different UI patterns and content types.',
      },
    },
  },
};

// Toast demo component
const ToastDemo = () => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    variant: "default" | "success" | "error" | "warning" | "info";
    title: string;
    description?: string;
  }>>([]);

  const addToast = (variant: "default" | "success" | "error" | "warning" | "info", title: string, description?: string) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { id, variant, title, description }]);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, 3000);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <ToastProvider>
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Toast Notifications</h3>
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={() => addToast("success", "Success!", "Book published successfully")}
            variant="outline"
            size="sm"
          >
            Success Toast
          </Button>
          <Button 
            onClick={() => addToast("error", "Error occurred", "Failed to publish book")}
            variant="outline"
            size="sm"
          >
            Error Toast
          </Button>
          <Button 
            onClick={() => addToast("warning", "Warning", "Review required before publishing")}
            variant="outline"
            size="sm"
          >
            Warning Toast
          </Button>
          <Button 
            onClick={() => addToast("info", "Info", "New feature available")}
            variant="outline"
            size="sm"
          >
            Info Toast
          </Button>
        </div>
        
        {/* Render toasts */}
        <div className="fixed bottom-4 right-4 z-50 space-y-2">
          {toasts.map(toast => (
            <ToastWithIcon
              key={toast.id}
              variant={toast.variant}
              title={toast.title}
              description={toast.description}
              onClose={() => removeToast(toast.id)}
            />
          ))}
        </div>
      </div>
      <ToastViewport />
    </ToastProvider>
  );
};

export const ToastNotifications: StoryObj = {
  render: () => <ToastDemo />,
  parameters: {
    docs: {
      description: {
        story: 'Toast notification system with different variants and auto-dismiss functionality.',
      },
    },
  },
};