import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, ArrowR<PERSON>, Heart } from "lucide-react"

const meta = {
  title: "UI/Button",
  component: Button,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: 'A versatile button component built with Airbnb-inspired design tokens. Supports multiple variants, sizes, loading states, and icons.',
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["default", "secondary", "outline", "ghost", "link", "destructive", "success"],
      description: "Visual style variant of the button",
    },
    size: {
      control: "select",
      options: ["sm", "default", "lg", "xl", "icon", "icon-sm", "icon-lg"],
      description: "Size of the button",
    },
    loading: {
      control: "boolean",
      description: "Show loading spinner and disable button",
    },
    disabled: {
      control: "boolean",
      description: "Disable the button",
    },
  },
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: "Create Book",
  },
}

export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-3">
        <Button variant="default">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="link">Link</Button>
        <Button variant="destructive">Delete</Button>
        <Button variant="success">Success</Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available button variants following the Airbnb-inspired design system.',
      },
    },
  },
}

export const AllSizes: Story = {
  render: () => (
    <div className="flex flex-wrap items-center gap-3">
      <Button size="sm">Small</Button>
      <Button size="default">Default</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different button sizes to fit various UI contexts.',
      },
    },
  },
}

export const WithIcons: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-3">
        <Button leftIcon={<Plus className="h-4 w-4" />}>
          Add Book
        </Button>
        <Button rightIcon={<ArrowRight className="h-4 w-4" />} variant="outline">
          Continue
        </Button>
        <Button leftIcon={<Heart className="h-4 w-4" />} variant="ghost">
          Favorite
        </Button>
      </div>
      <div className="flex flex-wrap gap-3">
        <Button size="icon" variant="outline">
          <Plus className="h-4 w-4" />
        </Button>
        <Button size="icon-sm" variant="ghost">
          <Heart className="h-3 w-3" />
        </Button>
        <Button size="icon-lg">
          <ArrowRight className="h-5 w-5" />
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons with left icons, right icons, and icon-only variants.',
      },
    },
  },
}

export const LoadingStates: Story = {
  render: () => (
    <div className="flex flex-wrap gap-3">
      <Button loading>Creating...</Button>
      <Button loading loadingText="Saving..." variant="secondary">
        Save Changes
      </Button>
      <Button loading variant="outline">
        Generate Content
      </Button>
      <Button loading variant="destructive">
        Deleting...
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Loading states with spinner animation. Shows custom loading text when provided.',
      },
    },
  },
}

export const DisabledStates: Story = {
  render: () => (
    <div className="flex flex-wrap gap-3">
      <Button disabled>Disabled Primary</Button>
      <Button disabled variant="secondary">Disabled Secondary</Button>
      <Button disabled variant="outline">Disabled Outline</Button>
      <Button disabled variant="ghost">Disabled Ghost</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Disabled button states across all variants.',
      },
    },
  },
}

export const RealWorldExamples: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">Book Creation Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Button leftIcon={<Plus className="h-4 w-4" />} size="lg">
            Create New Book
          </Button>
          <Button variant="outline" size="lg">
            Import Manuscript
          </Button>
          <Button variant="ghost" size="lg">
            View Templates
          </Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-3">Publishing Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="success" rightIcon={<ArrowRight className="h-4 w-4" />}>
            Publish to KDP
          </Button>
          <Button variant="secondary">Save Draft</Button>
          <Button variant="outline">Preview</Button>
          <Button variant="destructive">Delete Book</Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-3">Form Actions</h3>
        <div className="flex justify-between items-center p-4 bg-surface-100 rounded-lg">
          <div className="flex gap-3">
            <Button variant="ghost">Cancel</Button>
            <Button variant="outline">Save as Draft</Button>
          </div>
          <Button loading loadingText="Publishing...">
            Publish Book
          </Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Real-world usage examples showing how buttons work together in common UI patterns.',
      },
    },
  },
}

// Individual stories for testing
export const Primary: Story = {
  args: {
    children: "Primary Button",
    variant: "default",
  },
}

export const Secondary: Story = {
  args: {
    children: "Secondary Button", 
    variant: "secondary",
  },
}

export const WithLeftIcon: Story = {
  args: {
    children: "Add Book",
    leftIcon: <Plus className="h-4 w-4" />,
  },
}

export const Loading: Story = {
  args: {
    children: "Creating Book...",
    loading: true,
  },
}