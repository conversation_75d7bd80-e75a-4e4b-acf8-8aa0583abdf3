import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { StatsCard } from '@/components/ui/stats-card';
import { ActivityTimeline, type TimelineItem } from '@/components/ui/activity-timeline';
import { QuickActions, type QuickActionItem } from '@/components/ui/quick-actions';
import { BookCard, type BookData } from '@/components/ui/book-card';
import { ChartWrapper, ChartPlaceholder } from '@/components/ui/chart-wrapper';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen, 
  TrendingUp, 
  DollarSign, 
  Users, 
  BarChart3, 
  Plus, 
  FileText, 
  Settings, 
  Download,
  RefreshCw,
  Filter
} from 'lucide-react';

const meta: Meta = {
  title: 'UI/Dashboard Components',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Comprehensive dashboard components for displaying statistics, activities, quick actions, and book management. Built with Airbnb-inspired design tokens.',
      },
    },
  },
};

export default meta;

export const StatsCards: StoryObj = {
  render: () => (
    <div className="space-y-8">
      {/* Basic Stats Cards */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Basic Stats Cards</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            value="24"
            label="Total Books"
            icon={<BookOpen className="h-5 w-5" />}
            trend={{
              direction: "up",
              value: 12,
              label: "vs last month"
            }}
          />
          <StatsCard
            value="$3,240"
            label="Total Revenue"
            icon={<DollarSign className="h-5 w-5" />}
            trend={{
              direction: "up",
              value: 8.5,
              label: "vs last month"
            }}
            variant="success"
          />
          <StatsCard
            value="1,429"
            label="Total Sales"
            icon={<TrendingUp className="h-5 w-5" />}
            trend={{
              direction: "down",
              value: 3.2,
              label: "vs last month"
            }}
          />
          <StatsCard
            value="94%"
            label="Success Rate"
            description="Books successfully published"
            icon={<BarChart3 className="h-5 w-5" />}
            trend={{
              direction: "neutral",
              value: 0,
              label: "no change"
            }}
            variant="info"
          />
        </div>
      </div>

      {/* Different Sizes */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Different Sizes</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatsCard
            size="sm"
            value="156"
            label="Downloads"
            icon={<Download className="h-4 w-4" />}
          />
          <StatsCard
            size="default"
            value="89"
            label="Reviews"
            icon={<Users className="h-5 w-5" />}
            trend={{
              direction: "up",
              value: 15
            }}
          />
          <StatsCard
            size="lg"
            value="4.8"
            label="Avg Rating"
            description="Based on 89 reviews"
            icon={<TrendingUp className="h-6 w-6" />}
            variant="success"
          />
        </div>
      </div>

      {/* Loading States */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading States</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatsCard
            value="Loading..."
            label="Sales Today"
            loading={true}
          />
          <StatsCard
            value="Loading..."
            label="Revenue"
            loading={true}
          />
          <StatsCard
            value="Loading..."
            label="Active Books"
            loading={true}
          />
        </div>
      </div>

      {/* Clickable Cards */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Interactive Cards</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <StatsCard
            value="View Details"
            label="Sales Analytics"
            icon={<BarChart3 className="h-5 w-5" />}
            onClick={() => alert('Navigate to sales analytics')}
            variant="info"
          />
          <StatsCard
            value="Manage"
            label="Book Library"
            icon={<BookOpen className="h-5 w-5" />}
            onClick={() => alert('Navigate to book library')}
            variant="default"
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Stats cards for displaying key metrics with trend indicators, different sizes, and interactive states.',
      },
    },
  },
};

export const Timeline: StoryObj = {
  render: () => {
    const timelineItems: TimelineItem[] = [
      {
        id: "1",
        title: "Book 'AI Revolution' published successfully",
        description: "Your book has been published to Amazon KDP and is now live.",
        timestamp: "2 hours ago",
        status: "completed",
        metadata: {
          "Book ID": "BK-001",
          "Platform": "Amazon KDP"
        }
      },
      {
        id: "2", 
        title: "Cover design generated",
        description: "AI has created a new cover design for your upcoming book.",
        timestamp: "4 hours ago",
        status: "completed",
        metadata: {
          "Design": "Modern Tech"
        }
      },
      {
        id: "3",
        title: "Book content generation in progress",
        description: "AI is currently generating chapters 5-8 for your science fiction novel.",
        timestamp: "6 hours ago",
        status: "in-progress"
      },
      {
        id: "4",
        title: "Publishing failed - Review required",
        description: "Book 'Future Tech' failed KDP review. Content needs revision.",
        timestamp: "1 day ago",
        status: "failed",
        metadata: {
          "Error": "Content policy",
          "Action": "Revision needed"
        }
      },
      {
        id: "5",
        title: "New trend analysis completed",
        description: "Market analysis shows high demand for AI and technology books.",
        timestamp: "2 days ago",
        status: "info"
      }
    ];

    return (
      <div className="space-y-8">
        {/* Default Timeline */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Activity Timeline</h3>
          <div className="max-w-2xl">
            <ActivityTimeline items={timelineItems} />
          </div>
        </div>

        {/* Compact Timeline */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Compact Timeline</h3>
          <div className="max-w-2xl">
            <ActivityTimeline items={timelineItems.slice(0, 3)} compact showLines={false} />
          </div>
        </div>

        {/* Loading Timeline */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Loading State</h3>
          <div className="max-w-2xl">
            <ActivityTimeline items={[]} loading skeletonCount={3} />
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Activity timeline component for showing chronological events with different status types and metadata.',
      },
    },
  },
};

export const QuickActionsComponent: StoryObj = {
  render: () => {
    const quickActions: QuickActionItem[] = [
      {
        id: "1",
        title: "Generate New Book",
        description: "Create a new book with AI assistance",
        icon: <Plus className="h-5 w-5" />,
        variant: "primary",
        onClick: () => alert('Navigate to book creation')
      },
      {
        id: "2", 
        title: "View Analytics",
        description: "Check your sales and performance data",
        icon: <BarChart3 className="h-5 w-5" />,
        onClick: () => alert('Navigate to analytics')
      },
      {
        id: "3",
        title: "Manage Library",
        description: "Browse and edit your book collection",
        icon: <BookOpen className="h-5 w-5" />,
        badge: "24",
        onClick: () => alert('Navigate to library')
      },
      {
        id: "4",
        title: "Account Settings",
        description: "Update your profile and preferences",
        icon: <Settings className="h-5 w-5" />,
        onClick: () => alert('Navigate to settings')
      },
      {
        id: "5",
        title: "Draft Documents",
        description: "Access your work in progress",
        icon: <FileText className="h-5 w-5" />,
        badge: "3",
        variant: "secondary"
      },
      {
        id: "6",
        title: "Processing...",
        description: "Book generation in progress",
        icon: <RefreshCw className="h-5 w-5" />,
        loading: true,
        disabled: true
      }
    ];

    return (
      <div className="space-y-8">
        {/* Default Grid */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Quick Actions Grid</h3>
          <QuickActions items={quickActions} />
        </div>

        {/* Different Columns */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Two Column Layout</h3>
          <QuickActions items={quickActions.slice(0, 4)} columns={2} />
        </div>

        {/* Vertical Orientation */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Vertical Orientation</h3>
          <QuickActions 
            items={quickActions.slice(0, 4)} 
            orientation="vertical" 
            columns={4}
            itemSize="lg"
          />
        </div>

        {/* Loading State */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Loading State</h3>
          <QuickActions items={[]} loading skeletonCount={4} columns={2} />
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Quick actions component for common dashboard tasks with badges, loading states, and different layouts.',
      },
    },
  },
};

// Book cards component with state
const BookCardsComponent = () => {
  const sampleBooks: BookData[] = [
      {
        id: "1",
        title: "The Future of AI",
        description: "A comprehensive guide to artificial intelligence and its impact on society.",
        author: "AI Author",
        status: "published",
        genre: "Technology",
        coverUrl: "https://via.placeholder.com/200x300/FF385C/FFFFFF?text=AI+Book",
        publishedDate: "2024-01-15",
        lastModified: "2 days ago",
        salesCount: 127,
        revenue: 423,
        rating: 4.5,
        pages: 234
      },
      {
        id: "2",
        title: "Machine Learning Basics",
        description: "An introduction to machine learning concepts and applications.",
        author: "Tech Writer",
        status: "in-review",
        genre: "Education",
        lastModified: "5 hours ago",
        pages: 189
      },
      {
        id: "3",
        title: "Digital Transformation",
        author: "Business Expert",
        status: "draft",
        genre: "Business",
        lastModified: "1 day ago",
        pages: 156
      },
      {
        id: "4",
        title: "Python Programming Guide",
        description: "Learn Python programming from scratch with practical examples.",
        author: "Code Master",
        status: "failed",
        genre: "Programming",
        coverUrl: "https://via.placeholder.com/200x300/3B82F6/FFFFFF?text=Python",
        lastModified: "3 days ago",
        pages: 278
      }
    ];

    const [selectedBooks, setSelectedBooks] = useState<string[]>([]);

    const handleBookSelect = (book: BookData, selected: boolean) => {
      if (selected) {
        setSelectedBooks(prev => [...prev, book.id]);
      } else {
        setSelectedBooks(prev => prev.filter(id => id !== book.id));
      }
    };

    return (
      <div className="space-y-8">
        {/* Vertical Layout */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Book Cards - Vertical Layout</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sampleBooks.map((book) => (
              <BookCard
                key={book.id}
                book={book}
                onView={(book) => alert(`View ${book.title}`)}
                onEdit={(book) => alert(`Edit ${book.title}`)}
                onDelete={(book) => alert(`Delete ${book.title}`)}
              />
            ))}
          </div>
        </div>

        {/* Horizontal Layout */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Book Cards - Horizontal Layout</h3>
          <div className="space-y-4 max-w-2xl">
            {sampleBooks.slice(0, 3).map((book) => (
              <BookCard
                key={book.id}
                book={book}
                layout="horizontal"
                variant="compact"
                selected={selectedBooks.includes(book.id)}
                onSelect={handleBookSelect}
              />
            ))}
          </div>
        </div>

        {/* Featured Cards */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Featured Books</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <BookCard
              book={sampleBooks[0]}
              variant="featured"
              size="lg"
              onView={(book) => alert(`View ${book.title}`)}
            />
            <BookCard
              book={sampleBooks[1]}
              variant="featured"
              size="lg"
            />
          </div>
        </div>

        {/* Loading States */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Loading States</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <BookCard
                key={index}
                book={{} as BookData}
                loading
              />
            ))}
          </div>
        </div>
      </div>
    );
};

export const BookCards: StoryObj = {
  render: () => <BookCardsComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Book cards for displaying book information with different layouts, variants, and interactive features.',
      },
    },
  },
};

export const ChartComponents: StoryObj = {
  render: () => {
    const sampleData = Array.from({ length: 12 }, (_, i) => ({
      month: `Month ${i + 1}`,
      sales: Math.floor(Math.random() * 100) + 20,
      revenue: Math.floor(Math.random() * 1000) + 200
    }));

    return (
      <div className="space-y-8">
        {/* Chart with Data */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Chart Wrappers</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartWrapper
              title="Sales Analytics"
              description="Monthly sales performance over the last year"
              actions={
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              }
              footer={
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Last updated: 5 minutes ago</span>
                  <span>Total: 1,247 sales</span>
                </div>
              }
            >
              <ChartPlaceholder type="line" data={sampleData} />
            </ChartWrapper>

            <ChartWrapper
              title="Revenue Breakdown"
              description="Revenue by book category"
              variant="elevated"
            >
              <ChartPlaceholder type="pie" data={sampleData} />
            </ChartWrapper>
          </div>
        </div>

        {/* Different Chart Types */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Chart Types</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <ChartWrapper title="Line Chart" size="sm" height="sm">
              <ChartPlaceholder type="line" />
            </ChartWrapper>
            <ChartWrapper title="Bar Chart" size="sm" height="sm">
              <ChartPlaceholder type="bar" />
            </ChartWrapper>
            <ChartWrapper title="Area Chart" size="sm" height="sm">
              <ChartPlaceholder type="area" />
            </ChartWrapper>
            <ChartWrapper title="Scatter Plot" size="sm" height="sm">
              <ChartPlaceholder type="scatter" />
            </ChartWrapper>
          </div>
        </div>

        {/* Chart States */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Chart States</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <ChartWrapper
              title="Loading Chart"
              loading={true}
              height="sm"
            >
              <ChartPlaceholder type="line" />
            </ChartWrapper>

            <ChartWrapper
              title="Error State"
              error="Failed to load data"
              height="sm"
            >
              <ChartPlaceholder type="bar" />
            </ChartWrapper>

            <ChartWrapper
              title="Empty State"
              empty={true}
              emptyMessage="No sales data available"
              height="sm"
            >
              <ChartPlaceholder type="pie" />
            </ChartWrapper>
          </div>
        </div>

        {/* Large Chart */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Large Dashboard Chart</h3>
          <ChartWrapper
            title="Comprehensive Analytics Dashboard"
            description="Complete overview of your publishing performance"
            height="xl"
            variant="outlined"
            actions={
              <div className="flex gap-2">
                <Badge variant="success">Live Data</Badge>
                <Button variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            }
            footer={
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <span className="font-medium text-green-600">↗ 12%</span>
                  <span className="block text-muted-foreground">vs last month</span>
                </div>
                <div className="text-center">
                  <span className="font-medium">$3,247</span>
                  <span className="block text-muted-foreground">Total revenue</span>
                </div>
                <div className="text-center">
                  <span className="font-medium">1,429</span>
                  <span className="block text-muted-foreground">Total sales</span>
                </div>
              </div>
            }
          >
            <ChartPlaceholder type="area" data={sampleData} />
          </ChartWrapper>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart wrapper components for displaying analytics data with different states, sizes, and chart types.',
      },
    },
  },
};