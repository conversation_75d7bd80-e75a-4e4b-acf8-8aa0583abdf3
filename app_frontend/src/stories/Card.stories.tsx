import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const meta = {
  title: "UI/Card",
  component: Card,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Card>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>Book Title</CardTitle>
        <CardDescription>A brief description of the book</CardDescription>
      </CardHeader>
      <CardContent>
        <p>This is the main content area of the card. It can contain any content you need.</p>
      </CardContent>
      <CardFooter>
        <Button>View Details</Button>
      </CardFooter>
    </Card>
  ),
}

export const BookCard: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>The AI Revolution</CardTitle>
            <CardDescription>Technology & Innovation</CardDescription>
          </div>
          <Badge variant="default">Published</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            An in-depth exploration of how artificial intelligence is transforming every aspect of our lives.
          </p>
          <div className="flex gap-2">
            <Badge variant="outline">Non-fiction</Badge>
            <Badge variant="outline">Technology</Badge>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          <span>234 pages</span> • <span>12 chapters</span>
        </div>
        <Button size="sm">Edit</Button>
      </CardFooter>
    </Card>
  ),
}

export const StatsCard: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          Total Revenue
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">$45,231.89</div>
        <p className="text-xs text-muted-foreground">
          +20.1% from last month
        </p>
      </CardContent>
    </Card>
  ),
}