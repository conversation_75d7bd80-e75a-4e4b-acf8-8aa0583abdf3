"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import {
  useUserPreferences,
  useUpdateUserPreferences,
} from "@/hooks/api/use-settings";

type Theme = "light" | "dark" | "system";

// Default theme configuration
const DEFAULT_THEMES = {
  UNAUTHENTICATED: "system" as Theme, // Default for guests
  AUTHENTICATED: "system" as Theme, // Default for new users
  FALLBACK: "system" as Theme, // Fallback when everything fails
} as const;

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: "light" | "dark"; // The actual applied theme (resolves 'system')
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

// Inner provider that handles authenticated state
function AuthenticatedThemeProvider({
  children,
  theme,
  setTheme,
  actualTheme,
  storageKey,
}: {
  children: React.ReactNode;
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: "light" | "dark";
  storageKey: string;
}) {
  const { data: session } = useSession();
  const { data: preferences } = useUserPreferences();
  const updatePreferences = useUpdateUserPreferences();

  const userPrefsKey = `${storageKey}-user-${session?.user?.email || "unknown"}`;

  // Load user preferences from localStorage on mount
  useEffect(() => {
    if (session?.user?.email) {
      const savedUserPrefs = localStorage.getItem(userPrefsKey);
      if (savedUserPrefs) {
        try {
          const parsedPrefs = JSON.parse(savedUserPrefs);
          if (parsedPrefs.theme && parsedPrefs.theme !== theme) {
            setTheme(parsedPrefs.theme);
          }
        } catch (error) {
          console.error("Failed to parse saved user preferences:", error);
          // If parsing fails, set default theme
          setTheme("system");
        }
      } else {
        // If no saved preferences exist, set default theme for new user
        console.log(
          "No saved user preferences found, setting default theme:",
          DEFAULT_THEMES.AUTHENTICATED
        );
        setTheme(DEFAULT_THEMES.AUTHENTICATED);
      }
    }
  }, [session?.user?.email, userPrefsKey, theme, setTheme]);

  // Sync with API preferences when they load (API takes precedence)
  useEffect(() => {
    if (preferences?.theme && preferences.theme !== theme) {
      setTheme(preferences.theme);
      // Save to localStorage for future visits
      if (session?.user?.email) {
        localStorage.setItem(
          userPrefsKey,
          JSON.stringify({ theme: preferences.theme })
        );
      }
    }
  }, [preferences, theme, setTheme, session?.user?.email, userPrefsKey]);

  // Handle API preference loading errors
  const { error: preferencesError } = useUserPreferences();
  useEffect(() => {
    if (preferencesError && session?.user?.email) {
      console.warn("Failed to load user preferences from API, using fallback");
      // If API fails and no localStorage backup exists, use system default
      const savedUserPrefs = localStorage.getItem(userPrefsKey);
      if (!savedUserPrefs && theme === "system") {
        console.log("Setting fallback theme due to API error");
        localStorage.setItem(userPrefsKey, JSON.stringify({ theme: "system" }));
      }
    }
  }, [preferencesError, session?.user?.email, userPrefsKey, theme]);

  // Update API and localStorage when theme changes
  const handleSetTheme = (newTheme: Theme) => {
    setTheme(newTheme);

    // Convert 'system' theme to actual theme for API (API doesn't support 'system')
    const apiTheme: "light" | "dark" =
      newTheme === "system"
        ? window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light"
        : newTheme;

    updatePreferences.mutate({ theme: apiTheme });

    // Save to user-specific localStorage (keep original theme including 'system')
    if (session?.user?.email) {
      localStorage.setItem(userPrefsKey, JSON.stringify({ theme: newTheme }));
    }
  };

  const contextValue: ThemeContextType = {
    theme,
    setTheme: handleSetTheme,
    actualTheme,
    isLoading: false,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Main provider that works for both authenticated and unauthenticated states
export function ThemeProvider({
  children,
  defaultTheme = DEFAULT_THEMES.UNAUTHENTICATED,
  storageKey = "ui-theme",
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [actualTheme, setActualTheme] = useState<"light" | "dark">("light");
  const [isInitialized, setIsInitialized] = useState(false);

  // Check authentication status
  const { data: session, status } = useSession();
  const [previousSession, setPreviousSession] = useState<typeof session | null>(
    null
  );

  // Initialize theme from localStorage
  useEffect(() => {
    const storedTheme = localStorage.getItem(storageKey) as Theme;
    if (storedTheme && ["light", "dark", "system"].includes(storedTheme)) {
      setThemeState(storedTheme);
    } else {
      // If no valid stored theme, use default and save it
      console.log("No valid stored theme found, using default:", defaultTheme);
      setThemeState(defaultTheme);
      localStorage.setItem(storageKey, defaultTheme);
    }
    setIsInitialized(true);
  }, [storageKey, defaultTheme]);

  // Handle logout cleanup
  useEffect(() => {
    // If user was logged in but now isn't, clean up their preferences
    if (previousSession?.user?.email && !session?.user?.email) {
      const userPrefsKey = `${storageKey}-user-${previousSession.user.email}`;
      localStorage.removeItem(userPrefsKey);
      console.log("Cleaned up user preferences on logout");
    }
    setPreviousSession(session);
  }, [session, previousSession, storageKey]);

  // Update actual theme based on theme setting and system preference
  useEffect(() => {
    const updateActualTheme = () => {
      if (theme === "system") {
        const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
          .matches
          ? "dark"
          : "light";
        setActualTheme(systemTheme);
      } else {
        setActualTheme(theme);
      }
    };

    updateActualTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQuery.addEventListener("change", updateActualTheme);

    return () => mediaQuery.removeEventListener("change", updateActualTheme);
  }, [theme]);

  // Apply theme to document (only if different from current)
  useEffect(() => {
    const root = window.document.documentElement;
    const currentThemeClass = root.classList.contains("dark")
      ? "dark"
      : "light";

    // Only update if the theme has actually changed
    if (currentThemeClass !== actualTheme) {
      // Remove previous theme classes
      root.classList.remove("light", "dark");

      // Add current theme class
      root.classList.add(actualTheme);

      // Update CSS custom properties for theme
      if (actualTheme === "dark") {
        root.style.setProperty("--background", "222.2% 84% 4.9%");
        root.style.setProperty("--foreground", "210% 40% 98%");
        root.style.setProperty("--card", "222.2% 84% 4.9%");
        root.style.setProperty("--card-foreground", "210% 40% 98%");
        root.style.setProperty("--primary", "210% 40% 98%");
        root.style.setProperty("--primary-foreground", "222.2% 84% 4.9%");
        root.style.setProperty("--secondary", "217.2% 32.6% 17.5%");
        root.style.setProperty("--secondary-foreground", "210% 40% 98%");
        root.style.setProperty("--muted", "217.2% 32.6% 17.5%");
        root.style.setProperty("--muted-foreground", "215% 20.2% 65.1%");
        root.style.setProperty("--accent", "217.2% 32.6% 17.5%");
        root.style.setProperty("--accent-foreground", "210% 40% 98%");
        root.style.setProperty("--border", "217.2% 32.6% 17.5%");
        root.style.setProperty("--input", "217.2% 32.6% 17.5%");
        root.style.setProperty("--ring", "212.7% 26.8% 83.9%");
      } else {
        root.style.setProperty("--background", "0 0% 100%");
        root.style.setProperty("--foreground", "222.2% 84% 4.9%");
        root.style.setProperty("--card", "0 0% 100%");
        root.style.setProperty("--card-foreground", "222.2% 84% 4.9%");
        root.style.setProperty("--primary", "222.2% 47.4% 11.2%");
        root.style.setProperty("--primary-foreground", "210% 40% 98%");
        root.style.setProperty("--secondary", "210% 40% 96%");
        root.style.setProperty("--secondary-foreground", "222.2% 84% 4.9%");
        root.style.setProperty("--muted", "210% 40% 96%");
        root.style.setProperty("--muted-foreground", "215.4% 16.3% 46.9%");
        root.style.setProperty("--accent", "210% 40% 96%");
        root.style.setProperty("--accent-foreground", "222.2% 84% 4.9%");
        root.style.setProperty("--border", "214.3% 31.8% 91.4%");
        root.style.setProperty("--input", "214.3% 31.8% 91.4%");
        root.style.setProperty("--ring", "222.2% 84% 4.9%");
      }
    }
  }, [actualTheme]);

  // Theme setter for unauthenticated users
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem(storageKey, newTheme);
  };

  // For unauthenticated users or loading states, provide simple theme context
  if (status !== "authenticated") {
    const contextValue: ThemeContextType = {
      theme,
      setTheme,
      actualTheme,
      isLoading: status === "loading" || !isInitialized,
    };

    return (
      <ThemeContext.Provider value={contextValue}>
        {children}
      </ThemeContext.Provider>
    );
  }

  // For authenticated users, use the enhanced provider with API sync
  return (
    <AuthenticatedThemeProvider
      theme={theme}
      setTheme={setTheme}
      actualTheme={actualTheme}
      storageKey={storageKey}
    >
      {children}
    </AuthenticatedThemeProvider>
  );
}

// Theme toggle component
export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <div className="flex items-center space-x-2">
      <label htmlFor="theme-toggle" className="text-sm font-medium">
        Theme
      </label>
      <select
        id="theme-toggle"
        value={theme}
        onChange={(e) => setTheme(e.target.value as Theme)}
        className="px-3 py-1 border rounded-md bg-background text-foreground"
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="system">System</option>
      </select>
    </div>
  );
}
