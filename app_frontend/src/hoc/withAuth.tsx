"use client"

import React from "react"
import { AuthGuard } from "@/components/auth/AuthGuard"
import { LoadingGuard } from "@/components/auth/LoadingGuard"

interface WithAuthOptions {
  redirectTo?: string
  fallback?: React.ReactNode
  requireAuth?: boolean
  loadingComponent?: React.ReactNode
  displayName?: string
}

/**
 * Higher-Order Component that wraps a component with authentication protection
 * 
 * @param WrappedComponent - The component to protect with authentication
 * @param options - Configuration options for authentication behavior
 * @returns Protected component that requires authentication
 */
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const {
    redirectTo = "/auth/login",
    fallback,
    requireAuth = true,
    loadingComponent,
    displayName,
  } = options

  const WithAuthComponent = React.forwardRef<any, P>((props, ref) => {
    return (
      <AuthGuard
        redirectTo={redirectTo}
        fallback={fallback}
        requireAuth={requireAuth}
        loadingComponent={loadingComponent}
      >
        <WrappedComponent {...props} ref={ref} />
      </AuthGuard>
    )
  })

  // Set display name for better debugging
  const componentName = displayName || WrappedComponent.displayName || WrappedComponent.name || "Component"
  WithAuthComponent.displayName = `withAuth(${componentName})`

  // Copy static properties
  return hoistNonReactStatics(WithAuthComponent, WrappedComponent)
}

/**
 * Convenience HOC that always requires authentication
 */
export function requireAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<WithAuthOptions, "requireAuth"> = {}
) {
  return withAuth(WrappedComponent, { ...options, requireAuth: true })
}

/**
 * Convenience HOC that allows optional authentication (works for both auth and non-auth users)
 */
export function optionalAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<WithAuthOptions, "requireAuth"> = {}
) {
  return withAuth(WrappedComponent, { ...options, requireAuth: false })
}

/**
 * HOC that shows a loading state while authentication is being checked
 */
export function withAuthLoading<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthOptions & { loadingVariant?: "spinner" | "skeleton" | "minimal" } = {}
) {
  const { loadingVariant = "spinner", ...authOptions } = options
  
  const loadingComponent = <LoadingGuard variant={loadingVariant} />
  
  return withAuth(WrappedComponent, {
    ...authOptions,
    loadingComponent,
  })
}

/**
 * Utility function to hoist non-React static properties from one component to another
 * This ensures that static methods and properties are preserved when wrapping components
 */
function hoistNonReactStatics<T extends React.ComponentType<any>, S extends React.ComponentType<any>>(
  targetComponent: T,
  sourceComponent: S
): T {
  const REACT_STATICS = {
    childContextTypes: true,
    contextType: true,
    contextTypes: true,
    defaultProps: true,
    displayName: true,
    getDefaultProps: true,
    getDerivedStateFromError: true,
    getDerivedStateFromProps: true,
    mixins: true,
    propTypes: true,
    type: true,
  }

  const KNOWN_STATICS = {
    name: true,
    length: true,
    prototype: true,
    caller: true,
    callee: true,
    arguments: true,
    arity: true,
  }

  const FORWARD_REF_STATICS = {
    $$typeof: true,
    render: true,
    defaultProps: true,
    displayName: true,
    propTypes: true,
  }

  const getStatics = (component: any) => {
    // React.forwardRef case
    if (component.$$typeof === Symbol.for('react.forward_ref')) {
      return FORWARD_REF_STATICS
    }
    return REACT_STATICS
  }

  const isValidKey = (key: string) => {
    return !(key in KNOWN_STATICS) && !(key in getStatics(sourceComponent))
  }

  // Copy static properties
  Object.getOwnPropertyNames(sourceComponent).forEach((key) => {
    if (isValidKey(key)) {
      try {
        const descriptor = Object.getOwnPropertyDescriptor(sourceComponent, key)
        if (descriptor) {
          Object.defineProperty(targetComponent, key, descriptor)
        }
      } catch (e) {
        // Ignore failures
      }
    }
  })

  return targetComponent
}

// Type helpers for better TypeScript support
export type WithAuthProps<P = {}> = P & {
  // Add any additional props that the HOC might inject
}

export type AuthenticatedComponent<P = {}> = React.ComponentType<WithAuthProps<P>>

/**
 * Utility type to extract the original component props from a wrapped component
 */
export type UnwrapAuthComponent<T> = T extends React.ComponentType<infer P> ? P : never

/**
 * Creates a typed version of withAuth for better TypeScript inference
 */
export function createTypedWithAuth<TBaseProps = {}>() {
  return function typedWithAuth<P extends TBaseProps>(
    WrappedComponent: React.ComponentType<P>,
    options: WithAuthOptions = {}
  ): React.ComponentType<P> {
    return withAuth(WrappedComponent, options)
  }
}

/**
 * Compose multiple HOCs together
 * Example: compose(withAuth, withLayout, withTheme)(MyComponent)
 */
export function compose<T>(...funcs: Array<(component: any) => any>) {
  return (component: React.ComponentType<T>) => {
    return funcs.reduceRight((acc, func) => func(acc), component)
  }
}

/**
 * Creates a conditional auth HOC based on a condition
 */
export function conditionalAuth<P extends object>(
  condition: boolean | ((props: P) => boolean),
  authOptions: WithAuthOptions = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const ConditionalAuthComponent = React.forwardRef<any, P>((props, ref) => {
      const shouldRequireAuth = typeof condition === "function" ? condition(props) : condition

      if (shouldRequireAuth) {
        return (
          <AuthGuard {...authOptions}>
            <WrappedComponent {...props} ref={ref} />
          </AuthGuard>
        )
      }

      return <WrappedComponent {...props} ref={ref} />
    })

    ConditionalAuthComponent.displayName = `conditionalAuth(${WrappedComponent.displayName || WrappedComponent.name})`
    return hoistNonReactStatics(ConditionalAuthComponent, WrappedComponent)
  }
}