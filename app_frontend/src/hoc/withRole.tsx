"use client"

import React from "react"
import { RoleGuard, UserRole, Permission } from "@/components/auth/RoleGuard"
// Import the hoistNonReactStatics function (simplified version)
function hoistNonReactStatics<T extends React.ComponentType<any>, S extends React.ComponentType<any>>(
  targetComponent: T,
  sourceComponent: S
): T {
  // Copy static properties (simplified implementation)
  Object.getOwnPropertyNames(sourceComponent).forEach((key) => {
    if (key !== 'prototype' && key !== 'name' && key !== 'length') {
      try {
        const descriptor = Object.getOwnPropertyDescriptor(sourceComponent, key)
        if (descriptor) {
          Object.defineProperty(targetComponent, key, descriptor)
        }
      } catch (e) {
        // Ignore failures
      }
    }
  })
  return targetComponent
}

interface WithRoleOptions {
  requiredRole?: UserRole | UserRole[]
  requiredPermissions?: Permission | Permission[]
  allowedRoles?: UserRole[]
  deniedRoles?: UserRole[]
  customAccessCheck?: (user: any) => boolean
  redirectTo?: string
  fallback?: React.ReactNode
  showAccessDenied?: boolean
  accessDeniedMessage?: string
  displayName?: string
}

/**
 * Higher-Order Component that wraps a component with role-based access control
 * 
 * @param WrappedComponent - The component to protect with role-based access
 * @param options - Configuration options for role-based access control
 * @returns Protected component that requires specific roles/permissions
 */
export function withRole<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithRoleOptions = {}
) {
  const {
    requiredRole,
    requiredPermissions,
    allowedRoles,
    deniedRoles,
    customAccessCheck,
    redirectTo,
    fallback,
    showAccessDenied = true,
    accessDeniedMessage,
    displayName,
  } = options

  const WithRoleComponent = React.forwardRef<any, P>((props, ref) => {
    return (
      <RoleGuard
        requiredRole={requiredRole}
        requiredPermissions={requiredPermissions}
        allowedRoles={allowedRoles}
        deniedRoles={deniedRoles}
        customAccessCheck={customAccessCheck}
        redirectTo={redirectTo}
        fallback={fallback}
        showAccessDenied={showAccessDenied}
        accessDeniedMessage={accessDeniedMessage}
      >
        <WrappedComponent {...props} ref={ref} />
      </RoleGuard>
    )
  })

  // Set display name for better debugging
  const componentName = displayName || WrappedComponent.displayName || WrappedComponent.name || "Component"
  WithRoleComponent.displayName = `withRole(${componentName})`

  // Copy static properties
  return hoistNonReactStatics(WithRoleComponent, WrappedComponent)
}

/**
 * Convenience HOC for admin-only components
 */
export function requireAdmin<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<WithRoleOptions, "requiredRole"> = {}
) {
  return withRole(WrappedComponent, { ...options, requiredRole: "admin" })
}

/**
 * Convenience HOC for editor or admin components
 */
export function requireEditorOrAdmin<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<WithRoleOptions, "allowedRoles"> = {}
) {
  return withRole(WrappedComponent, { ...options, allowedRoles: ["editor", "admin"] })
}

/**
 * Convenience HOC for author and above (author, editor, admin)
 */
export function requireAuthorOrAbove<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<WithRoleOptions, "requiredRole"> = {}
) {
  return withRole(WrappedComponent, { ...options, requiredRole: "author" })
}

/**
 * Convenience HOC for specific permissions
 */
export function requirePermissions<P extends object>(
  permissions: Permission | Permission[],
  options: Omit<WithRoleOptions, "requiredPermissions"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    return withRole(WrappedComponent, { ...options, requiredPermissions: permissions })
  }
}

/**
 * HOC that denies access to specific roles
 */
export function denyRoles<P extends object>(
  roles: UserRole | UserRole[],
  options: Omit<WithRoleOptions, "deniedRoles"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const deniedRoles = Array.isArray(roles) ? roles : [roles]
    return withRole(WrappedComponent, { ...options, deniedRoles })
  }
}

/**
 * HOC that allows only specific roles
 */
export function allowOnlyRoles<P extends object>(
  roles: UserRole | UserRole[],
  options: Omit<WithRoleOptions, "allowedRoles"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const allowedRoles = Array.isArray(roles) ? roles : [roles]
    return withRole(WrappedComponent, { ...options, allowedRoles })
  }
}

/**
 * Advanced HOC that combines multiple role checks with AND logic
 */
export function requireAllRoles<P extends object>(
  roles: UserRole[],
  options: Omit<WithRoleOptions, "customAccessCheck"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const customAccessCheck = (user: any) => {
      const userRole = user?.role as UserRole
      if (!userRole) return false
      
      // For simplicity, we'll check if user has the highest required role
      // In a real app, you might want more complex logic for multiple roles
      const roleHierarchy: Record<UserRole, number> = {
        guest: 0,
        user: 1,
        author: 2,
        editor: 3,
        admin: 4,
      }
      
      const userLevel = roleHierarchy[userRole] || 0
      const requiredLevel = Math.max(...roles.map(role => roleHierarchy[role] || 0))
      
      return userLevel >= requiredLevel
    }
    
    return withRole(WrappedComponent, { ...options, customAccessCheck })
  }
}

/**
 * Advanced HOC that combines multiple role checks with OR logic
 */
export function requireAnyRole<P extends object>(
  roles: UserRole[],
  options: Omit<WithRoleOptions, "allowedRoles"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    return withRole(WrappedComponent, { ...options, allowedRoles: roles })
  }
}

/**
 * HOC that checks custom business logic for access control
 */
export function requireCustomAccess<P extends object>(
  accessCheck: (user: any, props: P) => boolean,
  options: Omit<WithRoleOptions, "customAccessCheck"> = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const WithCustomAccessComponent = React.forwardRef<any, P>((props, ref) => {
      const customAccessCheck = (user: any) => accessCheck(user, props)
      
      return (
        <RoleGuard
          {...options}
          customAccessCheck={customAccessCheck}
        >
          <WrappedComponent {...props} ref={ref} />
        </RoleGuard>
      )
    })
    
    WithCustomAccessComponent.displayName = `requireCustomAccess(${WrappedComponent.displayName || WrappedComponent.name})`
    return hoistNonReactStatics(WithCustomAccessComponent, WrappedComponent)
  }
}

/**
 * Conditional role HOC based on runtime conditions
 */
export function conditionalRole<P extends object>(
  condition: boolean | ((props: P) => boolean),
  roleOptions: WithRoleOptions = {}
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    const ConditionalRoleComponent = React.forwardRef<any, P>((props, ref) => {
      const shouldCheckRole = typeof condition === "function" ? condition(props) : condition

      if (shouldCheckRole) {
        return (
          <RoleGuard {...roleOptions}>
            <WrappedComponent {...props} ref={ref} />
          </RoleGuard>
        )
      }

      return <WrappedComponent {...props} ref={ref} />
    })

    ConditionalRoleComponent.displayName = `conditionalRole(${WrappedComponent.displayName || WrappedComponent.name})`
    return hoistNonReactStatics(ConditionalRoleComponent, WrappedComponent)
  }
}

/**
 * Combines authentication and role checking in a single HOC
 */
export function withAuthAndRole<P extends object>(
  roleOptions: WithRoleOptions = {},
  authRedirectTo = "/auth/login"
) {
  return (WrappedComponent: React.ComponentType<P>) => {
    return withRole(WrappedComponent, { ...roleOptions, redirectTo: authRedirectTo })
  }
}

// Type helpers for better TypeScript support
export type WithRoleProps<P = {}> = P & {
  // Add any additional props that the HOC might inject
}

export type RoleProtectedComponent<P = {}> = React.ComponentType<WithRoleProps<P>>

/**
 * Utility to create role-specific HOCs
 */
export function createRoleHOC(defaultOptions: WithRoleOptions) {
  return function<P extends object>(
    WrappedComponent: React.ComponentType<P>,
    overrideOptions: WithRoleOptions = {}
  ) {
    return withRole(WrappedComponent, { ...defaultOptions, ...overrideOptions })
  }
}

// Pre-configured HOCs for common patterns
export const withAdminAccess = createRoleHOC({ requiredRole: "admin" })
export const withEditorAccess = createRoleHOC({ requiredRole: "editor" })
export const withAuthorAccess = createRoleHOC({ requiredRole: "author" })
export const withContentManagement = createRoleHOC({ requiredPermissions: "manage_content" })
export const withUserManagement = createRoleHOC({ requiredPermissions: "manage_users" })