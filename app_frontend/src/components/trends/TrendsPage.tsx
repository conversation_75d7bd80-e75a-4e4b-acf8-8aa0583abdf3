'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON>rends<PERSON>eader } from './TrendsHeader'
import { TrendsFilters } from './TrendsFilters'
import { TrendsMetrics } from './TrendsMetrics'
import { TrendsCharts } from './TrendsCharts'
import { TrendsHeatmap } from './TrendsHeatmap'
import { TrendsForecasts } from './TrendsForecasts'
import { TrendsActions } from './TrendsActions'
import { useTrendsData } from '@/hooks/api/use-trends'
import { TrendsFilters as TrendsFiltersType, ChartEvent } from '@/types/trends'

const defaultFilters: TrendsFiltersType = {
  assetTypes: ['residential', 'commercial'],
  regions: ['north-america', 'europe'],
  timePeriod: '30D',
}

export const TrendsPage: React.FC = () => {
  const [filters, setFilters] = useState<TrendsFiltersType>(defaultFilters)
  const [selectedTimeRange, setSelectedTimeRange] = useState<[Date, Date]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    new Date(),
  ])

  const {
    data: trendsData,
    isLoading,
    error,
    refetch,
  } = useTrendsData(filters)

  const handleFiltersChange = useCallback((newFilters: Partial<TrendsFiltersType>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [])

  const handleChartInteraction = useCallback((event: ChartEvent) => {
    console.log('Chart interaction:', event)
    // Handle chart interactions (zoom, hover, etc.)
  }, [])

  const handleExport = useCallback((format: 'csv' | 'json' | 'pdf') => {
    console.log('Export requested:', format)
    // Export functionality will be implemented
  }, [])

  const handleRefresh = useCallback(() => {
    refetch()
  }, [refetch])

  const handleConfigureAlerts = useCallback(() => {
    console.log('Configure alerts requested')
    // Alert configuration will be implemented
  }, [])

  const handleRegionClick = useCallback((region: string) => {
    console.log('Region clicked:', region)
    // Handle region selection
  }, [])

  const handleMetricChange = useCallback((metric: string) => {
    console.log('Metric changed:', metric)
    // Handle heatmap metric change
  }, [])

  const handleCommentaryRefresh = useCallback(() => {
    console.log('Commentary refresh requested')
    // Refresh AI commentary
  }, [])

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Unable to load trends data
          </h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <TrendsHeader
          lastUpdated={trendsData?.commentary?.lastUpdated || new Date()}
          isLoading={isLoading}
          onRefresh={handleRefresh}
          onExport={handleExport}
          onConfigureAlerts={handleConfigureAlerts}
        />

        {/* Filters */}
        <div className="mb-6">
          <TrendsFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            availableAssetTypes={[
              { id: 'residential', name: 'Residential', category: 'property' },
              { id: 'commercial', name: 'Commercial', category: 'property' },
              { id: 'land', name: 'Land', category: 'property' },
            ]}
            availableRegions={[
              { id: 'north-america', name: 'North America', code: 'NA', continent: 'Americas' },
              { id: 'europe', name: 'Europe', code: 'EU', continent: 'Europe' },
              { id: 'asia', name: 'Asia', code: 'AS', continent: 'Asia' },
            ]}
            presets={[
              { id: 'hot-markets', name: 'Hot Markets', filters: { assetTypes: ['residential'], timePeriod: '7D' } },
              { id: 'new-listings', name: 'New Listings', filters: { timePeriod: '1D' } },
            ]}
          />
        </div>

        {/* Metrics */}
        <div className="mb-6">
          <TrendsMetrics
            metrics={trendsData?.metrics || {
              totalVolume: 0,
              avgPrice: 0,
              avgHoldingDuration: 0,
              volumeChange: 0,
              priceChange: 0,
              durationChange: 0,
            }}
            loading={isLoading}
            timeframe={filters.timePeriod}
          />
        </div>

        {/* Charts */}
        <div className="mb-6">
          <TrendsCharts
            volumeData={trendsData?.chartData || []}
            priceData={trendsData?.chartData || []}
            yieldData={[]}
            onChartInteraction={handleChartInteraction}
            selectedTimeRange={selectedTimeRange}
          />
        </div>

        {/* Heatmap */}
        <div className="mb-6">
          <TrendsHeatmap
            data={trendsData?.heatmapData || []}
            metric="volume"
            onRegionClick={handleRegionClick}
            onMetricChange={handleMetricChange}
            colorScale={{
              min: '#f3f4f6',
              mid: '#ff385c',
              max: '#991b1b',
            }}
          />
        </div>

        {/* Forecasts */}
        <div className="mb-6">
          <TrendsForecasts
            forecasts={trendsData?.forecasts || {
              priceForecasts: [],
              yieldForecasts: [],
              sentimentIndex: { value: 0, trend: 'stable', confidence: 0, factors: [] },
            }}
            commentary={trendsData?.commentary || {
              content: '',
              sources: [],
              confidence: 0,
              lastUpdated: new Date(),
              validatedSchema: false,
            }}
            confidenceLevel={75}
            onCommentaryRefresh={handleCommentaryRefresh}
          />
        </div>

        {/* Actions */}
        <TrendsActions
          onExport={handleExport}
          onSetAlert={handleConfigureAlerts}
          onShare={() => console.log('Share requested')}
          onConfigureAPI={() => console.log('API config requested')}
        />
      </div>
    </div>
  )
}