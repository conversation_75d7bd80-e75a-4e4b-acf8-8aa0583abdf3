'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter } from 'lucide-react'
import { TrendsFiltersProps, TimePeriod } from '@/types/trends'

const TIME_PERIODS: { value: TimePeriod; label: string }[] = [
  { value: '1D', label: 'Last 24 hours' },
  { value: '7D', label: 'Last 7 days' },
  { value: '30D', label: 'Last 30 days' },
  { value: '3M', label: 'Last 3 months' },
  { value: '1Y', label: 'Last year' },
  { value: 'custom', label: 'Custom range' },
]

export const TrendsFilters: React.FC<TrendsFiltersProps> = ({
  filters,
  onFiltersChange,
  availableAssetTypes,
  availableRegions,
  presets,
}) => {
  const handleAssetTypeToggle = (assetTypeId: string) => {
    const newAssetTypes = filters.assetTypes.includes(assetTypeId)
      ? filters.assetTypes.filter(id => id !== assetTypeId)
      : [...filters.assetTypes, assetTypeId]
    
    onFiltersChange({ assetTypes: newAssetTypes })
  }

  const handleRegionToggle = (regionId: string) => {
    const newRegions = filters.regions.includes(regionId)
      ? filters.regions.filter(id => id !== regionId)
      : [...filters.regions, regionId]
    
    onFiltersChange({ regions: newRegions })
  }

  const handleTimePeriodChange = (timePeriod: TimePeriod) => {
    onFiltersChange({ timePeriod })
  }

  const handlePresetApply = (presetFilters: Partial<typeof filters>) => {
    onFiltersChange(presetFilters)
  }

  const clearFilters = () => {
    onFiltersChange({
      assetTypes: [],
      regions: [],
      timePeriod: '30D',
      customDateRange: undefined,
    })
  }

  const hasActiveFilters = filters.assetTypes.length > 0 || filters.regions.length > 0

  return (
    <div className="bg-white rounded-lg border p-4">
      <div className="flex flex-col space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">Filters</h3>
          </div>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-xs"
            >
              Clear all
            </Button>
          )}
        </div>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Asset Types */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">
              Asset Type
            </label>
            <div className="flex flex-wrap gap-1">
              {availableAssetTypes.map((assetType) => (
                <Badge
                  key={assetType.id}
                  variant={filters.assetTypes.includes(assetType.id) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => handleAssetTypeToggle(assetType.id)}
                >
                  {assetType.name}
                  {filters.assetTypes.includes(assetType.id) && (
                    <X className="h-3 w-3 ml-1" />
                  )}
                </Badge>
              ))}
            </div>
          </div>

          {/* Regions */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">
              Region
            </label>
            <div className="flex flex-wrap gap-1">
              {availableRegions.map((region) => (
                <Badge
                  key={region.id}
                  variant={filters.regions.includes(region.id) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => handleRegionToggle(region.id)}
                >
                  {region.name}
                  {filters.regions.includes(region.id) && (
                    <X className="h-3 w-3 ml-1" />
                  )}
                </Badge>
              ))}
            </div>
          </div>

          {/* Time Period */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">
              Time Period
            </label>
            <Select
              value={filters.timePeriod}
              onValueChange={(value: TimePeriod) => handleTimePeriodChange(value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TIME_PERIODS.map((period) => (
                  <SelectItem key={period.value} value={period.value}>
                    {period.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Presets */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">
              Quick Filters
            </label>
            <div className="flex flex-wrap gap-1">
              {presets.map((preset) => (
                <Button
                  key={preset.id}
                  variant="outline"
                  size="sm"
                  className="text-xs h-7"
                  onClick={() => handlePresetApply(preset.filters)}
                >
                  {preset.name}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2 pt-2 border-t">
            <span className="text-xs text-gray-600">Active filters:</span>
            <div className="flex flex-wrap gap-1">
              {filters.assetTypes.map((assetTypeId) => {
                const assetType = availableAssetTypes.find(at => at.id === assetTypeId)
                return assetType ? (
                  <Badge key={assetTypeId} variant="secondary" className="text-xs">
                    {assetType.name}
                  </Badge>
                ) : null
              })}
              {filters.regions.map((regionId) => {
                const region = availableRegions.find(r => r.id === regionId)
                return region ? (
                  <Badge key={regionId} variant="secondary" className="text-xs">
                    {region.name}
                  </Badge>
                ) : null
              })}
              <Badge variant="secondary" className="text-xs">
                {TIME_PERIODS.find(p => p.value === filters.timePeriod)?.label}
              </Badge>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}