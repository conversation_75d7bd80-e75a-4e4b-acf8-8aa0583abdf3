'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { TrendsHeatmapProps } from '@/types/trends'

export const TrendsHeatmap: React.FC<TrendsHeatmapProps> = ({
  data,
  metric,
  onRegionClick,
  onMetricChange,
  colorScale,
}) => {
  const metrics = [
    { value: 'volume', label: 'Volume' },
    { value: 'price', label: 'Price' },
    { value: 'sentiment', label: 'Sentiment' },
  ]

  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Geographic Heatmap</h2>
          <p className="text-sm text-gray-600">
            Regional market activity and performance data
          </p>
        </div>
        
        <div className="flex gap-2">
          {metrics.map((m) => (
            <Button
              key={m.value}
              variant={metric === m.value ? "default" : "outline"}
              size="sm"
              onClick={() => onMetricChange(m.value)}
            >
              {m.label}
            </Button>
          ))}
        </div>
      </div>
      
      <div className="h-96 bg-gray-100 rounded flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 mb-2">Interactive World Map - Implementation coming soon</p>
          <p className="text-xs text-gray-400">
            Will show {metric} data with color-coded regions
          </p>
        </div>
      </div>
      
      {/* Legend */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-600">Low</span>
          <div className="flex space-x-1">
            <div 
              className="w-4 h-4 rounded" 
              style={{ backgroundColor: colorScale.min }}
            ></div>
            <div 
              className="w-4 h-4 rounded" 
              style={{ backgroundColor: colorScale.mid }}
            ></div>
            <div 
              className="w-4 h-4 rounded" 
              style={{ backgroundColor: colorScale.max }}
            ></div>
          </div>
          <span className="text-xs text-gray-600">High</span>
        </div>
        
        <div className="text-xs text-gray-500">
          {data.length} regions with data
        </div>
      </div>
    </div>
  )
}