'use client'

import React from 'react'
import { RefreshCw, TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TrendsForecastsProps } from '@/types/trends'

const SentimentGauge: React.FC<{ value: number; trend: 'up' | 'down' | 'stable' }> = ({ value, trend }) => {
  const normalizedValue = (value + 100) / 200 // Convert -100,100 to 0,1
  const rotation = normalizedValue * 180 - 90 // Convert to degrees
  
  const getSentimentColor = (val: number) => {
    if (val > 20) return '#22c55e' // Green
    if (val < -20) return '#ef4444' // Red
    return '#f59e0b' // Yellow
  }

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus

  return (
    <div className="relative w-48 h-32 mx-auto">
      <svg viewBox="0 0 200 120" className="w-full h-full">
        {/* Background arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke="#e5e7eb"
          strokeWidth="8"
          strokeLinecap="round"
        />
        {/* Colored progress arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke={getSentimentColor(value)}
          strokeWidth="8"
          strokeLinecap="round"
          strokeDasharray={`${normalizedValue * 251.2} 251.2`}
        />
        {/* Needle */}
        <line
          x1="100"
          y1="100"
          x2={100 + 70 * Math.cos((rotation * Math.PI) / 180)}
          y2={100 + 70 * Math.sin((rotation * Math.PI) / 180)}
          stroke="#374151"
          strokeWidth="2"
        />
        <circle cx="100" cy="100" r="4" fill="#374151" />
      </svg>
      
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center">
        <div className="text-2xl font-bold">{value > 0 ? '+' : ''}{value}</div>
        <div className="text-sm text-gray-600 flex items-center justify-center">
          <TrendIcon className="h-3 w-3 mr-1" />
          <span>
            {value > 20 ? 'Bullish' : value < -20 ? 'Bearish' : 'Neutral'}
          </span>
        </div>
      </div>
    </div>
  )
}

export const TrendsForecasts: React.FC<TrendsForecastsProps> = ({
  forecasts,
  commentary,
  confidenceLevel,
  onCommentaryRefresh,
}) => {
  return (
    <div>
      <div className="mb-4">
        <h2 className="text-lg font-semibold text-gray-900">AI Forecasts</h2>
        <p className="text-sm text-gray-600">
          Machine learning predictions and market sentiment analysis
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Price Forecast */}
        <div className="bg-white p-6 rounded-lg border text-center">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Price Forecast</h3>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-green-600">+15%</div>
            <div className="text-sm text-gray-600">±3% confidence</div>
            <div className="text-xs text-gray-500">(Next 3M)</div>
          </div>
        </div>

        {/* Yield Forecast */}
        <div className="bg-white p-6 rounded-lg border text-center">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Yield Forecast</h3>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-blue-600">6.2%</div>
            <div className="text-sm text-gray-600">±0.5% confidence</div>
            <div className="text-xs text-gray-500">(12M Average)</div>
          </div>
        </div>

        {/* Sentiment Gauge */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-900 mb-4 text-center">Sentiment Gauge</h3>
          <SentimentGauge 
            value={forecasts.sentimentIndex.value} 
            trend={forecasts.sentimentIndex.trend}
          />
        </div>
      </div>

      {/* AI Commentary */}
      <div className="bg-white p-6 rounded-lg border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-gray-900">AI Commentary</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={onCommentaryRefresh}
            className="flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
        
        <div className="space-y-4">
          <div className="prose prose-sm text-gray-700">
            {commentary.content || (
              <p className="italic text-gray-500">
                Market momentum strengthening in urban areas. Rising institutional interest 
                driving volume increases across major metropolitan regions. Short-term outlook 
                remains positive with continued growth expected through Q2.
              </p>
            )}
          </div>
          
          <div className="flex items-center justify-between text-xs text-gray-500 pt-4 border-t">
            <div className="flex items-center space-x-4">
              <span>Confidence: {commentary.confidence || confidenceLevel}%</span>
              <span>Sources: {commentary.sources?.length || 3}</span>
              <span className={`px-2 py-1 rounded ${
                commentary.validatedSchema ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'
              }`}>
                {commentary.validatedSchema ? 'Validated' : 'Pending Validation'}
              </span>
            </div>
            <span>
              Last updated: {commentary.lastUpdated?.toLocaleTimeString() || 'Never'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}