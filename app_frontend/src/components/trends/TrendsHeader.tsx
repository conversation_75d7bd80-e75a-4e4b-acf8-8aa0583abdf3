'use client'

import React from 'react'
import { RefreshCw, Download, Bell, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TrendsHeaderProps } from '@/types/trends'
import { formatDistanceToNow } from 'date-fns'

export const TrendsHeader: React.FC<TrendsHeaderProps> = ({
  lastUpdated,
  isLoading,
  onRefresh,
  onExport,
  onConfigureAlerts,
}) => {
  return (
    <div className="bg-white rounded-lg border p-6 mb-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        {/* Title and Last Updated */}
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Bridges Market Trends
          </h1>
          <p className="text-sm text-gray-600">
            Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center"
          >
            <RefreshCw 
              className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} 
            />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('csv')}
            className="flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onConfigureAlerts}
            className="flex items-center"
          >
            <Bell className="h-4 w-4 mr-2" />
            Alerts
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="flex items-center"
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>
    </div>
  )
}