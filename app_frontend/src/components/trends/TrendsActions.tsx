'use client'

import React from 'react'
import { Download, Bell, Share2, Code } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TrendsActionsProps } from '@/types/trends'

export const TrendsActions: React.FC<TrendsActionsProps> = ({
  onExport,
  onSetAlert,
  onShare,
  onConfigureAPI,
}) => {
  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="flex flex-col sm:flex-row items-center justify-between">
        <div className="mb-4 sm:mb-0">
          <h3 className="text-sm font-medium text-gray-900 mb-1">Actions</h3>
          <p className="text-xs text-gray-600">
            Export data, configure alerts, and manage integrations
          </p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onSetAlert}
            className="flex items-center"
          >
            <Bell className="h-4 w-4 mr-2" />
            Set Alert
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('csv')}
            className="flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('json')}
            className="flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('pdf')}
            className="flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            PDF Report
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onShare}
            className="flex items-center"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onConfigureAPI}
            className="flex items-center"
          >
            <Code className="h-4 w-4 mr-2" />
            API
          </Button>
        </div>
      </div>
    </div>
  )
}