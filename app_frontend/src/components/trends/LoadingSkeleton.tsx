'use client'

import React from 'react'

interface LoadingSkeletonProps {
  type: 'page' | 'metrics' | 'chart' | 'heatmap'
}

const MetricsSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    {[1, 2, 3].map(i => (
      <div key={i} className="bg-white p-6 rounded-lg border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    ))}
  </div>
)

const ChartSkeleton: React.FC = () => (
  <div className="bg-white p-6 rounded-lg border">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  </div>
)

const HeatmapSkeleton: React.FC = () => (
  <div className="bg-white p-6 rounded-lg border">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="h-96 bg-gray-200 rounded"></div>
    </div>
  </div>
)

const PageSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gray-50">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header Skeleton */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <div className="animate-pulse">
          <div className="flex justify-between items-start">
            <div>
              <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-32"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Skeleton */}
      <div className="bg-white rounded-lg border p-4 mb-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-16 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i}>
                <div className="h-3 bg-gray-200 rounded w-20 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Metrics Skeleton */}
      <div className="mb-6">
        <div className="mb-4">
          <div className="animate-pulse">
            <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-48"></div>
          </div>
        </div>
        <MetricsSkeleton />
      </div>

      {/* Charts Skeleton */}
      <div className="mb-6">
        <div className="mb-4">
          <div className="animate-pulse">
            <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <ChartSkeleton />
          <ChartSkeleton />
          <div className="xl:col-span-2">
            <ChartSkeleton />
          </div>
        </div>
      </div>

      {/* Heatmap Skeleton */}
      <div className="mb-6">
        <HeatmapSkeleton />
      </div>

      {/* Forecasts Skeleton */}
      <div className="mb-6">
        <div className="mb-4">
          <div className="animate-pulse">
            <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {[1, 2, 3].map(i => (
            <div key={i} className="bg-white p-6 rounded-lg border">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-20 mb-4 mx-auto"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2 mx-auto"></div>
                <div className="h-3 bg-gray-200 rounded w-24 mx-auto"></div>
              </div>
            </div>
          ))}
        </div>
        <div className="bg-white p-6 rounded-lg border">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-28 mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions Skeleton */}
      <div className="bg-white p-6 rounded-lg border">
        <div className="animate-pulse">
          <div className="flex justify-between items-center">
            <div>
              <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-48"></div>
            </div>
            <div className="flex space-x-2">
              {[1, 2, 3, 4, 5, 6].map(i => (
                <div key={i} className="h-8 bg-gray-200 rounded w-20"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type }) => {
  switch (type) {
    case 'metrics':
      return <MetricsSkeleton />
    case 'chart':
      return <ChartSkeleton />
    case 'heatmap':
      return <HeatmapSkeleton />
    case 'page':
    default:
      return <PageSkeleton />
  }
}