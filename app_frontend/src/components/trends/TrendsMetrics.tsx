'use client'

import React from 'react'
import { TrendingUp, TrendingDown, DollarSign, Clock, BarChart3 } from 'lucide-react'
import { TrendsMetricsProps } from '@/types/trends'

interface MetricCardProps {
  title: string
  value: string
  change: number
  icon: React.ReactNode
  loading: boolean
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  loading,
}) => {
  const isPositive = change >= 0
  const TrendIcon = isPositive ? TrendingUp : TrendingDown

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white p-6 rounded-lg border hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <div className="p-2 bg-gray-100 rounded-lg">
          {icon}
        </div>
      </div>
      
      <div className="mb-2">
        <p className="text-2xl font-bold text-gray-900">{value}</p>
      </div>
      
      <div className="flex items-center">
        <TrendIcon 
          className={`h-4 w-4 mr-1 ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`} 
        />
        <span
          className={`text-sm font-medium ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}
        >
          {isPositive ? '+' : ''}{change.toFixed(1)}%
        </span>
        <span className="text-sm text-gray-500 ml-1">vs previous period</span>
      </div>
    </div>
  )
}

const formatCurrency = (value: number): string => {
  if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(1)}K`
  }
  return `$${value.toFixed(0)}`
}

const formatDuration = (months: number): string => {
  if (months >= 12) {
    const years = months / 12
    return `${years.toFixed(1)}y`
  }
  return `${months.toFixed(0)}m`
}

export const TrendsMetrics: React.FC<TrendsMetricsProps> = ({
  metrics,
  loading,
  timeframe,
}) => {
  const metricCards = [
    {
      title: 'Trading Volume',
      value: formatCurrency(metrics.totalVolume),
      change: metrics.volumeChange,
      icon: <BarChart3 className="h-5 w-5 text-gray-600" />,
    },
    {
      title: 'Average Price',
      value: formatCurrency(metrics.avgPrice),
      change: metrics.priceChange,
      icon: <DollarSign className="h-5 w-5 text-gray-600" />,
    },
    {
      title: 'Holding Duration',
      value: formatDuration(metrics.avgHoldingDuration),
      change: metrics.durationChange,
      icon: <Clock className="h-5 w-5 text-gray-600" />,
    },
  ]

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Key Metrics</h2>
        <p className="text-sm text-gray-600">
          Market performance for the {timeframe.toLowerCase()} period
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {metricCards.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            icon={metric.icon}
            loading={loading}
          />
        ))}
      </div>
    </div>
  )
}