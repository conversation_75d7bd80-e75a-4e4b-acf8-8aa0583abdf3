'use client'

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { api } from "../utils/api";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const PerformanceDashboard = () => {
  const { data: analytics } = useQuery({
    queryKey: ["analytics-dashboard"],
    queryFn: () => api.get("/feedback/analytics/dashboard").then((res: any) => res.data)
  });

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">System Performance Analytics</h2>

      {analytics &&
        Object.entries(analytics).map(([metricType, data]: [string, any]) => (
          <div key={metricType} className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium mb-4 capitalize">
              {metricType.replace("_", " ")} Trends
            </h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data.values.map((value: any, index: number) => ({
                    value,
                    timestamp: data.timestamps[index],
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="value" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-4 flex justify-between text-sm">
              <span>Average: {data.average.toFixed(2)}</span>
              <span
                className={`capitalize ${
                  data.trend === "improving"
                    ? "text-green-600"
                    : data.trend === "declining"
                      ? "text-red-600"
                      : "text-gray-600"
                }`}
              >
                Trend: {data.trend}
              </span>
            </div>
          </div>
        ))}
    </div>
  );
};

export default PerformanceDashboard;
