"use client"

import React, { useEffect, useState, useRef } from 'react'
import { cn } from '@/lib/utils'
import { checkAccessibility, getContrastRatio, isContrastSufficient } from '@/lib/accessibility'
import { AlertTriangle, CheckCircle, Eye, EyeOff, Monitor } from 'lucide-react'

interface AccessibilityCheckerProps {
  enabled?: boolean
  targetSelector?: string
  showOverlay?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export function AccessibilityChecker({
  enabled = process.env.NODE_ENV === 'development',
  targetSelector = 'body',
  showOverlay = true,
  position = 'bottom-right',
}: AccessibilityCheckerProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [issues, setIssues] = useState<string[]>([])
  const [lastCheck, setLastCheck] = useState<Date | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!enabled) return

    const runCheck = () => {
      setIsChecking(true)
      const target = document.querySelector(targetSelector) as HTMLElement
      if (target) {
        const foundIssues = checkAccessibility(target)
        setIssues(foundIssues)
        setLastCheck(new Date())
      }
      setIsChecking(false)
    }

    // Initial check
    runCheck()

    // Set up periodic checking
    intervalRef.current = setInterval(runCheck, 5000) // Check every 5 seconds

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [enabled, targetSelector])

  if (!enabled) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  }

  return (
    <>
      {/* Floating toggle button */}
      <div className={cn('fixed z-50', positionClasses[position])}>
        <button
          onClick={() => setIsVisible(!isVisible)}
          className={cn(
            'p-3 rounded-full shadow-lg transition-colors',
            issues.length > 0
              ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
              : 'bg-success text-success-foreground hover:bg-success/90'
          )}
          title={`Accessibility Checker (${issues.length} issues)`}
          aria-label={`Accessibility Checker. ${issues.length} issues found. Click to ${isVisible ? 'hide' : 'show'} details.`}
        >
          {isChecking ? (
            <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
          ) : issues.length > 0 ? (
            <AlertTriangle className="w-5 h-5" />
          ) : (
            <CheckCircle className="w-5 h-5" />
          )}
          {issues.length > 0 && (
            <span className="absolute -top-2 -right-2 bg-background text-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center border">
              {issues.length}
            </span>
          )}
        </button>
      </div>

      {/* Overlay panel */}
      {isVisible && showOverlay && (
        <div
          className={cn(
            'fixed z-40 w-80 max-h-96 bg-background border shadow-xl rounded-lg overflow-hidden',
            position.includes('right') ? 'right-4' : 'left-4',
            position.includes('top') ? 'top-20' : 'bottom-20'
          )}
        >
          <div className="p-4 border-b bg-muted/50">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold flex items-center gap-2">
                <Monitor className="w-4 h-4" />
                Accessibility Report
              </h3>
              <button
                onClick={() => setIsVisible(false)}
                className="p-1 hover:bg-muted rounded"
                aria-label="Close accessibility report"
              >
                <EyeOff className="w-4 h-4" />
              </button>
            </div>
            {lastCheck && (
              <p className="text-xs text-muted-foreground mt-1">
                Last checked: {lastCheck.toLocaleTimeString()}
              </p>
            )}
          </div>

          <div className="p-4 max-h-64 overflow-y-auto">
            {issues.length === 0 ? (
              <div className="text-center py-4">
                <CheckCircle className="w-8 h-8 text-success mx-auto mb-2" />
                <p className="text-sm text-success font-medium">
                  No accessibility issues found!
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Your page meets basic accessibility standards.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="font-medium text-sm">
                    {issues.length} issue{issues.length !== 1 ? 's' : ''} found
                  </span>
                </div>
                <ul className="space-y-2">
                  {issues.map((issue, index) => (
                    <li
                      key={index}
                      className="text-xs bg-muted/50 p-2 rounded border-l-2 border-destructive"
                    >
                      {issue}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="p-4 border-t bg-muted/50">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>WCAG 2.1 AA compliance</span>
              <button
                onClick={() => {
                  const target = document.querySelector(targetSelector) as HTMLElement
                  if (target) {
                    const foundIssues = checkAccessibility(target)
                    setIssues(foundIssues)
                    setLastCheck(new Date())
                  }
                }}
                className="px-2 py-1 bg-primary text-primary-foreground rounded text-xs hover:bg-primary/90"
              >
                Re-check
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Color contrast checker component
interface ColorContrastCheckerProps {
  foreground: string
  background: string
  level?: 'AA' | 'AAA'
  size?: 'normal' | 'large'
  className?: string
}

export function ColorContrastChecker({
  foreground,
  background,
  level = 'AA',
  size = 'normal',
  className,
}: ColorContrastCheckerProps) {
  const ratio = getContrastRatio(foreground, background)
  const passes = isContrastSufficient(foreground, background, level, size)
  
  return (
    <div className={cn('p-4 border rounded-lg', className)}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium">Color Contrast</h4>
        <div className={cn(
          'px-2 py-1 rounded text-xs font-medium',
          passes ? 'bg-success text-success-foreground' : 'bg-destructive text-destructive-foreground'
        )}>
          {passes ? 'PASS' : 'FAIL'}
        </div>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Ratio:</span>
          <span className="font-mono">{ratio.toFixed(2)}:1</span>
        </div>
        
        <div className="flex justify-between">
          <span>Required:</span>
          <span className="font-mono">
            {level === 'AAA' 
              ? (size === 'large' ? '4.5:1' : '7:1')
              : (size === 'large' ? '3:1' : '4.5:1')
            }
          </span>
        </div>
        
        <div className="flex gap-2 mt-3">
          <div 
            className="w-8 h-8 rounded border"
            style={{ backgroundColor: background }}
            title={`Background: ${background}`}
          />
          <div 
            className="w-8 h-8 rounded border flex items-center justify-center text-xs"
            style={{ 
              backgroundColor: background, 
              color: foreground 
            }}
            title={`Foreground: ${foreground} on Background: ${background}`}
          >
            A
          </div>
        </div>
      </div>
    </div>
  )
}

// Focus indicator for testing
export function FocusIndicator() {
  const [focusedElement, setFocusedElement] = useState<string | null>(null)

  useEffect(() => {
    const handleFocus = (e: FocusEvent) => {
      const target = e.target as HTMLElement
      if (target) {
        const selector = target.tagName.toLowerCase() + 
          (target.id ? `#${target.id}` : '') +
          (target.className ? `.${target.className.split(' ').join('.')}` : '')
        setFocusedElement(selector)
      }
    }

    const handleBlur = () => {
      setFocusedElement(null)
    }

    document.addEventListener('focus', handleFocus, true)
    document.addEventListener('blur', handleBlur, true)

    return () => {
      document.removeEventListener('focus', handleFocus, true)
      document.removeEventListener('blur', handleBlur, true)
    }
  }, [])

  if (!focusedElement) return null

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-primary text-primary-foreground px-3 py-1 rounded shadow-lg text-xs font-mono">
      Focus: {focusedElement}
    </div>
  )
}

export default AccessibilityChecker