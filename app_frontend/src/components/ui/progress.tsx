"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative h-2 w-full overflow-hidden rounded-full bg-secondary",
  {
    variants: {
      size: {
        sm: "h-1",
        default: "h-2",
        lg: "h-3",
        xl: "h-4",
      },
      variant: {
        default: "bg-secondary",
        success: "bg-green-100",
        warning: "bg-yellow-100", 
        error: "bg-red-100",
        info: "bg-blue-100",
      }
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 bg-primary transition-all duration-300 ease-in-out",
  {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-green-500",
        warning: "bg-yellow-500",
        error: "bg-red-500", 
        info: "bg-blue-500",
      }
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  /** Show percentage text */
  showPercentage?: boolean
  /** Custom label */
  label?: string
  /** Position of label/percentage */
  labelPosition?: "top" | "bottom" | "inline"
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, size, variant, showPercentage, label, labelPosition = "top", ...props }, ref) => {
  const percentage = value || 0

  return (
    <div className="w-full space-y-1">
      {/* Top label/percentage */}
      {(label || showPercentage) && labelPosition === "top" && (
        <div className="flex justify-between text-sm">
          {label && <span className="text-foreground font-medium">{label}</span>}
          {showPercentage && <span className="text-muted-foreground">{Math.round(percentage)}%</span>}
        </div>
      )}
      
      {/* Progress bar with inline label */}
      <div className="relative">
        <ProgressPrimitive.Root
          ref={ref}
          className={cn(progressVariants({ size, variant }), className)}
          {...props}
        >
          <ProgressPrimitive.Indicator
            className={cn(progressIndicatorVariants({ variant }))}
            style={{ transform: `translateX(-${100 - percentage}%)` }}
          />
        </ProgressPrimitive.Root>
        
        {/* Inline percentage */}
        {showPercentage && labelPosition === "inline" && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-medium text-white mix-blend-difference">
              {Math.round(percentage)}%
            </span>
          </div>
        )}
      </div>
      
      {/* Bottom label/percentage */}
      {(label || showPercentage) && labelPosition === "bottom" && (
        <div className="flex justify-between text-sm">
          {label && <span className="text-foreground font-medium">{label}</span>}
          {showPercentage && <span className="text-muted-foreground">{Math.round(percentage)}%</span>}
        </div>
      )}
    </div>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

// Spinner component
const spinnerVariants = cva(
  "animate-spin rounded-full border-2 border-current border-t-transparent",
  {
    variants: {
      size: {
        sm: "h-4 w-4",
        default: "h-6 w-6", 
        lg: "h-8 w-8",
        xl: "h-12 w-12",
      },
      variant: {
        default: "text-primary",
        secondary: "text-secondary",
        success: "text-green-500",
        warning: "text-yellow-500",
        error: "text-red-500",
        info: "text-blue-500",
        muted: "text-muted-foreground",
      }
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
)

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  /** Loading text to display */
  text?: string
  /** Position of text relative to spinner */
  textPosition?: "right" | "bottom"
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, text, textPosition = "right", ...props }, ref) => {
    if (text) {
      return (
        <div 
          ref={ref}
          className={cn(
            "flex items-center gap-3",
            textPosition === "bottom" && "flex-col gap-2",
            className
          )}
          {...props}
        >
          <div className={cn(spinnerVariants({ size, variant }))} />
          <span className="text-sm text-muted-foreground">{text}</span>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(spinnerVariants({ size, variant }), className)}
        {...props}
      />
    )
  }
)
Spinner.displayName = "Spinner"

// Circular progress component
export interface CircularProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Progress value (0-100) */
  value: number
  /** Size of the circle */
  size?: number
  /** Stroke width */
  strokeWidth?: number
  /** Color variant */
  variant?: "default" | "success" | "warning" | "error" | "info"
  /** Show percentage in center */
  showPercentage?: boolean
  /** Custom content in center */
  children?: React.ReactNode
}

const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  ({ 
    className, 
    value, 
    size = 120, 
    strokeWidth = 8, 
    variant = "default", 
    showPercentage = false,
    children,
    ...props 
  }, ref) => {
    const normalizedValue = Math.min(Math.max(value, 0), 100)
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (normalizedValue / 100) * circumference

    const getColor = () => {
      switch (variant) {
        case "success": return "#10b981"
        case "warning": return "#f59e0b" 
        case "error": return "#ef4444"
        case "info": return "#3b82f6"
        default: return "#FF385C" // primary color
      }
    }

    return (
      <div 
        ref={ref}
        className={cn("relative inline-flex items-center justify-center", className)}
        style={{ width: size, height: size }}
        {...props}
      >
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-secondary opacity-25"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={getColor()}
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-300 ease-in-out"
          />
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex items-center justify-center">
          {children || (showPercentage && (
            <span className="text-sm font-semibold text-foreground">
              {Math.round(normalizedValue)}%
            </span>
          ))}
        </div>
      </div>
    )
  }
)
CircularProgress.displayName = "CircularProgress"

export { Progress, Spinner, CircularProgress, progressVariants, spinnerVariants }
