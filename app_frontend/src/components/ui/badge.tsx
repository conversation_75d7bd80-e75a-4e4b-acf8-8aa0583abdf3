import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-surface-100 text-accent hover:bg-surface-200",
        secondary: "border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        error: "border-transparent bg-red-100 text-red-800 hover:bg-red-200",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
        primary: "border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200",
        outline: "border-border text-accent hover:bg-surface-50",
        ghost: "border-transparent hover:bg-surface-100 text-accent",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        default: "px-2.5 py-1 text-sm",
        lg: "px-3 py-1.5 text-base",
      },
      dot: {
        true: "",
        false: "",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      dot: false,
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  /** Show a colored dot indicator */
  dot?: boolean
  /** Icon to display before the text */
  icon?: React.ReactNode
  /** Make the badge clickable */
  onClick?: () => void
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {
    const isClickable = !!onClick
    
    const getDotColor = () => {
      switch (variant) {
        case 'success': return 'bg-green-500'
        case 'warning': return 'bg-yellow-500'
        case 'error': return 'bg-red-500'
        case 'info': return 'bg-blue-500'
        case 'primary': return 'bg-primary'
        case 'secondary': return 'bg-secondary'
        default: return 'bg-accent'
      }
    }

    if (isClickable) {
      return (
        <button
          ref={ref as any}
          type="button"
          className={cn(
            badgeVariants({ variant, size }),
            "cursor-pointer hover:scale-105 active:scale-95",
            className
          )}
          onClick={onClick}
          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}
        >
          {dot && (
            <span
              className={cn(
                "h-1.5 w-1.5 rounded-full",
                getDotColor()
              )}
            />
          )}
          
          {icon && (
            <span className="shrink-0">
              {icon}
            </span>
          )}
          
          {children}
        </button>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size }), className)}
        {...(props as React.HTMLAttributes<HTMLDivElement>)}
      >
        {dot && (
          <span
            className={cn(
              "h-1.5 w-1.5 rounded-full",
              getDotColor()
            )}
          />
        )}
        
        {icon && (
          <span className="shrink-0">
            {icon}
          </span>
        )}
        
        {children}
      </div>
    )
  }
)
Badge.displayName = "Badge"

export { Badge, badgeVariants }
