import { cn } from "@/lib/utils"
import { HTMLAttributes, forwardRef } from "react"

export interface GridProps extends HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8
  responsive?: boolean
}

const Grid = forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols = 12, gap = 4, responsive = true, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          {
            // Responsive grid
            "grid-cols-1": (responsive && cols === 1) || (!responsive && cols === 1),
            "grid-cols-1 sm:grid-cols-2": responsive && cols === 2,
            "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3": responsive && cols === 3,
            "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4": responsive && cols === 4,
            "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5": responsive && cols === 5,
            "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6": responsive && cols === 6,
            "grid-cols-1 sm:grid-cols-6 lg:grid-cols-12": responsive && cols === 12,
            // Non-responsive grid (cols > 1)
            "grid-cols-2": !responsive && cols === 2,
            "grid-cols-3": !responsive && cols === 3,
            "grid-cols-4": !responsive && cols === 4,
            "grid-cols-5": !responsive && cols === 5,
            "grid-cols-6": !responsive && cols === 6,
            "grid-cols-12": !responsive && cols === 12,
            // Gap
            "gap-0": gap === 0,
            "gap-1": gap === 1,
            "gap-2": gap === 2,
            "gap-3": gap === 3,
            "gap-4": gap === 4,
            "gap-5": gap === 5,
            "gap-6": gap === 6,
            "gap-8": gap === 8,
          },
          className
        )}
        {...props}
      />
    )
  }
)
Grid.displayName = "Grid"

export interface GridItemProps extends HTMLAttributes<HTMLDivElement> {
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
}

const GridItem = forwardRef<HTMLDivElement, GridItemProps>(
  ({ className, span, start, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          {
            "col-span-1": span === 1,
            "col-span-2": span === 2,
            "col-span-3": span === 3,
            "col-span-4": span === 4,
            "col-span-5": span === 5,
            "col-span-6": span === 6,
            "col-span-7": span === 7,
            "col-span-8": span === 8,
            "col-span-9": span === 9,
            "col-span-10": span === 10,
            "col-span-11": span === 11,
            "col-span-12": span === 12,
            "col-start-1": start === 1,
            "col-start-2": start === 2,
            "col-start-3": start === 3,
            "col-start-4": start === 4,
            "col-start-5": start === 5,
            "col-start-6": start === 6,
            "col-start-7": start === 7,
            "col-start-8": start === 8,
            "col-start-9": start === 9,
            "col-start-10": start === 10,
            "col-start-11": start === 11,
            "col-start-12": start === 12,
          },
          className
        )}
        {...props}
      />
    )
  }
)
GridItem.displayName = "GridItem"

export { Grid, GridItem }