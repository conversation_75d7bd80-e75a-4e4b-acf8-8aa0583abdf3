import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"
import { HTMLAttributes } from "react"

export interface ErrorMessageProps extends HTMLAttributes<HTMLDivElement> {
  title?: string
  message: string
  variant?: "default" | "destructive"
}

export function ErrorMessage({
  title,
  message,
  variant = "destructive",
  className,
  ...props
}: ErrorMessageProps) {
  return (
    <div
      className={cn(
        "flex items-start gap-3 rounded-lg border p-4",
        {
          "border-destructive/50 bg-destructive/10 text-destructive": variant === "destructive",
          "border-border bg-surface text-foreground": variant === "default",
        },
        className
      )}
      {...props}
    >
      <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
      <div className="space-y-1">
        {title && <p className="font-medium text-sm">{title}</p>}
        <p className="text-sm">{message}</p>
      </div>
    </div>
  )
}