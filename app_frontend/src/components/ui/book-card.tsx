import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { MoreHorizontal, Eye, Edit, Trash2, Calendar, DollarSign } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "./badge"
import { <PERSON><PERSON> } from "./button"

const bookCardVariants = cva(
  "group relative flex flex-col rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-border hover:shadow-md hover:border-primary/20",
        compact: "border-border hover:shadow-sm",
        featured: "border-primary/20 bg-primary/5 shadow-md hover:shadow-lg",
      },
      size: {
        sm: "p-3",
        default: "p-4",
        lg: "p-6",
      },
      layout: {
        vertical: "flex-col",
        horizontal: "flex-row",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      layout: "vertical",
    },
  }
)

export interface BookData {
  id: string;
  title: string;
  description?: string;
  coverUrl?: string;
  status:
    | "draft"
    | "published"
    | "generating"
    | "failed"
    | "in-review"
    | "archived";
  genre?: string;
  author?: string;
  publishedDate?: string;
  lastModified?: string;
  salesCount?: number;
  revenue?: number;
  rating?: number;
  pages?: number;
}

export interface BookCardProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onSelect'>,
    VariantProps<typeof bookCardVariants> {
  /** Book data */
  book: BookData
  /** Show actions menu */
  showActions?: boolean
  /** Action handlers */
  onView?: (book: BookData) => void
  onEdit?: (book: BookData) => void
  onDelete?: (book: BookData) => void
  /** Loading state */
  loading?: boolean
  /** Selected state */
  selected?: boolean
  /** Selection handler */
  onSelect?: (book: BookData, selected: boolean) => void
}

const BookCard = React.forwardRef<HTMLDivElement, BookCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    layout, 
    book, 
    showActions = true,
    onView,
    onEdit,
    onDelete,
    loading = false,
    selected = false,
    onSelect,
    ...props 
  }, ref) => {
    const getStatusVariant = (status: BookData["status"]) => {
      switch (status) {
        case "published":
          return "success"
        case "in-review":
          return "warning"
        case "failed":
          return "error"
        case "archived":
          return "secondary"
        case "draft":
        default:
          return "default"
      }
    }

    const getStatusText = (status: BookData["status"]) => {
      switch (status) {
        case "published":
          return "Published"
        case "in-review":
          return "In Review"
        case "failed":
          return "Failed"
        case "archived":
          return "Archived"
        case "draft":
        default:
          return "Draft"
      }
    }

    if (loading) {
      return (
        <div 
          ref={ref} 
          className={cn(bookCardVariants({ variant, size, layout }), className)} 
          {...props}
        >
          {layout === "vertical" ? (
            <>
              {/* Cover skeleton */}
              <div className="w-full aspect-[3/4] bg-muted animate-pulse rounded-lg mb-4" />
              
              {/* Content skeleton */}
              <div className="space-y-3 flex-1">
                <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                <div className="h-3 w-1/2 bg-muted animate-pulse rounded" />
                <div className="h-3 w-full bg-muted animate-pulse rounded" />
                <div className="h-3 w-2/3 bg-muted animate-pulse rounded" />
              </div>
            </>
          ) : (
            <div className="flex gap-4">
              {/* Cover skeleton */}
              <div className="w-20 h-28 bg-muted animate-pulse rounded-lg flex-shrink-0" />
              
              {/* Content skeleton */}
              <div className="space-y-3 flex-1">
                <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                <div className="h-3 w-1/2 bg-muted animate-pulse rounded" />
                <div className="h-3 w-full bg-muted animate-pulse rounded" />
              </div>
            </div>
          )}
        </div>
      )
    }

    return (
      <div 
        ref={ref} 
        className={cn(
          bookCardVariants({ variant, size, layout }),
          selected && "ring-2 ring-primary ring-offset-2",
          onSelect && "cursor-pointer",
          className
        )} 
        onClick={onSelect ? () => onSelect(book, !selected) : undefined}
        {...props}
      >
        {/* Selection checkbox */}
        {onSelect && (
          <div className="absolute top-2 left-2 z-10">
            <input
              type="checkbox"
              checked={selected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect(book, e.target.checked)
              }}
              className="h-4 w-4 rounded border-border text-primary focus:ring-primary"
            />
          </div>
        )}

        {/* Actions menu */}
        {showActions && (
          <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.stopPropagation()
                // Handle dropdown menu
              }}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        )}

        {layout === "vertical" ? (
          <>
            {/* Book cover */}
            <div className="relative mb-4">
              {book.coverUrl ? (
                <>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={book.coverUrl}
                    alt={`Cover of ${book.title}`}
                    className="w-full aspect-[3/4] object-cover rounded-lg"
                  />
                </>
              ) : (
                <div className="w-full aspect-[3/4] bg-surface-100 rounded-lg flex items-center justify-center">
                  <span className="text-muted-foreground text-sm">No Cover</span>
                </div>
              )}
              
              {/* Status badge */}
              <div className="absolute top-2 left-2">
                <Badge variant={getStatusVariant(book.status)} size="sm">
                  {getStatusText(book.status)}
                </Badge>
              </div>
            </div>

            {/* Book info */}
            <div className="space-y-3 flex-1">
              <div>
                <h3 className="font-semibold text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                  {book.title}
                </h3>
                {book.author && (
                  <p className="text-sm text-muted-foreground">{book.author}</p>
                )}
              </div>

              {book.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {book.description}
                </p>
              )}

              {/* Metadata */}
              <div className="space-y-2">
                {book.genre && (
                  <Badge variant="outline" size="sm">{book.genre}</Badge>
                )}
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  {book.lastModified && (
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {book.lastModified}
                    </span>
                  )}
                  {book.pages && (
                    <span>{book.pages} pages</span>
                  )}
                </div>
              </div>

              {/* Stats */}
              {book.status === "published" && (book.salesCount || book.revenue) && (
                <div className="pt-2 border-t border-border space-y-1">
                  {book.salesCount !== undefined && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Sales</span>
                      <span className="font-medium">{book.salesCount}</span>
                    </div>
                  )}
                  {book.revenue !== undefined && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Revenue</span>
                      <span className="font-medium text-green-600">${book.revenue}</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Action buttons */}
            {(onView || onEdit || onDelete) && (
              <div className="pt-4 flex gap-2">
                {onView && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation()
                      onView(book)
                    }}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                )}
                {onEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation()
                      onEdit(book)
                    }}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                )}
                {onDelete && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDelete(book)
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            )}
          </>
        ) : (
          /* Horizontal layout */
          <div className="flex gap-4">
            {/* Book cover */}
            <div className="relative flex-shrink-0">
              {book.coverUrl ? (
                <>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={book.coverUrl}
                    alt={`Cover of ${book.title}`}
                    className="w-20 h-28 object-cover rounded-lg"
                  />
                </>
              ) : (
                <div className="w-20 h-28 bg-surface-100 rounded-lg flex items-center justify-center">
                  <span className="text-muted-foreground text-xs">No Cover</span>
                </div>
              )}
            </div>

            {/* Book info */}
            <div className="flex-1 space-y-2">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                    {book.title}
                  </h3>
                  {book.author && (
                    <p className="text-sm text-muted-foreground">{book.author}</p>
                  )}
                </div>
                <Badge variant={getStatusVariant(book.status)} size="sm">
                  {getStatusText(book.status)}
                </Badge>
              </div>

              {book.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {book.description}
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {book.genre && (
                    <Badge variant="outline" size="sm">{book.genre}</Badge>
                  )}
                </div>
                
                {book.status === "published" && book.revenue && (
                  <div className="flex items-center gap-1 text-sm text-green-600">
                    <DollarSign className="h-4 w-4" />
                    {book.revenue}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }
)
BookCard.displayName = "BookCard"

export { BookCard, bookCardVariants }