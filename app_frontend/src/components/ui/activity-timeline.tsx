import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { Check, Clock, AlertCircle, X, Info } from "lucide-react"
import { cn } from "@/lib/utils"

const timelineVariants = cva("relative", {
  variants: {
    size: {
      sm: "space-y-4",
      default: "space-y-6",
      lg: "space-y-8",
    }
  },
  defaultVariants: {
    size: "default",
  },
})

const timelineItemVariants = cva(
  "relative flex gap-4 pb-6 last:pb-0",
  {
    variants: {
      variant: {
        default: "",
        compact: "gap-3 pb-4",
      }
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface TimelineItem {
  id: string
  title: string
  description?: string
  timestamp: string
  status: "completed" | "in-progress" | "pending" | "failed" | "info"
  icon?: React.ReactNode
  metadata?: Record<string, any>
  href?: string
}

export interface ActivityTimelineProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof timelineVariants> {
  /** Timeline items */
  items: TimelineItem[]
  /** Show connecting lines */
  showLines?: boolean
  /** Compact variant */
  compact?: boolean
  /** Loading state */
  loading?: boolean
  /** Number of skeleton items to show when loading */
  skeletonCount?: number
}

const ActivityTimeline = React.forwardRef<HTMLDivElement, ActivityTimelineProps>(
  ({ 
    className, 
    size, 
    items, 
    showLines = true, 
    compact = false,
    loading = false,
    skeletonCount = 5,
    ...props 
  }, ref) => {
    const getStatusIcon = (status: TimelineItem["status"], customIcon?: React.ReactNode) => {
      if (customIcon) return customIcon
      
      switch (status) {
        case "completed":
          return <Check className="h-4 w-4" />
        case "in-progress":
          return <Clock className="h-4 w-4" />
        case "failed":
          return <X className="h-4 w-4" />
        case "info":
          return <Info className="h-4 w-4" />
        case "pending":
        default:
          return <AlertCircle className="h-4 w-4" />
      }
    }

    const getStatusColor = (status: TimelineItem["status"]) => {
      switch (status) {
        case "completed":
          return "bg-green-100 text-green-600 border-green-200"
        case "in-progress":
          return "bg-blue-100 text-blue-600 border-blue-200"
        case "failed":
          return "bg-red-100 text-red-600 border-red-200"
        case "info":
          return "bg-blue-100 text-blue-600 border-blue-200"
        case "pending":
        default:
          return "bg-muted text-muted-foreground border-border"
      }
    }

    const getLineColor = (status: TimelineItem["status"]) => {
      switch (status) {
        case "completed":
          return "bg-green-200"
        case "in-progress":
          return "bg-blue-200"
        case "failed":
          return "bg-red-200"
        case "info":
          return "bg-blue-200"
        case "pending":
        default:
          return "bg-border"
      }
    }

    if (loading) {
      return (
        <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <div key={index} className={cn(timelineItemVariants({ variant: compact ? "compact" : "default" }))}>
              {/* Icon skeleton */}
              <div className="flex-shrink-0 relative">
                <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
                {showLines && index < skeletonCount - 1 && (
                  <div className="absolute top-8 left-1/2 w-0.5 h-6 bg-muted animate-pulse transform -translate-x-1/2" />
                )}
              </div>
              
              {/* Content skeleton */}
              <div className="flex-1 space-y-2">
                <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                <div className="h-3 w-48 bg-muted animate-pulse rounded" />
                <div className="h-3 w-20 bg-muted animate-pulse rounded" />
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>
        {items.map((item, index) => {
          const isLast = index === items.length - 1
          const ItemComponent = item.href ? "a" : "div"
          
          return (
            <div key={item.id} className={cn(timelineItemVariants({ variant: compact ? "compact" : "default" }))}>
              {/* Timeline icon and line */}
              <div className="flex-shrink-0 relative">
                <div
                  className={cn(
                    "flex items-center justify-center h-8 w-8 rounded-full border-2",
                    getStatusColor(item.status)
                  )}
                >
                  {getStatusIcon(item.status, item.icon)}
                </div>
                
                {/* Connecting line */}
                {showLines && !isLast && (
                  <div
                    className={cn(
                      "absolute top-8 left-1/2 w-0.5 h-full transform -translate-x-1/2",
                      getLineColor(item.status)
                    )}
                  />
                )}
              </div>

              {/* Content */}
              <ItemComponent
                className={cn(
                  "flex-1 space-y-1",
                  item.href && "hover:bg-surface-50 -m-2 p-2 rounded-lg transition-colors"
                )}
                {...(item.href ? { href: item.href } : {})}
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-foreground">{item.title}</h4>
                  <time className="text-xs text-muted-foreground">{item.timestamp}</time>
                </div>
                
                {item.description && (
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                )}
                
                {/* Metadata */}
                {item.metadata && Object.keys(item.metadata).length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {Object.entries(item.metadata).map(([key, value]) => (
                      <span
                        key={key}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-surface-100 text-muted-foreground"
                      >
                        <span className="font-medium mr-1">{key}:</span>
                        <span>{String(value)}</span>
                      </span>
                    ))}
                  </div>
                )}
              </ItemComponent>
            </div>
          )
        })}
      </div>
    )
  }
)
ActivityTimeline.displayName = "ActivityTimeline"

export { ActivityTimeline, timelineVariants, timelineItemVariants }