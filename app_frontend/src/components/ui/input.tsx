import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const inputVariants = cva(
  "flex w-full rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-border hover:border-secondary-400 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20",
        error: "border-error bg-red-50 focus-visible:border-error focus-visible:ring-2 focus-visible:ring-error/20",
        success: "border-success bg-green-50 focus-visible:border-success focus-visible:ring-2 focus-visible:ring-success/20",
        warning: "border-warning bg-yellow-50 focus-visible:border-warning focus-visible:ring-2 focus-visible:ring-warning/20",
      },
      size: {
        sm: "h-8 px-3 py-1.5 text-xs",
        default: "h-10 px-4 py-2.5 text-sm",
        lg: "h-12 px-4 py-3 text-base",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  error?: string
  success?: string
  warning?: string
  helperText?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    variant, 
    size, 
    type, 
    leftIcon, 
    rightIcon, 
    error, 
    success, 
    warning, 
    helperText,
    ...props 
  }, ref) => {
    // Determine variant based on validation states
    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant

    const input = (
      <input
        type={type}
        className={cn(
          inputVariants({ variant: currentVariant, size }),
          leftIcon && "pl-10",
          rightIcon && "pr-10",
          className
        )}
        ref={ref}
        {...props}
      />
    )

    // If no icons, return simple input
    if (!leftIcon && !rightIcon) {
      return (
        <div className="w-full">
          {input}
          {(error || success || warning || helperText) && (
            <div className="mt-1.5 text-xs">
              {error && <p className="text-error flex items-center gap-1">{error}</p>}
              {success && <p className="text-success flex items-center gap-1">{success}</p>}
              {warning && <p className="text-warning flex items-center gap-1">{warning}</p>}
              {helperText && !error && !success && !warning && (
                <p className="text-muted-foreground">{helperText}</p>
              )}
            </div>
          )}
        </div>
      )
    }

    // Return input with icon wrapper
    return (
      <div className="w-full">
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          {input}
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || success || warning || helperText) && (
          <div className="mt-1.5 text-xs">
            {error && <p className="text-error flex items-center gap-1">{error}</p>}
            {success && <p className="text-success flex items-center gap-1">{success}</p>}
            {warning && <p className="text-warning flex items-center gap-1">{warning}</p>}
            {helperText && !error && !success && !warning && (
              <p className="text-muted-foreground">{helperText}</p>
            )}
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input, inputVariants }
