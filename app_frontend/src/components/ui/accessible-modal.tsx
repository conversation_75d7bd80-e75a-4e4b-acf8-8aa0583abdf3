"use client"

import React, { useEffect, useRef, useId } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/lib/utils'
import { FocusManager, keyboardHandlers, announceToScreenReader } from '@/lib/accessibility'
import { X } from 'lucide-react'

interface AccessibleModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  showCloseButton?: boolean
  initialFocus?: React.RefObject<HTMLElement>
  returnFocus?: boolean
  role?: 'dialog' | 'alertdialog'
  ariaLabel?: string
  preventScroll?: boolean
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  className,
  size = 'md',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  initialFocus,
  returnFocus = true,
  role = 'dialog',
  ariaLabel,
  preventScroll = true,
}: AccessibleModalProps) {
  const modalRef = useRef<HTMLDivElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const titleId = useId()
  const descriptionId = useId()
  const previousActiveElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isOpen) return

    // Store the previously focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    // Prevent body scroll
    if (preventScroll) {
      document.body.style.overflow = 'hidden'
    }

    // Announce modal opening to screen readers
    announceToScreenReader(`${title} dialog opened`, 'assertive')

    // Focus management
    const handleFocus = () => {
      if (modalRef.current) {
        const focusElement = initialFocus?.current || modalRef.current
        focusElement.focus()
      }
    }

    // Set up focus trap
    let removeFocusTrap: (() => void) | undefined
    if (modalRef.current) {
      removeFocusTrap = FocusManager.trapFocus(modalRef.current)
    }

    // Focus after animation
    const timeoutId = setTimeout(handleFocus, 100)

    return () => {
      clearTimeout(timeoutId)
      
      // Restore body scroll
      if (preventScroll) {
        document.body.style.overflow = ''
      }

      // Remove focus trap
      if (removeFocusTrap) {
        removeFocusTrap()
      }

      // Return focus to previously focused element
      if (returnFocus && previousActiveElement.current) {
        previousActiveElement.current.focus()
      }

      // Announce modal closing
      announceToScreenReader(`${title} dialog closed`, 'assertive')
    }
  }, [isOpen, title, initialFocus, returnFocus, preventScroll])

  // Keyboard event handlers
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return

    const handleKeyDown = keyboardHandlers.escape(onClose)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, closeOnEscape, onClose])

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === overlayRef.current) {
      onClose()
    }
  }

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4',
  }

  if (!isOpen) return null

  const modalContent = (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={handleOverlayClick}
    >
      <div
        ref={modalRef}
        role={role}
        aria-modal="true"
        aria-labelledby={titleId}
        aria-describedby={description ? descriptionId : undefined}
        aria-label={ariaLabel}
        className={cn(
          "w-full bg-background rounded-lg shadow-lg focus:outline-none",
          sizeClasses[size],
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2
            id={titleId}
            className="text-lg font-semibold leading-none tracking-tight"
          >
            {title}
          </h2>
          {showCloseButton && (
            <button
              type="button"
              onClick={onClose}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
              aria-label={`Close ${title} dialog`}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          )}
        </div>

        {/* Description */}
        {description && (
          <div className="px-6 pt-4">
            <p id={descriptionId} className="text-sm text-muted-foreground">
              {description}
            </p>
          </div>
        )}

        {/* Content */}
        <div className="p-6">{children}</div>
      </div>
    </div>
  )

  // Render in portal
  if (typeof window !== 'undefined') {
    return createPortal(modalContent, document.body)
  }

  return null
}

// Modal context for compound components
interface ModalContextValue {
  isOpen: boolean
  onClose: () => void
  titleId: string
  descriptionId: string
}

const ModalContext = React.createContext<ModalContextValue | null>(null)

export function useModalContext() {
  const context = React.useContext(ModalContext)
  if (!context) {
    throw new Error('Modal compound components must be used within a Modal')
  }
  return context
}

// Compound components
export function ModalProvider({
  children,
  isOpen,
  onClose,
}: {
  children: React.ReactNode
  isOpen: boolean
  onClose: () => void
}) {
  const titleId = useId()
  const descriptionId = useId()

  return (
    <ModalContext.Provider value={{ isOpen, onClose, titleId, descriptionId }}>
      {children}
    </ModalContext.Provider>
  )
}

export function ModalTrigger({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}

export function ModalContent({
  children,
  className,
  ...props
}: Omit<AccessibleModalProps, 'isOpen' | 'onClose' | 'title'> & {
  title: string
}) {
  const { isOpen, onClose } = useModalContext()
  
  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      className={className}
      {...props}
    >
      {children}
    </AccessibleModal>
  )
}

export function ModalHeader({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex items-center justify-between p-6 border-b">
      {children}
    </div>
  )
}

export function ModalTitle({ children }: { children: React.ReactNode }) {
  const { titleId } = useModalContext()
  
  return (
    <h2
      id={titleId}
      className="text-lg font-semibold leading-none tracking-tight"
    >
      {children}
    </h2>
  )
}

export function ModalDescription({ children }: { children: React.ReactNode }) {
  const { descriptionId } = useModalContext()
  
  return (
    <p id={descriptionId} className="text-sm text-muted-foreground">
      {children}
    </p>
  )
}

export function ModalBody({ children }: { children: React.ReactNode }) {
  return <div className="p-6">{children}</div>
}

export function ModalFooter({ 
  children,
  className,
}: { 
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn("flex items-center justify-end gap-4 p-6 border-t", className)}>
      {children}
    </div>
  )
}

export function ModalCloseButton({ 
  children,
  className,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { onClose } = useModalContext()
  
  return (
    <button
      type="button"
      onClick={onClose}
      className={cn(
        "rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        className
      )}
      {...props}
    >
      {children || (
        <>
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </>
      )}
    </button>
  )
}

export default AccessibleModal