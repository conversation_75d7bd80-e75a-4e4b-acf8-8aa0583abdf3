import { render, screen } from '@testing-library/react'
import { Heading, Text } from '../typography'

describe('Heading', () => {
  it('renders with correct variant styles', () => {
    render(<Heading variant="h1">Test Heading</Heading>)
    const heading = screen.getByText('Test Heading')
    expect(heading).toBeInTheDocument()
    expect(heading.tagName).toBe('H1')
    expect(heading).toHaveClass('text-4xl', 'font-semibold')
  })

  it('uses as prop to override element type', () => {
    render(<Heading as="h2" variant="h1">Test Heading</Heading>)
    const heading = screen.getByText('Test Heading')
    expect(heading.tagName).toBe('H2')
  })

  it('applies custom className', () => {
    render(<Heading variant="h1" className="custom-class">Test</Heading>)
    const heading = screen.getByText('Test')
    expect(heading).toHaveClass('custom-class')
  })
})

describe('Text', () => {
  it('renders with body variant by default', () => {
    render(<Text>Test text</Text>)
    const text = screen.getByText('Test text')
    expect(text).toBeInTheDocument()
    expect(text).toHaveClass('text-base', 'text-foreground')
  })

  it('renders different variants correctly', () => {
    const { rerender } = render(<Text variant="lead">Lead text</Text>)
    expect(screen.getByText('Lead text')).toHaveClass('text-lg', 'text-muted-foreground')

    rerender(<Text variant="small">Small text</Text>)
    expect(screen.getByText('Small text')).toHaveClass('text-sm')

    rerender(<Text variant="muted">Muted text</Text>)
    expect(screen.getByText('Muted text')).toHaveClass('text-sm', 'text-muted-foreground')
  })

  it('renders as different elements', () => {
    render(<Text as="span">Span text</Text>)
    const text = screen.getByText('Span text')
    expect(text.tagName).toBe('SPAN')
  })
})