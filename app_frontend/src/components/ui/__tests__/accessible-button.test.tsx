import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AccessibleButton } from '../accessible-button'
import { announceToScreenReader } from '@/lib/accessibility'

// Mock accessibility utilities
jest.mock('@/lib/accessibility', () => ({
  announceToScreenReader: jest.fn(),
}))

const mockedAnnounceToScreenReader = announceToScreenReader as jest.Mock

// Mock window.confirm
global.confirm = jest.fn()

describe('AccessibleButton', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.confirm as jest.Mock).mockReturnValue(true)
  })

  it('should render with default props', () => {
    render(<AccessibleButton>Click me</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('type', 'button')
  })

  it('should apply variant classes', () => {
    render(<AccessibleButton variant="destructive">Delete</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Delete' })
    expect(button).toHaveClass('bg-destructive')
  })

  it('should apply size classes', () => {
    render(<AccessibleButton size="lg">Large Button</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Large Button' })
    expect(button).toHaveClass('h-11')
  })

  it('should handle click events', async () => {
    const handleClick = jest.fn()
    render(<AccessibleButton onClick={handleClick}>Click me</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    await userEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should handle keyboard events (Enter)', async () => {
    const handleClick = jest.fn()
    render(<AccessibleButton onClick={handleClick}>Click me</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    button.focus()
    
    fireEvent.keyDown(button, { key: 'Enter' })
    
    await waitFor(() => {
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  it('should handle keyboard events (Space)', async () => {
    const handleClick = jest.fn()
    render(<AccessibleButton onClick={handleClick}>Click me</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    button.focus()
    
    fireEvent.keyDown(button, { key: ' ' })
    
    await waitFor(() => {
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  it('should show loading state', () => {
    render(
      <AccessibleButton loading loadingText="Please wait...">
        Submit
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveAttribute('aria-busy', 'true')
    expect(button).toHaveAttribute('aria-disabled', 'true')
    expect(screen.getByText('Please wait...')).toBeInTheDocument()
    expect(screen.getByLabelText('Please wait...')).toBeInTheDocument()
  })

  it('should prevent clicks when loading', async () => {
    const handleClick = jest.fn()
    render(
      <AccessibleButton loading onClick={handleClick}>
        Submit
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button')
    await userEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should prevent clicks when disabled', async () => {
    const handleClick = jest.fn()
    render(
      <AccessibleButton disabled onClick={handleClick}>
        Submit
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button')
    await userEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should show confirmation dialog when confirmAction is true', async () => {
    const handleClick = jest.fn()
    render(
      <AccessibleButton 
        confirmAction 
        confirmMessage="Are you sure?" 
        onClick={handleClick}
      >
        Delete
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Delete' })
    await userEvent.click(button)
    
    expect(global.confirm).toHaveBeenCalledWith('Are you sure?')
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should not execute onClick when confirmation is rejected', async () => {
    ;(global.confirm as jest.Mock).mockReturnValue(false)
    const handleClick = jest.fn()
    
    render(
      <AccessibleButton confirmAction onClick={handleClick}>
        Delete
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Delete' })
    await userEvent.click(button)
    
    expect(global.confirm).toHaveBeenCalled()
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should announce to screen reader when announceOnClick is provided', async () => {
    render(
      <AccessibleButton announceOnClick="Action completed">
        Complete
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Complete' })
    await userEvent.click(button)
    
    expect(mockedAnnounceToScreenReader).toHaveBeenCalledWith('Action completed')
  })

  it('should set ARIA attributes correctly', () => {
    render(
      <AccessibleButton
        describedBy="help-text"
        hasPopup="menu"
        expanded={true}
        pressed={false}
        controls="menu-content"
      >
        Menu
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Menu' })
    expect(button).toHaveAttribute('aria-describedby', 'help-text')
    expect(button).toHaveAttribute('aria-haspopup', 'menu')
    expect(button).toHaveAttribute('aria-expanded', 'true')
    expect(button).toHaveAttribute('aria-pressed', 'false')
    expect(button).toHaveAttribute('aria-controls', 'menu-content')
  })

  it('should handle asChild prop with Slot', () => {
    render(
      <AccessibleButton asChild>
        <a href="/test">Link Button</a>
      </AccessibleButton>
    )
    
    const link = screen.getByRole('link', { name: 'Link Button' })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/test')
  })

  it('should handle confirmation on keyboard events', async () => {
    const handleClick = jest.fn()
    render(
      <AccessibleButton 
        confirmAction 
        confirmMessage="Confirm action?" 
        onClick={handleClick}
      >
        Delete
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Delete' })
    button.focus()
    
    fireEvent.keyDown(button, { key: 'Enter' })
    
    expect(global.confirm).toHaveBeenCalledWith('Confirm action?')
    await waitFor(() => {
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  it('should not execute on keyboard when confirmation is rejected', async () => {
    ;(global.confirm as jest.Mock).mockReturnValue(false)
    const handleClick = jest.fn()
    
    render(
      <AccessibleButton confirmAction onClick={handleClick}>
        Delete
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Delete' })
    button.focus()
    
    fireEvent.keyDown(button, { key: 'Enter' })
    
    expect(global.confirm).toHaveBeenCalled()
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should apply custom className', () => {
    render(
      <AccessibleButton className="custom-class">
        Custom
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button', { name: 'Custom' })
    expect(button).toHaveClass('custom-class')
  })

  it('should pass through other props', () => {
    render(
      <AccessibleButton data-testid="custom-button" title="Custom title">
        Button
      </AccessibleButton>
    )
    
    const button = screen.getByTestId('custom-button')
    expect(button).toHaveAttribute('title', 'Custom title')
  })

  it('should ignore keyboard events other than Enter and Space', () => {
    const handleClick = jest.fn()
    render(<AccessibleButton onClick={handleClick}>Click me</AccessibleButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    button.focus()
    
    fireEvent.keyDown(button, { key: 'a' })
    fireEvent.keyDown(button, { key: 'Tab' })
    fireEvent.keyDown(button, { key: 'Escape' })
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should not trigger events when disabled via loading or disabled prop on keyboard', () => {
    const handleClick = jest.fn()
    
    const { rerender } = render(
      <AccessibleButton disabled onClick={handleClick}>
        Button
      </AccessibleButton>
    )
    
    const button = screen.getByRole('button')
    fireEvent.keyDown(button, { key: 'Enter' })
    
    expect(handleClick).not.toHaveBeenCalled()
    
    rerender(
      <AccessibleButton loading onClick={handleClick}>
        Button
      </AccessibleButton>
    )
    
    fireEvent.keyDown(button, { key: 'Enter' })
    
    expect(handleClick).not.toHaveBeenCalled()
  })
})