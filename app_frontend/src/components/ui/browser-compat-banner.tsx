"use client"

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { checkBrowserCompatibility, getBrowserInfo, PolyfillLoader } from '@/lib/browser-compat'
import { AlertTriangle, X, ExternalLink, RefreshCw } from 'lucide-react'

interface BrowserCompatBannerProps {
  className?: string
  autoHide?: boolean
  hideDelay?: number
  showPolyfillStatus?: boolean
}

export function BrowserCompatBanner({
  className,
  autoHide = false,
  hideDelay = 10000,
  showPolyfillStatus = true,
}: BrowserCompatBannerProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const [compatStatus, setCompatStatus] = useState<{
    compatible: boolean
    warnings: string[]
    critical: string[]
  } | null>(null)
  const [browserInfo, setBrowserInfo] = useState<any>(null)
  const [polyfillsLoaded, setPolyfillsLoaded] = useState(false)

  useEffect(() => {
    // Check if already dismissed
    const dismissed = localStorage.getItem('browser-compat-dismissed')
    if (dismissed) {
      setIsDismissed(true)
      return
    }

    // Check browser compatibility
    const status = checkBrowserCompatibility()
    const browser = getBrowserInfo()
    
    setCompatStatus(status)
    setBrowserInfo(browser)

    // Show banner if there are issues
    if (status.warnings.length > 0 || status.critical.length > 0) {
      setIsVisible(true)
    }

    // Load polyfills if needed
    if (!status.compatible && showPolyfillStatus) {
      PolyfillLoader.loadEssentialPolyfills().then(() => {
        setPolyfillsLoaded(true)
      })
    }

    // Auto-hide after delay
    if (autoHide && hideDelay > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, hideDelay)

      return () => clearTimeout(timer)
    }
  }, [autoHide, hideDelay, showPolyfillStatus])

  const handleDismiss = () => {
    setIsVisible(false)
    setIsDismissed(true)
    localStorage.setItem('browser-compat-dismissed', 'true')
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  const getBrowserDownloadUrl = (browserName: string): string => {
    const urls: Record<string, string> = {
      chrome: 'https://www.google.com/chrome/',
      firefox: 'https://www.mozilla.org/firefox/',
      safari: 'https://www.apple.com/safari/',
      edge: 'https://www.microsoft.com/edge',
    }
    return urls[browserName] || 'https://browsehappy.com/'
  }

  if (!isVisible || isDismissed || !compatStatus) {
    return null
  }

  const hasCriticalIssues = compatStatus.critical.length > 0
  const hasWarnings = compatStatus.warnings.length > 0

  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-50 border-b shadow-lg',
        hasCriticalIssues
          ? 'bg-destructive text-destructive-foreground'
          : 'bg-warning text-warning-foreground',
        className
      )}
      role="alert"
      aria-live="assertive"
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-start gap-3">
          <AlertTriangle
            className="h-5 w-5 flex-shrink-0 mt-0.5"
            aria-hidden="true"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="space-y-2">
                <h3 className="font-semibold text-sm">
                  {hasCriticalIssues
                    ? 'Browser Compatibility Issues Detected'
                    : 'Browser Update Recommended'}
                </h3>
                
                <div className="space-y-1 text-sm">
                  {compatStatus.critical.map((issue, index) => (
                    <p key={index} className="flex items-center gap-2">
                      <span className="inline-block w-1 h-1 bg-current rounded-full flex-shrink-0 mt-2" />
                      {issue}
                    </p>
                  ))}
                  
                  {compatStatus.warnings.map((warning, index) => (
                    <p key={index} className="flex items-center gap-2 opacity-90">
                      <span className="inline-block w-1 h-1 bg-current rounded-full flex-shrink-0 mt-2" />
                      {warning}
                    </p>
                  ))}
                  
                  {showPolyfillStatus && polyfillsLoaded && (
                    <p className="flex items-center gap-2 text-xs opacity-75">
                      <span className="inline-block w-1 h-1 bg-current rounded-full flex-shrink-0 mt-1.5" />
                      Compatibility fixes have been loaded automatically.
                    </p>
                  )}
                </div>

                {browserInfo && (
                  <div className="text-xs opacity-75">
                    Current browser: {browserInfo.name} {browserInfo.version} on {browserInfo.platform}
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 flex-shrink-0">
                {hasCriticalIssues && (
                  <button
                    onClick={handleRefresh}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-white/10 hover:bg-white/20 rounded text-xs transition-colors"
                    title="Refresh page to apply compatibility fixes"
                  >
                    <RefreshCw className="h-3 w-3" />
                    Refresh
                  </button>
                )}
                
                <a
                  href={getBrowserDownloadUrl(browserInfo?.name || '')}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 px-3 py-1 bg-white/10 hover:bg-white/20 rounded text-xs transition-colors"
                >
                  <ExternalLink className="h-3 w-3" />
                  Update Browser
                </a>
                
                <button
                  onClick={handleDismiss}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                  aria-label="Dismiss browser compatibility warning"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook for browser compatibility
export function useBrowserCompat() {
  const [compatStatus, setCompatStatus] = useState<{
    compatible: boolean
    warnings: string[]
    critical: string[]
  } | null>(null)
  
  const [browserInfo, setBrowserInfo] = useState<any>(null)
  const [isSupported, setIsSupported] = useState(true)

  useEffect(() => {
    const status = checkBrowserCompatibility()
    const browser = getBrowserInfo()
    
    setCompatStatus(status)
    setBrowserInfo(browser)
    setIsSupported(status.compatible)
    
    // Load polyfills if needed
    if (!status.compatible) {
      PolyfillLoader.loadEssentialPolyfills()
    }
  }, [])

  return {
    compatStatus,
    browserInfo,
    isSupported,
    hasCriticalIssues: compatStatus?.critical.length > 0,
    hasWarnings: compatStatus?.warnings.length > 0,
  }
}

// Feature detection hook
export function useFeatureDetection() {
  const [features, setFeatures] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    import('@/lib/browser-compat').then(({ detectFeatureSupport }) => {
      const detectedFeatures = detectFeatureSupport()
      setFeatures(detectedFeatures)
      setIsLoading(false)
    })
  }, [])

  const hasFeature = (featureName: string): boolean => {
    return features?.[featureName] ?? false
  }

  const requireFeature = (featureName: string, fallback?: () => void): boolean => {
    const supported = hasFeature(featureName)
    if (!supported && fallback) {
      fallback()
    }
    return supported
  }

  return {
    features,
    isLoading,
    hasFeature,
    requireFeature,
  }
}

export default BrowserCompatBanner