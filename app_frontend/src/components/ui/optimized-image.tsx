"use client"

import React, { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { optimizeImage, LazyLoader } from '@/lib/performance'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  lazy?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  fallback?: string
  priority?: boolean
  sizes?: string
  fill?: boolean
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  lazy = true,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  fallback = '/images/placeholder.jpg',
  priority = false,
  sizes,
  fill = false,
  style,
  onLoad,
  onError,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [shouldLoad, setShouldLoad] = useState(!lazy || priority)
  const imgRef = useRef<HTMLDivElement>(null)
  const lazyLoaderRef = useRef<LazyLoader | null>(null)

  // Lazy loading setup
  useEffect(() => {
    if (lazy && !priority && imgRef.current && !shouldLoad) {
      lazyLoaderRef.current = new LazyLoader(
        () => setShouldLoad(true),
        { rootMargin: '50px' }
      )
      lazyLoaderRef.current.observe(imgRef.current)
    }

    return () => {
      if (lazyLoaderRef.current) {
        lazyLoaderRef.current.disconnect()
      }
    }
  }, [lazy, priority, shouldLoad])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  const optimizedSrc = optimizeImage(src, width, height, quality)
  const imageSrc = hasError ? fallback : optimizedSrc

  if (!shouldLoad) {
    return (
      <div
        ref={imgRef}
        className={cn(
          'bg-muted animate-pulse',
          className
        )}
        style={{
          width: width || '100%',
          height: height || 'auto',
          aspectRatio: width && height ? `${width}/${height}` : undefined,
          ...style,
        }}
        aria-label={`Loading ${alt}`}
      />
    )
  }

  const imageProps = {
    src: imageSrc,
    alt,
    onLoad: handleLoad,
    onError: handleError,
    quality,
    priority,
    sizes,
    className: cn(
      'transition-opacity duration-300',
      isLoaded ? 'opacity-100' : 'opacity-0',
      className
    ),
    style,
    ...props,
  }

  if (fill) {
    return (
      <div ref={imgRef} className="relative overflow-hidden">
        <Image
          {...imageProps}
          fill
          placeholder={placeholder}
          blurDataURL={blurDataURL}
        />
        {!isLoaded && (
          <div className="absolute inset-0 bg-muted animate-pulse" />
        )}
      </div>
    )
  }

  return (
    <div ref={imgRef} className="relative">
      <Image
        {...imageProps}
        width={width}
        height={height}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
      />
      {!isLoaded && (
        <div 
          className="absolute inset-0 bg-muted animate-pulse"
          style={{
            width: width || '100%',
            height: height || 'auto',
          }}
        />
      )}
    </div>
  )
}

// Higher-order component for image optimization
export function withImageOptimization<P extends object>(
  Component: React.ComponentType<P>
) {
  return function OptimizedComponent(props: P) {
    return <Component {...props} />
  }
}

// Avatar component with optimization
interface OptimizedAvatarProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}

export function OptimizedAvatar({
  src,
  alt,
  size = 'md',
  fallback,
  className,
}: OptimizedAvatarProps) {
  const sizeMap = {
    sm: 32,
    md: 40,
    lg: 56,
    xl: 80,
  }

  const dimension = sizeMap[size]

  return (
    <OptimizedImage
      src={src || fallback || '/images/default-avatar.jpg'}
      alt={alt}
      width={dimension}
      height={dimension}
      className={cn(
        'rounded-full object-cover',
        className
      )}
      quality={85}
      priority={false}
      lazy={true}
    />
  )
}

// Book cover component with optimization
interface OptimizedBookCoverProps {
  src?: string
  title: string
  width?: number
  height?: number
  className?: string
  variant?: 'small' | 'medium' | 'large'
}

export function OptimizedBookCover({
  src,
  title,
  width,
  height,
  className,
  variant = 'medium',
}: OptimizedBookCoverProps) {
  const sizeMap = {
    small: { width: 120, height: 180 },
    medium: { width: 160, height: 240 },
    large: { width: 240, height: 360 },
  }

  const dimensions = width && height 
    ? { width, height }
    : sizeMap[variant]

  return (
    <OptimizedImage
      src={src || '/images/default-book-cover.jpg'}
      alt={`Cover of ${title}`}
      width={dimensions.width}
      height={dimensions.height}
      className={cn(
        'rounded-lg shadow-sm object-cover',
        className
      )}
      quality={80}
      priority={false}
      lazy={true}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
    />
  )
}

export default OptimizedImage