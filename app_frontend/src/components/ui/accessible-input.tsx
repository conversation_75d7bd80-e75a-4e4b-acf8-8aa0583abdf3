"use client"

import React, { forwardRef, useState, useId } from 'react'
import { cn } from '@/lib/utils'
import { 
  getFormErrorId, 
  getFormHelpId, 
  getFormLabelId,
  announceToScreenReader 
} from '@/lib/accessibility'
import { Eye, EyeOff, AlertCircle, CheckCircle, Info } from 'lucide-react'

export interface AccessibleInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  helpText?: string
  error?: string
  success?: string
  hideLabel?: boolean
  showPasswordToggle?: boolean
  invalid?: boolean
  describedBy?: string
  autoComplete?: string
  inputMode?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search'
}

const AccessibleInput = forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({
    className,
    type = 'text',
    label,
    helpText,
    error,
    success,
    hideLabel = false,
    showPasswordToggle = false,
    invalid,
    describedBy,
    autoComplete,
    inputMode,
    id,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const internalId = useId()
    const inputId = id || internalId
    
    const labelId = getFormLabelId(inputId)
    const errorId = getFormErrorId(inputId)
    const helpId = getFormHelpId(inputId)
    
    const isPassword = type === 'password'
    const actualType = isPassword && showPassword ? 'text' : type
    
    // Build describedBy string
    const ariaDescribedBy = [
      describedBy,
      helpText ? helpId : undefined,
      error ? errorId : undefined,
    ].filter(Boolean).join(' ') || undefined

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
      announceToScreenReader(
        showPassword ? 'Password hidden' : 'Password visible',
        'assertive'
      )
    }

    const handleInvalid = (e: React.FormEvent<HTMLInputElement>) => {
      // Announce validation errors to screen readers
      if (e.currentTarget.validationMessage) {
        announceToScreenReader(
          `Validation error: ${e.currentTarget.validationMessage}`,
          'assertive'
        )
      }
      props.onInvalid?.(e)
    }

    return (
      <div className="space-y-2">
        {label && (
          <label
            id={labelId}
            htmlFor={inputId}
            className={cn(
              "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              hideLabel && "sr-only"
            )}
          >
            {label}
            {props.required && (
              <span className="ml-1 text-destructive" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        <div className="relative">
          <input
            type={actualType}
            className={cn(
              "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
              error && "border-destructive focus-visible:ring-destructive",
              success && "border-success focus-visible:ring-success",
              invalid && "border-destructive focus-visible:ring-destructive",
              isPassword && showPasswordToggle && "pr-10",
              className
            )}
            ref={ref}
            id={inputId}
            aria-labelledby={label ? labelId : undefined}
            aria-describedby={ariaDescribedBy}
            aria-invalid={error || invalid ? 'true' : 'false'}
            aria-required={props.required ? 'true' : undefined}
            autoComplete={autoComplete}
            inputMode={inputMode}
            onInvalid={handleInvalid}
            {...props}
          />

          {/* Password toggle button */}
          {isPassword && showPasswordToggle && (
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3 hover:text-primary focus:text-primary focus:outline-none"
              onClick={togglePasswordVisibility}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
              tabIndex={0}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" aria-hidden="true" />
              ) : (
                <Eye className="h-4 w-4" aria-hidden="true" />
              )}
            </button>
          )}
        </div>

        {/* Help text */}
        {helpText && (
          <p
            id={helpId}
            className="text-sm text-muted-foreground flex items-start gap-2"
          >
            <Info className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
            {helpText}
          </p>
        )}

        {/* Error message */}
        {error && (
          <p
            id={errorId}
            className="text-sm text-destructive flex items-start gap-2"
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
            {error}
          </p>
        )}

        {/* Success message */}
        {success && (
          <p
            className="text-sm text-success flex items-start gap-2"
            role="status"
            aria-live="polite"
          >
            <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
            {success}
          </p>
        )}
      </div>
    )
  }
)

AccessibleInput.displayName = "AccessibleInput"

export { AccessibleInput }