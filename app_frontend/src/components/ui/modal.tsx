"use client"

import * as React from "react"
import { AlertTriangle, CheckCircle, Info, X } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "./dialog"
import { Button } from "./button"
import { cn } from "@/lib/utils"

// Confirmation Modal
export interface ConfirmationModalProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive" | "warning"
  onConfirm: () => void
  onCancel?: () => void
  children?: React.ReactNode
  loading?: boolean
}

export const ConfirmationModal = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
  onCancel,
  children,
  loading = false,
}: ConfirmationModalProps) => {
  const getIcon = () => {
    switch (variant) {
      case "destructive":
        return <AlertTriangle className="h-6 w-6 text-red-600" />
      case "warning":
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />
      default:
        return <Info className="h-6 w-6 text-blue-600" />
    }
  }

  const getConfirmVariant = () => {
    switch (variant) {
      case "destructive":
        return "destructive" as const
      case "warning":
        return "outline" as const
      default:
        return "default" as const
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent size="sm">
        <DialogHeader>
          <div className="flex items-center gap-3">
            {getIcon()}
            <DialogTitle>{title}</DialogTitle>
          </div>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              onCancel?.()
              onOpenChange?.(false)
            }}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={getConfirmVariant()}
            onClick={onConfirm}
            loading={loading}
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Filter Modal for Analytics
export interface FilterModalProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title?: string
  children: React.ReactNode
  onApplyFilters: (filters: any) => void
  onResetFilters: () => void
  trigger?: React.ReactNode
}

export const FilterModal = ({
  open,
  onOpenChange,
  title = "Filter Options",
  children,
  onApplyFilters,
  onResetFilters,
  trigger,
}: FilterModalProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent size="lg">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            Customize your view by applying filters
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {children}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onResetFilters}
          >
            Reset
          </Button>
          <Button
            onClick={() => onApplyFilters({})}
          >
            Apply Filters
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Success Modal
export interface SuccessModalProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title: string
  description?: string
  actionText?: string
  onAction?: () => void
  children?: React.ReactNode
}

export const SuccessModal = ({
  open,
  onOpenChange,
  title,
  description,
  actionText = "Continue",
  onAction,
  children,
}: SuccessModalProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent size="sm">
        <DialogHeader>
          <div className="flex flex-col items-center text-center space-y-3">
            <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <DialogTitle>{title}</DialogTitle>
            {description && <DialogDescription>{description}</DialogDescription>}
          </div>
        </DialogHeader>
        <DialogFooter>
          <Button
            onClick={() => {
              onAction?.()
              onOpenChange?.(false)
            }}
            className="w-full"
          >
            {actionText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Loading Modal
export interface LoadingModalProps {
  open?: boolean
  title?: string
  description?: string
  progress?: number
  showProgress?: boolean
}

export const LoadingModal = ({
  open = false,
  title = "Processing...",
  description,
  progress,
  showProgress = false,
}: LoadingModalProps) => {
  return (
    <Dialog open={open}>
      <DialogContent size="sm" hideCloseButton>
        <DialogHeader>
          <div className="flex flex-col items-center text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <DialogTitle>{title}</DialogTitle>
            {description && <DialogDescription>{description}</DialogDescription>}
            {showProgress && progress !== undefined && (
              <div className="w-full space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}

// Image Preview Modal
export interface ImagePreviewModalProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  src: string
  alt: string
  title?: string
  children?: React.ReactNode
}

export const ImagePreviewModal = ({
  open,
  onOpenChange,
  src,
  alt,
  title,
  children,
}: ImagePreviewModalProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent variant="fullscreen" className="p-0">
        <div className="relative h-full flex items-center justify-center bg-black/90">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={src}
            alt={alt}
            className="max-h-full max-w-full object-contain"
          />
          <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
            {title && (
              <h3 className="text-white text-lg font-semibold">{title}</h3>
            )}
            <DialogClose className="rounded-sm bg-white/20 p-2 text-white hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white/50">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}