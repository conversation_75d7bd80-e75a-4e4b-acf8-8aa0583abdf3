import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "./button"

const paginationVariants = cva("mx-auto flex w-full justify-center", {
  variants: {
    size: {
      sm: "gap-1",
      default: "gap-1", 
      lg: "gap-2",
    }
  },
  defaultVariants: {
    size: "default",
  },
})

const paginationItemVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "hover:bg-accent hover:text-accent-foreground",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        ghost: "hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        sm: "h-8 px-2 text-xs",
        default: "h-9 px-3",
        lg: "h-10 px-4",
      },
      active: {
        true: "bg-primary text-primary-foreground hover:bg-primary/90",
        false: "",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      active: false,
    },
  }
)

export interface PaginationProps
  extends React.ComponentPropsWithoutRef<"nav">,
    VariantProps<typeof paginationVariants> {}

function Pagination({ className, size, ...props }: PaginationProps) {
  return (
    <nav
      role="navigation"
      aria-label="pagination"
      className={cn(paginationVariants({ size }), className)}
      {...props}
    />
  )
}
Pagination.displayName = "Pagination"

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentPropsWithoutRef<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentPropsWithoutRef<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean
} & VariantProps<typeof paginationItemVariants> &
  React.ComponentPropsWithoutRef<"a">

const PaginationLink = ({
  className,
  isActive,
  size,
  variant,
  ...props
}: PaginationLinkProps) => (
  <a
    aria-current={isActive ? "page" : undefined}
    className={cn(
      paginationItemVariants({
        variant,
        size,
        active: isActive,
      }),
      className
    )}
    {...props}
  />
)
PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to previous page"
    className={cn("gap-1 pl-2.5", className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Previous</span>
  </PaginationLink>
)
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to next page"
    className={cn("gap-1 pr-2.5", className)}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </PaginationLink>
)
PaginationNext.displayName = "PaginationNext"

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"span">) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)
PaginationEllipsis.displayName = "PaginationEllipsis"

// Advanced Pagination Component
export interface AdvancedPaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showFirstLast?: boolean
  showPreviousNext?: boolean
  maxVisiblePages?: number
  size?: "sm" | "default" | "lg"
  className?: string
  disabled?: boolean
}

const AdvancedPagination = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  showPreviousNext = true,
  maxVisiblePages = 5,
  size = "default",
  className,
  disabled = false,
}: AdvancedPaginationProps) => {
  const getVisiblePages = () => {
    const delta = Math.floor(maxVisiblePages / 2)
    const range = []
    const rangeWithDots = []

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i)
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, "...")
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages)
    } else {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  const visiblePages = totalPages > 1 ? getVisiblePages() : [1]

  return (
    <Pagination className={className} size={size}>
      <PaginationContent>
        {/* First page */}
        {showFirstLast && currentPage > 1 && (
          <PaginationItem>
            <Button
              variant="outline"
              size={size}
              onClick={() => onPageChange(1)}
              disabled={disabled}
            >
              First
            </Button>
          </PaginationItem>
        )}

        {/* Previous page */}
        {showPreviousNext && (
          <PaginationItem>
            <Button
              variant="outline"
              size={size}
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              disabled={disabled || currentPage <= 1}
              className="gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
          </PaginationItem>
        )}

        {/* Page numbers */}
        {visiblePages.map((page, index) => (
          <PaginationItem key={index}>
            {page === "..." ? (
              <PaginationEllipsis />
            ) : (
              <Button
                variant={page === currentPage ? "default" : "outline"}
                size={size}
                onClick={() => onPageChange(page as number)}
                disabled={disabled}
                aria-current={page === currentPage ? "page" : undefined}
              >
                {page}
              </Button>
            )}
          </PaginationItem>
        ))}

        {/* Next page */}
        {showPreviousNext && (
          <PaginationItem>
            <Button
              variant="outline"
              size={size}
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              disabled={disabled || currentPage >= totalPages}
              className="gap-1"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </PaginationItem>
        )}

        {/* Last page */}
        {showFirstLast && currentPage < totalPages && (
          <PaginationItem>
            <Button
              variant="outline"
              size={size}
              onClick={() => onPageChange(totalPages)}
              disabled={disabled}
            >
              Last
            </Button>
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  )
}

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  AdvancedPagination,
}