"use client"

import React, { forwardRef } from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { keyboardHandlers, announceToScreenReader } from '@/lib/accessibility'

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 focus:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        link: "underline-offset-4 hover:underline focus:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface AccessibleButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  confirmAction?: boolean
  confirmMessage?: string
  announceOnClick?: string
  describedBy?: string
  hasPopup?: boolean | 'true' | 'false' | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog'
  expanded?: boolean
  pressed?: boolean
  controls?: string
}

const AccessibleButton = forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({
    className,
    variant,
    size,
    asChild = false,
    loading = false,
    loadingText = "Loading...",
    confirmAction = false,
    confirmMessage = "Are you sure you want to perform this action?",
    announceOnClick,
    describedBy,
    hasPopup,
    expanded,
    pressed,
    controls,
    children,
    onClick,
    onKeyDown,
    disabled,
    type = "button",
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) {
        e.preventDefault()
        return
      }

      if (confirmAction) {
        if (!window.confirm(confirmMessage)) {
          e.preventDefault()
          return
        }
      }

      if (announceOnClick) {
        announceToScreenReader(announceOnClick)
      }

      onClick?.(e)
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
      if (disabled || loading) {
        return
      }

      // Handle Enter and Space for accessibility
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        if (confirmAction) {
          if (!window.confirm(confirmMessage)) {
            return
          }
        }
        
        if (announceOnClick) {
          announceToScreenReader(announceOnClick)
        }
        
        // Trigger click event
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
        })
        e.currentTarget.dispatchEvent(clickEvent)
      }

      onKeyDown?.(e)
    }

    // Build ARIA attributes
    const ariaAttributes: Record<string, any> = {}
    
    if (describedBy) {
      ariaAttributes['aria-describedby'] = describedBy
    }
    
    if (hasPopup !== undefined) {
      ariaAttributes['aria-haspopup'] = hasPopup
    }
    
    if (expanded !== undefined) {
      ariaAttributes['aria-expanded'] = expanded
    }
    
    if (pressed !== undefined) {
      ariaAttributes['aria-pressed'] = pressed
    }
    
    if (controls) {
      ariaAttributes['aria-controls'] = controls
    }
    
    if (loading) {
      ariaAttributes['aria-busy'] = true
      ariaAttributes['aria-disabled'] = true
    }

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        type={type}
        disabled={disabled || loading}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        {...ariaAttributes}
        {...props}
      >
        {loading ? (
          <>
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <span className="sr-only">{loadingText}</span>
            {loadingText}
          </>
        ) : (
          children
        )}
      </Comp>
    )
  }
)

AccessibleButton.displayName = "AccessibleButton"

export { AccessibleButton, buttonVariants }