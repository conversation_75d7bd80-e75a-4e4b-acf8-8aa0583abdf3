import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const textareaVariants = cva(
  "flex w-full rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 resize-none",
  {
    variants: {
      variant: {
        default: "border-border hover:border-secondary-400 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20",
        error: "border-error bg-red-50 focus-visible:border-error focus-visible:ring-2 focus-visible:ring-error/20",
        success: "border-success bg-green-50 focus-visible:border-success focus-visible:ring-2 focus-visible:ring-success/20",
        warning: "border-warning bg-yellow-50 focus-visible:border-warning focus-visible:ring-2 focus-visible:ring-warning/20",
      },
      size: {
        sm: "min-h-[80px] text-xs",
        default: "min-h-[100px] text-sm",
        lg: "min-h-[120px] text-base",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {
  error?: string
  success?: string
  warning?: string
  helperText?: string
  maxLength?: number
  showCharCount?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    className, 
    variant, 
    size, 
    error, 
    success, 
    warning, 
    helperText,
    maxLength,
    showCharCount = false,
    value,
    ...props 
  }, ref) => {
    // Determine variant based on validation states
    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant
    
    // Calculate character count
    const charCount = typeof value === 'string' ? value.length : 0
    const showCount = showCharCount || maxLength

    return (
      <div className="w-full">
        <textarea
          className={cn(textareaVariants({ variant: currentVariant, size }), className)}
          ref={ref}
          value={value}
          maxLength={maxLength}
          {...props}
        />
        
        {/* Helper text and character count */}
        <div className="mt-1.5 flex items-center justify-between text-xs">
          <div>
            {error && <p className="text-error flex items-center gap-1">{error}</p>}
            {success && <p className="text-success flex items-center gap-1">{success}</p>}
            {warning && <p className="text-warning flex items-center gap-1">{warning}</p>}
            {helperText && !error && !success && !warning && (
              <p className="text-muted-foreground">{helperText}</p>
            )}
          </div>
          
          {showCount && (
            <p className={cn(
              "text-muted-foreground",
              maxLength && charCount > maxLength * 0.9 && "text-warning",
              maxLength && charCount >= maxLength && "text-error"
            )}>
              {charCount}{maxLength && `/${maxLength}`}
            </p>
          )}
        </div>
      </div>
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea, textareaVariants }