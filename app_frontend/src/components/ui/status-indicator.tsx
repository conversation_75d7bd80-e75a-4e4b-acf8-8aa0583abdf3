import { cn } from "@/lib/utils"
import { HTMLAttributes } from "react"

export interface StatusIndicatorProps extends HTMLAttributes<HTMLDivElement> {
  status: "success" | "warning" | "error" | "info" | "default"
  size?: "sm" | "md" | "lg"
  label?: string
  pulse?: boolean
}

export function StatusIndicator({
  status,
  size = "md",
  label,
  pulse = false,
  className,
  ...props
}: StatusIndicatorProps) {
  const sizeClasses = {
    sm: "h-2 w-2",
    md: "h-3 w-3",
    lg: "h-4 w-4",
  }

  const statusClasses = {
    success: "bg-success",
    warning: "bg-warning",
    error: "bg-error",
    info: "bg-info",
    default: "bg-muted-foreground",
  }

  return (
    <div
      className={cn("flex items-center gap-2", className)}
      {...props}
    >
      <span className="relative flex">
        <span
          className={cn(
            "rounded-full",
            sizeClasses[size],
            statusClasses[status],
            {
              "animate-ping absolute inline-flex h-full w-full rounded-full opacity-75": pulse,
            }
          )}
        />
        <span
          className={cn(
            "relative inline-flex rounded-full",
            sizeClasses[size],
            statusClasses[status]
          )}
        />
      </span>
      {label && (
        <span className="text-sm text-muted-foreground">{label}</span>
      )}
    </div>
  )
}