import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const skeletonVariants = cva(
  "animate-pulse rounded-md bg-muted",
  {
    variants: {
      variant: {
        default: "bg-muted",
        light: "bg-surface-100", 
        dark: "bg-surface-200",
        shimmer: "bg-gradient-to-r from-muted via-surface-100 to-muted bg-[length:200%_100%] animate-[shimmer_2s_infinite]",
      }
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(skeletonVariants({ variant }), className)}
        {...props}
      />
    )
  }
)
Skeleton.displayName = "Skeleton"

// Skeleton presets for common UI patterns
export interface SkeletonTextProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Number of lines */
  lines?: number
  /** Variant style */
  variant?: "default" | "light" | "dark" | "shimmer"
  /** Custom line heights */
  lineHeights?: string[]
}

const SkeletonText = React.forwardRef<HTMLDivElement, SkeletonTextProps>(
  ({ className, lines = 3, variant = "default", lineHeights, ...props }, ref) => {
    const defaultHeights = ["h-4", "h-4", "h-3"]
    const heights = lineHeights || defaultHeights

    return (
      <div ref={ref} className={cn("space-y-3", className)} {...props}>
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton
            key={index}
            variant={variant}
            className={cn(
              heights[index] || "h-4",
              index === lines - 1 && "w-4/5" // Last line shorter
            )}
          />
        ))}
      </div>
    )
  }
)
SkeletonText.displayName = "SkeletonText"

export interface SkeletonCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Show avatar */
  showAvatar?: boolean
  /** Show image */
  showImage?: boolean
  /** Number of text lines */
  textLines?: number
  /** Variant style */
  variant?: "default" | "light" | "dark" | "shimmer"
  /** Image aspect ratio */
  imageAspect?: "square" | "video" | "wide"
}

const SkeletonCard = React.forwardRef<HTMLDivElement, SkeletonCardProps>(
  ({ 
    className, 
    showAvatar = false, 
    showImage = true, 
    textLines = 3, 
    variant = "default",
    imageAspect = "video",
    ...props 
  }, ref) => {
    const getImageClasses = () => {
      switch (imageAspect) {
        case "square": return "aspect-square"
        case "video": return "aspect-video"
        case "wide": return "aspect-[21/9]"
        default: return "aspect-video"
      }
    }

    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        {/* Image */}
        {showImage && (
          <Skeleton 
            variant={variant}
            className={cn("w-full rounded-lg", getImageClasses())}
          />
        )}
        
        <div className="space-y-3">
          {/* Avatar and title row */}
          {showAvatar && (
            <div className="flex items-center space-x-3">
              <Skeleton variant={variant} className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton variant={variant} className="h-4 w-1/4" />
                <Skeleton variant={variant} className="h-3 w-1/6" />
              </div>
            </div>
          )}
          
          {/* Text content */}
          <SkeletonText lines={textLines} variant={variant} />
        </div>
      </div>
    )
  }
)
SkeletonCard.displayName = "SkeletonCard"

export interface SkeletonListProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Number of items */
  items?: number
  /** Show avatar for each item */
  showAvatar?: boolean
  /** Show secondary text */
  showSecondary?: boolean
  /** Variant style */
  variant?: "default" | "light" | "dark" | "shimmer"
}

const SkeletonList = React.forwardRef<HTMLDivElement, SkeletonListProps>(
  ({ className, items = 5, showAvatar = true, showSecondary = true, variant = "default", ...props }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        {Array.from({ length: items }).map((_, index) => (
          <div key={index} className="flex items-center space-x-3">
            {showAvatar && (
              <Skeleton variant={variant} className="h-10 w-10 rounded-full shrink-0" />
            )}
            <div className="space-y-2 flex-1">
              <Skeleton variant={variant} className="h-4 w-3/4" />
              {showSecondary && (
                <Skeleton variant={variant} className="h-3 w-1/2" />
              )}
            </div>
            <Skeleton variant={variant} className="h-4 w-16 shrink-0" />
          </div>
        ))}
      </div>
    )
  }
)
SkeletonList.displayName = "SkeletonList"

export { 
  Skeleton, 
  SkeletonText, 
  SkeletonCard, 
  SkeletonList,
  skeletonVariants 
}
