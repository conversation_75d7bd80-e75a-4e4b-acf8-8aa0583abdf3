import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { TrendingUp, TrendingDown, Minus } from "lucide-react"
import { cn } from "@/lib/utils"

const statsCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-border hover:shadow-md",
        success: "border-green-200 bg-green-50",
        warning: "border-yellow-200 bg-yellow-50",
        error: "border-red-200 bg-red-50",
        info: "border-blue-200 bg-blue-50",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface StatsCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statsCardVariants> {
  /** Main statistic value */
  value: string | number
  /** Label/title for the statistic */
  label: string
  /** Optional description */
  description?: string
  /** Icon to display */
  icon?: React.ReactNode
  /** Trend direction and percentage */
  trend?: {
    direction: "up" | "down" | "neutral"
    value: number
    label?: string
  }
  /** Show loading state */
  loading?: boolean
  /** Click handler */
  onClick?: () => void
}

const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    value, 
    label, 
    description, 
    icon, 
    trend, 
    loading = false,
    onClick,
    ...props 
  }, ref) => {
    const getTrendIcon = () => {
      if (!trend) return null
      
      switch (trend.direction) {
        case "up":
          return <TrendingUp className="h-4 w-4 text-green-600" />
        case "down":
          return <TrendingDown className="h-4 w-4 text-red-600" />
        case "neutral":
          return <Minus className="h-4 w-4 text-muted-foreground" />
        default:
          return null
      }
    }

    const getTrendColor = () => {
      if (!trend) return ""
      
      switch (trend.direction) {
        case "up":
          return "text-green-600"
        case "down":
          return "text-red-600"
        case "neutral":
          return "text-muted-foreground"
        default:
          return ""
      }
    }

    if (loading) {
      return (
        <div
          ref={ref}
          className={cn(statsCardVariants({ variant, size }), className)}
          {...props}
        >
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              {icon && <div className="h-5 w-5 bg-muted animate-pulse rounded" />}
            </div>
            <div className="h-8 w-24 bg-muted animate-pulse rounded" />
            {description && <div className="h-3 w-32 bg-muted animate-pulse rounded" />}
            {trend && <div className="h-4 w-16 bg-muted animate-pulse rounded" />}
          </div>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(
          statsCardVariants({ variant, size }),
          onClick && "cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
          className
        )}
        onClick={onClick}
        {...props}
      >
        <div className="space-y-3">
          {/* Header with label and icon */}
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-muted-foreground">{label}</p>
            {icon && (
              <div className="flex items-center justify-center h-8 w-8 rounded-lg bg-primary/10 text-primary">
                {icon}
              </div>
            )}
          </div>

          {/* Main value */}
          <div className="space-y-1">
            <p className="text-2xl font-bold text-foreground">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>

          {/* Trend indicator */}
          {trend && (
            <div className="flex items-center gap-1">
              {getTrendIcon()}
              <span className={cn("text-sm font-medium", getTrendColor())}>
                {trend.value}%
              </span>
              {trend.label && (
                <span className="text-xs text-muted-foreground">
                  {trend.label}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }
)
StatsCard.displayName = "StatsCard"

export { StatsCard, statsCardVariants }