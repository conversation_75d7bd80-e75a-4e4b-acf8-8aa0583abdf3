import * as React from "react"
import { cn } from "@/lib/utils"

export interface FormFieldProps {
  children: React.ReactNode
  label?: string
  required?: boolean
  error?: string
  success?: string
  warning?: string
  helperText?: string
  className?: string
  labelClassName?: string
  id?: string
}

const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ 
    children, 
    label, 
    required, 
    error, 
    success, 
    warning, 
    helperText, 
    className,
    labelClassName,
    id,
    ...props 
  }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-2", className)} {...props}>
        {label && (
          <label 
            htmlFor={id}
            className={cn(
              "block text-sm font-medium text-foreground",
              required && "after:content-['*'] after:ml-0.5 after:text-error",
              labelClassName
            )}
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                id: id || child.props.id,
                'aria-invalid': error ? 'true' : 'false',
                'aria-describedby': error || success || warning || helperText ? `${id}-message` : undefined,
                ...child.props,
              } as any)
            }
            return child
          })}
        </div>

        {/* Message area */}
        {(error || success || warning || helperText) && (
          <div id={`${id}-message`} className="text-xs space-y-1">
            {error && (
              <p className="text-error flex items-center gap-1.5">
                <svg className="h-3 w-3 shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </p>
            )}
            
            {success && !error && (
              <p className="text-success flex items-center gap-1.5">
                <svg className="h-3 w-3 shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {success}
              </p>
            )}
            
            {warning && !error && !success && (
              <p className="text-warning flex items-center gap-1.5">
                <svg className="h-3 w-3 shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {warning}
              </p>
            )}
            
            {helperText && !error && !success && !warning && (
              <p className="text-muted-foreground">{helperText}</p>
            )}
          </div>
        )}
      </div>
    )
  }
)
FormField.displayName = "FormField"

export { FormField }