import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const quickActionsVariants = cva(
  "grid gap-4",
  {
    variants: {
      columns: {
        1: "grid-cols-1",
        2: "grid-cols-1 sm:grid-cols-2",
        3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
        4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
        auto: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
      },
      size: {
        sm: "gap-3",
        default: "gap-4",
        lg: "gap-6",
      }
    },
    defaultVariants: {
      columns: "auto",
      size: "default",
    },
  }
)

const quickActionItemVariants = cva(
  "group relative flex items-center gap-3 rounded-lg border bg-card p-4 text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",
  {
    variants: {
      variant: {
        default: "border-border hover:border-primary/20 hover:bg-primary/5",
        primary: "border-primary/20 bg-primary/5 hover:bg-primary/10",
        secondary: "border-secondary/20 bg-secondary/5 hover:bg-secondary/10",
        success: "border-green-200 bg-green-50 hover:bg-green-100",
        warning: "border-yellow-200 bg-yellow-50 hover:bg-yellow-100",
        error: "border-red-200 bg-red-50 hover:bg-red-100",
      },
      size: {
        sm: "p-3 text-sm",
        default: "p-4",
        lg: "p-6 text-lg",
      },
      orientation: {
        horizontal: "flex-row",
        vertical: "flex-col text-center",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      orientation: "horizontal",
    },
  }
)

export interface QuickActionItem {
  id: string
  title: string
  description?: string
  icon: React.ReactNode
  href?: string
  onClick?: () => void
  variant?: "default" | "primary" | "secondary" | "success" | "warning" | "error"
  disabled?: boolean
  badge?: string | number
  loading?: boolean
}

export interface QuickActionsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof quickActionsVariants> {
  /** Quick action items */
  items: QuickActionItem[]
  /** Orientation of action items */
  orientation?: "horizontal" | "vertical"
  /** Size of action items */
  itemSize?: "sm" | "default" | "lg"
  /** Loading state */
  loading?: boolean
  /** Number of skeleton items when loading */
  skeletonCount?: number
}

const QuickActions = React.forwardRef<HTMLDivElement, QuickActionsProps>(
  ({ 
    className, 
    columns, 
    size, 
    items, 
    orientation = "horizontal",
    itemSize = "default",
    loading = false,
    skeletonCount = 6,
    ...props 
  }, ref) => {
    if (loading) {
      return (
        <div 
          ref={ref} 
          className={cn(quickActionsVariants({ columns, size }), className)} 
          {...props}
        >
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <div
              key={index}
              className={cn(quickActionItemVariants({ 
                variant: "default", 
                size: itemSize, 
                orientation 
              }))}
            >
              <div className="h-6 w-6 bg-muted animate-pulse rounded" />
              <div className="flex-1 space-y-2">
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                <div className="h-3 w-32 bg-muted animate-pulse rounded" />
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div 
        ref={ref} 
        className={cn(quickActionsVariants({ columns, size }), className)} 
        {...props}
      >
        {items.map((item) => {
          const Component = item.href ? "a" : "button"
          
          return (
            <Component
              key={item.id}
              className={cn(
                quickActionItemVariants({ 
                  variant: item.variant || "default", 
                  size: itemSize, 
                  orientation 
                }),
                item.disabled && "opacity-50 cursor-not-allowed",
                !item.disabled && (item.href || item.onClick) && "cursor-pointer hover:scale-[1.02] active:scale-[0.98]"
              )}
              onClick={item.disabled ? undefined : item.onClick}
              disabled={item.disabled}
              {...(item.href ? { href: item.href } : {})}
            >
              {/* Icon with loading state */}
              <div className="relative flex-shrink-0">
                {item.loading ? (
                  <div className="h-6 w-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                ) : (
                  <div className={cn(
                    "flex items-center justify-center h-6 w-6",
                    orientation === "vertical" && "h-8 w-8"
                  )}>
                    {item.icon}
                  </div>
                )}
                
                {/* Badge */}
                {item.badge && !item.loading && (
                  <span className="absolute -top-1 -right-1 flex items-center justify-center h-4 w-4 text-xs font-bold text-white bg-primary rounded-full">
                    {item.badge}
                  </span>
                )}
              </div>

              {/* Content */}
              <div className={cn(
                "flex-1 text-left",
                orientation === "vertical" && "text-center space-y-1"
              )}>
                <h3 className="font-medium text-foreground group-hover:text-primary transition-colors">
                  {item.title}
                </h3>
                {item.description && (
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                )}
              </div>

              {/* Arrow indicator for links */}
              {(item.href || item.onClick) && !item.disabled && orientation === "horizontal" && (
                <svg 
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 5l7 7-7 7" 
                  />
                </svg>
              )}
            </Component>
          )
        })}
      </div>
    )
  }
)
QuickActions.displayName = "QuickActions"

export { QuickActions, quickActionsVariants, quickActionItemVariants }