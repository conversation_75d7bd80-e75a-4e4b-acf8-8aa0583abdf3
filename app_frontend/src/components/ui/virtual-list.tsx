"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react'
import { calculateVirtualItems, throttle } from '@/lib/performance'
import { cn } from '@/lib/utils'

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  height: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
  getItemKey?: (item: T, index: number) => string | number
  onScroll?: (scrollTop: number) => void
  loadMoreItems?: () => void
  hasNextPage?: boolean
  isLoading?: boolean
}

export function VirtualList<T>({
  items,
  itemHeight,
  height,
  renderItem,
  className,
  overscan = 5,
  getItemKey = (_, index) => index,
  onScroll,
  loadMoreItems,
  hasNextPage = false,
  isLoading = false,
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  // Throttled scroll handler
  const handleScroll = useMemo(
    () => throttle((e: React.UIEvent<HTMLDivElement>) => {
      const newScrollTop = e.currentTarget.scrollTop
      setScrollTop(newScrollTop)
      onScroll?.(newScrollTop)
      
      // Load more items when near bottom
      if (loadMoreItems && hasNextPage && !isLoading) {
        const { scrollHeight, clientHeight } = e.currentTarget
        if (newScrollTop + clientHeight >= scrollHeight - itemHeight * 5) {
          loadMoreItems()
        }
      }
    }, 16), // ~60fps
    [onScroll, loadMoreItems, hasNextPage, isLoading, itemHeight]
  )

  // Calculate visible items
  const virtualItems = useMemo(() => {
    return calculateVirtualItems(items, scrollTop, {
      itemHeight,
      containerHeight: height,
      overscan,
    })
  }, [items, scrollTop, itemHeight, height, overscan])

  // Auto-scroll to top when items change significantly
  useEffect(() => {
    if (scrollElementRef.current && items.length === 0) {
      scrollElementRef.current.scrollTop = 0
      setScrollTop(0)
    }
  }, [items.length])

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{ height }}
    >
      <div
        ref={scrollElementRef}
        className="h-full overflow-auto"
        onScroll={handleScroll}
      >
        <div
          style={{
            height: virtualItems.totalHeight,
            position: 'relative',
          }}
        >
          {virtualItems.visibleItems.map(({ index, item, offsetTop }) => (
            <div
              key={getItemKey(item, index)}
              style={{
                position: 'absolute',
                top: offsetTop,
                left: 0,
                right: 0,
                height: itemHeight,
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
          
          {/* Loading indicator at bottom */}
          {isLoading && (
            <div
              style={{
                position: 'absolute',
                top: virtualItems.totalHeight,
                left: 0,
                right: 0,
                height: itemHeight,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-muted-foreground">Loading...</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Grid variant for virtual scrolling
interface VirtualGridProps<T> {
  items: T[]
  itemWidth: number
  itemHeight: number
  columns: number
  height: number
  gap?: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  getItemKey?: (item: T, index: number) => string | number
}

export function VirtualGrid<T>({
  items,
  itemWidth,
  itemHeight,
  columns,
  height,
  gap = 16,
  renderItem,
  className,
  getItemKey = (_, index) => index,
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const rowHeight = itemHeight + gap
  const totalRows = Math.ceil(items.length / columns)

  const handleScroll = useMemo(
    () => throttle((e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop)
    }, 16),
    []
  )

  // Calculate visible rows
  const visibleRows = useMemo(() => {
    const overscan = 2
    const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - overscan)
    const endRow = Math.min(
      totalRows - 1,
      Math.ceil((scrollTop + height) / rowHeight) + overscan
    )

    const rows = []
    for (let row = startRow; row <= endRow; row++) {
      const startIndex = row * columns
      const endIndex = Math.min(startIndex + columns - 1, items.length - 1)
      
      if (startIndex <= endIndex) {
        rows.push({
          row,
          startIndex,
          endIndex,
          offsetTop: row * rowHeight,
          items: items.slice(startIndex, endIndex + 1),
        })
      }
    }

    return { rows, totalHeight: totalRows * rowHeight }
  }, [scrollTop, rowHeight, totalRows, height, columns, items])

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{ height }}
    >
      <div
        className="h-full overflow-auto"
        onScroll={handleScroll}
      >
        <div
          style={{
            height: visibleRows.totalHeight,
            position: 'relative',
          }}
        >
          {visibleRows.rows.map(({ row, startIndex, offsetTop, items: rowItems }) => (
            <div
              key={row}
              style={{
                position: 'absolute',
                top: offsetTop,
                left: 0,
                right: 0,
                height: itemHeight,
                display: 'flex',
                gap,
              }}
            >
              {rowItems.map((item, colIndex) => {
                const itemIndex = startIndex + colIndex
                return (
                  <div
                    key={getItemKey(item, itemIndex)}
                    style={{ width: itemWidth, height: itemHeight }}
                  >
                    {renderItem(item, itemIndex)}
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Infinite scroll hook
export function useInfiniteScroll(
  callback: () => void,
  hasNextPage: boolean,
  isLoading: boolean
) {
  const observerRef = useRef<IntersectionObserver | null>(null)
  const triggerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    if (isLoading || !hasNextPage) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          callback()
        }
      },
      { threshold: 1.0 }
    )

    if (triggerRef.current) {
      observerRef.current.observe(triggerRef.current)
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [callback, hasNextPage, isLoading])

  return triggerRef
}

export default VirtualList