'use client'

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const chartWrapperVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm",
  {
    variants: {
      variant: {
        default: "border-border",
        outlined: "border-2 border-border",
        elevated: "border-border shadow-md",
        flush: "border-0 shadow-none bg-transparent",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
      },
      height: {
        sm: "h-64",
        default: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
        auto: "h-auto",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      height: "default",
    },
  }
)

export interface ChartWrapperProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chartWrapperVariants> {
  /** Chart title */
  title?: string
  /** Chart description */
  description?: string
  /** Chart content */
  children: React.ReactNode
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string
  /** Empty state */
  empty?: boolean
  /** Empty state message */
  emptyMessage?: string
  /** Header actions */
  actions?: React.ReactNode
  /** Footer content */
  footer?: React.ReactNode
}

const ChartWrapper = React.forwardRef<HTMLDivElement, ChartWrapperProps>(
  ({ 
    className, 
    variant, 
    size, 
    height,
    title, 
    description, 
    children, 
    loading = false,
    error,
    empty = false,
    emptyMessage = "No data available",
    actions,
    footer,
    ...props 
  }, ref) => {
    const renderContent = () => {
      if (loading) {
        return (
          <div className="flex items-center justify-center h-full">
            <div className="space-y-4 text-center">
              <div className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-sm text-muted-foreground">Loading chart data...</p>
            </div>
          </div>
        )
      }

      if (error) {
        return (
          <div className="flex items-center justify-center h-full">
            <div className="space-y-2 text-center">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mx-auto">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-sm font-medium text-red-900">Failed to load chart</p>
              <p className="text-xs text-red-600">{error}</p>
            </div>
          </div>
        )
      }

      if (empty) {
        return (
          <div className="flex items-center justify-center h-full">
            <div className="space-y-2 text-center">
              <div className="h-12 w-12 rounded-full bg-surface-100 flex items-center justify-center mx-auto">
                <svg className="h-6 w-6 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p className="text-sm text-muted-foreground">{emptyMessage}</p>
            </div>
          </div>
        )
      }

      return children
    }

    return (
      <div 
        ref={ref} 
        className={cn(chartWrapperVariants({ variant, size }), className)} 
        {...props}
      >
        {/* Header */}
        {(title || description || actions) && (
          <div className="flex items-start justify-between mb-4">
            <div className="space-y-1">
              {title && (
                <h3 className="text-lg font-semibold text-foreground">{title}</h3>
              )}
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
            {actions && (
              <div className="flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
        )}

        {/* Chart content */}
        <div className={cn(
          "relative",
          height && chartWrapperVariants({ height })
        )}>
          {renderContent()}
        </div>

        {/* Footer */}
        {footer && (
          <div className="mt-4 pt-4 border-t border-border">
            {footer}
          </div>
        )}
      </div>
    )
  }
)
ChartWrapper.displayName = "ChartWrapper"

// Simple chart placeholder component for when no charting library is available
export interface ChartPlaceholderProps {
  type: "line" | "bar" | "pie" | "area" | "scatter"
  data?: any[]
  className?: string
}

const ChartPlaceholder = React.forwardRef<HTMLDivElement, ChartPlaceholderProps>(
  ({ type, data, className }, ref) => {
    const getChartIcon = () => {
      switch (type) {
        case "line":
          return (
            <svg viewBox="0 0 24 24" className="h-16 w-16 text-primary/20">
              <path fill="currentColor" d="M3 3v18h18V3H3zm16 16H5V5h14v14z"/>
              <path fill="currentColor" d="M7 14l2-2 2 2 4-4v2l-4 4-2-2-2 2z"/>
            </svg>
          )
        case "bar":
          return (
            <svg viewBox="0 0 24 24" className="h-16 w-16 text-primary/20">
              <path fill="currentColor" d="M7 17h2v-4H7v4zm4 0h2V7h-2v10zm4 0h2v-7h-2v7z"/>
            </svg>
          )
        case "pie":
          return (
            <svg viewBox="0 0 24 24" className="h-16 w-16 text-primary/20">
              <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 18c-4.41 0-8-3.59-8-8s3.59-8 8-8v8h8c0 4.41-3.59 8-8 8z"/>
            </svg>
          )
        case "area":
          return (
            <svg viewBox="0 0 24 24" className="h-16 w-16 text-primary/20">
              <path fill="currentColor" d="M3 3v18h18V3H3zm16 16H5V5h14v14z"/>
              <path fill="currentColor" d="M7 14l2-2 2 2 4-4v6H7v-2z"/>
            </svg>
          )
        default:
          return (
            <svg viewBox="0 0 24 24" className="h-16 w-16 text-primary/20">
              <path fill="currentColor" d="M3 3v18h18V3H3zm16 16H5V5h14v14z"/>
            </svg>
          )
      }
    }

    return (
      <div 
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center h-full space-y-2 text-center",
          className
        )}
      >
        {getChartIcon()}
        <p className="text-sm font-medium text-muted-foreground capitalize">{type} Chart</p>
        {data && (
          <p className="text-xs text-muted-foreground">{data.length} data points</p>
        )}
      </div>
    )
  }
)
ChartPlaceholder.displayName = "ChartPlaceholder"

export { ChartWrapper, ChartPlaceholder, chartWrapperVariants }