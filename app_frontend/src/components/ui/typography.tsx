import { cn } from "@/lib/utils"
import { VariantProps, cva } from "class-variance-authority"
import { HTMLAttributes, forwardRef } from "react"

const headingVariants = cva(
  "font-semibold text-accent tracking-tight",
  {
    variants: {
      variant: {
        h1: "text-4xl md:text-5xl lg:text-6xl",
        h2: "text-3xl md:text-4xl lg:text-5xl",
        h3: "text-2xl md:text-3xl lg:text-4xl",
        h4: "text-xl md:text-2xl lg:text-3xl",
        h5: "text-lg md:text-xl lg:text-2xl",
        h6: "text-base md:text-lg lg:text-xl",
      },
    },
    defaultVariants: {
      variant: "h1",
    },
  }
)

const textVariants = cva(
  "",
  {
    variants: {
      variant: {
        body: "text-base text-foreground",
        lead: "text-lg text-muted-foreground",
        small: "text-sm text-foreground",
        muted: "text-sm text-muted-foreground",
        caption: "text-xs text-muted-foreground",
      },
    },
    defaultVariants: {
      variant: "body",
    },
  }
)

export interface HeadingProps
  extends HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof headingVariants> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6"
}

export const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(
  ({ className, variant, as, ...props }, ref) => {
    const Comp = as || variant || "h1"
    return (
      <Comp
        className={cn(headingVariants({ variant: variant || as }), className)}
        ref={ref}
        {...props}
      />
    )
  }
)
Heading.displayName = "Heading"

export interface TextProps
  extends HTMLAttributes<HTMLParagraphElement>,
    VariantProps<typeof textVariants> {
  as?: "p" | "span" | "div"
}

export const Text = forwardRef<HTMLParagraphElement, TextProps>(
  ({ className, variant, as: Comp = "p", ...props }, ref) => {
    return (
      <Comp
        className={cn(textVariants({ variant }), className)}
        ref={ref}
        {...props}
      />
    )
  }
)
Text.displayName = "Text"