import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"
import { HTMLAttributes } from "react"
import { Button } from "./button"
import { Heading, Text } from "./typography"

export interface EmptyStateProps extends HTMLAttributes<HTMLDivElement> {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className,
  ...props
}: EmptyStateProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-12 px-4 text-center",
        className
      )}
      {...props}
    >
      {Icon && (
        <div className="rounded-full bg-surface p-3 mb-4">
          <Icon className="h-6 w-6 text-muted-foreground" />
        </div>
      )}
      <Heading as="h3" variant="h5" className="mb-2">
        {title}
      </Heading>
      {description && (
        <Text variant="muted" className="mb-6 max-w-sm">
          {description}
        </Text>
      )}
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  )
}