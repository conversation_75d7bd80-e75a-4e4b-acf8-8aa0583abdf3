'use client'

import React, { lazy } from 'react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

// Lazy loading wrapper with fallback
function withLazyLoading<T extends object>(
  componentImport: () => Promise<{ default: React.ComponentType<T> }>,
  fallback?: React.ReactNode
) {
  const LazyComponent = lazy(componentImport)
  
  return function WrappedComponent(props: T) {
    return (
      <React.Suspense fallback={fallback || <LoadingSpinner size="lg" />}>
        <LazyComponent {...props} />
      </React.Suspense>
    )
  }
}

// NOTE: Next.js 13+ app directory pages are server components by default
// and cannot be lazy-loaded with React.lazy(). Only client components can be lazy-loaded.

// Chart components - can be heavy due to recharts
export const LazyChartWrapper = withLazyLoading(
  () => import('@/components/ui/chart-wrapper').then(mod => ({ default: mod.ChartWrapper })),
  <div className="h-64 bg-muted rounded-lg animate-pulse" />
)

// Heavy UI components that can benefit from lazy loading
export const LazyNewBookWizard = withLazyLoading(
  () => import('@/components/books/new-book-wizard').then(mod => ({ default: mod.NewBookWizard })),
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" />
  </div>
)

export const LazyVirtualList = withLazyLoading(
  () => import('@/components/ui/virtual-list').then(mod => ({ default: mod.VirtualList })),
  <div className="flex items-center justify-center min-h-[200px]">
    <LoadingSpinner size="lg" />
  </div>
)

export const LazyNewBookModal = withLazyLoading(
  () => import('@/components/NewBookModal').then(mod => ({ default: mod.default })),
  null // No fallback for modals
)

// Dashboard components that might be heavy
export const LazyPerformanceDashboard = withLazyLoading(
  () => import('@/components/PerformanceDashboard').then(mod => ({ default: mod.default })),
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" />
  </div>
)

export const LazyPredictionDashboard = withLazyLoading(
  () => import('@/components/PredictionDashboard').then(mod => ({ default: mod.default })),
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" />
  </div>
)

export const LazyVERLMonitorDashboard = withLazyLoading(
  () => import('@/components/VERLMonitorDashboard').then(mod => ({ default: mod.default })),
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" />
  </div>
)

// Export utilities for dynamic imports
export const loadComponent = async (componentPath: string) => {
  try {
    const importedModule = await import(componentPath)
    return importedModule.default
  } catch (error) {
    console.error(`Failed to load component: ${componentPath}`, error)
    return null
  }
}

// Preload critical components
export const preloadCriticalComponents = () => {
  // Preload client components that are likely to be needed
  if (typeof window !== 'undefined') {
    // Preload after initial render
    setTimeout(() => {
      import('@/components/ui/chart-wrapper')
      import('@/components/books/new-book-wizard')
    }, 2000)
  }
}

export default {
  LazyChartWrapper,
  LazyNewBookWizard,
  LazyVirtualList,
  LazyNewBookModal,
  LazyPerformanceDashboard,
  LazyPredictionDashboard,
  LazyVERLMonitorDashboard,
  loadComponent,
  preloadCriticalComponents,
}