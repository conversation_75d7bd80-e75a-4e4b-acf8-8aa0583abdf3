import { usePublications } from '@/hooks/api/use-publications';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { BookOpen, DollarSign, Download, Calendar } from 'lucide-react';

interface PublicationCardProps {
  className?: string;
  limit?: number;
}

export default function PublicationCard({ className, limit = 3 }: PublicationCardProps) {
  // Use real API data with filters for recent publications
  const { data: publicationsData, isLoading, error } = usePublications({
    status: 'published',
    sort: 'created_at',
    limit: limit
  });

  const publications = publicationsData?.publications || [];

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            <CardTitle>Recent Publications</CardTitle>
          </div>
          <CardDescription>Your latest published works</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <LoadingSpinner />
            <div className="animate-pulse space-y-3">
              {[...Array(limit)].map((_, i) => (
                <div key={i} className="border rounded-lg p-4">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            <CardTitle>Recent Publications</CardTitle>
          </div>
          <CardDescription>Unable to load publications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Failed to load publications</p>
            <p className="text-xs">Please try refreshing the page</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'published': return 'success';
      case 'pending': return 'warning';
      case 'draft': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-primary" />
          <CardTitle>Recent Publications</CardTitle>
        </div>
        <CardDescription>Your latest published works</CardDescription>
      </CardHeader>
      <CardContent>
        {publications.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No publications yet</p>
            <p className="text-xs">Start by creating your first book</p>
          </div>
        ) : (
          <div className="space-y-4">
            {publications.map((pub) => (
              <div key={pub.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <h4 className="font-medium text-foreground line-clamp-1">{pub.title}</h4>
                  <Badge variant={getStatusVariant(pub.status)} className="text-xs">
                    {pub.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <DollarSign className="h-3 w-3" />
                    <span className="font-medium">Revenue:</span> ${pub.revenue.toFixed(2)}
                  </div>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Download className="h-3 w-3" />
                    <span className="font-medium">Sales:</span> {pub.sales || 0}
                  </div>
                  {pub.published_date && (
                    <div className="col-span-2 flex items-center gap-1 text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span className="font-medium">Published:</span> {new Date(pub.published_date).toLocaleDateString()}
                    </div>
                  )}
                </div>
                
                {pub.platform && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {pub.platform}
                    </Badge>
                    {pub.rating && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <span>⭐</span>
                        <span>{pub.rating.toFixed(1)}</span>
                        {pub.review_count && (
                          <span>({pub.review_count} reviews)</span>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
        
        {publicationsData?.total && publicationsData.total > limit && (
          <div className="mt-4 pt-4 border-t">
            <button className="text-sm text-primary hover:text-primary/80 font-medium">
              View all {publicationsData.total} publications →
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}