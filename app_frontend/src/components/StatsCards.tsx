import { StatsCard } from '@/components/ui/stats-card';
import { BookO<PERSON>, CheckCircle, DollarSign, TrendingUp } from 'lucide-react';

interface Book {
  id: string;
  title: string;
  status: string;
  revenue?: number;
  sales?: number;
  [key: string]: any;
}

interface StatsCardsProps {
  books: Book[];
  className?: string;
}

export default function StatsCards({ books, className }: StatsCardsProps) {
  // Calculate stats from real book data
  const totalBooks = books.length;
  const publishedBooks = books.filter(book => book.status === 'published').length;
  const totalRevenue = books.reduce((sum, book) => sum + (book.revenue || 0), 0);
  const totalSales = books.reduce((sum, book) => sum + (book.sales || 0), 0);
  
  // Calculate monthly growth (simplified - compare published books this month vs last month)
  const now = new Date();
  const thisMonth = now.getMonth();
  const thisYear = now.getFullYear();
  
  const booksThisMonth = books.filter(book => {
    if (book.status !== 'published' || !book.updated_at) return false;
    const bookDate = new Date(book.updated_at);
    return bookDate.getMonth() === thisMonth && bookDate.getFullYear() === thisYear;
  }).length;
  
  const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
  const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;
  
  const booksLastMonth = books.filter(book => {
    if (book.status !== 'published' || !book.updated_at) return false;
    const bookDate = new Date(book.updated_at);
    return bookDate.getMonth() === lastMonth && bookDate.getFullYear() === lastMonthYear;
  }).length;
  
  const monthlyGrowth = booksLastMonth > 0 ? ((booksThisMonth - booksLastMonth) / booksLastMonth) * 100 : 0;

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      <StatsCard
        value={totalBooks.toString()}
        label="Total Books"
        description="Your book collection"
        icon={<BookOpen className="h-5 w-5" />}
        trend={monthlyGrowth !== 0 ? {
          direction: monthlyGrowth > 0 ? "up" : "down",
          value: Math.abs(Math.round(monthlyGrowth)),
          label: "vs last month"
        } : undefined}
      />
      
      <StatsCard
        value={publishedBooks.toString()}
        label="Published"
        description="Available to readers"
        icon={<CheckCircle className="h-5 w-5" />}
        variant="success"
      />
      
      <StatsCard
        value={`$${totalRevenue.toFixed(2)}`}
        label="Total Revenue"
        description="Lifetime earnings"
        icon={<DollarSign className="h-5 w-5" />}
        variant="info"
      />
      
      <StatsCard
        value={totalSales.toString()}
        label="Total Sales"
        description="Books sold"
        icon={<TrendingUp className="h-5 w-5" />}
        trend={booksThisMonth > booksLastMonth ? {
          direction: "up",
          value: booksThisMonth,
          label: "this month"
        } : undefined}
        variant="warning"
      />
    </div>
  );
}