'use client'

//  PredictionDashboard.tsx

import React, { useState, useEffect } from "react";
import {
  TrendingUp,
  DollarSign,
  Target,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";

interface PredictionResult {
  prediction_id: number;
  predictions: {
    sales_30d: number;
    revenue_30d: number;
    sales_90d: number;
    revenue_90d: number;
    success_probability: number;
  };
  confidence: number;
  risk_assessment: {
    risk_level: string;
    risk_score: number;
    identified_risks: string[];
  };
  recommendations: Array<{
    type: string;
    priority: string;
    title: string;
    description: string;
  }>;
  price_optimization: {
    recommended_price: number;
    market_average: number;
    pricing_strategy: string;
  };
}

const PredictionDashboard: React.FC<{ bookId: number }> = ({ bookId }) => {
  const [prediction, setPrediction] = useState<PredictionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [targetPrice, setTargetPrice] = useState(4.99);

  const generatePrediction = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/predictions/books/${bookId}/predict`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ target_price: targetPrice }),
      });
      const data = await response.json();
      setPrediction(data);
    } catch (error) {
      console.error("Prediction failed:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!prediction) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            📈 Sales Prediction Engine
          </h2>
          <p className="text-gray-600 mb-6">
            Get AI-powered predictions for your book&apos;s performance
          </p>

          <div className="max-w-md mx-auto space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Price
              </label>
              <input
                type="number"
                value={targetPrice}
                onChange={(e) => setTargetPrice(parseFloat(e.target.value))}
                step="0.01"
                min="0.99"
                max="99.99"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <button
              onClick={generatePrediction}
              disabled={loading}
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? "Generating Prediction..." : "🔮 Predict Performance"}
            </button>
          </div>
        </div>
      </div>
    );
  }

  const {
    predictions,
    confidence,
    risk_assessment,
    recommendations,
    price_optimization,
  } = prediction;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">
          📊 Performance Prediction
        </h2>
        <div className="mt-2 flex items-center justify-center space-x-4">
          <div
            className={`px-3 py-1 rounded-full text-sm font-medium ${
              confidence > 0.8
                ? "bg-green-100 text-green-800"
                : confidence > 0.6
                  ? "bg-yellow-100 text-yellow-800"
                  : "bg-red-100 text-red-800"
            }`}
          >
            {(confidence * 100).toFixed(0)}% Confidence
          </div>
          <div
            className={`px-3 py-1 rounded-full text-sm font-medium ${
              predictions.success_probability > 0.7
                ? "bg-green-100 text-green-800"
                : predictions.success_probability > 0.4
                  ? "bg-yellow-100 text-yellow-800"
                  : "bg-red-100 text-red-800"
            }`}
          >
            {(predictions.success_probability * 100).toFixed(0)}% Success
            Probability
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="30-Day Sales"
          value={predictions.sales_30d.toString()}
          icon={<TrendingUp className="h-6 w-6" />}
          color="blue"
        />
        <MetricCard
          title="30-Day Revenue"
          value={`$${predictions.revenue_30d.toFixed(2)}`}
          icon={<DollarSign className="h-6 w-6" />}
          color="green"
        />
        <MetricCard
          title="90-Day Sales"
          value={predictions.sales_90d.toString()}
          icon={<Target className="h-6 w-6" />}
          color="purple"
        />
        <MetricCard
          title="Recommended Price"
          value={`$${price_optimization.recommended_price}`}
          icon={<DollarSign className="h-6 w-6" />}
          color="orange"
        />
      </div>

      {/* Performance Chart */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Projected Performance</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={[
                {
                  period: "30 Days",
                  sales: predictions.sales_30d,
                  revenue: predictions.revenue_30d,
                },
                {
                  period: "90 Days",
                  sales: predictions.sales_90d,
                  revenue: predictions.revenue_90d,
                },
              ]}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="sales" fill="#3B82F6" name="Sales" />
              <Bar dataKey="revenue" fill="#10B981" name="Revenue ($)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center space-x-2 mb-4">
          <AlertTriangle
            className={`h-5 w-5 ${
              risk_assessment.risk_level === "low"
                ? "text-green-500"
                : risk_assessment.risk_level === "medium"
                  ? "text-yellow-500"
                  : "text-red-500"
            }`}
          />
          <h3 className="text-lg font-medium">Risk Assessment</h3>
          <div
            className={`px-2 py-1 rounded text-sm font-medium ${
              risk_assessment.risk_level === "low"
                ? "bg-green-100 text-green-800"
                : risk_assessment.risk_level === "medium"
                  ? "bg-yellow-100 text-yellow-800"
                  : "bg-red-100 text-red-800"
            }`}
          >
            {risk_assessment.risk_level.toUpperCase()} RISK
          </div>
        </div>

        {risk_assessment.identified_risks.length > 0 && (
          <div className="space-y-2">
            {risk_assessment.identified_risks.map((risk, index) => (
              <div key={index} className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                <span className="text-sm text-gray-700">{risk}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recommendations */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">💡 Recommendations</h3>
        <div className="space-y-4">
          {recommendations.map((rec, index) => (
            <div key={index} className="border-l-4 border-blue-500 pl-4">
              <div className="flex items-center space-x-2 mb-1">
                <CheckCircle className="h-4 w-4 text-blue-500" />
                <span className="font-medium text-gray-900">{rec.title}</span>
                <span
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    rec.priority === "high"
                      ? "bg-red-100 text-red-800"
                      : rec.priority === "medium"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {rec.priority}
                </span>
              </div>
              <p className="text-sm text-gray-600">{rec.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Price Optimization */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">💰 Price Optimization</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              ${price_optimization.recommended_price}
            </div>
            <div className="text-sm text-gray-600">Recommended Price</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              ${price_optimization.market_average}
            </div>
            <div className="text-sm text-gray-600">Market Average</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600 capitalize">
              {price_optimization.pricing_strategy}
            </div>
            <div className="text-sm text-gray-600">Strategy</div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-4">
        <button
          onClick={() => setPrediction(null)}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          New Prediction
        </button>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
          Apply Recommendations
        </button>
        <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
          Set Recommended Price
        </button>
      </div>
    </div>
  );
};

interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  color,
}) => {
  const colorClasses: { [key: string]: string } = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    purple: "text-purple-600 bg-purple-100",
    orange: "text-orange-600 bg-orange-100",
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>{icon}</div>
      </div>
    </div>
  );
};

export default PredictionDashboard;