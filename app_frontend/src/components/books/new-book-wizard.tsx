"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { TopicSelectionStep } from "./wizard-steps/topic-selection-step"
import { ContentGenerationStep } from "./wizard-steps/content-generation-step"
import { CoverDesignStep } from "./wizard-steps/cover-design-step"
import { MetadataStep } from "./wizard-steps/metadata-step"
import { ReviewStep } from "./wizard-steps/review-step"
import { GenerationProgressStep } from "./wizard-steps/generation-progress-step"
import { CheckCircle, Circle } from "lucide-react"

export interface BookCreationData {
  // Step 1: Topic Selection
  topic: string
  genre: string
  targetAudience: string
  keywords: string[]
  industryFocus?: string[]
  trendData?: any
  
  // Step 2: Content Generation
  outline: string[]
  tone: string
  length: "short" | "medium" | "long"
  chapterCount: number
  
  // Step 3: Cover Design
  coverStyle: string
  coverColors: string[]
  coverText: string
  selectedCover?: string
  
  // Step 4: Metadata
  title: string
  description: string
  isbn?: string
  price: number
  categories: string[]
  language: string
  
  // Generation tracking
  generationStatus: "pending" | "generating" | "completed" | "failed"
  generatedContent?: {
    manuscript: string
    coverUrl: string
    metadata: any
  }
}

const steps = [
  { id: 1, name: "Topic", description: "Choose your book topic and niche" },
  { id: 2, name: "Content", description: "Configure content generation" },
  { id: 3, name: "Cover", description: "Design your book cover" },
  { id: 4, name: "Details", description: "Add metadata and pricing" },
  { id: 5, name: "Generate", description: "AI generates your book" },
  { id: 6, name: "Review", description: "Review and finalize" },
]

interface NewBookWizardProps {
  onComplete: (bookId: string) => void
  onCancel: () => void
}

export function NewBookWizard({ onComplete, onCancel }: NewBookWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [bookData, setBookData] = useState<Partial<BookCreationData>>({
    generationStatus: "pending",
  })
  const [isGenerating, setIsGenerating] = useState(false)

  const progress = ((currentStep - 1) / (steps.length - 1)) * 100

  const updateBookData = (stepData: Partial<BookCreationData>) => {
    setBookData(prev => ({ ...prev, ...stepData }))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepNumber: number) => {
    // Only allow going to previous steps or current step
    if (stepNumber <= currentStep) {
      setCurrentStep(stepNumber)
    }
  }

  const handleStartGeneration = async () => {
    setIsGenerating(true)
    setBookData(prev => ({ ...prev, generationStatus: "generating" }))
    
    try {
      // Simulate API call to start book generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock successful generation
      const mockGeneratedContent = {
        manuscript: "Generated manuscript content...",
        coverUrl: "/images/generated-cover.jpg",
        metadata: { ...bookData },
      }
      
      setBookData(prev => ({
        ...prev,
        generationStatus: "completed",
        generatedContent: mockGeneratedContent,
      }))
      
      setCurrentStep(6) // Move to review step
    } catch (error) {
      setBookData(prev => ({ ...prev, generationStatus: "failed" }))
    } finally {
      setIsGenerating(false)
    }
  }

  const handleComplete = async () => {
    try {
      // Save book to database
      const bookId = "new-book-id" // Mock ID
      onComplete(bookId)
    } catch (error) {
      console.error("Failed to create book:", error)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <TopicSelectionStep
            data={bookData}
            onUpdate={updateBookData}
            onNext={handleNext}
            onCancel={onCancel}
          />
        )
      case 2:
        return (
          <ContentGenerationStep
            data={bookData}
            onUpdate={updateBookData}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        )
      case 3:
        return (
          <CoverDesignStep
            data={bookData}
            onUpdate={updateBookData}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        )
      case 4:
        return (
          <MetadataStep
            data={bookData}
            onUpdate={updateBookData}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        )
      case 5:
        return (
          <GenerationProgressStep
            data={bookData}
            isGenerating={isGenerating}
            onStartGeneration={handleStartGeneration}
            onPrevious={handlePrevious}
          />
        )
      case 6:
        return (
          <ReviewStep
            data={bookData}
            onComplete={handleComplete}
            onPrevious={handlePrevious}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-8">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <div>
              <CardTitle>Create Your Book</CardTitle>
              <CardDescription>
                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}
              </CardDescription>
            </div>
            <Badge variant="outline" className="px-3 py-1">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="h-2" />
        </CardHeader>
      </Card>

      {/* Step Navigation */}
      <div className="flex items-center justify-between space-x-4 overflow-x-auto pb-4">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center flex-shrink-0">
            <button
              onClick={() => handleStepClick(step.id)}
              disabled={step.id > currentStep}
              className={`flex items-center gap-3 p-3 rounded-lg transition-colors min-w-0 ${
                step.id === currentStep
                  ? "bg-primary text-primary-foreground"
                  : step.id < currentStep
                  ? "bg-primary/10 text-primary hover:bg-primary/20"
                  : "bg-muted text-muted-foreground cursor-not-allowed"
              }`}
            >
              {step.id < currentStep ? (
                <CheckCircle className="h-5 w-5 flex-shrink-0" />
              ) : (
                <Circle className="h-5 w-5 flex-shrink-0" />
              )}
              <div className="text-left">
                <div className="font-medium text-sm">{step.name}</div>
                <div className="text-xs opacity-75 hidden sm:block">
                  {step.description}
                </div>
              </div>
            </button>
            {index < steps.length - 1 && (
              <Separator orientation="horizontal" className="w-8 mx-2" />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div className="min-h-[500px]">
        {renderStepContent()}
      </div>
    </div>
  )
}