import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Grid } from "@/components/ui/grid"
import {
  BookOpen,
  Upload,
  DollarSign,
  TrendingUp,
  Clock,
  Eye,
} from "lucide-react"
import { Book } from "./book-card"

interface StatsCardsProps {
  books: Book[]
}

export function StatsCards({ books }: StatsCardsProps) {
  // Calculate stats
  const totalBooks = books.length
  const publishedBooks = books.filter(book => book.status === "published").length
  const draftBooks = books.filter(book => book.status === "draft").length
  const generatingBooks = books.filter(book => book.status === "generating").length
  
  const totalRevenue = books.reduce((sum, book) => sum + book.revenue, 0)
  const totalSales = books.reduce((sum, book) => sum + book.sales, 0)
  
  const avgRating = books.length > 0 
    ? books.filter(book => book.rating > 0).reduce((sum, book) => sum + book.rating, 0) / 
      books.filter(book => book.rating > 0).length
    : 0

  const recentBooks = books.filter(book => {
    const createdDate = new Date(book.createdAt)
    const weekAgo = new Date()
    weekAgo.setDate(weekAgo.getDate() - 7)
    return createdDate >= weekAgo
  }).length

  const stats = [
    {
      title: "Total Books",
      value: totalBooks.toString(),
      description: `${publishedBooks} published, ${draftBooks} drafts`,
      icon: BookOpen,
      trend: recentBooks > 0 ? `+${recentBooks} this week` : "No new books this week",
      trendUp: recentBooks > 0,
    },
    {
      title: "Published",
      value: publishedBooks.toString(),
      description: `${((publishedBooks / Math.max(totalBooks, 1)) * 100).toFixed(0)}% of total`,
      icon: Upload,
      trend: generatingBooks > 0 ? `${generatingBooks} generating` : "Ready to publish",
      trendUp: generatingBooks > 0,
    },
    {
      title: "Total Revenue",
      value: `$${totalRevenue.toFixed(2)}`,
      description: `From ${totalSales} sales`,
      icon: DollarSign,
      trend: totalSales > 0 ? `$${(totalRevenue / Math.max(totalSales, 1)).toFixed(2)} avg per sale` : "No sales yet",
      trendUp: totalRevenue > 0,
    },
    {
      title: "Average Rating",
      value: avgRating > 0 ? avgRating.toFixed(1) : "N/A",
      description: avgRating > 0 ? "Across published books" : "No ratings yet",
      icon: TrendingUp,
      trend: avgRating > 0 ? (avgRating >= 4 ? "Excellent!" : avgRating >= 3.5 ? "Good" : "Needs improvement") : "Publish to get ratings",
      trendUp: avgRating >= 4,
    },
  ]

  return (
    <div className="mb-8">
      <Grid cols={4} gap={6} className="grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
                <div className="flex items-center mt-2">
                  <Badge 
                    variant={stat.trendUp ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {stat.trendUp ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <Clock className="h-3 w-3 mr-1" />
                    )}
                    {stat.trend}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </Grid>
    </div>
  )
}