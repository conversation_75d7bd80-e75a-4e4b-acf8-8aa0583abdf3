import { render, screen, fireEvent } from '@testing-library/react'
import { BookCard, Book } from '../book-card'

const mockBook: Book = {
  id: '1',
  title: 'Test Book',
  description: 'This is a test book description',
  status: 'published',
  genre: 'Technology',
  pages: 150,
  chapters: 8,
  createdAt: '2024-01-15',
  updatedAt: '2024-01-20',
  coverUrl: '/test-cover.jpg',
  revenue: 299.99,
  sales: 25,
  rating: 4.5,
}

const mockOnAction = jest.fn()

describe('BookCard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders book information correctly', () => {
    render(<BookCard book={mockBook} onAction={mockOnAction} />)
    
    expect(screen.getByText('Test Book')).toBeInTheDocument()
    expect(screen.getByText('This is a test book description')).toBeInTheDocument()
    expect(screen.getByText('Technology')).toBeInTheDocument()
    expect(screen.getByText('150 pages')).toBeInTheDocument()
    expect(screen.getByText('8 chapters')).toBeInTheDocument()
  })

  it('shows published status and revenue for published books', () => {
    render(<BookCard book={mockBook} onAction={mockOnAction} />)
    
    expect(screen.getByText('Published')).toBeInTheDocument()
    expect(screen.getByText('$299.99')).toBeInTheDocument()
    expect(screen.getByText('25 sales')).toBeInTheDocument()
    expect(screen.getByText('4.5')).toBeInTheDocument()
  })

  it('shows draft status for draft books', () => {
    const draftBook = { ...mockBook, status: 'draft' as const, revenue: 0, sales: 0 }
    render(<BookCard book={draftBook} onAction={mockOnAction} />)
    
    expect(screen.getByText('Draft')).toBeInTheDocument()
    expect(screen.queryByText('$299.99')).not.toBeInTheDocument()
  })

  it('calls onAction when view button is clicked', () => {
    render(<BookCard book={mockBook} onAction={mockOnAction} />)
    
    const viewButton = screen.getByRole('button', { name: /view/i })
    fireEvent.click(viewButton)
    
    // Note: This tests the link, not the dropdown action
    expect(viewButton).toBeInTheDocument()
  })

  it('formats dates correctly', () => {
    render(<BookCard book={mockBook} onAction={mockOnAction} />)
    
    expect(screen.getByText(/Updated Jan 20, 2024/)).toBeInTheDocument()
  })

  it('shows generating status with pulse indicator', () => {
    const generatingBook = { ...mockBook, status: 'generating' as const }
    render(<BookCard book={generatingBook} onAction={mockOnAction} />)
    
    expect(screen.getByText('Generating')).toBeInTheDocument()
  })
})