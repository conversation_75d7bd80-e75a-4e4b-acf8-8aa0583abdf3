"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { 
  Zap, 
  CheckCircle, 
  Clock, 
  Brain, 
  FileText, 
  Image, 
  BookOpen,
  AlertCircle,
  Pause,
  Play,
  RotateCcw
} from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

interface GenerationProgressStepProps {
  data: Partial<BookCreationData>
  isGenerating: boolean
  onStartGeneration: () => void
  onPrevious: () => void
}

interface GenerationStep {
  id: string
  name: string
  description: string
  status: "pending" | "running" | "completed" | "failed"
  progress: number
  estimatedTime: string
  icon: React.ComponentType<{ className?: string }>
}

export function GenerationProgressStep({ 
  data, 
  isGenerating, 
  onStartGeneration, 
  onPrevious 
}: GenerationProgressStepProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState("12-15 minutes")

  const [generationSteps, setGenerationSteps] = useState<GenerationStep[]>([
    {
      id: "outline",
      name: "Content Outline",
      description: "Creating detailed chapter outline and structure",
      status: "pending",
      progress: 0,
      estimatedTime: "2-3 min",
      icon: FileText,
    },
    {
      id: "content",
      name: "Content Generation",
      description: "Writing chapters with AI-powered content creation",
      status: "pending",
      progress: 0,
      estimatedTime: "8-10 min",
      icon: Brain,
    },
    {
      id: "cover",
      name: "Cover Creation",
      description: "Designing professional book cover",
      status: "pending",
      progress: 0,
      estimatedTime: "1-2 min",
      icon: Image,
    },
    {
      id: "formatting",
      name: "Formatting & Layout",
      description: "Applying professional formatting and layout",
      status: "pending",
      progress: 0,
      estimatedTime: "1-2 min",
      icon: BookOpen,
    },
  ])

  // Simulate generation progress
  useEffect(() => {
    if (!isGenerating || isPaused) return

    const interval = setInterval(() => {
      setGenerationSteps(prev => {
        const updated = [...prev]
        const currentStep = updated[currentStepIndex]

        if (currentStep && currentStep.status !== "completed") {
          if (currentStep.status === "pending") {
            currentStep.status = "running"
          }

          // Increase progress
          currentStep.progress = Math.min(currentStep.progress + Math.random() * 15 + 5, 100)

          // Mark as completed when progress reaches 100
          if (currentStep.progress >= 100) {
            currentStep.status = "completed"
            currentStep.progress = 100
            
            // Move to next step
            if (currentStepIndex < updated.length - 1) {
              setCurrentStepIndex(currentStepIndex + 1)
            }
          }
        }

        return updated
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isGenerating, currentStepIndex, isPaused])

  const getTotalProgress = () => {
    const completedSteps = generationSteps.filter(step => step.status === "completed").length
    const currentProgress = generationSteps[currentStepIndex]?.progress || 0
    return ((completedSteps * 100) + currentProgress) / generationSteps.length
  }

  const getStatusIcon = (step: GenerationStep) => {
    switch (step.status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "running":
        return <LoadingSpinner size="sm" />
      case "failed":
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }

  const getStatusColor = (step: GenerationStep) => {
    switch (step.status) {
      case "completed":
        return "border-green-200 bg-green-50"
      case "running":
        return "border-primary bg-primary/5"
      case "failed":
        return "border-red-200 bg-red-50"
      default:
        return "border-border bg-background"
    }
  }

  const handlePauseResume = () => {
    setIsPaused(!isPaused)
  }

  const handleRestart = () => {
    setCurrentStepIndex(0)
    setIsPaused(false)
    setGenerationSteps(prev => prev.map(step => ({
      ...step,
      status: "pending",
      progress: 0,
    })))
  }

  const allStepsCompleted = generationSteps.every(step => step.status === "completed")

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            AI Book Generation
          </CardTitle>
          <CardDescription>
            Our AI agents are working together to create your book. This process typically takes 12-15 minutes.
            You can monitor the progress below.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Generation Overview */}
          <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-primary">
                  {data.chapterCount || 8}
                </div>
                <Text variant="caption" className="text-muted-foreground">
                  Chapters
                </Text>
              </div>
              <div>
                <div className="text-lg font-semibold text-primary">
                  {data.length === "short" ? "50-75" : data.length === "medium" ? "100-150" : "200-300"}
                </div>
                <Text variant="caption" className="text-muted-foreground">
                  Pages
                </Text>
              </div>
              <div>
                <div className="text-lg font-semibold text-primary">
                  {data.tone ? data.tone.charAt(0).toUpperCase() + data.tone.slice(1) : "—"}
                </div>
                <Text variant="caption" className="text-muted-foreground">
                  Tone
                </Text>
              </div>
              <div>
                <div className="text-lg font-semibold text-primary">
                  {estimatedTimeRemaining}
                </div>
                <Text variant="caption" className="text-muted-foreground">
                  Time Left
                </Text>
              </div>
            </div>
          </div>

          {!isGenerating && !allStepsCompleted && (
            <div className="text-center py-8">
              <div className="mb-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <Heading variant="h3" className="mb-2">
                  Ready to Generate Your Book
                </Heading>
                <Text variant="body" className="text-muted-foreground max-w-md mx-auto">
                  All settings configured! Click the button below to start the AI generation process.
                  This will create your complete book including content, cover, and formatting.
                </Text>
              </div>
              <Button onClick={onStartGeneration} size="lg" className="gap-2">
                <Zap className="h-5 w-5" />
                Start Generation
              </Button>
            </div>
          )}

          {(isGenerating || allStepsCompleted) && (
            <>
              {/* Overall Progress */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Text variant="small" className="font-medium">
                    Overall Progress
                  </Text>
                  <div className="flex items-center gap-2">
                    <Badge variant={allStepsCompleted ? "default" : "secondary"}>
                      {Math.round(getTotalProgress())}%
                    </Badge>
                    {isGenerating && !allStepsCompleted && (
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handlePauseResume}
                          className="h-8 w-8 p-0"
                        >
                          {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleRestart}
                          className="h-8 w-8 p-0"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                <Progress value={getTotalProgress()} className="h-3" />
              </div>

              <Separator />

              {/* Step Progress */}
              <div className="space-y-4">
                <Text variant="small" className="font-medium">
                  Generation Steps
                </Text>
                <div className="space-y-3">
                  {generationSteps.map((step, index) => {
                    const Icon = step.icon
                    return (
                      <div
                        key={step.id}
                        className={`p-4 border rounded-lg transition-all ${getStatusColor(step)}`}
                      >
                        <div className="flex items-center gap-4">
                          <div className="flex-shrink-0">
                            <Icon className="h-5 w-5 text-muted-foreground" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <Text variant="small" className="font-medium">
                                {step.name}
                              </Text>
                              <div className="flex items-center gap-2">
                                <Text variant="caption" className="text-muted-foreground">
                                  {step.estimatedTime}
                                </Text>
                                {getStatusIcon(step)}
                              </div>
                            </div>
                            <Text variant="caption" className="text-muted-foreground mb-2">
                              {step.description}
                            </Text>
                            {step.status === "running" && (
                              <Progress value={step.progress} className="h-2" />
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Status Messages */}
              {isPaused && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Pause className="h-4 w-4 text-yellow-600" />
                    <Text variant="small" className="text-yellow-800 font-medium">
                      Generation Paused
                    </Text>
                  </div>
                  <Text variant="caption" className="text-yellow-700 mt-1">
                    Click the play button to resume the generation process.
                  </Text>
                </div>
              )}

              {allStepsCompleted && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <Text variant="small" className="text-green-800 font-medium">
                      Generation Complete!
                    </Text>
                  </div>
                  <Text variant="caption" className="text-green-700 mt-1">
                    Your book has been successfully generated. Proceed to review the results.
                  </Text>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          type="button" 
          onClick={onPrevious} 
          variant="outline"
          disabled={isGenerating && !isPaused}
        >
          Previous: Book Details
        </Button>
        <Button 
          disabled={!allStepsCompleted}
          onClick={() => {/* Will be handled by parent wizard */}}
        >
          {allStepsCompleted ? "Review Book" : "Generating..."}
        </Button>
      </div>
    </div>
  )
}