"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { TrendingUp, Lightbulb, Target, Search, Sparkles } from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

const topicSchema = z.object({
  topic: z.string().min(5, "Topic must be at least 5 characters"),
  genre: z.string().min(1, "Please select a genre"),
  targetAudience: z.string().min(5, "Please describe your target audience"),
  industryFocus: z.array(z.string()).max(3, "Maximum 3 industries allowed").optional(),
})

type TopicFormData = z.infer<typeof topicSchema>

interface TopicSelectionStepProps {
  data: Partial<BookCreationData>
  onUpdate: (data: Partial<BookCreationData>) => void
  onNext: () => void
  onCancel: () => void
}

const genres = [
  "Business & Economics",
  "Self-Help & Personal Development",
  "Health & Fitness",
  "Technology",
  "Fiction",
  "Non-Fiction",
  "Biography & Memoir",
  "History",
  "Science",
  "Travel",
  "Cooking",
  "Art & Design",
  "Education",
  "Parenting",
  "Religion & Spirituality",
  "Romance",
  "Mystery & Thriller",
  "Science Fiction & Fantasy",
  "Young Adult",
  "Children's Books",
]

const trendingTopics = [
  { topic: "AI and Machine Learning for Beginners", trend: "+125%", genre: "Technology" },
  { topic: "Sustainable Living and Zero Waste", trend: "+89%", genre: "Lifestyle" },
  { topic: "Cryptocurrency Investment Guide", trend: "+67%", genre: "Finance" },
  { topic: "Remote Work Productivity", trend: "+54%", genre: "Business" },
  { topic: "Mental Health and Mindfulness", trend: "+43%", genre: "Self-Help" },
  { topic: "Plant-Based Nutrition", trend: "+38%", genre: "Health" },
]

const industryOptions = [
  "Health", "Wealth", "Beauty", "Technology", "Business", "Fitness",
  "Nutrition", "Self-Help", "Finance", "Real Estate", "Marketing",
  "Education", "Relationships", "Lifestyle", "Travel", "Food",
  "Fashion", "Entertainment", "Sports", "Science"
]

export function TopicSelectionStep({ data, onUpdate, onNext, onCancel }: TopicSelectionStepProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>(data.keywords || [])
  const [customKeyword, setCustomKeyword] = useState("")
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>(
    data.industryFocus || ["Health", "Wealth", "Beauty"]
  )

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<TopicFormData>({
    resolver: zodResolver(topicSchema),
    defaultValues: {
      topic: data.topic || "",
      genre: data.genre || "",
      targetAudience: data.targetAudience || "",
      industryFocus: data.industryFocus || ["Health", "Wealth", "Beauty"],
    },
  })

  const watchedTopic = watch("topic")

  const handleTrendingTopicSelect = (topic: string, genre: string) => {
    setValue("topic", topic)
    setValue("genre", genre)
  }

  const addKeyword = () => {
    if (customKeyword.trim() && !selectedKeywords.includes(customKeyword.trim())) {
      const newKeywords = [...selectedKeywords, customKeyword.trim()]
      setSelectedKeywords(newKeywords)
      setCustomKeyword("")
    }
  }

  const removeKeyword = (keyword: string) => {
    setSelectedKeywords(selectedKeywords.filter(k => k !== keyword))
  }

  const toggleIndustry = (industry: string) => {
    setSelectedIndustries(current => {
      if (current.includes(industry)) {
        return current.filter(i => i !== industry)
      } else if (current.length < 3) {
        return [...current, industry]
      }
      return current
    })
  }

  const analyzeTrends = async () => {
    setIsAnalyzing(true)
    try {
      // Simulate API call to analyze trends
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock trend analysis results
      const mockTrendData = {
        searchVolume: Math.floor(Math.random() * 10000) + 1000,
        competition: ["Low", "Medium", "High"][Math.floor(Math.random() * 3)],
        profitability: Math.floor(Math.random() * 5) + 1,
      }
      
      onUpdate({ trendData: mockTrendData })
    } catch (error) {
      console.error("Failed to analyze trends:", error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const onSubmit = (formData: TopicFormData) => {
    onUpdate({
      ...formData,
      keywords: selectedKeywords,
      industryFocus: selectedIndustries,
    })
    onNext()
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-primary" />
            Choose Your Book Topic
          </CardTitle>
          <CardDescription>
            Select a topic that resonates with your interests and has market potential.
            Our AI will help you identify trending niches.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Trending Topics */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <Label className="font-medium">Trending Topics</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {trendingTopics.map((trending, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleTrendingTopicSelect(trending.topic, trending.genre)}
                  className="p-4 text-left border rounded-lg hover:bg-primary/5 hover:border-primary transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{trending.topic}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {trending.genre}
                      </div>
                    </div>
                    <Badge variant="default" className="text-xs">
                      {trending.trend}
                    </Badge>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Custom Topic */}
          <div className="space-y-4">
            <Label htmlFor="topic">Book Topic *</Label>
            <Textarea
              id="topic"
              placeholder="Describe your book topic in detail. What specific problem does it solve? What value does it provide?"
              className="min-h-[100px]"
              {...register("topic")}
            />
            {errors.topic && (
              <Text variant="caption" className="text-destructive">
                {errors.topic.message}
              </Text>
            )}
          </div>

          {/* Genre Selection */}
          <div className="space-y-2">
            <Label>Genre *</Label>
            <Select
              value={watch("genre")}
              onValueChange={(value) => setValue("genre", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a genre" />
              </SelectTrigger>
              <SelectContent>
                {genres.map((genre) => (
                  <SelectItem key={genre} value={genre}>
                    {genre}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.genre && (
              <Text variant="caption" className="text-destructive">
                {errors.genre.message}
              </Text>
            )}
          </div>

          {/* Target Audience */}
          <div className="space-y-2">
            <Label htmlFor="targetAudience">Target Audience *</Label>
            <Textarea
              id="targetAudience"
              placeholder="Who is your ideal reader? Age group, interests, profession, pain points..."
              {...register("targetAudience")}
            />
            {errors.targetAudience && (
              <Text variant="caption" className="text-destructive">
                {errors.targetAudience.message}
              </Text>
            )}
          </div>

          {/* Industry Focus */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-primary" />
              <Label className="font-medium">
                Industry Focus (1-3 industries)
              </Label>
            </div>
            <Text variant="caption" className="text-muted-foreground">
              Select up to 3 industries to focus your research and content on. 
              Default: Health, Wealth, Beauty
            </Text>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {industryOptions.map((industry) => (
                <button
                  key={industry}
                  type="button"
                  onClick={() => toggleIndustry(industry)}
                  className={`p-3 text-sm rounded-lg border transition-all ${
                    selectedIndustries.includes(industry)
                      ? "bg-primary text-primary-foreground border-primary shadow-sm"
                      : "bg-background hover:bg-muted border-border"
                  }`}
                >
                  {industry}
                </button>
              ))}
            </div>
            {selectedIndustries.length > 0 && (
              <div className="p-3 bg-primary/5 rounded-lg border border-primary/20">
                <Text variant="small" className="font-medium mb-2">
                  Selected Industries ({selectedIndustries.length}/3):
                </Text>
                <div className="flex flex-wrap gap-2">
                  {selectedIndustries.map((industry) => (
                    <Badge key={industry} variant="default">
                      {industry}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Keywords */}
          <div className="space-y-4">
            <Label>Keywords (Optional)</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Add relevant keywords"
                value={customKeyword}
                onChange={(e) => setCustomKeyword(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyword())}
              />
              <Button type="button" onClick={addKeyword} variant="outline">
                Add
              </Button>
            </div>
            {selectedKeywords.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {selectedKeywords.map((keyword) => (
                  <Badge
                    key={keyword}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeKeyword(keyword)}
                  >
                    {keyword} ×
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Trend Analysis */}
          {watchedTopic && watchedTopic.length > 10 && (
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-primary" />
                  <Text variant="small" className="font-medium">
                    AI Trend Analysis
                  </Text>
                </div>
                <Button
                  type="button"
                  onClick={analyzeTrends}
                  disabled={isAnalyzing}
                  size="sm"
                  variant="outline"
                >
                  {isAnalyzing ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Analyze Trends
                    </>
                  )}
                </Button>
              </div>
              {data.trendData && (
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <Text variant="caption" className="text-muted-foreground">
                      Search Volume
                    </Text>
                    <div className="font-medium">{data.trendData.searchVolume.toLocaleString()}</div>
                  </div>
                  <div>
                    <Text variant="caption" className="text-muted-foreground">
                      Competition
                    </Text>
                    <div className="font-medium">{data.trendData.competition}</div>
                  </div>
                  <div>
                    <Text variant="caption" className="text-muted-foreground">
                      Profit Score
                    </Text>
                    <div className="font-medium">{data.trendData.profitability}/5 ⭐</div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button type="button" onClick={onCancel} variant="outline">
          Cancel
        </Button>
        <Button type="submit" disabled={!isValid}>
          Next: Configure Content
        </Button>
      </div>
    </form>
  )
}