"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { Switch } from "@/components/ui/switch"
import { BookOpen, DollarSign, Globe, Tag, Lightbulb, TrendingUp } from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

const metadataSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  description: z.string().min(50, "Description must be at least 50 characters"),
  price: z.number().min(0.99).max(999.99),
  language: z.string().min(1, "Please select a language"),
})

type MetadataFormData = z.infer<typeof metadataSchema>

interface MetadataStepProps {
  data: Partial<BookCreationData>
  onUpdate: (data: Partial<BookCreationData>) => void
  onNext: () => void
  onPrevious: () => void
}

const languages = [
  { value: "en", label: "English" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
  { value: "de", label: "German" },
  { value: "it", label: "Italian" },
  { value: "pt", label: "Portuguese" },
  { value: "zh", label: "Chinese" },
  { value: "ja", label: "Japanese" },
]

const suggestedCategories = [
  "Business & Economics",
  "Self-Help & Personal Development",
  "Technology & Computers",
  "Health & Fitness",
  "Education & Teaching",
  "Science & Mathematics",
  "Arts & Entertainment",
  "Travel & Tourism",
  "Cooking & Food",
  "Parenting & Family",
  "Religion & Spirituality",
  "History & Politics",
  "Biography & Memoir",
  "Fiction & Literature",
]

const pricingTiers = [
  { range: "$0.99 - $2.99", label: "Budget", description: "Entry-level pricing for new authors" },
  { range: "$2.99 - $9.99", label: "Standard", description: "Most popular range for ebooks" },
  { range: "$9.99 - $19.99", label: "Premium", description: "Higher value, comprehensive content" },
  { range: "$19.99+", label: "Professional", description: "Expert-level, specialized content" },
]

export function MetadataStep({ data, onUpdate, onNext, onPrevious }: MetadataStepProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>(data.categories || [])
  const [generateISBN, setGenerateISBN] = useState(true)
  const [useAISuggestions, setUseAISuggestions] = useState(true)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<MetadataFormData>({
    resolver: zodResolver(metadataSchema),
    defaultValues: {
      title: data.title || "",
      description: data.description || "",
      price: data.price || 4.99,
      language: data.language || "en",
    },
  })

  const watchedPrice = watch("price")
  const watchedTitle = watch("title")
  const watchedDescription = watch("description")

  const addCategory = (category: string) => {
    if (!selectedCategories.includes(category) && selectedCategories.length < 3) {
      setSelectedCategories([...selectedCategories, category])
    }
  }

  const removeCategory = (category: string) => {
    setSelectedCategories(selectedCategories.filter(c => c !== category))
  }

  const getPricingTier = (price: number) => {
    if (price < 2.99) return pricingTiers[0]
    if (price < 9.99) return pricingTiers[1]
    if (price < 19.99) return pricingTiers[2]
    return pricingTiers[3]
  }

  const generateSuggestedPrice = () => {
    // Mock AI price suggestion based on genre and content
    const basePrice = data.genre === "Technology" ? 7.99 : 4.99
    const adjustment = Math.random() * 2 - 1 // Random adjustment
    const suggestedPrice = Math.max(0.99, Math.round((basePrice + adjustment) * 100) / 100)
    setValue("price", suggestedPrice)
  }

  const generateDescription = () => {
    if (!data.topic || !data.outline) return
    
    // Mock AI description generation
    const mockDescription = `Discover the essential guide to ${data.topic.toLowerCase()}. This comprehensive book provides practical insights and actionable strategies that will transform your understanding and approach.

In this expertly crafted guide, you'll learn:
${data.outline?.slice(0, 3).map(chapter => `• ${chapter.replace(/^Chapter \d+:?\s*/, '')}`).join('\n')}

Whether you're a beginner or looking to advance your knowledge, this book offers valuable perspectives and real-world applications that you can implement immediately. Join thousands of readers who have already benefited from these proven strategies and insights.

Perfect for ${data.targetAudience?.toLowerCase() || 'anyone interested in this topic'}, this book combines theoretical knowledge with practical applications to deliver maximum value and impact.`

    setValue("description", mockDescription)
  }

  const onSubmit = (formData: MetadataFormData) => {
    const isbn = generateISBN ? `978-${Math.random().toString().slice(2, 15)}` : undefined
    
    onUpdate({
      ...formData,
      categories: selectedCategories,
      isbn,
    })
    onNext()
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            Book Details & Metadata
          </CardTitle>
          <CardDescription>
            Add the essential information that will help readers discover and understand your book.
            This metadata is crucial for marketing and sales.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Book Title *</Label>
            <Input
              id="title"
              placeholder="Enter a compelling, descriptive title"
              {...register("title")}
            />
            {errors.title && (
              <Text variant="caption" className="text-destructive">
                {errors.title.message}
              </Text>
            )}
            <Text variant="caption" className="text-muted-foreground">
              Keep it under 60 characters for better visibility in marketplaces
            </Text>
          </div>

          {/* Description */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="description">Book Description *</Label>
              {useAISuggestions && (
                <Button
                  type="button"
                  onClick={generateDescription}
                  size="sm"
                  variant="outline"
                  disabled={!data.topic || !data.outline}
                >
                  <Lightbulb className="h-4 w-4 mr-2" />
                  AI Suggest
                </Button>
              )}
            </div>
            <Textarea
              id="description"
              placeholder="Write a compelling description that highlights the value and benefits of your book..."
              className="min-h-[120px]"
              {...register("description")}
            />
            {errors.description && (
              <Text variant="caption" className="text-destructive">
                {errors.description.message}
              </Text>
            )}
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>
                {watchedDescription?.length || 0} / 4000 characters
              </span>
              <span>
                Aim for 150-300 words for optimal engagement
              </span>
            </div>
          </div>

          <Separator />

          {/* Categories */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Tag className="h-4 w-4 text-blue-600" />
              <Label className="font-medium">Categories (Max 3)</Label>
            </div>
            
            {selectedCategories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {selectedCategories.map((category) => (
                  <Badge
                    key={category}
                    variant="default"
                    className="cursor-pointer"
                    onClick={() => removeCategory(category)}
                  >
                    {category} ×
                  </Badge>
                ))}
              </div>
            )}

            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {suggestedCategories
                .filter(cat => !selectedCategories.includes(cat))
                .slice(0, 12)
                .map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => addCategory(category)}
                  disabled={selectedCategories.length >= 3}
                  className="p-2 text-left text-sm border rounded-lg hover:bg-primary/5 hover:border-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {category}
                </button>
              ))}
            </div>
            
            <Text variant="caption" className="text-muted-foreground">
              Choose categories that best describe your book's content and target audience
            </Text>
          </div>

          <Separator />

          {/* Pricing */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <Label className="font-medium">Pricing Strategy</Label>
              </div>
              <Button
                type="button"
                onClick={generateSuggestedPrice}
                size="sm"
                variant="outline"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                AI Suggest Price
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">Price (USD) *</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="price"
                    type="number"
                    min="0.99"
                    max="999.99"
                    step="0.01"
                    className="pl-10"
                    {...register("price", { valueAsNumber: true })}
                  />
                </div>
                {errors.price && (
                  <Text variant="caption" className="text-destructive">
                    {errors.price.message}
                  </Text>
                )}
              </div>

              <div className="space-y-2">
                <Label>Pricing Tier</Label>
                <div className="p-3 border rounded-lg bg-muted/30">
                  <div className="font-medium text-sm text-primary">
                    {getPricingTier(watchedPrice).label}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {getPricingTier(watchedPrice).description}
                  </div>
                </div>
              </div>
            </div>

            {/* Pricing recommendations */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {pricingTiers.map((tier, index) => (
                <div
                  key={index}
                  className={`p-3 border rounded-lg text-center cursor-pointer transition-colors ${
                    getPricingTier(watchedPrice).label === tier.label
                      ? "border-primary bg-primary/5"
                      : "border-border hover:bg-muted/50"
                  }`}
                  onClick={() => {
                    const prices = [1.99, 4.99, 12.99, 24.99]
                    setValue("price", prices[index])
                  }}
                >
                  <div className="text-sm font-medium">{tier.label}</div>
                  <div className="text-xs text-muted-foreground">{tier.range}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Language & ISBN */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Language *</Label>
              <Select
                value={watch("language")}
                onValueChange={(value) => setValue("language", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        {lang.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>ISBN Settings</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="generate-isbn"
                  checked={generateISBN}
                  onCheckedChange={setGenerateISBN}
                />
                <Label htmlFor="generate-isbn" className="text-sm">
                  Auto-generate ISBN
                </Label>
              </div>
              <Text variant="caption" className="text-muted-foreground">
                We'll generate a valid ISBN for your book automatically
              </Text>
            </div>
          </div>

          {/* AI Assistance Toggle */}
          <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center justify-between">
              <div>
                <Text variant="small" className="font-medium">
                  AI Writing Assistance
                </Text>
                <Text variant="caption" className="text-muted-foreground">
                  Get AI-powered suggestions for titles, descriptions, and pricing
                </Text>
              </div>
              <Switch
                checked={useAISuggestions}
                onCheckedChange={setUseAISuggestions}
              />
            </div>
          </div>

          {/* Metadata Summary */}
          {watchedTitle && watchedDescription && (
            <div className="p-4 bg-muted/30 rounded-lg border">
              <Text variant="small" className="font-medium mb-3 text-muted-foreground">
                Metadata Preview:
              </Text>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <Text variant="caption" className="text-muted-foreground">Title Length</Text>
                  <div className={`font-medium ${watchedTitle.length > 60 ? 'text-orange-600' : 'text-green-600'}`}>
                    {watchedTitle.length}/60
                  </div>
                </div>
                <div>
                  <Text variant="caption" className="text-muted-foreground">Description</Text>
                  <div className={`font-medium ${watchedDescription.length < 150 ? 'text-orange-600' : 'text-green-600'}`}>
                    {watchedDescription.length} chars
                  </div>
                </div>
                <div>
                  <Text variant="caption" className="text-muted-foreground">Categories</Text>
                  <div className={`font-medium ${selectedCategories.length === 0 ? 'text-orange-600' : 'text-green-600'}`}>
                    {selectedCategories.length}/3
                  </div>
                </div>
                <div>
                  <Text variant="caption" className="text-muted-foreground">Price</Text>
                  <div className="font-medium text-primary">
                    ${watchedPrice.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button type="button" onClick={onPrevious} variant="outline">
          Previous: Cover Design
        </Button>
        <Button 
          type="submit" 
          disabled={!isValid || selectedCategories.length === 0}
        >
          Next: Generate Book
        </Button>
      </div>
    </form>
  )
}