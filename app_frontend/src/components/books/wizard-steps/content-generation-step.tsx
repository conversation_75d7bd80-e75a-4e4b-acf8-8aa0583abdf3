"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { FileText, Brain, Lightbulb, Target, BookOpen, Zap } from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

const contentSchema = z.object({
  tone: z.string().min(1, "Please select a tone"),
  length: z.enum(["short", "medium", "long"]),
  chapterCount: z.number().min(3).max(50),
})

type ContentFormData = z.infer<typeof contentSchema>

interface ContentGenerationStepProps {
  data: Partial<BookCreationData>
  onUpdate: (data: Partial<BookCreationData>) => void
  onNext: () => void
  onPrevious: () => void
}

const tones = [
  { value: "professional", label: "Professional", description: "Formal, authoritative, and expert-level" },
  { value: "conversational", label: "Conversational", description: "Friendly, approachable, and engaging" },
  { value: "educational", label: "Educational", description: "Clear, instructional, and easy to follow" },
  { value: "inspirational", label: "Inspirational", description: "Motivating, uplifting, and encouraging" },
  { value: "humorous", label: "Humorous", description: "Light-hearted, witty, and entertaining" },
  { value: "academic", label: "Academic", description: "Scholarly, detailed, and research-based" },
]

const lengthOptions = [
  { value: "short", label: "Short", pages: "50-75", description: "Quick read, focused content" },
  { value: "medium", label: "Medium", pages: "100-150", description: "Comprehensive coverage" },
  { value: "long", label: "Long", pages: "200-300", description: "In-depth exploration" },
]

export function ContentGenerationStep({ data, onUpdate, onNext, onPrevious }: ContentGenerationStepProps) {
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false)
  const [generatedOutline, setGeneratedOutline] = useState<string[]>(data.outline || [])

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<ContentFormData>({
    resolver: zodResolver(contentSchema),
    defaultValues: {
      tone: data.tone || "",
      length: data.length || "medium",
      chapterCount: data.chapterCount || 8,
    },
  })

  const watchedLength = watch("length")
  const watchedChapterCount = watch("chapterCount")

  const generateOutline = async () => {
    setIsGeneratingOutline(true)
    try {
      // Simulate AI outline generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const mockOutline = [
        "Introduction: Understanding the Problem",
        "Chapter 1: Fundamentals and Core Concepts",
        "Chapter 2: Getting Started - First Steps",
        "Chapter 3: Advanced Techniques and Strategies",
        "Chapter 4: Common Challenges and Solutions",
        "Chapter 5: Real-World Applications",
        "Chapter 6: Best Practices and Guidelines",
        "Chapter 7: Future Trends and Innovations",
        "Conclusion: Putting It All Together",
      ].slice(0, watchedChapterCount + 1)

      setGeneratedOutline(mockOutline)
      onUpdate({ outline: mockOutline })
    } catch (error) {
      console.error("Failed to generate outline:", error)
    } finally {
      setIsGeneratingOutline(false)
    }
  }

  const onSubmit = (formData: ContentFormData) => {
    onUpdate({
      ...formData,
      outline: generatedOutline,
    })
    onNext()
  }

  const getLengthInfo = (length: string) => {
    return lengthOptions.find(option => option.value === length)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Configure Content Generation
          </CardTitle>
          <CardDescription>
            Define how your book content will be generated. Our AI will create engaging,
            well-structured content based on your preferences.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Tone Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Brain className="h-4 w-4 text-blue-600" />
              <Label className="font-medium">Writing Tone & Style</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {tones.map((tone) => (
                <button
                  key={tone.value}
                  type="button"
                  onClick={() => setValue("tone", tone.value)}
                  className={`p-4 text-left border rounded-lg transition-colors ${
                    watch("tone") === tone.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:bg-muted/50"
                  }`}
                >
                  <div className="font-medium text-sm">{tone.label}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {tone.description}
                  </div>
                </button>
              ))}
            </div>
            {errors.tone && (
              <Text variant="caption" className="text-destructive">
                {errors.tone.message}
              </Text>
            )}
          </div>

          <Separator />

          {/* Book Length */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <BookOpen className="h-4 w-4 text-green-600" />
              <Label className="font-medium">Book Length</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {lengthOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => setValue("length", option.value as "short" | "medium" | "long")}
                  className={`p-4 text-center border rounded-lg transition-colors ${
                    watchedLength === option.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:bg-muted/50"
                  }`}
                >
                  <div className="font-medium text-sm">{option.label}</div>
                  <div className="text-xs text-primary font-medium">{option.pages} pages</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {option.description}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Chapter Count */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="font-medium">Number of Chapters</Label>
              <Badge variant="outline" className="px-3">
                {watchedChapterCount} chapters
              </Badge>
            </div>
            <div className="px-4">
              <Slider
                value={[watchedChapterCount]}
                onValueChange={([value]) => setValue("chapterCount", value)}
                min={3}
                max={20}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-2">
                <span>3 chapters</span>
                <span>20 chapters</span>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Estimated reading time: {Math.round(watchedChapterCount * 12)} minutes
            </div>
          </div>

          <Separator />

          {/* AI Outline Generation */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-yellow-600" />
                <Label className="font-medium">Content Outline</Label>
              </div>
              <Button
                type="button"
                onClick={generateOutline}
                disabled={isGeneratingOutline || !data.topic}
                size="sm"
                variant="outline"
              >
                {isGeneratingOutline ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Generate Outline
                  </>
                )}
              </Button>
            </div>

            {!data.topic && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <Text variant="small" className="text-yellow-800">
                  Complete the topic selection step to generate an AI-powered outline.
                </Text>
              </div>
            )}

            {generatedOutline.length > 0 && (
              <div className="space-y-2">
                <Text variant="small" className="font-medium text-muted-foreground">
                  Generated Outline Preview:
                </Text>
                <div className="bg-muted/30 border rounded-lg p-4">
                  <div className="space-y-2">
                    {generatedOutline.map((chapter, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <Badge variant="secondary" className="text-xs min-w-[24px] h-6">
                          {index + 1}
                        </Badge>
                        <Text variant="small" className="flex-1">
                          {chapter}
                        </Text>
                      </div>
                    ))}
                  </div>
                </div>
                <Text variant="caption" className="text-muted-foreground">
                  This outline will guide the AI content generation. You can modify it in the next steps.
                </Text>
              </div>
            )}
          </div>

          {/* Content Preview */}
          {watchedLength && (
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-lg font-semibold text-primary">
                    {getLengthInfo(watchedLength)?.pages}
                  </div>
                  <Text variant="caption" className="text-muted-foreground">
                    Pages
                  </Text>
                </div>
                <div>
                  <div className="text-lg font-semibold text-primary">
                    {watchedChapterCount}
                  </div>
                  <Text variant="caption" className="text-muted-foreground">
                    Chapters
                  </Text>
                </div>
                <div>
                  <div className="text-lg font-semibold text-primary">
                    {Math.round(watchedChapterCount * 12)}m
                  </div>
                  <Text variant="caption" className="text-muted-foreground">
                    Read Time
                  </Text>
                </div>
                <div>
                  <div className="text-lg font-semibold text-primary">
                    {watch("tone") ? tones.find(t => t.value === watch("tone"))?.label : "—"}
                  </div>
                  <Text variant="caption" className="text-muted-foreground">
                    Tone
                  </Text>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button type="button" onClick={onPrevious} variant="outline">
          Previous: Topic Selection
        </Button>
        <Button 
          type="submit" 
          disabled={!isValid || (generatedOutline.length === 0 && !!data.topic)}
        >
          Next: Design Cover
        </Button>
      </div>
    </form>
  )
}