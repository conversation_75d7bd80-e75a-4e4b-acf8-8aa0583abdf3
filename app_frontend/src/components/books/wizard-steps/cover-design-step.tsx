"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Palette, Image, Sparkles, Eye, Download, RefreshCw } from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

const coverSchema = z.object({
  coverStyle: z.string().min(1, "Please select a cover style"),
  coverText: z.string().min(1, "Please enter cover text"),
})

type CoverFormData = z.infer<typeof coverSchema>

interface CoverDesignStepProps {
  data: Partial<BookCreationData>
  onUpdate: (data: Partial<BookCreationData>) => void
  onNext: () => void
  onPrevious: () => void
}

const coverStyles = [
  { value: "modern", label: "Modern", description: "Clean, minimalist design with bold typography" },
  { value: "classic", label: "Classic", description: "Traditional, elegant with serif fonts" },
  { value: "creative", label: "Creative", description: "Artistic, unique with custom illustrations" },
  { value: "professional", label: "Professional", description: "Corporate, business-focused design" },
  { value: "playful", label: "Playful", description: "Fun, colorful with creative elements" },
  { value: "dramatic", label: "Dramatic", description: "Bold, high-contrast with strong imagery" },
]

const colorPalettes = [
  { name: "Ocean Blue", colors: ["#0066CC", "#4A90E2", "#7BB3F0", "#FFFFFF"] },
  { name: "Forest Green", colors: ["#2E7D32", "#4CAF50", "#81C784", "#FFFFFF"] },
  { name: "Sunset Orange", colors: ["#FF6B35", "#F7931E", "#FFB74D", "#FFFFFF"] },
  { name: "Royal Purple", colors: ["#6A1B9A", "#9C27B0", "#BA68C8", "#FFFFFF"] },
  { name: "Charcoal Gray", colors: ["#424242", "#616161", "#9E9E9E", "#FFFFFF"] },
  { name: "Crimson Red", colors: ["#C62828", "#F44336", "#EF5350", "#FFFFFF"] },
]

const mockCoverPreviews = [
  "/images/cover-preview-1.jpg",
  "/images/cover-preview-2.jpg",
  "/images/cover-preview-3.jpg",
  "/images/cover-preview-4.jpg",
]

export function CoverDesignStep({ data, onUpdate, onNext, onPrevious }: CoverDesignStepProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedPalette, setSelectedPalette] = useState<string[]>(data.coverColors || colorPalettes[0].colors)
  const [generatedCovers, setGeneratedCovers] = useState<string[]>([])
  const [selectedCover, setSelectedCover] = useState<string>(data.selectedCover || "")

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<CoverFormData>({
    resolver: zodResolver(coverSchema),
    defaultValues: {
      coverStyle: data.coverStyle || "",
      coverText: data.coverText || data.title || "",
    },
  })

  const watchedStyle = watch("coverStyle")
  const watchedText = watch("coverText")

  const generateCovers = async () => {
    setIsGenerating(true)
    try {
      // Simulate AI cover generation
      await new Promise(resolve => setTimeout(resolve, 4000))
      
      setGeneratedCovers(mockCoverPreviews)
    } catch (error) {
      console.error("Failed to generate covers:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const selectPalette = (colors: string[]) => {
    setSelectedPalette(colors)
  }

  const onSubmit = (formData: CoverFormData) => {
    onUpdate({
      ...formData,
      coverColors: selectedPalette,
      selectedCover,
    })
    onNext()
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-primary" />
            Design Your Book Cover
          </CardTitle>
          <CardDescription>
            Create an eye-catching cover that attracts readers and represents your book's content.
            Our AI will generate multiple design options based on your preferences.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Cover Style Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Image className="h-4 w-4 text-purple-600" />
              <Label className="font-medium">Cover Style</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {coverStyles.map((style) => (
                <button
                  key={style.value}
                  type="button"
                  onClick={() => setValue("coverStyle", style.value)}
                  className={`p-4 text-left border rounded-lg transition-colors ${
                    watchedStyle === style.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:bg-muted/50"
                  }`}
                >
                  <div className="font-medium text-sm">{style.label}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {style.description}
                  </div>
                </button>
              ))}
            </div>
            {errors.coverStyle && (
              <Text variant="caption" className="text-destructive">
                {errors.coverStyle.message}
              </Text>
            )}
          </div>

          <Separator />

          {/* Color Palette Selection */}
          <div className="space-y-4">
            <Label className="font-medium">Color Palette</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {colorPalettes.map((palette) => (
                <button
                  key={palette.name}
                  type="button"
                  onClick={() => selectPalette(palette.colors)}
                  className={`p-3 border rounded-lg transition-colors ${
                    JSON.stringify(selectedPalette) === JSON.stringify(palette.colors)
                      ? "border-primary bg-primary/5"
                      : "border-border hover:bg-muted/50"
                  }`}
                >
                  <div className="text-sm font-medium mb-2">{palette.name}</div>
                  <div className="flex gap-1">
                    {palette.colors.map((color, index) => (
                      <div
                        key={index}
                        className="w-6 h-6 rounded border"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Cover Text */}
          <div className="space-y-2">
            <Label htmlFor="coverText">Cover Text *</Label>
            <Input
              id="coverText"
              placeholder="Enter the title and subtitle for your cover"
              {...register("coverText")}
            />
            {errors.coverText && (
              <Text variant="caption" className="text-destructive">
                {errors.coverText.message}
              </Text>
            )}
            <Text variant="caption" className="text-muted-foreground">
              This text will appear on your book cover. Keep it concise and impactful.
            </Text>
          </div>

          <Separator />

          {/* AI Cover Generation */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-yellow-600" />
                <Label className="font-medium">AI Generated Covers</Label>
              </div>
              <Button
                type="button"
                onClick={generateCovers}
                disabled={isGenerating || !watchedStyle || !watchedText}
                size="sm"
                variant="outline"
              >
                {isGenerating ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Generate Covers
                  </>
                )}
              </Button>
            </div>

            {(!watchedStyle || !watchedText) && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <Text variant="small" className="text-yellow-800">
                  Select a cover style and enter cover text to generate AI-powered cover designs.
                </Text>
              </div>
            )}

            {isGenerating && (
              <div className="p-8 text-center">
                <LoadingSpinner className="mx-auto mb-4" />
                <Text variant="small" className="text-muted-foreground">
                  Our AI is creating unique cover designs based on your preferences...
                </Text>
              </div>
            )}

            {generatedCovers.length > 0 && (
              <div className="space-y-4">
                <Text variant="small" className="font-medium text-muted-foreground">
                  Choose your favorite cover design:
                </Text>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {generatedCovers.map((cover, index) => (
                    <div key={index} className="space-y-2">
                      <button
                        type="button"
                        onClick={() => setSelectedCover(cover)}
                        className={`relative w-full aspect-[3/4] border-2 rounded-lg overflow-hidden transition-all ${
                          selectedCover === cover
                            ? "border-primary shadow-lg scale-105"
                            : "border-border hover:border-primary/50"
                        }`}
                      >
                        <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/30 flex items-center justify-center">
                          <div className="text-center p-4">
                            <div className="text-sm font-medium mb-2">{watchedText}</div>
                            <div className="text-xs text-muted-foreground">
                              {watchedStyle} Style
                            </div>
                          </div>
                        </div>
                        {selectedCover === cover && (
                          <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
                            <Eye className="h-3 w-3" />
                          </div>
                        )}
                      </button>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Eye className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                
                {selectedCover && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <Text variant="small" className="text-green-800 font-medium">
                      ✓ Cover selected! You can change this later or generate new options.
                    </Text>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Design Preview */}
          {watchedStyle && selectedPalette.length > 0 && (
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <div className="flex items-center gap-4">
                <div className="flex flex-col items-center">
                  <Text variant="caption" className="text-muted-foreground mb-2">
                    Style
                  </Text>
                  <Badge variant="outline">
                    {coverStyles.find(s => s.value === watchedStyle)?.label}
                  </Badge>
                </div>
                <div className="flex flex-col items-center">
                  <Text variant="caption" className="text-muted-foreground mb-2">
                    Colors
                  </Text>
                  <div className="flex gap-1">
                    {selectedPalette.slice(0, 3).map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
                <div className="flex flex-col items-center flex-1">
                  <Text variant="caption" className="text-muted-foreground mb-2">
                    Cover Text
                  </Text>
                  <Text variant="small" className="font-medium text-center">
                    {watchedText || "Enter cover text"}
                  </Text>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button type="button" onClick={onPrevious} variant="outline">
          Previous: Content Settings
        </Button>
        <Button 
          type="submit" 
          disabled={!isValid || (generatedCovers.length > 0 && !selectedCover)}
        >
          Next: Book Details
        </Button>
      </div>
    </form>
  )
}