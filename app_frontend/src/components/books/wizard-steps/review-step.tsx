"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Heading, Text } from "@/components/ui/typography"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  BookOpen, 
  Eye, 
  Download, 
  Edit3, 
  Share2, 
  CheckCircle,
  Image,
  FileText,
  Star,
  Calendar,
  DollarSign,
  Users
} from "lucide-react"
import { BookCreationData } from "../new-book-wizard"

interface ReviewStepProps {
  data: Partial<BookCreationData>
  onComplete: () => void
  onPrevious: () => void
}

const mockChapters = [
  "Introduction: Understanding the Problem",
  "Chapter 1: Fundamentals and Core Concepts", 
  "Chapter 2: Getting Started - First Steps",
  "Chapter 3: Advanced Techniques and Strategies",
  "Chapter 4: Common Challenges and Solutions",
  "Chapter 5: Real-World Applications",
  "Chapter 6: Best Practices and Guidelines",
  "Chapter 7: Future Trends and Innovations",
  "Conclusion: Putting It All Together"
]

const mockContent = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## Key Points

1. **Important Concept**: This is a crucial understanding that will help you succeed
2. **Practical Application**: Here's how you can implement this in real life
3. **Common Mistakes**: Avoid these pitfalls that many beginners encounter

> "This is an inspiring quote that reinforces the chapter's main message."

The content continues with detailed explanations, examples, and actionable insights that provide real value to readers.`

export function ReviewStep({ data, onComplete, onPrevious }: ReviewStepProps) {
  const [selectedChapter, setSelectedChapter] = useState(0)
  const [isCreating, setIsCreating] = useState(false)

  const handleCreateBook = async () => {
    setIsCreating(true)
    try {
      // Simulate API call to create book
      await new Promise(resolve => setTimeout(resolve, 2000))
      onComplete()
    } catch (error) {
      console.error("Failed to create book:", error)
    } finally {
      setIsCreating(false)
    }
  }

  const getWordCount = () => {
    const baseWords = data.length === "short" ? 15000 : data.length === "medium" ? 25000 : 40000
    return baseWords.toLocaleString()
  }

  const getReadingTime = () => {
    const words = data.length === "short" ? 15000 : data.length === "medium" ? 25000 : 40000
    return Math.round(words / 200) // Average reading speed: 200 words per minute
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-primary" />
            Review Your Book
          </CardTitle>
          <CardDescription>
            Review the generated content, cover, and metadata before creating your book.
            You can make edits or regenerate sections if needed.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Book Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Cover Preview */}
            <div className="space-y-4">
              <Text variant="small" className="font-medium text-muted-foreground">
                Book Cover
              </Text>
              <div className="aspect-[3/4] bg-gradient-to-br from-primary/10 to-primary/30 rounded-lg border-2 border-primary/20 flex items-center justify-center">
                <div className="text-center p-6">
                  <Heading variant="h4" className="mb-4">
                    {data.title || "Your Book Title"}
                  </Heading>
                  <Text variant="body" className="text-sm opacity-75">
                    {data.coverStyle ? `${data.coverStyle.charAt(0).toUpperCase()}${data.coverStyle.slice(1)} Style` : 'No Style Selected'}
                  </Text>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit Cover
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Book Details */}
            <div className="space-y-6">
              <div>
                <Text variant="small" className="font-medium text-muted-foreground mb-2">
                  Book Information
                </Text>
                <div className="space-y-3">
                  <div>
                    <Text variant="small" className="font-medium">
                      {data.title}
                    </Text>
                    <Text variant="caption" className="text-muted-foreground block">
                      {data.genre} • {data.language?.toUpperCase()}
                    </Text>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {data.categories?.map((category) => (
                      <Badge key={category} variant="secondary" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>{data.chapterCount} chapters</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-muted-foreground" />
                      <span>{getWordCount()} words</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{getReadingTime()} min read</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span>${data.price?.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <Text variant="small" className="font-medium text-muted-foreground mb-2">
                  Description
                </Text>
                <ScrollArea className="h-32">
                  <Text variant="small" className="text-muted-foreground leading-relaxed">
                    {data.description}
                  </Text>
                </ScrollArea>
              </div>

              <div>
                <Text variant="small" className="font-medium text-muted-foreground mb-2">
                  Target Audience
                </Text>
                <Text variant="small" className="text-muted-foreground">
                  {data.targetAudience}
                </Text>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Content Preview</CardTitle>
          <CardDescription>
            Review the generated chapters and content structure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="outline" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="outline">Chapter Outline</TabsTrigger>
              <TabsTrigger value="content">Sample Content</TabsTrigger>
            </TabsList>
            
            <TabsContent value="outline" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Text variant="small" className="font-medium text-muted-foreground">
                    Chapter Structure
                  </Text>
                  <div className="space-y-2">
                    {(data.outline || mockChapters).map((chapter, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedChapter(index)}
                        className={`w-full p-3 text-left border rounded-lg transition-colors ${
                          selectedChapter === index
                            ? "border-primary bg-primary/5"
                            : "border-border hover:bg-muted/50"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="text-xs">
                            {index + 1}
                          </Badge>
                          <Text variant="small" className="flex-1">
                            {chapter}
                          </Text>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Text variant="small" className="font-medium text-muted-foreground">
                    Chapter Details
                  </Text>
                  <div className="p-4 border rounded-lg bg-muted/30">
                    <Text variant="small" className="font-medium mb-2">
                      {(data.outline || mockChapters)[selectedChapter]}
                    </Text>
                    <div className="space-y-2 text-xs text-muted-foreground">
                      <div>Estimated length: 2,500-3,000 words</div>
                      <div>Reading time: 12-15 minutes</div>
                      <div>Key topics: 3-4 main concepts</div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Text variant="small" className="font-medium text-muted-foreground">
                    Sample Content from Chapter 1
                  </Text>
                  <Button variant="outline" size="sm">
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit Content
                  </Button>
                </div>
                <ScrollArea className="h-80 p-4 border rounded-lg bg-background">
                  <div className="prose prose-sm max-w-none">
                    <Text variant="body" className="leading-relaxed whitespace-pre-line">
                      {mockContent}
                    </Text>
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quality Metrics */}
      <Card>
        <CardContent className="p-6">
          <Text variant="small" className="font-medium text-muted-foreground mb-4">
            Quality Assessment
          </Text>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Star className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="text-lg font-semibold">9.2/10</div>
              <Text variant="caption" className="text-muted-foreground">
                Content Quality
              </Text>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-5 w-5 text-blue-500" />
              </div>
              <div className="text-lg font-semibold">8.8/10</div>
              <Text variant="caption" className="text-muted-foreground">
                Audience Match
              </Text>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <BookOpen className="h-5 w-5 text-green-500" />
              </div>
              <div className="text-lg font-semibold">9.5/10</div>
              <Text variant="caption" className="text-muted-foreground">
                Readability
              </Text>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Image className="h-5 w-5 text-purple-500" />
              </div>
              <div className="text-lg font-semibold">9.0/10</div>
              <Text variant="caption" className="text-muted-foreground">
                Cover Appeal
              </Text>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Final Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <Text variant="small" className="font-medium">
                Ready to Create Your Book?
              </Text>
              <Text variant="caption" className="text-muted-foreground">
                Your book will be saved and ready for publishing once created.
              </Text>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <Badge variant="default">All Systems Ready</Badge>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={handleCreateBook}
              disabled={isCreating}
              className="flex-1"
              size="lg"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Creating Book...
                </>
              ) : (
                <>
                  <BookOpen className="h-5 w-5 mr-2" />
                  Create My Book
                </>
              )}
            </Button>
            <Button variant="outline" size="lg">
              <Share2 className="h-5 w-5 mr-2" />
              Share Preview
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          type="button" 
          onClick={onPrevious} 
          variant="outline"
          disabled={isCreating}
        >
          Previous: Generation
        </Button>
        <Button 
          onClick={handleCreateBook}
          disabled={isCreating}
          variant="outline"
        >
          {isCreating ? "Creating..." : "Create Book"}
        </Button>
      </div>
    </div>
  )
}