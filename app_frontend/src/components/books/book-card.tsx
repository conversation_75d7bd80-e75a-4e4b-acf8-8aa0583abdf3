import Image from "next/image"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ead<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StatusIndicator } from "@/components/ui/status-indicator"
import { Text } from "@/components/ui/typography"
import {
  MoreVertical,
  Eye,
  Edit,
  Upload,
  Trash2,
  DollarSign,
  Star,
  FileText,
  Calendar,
} from "lucide-react"
import { cn } from "@/lib/utils"

export interface Book {
  id: string
  title: string
  description: string
  status: "draft" | "generating" | "review" | "published"
  genre: string
  pages: number
  chapters: number
  createdAt: string
  updatedAt: string
  coverUrl?: string | null
  revenue: number
  sales: number
  rating: number
}

interface BookCardProps {
  book: Book
  onAction: (bookId: string, action: string) => void
}

const statusConfig = {
  draft: { label: "Draft", variant: "secondary" as const, indicator: "default" as const },
  generating: { label: "Generating", variant: "default" as const, indicator: "info" as const, pulse: true },
  review: { label: "Review", variant: "outline" as const, indicator: "warning" as const },
  published: { label: "Published", variant: "default" as const, indicator: "success" as const },
}

export function BookCard({ book, onAction }: BookCardProps) {
  const status = statusConfig[book.status]
  const isPublished = book.status === "published"
  const canEdit = book.status === "draft" || book.status === "review"
  const canPublish = book.status === "review"

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
      <CardHeader className="p-0">
        {/* Book Cover */}
        <div className="aspect-[3/4] relative bg-gradient-to-br from-primary/10 to-primary/20 rounded-t-lg overflow-hidden">
          {book.coverUrl ? (
            <Image
              src={book.coverUrl}
              alt={book.title}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <FileText className="h-16 w-16 text-primary/40" />
            </div>
          )}
          
          {/* Status overlay */}
          <div className="absolute top-3 left-3">
            <StatusIndicator
              status={status.indicator}
              label={status.label}
              pulse={'pulse' in status ? status.pulse : false}
            />
          </div>

          {/* Actions dropdown */}
          <div className="absolute top-3 right-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 bg-background/80 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onAction(book.id, "view")}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                {canEdit && (
                  <DropdownMenuItem onClick={() => onAction(book.id, "edit")}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {canPublish && (
                  <DropdownMenuItem onClick={() => onAction(book.id, "publish")}>
                    <Upload className="mr-2 h-4 w-4" />
                    Publish
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onAction(book.id, "delete")}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Genre badge */}
          <div className="absolute bottom-3 left-3">
            <Badge variant="secondary" className="text-xs">
              {book.genre}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4 space-y-3">
        {/* Title and Description */}
        <div>
          <Link
            href={`/dashboard/books/${book.id}`}
            className="font-semibold text-lg hover:text-primary transition-colors line-clamp-2"
          >
            {book.title}
          </Link>
          <Text variant="small" className="text-muted-foreground line-clamp-2 mt-1">
            {book.description}
          </Text>
        </div>

        {/* Book Stats */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            <span>{book.pages} pages</span>
          </div>
          <div className="flex items-center gap-1">
            <span>{book.chapters} chapters</span>
          </div>
        </div>

        {/* Performance Stats (for published books) */}
        {isPublished && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center gap-1 text-sm">
              <DollarSign className="h-3 w-3 text-green-600" />
              <span className="font-medium">${book.revenue.toFixed(2)}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{book.sales} sales</span>
              {book.rating > 0 && (
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span>{book.rating.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          <span>Updated {formatDate(book.updatedAt)}</span>
        </div>
        
        <Link href={`/dashboard/books/${book.id}`}>
          <Button variant="ghost" size="sm">
            View
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}