"use client"

import { useEffect } from 'react'
import { clearSensitiveUrlParams, disableDevToolsInProduction } from '@/lib/security'

interface SecurityProviderProps {
  children: React.ReactNode
}

export function SecurityProvider({ children }: SecurityProviderProps) {
  useEffect(() => {
    // Clear any sensitive URL parameters on app load
    clearSensitiveUrlParams()
    
    // Disable dev tools in production
    disableDevToolsInProduction()
    
    // Override console methods in production to prevent credential leakage
    if (process.env.NODE_ENV === 'production') {
      const originalConsole = { ...console }
      
      console.log = () => {}
      console.warn = () => {}
      console.error = () => {}
      console.info = () => {}
      console.debug = () => {}
      
      // Only allow critical errors to be logged
      window.addEventListener('error', (event) => {
        // Log to external service instead of console
        // Ensure no sensitive data is included
        originalConsole.error('Application error:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        })
      })
    }
    
    // Security: Clear sensitive data from URL on navigation
    const handlePopState = () => {
      clearSensitiveUrlParams()
    }
    
    window.addEventListener('popstate', handlePopState)
    
    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [])

  return <>{children}</>
}