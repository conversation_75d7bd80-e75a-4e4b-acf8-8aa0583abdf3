import { useTrendsData } from '@/hooks/api/use-trends';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Eye, Target } from 'lucide-react';

interface TrendAnalysisCardProps {
  className?: string;
  limit?: number;
}

export default function TrendAnalysisCard({ className, limit = 3 }: TrendAnalysisCardProps) {
  // Use real API data
  const { data: trendsData, isLoading, error } = useTrendsData({
    sortBy: 'search_volume',
    orderBy: 'desc'
  });

  const trends = trendsData?.trends?.slice(0, limit) || [];

  if (isLoading) {
    return (
      <div className={`bg-card border rounded-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(limit)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-3 bg-muted rounded w-3/4"></div>
                <div className="h-2 bg-muted rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-card border rounded-lg p-6 ${className}`}>
        <div className="text-center text-muted-foreground">
          <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Unable to load trending topics</p>
          <p className="text-xs">Try refreshing the page</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-card border rounded-lg p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <TrendingUp className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Trending Topics</h3>
      </div>
      
      {trends.length === 0 ? (
        <div className="text-center text-muted-foreground py-8">
          <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No trends available</p>
          <p className="text-xs">Check back later for updates</p>
        </div>
      ) : (
        <div className="space-y-4">
          {trends.map((trend, index) => (
            <div key={trend.id} className="border-l-4 border-primary pl-4 pb-4 last:pb-0">
              <div className="flex items-start justify-between">
                <div className="space-y-1 flex-1">
                  <h4 className="font-medium text-foreground">{trend.title}</h4>
                  <div className="flex items-center gap-3 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {trend.search_volume?.toLocaleString() || 'N/A'} searches
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {trend.category}
                    </Badge>
                  </div>
                </div>
                <Badge 
                  variant={
                    trend.competition === 'Low' ? 'success' :
                    trend.competition === 'Medium' ? 'warning' : 'destructive'
                  }
                  className="text-xs"
                >
                  {trend.competition} competition
                </Badge>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {trendsData?.total && trendsData.total > limit && (
        <div className="mt-4 pt-4 border-t">
          <button className="text-sm text-primary hover:text-primary/80 font-medium">
            View all {trendsData.total} trends →
          </button>
        </div>
      )}
    </div>
  );
}