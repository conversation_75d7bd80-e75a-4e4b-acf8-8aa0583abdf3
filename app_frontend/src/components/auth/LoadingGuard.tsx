"use client"

import { useEffect, useState } from "react"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Container } from "@/components/ui/container"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

interface LoadingGuardProps {
  variant?: "spinner" | "skeleton" | "minimal"
  timeout?: number
  onTimeout?: () => void
  message?: string
  showProgress?: boolean
}

export function LoadingGuard({ 
  variant = "spinner",
  timeout = 10000, // 10 seconds default timeout
  onTimeout,
  message = "Authenticating...",
  showProgress = false
}: LoadingGuardProps) {
  const [hasTimedOut, setHasTimedOut] = useState(false)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setHasTimedOut(true)
      onTimeout?.()
    }, timeout)

    // Progress simulation if enabled
    let progressInterval: NodeJS.Timeout
    if (showProgress) {
      progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev
          return prev + Math.random() * 10
        })
      }, 200)
    }

    return () => {
      clearTimeout(timeoutId)
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    }
  }, [timeout, onTimeout, showProgress])

  if (hasTimedOut) {
    return <LoadingTimeout onRetry={() => setHasTimedOut(false)} />
  }

  switch (variant) {
    case "skeleton":
      return <SkeletonLoader />
    case "minimal":
      return <MinimalLoader message={message} progress={showProgress ? progress : undefined} />
    default:
      return <SpinnerLoader message={message} progress={showProgress ? progress : undefined} />
  }
}

function SpinnerLoader({ message, progress }: { message: string; progress?: number }) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Container maxWidth="sm">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">{message}</p>
            {progress !== undefined && (
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${Math.min(progress, 100)}%` }}
                />
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  )
}

function SkeletonLoader() {
  return (
    <div className="min-h-screen">
      {/* Header Skeleton */}
      <div className="border-b bg-background/95 backdrop-blur">
        <Container maxWidth="full">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-32" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          </div>
        </Container>
      </div>

      {/* Main Content Skeleton */}
      <Container maxWidth="7xl" className="py-8">
        <div className="space-y-6">
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-5/6" />
                    <Skeleton className="h-4 w-4/6" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </Container>
    </div>
  )
}

function MinimalLoader({ message, progress }: { message: string; progress?: number }) {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-background border rounded-lg p-6 shadow-lg min-w-[200px]">
        <div className="flex items-center gap-3">
          <LoadingSpinner size="sm" />
          <span className="text-sm">{message}</span>
        </div>
        {progress !== undefined && (
          <div className="mt-3">
            <div className="w-full bg-muted rounded-full h-1">
              <div 
                className="bg-primary h-1 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(progress, 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function LoadingTimeout({ onRetry }: { onRetry: () => void }) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Container maxWidth="sm">
        <div className="text-center space-y-4">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold text-destructive">Authentication Timeout</h2>
            <p className="text-sm text-muted-foreground">
              Authentication is taking longer than expected. This might be due to a slow connection or server issues.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-muted text-muted-foreground rounded-md hover:bg-muted/90 transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      </Container>
    </div>
  )
}

// Convenience exports for common loading patterns
export const AuthenticationLoading = () => <LoadingGuard variant="spinner" message="Authenticating..." />
export const PageLoading = () => <LoadingGuard variant="skeleton" />
export const QuickLoading = () => <LoadingGuard variant="minimal" message="Loading..." />
export const ProgressLoading = (props: { message?: string }) => (
  <LoadingGuard variant="spinner" showProgress message={props.message || "Loading..."} />
)