import { render, screen } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { ProtectedRoute, PublicRoute } from '../protected-route'

// Mock next-auth
jest.mock('next-auth/react')
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

const mockPush = jest.fn()
mockUseRouter.mockReturnValue({
  push: mockPush,
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
} as any)

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('shows loading when session is loading', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading',
      update: jest.fn(),
    })

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(screen.getByText('Checking authentication...')).toBeInTheDocument()
  })

  it('redirects to login when unauthenticated', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
      update: jest.fn(),
    })

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(mockPush).toHaveBeenCalledWith('/auth/login')
  })

  it('renders children when authenticated', () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      status: 'authenticated',
      update: jest.fn(),
    } as any)

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(screen.getByText('Protected Content')).toBeInTheDocument()
  })
})

describe('PublicRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('redirects to dashboard when authenticated', () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      status: 'authenticated',
      update: jest.fn(),
    } as any)

    render(
      <PublicRoute>
        <div>Public Content</div>
      </PublicRoute>
    )

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  it('renders children when unauthenticated', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
      update: jest.fn(),
    })

    render(
      <PublicRoute>
        <div>Public Content</div>
      </PublicRoute>
    )

    expect(screen.getByText('Public Content')).toBeInTheDocument()
  })
})