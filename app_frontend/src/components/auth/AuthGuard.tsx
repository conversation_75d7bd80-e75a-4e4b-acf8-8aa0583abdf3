"use client"

import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { useEffect, useState } from "react"
import { LoadingGuard } from "./LoadingGuard"
import { getAuthRedirectUrl } from "@/lib/auth/authHelpers"

interface AuthGuardProps {
  children: React.ReactNode
  redirectTo?: string
  fallback?: React.ReactNode
  requireAuth?: boolean
  loadingComponent?: React.ReactNode
}

export function AuthGuard({ 
  children, 
  redirectTo = "/auth/login",
  fallback,
  requireAuth = true,
  loadingComponent
}: AuthGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)

  useEffect(() => {
    // Only proceed if session loading is complete
    if (status === "loading") return

    // If authentication is required but user is not authenticated
    if (requireAuth && status === "unauthenticated") {
      setIsRedirecting(true)
      
      // Preserve the current URL for post-login redirect
      const currentUrl = window.location.pathname + window.location.search
      const callbackUrl = encodeURIComponent(currentUrl)
      
      // Navigate to login with callback URL
      const loginUrl = `${redirectTo}?callbackUrl=${callbackUrl}`
      router.push(loginUrl)
      return
    }

    // If authentication is not required and user is authenticated, 
    // they might be on a public page - allow access
    if (!requireAuth) {
      return
    }

    // If authenticated, ensure we're not in a redirecting state
    if (status === "authenticated" && isRedirecting) {
      setIsRedirecting(false)
    }
  }, [status, requireAuth, redirectTo, router, isRedirecting])

  // Show loading state while session is being fetched
  if (status === "loading" || isRedirecting) {
    if (loadingComponent) {
      return <>{loadingComponent}</>
    }
    return <LoadingGuard />
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && status === "unauthenticated") {
    // If a fallback component is provided, show it instead of redirecting
    if (fallback) {
      return <>{fallback}</>
    }
    
    // Return null while redirect is happening
    return null
  }

  // User is authenticated or authentication is not required
  return <>{children}</>
}

// Convenience wrapper for pages that always require authentication
export function RequireAuth({ children, ...props }: Omit<AuthGuardProps, "requireAuth">) {
  return (
    <AuthGuard requireAuth={true} {...props}>
      {children}
    </AuthGuard>
  )
}

// Convenience wrapper for pages that should be accessible to both authenticated and unauthenticated users
export function OptionalAuth({ children, ...props }: Omit<AuthGuardProps, "requireAuth">) {
  return (
    <AuthGuard requireAuth={false} {...props}>
      {children}
    </AuthGuard>
  )
}