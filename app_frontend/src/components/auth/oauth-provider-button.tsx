"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { cn } from "@/lib/utils"
import type { OAuthProvider } from "./provider-icons"

interface OAuthProviderButtonProps {
  provider: OAuthProvider
  onSignIn: (provider: string) => Promise<void>
  className?: string
  variant?: "default" | "outline"
  size?: "sm" | "default" | "lg"
  disabled?: boolean
}

export function OAuthProviderButton({
  provider,
  onSignIn,
  className,
  variant = "outline",
  size = "default",
  disabled = false
}: OAuthProviderButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleClick = async () => {
    if (disabled || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      await onSignIn(provider.name)
    } catch (err) {
      console.error(`OAuth ${provider.name} sign-in error:`, err)
      setError(`Failed to sign in with ${provider.displayName}`)
    } finally {
      setIsLoading(false)
    }
  }

  const Icon = provider.icon

  return (
    <div className="space-y-1">
      <Button
        variant={variant}
        size={size}
        onClick={handleClick}
        disabled={disabled || isLoading}
        className={cn(
          "relative",
          // Custom styling for outline variant to match provider colors
          variant === "outline" && [
            "border border-border hover:bg-muted/50",
            "text-foreground hover:text-foreground"
          ],
          // Custom styling for default variant to use provider colors
          variant === "default" && [
            provider.bgColor,
            provider.textColor,
            "border-0"
          ],
          className
        )}
      >
        {isLoading ? (
          <>
            <LoadingSpinner size="sm" className="mr-2" />
            Connecting...
          </>
        ) : (
          <>
            <Icon className="mr-2 h-4 w-4" />
            Continue with {provider.displayName}
          </>
        )}
      </Button>
      
      {error && (
        <p className="text-xs text-destructive text-center">{error}</p>
      )}
    </div>
  )
}

// Compact version for smaller spaces
export function OAuthProviderIconButton({
  provider,
  onSignIn,
  className,
  disabled = false
}: Omit<OAuthProviderButtonProps, "variant" | "size">) {
  const [isLoading, setIsLoading] = useState(false)

  const handleClick = async () => {
    if (disabled || isLoading) return

    setIsLoading(true)
    try {
      await onSignIn(provider.name)
    } catch (err) {
      console.error(`OAuth ${provider.name} sign-in error:`, err)
    } finally {
      setIsLoading(false)
    }
  }

  const Icon = provider.icon

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={cn(
        "h-10 w-10",
        className
      )}
      title={`Sign in with ${provider.displayName}`}
    >
      {isLoading ? (
        <LoadingSpinner size="sm" />
      ) : (
        <Icon className="h-4 w-4" />
      )}
    </Button>
  )
}