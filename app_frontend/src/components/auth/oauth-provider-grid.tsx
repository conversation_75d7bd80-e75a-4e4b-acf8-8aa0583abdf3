"use client"

import { OAuthProviderButton } from "./oauth-provider-button"
import { oauthProviders } from "./provider-icons"
import { OAuthErrorBoundary } from "./auth-error-boundary"
import { cn } from "@/lib/utils"
import { AlertTriangle } from "lucide-react"

interface OAuthProviderGridProps {
  onSignIn: (provider: string) => Promise<void>
  className?: string
  columns?: 1 | 2 | 3
  variant?: "default" | "outline"
  size?: "sm" | "default" | "lg"
  providers?: string[] // Optional: limit which providers to show
}

export function OAuthProviderGrid({
  onSignIn,
  className,
  columns = 2,
  variant = "outline",
  size = "default",
  providers
}: OAuthProviderGridProps) {
  // Filter providers if specific ones are requested
  const displayProviders = providers
    ? oauthProviders.filter(p => providers.includes(p.name))
    : oauthProviders

  // If no providers match the filter, show a fallback
  if (displayProviders.length === 0) {
    return (
      <div className={cn("text-center p-4", className)}>
        <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">
          No OAuth providers are currently available.
        </p>
      </div>
    )
  }

  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-2",
    3: "grid-cols-3"
  }

  return (
    <OAuthErrorBoundary>
      <div className={cn(
        "grid gap-3",
        gridCols[columns],
        className
      )}>
        {displayProviders.map((provider) => (
          <OAuthProviderButton
            key={provider.name}
            provider={provider}
            onSignIn={onSignIn}
            variant={variant}
            size={size}
            className="w-full"
          />
        ))}
      </div>
    </OAuthErrorBoundary>
  )
}