"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { Text } from "@/components/ui/typography"
import { Building2 } from "lucide-react"
import { apiClient } from "@/lib/api"
import { toast } from "react-hot-toast"

const ssoSchema = z.object({
  domain: z.string().min(1, "Domain is required").optional(),
  providerId: z.string().min(1, "Provider ID is required").optional()
}).refine((data) => data.domain || data.providerId, {
  message: "Either domain or provider ID is required",
  path: ["domain"]
})

type SSOFormData = z.infer<typeof ssoSchema>

interface SSOSignInFormProps {
  onSuccess?: () => void
  className?: string
}

export function SSOSignInForm({ onSuccess, className }: SSOSignInFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [inputType, setInputType] = useState<"domain" | "providerId">("domain")

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<SSOFormData>({
    resolver: zodResolver(ssoSchema)
  })

  const onSubmit = async (data: SSOFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await apiClient.post<{ url: string }>("/api/auth/sso/signin", {
        domain: inputType === "domain" ? data.domain : undefined,
        provider_id: inputType === "providerId" ? data.providerId : undefined
      })

      if (response.url) {
        // Redirect to SSO provider
        window.location.href = response.url
      } else {
        throw new Error("No redirect URL received")
      }

      if (onSuccess) {
        onSuccess()
      }
    } catch (error: any) {
      setError(error.message || "SSO sign-in failed")
      toast.error("SSO sign-in failed")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Building2 className="h-5 w-5 text-muted-foreground" />
          <Text variant="large" className="font-semibold">
            Enterprise Single Sign-On
          </Text>
        </div>

        {error && <ErrorMessage message={error} />}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <div className="flex gap-2 mb-3">
              <Button
                type="button"
                variant={inputType === "domain" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setInputType("domain")
                  reset()
                }}
              >
                Sign in with Domain
              </Button>
              <Button
                type="button"
                variant={inputType === "providerId" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setInputType("providerId")
                  reset()
                }}
              >
                Sign in with Provider ID
              </Button>
            </div>

            {inputType === "domain" ? (
              <div className="space-y-2">
                <Label htmlFor="domain">Organization Domain</Label>
                <Input
                  id="domain"
                  type="text"
                  placeholder="company.com"
                  {...register("domain")}
                  className={errors.domain ? "border-destructive" : ""}
                />
                {errors.domain && (
                  <Text variant="caption" className="text-destructive">
                    {errors.domain.message}
                  </Text>
                )}
                <Text variant="small" className="text-muted-foreground">
                  Enter your organization's email domain
                </Text>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="providerId">SSO Provider ID</Label>
                <Input
                  id="providerId"
                  type="text"
                  placeholder="sso-provider-id"
                  {...register("providerId")}
                  className={errors.providerId ? "border-destructive" : ""}
                />
                {errors.providerId && (
                  <Text variant="caption" className="text-destructive">
                    {errors.providerId.message}
                  </Text>
                )}
                <Text variant="small" className="text-muted-foreground">
                  Enter your SSO provider ID (provided by your IT admin)
                </Text>
              </div>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Connecting to SSO...
              </>
            ) : (
              "Continue with SSO"
            )}
          </Button>
        </form>
      </div>
    </div>
  )
}