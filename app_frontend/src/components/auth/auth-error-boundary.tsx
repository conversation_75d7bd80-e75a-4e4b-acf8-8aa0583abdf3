"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert<PERSON>riangle, RefreshCw } from "lucide-react"

interface AuthErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface AuthErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  showDetails?: boolean
}

export class AuthErrorBoundary extends React.Component<
  AuthErrorBoundaryProps,
  AuthErrorBoundaryState
> {
  constructor(props: AuthErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<AuthErrorBoundaryState> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Log the error for debugging
    console.error("Auth Error Boundary caught an error:", error, errorInfo)

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props

      if (Fallback && this.state.error) {
        return <Fallback error={this.state.error} retry={this.handleRetry} />
      }

      return (
        <AuthErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={this.handleRetry}
          showDetails={this.props.showDetails}
        />
      )
    }

    return this.props.children
  }
}

interface AuthErrorFallbackProps {
  error: Error | null
  errorInfo: React.ErrorInfo | null
  onRetry: () => void
  showDetails?: boolean
}

function AuthErrorFallback({
  error,
  errorInfo,
  onRetry,
  showDetails = false,
}: AuthErrorFallbackProps) {
  const isAuthError = error?.message?.toLowerCase().includes("auth") ||
                     error?.message?.toLowerCase().includes("session") ||
                     error?.message?.toLowerCase().includes("login")

  return (
    <div className="flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-destructive/20">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-destructive">
            {isAuthError ? "Authentication Error" : "Something went wrong"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p className="text-sm text-muted-foreground">
            {isAuthError
              ? "There was a problem with the authentication system. Please try again."
              : "An unexpected error occurred. Please try again or contact support if the problem persists."}
          </p>

          {showDetails && error && (
            <details className="text-left">
              <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
                Error Details
              </summary>
              <div className="mt-2 rounded bg-muted p-2 text-xs font-mono">
                <div className="text-destructive font-semibold">Error:</div>
                <div className="mb-2">{error.message}</div>
                {errorInfo && (
                  <>
                    <div className="text-destructive font-semibold">Stack:</div>
                    <div className="whitespace-pre-wrap text-xs">
                      {errorInfo.componentStack}
                    </div>
                  </>
                )}
              </div>
            </details>
          )}

          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button onClick={onRetry} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Functional wrapper for easier use
export function withAuthErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<AuthErrorBoundaryProps, "children">
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <AuthErrorBoundary {...errorBoundaryProps}>
      <Component {...props} ref={ref} />
    </AuthErrorBoundary>
  ))

  WrappedComponent.displayName = `withAuthErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Hook for manual error reporting
export function useAuthErrorHandler() {
  const handleError = React.useCallback((error: Error, context?: string) => {
    console.error(`Auth Error ${context ? `in ${context}` : ""}:`, error)
    
    // In a real app, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === "development") {
      console.group("Auth Error Details")
      console.error("Error:", error)
      console.error("Context:", context)
      console.error("Stack:", error.stack)
      console.groupEnd()
    }
  }, [])

  return { handleError }
}

// Specific error boundary for OAuth components
export function OAuthErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <AuthErrorBoundary
      onError={(error, errorInfo) => {
        console.error("OAuth Error:", error, errorInfo)
      }}
      fallback={({ error, retry }) => (
        <div className="text-center p-4 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
          <p className="text-sm text-destructive mb-3">
            Failed to load OAuth providers
          </p>
          <Button size="sm" onClick={retry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      )}
    >
      {children}
    </AuthErrorBoundary>
  )
}