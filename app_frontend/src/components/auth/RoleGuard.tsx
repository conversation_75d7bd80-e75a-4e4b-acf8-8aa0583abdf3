"use client"

import { useSession } from "next-auth/react"
import { AuthGuard } from "./AuthGuard"
import { Container } from "@/components/ui/container"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, ArrowLeft, <PERSON>ert<PERSON>riangle } from "lucide-react"
import Link from "next/link"

// Define role hierarchy and permissions
export type UserRole = "admin" | "editor" | "author" | "user" | "guest"
export type Permission = "read" | "write" | "delete" | "admin" | "manage_users" | "manage_content"

interface RoleGuardProps {
  children: React.ReactNode
  requiredRole?: UserRole | UserRole[]
  requiredPermissions?: Permission | Permission[]
  fallback?: React.ReactNode
  redirectTo?: string
  allowedRoles?: UserRole[]
  deniedRoles?: UserRole[]
  customAccessCheck?: (user: any) => boolean
  showAccessDenied?: boolean
  accessDeniedMessage?: string
}

// Role hierarchy mapping (higher number = higher privilege)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  guest: 0,
  user: 1,
  author: 2,
  editor: 3,
  admin: 4,
}

// Permission mapping by role
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  guest: ["read"],
  user: ["read"],
  author: ["read", "write"],
  editor: ["read", "write", "manage_content"],
  admin: ["read", "write", "delete", "admin", "manage_users", "manage_content"],
}

export function RoleGuard({
  children,
  requiredRole,
  requiredPermissions,
  fallback,
  redirectTo,
  allowedRoles,
  deniedRoles,
  customAccessCheck,
  showAccessDenied = true,
  accessDeniedMessage,
}: RoleGuardProps) {
  const { data: session } = useSession()

  // First, ensure user is authenticated
  return (
    <AuthGuard redirectTo={redirectTo}>
      <RoleCheck
        session={session}
        requiredRole={requiredRole}
        requiredPermissions={requiredPermissions}
        allowedRoles={allowedRoles}
        deniedRoles={deniedRoles}
        customAccessCheck={customAccessCheck}
        showAccessDenied={showAccessDenied}
        accessDeniedMessage={accessDeniedMessage}
        fallback={fallback}
      >
        {children}
      </RoleCheck>
    </AuthGuard>
  )
}

function RoleCheck({
  children,
  session,
  requiredRole,
  requiredPermissions,
  allowedRoles,
  deniedRoles,
  customAccessCheck,
  showAccessDenied,
  accessDeniedMessage,
  fallback,
}: RoleGuardProps & { session: any }) {
  // Extract user role from session (fallback to 'user' if not specified)
  const userRole = (session?.user?.role as UserRole) || "user"
  const userPermissions = ROLE_PERMISSIONS[userRole] || []

  // Check if user has access
  const hasAccess = checkUserAccess({
    userRole,
    userPermissions,
    requiredRole,
    requiredPermissions,
    allowedRoles,
    deniedRoles,
    customAccessCheck,
    user: session?.user,
  })

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    if (showAccessDenied) {
      return (
        <AccessDenied 
          userRole={userRole}
          requiredRole={requiredRole}
          requiredPermissions={requiredPermissions}
          customMessage={accessDeniedMessage}
        />
      )
    }

    return null
  }

  return <>{children}</>
}

function checkUserAccess({
  userRole,
  userPermissions,
  requiredRole,
  requiredPermissions,
  allowedRoles,
  deniedRoles,
  customAccessCheck,
  user,
}: {
  userRole: UserRole
  userPermissions: Permission[]
  requiredRole?: UserRole | UserRole[]
  requiredPermissions?: Permission | Permission[]
  allowedRoles?: UserRole[]
  deniedRoles?: UserRole[]
  customAccessCheck?: (user: any) => boolean
  user: any
}): boolean {
  // If custom access check is provided, use it
  if (customAccessCheck) {
    return customAccessCheck(user)
  }

  // Check denied roles first (explicit deny)
  if (deniedRoles && deniedRoles.includes(userRole)) {
    return false
  }

  // Check allowed roles (explicit allow)
  if (allowedRoles && allowedRoles.length > 0) {
    return allowedRoles.includes(userRole)
  }

  // Check required role(s)
  if (requiredRole) {
    const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    const hasRequiredRole = requiredRoles.some(role => 
      hasRoleAccess(userRole, role)
    )
    if (!hasRequiredRole) {
      return false
    }
  }

  // Check required permissions
  if (requiredPermissions) {
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    const hasAllPermissions = permissions.every(permission => 
      userPermissions.includes(permission)
    )
    if (!hasAllPermissions) {
      return false
    }
  }

  return true
}

function hasRoleAccess(userRole: UserRole, requiredRole: UserRole): boolean {
  const userLevel = ROLE_HIERARCHY[userRole] || 0
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0
  return userLevel >= requiredLevel
}

function AccessDenied({
  userRole,
  requiredRole,
  requiredPermissions,
  customMessage,
}: {
  userRole: UserRole
  requiredRole?: UserRole | UserRole[]
  requiredPermissions?: Permission | Permission[]
  customMessage?: string
}) {
  const getRequiredRoleText = () => {
    if (!requiredRole) return null
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    return roles.join(" or ")
  }

  const getRequiredPermissionsText = () => {
    if (!requiredPermissions) return null
    const perms = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    return perms.join(", ")
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Container maxWidth="sm">
        <Card className="border-destructive/20">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
              <Shield className="h-8 w-8 text-destructive" />
            </div>
            <CardTitle className="text-destructive">Access Denied</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            {customMessage ? (
              <p className="text-muted-foreground">{customMessage}</p>
            ) : (
              <div className="space-y-2">
                <p className="text-muted-foreground">
                  You don't have permission to access this page.
                </p>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Your role: <span className="font-medium text-foreground">{userRole}</span></div>
                  {getRequiredRoleText() && (
                    <div>Required role: <span className="font-medium text-foreground">{getRequiredRoleText()}</span></div>
                  )}
                  {getRequiredPermissionsText() && (
                    <div>Required permissions: <span className="font-medium text-foreground">{getRequiredPermissionsText()}</span></div>
                  )}
                </div>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button asChild variant="outline">
                <Link href="/dashboard" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Dashboard
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/contact" className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Request Access
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </Container>
    </div>
  )
}

// Convenience components for common role patterns
export function AdminOnly({ children, ...props }: Omit<RoleGuardProps, "requiredRole">) {
  return (
    <RoleGuard requiredRole="admin" {...props}>
      {children}
    </RoleGuard>
  )
}

export function EditorOrAdmin({ children, ...props }: Omit<RoleGuardProps, "allowedRoles">) {
  return (
    <RoleGuard allowedRoles={["editor", "admin"]} {...props}>
      {children}
    </RoleGuard>
  )
}

export function AuthorAndAbove({ children, ...props }: Omit<RoleGuardProps, "requiredRole">) {
  return (
    <RoleGuard requiredRole="author" {...props}>
      {children}
    </RoleGuard>
  )
}

export function RequirePermissions({ 
  children, 
  permissions, 
  ...props 
}: Omit<RoleGuardProps, "requiredPermissions"> & { permissions: Permission | Permission[] }) {
  return (
    <RoleGuard requiredPermissions={permissions} {...props}>
      {children}
    </RoleGuard>
  )
}

// Export types and utilities for external use
export { ROLE_HIERARCHY, ROLE_PERMISSIONS, hasRoleAccess, checkUserAccess }