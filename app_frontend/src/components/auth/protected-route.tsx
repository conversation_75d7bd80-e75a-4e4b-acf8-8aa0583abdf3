"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Loading } from "@/components/layout/loading"

interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
}

export function ProtectedRoute({ 
  children, 
  redirectTo = "/auth/login" 
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push(redirectTo)
    }
  }, [status, router, redirectTo])

  if (status === "loading") {
    return <Loading fullScreen message="Checking authentication..." />
  }

  if (status === "unauthenticated") {
    return null
  }

  return <>{children}</>
}

export function PublicRoute({ 
  children, 
  redirectTo = "/dashboard" 
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "authenticated") {
      router.push(redirectTo)
    }
  }, [status, router, redirectTo])

  if (status === "loading") {
    return <Loading fullScreen message="Checking authentication..." />
  }

  if (status === "authenticated") {
    return null
  }

  return <>{children}</>
}