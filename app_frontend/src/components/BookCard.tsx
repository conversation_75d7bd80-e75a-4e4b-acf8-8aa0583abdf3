"use client";

import React from "react";
import { Book, Clock, CheckCircle, XCircle, Eye, Upload } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import toast from "react-hot-toast";

import { api } from "../utils/api";
import { Book as BookType } from "../types";

interface BookCardProps {
  book: BookType;
  onUpdate: () => void;
}

const BookCard: React.FC<BookCardProps> = ({ book, onUpdate }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "generating":
        return "bg-yellow-100 text-yellow-800";
      case "awaiting_approval":
        return "bg-blue-100 text-blue-800";
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "published":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "generating":
        return <Clock className="h-4 w-4" />;
      case "awaiting_approval":
        return <Eye className="h-4 w-4" />;
      case "approved":
        return <CheckCircle className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "published":
        return <Upload className="h-4 w-4" />;
      default:
        return <Book className="h-4 w-4" />;
    }
  };

  const handleApprove = async () => {
    try {
      await api.post(`/books/${book.id}/approve`);
      toast.success("Book approved!");
      onUpdate();
    } catch (error) {
      toast.error("Failed to approve book");
    }
  };

  const handleReject = async () => {
    const reason = prompt("Reason for rejection (optional):");
    try {
      await api.post(`/books/${book.id}/reject`, { reason });
      toast.success("Book rejected");
      onUpdate();
    } catch (error) {
      toast.error("Failed to reject book");
    }
  };

  const handlePublish = async () => {
    try {
      await api.post(`/publications/${book.id}/publish`, {
        price: 2.99,
        royalty_rate: 70,
        auto_publish: false,
      });
      toast.success("Publication started!");
      onUpdate();
    } catch (error) {
      toast.error("Failed to start publication");
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 truncate">
              {book.title}
            </h3>
            {book.subtitle && (
              <p className="text-sm text-gray-600 truncate">{book.subtitle}</p>
            )}
          </div>
          <div
            className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(book.status)}`}
          >
            {getStatusIcon(book.status)}
            <span className="capitalize">{book.status.replace("_", " ")}</span>
          </div>
        </div>

        {/* Metadata */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Category:</span>
            <span className="text-gray-900 capitalize">{book.category}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Word Count:</span>
            <span className="text-gray-900">
              {book.word_count?.toLocaleString() || "N/A"}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Created:</span>
            <span className="text-gray-900">
              {formatDistanceToNow(new Date(book.created_at), {
                addSuffix: true,
              })}
            </span>
          </div>
          {book.quality_score && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Quality Score:</span>
              <span className="text-gray-900">
                {Math.round(book.quality_score)}/100
              </span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          {book.status === "awaiting_approval" && (
            <>
              <button
                onClick={handleApprove}
                className="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
              >
                Approve
              </button>
              <button
                onClick={handleReject}
                className="flex-1 px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
              >
                Reject
              </button>
            </>
          )}

          {book.status === "approved" && (
            <button
              onClick={handlePublish}
              className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
            >
              Publish to KDP
            </button>
          )}

          {book.status === "rejected" && book.rejection_reason && (
            <div className="w-full text-sm text-red-600 bg-red-50 p-2 rounded">
              Rejected: {book.rejection_reason}
            </div>
          )}

          {book.status === "generating" && (
            <div className="w-full text-sm text-yellow-600 bg-yellow-50 p-2 rounded text-center">
              Generating manuscript...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookCard;
