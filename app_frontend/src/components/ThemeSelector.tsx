import React from "react";

interface ThemeSelectorProps {
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  category: string;
}

const themes = {
  clean_professional: {
    name: "Clean Professional",
    description:
      "Modern, clean design perfect for business and self-help books",
    preview: "/theme-previews/clean-professional.png",
  },
  modern_minimalist: {
    name: "Modern Minimalist",
    description: "Simple, elegant layout with lots of white space",
    preview: "/theme-previews/modern-minimalist.png",
  },
  classic_literary: {
    name: "Classic Literary",
    description: "Traditional book design with ornate elements",
    preview: "/theme-previews/classic-literary.png",
  },
  tech_business: {
    name: "Tech Business",
    description: "Corporate-focused design for professional content",
    preview: "/theme-previews/tech-business.png",
  },
  romantic_elegant: {
    name: "Romantic Elegant",
    description: "Elegant design perfect for romance novels",
    preview: "/theme-previews/romantic-elegant.png",
  },
};

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  selectedTheme,
  onThemeChange,
  category,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Object.entries(themes).map(([key, theme]) => (
        <div
          key={key}
          className={`border rounded-lg p-4 cursor-pointer transition-all ${
            selectedTheme === key
              ? "border-blue-500 bg-blue-50"
              : "border-gray-200 hover:border-gray-300"
          }`}
          onClick={() => onThemeChange(key)}
        >
          <div className="aspect-video bg-gray-100 rounded mb-3">
            {/* Theme preview image would go here */}
            <div className="w-full h-full flex items-center justify-center text-gray-500">
              Preview
            </div>
          </div>
          <h3 className="font-medium text-gray-900">{theme.name}</h3>
          <p className="text-sm text-gray-600 mt-1">{theme.description}</p>
        </div>
      ))}
    </div>
  );
};

export default ThemeSelector;
