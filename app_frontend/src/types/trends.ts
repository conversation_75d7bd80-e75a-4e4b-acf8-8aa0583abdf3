// Trends Page Types

export type TimePeriod = '1D' | '7D' | '30D' | '3M' | '1Y' | 'custom'

export interface TrendsFilters {
  assetTypes: string[]
  regions: string[]
  timePeriod: TimePeriod
  customDateRange?: [Date, Date]
}

export interface FilterPreset {
  id: string
  name: string
  filters: Partial<TrendsFilters>
}

export interface AssetType {
  id: string
  name: string
  category: string
}

export interface Region {
  id: string
  name: string
  code: string
  continent: string
}

export interface TrendsMetrics {
  totalVolume: number
  avgPrice: number
  avgHoldingDuration: number
  volumeChange: number
  priceChange: number
  durationChange: number
}

export interface ChartDataPoint {
  timestamp: string
  value: number
  volume?: number
  assetType?: string
}

export interface YieldCurvePoint {
  duration: number
  historicalYield: number
  forecastYield: number
  confidenceInterval: [number, number]
}

export interface GeographicDataPoint {
  region: string
  lat: number
  lng: number
  volume: number
  avgPrice: number
  sentiment: number
  count: number
}

export interface PriceForecast {
  assetType: string
  currentPrice: number
  predictedPrice: number
  confidenceInterval: [number, number]
  timeHorizon: string
}

export interface YieldForecast {
  assetType: string
  currentYield: number
  predictedYield: number
  confidenceInterval: [number, number]
  timeHorizon: string
}

export interface SentimentData {
  value: number
  trend: 'up' | 'down' | 'stable'
  confidence: number
  factors: string[]
}

export interface AICommentary {
  content: string
  sources: string[]
  confidence: number
  lastUpdated: Date
  validatedSchema: boolean
}

export interface TrendsData {
  metrics: TrendsMetrics
  chartData: ChartDataPoint[]
  heatmapData: GeographicDataPoint[]
  forecasts: {
    priceForecasts: PriceForecast[]
    yieldForecasts: YieldForecast[]
    sentimentIndex: SentimentData
  }
  commentary: AICommentary
  filters: TrendsFilters
}

export interface ColorScale {
  min: string
  mid: string
  max: string
}

export interface ChartEvent {
  type: 'hover' | 'click' | 'zoom'
  data: any
  timestamp?: string
}

export interface AlertConfig {
  threshold: number
  assetTypes: string[]
  regions: string[]
  webhook_url?: string
}

// Component Props Types

export interface TrendsHeaderProps {
  lastUpdated: Date
  isLoading: boolean
  onRefresh: () => void
  onExport: (format: 'csv' | 'json' | 'pdf') => void
  onConfigureAlerts: () => void
}

export interface TrendsFiltersProps {
  filters: TrendsFilters
  onFiltersChange: (filters: Partial<TrendsFilters>) => void
  availableAssetTypes: AssetType[]
  availableRegions: Region[]
  presets: FilterPreset[]
}

export interface TrendsMetricsProps {
  metrics: TrendsMetrics
  loading: boolean
  timeframe: string
}

export interface TrendsChartsProps {
  volumeData: ChartDataPoint[]
  priceData: ChartDataPoint[]
  yieldData: YieldCurvePoint[]
  onChartInteraction: (event: ChartEvent) => void
  selectedTimeRange: [Date, Date]
}

export interface TrendsHeatmapProps {
  data: GeographicDataPoint[]
  metric: 'volume' | 'price' | 'sentiment'
  onRegionClick: (region: string) => void
  onMetricChange: (metric: string) => void
  colorScale: ColorScale
}

export interface TrendsForecastsProps {
  forecasts: {
    priceForecasts: PriceForecast[]
    yieldForecasts: YieldForecast[]
    sentimentIndex: SentimentData
  }
  commentary: AICommentary
  confidenceLevel: number
  onCommentaryRefresh: () => void
}

export interface TrendsActionsProps {
  onExport: (format: 'csv' | 'json' | 'pdf') => void
  onSetAlert: () => void
  onShare: () => void
  onConfigureAPI: () => void
}

// Chart Component Props

export interface VolumeChartProps {
  data: ChartDataPoint[]
  onInteraction: (event: ChartEvent) => void
  height?: number
}

export interface SentimentGaugeProps {
  value: number
  trend: 'up' | 'down' | 'stable'
  confidence: number
}