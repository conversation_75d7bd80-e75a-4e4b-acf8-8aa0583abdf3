import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
    } & DefaultSession["user"]
    accessToken?: string
    error?: string
  }

  interface User extends DefaultUser {
    id: string
    email: string
    name?: string | null
    image?: string | null
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    id: string
    email?: string
    accessToken?: string
    refreshToken?: string
    error?: string
    iat?: number
    exp?: number
  }
}

// Authentication response types
export interface AuthResponse {
  user?: {
    id: string
    email: string
    name?: string
    avatar_url?: string
  }
  access_token?: string
  refresh_token?: string
  error?: string
  message?: string
}

// OAuth callback response
export interface OAuthCallbackResponse {
  user: {
    id: string
    email: string
    name?: string
    avatar_url?: string
  }
  access_token: string
  refresh_token?: string
}

// Authentication error types
export enum AuthErrorType {
  CredentialsSignIn = "CredentialsSignIn",
  OAuthSignIn = "OAuthSignIn",
  OAuthCallback = "OAuthCallback",
  OAuthCreateAccount = "OAuthCreateAccount",
  OAuthAccountNotLinked = "OAuthAccountNotLinked",
  SessionRequired = "SessionRequired",
  Default = "Default",
}

export interface AuthError {
  type: AuthErrorType
  message: string
  code?: string
}

// Provider types
export type OAuthProviderType = "google" | "apple"

// Sign in options
export interface SignInOptions {
  email?: string
  password?: string
  provider?: OAuthProviderType
  callbackUrl?: string
  redirect?: boolean
}

// User profile type
export interface UserProfile {
  id: string
  email: string
  name?: string
  image?: string
  emailVerified?: Date | null
  createdAt?: Date
  updatedAt?: Date
}