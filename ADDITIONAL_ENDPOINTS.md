# API Endpoints Documentation

## Overview

This document provides comprehensive coverage of all API endpoints in the AI E-book Publishing Platform, including existing endpoints and those that need to be implemented.

## Database Tables Coverage

### Tables WITH Existing APIs ✅

#### 1. Users (`users` table)
**Router**: `app/api/supabase_auth.py` → `/api/auth/*`
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/oauth/signin` - OAuth authentication
- `POST /api/auth/sso/signin` - Enterprise SSO
- `POST /api/auth/oauth/callback` - OAuth callback

#### 2. Books (`books` table)
**Router**: `app/api/supabase_books.py` → `/api/books/*`
- `GET /api/books/test` - Test endpoint
- `GET /api/books/public/recent` - Recent public books
- `GET /api/books/` - Get user's books
- `POST /api/books/` - Create new book
- `GET /api/books/{book_id}` - Get specific book
- `PUT /api/books/{book_id}` - Update book
- `DELETE /api/books/{book_id}` - Delete book
- `POST /api/books/{book_id}/generate` - Generate manuscript

#### 3. Publications (`publications` table)
**Router**: `app/api/publications.py` → `/api/publications/*`
- `GET /api/publications/` - List publications
- `POST /api/publications/` - Create publication
- `GET /api/publications/{pub_id}` - Get publication
- `PUT /api/publications/{pub_id}` - Update publication
- `DELETE /api/publications/{pub_id}` - Delete publication
- `GET /api/publications/summary/statistics` - Publication statistics

#### 4. Trends & Trend Analyses (`trends`, `trend_analyses` tables)
**Router**: `app/api/trends.py` → `/api/trends/*`
- `GET /api/trends/` - Get trends data
- `GET /api/trends/{trend_id}/analysis` - Get trend analysis
- `GET /api/trends/predictions` - Get trend predictions
- `GET /api/trends/realtime` - Real-time trends
- `GET /api/trends/search` - Search trends

#### 5. Sales Data (`sales_data` table)
**Router**: `app/api/analytics.py` → `/api/analytics/*`
- `GET /api/analytics/dashboard` - Dashboard analytics
- `GET /api/analytics/revenue` - Revenue analytics
- `GET /api/analytics/audience` - Audience analytics

#### 6. Feedback Metrics (`feedback_metrics` table)
**Router**: `app/api/feedback.py` → `/api/feedback/*`
- `GET /api/feedback/` - Get feedback data
- `POST /api/feedback/` - Submit feedback
- `GET /api/feedback/analytics` - Feedback analytics
- `GET /api/feedback/trend-analysis` - Feedback trends

#### 7. VERL Training (`verl_training_jobs`, `model_performance` tables)
**Router**: `app/api/monitoring.py` → `/api/monitoring/*`
- `GET /api/monitoring/verl` - VERL monitoring data
- `GET /api/monitoring/system` - System monitoring
- `GET /api/monitoring/performance` - Performance metrics
- `GET /api/monitoring/alerts` - Monitoring alerts

#### 8. Sales Predictions (`sales_predictions` table)
**Router**: `app/api/predictions.py` → `/api/predictions/*`
- `GET /api/predictions/` - Get predictions
- `POST /api/predictions/` - Create prediction
- `GET /api/predictions/{pred_id}` - Get specific prediction

#### 9. Compliance (`compliance_audit_events` table)
**Router**: `app/api/compliance.py` → `/api/compliance/*`
- `GET /api/compliance/audit-events` - Audit events
- `POST /api/compliance/audit-events` - Create audit event
- `GET /api/compliance/reports` - Compliance reports

#### 10. Security (`security_audit_events` table)
**Router**: `app/api/security.py` → `/api/security/*`
- `GET /api/security/threats` - Security threats
- `GET /api/security/audit-log` - Security audit log
- `POST /api/security/report-incident` - Report security incident

#### 11. Agents (`agents` processing)
**Router**: `app/api/agents.py` → `/api/agents/*`
- `POST /api/agents/generate-manuscript` - Generate manuscript
- `GET /api/agents/tasks` - Get agent tasks
- `GET /api/agents/tasks/{task_id}` - Get specific task
- `GET /api/agents/tasks/{task_id}/status` - Get task status
- `POST /api/agents/analyze-trends` - Analyze trends
- `POST /api/agents/generate-cover` - Generate cover

---

### Tables WITHOUT APIs ❌ (NEED IMPLEMENTATION)

#### 1. User Settings & Preferences ✅ **IMPLEMENTED**
**Router**: `app/api/users.py` → `/api/users/*`
- `GET /api/users/profile` - Get user profile
- `PATCH /api/users/profile` - Update user profile
- `GET /api/users/preferences` - Get user preferences
- `PATCH /api/users/preferences` - Update user preferences
- `GET /api/users/publishing-settings` - Get publishing settings
- `PATCH /api/users/publishing-settings` - Update publishing settings
- `GET /api/users/integrations` - Get platform integrations
- `PATCH /api/users/integrations/{platform}` - Toggle integration
- `GET /api/users/security` - Get security settings
- `PATCH /api/users/security` - Update security settings
- `GET /api/users/sessions` - Get active sessions
- `DELETE /api/users/sessions/{session_id}` - Terminate session
- `PATCH /api/users/change-password` - Change password
- `GET /api/users/api-keys` - Get user's API keys
- `POST /api/users/api-keys` - Create new API key
- `POST /api/users/api-keys/{key_id}/regenerate` - Regenerate API key

#### 2. API Keys Management (`api_keys`, `api_key_usage` tables) ✅ **IMPLEMENTED**
**Note**: Implemented as part of User Settings API (`/api/users/api-keys/*`)
- ✅ `GET /api/users/api-keys` - List user's API keys
- ✅ `POST /api/users/api-keys` - Create new API key
- ✅ `POST /api/users/api-keys/{key_id}/regenerate` - Regenerate API key
- ⏳ `GET /api/api-keys/{key_id}` - Get API key details (future)
- ⏳ `PUT /api/api-keys/{key_id}` - Update API key (future)
- ⏳ `DELETE /api/api-keys/{key_id}` - Delete API key (future)
- ⏳ `GET /api/api-keys/{key_id}/usage` - Get usage statistics (future)
- ⏳ `GET /api/api-keys/usage/summary` - Usage summary (future)

#### 3. Market Analysis (`market_analyses` table) ✅ **IMPLEMENTED**
**Router**: `app/api/market_analysis.py` → `/api/market-analysis/*`
- ✅ `GET /api/market-analysis/` - List market analyses
- ✅ `POST /api/market-analysis/` - Create market analysis
- ✅ `GET /api/market-analysis/{analysis_id}` - Get specific analysis
- ✅ `PUT /api/market-analysis/{analysis_id}` - Update analysis
- ✅ `DELETE /api/market-analysis/{analysis_id}` - Delete analysis
- ✅ `GET /api/market-analysis/{analysis_id}/insights` - Get insights
- ✅ `POST /api/market-analysis/{analysis_id}/refresh` - Refresh analysis

#### 4. Prediction Accuracy (`prediction_accuracy` table) ✅ **IMPLEMENTED**
**Router**: `app/api/prediction_accuracy.py` → `/api/predictions/accuracy/*`
- ✅ `GET /api/predictions/accuracy/` - Get accuracy metrics
- ✅ `POST /api/predictions/accuracy/` - Record actual results
- ✅ `GET /api/predictions/accuracy/{pred_id}` - Get specific accuracy
- ✅ `GET /api/predictions/accuracy/summary/dashboard` - Accuracy summary
- ✅ `GET /api/predictions/accuracy/models/comparison` - Model performance comparison

#### 5. Market Data (`scraped_market_data` table)
**Missing Endpoints**: `/api/market-data/*`
- `GET /api/market-data/` - List scraped market data
- `POST /api/market-data/` - Add market data
- `GET /api/market-data/{data_id}` - Get specific data
- `DELETE /api/market-data/{data_id}` - Delete market data
- `GET /api/market-data/search` - Search market data
- `POST /api/market-data/scrape` - Trigger scraping

#### 6. GDPR Compliance (`gdpr_data_subject_requests` table)
**Missing Endpoints**: `/api/gdpr/*`
- `GET /api/gdpr/requests/` - List GDPR requests
- `POST /api/gdpr/requests/` - Create GDPR request
- `GET /api/gdpr/requests/{request_id}` - Get specific request
- `PUT /api/gdpr/requests/{request_id}` - Update request status
- `POST /api/gdpr/requests/{request_id}/process` - Process request
- `GET /api/gdpr/data-export/{user_id}` - Export user data
- `POST /api/gdpr/data-deletion/{user_id}` - Delete user data

#### 7. Processing Activities (`processing_activities` table)
**Missing Endpoints**: `/api/processing/*`
- `GET /api/processing/activities/` - List processing activities
- `POST /api/processing/activities/` - Create activity
- `GET /api/processing/activities/{activity_id}` - Get specific activity
- `PUT /api/processing/activities/{activity_id}` - Update activity
- `DELETE /api/processing/activities/{activity_id}` - Delete activity

#### 8. Privacy Impact Assessments (`privacy_impact_assessments` table)
**Missing Endpoints**: `/api/privacy/*`
- `GET /api/privacy/assessments/` - List PIAs
- `POST /api/privacy/assessments/` - Create PIA
- `GET /api/privacy/assessments/{pia_id}` - Get specific PIA
- `PUT /api/privacy/assessments/{pia_id}` - Update PIA
- `DELETE /api/privacy/assessments/{pia_id}` - Delete PIA

#### 9. Data Retention (`data_retention_policies` table)
**Missing Endpoints**: `/api/retention/*`
- `GET /api/retention/policies/` - List retention policies
- `POST /api/retention/policies/` - Create policy
- `GET /api/retention/policies/{policy_id}` - Get specific policy
- `PUT /api/retention/policies/{policy_id}` - Update policy
- `DELETE /api/retention/policies/{policy_id}` - Delete policy
- `POST /api/retention/execute/{policy_id}` - Execute retention

#### 10. Data Anonymization (`data_anonymization_log` table)
**Missing Endpoints**: `/api/anonymization/*`
- `GET /api/anonymization/logs/` - List anonymization logs
- `POST /api/anonymization/execute` - Execute anonymization
- `GET /api/anonymization/logs/{log_id}` - Get specific log
- `POST /api/anonymization/rollback/{log_id}` - Rollback if possible

---

## Implementation Priority

### Phase 1: Critical User Endpoints ✅ **COMPLETED**
1. ✅ **User Settings API** (`/api/users/*`) - Complete with 17 endpoints
2. ✅ **API Keys Management** (`/api/users/api-keys/*`) - Core functionality implemented

### Phase 2: Business Intelligence ✅ **COMPLETED**
3. ✅ **Market Analysis API** (`/api/market-analysis/*`) - Business insights
4. ✅ **Prediction Accuracy API** (`/api/predictions/accuracy/*`) - ML improvement
5. **Market Data API** (`/api/market-data/*`) - Research capabilities (NEXT)

### Phase 3: Compliance (FUTURE)
6. **GDPR API** (`/api/gdpr/*`) - Legal compliance
7. **Processing Activities API** (`/api/processing/*`) - Compliance tracking
8. **Privacy Impact Assessments** (`/api/privacy/*`) - Risk management
9. **Data Retention API** (`/api/retention/*`) - Data lifecycle
10. **Anonymization API** (`/api/anonymization/*`) - Privacy protection

---

## Authentication Requirements

All endpoints require:
- **Authentication**: JWT Bearer token from Supabase Auth
- **Authorization**: User can only access their own data (RLS)
- **Rate Limiting**: Applied per endpoint type
- **Request Validation**: Pydantic schemas for all inputs
- **Error Handling**: Standardized error responses

## Next Steps

1. ✅ **Fix User-Agent header** - Remove from frontend
2. ✅ **Implement Phase 1** - User settings and API keys complete with 17 endpoints
3. ✅ **Update main router** - New `/api/users/*` endpoints registered
4. ✅ **Create database schema updates** - `schema_updates_users_settings.sql` created
5. ✅ **Database schema integrated** - All migrations merged into `supabase_production_schema.sql`
6. ✅ **Implement Phase 2** - Business intelligence endpoints
7. ⏳ **Implement Phase 3** - Compliance endpoints
8. ⏳ **Add comprehensive tests** - Unit and integration tests for user settings
9. ⏳ **Update OpenAPI docs** - Auto-generated documentation

This implementation will provide complete API coverage for all 28 database tables and enable full frontend functionality.