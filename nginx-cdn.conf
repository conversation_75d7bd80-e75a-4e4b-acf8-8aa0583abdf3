# Nginx configuration for CDN-style static asset serving
# Optimized for high-performance static file delivery with caching

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Open file cache
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=static:10m rate=10r/s;

    # Main server block for static assets
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Static assets with versioning
        location /static/ {
            alias /usr/share/nginx/html/static/;
            
            # Rate limiting
            limit_req zone=static burst=20 nodelay;
            
            # Cache headers for versioned assets
            location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header Vary "Accept-Encoding";
                
                # ETag support
                etag on;
                
                # CORS for fonts and assets
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }
                
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
            }
            
            # Manuscripts and covers with shorter cache
            location ~* \.(pdf|docx|epub)$ {
                expires 7d;
                add_header Cache-Control "public";
                add_header Vary "Accept-Encoding";
            }
            
            # Default static file handling
            try_files $uri $uri/ =404;
        }

        # Book covers
        location /static/covers/ {
            alias /usr/share/nginx/html/static/covers/;
            expires 30d;
            add_header Cache-Control "public";
            add_header Vary "Accept-Encoding";
            
            # Image optimization headers
            add_header X-Content-Type-Options "nosniff";
            
            # Conditional requests support
            if_modified_since exact;
            etag on;
        }

        # Generated manuscripts
        location /static/manuscripts/ {
            alias /usr/share/nginx/html/static/manuscripts/;
            expires 7d;
            add_header Cache-Control "public";
            add_header Vary "Accept-Encoding";
            
            # Content disposition for downloads
            add_header Content-Disposition "attachment";
        }

        # Published books
        location /static/published/ {
            alias /usr/share/nginx/html/static/published/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
            
            # Content disposition for downloads
            add_header Content-Disposition "attachment";
        }

        # Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Asset manifest
        location = /static/asset-manifest.json {
            expires 1h;
            add_header Cache-Control "public";
            add_header Content-Type "application/json";
        }

        # Favicon handling
        location = /favicon.ico {
            alias /usr/share/nginx/html/static/favicon.ico;
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }

        # Robots.txt
        location = /robots.txt {
            alias /usr/share/nginx/html/static/robots.txt;
            expires 1d;
            add_header Cache-Control "public";
            access_log off;
        }

        # Default location
        location / {
            return 404;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }

    # Additional server block for SSL termination (if needed)
    # server {
    #     listen 443 ssl http2;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     # SSL configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    #     
    #     # Include the same location blocks as above
    # }
}