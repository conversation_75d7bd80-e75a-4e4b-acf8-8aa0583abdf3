CREATE EXTENSION
CREATE EXTENSION
CREATE EXTENSION
psql:supabase_production_schema.sql:20: NOTICE:  view "user_analytics" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:21: NOTICE:  view "book_performance" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:22: NOTICE:  view "prediction_performance_view" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:23: NOTICE:  view "market_opportunities_view" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:24: NOTICE:  view "database_health_view" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:25: NOTICE:  view "query_performance_view" does not exist, skipping
DROP VIEW
psql:supabase_production_schema.sql:28: NOTICE:  function update_updated_at_column() does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:29: NOTICE:  function calculate_book_quality_score(uuid) does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:30: NOTICE:  function update_user_analytics(uuid) does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:31: NOTICE:  function anonymize_user_data(uuid) does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:32: NOTICE:  function generate_compliance_report(date,date) does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:33: NOTICE:  function analyze_database_performance() does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:34: NOTICE:  function update_api_key_updated_at() does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:35: NOTICE:  function increment_api_key_usage(text) does not exist, skipping
DROP FUNCTION
psql:supabase_production_schema.sql:38: NOTICE:  table "compliance_audit_events" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:39: NOTICE:  table "data_retention_policies" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:40: NOTICE:  table "data_anonymization_log" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:41: NOTICE:  table "privacy_impact_assessments" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:42: NOTICE:  table "processing_activities" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:43: NOTICE:  table "gdpr_data_subject_requests" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:44: NOTICE:  table "oauth_refresh_tokens" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:45: NOTICE:  table "oauth_authorization_codes" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:46: NOTICE:  table "oauth_access_tokens" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:47: NOTICE:  table "api_key_usage" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:48: NOTICE:  table "api_keys" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:49: NOTICE:  table "security_audit_events" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:50: NOTICE:  table "prediction_accuracy" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:51: NOTICE:  table "market_analyses" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:52: NOTICE:  table "sales_predictions" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:53: NOTICE:  table "model_performance" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:54: NOTICE:  table "verl_training_jobs" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:55: NOTICE:  table "scraped_market_data" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:56: NOTICE:  table "feedback_metrics" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:57: NOTICE:  table "sales_data" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:58: NOTICE:  table "trend_analyses" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:59: NOTICE:  table "trends" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:60: NOTICE:  table "publications" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:61: NOTICE:  table "books" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:62: NOTICE:  table "users" does not exist, skipping
DROP TABLE
psql:supabase_production_schema.sql:65: NOTICE:  type "user_tier" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:66: NOTICE:  type "book_status" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:67: NOTICE:  type "publication_status" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:68: NOTICE:  type "training_status" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:69: NOTICE:  type "subscription_tier" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:70: NOTICE:  type "gdpr_request_type" does not exist, skipping
DROP TYPE
psql:supabase_production_schema.sql:71: NOTICE:  type "gdpr_request_status" does not exist, skipping
DROP TYPE
CREATE TYPE
CREATE TYPE
CREATE TYPE
CREATE TYPE
CREATE TYPE
CREATE TYPE
CREATE TYPE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
psql:supabase_production_schema.sql:1193: NOTICE:  trigger "update_users_updated_at" for relation "public.users" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
psql:supabase_production_schema.sql:1199: NOTICE:  trigger "update_books_updated_at" for relation "public.books" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
psql:supabase_production_schema.sql:1205: NOTICE:  trigger "update_api_keys_updated_at" for relation "public.api_keys" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
psql:supabase_production_schema.sql:1211: NOTICE:  trigger "update_publications_updated_at" for relation "public.publications" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
psql:supabase_production_schema.sql:1217: NOTICE:  trigger "update_trends_updated_at" for relation "public.trends" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
psql:supabase_production_schema.sql:1223: NOTICE:  trigger "update_trend_analyses_updated_at" for relation "public.trend_analyses" does not exist, skipping
DROP TRIGGER
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
psql:supabase_production_schema.sql:1248: NOTICE:  policy "Users can view own profile" for relation "public.users" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1252: NOTICE:  policy "Users can update own profile" for relation "public.users" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1257: NOTICE:  policy "Users can view own books" for relation "public.books" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1261: NOTICE:  policy "Users can create own books" for relation "public.books" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1265: NOTICE:  policy "Users can update own books" for relation "public.books" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1269: NOTICE:  policy "Users can delete own books" for relation "public.books" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1274: NOTICE:  policy "Users can view own api keys" for relation "public.api_keys" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1278: NOTICE:  policy "Users can create own api keys" for relation "public.api_keys" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1282: NOTICE:  policy "Users can update own api keys" for relation "public.api_keys" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1286: NOTICE:  policy "Users can delete own api keys" for relation "public.api_keys" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1291: NOTICE:  policy "Users can view own api key usage" for relation "public.api_key_usage" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1302: NOTICE:  policy "Users can view own publications" for relation "public.publications" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1306: NOTICE:  policy "Users can create own publications" for relation "public.publications" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1310: NOTICE:  policy "Users can update own publications" for relation "public.publications" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1314: NOTICE:  policy "Users can delete own publications" for relation "public.publications" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1319: NOTICE:  policy "Users can view own sales data" for relation "public.sales_data" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1324: NOTICE:  policy "Users can view own feedback" for relation "public.feedback_metrics" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1328: NOTICE:  policy "Users can create own feedback" for relation "public.feedback_metrics" does not exist, skipping
DROP POLICY
CREATE POLICY
psql:supabase_production_schema.sql:1333: NOTICE:  policy "Users can view own security events" for relation "public.security_audit_events" does not exist, skipping
DROP POLICY
CREATE POLICY
CREATE VIEW
CREATE VIEW
UPDATE 0
UPDATE 0
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
COMMENT
psql:supabase_production_schema.sql:1497: NOTICE:  ✅ COMPLETE PRODUCTION SCHEMA DEPLOYED SUCCESSFULLY!
psql:supabase_production_schema.sql:1497: NOTICE:  📊 Tables: 28 core tables with comprehensive functionality
psql:supabase_production_schema.sql:1497: NOTICE:  🔑 New tables: api_keys, api_key_usage
psql:supabase_production_schema.sql:1497: NOTICE:  📈 Enhanced columns: users table with 21 new preference/settings columns
psql:supabase_production_schema.sql:1497: NOTICE:  🎯 VERL integration: reward_signal column added to feedback_metrics
psql:supabase_production_schema.sql:1497: NOTICE:  🛡️ Security: RLS policies applied, proper constraints and indexes
psql:supabase_production_schema.sql:1497: NOTICE:  📱 User preferences: Complete theme, platform, and security settings support
psql:supabase_production_schema.sql:1497: NOTICE:  🔗 Migration integration: All migration files merged successfully
psql:supabase_production_schema.sql:1497: NOTICE:  🎉 READY FOR PRODUCTION: Full API support for user settings endpoints
DO
