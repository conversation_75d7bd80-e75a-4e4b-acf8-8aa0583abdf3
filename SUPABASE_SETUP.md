# Supabase Setup Guide for Publish-AI

This guide will walk you through setting up Supabase for the Publish-AI e-book generation system.

## 📋 Prerequisites

- A Supabase account (free tier is sufficient for development)
- Node.js installed (for Supabase CLI, optional but recommended)

## 🚀 Step 1: Create a Supabase Project

1. **Sign up/Login to Supabase**
   - Go to [https://supabase.com](https://supabase.com)
   - Click "Start your project" or sign in

2. **Create a New Project**
   - Click "New project"
   - Fill in the details:
     - **Name**: `publish-ai` (or your preferred name)
     - **Database Password**: Generate a strong password and **save it securely**
     - **Region**: Choose the closest to your location
     - **Pricing Plan**: Free tier is fine for development

3. **Wait for Project Setup**
   - This takes about 2 minutes
   - You'll see a dashboard once ready

## 🔑 Step 2: Get Your API Keys

1. **Navigate to Settings**
   - Click on "Settings" in the left sidebar
   - Go to "API" section

2. **Copy Your Keys**
   ```bash
   # You'll need these two values:
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_SERVICE_KEY=your-service-key-here
   ```
   
   - **Project URL**: This is your Supabase URL
   - **service_role key**: This is your service key (⚠️ Keep this secret!)
   - The `anon` key is for client-side apps (not needed for backend)

## 📊 Step 3: Set Up the Database Schema

1. **Option A: Using the SQL Editor (Recommended)**
   - Go to "SQL Editor" in the Supabase dashboard
   - Click "New query"
   - Copy and paste the contents of `supabase_schema.sql` from this project
   - Click "Run" to execute

2. **Option B: Using Supabase CLI**
   ```bash
   # Install Supabase CLI
   npm install -g supabase
   
   # Login to Supabase
   supabase login
   
   # Link to your project
   supabase link --project-ref your-project-id
   
   # Run the schema
   supabase db push
   ```

## 🔧 Step 4: Configure Your Project

1. **Create/Update `.env` file**
   ```bash
   # In your project root
   cp .env.example .env
   ```

2. **Add Supabase credentials to `.env`**
   ```env
   # Supabase Configuration
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_SERVICE_KEY=your-service-key-here
   
   # Optional: If you want to use Supabase Auth
   SUPABASE_JWT_SECRET=your-jwt-secret-here
   ```

3. **Get JWT Secret (if using Supabase Auth)**
   - Go to Settings → API
   - Find "JWT Settings"
   - Copy the JWT secret

## ✅ Step 5: Verify Your Setup

1. **Test the connection**
   ```bash
   # Run the test script
   python test_supabase_connection.py
   ```

2. **Create the test script** (if it doesn't exist)
   ```python
   # test_supabase_connection.py
   import os
   from dotenv import load_dotenv
   from supabase import create_client, Client
   
   load_dotenv()
   
   url = os.getenv("SUPABASE_URL")
   key = os.getenv("SUPABASE_SERVICE_KEY")
   
   if not url or not key:
       print("❌ Missing SUPABASE_URL or SUPABASE_SERVICE_KEY in .env")
       exit(1)
   
   try:
       supabase: Client = create_client(url, key)
       
       # Test query
       result = supabase.table("users").select("id").limit(1).execute()
       print("✅ Successfully connected to Supabase!")
       print(f"   URL: {url}")
       
   except Exception as e:
       print(f"❌ Failed to connect: {e}")
   ```

## 🛡️ Step 6: Enable Row Level Security (RLS)

1. **Go to Authentication → Policies**
2. **Enable RLS for each table**:
   - Click on each table
   - Toggle "Enable RLS"
   - The schema file should have already created the policies

## 📦 Step 7: Set Up Storage (Optional)

If you plan to store book covers and manuscripts in Supabase:

1. **Go to Storage**
2. **Create buckets**:
   ```sql
   -- Run in SQL Editor
   INSERT INTO storage.buckets (id, name, public)
   VALUES 
     ('covers', 'covers', true),
     ('manuscripts', 'manuscripts', false);
   ```

3. **Set up storage policies**:
   ```sql
   -- Allow authenticated users to upload to their folders
   CREATE POLICY "Users can upload their own files"
   ON storage.objects FOR INSERT
   WITH CHECK (bucket_id IN ('covers', 'manuscripts') 
     AND auth.uid()::text = (storage.foldername(name))[1]);
   ```

## 🚀 Step 8: Run the Application

1. **Start with Supabase backend**
   ```bash
   poetry run uvicorn app.main_supabase:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Or use the migration script to test**
   ```bash
   python test_supabase_migration.py
   ```

## 🧪 Step 9: Run Tests

1. **Run agent tests with Supabase**
   ```bash
   # Supabase tests only
   python tests/test_agents/run_agent_tests.py --database-only
   
   # All tests
   python tests/test_agents/run_agent_tests.py --suite all
   ```

## 🔍 Troubleshooting

### Common Issues:

1. **"relation does not exist" error**
   - Make sure you ran the schema SQL
   - Check if you're connected to the right project

2. **Authentication errors**
   - Verify your service key is correct
   - Make sure you're using service_role key, not anon key

3. **RLS policy errors**
   - Temporarily disable RLS for testing
   - Or use service_role key which bypasses RLS

4. **Connection timeouts**
   - Check your internet connection
   - Verify the Supabase URL is correct
   - Try a different region if issues persist

### Debug Mode

Add this to your `.env` for detailed logging:
```env
SUPABASE_DEBUG=true
LOG_LEVEL=DEBUG
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Python Client](https://supabase.com/docs/reference/python/introduction)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli/introduction)

## 🎯 Next Steps

After setup is complete:

1. **Create your first user** via the API or Supabase dashboard
2. **Test book creation** through the `/api/books` endpoints
3. **Set up API keys** for OpenAI/Anthropic to enable AI features
4. **Configure storage** if you want to store files in Supabase

## 🔒 Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use different keys** for development and production
3. **Enable RLS** on all tables in production
4. **Rotate keys regularly**
5. **Use environment variables** for all sensitive data
6. **Set up API rate limiting** in production

---

Need help? Check the [project documentation](./README.md) or open an issue on GitHub.