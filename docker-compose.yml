version: '3.8'

services:
  # ===========================================
  # SUPABASE SELF-HOSTED STACK
  # ===========================================

  # Supabase Database (PostgreSQL)
  supabase-db:
    image: supabase/postgres:**********
    healthcheck:
      test: pg_isready -U postgres -h localhost
      interval: 5s
      timeout: 5s
      retries: 10
    command:
      - postgres
      - -c
      - config_file=/etc/postgresql/postgresql.conf
      - -c
      - log_min_messages=fatal
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_HOST: /var/run/postgresql
      PGPORT: 5432
      POSTGRES_PORT: 5432
      PGPASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
      PGDATABASE: postgres
      POSTGRES_DB: postgres
      PGUSER: supabase_admin
      POSTGRES_USER: supabase_admin
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
      - ./supabase/volumes/db/realtime.sql:/docker-entrypoint-initdb.d/migrations/99-realtime.sql:Z
      - ./supabase/volumes/db/webhooks.sql:/docker-entrypoint-initdb.d/init-scripts/98-webhooks.sql:Z
      - ./supabase/volumes/db/roles.sql:/docker-entrypoint-initdb.d/init-scripts/99-roles.sql:Z
    networks:
      - publish-network

  # Supabase Studio (Dashboard)
  supabase-studio:
    image: supabase/studio:20240326-5e5586d
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/profile', (r) => {if (r.statusCode !== 200) throw new Error(r.statusCode)})"]
      timeout: 5s
      interval: 5s
      retries: 3
    ports:
      - "3001:3000"  # Studio on port 3001 (3000 reserved for your frontend)
    environment:
      STUDIO_PG_META_URL: http://supabase-meta:8080
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
      DEFAULT_ORGANIZATION_NAME: ${STUDIO_DEFAULT_ORGANIZATION:-Default Organization}
      DEFAULT_PROJECT_NAME: ${STUDIO_DEFAULT_PROJECT:-Default Project}
      SUPABASE_URL: http://localhost:8000
      SUPABASE_PUBLIC_URL: ${SUPABASE_PUBLIC_URL}
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
    networks:
      - publish-network

  # Supabase Kong (API Gateway)
  supabase-kong:
    image: kong:2.8.1
    restart: unless-stopped
    ports:
      - "8000:8000/tcp"
      - "8443:8443/tcp"
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
    volumes:
      - ./supabase/volumes/api/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      supabase-auth:
        condition: service_healthy
      supabase-rest:
        condition: service_healthy
      supabase-realtime:
        condition: service_healthy
      supabase-storage:
        condition: service_healthy
    networks:
      - publish-network

  # Supabase Auth (GoTrue)
  supabase-auth:
    image: supabase/gotrue:v2.151.0
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9999/health"]
      timeout: 5s
      interval: 5s
      retries: 3
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: ${API_EXTERNAL_URL}
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://supabase_auth_admin:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@${POSTGRES_HOST:-supabase-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-postgres}
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: ${JWT_EXPIRY}
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: ${ENABLE_EMAIL_SIGNUP}
      GOTRUE_EXTERNAL_ANONYMOUS_USERS_ENABLED: ${ENABLE_ANONYMOUS_USERS}
      GOTRUE_MAILER_AUTOCONFIRM: ${ENABLE_EMAIL_AUTOCONFIRM}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL}
      GOTRUE_SMTP_HOST: ${SMTP_HOST}
      GOTRUE_SMTP_PORT: ${SMTP_PORT}
      GOTRUE_SMTP_USER: ${SMTP_USER}
      GOTRUE_SMTP_PASS: ${SMTP_PASS}
      GOTRUE_SMTP_SENDER_NAME: ${SMTP_SENDER_NAME}
      GOTRUE_MAILER_URLPATHS_INVITE: "/auth/v1/verify"
      GOTRUE_MAILER_URLPATHS_CONFIRMATION: "/auth/v1/verify"
      GOTRUE_MAILER_URLPATHS_RECOVERY: "/auth/v1/verify"
      GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE: "/auth/v1/verify"
      GOTRUE_EXTERNAL_PHONE_ENABLED: ${ENABLE_PHONE_SIGNUP}
      GOTRUE_SMS_AUTOCONFIRM: ${ENABLE_PHONE_AUTOCONFIRM}
    networks:
      - publish-network

  # Supabase REST API (PostgREST)
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/ready || exit 1"]
      timeout: 5s
      interval: 5s
      retries: 3
    environment:
      PGRST_DB_URI: postgres://authenticator:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@${POSTGRES_HOST:-supabase-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-postgres}
      PGRST_DB_SCHEMAS: ${PGRST_DB_SCHEMAS}
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: ${JWT_SECRET}
      PGRST_APP_SETTINGS_JWT_EXP: ${JWT_EXPIRY}
    command: "postgrest"
    networks:
      - publish-network

  # Supabase Realtime
  supabase-realtime:
    image: supabase/realtime:v2.25.66
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      timeout: 5s
      interval: 5s
      retries: 3
    restart: unless-stopped
    environment:
      PORT: 4000
      DB_HOST: ${POSTGRES_HOST:-supabase-db}
      DB_PORT: ${POSTGRES_PORT:-5432}
      DB_USER: supabase_realtime_admin
      DB_PASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
      DB_NAME: ${POSTGRES_DB:-postgres}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET}
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    command: >
      sh -c "/app/bin/migrate && /app/bin/realtime eval 'Realtime.Release.seeds(Realtime.Repo)' && /app/bin/server"
    networks:
      - publish-network

  # Supabase Storage
  supabase-storage:
    image: supabase/storage-api:v0.46.4
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_healthy
      supabase-imgproxy:
        condition: service_started
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/status"]
      timeout: 5s
      interval: 5s
      retries: 3
    restart: unless-stopped
    environment:
      ANON_KEY: ${ANON_KEY}
      SERVICE_KEY: ${SERVICE_ROLE_KEY}
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgres://supabase_storage_admin:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@${POSTGRES_HOST:-supabase-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-postgres}
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: "true"
      IMGPROXY_URL: http://supabase-imgproxy:5001
    volumes:
      - supabase_storage_data:/var/lib/storage:z
    networks:
      - publish-network

  # Image Proxy (for image transformations)
  supabase-imgproxy:
    image: darthsim/imgproxy:v3.8.0
    healthcheck:
      test: ["CMD", "imgproxy", "health"]
      timeout: 5s
      interval: 5s
      retries: 3
    environment:
      IMGPROXY_BIND: ":5001"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: "true"
      IMGPROXY_ENABLE_WEBP_DETECTION: ${IMGPROXY_ENABLE_WEBP_DETECTION}
    volumes:
      - supabase_storage_data:/var/lib/storage:z
    networks:
      - publish-network

  # Supabase Meta (Database metadata API)
  supabase-meta:
    image: supabase/postgres-meta:v0.68.0
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    restart: unless-stopped
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: ${POSTGRES_HOST:-supabase-db}
      PG_META_DB_PORT: ${POSTGRES_PORT:-5432}
      PG_META_DB_NAME: ${POSTGRES_DB:-postgres}
      PG_META_DB_USER: supabase_admin
      PG_META_DB_PASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
    networks:
      - publish-network

  # Supabase Functions (Edge Functions)
  supabase-functions:
    image: supabase/edge-runtime:v1.22.4
    restart: unless-stopped
    depends_on:
      supabase-analytics:
        condition: service_healthy
    environment:
      JWT_SECRET: ${JWT_SECRET}
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
      SUPABASE_DB_URL: postgresql://postgres:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@${POSTGRES_HOST:-supabase-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-postgres}
      VERIFY_JWT: "${FUNCTIONS_VERIFY_JWT:-false}"
    volumes:
      - ./supabase/functions:/home/<USER>/functions:Z
    command:
      - start
      - --main-service
      - /home/<USER>/functions/main
    networks:
      - publish-network

  # Supabase Analytics (LogFlare)
  supabase-analytics:
    image: supabase/logflare:1.4.0
    healthcheck:
      test: ["CMD", "curl", "http://localhost:4000/health"]
      timeout: 5s
      interval: 5s
      retries: 10
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      LOGFLARE_NODE_HOST: 127.0.0.1
      DB_USERNAME: supabase_admin
      DB_DATABASE: ${POSTGRES_DB:-postgres}
      DB_HOSTNAME: ${POSTGRES_HOST:-supabase-db}
      DB_PORT: ${POSTGRES_PORT:-5432}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}
      DB_SCHEMA: _analytics
      LOGFLARE_API_KEY: ${LOGFLARE_API_KEY}
      LOGFLARE_SINGLE_TENANT: true
      LOGFLARE_SUPABASE_MODE: true
      POSTGRES_BACKEND_URL: postgresql://supabase_admin:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@${POSTGRES_HOST:-supabase-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-postgres}
      POSTGRES_BACKEND_SCHEMA: _analytics
      LOGFLARE_FEATURE_FLAG_OVERRIDE: multibackend=true
    command: >
      sh -c "/app/bin/migrate && /app/bin/logflare eval 'Logflare.Release.seeds(Logflare.Repo)' && /app/bin/server"
    networks:
      - publish-network

  # ===========================================
  # YOUR PUBLISHING APP SERVICES
  # ===========================================

  # Main Publishing App (Poetry-based with Supabase)
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "3002:8000"  # Changed port to avoid conflict with Supabase Kong
    environment:
      # Supabase Configuration
      - SUPABASE_URL=http://supabase-kong:8000
      - SUPABASE_ANON_KEY=${ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@supabase-db:5432/postgres
      
      # Redis & VERL
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ENABLE_VERL=true
      - VERL_TRAINER_URL=http://verl-trainer:8001
      
      # CDN Configuration
      - CDN_URL=http://nginx-cdn:80/static
      - ENABLE_ASSET_COMPRESSION=true
      
      # AI APIs
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      
      # JWT Configuration
      - JWT_SECRET=${JWT_SECRET}
      - JWT_ALGORITHM=HS256
    depends_on:
      - supabase-kong
      - supabase-db
      - redis
      - verl-trainer
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - shared-data:/shared
    networks:
      - publish-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Dashboard  
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      # Point to your app (not Supabase Kong directly)
      - NEXT_PUBLIC_API_URL=http://localhost:3002
      
      # Supabase Configuration for frontend
      - NEXT_PUBLIC_SUPABASE_URL=http://localhost:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${ANON_KEY}
    depends_on:
      - app
      - supabase-kong
    networks:
      - publish-network

  # Redis for caching and task queue (with cluster support)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - publish-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    environment:
      - REDIS_MAXMEMORY=512mb
      - REDIS_MAXMEMORY_POLICY=allkeys-lru

  # CDN/Nginx for static asset serving
  nginx-cdn:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./storage:/usr/share/nginx/html/static:ro
      - ./nginx-cdn.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app
    networks:
      - publish-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for background tasks (Poetry-based)
  celery-worker:
    build: 
      context: .
      dockerfile: Dockerfile
    command: poetry run celery -A app.celery_app worker --loglevel=info
    environment:
      # Supabase Configuration
      - SUPABASE_URL=http://supabase-kong:8000
      - SUPABASE_SERVICE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@supabase-db:5432/postgres
      
      # Other services
      - REDIS_URL=redis://redis:6379
      - ENABLE_VERL=true
      - VERL_TRAINER_URL=http://verl-trainer:8001
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    depends_on:
      - supabase-db
      - redis
      - verl-trainer
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - shared-data:/shared
    networks:
      - publish-network

  # 🎯 VERL Training Container (Poetry + GPU-enabled)
  verl-trainer:
    build:
      context: .
      dockerfile: Dockerfile.verl
    ports:
      - "8001:8001"
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
      
      # Supabase Configuration for VERL
      - SUPABASE_URL=http://supabase-kong:8000
      - SUPABASE_SERVICE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-your-super-secret-and-long-postgres-password}@supabase-db:5432/postgres
      
      - REDIS_URL=redis://redis:6379
      - RAY_DISABLE_IMPORT_WARNING=1
      - RAY_ADDRESS=auto
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    depends_on:
      - supabase-db
      - redis
    volumes:
      - ./storage:/app/storage
      - shared-data:/shared
      - /dev/shm:/dev/shm
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - publish-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 60s
      timeout: 30s
      retries: 3

volumes:
  supabase_db_data:
  supabase_storage_data:
  redis_data:
  shared-data:

networks:
  publish-network:
    driver: bridge