# Production Deployment Runbook

## 📋 Overview

This runbook provides comprehensive step-by-step procedures for deploying the Publish AI platform to production environments. It includes pre-deployment checks, deployment procedures, post-deployment validation, and rollback procedures.

## 🎯 Deployment Checklist

### Pre-Deployment Requirements

**Infrastructure Readiness:**
- [ ] Production server infrastructure provisioned
- [ ] Load balancer configured with health checks
- [ ] Database cluster operational (Supabase production)
- [ ] Redis cluster configured for session storage and caching
- [ ] CDN configured for static assets
- [ ] SSL certificates installed and validated
- [ ] Monitoring and logging infrastructure ready (Sentry, Logflare)

**Environment Configuration:**
- [ ] All environment variables configured in production
- [ ] API keys validated (OpenAI, Anthropic, Supabase, etc.)
- [ ] Database connection strings tested
- [ ] External service endpoints verified
- [ ] Secret management system configured
- [ ] Backup and disaster recovery procedures tested

**Security Validation:**
- [ ] Security scanning completed (bandit, safety, semgrep)
- [ ] Dependency vulnerability scan passed
- [ ] Penetration testing completed
- [ ] GDPR compliance validation
- [ ] Data retention policies implemented

## 🚀 Deployment Procedures

### 1. Pre-Deployment Preparation

```bash
# 1. Create deployment branch
git checkout main
git pull origin main
git checkout -b release/$(date +%Y%m%d-%H%M)

# 2. Run comprehensive test suite
poetry install
poetry run pytest --cov=app --cov-report=html
poetry run pytest tests/test_agents/ --tb=short
poetry run pytest tests/test_analytics/ --tb=short

# 3. Security and quality checks
poetry run bandit -r app/ -f json -o security-report.json
poetry run safety check --json --output safety-report.json
poetry run black --check app/ tests/
poetry run isort --check-only app/ tests/
poetry run flake8 app/ tests/
poetry run mypy app/

# 4. Performance benchmarking
poetry run pytest tests/load_testing/ --tb=short
python tests/load_testing/performance_tests.py --benchmark

# 5. Build and validation
docker build -t publish-ai:$(git rev-parse --short HEAD) .
docker run --rm publish-ai:$(git rev-parse --short HEAD) pytest --version
```

### 2. Database Migration

```bash
# 1. Backup production database
./scripts/backup_production_db.sh

# 2. Test migration on staging
./scripts/setup_supabase_db.sh --environment=staging
./scripts/test_supabase_connection.sh --environment=staging

# 3. Apply migration to production (if needed)
./scripts/setup_supabase_db.sh --environment=production
./scripts/test_supabase_connection.sh --environment=production

# 4. Validate data integrity
python scripts/validate_data_integrity.py --environment=production
```

### 3. Application Deployment

**Blue-Green Deployment Strategy:**

```bash
# 1. Deploy to green environment
docker tag publish-ai:$(git rev-parse --short HEAD) publish-ai:green
kubectl apply -f k8s/green-deployment.yaml

# 2. Wait for green environment to be ready
kubectl rollout status deployment/publish-ai-green

# 3. Run health checks on green environment
curl -f https://green.publish-ai.com/health
curl -f https://green.publish-ai.com/api/setup/validate

# 4. Run smoke tests
pytest tests/e2e/test_critical_user_flows.py --environment=green

# 5. Switch traffic to green (if all checks pass)
kubectl patch service publish-ai -p '{"spec":{"selector":{"version":"green"}}}'

# 6. Monitor for 10 minutes
./scripts/monitor_deployment.sh --duration=600

# 7. Scale down blue environment (if stable)
kubectl scale deployment publish-ai-blue --replicas=0
```

**Rolling Deployment Strategy (Alternative):**

```bash
# 1. Update deployment with new image
kubectl set image deployment/publish-ai app=publish-ai:$(git rev-parse --short HEAD)

# 2. Monitor rollout
kubectl rollout status deployment/publish-ai --timeout=600s

# 3. Validate deployment
kubectl get pods -l app=publish-ai
kubectl logs -l app=publish-ai --tail=100
```

### 4. Configuration Updates

```bash
# 1. Update environment variables (if needed)
kubectl create secret generic publish-ai-secrets \
  --from-env-file=.env.production \
  --dry-run=client -o yaml | kubectl apply -f -

# 2. Update ConfigMaps
kubectl apply -f k8s/configmap.yaml

# 3. Restart pods to pick up new configuration
kubectl rollout restart deployment/publish-ai
```

## ✅ Post-Deployment Validation

### 1. Health Check Validation

```bash
# System health endpoints
curl -f https://api.publish-ai.com/health
curl -f https://api.publish-ai.com/api/setup/validate

# Database connectivity
curl -f https://api.publish-ai.com/api/monitoring/database-health

# External service connectivity
curl -f https://api.publish-ai.com/api/monitoring/external-services

# Authentication system
curl -f https://api.publish-ai.com/api/auth/health
```

### 2. Functional Testing

```bash
# Critical user flows
pytest tests/e2e/test_critical_user_flows.py --environment=production --tb=short

# API integration tests
pytest tests/test_api/ --environment=production --tb=short

# Agent functionality (with API keys)
python tests/test_agents/run_agent_tests.py --suite all --environment=production
```

### 3. Performance Validation

```bash
# Load testing (light load initially)
locust -f tests/load_testing/locustfile.py \
  --host=https://api.publish-ai.com \
  --users=10 --spawn-rate=2 --run-time=5m

# Response time validation
python tests/load_testing/performance_tests.py --environment=production

# Resource utilization check
kubectl top pods -l app=publish-ai
kubectl top nodes
```

### 4. Monitoring and Alerting

```bash
# Verify monitoring systems
curl -f https://api.publish-ai.com/api/monitoring/metrics

# Check Sentry error rates
# Check Logflare log ingestion
# Verify alerting rules are active

# Review dashboards
echo "✅ Check Grafana dashboards"
echo "✅ Check Sentry dashboard"
echo "✅ Check Logflare dashboard"
```

## 🔄 Rollback Procedures

### Immediate Rollback (Emergency)

```bash
# 1. Switch traffic back to previous version
kubectl patch service publish-ai -p '{"spec":{"selector":{"version":"blue"}}}'

# 2. Scale up previous version
kubectl scale deployment publish-ai-blue --replicas=3

# 3. Verify rollback
curl -f https://api.publish-ai.com/health

# 4. Scale down problematic version
kubectl scale deployment publish-ai-green --replicas=0
```

### Full Rollback

```bash
# 1. Revert to previous Docker image
kubectl set image deployment/publish-ai app=publish-ai:$(git rev-parse --short HEAD~1)

# 2. Rollback database (if needed)
./scripts/rollback_database.sh --backup-timestamp=YYYYMMDD-HHMMSS

# 3. Revert configuration changes
git checkout HEAD~1 -- k8s/
kubectl apply -f k8s/

# 4. Validate rollback
pytest tests/e2e/test_critical_user_flows.py --environment=production
```

## 📊 Deployment Metrics

### Success Criteria
- [ ] Deployment completes within 15 minutes
- [ ] Zero downtime during deployment
- [ ] All health checks pass within 2 minutes
- [ ] Error rate < 0.1% for 10 minutes post-deployment
- [ ] Response time < 500ms for critical endpoints
- [ ] CPU utilization < 70%
- [ ] Memory utilization < 80%

### Key Metrics to Monitor
```bash
# Application metrics
- Request rate (requests/second)
- Error rate (errors/total requests)
- Response time (95th percentile)
- Throughput (successful requests/second)

# Infrastructure metrics
- CPU utilization per pod
- Memory utilization per pod
- Database connection pool usage
- Redis cache hit ratio

# Business metrics
- User registration rate
- Book generation completion rate
- Publication success rate
- User satisfaction scores
```

## 🚨 Emergency Contacts

**On-Call Engineering:**
- Primary: <EMAIL>
- Secondary: <EMAIL>

**Infrastructure Team:**
- Platform: <EMAIL>
- Database: <EMAIL>

**External Services:**
- Supabase Support: Enterprise Support Portal
- OpenAI Support: Platform Support
- Anthropic Support: Enterprise Support

## 📝 Deployment Logs

**Record the following for each deployment:**

```yaml
deployment_record:
  timestamp: "2025-06-28T15:30:00Z"
  version: "v1.2.3"
  git_commit: "af33822"
  deployed_by: "engineering-team"
  deployment_method: "blue-green"
  duration_minutes: 12
  rollback_required: false
  issues_encountered: []
  post_deployment_metrics:
    error_rate: "0.05%"
    response_time_p95: "245ms"
    cpu_utilization: "45%"
    memory_utilization: "62%"
  validation_results:
    health_checks: "PASS"
    functional_tests: "PASS"
    performance_tests: "PASS"
    monitoring_systems: "ACTIVE"
```

## 🔐 Security Considerations

**Pre-Deployment Security:**
- [ ] All secrets rotated for production
- [ ] API rate limiting configured
- [ ] CORS settings appropriate for production
- [ ] Security headers configured
- [ ] SQL injection protection validated
- [ ] XSS protection enabled

**Post-Deployment Security:**
- [ ] Security monitoring active
- [ ] Intrusion detection systems operational
- [ ] Log aggregation and analysis working
- [ ] Backup encryption validated
- [ ] Access controls verified

## 📚 Related Documentation

- [Incident Response Procedures](./INCIDENT_RESPONSE.md)
- [Code Review Standards](./CODE_REVIEW_STANDARDS.md)
- [Performance Benchmarking](./PERFORMANCE_BENCHMARKING.md)
- [GDPR Compliance Guide](./GDPR_COMPLIANCE.md)
- [Security Audit Trail](./SECURITY_AUDIT.md)

---

**Last Updated:** 2025-06-28  
**Version:** 1.0  
**Owner:** Platform Engineering Team