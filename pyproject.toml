[tool.poetry]
name = "publish-ai"
version = "0.1.0"
description = "AI-powered e-book generation and publishing system"
authors = ["<PERSON><PERSON><PERSON>s <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.32.0"}
pydantic = {extras = ["email"], version = "^2.5.0"}
alembic = "^1.13.0"
redis = "^5.0.1"
celery = "^5.3.4"
httpx = "^0.28.1"
beautifulsoup4 = "^4.12.2"
selenium = "^4.15.2"
openai = "^1.30.0"
anthropic = "^0.52.0"
python-multipart = "^0.0.9"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-docx = "^1.1.0"
markdown = "^3.5.1"
pillow = "^10.1.0"
matplotlib = "^3.8.2"
requests = "^2.31.0"
pandas = "^2.1.4"
numpy = "^1.26.2"
pytrends = "^4.9.2"
supabase = "^2.0.2"
python-dotenv = "^1.0.0"
pdfkit = "^1.0.0"
ebooklib = "^0.18"
pydantic-settings = "^2.1.0"
pydantic-ai = "^0.3.2"
textstat = "^0.7.7"
psutil = "^5.9.0"
torch = {version = "^2.0.0", optional = true}
asyncpg = "^0.30.0"
sentry-sdk = {extras = ["fastapi"], version = "^2.31.0"}
pydantic-ai-slim = {extras = ["anthropic"], version = "^0.3.3"}
snapshottest = "^0.6.0"
asyncpraw = "^7.8.1"
# logflare = "^0.4.1"  # Package not available, using mock implementation
structlog = "^24.1.0"
slowapi = "^0.1.9"
circuitbreaker = "^2.0.0"
validators = "^0.28.3"
python-magic = "^0.4.27"
faker = "^30.0.0"
locust = "^2.31.8"
uuid = "^1.30"

[tool.poetry.extras]
ml = ["torch"]

[tool.poetry.group.dev.dependencies]
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pytest-cov = "^6.2.1"
asyncpraw-stubs = "^0.0.3"
pytest = "^8.4.1"
pytest-asyncio = "^1.0.0"
playwright = "^1.49.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
