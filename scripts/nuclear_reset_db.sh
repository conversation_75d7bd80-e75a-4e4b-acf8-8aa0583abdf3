#!/bin/bash

# Nuclear Database Reset for Publish-AI
# ⚠️  DANGER: This will COMPLETELY DESTROY everything in your database!
# ⚠️  This includes ALL TABLES, ALL DATA, ALL USERS, ALL FUNCTIONS, ALL VIEWS
# ⚠️  Use this ONLY when you want to start completely fresh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
BLINK='\033[5m'
NC='\033[0m'

# Helper functions
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error()   { echo -e "${RED}❌ $1${NC}"; }
print_info()    { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_danger()  { echo -e "${RED}${BOLD}${BLINK}💀 $1${NC}"; }

print_header() {
  echo -e "\n${RED}${BOLD}${BLINK}💀💀💀 NUCLEAR DATABASE RESET 💀💀💀${NC}"
  echo -e "${RED}${BOLD}⚠️  THIS WILL DESTROY EVERYTHING IN YOUR DATABASE ⚠️${NC}"
  echo "=================================================================="
  echo -e "${YELLOW}This script will:${NC}"
  echo "  💥 Drop ALL tables in public schema"
  echo "  💥 Drop ALL views, functions, and procedures"
  echo "  💥 Drop ALL custom types and enums"
  echo "  💥 Drop ALL sequences"
  echo "  💥 Remove ALL Supabase Auth users"
  echo "  💥 Reset ALL RLS policies"
  echo "  💥 Clear ALL storage buckets"
  echo "  💥 Reset database to factory state"
  echo "=================================================================="
}

# Load DATABASE_URL
load_env() {
  if [ -f ".env" ]; then
    export DATABASE_URL=$(grep '^DATABASE_URL=' .env | cut -d '=' -f2- | sed "s/^['\"]//; s/['\"]$//")
    if [ -n "$DATABASE_URL" ]; then
      print_success ".env DATABASE_URL loaded"
    else
      print_error "DATABASE_URL not found in .env"
      print_info "Make sure your .env has: DATABASE_URL='postgresql://...'"
      exit 1
    fi
  else
    print_error ".env file not found"
    exit 1
  fi
}

# Test connection
test_connection() {
  print_info "Testing database connection..."
  if timeout 10 psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
    MASKED_URL=$(echo $DATABASE_URL | sed 's/:[^@]*@/:***@/')
    print_success "Connected to database: $MASKED_URL"
    return 0
  else
    print_error "Failed to connect to database"
    print_info "Run ./scripts/fix_database_url.sh to fix your connection"
    return 1
  fi
}

# Show what will be destroyed
show_destruction_preview() {
  print_warning "Scanning database for items to destroy..."
  
  echo -e "\n${YELLOW}📊 DESTRUCTION PREVIEW:${NC}"
  
  # Count tables
  TABLE_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  " 2>/dev/null | xargs || echo "0")
  
  # Count views
  VIEW_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.views 
    WHERE table_schema = 'public';
  " 2>/dev/null | xargs || echo "0")
  
  # Count functions
  FUNCTION_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.routines 
    WHERE routine_schema = 'public';
  " 2>/dev/null | xargs || echo "0")
  
  # Count sequences
  SEQUENCE_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.sequences 
    WHERE sequence_schema = 'public';
  " 2>/dev/null | xargs || echo "0")
  
  # Count custom types
  TYPE_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM pg_type t 
    JOIN pg_namespace n ON t.typnamespace = n.oid 
    WHERE n.nspname = 'public' AND t.typtype = 'e';
  " 2>/dev/null | xargs || echo "0")
  
  # Count auth users (if accessible)
  USER_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM auth.users;
  " 2>/dev/null | xargs || echo "Unknown")
  
  # Count RLS policies
  POLICY_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM pg_policies 
    WHERE schemaname = 'public';
  " 2>/dev/null | xargs || echo "0")
  
  # Count triggers
  TRIGGER_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
  " 2>/dev/null | xargs || echo "0")
  
  # Count storage buckets
  BUCKET_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM storage.buckets;
  " 2>/dev/null | xargs || echo "Unknown")
  
  echo "  💥 Tables to drop: $TABLE_COUNT"
  echo "  💥 Views to drop: $VIEW_COUNT"
  echo "  💥 Functions to drop: $FUNCTION_COUNT"
  echo "  💥 Sequences to drop: $SEQUENCE_COUNT"
  echo "  💥 Custom types to drop: $TYPE_COUNT"
  echo "  💥 RLS policies to drop: $POLICY_COUNT"
  echo "  💥 Triggers to drop: $TRIGGER_COUNT"
  echo "  💥 Storage buckets to clear: $BUCKET_COUNT"
  echo "  💥 Auth users to remove: $USER_COUNT"
  
  # List some table names if any exist
  if [ "$TABLE_COUNT" -gt 0 ]; then
    echo -e "\n${YELLOW}📋 Some tables that will be destroyed:${NC}"
    timeout 10 psql "$DATABASE_URL" -t -c "
      SELECT '  💀 ' || table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_type = 'BASE TABLE' 
      ORDER BY table_name 
      LIMIT 10;
    " 2>/dev/null || echo "  (Could not list tables)"
    
    if [ "$TABLE_COUNT" -gt 10 ]; then
      echo "  ... and $((TABLE_COUNT - 10)) more tables"
    fi
  fi
  
  TOTAL_ITEMS=$((TABLE_COUNT + VIEW_COUNT + FUNCTION_COUNT + SEQUENCE_COUNT + TYPE_COUNT + POLICY_COUNT + TRIGGER_COUNT))
  if [ "$TOTAL_ITEMS" -eq 0 ]; then
    print_info "Database appears to be empty already"
    return 1
  fi
  
  echo -e "\n${RED}${BOLD}TOTAL ITEMS TO DESTROY: $TOTAL_ITEMS${NC}"
  return 0
}

# Ultra secure confirmation
get_nuclear_confirmation() {
  local timestamp=$(date +"%Y%m%d_%H%M%S")
  local confirm_text="NUCLEAR_RESET_${timestamp}"
  local project_id=$(echo $DATABASE_URL | grep -o 'nutrbmmtnvkuxygtgrch' || echo "UNKNOWN")
  
  echo -e "\n${RED}${BOLD}${BLINK}⚠️  FINAL WARNING ⚠️${NC}"
  echo -e "${RED}This action is IRREVERSIBLE and will destroy EVERYTHING!${NC}"
  echo -e "${RED}Project: $project_id${NC}"
  echo -e "${RED}Time: $(date)${NC}"
  echo ""
  echo -e "${YELLOW}To proceed with nuclear reset, type EXACTLY:${NC}"
  echo -e "${RED}${BOLD}$confirm_text${NC}"
  echo ""
  echo -e "${YELLOW}You have 60 seconds to type the confirmation text...${NC}"
  echo -n "Enter confirmation: "
  
  # Use a simpler read approach
  local response
  if read -t 60 -r response; then
    if [ "$response" = "$confirm_text" ]; then
      echo -e "\n${RED}Nuclear confirmation received. Proceeding with destruction...${NC}"
      return 0
    else
      echo -e "\n${RED}Confirmation text does not match. Expected:${NC}"
      echo -e "${RED}$confirm_text${NC}"
      echo -e "${RED}You entered:${NC}"
      echo -e "${RED}$response${NC}"
      echo -e "\n${GREEN}Nuclear reset cancelled due to incorrect confirmation.${NC}"
      return 1
    fi
  else
    echo -e "\n${GREEN}Nuclear reset cancelled or timed out. Your database is safe.${NC}"
    return 1
  fi
}

# Nuclear destruction function
execute_nuclear_reset() {
  print_danger "BEGINNING NUCLEAR RESET..."
  
  local destroyed=0
  local errors=0
  
  # Step 1: Remove all auth users
  print_info "🔥 Step 1: Removing all Supabase Auth users..."
  if timeout 30 psql "$DATABASE_URL" -c "DELETE FROM auth.users;" >/dev/null 2>&1; then
    print_success "All auth users removed"
    ((destroyed++))
  else
    print_warning "Could not remove auth users (may not have permission)"
  fi
  
  # Step 2: Drop all views (must be done before tables due to dependencies)
  print_info "🔥 Step 2: Dropping all views..."
  VIEWS=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT viewname 
    FROM pg_views 
    WHERE schemaname = 'public';
  " 2>/dev/null || echo "")
  
  for view in $VIEWS; do
    if [ -n "$view" ] && [ "$view" != "viewname" ]; then
      if timeout 10 psql "$DATABASE_URL" -c "DROP VIEW IF EXISTS \"$view\" CASCADE;" >/dev/null 2>&1; then
        print_success "Dropped view: $view"
        ((destroyed++))
      else
        print_error "Failed to drop view: $view"
        ((errors++))
      fi
    fi
  done
  
  # Step 3: Drop all functions and procedures
  print_info "🔥 Step 3: Dropping all functions..."
  FUNCTIONS=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT routine_name || '(' || string_agg(parameter_mode || ' ' || parameter_name || ' ' || data_type, ', ') || ')' as signature
    FROM information_schema.routines r
    LEFT JOIN information_schema.parameters p ON r.specific_name = p.specific_name
    WHERE r.routine_schema = 'public'
    GROUP BY routine_name, r.specific_name;
  " 2>/dev/null || echo "")
  
  # Fallback: Drop functions by name only
  FUNCTION_NAMES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT routine_name 
    FROM information_schema.routines 
    WHERE routine_schema = 'public';
  " 2>/dev/null || echo "")
  
  for func in $FUNCTION_NAMES; do
    if [ -n "$func" ] && [ "$func" != "routine_name" ]; then
      if timeout 10 psql "$DATABASE_URL" -c "DROP FUNCTION IF EXISTS \"$func\" CASCADE;" >/dev/null 2>&1; then
        print_success "Dropped function: $func"
        ((destroyed++))
      else
        print_warning "Could not drop function: $func"
      fi
    fi
  done
  
  # Step 4: Drop all tables
  print_info "🔥 Step 4: Dropping all tables..."
  TABLES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  " 2>/dev/null || echo "")
  
  for table in $TABLES; do
    if [ -n "$table" ] && [ "$table" != "table_name" ]; then
      if timeout 30 psql "$DATABASE_URL" -c "DROP TABLE IF EXISTS \"$table\" CASCADE;" >/dev/null 2>&1; then
        print_success "Dropped table: $table"
        ((destroyed++))
      else
        print_error "Failed to drop table: $table"
        ((errors++))
      fi
    fi
  done
  
  # Step 5: Drop all custom types
  print_info "🔥 Step 5: Dropping all custom types..."
  TYPES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT t.typname 
    FROM pg_type t 
    JOIN pg_namespace n ON t.typnamespace = n.oid 
    WHERE n.nspname = 'public' AND t.typtype = 'e';
  " 2>/dev/null || echo "")
  
  for type in $TYPES; do
    if [ -n "$type" ] && [ "$type" != "typname" ]; then
      if timeout 10 psql "$DATABASE_URL" -c "DROP TYPE IF EXISTS \"$type\" CASCADE;" >/dev/null 2>&1; then
        print_success "Dropped type: $type"
        ((destroyed++))
      else
        print_warning "Could not drop type: $type"
      fi
    fi
  done
  
  # Step 6: Drop all sequences
  print_info "🔥 Step 6: Dropping all sequences..."
  SEQUENCES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT sequence_name 
    FROM information_schema.sequences 
    WHERE sequence_schema = 'public';
  " 2>/dev/null || echo "")
  
  for seq in $SEQUENCES; do
    if [ -n "$seq" ] && [ "$seq" != "sequence_name" ]; then
      if timeout 10 psql "$DATABASE_URL" -c "DROP SEQUENCE IF EXISTS \"$seq\" CASCADE;" >/dev/null 2>&1; then
        print_success "Dropped sequence: $seq"
        ((destroyed++))
      else
        print_warning "Could not drop sequence: $seq"
      fi
    fi
  done
  
  # Step 7: Drop all RLS policies
  print_info "🔥 Step 7: Dropping all RLS policies..."
  POLICIES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT schemaname || '.' || tablename || '.' || policyname 
    FROM pg_policies 
    WHERE schemaname = 'public';
  " 2>/dev/null || echo "")
  
  for policy in $POLICIES; do
    if [ -n "$policy" ] && [ "$policy" != "schemaname.tablename.policyname" ]; then
      table_name=$(echo $policy | cut -d'.' -f2)
      policy_name=$(echo $policy | cut -d'.' -f3)
      if timeout 10 psql "$DATABASE_URL" -c "DROP POLICY IF EXISTS \"$policy_name\" ON \"$table_name\";" >/dev/null 2>&1; then
        print_success "Dropped policy: $policy_name on $table_name"
        ((destroyed++))
      else
        print_warning "Could not drop policy: $policy_name"
      fi
    fi
  done
  
  # Step 8: Drop all triggers
  print_info "🔥 Step 8: Dropping all triggers..."
  TRIGGERS=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT trigger_name || ' ON ' || event_object_table 
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
  " 2>/dev/null || echo "")
  
  for trigger in $TRIGGERS; do
    if [ -n "$trigger" ] && [ "$trigger" != "trigger_name ON event_object_table" ]; then
      trigger_name=$(echo $trigger | cut -d' ' -f1)
      table_name=$(echo $trigger | cut -d' ' -f3)
      if timeout 10 psql "$DATABASE_URL" -c "DROP TRIGGER IF EXISTS \"$trigger_name\" ON \"$table_name\";" >/dev/null 2>&1; then
        print_success "Dropped trigger: $trigger_name on $table_name"
        ((destroyed++))
      else
        print_warning "Could not drop trigger: $trigger_name"
      fi
    fi
  done
  
  # Step 9: Disable RLS on any remaining tables
  print_info "🔥 Step 9: Disabling RLS on all tables..."
  RLS_TABLES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  " 2>/dev/null || echo "")
  
  for table in $RLS_TABLES; do
    if [ -n "$table" ] && [ "$table" != "table_name" ]; then
      timeout 10 psql "$DATABASE_URL" -c "ALTER TABLE IF EXISTS \"$table\" DISABLE ROW LEVEL SECURITY;" >/dev/null 2>&1
    fi
  done
  
  # Step 10: Drop all storage buckets (if accessible)
  print_info "🔥 Step 10: Dropping storage buckets..."
  if timeout 10 psql "$DATABASE_URL" -c "DELETE FROM storage.buckets;" >/dev/null 2>&1; then
    print_success "Cleared storage buckets"
    ((destroyed++))
  else
    print_warning "Could not clear storage buckets (may not have permission)"
  fi
  
  # Step 11: Clear auth schema tables (but don't drop the schema)
  print_info "🔥 Step 11: Clearing auth tables..."
  AUTH_TABLES=("refresh_tokens" "sessions" "users" "identities" "instances" "audit_log_entries")
  for table in "${AUTH_TABLES[@]}"; do
    if timeout 10 psql "$DATABASE_URL" -c "DELETE FROM auth.\"$table\";" >/dev/null 2>&1; then
      print_success "Cleared auth.$table"
      ((destroyed++))
    else
      print_warning "Could not clear auth.$table"
    fi
  done
  
  # Step 12: Reset sequences in auth schema
  print_info "🔥 Step 12: Resetting auth sequences..."
  timeout 10 psql "$DATABASE_URL" -c "
    SELECT setval(sequence_name, 1, false) 
    FROM information_schema.sequences 
    WHERE sequence_schema = 'auth';
  " >/dev/null 2>&1 || print_warning "Could not reset auth sequences"
  
  # Step 13: Final cleanup - Drop extensions (be careful not to drop essential ones)
  print_info "🔥 Step 13: Final cleanup..."
  
  # Drop non-essential extensions
  timeout 10 psql "$DATABASE_URL" -c "
    DROP EXTENSION IF EXISTS \"uuid-ossp\" CASCADE;
    DROP EXTENSION IF EXISTS \"pgcrypto\" CASCADE;
    DROP EXTENSION IF EXISTS \"citext\" CASCADE;
  " >/dev/null 2>&1 || print_warning "Could not drop some extensions"
  
  echo -e "\n${RED}=================================================================="
  echo -e "💀 NUCLEAR RESET COMPLETE 💀"
  echo -e "=================================================================="
  echo -e "  ✅ Objects destroyed: $destroyed"
  echo -e "  ❌ Errors encountered: $errors"
  echo -e "  🕐 Completed at: $(date)"
  echo -e "=================================================================="
  
  if [ $errors -eq 0 ]; then
    print_success "Nuclear reset completed successfully!"
    print_info "Your database is now completely empty."
    print_info "Run './scripts/setup_supabase_db.sh' to recreate the schema."
  else
    print_warning "Nuclear reset completed with $errors errors."
    print_info "Some objects may still exist. Check manually if needed."
  fi
  
  return $errors
}

# Verify destruction
verify_destruction() {
  print_info "Verifying nuclear destruction..."
  
  TABLE_COUNT=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  " 2>/dev/null | xargs || echo "0")
  
  if [ "$TABLE_COUNT" -eq 0 ]; then
    print_success "✅ Verification passed: No tables remain"
    return 0
  else
    print_warning "⚠️  Verification failed: $TABLE_COUNT tables still exist"
    return 1
  fi
}

# Main execution
main() {
  print_header
  
  # Check prerequisites
  if ! command -v psql >/dev/null; then
    print_error "psql not found. Please install PostgreSQL client."
    exit 1
  fi
  
  load_env
  test_connection || exit 1
  
  # Show what will be destroyed
  if ! show_destruction_preview; then
    print_info "Database appears empty. Nothing to destroy."
    exit 0
  fi
  
  # Get nuclear confirmation
  if [ "$FORCE_NUCLEAR" = true ]; then
    print_warning "FORCE MODE: Skipping confirmation"
  else
    if ! get_nuclear_confirmation; then
      print_success "Nuclear reset cancelled. Your database is safe."
      exit 0
    fi
  fi
  
  # Execute nuclear reset
  echo -e "\n${RED}${BOLD}BEGINNING DESTRUCTION IN 3 SECONDS...${NC}"
  sleep 1
  echo -e "${RED}${BOLD}3...${NC}"
  sleep 1
  echo -e "${RED}${BOLD}2...${NC}"
  sleep 1
  echo -e "${RED}${BOLD}1...${NC}"
  sleep 1
  echo -e "${RED}${BOLD}💥 EXECUTING NUCLEAR RESET 💥${NC}"
  
  execute_nuclear_reset
  verify_destruction
  
  echo -e "\n${BLUE}Next steps:${NC}"
  echo "1. Run: ./scripts/setup_supabase_db.sh (to recreate schema)"
  echo "2. Or apply your schema manually"
  echo "3. Create new users as needed"
}

# Handle command line args
case "$1" in
  --help)
    echo "Nuclear Database Reset Script"
    echo "Usage: $0 [--help|--force]"
    echo ""
    echo "Options:"
    echo "  --help   Show this help message"
    echo "  --force  Skip confirmation (DANGEROUS!)"
    echo ""
    echo "⚠️  WARNING: This completely destroys your database!"
    echo "Use only when you want to start completely fresh."
    exit 0
    ;;
  --force)
    FORCE_NUCLEAR=true
    ;;
esac

main "$@"