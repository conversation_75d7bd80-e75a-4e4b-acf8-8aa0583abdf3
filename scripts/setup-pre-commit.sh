#!/bin/bash

# Pre-commit Setup Script for Publish AI Platform
# This script sets up pre-commit hooks and validates the configuration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [[ ! -f "pyproject.toml" ]]; then
    log_error "pyproject.toml not found. Please run this script from the project root."
    exit 1
fi

log_info "Setting up pre-commit hooks for Publish AI platform..."

# Install pre-commit if not already installed
if ! command -v pre-commit &> /dev/null; then
    log_info "Installing pre-commit..."
    
    # Try different installation methods
    if command -v poetry &> /dev/null; then
        poetry add --group dev pre-commit
    elif command -v pip &> /dev/null; then
        pip install pre-commit
    else
        log_error "No package manager found. Please install pre-commit manually."
        exit 1
    fi
    
    log_success "Pre-commit installed successfully"
else
    log_info "Pre-commit is already installed"
fi

# Install pre-commit hooks
log_info "Installing pre-commit hooks..."
pre-commit install

# Install commit-msg hook for conventional commits
log_info "Installing commit-msg hook..."
pre-commit install --hook-type commit-msg

# Create secrets baseline if it doesn't exist
if [[ ! -f ".secrets.baseline" ]]; then
    log_info "Creating secrets baseline..."
    detect-secrets scan --all-files --baseline .secrets.baseline
    log_success "Secrets baseline created"
else
    log_info "Secrets baseline already exists"
fi

# Validate pre-commit configuration
log_info "Validating pre-commit configuration..."
if pre-commit validate-config; then
    log_success "Pre-commit configuration is valid"
else
    log_error "Pre-commit configuration is invalid"
    exit 1
fi

# Run pre-commit on all files (optional)
read -p "Do you want to run pre-commit on all files now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Running pre-commit on all files..."
    
    # This might take a while, so run with timeout
    if timeout 600 pre-commit run --all-files; then
        log_success "Pre-commit checks passed on all files"
    else
        exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_warning "Pre-commit checks timed out after 10 minutes"
        else
            log_warning "Some pre-commit checks failed. You can fix them later."
        fi
    fi
else
    log_info "Skipping pre-commit run on all files"
fi

# Create Git hooks directory if it doesn't exist
mkdir -p .git/hooks

# Create additional custom hooks
log_info "Setting up custom Git hooks..."

# Create prepare-commit-msg hook for automatic issue linking
cat > .git/hooks/prepare-commit-msg << 'EOF'
#!/bin/bash

# Get the current branch name
branch_name=$(git symbolic-ref --short HEAD 2>/dev/null)

# Extract issue number from branch name (e.g., feature/ISSUE-123 -> ISSUE-123)
issue_number=$(echo "$branch_name" | grep -oE '[A-Z]+-[0-9]+' | head -1)

# If we found an issue number and the commit message doesn't already contain it
if [[ -n "$issue_number" ]] && ! grep -q "$issue_number" "$1"; then
    # Get the current commit message
    current_message=$(cat "$1")
    
    # Prepend the issue number if the message doesn't start with a conventional commit type
    if [[ ! "$current_message" =~ ^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?!?: ]]; then
        echo "[$issue_number] $current_message" > "$1"
    else
        # For conventional commits, add the issue number to the end
        echo "$current_message" > "$1"
        echo "" >> "$1"
        echo "Refs: $issue_number" >> "$1"
    fi
fi
EOF

chmod +x .git/hooks/prepare-commit-msg

# Create commit-msg hook for conventional commits validation
cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash

# Read the commit message
commit_message=$(cat "$1")

# Check if it's a merge commit (skip validation)
if [[ "$commit_message" =~ ^Merge ]]; then
    exit 0
fi

# Check if it's a revert commit (skip validation)
if [[ "$commit_message" =~ ^Revert ]]; then
    exit 0
fi

# Conventional commit pattern
pattern="^(feat|fix|docs|style|refactor|perf|test|chore|ci|build|revert)(\(.+\))?!?: .{1,50}"

if [[ ! "$commit_message" =~ $pattern ]]; then
    echo "❌ Commit message does not follow conventional commit format!"
    echo ""
    echo "Expected format: type(scope): description"
    echo ""
    echo "Types: feat, fix, docs, style, refactor, perf, test, chore, ci, build, revert"
    echo "Example: feat(auth): add OAuth2 authentication"
    echo "Example: fix(api): resolve book creation validation error"
    echo ""
    echo "Your commit message:"
    echo "$commit_message"
    exit 1
fi

# Check commit message length
first_line=$(echo "$commit_message" | head -n1)
if [[ ${#first_line} -gt 72 ]]; then
    echo "❌ Commit message first line is too long (${#first_line} > 72 characters)"
    echo "Please keep the first line under 72 characters."
    exit 1
fi

echo "✅ Commit message follows conventional commit format"
EOF

chmod +x .git/hooks/commit-msg

# Create pre-push hook for additional safety checks
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Running pre-push safety checks...${NC}"

# Check if we're pushing to main or develop
while read local_ref local_sha remote_ref remote_sha; do
    if [[ "$remote_ref" =~ refs/heads/(main|master|develop) ]]; then
        echo -e "${YELLOW}Pushing to protected branch: $(basename $remote_ref)${NC}"
        
        # Run quick tests
        echo "Running quick safety checks..."
        
        # Check for debug statements
        if git diff --cached --name-only | grep '\.py$' | xargs grep -l 'pdb\|breakpoint\|debugger' 2>/dev/null; then
            echo -e "${RED}❌ Debug statements found in Python files!${NC}"
            echo "Please remove debug statements before pushing to $(basename $remote_ref)"
            exit 1
        fi
        
        # Check for TODO/FIXME in main branch
        if [[ "$(basename $remote_ref)" == "main" ]]; then
            if git diff --cached --name-only | grep '^app/.*\.py$' | xargs grep -l 'TODO\|FIXME' 2>/dev/null; then
                echo -e "${YELLOW}⚠️  TODO/FIXME comments found in app/ files${NC}"
                echo "Consider resolving these before pushing to main branch"
                read -p "Continue anyway? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    exit 1
                fi
            fi
        fi
        
        echo -e "${GREEN}✅ Pre-push checks passed${NC}"
    fi
done

exit 0
EOF

chmod +x .git/hooks/pre-push

log_success "Custom Git hooks created successfully"

# Create pre-commit configuration documentation
cat > .pre-commit-docs.md << 'EOF'
# Pre-commit Hooks Documentation

This document describes the pre-commit hooks configured for the Publish AI platform.

## Installed Hooks

### Code Formatting
- **black**: Python code formatter
- **isort**: Import statement organizer
- **trailing-whitespace**: Removes trailing whitespace
- **end-of-file-fixer**: Ensures files end with newline

### Code Quality
- **flake8**: Python linter with additional plugins
- **mypy**: Static type checker
- **pylint**: Additional Python linting

### Security
- **bandit**: Python security linter
- **detect-secrets**: Prevents committing secrets
- **safety**: Dependency vulnerability scanner

### Other Checks
- **check-yaml**: YAML file validation
- **check-json**: JSON file validation
- **check-toml**: TOML file validation
- **check-merge-conflict**: Detects merge conflict markers
- **check-added-large-files**: Prevents large file commits

## Usage

### Initial Setup
```bash
# Install pre-commit hooks
pre-commit install

# Run on all files
pre-commit run --all-files
```

### Daily Usage
Pre-commit hooks run automatically on `git commit`. If hooks fail:

```bash
# Fix issues and retry commit
git add .
git commit -m "your message"

# Skip hooks (not recommended)
git commit --no-verify -m "your message"

# Run specific hook manually
pre-commit run black --all-files
```

### Configuration

The configuration is in `.pre-commit-config.yaml`. To update:

```bash
# Update hook versions
pre-commit autoupdate

# Validate configuration
pre-commit validate-config
```

### Troubleshooting

#### Common Issues

1. **Black formatting conflicts with flake8**
   - Solution: Use `--extend-ignore=E203,W503` in flake8 config

2. **MyPy import errors**
   - Solution: Add missing type stubs to `additional_dependencies`

3. **Secrets detection false positives**
   - Solution: Update `.secrets.baseline` with approved patterns

4. **Hook takes too long**
   - Solution: Use `exclude` patterns to skip irrelevant files

#### Manual Hook Execution

```bash
# Run all hooks
pre-commit run --all-files

# Run specific hook
pre-commit run bandit
pre-commit run mypy
pre-commit run black

# Skip failing hooks temporarily
SKIP=flake8 git commit -m "temp commit"
```

## Custom Git Hooks

Additional custom hooks are installed:

1. **prepare-commit-msg**: Automatically adds issue numbers from branch names
2. **commit-msg**: Validates conventional commit format
3. **pre-push**: Additional safety checks for protected branches

## Conventional Commits

This project uses conventional commit format:

```
type(scope): description

feat(auth): add OAuth2 authentication
fix(api): resolve book creation validation error
docs(readme): update installation instructions
```

Valid types: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `chore`, `ci`, `build`, `revert`
EOF

log_success "Pre-commit documentation created (.pre-commit-docs.md)"

# Display final information
echo ""
log_success "Pre-commit setup completed successfully!"
echo ""
echo "📋 Summary:"
echo "  ✅ Pre-commit hooks installed"
echo "  ✅ Secrets baseline created"
echo "  ✅ Custom Git hooks configured"
echo "  ✅ Documentation generated"
echo ""
echo "🔧 Next steps:"
echo "  1. Review .pre-commit-config.yaml for any customizations"
echo "  2. Update .secrets.baseline if needed"
echo "  3. Read .pre-commit-docs.md for usage instructions"
echo "  4. Test with: git commit --allow-empty -m 'test: pre-commit setup'"
echo ""
echo "💡 Tip: Use 'pre-commit run --all-files' to check all existing files"