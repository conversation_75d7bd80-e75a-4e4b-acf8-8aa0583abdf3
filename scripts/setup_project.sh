#!/bin/bash
# setup_project.sh - Complete AI Publishing Platform Setup Script

set -e

echo "🚀 Starting AI E-book Publishing Platform Setup"

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Docker
if ! command -v docker &>/dev/null; then
  echo "❌ Docker is required but not installed"
  exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &>/dev/null; then
  echo "❌ Docker Compose is required but not installed"
  exit 1
fi

# Check for GPU (optional but recommended)
if command -v nvidia-smi &>/dev/null; then
  echo "✅ NVIDIA GPU detected"
  GPU_AVAILABLE=true
else
  echo "⚠️  No NVIDIA GPU detected - VERL will run on CPU (slower)"
  GPU_AVAILABLE=false
fi

# Setup environment
echo "🔧 Setting up environment..."

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
  echo "📝 Creating .env file..."
  if [ -f .env.supabase.example ]; then
    cp .env.supabase.example .env
    echo "✅ Copied .env.supabase.example to .env"
  else
    cat >.env <<EOF
# Supabase Configuration (REQUIRED)
DATABASE_URL='postgresql://postgres:<EMAIL>:6543/postgres'
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key_here

# AI API Keys (REQUIRED - Add your keys here)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
PREFERRED_MODEL=openai

# VERL Configuration
ENABLE_VERL=true
MIN_TRAINING_EXAMPLES=50
VERL_TRAINING_INTERVAL_HOURS=24

# Application Settings
SECRET_KEY=$(openssl rand -hex 32)
DEBUG=false
LOG_LEVEL=INFO

# Optional: Amazon KDP Integration
KDP_EMAIL=
KDP_PASSWORD=

# Optional: External APIs
GOOGLE_TRENDS_API_KEY=
AMAZON_API_KEY=

# Redis (Optional - for background tasks)
REDIS_URL=redis://localhost:6379
EOF
  fi
  echo "⚠️  Please edit .env file with your Supabase and API keys before continuing!"
  echo "   Required: DATABASE_URL, SUPABASE_URL, SUPABASE_SERVICE_KEY"
  echo "   Required: At least one AI API key (OPENAI_API_KEY or ANTHROPIC_API_KEY)"
  read -p "Press Enter after editing .env file..."
fi

# Validate required configuration
source .env
if [[ -z "$DATABASE_URL" || -z "$SUPABASE_URL" || -z "$SUPABASE_SERVICE_KEY" ]]; then
  echo "❌ Supabase configuration is required (DATABASE_URL, SUPABASE_URL, SUPABASE_SERVICE_KEY)"
  echo "   Please edit .env file and add your Supabase credentials"
  exit 1
fi

if [[ -z "$OPENAI_API_KEY" && -z "$ANTHROPIC_API_KEY" ]]; then
  echo "❌ At least one AI API key (OpenAI or Anthropic) is required"
  echo "   Please edit .env file and add your API keys"
  exit 1
fi

# Check for Python and Poetry
echo "🐍 Checking Python environment..."
if ! command -v python3 &>/dev/null; then
  echo "❌ Python 3 is required but not installed"
  echo "   Please install Python 3.8+ and try again"
  exit 1
fi

if ! command -v poetry &>/dev/null; then
  echo "❌ Poetry is required but not installed"
  echo "   Install Poetry: curl -sSL https://install.python-poetry.org | python3 -"
  exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p storage/{manuscripts,covers,published,verl_checkpoints,training_data,model_cache}
mkdir -p logs
mkdir -p temp

# Set appropriate permissions
chmod 755 storage
chmod 755 logs
chmod 755 temp

# Install Python dependencies
echo "📦 Installing Python dependencies..."
poetry install --no-root

# Setup Supabase database
echo "🗄️ Setting up Supabase database..."
if [ -f "scripts/setup_supabase_db.sh" ]; then
  chmod +x scripts/setup_supabase_db.sh
  ./scripts/setup_supabase_db.sh
else
  echo "❌ Database setup script not found"
  echo "   Expected: scripts/setup_supabase_db.sh"
  exit 1
fi

# Install frontend dependencies (if frontend exists)
if [ -d "frontend" ]; then
  echo "🌐 Setting up frontend..."
  cd frontend
  if command -v npm &>/dev/null; then
    echo "📦 Installing frontend dependencies with conflict resolution..."
    npm install --legacy-peer-deps --no-audit
    if [ $? -eq 0 ]; then
      echo "✅ Frontend dependencies installed successfully"
    else
      echo "⚠️  Frontend dependency installation had issues, trying alternative approach..."
      # Clear npm cache and node_modules
      rm -rf node_modules package-lock.json
      npm cache clean --force
      npm install --legacy-peer-deps --no-audit --force
      if [ $? -eq 0 ]; then
        echo "✅ Frontend dependencies installed with clean install"
      else
        echo "❌ Frontend dependency installation failed"
        echo "   You can manually run: cd frontend && rm -rf node_modules package-lock.json && npm install --legacy-peer-deps"
      fi
    fi
  else
    echo "⚠️  npm not found - skipping frontend setup"
    echo "   Install Node.js and npm to enable frontend development"
  fi
  cd ..
fi

# Test application startup
echo "🧪 Testing application startup..."

# Test backend startup
echo "🔧 Testing backend..."
if poetry run python -c "from app.main_supabase import app; print('✅ Backend imports successful')"; then
  echo "✅ Backend configuration is valid"
else
  echo "❌ Backend configuration has issues"
  echo "   Check your .env file and Python dependencies"
fi

# Test database connection
echo "🔗 Testing database connection..."
if poetry run python -c "
from app.utils.supabase.supabase_client import get_supabase_client
try:
    client = get_supabase_client()
    result = client.client.table('users').select('count', count='exact').execute()
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"; then
  echo "✅ Supabase connection verified"
else
  echo "❌ Database connection issues detected"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📊 Start your application:"
echo "   Backend API: poetry run uvicorn app.main_supabase:app --reload --host 0.0.0.0 --port 8000"
echo "   Frontend: cd frontend && npm run dev"
echo ""
echo "📖 Access your application:"
echo "   API Documentation: http://localhost:8000/docs"
echo "   API Health Check: http://localhost:8000/health"
echo "   Frontend Dashboard: http://localhost:3000 (if frontend installed)"
echo ""
echo "📝 Useful commands:"
echo "   Backend development: poetry run uvicorn app.main_supabase:app --reload"
echo "   Run tests: poetry run pytest"
echo "   Code quality: poetry run black app/ && poetry run isort app/"
echo "   Clear database: ./scripts/clear_supabase_db.sh"
echo ""
echo "🗄️ Database management:"
echo "   Setup database: ./scripts/setup_supabase_db.sh"
echo "   Test connection: ./scripts/test_supabase_connection.sh"
echo ""

# Create monitoring script
cat >monitor_system.sh <<'EOF'
#!/bin/bash
# monitor_system.sh - Monitor AI Publishing Platform health

echo "🔍 AI Publishing Platform Health Monitor"
echo "========================================"

echo ""
echo "🏥 Service Health:"

# Check if backend is running
if curl -s http://localhost:8000/health 2>/dev/null | grep -q "healthy"; then
    echo "✅ Backend API: Running (http://localhost:8000)"
    # Get API info
    VERSION=$(curl -s http://localhost:8000/health 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$VERSION" ]; then
        echo "   📋 Version: $VERSION"
    fi
else
    echo "❌ Backend API: Not running"
    echo "   💡 Start with: poetry run uvicorn app.main_supabase:app --reload"
fi

# Check frontend
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ Frontend: Running (http://localhost:3000)"
else
    echo "❌ Frontend: Not running"
    echo "   💡 Start with: cd frontend && npm run dev"
fi

# Check database connection
echo ""
echo "🗄️ Database Status:"
if poetry run python -c "
from app.utils.supabase.supabase_client import get_supabase_client
try:
    client = get_supabase_client()
    result = client.client.table('users').select('count', count='exact').execute()
    print('✅ Supabase: Connected')
    print(f'   📊 Total users: {result.count}')
except Exception as e:
    print(f'❌ Supabase: Connection failed')
" 2>/dev/null; then
    echo "✅ Database connection verified"
else
    echo "❌ Database connection issues"
fi

echo ""
echo "💾 Storage Status:"
if [ -d "storage" ]; then
    echo "📁 Storage directory exists"
    echo "   📚 Manuscripts: $(find storage/manuscripts -name "*.txt" 2>/dev/null | wc -l) files"
    echo "   🎨 Covers: $(find storage/covers -name "*.png" -o -name "*.jpg" 2>/dev/null | wc -l) files"
    echo "   📖 Published: $(find storage/published -name "*" 2>/dev/null | wc -l) files"
    echo "   💾 Total size: $(du -sh storage 2>/dev/null | cut -f1)"
else
    echo "❌ Storage directory missing"
fi

echo ""
echo "🔧 Python Environment:"
PYTHON_VERSION=$(python3 --version 2>/dev/null)
echo "   🐍 Python: $PYTHON_VERSION"

if command -v poetry >/dev/null 2>&1; then
    POETRY_VERSION=$(poetry --version 2>/dev/null)
    echo "   📦 Poetry: $POETRY_VERSION"
    echo "   📋 Virtual env: $(poetry env info --path 2>/dev/null)"
else
    echo "   ❌ Poetry: Not found"
fi

echo ""
echo "📈 Recent Activity:"
if [ -f "logs/app.log" ]; then
    echo "Recent log entries:"
    tail -3 logs/app.log 2>/dev/null || echo "No recent log activity"
else
    echo "No log file found"
fi

EOF

chmod +x monitor_system.sh

echo "📊 Created monitoring script: ./monitor_system.sh"
echo "   Run it anytime to check system health"

echo ""
echo "🎯 Next Steps:"
echo "1. Start the backend: poetry run uvicorn app.main_supabase:app --reload"
echo "2. (Optional) Start the frontend: cd frontend && npm run dev"
echo "3. Visit http://localhost:8000/docs to explore the API"
echo "4. Generate your first e-book to test the system"
echo "5. Monitor system health with: ./monitor_system.sh"
echo ""
echo "🚀 Quick Start Commands:"
echo "   ./monitor_system.sh              - Check system health"
echo "   ./scripts/test_supabase_connection.sh  - Test database"
echo "   ./scripts/clear_supabase_db.sh   - Clear database (if needed)"
echo ""
echo "📚 Happy publishing! ✨"
