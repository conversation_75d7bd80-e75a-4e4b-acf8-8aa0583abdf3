#!/bin/bash

# RLS Policy Generator for Supabase
# Tests for Row Level Security and generates policies as needed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m'

# Helper functions
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error()   { echo -e "${RED}❌ $1${NC}"; }
print_info()    { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

print_header() {
  echo -e "\n${BLUE}${BOLD}🔒 RLS Policy Generator for Publish-AI${NC}"
  echo "=================================================="
  echo "This script will:"
  echo "  🔍 Scan all tables for RLS status"
  echo "  🔒 Enable RLS where needed"
  echo "  📋 Generate appropriate policies"
  echo "  ✅ Apply policies to tables"
  echo "=================================================="
}

# Load DATABASE_URL
load_env() {
  if [ -f ".env" ]; then
    export DATABASE_URL=$(grep '^DATABASE_URL=' .env | cut -d '=' -f2- | sed "s/^['\"]//; s/['\"]$//")
    if [ -n "$DATABASE_URL" ]; then
      print_success ".env DATABASE_URL loaded"
    else
      print_error "DATABASE_URL not found in .env"
      exit 1
    fi
  else
    print_error ".env file not found"
    exit 1
  fi
}

# Test connection
test_connection() {
  print_info "Testing database connection..."
  if timeout 10 psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
    print_success "Connected to database"
    return 0
  else
    print_error "Failed to connect to database"
    return 1
  fi
}

# Get all user tables
get_user_tables() {
  print_info "Scanning for user tables..."
  
  TABLES=$(timeout 10 psql "$DATABASE_URL" -t -c "
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    AND table_name NOT LIKE '%_backup%'
    ORDER BY table_name;
  " 2>/dev/null | xargs)
  
  if [ -z "$TABLES" ]; then
    print_warning "No user tables found in public schema"
    return 1
  fi
  
  TABLE_COUNT=$(echo $TABLES | wc -w)
  print_success "Found $TABLE_COUNT user tables: $TABLES"
  return 0
}

# Check RLS status for a table
check_rls_status() {
  local table_name="$1"
  
  RLS_ENABLED=$(timeout 5 psql "$DATABASE_URL" -t -c "
    SELECT c.relrowsecurity 
    FROM pg_class c 
    JOIN pg_namespace n ON c.relnamespace = n.oid 
    WHERE n.nspname = 'public' AND c.relname = '$table_name';
  " 2>/dev/null | xargs)
  
  if [ "$RLS_ENABLED" = "t" ]; then
    return 0  # RLS is enabled
  else
    return 1  # RLS is disabled
  fi
}

# Check existing policies for a table
get_existing_policies() {
  local table_name="$1"
  
  POLICIES=$(timeout 5 psql "$DATABASE_URL" -t -c "
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = '$table_name';
  " 2>/dev/null | xargs)
  
  echo "$POLICIES"
}

# Enable RLS on a table
enable_rls() {
  local table_name="$1"
  
  if timeout 10 psql "$DATABASE_URL" -c "ALTER TABLE \"$table_name\" ENABLE ROW LEVEL SECURITY;" >/dev/null 2>&1; then
    print_success "Enabled RLS on $table_name"
    return 0
  else
    print_error "Failed to enable RLS on $table_name"
    return 1
  fi
}

# Check if table has user_id column
has_user_id_column() {
  local table_name="$1"
  
  HAS_USER_ID=$(timeout 5 psql "$DATABASE_URL" -t -c "
    SELECT EXISTS (
      SELECT 1 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = '$table_name' 
      AND column_name IN ('user_id', 'owner_id', 'created_by')
    );
  " 2>/dev/null | xargs)
  
  if [ "$HAS_USER_ID" = "t" ]; then
    return 0
  else
    return 1
  fi
}

# Get the user column name for a table
get_user_column() {
  local table_name="$1"
  
  USER_COLUMN=$(timeout 5 psql "$DATABASE_URL" -t -c "
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = '$table_name' 
    AND column_name IN ('user_id', 'owner_id', 'created_by')
    ORDER BY 
      CASE column_name 
        WHEN 'user_id' THEN 1
        WHEN 'owner_id' THEN 2  
        WHEN 'created_by' THEN 3
      END
    LIMIT 1;
  " 2>/dev/null | xargs)
  
  echo "$USER_COLUMN"
}

# Generate standard RLS policies for a table
generate_policies() {
  local table_name="$1"
  local user_column="$2"
  
  print_info "Generating policies for $table_name (using $user_column column)..."
  
  # Policy 1: Users can view their own records
  SELECT_POLICY="
CREATE POLICY \"${table_name}_select_own\" ON \"$table_name\"
  FOR SELECT 
  USING (auth.uid() = \"$user_column\");
"
  
  # Policy 2: Users can insert their own records  
  INSERT_POLICY="
CREATE POLICY \"${table_name}_insert_own\" ON \"$table_name\"
  FOR INSERT 
  WITH CHECK (auth.uid() = \"$user_column\");
"
  
  # Policy 3: Users can update their own records
  UPDATE_POLICY="
CREATE POLICY \"${table_name}_update_own\" ON \"$table_name\"
  FOR UPDATE 
  USING (auth.uid() = \"$user_column\")
  WITH CHECK (auth.uid() = \"$user_column\");
"
  
  # Policy 4: Users can delete their own records
  DELETE_POLICY="
CREATE POLICY \"${table_name}_delete_own\" ON \"$table_name\"
  FOR DELETE 
  USING (auth.uid() = \"$user_column\");
"
  
  # Apply the policies
  echo "$SELECT_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created SELECT policy for $table_name" || \
    print_warning "SELECT policy for $table_name may already exist"
    
  echo "$INSERT_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created INSERT policy for $table_name" || \
    print_warning "INSERT policy for $table_name may already exist"
    
  echo "$UPDATE_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created UPDATE policy for $table_name" || \
    print_warning "UPDATE policy for $table_name may already exist"
    
  echo "$DELETE_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created DELETE policy for $table_name" || \
    print_warning "DELETE policy for $table_name may already exist"
}

# Generate public read policies for tables that should be publicly readable
generate_public_policies() {
  local table_name="$1"
  
  print_info "Generating public read policy for $table_name..."
  
  PUBLIC_SELECT_POLICY="
CREATE POLICY \"${table_name}_public_select\" ON \"$table_name\"
  FOR SELECT 
  USING (true);
"
  
  echo "$PUBLIC_SELECT_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created public SELECT policy for $table_name" || \
    print_warning "Public SELECT policy for $table_name may already exist"
}

# Generate admin policies for service role
generate_admin_policies() {
  local table_name="$1"
  
  print_info "Generating admin policies for $table_name..."
  
  ADMIN_POLICY="
CREATE POLICY \"${table_name}_admin_all\" ON \"$table_name\"
  FOR ALL 
  USING (auth.jwt() ->> 'role' = 'service_role');
"
  
  echo "$ADMIN_POLICY" | timeout 10 psql "$DATABASE_URL" >/dev/null 2>&1 && \
    print_success "Created admin policy for $table_name" || \
    print_warning "Admin policy for $table_name may already exist"
}

# Determine table type and generate appropriate policies
process_table() {
  local table_name="$1"
  
  echo -e "\n${YELLOW}📋 Processing table: $table_name${NC}"
  
  # Check if RLS is enabled
  if check_rls_status "$table_name"; then
    print_info "RLS already enabled on $table_name"
  else
    print_warning "RLS not enabled on $table_name"
    if enable_rls "$table_name"; then
      print_success "Enabled RLS on $table_name"
    else
      print_error "Failed to enable RLS on $table_name, skipping..."
      return 1
    fi
  fi
  
  # Check existing policies
  existing_policies=$(get_existing_policies "$table_name")
  if [ -n "$existing_policies" ]; then
    print_info "Existing policies: $existing_policies"
  else
    print_info "No existing policies found"
  fi
  
  # Determine policy type based on table name and structure
  case "$table_name" in
    "users")
      print_info "Users table detected - generating user-specific policies"
      generate_policies "$table_name" "id"
      generate_admin_policies "$table_name"
      ;;
    "books"|"publications"|"sales_data"|"feedback_metrics"|"api_keys"|"market_analyses"|"prediction_accuracy")
      if has_user_id_column "$table_name"; then
        user_col=$(get_user_column "$table_name")
        print_info "User-owned table detected - generating user-specific policies"
        generate_policies "$table_name" "$user_col"
        generate_admin_policies "$table_name"
      else
        print_warning "No user column found, generating admin-only policies"
        generate_admin_policies "$table_name"
      fi
      ;;
    "trends"|"trend_analyses")
      print_info "Public data table detected - generating public read policies"
      generate_public_policies "$table_name"
      if has_user_id_column "$table_name"; then
        user_col=$(get_user_column "$table_name")
        generate_policies "$table_name" "$user_col"
      fi
      generate_admin_policies "$table_name"
      ;;
    *)
      if has_user_id_column "$table_name"; then
        user_col=$(get_user_column "$table_name")
        print_info "Generic user table detected - generating user-specific policies"
        generate_policies "$table_name" "$user_col"
        generate_admin_policies "$table_name"
      else
        print_info "Generic table detected - generating admin-only policies"
        generate_admin_policies "$table_name"
      fi
      ;;
  esac
}

# Generate summary report
generate_report() {
  print_info "Generating RLS status report..."
  
  echo -e "\n${BLUE}📊 RLS STATUS REPORT${NC}"
  echo "=================================="
  
  for table in $TABLES; do
    if check_rls_status "$table"; then
      rls_status="${GREEN}✅ Enabled${NC}"
    else
      rls_status="${RED}❌ Disabled${NC}"
    fi
    
    policy_count=$(get_existing_policies "$table" | wc -w)
    
    printf "%-20s %s %s\n" "$table" "$rls_status" "(${policy_count} policies)"
  done
  
  echo "=================================="
}

# Verify policies work
test_policies() {
  print_info "Testing policy functionality..."
  
  # Test that policies don't block service role
  for table in $TABLES; do
    if timeout 5 psql "$DATABASE_URL" -c "SELECT COUNT(*) FROM \"$table\";" >/dev/null 2>&1; then
      print_success "Service role can access $table"
    else
      print_warning "Service role access issues with $table"
    fi
  done
}

# Main execution
main() {
  print_header
  
  # Check prerequisites
  if ! command -v psql >/dev/null; then
    print_error "psql not found. Please install PostgreSQL client."
    exit 1
  fi
  
  load_env
  test_connection || exit 1
  
  # Get all tables
  if ! get_user_tables; then
    print_error "No tables found to process"
    exit 1
  fi
  
  # Process each table
  processed=0
  errors=0
  
  for table in $TABLES; do
    if process_table "$table"; then
      ((processed++))
    else
      ((errors++))
    fi
  done
  
  # Generate reports
  generate_report
  test_policies
  
  echo -e "\n${GREEN}✅ RLS Policy Generation Complete!${NC}"
  echo "Processed: $processed tables"
  echo "Errors: $errors"
  
  if [ $errors -eq 0 ]; then
    print_success "All tables now have appropriate RLS policies!"
  else
    print_warning "Some tables had issues. Check the output above."
  fi
}

# Handle command line args
case "$1" in
  --help)
    echo "RLS Policy Generator Script"
    echo "Usage: $0 [--help|--dry-run]"
    echo ""
    echo "Options:"
    echo "  --help     Show this help message"
    echo "  --dry-run  Show what would be done without making changes"
    echo ""
    echo "This script automatically enables RLS and creates policies for all tables."
    exit 0
    ;;
  --dry-run)
    echo "DRY RUN MODE - No changes will be made"
    DRY_RUN=true
    ;;
esac

main "$@"