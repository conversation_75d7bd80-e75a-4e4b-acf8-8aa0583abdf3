#!/bin/bash

# Test Supabase Connection for Publish-AI
# Verifies that your Supabase configuration is working correctly.
# Uses psql to interact directly with PostgreSQL

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}🚀 Supabase Connection Test for Publish-AI${NC}"
    echo "============================================================"
}

# Load environment variables from .env
load_env() {
    if [ -f ".env" ]; then
        set -a
        source <(grep -v '^#' .env | grep -v '^$' | sed 's/^/export /')
        set +a
    fi
}

# Check environment variables
check_env_vars() {
    print_info "🔍 Checking Supabase Configuration..."
    echo "============================================================"
    
    load_env
    
    if [ -z "$DATABASE_URL" ]; then
        print_error "Missing DATABASE_URL in .env file"
        print_info "Please add: DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres"
        return 1
    fi
    
    if [ -z "$SUPABASE_URL" ]; then
        print_error "Missing SUPABASE_URL in .env file"
        print_info "Please add: SUPABASE_URL=https://your-project.supabase.co"
        return 1
    fi
    
    if [ -z "$SUPABASE_SERVICE_KEY" ]; then
        print_error "Missing SUPABASE_SERVICE_KEY in .env file"
        print_info "Please add: SUPABASE_SERVICE_KEY=your-service-key-here"
        return 1
    fi
    
    print_success "DATABASE_URL found: $(echo $DATABASE_URL | sed 's/:[^@]*@/:***@/')"
    print_success "SUPABASE_URL found: $SUPABASE_URL"
    print_success "SUPABASE_SERVICE_KEY found: ${SUPABASE_SERVICE_KEY:0:20}..."
    
    return 0
}

# Test database connection
test_connection() {
    print_info "📊 Testing Database Connection..."
    echo "------------------------------------------------------------"
    
    # Test basic connection
    if psql "$DATABASE_URL" -c "SELECT version();" >/dev/null 2>&1; then
        print_success "Successfully connected to PostgreSQL"
        
        # Get connection details
        DB_INFO=$(psql "$DATABASE_URL" -t -c "
            SELECT 
                current_database() as database,
                current_user as user,
                inet_server_addr() as host,
                inet_server_port() as port,
                version() as version;
        " 2>/dev/null | head -1 | xargs)
        
        print_info "Connection details: $DB_INFO"
        
        # Test if we can create/drop a test table (permissions test)
        if psql "$DATABASE_URL" -c "
            CREATE TABLE IF NOT EXISTS connection_test (id INT);
            DROP TABLE connection_test;
        " >/dev/null 2>&1; then
            print_success "Database permissions verified"
        else
            print_warning "Limited database permissions (read-only?)"
        fi
        
        return 0
    else
        print_error "Failed to connect to database"
        print_info "Check your DATABASE_URL in .env file"
        return 1
    fi
}

# Check required tables
check_tables() {
    print_info "📋 Checking Required Tables..."
    
    REQUIRED_TABLES=("users" "books" "publications" "trends" "trend_analyses" "sales_data" "feedback_metrics" "scraped_market_data" "model_performance")
    
    local all_good=true
    local existing_count=0
    local missing_count=0
    
    for table in "${REQUIRED_TABLES[@]}"; do
        if psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | grep -q "t"; then
            # Get record count
            COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | xargs)
            printf "   ✅ %-25s - OK (%s records)\n" "$table" "$COUNT"
            ((existing_count++))
        else
            printf "   ❌ %-25s - MISSING\n" "$table"
            all_good=false
            ((missing_count++))
        fi
    done
    
    if $all_good; then
        print_success "All tables are properly set up!"
        return 0
    else
        print_warning "Missing $missing_count tables. Run: ./scripts/setup_supabase_db.sh"
        return 1
    fi
}

# Test write operations
test_write_operations() {
    print_info "✍️  Testing Write Operations..."
    
    # Check if users table exists first
    if ! psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users');" 2>/dev/null | grep -q "t"; then
        print_warning "Users table doesn't exist - skipping write test"
        return 0
    fi
    
    # Test user creation and deletion
    TEST_USER_ID="test-connection-$(date +%s)"
    
    # Insert test user
    if psql "$DATABASE_URL" -c "
        INSERT INTO users (id, email, full_name, subscription_tier) 
        VALUES ('$TEST_USER_ID', '<EMAIL>', 'Connection Test User', 'test');
    " >/dev/null 2>&1; then
        print_success "Successfully inserted test user"
        
        # Verify the user exists
        USER_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM users WHERE id = '$TEST_USER_ID';" 2>/dev/null | xargs)
        if [ "$USER_COUNT" -eq 1 ]; then
            print_success "Test user verified in database"
        else
            print_warning "Test user not found after insertion"
        fi
        
        # Clean up test user
        if psql "$DATABASE_URL" -c "DELETE FROM users WHERE id = '$TEST_USER_ID';" >/dev/null 2>&1; then
            print_success "Successfully deleted test user"
        else
            print_warning "Could not delete test user"
        fi
        
        return 0
    else
        print_error "Write operation failed"
        print_info "This might be due to RLS policies or insufficient permissions"
        return 1
    fi
}

# Check database views
check_views() {
    print_info "👁️  Checking Database Views..."
    
    VIEWS=("book_analytics" "user_book_summary" "trend_performance")
    
    for view in "${VIEWS[@]}"; do
        if psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.views WHERE table_schema = 'public' AND table_name = '$view');" 2>/dev/null | grep -q "t"; then
            printf "   ✅ %-25s - OK\n" "$view"
        else
            printf "   ⚠️  %-25s - Not found (optional)\n" "$view"
        fi
    done
}

# Check database functions
check_functions() {
    print_info "🔧 Checking Database Functions..."
    
    FUNCTIONS=("update_updated_at_column" "calculate_trend_score")
    
    for func in "${FUNCTIONS[@]}"; do
        if psql "$DATABASE_URL" -t -c "
            SELECT EXISTS (
                SELECT FROM information_schema.routines 
                WHERE routine_schema = 'public' 
                AND routine_name = '$func'
            );
        " 2>/dev/null | grep -q "t"; then
            printf "   ✅ %-25s - OK\n" "$func"
        else
            printf "   ⚠️  %-25s - Not found (optional)\n" "$func"
        fi
    done
}

# Check indexes and performance
check_performance() {
    print_info "⚡ Checking Database Performance..."
    
    # Check if we have indexes on key columns
    KEY_INDEXES=(
        "users:email"
        "books:user_id"
        "publications:book_id"
        "trends:category"
    )
    
    for index_info in "${KEY_INDEXES[@]}"; do
        table=$(echo "$index_info" | cut -d: -f1)
        column=$(echo "$index_info" | cut -d: -f2)
        
        # Check if table exists first
        if psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | grep -q "t"; then
            # Check for indexes on this column
            INDEX_COUNT=$(psql "$DATABASE_URL" -t -c "
                SELECT COUNT(*) 
                FROM pg_indexes 
                WHERE tablename = '$table' 
                AND indexdef LIKE '%$column%';
            " 2>/dev/null | xargs)
            
            if [ "$INDEX_COUNT" -gt 0 ]; then
                printf "   ✅ %-25s - Indexed\n" "$table.$column"
            else
                printf "   ⚠️  %-25s - No index\n" "$table.$column"
            fi
        fi
    done
    
    # Check database size
    DB_SIZE=$(psql "$DATABASE_URL" -t -c "
        SELECT pg_size_pretty(pg_database_size(current_database()));
    " 2>/dev/null | xargs)
    
    print_info "Database size: $DB_SIZE"
}

# Check optional features
check_optional_features() {
    print_info "🔧 Checking Optional Features..."
    echo "------------------------------------------------------------"
    
    load_env
    
    # Check JWT secret
    if [ -n "$SUPABASE_JWT_SECRET" ] && [ "$SUPABASE_JWT_SECRET" != "" ]; then
        print_success "JWT Secret configured - Auth features available"
    else
        print_info "JWT Secret not configured - Using basic auth"
    fi
    
    # Check storage URL
    if [ -n "$SUPABASE_STORAGE_URL" ]; then
        print_success "Storage URL configured - File uploads available"
    else
        print_info "Storage not configured - Files stored locally"
    fi
    
    # Check realtime
    if [ "${ENABLE_REALTIME:-false}" = "true" ]; then
        print_success "Realtime subscriptions enabled"
    else
        print_info "Realtime subscriptions not enabled"
    fi
    
    # Check if RLS is enabled
    RLS_TABLES=$(psql "$DATABASE_URL" -t -c "
        SELECT string_agg(tablename, ', ') 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND rowsecurity = true;
    " 2>/dev/null | xargs)
    
    if [ -n "$RLS_TABLES" ]; then
        print_success "Row Level Security enabled on: $RLS_TABLES"
    else
        print_info "Row Level Security not enabled"
    fi
}

# Show next steps
show_next_steps() {
    echo ""
    echo "============================================================"
    print_success "🎉 Connection test completed!"
    
    print_info "Next steps:"
    echo "1. Run: poetry run uvicorn app.main_supabase:app --reload"
    echo "2. Visit: http://localhost:8000/docs"
    echo "3. Start creating books with the API!"
    
    # Show useful psql commands
    echo ""
    print_info "Useful psql commands:"
    echo "  psql \"$DATABASE_URL\" -c \"\\dt\"          # List tables"
    echo "  psql \"$DATABASE_URL\" -c \"\\dv\"          # List views"
    echo "  psql \"$DATABASE_URL\" -c \"\\df\"          # List functions"
    echo "  psql \"$DATABASE_URL\" -c \"SELECT COUNT(*) FROM users;\"  # Count users"
}

# Main execution
main() {
    print_header
    
    # Check requirements
    if [ ! -f ".env" ]; then
        print_error ".env file not found"
        print_info "Copy .env.example to .env and configure your credentials"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        print_error "psql not found"
        print_info "Install PostgreSQL client:"
        print_info "  macOS: brew install postgresql"
        print_info "  Ubuntu/Debian: sudo apt-get install postgresql-client"
        exit 1
    fi
    
    # Run tests
    local overall_success=true
    
    # Test 1: Environment variables
    if ! check_env_vars; then
        overall_success=false
    fi
    
    # Test 2: Database connection
    if ! test_connection; then
        overall_success=false
    fi
    
    # Only continue with other tests if connection works
    if $overall_success; then
        
        # Test 3: Check tables
        tables_exist=true
        if ! check_tables; then
            tables_exist=false
            print_warning "Some tables missing - setup may be incomplete"
        fi
        
        # Test 4: Write operations (only if tables exist)
        if $tables_exist; then
            if ! test_write_operations; then
                print_warning "Write operations failed but connection works"
            fi
        fi
        
        # Test 5: Check views
        check_views
        
        # Test 6: Check functions
        check_functions
        
        # Test 7: Performance check
        check_performance
        
        # Test 8: Optional features
        check_optional_features
        
        # Show next steps
        show_next_steps
    fi
    
    if $overall_success; then
        print_success "\n✅ All core tests passed! Supabase is ready to use."
        exit 0
    else
        echo ""
        print_error "❌ Connection test failed. Please check your configuration."
        exit 1
    fi
}

# Run main function
main "$@"