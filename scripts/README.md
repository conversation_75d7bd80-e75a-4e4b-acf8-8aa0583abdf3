# Supabase Database Scripts

This folder contains utility scripts for managing your Supabase database.

## 📜 Available Scripts

### 🚀 `setup_supabase_db.sh`
**Sets up the database schema using psql**

```bash
./scripts/setup_supabase_db.sh
```

**What it does:**
- Tests connection to Supabase
- Checks which tables already exist
- Provides step-by-step instructions for running the schema
- Verifies setup after completion
- Tests basic database operations

**When to use:**
- First time setting up the database
- After creating a new Supabase project
- When you want to verify your schema is correct

---

### 🧪 `test_supabase_connection.sh`
**Tests your Supabase connection and configuration using psql**

```bash
./scripts/test_supabase_connection.sh
```

**What it does:**
- Validates environment variables
- Tests database connection
- Checks all required tables exist
- Verifies read/write operations
- Tests database views and functions

**When to use:**
- To verify your `.env` configuration
- Before starting development
- When troubleshooting connection issues
- After any configuration changes

---

### 🗑️ `clear_supabase_db.sh`
**Clears data from your database using psql**

```bash
./scripts/clear_supabase_db.sh
```

**What it does:**
- Offers 3 clearing options:
  1. **Clear data only** (preserve schema)
  2. **Clear data + reset sequences**
  3. **Drop everything** (complete reset)
- Counts records before deletion
- Provides confirmation prompts
- Shows progress for large datasets

**⚠️ WARNING:** This will delete data! Use with caution.

**When to use:**
- Clearing test data
- Resetting development database
- Starting fresh with clean data
- Before major schema changes

---

### 🔥 `revert_supabase_schema.sh`
**Completely removes all schema objects created by supabase_schema.sql**

```bash
./scripts/revert_supabase_schema.sh
# Or skip confirmation:
./scripts/revert_supabase_schema.sh --force
```

**What it does:**
- Drops ALL tables, views, functions, triggers, and custom types
- Removes objects in correct dependency order
- Preserves PostgreSQL extensions (uuid-ossp, pgcrypto)
- Verifies complete removal
- Provides detailed progress reporting

**⚠️ EXTREME WARNING:** This PERMANENTLY DELETES the entire schema!

**When to use:**
- Completely starting over with schema
- Preparing for schema migration
- Cleaning up before uninstalling
- Testing schema creation scripts

---

## 🔧 Setup Process

### First Time Setup

1. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

2. **Test connection**:
   ```bash
   ./scripts/test_supabase_connection.sh
   ```

3. **Set up database**:
   ```bash
   ./scripts/setup_supabase_db.sh
   ```

4. **Verify everything works**:
   ```bash
   ./scripts/test_supabase_connection.sh
   ```

### Resetting Database

1. **Clear data** (if you want to keep schema):
   ```bash
   ./scripts/clear_supabase_db.sh
   # Choose option 1: Clear all data (preserve schema)
   ```

2. **Complete reset** (if you want to start over):
   ```bash
   ./scripts/clear_supabase_db.sh
   # Choose option 3: DROP EVERYTHING
   # Then run setup again:
   ./scripts/setup_supabase_db.sh
   ```

## 📋 Requirements

- PostgreSQL client (`psql`)
  - **macOS**: `brew install postgresql`
  - **Ubuntu/Debian**: `sudo apt-get install postgresql-client`
  - **CentOS/RHEL**: `sudo yum install postgresql`
- Supabase account and project
- Environment variables configured in `.env`:
  - `DATABASE_URL` (PostgreSQL connection string)
  - `SUPABASE_URL`
  - `SUPABASE_SERVICE_KEY`

## 🚨 Important Notes

### Security
- **Never commit `.env` files** to version control
- **Keep your service key secret** - it has admin access
- **Use different projects** for development and production

### Data Safety
- **Always backup** important data before clearing
- **Test on development** before running on production
- **The clear script requires confirmation** for safety

### Troubleshooting

**Connection Issues:**
```bash
# Check your credentials
./scripts/test_supabase_connection.sh

# Verify .env file exists and has correct values
cat .env | grep -E "(DATABASE_URL|SUPABASE)"

# Test direct psql connection
psql "$DATABASE_URL" -c "SELECT version();"
```

**Missing Tables:**
```bash
# Re-run setup
./scripts/setup_supabase_db.sh

# Or check what tables exist
psql "$DATABASE_URL" -c "\dt"
```

**Permission Errors:**
- Ensure you're using the `service_role` key, not `anon` key
- Check that Row Level Security (RLS) policies allow your operations

## 🔗 Related Files

- `../supabase_schema.sql` - Database schema definition
- `../.env` - Environment configuration
- `../SUPABASE_SETUP.md` - Detailed setup instructions
- `../TESTING_SETUP.md` - Testing configuration guide

## 🆘 Getting Help

If you encounter issues:

1. Check the error messages carefully
2. Verify your `.env` configuration
3. Test your connection first
4. Check the Supabase Dashboard for error logs
5. Review the main setup guide: `../SUPABASE_SETUP.md`

---

*Generated for Publish-AI - An AI-powered e-book generation system*