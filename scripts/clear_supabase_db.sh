#!/bin/bash

# Clear Supabase Database for Publish-AI
# WARNING: This will <PERSON>LETE ALL DATA from the database!
# Uses psql to interact directly with PostgreSQL

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Helper functions
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error()   { echo -e "${RED}❌ $1${NC}"; }
print_info()    { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

print_header() {
  echo -e "\n${RED}${BOLD}⚠️  Supabase Database Cleaner for Publish-AI ⚠️${NC}"
  echo "============================================================"
}

# Load .env variables safely
load_env() {
  if [ -f ".env" ]; then
    # Export only DATABASE_URL to avoid polluting environment
    # Handle both single and double quotes
    export DATABASE_URL=$(grep '^DATABASE_URL=' .env | cut -d '=' -f2- | sed "s/^['\"]//; s/['\"]$//")
    if [ -n "$DATABASE_URL" ]; then
      print_success ".env DATABASE_URL loaded"
      # Test if URL looks valid
      if [[ "$DATABASE_URL" == postgresql://* ]]; then
        print_success "DATABASE_URL format looks correct"
      else
        print_warning "DATABASE_URL format may be incorrect (should start with postgresql://)"
      fi
    else
      print_error "DATABASE_URL not found in .env"
      print_info "Run ./scripts/fix_database_url.sh to set up your DATABASE_URL"
      exit 1
    fi
  else
    print_error ".env file not found"
    exit 1
  fi
}

# Test DB connection with timeout
test_connection() {
  if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL is empty"
    exit 1
  fi

  print_info "Testing database connection..."
  if timeout 10 psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
    # Mask password in URL for display
    MASKED_URL=$(echo $DATABASE_URL | sed 's/:[^@]*@/:***@/')
    print_success "Connected to Supabase: $MASKED_URL"
    return 0
  else
    print_error "Failed to connect to Supabase (timeout after 10 seconds)"
    return 1
  fi
}

# Count total records with timeout per query
count_records() {
  print_info "Counting records before clearing..."
  TABLES=("users" "books" "publications" "sales_data" "trends" "trend_analyses" "feedback_metrics" "verl_training_jobs" "model_performance" "sales_predictions" "market_analyses" "prediction_accuracy" "api_keys" "api_key_usage" "security_audit_events" "scraped_market_data")
  TOTAL_RECORDS=0
  
  for table in "${TABLES[@]}"; do
    # Check if table exists with timeout
    if timeout 5 psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | grep -q "t"; then
      # Count records with timeout
      COUNT=$(timeout 5 psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | xargs || echo "0")
      if [ "$COUNT" -gt 0 ] 2>/dev/null; then
        echo "  $table: $COUNT records"
        TOTAL_RECORDS=$((TOTAL_RECORDS + COUNT))
      fi
    else
      print_warning "Table $table not found or inaccessible"
    fi
  done
  
  if [ "$TOTAL_RECORDS" -eq 0 ]; then
    print_info "Database is already empty!"
    return 1
  fi
  print_warning "Total records to delete: $TOTAL_RECORDS"
  return 0
}

# Clear data with better error handling and timeouts
clear_data_only() {
  print_info "Clearing all data (preserving schema)..."
  
  # Tables in dependency order (children first)
  TABLES=("api_key_usage" "api_keys" "security_audit_events" "prediction_accuracy" "market_analyses" "sales_predictions" "model_performance" "verl_training_jobs" "feedback_metrics" "scraped_market_data" "sales_data" "publications" "trend_analyses" "trends" "books" "users")
  
  local cleared=0
  local errors=0

  # Disable foreign key checks temporarily for faster deletion
  print_info "Disabling foreign key checks..."
  timeout 10 psql "$DATABASE_URL" -c "SET session_replication_role = replica;" >/dev/null 2>&1 || print_warning "Could not disable FK checks"

  for table in "${TABLES[@]}"; do
    print_info "Processing table: $table"
    
    # Check if table exists
    if ! timeout 5 psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | grep -q "t"; then
      print_warning "Table $table does not exist, skipping"
      continue
    fi
    
    # Get count with timeout
    BEFORE_COUNT=$(timeout 5 psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | xargs || echo "0")
    
    if [ "$BEFORE_COUNT" -eq 0 ] 2>/dev/null; then
      print_info "$table is already empty"
      ((cleared++))
      continue
    fi
    
    # Clear table with timeout - use TRUNCATE for faster deletion
    print_info "Truncating $table ($BEFORE_COUNT records)..."
    if timeout 30 psql "$DATABASE_URL" -c "TRUNCATE TABLE $table RESTART IDENTITY CASCADE;" >/dev/null 2>&1; then
      print_success "Cleared $table ($BEFORE_COUNT records deleted)"
      ((cleared++))
    else
      print_warning "TRUNCATE failed for $table, trying DELETE..."
      if timeout 60 psql "$DATABASE_URL" -c "DELETE FROM $table;" >/dev/null 2>&1; then
        print_success "Cleared $table with DELETE ($BEFORE_COUNT records deleted)"
        ((cleared++))
      else
        print_error "Failed to clear $table"
        ((errors++))
      fi
    fi
  done

  # Re-enable foreign key checks
  print_info "Re-enabling foreign key checks..."
  timeout 10 psql "$DATABASE_URL" -c "SET session_replication_role = DEFAULT;" >/dev/null 2>&1 || print_warning "Could not re-enable FK checks"

  echo -e "\n============================================================"
  echo "Database clearing complete!"
  echo "  ✅ Cleared: $cleared tables"
  echo "  ❌ Errors: $errors"
  echo "============================================================"
  return $errors
}

# Quick clear function (no confirmation)
quick_clear() {
  print_header
  load_env
  test_connection || exit 1
  
  print_warning "QUICK CLEAR MODE - No confirmation required"
  clear_data_only
  
  if [ $? -eq 0 ]; then
    print_success "Database cleared successfully!"
  else
    print_error "Some errors occurred during clearing"
    exit 1
  fi
}

# Confirmation step with timeout
get_confirmation() {
  if [ "$FORCE_MODE" = true ]; then return 0; fi
  
  local timeout_seconds=30
  local timestamp=$(date +"%Y%m%d_%H%M%S")
  local confirm_text="DELETE_${timestamp}"
  
  echo -e "\n${RED}Type '${confirm_text}' to confirm $1:${NC}"
  echo -e "${YELLOW}(You have $timeout_seconds seconds)${NC}"
  
  # Use timeout for read
  if timeout $timeout_seconds bash -c "read -r response && [ \"\$response\" == \"$confirm_text\" ]"; then
    return 0
  else
    print_error "Confirmation failed or timed out"
    return 1
  fi
}

# Menu with timeout
show_menu() {
  echo -e "\nSelect an option:"
  echo "1. Clear all data (preserve schema) - RECOMMENDED"
  echo "2. Quick clear (no confirmation)"
  echo "3. Cancel"
  echo -e "\n${YELLOW}Enter your choice (1-3):${NC}"
  
  # Use timeout for menu selection
  if timeout 30 bash -c 'read -r choice; echo $choice'; then
    choice=$(timeout 30 bash -c 'read -r choice; echo $choice' 2>/dev/null || echo "3")
    case $choice in
      1) echo "clear_data";;
      2) echo "quick_clear";;
      3) exit 0;;
      *) print_error "Invalid choice or timeout."; exit 1;;
    esac
  else
    print_error "Menu selection timed out"
    exit 1
  fi
}

# Main function
main() {
  # Handle command line arguments
  case "$1" in
    --quick)
      quick_clear
      exit 0
      ;;
    --force)
      export FORCE_MODE=true
      ;;
    --help)
      echo "Usage: $0 [--quick|--force|--help]"
      echo "  --quick  Clear database without confirmation"
      echo "  --force  Skip all confirmations"
      echo "  --help   Show this help"
      exit 0
      ;;
  esac
  
  print_header
  load_env
  
  # Check if psql is available
  if ! command -v psql >/dev/null; then
    print_error "psql not found. Please install PostgreSQL client."
    exit 1
  fi
  
  test_connection || exit 1
  count_records || exit 0
  
  action=$(show_menu)
  case $action in
    clear_data)
      if get_confirmation " data wipe"; then
        clear_data_only && print_success "Data cleared successfully!"
      else
        print_info "Operation cancelled"
      fi
      ;;
    quick_clear)
      quick_clear
      ;;
  esac
}

main "$@"