#!/bin/bash

# Setup Supabase Database for Publish-AI
# Uses psql to set up the database schema directly

set -e # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}${BOLD}🚀 Supabase Database Setup for Publish-AI${NC}"
    echo "============================================================"
}

# Load environment variables from .env
load_env() {
    if [ -f ".env" ]; then
        if grep -qvE '^[A-Za-z_][A-Za-z0-9_]*=.*' .env; then
            print_warning ".env may contain malformed lines"
        fi
        set -o allexport
        source .env
        set +o allexport
        print_success ".env file loaded"
    else
        print_error ".env file not found"
        exit 1
    fi
}

# Generate .env.example
generate_env_example() {
    cat <<EOF >.env.example
DATABASE_URL='postgresql://postgres:<EMAIL>:6543/postgres'
EOF
    print_success "Generated .env.example"
}

# Check requirements
check_requirements() {
    print_info "Checking requirements..."

    if [ ! -f ".env" ]; then
        print_error ".env file not found"
        print_info "Copy .env.example to .env and configure your Supabase credentials"
        generate_env_example
        return 1
    fi

    if [ ! -f "supabase_production_schema.sql" ]; then
        print_error "supabase_production_schema.sql not found"
        print_info "Make sure the production schema file exists in the project root"
        return 1
    fi

    if ! command -v psql &>/dev/null; then
        print_error "psql not found"
        print_info "Install PostgreSQL client:"
        print_info "  macOS: brew install postgresql"
        print_info "  Ubuntu/Debian: sudo apt-get install postgresql-client"
        print_info "  CentOS/RHEL: sudo yum install postgresql"
        return 1
    fi

    print_success "All requirements met"
    return 0
}

# Test connection to Supabase
test_connection() {
    print_info "Step 1: Testing Supabase connection..."

    load_env
    echo "$DATABASE_URL"
    if [ -z "$DATABASE_URL" ]; then
        print_error "Missing DATABASE_URL in .env"
        print_info "Add: DATABASE_URL='postgresql://user:pass@host:port/db'"
        return 1
    fi

    print_info "Connecting to: $DATABASE_URL"

    if psql "$DATABASE_URL" -c "SELECT version();" >/dev/null 2>&1; then
        print_success "Successfully connected to Supabase PostgreSQL"
        DB_INFO=$(psql "$DATABASE_URL" -t -c "SELECT current_database(), current_user, inet_server_addr(), inet_server_port();" 2>/dev/null)
        print_info "Connected to: $DB_INFO"
        return 0
    else
        print_error "Failed to connect to Supabase"
        print_info "Check your DATABASE_URL in .env file"
        return 1
    fi
}

# Check current schema status
check_schema() {
    print_info "Step 2: Checking current schema status..."

    REQUIRED_TABLES=("users" "books" "publications" "trends" "trend_analyses" "sales_data" "feedback_metrics" "scraped_market_data" "model_performance" "verl_training_jobs" "sales_predictions" "market_analyses" "prediction_accuracy" "api_keys" "api_key_usage" "security_audit_events" "gdpr_data_subject_requests" "processing_activities" "privacy_impact_assessments" "data_retention_policies" "data_anonymization_log" "compliance_audit_events")
    EXISTING_TABLES=()
    MISSING_TABLES=()

    for table in "${REQUIRED_TABLES[@]}"; do
        if psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | grep -q "t"; then
            EXISTING_TABLES+=("$table")
        else
            MISSING_TABLES+=("$table")
        fi
    done

    if [ ${#EXISTING_TABLES[@]} -gt 0 ]; then
        print_success "Found ${#EXISTING_TABLES[@]} existing tables:"
        for table in "${EXISTING_TABLES[@]}"; do
            echo "   ✅ $table"
        done
    fi

    if [ ${#MISSING_TABLES[@]} -eq 0 ]; then
        print_success "All tables already exist! Database setup is complete."
        return 0
    else
        print_warning "Missing ${#MISSING_TABLES[@]} tables:"
        for table in "${MISSING_TABLES[@]}"; do
            echo "   ❌ $table"
        done
        return 1
    fi
}

# Execute schema SQL
execute_schema() {
    print_info "Step 3: Executing database schema..."
    print_info "Running supabase_production_schema.sql..."
    SCHEMA_LOG="setup_schema.log"

    if psql "$DATABASE_URL" -f "supabase_production_schema.sql" >"$SCHEMA_LOG" 2>&1; then
        print_success "Schema executed successfully"
        return 0
    else
        print_error "Schema execution failed. See $SCHEMA_LOG for details."
        cat "$SCHEMA_LOG"
        return 1
    fi
}

# Verify setup completion
verify_setup() {
    print_info "Step 4: Verifying database setup..."

    if check_schema; then
        print_success "🎉 Database setup complete! All tables are ready."
        print_info "Testing basic operations..."

        TEST_USER_ID=$(uuidgen 2>/dev/null || echo "test-setup-$(date +%s)")
        if psql "$DATABASE_URL" -c "INSERT INTO users (id, email, full_name, subscription_tier) VALUES ('$TEST_USER_ID', '<EMAIL>', 'Setup Test User', 'free');" >/dev/null 2>&1; then
            print_success "✅ User creation test passed"
            if psql "$DATABASE_URL" -c "DELETE FROM users WHERE id = '$TEST_USER_ID';" >/dev/null 2>&1; then
                print_success "✅ User deletion test passed"
            else
                print_warning "Could not clean up test user"
            fi
        else
            print_warning "Basic operations test failed (might be RLS policies)"
        fi

        print_info "Table status:"
        for table in "${REQUIRED_TABLES[@]}"; do
            COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | xargs)
            if [ "$COUNT" ]; then
                echo "   📊 $table: $COUNT records"
            fi
        done

        print_info "\nNext steps:"
        echo "1. Run: ./scripts/test_supabase_connection.sh"
        echo "2. Start the app: poetry run uvicorn app.main_supabase:app --reload"
        echo "3. Visit: http://localhost:8000/docs"

        return 0
    else
        print_error "Setup incomplete. Some tables are still missing."
        return 1
    fi
}

# Main execution
main() {
    print_header

    if ! check_requirements; then exit 1; fi
    load_env
    if ! test_connection; then exit 1; fi
    if check_schema; then exit 0; fi

    print_warning "This will create all tables, views, and functions in your Supabase database."
    echo -e "\n${YELLOW}Continue with setup? (yes/no):${NC}"
    read -r response
    if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "Setup cancelled."
        exit 0
    fi

    if ! execute_schema; then exit 1; fi
    if verify_setup; then
        print_success "Setup completed successfully!"
        exit 0
    else
        print_error "Setup verification failed"
        exit 1
    fi
}

main "$@"
