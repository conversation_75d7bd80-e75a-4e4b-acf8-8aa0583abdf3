#!/bin/bash

# Fix Database URL for Supabase Connection
# This script helps generate the correct DATABASE_URL from Supabase credentials

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error()   { echo -e "${RED}❌ $1${NC}"; }
print_info()    { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

echo -e "\n${BLUE}🔧 Supabase DATABASE_URL Fixer${NC}"
echo "============================================"

# Load current .env
if [ -f ".env" ]; then
    source .env
    print_success "Loaded .env file"
else
    print_error ".env file not found"
    exit 1
fi

# Extract Supabase project details
if [ -n "$SUPABASE_URL" ]; then
    PROJECT_REF=$(echo $SUPABASE_URL | sed 's|https://||' | sed 's|\.supabase\.co||')
    print_info "Project Reference: $PROJECT_REF"
else
    print_error "SUPABASE_URL not found in .env"
    exit 1
fi

echo -e "\n${YELLOW}To get your correct DATABASE_URL:${NC}"
echo "1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/$PROJECT_REF"
echo "2. Click on 'Settings' → 'Database'"
echo "3. Scroll down to 'Connection Parameters' or 'Connection pooling'"
echo "4. Look for 'Connection string' or 'Database URL'"
echo "5. Copy the connection string that looks like:"
echo "   postgresql://postgres.[ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres"

echo -e "\n${YELLOW}Or use these details to construct it manually:${NC}"
echo "Host: aws-0-us-east-2.pooler.supabase.com (or your region)"
echo "Port: 6543"
echo "Database: postgres"
echo "Username: postgres.$PROJECT_REF"
echo "Password: [Your database password - NOT the API keys]"

echo -e "\n${RED}⚠️  IMPORTANT:${NC}"
echo "- Use your DATABASE PASSWORD (set when you created the project)"
echo "- NOT the SUPABASE_SERVICE_KEY or SUPABASE_ANON_KEY"
echo "- If you forgot your password, reset it in Supabase Dashboard → Settings → Database"

echo -e "\n${BLUE}Current DATABASE_URL in .env:${NC}"
if [ -n "$DATABASE_URL" ]; then
    # Mask the password for security
    MASKED_URL=$(echo $DATABASE_URL | sed 's/:[^@]*@/:***@/')
    echo "$MASKED_URL"
else
    echo "Not set"
fi

echo -e "\n${YELLOW}Would you like to update your DATABASE_URL now? (y/n):${NC}"
read -r response

if [ "$response" = "y" ] || [ "$response" = "Y" ]; then
    echo -e "${BLUE}Enter your new DATABASE_URL:${NC}"
    read -r new_url
    
    if [ -n "$new_url" ]; then
        # Backup current .env
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        
        # Update DATABASE_URL in .env
        if grep -q "^DATABASE_URL=" .env; then
            # Replace existing DATABASE_URL
            sed -i.bak "s|^DATABASE_URL=.*|DATABASE_URL='$new_url'|" .env
        else
            # Add new DATABASE_URL
            echo "DATABASE_URL='$new_url'" >> .env
        fi
        
        print_success "Updated DATABASE_URL in .env"
        
        # Test the connection
        print_info "Testing new connection..."
        if timeout 10 psql "$new_url" -c "SELECT 1;" >/dev/null 2>&1; then
            print_success "✅ Connection test successful!"
            echo -e "\n${GREEN}You can now run: ./scripts/clear_supabase_db.sh${NC}"
        else
            print_error "❌ Connection test failed"
            echo "Please verify your DATABASE_URL is correct"
        fi
    else
        print_info "No URL provided, skipping update"
    fi
else
    print_info "Skipping update"
fi

echo -e "\n${BLUE}Troubleshooting Tips:${NC}"
echo "• Make sure you're using the database password, not API keys"
echo "• Check if your IP is allowed in Supabase Dashboard → Settings → Database → Network"
echo "• Try connecting from Supabase SQL Editor first to verify credentials"
echo "• If using connection pooling, try the direct connection string instead"