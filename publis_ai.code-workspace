{
  "folders": [
    {
      "name": "Publishing App (Poetry)",
      "path": ".",
    },
  ],
  "settings": {
    // Poetry Python Configuration
    "python.defaultInterpreterPath": "./pyproject.toml",
    "python.terminal.activateEnvironment": true,
    "python.poetryPath": "poetry",

    // Python Settings
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=88"],
    "python.sortImports.args": ["--profile", "black"],

    // Editor Settings
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true,
      "source.fixAll": true,
    },
    "editor.rulers": [88],
    "editor.tabSize": 4,
    "editor.insertSpaces": true,

    // File Associations
    "files.associations": {
      "*.env*": "dotenv",
      "Dockerfile*": "dockerfile",
      "docker-compose*.yml": "dockercompose",
      "pyproject.toml": "toml",
      "poetry.lock": "toml",
    },

    // Search & Files (Poetry-specific exclusions)
    "search.exclude": {
      "**/node_modules": true,
      "**/.venv": true,
      "**/__pycache__": true,
      "**/storage": true,
      "**/logs": true,
      "**/.git": true,
      "**/models": true,
      "**/checkpoints": true,
      "**/dist": true,
      "**/.pytest_cache": true,
    },
    "files.exclude": {
      "**/__pycache__": true,
      "**/*.pyc": true,
      "**/.venv": true,
      "**/.pytest_cache": true,
      "**/node_modules": true,
      "**/dist": true,
    },

    // Git Configuration
    "git.autofetch": true,
    "git.confirmSync": false,
    "git.enableSmartCommit": true,
    "git.postCommitCommand": "push",
    "gitlens.currentLine.enabled": false,

    // Terminal Configuration (Poetry-aware)
    "terminal.integrated.defaultProfile.linux": "bash",
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.cwd": "${workspaceFolder}",
    "terminal.integrated.env.linux": {
      "POETRY_ACTIVE": "1",
    },
    "terminal.integrated.env.osx": {
      "POETRY_ACTIVE": "1",
    },

    // Docker Configuration
    "docker.showStartPage": false,
    "docker.commands.build": "${workspaceFolder}/Dockerfile",
    "docker.commands.compose": "${workspaceFolder}/docker-compose.yml",

    // Poetry & VERL Development Specific
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.testing.unittestEnabled": false,
    "python.testing.cwd": "${workspaceFolder}",
    "jupyter.askForKernelRestart": false,

    // API Development
    "rest-client.environmentVariables": {
      "local": {
        "baseUrl": "http://localhost:8000",
        "verlUrl": "http://localhost:8001",
      },
      "docker": {
        "baseUrl": "http://localhost:8000",
        "verlUrl": "http://localhost:8001",
      },
    },

    // Code Quality
    "mypy.enabled": true,
    "mypy.targets": ["app"],
    "pylint.args": ["--load-plugins=pylint_django"],

    // Spell Checker (Poetry + VERL specific)
    "cSpell.words": [
      "VERL",
      "fastapi",
      "pydantic",
      "uvicorn",
      "anthropic",
      "openai",
      "redis",
      "postgresql",
      "sqlite",
      "celery",
      "kdp",
      "ebook",
      "amazonaws",
      "pytorch",
      "transformers",
      "pyproject",
      "toml",
      "poetry",
    ],
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Poetry: Install Dependencies",
        "type": "shell",
        "command": "poetry",
        "args": ["install"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
        "problemMatcher": [],
      },
      {
        "label": "Poetry: Start Development Server",
        "type": "shell",
        "command": "poetry",
        "args": [
          "run",
          "uvicorn",
          "app.main:app",
          "--reload",
          "--host",
          "0.0.0.0",
          "--port",
          "8000",
        ],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
        "problemMatcher": [],
      },
      {
        "label": "Poetry: Run Tests",
        "type": "shell",
        "command": "poetry",
        "args": ["run", "pytest", "tests/", "-v", "--cov=app"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Poetry: Check VERL Health",
        "type": "shell",
        "command": "poetry",
        "args": [
          "run",
          "python",
          "-c",
          "import requests; print(requests.get('http://localhost:8000/api/verl/health').json())",
        ],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Poetry: Format Code",
        "type": "shell",
        "command": "poetry",
        "args": ["run", "black", "app/", "tests/"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "silent",
          "focus": false,
          "panel": "shared",
        },
      },
      {
        "label": "Poetry: Lint Code",
        "type": "shell",
        "command": "poetry",
        "args": ["run", "flake8", "app/", "tests/"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Poetry: Type Check",
        "type": "shell",
        "command": "poetry",
        "args": ["run", "mypy", "app/"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Poetry: Update Dependencies",
        "type": "shell",
        "command": "poetry",
        "args": ["update"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Poetry: Show Environment Info",
        "type": "shell",
        "command": "poetry",
        "args": ["env", "info"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "Docker: Compose Up",
        "type": "shell",
        "command": "docker-compose",
        "args": ["up", "--build", "-d"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
      {
        "label": "VERL: Integration Test",
        "type": "shell",
        "command": "poetry",
        "args": ["run", "pytest", "tests/test_verl/", "-v"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new",
        },
      },
    ],
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Poetry: Debug FastAPI",
        "type": "python",
        "request": "launch",
        "program": "${workspaceFolder}",
        "module": "uvicorn",
        "args": [
          "app.main:app",
          "--reload",
          "--host",
          "0.0.0.0",
          "--port",
          "8000",
        ],
        "env": {
          "DEBUG": "true",
          "LOG_LEVEL": "DEBUG",
        },
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "python": "${command:python.interpreterPath}",
      },
      {
        "name": "Poetry: Debug VERL Service",
        "type": "python",
        "request": "launch",
        "program": "${workspaceFolder}/app/ml/verl_service.py",
        "env": {
          "DEBUG": "true",
          "LOG_LEVEL": "DEBUG",
          "REDIS_URL": "redis://localhost:6379",
        },
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "python": "${command:python.interpreterPath}",
      },
      {
        "name": "Poetry: Test Specific File",
        "type": "python",
        "request": "launch",
        "module": "pytest",
        "args": ["${file}", "-v"],
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "python": "${command:python.interpreterPath}",
      },
      {
        "name": "Poetry: Run Config Validation",
        "type": "python",
        "request": "launch",
        "program": "${workspaceFolder}/app/config.py",
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "python": "${command:python.interpreterPath}",
      },
    ],
  },
}
