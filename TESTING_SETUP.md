# Testing Setup Guide

## Supabase Database Testing

The tests require a real Supabase database connection to properly test the database integration. Here's how to set it up:

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Create a new project
3. Wait for the project to be fully initialized

### 2. Get Your Credentials

From your Supabase dashboard:

1. Go to **Settings** → **API**
2. Copy your:
   - **Project URL** (looks like `https://your-project.supabase.co`)
   - **Service Role Key** (secret key, keep this secure!)
   - **Anon Key** (public key)

3. Go to **Settings** → **Database** 
4. Copy your **Connection String** (PostgreSQL format)

### 3. Set Up Your Environment

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Supabase credentials:
   ```bash
   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_SERVICE_KEY=your-service-role-key-here
   SUPABASE_ANON_KEY=your-anon-key-here
   DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
   ```

### 4. Initialize the Database Schema

Run the SQL schema to create the required tables:

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy and paste the contents of `supabase_schema.sql`
3. Run the query to create all tables

### 5. Run the Tests

Now you can run tests that require the database:

```bash
# Run all tests
poetry run pytest

# Run only database tests
poetry run pytest -m supabase

# Run a specific test
poetry run pytest tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_success -v
```

### 6. Test Markers

Tests are marked with different categories:

- `@pytest.mark.supabase` - Requires Supabase connection
- `@pytest.mark.database` - Requires database access
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.slow` - Slow-running tests

### 7. Skipping Database Tests

If you don't have Supabase configured, the tests will automatically skip with a message:

```
SKIPPED [1] - Supabase credentials not configured. Set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.
```

### 8. Test Data Cleanup

The test fixtures automatically:
- Create test users and books before each test
- Clean up all test data after each test
- Handle foreign key constraints properly

### Security Notes

⚠️ **Important Security Guidelines:**

1. **Never commit your `.env` file** - it's already in `.gitignore`
2. **Use a separate test project** - don't use your production Supabase project for testing
3. **Rotate keys regularly** - especially if they might have been exposed
4. **Use Row Level Security (RLS)** - the schema includes RLS policies for data protection

### Troubleshooting

**Connection Issues:**
- Verify your credentials are correct
- Check that your Supabase project is active
- Ensure your IP is not blocked (Supabase has IP restrictions in some cases)

**Schema Issues:**
- Make sure you ran the `supabase_schema.sql` file completely
- Check that all tables were created successfully
- Verify the RLS policies are in place

**Test Failures:**
- Check the test output for specific error messages
- Verify your test data is being created and cleaned up properly
- Run tests with `-v` flag for more verbose output