# Centralized Logging with <PERSON><PERSON><PERSON> Stack for Publish AI

A production-ready centralized logging solution using Elasticsearch, Logstash, and Kibana (ELK) with Filebeat for comprehensive log aggregation, analysis, and visualization across all microservices.

## Overview

The centralized logging system provides:

- **Complete Log Aggregation**: All microservice logs in a single, searchable location
- **Real-time Analysis**: Instant log search and filtering capabilities
- **Advanced Visualization**: Interactive dashboards and charts for log analytics
- **Automated Alerting**: Log-based alerts for critical events and errors
- **Security Monitoring**: Audit trails and security event detection
- **Performance Insights**: Application performance monitoring through logs

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Centralized Logging Pipeline                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │   Log       │    │   Data      │    │   Search &  │         │
│  │ Collection  │───▶│ Processing  │───▶│ Visualization│         │
│  │ (Filebeat)  │    │ (Logstash)  │    │  (Kibana)   │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │               │
│         │                   │                   │               │
│  ┌──────┴─────────────────────┴─────────────────┴──────┐        │
│  │              Data Storage & Indexing                │        │
│  │                (Elasticsearch)                      │        │
│  │                                                     │        │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │        │
│  │  │Service A│  │Service B│  │Service C│  │Service D│ │        │
│  │  │  Logs   │  │  Logs   │  │  Logs   │  │  Logs   │ │        │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │        │
│  │                                                     │        │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │        │
│  │  │Istio    │  │K8s      │  │System   │  │Security │ │        │
│  │  │Logs     │  │Logs     │  │Logs     │  │Logs     │ │        │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │        │
│  └─────────────────────────────────────────────────────┘        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Components

### 1. Elasticsearch Cluster

High-performance distributed search and analytics engine:

- **Multi-node Setup**: 3-node cluster for high availability
- **Index Management**: Automated index lifecycle management
- **Data Retention**: Configurable retention policies (7 days hot, 30 days warm, 90 days cold)
- **Security**: Authentication, authorization, and encryption
- **Performance Optimization**: Custom mappings and shard allocation

### 2. Logstash

Powerful data processing pipeline:

- **Input Plugins**: Beats, Kafka, HTTP, and file inputs
- **Filter Plugins**: Parsing, enrichment, and transformation
- **Output Plugins**: Elasticsearch, monitoring systems, and alerts
- **Performance**: Multi-pipeline processing with queue management
- **Resilience**: Dead letter queues and error handling

### 3. Kibana

Interactive data visualization and exploration:

- **Discover**: Real-time log search and filtering
- **Visualizations**: Charts, graphs, and heatmaps
- **Dashboards**: Pre-built and custom dashboards
- **Alerting**: Watcher-based alerts and notifications
- **Security**: Role-based access control

### 4. Filebeat

Lightweight log shipper:

- **Container Integration**: Kubernetes container log collection
- **Multi-line Support**: Stack trace and error log handling
- **Autodiscovery**: Automatic service discovery and configuration
- **Resilience**: At-least-once delivery guarantees
- **Performance**: Low resource footprint

## Log Sources

### Application Logs

All microservice application logs with structured JSON format:

```json
{
  "@timestamp": "2024-07-06T10:30:00.000Z",
  "level": "INFO",
  "service": "content-generation",
  "namespace": "tier1-services",
  "pod": "content-generation-7b8f9d6c54-8xk2p",
  "container": "content-generation",
  "message": "AI content generation completed successfully",
  "request_id": "req-12345",
  "user_id": "user-67890",
  "duration": 2500,
  "ai_model": "gpt-4",
  "tokens_used": 1250,
  "cost_cents": 15,
  "content_quality": 4.2
}
```

### System Logs

Kubernetes and infrastructure logs:

- **Kubernetes Events**: Pod lifecycle, deployments, and errors
- **Container Logs**: Runtime messages and system events
- **Node Logs**: System-level messages and resource alerts
- **Storage Logs**: Volume mount and disk I/O events

### Security Logs

Security-related events and audit trails:

- **Authentication Events**: Login attempts and token validation
- **Authorization Events**: Access control and permission checks
- **API Security**: Rate limiting and suspicious activity
- **Network Security**: Istio security policy violations

### Performance Logs

Application and system performance metrics:

- **Request Tracing**: Distributed trace correlation
- **Resource Usage**: CPU, memory, and disk utilization
- **Database Operations**: Query performance and connection pooling
- **External API Calls**: Third-party service interactions

## Index Management

### Index Templates

Structured index templates for different log types:

```yaml
# Application logs template
application-logs-*:
  settings:
    number_of_shards: 3
    number_of_replicas: 1
    refresh_interval: 5s
  mappings:
    properties:
      "@timestamp":
        type: date
      level:
        type: keyword
      service:
        type: keyword
      message:
        type: text
        analyzer: standard
      request_id:
        type: keyword
      duration:
        type: long
```

### Lifecycle Policies

Automated index lifecycle management:

```yaml
# 7-day hot, 30-day warm, 90-day cold policy
logging-policy:
  phases:
    hot:
      actions:
        rollover:
          max_size: 10GB
          max_age: 1d
    warm:
      min_age: 7d
      actions:
        allocate:
          number_of_replicas: 0
    cold:
      min_age: 30d
      actions:
        allocate:
          number_of_replicas: 0
    delete:
      min_age: 90d
```

## Log Processing Pipelines

### Structured Logging Pipeline

For JSON-formatted application logs:

```ruby
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][log_type] == "application" {
    json {
      source => "message"
    }
    
    # Enrich with service metadata
    if [service] {
      mutate {
        add_field => {
          "service_tier" => "%{[kubernetes][labels][service-tier]}"
          "deployment_version" => "%{[kubernetes][labels][version]}"
        }
      }
    }
    
    # Parse AI-specific metrics
    if [service] =~ /content-generation|market-intelligence|cover-designer|multimodal-generator/ {
      mutate {
        add_tag => ["ai-service"]
      }
      
      if [ai_model] {
        mutate {
          add_field => {
            "ai_provider" => "%{[ai_model]}"
          }
        }
      }
    }
    
    # Calculate cost per service
    if [cost_cents] {
      ruby {
        code => "
          cost = event.get('cost_cents').to_f / 100.0
          event.set('cost_dollars', cost)
        "
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch-master:9200"]
    index => "application-logs-%{+YYYY.MM.dd}"
    template_name => "application-logs"
  }
}
```

### Error Processing Pipeline

Specialized processing for error logs:

```ruby
filter {
  if [level] == "ERROR" or [level] == "FATAL" {
    mutate {
      add_tag => ["error"]
    }
    
    # Extract stack traces
    if [message] =~ /Traceback|Exception|Error/ {
      multiline {
        pattern => "^[[:space:]]"
        what => "previous"
        negate => false
      }
    }
    
    # Classify error types
    if [message] =~ /(?i)timeout/ {
      mutate { add_tag => ["timeout-error"] }
    } else if [message] =~ /(?i)connection/ {
      mutate { add_tag => ["connection-error"] }
    } else if [message] =~ /(?i)authentication|unauthorized/ {
      mutate { add_tag => ["auth-error"] }
    } else if [message] =~ /(?i)rate.*limit/ {
      mutate { add_tag => ["rate-limit-error"] }
    }
  }
}
```

### Security Event Pipeline

Processing security-related logs:

```ruby
filter {
  if "security" in [tags] {
    # Detect suspicious patterns
    if [message] =~ /(?i)failed.*login|authentication.*failed/ {
      mutate {
        add_tag => ["failed-auth"]
        add_field => {
          "security_event" => "authentication_failure"
          "risk_score" => 3
        }
      }
    }
    
    # Rate limiting events
    if [message] =~ /(?i)rate.*limit.*exceeded/ {
      mutate {
        add_tag => ["rate-limit-violation"]
        add_field => {
          "security_event" => "rate_limit_exceeded"
          "risk_score" => 2
        }
      }
    }
    
    # API abuse detection
    if [user_agent] =~ /(?i)bot|crawler|scanner/ and ![user_agent] =~ /(?i)googlebot|bingbot/ {
      mutate {
        add_tag => ["suspicious-user-agent"]
        add_field => {
          "security_event" => "suspicious_user_agent"
          "risk_score" => 1
        }
      }
    }
  }
}
```

## Dashboards and Visualizations

### Service Overview Dashboard

Real-time service health and performance:

- **Service Status**: Up/down status and health checks
- **Request Volume**: Requests per second by service
- **Error Rates**: Error percentage and trending
- **Response Times**: Latency percentiles and distribution
- **Resource Usage**: CPU and memory utilization

### AI Services Dashboard

Specialized dashboard for AI/ML services:

- **Generation Metrics**: Content generation success rates
- **Quality Scores**: AI output quality trending
- **Cost Analysis**: Token usage and cost per generation
- **Model Performance**: Response times by AI model
- **Error Analysis**: AI-specific error patterns

### Security Dashboard

Security event monitoring and analysis:

- **Authentication Events**: Login success/failure rates
- **Rate Limiting**: API rate limit violations
- **Suspicious Activity**: Unusual access patterns
- **Compliance**: Audit trail and access logs
- **Threat Detection**: Security event correlation

### Operations Dashboard

Infrastructure and operational insights:

- **Log Volume**: Logs per second by service
- **System Health**: Kubernetes cluster status
- **Storage Usage**: Disk usage and index sizes
- **Pipeline Performance**: Logstash processing rates
- **Alerting Status**: Active alerts and acknowledgments

## Alerting and Monitoring

### Critical Alerts

High-priority alerts for immediate attention:

```yaml
# High error rate alert
- alert: HighErrorRate
  condition: |
    count(query='level:ERROR AND @timestamp:[now-5m TO now]', index='application-logs-*') > 100
  actions:
    - email: <EMAIL>
    - slack: "#alerts"
    - pagerduty: critical

# Service down alert
- alert: ServiceDown
  condition: |
    count(query='message:"health check failed" AND @timestamp:[now-2m TO now]', index='application-logs-*') > 0
  actions:
    - email: <EMAIL>
    - slack: "#alerts"
    - pagerduty: critical

# AI service quality degradation
- alert: AIQualityDegradation
  condition: |
    avg(query='ai-service AND content_quality AND @timestamp:[now-15m TO now]', field='content_quality') < 3.5
  actions:
    - email: <EMAIL>
    - slack: "#ai-alerts"
```

### Warning Alerts

Medium-priority alerts for investigation:

```yaml
# High cost detection
- alert: HighAICosts
  condition: |
    sum(query='cost_dollars AND @timestamp:[now-1h TO now]', field='cost_dollars') > 100
  actions:
    - email: <EMAIL>
    - slack: "#cost-alerts"

# Rate limiting threshold
- alert: RateLimitApproaching
  condition: |
    count(query='rate-limit-error AND @timestamp:[now-10m TO now]', index='application-logs-*') > 50
  actions:
    - slack: "#ops-alerts"

# Long-running operations
- alert: LongRunningOperations
  condition: |
    count(query='duration:>300000 AND @timestamp:[now-5m TO now]', index='application-logs-*') > 10
  actions:
    - slack: "#performance-alerts"
```

## Performance Optimization

### Elasticsearch Tuning

Optimized cluster configuration:

```yaml
# Node roles and allocation
master_nodes: 3
data_nodes: 3
ingest_nodes: 2

# Memory and storage
heap_size: 4GB
disk_threshold:
  low: 85%
  high: 90%
  flood_stage: 95%

# Indexing performance
refresh_interval: 5s
number_of_shards: 3
number_of_replicas: 1
translog_durability: async
```

### Logstash Optimization

Pipeline performance tuning:

```yaml
# Pipeline workers
pipeline.workers: 4
pipeline.batch.size: 125
pipeline.batch.delay: 50

# Queue configuration
queue.type: persisted
queue.max_events: 10000
queue.max_bytes: 1GB

# JVM settings
heap_size: 2GB
gc_algorithm: G1GC
```

### Index Optimization

Efficient index management:

```yaml
# Index settings
index.number_of_routing_shards: 6
index.sort.field: "@timestamp"
index.sort.order: desc

# Mapping optimization
dynamic: strict
numeric_detection: false
date_detection: false

# Compression
index.codec: best_compression
```

## Security Configuration

### Authentication and Authorization

Multi-layered security:

```yaml
# X-Pack security
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.http.ssl.enabled: true

# Role-based access
roles:
  logs_reader:
    indices:
      - names: ["application-logs-*", "system-logs-*"]
        privileges: ["read"]
  
  logs_admin:
    indices:
      - names: ["*"]
        privileges: ["all"]
    cluster: ["all"]
  
  developers:
    indices:
      - names: ["application-logs-*"]
        privileges: ["read"]
        query: '{"term": {"service": "content-generation"}}'
```

### Network Security

Secure communication:

```yaml
# TLS configuration
ssl:
  certificate: /etc/ssl/certs/elasticsearch.crt
  private_key: /etc/ssl/private/elasticsearch.key
  certificate_authorities: ["/etc/ssl/certs/ca.crt"]

# IP filtering
network.host: ["_local_", "_site_"]
discovery.seed_hosts: ["master-1", "master-2", "master-3"]

# Firewall rules
allowed_ports: [9200, 9300, 5601, 5044]
```

## Deployment Architecture

### High Availability Setup

Production-ready cluster deployment:

```yaml
# Elasticsearch cluster
elasticsearch:
  master_nodes: 3
  data_nodes: 3
  client_nodes: 2
  minimum_master_nodes: 2

# Logstash cluster
logstash:
  instances: 3
  load_balancer: true
  pipeline_coordination: true

# Kibana cluster
kibana:
  instances: 2
  load_balancer: true
  session_persistence: true
```

### Resource Requirements

Recommended resource allocation:

```yaml
# Elasticsearch nodes
elasticsearch_master:
  cpu: 2 cores
  memory: 8GB
  storage: 100GB SSD

elasticsearch_data:
  cpu: 4 cores
  memory: 16GB
  storage: 1TB SSD

# Logstash nodes
logstash:
  cpu: 2 cores
  memory: 4GB
  storage: 50GB

# Kibana nodes
kibana:
  cpu: 1 core
  memory: 2GB
  storage: 10GB
```

## Troubleshooting

### Common Issues

#### High Memory Usage

```bash
# Check heap usage
curl -X GET "elasticsearch:9200/_cat/nodes?v&h=name,heap.percent,heap.current,heap.max"

# Clear field data cache
curl -X POST "elasticsearch:9200/_cache/clear?fielddata=true"

# Optimize indices
curl -X POST "elasticsearch:9200/_optimize?max_num_segments=1"
```

#### Slow Queries

```bash
# Enable slow query logging
curl -X PUT "elasticsearch:9200/_all/_settings" -H 'Content-Type: application/json' -d'
{
  "index.search.slowlog.threshold.query.warn": "10s",
  "index.search.slowlog.threshold.query.info": "5s"
}'

# Check slow queries
tail -f /var/log/elasticsearch/elasticsearch_index_search_slowlog.log
```

#### Index Management

```bash
# Check index health
curl -X GET "elasticsearch:9200/_cat/indices?v&health=yellow&health=red"

# Force merge indices
curl -X POST "elasticsearch:9200/application-logs-*/_forcemerge?max_num_segments=1"

# Delete old indices
curl -X DELETE "elasticsearch:9200/application-logs-2024.01.*"
```

### Debug Commands

```bash
# Elasticsearch cluster health
curl -X GET "elasticsearch:9200/_cluster/health?pretty"

# Logstash pipeline stats
curl -X GET "logstash:9600/_node/stats/pipelines?pretty"

# Kibana status
curl -X GET "kibana:5601/api/status"

# Filebeat status
./filebeat test output
./filebeat test config
```

## Maintenance Procedures

### Regular Maintenance

```bash
# Daily tasks
- Check cluster health
- Monitor disk usage
- Review error logs
- Validate data retention

# Weekly tasks
- Optimize indices
- Update index templates
- Review and update alerting rules
- Backup configuration

# Monthly tasks
- Review and update mappings
- Analyze storage growth trends
- Performance tuning review
- Security audit
```

---

For implementation details and deployment scripts, see:
- `./elasticsearch/` - Elasticsearch cluster configuration
- `./logstash/` - Log processing pipelines
- `./kibana/` - Dashboards and visualizations
- `./filebeat/` - Log collection configuration
- `./scripts/` - Deployment and maintenance scripts