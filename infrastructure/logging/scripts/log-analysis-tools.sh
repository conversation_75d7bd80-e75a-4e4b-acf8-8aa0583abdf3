#!/bin/bash

# Log Analysis Tools for Publish AI ELK Stack
# Provides utilities for log analysis, troubleshooting, and maintenance

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="logging"
ELASTICSEARCH_URL="https://elasticsearch.logging.svc.cluster.local:9200"
KIBANA_URL="http://kibana.logging.svc.cluster.local:5601"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 <command> [options]

Log analysis and maintenance tools for the Publish AI ELK stack.

Commands:
  health              Check overall ELK stack health
  indices             List and manage Elasticsearch indices
  logs <service>      Show recent logs for a specific service
  errors              Find and analyze error logs
  performance         Analyze performance metrics from logs
  ai-metrics          Show AI service specific metrics
  security            Show security events and analysis
  cleanup             Clean up old indices and data
  backup              Backup Elasticsearch indices
  restore             Restore Elasticsearch indices
  reindex             Reindex data with new mappings
  optimize            Optimize Elasticsearch performance

Service Log Commands:
  logs api-gateway              # API Gateway logs
  logs content-generation       # Content Generation service logs
  logs market-intelligence      # Market Intelligence service logs
  logs publishing-service       # Publishing service logs
  logs cover-designer           # Cover Designer service logs
  logs sales-monitor            # Sales Monitor service logs

Analysis Commands:
  errors --last 1h              # Errors in last hour
  errors --service api-gateway  # Errors for specific service
  performance --slow-queries    # Find slow operations
  ai-metrics --costs            # AI cost analysis
  security --failed-auth        # Failed authentication attempts

Examples:
  $0 health                     # Check stack health
  $0 logs api-gateway           # Show API Gateway logs
  $0 errors --last 2h          # Show errors from last 2 hours
  $0 ai-metrics --quality       # Show AI quality metrics
  $0 cleanup --older-than 30d   # Clean up logs older than 30 days

EOF
}

# Execute Elasticsearch query
es_query() {
    local query="$1"
    local index="${2:-_all}"
    
    kubectl exec -n "$NAMESPACE" elasticsearch-master-0 -- \
        curl -k -u elastic:elastic -s \
        -H "Content-Type: application/json" \
        "$ELASTICSEARCH_URL/$index/_search" \
        -d "$query" | jq '.'
}

# Execute Elasticsearch API call
es_api() {
    local endpoint="$1"
    local method="${2:-GET}"
    local data="${3:-}"
    
    local curl_cmd="curl -k -u elastic:elastic -s -X $method"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    kubectl exec -n "$NAMESPACE" elasticsearch-master-0 -- \
        bash -c "$curl_cmd '$ELASTICSEARCH_URL/$endpoint'" | jq '.' 2>/dev/null || \
        kubectl exec -n "$NAMESPACE" elasticsearch-master-0 -- \
        bash -c "$curl_cmd '$ELASTICSEARCH_URL/$endpoint'"
}

# Check ELK stack health
check_health() {
    log "Checking ELK stack health..."
    
    # Check Elasticsearch cluster health
    info "Elasticsearch cluster health:"
    es_api "_cluster/health?pretty"
    
    # Check individual component status
    info ""
    info "Component status:"
    
    # Elasticsearch
    local es_pods=$(kubectl get pods -n "$NAMESPACE" -l app=elasticsearch --no-headers | wc -l)
    local es_ready=$(kubectl get pods -n "$NAMESPACE" -l app=elasticsearch -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    info "Elasticsearch: $es_ready/$es_pods pods ready"
    
    # Logstash
    local ls_pods=$(kubectl get pods -n "$NAMESPACE" -l app=logstash --no-headers | wc -l)
    local ls_ready=$(kubectl get pods -n "$NAMESPACE" -l app=logstash -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    info "Logstash: $ls_ready/$ls_pods pods ready"
    
    # Kibana
    local kb_pods=$(kubectl get pods -n "$NAMESPACE" -l app=kibana --no-headers | wc -l)
    local kb_ready=$(kubectl get pods -n "$NAMESPACE" -l app=kibana -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    info "Kibana: $kb_ready/$kb_pods pods ready"
    
    # Filebeat
    local fb_pods=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat --no-headers | wc -l)
    local fb_ready=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    info "Filebeat: $fb_ready/$fb_pods pods ready"
    
    # Check index status
    info ""
    info "Index statistics:"
    es_api "_cat/indices?v&s=store.size:desc&h=index,docs.count,store.size,health"
    
    # Check recent indexing rate
    info ""
    info "Recent indexing rate:"
    es_query '{
        "aggs": {
            "logs_per_minute": {
                "date_histogram": {
                    "field": "@timestamp",
                    "fixed_interval": "1m"
                }
            }
        },
        "size": 0,
        "query": {
            "range": {
                "@timestamp": {
                    "gte": "now-10m"
                }
            }
        }
    }' | jq '.aggregations.logs_per_minute.buckets[-5:] | .[] | {time: .key_as_string, count: .doc_count}'
}

# List and manage indices
manage_indices() {
    local action="${1:-list}"
    
    case "$action" in
        list)
            info "Elasticsearch indices:"
            es_api "_cat/indices?v&s=index"
            ;;
        health)
            info "Index health status:"
            es_api "_cat/indices?v&h=index,health,status,docs.count,store.size&s=health,index"
            ;;
        shards)
            info "Shard allocation:"
            es_api "_cat/shards?v&s=index"
            ;;
        settings)
            local index="${2:-}"
            if [ -z "$index" ]; then
                error "Please specify an index name"
                return 1
            fi
            info "Settings for index $index:"
            es_api "$index/_settings?pretty"
            ;;
        *)
            error "Unknown action: $action"
            info "Available actions: list, health, shards, settings"
            ;;
    esac
}

# Show logs for specific service
show_service_logs() {
    local service="$1"
    local time_range="${2:-now-1h}"
    local size="${3:-50}"
    
    log "Showing logs for service: $service (last ${time_range#now-})"
    
    local query='{
        "query": {
            "bool": {
                "must": [
                    {"term": {"service": "'$service'"}},
                    {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                ]
            }
        },
        "sort": [{"@timestamp": {"order": "desc"}}],
        "size": '$size'
    }'
    
    es_query "$query" "application-logs-*" | jq -r '.hits.hits[] | {
        timestamp: ._source["@timestamp"],
        level: ._source.level,
        message: ._source.message,
        request_id: ._source.request_id
    } | @json' | while read -r line; do
        local timestamp=$(echo "$line" | jq -r '.timestamp')
        local level=$(echo "$line" | jq -r '.level')
        local message=$(echo "$line" | jq -r '.message')
        local request_id=$(echo "$line" | jq -r '.request_id // "N/A"')
        
        case "$level" in
            ERROR|FATAL) echo -e "${RED}[$timestamp] $level: $message (req: $request_id)${NC}" ;;
            WARN) echo -e "${YELLOW}[$timestamp] $level: $message (req: $request_id)${NC}" ;;
            *) echo -e "${NC}[$timestamp] $level: $message (req: $request_id)" ;;
        esac
    done
}

# Analyze errors
analyze_errors() {
    local time_range="${1:-now-1h}"
    local service="${2:-}"
    
    log "Analyzing errors (last ${time_range#now-})"
    
    local service_filter=""
    if [ -n "$service" ]; then
        service_filter='{"term": {"service": "'$service'"}},'
    fi
    
    local query='{
        "query": {
            "bool": {
                "must": [
                    '$service_filter'
                    {"terms": {"level": ["ERROR", "FATAL"]}},
                    {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                ]
            }
        },
        "aggs": {
            "error_by_service": {
                "terms": {"field": "service", "size": 10}
            },
            "error_by_type": {
                "terms": {"field": "error_type", "size": 10}
            },
            "error_timeline": {
                "date_histogram": {
                    "field": "@timestamp",
                    "fixed_interval": "5m"
                }
            }
        },
        "size": 0
    }'
    
    info "Error summary:"
    es_query "$query" "application-logs-*" | jq -r '
        "Total errors: " + (.hits.total.value | tostring) + "\n" +
        "\nErrors by service:" +
        (.aggregations.error_by_service.buckets[] | 
            "\n  " + .key + ": " + (.doc_count | tostring)) +
        "\n\nErrors by type:" +
        (.aggregations.error_by_type.buckets[] | 
            "\n  " + .key + ": " + (.doc_count | tostring))
    '
    
    # Show recent error details
    info ""
    info "Recent error details:"
    local error_details='{
        "query": {
            "bool": {
                "must": [
                    '$service_filter'
                    {"terms": {"level": ["ERROR", "FATAL"]}},
                    {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                ]
            }
        },
        "sort": [{"@timestamp": {"order": "desc"}}],
        "size": 5
    }'
    
    es_query "$error_details" "application-logs-*" | jq -r '.hits.hits[] | {
        timestamp: ._source["@timestamp"],
        service: ._source.service,
        level: ._source.level,
        message: ._source.message,
        error_type: ._source.error_type
    } | @json' | while read -r line; do
        local timestamp=$(echo "$line" | jq -r '.timestamp')
        local service=$(echo "$line" | jq -r '.service')
        local level=$(echo "$line" | jq -r '.level')
        local message=$(echo "$line" | jq -r '.message')
        local error_type=$(echo "$line" | jq -r '.error_type // "unknown"')
        
        echo -e "${RED}[$timestamp] $service/$level ($error_type): $message${NC}"
    done
}

# Analyze performance metrics
analyze_performance() {
    local metric_type="${1:-overview}"
    local time_range="${2:-now-1h}"
    
    log "Analyzing performance metrics: $metric_type (last ${time_range#now-})"
    
    case "$metric_type" in
        overview)
            local query='{
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "duration"}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "avg_duration": {"avg": {"field": "duration"}},
                    "max_duration": {"max": {"field": "duration"}},
                    "duration_percentiles": {
                        "percentiles": {"field": "duration", "percents": [50, 90, 95, 99]}
                    },
                    "duration_by_service": {
                        "terms": {"field": "service", "size": 10},
                        "aggs": {"avg_duration": {"avg": {"field": "duration"}}}
                    }
                },
                "size": 0
            }'
            
            info "Performance overview:"
            es_query "$query" "application-logs-*" | jq -r '
                "Average duration: " + (.aggregations.avg_duration.value | round | tostring) + "ms\n" +
                "Max duration: " + (.aggregations.max_duration.value | round | tostring) + "ms\n" +
                "\nDuration percentiles:" +
                (.aggregations.duration_percentiles.values | to_entries[] | 
                    "\n  P" + .key + ": " + (.value | round | tostring) + "ms") +
                "\n\nAverage duration by service:" +
                (.aggregations.duration_by_service.buckets[] | 
                    "\n  " + .key + ": " + (.avg_duration.value | round | tostring) + "ms")
            '
            ;;
        slow-queries)
            info "Slow operations (>5 seconds):"
            local slow_query='{
                "query": {
                    "bool": {
                        "must": [
                            {"range": {"duration": {"gte": 5000}}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "sort": [{"duration": {"order": "desc"}}],
                "size": 10
            }'
            
            es_query "$slow_query" "application-logs-*" | jq -r '.hits.hits[] | {
                timestamp: ._source["@timestamp"],
                service: ._source.service,
                duration: ._source.duration,
                message: ._source.message
            } | @json' | while read -r line; do
                local timestamp=$(echo "$line" | jq -r '.timestamp')
                local service=$(echo "$line" | jq -r '.service')
                local duration=$(echo "$line" | jq -r '.duration')
                local message=$(echo "$line" | jq -r '.message')
                
                echo -e "${YELLOW}[$timestamp] $service: ${duration}ms - $message${NC}"
            done
            ;;
        *)
            error "Unknown metric type: $metric_type"
            info "Available types: overview, slow-queries"
            ;;
    esac
}

# Analyze AI service metrics
analyze_ai_metrics() {
    local metric_type="${1:-overview}"
    local time_range="${2:-now-1h}"
    
    log "Analyzing AI service metrics: $metric_type (last ${time_range#now-})"
    
    case "$metric_type" in
        overview)
            local query='{
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "ai_model"}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "total_cost": {"sum": {"field": "cost_dollars"}},
                    "avg_quality": {"avg": {"field": "content_quality"}},
                    "total_tokens": {"sum": {"field": "tokens_used"}},
                    "generations_by_model": {
                        "terms": {"field": "ai_model", "size": 10}
                    },
                    "cost_by_service": {
                        "terms": {"field": "service", "size": 10},
                        "aggs": {"total_cost": {"sum": {"field": "cost_dollars"}}}
                    }
                },
                "size": 0
            }'
            
            info "AI metrics overview:"
            es_query "$query" "ai-services-logs-*" | jq -r '
                "Total cost: $" + (.aggregations.total_cost.value | round * 100 / 100 | tostring) + "\n" +
                "Average quality: " + (.aggregations.avg_quality.value | round * 100 / 100 | tostring) + "/5\n" +
                "Total tokens: " + (.aggregations.total_tokens.value | tostring) + "\n" +
                "\nGenerations by model:" +
                (.aggregations.generations_by_model.buckets[] | 
                    "\n  " + .key + ": " + (.doc_count | tostring)) +
                "\n\nCost by service:" +
                (.aggregations.cost_by_service.buckets[] | 
                    "\n  " + .key + ": $" + (.total_cost.value | round * 100 / 100 | tostring))
            '
            ;;
        costs)
            info "AI cost analysis:"
            local cost_query='{
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "cost_dollars"}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "cost_timeline": {
                        "date_histogram": {
                            "field": "@timestamp",
                            "fixed_interval": "1h"
                        },
                        "aggs": {"hourly_cost": {"sum": {"field": "cost_dollars"}}}
                    },
                    "expensive_operations": {
                        "terms": {
                            "script": {
                                "source": "doc['\''service'\''].value + '\'' - '\'' + doc['\''ai_model'\''].value"
                            },
                            "size": 5
                        },
                        "aggs": {"avg_cost": {"avg": {"field": "cost_dollars"}}}
                    }
                },
                "size": 0
            }'
            
            es_query "$cost_query" "ai-services-logs-*" | jq -r '
                "\nCost timeline (last 5 hours):" +
                (.aggregations.cost_timeline.buckets[-5:] | 
                    .[] | "\n  " + .key_as_string + ": $" + (.hourly_cost.value | round * 100 / 100 | tostring)) +
                "\n\nMost expensive operations:" +
                (.aggregations.expensive_operations.buckets[] | 
                    "\n  " + .key + ": $" + (.avg_cost.value | round * 100 / 100 | tostring) + " avg")
            '
            ;;
        quality)
            info "Content quality analysis:"
            local quality_query='{
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "content_quality"}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "quality_distribution": {
                        "histogram": {
                            "field": "content_quality",
                            "interval": 0.5,
                            "min_doc_count": 1
                        }
                    },
                    "quality_by_service": {
                        "terms": {"field": "service", "size": 10},
                        "aggs": {"avg_quality": {"avg": {"field": "content_quality"}}}
                    }
                },
                "size": 0
            }'
            
            es_query "$quality_query" "ai-services-logs-*" | jq -r '
                "Quality distribution:" +
                (.aggregations.quality_distribution.buckets[] | 
                    "\n  " + (.key | tostring) + "-" + ((.key + 0.5) | tostring) + ": " + (.doc_count | tostring) + " items") +
                "\n\nAverage quality by service:" +
                (.aggregations.quality_by_service.buckets[] | 
                    "\n  " + .key + ": " + (.avg_quality.value | round * 100 / 100 | tostring) + "/5")
            '
            ;;
        *)
            error "Unknown AI metric type: $metric_type"
            info "Available types: overview, costs, quality"
            ;;
    esac
}

# Analyze security events
analyze_security() {
    local event_type="${1:-overview}"
    local time_range="${2:-now-24h}"
    
    log "Analyzing security events: $event_type (last ${time_range#now-})"
    
    case "$event_type" in
        overview)
            local query='{
                "query": {
                    "bool": {
                        "must": [
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "events_by_type": {
                        "terms": {"field": "security_event", "size": 10}
                    },
                    "high_risk_events": {
                        "filter": {"range": {"risk_score": {"gte": 5}}},
                        "aggs": {
                            "by_source_ip": {
                                "terms": {"field": "source_ip", "size": 10}
                            }
                        }
                    },
                    "events_timeline": {
                        "date_histogram": {
                            "field": "@timestamp",
                            "fixed_interval": "1h"
                        }
                    }
                },
                "size": 0
            }'
            
            info "Security overview:"
            es_query "$query" "security-logs-*" | jq -r '
                "Total security events: " + (.hits.total.value | tostring) + "\n" +
                "\nEvents by type:" +
                (.aggregations.events_by_type.buckets[] | 
                    "\n  " + .key + ": " + (.doc_count | tostring)) +
                "\n\nHigh risk events by source IP:" +
                (.aggregations.high_risk_events.by_source_ip.buckets[] | 
                    "\n  " + .key + ": " + (.doc_count | tostring))
            '
            ;;
        failed-auth)
            info "Failed authentication analysis:"
            local auth_query='{
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"security_event": "authentication_failure"}},
                            {"range": {"@timestamp": {"gte": "'$time_range'"}}}
                        ]
                    }
                },
                "aggs": {
                    "failed_by_ip": {
                        "terms": {"field": "source_ip", "size": 10}
                    },
                    "failed_by_user": {
                        "terms": {"field": "user_id", "size": 10}
                    }
                },
                "sort": [{"@timestamp": {"order": "desc"}}],
                "size": 10
            }'
            
            info "Failed authentication attempts:"
            es_query "$auth_query" "security-logs-*" | jq -r '
                "Total failed attempts: " + (.hits.total.value | tostring) + "\n" +
                "\nFailed attempts by IP:" +
                (.aggregations.failed_by_ip.buckets[] | 
                    "\n  " + .key + ": " + (.doc_count | tostring)) +
                "\n\nFailed attempts by user:" +
                (.aggregations.failed_by_user.buckets[] | 
                    "\n  " + .key + ": " + (.doc_count | tostring)) +
                "\n\nRecent failures:"
            '
            
            es_query "$auth_query" "security-logs-*" | jq -r '.hits.hits[] | {
                timestamp: ._source["@timestamp"],
                source_ip: ._source.source_ip,
                user_id: ._source.user_id,
                user_agent: ._source.user_agent
            } | @json' | while read -r line; do
                local timestamp=$(echo "$line" | jq -r '.timestamp')
                local source_ip=$(echo "$line" | jq -r '.source_ip')
                local user_id=$(echo "$line" | jq -r '.user_id // "N/A"')
                local user_agent=$(echo "$line" | jq -r '.user_agent // "N/A"')
                
                echo -e "${RED}  [$timestamp] $source_ip -> $user_id ($user_agent)${NC}"
            done
            ;;
        *)
            error "Unknown security event type: $event_type"
            info "Available types: overview, failed-auth"
            ;;
    esac
}

# Cleanup old data
cleanup_data() {
    local older_than="${1:-30d}"
    local dry_run="${2:-false}"
    
    log "Cleaning up data older than $older_than"
    
    if [ "$dry_run" = "true" ]; then
        info "DRY RUN: Showing indices that would be deleted"
    fi
    
    # Calculate cutoff date
    local cutoff_date
    if command -v gdate &> /dev/null; then
        cutoff_date=$(gdate -d "$older_than ago" +%Y.%m.%d)
    else
        cutoff_date=$(date -d "$older_than ago" +%Y.%m.%d 2>/dev/null || date -v -"${older_than}" +%Y.%m.%d)
    fi
    
    info "Cutoff date: $cutoff_date"
    
    # Find old indices
    local indices_to_delete=$(es_api "_cat/indices?h=index" | grep -E "(application-logs|system-logs|security-logs|ai-services-logs)-[0-9]{4}\.[0-9]{2}\.[0-9]{2}" | while read -r index; do
        local index_date=$(echo "$index" | grep -o '[0-9]{4}\.[0-9]{2}\.[0-9]{2}$')
        if [[ "$index_date" < "$cutoff_date" ]]; then
            echo "$index"
        fi
    done)
    
    if [ -z "$indices_to_delete" ]; then
        info "No indices found older than $older_than"
        return 0
    fi
    
    info "Indices to delete:"
    echo "$indices_to_delete" | while read -r index; do
        local size=$(es_api "_cat/indices/$index?h=store.size" | tr -d ' ')
        info "  $index ($size)"
    done
    
    if [ "$dry_run" = "false" ]; then
        read -p "Are you sure you want to delete these indices? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "$indices_to_delete" | while read -r index; do
                info "Deleting index: $index"
                es_api "$index" "DELETE"
            done
            log "Cleanup completed"
        else
            info "Cleanup cancelled"
        fi
    fi
}

# Main function
main() {
    local command="${1:-help}"
    shift || true
    
    case "$command" in
        health)
            check_health
            ;;
        indices)
            manage_indices "$@"
            ;;
        logs)
            if [ $# -lt 1 ]; then
                error "Please specify a service name"
                usage
                exit 1
            fi
            show_service_logs "$@"
            ;;
        errors)
            local time_range="now-1h"
            local service=""
            
            while [[ $# -gt 0 ]]; do
                case $1 in
                    --last)
                        time_range="now-$2"
                        shift 2
                        ;;
                    --service)
                        service="$2"
                        shift 2
                        ;;
                    *)
                        error "Unknown option: $1"
                        exit 1
                        ;;
                esac
            done
            
            analyze_errors "$time_range" "$service"
            ;;
        performance)
            analyze_performance "$@"
            ;;
        ai-metrics)
            analyze_ai_metrics "$@"
            ;;
        security)
            analyze_security "$@"
            ;;
        cleanup)
            local older_than="30d"
            local dry_run="false"
            
            while [[ $# -gt 0 ]]; do
                case $1 in
                    --older-than)
                        older_than="$2"
                        shift 2
                        ;;
                    --dry-run)
                        dry_run="true"
                        shift
                        ;;
                    *)
                        error "Unknown option: $1"
                        exit 1
                        ;;
                esac
            done
            
            cleanup_data "$older_than" "$dry_run"
            ;;
        help|*)
            usage
            ;;
    esac
}

# Check if running in Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    error "Not connected to Kubernetes cluster"
    exit 1
fi

# Check if ELK stack is deployed
if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
    error "Logging namespace '$NAMESPACE' not found. Is the ELK stack deployed?"
    exit 1
fi

# Run main function
main "$@"