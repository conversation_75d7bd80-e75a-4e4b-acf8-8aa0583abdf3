#!/bin/bash

# ELK Stack Deployment Script for Publish AI
# Deploys Elasticsearch, Logstash, Kibana, and Filebeat for centralized logging

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOGGING_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
NAMESPACE="logging"
ELASTICSEARCH_VERSION="8.11.1"
TIMEOUT="600s"

# Default values
DRY_RUN=false
SKIP_SETUP=false
MINIMAL_INSTALL=false
PRODUCTION_MODE=true

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy the complete ELK stack for centralized logging in Publish AI.

Options:
  -d, --dry-run           Show what would be deployed without making changes
  -s, --skip-setup        Skip initial setup jobs (use for upgrades)
  -m, --minimal           Deploy minimal configuration for development
  -p, --production        Deploy production configuration (default)
  -t, --timeout DURATION  Deployment timeout (default: 600s)
  -h, --help              Show this help message

Components Deployed:
  - Elasticsearch cluster (3 master + 3 data nodes)
  - Logstash processing pipelines (3 instances)
  - Kibana visualization platform (2 instances)
  - Filebeat log collection (DaemonSet)
  - Index templates and lifecycle policies
  - Pre-built dashboards for application monitoring

Examples:
  $0                      # Deploy production ELK stack
  $0 --minimal            # Deploy minimal stack for development
  $0 --dry-run            # Preview deployment without applying
  $0 --skip-setup         # Deploy without running setup jobs

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -s|--skip-setup)
                SKIP_SETUP=true
                shift
                ;;
            -m|--minimal)
                MINIMAL_INSTALL=true
                PRODUCTION_MODE=false
                shift
                ;;
            -p|--production)
                PRODUCTION_MODE=true
                MINIMAL_INSTALL=false
                shift
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check cluster admin privileges
    if ! kubectl auth can-i create clusterrole &> /dev/null; then
        error "Insufficient permissions. Please run with cluster admin privileges"
        exit 1
    fi
    
    # Check cluster resources
    local nodes=$(kubectl get nodes --no-headers | wc -l)
    if [ "$nodes" -lt 3 ] && [ "$PRODUCTION_MODE" = true ]; then
        warn "Cluster has less than 3 nodes. Production deployment may not be optimal."
    fi
    
    # Check storage classes
    if ! kubectl get storageclass fast-ssd &> /dev/null; then
        warn "StorageClass 'fast-ssd' not found. Using default storage class."
    fi
    
    # Check available resources
    local total_cpu=$(kubectl top nodes 2>/dev/null | awk 'NR>1 {sum+=$3} END {print sum}' | sed 's/m//' || echo "0")
    local total_memory=$(kubectl top nodes 2>/dev/null | awk 'NR>1 {sum+=$5} END {print sum}' | sed 's/Mi//' || echo "0")
    
    if [ "$total_cpu" -lt 8000 ] && [ "$PRODUCTION_MODE" = true ]; then
        warn "Cluster has less than 8 CPU cores available. Consider using --minimal mode."
    fi
    
    if [ "$total_memory" -lt 16384 ] && [ "$PRODUCTION_MODE" = true ]; then
        warn "Cluster has less than 16GB memory available. Consider using --minimal mode."
    fi
    
    log "Prerequisites check passed"
}

# Create namespace and basic resources
create_namespace() {
    log "Creating logging namespace and basic resources..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace $NAMESPACE"
        return 0
    fi
    
    # Create namespace
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespace for Istio injection
    kubectl label namespace "$NAMESPACE" istio-injection=enabled --overwrite
    
    # Create basic RBAC
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: elk-operator
  namespace: $NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: elk-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "statefulsets", "daemonsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["monitoring.coreos.com"]
  resources: ["servicemonitors"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: elk-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: elk-operator
subjects:
- kind: ServiceAccount
  name: elk-operator
  namespace: $NAMESPACE
EOF
    
    log "Namespace and basic resources created"
}

# Deploy Elasticsearch cluster
deploy_elasticsearch() {
    log "Deploying Elasticsearch cluster..."
    
    local config_file="$LOGGING_CONFIG_DIR/elasticsearch/elasticsearch-cluster.yaml"
    
    if [ "$MINIMAL_INSTALL" = true ]; then
        # Create minimal Elasticsearch configuration
        local temp_config="/tmp/elasticsearch-minimal.yaml"
        sed -e 's/replicas: 3/replicas: 1/g' \
            -e 's/memory: 8Gi/memory: 2Gi/g' \
            -e 's/storage: 1Ti/storage: 50Gi/g' \
            -e 's/cpu: 2000m/cpu: 500m/g' \
            "$config_file" > "$temp_config"
        config_file="$temp_config"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Elasticsearch cluster"
        if [ "$MINIMAL_INSTALL" = true ]; then
            info "Configuration: Minimal (1 replica, 2GB memory, 50GB storage)"
        else
            info "Configuration: Production (3 masters + 3 data nodes)"
        fi
        return 0
    fi
    
    # Apply Elasticsearch configuration
    kubectl apply -f "$config_file"
    
    # Wait for Elasticsearch master nodes to be ready
    log "Waiting for Elasticsearch master nodes to be ready..."
    kubectl wait --for=condition=ready pod -l app=elasticsearch,role=master -n "$NAMESPACE" --timeout="$TIMEOUT"
    
    # Wait for Elasticsearch data nodes to be ready (if production mode)
    if [ "$PRODUCTION_MODE" = true ]; then
        log "Waiting for Elasticsearch data nodes to be ready..."
        kubectl wait --for=condition=ready pod -l app=elasticsearch,role=data -n "$NAMESPACE" --timeout="$TIMEOUT"
    fi
    
    # Verify Elasticsearch cluster health
    log "Verifying Elasticsearch cluster health..."
    local max_retries=30
    local retry=0
    
    while [ $retry -lt $max_retries ]; do
        if kubectl exec -n "$NAMESPACE" elasticsearch-master-0 -- curl -k -u elastic:elastic https://localhost:9200/_cluster/health?wait_for_status=yellow &> /dev/null; then
            log "Elasticsearch cluster is healthy"
            break
        fi
        
        retry=$((retry + 1))
        info "Waiting for Elasticsearch cluster health... ($retry/$max_retries)"
        sleep 10
    done
    
    if [ $retry -eq $max_retries ]; then
        error "Elasticsearch cluster failed to become healthy"
        exit 1
    fi
    
    # Clean up temporary file
    if [ "$MINIMAL_INSTALL" = true ] && [ -f "/tmp/elasticsearch-minimal.yaml" ]; then
        rm -f "/tmp/elasticsearch-minimal.yaml"
    fi
    
    log "Elasticsearch cluster deployed successfully"
}

# Deploy Logstash
deploy_logstash() {
    log "Deploying Logstash processing pipelines..."
    
    local config_file="$LOGGING_CONFIG_DIR/logstash/logstash-deployment.yaml"
    
    if [ "$MINIMAL_INSTALL" = true ]; then
        # Create minimal Logstash configuration
        local temp_config="/tmp/logstash-minimal.yaml"
        sed -e 's/replicas: 3/replicas: 1/g' \
            -e 's/memory: 4Gi/memory: 1Gi/g' \
            -e 's/cpu: 2000m/cpu: 500m/g' \
            "$config_file" > "$temp_config"
        config_file="$temp_config"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Logstash"
        return 0
    fi
    
    # Apply Logstash configuration
    kubectl apply -f "$config_file"
    
    # Wait for Logstash to be ready
    log "Waiting for Logstash to be ready..."
    kubectl wait --for=condition=available deployment/logstash -n "$NAMESPACE" --timeout="$TIMEOUT"
    
    # Verify Logstash is processing
    log "Verifying Logstash status..."
    sleep 30  # Allow time for startup
    
    local logstash_pod=$(kubectl get pods -n "$NAMESPACE" -l app=logstash -o jsonpath='{.items[0].metadata.name}')
    if kubectl exec -n "$NAMESPACE" "$logstash_pod" -- curl -f http://localhost:9600/_node/stats &> /dev/null; then
        log "Logstash is running and responsive"
    else
        warn "Logstash may not be fully ready yet"
    fi
    
    # Clean up temporary file
    if [ "$MINIMAL_INSTALL" = true ] && [ -f "/tmp/logstash-minimal.yaml" ]; then
        rm -f "/tmp/logstash-minimal.yaml"
    fi
    
    log "Logstash deployed successfully"
}

# Deploy Kibana
deploy_kibana() {
    log "Deploying Kibana visualization platform..."
    
    local config_file="$LOGGING_CONFIG_DIR/kibana/kibana-deployment.yaml"
    
    if [ "$MINIMAL_INSTALL" = true ]; then
        # Create minimal Kibana configuration
        local temp_config="/tmp/kibana-minimal.yaml"
        sed -e 's/replicas: 2/replicas: 1/g' \
            -e 's/memory: 4Gi/memory: 1Gi/g' \
            -e 's/cpu: 2000m/cpu: 500m/g' \
            "$config_file" > "$temp_config"
        config_file="$temp_config"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Kibana"
        return 0
    fi
    
    # Apply Kibana configuration
    kubectl apply -f "$config_file"
    
    # Wait for Kibana to be ready
    log "Waiting for Kibana to be ready..."
    kubectl wait --for=condition=available deployment/kibana -n "$NAMESPACE" --timeout="$TIMEOUT"
    
    # Verify Kibana is accessible
    log "Verifying Kibana status..."
    sleep 60  # Allow time for Kibana to initialize
    
    local kibana_pod=$(kubectl get pods -n "$NAMESPACE" -l app=kibana -o jsonpath='{.items[0].metadata.name}')
    if kubectl exec -n "$NAMESPACE" "$kibana_pod" -- curl -f http://localhost:5601/api/status &> /dev/null; then
        log "Kibana is running and responsive"
    else
        warn "Kibana may not be fully ready yet"
    fi
    
    # Clean up temporary file
    if [ "$MINIMAL_INSTALL" = true ] && [ -f "/tmp/kibana-minimal.yaml" ]; then
        rm -f "/tmp/kibana-minimal.yaml"
    fi
    
    log "Kibana deployed successfully"
}

# Deploy Filebeat
deploy_filebeat() {
    log "Deploying Filebeat log collection..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Filebeat DaemonSet"
        return 0
    fi
    
    # Apply Filebeat configuration
    kubectl apply -f "$LOGGING_CONFIG_DIR/filebeat/filebeat-daemonset.yaml"
    
    # Wait for Filebeat DaemonSet to be ready
    log "Waiting for Filebeat DaemonSet to be ready..."
    kubectl rollout status daemonset/filebeat -n "$NAMESPACE" --timeout="$TIMEOUT"
    
    # Verify Filebeat is collecting logs
    log "Verifying Filebeat status..."
    sleep 30
    
    local filebeat_pods=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat --no-headers | wc -l)
    local ready_pods=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    
    info "Filebeat pods: $ready_pods/$filebeat_pods ready"
    
    if [ "$ready_pods" -gt 0 ]; then
        log "Filebeat is collecting logs from cluster nodes"
    else
        warn "No Filebeat pods are ready yet"
    fi
    
    log "Filebeat deployed successfully"
}

# Run setup jobs
run_setup_jobs() {
    if [ "$SKIP_SETUP" = true ]; then
        info "Skipping setup jobs as requested"
        return 0
    fi
    
    log "Running setup jobs..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would run setup jobs"
        return 0
    fi
    
    # Run Elasticsearch setup
    log "Running Elasticsearch setup job..."
    if kubectl get job elasticsearch-setup -n "$NAMESPACE" &> /dev/null; then
        kubectl delete job elasticsearch-setup -n "$NAMESPACE"
    fi
    
    # Wait for setup job to complete
    kubectl wait --for=condition=complete job/elasticsearch-setup -n "$NAMESPACE" --timeout="300s" || {
        warn "Elasticsearch setup job did not complete successfully"
        kubectl logs job/elasticsearch-setup -n "$NAMESPACE" || true
    }
    
    # Run Kibana setup
    log "Running Kibana setup job..."
    if kubectl get job kibana-setup -n "$NAMESPACE" &> /dev/null; then
        kubectl delete job kibana-setup -n "$NAMESPACE"
    fi
    
    kubectl wait --for=condition=complete job/kibana-setup -n "$NAMESPACE" --timeout="300s" || {
        warn "Kibana setup job did not complete successfully"
        kubectl logs job/kibana-setup -n "$NAMESPACE" || true
    }
    
    # Run Filebeat setup
    if kubectl get job filebeat-setup -n "$NAMESPACE" &> /dev/null; then
        log "Running Filebeat setup job..."
        kubectl delete job filebeat-setup -n "$NAMESPACE"
        kubectl wait --for=condition=complete job/filebeat-setup -n "$NAMESPACE" --timeout="300s" || {
            warn "Filebeat setup job did not complete successfully"
            kubectl logs job/filebeat-setup -n "$NAMESPACE" || true
        }
    fi
    
    log "Setup jobs completed"
}

# Verify deployment
verify_deployment() {
    log "Verifying ELK stack deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check Elasticsearch
    local es_pods=$(kubectl get pods -n "$NAMESPACE" -l app=elasticsearch --no-headers | wc -l)
    local es_ready=$(kubectl get pods -n "$NAMESPACE" -l app=elasticsearch -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    
    if [ "$es_ready" -gt 0 ]; then
        info "✅ Elasticsearch: $es_ready/$es_pods pods ready"
    else
        error "❌ Elasticsearch: No pods ready"
    fi
    
    # Check Logstash
    local ls_pods=$(kubectl get pods -n "$NAMESPACE" -l app=logstash --no-headers | wc -l)
    local ls_ready=$(kubectl get pods -n "$NAMESPACE" -l app=logstash -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    
    if [ "$ls_ready" -gt 0 ]; then
        info "✅ Logstash: $ls_ready/$ls_pods pods ready"
    else
        error "❌ Logstash: No pods ready"
    fi
    
    # Check Kibana
    local kb_pods=$(kubectl get pods -n "$NAMESPACE" -l app=kibana --no-headers | wc -l)
    local kb_ready=$(kubectl get pods -n "$NAMESPACE" -l app=kibana -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    
    if [ "$kb_ready" -gt 0 ]; then
        info "✅ Kibana: $kb_ready/$kb_pods pods ready"
    else
        error "❌ Kibana: No pods ready"
    fi
    
    # Check Filebeat
    local fb_pods=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat --no-headers | wc -l)
    local fb_ready=$(kubectl get pods -n "$NAMESPACE" -l app=filebeat -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    
    if [ "$fb_ready" -gt 0 ]; then
        info "✅ Filebeat: $fb_ready/$fb_pods pods ready"
    else
        error "❌ Filebeat: No pods ready"
    fi
    
    # Test end-to-end logging
    log "Testing end-to-end logging pipeline..."
    
    # Create test log entry
    kubectl run test-logger --image=busybox --rm -it --restart=Never -- sh -c "echo 'Test log entry from ELK deployment script' && sleep 5" || true
    
    # Wait a moment for log processing
    sleep 30
    
    # Check if logs are being indexed (this is a basic check)
    local es_pod=$(kubectl get pods -n "$NAMESPACE" -l app=elasticsearch,role=master -o jsonpath='{.items[0].metadata.name}')
    if kubectl exec -n "$NAMESPACE" "$es_pod" -- curl -k -u elastic:elastic -s "https://localhost:9200/_cat/indices?v" | grep -q "application-logs\|system-logs"; then
        info "✅ Logs are being indexed in Elasticsearch"
    else
        warn "❓ No log indices found yet (may take a few minutes)"
    fi
    
    log "Deployment verification completed"
}

# Show access information
show_access_info() {
    log "ELK Stack access information:"
    
    # Get external IPs or provide port-forward instructions
    local external_ip=""
    if kubectl get service istio-ingressgateway -n istio-system &> /dev/null; then
        external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        if [ -z "$external_ip" ]; then
            external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
        fi
    fi
    
    info ""
    if [ -n "$external_ip" ]; then
        info "External Access (add to /etc/hosts or DNS):"
        info "$external_ip kibana.publish-ai.local"
        info ""
        info "Kibana Dashboard: https://kibana.publish-ai.local"
    else
        info "Port-forward access:"
        info "kubectl port-forward svc/kibana 5601:5601 -n $NAMESPACE"
        info "Kibana Dashboard: http://localhost:5601"
    fi
    
    info ""
    info "Default credentials:"
    info "Username: elastic"
    info "Password: elastic (change in production!)"
    
    info ""
    info "Useful commands:"
    info "kubectl logs -l app=elasticsearch -n $NAMESPACE     # Elasticsearch logs"
    info "kubectl logs -l app=logstash -n $NAMESPACE          # Logstash logs"
    info "kubectl logs -l app=kibana -n $NAMESPACE            # Kibana logs"
    info "kubectl logs -l app=filebeat -n $NAMESPACE          # Filebeat logs"
    
    info ""
    info "Health checks:"
    info "kubectl exec -n $NAMESPACE elasticsearch-master-0 -- curl -k -u elastic:elastic https://localhost:9200/_cluster/health"
    info "kubectl exec -n $NAMESPACE \$(kubectl get pods -l app=logstash -o name | head -1) -- curl http://localhost:9600/_node/stats"
    
    info ""
    info "Monitoring:"
    info "Prometheus metrics are available for all components"
    info "ServiceMonitors are configured for automatic discovery"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        error "ELK stack deployment failed with exit code $exit_code"
        
        if [ "$DRY_RUN" = false ]; then
            warn "Check the following for troubleshooting:"
            warn "kubectl get pods -n $NAMESPACE"
            warn "kubectl describe pods -n $NAMESPACE"
            warn "kubectl logs -l app=elasticsearch -n $NAMESPACE"
        fi
    fi
}

# Main function
main() {
    log "Starting ELK Stack Deployment for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    create_namespace
    deploy_elasticsearch
    deploy_logstash
    deploy_kibana
    deploy_filebeat
    run_setup_jobs
    verify_deployment
    show_access_info
    
    log "✅ ELK Stack deployment completed successfully!"
    log ""
    info "Next steps:"
    info "1. Access Kibana dashboard and explore pre-built visualizations"
    info "2. Configure additional log sources if needed"
    info "3. Set up alerting for critical log events"
    info "4. Review and adjust index lifecycle policies"
    info "5. Update default passwords for production use"
    log ""
    log "For troubleshooting, check component logs and cluster status"
}

# Set up signal handlers
trap cleanup EXIT

# Run main function
main "$@"