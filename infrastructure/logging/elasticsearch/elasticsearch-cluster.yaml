apiVersion: v1
kind: Namespace
metadata:
  name: logging
  labels:
    istio-injection: enabled

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: logging
data:
  elasticsearch.yml: |
    cluster.name: "publish-ai-logs"
    network.host: 0.0.0.0
    
    # Discovery and cluster formation
    discovery.seed_hosts: ["elasticsearch-master-0.elasticsearch-master", "elasticsearch-master-1.elasticsearch-master", "elasticsearch-master-2.elasticsearch-master"]
    cluster.initial_master_nodes: ["elasticsearch-master-0", "elasticsearch-master-1", "elasticsearch-master-2"]
    
    # Node roles
    node.roles: ${NODE_ROLES}
    
    # Memory and performance
    bootstrap.memory_lock: true
    indices.memory.index_buffer_size: 30%
    indices.memory.min_index_buffer_size: 96MB
    
    # Index management
    action.auto_create_index: true
    action.destructive_requires_name: true
    
    # Security
    xpack.security.enabled: true
    xpack.security.transport.ssl.enabled: true
    xpack.security.transport.ssl.verification_mode: certificate
    xpack.security.transport.ssl.keystore.path: certs/elastic-certificates.p12
    xpack.security.transport.ssl.truststore.path: certs/elastic-certificates.p12
    xpack.security.http.ssl.enabled: true
    xpack.security.http.ssl.keystore.path: certs/elastic-certificates.p12
    
    # Monitoring
    xpack.monitoring.collection.enabled: true
    xpack.monitoring.exporters.prometheus.type: http
    xpack.monitoring.exporters.prometheus.host: ["http://prometheus.monitoring.svc.cluster.local:9090"]
    
    # Index lifecycle management
    xpack.ilm.enabled: true
    
    # Performance tuning
    thread_pool.write.queue_size: 1000
    thread_pool.search.queue_size: 1000
    indices.queries.cache.size: 20%
    indices.requests.cache.size: 2%
    
    # Logging
    logger.org.elasticsearch.discovery: DEBUG

---
apiVersion: v1
kind: Secret
metadata:
  name: elasticsearch-certificates
  namespace: logging
type: Opaque
data:
  # Self-signed certificates for development (replace with real certs in production)
  elastic-certificates.p12: MIIJvQIBAzCCCXcGCSqGSIb3DQEHAaCCCWgEgglkMIIJYDCCA88GCSqGSIb3DQEHBqCCA8AwggO8AgEAMIIDtQYJKoZIhvcNAQcBMBwGCiqGSIb3DQEMAQYwDgQI5x7JRQXXZqECAggABIIDmCjR4r3VGF8ZcOoEOYNIKJKkOWWaJzJZxVf
  ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMrVENDQWVHZ0F3SUJBZ0lCQVRBTkJna3Foa2lHOXcwQkFRc0ZBREFOTVFzd0NRWURWUVFERXdKallUQWUKRncweU5EQTJNekV4TWpBd01EQmFGdzB5TlRBMk16RXhNakF3TURCYU1BMHhDekFKQmdOVkJBTVRBbU5oTUlJQgpJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBd
  elasticsearch.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURFVENDQWZtZ0F3SUJBZ0lJQVhCY0V6WUY4UUF3RFFZSktvWklodmNOQVFFTEJRQXdEVEVMTUFrR0ExVUUKQXhNQ1kyRXdIaGNOTWpRd05qTXhNVEl3TURBd1doY05NalV3TmpNeE1USXdNREF3V2pBUk1ROHdEUVlEVlFRRApFd1psYkdGemRHbGpNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXpL
  elasticsearch.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2Z0lCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktnd2dnU2tBZ0VBQW9JQkFRRE1ySDVlUEdYOEJMOEwKUENBRGN5Y3N5TG9GWjd3M3FmOTdKMW5FZE5CT05Va3ZuV09hSk5mVkJXM0xQYlk1MVlsdGNhb2xMUk5mRXMxWgphdThCdFN1azFYRTVrRWZteGhOOGJmSXpPekp5M1c0ZzVyWER0MTRWUWV1cG1nVklLNjR6dE9oTHIyQmFGaUxhCkJvd0tKTnpkWWtFM1kwcTI

---
apiVersion: v1
kind: Secret
metadata:
  name: elasticsearch-credentials
  namespace: logging
type: Opaque
data:
  # Default credentials (change in production)
  elastic: ZWxhc3RpYw==  # elastic
  kibana_system: a2liYW5hX3N5c3RlbQ==  # kibana_system

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch-master
  namespace: logging
  labels:
    app: elasticsearch
    role: master
spec:
  serviceName: elasticsearch-master
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
      role: master
  template:
    metadata:
      labels:
        app: elasticsearch
        role: master
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9200"
        prometheus.io/path: "/_prometheus/metrics"
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      - name: increase-vm-max-map
        image: busybox:1.36
        command:
        - sysctl
        - -w
        - vm.max_map_count=262144
        securityContext:
          privileged: true
      - name: increase-fd-ulimit
        image: busybox:1.36
        command:
        - sh
        - -c
        - ulimit -n 65536
        securityContext:
          privileged: true
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.1
        env:
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: NODE_ROLES
          value: "master,ingest"
        - name: ES_JAVA_OPTS
          value: "-Xms4g -Xmx4g"
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: elastic
        - name: xpack.security.enabled
          value: "true"
        - name: xpack.security.enrollment.enabled
          value: "false"
        ports:
        - containerPort: 9200
          name: http
        - containerPort: 9300
          name: transport
        resources:
          requests:
            cpu: 1000m
            memory: 4Gi
          limits:
            cpu: 2000m
            memory: 8Gi
        volumeMounts:
        - name: data
          mountPath: /usr/share/elasticsearch/data
        - name: config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
        - name: certs
          mountPath: /usr/share/elasticsearch/config/certs
          readOnly: true
        livenessProbe:
          httpGet:
            scheme: HTTPS
            path: /_cluster/health
            port: 9200
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            scheme: HTTPS
            path: /_cluster/health?wait_for_status=yellow&timeout=5s
            port: 9200
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: elasticsearch-config
      - name: certs
        secret:
          secretName: elasticsearch-certificates
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch-data
  namespace: logging
  labels:
    app: elasticsearch
    role: data
spec:
  serviceName: elasticsearch-data
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
      role: data
  template:
    metadata:
      labels:
        app: elasticsearch
        role: data
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9200"
        prometheus.io/path: "/_prometheus/metrics"
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      - name: increase-vm-max-map
        image: busybox:1.36
        command:
        - sysctl
        - -w
        - vm.max_map_count=262144
        securityContext:
          privileged: true
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.1
        env:
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: NODE_ROLES
          value: "data,data_content,data_hot,data_warm,data_cold"
        - name: ES_JAVA_OPTS
          value: "-Xms8g -Xmx8g"
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: elastic
        ports:
        - containerPort: 9200
          name: http
        - containerPort: 9300
          name: transport
        resources:
          requests:
            cpu: 2000m
            memory: 8Gi
          limits:
            cpu: 4000m
            memory: 16Gi
        volumeMounts:
        - name: data
          mountPath: /usr/share/elasticsearch/data
        - name: config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
        - name: certs
          mountPath: /usr/share/elasticsearch/config/certs
          readOnly: true
        livenessProbe:
          httpGet:
            scheme: HTTPS
            path: /_cluster/health
            port: 9200
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            scheme: HTTPS
            path: /_cluster/health?wait_for_status=yellow&timeout=5s
            port: 9200
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: elasticsearch-config
      - name: certs
        secret:
          secretName: elasticsearch-certificates
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 1Ti

---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-master
  namespace: logging
  labels:
    app: elasticsearch
    role: master
spec:
  clusterIP: None
  selector:
    app: elasticsearch
    role: master
  ports:
  - name: http
    port: 9200
    targetPort: 9200
  - name: transport
    port: 9300
    targetPort: 9300

---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-data
  namespace: logging
  labels:
    app: elasticsearch
    role: data
spec:
  clusterIP: None
  selector:
    app: elasticsearch
    role: data
  ports:
  - name: http
    port: 9200
    targetPort: 9200
  - name: transport
    port: 9300
    targetPort: 9300

---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: logging
  labels:
    app: elasticsearch
spec:
  selector:
    app: elasticsearch
  ports:
  - name: http
    port: 9200
    targetPort: 9200
  - name: transport
    port: 9300
    targetPort: 9300
  type: ClusterIP

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: elasticsearch-dr
  namespace: logging
spec:
  host: elasticsearch.logging.svc.cluster.local
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 5
        maxRetries: 3
        timeout: 300s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: elasticsearch-access
  namespace: logging
spec:
  selector:
    matchLabels:
      app: elasticsearch
  rules:
  # Allow access from logging namespace (Kibana, Logstash)
  - from:
    - source:
        namespaces: ["logging"]
  
  # Allow access from monitoring namespace (Prometheus)
  - from:
    - source:
        namespaces: ["monitoring"]
    to:
    - operation:
        paths: ["/_prometheus/metrics", "/_cluster/health"]
  
  # Allow access from filebeat in all namespaces
  - from:
    - source:
        principals: ["cluster.local/ns/*/sa/filebeat"]

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-index-templates
  namespace: logging
data:
  application-logs-template.json: |
    {
      "index_patterns": ["application-logs-*"],
      "template": {
        "settings": {
          "number_of_shards": 3,
          "number_of_replicas": 1,
          "refresh_interval": "5s",
          "index.lifecycle.name": "logging-policy",
          "index.lifecycle.rollover_alias": "application-logs"
        },
        "mappings": {
          "properties": {
            "@timestamp": {
              "type": "date"
            },
            "level": {
              "type": "keyword"
            },
            "service": {
              "type": "keyword"
            },
            "namespace": {
              "type": "keyword"
            },
            "pod": {
              "type": "keyword"
            },
            "container": {
              "type": "keyword"
            },
            "message": {
              "type": "text",
              "analyzer": "standard"
            },
            "request_id": {
              "type": "keyword"
            },
            "user_id": {
              "type": "keyword"
            },
            "duration": {
              "type": "long"
            },
            "ai_model": {
              "type": "keyword"
            },
            "tokens_used": {
              "type": "long"
            },
            "cost_cents": {
              "type": "long"
            },
            "cost_dollars": {
              "type": "float"
            },
            "content_quality": {
              "type": "float"
            },
            "error_type": {
              "type": "keyword"
            },
            "stack_trace": {
              "type": "text",
              "index": false
            }
          }
        }
      }
    }
  
  system-logs-template.json: |
    {
      "index_patterns": ["system-logs-*", "k8s-logs-*"],
      "template": {
        "settings": {
          "number_of_shards": 2,
          "number_of_replicas": 1,
          "refresh_interval": "10s",
          "index.lifecycle.name": "logging-policy"
        },
        "mappings": {
          "properties": {
            "@timestamp": {
              "type": "date"
            },
            "level": {
              "type": "keyword"
            },
            "source": {
              "type": "keyword"
            },
            "node": {
              "type": "keyword"
            },
            "component": {
              "type": "keyword"
            },
            "message": {
              "type": "text"
            },
            "kubernetes": {
              "properties": {
                "namespace": {
                  "type": "keyword"
                },
                "pod": {
                  "type": "keyword"
                },
                "container": {
                  "type": "keyword"
                }
              }
            }
          }
        }
      }
    }
  
  security-logs-template.json: |
    {
      "index_patterns": ["security-logs-*", "audit-logs-*"],
      "template": {
        "settings": {
          "number_of_shards": 1,
          "number_of_replicas": 2,
          "refresh_interval": "1s",
          "index.lifecycle.name": "security-logging-policy"
        },
        "mappings": {
          "properties": {
            "@timestamp": {
              "type": "date"
            },
            "event_type": {
              "type": "keyword"
            },
            "user_id": {
              "type": "keyword"
            },
            "source_ip": {
              "type": "ip"
            },
            "user_agent": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            },
            "security_event": {
              "type": "keyword"
            },
            "risk_score": {
              "type": "integer"
            },
            "authentication_result": {
              "type": "keyword"
            },
            "resource_accessed": {
              "type": "keyword"
            },
            "action": {
              "type": "keyword"
            }
          }
        }
      }
    }

---
apiVersion: batch/v1
kind: Job
metadata:
  name: elasticsearch-setup
  namespace: logging
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: setup
        image: curlimages/curl:8.4.0
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for Elasticsearch to be ready..."
          until curl -k -u elastic:elastic https://elasticsearch:9200/_cluster/health?wait_for_status=yellow&timeout=60s; do
            echo "Waiting for Elasticsearch..."
            sleep 10
          done
          
          echo "Creating index lifecycle policies..."
          curl -k -u elastic:elastic -X PUT "https://elasticsearch:9200/_ilm/policy/logging-policy" -H 'Content-Type: application/json' -d'
          {
            "policy": {
              "phases": {
                "hot": {
                  "actions": {
                    "rollover": {
                      "max_size": "10GB",
                      "max_age": "1d"
                    }
                  }
                },
                "warm": {
                  "min_age": "7d",
                  "actions": {
                    "allocate": {
                      "number_of_replicas": 0
                    }
                  }
                },
                "cold": {
                  "min_age": "30d",
                  "actions": {
                    "allocate": {
                      "number_of_replicas": 0
                    }
                  }
                },
                "delete": {
                  "min_age": "90d"
                }
              }
            }
          }'
          
          curl -k -u elastic:elastic -X PUT "https://elasticsearch:9200/_ilm/policy/security-logging-policy" -H 'Content-Type: application/json' -d'
          {
            "policy": {
              "phases": {
                "hot": {
                  "actions": {
                    "rollover": {
                      "max_size": "5GB",
                      "max_age": "1d"
                    }
                  }
                },
                "warm": {
                  "min_age": "30d",
                  "actions": {
                    "allocate": {
                      "number_of_replicas": 1
                    }
                  }
                },
                "cold": {
                  "min_age": "90d",
                  "actions": {
                    "allocate": {
                      "number_of_replicas": 0
                    }
                  }
                },
                "delete": {
                  "min_age": "365d"
                }
              }
            }
          }'
          
          echo "Setup completed successfully"