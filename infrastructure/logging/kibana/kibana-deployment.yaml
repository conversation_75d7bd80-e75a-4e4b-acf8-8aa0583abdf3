apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-config
  namespace: logging
data:
  kibana.yml: |
    server.name: "kibana"
    server.host: "0.0.0.0"
    server.port: 5601
    server.publicBaseUrl: "https://kibana.publish-ai.local"
    
    # Elasticsearch connection
    elasticsearch.hosts: ["https://elasticsearch:9200"]
    elasticsearch.username: "kibana_system"
    elasticsearch.password: "${KIBANA_SYSTEM_PASSWORD}"
    elasticsearch.ssl.verificationMode: none
    
    # Security
    xpack.security.enabled: true
    xpack.security.encryptionKey: "${KIBANA_ENCRYPTION_KEY}"
    xpack.security.session.idleTimeout: "1h"
    xpack.security.session.lifespan: "8h"
    
    # Monitoring
    monitoring.enabled: true
    monitoring.collection.enabled: true
    monitoring.kibana.collection.enabled: true
    
    # Logging
    logging.appenders.file.type: file
    logging.appenders.file.fileName: /usr/share/kibana/logs/kibana.log
    logging.appenders.file.layout.type: json
    logging.root.level: info
    
    # Performance
    elasticsearch.requestTimeout: 300000
    elasticsearch.shardTimeout: 30000
    kibana.index: ".kibana-publish-ai"
    
    # UI settings
    kibana.defaultAppId: "discover"
    map.includeElasticMapsService: false
    telemetry.enabled: false
    newsfeed.enabled: false
    
    # Advanced settings
    data.search.timeout: 600000
    elasticsearch.pingTimeout: 30000
    server.maxPayload: 1048576

---
apiVersion: v1
kind: Secret
metadata:
  name: kibana-credentials
  namespace: logging
type: Opaque
data:
  # Default credentials (change in production)
  kibana-system-password: a2liYW5hX3N5c3RlbQ==  # kibana_system
  kibana-encryption-key: YWJjZGVmZ2hpams=  # abcdefghijk (32+ chars in production)

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: logging
  labels:
    app: kibana
spec:
  replicas: 2
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "5601"
        prometheus.io/path: "/api/status"
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:8.11.1
        env:
        - name: KIBANA_SYSTEM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kibana-credentials
              key: kibana-system-password
        - name: KIBANA_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: kibana-credentials
              key: kibana-encryption-key
        - name: NODE_OPTIONS
          value: "--max-old-space-size=2048"
        ports:
        - containerPort: 5601
          name: http
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        volumeMounts:
        - name: config
          mountPath: /usr/share/kibana/config/kibana.yml
          subPath: kibana.yml
        - name: data
          mountPath: /usr/share/kibana/data
        - name: logs
          mountPath: /usr/share/kibana/logs
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: kibana-config
      - name: data
        emptyDir: {}
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: logging
  labels:
    app: kibana
spec:
  selector:
    app: kibana
  ports:
  - name: http
    port: 5601
    targetPort: 5601
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-dashboards
  namespace: logging
data:
  application-logs-dashboard.json: |
    {
      "version": "8.11.1",
      "objects": [
        {
          "id": "application-logs-dashboard",
          "type": "dashboard",
          "attributes": {
            "title": "Application Logs Overview",
            "description": "Comprehensive view of application logs across all microservices",
            "panelsJSON": "[{\"version\":\"8.11.1\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]",
            "timeRestore": false,
            "timeTo": "now",
            "timeFrom": "now-24h",
            "refreshInterval": {
              "pause": false,
              "value": 30000
            },
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"
            }
          },
          "references": [
            {
              "name": "panel_1",
              "type": "visualization",
              "id": "service-logs-histogram"
            }
          ]
        },
        {
          "id": "service-logs-histogram",
          "type": "visualization",
          "attributes": {
            "title": "Service Logs Over Time",
            "visState": "{\"title\":\"Service Logs Over Time\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"useNormalizedEsInterval\":true,\"interval\":\"auto\",\"drop_partials\":false,\"min_doc_count\":1,\"extended_bounds\":{}}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"group\",\"params\":{\"field\":\"service\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}",
            "uiStateJSON": "{}",
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"index\":\"application-logs-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"
            }
          }
        }
      ]
    }

  ai-services-dashboard.json: |
    {
      "version": "8.11.1",
      "objects": [
        {
          "id": "ai-services-dashboard",
          "type": "dashboard",
          "attributes": {
            "title": "AI Services Analytics",
            "description": "Specialized dashboard for AI/ML service monitoring and cost analysis",
            "panelsJSON": "[{\"version\":\"8.11.1\",\"gridData\":{\"x\":0,\"y\":0,\"w\":12,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.1\",\"gridData\":{\"x\":12,\"y\":0,\"w\":12,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.1\",\"gridData\":{\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{},\"panelRefName\":\"panel_3\"}]",
            "timeRestore": false,
            "timeTo": "now",
            "timeFrom": "now-4h",
            "refreshInterval": {
              "pause": false,
              "value": 60000
            },
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[{\"meta\":{\"index\":\"ai-services-logs-*\",\"negate\":false,\"disabled\":false,\"alias\":null,\"type\":\"exists\",\"key\":\"ai_model\",\"value\":\"exists\"},\"exists\":{\"field\":\"ai_model\"},\"$state\":{\"store\":\"appState\"}}]}"
            }
          },
          "references": [
            {
              "name": "panel_1",
              "type": "visualization", 
              "id": "ai-cost-over-time"
            },
            {
              "name": "panel_2",
              "type": "visualization",
              "id": "content-quality-distribution"
            },
            {
              "name": "panel_3",
              "type": "visualization",
              "id": "ai-model-performance"
            }
          ]
        },
        {
          "id": "ai-cost-over-time",
          "type": "visualization",
          "attributes": {
            "title": "AI Generation Costs Over Time",
            "visState": "{\"title\":\"AI Generation Costs Over Time\",\"type\":\"line\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Cost ($)\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"line\",\"mode\":\"normal\",\"data\":{\"label\":\"Average cost_dollars\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"cost_dollars\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"useNormalizedEsInterval\":true,\"interval\":\"auto\",\"drop_partials\":false,\"min_doc_count\":1,\"extended_bounds\":{}}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"group\",\"params\":{\"field\":\"service\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}",
            "uiStateJSON": "{}",
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"index\":\"ai-services-logs-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"
            }
          }
        },
        {
          "id": "content-quality-distribution",
          "type": "visualization",
          "attributes": {
            "title": "Content Quality Distribution",
            "visState": "{\"title\":\"Content Quality Distribution\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"quality_category\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}",
            "uiStateJSON": "{}",
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"index\":\"ai-services-logs-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"
            }
          }
        }
      ]
    }

  security-dashboard.json: |
    {
      "version": "8.11.1",
      "objects": [
        {
          "id": "security-dashboard",
          "type": "dashboard",
          "attributes": {
            "title": "Security Monitoring",
            "description": "Security events, threats, and compliance monitoring",
            "panelsJSON": "[{\"version\":\"8.11.1\",\"gridData\":{\"x\":0,\"y\":0,\"w\":12,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.1\",\"gridData\":{\"x\":12,\"y\":0,\"w\":12,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.1\",\"gridData\":{\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{},\"panelRefName\":\"panel_3\"}]",
            "timeRestore": false,
            "timeTo": "now", 
            "timeFrom": "now-24h",
            "refreshInterval": {
              "pause": false,
              "value": 30000
            },
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"
            }
          },
          "references": [
            {
              "name": "panel_1",
              "type": "visualization",
              "id": "security-events-timeline"
            },
            {
              "name": "panel_2", 
              "type": "visualization",
              "id": "risk-score-distribution"
            },
            {
              "name": "panel_3",
              "type": "visualization", 
              "id": "geographic-threats"
            }
          ]
        }
      ]
    }

---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-setup
  namespace: logging
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: setup
        image: curlimages/curl:8.4.0
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for Kibana to be ready..."
          until curl -f http://kibana:5601/api/status; do
            echo "Waiting for Kibana..."
            sleep 10
          done
          
          echo "Creating index patterns..."
          
          # Application logs index pattern
          curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/application-logs-*" \
            -H 'Content-Type: application/json' \
            -H 'kbn-xsrf: true' \
            -d'{
              "attributes": {
                "title": "application-logs-*",
                "timeFieldName": "@timestamp"
              }
            }'
          
          # AI services logs index pattern
          curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/ai-services-logs-*" \
            -H 'Content-Type: application/json' \
            -H 'kbn-xsrf: true' \
            -d'{
              "attributes": {
                "title": "ai-services-logs-*",
                "timeFieldName": "@timestamp"
              }
            }'
          
          # Security logs index pattern
          curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/security-logs-*" \
            -H 'Content-Type: application/json' \
            -H 'kbn-xsrf: true' \
            -d'{
              "attributes": {
                "title": "security-logs-*",
                "timeFieldName": "@timestamp"
              }
            }'
          
          # System logs index pattern
          curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/system-logs-*" \
            -H 'Content-Type: application/json' \
            -H 'kbn-xsrf: true' \
            -d'{
              "attributes": {
                "title": "system-logs-*",
                "timeFieldName": "@timestamp"
              }
            }'
          
          echo "Setting up default dashboard..."
          curl -X POST "http://kibana:5601/api/kibana/settings/defaultIndex" \
            -H 'Content-Type: application/json' \
            -H 'kbn-xsrf: true' \
            -d'{"value": "application-logs-*"}'
          
          echo "Kibana setup completed successfully"

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kibana
  namespace: logging
  labels:
    app: kibana
spec:
  selector:
    matchLabels:
      app: kibana
  endpoints:
  - port: http
    interval: 30s
    path: /api/status
    scrapeTimeout: 10s

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: kibana-vs
  namespace: logging
spec:
  hosts:
  - kibana.publish-ai.local
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: kibana.logging.svc.cluster.local
        port:
          number: 5601
    timeout: 300s
    headers:
      request:
        add:
          x-forwarded-proto: https

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: kibana-dr
  namespace: logging
spec:
  host: kibana.logging.svc.cluster.local
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 20
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 20
        maxRequestsPerConnection: 3
        maxRetries: 3
        timeout: 300s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: kibana-access
  namespace: logging
spec:
  selector:
    matchLabels:
      app: kibana
  rules:
  # Allow access from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
  
  # Allow external access through ingress gateway
  - from:
    - source:
        principals: ["cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account"]
  
  # Allow internal monitoring
  - to:
    - operation:
        paths: ["/api/status", "/api/stats"]