apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config
  namespace: logging
data:
  filebeat.yml: |
    filebeat.inputs:
    # Application logs from containers
    - type: container
      paths:
        - /var/log/containers/*api-gateway*.log
        - /var/log/containers/*content-generation*.log
        - /var/log/containers/*market-intelligence*.log
        - /var/log/containers/*publishing-service*.log
        - /var/log/containers/*cover-designer*.log
        - /var/log/containers/*sales-monitor*.log
        - /var/log/containers/*personalization*.log
        - /var/log/containers/*research*.log
        - /var/log/containers/*multimodal-generator*.log
      fields:
        log_type: application
        source: container
      fields_under_root: true
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"
      - decode_json_fields:
          fields: ["message"]
          target: "json"
          overwrite_keys: true
      - multiline:
          pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
          negate: true
          match: after

    # AI services logs (separate input for specialized processing)
    - type: container
      paths:
        - /var/log/containers/*content-generation*.log
        - /var/log/containers/*market-intelligence*.log
        - /var/log/containers/*cover-designer*.log
        - /var/log/containers/*multimodal-generator*.log
      fields:
        log_type: ai-services
        source: ai-container
      fields_under_root: true
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"
      - decode_json_fields:
          fields: ["message"]
          target: "ai"
          overwrite_keys: true

    # System logs from Kubernetes nodes
    - type: log
      paths:
        - /var/log/kubelet.log
        - /var/log/kube-proxy.log
        - /var/log/kube-apiserver.log
        - /var/log/kube-controller-manager.log
        - /var/log/kube-scheduler.log
      fields:
        log_type: system
        source: kubernetes
      fields_under_root: true
      multiline.pattern: '^\d{4}-\d{2}-\d{2}'
      multiline.negate: true
      multiline.match: after

    # Docker daemon logs
    - type: log
      paths:
        - /var/log/docker.log
        - /var/log/containerd.log
      fields:
        log_type: system
        source: container-runtime
      fields_under_root: true

    # Security logs
    - type: log
      paths:
        - /var/log/auth.log
        - /var/log/secure
        - /var/log/audit/audit.log
      fields:
        log_type: security
        source: system-security
      fields_under_root: true

    # Istio logs
    - type: container
      paths:
        - /var/log/containers/*istio-proxy*.log
        - /var/log/containers/*istiod*.log
        - /var/log/containers/*istio-ingressgateway*.log
        - /var/log/containers/*istio-egressgateway*.log
      fields:
        log_type: system
        source: istio
      fields_under_root: true
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"

    processors:
    # Add host information
    - add_host_metadata:
        when.not.contains.tags: forwarded
    
    # Add Docker metadata
    - add_docker_metadata: ~
    
    # Drop empty lines
    - drop_event:
        when:
          regexp:
            message: '^\s*$'
    
    # Enrich with environment information
    - add_fields:
        target: environment
        fields:
          cluster: publish-ai
          region: us-west-2
          environment: production

    # Route different log types to different outputs
    output.logstash:
      hosts: ["logstash:5044"]
      when.equals:
        fields.log_type: "application"
    
    output.logstash:
      hosts: ["logstash:5045"]
      when.equals:
        fields.log_type: "ai-services"
    
    output.logstash:
      hosts: ["logstash:5046"]
      when.equals:
        fields.log_type: "system"
    
    output.logstash:
      hosts: ["logstash:5047"]
      when.equals:
        fields.log_type: "security"

    # Filebeat configuration
    setup.template.enabled: false
    setup.ilm.enabled: false
    
    # Monitoring
    monitoring.enabled: true
    monitoring.elasticsearch:
      hosts: ["https://elasticsearch:9200"]
      username: "elastic"
      password: "${ELASTIC_PASSWORD}"
      ssl.verification_mode: none
    
    # Logging
    logging.level: info
    logging.to_files: true
    logging.files:
      path: /usr/share/filebeat/logs
      name: filebeat
      keepfiles: 7
      permissions: 0644
    
    # Performance settings
    queue.mem:
      events: 4096
      flush.min_events: 512
      flush.timeout: 5s

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: filebeat
  namespace: logging

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: filebeat
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods", "nodes"]
  verbs: ["get", "watch", "list"]
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: filebeat
subjects:
- kind: ServiceAccount
  name: filebeat
  namespace: logging
roleRef:
  kind: ClusterRole
  name: filebeat
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: Secret
metadata:
  name: filebeat-credentials
  namespace: logging
type: Opaque
data:
  elastic-password: ZWxhc3RpYw==  # elastic

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat
  namespace: logging
  labels:
    app: filebeat
spec:
  selector:
    matchLabels:
      app: filebeat
  template:
    metadata:
      labels:
        app: filebeat
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "5067"
        prometheus.io/path: "/stats"
    spec:
      serviceAccountName: filebeat
      terminationGracePeriodSeconds: 30
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      securityContext:
        runAsUser: 0
        runAsGroup: 0
      containers:
      - name: filebeat
        image: docker.elastic.co/beats/filebeat:8.11.1
        args: [
          "-c", "/etc/filebeat.yml",
          "-e",
        ]
        env:
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: filebeat-credentials
              key: elastic-password
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        securityContext:
          runAsUser: 0
          privileged: true
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
          limits:
            cpu: 500m
            memory: 512Mi
        volumeMounts:
        - name: config
          mountPath: /etc/filebeat.yml
          readOnly: true
          subPath: filebeat.yml
        - name: data
          mountPath: /usr/share/filebeat/data
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: varlog
          mountPath: /var/log
          readOnly: true
        - name: dockersock
          mountPath: /var/run/docker.sock
          readOnly: true
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - |
              curl -f http://localhost:5067/stats || exit 1
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - |
              filebeat test config -c /etc/filebeat.yml
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: filebeat-config
          defaultMode: 0600
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: varlog
        hostPath:
          path: /var/log
      - name: dockersock
        hostPath:
          path: /var/run/docker.sock
      - name: data
        hostPath:
          path: /var/lib/filebeat-data
          type: DirectoryOrCreate

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: filebeat
  namespace: logging
  labels:
    app: filebeat
spec:
  selector:
    matchLabels:
      app: filebeat
  endpoints:
  - port: http
    interval: 30s
    path: /stats
    scrapeTimeout: 10s