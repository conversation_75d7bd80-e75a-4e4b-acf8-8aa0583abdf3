apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: logging
data:
  logstash.yml: |
    http.host: "0.0.0.0"
    xpack.monitoring.enabled: true
    xpack.monitoring.elasticsearch.hosts: ["https://elasticsearch:9200"]
    xpack.monitoring.elasticsearch.username: "logstash_system"
    xpack.monitoring.elasticsearch.password: "${LOGSTASH_SYSTEM_PASSWORD}"
    xpack.monitoring.elasticsearch.ssl.verification_mode: none
    
    # Pipeline settings
    pipeline.workers: 4
    pipeline.batch.size: 125
    pipeline.batch.delay: 50
    
    # Queue configuration
    queue.type: persisted
    queue.max_events: 10000
    queue.max_bytes: 1GB
    queue.checkpoint.writes: 1024
    
    # Dead letter queue
    dead_letter_queue.enable: true
    dead_letter_queue.max_bytes: 1GB
    
    # Path settings
    path.data: /usr/share/logstash/data
    path.logs: /usr/share/logstash/logs
    path.settings: /usr/share/logstash/config
    
    # Performance tuning
    config.reload.automatic: true
    config.reload.interval: 30s

  pipelines.yml: |
    - pipeline.id: application-logs
      path.config: "/usr/share/logstash/pipeline/application-logs.conf"
      pipeline.workers: 2
      pipeline.batch.size: 125
      
    - pipeline.id: system-logs
      path.config: "/usr/share/logstash/pipeline/system-logs.conf"
      pipeline.workers: 1
      pipeline.batch.size: 50
      
    - pipeline.id: security-logs
      path.config: "/usr/share/logstash/pipeline/security-logs.conf"
      pipeline.workers: 1
      pipeline.batch.size: 25
      
    - pipeline.id: ai-services-logs
      path.config: "/usr/share/logstash/pipeline/ai-services-logs.conf"
      pipeline.workers: 2
      pipeline.batch.size: 50

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-pipelines
  namespace: logging
data:
  application-logs.conf: |
    input {
      beats {
        port => 5044
        type => "application"
      }
    }
    
    filter {
      if [fields][log_type] == "application" {
        # Parse JSON logs
        json {
          source => "message"
          target => "app"
        }
        
        # Extract common fields
        if [app] {
          mutate {
            add_field => {
              "level" => "%{[app][level]}"
              "service" => "%{[app][service]}"
              "request_id" => "%{[app][request_id]}"
              "user_id" => "%{[app][user_id]}"
              "duration" => "%{[app][duration]}"
            }
          }
        }
        
        # Enrich with Kubernetes metadata
        if [kubernetes] {
          mutate {
            add_field => {
              "namespace" => "%{[kubernetes][namespace]}"
              "pod" => "%{[kubernetes][pod][name]}"
              "container" => "%{[kubernetes][container][name]}"
              "node" => "%{[kubernetes][node][name]}"
            }
          }
        }
        
        # Add service tier information
        if [service] =~ /api-gateway/ {
          mutate { add_field => { "service_tier" => "gateway" } }
        } else if [service] =~ /content-generation|market-intelligence|publishing-service|multimodal-generator/ {
          mutate { add_field => { "service_tier" => "tier1" } }
        } else if [service] =~ /cover-designer|sales-monitor/ {
          mutate { add_field => { "service_tier" => "tier2" } }
        } else if [service] =~ /personalization|research/ {
          mutate { add_field => { "service_tier" => "tier3" } }
        }
        
        # Parse error logs
        if [level] == "ERROR" or [level] == "FATAL" {
          mutate { add_tag => ["error"] }
          
          # Extract stack traces
          if [app][stack_trace] {
            mutate {
              add_field => { "stack_trace" => "%{[app][stack_trace]}" }
            }
          }
          
          # Classify error types
          if [app][message] =~ /(?i)timeout/ {
            mutate { 
              add_tag => ["timeout-error"]
              add_field => { "error_type" => "timeout" }
            }
          } else if [app][message] =~ /(?i)connection/ {
            mutate { 
              add_tag => ["connection-error"]
              add_field => { "error_type" => "connection" }
            }
          } else if [app][message] =~ /(?i)authentication|unauthorized/ {
            mutate { 
              add_tag => ["auth-error"]
              add_field => { "error_type" => "authentication" }
            }
          } else if [app][message] =~ /(?i)rate.*limit/ {
            mutate { 
              add_tag => ["rate-limit-error"]
              add_field => { "error_type" => "rate_limit" }
            }
          }
        }
        
        # Add timestamp
        date {
          match => [ "[app][@timestamp]", "ISO8601" ]
          target => "@timestamp"
        }
        
        # Clean up temporary fields
        mutate {
          remove_field => ["app", "fields", "agent", "ecs", "host", "input"]
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["https://elasticsearch:9200"]
        index => "application-logs-%{+YYYY.MM.dd}"
        user => "elastic"
        password => "${ELASTIC_PASSWORD}"
        ssl => true
        ssl_certificate_verification => false
        template_name => "application-logs"
        template_pattern => "application-logs-*"
        template_overwrite => true
      }
      
      # Debug output (remove in production)
      if "_grokparsefailure" in [tags] or "_jsonparsefailure" in [tags] {
        stdout { 
          codec => rubydebug {
            metadata => true
          }
        }
      }
    }

  ai-services-logs.conf: |
    input {
      beats {
        port => 5045
        type => "ai-services"
      }
    }
    
    filter {
      if [fields][log_type] == "ai-services" {
        json {
          source => "message"
          target => "ai"
        }
        
        # Extract AI-specific fields
        if [ai] {
          mutate {
            add_field => {
              "level" => "%{[ai][level]}"
              "service" => "%{[ai][service]}"
              "ai_model" => "%{[ai][ai_model]}"
              "tokens_used" => "%{[ai][tokens_used]}"
              "cost_cents" => "%{[ai][cost_cents]}"
              "content_quality" => "%{[ai][content_quality]}"
              "generation_type" => "%{[ai][generation_type]}"
              "prompt_tokens" => "%{[ai][prompt_tokens]}"
              "completion_tokens" => "%{[ai][completion_tokens]}"
            }
          }
        }
        
        # Calculate cost in dollars
        if [cost_cents] and [cost_cents] != "" {
          ruby {
            code => "
              cost_cents = event.get('cost_cents').to_f
              event.set('cost_dollars', cost_cents / 100.0)
            "
          }
        }
        
        # Tag AI services
        mutate { add_tag => ["ai-service"] }
        
        # Identify AI model provider
        if [ai_model] =~ /gpt|openai/ {
          mutate { add_field => { "ai_provider" => "openai" } }
        } else if [ai_model] =~ /claude|anthropic/ {
          mutate { add_field => { "ai_provider" => "anthropic" } }
        }
        
        # Calculate token efficiency
        if [tokens_used] and [duration] and [tokens_used] != "" and [duration] != "" {
          ruby {
            code => "
              tokens = event.get('tokens_used').to_f
              duration_ms = event.get('duration').to_f
              if duration_ms > 0
                tokens_per_second = tokens / (duration_ms / 1000.0)
                event.set('tokens_per_second', tokens_per_second)
              end
            "
          }
        }
        
        # Quality score categorization
        if [content_quality] and [content_quality] != "" {
          ruby {
            code => "
              quality = event.get('content_quality').to_f
              if quality >= 4.5
                event.set('quality_category', 'excellent')
              elsif quality >= 4.0
                event.set('quality_category', 'good')
              elsif quality >= 3.0
                event.set('quality_category', 'acceptable')
              else
                event.set('quality_category', 'poor')
              end
            "
          }
        }
        
        # High cost alert
        if [cost_dollars] and [cost_dollars] != "" {
          ruby {
            code => "
              cost = event.get('cost_dollars').to_f
              if cost > 1.0
                event.set('high_cost_alert', true)
              end
            "
          }
        }
        
        # Long generation time alert
        if [duration] and [duration] != "" {
          ruby {
            code => "
              duration_ms = event.get('duration').to_f
              if duration_ms > 60000  # 60 seconds
                event.set('long_generation_alert', true)
              end
            "
          }
        }
        
        date {
          match => [ "[ai][@timestamp]", "ISO8601" ]
          target => "@timestamp"
        }
        
        mutate {
          remove_field => ["ai", "fields", "agent", "ecs", "host", "input"]
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["https://elasticsearch:9200"]
        index => "ai-services-logs-%{+YYYY.MM.dd}"
        user => "elastic"
        password => "${ELASTIC_PASSWORD}"
        ssl => true
        ssl_certificate_verification => false
      }
      
      # Send high-value logs to different index for faster search
      if [high_cost_alert] or [long_generation_alert] or [quality_category] == "poor" {
        elasticsearch {
          hosts => ["https://elasticsearch:9200"]
          index => "ai-alerts-%{+YYYY.MM.dd}"
          user => "elastic"
          password => "${ELASTIC_PASSWORD}"
          ssl => true
          ssl_certificate_verification => false
        }
      }
    }

  system-logs.conf: |
    input {
      beats {
        port => 5046
        type => "system"
      }
    }
    
    filter {
      if [fields][log_type] == "system" {
        # Parse Kubernetes logs
        if [kubernetes] {
          mutate {
            add_field => {
              "namespace" => "%{[kubernetes][namespace]}"
              "pod" => "%{[kubernetes][pod][name]}"
              "container" => "%{[kubernetes][container][name]}"
              "node" => "%{[kubernetes][node][name]}"
            }
          }
        }
        
        # Parse syslog format
        grok {
          match => { 
            "message" => "%{SYSLOGTIMESTAMP:timestamp} %{IPORHOST:host} %{DATA:program}(?:\[%{POSINT:pid}\])?: %{GREEDYDATA:message}" 
          }
          overwrite => [ "message" ]
        }
        
        # Parse Kubernetes events
        if [message] =~ /Events:/ {
          mutate { add_tag => ["k8s-event"] }
          
          grok {
            match => { 
              "message" => "Events:.*%{DATA:event_type}.*%{DATA:reason}.*%{GREEDYDATA:event_message}" 
            }
          }
        }
        
        # Parse container runtime logs
        if [message] =~ /containerd|docker/ {
          mutate { add_tag => ["container-runtime"] }
        }
        
        # Parse storage-related logs
        if [message] =~ /disk|volume|mount/ {
          mutate { add_tag => ["storage"] }
        }
        
        # Parse network-related logs
        if [message] =~ /network|iptables|dns/ {
          mutate { add_tag => ["network"] }
        }
        
        date {
          match => [ "timestamp", "MMM dd HH:mm:ss", "MMM  d HH:mm:ss" ]
          target => "@timestamp"
        }
        
        mutate {
          remove_field => ["fields", "agent", "ecs", "host", "input", "timestamp"]
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["https://elasticsearch:9200"]
        index => "system-logs-%{+YYYY.MM.dd}"
        user => "elastic"
        password => "${ELASTIC_PASSWORD}"
        ssl => true
        ssl_certificate_verification => false
      }
    }

  security-logs.conf: |
    input {
      beats {
        port => 5047
        type => "security"
      }
    }
    
    filter {
      if [fields][log_type] == "security" {
        json {
          source => "message"
          target => "security"
        }
        
        if [security] {
          mutate {
            add_field => {
              "event_type" => "%{[security][event_type]}"
              "user_id" => "%{[security][user_id]}"
              "source_ip" => "%{[security][source_ip]}"
              "user_agent" => "%{[security][user_agent]}"
              "resource_accessed" => "%{[security][resource_accessed]}"
              "action" => "%{[security][action]}"
            }
          }
        }
        
        # Detect suspicious patterns
        if [message] =~ /(?i)failed.*login|authentication.*failed/ {
          mutate {
            add_tag => ["failed-auth"]
            add_field => {
              "security_event" => "authentication_failure"
              "risk_score" => 3
            }
          }
        }
        
        # Multiple failed attempts from same IP
        if [source_ip] and "failed-auth" in [tags] {
          aggregate {
            task_id => "%{source_ip}"
            code => "
              map['failed_attempts'] ||= 0
              map['failed_attempts'] += 1
              event.set('failed_attempts_from_ip', map['failed_attempts'])
            "
            push_map_as_event_on_timeout => true
            timeout_task_id_field => "source_ip"
            timeout => 300
          }
          
          if [failed_attempts_from_ip] and [failed_attempts_from_ip] > 5 {
            mutate {
              add_tag => ["potential-brute-force"]
              update => {
                "risk_score" => 8
                "security_event" => "potential_brute_force_attack"
              }
            }
          }
        }
        
        # Rate limiting events
        if [message] =~ /(?i)rate.*limit.*exceeded/ {
          mutate {
            add_tag => ["rate-limit-violation"]
            add_field => {
              "security_event" => "rate_limit_exceeded"
              "risk_score" => 2
            }
          }
        }
        
        # Suspicious user agents
        if [user_agent] =~ /(?i)bot|crawler|scanner/ and ![user_agent] =~ /(?i)googlebot|bingbot/ {
          mutate {
            add_tag => ["suspicious-user-agent"]
            add_field => {
              "security_event" => "suspicious_user_agent"
              "risk_score" => 1
            }
          }
        }
        
        # Privilege escalation attempts
        if [message] =~ /(?i)sudo|su |admin|root/ and [action] =~ /(?i)elevate|escalate/ {
          mutate {
            add_tag => ["privilege-escalation"]
            add_field => {
              "security_event" => "privilege_escalation_attempt"
              "risk_score" => 7
            }
          }
        }
        
        # API abuse patterns
        if [resource_accessed] =~ /(?i)api/ and [action] =~ /(?i)POST|PUT|DELETE/ {
          aggregate {
            task_id => "%{user_id}_%{source_ip}"
            code => "
              map['api_calls'] ||= 0
              map['api_calls'] += 1
              event.set('api_calls_per_user', map['api_calls'])
            "
            push_map_as_event_on_timeout => true
            timeout_task_id_field => "user_session"
            timeout => 60
          }
          
          if [api_calls_per_user] and [api_calls_per_user] > 100 {
            mutate {
              add_tag => ["api-abuse"]
              add_field => {
                "security_event" => "api_abuse_pattern"
                "risk_score" => 5
              }
            }
          }
        }
        
        # GeoIP enrichment for source IPs
        if [source_ip] {
          geoip {
            source => "source_ip"
            target => "geoip"
          }
          
          # Flag foreign countries (customize as needed)
          if [geoip][country_name] and ![geoip][country_name] =~ /(?i)united states|canada/ {
            mutate {
              add_tag => ["foreign-access"]
              update => {
                "risk_score" => "%{risk_score} + 1"
              }
            }
          }
        }
        
        date {
          match => [ "[security][@timestamp]", "ISO8601" ]
          target => "@timestamp"
        }
        
        mutate {
          remove_field => ["security", "fields", "agent", "ecs", "host", "input"]
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["https://elasticsearch:9200"]
        index => "security-logs-%{+YYYY.MM.dd}"
        user => "elastic"
        password => "${ELASTIC_PASSWORD}"
        ssl => true
        ssl_certificate_verification => false
      }
      
      # Send high-risk security events to separate index
      if [risk_score] and [risk_score] >= 5 {
        elasticsearch {
          hosts => ["https://elasticsearch:9200"]
          index => "security-alerts-%{+YYYY.MM.dd}"
          user => "elastic"
          password => "${ELASTIC_PASSWORD}"
          ssl => true
          ssl_certificate_verification => false
        }
      }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: logstash-credentials
  namespace: logging
type: Opaque
data:
  # Default credentials (change in production)
  elastic-password: ZWxhc3RpYw==  # elastic
  logstash-system-password: bG9nc3Rhc2hfc3lzdGVt  # logstash_system

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: logging
  labels:
    app: logstash
spec:
  replicas: 3
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9600"
        prometheus.io/path: "/_node/stats"
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: logstash
        image: docker.elastic.co/logstash/logstash:8.11.1
        env:
        - name: LS_JAVA_OPTS
          value: "-Xms2g -Xmx2g"
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: logstash-credentials
              key: elastic-password
        - name: LOGSTASH_SYSTEM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: logstash-credentials
              key: logstash-system-password
        ports:
        - containerPort: 5044
          name: beats
        - containerPort: 5045
          name: ai-services
        - containerPort: 5046
          name: system
        - containerPort: 5047
          name: security
        - containerPort: 9600
          name: http
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        volumeMounts:
        - name: config
          mountPath: /usr/share/logstash/config/logstash.yml
          subPath: logstash.yml
        - name: config
          mountPath: /usr/share/logstash/config/pipelines.yml
          subPath: pipelines.yml
        - name: pipelines
          mountPath: /usr/share/logstash/pipeline
        - name: data
          mountPath: /usr/share/logstash/data
        livenessProbe:
          httpGet:
            path: /_node/stats
            port: 9600
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /_node/stats
            port: 9600
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: logstash-config
      - name: pipelines
        configMap:
          name: logstash-pipelines
      - name: data
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: logstash
  namespace: logging
  labels:
    app: logstash
spec:
  selector:
    app: logstash
  ports:
  - name: beats
    port: 5044
    targetPort: 5044
  - name: ai-services
    port: 5045
    targetPort: 5045
  - name: system
    port: 5046
    targetPort: 5046
  - name: security
    port: 5047
    targetPort: 5047
  - name: http
    port: 9600
    targetPort: 9600
  type: ClusterIP

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: logstash
  namespace: logging
  labels:
    app: logstash
spec:
  selector:
    matchLabels:
      app: logstash
  endpoints:
  - port: http
    interval: 30s
    path: /_node/stats
    scrapeTimeout: 10s

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: logstash-dr
  namespace: logging
spec:
  host: logstash.logging.svc.cluster.local
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 50
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 25
        http2MaxRequests: 50
        maxRequestsPerConnection: 5
        maxRetries: 3
        timeout: 120s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50