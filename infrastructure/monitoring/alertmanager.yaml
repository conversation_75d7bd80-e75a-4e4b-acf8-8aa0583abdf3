apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'localhost:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '${SMTP_PASSWORD}'
      
    templates:
      - '/etc/alertmanager/templates/*.tmpl'

    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'web.hook'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
        routes:
        - match:
            alertname: ServiceDown
          receiver: 'service-down-alerts'
        - match:
            alertname: PublishingServiceFailure
          receiver: 'publishing-alerts'
      - match:
          severity: warning
        receiver: 'warning-alerts'
      - match:
          service: api-gateway
        receiver: 'gateway-alerts'

    receivers:
    - name: 'web.hook'
      webhook_configs:
      - url: 'http://webhook-receiver:9093/webhook'
        send_resolved: true

    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ .GroupLabels.alertname }} in {{ .GroupLabels.service }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          Started: {{ .StartsAt }}
          {{ end }}
        headers:
          Priority: 'high'
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#ops-alerts'
        title: 'CRITICAL Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          Service: {{ .Labels.service }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
        send_resolved: true

    - name: 'service-down-alerts'
      email_configs:
      - to: '<EMAIL>, <EMAIL>'
        subject: 'SERVICE DOWN: {{ .GroupLabels.service }}'
        body: |
          URGENT: Service {{ .GroupLabels.service }} is DOWN!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}
          
          Please investigate immediately.
      pagerduty_configs:
      - routing_key: '${PAGERDUTY_INTEGRATION_KEY}'
        description: 'Service {{ .GroupLabels.service }} is down'
        severity: 'critical'

    - name: 'publishing-alerts'
      email_configs:
      - to: '<EMAIL>, <EMAIL>'
        subject: 'PUBLISHING FAILURE: Critical Issue Detected'
        body: |
          PUBLISHING SYSTEM ALERT!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}
          
          This may impact book publishing operations.
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#publishing-alerts'
        title: 'Publishing System Alert'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

    - name: 'warning-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'WARNING: {{ .GroupLabels.alertname }}'
        body: |
          Warning alert triggered:
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Started: {{ .StartsAt }}
          {{ end }}

    - name: 'gateway-alerts'
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#gateway-alerts'
        title: 'API Gateway Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

    inhibit_rules:
    - source_match:
        severity: 'critical'
      target_match:
        severity: 'warning'
      equal: ['alertname', 'service']

  notification.tmpl: |
    {{ define "email.default.subject" }}
    [{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .GroupLabels.SortedPairs.Values | join " " }} {{ if gt (len .CommonLabels) (len .GroupLabels) }}({{ with .CommonLabels.Remove .GroupLabels.Names }}{{ .Values | join " " }}{{ end }}){{ end }}
    {{ end }}

    {{ define "email.default.html" }}
    <!DOCTYPE html>
    <html>
    <head>
    <meta charset="UTF-8">
    <style>
    body { font-family: Arial, sans-serif; }
    .alert { margin: 10px 0; padding: 10px; border-left: 4px solid #d9534f; }
    .resolved { border-left-color: #5cb85c; }
    .firing { border-left-color: #d9534f; }
    .warning { border-left-color: #f0ad4e; }
    .critical { border-left-color: #d9534f; background-color: #f2dede; }
    </style>
    </head>
    <body>
    <h2>Publish AI Alert Summary</h2>
    
    {{ if gt (len .Alerts.Firing) 0 }}
    <h3>Firing Alerts ({{ .Alerts.Firing | len }})</h3>
    {{ range .Alerts.Firing }}
    <div class="alert firing {{ .Labels.severity }}">
    <strong>{{ .Annotations.summary }}</strong><br>
    Description: {{ .Annotations.description }}<br>
    Service: {{ .Labels.service }}<br>
    Started: {{ .StartsAt.Format "2006-01-02 15:04:05" }}<br>
    </div>
    {{ end }}
    {{ end }}
    
    {{ if gt (len .Alerts.Resolved) 0 }}
    <h3>Resolved Alerts ({{ .Alerts.Resolved | len }})</h3>
    {{ range .Alerts.Resolved }}
    <div class="alert resolved">
    <strong>{{ .Annotations.summary }}</strong><br>
    Description: {{ .Annotations.description }}<br>
    Service: {{ .Labels.service }}<br>
    Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05" }}<br>
    </div>
    {{ end }}
    {{ end }}
    
    </body>
    </html>
    {{ end }}

---
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-secrets
  namespace: monitoring
type: Opaque
data:
  # SMTP password (base64 encoded placeholder)
  smtp-password: c210cC1wYXNzd29yZA==
  # Slack webhook URL (base64 encoded placeholder)
  slack-webhook-url: aHR0cHM6Ly9ob29rcy5zbGFjay5jb20vc2VydmljZXMvLi4u
  # PagerDuty integration key (base64 encoded placeholder)
  pagerduty-key: cGFnZXJkdXR5LWludGVncmF0aW9uLWtleQ==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app: alertmanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.26.0
        args:
          - "--config.file=/etc/alertmanager/alertmanager.yml"
          - "--storage.path=/alertmanager"
          - "--web.external-url=http://alertmanager.publish-ai.local"
          - "--web.listen-address=0.0.0.0:9093"
          - "--cluster.listen-address=0.0.0.0:9094"
          - "--log.level=info"
        env:
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: smtp-password
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: slack-webhook-url
        - name: PAGERDUTY_INTEGRATION_KEY
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: pagerduty-key
        ports:
        - containerPort: 9093
          name: web
        - containerPort: 9094
          name: cluster
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: config-volume
          mountPath: /etc/alertmanager
        - name: alertmanager-storage-volume
          mountPath: /alertmanager
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9093
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9093
          initialDelaySeconds: 10
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: alertmanager-config
      - name: alertmanager-storage-volume
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app: alertmanager
spec:
  type: ClusterIP
  ports:
  - port: 9093
    targetPort: 9093
    name: web
  - port: 9094
    targetPort: 9094
    name: cluster
  selector:
    app: alertmanager

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager-external
  namespace: monitoring
  labels:
    app: alertmanager
spec:
  type: LoadBalancer
  ports:
  - port: 9093
    targetPort: 9093
    name: web
  selector:
    app: alertmanager

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: webhook-receiver
  namespace: monitoring
  labels:
    app: webhook-receiver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: webhook-receiver
  template:
    metadata:
      labels:
        app: webhook-receiver
    spec:
      containers:
      - name: webhook-receiver
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: webhook-config
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: webhook-config
        configMap:
          name: webhook-receiver-config

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: webhook-receiver-config
  namespace: monitoring
data:
  default.conf: |
    server {
        listen 80;
        server_name _;
        
        location /webhook {
            access_log /var/log/nginx/webhook.log;
            return 200 "Webhook received\n";
            add_header Content-Type text/plain;
        }
        
        location /health {
            access_log off;
            return 200 "OK\n";
            add_header Content-Type text/plain;
        }
    }

---
apiVersion: v1
kind: Service
metadata:
  name: webhook-receiver
  namespace: monitoring
  labels:
    app: webhook-receiver
spec:
  type: ClusterIP
  ports:
  - port: 9093
    targetPort: 80
    name: webhook
  selector:
    app: webhook-receiver