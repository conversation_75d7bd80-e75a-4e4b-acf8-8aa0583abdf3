# Publish AI Monitoring Stack

A comprehensive monitoring, observability, and alerting solution for the Publish AI microservices platform.

## Overview

The monitoring stack provides full observability across all microservices with:

- **Metrics Collection**: Prometheus for metrics scraping and storage
- **Visualization**: Grafana with pre-built dashboards  
- **Distributed Tracing**: <PERSON><PERSON><PERSON> for request flow analysis
- **Alerting**: Alertmanager with multi-channel notifications
- **Log Aggregation**: Fluent Bit for centralized logging

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Monitoring Stack                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  Grafana    │    │ Prometheus  │    │    <PERSON><PERSON><PERSON>   │     │
│  │   :3000     │    │    :9090    │    │   :16686    │     │
│  │             │◄───┤             │    │             │     │
│  │ Dashboards  │    │  Metrics    │    │   Tracing   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │Alertmanager │◄───┤ OTEL        │    │ Fluent Bit  │     │
│  │   :9093     │    │ Collector   │    │             │     │
│  │             │    │   :4317     │    │    Logs     │     │
│  │   Alerts    │    │             │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    Microservices                            │
│                                                             │
│  API Gateway  │  Service Discovery  │  Event Bus           │
│  Research     │  Personalization    │  Cover Designer      │
│  Sales Mon.   │  Market Intel.      │  Content Gen.        │
│  Publishing   │  Multimodal Gen.    │                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Components

### Prometheus (Metrics)
- **Purpose**: Metrics collection, storage, and querying
- **Port**: 9090
- **Features**:
  - Automatic service discovery for all microservices
  - Custom metrics for AI operations, circuit breakers, rate limiting
  - 15-day data retention
  - Alert rule evaluation

### Grafana (Visualization)
- **Purpose**: Metrics visualization and dashboards
- **Port**: 3000
- **Credentials**: admin/admin123
- **Features**:
  - Pre-built dashboards for system overview, API Gateway, and AI services
  - Custom alerting rules
  - Multi-datasource support
  - Role-based access control

### Jaeger (Distributed Tracing)
- **Purpose**: Request tracing across microservices
- **Port**: 16686
- **Features**:
  - End-to-end request flow visualization
  - Performance bottleneck identification
  - Dependency mapping
  - Error rate analysis by service

### Alertmanager (Alerting)
- **Purpose**: Alert management and notification routing
- **Port**: 9093
- **Features**:
  - Multi-channel notifications (email, Slack, PagerDuty)
  - Alert grouping and deduplication
  - Escalation policies
  - Silence management

### OpenTelemetry Collector
- **Purpose**: Telemetry data collection and processing
- **Ports**: 4317 (gRPC), 4318 (HTTP)
- **Features**:
  - Metrics and traces collection
  - Data transformation and routing
  - Backend-agnostic telemetry

### Fluent Bit (Log Aggregation)
- **Purpose**: Log collection and forwarding
- **Features**:
  - Kubernetes log collection
  - Log parsing and enrichment
  - Multiple output destinations

## Installation

### Prerequisites
- Kubernetes cluster with admin access
- kubectl configured
- 4GB+ available memory
- 10GB+ available storage

### Quick Deployment
```bash
# Deploy complete monitoring stack
./deploy-monitoring.sh

# Verify deployment
./deploy-monitoring.sh verify

# Remove monitoring stack
./deploy-monitoring.sh cleanup
```

### Manual Deployment
```bash
# Create namespace
kubectl create namespace monitoring

# Deploy components
kubectl apply -f prometheus.yaml
kubectl apply -f grafana.yaml
kubectl apply -f jaeger.yaml
kubectl apply -f alertmanager.yaml
```

## Configuration

### Prometheus Configuration
Key scrape jobs configured:
- **API Gateway**: Metrics on gateway performance, rate limiting, circuit breakers
- **Infrastructure Services**: Service discovery, event bus, database metrics
- **Tier 1 Services**: Market intelligence, content generation, publishing
- **Tier 2 Services**: Cover designer, sales monitor, multimodal generator
- **Tier 3 Services**: Research, personalization
- **System Metrics**: Node metrics, cAdvisor, Kubernetes API

### Alert Rules
Pre-configured alerts:
- **Service Down**: Critical alert when service is unavailable
- **High Error Rate**: Warning when error rate exceeds 5%
- **High Response Time**: Warning when 95th percentile exceeds 2s
- **Resource Usage**: Memory/CPU usage above thresholds
- **AI Model Failures**: Specific alerts for AI service issues
- **Publishing Failures**: Critical alerts for publishing system issues

### Grafana Dashboards
Three pre-built dashboards:
1. **System Overview**: Overall platform health and performance
2. **API Gateway**: Detailed gateway metrics and routing analysis
3. **AI Services**: AI-specific metrics and publishing performance

## Metrics Reference

### Standard HTTP Metrics
```prometheus
# Request rate
rate(http_requests_total[5m])

# Error rate
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

# Response time
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
```

### API Gateway Metrics
```prometheus
# Gateway-specific metrics
gateway_requests_total
gateway_request_duration_seconds
circuit_breaker_state
circuit_breaker_trips_total
rate_limit_exceeded_total
load_balancer_requests_total
```

### AI Service Metrics
```prometheus
# AI model metrics
ai_model_requests_total
ai_model_failures_total
content_generation_duration_seconds
publishing_attempts_total
research_api_calls_total
```

### Resource Metrics
```prometheus
# Container metrics
container_memory_usage_bytes
container_cpu_usage_seconds_total
container_spec_memory_limit_bytes

# Node metrics
node_memory_MemAvailable_bytes
node_cpu_seconds_total
node_filesystem_size_bytes
```

## Alert Configuration

### Email Notifications
Configure SMTP settings in `alertmanager.yaml`:
```yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'
```

### Slack Integration
Set up Slack webhook in secrets:
```bash
# Update slack webhook URL
kubectl create secret generic alertmanager-secrets \
  --from-literal=slack-webhook-url="https://hooks.slack.com/services/..." \
  -n monitoring
```

### PagerDuty Integration
Configure PagerDuty integration key:
```bash
# Update PagerDuty key
kubectl create secret generic alertmanager-secrets \
  --from-literal=pagerduty-key="your-pagerduty-integration-key" \
  -n monitoring
```

## Access and Usage

### Prometheus UI
```bash
# Port forward
kubectl port-forward svc/prometheus 9090:9090 -n monitoring

# Access at http://localhost:9090
# Query examples:
# - up{job=~".*-service"}
# - rate(http_requests_total[5m])
# - histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
```

### Grafana UI
```bash
# Port forward
kubectl port-forward svc/grafana 3000:3000 -n monitoring

# Access at http://localhost:3000
# Default credentials: admin/admin123
# Pre-built dashboards available in default folder
```

### Jaeger UI
```bash
# Port forward
kubectl port-forward svc/jaeger-query 16686:16686 -n monitoring

# Access at http://localhost:16686
# Search traces by service, operation, or tags
```

### Alertmanager UI
```bash
# Port forward
kubectl port-forward svc/alertmanager 9093:9093 -n monitoring

# Access at http://localhost:9093
# View active alerts and silences
```

## Troubleshooting

### Common Issues

#### Prometheus Not Scraping Services
```bash
# Check service annotations
kubectl get pod <pod-name> -o yaml | grep -A 5 annotations

# Verify prometheus config
kubectl get configmap prometheus-config -n monitoring -o yaml

# Check prometheus targets
# Go to Prometheus UI -> Status -> Targets
```

#### Grafana Dashboard Not Loading
```bash
# Check Grafana logs
kubectl logs deployment/grafana -n monitoring

# Verify datasource connection
# Go to Grafana UI -> Configuration -> Data Sources

# Check dashboard JSON syntax
kubectl get configmap grafana-dashboard-overview -n monitoring -o yaml
```

#### Jaeger No Traces
```bash
# Check if services are instrumented
kubectl logs <service-pod> | grep -i trace

# Verify OTEL collector
kubectl logs deployment/otel-collector -n monitoring

# Check Jaeger collector
kubectl logs deployment/jaeger-collector -n monitoring
```

#### Alerts Not Firing
```bash
# Check alert rules
kubectl get configmap prometheus-config -n monitoring -o yaml

# Verify Alertmanager config
kubectl logs deployment/alertmanager -n monitoring

# Test alert rule
# Go to Prometheus UI -> Alerts
```

### Performance Tuning

#### Memory Optimization
```yaml
# Prometheus memory settings
args:
  - "--storage.tsdb.retention.time=15d"
  - "--storage.tsdb.wal-compression"
resources:
  limits:
    memory: 4Gi  # Increase for larger deployments
```

#### Storage Optimization
```yaml
# Use persistent volumes for production
volumeClaimTemplates:
- metadata:
    name: prometheus-storage
  spec:
    accessModes: ["ReadWriteOnce"]
    resources:
      requests:
        storage: 50Gi
```

## Monitoring Best Practices

### Service Instrumentation
1. **Add Prometheus annotations** to all service deployments
2. **Implement health check endpoints** for all services
3. **Use structured logging** with consistent formats
4. **Add trace instrumentation** for request flows

### Alert Design
1. **Use symptom-based alerts** rather than cause-based
2. **Set appropriate thresholds** based on SLA requirements
3. **Include runbook links** in alert annotations
4. **Test alert notification channels** regularly

### Dashboard Design
1. **Use RED method** (Rate, Errors, Duration) for services
2. **Show both current state and trends**
3. **Include relevant context** and drill-down capabilities
4. **Optimize for different audiences** (ops, dev, business)

### Retention and Storage
1. **Set appropriate retention periods** for different data types
2. **Use downsampling** for long-term trend analysis
3. **Archive important alerts** and incidents
4. **Regular backup** of configuration and dashboards

## Security Considerations

### Access Control
- **RBAC**: Implement role-based access for Grafana
- **Network Policies**: Restrict inter-pod communication
- **TLS**: Enable TLS for all monitoring endpoints
- **Authentication**: Use strong passwords and API keys

### Data Privacy
- **Scrub sensitive data** from metrics and logs
- **Limit metric cardinality** to prevent DoS
- **Encrypt communication** between components
- **Regular security updates** for all images

## Scaling and High Availability

### Horizontal Scaling
```yaml
# Scale Prometheus for high load
replicas: 3
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchLabels:
          app: prometheus
      topologyKey: kubernetes.io/hostname
```

### Data Persistence
```yaml
# Use persistent volumes
volumeClaimTemplates:
- metadata:
    name: prometheus-data
  spec:
    accessModes: ["ReadWriteOnce"]
    resources:
      requests:
        storage: 100Gi
    storageClassName: fast-ssd
```

## Integration with CI/CD

### Automated Monitoring Setup
```yaml
# Helm chart integration
helm upgrade --install monitoring ./monitoring-chart \
  --namespace monitoring \
  --create-namespace \
  --values values-production.yaml
```

### Monitoring as Code
- **Version control** all monitoring configurations
- **Automated testing** of alert rules and dashboards
- **GitOps workflow** for monitoring updates
- **Documentation** as part of service development

---

For additional support and advanced configuration, refer to the individual component documentation:
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Jaeger Documentation](https://www.jaegertracing.io/docs/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)