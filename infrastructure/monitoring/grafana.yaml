apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
data:
  grafana.ini: |
    [server]
    domain = grafana.publish-ai.local
    root_url = %(protocol)s://%(domain)s:%(http_port)s/
    serve_from_sub_path = false

    [database]
    type = sqlite3
    path = /var/lib/grafana/grafana.db

    [auth]
    disable_login_form = false

    [auth.anonymous]
    enabled = false

    [security]
    admin_user = admin
    admin_password = ${GRAFANA_ADMIN_PASSWORD}
    secret_key = ${GRAFANA_SECRET_KEY}

    [users]
    allow_sign_up = false
    auto_assign_org = true
    auto_assign_org_role = Viewer

    [alerting]
    enabled = true

    [unified_alerting]
    enabled = true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: monitoring
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus:9090
        isDefault: true
        editable: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: monitoring
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
      - name: 'default'
        orgId: 1
        folder: ''
        type: file
        disableDeletion: false
        updateIntervalSeconds: 30
        allowUiUpdates: true
        options:
          path: /etc/grafana/provisioning/dashboards

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-overview
  namespace: monitoring
data:
  publish-ai-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Publish AI - System Overview",
        "tags": ["publish-ai", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=~\".*-service\"} == 1",
                "legendFormat": "{{service}}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (service)",
                "legendFormat": "{{service}}"
              }
            ],
            "yAxes": [
              {
                "label": "Requests/sec",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service) * 100",
                "legendFormat": "{{service}}"
              }
            ],
            "yAxes": [
              {
                "label": "Error %",
                "min": 0,
                "max": 100
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Response Time (95th percentile)",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le))",
                "legendFormat": "{{service}}"
              }
            ],
            "yAxes": [
              {
                "label": "Seconds",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 5,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(container_memory_usage_bytes{container!=\"POD\"}) by (pod) / 1024 / 1024",
                "legendFormat": "{{pod}}"
              }
            ],
            "yAxes": [
              {
                "label": "MB",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
          },
          {
            "id": 6,
            "title": "CPU Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(container_cpu_usage_seconds_total{container!=\"POD\"}[5m])) by (pod) * 100",
                "legendFormat": "{{pod}}"
              }
            ],
            "yAxes": [
              {
                "label": "CPU %",
                "min": 0,
                "max": 100
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "10s"
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-gateway
  namespace: monitoring
data:
  api-gateway-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "API Gateway - Detailed Metrics",
        "tags": ["publish-ai", "api-gateway"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Gateway Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(gateway_requests_total[5m])) by (method, status)",
                "legendFormat": "{{method}} - {{status}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Circuit Breaker Status",
            "type": "stat",
            "targets": [
              {
                "expr": "circuit_breaker_state",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Rate Limit Hits",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(rate_limit_exceeded_total[5m])",
                "legendFormat": "Rate Limit Exceeded"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Load Balancer Distribution",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum(rate(load_balancer_requests_total[5m])) by (backend)",
                "legendFormat": "{{backend}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "10s"
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-ai-services
  namespace: monitoring
data:
  ai-services-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "AI Services - Performance Metrics",
        "tags": ["publish-ai", "ai-services"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "AI Model Request Success Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(ai_model_requests_total{status=\"success\"}[5m])) by (service, model) / sum(rate(ai_model_requests_total[5m])) by (service, model) * 100",
                "legendFormat": "{{service}} - {{model}}"
              }
            ],
            "yAxes": [
              {
                "label": "Success %",
                "min": 0,
                "max": 100
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Content Generation Latency",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(content_generation_duration_seconds_bucket[5m])) by (service, le))",
                "legendFormat": "{{service}} - 95th percentile"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Publishing Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(publishing_attempts_total{status=\"success\"}[5m])) / sum(rate(publishing_attempts_total[5m])) * 100",
                "legendFormat": "Success Rate"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 80},
                    {"color": "green", "value": 95}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Research API Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(research_api_calls_total[5m])) by (api_provider)",
                "legendFormat": "{{api_provider}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "10s"
      }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: monitoring
type: Opaque
data:
  # Default password: admin123 (base64 encoded)
  admin-password: YWRtaW4xMjM=
  # Secret key for sessions (base64 encoded)
  secret-key: Z3JhZmFuYS1zZWNyZXQta2V5LWZvci1zZXNzaW9ucy1hbmQtY29va2llcw==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.2.2
        ports:
        - containerPort: 3000
        env:
        - name: GRAFANA_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GRAFANA_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret-key
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        volumeMounts:
        - name: grafana-config
          mountPath: /etc/grafana
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboard-overview
          mountPath: /etc/grafana/provisioning/dashboards/overview
        - name: grafana-dashboard-gateway
          mountPath: /etc/grafana/provisioning/dashboards/gateway
        - name: grafana-dashboard-ai-services
          mountPath: /etc/grafana/provisioning/dashboards/ai-services
        - name: grafana-storage
          mountPath: /var/lib/grafana
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 60
          timeoutSeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 30
          periodSeconds: 5
      volumes:
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboard-overview
        configMap:
          name: grafana-dashboard-overview
      - name: grafana-dashboard-gateway
        configMap:
          name: grafana-dashboard-gateway
      - name: grafana-dashboard-ai-services
        configMap:
          name: grafana-dashboard-ai-services
      - name: grafana-storage
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app: grafana
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    name: grafana
  selector:
    app: grafana

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-external
  namespace: monitoring
  labels:
    app: grafana
spec:
  type: LoadBalancer
  ports:
  - port: 3000
    targetPort: 3000
    name: grafana
  selector:
    app: grafana