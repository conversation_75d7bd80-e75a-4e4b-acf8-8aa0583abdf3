apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'publish-ai-cluster'
        environment: 'production'

    rule_files:
      - "*.rules.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    scrape_configs:
      # Prometheus itself
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https

      # Kubernetes nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)

      # Kubernetes node cadvisor
      - job_name: 'kubernetes-cadvisor'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        metrics_path: /metrics/cadvisor
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)

      # API Gateway Service
      - job_name: 'api-gateway'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - api-gateway
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: api-gateway
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      # Infrastructure Services
      - job_name: 'service-discovery'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - infrastructure
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: service-discovery
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'event-bus'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - infrastructure
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: event-bus
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      # Tier 3 Services
      - job_name: 'research-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier3-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: research-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'personalization-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier3-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: personalization-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      # Tier 2 Services
      - job_name: 'cover-designer-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier2-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: cover-designer-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'sales-monitor-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier2-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: sales-monitor-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'multimodal-generator-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier2-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: multimodal-generator-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      # Tier 1 Services
      - job_name: 'market-intelligence-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier1-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: market-intelligence-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'content-generation-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier1-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: content-generation-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      - job_name: 'publishing-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - tier1-services
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: publishing-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
        metrics_path: /metrics

      # Redis instances
      - job_name: 'redis'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - infrastructure
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: redis
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: metrics
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod

      # PostgreSQL/Supabase metrics
      - job_name: 'postgres'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - infrastructure
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: postgres
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: metrics
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod

  alert.rules.yml: |
    groups:
      - name: publish-ai-alerts
        rules:
          # Service availability alerts
          - alert: ServiceDown
            expr: up{job=~".*-service"} == 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Service {{ $labels.service }} is down"
              description: "Service {{ $labels.service }} in namespace {{ $labels.namespace }} has been down for more than 5 minutes."

          # High error rate alerts
          - alert: HighErrorRate
            expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High error rate on {{ $labels.service }}"
              description: "Error rate is above 5% for service {{ $labels.service }}"

          # High response time alerts
          - alert: HighResponseTime
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High response time on {{ $labels.service }}"
              description: "95th percentile response time is above 2 seconds for service {{ $labels.service }}"

          # Resource alerts
          - alert: HighMemoryUsage
            expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage on {{ $labels.pod }}"
              description: "Memory usage is above 90% for pod {{ $labels.pod }}"

          - alert: HighCPUUsage
            expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage on {{ $labels.pod }}"
              description: "CPU usage is above 80% for pod {{ $labels.pod }}"

          # API Gateway specific alerts
          - alert: GatewayHighLatency
            expr: histogram_quantile(0.95, rate(gateway_request_duration_seconds_bucket[5m])) > 1
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "API Gateway high latency"
              description: "API Gateway 95th percentile latency is above 1 second"

          - alert: CircuitBreakerTripped
            expr: increase(circuit_breaker_trips_total[5m]) > 0
            for: 1m
            labels:
              severity: warning
            annotations:
              summary: "Circuit breaker tripped for {{ $labels.service }}"
              description: "Circuit breaker has been tripped for service {{ $labels.service }}"

          - alert: RateLimitExceeded
            expr: increase(rate_limit_exceeded_total[5m]) > 10
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "Rate limit exceeded"
              description: "Rate limit has been exceeded more than 10 times in the last 5 minutes"

          # AI service specific alerts
          - alert: AIModelFailure
            expr: increase(ai_model_failures_total[5m]) > 5
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "AI model failures detected"
              description: "AI model has failed more than 5 times in the last 5 minutes"

          - alert: PublishingServiceFailure
            expr: increase(publishing_failures_total[5m]) > 2
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Publishing service failures"
              description: "Publishing service has failed more than 2 times in the last 5 minutes"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: monitoring

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.48.0
        args:
          - "--config.file=/etc/prometheus/prometheus.yml"
          - "--storage.tsdb.path=/prometheus/"
          - "--web.console.libraries=/etc/prometheus/console_libraries"
          - "--web.console.templates=/etc/prometheus/consoles"
          - "--storage.tsdb.retention.time=15d"
          - "--web.enable-lifecycle"
          - "--web.enable-admin-api"
          - "--storage.tsdb.wal-compression"
        ports:
        - containerPort: 9090
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: config-volume
          mountPath: /etc/prometheus/
        - name: prometheus-storage-volume
          mountPath: /prometheus/
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          timeoutSeconds: 30
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 30
          timeoutSeconds: 30
      volumes:
      - name: config-volume
        configMap:
          defaultMode: 420
          name: prometheus-config
      - name: prometheus-storage-volume
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: monitoring
  labels:
    app: prometheus
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    name: prometheus
  selector:
    app: prometheus

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-external
  namespace: monitoring
  labels:
    app: prometheus
spec:
  type: LoadBalancer
  ports:
  - port: 9090
    targetPort: 9090
    name: prometheus
  selector:
    app: prometheus