#!/bin/bash

# Publish AI Monitoring Stack Deployment Script
# Deploys Prometheus, Grafana, Jaeger, and Alertmanager

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="monitoring"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if running as cluster admin
    if ! kubectl auth can-i create clusterrole &> /dev/null; then
        error "Insufficient permissions. Please run with cluster admin privileges"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Create namespace
create_namespace() {
    log "Creating monitoring namespace..."
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        info "Namespace $NAMESPACE already exists"
    else
        kubectl create namespace $NAMESPACE
        log "Created namespace $NAMESPACE"
    fi
    
    # Label namespace for Istio injection if Istio is available
    if kubectl get crd gateways.networking.istio.io &> /dev/null; then
        kubectl label namespace $NAMESPACE istio-injection=enabled --overwrite
        log "Enabled Istio injection for monitoring namespace"
    fi
}

# Deploy Prometheus
deploy_prometheus() {
    log "Deploying Prometheus..."
    
    kubectl apply -f "$SCRIPT_DIR/prometheus.yaml"
    
    # Wait for Prometheus to be ready
    log "Waiting for Prometheus to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/prometheus -n $NAMESPACE
    
    log "Prometheus deployed successfully"
}

# Deploy Grafana
deploy_grafana() {
    log "Deploying Grafana..."
    
    kubectl apply -f "$SCRIPT_DIR/grafana.yaml"
    
    # Wait for Grafana to be ready
    log "Waiting for Grafana to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/grafana -n $NAMESPACE
    
    log "Grafana deployed successfully"
}

# Deploy Jaeger
deploy_jaeger() {
    log "Deploying Jaeger distributed tracing..."
    
    kubectl apply -f "$SCRIPT_DIR/jaeger.yaml"
    
    # Wait for Jaeger components to be ready
    log "Waiting for Jaeger components to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/jaeger-collector -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=300s deployment/jaeger-query -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=300s deployment/otel-collector -n $NAMESPACE
    
    log "Jaeger deployed successfully"
}

# Deploy Alertmanager
deploy_alertmanager() {
    log "Deploying Alertmanager..."
    
    kubectl apply -f "$SCRIPT_DIR/alertmanager.yaml"
    
    # Wait for Alertmanager to be ready
    log "Waiting for Alertmanager to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/alertmanager -n $NAMESPACE
    
    log "Alertmanager deployed successfully"
}

# Verify deployment
verify_deployment() {
    log "Verifying monitoring stack deployment..."
    
    # Check all pods are running
    info "Checking pod status..."
    kubectl get pods -n $NAMESPACE
    
    # Check services
    info "Checking services..."
    kubectl get services -n $NAMESPACE
    
    # Get external service URLs
    log "Getting external service URLs..."
    
    # Prometheus
    PROMETHEUS_URL=$(kubectl get service prometheus-external -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    if [ "$PROMETHEUS_URL" != "pending" ]; then
        info "Prometheus UI: http://$PROMETHEUS_URL:9090"
    else
        warn "Prometheus external IP is pending"
        info "Use port-forward: kubectl port-forward svc/prometheus 9090:9090 -n $NAMESPACE"
    fi
    
    # Grafana
    GRAFANA_URL=$(kubectl get service grafana-external -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    if [ "$GRAFANA_URL" != "pending" ]; then
        info "Grafana UI: http://$GRAFANA_URL:3000"
        info "Default credentials: admin/admin123"
    else
        warn "Grafana external IP is pending"
        info "Use port-forward: kubectl port-forward svc/grafana 3000:3000 -n $NAMESPACE"
    fi
    
    # Jaeger
    JAEGER_URL=$(kubectl get service jaeger-query-external -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    if [ "$JAEGER_URL" != "pending" ]; then
        info "Jaeger UI: http://$JAEGER_URL:16686"
    else
        warn "Jaeger external IP is pending"
        info "Use port-forward: kubectl port-forward svc/jaeger-query 16686:16686 -n $NAMESPACE"
    fi
    
    # Alertmanager
    ALERTMANAGER_URL=$(kubectl get service alertmanager-external -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    if [ "$ALERTMANAGER_URL" != "pending" ]; then
        info "Alertmanager UI: http://$ALERTMANAGER_URL:9093"
    else
        warn "Alertmanager external IP is pending"
        info "Use port-forward: kubectl port-forward svc/alertmanager 9093:9093 -n $NAMESPACE"
    fi
}

# Configure monitoring for existing services
configure_service_monitoring() {
    log "Configuring monitoring for existing services..."
    
    # Add Prometheus annotations to service deployments
    local services=("api-gateway" "service-discovery" "event-bus")
    local namespaces=("api-gateway" "infrastructure" "infrastructure")
    
    for i in "${!services[@]}"; do
        local service="${services[$i]}"
        local ns="${namespaces[$i]}"
        
        if kubectl get deployment "$service" -n "$ns" &> /dev/null; then
            info "Adding monitoring annotations to $service"
            kubectl patch deployment "$service" -n "$ns" -p '{
                "spec": {
                    "template": {
                        "metadata": {
                            "annotations": {
                                "prometheus.io/scrape": "true",
                                "prometheus.io/port": "8080",
                                "prometheus.io/path": "/metrics"
                            }
                        }
                    }
                }
            }'
        else
            warn "Service $service not found in namespace $ns"
        fi
    done
}

# Create monitoring ServiceMonitors for better Prometheus integration
create_service_monitors() {
    log "Creating ServiceMonitor resources..."
    
    # Check if Prometheus Operator is available
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        info "Prometheus Operator detected, creating ServiceMonitors..."
        
        # This would create ServiceMonitor resources for each service
        # For now, we'll use the scrape configs in prometheus.yaml
        log "ServiceMonitor creation skipped - using static scrape configs"
    else
        info "Prometheus Operator not detected, using static scrape configuration"
    fi
}

# Setup log aggregation integration
setup_log_aggregation() {
    log "Setting up log aggregation integration..."
    
    # Add Fluent Bit DaemonSet for log collection
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: $NAMESPACE
  labels:
    app: fluent-bit
spec:
  selector:
    matchLabels:
      app: fluent-bit
  template:
    metadata:
      labels:
        app: fluent-bit
    spec:
      serviceAccountName: fluent-bit
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:2.2.0
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: varlog
          mountPath: /var/log
          readOnly: true
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: fluent-bit-config
          mountPath: /fluent-bit/etc/
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: fluent-bit-config
        configMap:
          name: fluent-bit-config
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: $NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit
subjects:
- kind: ServiceAccount
  name: fluent-bit
  namespace: $NAMESPACE
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: $NAMESPACE
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
    
    [INPUT]
        Name              tail
        Path              /var/log/containers/*.log
        Parser            docker
        Tag               kube.*
        Refresh_Interval  5
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
    
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off
    
    [OUTPUT]
        Name  stdout
        Match *
  
  parsers.conf: |
    [PARSER]
        Name   docker
        Format json
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On
EOF
    
    log "Log aggregation setup completed"
}

# Main deployment function
main() {
    log "Starting Publish AI Monitoring Stack Deployment"
    
    check_prerequisites
    create_namespace
    deploy_prometheus
    deploy_grafana
    deploy_jaeger
    deploy_alertmanager
    configure_service_monitoring
    create_service_monitors
    setup_log_aggregation
    verify_deployment
    
    log "✅ Monitoring stack deployment completed successfully!"
    log ""
    info "Next steps:"
    info "1. Access Grafana UI and explore the pre-configured dashboards"
    info "2. Configure alert notification channels in Alertmanager"
    info "3. Review and customize Prometheus alert rules"
    info "4. Set up external integrations (Slack, PagerDuty, etc.)"
    info "5. Configure long-term storage for metrics (optional)"
    log ""
    log "For troubleshooting, check: kubectl logs -n $NAMESPACE <pod-name>"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "verify")
        verify_deployment
        ;;
    "cleanup")
        warn "Removing monitoring stack..."
        kubectl delete namespace $NAMESPACE --ignore-not-found=true
        log "Monitoring stack removed"
        ;;
    "help")
        echo "Usage: $0 [deploy|verify|cleanup|help]"
        echo "  deploy   - Deploy the complete monitoring stack (default)"
        echo "  verify   - Verify existing deployment"
        echo "  cleanup  - Remove the monitoring stack"
        echo "  help     - Show this help message"
        ;;
    *)
        error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac