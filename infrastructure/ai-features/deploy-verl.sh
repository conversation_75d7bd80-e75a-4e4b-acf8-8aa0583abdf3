#!/bin/bash

# Deploy VERL Training Service - Advanced AI Features
# Production-ready deployment with GPU support and model optimization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
NAMESPACE="ai-training"
SERVICE_NAME="verl-training-service"
DEPLOYMENT_NAME="verl-training-service"

# Default values
ENVIRONMENT="production"
DRY_RUN=false
WAIT_FOR_READY=true
GPU_ENABLED=true
MODEL_PRELOAD=true
MONITORING_ENABLED=true

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy VERL Training Service with advanced AI features to Kubernetes.

Options:
  -e, --environment ENV    Environment: development, staging, production (default: production)
  -n, --namespace NAME     Kubernetes namespace (default: ai-training)
  -d, --dry-run           Preview deployment without making changes
  -w, --no-wait           Don't wait for rollout to complete
  -g, --no-gpu            Deploy without GPU support (CPU-only)
  -m, --no-models         Skip model preloading
  -M, --no-monitoring     Skip monitoring setup
  -h, --help              Show this help message

Examples:
  $0                                    # Deploy to production with all features
  $0 -e staging -n ai-staging          # Deploy to staging environment
  $0 -d                                # Dry run to preview changes
  $0 --no-gpu --no-models              # CPU-only deployment without model preload

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -w|--no-wait)
                WAIT_FOR_READY=false
                shift
                ;;
            -g|--no-gpu)
                GPU_ENABLED=false
                shift
                ;;
            -m|--no-models)
                MODEL_PRELOAD=false
                shift
                ;;
            -M|--no-monitoring)
                MONITORING_ENABLED=false
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if GPU nodes are available
    if [ "$GPU_ENABLED" = true ]; then
        gpu_nodes=$(kubectl get nodes -l accelerator!=none --no-headers 2>/dev/null | wc -l || echo "0")
        if [ "$gpu_nodes" -eq 0 ]; then
            warn "No GPU nodes found in cluster. GPU features may not work."
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        else
            info "Found $gpu_nodes GPU-enabled nodes"
        fi
    fi
    
    # Check required tools
    if [ "$MONITORING_ENABLED" = true ]; then
        if ! kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
            warn "Prometheus Operator not found. Monitoring may not work properly."
        fi
    fi
    
    log "Prerequisites check passed"
}

# Setup namespace and RBAC
setup_namespace() {
    log "Setting up namespace and RBAC..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace and RBAC resources"
        return 0
    fi
    
    # Create namespace if it doesn't exist
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" \
            name="$NAMESPACE" \
            project="publish-ai" \
            component="verl-training" \
            environment="$ENVIRONMENT"
    fi
    
    # Apply RBAC and other base resources
    kubectl apply -f - << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    tier: ai-training
    component: verl
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
subjects:
- kind: ServiceAccount
  name: $SERVICE_NAME
  namespace: $NAMESPACE
roleRef:
  kind: Role
  name: $SERVICE_NAME
  apiGroup: rbac.authorization.k8s.io
EOF
    
    log "Namespace and RBAC setup completed"
}

# Create storage resources
setup_storage() {
    log "Setting up persistent storage..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create storage resources"
        return 0
    fi
    
    # Determine storage class based on environment
    local storage_class="standard"
    local fast_storage_class="fast-ssd"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        storage_class="gp3"
        fast_storage_class="io2"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        storage_class="gp2"
        fast_storage_class="gp3"
    fi
    
    # Create PVCs
    kubectl apply -f - << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-models-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: models
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $fast_storage_class
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-checkpoints-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: checkpoints
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $fast_storage_class
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-data-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $storage_class
  resources:
    requests:
      storage: 200Gi
EOF
    
    log "Storage resources created"
}

# Setup configuration and secrets
setup_config() {
    log "Setting up configuration and secrets..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create configuration and secrets"
        return 0
    fi
    
    # Environment-specific configuration
    local training_batch_size=8
    local max_concurrent_trainings=2
    local gpu_memory_limit=""
    
    case "$ENVIRONMENT" in
        "development")
            training_batch_size=4
            max_concurrent_trainings=1
            gpu_memory_limit="4000"
            ;;
        "staging")
            training_batch_size=6
            max_concurrent_trainings=1
            gpu_memory_limit="6000"
            ;;
        "production")
            training_batch_size=8
            max_concurrent_trainings=2
            gpu_memory_limit=""
            ;;
    esac
    
    # Create ConfigMap
    kubectl apply -f - << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: verl-config
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: config
data:
  ENVIRONMENT: "$ENVIRONMENT"
  LOG_LEVEL: "INFO"
  VERL_ENABLED: "true"
  PPO_ENABLED: "true"
  TRAINING_BATCH_SIZE: "$training_batch_size"
  MAX_CONCURRENT_TRAININGS: "$max_concurrent_trainings"
  GPU_ENABLED: "$GPU_ENABLED"
  MODEL_PRELOAD: "$MODEL_PRELOAD"
  MONITORING_ENABLED: "$MONITORING_ENABLED"
  MIXED_PRECISION: "true"
  GRADIENT_CHECKPOINTING: "true"
  SAFETY_CHECKS_ENABLED: "true"
  MODEL_VALIDATION_ENABLED: "true"
  CONTENT_FILTER_ENABLED: "true"
EOF
    
    # Create secrets (you should replace these with actual values)
    if ! kubectl get secret verl-secrets -n "$NAMESPACE" &> /dev/null; then
        warn "Creating placeholder secrets. Please update with actual values."
        kubectl create secret generic verl-secrets -n "$NAMESPACE" \
            --from-literal=SUPABASE_URL="${SUPABASE_URL:-placeholder}" \
            --from-literal=SUPABASE_ANON_KEY="${SUPABASE_ANON_KEY:-placeholder}" \
            --from-literal=SUPABASE_SERVICE_KEY="${SUPABASE_SERVICE_KEY:-placeholder}" \
            --from-literal=DATABASE_URL="${DATABASE_URL:-placeholder}" \
            --from-literal=REDIS_URL="${REDIS_URL:-redis://redis:6379}" \
            --from-literal=MINIO_ENDPOINT="${MINIO_ENDPOINT:-minio:9000}" \
            --from-literal=MINIO_ACCESS_KEY="${MINIO_ACCESS_KEY:-minioadmin}" \
            --from-literal=MINIO_SECRET_KEY="${MINIO_SECRET_KEY:-minioadmin}" \
            --from-literal=OPENAI_API_KEY="${OPENAI_API_KEY:-placeholder}" \
            --from-literal=ANTHROPIC_API_KEY="${ANTHROPIC_API_KEY:-placeholder}" \
            --from-literal=HUGGINGFACE_TOKEN="${HUGGINGFACE_TOKEN:-placeholder}" \
            --from-literal=WANDB_API_KEY="${WANDB_API_KEY:-placeholder}" \
            --from-literal=SECRET_KEY="${SECRET_KEY:-$(openssl rand -hex 32)}"
    fi
    
    log "Configuration and secrets setup completed"
}

# Deploy the main service
deploy_service() {
    log "Deploying VERL Training Service..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy VERL Training Service"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/verl-deployment.yaml"
        return 0
    fi
    
    # Modify deployment based on options
    local deployment_file="/tmp/verl-deployment-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/verl-deployment.yaml" "$deployment_file"
    
    # Environment-specific modifications
    sed -i.bak "s/namespace: ai-training/namespace: $NAMESPACE/g" "$deployment_file"
    
    if [ "$GPU_ENABLED" = false ]; then
        # Remove GPU-specific configurations
        sed -i.bak '/nvidia\.com\/gpu/d' "$deployment_file"
        sed -i.bak '/accelerator:/d' "$deployment_file"
        sed -i.bak '/NVIDIA_/d' "$deployment_file"
        sed -i.bak '/CUDA_/d' "$deployment_file"
    fi
    
    if [ "$MODEL_PRELOAD" = false ]; then
        # Remove model downloader init container
        sed -i.bak '/initContainers:/,/containers:/c\
      containers:' "$deployment_file"
    fi
    
    # Apply the deployment
    kubectl apply -f "$deployment_file"
    
    # Clean up temporary file
    rm -f "$deployment_file" "$deployment_file.bak"
    
    log "VERL Training Service deployed"
}

# Setup monitoring
setup_monitoring() {
    if [ "$MONITORING_ENABLED" = false ]; then
        info "Skipping monitoring setup"
        return 0
    fi
    
    log "Setting up monitoring..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup monitoring"
        return 0
    fi
    
    # Check if Prometheus Operator is available
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        kubectl apply -f - << EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: verl-training-service-metrics
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: $SERVICE_NAME
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: verl-training-alerts
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    prometheus: kube-prometheus
spec:
  groups:
  - name: verl-training.rules
    rules:
    - alert: VERLTrainingServiceDown
      expr: up{job="verl-training-service"} == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "VERL Training Service is down"
    
    - alert: VERLGPUMemoryHigh
      expr: gpu_memory_usage_percent > 90
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "GPU memory usage is high"
EOF
        
        log "Monitoring setup completed"
    else
        warn "Prometheus Operator not found. Skipping ServiceMonitor setup."
    fi
}

# Setup networking
setup_networking() {
    log "Setting up networking..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup networking"
        return 0
    fi
    
    # Create network policy
    kubectl apply -f - << EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: verl-training-network-policy
  namespace: $NAMESPACE
spec:
  podSelector:
    matchLabels:
      app: $SERVICE_NAME
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: $NAMESPACE
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8080
  egress:
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 9000
EOF
    
    log "Networking setup completed"
}

# Wait for deployment to be ready
wait_for_ready() {
    if [ "$WAIT_FOR_READY" = false ]; then
        info "Skipping readiness check"
        return 0
    fi
    
    log "Waiting for VERL Training Service to be ready..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would wait for readiness"
        return 0
    fi
    
    # Wait for deployment to be ready
    if kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=600s; then
        log "✅ VERL Training Service is ready"
    else
        error "❌ VERL Training Service failed to become ready"
        return 1
    fi
    
    # Check service endpoints
    local retries=30
    local count=0
    
    while [ $count -lt $retries ]; do
        if kubectl get endpoints "$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.subsets[0].addresses[0].ip}' &> /dev/null; then
            log "✅ Service endpoints are ready"
            break
        fi
        
        count=$((count + 1))
        info "Waiting for service endpoints... ($count/$retries)"
        sleep 10
    done
    
    if [ $count -eq $retries ]; then
        error "❌ Service endpoints not ready after 5 minutes"
        return 1
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying VERL Training Service deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check deployment status
    info "Deployment status:"
    kubectl get deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE"
    
    # Check pod status
    info "Pod status:"
    kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check service status
    info "Service status:"
    kubectl get service "$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check storage
    info "Storage status:"
    kubectl get pvc -n "$NAMESPACE"
    
    # Test health endpoint
    local pod_name=$(kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$pod_name" ]; then
        info "Testing health endpoint..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- curl -sf http://localhost:8000/health &> /dev/null; then
            log "✅ Health endpoint is responding"
        else
            warn "⚠️  Health endpoint is not responding yet"
        fi
    fi
    
    log "Deployment verification completed"
}

# Main deployment function
main() {
    log "Starting VERL Training Service deployment"
    
    parse_args "$@"
    
    # Show deployment summary
    info "Deployment Configuration:"
    info "  Environment: $ENVIRONMENT"
    info "  Namespace: $NAMESPACE"
    info "  GPU Enabled: $GPU_ENABLED"
    info "  Model Preload: $MODEL_PRELOAD"
    info "  Monitoring: $MONITORING_ENABLED"
    info "  Dry Run: $DRY_RUN"
    
    # Confirm production deployment
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy to PRODUCTION environment!"
        warn "  Namespace: $NAMESPACE"
        warn "  GPU Support: $GPU_ENABLED"
        warn "  Model Preload: $MODEL_PRELOAD"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    check_prerequisites
    setup_namespace
    setup_storage
    setup_config
    deploy_service
    setup_monitoring
    setup_networking
    wait_for_ready
    verify_deployment
    
    # Final status
    if [ "$DRY_RUN" = false ]; then
        log ""
        log "🎉 VERL Training Service deployment completed successfully!"
        log ""
        info "Service Information:"
        info "  Namespace: $NAMESPACE"
        info "  Service: $SERVICE_NAME"
        info "  Environment: $ENVIRONMENT"
        
        if [ "$GPU_ENABLED" = true ]; then
            info "  GPU Support: Enabled"
        fi
        
        if [ "$MONITORING_ENABLED" = true ]; then
            info "  Monitoring: Enabled"
        fi
        
        log ""
        info "Access the service:"
        info "  Health: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8000:8000"
        info "  Logs: kubectl logs -f -n $NAMESPACE deployment/$DEPLOYMENT_NAME"
        info "  Metrics: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8080:8080"
        
        if [ "$MODEL_PRELOAD" = true ]; then
            info "  TensorBoard: kubectl port-forward -n $NAMESPACE svc/verl-tensorboard 6006:6006"
        fi
    else
        log "🔍 Dry run completed - no changes were made"
    fi
}

# Run main function
main "$@"