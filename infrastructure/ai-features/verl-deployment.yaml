# VERL Training Service Kubernetes Deployment
# Production-ready deployment with GPU support and advanced AI features

apiVersion: v1
kind: Namespace
metadata:
  name: ai-training
  labels:
    name: ai-training
    project: publish-ai
    component: verl-training

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: verl-config
  namespace: ai-training
  labels:
    app: verl-training-service
    component: config
data:
  # Service Configuration
  HOST: "0.0.0.0"
  PORT: "8000"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Training Configuration
  VERL_ENABLED: "true"
  PPO_ENABLED: "true"
  MIN_TRAINING_EXAMPLES: "100"
  MAX_TRAINING_EXAMPLES: "10000"
  TRAINING_BATCH_SIZE: "8"
  EVAL_BATCH_SIZE: "16"
  LEARNING_RATE: "1e-5"
  MAX_EPOCHS: "3"
  WARMUP_STEPS: "100"
  
  # PPO Configuration
  PPO_EPOCHS: "4"
  PPO_CLIP_RANGE: "0.2"
  PPO_KL_PENALTY: "0.1"
  PPO_VALUE_LOSS_COEF: "0.5"
  PPO_ENTROPY_COEF: "0.01"
  
  # GPU Configuration
  MIXED_PRECISION: "true"
  GRADIENT_CHECKPOINTING: "true"
  DATALOADER_NUM_WORKERS: "4"
  PIN_MEMORY: "true"
  
  # Model Management
  MAX_MODEL_CACHE_SIZE: "10"
  MODEL_SAVE_INTERVAL: "1000"
  
  # Training Scheduling
  TRAINING_SCHEDULE_ENABLED: "true"
  TRAINING_INTERVAL_HOURS: "24"
  AUTO_TRAINING_THRESHOLD: "0.1"
  MAX_CONCURRENT_TRAININGS: "2"
  
  # Evaluation and Metrics
  EVALUATION_ENABLED: "true"
  EVALUATION_INTERVAL_STEPS: "500"
  METRIC_COLLECTION_ENABLED: "true"
  
  # Monitoring
  PROMETHEUS_ENABLED: "true"
  JAEGER_ENABLED: "true"
  JAEGER_ENDPOINT: "http://jaeger:14268/api/traces"
  
  # Data Processing
  DATA_PREPROCESSING_WORKERS: "4"
  DATA_VALIDATION_ENABLED: "true"
  MAX_SEQUENCE_LENGTH: "2048"
  
  # Performance Optimization
  MEMORY_OPTIMIZATION: "true"
  
  # Distributed Training
  DISTRIBUTED_TRAINING: "false"
  WORLD_SIZE: "1"
  RANK: "0"
  
  # Safety and Validation
  SAFETY_CHECKS_ENABLED: "true"
  MODEL_VALIDATION_ENABLED: "true"
  CONTENT_FILTER_ENABLED: "true"
  BIAS_DETECTION_ENABLED: "true"
  
  # External Service Timeouts
  API_TIMEOUT: "300"
  DATABASE_TIMEOUT: "30"
  STORAGE_TIMEOUT: "60"

---
apiVersion: v1
kind: Secret
metadata:
  name: verl-secrets
  namespace: ai-training
  labels:
    app: verl-training-service
    component: secrets
type: Opaque
stringData:
  # Database Configuration
  SUPABASE_URL: "${SUPABASE_URL}"
  SUPABASE_ANON_KEY: "${SUPABASE_ANON_KEY}"
  SUPABASE_SERVICE_KEY: "${SUPABASE_SERVICE_KEY}"
  DATABASE_URL: "${DATABASE_URL}"
  
  # Redis Configuration
  REDIS_URL: "${REDIS_URL}"
  
  # Storage Configuration
  MINIO_ENDPOINT: "${MINIO_ENDPOINT}"
  MINIO_ACCESS_KEY: "${MINIO_ACCESS_KEY}"
  MINIO_SECRET_KEY: "${MINIO_SECRET_KEY}"
  MINIO_BUCKET: "verl-models"
  
  # AI Model Configuration
  OPENAI_API_KEY: "${OPENAI_API_KEY}"
  ANTHROPIC_API_KEY: "${ANTHROPIC_API_KEY}"
  HUGGINGFACE_TOKEN: "${HUGGINGFACE_TOKEN}"
  
  # Weights & Biases
  WANDB_API_KEY: "${WANDB_API_KEY}"
  WANDB_PROJECT: "publish-ai-verl"
  
  # Monitoring
  SENTRY_DSN: "${SENTRY_DSN}"
  
  # Security
  SECRET_KEY: "${SECRET_KEY}"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-models-pvc
  namespace: ai-training
  labels:
    app: verl-training-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-checkpoints-pvc
  namespace: ai-training
  labels:
    app: verl-training-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: verl-data-pvc
  namespace: ai-training
  labels:
    app: verl-training-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 200Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: verl-training-service
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    version: v1
    component: verl
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: "ai-training"
    app.kubernetes.io/part-of: "publish-ai"
    app.kubernetes.io/managed-by: "kubernetes"
spec:
  replicas: 1  # Single replica for GPU training
  strategy:
    type: Recreate  # Required for GPU workloads
  selector:
    matchLabels:
      app: verl-training-service
  template:
    metadata:
      labels:
        app: verl-training-service
        tier: ai-training
        version: v1
        component: verl
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        kubectl.kubernetes.io/restartedAt: "${RESTART_TIMESTAMP}"
    spec:
      serviceAccountName: verl-training-service
      priorityClassName: high-priority
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        supplementalGroups: [44, 107]  # Docker and render groups for GPU access
      
      # GPU node selection
      nodeSelector:
        accelerator: nvidia-tesla-v100
        node.kubernetes.io/instance-type: p3.2xlarge
      
      # Anti-affinity to avoid multiple GPU workloads on same node
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: component
                operator: In
                values: ["ai-training", "gpu-workload"]
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: accelerator
                operator: In
                values: ["nvidia-tesla-v100", "nvidia-a100"]
              - key: node.kubernetes.io/instance-type
                operator: In
                values: ["p3.2xlarge", "p3.8xlarge", "p4d.24xlarge"]
      
      # GPU toleration
      tolerations:
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "workload"
        operator: "Equal"
        value: "ai-training"
        effect: "NoSchedule"
      
      initContainers:
      # Model download and preparation
      - name: model-downloader
        image: python:3.11-slim
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "Downloading base models..."
            pip install -q transformers torch
            python -c "
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import os
            
            models = [
              'microsoft/DialoGPT-medium',
              'microsoft/DialoGPT-large'
            ]
            
            cache_dir = '/app/models'
            os.makedirs(cache_dir, exist_ok=True)
            
            for model_name in models:
              print(f'Downloading {model_name}...')
              tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=cache_dir)
              # Note: Skip model download in init to save time
              print(f'Tokenizer for {model_name} downloaded')
            
            print('Model preparation completed')
            "
        volumeMounts:
        - name: model-cache
          mountPath: /app/models
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
      
      containers:
      - name: verl-training-service
        image: ghcr.io/publishai/verl-training-service:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8080
          protocol: TCP
        - name: tensorboard
          containerPort: 6006
          protocol: TCP
        
        env:
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        - name: NVIDIA_VISIBLE_DEVICES
          value: "all"
        - name: NVIDIA_DRIVER_CAPABILITIES
          value: "compute,utility"
        - name: PYTORCH_CUDA_ALLOC_CONF
          value: "max_split_size_mb:512"
        - name: MODEL_CACHE_DIR
          value: "/app/models"
        - name: CHECKPOINT_DIR
          value: "/app/checkpoints"
        - name: DATA_DIR
          value: "/app/data"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        
        envFrom:
        - configMapRef:
            name: verl-config
        - secretRef:
            name: verl-secrets
        
        resources:
          requests:
            cpu: 2000m
            memory: 8Gi
            nvidia.com/gpu: 1
            ephemeral-storage: 10Gi
          limits:
            cpu: 8000m
            memory: 32Gi
            nvidia.com/gpu: 1
            ephemeral-storage: 50Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 120  # Allow time for model loading
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health/startup
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 20
          successThreshold: 1
        
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - |
                echo "Gracefully shutting down VERL Training Service..."
                # Save any in-progress training state
                kill -TERM 1
                sleep 30
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false  # AI workloads need write access
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
            add:
            - SYS_ADMIN  # Required for some GPU operations
          seccompProfile:
            type: RuntimeDefault
        
        volumeMounts:
        - name: model-cache
          mountPath: /app/models
        - name: checkpoint-storage
          mountPath: /app/checkpoints
        - name: training-data
          mountPath: /app/data
        - name: tmp
          mountPath: /tmp
        - name: var-tmp
          mountPath: /var/tmp
        - name: cache
          mountPath: /app/.cache
        - name: huggingface-cache
          mountPath: /root/.cache/huggingface
        - name: torch-cache
          mountPath: /root/.cache/torch
      
      volumes:
      - name: model-cache
        persistentVolumeClaim:
          claimName: verl-models-pvc
      - name: checkpoint-storage
        persistentVolumeClaim:
          claimName: verl-checkpoints-pvc
      - name: training-data
        persistentVolumeClaim:
          claimName: verl-data-pvc
      - name: tmp
        emptyDir:
          sizeLimit: 5Gi
      - name: var-tmp
        emptyDir:
          sizeLimit: 5Gi
      - name: cache
        emptyDir:
          sizeLimit: 10Gi
      - name: huggingface-cache
        emptyDir:
          sizeLimit: 20Gi
      - name: torch-cache
        emptyDir:
          sizeLimit: 10Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 120

---
apiVersion: v1
kind: Service
metadata:
  name: verl-training-service
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: metrics
    protocol: TCP
  - name: tensorboard
    port: 6006
    targetPort: tensorboard
    protocol: TCP
  selector:
    app: verl-training-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: verl-training-service
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT:role/verl-training-service-role"
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: verl-training-service
  namespace: ai-training
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: verl-training-service
  namespace: ai-training
subjects:
- kind: ServiceAccount
  name: verl-training-service
  namespace: ai-training
roleRef:
  kind: Role
  name: verl-training-service
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: verl-training-service-pdb
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
spec:
  maxUnavailable: 0  # No disruption allowed for training workloads
  selector:
    matchLabels:
      app: verl-training-service

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: verl-training-service-network-policy
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
spec:
  podSelector:
    matchLabels:
      app: verl-training-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from API Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 8000
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8080
  # Allow traffic from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-training
    ports:
    - protocol: TCP
      port: 8000
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS outbound (for external APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow HTTP outbound (for internal services)
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
  # Allow database connections
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  # Allow MinIO connections
  - to: []
    ports:
    - protocol: TCP
      port: 9000  # MinIO API
    - protocol: TCP
      port: 9001  # MinIO Console

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: verl-training-service-metrics
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: verl-training-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s
    honorLabels: true
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'training_.*'
      targetLabel: __tmp_training_metric
      replacement: 'true'
  namespaceSelector:
    matchNames:
    - ai-training

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: verl-training-service-alerts
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
    prometheus: kube-prometheus
spec:
  groups:
  - name: verl-training.rules
    interval: 30s
    rules:
    - alert: VERLTrainingServiceDown
      expr: up{job="verl-training-service"} == 0
      for: 5m
      labels:
        severity: critical
        service: verl-training-service
        component: ai-training
      annotations:
        summary: "VERL Training Service is down"
        description: "VERL Training Service has been down for more than 5 minutes"
    
    - alert: VERLTrainingJobFailure
      expr: increase(training_jobs_failed_total[5m]) > 2
      for: 2m
      labels:
        severity: warning
        service: verl-training-service
        component: ai-training
      annotations:
        summary: "High training job failure rate"
        description: "More than 2 training jobs have failed in the last 5 minutes"
    
    - alert: VERLGPUMemoryHigh
      expr: gpu_memory_usage_percent > 90
      for: 10m
      labels:
        severity: warning
        service: verl-training-service
        component: ai-training
      annotations:
        summary: "GPU memory usage is high"
        description: "GPU memory usage is above 90% for more than 10 minutes"
    
    - alert: VERLTrainingQueueBacklog
      expr: training_queue_size > 10
      for: 15m
      labels:
        severity: warning
        service: verl-training-service
        component: ai-training
      annotations:
        summary: "Training queue backlog is high"
        description: "Training queue has more than 10 pending jobs for 15 minutes"
    
    - alert: VERLModelPerformanceDegradation
      expr: avg_over_time(model_reward_score[24h]) < 0.6
      for: 1h
      labels:
        severity: warning
        service: verl-training-service
        component: ai-training
      annotations:
        summary: "Model performance has degraded"
        description: "Average model reward score is below 0.6 over the last 24 hours"

---
apiVersion: v1
kind: Service
metadata:
  name: verl-tensorboard
  namespace: ai-training
  labels:
    app: verl-training-service
    component: tensorboard
spec:
  type: ClusterIP
  ports:
  - name: tensorboard
    port: 6006
    targetPort: tensorboard
    protocol: TCP
  selector:
    app: verl-training-service

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: verl-training-ingress
  namespace: ai-training
  labels:
    app: verl-training-service
    tier: ai-training
    component: verl
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
spec:
  tls:
  - hosts:
    - verl-training.publishai.com
    - tensorboard.publishai.com
    secretName: verl-training-tls
  rules:
  - host: verl-training.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: verl-training-service
            port:
              number: 8000
  - host: tensorboard.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: verl-tensorboard
            port:
              number: 6006