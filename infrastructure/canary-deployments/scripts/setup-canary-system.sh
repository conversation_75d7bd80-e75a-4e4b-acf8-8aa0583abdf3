#!/bin/bash

# Canary System Setup Script for Publish AI
# Installs and configures Argo Rollouts with Istio integration for canary deployments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CANARY_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
ARGO_ROLLOUTS_VERSION="v1.6.0"
TEMP_DIR="/tmp/canary-setup"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check cluster admin privileges
    if ! kubectl auth can-i create clusterrole &> /dev/null; then
        error "Insufficient permissions. Please run with cluster admin privileges"
        exit 1
    fi
    
    # Check Istio installation
    if ! kubectl get namespace istio-system &> /dev/null; then
        error "Istio not found. Please install Istio first."
        exit 1
    fi
    
    # Check monitoring stack
    if ! kubectl get namespace monitoring &> /dev/null; then
        warn "Monitoring namespace not found. Some features may not work properly."
    fi
    
    log "Prerequisites check passed"
}

# Install Argo Rollouts
install_argo_rollouts() {
    log "Installing Argo Rollouts $ARGO_ROLLOUTS_VERSION..."
    
    # Create namespace
    kubectl create namespace argo-rollouts || true
    
    # Install Argo Rollouts CRDs and controller
    kubectl apply -n argo-rollouts -f "https://github.com/argoproj/argo-rollouts/releases/download/$ARGO_ROLLOUTS_VERSION/install.yaml"
    
    # Wait for deployment to be ready
    log "Waiting for Argo Rollouts controller to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/argo-rollouts -n argo-rollouts
    
    # Verify installation
    if kubectl get crd rollouts.argoproj.io &> /dev/null; then
        log "Argo Rollouts installed successfully"
    else
        error "Failed to install Argo Rollouts"
        exit 1
    fi
}

# Install Argo Rollouts CLI
install_argo_rollouts_cli() {
    log "Installing Argo Rollouts CLI..."
    
    # Create temp directory
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    # Detect platform
    local platform=""
    case "$(uname -s)" in
        Darwin*) platform="darwin" ;;
        Linux*) platform="linux" ;;
        *) 
            warn "Unsupported platform for CLI installation"
            return 0
            ;;
    esac
    
    # Download and install CLI
    local cli_url="https://github.com/argoproj/argo-rollouts/releases/download/$ARGO_ROLLOUTS_VERSION/kubectl-argo-rollouts-${platform}-amd64"
    
    if command -v wget &> /dev/null; then
        wget -O kubectl-argo-rollouts "$cli_url"
    elif command -v curl &> /dev/null; then
        curl -LO "$cli_url"
        mv "kubectl-argo-rollouts-${platform}-amd64" kubectl-argo-rollouts
    else
        warn "Neither wget nor curl found. Cannot download CLI."
        return 0
    fi
    
    chmod +x kubectl-argo-rollouts
    
    # Try to install to common paths
    local install_paths=("/usr/local/bin" "$HOME/bin" "/usr/bin")
    local installed=false
    
    for path in "${install_paths[@]}"; do
        if [ -w "$path" ] || sudo -n true 2>/dev/null; then
            if [ -w "$path" ]; then
                mv kubectl-argo-rollouts "$path/"
            else
                sudo mv kubectl-argo-rollouts "$path/"
            fi
            log "Argo Rollouts CLI installed to $path"
            installed=true
            break
        fi
    done
    
    if [ "$installed" = false ]; then
        warn "Could not install CLI to system path. Binary is available at: $TEMP_DIR/kubectl-argo-rollouts"
    fi
    
    # Verify CLI installation
    if command -v kubectl-argo-rollouts &> /dev/null; then
        kubectl-argo-rollouts version || true
    fi
}

# Setup canary controller
setup_canary_controller() {
    log "Setting up canary controller..."
    
    # Apply canary controller configuration
    kubectl apply -f "$CANARY_CONFIG_DIR/canary-controller.yaml"
    
    # Wait for controller to be ready
    log "Waiting for canary controller to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/canary-controller -n canary-system
    
    log "Canary controller setup completed"
}

# Configure RBAC for canary deployments
setup_rbac() {
    log "Setting up RBAC for canary deployments..."
    
    # Create service accounts for each service namespace
    local namespaces=("api-gateway" "tier1-services" "tier2-services" "tier3-services")
    
    for ns in "${namespaces[@]}"; do
        if kubectl get namespace "$ns" &> /dev/null; then
            info "Setting up RBAC for namespace $ns"
            
            # Create service account for rollouts
            kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-rollouts
  namespace: $ns
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: $ns
  name: argo-rollouts
rules:
- apiGroups: [""]
  resources: ["services", "endpoints", "pods"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["argoproj.io"]
  resources: ["rollouts", "analysistemplates", "analysisruns"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-rollouts
  namespace: $ns
subjects:
- kind: ServiceAccount
  name: argo-rollouts
  namespace: $ns
roleRef:
  kind: Role
  name: argo-rollouts
  apiGroup: rbac.authorization.k8s.io
EOF
        else
            warn "Namespace $ns not found. Skipping RBAC setup."
        fi
    done
    
    log "RBAC setup completed"
}

# Install analysis templates
install_analysis_templates() {
    log "Installing analysis templates..."
    
    # Apply analysis templates to canary-system namespace (global templates)
    kubectl apply -f "$CANARY_CONFIG_DIR/canary-controller.yaml"
    
    # Install namespace-specific templates
    local namespaces=("api-gateway" "tier1-services" "tier2-services" "tier3-services")
    
    for ns in "${namespaces[@]}"; do
        if kubectl get namespace "$ns" &> /dev/null; then
            info "Installing analysis templates for namespace $ns"
            
            # Copy global templates to each namespace
            kubectl get analysistemplate -n canary-system -o yaml | \
                sed "s/namespace: canary-system/namespace: $ns/g" | \
                kubectl apply -f -
        fi
    done
    
    log "Analysis templates installed"
}

# Setup monitoring integration
setup_monitoring() {
    log "Setting up monitoring integration..."
    
    # Apply monitoring configurations
    kubectl apply -f "$CANARY_CONFIG_DIR/monitoring/canary-dashboards.yaml"
    
    # Create ServiceMonitor for canary controller
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: argo-rollouts
  namespace: argo-rollouts
  labels:
    app: argo-rollouts
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: argo-rollouts-metrics
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
EOF
    
    # Check if Grafana is available and restart it to load new dashboards
    if kubectl get deployment grafana -n monitoring &> /dev/null; then
        info "Restarting Grafana to load canary dashboards..."
        kubectl rollout restart deployment/grafana -n monitoring
    fi
    
    log "Monitoring integration setup completed"
}

# Configure Istio integration
configure_istio_integration() {
    log "Configuring Istio integration..."
    
    # Enable Istio injection for argo-rollouts namespace
    kubectl label namespace argo-rollouts istio-injection=enabled --overwrite
    
    # Enable Istio injection for canary-system namespace
    kubectl label namespace canary-system istio-injection=enabled --overwrite
    
    # Restart argo-rollouts deployment to inject sidecar
    kubectl rollout restart deployment/argo-rollouts -n argo-rollouts
    
    # Wait for restart to complete
    kubectl wait --for=condition=available --timeout=300s deployment/argo-rollouts -n argo-rollouts
    
    log "Istio integration configured"
}

# Create example configurations
create_examples() {
    log "Creating example configurations..."
    
    # Check if example services exist and create rollout configs
    local services=("api-gateway" "content-generation" "market-intelligence" "publishing-service" "cover-designer" "sales-monitor")
    
    for service in "${services[@]}"; do
        local example_file="$CANARY_CONFIG_DIR/examples/$service-canary.yaml"
        
        if [ -f "$example_file" ]; then
            info "Example configuration available for $service at $example_file"
        else
            warn "No example configuration found for $service"
        fi
    done
    
    log "Example configurations ready"
}

# Verify installation
verify_installation() {
    log "Verifying canary system installation..."
    
    # Check Argo Rollouts
    if kubectl get deployment argo-rollouts -n argo-rollouts &> /dev/null; then
        local rollouts_status=$(kubectl get deployment argo-rollouts -n argo-rollouts -o jsonpath='{.status.readyReplicas}')
        if [ "$rollouts_status" = "1" ]; then
            info "✅ Argo Rollouts controller is running"
        else
            warn "❓ Argo Rollouts controller may not be ready"
        fi
    else
        error "❌ Argo Rollouts controller not found"
    fi
    
    # Check canary controller
    if kubectl get deployment canary-controller -n canary-system &> /dev/null; then
        local controller_status=$(kubectl get deployment canary-controller -n canary-system -o jsonpath='{.status.readyReplicas}')
        if [ "$controller_status" = "2" ]; then
            info "✅ Canary controller is running"
        else
            warn "❓ Canary controller may not be ready"
        fi
    else
        error "❌ Canary controller not found"
    fi
    
    # Check CRDs
    local crds=("rollouts.argoproj.io" "analysistemplates.argoproj.io" "analysisruns.argoproj.io")
    for crd in "${crds[@]}"; do
        if kubectl get crd "$crd" &> /dev/null; then
            info "✅ CRD $crd is installed"
        else
            error "❌ CRD $crd not found"
        fi
    done
    
    # Check CLI
    if command -v kubectl-argo-rollouts &> /dev/null; then
        info "✅ Argo Rollouts CLI is available"
    else
        warn "❓ Argo Rollouts CLI not found in PATH"
    fi
    
    log "Installation verification completed"
}

# Show access information
show_access_info() {
    log "Canary deployment system access information:"
    
    # Argo Rollouts dashboard
    info ""
    info "Argo Rollouts Dashboard:"
    info "kubectl argo rollouts dashboard"
    info "# Access at http://localhost:3100"
    
    # CLI usage
    info ""
    info "Common CLI commands:"
    info "kubectl argo rollouts list                          # List all rollouts"
    info "kubectl argo rollouts get rollout <name>            # Get rollout status"
    info "kubectl argo rollouts promote <name>                # Promote canary"
    info "kubectl argo rollouts abort <name>                  # Abort canary"
    info "kubectl argo rollouts retry <name>                  # Retry failed rollout"
    
    # Deployment scripts
    info ""
    info "Deployment scripts:"
    info "$SCRIPT_DIR/deploy-canary.sh <service> <image>      # Deploy canary"
    info "$SCRIPT_DIR/rollback-canary.sh <service>            # Rollback canary"
    
    # Monitoring
    info ""
    info "Monitoring:"
    info "Grafana dashboards: Canary Deployments Overview, Canary Analysis"
    info "Prometheus metrics: Available at /metrics endpoints"
    
    # Example usage
    info ""
    info "Example usage:"
    info "$SCRIPT_DIR/deploy-canary.sh api-gateway v2.1.0"
    info "$SCRIPT_DIR/deploy-canary.sh content-generation v1.5.2 --dry-run"
    info "$SCRIPT_DIR/rollback-canary.sh api-gateway --mode gradual"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    # Clean up temporary files
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    if [ $exit_code -ne 0 ]; then
        error "Setup failed with exit code $exit_code"
        warn "Check the logs above for details"
    fi
}

# Main function
main() {
    log "Starting Publish AI Canary Deployment System Setup"
    
    check_prerequisites
    install_argo_rollouts
    install_argo_rollouts_cli
    setup_canary_controller
    setup_rbac
    install_analysis_templates
    setup_monitoring
    configure_istio_integration
    create_examples
    verify_installation
    show_access_info
    
    log "✅ Canary deployment system setup completed successfully!"
    log ""
    info "Next steps:"
    info "1. Review example configurations in $CANARY_CONFIG_DIR/examples/"
    info "2. Customize canary strategies for your services"
    info "3. Set up notification webhooks in canary-controller-secrets"
    info "4. Test with a simple canary deployment"
    info "5. Configure monitoring alerts and dashboards"
    log ""
    log "For troubleshooting, check:"
    log "- kubectl logs -n argo-rollouts deployment/argo-rollouts"
    log "- kubectl logs -n canary-system deployment/canary-controller"
}

# Set up signal handlers
trap cleanup EXIT

# Make scripts executable
chmod +x "$SCRIPT_DIR"/deploy-canary.sh
chmod +x "$SCRIPT_DIR"/rollback-canary.sh

# Run main function
main "$@"