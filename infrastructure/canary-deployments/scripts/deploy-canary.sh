#!/bin/bash

# Canary Deployment Script for Publish AI
# Automates canary deployments with Istio and Argo Rollouts

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CANARY_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
ROLLOUT_TIMEOUT="30m"
ANALYSIS_TIMEOUT="10m"

# Default values
DRY_RUN=false
FORCE=false
SKIP_ANALYSIS=false
NOTIFICATION_ENABLED=true
AUTO_PROMOTE=false

# Service configurations
declare -A SERVICE_CONFIGS=(
    ["api-gateway"]="api-gateway conservative 5,10,25,50,100"
    ["content-generation"]="tier1-services cautious 1,5,10,25,50,100"
    ["market-intelligence"]="tier1-services moderate 5,15,35,70,100"
    ["publishing-service"]="tier1-services conservative 5,10,25,50,100"
    ["cover-designer"]="tier2-services moderate 10,25,50,100"
    ["sales-monitor"]="tier2-services moderate 15,40,80,100"
    ["personalization"]="tier3-services aggressive 10,50,100"
    ["research"]="tier3-services aggressive 15,60,100"
)

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS] <service-name> <new-image>

Deploy a canary version of a microservice with automated analysis and rollback.

Arguments:
  service-name    Name of the service to deploy (e.g., api-gateway, content-generation)
  new-image       Docker image tag for the new version (e.g., v2.1.0)

Options:
  -d, --dry-run           Show what would be deployed without making changes
  -f, --force             Force deployment even if previous rollout is in progress
  -s, --skip-analysis     Skip automated analysis (manual promotion required)
  -n, --no-notifications Disable Slack/email notifications
  -a, --auto-promote      Automatically promote on success (skip final approval)
  -t, --timeout DURATION  Rollout timeout (default: 30m)
  -c, --config FILE       Custom canary configuration file
  -h, --help              Show this help message

Examples:
  $0 api-gateway v2.1.0
  $0 content-generation v1.5.2 --dry-run
  $0 publishing-service v3.0.0 --skip-analysis --timeout 45m
  $0 market-intelligence v2.3.1 --auto-promote

Supported Services:
  - api-gateway (conservative strategy)
  - content-generation (cautious strategy, extended validation)
  - market-intelligence (moderate strategy)
  - publishing-service (conservative strategy, business hours only)
  - cover-designer (moderate strategy)
  - sales-monitor (moderate strategy)
  - personalization (aggressive strategy)
  - research (aggressive strategy)

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -s|--skip-analysis)
                SKIP_ANALYSIS=true
                shift
                ;;
            -n|--no-notifications)
                NOTIFICATION_ENABLED=false
                shift
                ;;
            -a|--auto-promote)
                AUTO_PROMOTE=true
                shift
                ;;
            -t|--timeout)
                ROLLOUT_TIMEOUT="$2"
                shift 2
                ;;
            -c|--config)
                CUSTOM_CONFIG="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                if [ -z "${SERVICE_NAME:-}" ]; then
                    SERVICE_NAME="$1"
                elif [ -z "${NEW_IMAGE:-}" ]; then
                    NEW_IMAGE="$1"
                else
                    error "Too many arguments"
                    usage
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "${SERVICE_NAME:-}" ] || [ -z "${NEW_IMAGE:-}" ]; then
        error "Missing required arguments"
        usage
        exit 1
    fi

    # Validate service name
    if [[ ! " ${!SERVICE_CONFIGS[@]} " =~ " ${SERVICE_NAME} " ]]; then
        error "Unknown service: $SERVICE_NAME"
        echo "Supported services: ${!SERVICE_CONFIGS[@]}"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check Argo Rollouts
    if ! kubectl get crd rollouts.argoproj.io &> /dev/null; then
        error "Argo Rollouts CRD not found. Please install Argo Rollouts."
        exit 1
    fi
    
    # Check Istio
    if ! kubectl get namespace istio-system &> /dev/null; then
        error "Istio not found. Please install Istio service mesh."
        exit 1
    fi
    
    # Check monitoring
    if ! kubectl get service prometheus -n monitoring &> /dev/null; then
        warn "Prometheus not found. Analysis may not work properly."
    fi
    
    log "Prerequisites check passed"
}

# Get service configuration
get_service_config() {
    local service_name="$1"
    local config="${SERVICE_CONFIGS[$service_name]}"
    
    NAMESPACE=$(echo "$config" | cut -d' ' -f1)
    STRATEGY=$(echo "$config" | cut -d' ' -f2)
    STEPS=$(echo "$config" | cut -d' ' -f3)
    
    info "Service: $service_name"
    info "Namespace: $NAMESPACE"
    info "Strategy: $STRATEGY"
    info "Steps: $STEPS"
}

# Check current rollout status
check_current_rollout() {
    local service_name="$1"
    local namespace="$2"
    
    log "Checking current rollout status for $service_name..."
    
    # Check if rollout exists
    if ! kubectl get rollout "$service_name-canary" -n "$namespace" &> /dev/null; then
        warn "No existing rollout found for $service_name. This might be the first canary deployment."
        return 0
    fi
    
    # Check rollout status
    local status=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.phase}')
    
    case "$status" in
        "Progressing")
            if [ "$FORCE" = true ]; then
                warn "Rollout is in progress but --force was specified. Aborting current rollout..."
                kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
                sleep 10
            else
                error "Rollout is already in progress. Use --force to abort and start new deployment."
                exit 1
            fi
            ;;
        "Paused")
            if [ "$FORCE" = true ]; then
                warn "Rollout is paused but --force was specified. Aborting current rollout..."
                kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
                sleep 10
            else
                error "Rollout is paused. Use --force to abort and start new deployment."
                exit 1
            fi
            ;;
        "Healthy")
            info "Previous rollout completed successfully"
            ;;
        "Degraded")
            warn "Previous rollout is in degraded state"
            ;;
        *)
            info "Rollout status: $status"
            ;;
    esac
}

# Validate new image
validate_image() {
    local image="$1"
    
    log "Validating image: $image"
    
    # Check if image exists (simple format validation)
    if [[ ! "$image" =~ ^[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+:[a-zA-Z0-9._-]+$ ]]; then
        error "Invalid image format: $image"
        error "Expected format: registry/repository:tag"
        exit 1
    fi
    
    info "Image format validation passed"
}

# Send notification
send_notification() {
    local event="$1"
    local service="$2"
    local image="$3"
    local status="${4:-}"
    
    if [ "$NOTIFICATION_ENABLED" = false ]; then
        return 0
    fi
    
    local webhook_url
    webhook_url=$(kubectl get secret canary-controller-secrets -n canary-system -o jsonpath='{.data.slack-webhook-url}' 2>/dev/null | base64 -d || echo "")
    
    if [ -z "$webhook_url" ]; then
        warn "Slack webhook URL not configured. Skipping notification."
        return 0
    fi
    
    local color="warning"
    case "$event" in
        "started") color="good" ;;
        "completed") color="good" ;;
        "failed") color="danger" ;;
        "rollback") color="danger" ;;
    esac
    
    local payload=$(cat << EOF
{
    "channel": "#deployments",
    "username": "Canary Bot",
    "text": "Canary Deployment: $event",
    "attachments": [
        {
            "color": "$color",
            "fields": [
                {
                    "title": "Service",
                    "value": "$service",
                    "short": true
                },
                {
                    "title": "Image",
                    "value": "$image",
                    "short": true
                },
                {
                    "title": "Event",
                    "value": "$event",
                    "short": true
                },
                {
                    "title": "Status",
                    "value": "$status",
                    "short": true
                }
            ]
        }
    ]
}
EOF
    )
    
    curl -X POST -H 'Content-type: application/json' --data "$payload" "$webhook_url" &> /dev/null || {
        warn "Failed to send Slack notification"
    }
}

# Create or update rollout configuration
create_rollout_config() {
    local service_name="$1"
    local namespace="$2"
    local image="$3"
    
    log "Creating rollout configuration for $service_name..."
    
    local config_file="/tmp/${service_name}-canary-config.yaml"
    
    # Use existing example as template or create new one
    local template_file="$CANARY_CONFIG_DIR/examples/${service_name}-canary.yaml"
    if [ ! -f "$template_file" ]; then
        # Use generic template for unknown services
        template_file="$CANARY_CONFIG_DIR/examples/generic-canary.yaml"
    fi
    
    if [ ! -f "$template_file" ]; then
        error "No canary template found for $service_name"
        exit 1
    fi
    
    # Update image in template
    sed "s|image: .*|image: $image|g" "$template_file" > "$config_file"
    
    info "Rollout configuration created: $config_file"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would apply the following configuration:"
        cat "$config_file"
        return 0
    fi
    
    # Apply the configuration
    kubectl apply -f "$config_file"
    
    # Clean up temporary file
    rm -f "$config_file"
}

# Start canary deployment
start_canary_deployment() {
    local service_name="$1"
    local namespace="$2"
    local image="$3"
    
    log "Starting canary deployment for $service_name..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would start canary deployment with image $image"
        return 0
    fi
    
    # Update the rollout with new image
    kubectl argo rollouts set image "$service_name-canary" "$service_name=$image" -n "$namespace"
    
    # Start the rollout
    info "Rollout started. Monitoring progress..."
    
    # Send notification
    send_notification "started" "$service_name" "$image" "Canary deployment initiated"
}

# Monitor rollout progress
monitor_rollout() {
    local service_name="$1"
    local namespace="$2"
    local image="$3"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would monitor rollout progress"
        return 0
    fi
    
    log "Monitoring rollout progress..."
    
    # Monitor with timeout
    if timeout "$ROLLOUT_TIMEOUT" kubectl argo rollouts get rollout "$service_name-canary" -n "$namespace" --watch; then
        log "Rollout completed successfully"
        send_notification "completed" "$service_name" "$image" "Canary deployment completed"
        return 0
    else
        error "Rollout timed out or failed"
        
        # Get rollout status for diagnostics
        kubectl argo rollouts get rollout "$service_name-canary" -n "$namespace"
        
        # Check for analysis failures
        local analysis_runs=$(kubectl get analysisrun -n "$namespace" -l rollout="$service_name-canary" --sort-by=.metadata.creationTimestamp -o name | tail -5)
        if [ -n "$analysis_runs" ]; then
            warn "Recent analysis runs:"
            for run in $analysis_runs; do
                kubectl get "$run" -n "$namespace" -o wide
            done
        fi
        
        send_notification "failed" "$service_name" "$image" "Canary deployment failed"
        return 1
    fi
}

# Handle rollback
handle_rollback() {
    local service_name="$1"
    local namespace="$2"
    local image="$3"
    
    warn "Initiating rollback for $service_name..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would initiate rollback"
        return 0
    fi
    
    # Abort the rollout
    kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
    
    # Wait for abort to complete
    sleep 10
    
    # Verify rollback
    local status=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.phase}')
    if [ "$status" = "Healthy" ]; then
        log "Rollback completed successfully"
        send_notification "rollback" "$service_name" "$image" "Rollback completed"
    else
        error "Rollback failed. Manual intervention required."
        send_notification "rollback" "$service_name" "$image" "Rollback failed"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        error "Deployment failed with exit code $exit_code"
        
        if [ -n "${SERVICE_NAME:-}" ] && [ -n "${NAMESPACE:-}" ] && [ -n "${NEW_IMAGE:-}" ]; then
            if [ "$DRY_RUN" = false ]; then
                read -p "Do you want to initiate rollback? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    handle_rollback "$SERVICE_NAME" "$NAMESPACE" "$NEW_IMAGE"
                fi
            fi
        fi
    fi
}

# Main function
main() {
    log "Starting Publish AI Canary Deployment"
    
    parse_args "$@"
    check_prerequisites
    get_service_config "$SERVICE_NAME"
    validate_image "$NEW_IMAGE"
    check_current_rollout "$SERVICE_NAME" "$NAMESPACE"
    
    # Create and apply rollout configuration
    create_rollout_config "$SERVICE_NAME" "$NAMESPACE" "$NEW_IMAGE"
    
    # Start canary deployment
    start_canary_deployment "$SERVICE_NAME" "$NAMESPACE" "$NEW_IMAGE"
    
    # Monitor progress
    if monitor_rollout "$SERVICE_NAME" "$NAMESPACE" "$NEW_IMAGE"; then
        log "✅ Canary deployment completed successfully!"
        
        if [ "$AUTO_PROMOTE" = true ]; then
            info "Auto-promotion enabled. Canary will be automatically promoted."
        else
            info "Canary deployment is ready for manual promotion or rollback."
            info "Use 'kubectl argo rollouts promote $SERVICE_NAME-canary -n $NAMESPACE' to promote."
            info "Use 'kubectl argo rollouts abort $SERVICE_NAME-canary -n $NAMESPACE' to rollback."
        fi
    else
        error "❌ Canary deployment failed!"
        exit 1
    fi
}

# Set up signal handlers
trap cleanup EXIT

# Run main function
main "$@"