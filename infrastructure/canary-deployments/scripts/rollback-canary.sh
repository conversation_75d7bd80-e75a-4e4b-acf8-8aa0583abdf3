#!/bin/bash

# Canary Rollback Script for Publish AI
# Provides immediate and gradual rollback capabilities for canary deployments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROLLBACK_TIMEOUT="10m"

# Default values
DRY_RUN=false
FORCE=false
GRADUAL=false
IMMEDIATE=true
NOTIFICATION_ENABLED=true
REASON=""

# Rollback modes
ROLLBACK_MODE="immediate"  # immediate, gradual, blue-green

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS] <service-name> [namespace]

Rollback a canary deployment to the stable version.

Arguments:
  service-name    Name of the service to rollback (e.g., api-gateway)
  namespace       Kubernetes namespace (optional, will be auto-detected)

Options:
  -m, --mode MODE         Rollback mode: immediate, gradual, blue-green (default: immediate)
  -r, --reason REASON     Reason for rollback (for audit logs)
  -d, --dry-run           Show what would be done without making changes
  -f, --force             Force rollback even if service appears healthy
  -n, --no-notifications Disable Slack/email notifications
  -t, --timeout DURATION  Rollback timeout (default: 10m)
  -h, --help              Show this help message

Rollback Modes:
  immediate    Instant traffic switch to stable version (default)
  gradual      Progressive traffic shift back to stable (reverse canary)
  blue-green   Switch traffic completely using blue-green strategy

Examples:
  $0 api-gateway
  $0 content-generation tier1-services --mode gradual
  $0 publishing-service --reason "High error rate detected"
  $0 market-intelligence --dry-run --mode blue-green

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                ROLLBACK_MODE="$2"
                shift 2
                ;;
            -r|--reason)
                REASON="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -n|--no-notifications)
                NOTIFICATION_ENABLED=false
                shift
                ;;
            -t|--timeout)
                ROLLBACK_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                if [ -z "${SERVICE_NAME:-}" ]; then
                    SERVICE_NAME="$1"
                elif [ -z "${NAMESPACE:-}" ]; then
                    NAMESPACE="$1"
                else
                    error "Too many arguments"
                    usage
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "${SERVICE_NAME:-}" ]; then
        error "Missing required argument: service-name"
        usage
        exit 1
    fi

    # Validate rollback mode
    case "$ROLLBACK_MODE" in
        immediate|gradual|blue-green)
            ;;
        *)
            error "Invalid rollback mode: $ROLLBACK_MODE"
            echo "Valid modes: immediate, gradual, blue-green"
            exit 1
            ;;
    esac
}

# Auto-detect namespace if not provided
detect_namespace() {
    local service_name="$1"
    
    if [ -n "${NAMESPACE:-}" ]; then
        return 0
    fi
    
    log "Auto-detecting namespace for $service_name..."
    
    # Common namespace mappings
    case "$service_name" in
        api-gateway)
            NAMESPACE="api-gateway"
            ;;
        content-generation|market-intelligence|publishing-service|multimodal-generator)
            NAMESPACE="tier1-services"
            ;;
        cover-designer|sales-monitor)
            NAMESPACE="tier2-services"
            ;;
        personalization|research)
            NAMESPACE="tier3-services"
            ;;
        *)
            # Try to find the service in any namespace
            local found_ns
            found_ns=$(kubectl get rollout "$service_name-canary" --all-namespaces -o jsonpath='{.items[0].metadata.namespace}' 2>/dev/null || echo "")
            
            if [ -n "$found_ns" ]; then
                NAMESPACE="$found_ns"
                info "Found $service_name in namespace: $NAMESPACE"
            else
                error "Could not auto-detect namespace for $service_name"
                error "Please specify namespace manually"
                exit 1
            fi
            ;;
    esac
    
    info "Using namespace: $NAMESPACE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check Argo Rollouts
    if ! kubectl get crd rollouts.argoproj.io &> /dev/null; then
        error "Argo Rollouts CRD not found"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Validate rollout exists and get status
validate_rollout() {
    local service_name="$1"
    local namespace="$2"
    
    log "Validating rollout for $service_name in $namespace..."
    
    # Check if rollout exists
    if ! kubectl get rollout "$service_name-canary" -n "$namespace" &> /dev/null; then
        error "Rollout $service_name-canary not found in namespace $namespace"
        exit 1
    fi
    
    # Get rollout status
    local status=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.phase}')
    local current_step=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.currentStepIndex}' 2>/dev/null || echo "0")
    local canary_weight=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.canaryStatus.weights.canary}' 2>/dev/null || echo "0")
    
    info "Rollout status: $status"
    info "Current step: $current_step"
    info "Canary weight: $canary_weight%"
    
    # Check if rollback is necessary
    case "$status" in
        "Healthy")
            if [ "$FORCE" = true ]; then
                warn "Rollout appears healthy but --force was specified"
            else
                warn "Rollout appears to be healthy. Are you sure you want to rollback?"
                read -p "Continue with rollback? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    info "Rollback cancelled by user"
                    exit 0
                fi
            fi
            ;;
        "Degraded"|"Progressing"|"Paused")
            info "Rollout is in $status state. Rollback is appropriate."
            ;;
        *)
            warn "Unknown rollout status: $status"
            ;;
    esac
    
    export CURRENT_STATUS="$status"
    export CURRENT_STEP="$current_step"
    export CANARY_WEIGHT="$canary_weight"
}

# Send notification
send_notification() {
    local event="$1"
    local service="$2"
    local mode="$3"
    local reason="${4:-No reason provided}"
    
    if [ "$NOTIFICATION_ENABLED" = false ]; then
        return 0
    fi
    
    local webhook_url
    webhook_url=$(kubectl get secret canary-controller-secrets -n canary-system -o jsonpath='{.data.slack-webhook-url}' 2>/dev/null | base64 -d || echo "")
    
    if [ -z "$webhook_url" ]; then
        warn "Slack webhook URL not configured. Skipping notification."
        return 0
    fi
    
    local color="danger"
    case "$event" in
        "started") color="warning" ;;
        "completed") color="good" ;;
        "failed") color="danger" ;;
    esac
    
    local payload=$(cat << EOF
{
    "channel": "#deployments",
    "username": "Rollback Bot",
    "text": "🔄 Canary Rollback: $event",
    "attachments": [
        {
            "color": "$color",
            "fields": [
                {
                    "title": "Service",
                    "value": "$service",
                    "short": true
                },
                {
                    "title": "Mode",
                    "value": "$mode",
                    "short": true
                },
                {
                    "title": "Event",
                    "value": "$event",
                    "short": true
                },
                {
                    "title": "Reason",
                    "value": "$reason",
                    "short": false
                }
            ]
        }
    ]
}
EOF
    )
    
    curl -X POST -H 'Content-type: application/json' --data "$payload" "$webhook_url" &> /dev/null || {
        warn "Failed to send Slack notification"
    }
}

# Immediate rollback
immediate_rollback() {
    local service_name="$1"
    local namespace="$2"
    
    log "Performing immediate rollback for $service_name..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would perform immediate rollback"
        return 0
    fi
    
    # Abort the rollout immediately
    kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
    
    # Wait for abort to complete
    info "Waiting for rollback to complete..."
    sleep 5
    
    # Set traffic to 100% stable
    kubectl patch virtualservice "$service_name-vs" -n "$namespace" --type='json' \
        -p='[{"op": "replace", "path": "/spec/http/0/route/0/weight", "value": 100},
             {"op": "replace", "path": "/spec/http/0/route/1/weight", "value": 0}]' 2>/dev/null || {
        warn "Could not update VirtualService traffic weights. Manual adjustment may be needed."
    }
    
    log "Immediate rollback completed"
}

# Gradual rollback
gradual_rollback() {
    local service_name="$1"
    local namespace="$2"
    
    log "Performing gradual rollback for $service_name..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would perform gradual rollback"
        return 0
    fi
    
    # Get current canary weight
    local current_weight="${CANARY_WEIGHT:-0}"
    
    # Define rollback steps (reverse of canary steps)
    local rollback_steps=(50 25 10 5 0)
    
    # Filter steps based on current weight
    local applicable_steps=()
    for step in "${rollback_steps[@]}"; do
        if [ "$step" -lt "$current_weight" ]; then
            applicable_steps+=("$step")
        fi
    done
    
    # If no applicable steps, go directly to 0
    if [ ${#applicable_steps[@]} -eq 0 ]; then
        applicable_steps=(0)
    fi
    
    log "Gradual rollback steps: ${applicable_steps[*]}"
    
    for weight in "${applicable_steps[@]}"; do
        info "Setting canary traffic to ${weight}%..."
        
        # Update traffic weights
        kubectl argo rollouts set weight "$service_name-canary" "$weight" -n "$namespace"
        
        # Wait for traffic to stabilize
        sleep 30
        
        # Check metrics during rollback
        check_rollback_health "$service_name" "$namespace"
        
        if [ "$weight" -eq 0 ]; then
            break
        fi
    done
    
    # Finally abort the rollout
    kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
    
    log "Gradual rollback completed"
}

# Blue-green rollback
blue_green_rollback() {
    local service_name="$1"
    local namespace="$2"
    
    log "Performing blue-green rollback for $service_name..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would perform blue-green rollback"
        return 0
    fi
    
    # For blue-green, we immediately switch all traffic to stable
    # and then clean up the canary deployment
    
    # Set all traffic to stable (blue)
    kubectl patch virtualservice "$service_name-vs" -n "$namespace" --type='json' \
        -p='[{"op": "replace", "path": "/spec/http/0/route/0/weight", "value": 100},
             {"op": "replace", "path": "/spec/http/0/route/1/weight", "value": 0}]'
    
    # Wait for traffic switch to take effect
    sleep 10
    
    # Verify traffic switch
    check_rollback_health "$service_name" "$namespace"
    
    # Abort the rollout
    kubectl argo rollouts abort "$service_name-canary" -n "$namespace"
    
    log "Blue-green rollback completed"
}

# Check health during rollback
check_rollback_health() {
    local service_name="$1"
    local namespace="$2"
    
    info "Checking service health during rollback..."
    
    # Check if Prometheus is available
    if ! kubectl get service prometheus -n monitoring &> /dev/null; then
        warn "Prometheus not available. Skipping health check."
        return 0
    fi
    
    # Basic health check via service endpoint
    local service_ip
    service_ip=$(kubectl get service "$service_name-stable" -n "$namespace" -o jsonpath='{.spec.clusterIP}' 2>/dev/null || echo "")
    
    if [ -n "$service_ip" ]; then
        # Simple connectivity test
        if kubectl run rollback-health-check --image=nicolaka/netshoot --rm -it --restart=Never -- \
           curl -f "http://$service_ip:8080/health" --max-time 10 &>/dev/null; then
            info "Service health check passed"
        else
            warn "Service health check failed"
        fi
    fi
}

# Monitor rollback progress
monitor_rollback() {
    local service_name="$1"
    local namespace="$2"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would monitor rollback progress"
        return 0
    fi
    
    log "Monitoring rollback progress..."
    
    # Wait for rollout to reach stable state
    if timeout "$ROLLBACK_TIMEOUT" bash -c "
        while true; do
            status=\$(kubectl get rollout $service_name-canary -n $namespace -o jsonpath='{.status.phase}' 2>/dev/null || echo 'NotFound')
            if [ \"\$status\" = 'Healthy' ] || [ \"\$status\" = 'NotFound' ]; then
                break
            fi
            echo \"Waiting for rollback to complete... Current status: \$status\"
            sleep 10
        done
    "; then
        log "Rollback monitoring completed"
        return 0
    else
        error "Rollback monitoring timed out"
        return 1
    fi
}

# Verify rollback completion
verify_rollback() {
    local service_name="$1"
    local namespace="$2"
    
    log "Verifying rollback completion..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify rollback completion"
        return 0
    fi
    
    # Check rollout status
    local status=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
    local canary_weight=$(kubectl get rollout "$service_name-canary" -n "$namespace" -o jsonpath='{.status.canaryStatus.weights.canary}' 2>/dev/null || echo "0")
    
    info "Final rollout status: $status"
    info "Final canary weight: $canary_weight%"
    
    # Verify traffic routing
    if kubectl get virtualservice "$service_name-vs" -n "$namespace" &> /dev/null; then
        local stable_weight=$(kubectl get virtualservice "$service_name-vs" -n "$namespace" -o jsonpath='{.spec.http[0].route[0].weight}' 2>/dev/null || echo "unknown")
        info "Stable traffic weight: $stable_weight%"
        
        if [ "$stable_weight" = "100" ] || [ "$canary_weight" = "0" ]; then
            log "✅ Traffic successfully routed to stable version"
        else
            warn "❓ Traffic routing may not be fully reverted"
        fi
    fi
    
    # Final health check
    check_rollback_health "$service_name" "$namespace"
    
    log "Rollback verification completed"
}

# Generate rollback report
generate_report() {
    local service_name="$1"
    local namespace="$2"
    local mode="$3"
    local reason="$4"
    
    log "Generating rollback report..."
    
    local report_file="/tmp/rollback-report-${service_name}-$(date +%Y%m%d-%H%M%S).json"
    
    cat > "$report_file" << EOF
{
    "rollback_event": {
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "service": "$service_name",
        "namespace": "$namespace",
        "mode": "$mode",
        "reason": "$reason",
        "operator": "$(whoami)",
        "cluster": "$(kubectl config current-context)",
        "initial_status": "$CURRENT_STATUS",
        "initial_step": "$CURRENT_STEP",
        "initial_canary_weight": "$CANARY_WEIGHT"
    }
}
EOF
    
    info "Rollback report saved to: $report_file"
    
    # Optionally send to audit system
    if command -v jq &> /dev/null; then
        jq . "$report_file"
    else
        cat "$report_file"
    fi
}

# Main function
main() {
    log "Starting Publish AI Canary Rollback"
    
    parse_args "$@"
    check_prerequisites
    detect_namespace "$SERVICE_NAME"
    validate_rollout "$SERVICE_NAME" "$NAMESPACE"
    
    # Send notification
    send_notification "started" "$SERVICE_NAME" "$ROLLBACK_MODE" "$REASON"
    
    # Perform rollback based on mode
    case "$ROLLBACK_MODE" in
        immediate)
            immediate_rollback "$SERVICE_NAME" "$NAMESPACE"
            ;;
        gradual)
            gradual_rollback "$SERVICE_NAME" "$NAMESPACE"
            ;;
        blue-green)
            blue_green_rollback "$SERVICE_NAME" "$NAMESPACE"
            ;;
    esac
    
    # Monitor rollback progress
    if monitor_rollback "$SERVICE_NAME" "$NAMESPACE"; then
        verify_rollback "$SERVICE_NAME" "$NAMESPACE"
        generate_report "$SERVICE_NAME" "$NAMESPACE" "$ROLLBACK_MODE" "$REASON"
        
        log "✅ Rollback completed successfully!"
        send_notification "completed" "$SERVICE_NAME" "$ROLLBACK_MODE" "$REASON"
    else
        error "❌ Rollback failed or timed out!"
        send_notification "failed" "$SERVICE_NAME" "$ROLLBACK_MODE" "$REASON"
        exit 1
    fi
}

# Run main function
main "$@"