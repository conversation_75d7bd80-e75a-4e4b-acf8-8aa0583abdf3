apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-dashboards
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  canary-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Canary Deployments Overview",
        "tags": ["canary", "deployments", "istio"],
        "timezone": "browser",
        "refresh": "10s",
        "time": {
          "from": "now-30m",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "Active Canary Deployments",
            "type": "stat",
            "targets": [
              {
                "expr": "count(kube_deployment_labels{label_app=~\".*-canary\"})",
                "legendFormat": "Active Canaries"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 1},
                    {"color": "red", "value": 3}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Canary Success Rate (24h)",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(canary_deployment_success_total[24h])) / sum(rate(canary_deployment_total[24h])) * 100",
                "legendFormat": "Success Rate %"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 80},
                    {"color": "green", "value": 95}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Current Traffic Split",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum by (version) (istio_request_total{destination_service_name=~\".*\", version=~\"stable|canary\"})",
                "legendFormat": "{{version}}"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
          },
          {
            "id": 4,
            "title": "Rollback Events (24h)",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(increase(canary_rollback_total[24h]))",
                "legendFormat": "Rollbacks"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 1},
                    {"color": "red", "value": 5}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
          },
          {
            "id": 5,
            "title": "Canary Request Rate by Service",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (destination_service_name, version) (rate(istio_requests_total{version=\"canary\"}[5m]))",
                "legendFormat": "{{destination_service_name}} (canary)"
              },
              {
                "expr": "sum by (destination_service_name, version) (rate(istio_requests_total{version=\"stable\"}[5m]))",
                "legendFormat": "{{destination_service_name}} (stable)"
              }
            ],
            "yAxes": [
              {
                "label": "Requests/sec",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 6,
            "title": "Canary vs Stable Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (destination_service_name) (rate(istio_requests_total{version=\"canary\", response_code=~\"5.*\"}[5m])) / sum by (destination_service_name) (rate(istio_requests_total{version=\"canary\"}[5m])) * 100",
                "legendFormat": "{{destination_service_name}} (canary)"
              },
              {
                "expr": "sum by (destination_service_name) (rate(istio_requests_total{version=\"stable\", response_code=~\"5.*\"}[5m])) / sum by (destination_service_name) (rate(istio_requests_total{version=\"stable\"}[5m])) * 100",
                "legendFormat": "{{destination_service_name}} (stable)"
              }
            ],
            "yAxes": [
              {
                "label": "Error Rate %",
                "min": 0,
                "max": 10
              }
            ],
            "thresholds": [
              {
                "value": 1,
                "colorMode": "critical",
                "op": "gt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 7,
            "title": "Canary vs Stable Latency (P99)",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.99, sum by (destination_service_name, le) (rate(istio_request_duration_milliseconds_bucket{version=\"canary\"}[5m])))",
                "legendFormat": "{{destination_service_name}} (canary P99)"
              },
              {
                "expr": "histogram_quantile(0.99, sum by (destination_service_name, le) (rate(istio_request_duration_milliseconds_bucket{version=\"stable\"}[5m])))",
                "legendFormat": "{{destination_service_name}} (stable P99)"
              }
            ],
            "yAxes": [
              {
                "label": "Latency (ms)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
          },
          {
            "id": 8,
            "title": "Deployment Timeline",
            "type": "table",
            "targets": [
              {
                "expr": "canary_deployment_info",
                "format": "table",
                "instant": true
              }
            ],
            "transformations": [
              {
                "id": "organize",
                "options": {
                  "excludeByName": {
                    "__name__": true,
                    "job": true,
                    "instance": true
                  }
                }
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
          }
        ]
      }
    }

  canary-analysis.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Canary Analysis Dashboard",
        "tags": ["canary", "analysis", "metrics"],
        "timezone": "browser",
        "refresh": "30s",
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "templating": {
          "list": [
            {
              "name": "service",
              "type": "query",
              "query": "label_values(istio_requests_total, destination_service_name)",
              "current": {
                "text": "All",
                "value": "$__all"
              },
              "includeAll": true
            },
            {
              "name": "namespace",
              "type": "query",
              "query": "label_values(istio_requests_total{destination_service_name=~\"$service\"}, destination_service_namespace)",
              "current": {
                "text": "All",
                "value": "$__all"
              },
              "includeAll": true
            }
          ]
        },
        "panels": [
          {
            "id": 1,
            "title": "Request Success Rate Comparison",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (version) (rate(istio_requests_total{destination_service_name=~\"$service\", destination_service_namespace=~\"$namespace\", response_code!~\"5.*\"}[5m])) / sum by (version) (rate(istio_requests_total{destination_service_name=~\"$service\", destination_service_namespace=~\"$namespace\"}[5m])) * 100",
                "legendFormat": "{{version}} Success Rate"
              }
            ],
            "yAxes": [
              {
                "label": "Success Rate %",
                "min": 90,
                "max": 100
              }
            ],
            "thresholds": [
              {
                "value": 95,
                "colorMode": "critical",
                "op": "lt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Response Time Distribution",
            "type": "heatmap",
            "targets": [
              {
                "expr": "sum by (le, version) (rate(istio_request_duration_milliseconds_bucket{destination_service_name=~\"$service\", destination_service_namespace=~\"$namespace\"}[5m]))",
                "format": "heatmap",
                "legendFormat": "{{le}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Error Rate by Response Code",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (response_code, version) (rate(istio_requests_total{destination_service_name=~\"$service\", destination_service_namespace=~\"$namespace\", response_code=~\"[45].*\"}[5m]))",
                "legendFormat": "{{version}} - {{response_code}}"
              }
            ],
            "yAxes": [
              {
                "label": "Errors/sec",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Traffic Distribution",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (version) (rate(istio_requests_total{destination_service_name=~\"$service\", destination_service_namespace=~\"$namespace\"}[5m]))",
                "legendFormat": "{{version}} Traffic"
              }
            ],
            "yAxes": [
              {
                "label": "Requests/sec",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 5,
            "title": "Resource Utilization - CPU",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (version) (rate(container_cpu_usage_seconds_total{pod=~\".*$service.*\"}[5m])) * 100",
                "legendFormat": "{{version}} CPU Usage"
              }
            ],
            "yAxes": [
              {
                "label": "CPU %",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
          },
          {
            "id": 6,
            "title": "Resource Utilization - Memory",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (version) (container_memory_usage_bytes{pod=~\".*$service.*\"}) / 1024 / 1024",
                "legendFormat": "{{version}} Memory Usage (MB)"
              }
            ],
            "yAxes": [
              {
                "label": "Memory (MB)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
          }
        ]
      }
    }

  ai-services-canary.json: |
    {
      "dashboard": {
        "id": null,
        "title": "AI Services Canary Analysis",
        "tags": ["canary", "ai", "machine-learning"],
        "timezone": "browser",
        "refresh": "1m",
        "time": {
          "from": "now-2h",
          "to": "now"
        },
        "templating": {
          "list": [
            {
              "name": "ai_service",
              "type": "query",
              "query": "label_values(ai_content_generation_total, service)",
              "current": {
                "text": "content-generation",
                "value": "content-generation"
              }
            }
          ]
        },
        "panels": [
          {
            "id": 1,
            "title": "AI Generation Success Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (version) (rate(ai_content_generation_total{service=~\"$ai_service\", status=\"success\"}[10m])) / sum by (version) (rate(ai_content_generation_total{service=~\"$ai_service\"}[10m])) * 100",
                "legendFormat": "{{version}} Success Rate"
              }
            ],
            "yAxes": [
              {
                "label": "Success Rate %",
                "min": 80,
                "max": 100
              }
            ],
            "thresholds": [
              {
                "value": 95,
                "colorMode": "critical",
                "op": "lt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Content Quality Score",
            "type": "graph",
            "targets": [
              {
                "expr": "avg by (version) (ai_content_quality_score{service=~\"$ai_service\"})",
                "legendFormat": "{{version}} Quality Score"
              }
            ],
            "yAxes": [
              {
                "label": "Quality Score",
                "min": 0,
                "max": 5
              }
            ],
            "thresholds": [
              {
                "value": 4,
                "colorMode": "critical",
                "op": "lt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Generation Latency Distribution",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.50, sum by (version, le) (rate(ai_model_request_duration_milliseconds_bucket{service=~\"$ai_service\"}[10m])))",
                "legendFormat": "{{version}} P50"
              },
              {
                "expr": "histogram_quantile(0.95, sum by (version, le) (rate(ai_model_request_duration_milliseconds_bucket{service=~\"$ai_service\"}[10m])))",
                "legendFormat": "{{version}} P95"
              },
              {
                "expr": "histogram_quantile(0.99, sum by (version, le) (rate(ai_model_request_duration_milliseconds_bucket{service=~\"$ai_service\"}[10m])))",
                "legendFormat": "{{version}} P99"
              }
            ],
            "yAxes": [
              {
                "label": "Latency (ms)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Token Usage Efficiency",
            "type": "graph",
            "targets": [
              {
                "expr": "avg by (version) (ai_token_efficiency_ratio{service=~\"$ai_service\"})",
                "legendFormat": "{{version}} Token Efficiency"
              }
            ],
            "yAxes": [
              {
                "label": "Efficiency Ratio",
                "min": 0,
                "max": 1
              }
            ],
            "thresholds": [
              {
                "value": 0.7,
                "colorMode": "critical",
                "op": "lt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 5,
            "title": "Generation Cost per Request",
            "type": "graph",
            "targets": [
              {
                "expr": "avg by (version) (ai_generation_cost_cents{service=~\"$ai_service\"})",
                "legendFormat": "{{version}} Cost (cents)"
              }
            ],
            "yAxes": [
              {
                "label": "Cost (cents)",
                "min": 0
              }
            ],
            "thresholds": [
              {
                "value": 50,
                "colorMode": "critical",
                "op": "gt"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
          },
          {
            "id": 6,
            "title": "Queue Depth and Processing Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "ai_generation_queue_depth{service=~\"$ai_service\"}",
                "legendFormat": "Queue Depth"
              },
              {
                "expr": "rate(ai_content_generation_total{service=~\"$ai_service\"}[5m]) * 60",
                "legendFormat": "Processing Rate (per minute)"
              }
            ],
            "yAxes": [
              {
                "label": "Count",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
          }
        ]
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-alerts
  namespace: monitoring
data:
  canary-alerts.yaml: |
    groups:
    - name: canary-deployments
      rules:
      # General canary alerts
      - alert: CanaryDeploymentStuck
        expr: |
          (time() - kube_deployment_status_observed_generation{deployment=~".*-canary"}) > 1800
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Canary deployment appears to be stuck"
          description: "Canary deployment {{ $labels.deployment }} has not progressed for over 30 minutes"
      
      - alert: CanaryHighErrorRate
        expr: |
          (
            sum by (destination_service_name) (rate(istio_requests_total{version="canary", response_code=~"5.*"}[5m])) /
            sum by (destination_service_name) (rate(istio_requests_total{version="canary"}[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate in canary deployment"
          description: "Canary version of {{ $labels.destination_service_name }} has error rate of {{ $value | humanizePercentage }}"
      
      - alert: CanaryLatencyDegradation
        expr: |
          (
            histogram_quantile(0.99, 
              sum by (destination_service_name, le) (rate(istio_request_duration_milliseconds_bucket{version="canary"}[5m]))
            ) /
            histogram_quantile(0.99, 
              sum by (destination_service_name, le) (rate(istio_request_duration_milliseconds_bucket{version="stable"}[5m]))
            )
          ) > 1.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Canary deployment showing latency degradation"
          description: "Canary version of {{ $labels.destination_service_name }} is {{ $value | humanize }}x slower than stable"
      
      # AI service specific alerts
      - alert: AICanaryLowQuality
        expr: |
          avg by (service) (ai_content_quality_score{version="canary"}) < 3.5
        for: 15m
        labels:
          severity: critical
        annotations:
          summary: "AI canary deployment producing low quality content"
          description: "Canary version of {{ $labels.service }} has quality score of {{ $value }}"
      
      - alert: AICanaryHighCost
        expr: |
          avg by (service) (ai_generation_cost_cents{version="canary"}) > 100
        for: 20m
        labels:
          severity: warning
        annotations:
          summary: "AI canary deployment showing high costs"
          description: "Canary version of {{ $labels.service }} costs {{ $value }} cents per generation"
      
      - alert: AICanaryLowTokenEfficiency
        expr: |
          avg by (service) (ai_token_efficiency_ratio{version="canary"}) < 0.5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "AI canary deployment has low token efficiency"
          description: "Canary version of {{ $labels.service }} has token efficiency of {{ $value | humanizePercentage }}"
      
      # Rollback alerts
      - alert: FrequentCanaryRollbacks
        expr: |
          sum by (service) (increase(canary_rollback_total[24h])) > 3
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Frequent canary rollbacks detected"
          description: "Service {{ $labels.service }} has had {{ $value }} rollbacks in the last 24 hours"
      
      - alert: CanaryAnalysisFailure
        expr: |
          increase(argo_rollouts_analysis_run_phase{phase="Failed"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "Canary analysis run failed"
          description: "Analysis run {{ $labels.name }} failed for rollout {{ $labels.rollout }}"