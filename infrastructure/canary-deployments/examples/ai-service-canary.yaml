apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: content-generation-canary
  namespace: tier1-services
  labels:
    app: content-generation
    deployment-strategy: canary
    service-tier: tier1
spec:
  replicas: 3
  strategy:
    canary:
      # AI services require special handling due to longer processing times
      canaryService: content-generation-canary
      stableService: content-generation-stable
      
      # Analysis configuration for AI workloads
      analysis:
        templates:
        - templateName: ai-service-analysis
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
        - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
        
        # Extended analysis period for AI services
        startingStep: 1
        args:
        - name: service-name
          value: content-generation
        - name: namespace
          value: tier1-services
      
      # Traffic routing via Istio
      trafficRouting:
        istio:
          virtualService:
            name: content-generation-vs
            routes:
            - primary
          destinationRule:
            name: content-generation-dr
            canarySubsetName: canary
            stableSubsetName: stable
      
      # Conservative canary steps for AI services
      steps:
      # Step 1: 1% traffic - minimal validation
      - setWeight: 1
      - pause:
          duration: 10m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
      
      # Step 2: 5% traffic - basic functionality
      - setWeight: 5
      - pause:
          duration: 15m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
      
      # Step 3: 10% traffic - extended validation
      - setWeight: 10
      - pause:
          duration: 20m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
      
      # Step 4: 25% traffic - moderate load
      - setWeight: 25
      - pause:
          duration: 25m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
      
      # Step 5: 50% traffic - high load validation
      - setWeight: 50
      - pause:
          duration: 30m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
      
      # Step 6: 100% traffic - full promotion
      - setWeight: 100
      - pause:
          duration: 10m
      - analysis:
          templates:
          - templateName: ai-service-analysis
          - templateName: ai-business-metrics
          args:
          - name: service-name
            value: content-generation
          - name: namespace
            value: tier1-services
  
  selector:
    matchLabels:
      app: content-generation
  template:
    metadata:
      labels:
        app: content-generation
        service-tier: tier1
        ai-service: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
        sidecar.istio.io/inject: "true"
    spec:
      serviceAccountName: content-generation
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: content-generation
        image: publish-ai/content-generation:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: APP_ENV
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: AI_MODEL_PROVIDER
          value: "openai"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: anthropic-api-key
        - name: MAX_CONCURRENT_GENERATIONS
          value: "3"
        - name: GENERATION_TIMEOUT
          value: "300s"
        - name: CANARY_MODE
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['rollouts-pod-template-hash']
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 2
        volumeMounts:
        - name: config
          mountPath: /etc/config
          readOnly: true
        - name: ai-models-cache
          mountPath: /var/cache/ai-models
      volumes:
      - name: config
        configMap:
          name: content-generation-config
      - name: ai-models-cache
        emptyDir:
          sizeLimit: 10Gi

---
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: ai-service-analysis
  namespace: tier1-services
spec:
  args:
  - name: service-name
  - name: namespace
  metrics:
  # Standard metrics with AI-specific thresholds
  - name: success-rate
    interval: 120s  # Longer interval for AI services
    count: 5
    successCondition: result[0] >= 0.95
    failureCondition: result[0] < 0.90
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}", response_code!~"5.*"}[10m])) /
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[10m]))
  
  - name: error-rate
    interval: 120s
    count: 5
    successCondition: result[0] <= 0.02
    failureCondition: result[0] > 0.05
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}", response_code=~"5.*"}[10m])) /
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[10m]))
  
  - name: latency-p99
    interval: 120s
    count: 5
    successCondition: result[0] <= 30000  # 30 seconds for AI operations
    failureCondition: result[0] > 60000   # 60 seconds failure threshold
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          histogram_quantile(0.99,
            sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[10m])) by (le)
          )
  
  # AI-specific metrics
  - name: generation-queue-depth
    interval: 60s
    count: 3
    successCondition: result[0] <= 10
    failureCondition: result[0] > 50
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          ai_generation_queue_depth{service="{{args.service-name}}", namespace="{{args.namespace}}"}
  
  - name: ai-model-response-time
    interval: 180s  # AI model calls are slow
    count: 3
    successCondition: result[0] <= 20000  # 20 seconds
    failureCondition: result[0] > 45000   # 45 seconds
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          avg(ai_model_request_duration_milliseconds{service="{{args.service-name}}", namespace="{{args.namespace}}"})

---
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: ai-business-metrics
  namespace: tier1-services
spec:
  args:
  - name: service-name
  - name: namespace
  metrics:
  # Content generation success rate
  - name: generation-success-rate
    interval: 180s
    count: 5
    successCondition: result[0] >= 0.95
    failureCondition: result[0] < 0.85
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(ai_content_generation_total{service="{{args.service-name}}", namespace="{{args.namespace}}", status="success"}[10m])) /
          sum(rate(ai_content_generation_total{service="{{args.service-name}}", namespace="{{args.namespace}}"}[10m]))
  
  # Content quality score
  - name: content-quality-score
    interval: 300s  # Quality assessment takes time
    count: 3
    successCondition: result[0] >= 4.0
    failureCondition: result[0] < 3.5
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          avg(ai_content_quality_score{service="{{args.service-name}}", namespace="{{args.namespace}}"})
  
  # Token usage efficiency
  - name: token-usage-efficiency
    interval: 240s
    count: 3
    successCondition: result[0] >= 0.7
    failureCondition: result[0] < 0.5
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          avg(ai_token_efficiency_ratio{service="{{args.service-name}}", namespace="{{args.namespace}}"})
  
  # Cost per generation (in cents)
  - name: cost-per-generation
    interval: 300s
    count: 3
    successCondition: result[0] <= 50  # $0.50 per generation
    failureCondition: result[0] > 100  # $1.00 per generation
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          avg(ai_generation_cost_cents{service="{{args.service-name}}", namespace="{{args.namespace}}"})

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: content-generation-vs
  namespace: tier1-services
  labels:
    app: content-generation
spec:
  hosts:
  - content-generation.tier1-services.svc.cluster.local
  gateways:
  - mesh
  http:
  # Health endpoints (no canary routing)
  - match:
    - uri:
        prefix: /health
    - uri:
        prefix: /ready
    - uri:
        prefix: /metrics
    route:
    - destination:
        host: content-generation-stable.tier1-services.svc.cluster.local
        port:
          number: 8080
      weight: 100
    timeout: 30s
    
  # AI generation endpoints (canary routing)
  - name: primary
    match:
    - uri:
        prefix: /api/generate
    - uri:
        prefix: /api/content
    route:
    - destination:
        host: content-generation-stable.tier1-services.svc.cluster.local
        port:
          number: 8080
        subset: stable
      weight: 100
    - destination:
        host: content-generation-canary.tier1-services.svc.cluster.local
        port:
          number: 8080
        subset: canary
      weight: 0
    timeout: 300s  # Long timeout for AI operations
    headers:
      request:
        add:
          x-ai-service: content-generation
          x-request-timeout: "300000"
    retries:
      attempts: 2  # Fewer retries for AI services
      perTryTimeout: 120s
      retryOn: 5xx,reset,connect-failure

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: content-generation-dr
  namespace: tier1-services
  labels:
    app: content-generation
spec:
  host: content-generation.tier1-services.svc.cluster.local
  subsets:
  - name: stable
    labels:
      app: content-generation
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 20  # Limited for AI services
          connectTimeout: 30s
        http:
          http1MaxPendingRequests: 10
          http2MaxRequests: 20
          maxRequestsPerConnection: 3
          maxRetries: 2
          timeout: 300s  # Long timeout for AI operations
      loadBalancer:
        simple: LEAST_CONN
      outlierDetection:
        consecutiveGatewayErrors: 3
        consecutive5xxErrors: 3
        interval: 60s
        baseEjectionTime: 60s
        maxEjectionPercent: 30
  - name: canary
    labels:
      app: content-generation
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 10  # Even more limited for canary
          connectTimeout: 30s
        http:
          http1MaxPendingRequests: 5
          http2MaxRequests: 10
          maxRequestsPerConnection: 2
          maxRetries: 1
          timeout: 300s
      loadBalancer:
        simple: LEAST_CONN
      outlierDetection:
        consecutiveGatewayErrors: 2
        consecutive5xxErrors: 2
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50  # More aggressive ejection for canary

---
apiVersion: v1
kind: Service
metadata:
  name: content-generation-stable
  namespace: tier1-services
  labels:
    app: content-generation
    service-type: stable
    ai-service: "true"
spec:
  selector:
    app: content-generation
  ports:
  - name: http
    port: 8080
    targetPort: 8080

---
apiVersion: v1
kind: Service
metadata:
  name: content-generation-canary
  namespace: tier1-services
  labels:
    app: content-generation
    service-type: canary
    ai-service: "true"
spec:
  selector:
    app: content-generation
  ports:
  - name: http
    port: 8080
    targetPort: 8080

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: content-generation-canary
  namespace: tier1-services
  labels:
    app: content-generation
    monitoring: canary
spec:
  selector:
    matchLabels:
      ai-service: "true"
  endpoints:
  - port: http
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s
  - port: http
    interval: 60s
    path: /ai-metrics
    scrapeTimeout: 30s

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-service-canary-alerts
  namespace: tier1-services
data:
  alerts.yaml: |
    groups:
    - name: ai-service-canary
      rules:
      - alert: AIServiceCanaryHighLatency
        expr: |
          histogram_quantile(0.99,
            sum(rate(istio_request_duration_milliseconds_bucket{
              destination_service_name="content-generation",
              destination_service_namespace="tier1-services"
            }[5m])) by (le)
          ) > 60000
        for: 5m
        labels:
          severity: warning
          service: content-generation
          deployment_type: canary
        annotations:
          summary: "AI service canary showing high latency"
          description: "Content generation canary P99 latency is {{ $value }}ms"
      
      - alert: AIServiceCanaryLowQuality
        expr: |
          avg(ai_content_quality_score{
            service="content-generation",
            namespace="tier1-services"
          }) < 3.5
        for: 10m
        labels:
          severity: critical
          service: content-generation
          deployment_type: canary
        annotations:
          summary: "AI service canary producing low quality content"
          description: "Content quality score dropped to {{ $value }}"
      
      - alert: AIServiceCanaryHighCost
        expr: |
          avg(ai_generation_cost_cents{
            service="content-generation",
            namespace="tier1-services"
          }) > 100
        for: 15m
        labels:
          severity: warning
          service: content-generation
          deployment_type: canary
        annotations:
          summary: "AI service canary showing high generation costs"
          description: "Average cost per generation is {{ $value }} cents"