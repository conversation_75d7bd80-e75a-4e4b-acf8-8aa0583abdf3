apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: api-gateway-canary
  namespace: api-gateway
  labels:
    app: api-gateway
    deployment-strategy: canary
spec:
  replicas: 5
  strategy:
    canary:
      # Canary service for traffic splitting
      canaryService: api-gateway-canary
      stableService: api-gateway-stable
      
      # Traffic routing via Istio
      trafficRouting:
        istio:
          virtualService:
            name: api-gateway-vs
            routes:
            - primary
          destinationRule:
            name: api-gateway-dr
            canarySubsetName: canary
            stableSubsetName: stable
      
      # Analysis configuration
      analysis:
        templates:
        - templateName: success-rate-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
        - templateName: latency-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
          - name: latency-threshold
            value: "100"
        
        # Analysis timing
        startingStep: 1
        args:
        - name: service-name
          value: api-gateway
        - name: namespace
          value: api-gateway
      
      # Canary deployment steps
      steps:
      # Step 1: 5% traffic to canary
      - setWeight: 5
      - pause:
          duration: 5m
      - analysis:
          templates:
          - templateName: success-rate-analysis
            args:
            - name: service-name
              value: api-gateway
            - name: namespace
              value: api-gateway
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
      
      # Step 2: 10% traffic to canary
      - setWeight: 10
      - pause:
          duration: 10m
      - analysis:
          templates:
          - templateName: success-rate-analysis
          - templateName: latency-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
          - name: latency-threshold
            value: "100"
      
      # Step 3: 25% traffic to canary
      - setWeight: 25
      - pause:
          duration: 10m
      - analysis:
          templates:
          - templateName: success-rate-analysis
          - templateName: latency-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
          - name: latency-threshold
            value: "100"
      
      # Step 4: 50% traffic to canary
      - setWeight: 50
      - pause:
          duration: 15m
      - analysis:
          templates:
          - templateName: success-rate-analysis
          - templateName: latency-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
          - name: latency-threshold
            value: "100"
      
      # Step 5: 100% traffic to canary (full promotion)
      - setWeight: 100
      - pause:
          duration: 5m
      - analysis:
          templates:
          - templateName: success-rate-analysis
          - templateName: latency-analysis
          args:
          - name: service-name
            value: api-gateway
          - name: namespace
            value: api-gateway
          - name: latency-threshold
            value: "100"
  
  # Pod template specification
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: api-gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: api-gateway
        image: publish-ai/api-gateway:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8443
          name: https
        env:
        - name: APP_ENV
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: METRICS_ENABLED
          value: "true"
        - name: ISTIO_SIDECAR
          value: "true"
        - name: DEPLOYMENT_VERSION
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['rollouts-pod-template-hash']
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        volumeMounts:
        - name: config
          mountPath: /etc/config
          readOnly: true
        - name: tls-certs
          mountPath: /etc/ssl/certs
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: api-gateway-config
      - name: tls-certs
        secret:
          secretName: api-gateway-tls

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-stable
  namespace: api-gateway
  labels:
    app: api-gateway
    service-type: stable
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: https
    port: 8443
    targetPort: 8443

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-canary
  namespace: api-gateway
  labels:
    app: api-gateway
    service-type: canary
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: https
    port: 8443
    targetPort: 8443

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-gateway-vs
  namespace: api-gateway
  labels:
    app: api-gateway
spec:
  hosts:
  - api.publish-ai.local
  - api-gateway.api-gateway.svc.cluster.local
  gateways:
  - istio-system/publish-ai-gateway
  - mesh
  http:
  # Health check endpoints (no canary routing)
  - match:
    - uri:
        prefix: /health
    - uri:
        prefix: /ready
    - uri:
        prefix: /metrics
    route:
    - destination:
        host: api-gateway-stable.api-gateway.svc.cluster.local
        port:
          number: 8080
      weight: 100
    timeout: 30s
    
  # Main API traffic (canary routing enabled)
  - name: primary
    match:
    - uri:
        prefix: /api/
    route:
    - destination:
        host: api-gateway-stable.api-gateway.svc.cluster.local
        port:
          number: 8080
        subset: stable
      weight: 100
    - destination:
        host: api-gateway-canary.api-gateway.svc.cluster.local
        port:
          number: 8080
        subset: canary
      weight: 0
    timeout: 300s
    headers:
      request:
        add:
          x-forwarded-proto: https
          x-canary-deployment: "true"
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 2s
    retries:
      attempts: 3
      perTryTimeout: 30s
      retryOn: 5xx,reset,connect-failure,refused-stream
  
  # Admin endpoints (no canary, stable only)
  - match:
    - uri:
        prefix: /admin
    route:
    - destination:
        host: api-gateway-stable.api-gateway.svc.cluster.local
        port:
          number: 8080
      weight: 100
    timeout: 60s

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: api-gateway-dr
  namespace: api-gateway
  labels:
    app: api-gateway
spec:
  host: api-gateway.api-gateway.svc.cluster.local
  subsets:
  - name: stable
    labels:
      app: api-gateway
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 100
          connectTimeout: 30s
        http:
          http1MaxPendingRequests: 50
          http2MaxRequests: 100
          maxRequestsPerConnection: 10
          maxRetries: 3
      loadBalancer:
        simple: LEAST_CONN
      outlierDetection:
        consecutiveGatewayErrors: 5
        consecutive5xxErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50
  - name: canary
    labels:
      app: api-gateway
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 50
          connectTimeout: 30s
        http:
          http1MaxPendingRequests: 25
          http2MaxRequests: 50
          maxRequestsPerConnection: 5
          maxRetries: 3
      loadBalancer:
        simple: LEAST_CONN
      outlierDetection:
        consecutiveGatewayErrors: 3
        consecutive5xxErrors: 3
        interval: 20s
        baseEjectionTime: 20s
        maxEjectionPercent: 30

---
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: api-gateway-business-metrics
  namespace: api-gateway
spec:
  args:
  - name: service-name
    value: api-gateway
  - name: namespace
    value: api-gateway
  metrics:
  - name: authentication-success-rate
    interval: 60s
    count: 5
    successCondition: result[0] >= 0.98
    failureCondition: result[0] < 0.95
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(http_requests_total{service="{{args.service-name}}", namespace="{{args.namespace}}", endpoint="/api/auth/login", status=~"2.*"}[5m])) /
          sum(rate(http_requests_total{service="{{args.service-name}}", namespace="{{args.namespace}}", endpoint="/api/auth/login"}[5m]))
  
  - name: api-gateway-throughput
    interval: 60s
    count: 3
    successCondition: result[0] >= 100
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(http_requests_total{service="{{args.service-name}}", namespace="{{args.namespace}}"}[5m]))
  
  - name: circuit-breaker-status
    interval: 30s
    count: 10
    successCondition: result[0] < 0.1
    failureCondition: result[0] > 0.2
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(circuit_breaker_open_total{service="{{args.service-name}}", namespace="{{args.namespace}}"}[2m])) /
          sum(rate(circuit_breaker_total{service="{{args.service-name}}", namespace="{{args.namespace}}"}[2m]))

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-canary-notifications
  namespace: api-gateway
data:
  webhook-config.yaml: |
    webhooks:
      - name: slack-deployments
        url: "${SLACK_WEBHOOK_URL}"
        events:
          - rollout-started
          - rollout-completed
          - rollout-failed
          - analysis-failed
          - rollback-triggered
        headers:
          Content-Type: "application/json"
        template: |
          {
            "channel": "#deployments",
            "username": "Canary Bot",
            "text": "API Gateway Canary Deployment: {{.Event}}",
            "attachments": [
              {
                "color": "{{if eq .Event 'rollout-completed'}}good{{else if eq .Event 'rollout-failed'}}danger{{else}}warning{{end}}",
                "fields": [
                  {
                    "title": "Service",
                    "value": "{{.Service}}",
                    "short": true
                  },
                  {
                    "title": "Version",
                    "value": "{{.Version}}",
                    "short": true
                  },
                  {
                    "title": "Status",
                    "value": "{{.Status}}",
                    "short": true
                  },
                  {
                    "title": "Traffic Split",
                    "value": "{{.TrafficSplit}}",
                    "short": true
                  }
                ]
              }
            ]
          }
      
      - name: pagerduty-critical
        url: "https://events.pagerduty.com/v2/enqueue"
        events:
          - rollout-failed
          - analysis-failed
          - rollback-triggered
        headers:
          Content-Type: "application/json"
          Authorization: "Token token=${PAGERDUTY_TOKEN}"
        template: |
          {
            "routing_key": "${PAGERDUTY_ROUTING_KEY}",
            "event_action": "trigger",
            "payload": {
              "summary": "API Gateway Canary Deployment {{.Event}}",
              "severity": "error",
              "source": "canary-controller",
              "component": "{{.Service}}",
              "group": "deployments",
              "class": "canary-failure"
            }
          }

---
apiVersion: batch/v1
kind: Job
metadata:
  name: api-gateway-canary-setup
  namespace: api-gateway
  labels:
    app: api-gateway
    job-type: canary-setup
spec:
  template:
    spec:
      serviceAccountName: api-gateway
      restartPolicy: Never
      containers:
      - name: setup
        image: bitnami/kubectl:latest
        command:
        - /bin/bash
        - -c
        - |
          echo "Setting up API Gateway canary deployment infrastructure..."
          
          # Verify Istio injection is enabled
          kubectl get namespace api-gateway -o jsonpath='{.metadata.labels.istio-injection}' | grep -q enabled || {
            echo "Enabling Istio injection for api-gateway namespace"
            kubectl label namespace api-gateway istio-injection=enabled --overwrite
          }
          
          # Create initial VirtualService and DestinationRule if they don't exist
          kubectl get virtualservice api-gateway-vs -n api-gateway || {
            echo "Creating initial VirtualService for canary deployment"
            kubectl apply -f /etc/config/initial-virtualservice.yaml
          }
          
          kubectl get destinationrule api-gateway-dr -n api-gateway || {
            echo "Creating initial DestinationRule for canary deployment"
            kubectl apply -f /etc/config/initial-destinationrule.yaml
          }
          
          # Verify Prometheus is accessible
          curl -f http://prometheus.monitoring.svc.cluster.local:9090/-/healthy || {
            echo "WARNING: Prometheus is not accessible. Canary analysis may fail."
          }
          
          echo "API Gateway canary setup completed successfully"
        volumeMounts:
        - name: config
          mountPath: /etc/config
      volumes:
      - name: config
        configMap:
          name: api-gateway-canary-config