apiVersion: v1
kind: Namespace
metadata:
  name: canary-system
  labels:
    istio-injection: enabled

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: canary-controller
  namespace: canary-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: canary-controller
rules:
- apiGroups: [""]
  resources: ["services", "endpoints", "pods"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules", "gateways"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["argoproj.io"]
  resources: ["rollouts", "analysistemplates", "analysisruns"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["*"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: canary-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: canary-controller
subjects:
- kind: ServiceAccount
  name: canary-controller
  namespace: canary-system

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-controller-config
  namespace: canary-system
data:
  config.yaml: |
    # Global canary deployment configuration
    global:
      default_strategy: progressive
      max_concurrent_canaries: 3
      business_hours_only: true
      notification_enabled: true
      
    # Service-specific configurations
    services:
      api-gateway:
        namespace: api-gateway
        strategy: progressive
        steps: [5, 10, 25, 50, 100]
        step_duration: "10m"
        success_criteria:
          error_rate_threshold: 0.1
          latency_p99_threshold: 100
          min_request_count: 100
        rollback_criteria:
          error_rate_threshold: 1.0
          latency_p99_threshold: 500
          consecutive_failures: 3
        
      content-generation:
        namespace: tier1-services
        strategy: progressive
        steps: [1, 5, 10, 25, 50, 100]
        step_duration: "15m"
        success_criteria:
          error_rate_threshold: 0.5
          latency_p99_threshold: 2000
          min_request_count: 50
        rollback_criteria:
          error_rate_threshold: 2.0
          latency_p99_threshold: 5000
          consecutive_failures: 2
        custom_metrics:
          - name: ai_generation_success_rate
            threshold: 95.0
            comparison: ">"
          - name: content_quality_score
            threshold: 4.0
            comparison: ">"
            
      market-intelligence:
        namespace: tier1-services
        strategy: progressive
        steps: [5, 15, 35, 70, 100]
        step_duration: "8m"
        success_criteria:
          error_rate_threshold: 0.3
          latency_p99_threshold: 1500
          min_request_count: 30
        rollback_criteria:
          error_rate_threshold: 1.5
          latency_p99_threshold: 3000
          consecutive_failures: 2
          
      publishing-service:
        namespace: tier1-services
        strategy: blue_green_canary
        canary_validation_traffic: 5
        step_duration: "20m"
        success_criteria:
          error_rate_threshold: 0.1
          latency_p99_threshold: 3000
          min_request_count: 20
        rollback_criteria:
          error_rate_threshold: 0.5
          latency_p99_threshold: 10000
          consecutive_failures: 1
        business_metrics:
          - name: publishing_success_rate
            threshold: 99.0
            comparison: ">"
            
      cover-designer:
        namespace: tier2-services
        strategy: progressive
        steps: [10, 25, 50, 100]
        step_duration: "12m"
        success_criteria:
          error_rate_threshold: 0.5
          latency_p99_threshold: 5000
          min_request_count: 20
        rollback_criteria:
          error_rate_threshold: 2.0
          latency_p99_threshold: 15000
          consecutive_failures: 2
          
      sales-monitor:
        namespace: tier2-services
        strategy: progressive
        steps: [15, 40, 80, 100]
        step_duration: "10m"
        success_criteria:
          error_rate_threshold: 0.3
          latency_p99_threshold: 800
          min_request_count: 50
        rollback_criteria:
          error_rate_threshold: 1.0
          latency_p99_threshold: 2000
          consecutive_failures: 3
          
    # Monitoring configuration
    monitoring:
      prometheus_url: "http://prometheus.monitoring.svc.cluster.local:9090"
      grafana_url: "http://grafana.monitoring.svc.cluster.local:3000"
      metrics_interval: "30s"
      
    # Notification configuration
    notifications:
      slack:
        webhook_url: "${SLACK_WEBHOOK_URL}"
        channel: "#deployments"
        events: ["started", "completed", "failed", "rollback"]
      email:
        smtp_server: "${SMTP_SERVER}"
        recipients: ["<EMAIL>", "<EMAIL>"]
        events: ["failed", "rollback"]
      pagerduty:
        integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
        events: ["failed", "rollback"]
        
    # Security policies
    security:
      deployment_windows:
        monday_friday: "09:00-17:00"
        saturday: "10:00-14:00"
        sunday: false
      approval_required_services: ["api-gateway", "publishing-service"]
      restricted_endpoints:
        - path: "/api/admin/*"
          max_canary_traffic: 5
        - path: "/api/publish/*"
          max_canary_traffic: 10

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: canary-controller
  namespace: canary-system
  labels:
    app: canary-controller
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: canary-controller
  template:
    metadata:
      labels:
        app: canary-controller
        version: v1.0.0
    spec:
      serviceAccountName: canary-controller
      containers:
      - name: controller
        image: publish-ai/canary-controller:v1.0.0
        ports:
        - containerPort: 8080
          name: http-metrics
        - containerPort: 9090
          name: http-webhook
        env:
        - name: CONFIG_PATH
          value: "/etc/config/config.yaml"
        - name: PROMETHEUS_URL
          value: "http://prometheus.monitoring.svc.cluster.local:9090"
        - name: GRAFANA_URL
          value: "http://grafana.monitoring.svc.cluster.local:3000"
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: canary-controller-secrets
              key: slack-webhook-url
              optional: true
        - name: SMTP_SERVER
          valueFrom:
            secretKeyRef:
              name: canary-controller-secrets
              key: smtp-server
              optional: true
        - name: PAGERDUTY_INTEGRATION_KEY
          valueFrom:
            secretKeyRef:
              name: canary-controller-secrets
              key: pagerduty-integration-key
              optional: true
        volumeMounts:
        - name: config
          mountPath: /etc/config
          readOnly: true
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: canary-controller-config

---
apiVersion: v1
kind: Service
metadata:
  name: canary-controller
  namespace: canary-system
  labels:
    app: canary-controller
spec:
  selector:
    app: canary-controller
  ports:
  - name: http-metrics
    port: 8080
    targetPort: 8080
  - name: http-webhook
    port: 9090
    targetPort: 9090

---
apiVersion: v1
kind: Service
metadata:
  name: canary-controller-metrics
  namespace: canary-system
  labels:
    app: canary-controller
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: canary-controller
  ports:
  - name: metrics
    port: 8080
    targetPort: 8080

---
apiVersion: v1
kind: Secret
metadata:
  name: canary-controller-secrets
  namespace: canary-system
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  slack-webhook-url: aHR0cHM6Ly9ob29rcy5zbGFjay5jb20vc2VydmljZXMvLi4u  # https://hooks.slack.com/services/...
  smtp-server: c210cC5leGFtcGxlLmNvbTo1ODc=  # smtp.example.com:587
  pagerduty-integration-key: eW91ci1wYWdlcmR1dHktaW50ZWdyYXRpb24ta2V5  # your-pagerduty-integration-key

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: canary-controller
  namespace: canary-system
  labels:
    app: canary-controller
spec:
  selector:
    matchLabels:
      app: canary-controller
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: canary-controller-dr
  namespace: canary-system
spec:
  host: canary-controller.canary-system.svc.cluster.local
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 10
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 5
        http2MaxRequests: 10
        maxRequestsPerConnection: 2
        maxRetries: 3
        timeout: 60s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: canary-controller-access
  namespace: canary-system
spec:
  selector:
    matchLabels:
      app: canary-controller
  rules:
  # Allow access from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
    to:
    - operation:
        paths: ["/metrics", "/health", "/ready"]
  
  # Allow webhook access from external systems
  - to:
    - operation:
        paths: ["/webhook/*"]
        ports: ["9090"]
  
  # Allow inter-service communication within canary-system
  - from:
    - source:
        namespaces: ["canary-system"]

---
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: success-rate-analysis
  namespace: canary-system
spec:
  args:
  - name: service-name
  - name: namespace
  metrics:
  - name: success-rate
    interval: 60s
    count: 5
    successCondition: result[0] >= 0.95
    failureCondition: result[0] < 0.90
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}", response_code!~"5.*"}[5m])) /
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[5m]))
  - name: error-rate
    interval: 60s
    count: 5
    successCondition: result[0] <= 0.01
    failureCondition: result[0] > 0.05
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}", response_code=~"5.*"}[5m])) /
          sum(rate(istio_requests_total{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[5m]))

---
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: latency-analysis
  namespace: canary-system
spec:
  args:
  - name: service-name
  - name: namespace
  - name: latency-threshold
    value: "500"
  metrics:
  - name: latency-p99
    interval: 60s
    count: 5
    successCondition: result[0] <= {{args.latency-threshold}}
    failureCondition: result[0] > {{args.latency-threshold}} * 2
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          histogram_quantile(0.99,
            sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[5m])) by (le)
          )
  - name: latency-p95
    interval: 60s
    count: 5
    successCondition: result[0] <= {{args.latency-threshold}} * 0.8
    provider:
      prometheus:
        address: http://prometheus.monitoring.svc.cluster.local:9090
        query: |
          histogram_quantile(0.95,
            sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name="{{args.service-name}}", destination_service_namespace="{{args.namespace}}"}[5m])) by (le)
          )

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-scripts
  namespace: canary-system
data:
  deploy-canary.sh: |
    #!/bin/bash
    # Canary deployment script
    set -euo pipefail
    
    SERVICE_NAME=${1:-}
    SERVICE_NAMESPACE=${2:-}
    NEW_IMAGE=${3:-}
    
    if [ -z "$SERVICE_NAME" ] || [ -z "$SERVICE_NAMESPACE" ] || [ -z "$NEW_IMAGE" ]; then
        echo "Usage: $0 <service-name> <service-namespace> <new-image>"
        exit 1
    fi
    
    echo "Starting canary deployment for $SERVICE_NAME in $SERVICE_NAMESPACE"
    echo "New image: $NEW_IMAGE"
    
    # Create canary deployment
    kubectl patch deployment $SERVICE_NAME -n $SERVICE_NAMESPACE -p '{"spec":{"template":{"metadata":{"labels":{"version":"canary"}},"spec":{"containers":[{"name":"'$SERVICE_NAME'","image":"'$NEW_IMAGE'"}]}}}}'
    
    # Wait for rollout
    kubectl rollout status deployment/$SERVICE_NAME -n $SERVICE_NAMESPACE --timeout=300s
    
    echo "Canary deployment initiated successfully"
  
  rollback-canary.sh: |
    #!/bin/bash
    # Canary rollback script
    set -euo pipefail
    
    SERVICE_NAME=${1:-}
    SERVICE_NAMESPACE=${2:-}
    
    if [ -z "$SERVICE_NAME" ] || [ -z "$SERVICE_NAMESPACE" ]; then
        echo "Usage: $0 <service-name> <service-namespace>"
        exit 1
    fi
    
    echo "Rolling back canary deployment for $SERVICE_NAME in $SERVICE_NAMESPACE"
    
    # Rollback to previous version
    kubectl rollout undo deployment/$SERVICE_NAME -n $SERVICE_NAMESPACE
    
    # Wait for rollback to complete
    kubectl rollout status deployment/$SERVICE_NAME -n $SERVICE_NAMESPACE --timeout=300s
    
    # Reset traffic to stable version
    kubectl patch virtualservice $SERVICE_NAME-vs -n $SERVICE_NAMESPACE --type='json' -p='[{"op": "replace", "path": "/spec/http/0/route/0/weight", "value": 100}]'
    
    echo "Canary rollback completed successfully"