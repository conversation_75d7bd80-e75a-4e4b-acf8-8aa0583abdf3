# Business Intelligence Service Kubernetes Deployment
# Production-ready deployment with analytics and revenue optimization

apiVersion: v1
kind: Namespace
metadata:
  name: business-intelligence
  labels:
    name: business-intelligence
    project: publish-ai
    component: analytics

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: bi-config
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: config
data:
  # Service Configuration
  HOST: "0.0.0.0"
  PORT: "8000"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Analytics Configuration
  ANALYTICS_ENABLED: "true"
  REVENUE_TRACKING_ENABLED: "true"
  FORECASTING_ENABLED: "true"
  REAL_TIME_PROCESSING: "true"
  EVENT_BATCH_SIZE: "100"
  METRICS_AGGREGATION_INTERVAL: "3600"
  
  # Data Processing
  MAX_CONCURRENT_QUERIES: "10"
  QUERY_TIMEOUT: "300"
  CACHE_TTL: "300"
  DATA_RETENTION_DAYS: "365"
  
  # Reporting Configuration
  REPORT_GENERATION_ENABLED: "true"
  AUTOMATED_REPORTS_ENABLED: "true"
  MAX_REPORT_SIZE_MB: "50"
  REPORT_STORAGE_RETENTION_DAYS: "90"
  
  # Dashboard Configuration
  CUSTOM_DASHBOARD_ENABLED: "true"
  DASHBOARD_REFRESH_INTERVAL: "300"
  MAX_DASHBOARD_WIDGETS: "20"
  REAL_TIME_DASHBOARD_ENABLED: "true"
  
  # Export Configuration
  DATA_EXPORT_ENABLED: "true"
  MAX_EXPORT_RECORDS: "100000"
  EXPORT_FORMATS: "json,csv,excel"
  
  # Performance Configuration
  ASYNC_PROCESSING_ENABLED: "true"
  WORKER_POOL_SIZE: "4"
  MAX_MEMORY_USAGE_MB: "2048"
  
  # External Integrations
  STRIPE_INTEGRATION_ENABLED: "false"
  GOOGLE_ANALYTICS_INTEGRATION_ENABLED: "false"
  MIXPANEL_INTEGRATION_ENABLED: "false"
  
  # Security
  API_RATE_LIMIT: "1000"
  QUERY_COMPLEXITY_LIMIT: "100"
  PII_DETECTION_ENABLED: "true"
  DATA_ANONYMIZATION_ENABLED: "true"
  
  # Monitoring
  PROMETHEUS_ENABLED: "true"
  METRICS_EXPORT_ENABLED: "true"
  HEALTH_CHECK_INTERVAL: "30"
  
  # Email Configuration
  EMAIL_ENABLED: "true"
  EMAIL_SMTP_SERVER: "smtp.gmail.com"
  EMAIL_SMTP_PORT: "587"
  EMAIL_USE_TLS: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: bi-secrets
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: secrets
type: Opaque
stringData:
  # Database Configuration
  SUPABASE_URL: "${SUPABASE_URL}"
  SUPABASE_ANON_KEY: "${SUPABASE_ANON_KEY}"
  SUPABASE_SERVICE_KEY: "${SUPABASE_SERVICE_KEY}"
  DATABASE_URL: "${DATABASE_URL}"
  
  # Redis Configuration
  REDIS_URL: "${REDIS_URL}"
  
  # External API Keys
  STRIPE_API_KEY: "${STRIPE_API_KEY}"
  STRIPE_WEBHOOK_SECRET: "${STRIPE_WEBHOOK_SECRET}"
  GOOGLE_ANALYTICS_KEY: "${GOOGLE_ANALYTICS_KEY}"
  MIXPANEL_TOKEN: "${MIXPANEL_TOKEN}"
  SEGMENT_WRITE_KEY: "${SEGMENT_WRITE_KEY}"
  
  # Email Configuration
  EMAIL_USERNAME: "${EMAIL_USERNAME}"
  EMAIL_PASSWORD: "${EMAIL_PASSWORD}"
  
  # Report Storage
  REPORT_STORAGE_ACCESS_KEY: "${REPORT_STORAGE_ACCESS_KEY}"
  REPORT_STORAGE_SECRET_KEY: "${REPORT_STORAGE_SECRET_KEY}"
  
  # Security
  SECRET_KEY: "${SECRET_KEY}"
  JWT_SECRET_KEY: "${JWT_SECRET_KEY}"
  
  # External Services
  METABASE_API_KEY: "${METABASE_API_KEY}"
  SUPERSET_API_KEY: "${SUPERSET_API_KEY}"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-reports-pvc
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-cache-pvc
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 20Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-data-pvc
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 100Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: business-intelligence-service
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    version: v1
    component: business-intelligence
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: "business-intelligence"
    app.kubernetes.io/part-of: "publish-ai"
    app.kubernetes.io/managed-by: "kubernetes"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: business-intelligence-service
  template:
    metadata:
      labels:
        app: business-intelligence-service
        tier: analytics
        version: v1
        component: business-intelligence
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        kubectl.kubernetes.io/restartedAt: "${RESTART_TIMESTAMP}"
    spec:
      serviceAccountName: business-intelligence-service
      priorityClassName: normal-priority
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        supplementalGroups: [1000]
      
      # Node selection for analytics workloads
      nodeSelector:
        node.kubernetes.io/workload: analytics
        node.kubernetes.io/instance-type: compute-optimized
      
      # Spread across different nodes for high availability
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: [business-intelligence-service]
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: In
                values: ["compute-optimized", "memory-optimized"]
      
      # Toleration for analytics workloads
      tolerations:
      - key: "workload"
        operator: "Equal"
        value: "analytics"
        effect: "NoSchedule"
      
      initContainers:
      # Database migration and setup
      - name: db-migration
        image: python:3.11-slim
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "Running database migrations..."
            pip install -q asyncpg sqlalchemy alembic
            
            # Check database connectivity
            python -c "
            import asyncio
            import asyncpg
            import os
            
            async def check_db():
              try:
                conn = await asyncpg.connect(os.environ['DATABASE_URL'])
                await conn.execute('SELECT 1')
                await conn.close()
                print('Database connection successful')
              except Exception as e:
                print(f'Database connection failed: {e}')
                exit(1)
            
            asyncio.run(check_db())
            "
            
            echo "Database migration completed"
        envFrom:
        - secretRef:
            name: bi-secrets
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      
      containers:
      - name: business-intelligence-service
        image: ghcr.io/publishai/business-intelligence-service:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8080
          protocol: TCP
        
        env:
        - name: REPORTS_DIR
          value: "/app/reports"
        - name: CACHE_DIR
          value: "/app/cache"
        - name: DATA_DIR
          value: "/app/data"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        
        envFrom:
        - configMapRef:
            name: bi-config
        - secretRef:
            name: bi-secrets
        
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
            ephemeral-storage: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
            ephemeral-storage: 10Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health/startup
            port: http
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 20
          successThreshold: 1
        
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - |
                echo "Gracefully shutting down Business Intelligence Service..."
                # Finish processing current analytics jobs
                kill -TERM 1
                sleep 15
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false  # Analytics needs write access
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        
        volumeMounts:
        - name: reports-storage
          mountPath: /app/reports
        - name: cache-storage
          mountPath: /app/cache
        - name: data-storage
          mountPath: /app/data
        - name: tmp
          mountPath: /tmp
        - name: var-tmp
          mountPath: /var/tmp
        - name: app-cache
          mountPath: /app/.cache
      
      volumes:
      - name: reports-storage
        persistentVolumeClaim:
          claimName: bi-reports-pvc
      - name: cache-storage
        persistentVolumeClaim:
          claimName: bi-cache-pvc
      - name: data-storage
        persistentVolumeClaim:
          claimName: bi-data-pvc
      - name: tmp
        emptyDir:
          sizeLimit: 2Gi
      - name: var-tmp
        emptyDir:
          sizeLimit: 2Gi
      - name: app-cache
        emptyDir:
          sizeLimit: 5Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 60

---
apiVersion: v1
kind: Service
metadata:
  name: business-intelligence-service
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: metrics
    protocol: TCP
  selector:
    app: business-intelligence-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: business-intelligence-service
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT:role/business-intelligence-service-role"
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: business-intelligence-service
  namespace: business-intelligence
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: business-intelligence-service
  namespace: business-intelligence
subjects:
- kind: ServiceAccount
  name: business-intelligence-service
  namespace: business-intelligence
roleRef:
  kind: Role
  name: business-intelligence-service
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: business-intelligence-service-hpa
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: business-intelligence-service
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: business-intelligence-service-pdb
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: business-intelligence-service

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: business-intelligence-network-policy
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
spec:
  podSelector:
    matchLabels:
      app: business-intelligence-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from API Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 8000
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8080
  # Allow traffic from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: business-intelligence
    ports:
    - protocol: TCP
      port: 8000
  # Allow traffic from frontend services
  - from:
    - namespaceSelector:
        matchLabels:
          name: frontend
    ports:
    - protocol: TCP
      port: 8000
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS outbound (for external APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow HTTP outbound (for internal services)
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
  # Allow database connections
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  # Allow email (SMTP)
  - to: []
    ports:
    - protocol: TCP
      port: 587  # SMTP TLS
    - protocol: TCP
      port: 465  # SMTP SSL

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: business-intelligence-service-metrics
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: business-intelligence-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s
    honorLabels: true
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'analytics_.*'
      targetLabel: __tmp_analytics_metric
      replacement: 'true'
  namespaceSelector:
    matchNames:
    - business-intelligence

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: business-intelligence-service-alerts
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
    prometheus: kube-prometheus
spec:
  groups:
  - name: business-intelligence.rules
    interval: 30s
    rules:
    - alert: BusinessIntelligenceServiceDown
      expr: up{job="business-intelligence-service"} == 0
      for: 5m
      labels:
        severity: critical
        service: business-intelligence-service
        component: analytics
      annotations:
        summary: "Business Intelligence Service is down"
        description: "Business Intelligence Service has been down for more than 5 minutes"
    
    - alert: HighEventProcessingDelay
      expr: analytics_event_processing_delay_seconds > 300
      for: 10m
      labels:
        severity: warning
        service: business-intelligence-service
        component: analytics
      annotations:
        summary: "High event processing delay"
        description: "Event processing delay is {{ $value }} seconds"
    
    - alert: LowDataQuality
      expr: analytics_data_quality_score < 0.8
      for: 15m
      labels:
        severity: warning
        service: business-intelligence-service
        component: analytics
      annotations:
        summary: "Low data quality detected"
        description: "Data quality score is {{ $value }}, below threshold of 0.8"
    
    - alert: RevenueTrackingFailure
      expr: increase(revenue_tracking_failures_total[5m]) > 5
      for: 5m
      labels:
        severity: critical
        service: business-intelligence-service
        component: revenue
      annotations:
        summary: "Revenue tracking failures detected"
        description: "{{ $value }} revenue tracking failures in the last 5 minutes"
    
    - alert: HighMemoryUsage
      expr: container_memory_usage_bytes{container="business-intelligence-service"} / container_spec_memory_limit_bytes{container="business-intelligence-service"} > 0.9
      for: 10m
      labels:
        severity: warning
        service: business-intelligence-service
        component: analytics
      annotations:
        summary: "High memory usage"
        description: "Memory usage is above 90% for more than 10 minutes"
    
    - alert: ReportGenerationBacklog
      expr: report_generation_queue_size > 20
      for: 30m
      labels:
        severity: warning
        service: business-intelligence-service
        component: reporting
      annotations:
        summary: "Report generation backlog"
        description: "Report generation queue has {{ $value }} pending reports"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: daily-analytics-aggregation
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: batch-job
spec:
  schedule: "0 2 * * *"  # Run at 2 AM daily
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: business-intelligence-service
            component: batch-job
        spec:
          restartPolicy: OnFailure
          containers:
          - name: analytics-aggregation
            image: ghcr.io/publishai/business-intelligence-service:v1.0.0
            command: ["python", "scripts/daily_aggregation.py"]
            envFrom:
            - configMapRef:
                name: bi-config
            - secretRef:
                name: bi-secrets
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 1000m
                memory: 2Gi

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: weekly-revenue-report
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    component: batch-job
spec:
  schedule: "0 9 * * 1"  # Run at 9 AM every Monday
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: business-intelligence-service
            component: batch-job
        spec:
          restartPolicy: OnFailure
          containers:
          - name: revenue-report
            image: ghcr.io/publishai/business-intelligence-service:v1.0.0
            command: ["python", "scripts/weekly_revenue_report.py"]
            envFrom:
            - configMapRef:
                name: bi-config
            - secretRef:
                name: bi-secrets
            resources:
              requests:
                cpu: 300m
                memory: 1Gi
              limits:
                cpu: 1000m
                memory: 2Gi

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: business-intelligence-ingress
  namespace: business-intelligence
  labels:
    app: business-intelligence-service
    tier: analytics
    component: business-intelligence
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Business Intelligence Dashboard"
spec:
  tls:
  - hosts:
    - analytics.publishai.com
    - bi.publishai.com
    secretName: business-intelligence-tls
  rules:
  - host: analytics.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: business-intelligence-service
            port:
              number: 8000
  - host: bi.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: business-intelligence-service
            port:
              number: 8000