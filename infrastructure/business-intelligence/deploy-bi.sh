#!/bin/bash

# Deploy Business Intelligence Service - Advanced Analytics and Revenue Optimization
# Production-ready deployment with comprehensive business intelligence capabilities

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
NAMESPACE="business-intelligence"
SERVICE_NAME="business-intelligence-service"
DEPLOYMENT_NAME="business-intelligence-service"

# Default values
ENVIRONMENT="production"
DRY_RUN=false
WAIT_FOR_READY=true
ANALYTICS_ENABLED=true
FORECASTING_ENABLED=true
REPORTING_ENABLED=true
DASHBOARDS_ENABLED=true
EXTERNAL_INTEGRATIONS=false

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Business Intelligence Service with advanced analytics and revenue optimization.

Options:
  -e, --environment ENV    Environment: development, staging, production (default: production)
  -n, --namespace NAME     Kubernetes namespace (default: business-intelligence)
  -d, --dry-run           Preview deployment without making changes
  -w, --no-wait           Don't wait for rollout to complete
  -a, --no-analytics      Disable analytics features
  -f, --no-forecasting    Disable forecasting features
  -r, --no-reporting      Disable automated reporting
  -D, --no-dashboards     Disable dashboard features
  -x, --external-integrations  Enable external integrations (Stripe, GA, etc.)
  -h, --help              Show this help message

Examples:
  $0                                    # Deploy to production with all features
  $0 -e staging -n bi-staging          # Deploy to staging environment
  $0 -d                                # Dry run to preview changes
  $0 --no-forecasting --no-reporting   # Deploy without forecasting and reporting

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -w|--no-wait)
                WAIT_FOR_READY=false
                shift
                ;;
            -a|--no-analytics)
                ANALYTICS_ENABLED=false
                shift
                ;;
            -f|--no-forecasting)
                FORECASTING_ENABLED=false
                shift
                ;;
            -r|--no-reporting)
                REPORTING_ENABLED=false
                shift
                ;;
            -D|--no-dashboards)
                DASHBOARDS_ENABLED=false
                shift
                ;;
            -x|--external-integrations)
                EXTERNAL_INTEGRATIONS=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if analytics nodes are available (optional)
    analytics_nodes=$(kubectl get nodes -l node.kubernetes.io/workload=analytics --no-headers 2>/dev/null | wc -l || echo "0")
    if [ "$analytics_nodes" -eq 0 ]; then
        warn "No analytics-specific nodes found. Will use general compute nodes."
    else
        info "Found $analytics_nodes analytics-optimized nodes"
    fi
    
    # Check required tools for business intelligence
    if [ "$REPORTING_ENABLED" = true ]; then
        info "Reporting features enabled - PDF and Excel generation available"
    fi
    
    if [ "$FORECASTING_ENABLED" = true ]; then
        info "Forecasting features enabled - Advanced predictions available"
    fi
    
    log "Prerequisites check passed"
}

# Setup namespace and RBAC
setup_namespace() {
    log "Setting up namespace and RBAC..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace and RBAC resources"
        return 0
    fi
    
    # Create namespace if it doesn't exist
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" \
            name="$NAMESPACE" \
            project="publish-ai" \
            component="business-intelligence" \
            environment="$ENVIRONMENT"
    fi
    
    # Apply RBAC and other base resources
    kubectl apply -f - << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    tier: analytics
    component: business-intelligence
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
subjects:
- kind: ServiceAccount
  name: $SERVICE_NAME
  namespace: $NAMESPACE
roleRef:
  kind: Role
  name: $SERVICE_NAME
  apiGroup: rbac.authorization.k8s.io
EOF
    
    log "Namespace and RBAC setup completed"
}

# Create storage resources
setup_storage() {
    log "Setting up persistent storage for analytics..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create storage resources"
        return 0
    fi
    
    # Determine storage class based on environment
    local storage_class="standard"
    local fast_storage_class="fast-ssd"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        storage_class="gp3"
        fast_storage_class="io2"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        storage_class="gp2"
        fast_storage_class="gp3"
    fi
    
    # Environment-specific storage sizes
    local reports_size="50Gi"
    local cache_size="20Gi"
    local data_size="100Gi"
    
    case "$ENVIRONMENT" in
        "development")
            reports_size="10Gi"
            cache_size="5Gi"
            data_size="20Gi"
            ;;
        "staging")
            reports_size="25Gi"
            cache_size="10Gi"
            data_size="50Gi"
            ;;
        "production")
            reports_size="100Gi"
            cache_size="50Gi"
            data_size="500Gi"
            ;;
    esac
    
    # Create PVCs
    kubectl apply -f - << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-reports-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: reports
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $fast_storage_class
  resources:
    requests:
      storage: $reports_size

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-cache-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: cache
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $fast_storage_class
  resources:
    requests:
      storage: $cache_size

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bi-data-pvc
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: storage
    storage-type: data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: $storage_class
  resources:
    requests:
      storage: $data_size
EOF
    
    log "Storage resources created"
}

# Setup configuration and secrets
setup_config() {
    log "Setting up configuration and secrets..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create configuration and secrets"
        return 0
    fi
    
    # Environment-specific configuration
    local replicas=2
    local max_concurrent_queries=10
    local cache_ttl=300
    local report_retention_days=90
    
    case "$ENVIRONMENT" in
        "development")
            replicas=1
            max_concurrent_queries=5
            cache_ttl=600
            report_retention_days=30
            ;;
        "staging")
            replicas=2
            max_concurrent_queries=8
            cache_ttl=300
            report_retention_days=60
            ;;
        "production")
            replicas=3
            max_concurrent_queries=20
            cache_ttl=300
            report_retention_days=365
            ;;
    esac
    
    # Create ConfigMap
    kubectl apply -f - << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: bi-config
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: config
data:
  ENVIRONMENT: "$ENVIRONMENT"
  LOG_LEVEL: "INFO"
  ANALYTICS_ENABLED: "$ANALYTICS_ENABLED"
  FORECASTING_ENABLED: "$FORECASTING_ENABLED"
  REPORTING_ENABLED: "$REPORTING_ENABLED"
  DASHBOARDS_ENABLED: "$DASHBOARDS_ENABLED"
  EXTERNAL_INTEGRATIONS: "$EXTERNAL_INTEGRATIONS"
  MAX_CONCURRENT_QUERIES: "$max_concurrent_queries"
  CACHE_TTL: "$cache_ttl"
  REPORT_RETENTION_DAYS: "$report_retention_days"
  REAL_TIME_PROCESSING: "true"
  DATA_EXPORT_ENABLED: "true"
  ASYNC_PROCESSING_ENABLED: "true"
  PII_DETECTION_ENABLED: "true"
  DATA_ANONYMIZATION_ENABLED: "true"
  PROMETHEUS_ENABLED: "true"
  EMAIL_ENABLED: "true"
EOF
    
    # Create secrets (you should replace these with actual values)
    if ! kubectl get secret bi-secrets -n "$NAMESPACE" &> /dev/null; then
        warn "Creating placeholder secrets. Please update with actual values."
        kubectl create secret generic bi-secrets -n "$NAMESPACE" \
            --from-literal=SUPABASE_URL="${SUPABASE_URL:-placeholder}" \
            --from-literal=SUPABASE_ANON_KEY="${SUPABASE_ANON_KEY:-placeholder}" \
            --from-literal=SUPABASE_SERVICE_KEY="${SUPABASE_SERVICE_KEY:-placeholder}" \
            --from-literal=DATABASE_URL="${DATABASE_URL:-placeholder}" \
            --from-literal=REDIS_URL="${REDIS_URL:-redis://redis:6379}" \
            --from-literal=EMAIL_USERNAME="${EMAIL_USERNAME:-<EMAIL>}" \
            --from-literal=EMAIL_PASSWORD="${EMAIL_PASSWORD:-placeholder}" \
            --from-literal=SECRET_KEY="${SECRET_KEY:-$(openssl rand -hex 32)}" \
            --from-literal=JWT_SECRET_KEY="${JWT_SECRET_KEY:-$(openssl rand -hex 32)}"
        
        # Add external integration secrets if enabled
        if [ "$EXTERNAL_INTEGRATIONS" = true ]; then
            kubectl patch secret bi-secrets -n "$NAMESPACE" --type='merge' -p='{
                "data": {
                    "STRIPE_API_KEY": "'$(echo -n "${STRIPE_API_KEY:-placeholder}" | base64 -w 0)'",
                    "GOOGLE_ANALYTICS_KEY": "'$(echo -n "${GOOGLE_ANALYTICS_KEY:-placeholder}" | base64 -w 0)'",
                    "MIXPANEL_TOKEN": "'$(echo -n "${MIXPANEL_TOKEN:-placeholder}" | base64 -w 0)'"
                }
            }'
        fi
    fi
    
    log "Configuration and secrets setup completed"
}

# Deploy the main service
deploy_service() {
    log "Deploying Business Intelligence Service..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Business Intelligence Service"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/bi-deployment.yaml"
        return 0
    fi
    
    # Modify deployment based on options
    local deployment_file="/tmp/bi-deployment-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/bi-deployment.yaml" "$deployment_file"
    
    # Environment-specific modifications
    sed -i.bak "s/namespace: business-intelligence/namespace: $NAMESPACE/g" "$deployment_file"
    
    # Adjust replicas based on environment
    case "$ENVIRONMENT" in
        "development")
            sed -i.bak 's/replicas: 2/replicas: 1/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 2/minReplicas: 1/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 8/maxReplicas: 3/g' "$deployment_file"
            ;;
        "staging")
            sed -i.bak 's/replicas: 2/replicas: 2/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 2/minReplicas: 2/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 8/maxReplicas: 5/g' "$deployment_file"
            ;;
        "production")
            # Use production values as-is
            ;;
    esac
    
    # Disable features if requested
    if [ "$ANALYTICS_ENABLED" = false ]; then
        sed -i.bak 's/ANALYTICS_ENABLED: "true"/ANALYTICS_ENABLED: "false"/g' "$deployment_file"
    fi
    
    if [ "$FORECASTING_ENABLED" = false ]; then
        sed -i.bak 's/FORECASTING_ENABLED: "true"/FORECASTING_ENABLED: "false"/g' "$deployment_file"
    fi
    
    if [ "$REPORTING_ENABLED" = false ]; then
        sed -i.bak 's/REPORT_GENERATION_ENABLED: "true"/REPORT_GENERATION_ENABLED: "false"/g' "$deployment_file"
    fi
    
    if [ "$DASHBOARDS_ENABLED" = false ]; then
        sed -i.bak 's/CUSTOM_DASHBOARD_ENABLED: "true"/CUSTOM_DASHBOARD_ENABLED: "false"/g' "$deployment_file"
    fi
    
    # Apply the deployment
    kubectl apply -f "$deployment_file"
    
    # Clean up temporary file
    rm -f "$deployment_file" "$deployment_file.bak"
    
    log "Business Intelligence Service deployed"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up comprehensive monitoring..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup monitoring"
        return 0
    fi
    
    # Check if Prometheus Operator is available
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        kubectl apply -f - << EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: business-intelligence-service-metrics
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: $SERVICE_NAME
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: business-intelligence-alerts
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    prometheus: kube-prometheus
spec:
  groups:
  - name: business-intelligence.rules
    rules:
    - alert: BusinessIntelligenceServiceDown
      expr: up{job="business-intelligence-service"} == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Business Intelligence Service is down"
    
    - alert: HighEventProcessingDelay
      expr: analytics_event_processing_delay_seconds > 300
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High event processing delay"
    
    - alert: RevenueTrackingFailure
      expr: increase(revenue_tracking_failures_total[5m]) > 5
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Revenue tracking failures detected"
EOF
        
        log "Monitoring setup completed"
    else
        warn "Prometheus Operator not found. Skipping ServiceMonitor setup."
    fi
}

# Setup scheduled jobs
setup_scheduled_jobs() {
    log "Setting up scheduled analytics jobs..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup scheduled jobs"
        return 0
    fi
    
    # Only create jobs if analytics is enabled
    if [ "$ANALYTICS_ENABLED" = true ]; then
        kubectl apply -f - << EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: daily-analytics-aggregation
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: batch-job
spec:
  schedule: "0 2 * * *"  # Run at 2 AM daily
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: $SERVICE_NAME
            component: batch-job
        spec:
          restartPolicy: OnFailure
          containers:
          - name: analytics-aggregation
            image: ghcr.io/publishai/business-intelligence-service:v1.0.0
            command: ["python", "scripts/daily_aggregation.py"]
            envFrom:
            - configMapRef:
                name: bi-config
            - secretRef:
                name: bi-secrets
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 1000m
                memory: 2Gi
EOF
    fi
    
    # Create reporting job if reporting is enabled
    if [ "$REPORTING_ENABLED" = true ]; then
        kubectl apply -f - << EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: weekly-revenue-report
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME
    component: batch-job
spec:
  schedule: "0 9 * * 1"  # Run at 9 AM every Monday
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: $SERVICE_NAME
            component: batch-job
        spec:
          restartPolicy: OnFailure
          containers:
          - name: revenue-report
            image: ghcr.io/publishai/business-intelligence-service:v1.0.0
            command: ["python", "scripts/weekly_revenue_report.py"]
            envFrom:
            - configMapRef:
                name: bi-config
            - secretRef:
                name: bi-secrets
            resources:
              requests:
                cpu: 300m
                memory: 1Gi
              limits:
                cpu: 1000m
                memory: 2Gi
EOF
    fi
    
    log "Scheduled jobs setup completed"
}

# Wait for deployment to be ready
wait_for_ready() {
    if [ "$WAIT_FOR_READY" = false ]; then
        info "Skipping readiness check"
        return 0
    fi
    
    log "Waiting for Business Intelligence Service to be ready..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would wait for readiness"
        return 0
    fi
    
    # Wait for deployment to be ready
    if kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=600s; then
        log "✅ Business Intelligence Service is ready"
    else
        error "❌ Business Intelligence Service failed to become ready"
        return 1
    fi
    
    # Check service endpoints
    local retries=30
    local count=0
    
    while [ $count -lt $retries ]; do
        if kubectl get endpoints "$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.subsets[0].addresses[0].ip}' &> /dev/null; then
            log "✅ Service endpoints are ready"
            break
        fi
        
        count=$((count + 1))
        info "Waiting for service endpoints... ($count/$retries)"
        sleep 10
    done
    
    if [ $count -eq $retries ]; then
        error "❌ Service endpoints not ready after 5 minutes"
        return 1
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying Business Intelligence Service deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check deployment status
    info "Deployment status:"
    kubectl get deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE"
    
    # Check pod status
    info "Pod status:"
    kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check service status
    info "Service status:"
    kubectl get service "$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check storage
    info "Storage status:"
    kubectl get pvc -n "$NAMESPACE"
    
    # Check HPA
    info "Auto-scaling status:"
    kubectl get hpa -n "$NAMESPACE"
    
    # Test health endpoint
    local pod_name=$(kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$pod_name" ]; then
        info "Testing health endpoint..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- curl -sf http://localhost:8000/health &> /dev/null; then
            log "✅ Health endpoint is responding"
        else
            warn "⚠️  Health endpoint is not responding yet"
        fi
    fi
    
    # Test analytics endpoint if enabled
    if [ "$ANALYTICS_ENABLED" = true ] && [ -n "$pod_name" ]; then
        info "Testing analytics endpoint..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- curl -sf http://localhost:8000/api/analytics/summary &> /dev/null; then
            log "✅ Analytics endpoint is responding"
        else
            warn "⚠️  Analytics endpoint is not responding yet"
        fi
    fi
    
    log "Deployment verification completed"
}

# Main deployment function
main() {
    log "Starting Business Intelligence Service deployment"
    
    parse_args "$@"
    
    # Show deployment summary
    info "Deployment Configuration:"
    info "  Environment: $ENVIRONMENT"
    info "  Namespace: $NAMESPACE"
    info "  Analytics Enabled: $ANALYTICS_ENABLED"
    info "  Forecasting Enabled: $FORECASTING_ENABLED"
    info "  Reporting Enabled: $REPORTING_ENABLED"
    info "  Dashboards Enabled: $DASHBOARDS_ENABLED"
    info "  External Integrations: $EXTERNAL_INTEGRATIONS"
    info "  Dry Run: $DRY_RUN"
    
    # Confirm production deployment
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy to PRODUCTION environment!"
        warn "  Namespace: $NAMESPACE"
        warn "  Analytics: $ANALYTICS_ENABLED"
        warn "  Forecasting: $FORECASTING_ENABLED"
        warn "  Reporting: $REPORTING_ENABLED"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    check_prerequisites
    setup_namespace
    setup_storage
    setup_config
    deploy_service
    setup_monitoring
    setup_scheduled_jobs
    wait_for_ready
    verify_deployment
    
    # Final status
    if [ "$DRY_RUN" = false ]; then
        log ""
        log "🎉 Business Intelligence Service deployment completed successfully!"
        log ""
        info "Service Information:"
        info "  Namespace: $NAMESPACE"
        info "  Service: $SERVICE_NAME"
        info "  Environment: $ENVIRONMENT"
        
        if [ "$ANALYTICS_ENABLED" = true ]; then
            info "  Analytics: Enabled - Real-time event tracking and user behavior analysis"
        fi
        
        if [ "$FORECASTING_ENABLED" = true ]; then
            info "  Forecasting: Enabled - Revenue predictions and trend analysis"
        fi
        
        if [ "$REPORTING_ENABLED" = true ]; then
            info "  Reporting: Enabled - Automated PDF and Excel report generation"
        fi
        
        if [ "$DASHBOARDS_ENABLED" = true ]; then
            info "  Dashboards: Enabled - Interactive analytics and revenue dashboards"
        fi
        
        log ""
        info "Access the service:"
        info "  Health: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8000:8000"
        info "  Analytics API: http://localhost:8000/api/analytics/summary"
        info "  Revenue API: http://localhost:8000/api/revenue/summary"
        info "  Dashboards: http://localhost:8000/api/dashboards/executive"
        info "  Logs: kubectl logs -f -n $NAMESPACE deployment/$DEPLOYMENT_NAME"
        info "  Metrics: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8080:8080"
        
        if [ "$EXTERNAL_INTEGRATIONS" = true ]; then
            info "  External Integrations: Enabled (remember to configure API keys)"
        fi
    else
        log "🔍 Dry run completed - no changes were made"
    fi
}

# Run main function
main "$@"