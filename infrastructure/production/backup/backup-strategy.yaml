# Backup and Disaster Recovery Strategy for Publish AI Production
# Comprehensive backup solution with automated restoration capabilities

apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    name: backup-system
    purpose: backup-disaster-recovery

---
# Velero for Kubernetes backup
apiVersion: apps/v1
kind: Deployment
metadata:
  name: velero
  namespace: backup-system
  labels:
    component: velero
spec:
  replicas: 1
  selector:
    matchLabels:
      component: velero
  template:
    metadata:
      labels:
        component: velero
    spec:
      serviceAccountName: velero
      containers:
      - name: velero
        image: velero/velero:v1.12.1
        command:
        - /velero
        args:
        - server
        - --restic-timeout=6h
        - --default-backup-storage-location=default
        - --default-volume-snapshot-locations=default
        - --restore-resource-priorities=namespaces,storageclasses,customresourcedefinitions,persistentvolumes,persistentvolumeclaims,secrets,configmaps,serviceaccounts,limitranges,pods
        env:
        - name: VELERO_SCRATCH_DIR
          value: /scratch
        - name: VELERO_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: LD_LIBRARY_PATH
          value: /plugins
        - name: AWS_SHARED_CREDENTIALS_FILE
          value: /credentials/cloud
        resources:
          requests:
            cpu: 500m
            memory: 128Mi
          limits:
            cpu: 1000m
            memory: 512Mi
        volumeMounts:
        - name: plugins
          mountPath: /plugins
        - name: scratch
          mountPath: /scratch
        - name: cloud-credentials
          mountPath: /credentials
      volumes:
      - name: plugins
        emptyDir: {}
      - name: scratch
        emptyDir: {}
      - name: cloud-credentials
        secret:
          secretName: cloud-credentials
      restartPolicy: Always

---
# Service Account for Velero
apiVersion: v1
kind: ServiceAccount
metadata:
  name: velero
  namespace: backup-system

---
# ClusterRole for Velero
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: velero
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]
- nonResourceURLs: ["*"]
  verbs: ["*"]

---
# ClusterRoleBinding for Velero
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: velero
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: velero
subjects:
- kind: ServiceAccount
  name: velero
  namespace: backup-system

---
# BackupStorageLocation for AWS S3
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: aws-s3
  namespace: backup-system
spec:
  provider: aws
  objectStorage:
    bucket: publish-ai-backups
    prefix: kubernetes
  config:
    region: us-west-2
    s3ForcePathStyle: "false"

---
# VolumeSnapshotLocation for AWS EBS
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: aws-ebs
  namespace: backup-system
spec:
  provider: aws
  config:
    region: us-west-2

---
# Scheduled backup for all production resources
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
  namespace: backup-system
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM UTC
  template:
    metadata:
      labels:
        backup-type: daily
    spec:
      includedNamespaces:
      - publish-ai-production
      - monitoring
      - logging
      - istio-system
      includedResources:
      - "*"
      excludedResources:
      - events
      - events.events.k8s.io
      storageLocation: aws-s3
      volumeSnapshotLocations:
      - aws-ebs
      ttl: 720h  # 30 days retention
      snapshotVolumes: true
      includeClusterResources: true

---
# Weekly full cluster backup
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: weekly-full-backup
  namespace: backup-system
spec:
  schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM UTC
  template:
    metadata:
      labels:
        backup-type: weekly-full
    spec:
      includedNamespaces:
      - "*"
      storageLocation: aws-s3
      volumeSnapshotLocations:
      - aws-ebs
      ttl: 2160h  # 90 days retention
      snapshotVolumes: true
      includeClusterResources: true

---
# Critical services backup (every 6 hours)
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: critical-backup
  namespace: backup-system
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  template:
    metadata:
      labels:
        backup-type: critical
    spec:
      includedNamespaces:
      - publish-ai-production
      labelSelector:
        matchLabels:
          priority: critical
      storageLocation: aws-s3
      volumeSnapshotLocations:
      - aws-ebs
      ttl: 168h  # 7 days retention
      snapshotVolumes: true

---
# Database backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
  namespace: backup-system
spec:
  schedule: "0 1 * * *"  # Daily at 1 AM UTC
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: database-backup
          containers:
          - name: postgres-backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              set -e
              
              # Create backup directory with timestamp
              BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
              mkdir -p "$BACKUP_DIR"
              
              # Database backup with compression
              echo "Starting database backup..."
              pg_dump "$DATABASE_URL" \
                --verbose \
                --no-password \
                --format=custom \
                --compress=9 \
                --file="$BACKUP_DIR/database.backup"
              
              # Schema-only backup for quick recovery
              pg_dump "$DATABASE_URL" \
                --verbose \
                --no-password \
                --schema-only \
                --file="$BACKUP_DIR/schema.sql"
              
              # Create backup metadata
              cat > "$BACKUP_DIR/metadata.json" << EOF
              {
                "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
                "database_size": "$(du -sh $BACKUP_DIR/database.backup | cut -f1)",
                "backup_type": "full",
                "retention_days": 30,
                "environment": "$ENVIRONMENT"
              }
              EOF
              
              # Upload to cloud storage
              echo "Uploading backup to cloud storage..."
              aws s3 sync "$BACKUP_DIR" "s3://publish-ai-db-backups/$(date +%Y%m%d_%H%M%S)/"
              
              # Cleanup old local backups (keep last 3)
              find /backups -type d -name "20*" | sort -r | tail -n +4 | xargs rm -rf
              
              echo "Database backup completed successfully"
              
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-backup-credentials
                  key: database-url
            - name: ENVIRONMENT
              value: "production"
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            resources:
              requests:
                cpu: 500m
                memory: 1Gi
              limits:
                cpu: 1000m
                memory: 2Gi
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure

---
# PVC for backup storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-storage-pvc
  namespace: backup-system
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd

---
# Service Account for database backup
apiVersion: v1
kind: ServiceAccount
metadata:
  name: database-backup
  namespace: backup-system

---
# Application data backup job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: application-data-backup
  namespace: backup-system
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM UTC
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: application-backup
          containers:
          - name: app-data-backup
            image: alpine:3.18
            command:
            - /bin/sh
            - -c
            - |
              set -e
              
              # Install required tools
              apk add --no-cache curl aws-cli tar gzip
              
              BACKUP_DIR="/backups/app-data/$(date +%Y%m%d_%H%M%S)"
              mkdir -p "$BACKUP_DIR"
              
              echo "Starting application data backup..."
              
              # Backup AI models and generated content
              echo "Backing up AI models..."
              kubectl get pods -n publish-ai-production -l app=content-generation -o jsonpath='{.items[0].metadata.name}' | \
              xargs -I {} kubectl exec -n publish-ai-production {} -- tar czf - /app/models | \
              aws s3 cp - "s3://publish-ai-app-backups/models/$(date +%Y%m%d_%H%M%S).tar.gz"
              
              # Backup configuration data
              echo "Backing up configuration..."
              kubectl get configmaps -n publish-ai-production -o yaml > "$BACKUP_DIR/configmaps.yaml"
              kubectl get secrets -n publish-ai-production -o yaml > "$BACKUP_DIR/secrets.yaml"
              
              # Create application metadata
              cat > "$BACKUP_DIR/metadata.json" << EOF
              {
                "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
                "backup_type": "application-data",
                "namespace": "publish-ai-production",
                "retention_days": 14,
                "components": ["models", "configs", "secrets"]
              }
              EOF
              
              # Upload to cloud storage
              aws s3 sync "$BACKUP_DIR" "s3://publish-ai-app-backups/app-data/$(date +%Y%m%d_%H%M%S)/"
              
              echo "Application data backup completed"
              
            env:
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 500m
                memory: 1Gi
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure

---
# Service Account for application backup
apiVersion: v1
kind: ServiceAccount
metadata:
  name: application-backup
  namespace: backup-system

---
# ClusterRole for application backup
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: application-backup
rules:
- apiGroups: [""]
  resources: ["pods", "configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]

---
# ClusterRoleBinding for application backup
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: application-backup
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: application-backup
subjects:
- kind: ServiceAccount
  name: application-backup
  namespace: backup-system

---
# Disaster Recovery Job Template
apiVersion: batch/v1
kind: Job
metadata:
  name: disaster-recovery-template
  namespace: backup-system
  labels:
    job-type: disaster-recovery
spec:
  template:
    spec:
      serviceAccountName: disaster-recovery
      containers:
      - name: disaster-recovery
        image: velero/velero:v1.12.1
        command:
        - /bin/bash
        - -c
        - |
          set -e
          
          echo "Starting disaster recovery process..."
          
          # Get the latest backup
          LATEST_BACKUP=$(velero backup get -o json | jq -r '.items | sort_by(.metadata.creationTimestamp) | last | .metadata.name')
          
          if [ "$LATEST_BACKUP" = "null" ]; then
            echo "ERROR: No backups found"
            exit 1
          fi
          
          echo "Latest backup found: $LATEST_BACKUP"
          
          # Restore from backup
          echo "Creating restore from backup: $LATEST_BACKUP"
          velero restore create "disaster-recovery-$(date +%Y%m%d-%H%M%S)" \
            --from-backup "$LATEST_BACKUP" \
            --include-namespaces publish-ai-production \
            --wait
          
          # Verify restoration
          echo "Verifying restored resources..."
          kubectl get pods -n publish-ai-production
          kubectl get services -n publish-ai-production
          
          # Run health checks
          echo "Running post-recovery health checks..."
          kubectl wait --for=condition=ready pods -l app=api-gateway -n publish-ai-production --timeout=300s
          
          echo "Disaster recovery completed successfully"
          
        env:
        - name: VELERO_NAMESPACE
          value: backup-system
        resources:
          requests:
            cpu: 500m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 512Mi
      restartPolicy: Never
  backoffLimit: 3

---
# Service Account for disaster recovery
apiVersion: v1
kind: ServiceAccount
metadata:
  name: disaster-recovery
  namespace: backup-system

---
# Backup verification CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: backup-system
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM UTC
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-verification
          containers:
          - name: verify-backups
            image: alpine:3.18
            command:
            - /bin/sh
            - -c
            - |
              set -e
              
              # Install required tools
              apk add --no-cache curl aws-cli jq
              
              echo "Starting backup verification..."
              
              # Check Velero backup status
              kubectl get backups -n backup-system -o json | \
              jq -r '.items[] | select(.metadata.creationTimestamp > (now - 86400 | strftime("%Y-%m-%dT%H:%M:%SZ"))) | .metadata.name + " " + .status.phase' | \
              while read backup_name status; do
                echo "Backup: $backup_name Status: $status"
                if [ "$status" != "Completed" ]; then
                  echo "ERROR: Backup $backup_name failed with status: $status"
                fi
              done
              
              # Verify cloud storage backups
              echo "Verifying cloud storage backups..."
              
              # Check database backups
              DB_BACKUP_COUNT=$(aws s3 ls s3://publish-ai-db-backups/ --recursive | grep "$(date +%Y%m%d)" | wc -l)
              if [ "$DB_BACKUP_COUNT" -eq 0 ]; then
                echo "ERROR: No database backups found for today"
                exit 1
              else
                echo "Found $DB_BACKUP_COUNT database backup(s) for today"
              fi
              
              # Check application backups
              APP_BACKUP_COUNT=$(aws s3 ls s3://publish-ai-app-backups/ --recursive | grep "$(date +%Y%m%d)" | wc -l)
              if [ "$APP_BACKUP_COUNT" -eq 0 ]; then
                echo "WARNING: No application backups found for today"
              else
                echo "Found $APP_BACKUP_COUNT application backup(s) for today"
              fi
              
              # Send verification report
              cat > /tmp/backup-report.json << EOF
              {
                "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
                "status": "success",
                "database_backups": $DB_BACKUP_COUNT,
                "application_backups": $APP_BACKUP_COUNT,
                "kubernetes_backups": "verified"
              }
              EOF
              
              # Upload report to monitoring
              curl -X POST "${MONITORING_WEBHOOK}" \
                -H "Content-Type: application/json" \
                -d @/tmp/backup-report.json || echo "Failed to send monitoring report"
              
              echo "Backup verification completed successfully"
              
            env:
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            - name: MONITORING_WEBHOOK
              valueFrom:
                secretKeyRef:
                  name: monitoring-webhooks
                  key: backup-verification-url
            resources:
              requests:
                cpu: 100m
                memory: 128Mi
              limits:
                cpu: 200m
                memory: 256Mi
          restartPolicy: OnFailure

---
# Service Account for backup verification
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backup-verification
  namespace: backup-system

---
# RBAC for backup verification
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: backup-verification
rules:
- apiGroups: ["velero.io"]
  resources: ["backups"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: backup-verification
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: backup-verification
subjects:
- kind: ServiceAccount
  name: backup-verification
  namespace: backup-system

---
# Backup retention policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-retention-policy
  namespace: backup-system
data:
  policy.yaml: |
    retention_policies:
      critical_backups:
        frequency: "6h"
        retention: "7d"
        storage_class: "standard"
      
      daily_backups:
        frequency: "24h"
        retention: "30d"
        storage_class: "standard"
        
      weekly_backups:
        frequency: "7d"
        retention: "90d"
        storage_class: "glacier"
        
      monthly_backups:
        frequency: "30d"
        retention: "1y"
        storage_class: "deep_archive"
    
    cleanup_policies:
      - remove_backups_older_than: "90d"
        backup_type: "daily"
      - remove_backups_older_than: "1y"
        backup_type: "weekly"
      - remove_backups_older_than: "2y"
        backup_type: "monthly"