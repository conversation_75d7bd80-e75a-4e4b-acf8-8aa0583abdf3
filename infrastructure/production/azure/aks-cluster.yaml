# Azure AKS Production Cluster Configuration
# Terraform configuration for production-ready AKS cluster with auto-scaling and security

terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.10"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.5"
    }
  }
}

# Configure Azure Provider
provider "azurerm" {
  features {}
}

# Variables
variable "cluster_name" {
  description = "Name of the AKS cluster"
  type        = string
  default     = "publish-ai-production"
}

variable "cluster_version" {
  description = "Kubernetes version"
  type        = string
  default     = "1.28"
}

variable "region" {
  description = "Azure region"
  type        = string
  default     = "East US"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "network_cidr" {
  description = "CIDR block for VNet"
  type        = string
  default     = "10.0.0.0/16"
}

variable "min_nodes" {
  description = "Minimum number of nodes"
  type        = number
  default     = 15
}

variable "max_nodes" {
  description = "Maximum number of nodes"
  type        = number
  default     = 50
}

variable "high_availability" {
  description = "Enable high availability features"
  type        = bool
  default     = true
}

variable "spot_instances" {
  description = "Use spot instances"
  type        = bool
  default     = true
}

variable "monitoring_level" {
  description = "Monitoring level"
  type        = string
  default     = "full"
}

variable "tags" {
  description = "Resource tags"
  type        = map(string)
  default     = {}
}

# Local values
locals {
  name = var.cluster_name
  
  common_tags = merge({
    Terraform   = "true"
    Environment = var.environment
    Project     = "publish-ai"
    ManagedBy   = "terraform"
  }, var.tags)
  
  # Calculate zones based on region
  zones = var.region == "East US" ? ["1", "2", "3"] :
          var.region == "West US 2" ? ["1", "2", "3"] :
          ["1", "2", "3"]
}

# Data sources
data "azurerm_client_config" "current" {}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "${local.name}-rg"
  location = var.region
  
  tags = local.common_tags
}

# Virtual Network
resource "azurerm_virtual_network" "main" {
  name                = "${local.name}-vnet"
  address_space       = [var.network_cidr]
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  tags = local.common_tags
}

# Subnet for AKS cluster
resource "azurerm_subnet" "aks" {
  name                 = "${local.name}-aks-subnet"
  resource_group_name  = azurerm_resource_group.main.name
  virtual_network_name = azurerm_virtual_network.main.name
  address_prefixes     = ["********/24"]
}

# Subnet for Azure Database
resource "azurerm_subnet" "database" {
  name                 = "${local.name}-db-subnet"
  resource_group_name  = azurerm_resource_group.main.name
  virtual_network_name = azurerm_virtual_network.main.name
  address_prefixes     = ["********/24"]
  
  delegation {
    name = "database-delegation"
    
    service_delegation {
      name    = "Microsoft.DBforPostgreSQL/flexibleServers"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/action"
      ]
    }
  }
}

# Network Security Group
resource "azurerm_network_security_group" "aks" {
  name                = "${local.name}-aks-nsg"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  security_rule {
    name                       = "AllowHTTPS"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }
  
  security_rule {
    name                       = "AllowHTTP"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }
  
  tags = local.common_tags
}

# Associate NSG with subnet
resource "azurerm_subnet_network_security_group_association" "aks" {
  subnet_id                 = azurerm_subnet.aks.id
  network_security_group_id = azurerm_network_security_group.aks.id
}

# Log Analytics Workspace for monitoring
resource "azurerm_log_analytics_workspace" "main" {
  name                = "${local.name}-logs"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
  
  tags = local.common_tags
}

# User Assigned Identity for AKS
resource "azurerm_user_assigned_identity" "aks" {
  name                = "${local.name}-aks-identity"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  tags = local.common_tags
}

# Role assignment for AKS identity
resource "azurerm_role_assignment" "aks_network_contributor" {
  scope                = azurerm_virtual_network.main.id
  role_definition_name = "Network Contributor"
  principal_id         = azurerm_user_assigned_identity.aks.principal_id
}

# AKS Cluster
resource "azurerm_kubernetes_cluster" "main" {
  name                = local.name
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  dns_prefix          = "${local.name}-k8s"
  kubernetes_version  = var.cluster_version
  
  # Default node pool (will be replaced by custom node pools)
  default_node_pool {
    name                = "system"
    node_count          = 3
    vm_size             = "Standard_D2_v2"
    type                = "VirtualMachineScaleSets"
    availability_zones  = local.zones
    
    vnet_subnet_id = azurerm_subnet.aks.id
    
    # Enable auto-scaling
    enable_auto_scaling = true
    min_count          = 3
    max_count          = 5
    
    # Node configuration
    os_disk_size_gb = 50
    os_disk_type    = "Managed"
    
    # Taints for system workloads
    node_taints = ["CriticalAddonsOnly=true:NoSchedule"]
    
    node_labels = {
      "node-pool" = "system"
      "workload"  = "system"
    }
    
    tags = local.common_tags
  }
  
  # Identity configuration
  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.aks.id]
  }
  
  # Network configuration
  network_profile {
    network_plugin    = "azure"
    network_policy    = "azure"
    load_balancer_sku = "standard"
    
    service_cidr       = "********/16"
    dns_service_ip     = "*********"
    docker_bridge_cidr = "**********/16"
  }
  
  # Enable RBAC
  role_based_access_control_enabled = true
  
  # Azure AD integration
  azure_active_directory_role_based_access_control {
    managed                = true
    admin_group_object_ids = []
    azure_rbac_enabled     = true
  }
  
  # Monitoring
  oms_agent {
    log_analytics_workspace_id = azurerm_log_analytics_workspace.main.id
  }
  
  # Key Vault secrets provider
  key_vault_secrets_provider {
    secret_rotation_enabled = true
  }
  
  # Auto-scaler profile
  auto_scaler_profile {
    balance_similar_node_groups      = false
    expander                        = "random"
    max_graceful_termination_sec    = "600"
    max_node_provisioning_time      = "15m"
    max_unready_nodes               = 3
    max_unready_percentage          = 45
    new_pod_scale_up_delay          = "10s"
    scale_down_delay_after_add      = "10m"
    scale_down_delay_after_delete   = "10s"
    scale_down_delay_after_failure  = "3m"
    scan_interval                   = "10s"
    scale_down_unneeded             = "10m"
    scale_down_unready              = "20m"
    scale_down_utilization_threshold = "0.5"
  }
  
  tags = local.common_tags
  
  depends_on = [
    azurerm_role_assignment.aks_network_contributor
  ]
}

# API Gateway node pool
resource "azurerm_kubernetes_cluster_node_pool" "api_gateway" {
  name                  = "apigateway"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.main.id
  vm_size              = "Standard_D2s_v3"
  availability_zones   = local.zones
  
  # Auto-scaling configuration
  enable_auto_scaling = true
  min_count          = 3
  max_count          = 10
  
  # Node configuration
  os_disk_size_gb = 100
  os_disk_type    = "Managed"
  
  # Use spot instances for cost savings
  priority        = var.spot_instances ? "Spot" : "Regular"
  eviction_policy = var.spot_instances ? "Delete" : null
  spot_max_price  = var.spot_instances ? 0.1 : null
  
  vnet_subnet_id = azurerm_subnet.aks.id
  
  node_labels = {
    "node-pool" = "api-gateway"
    "workload"  = "api-gateway"
  }
  
  tags = local.common_tags
}

# AI Services node pool
resource "azurerm_kubernetes_cluster_node_pool" "ai_services" {
  name                  = "aiservices"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.main.id
  vm_size              = "Standard_F8s_v2"  # High CPU for AI workloads
  availability_zones   = local.zones
  
  # Auto-scaling configuration
  enable_auto_scaling = true
  min_count          = 2
  max_count          = 20
  
  # Node configuration
  os_disk_size_gb = 200
  os_disk_type    = "Managed"
  
  # Use spot instances for cost savings
  priority        = var.spot_instances ? "Spot" : "Regular"
  eviction_policy = var.spot_instances ? "Delete" : null
  spot_max_price  = var.spot_instances ? 0.2 : null
  
  vnet_subnet_id = azurerm_subnet.aks.id
  
  node_labels = {
    "node-pool"      = "ai-services"
    "workload"       = "ai-services"
    "instance-type"  = "high-compute"
  }
  
  tags = local.common_tags
}

# General services node pool
resource "azurerm_kubernetes_cluster_node_pool" "general_services" {
  name                  = "general"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.main.id
  vm_size              = "Standard_D2s_v3"
  availability_zones   = local.zones
  
  # Auto-scaling configuration
  enable_auto_scaling = true
  min_count          = 3
  max_count          = 15
  
  # Node configuration
  os_disk_size_gb = 100
  os_disk_type    = "Managed"
  
  # Use spot instances for cost savings
  priority        = var.spot_instances ? "Spot" : "Regular"
  eviction_policy = var.spot_instances ? "Delete" : null
  spot_max_price  = var.spot_instances ? 0.1 : null
  
  vnet_subnet_id = azurerm_subnet.aks.id
  
  node_labels = {
    "node-pool" = "general-services"
    "workload"  = "general-services"
  }
  
  tags = local.common_tags
}

# Infrastructure node pool
resource "azurerm_kubernetes_cluster_node_pool" "infrastructure" {
  name                  = "infra"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.main.id
  vm_size              = "Standard_D4s_v3"  # Higher memory for monitoring/logging
  availability_zones   = local.zones
  
  # Auto-scaling configuration
  enable_auto_scaling = true
  min_count          = 3
  max_count          = 8
  
  # Node configuration
  os_disk_size_gb = 200
  os_disk_type    = "Managed"
  
  # Keep infrastructure stable (no spot instances)
  priority = "Regular"
  
  vnet_subnet_id = azurerm_subnet.aks.id
  
  node_labels = {
    "node-pool" = "infrastructure"
    "workload"  = "infrastructure"
  }
  
  tags = local.common_tags
}

# Key Vault for secrets
resource "azurerm_key_vault" "main" {
  name                       = "${local.name}-kv-${random_string.keyvault_suffix.result}"
  location                   = azurerm_resource_group.main.location
  resource_group_name        = azurerm_resource_group.main.name
  tenant_id                  = data.azurerm_client_config.current.tenant_id
  sku_name                   = "standard"
  
  enabled_for_disk_encryption     = true
  enabled_for_deployment          = true
  enabled_for_template_deployment = true
  purge_protection_enabled        = var.environment == "production"
  
  network_acls {
    default_action = "Deny"
    bypass         = "AzureServices"
    virtual_network_subnet_ids = [azurerm_subnet.aks.id]
  }
  
  tags = local.common_tags
}

# Access policy for AKS
resource "azurerm_key_vault_access_policy" "aks" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_kubernetes_cluster.main.key_vault_secrets_provider[0].secret_identity[0].object_id
  
  secret_permissions = [
    "Get",
    "List"
  ]
  
  certificate_permissions = [
    "Get",
    "List"
  ]
  
  key_permissions = [
    "Get",
    "List"
  ]
}

resource "random_string" "keyvault_suffix" {
  length  = 4
  special = false
  upper   = false
}

# PostgreSQL Flexible Server
resource "azurerm_postgresql_flexible_server" "main" {
  name                   = "${local.name}-postgres"
  resource_group_name    = azurerm_resource_group.main.name
  location               = azurerm_resource_group.main.location
  version                = "15"
  delegated_subnet_id    = azurerm_subnet.database.id
  private_dns_zone_id    = azurerm_private_dns_zone.postgres.id
  
  administrator_login    = "publishai"
  administrator_password = random_password.postgres_password.result
  
  zone = "1"
  
  storage_mb        = 65536  # 64 GB
  storage_tier      = "P4"
  
  sku_name          = "GP_Standard_D2s_v3"
  
  backup_retention_days        = 35
  geo_redundant_backup_enabled = var.high_availability
  
  high_availability {
    mode                      = var.high_availability ? "ZoneRedundant" : "Disabled"
    standby_availability_zone = var.high_availability ? "2" : null
  }
  
  maintenance_window {
    day_of_week  = 0
    start_hour   = 2
    start_minute = 0
  }
  
  tags = local.common_tags
  
  depends_on = [azurerm_private_dns_zone_virtual_network_link.postgres]
}

# Private DNS zone for PostgreSQL
resource "azurerm_private_dns_zone" "postgres" {
  name                = "${local.name}-postgres.private.postgres.database.azure.com"
  resource_group_name = azurerm_resource_group.main.name
  
  tags = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "postgres" {
  name                  = "${local.name}-postgres-link"
  private_dns_zone_name = azurerm_private_dns_zone.postgres.name
  virtual_network_id    = azurerm_virtual_network.main.id
  resource_group_name   = azurerm_resource_group.main.name
  
  tags = local.common_tags
}

resource "random_password" "postgres_password" {
  length  = 16
  special = true
}

# Azure Cache for Redis
resource "azurerm_redis_cache" "main" {
  name                = "${local.name}-redis"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  capacity            = 2
  family              = "C"
  sku_name            = "Standard"
  enable_non_ssl_port = false
  minimum_tls_version = "1.2"
  
  subnet_id = azurerm_subnet.aks.id
  
  redis_configuration {
    enable_authentication = true
  }
  
  tags = local.common_tags
}

# Storage Account for AI models
resource "azurerm_storage_account" "ai_models" {
  name                     = "${replace(local.name, "-", "")}aimodels${random_string.storage_suffix.result}"
  resource_group_name      = azurerm_resource_group.main.name
  location                 = azurerm_resource_group.main.location
  account_tier             = "Standard"
  account_replication_type = var.high_availability ? "GRS" : "LRS"
  
  blob_properties {
    versioning_enabled = true
    
    delete_retention_policy {
      days = 30
    }
    
    container_delete_retention_policy {
      days = 30
    }
  }
  
  network_rules {
    default_action             = "Deny"
    bypass                     = ["AzureServices"]
    virtual_network_subnet_ids = [azurerm_subnet.aks.id]
  }
  
  tags = local.common_tags
}

resource "azurerm_storage_container" "ai_models" {
  name                  = "models"
  storage_account_name  = azurerm_storage_account.ai_models.name
  container_access_type = "private"
}

resource "random_string" "storage_suffix" {
  length  = 4
  special = false
  upper   = false
}

# Container Registry
resource "azurerm_container_registry" "main" {
  name                = "${replace(local.name, "-", "")}acr${random_string.acr_suffix.result}"
  resource_group_name = azurerm_resource_group.main.name
  location            = azurerm_resource_group.main.location
  sku                 = "Premium"
  admin_enabled       = false
  
  identity {
    type = "SystemAssigned"
  }
  
  encryption {
    enabled = true
  }
  
  trust_policy {
    enabled = true
  }
  
  retention_policy {
    days    = 30
    enabled = true
  }
  
  quarantine_policy {
    enabled = true
  }
  
  tags = local.common_tags
}

# Role assignment for AKS to pull from ACR
resource "azurerm_role_assignment" "aks_acr_pull" {
  scope                = azurerm_container_registry.main.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_kubernetes_cluster.main.kubelet_identity[0].object_id
}

resource "random_string" "acr_suffix" {
  length  = 4
  special = false
  upper   = false
}

# Application Insights
resource "azurerm_application_insights" "main" {
  name                = "${local.name}-insights"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  workspace_id        = azurerm_log_analytics_workspace.main.id
  application_type    = "web"
  
  tags = local.common_tags
}

# Outputs
output "cluster_name" {
  description = "AKS cluster name"
  value       = azurerm_kubernetes_cluster.main.name
}

output "cluster_endpoint" {
  description = "AKS cluster endpoint"
  value       = azurerm_kubernetes_cluster.main.kube_config.0.host
  sensitive   = true
}

output "cluster_ca_certificate" {
  description = "AKS cluster CA certificate"
  value       = azurerm_kubernetes_cluster.main.kube_config.0.cluster_ca_certificate
  sensitive   = true
}

output "resource_group_name" {
  description = "Resource group name"
  value       = azurerm_resource_group.main.name
}

output "vnet_name" {
  description = "Virtual network name"
  value       = azurerm_virtual_network.main.name
}

output "subnet_name" {
  description = "AKS subnet name"
  value       = azurerm_subnet.aks.name
}

output "database_fqdn" {
  description = "PostgreSQL server FQDN"
  value       = azurerm_postgresql_flexible_server.main.fqdn
  sensitive   = true
}

output "database_username" {
  description = "PostgreSQL server username"
  value       = azurerm_postgresql_flexible_server.main.administrator_login
  sensitive   = true
}

output "database_password" {
  description = "PostgreSQL server password"
  value       = azurerm_postgresql_flexible_server.main.administrator_password
  sensitive   = true
}

output "redis_hostname" {
  description = "Redis cache hostname"
  value       = azurerm_redis_cache.main.hostname
  sensitive   = true
}

output "redis_primary_key" {
  description = "Redis cache primary key"
  value       = azurerm_redis_cache.main.primary_access_key
  sensitive   = true
}

output "storage_account_name" {
  description = "Storage account name"
  value       = azurerm_storage_account.ai_models.name
}

output "container_registry_name" {
  description = "Container registry name"
  value       = azurerm_container_registry.main.name
}

output "key_vault_name" {
  description = "Key Vault name"
  value       = azurerm_key_vault.main.name
}