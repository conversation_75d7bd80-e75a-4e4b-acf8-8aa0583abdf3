# Google GKE Production Cluster Configuration
# Terraform configuration for production-ready GKE cluster with auto-scaling and security

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.10"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.5"
    }
  }
}

# Variables
variable "cluster_name" {
  description = "Name of the GKE cluster"
  type        = string
  default     = "publish-ai-production"
}

variable "cluster_version" {
  description = "Kubernetes version"
  type        = string
  default     = "1.28"
}

variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-central1"
}

variable "zones" {
  description = "Number of zones to use"
  type        = number
  default     = 3
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "network_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "min_nodes" {
  description = "Minimum number of nodes"
  type        = number
  default     = 15
}

variable "max_nodes" {
  description = "Maximum number of nodes"
  type        = number
  default     = 50
}

variable "high_availability" {
  description = "Enable high availability features"
  type        = bool
  default     = true
}

variable "spot_instances" {
  description = "Use spot instances"
  type        = bool
  default     = true
}

variable "monitoring_level" {
  description = "Monitoring level"
  type        = string
  default     = "full"
}

variable "tags" {
  description = "Resource tags"
  type        = map(string)
  default     = {}
}

# Local values
locals {
  name = var.cluster_name
  
  common_labels = merge({
    terraform   = "true"
    environment = var.environment
    project     = "publish-ai"
    managed-by  = "terraform"
  }, var.tags)
  
  # Calculate zones based on region and zone count
  zones = var.zones == 1 ? ["${var.region}-a"] : 
          var.zones == 2 ? ["${var.region}-a", "${var.region}-b"] :
          ["${var.region}-a", "${var.region}-b", "${var.region}-c"]
}

# Data sources
data "google_project" "current" {
  project_id = var.project_id
}

data "google_client_config" "default" {}

# VPC Network
resource "google_compute_network" "vpc" {
  name                    = "${local.name}-vpc"
  auto_create_subnetworks = false
  routing_mode           = "REGIONAL"
  
  project = var.project_id
}

# Subnet for GKE cluster
resource "google_compute_subnetwork" "subnet" {
  name          = "${local.name}-subnet"
  ip_cidr_range = var.network_cidr
  region        = var.region
  network       = google_compute_network.vpc.id
  
  project = var.project_id
  
  # Secondary IP ranges for pods and services
  secondary_ip_range {
    range_name    = "pods"
    ip_cidr_range = "********/16"
  }
  
  secondary_ip_range {
    range_name    = "services"
    ip_cidr_range = "********/16"
  }
  
  private_ip_google_access = true
}

# Cloud Router for NAT
resource "google_compute_router" "router" {
  name    = "${local.name}-router"
  region  = var.region
  network = google_compute_network.vpc.id
  project = var.project_id
}

# Cloud NAT for outbound internet access
resource "google_compute_router_nat" "nat" {
  name                               = "${local.name}-nat"
  router                            = google_compute_router.router.name
  region                            = var.region
  nat_ip_allocate_option            = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  
  project = var.project_id
  
  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# Firewall rules
resource "google_compute_firewall" "allow_internal" {
  name    = "${local.name}-allow-internal"
  network = google_compute_network.vpc.name
  project = var.project_id
  
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "icmp"
  }
  
  source_ranges = [var.network_cidr, "********/16", "********/16"]
}

resource "google_compute_firewall" "allow_external_health_checks" {
  name    = "${local.name}-allow-health-checks"
  network = google_compute_network.vpc.name
  project = var.project_id
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443", "8000-8999"]
  }
  
  source_ranges = ["***********/22", "**********/16"]
  target_tags   = ["gke-node"]
}

# Service Account for GKE nodes
resource "google_service_account" "gke_nodes" {
  account_id   = "${local.name}-gke-nodes"
  display_name = "GKE Node Service Account"
  project      = var.project_id
}

# IAM bindings for GKE node service account
resource "google_project_iam_member" "gke_nodes_storage_reader" {
  project = var.project_id
  role    = "roles/storage.objectViewer"
  member  = "serviceAccount:${google_service_account.gke_nodes.email}"
}

resource "google_project_iam_member" "gke_nodes_registry_reader" {
  project = var.project_id
  role    = "roles/containerregistry.ServiceAgent"
  member  = "serviceAccount:${google_service_account.gke_nodes.email}"
}

resource "google_project_iam_member" "gke_nodes_monitoring_writer" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.gke_nodes.email}"
}

resource "google_project_iam_member" "gke_nodes_logging_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.gke_nodes.email}"
}

# GKE Cluster
resource "google_container_cluster" "primary" {
  name     = local.name
  location = var.zones == 1 ? local.zones[0] : var.region
  project  = var.project_id
  
  # Kubernetes version
  min_master_version = var.cluster_version
  
  # Network configuration
  network    = google_compute_network.vpc.id
  subnetwork = google_compute_subnetwork.subnet.id
  
  # IP allocation policy
  ip_allocation_policy {
    cluster_secondary_range_name  = "pods"
    services_secondary_range_name = "services"
  }
  
  # Remove default node pool
  remove_default_node_pool = true
  initial_node_count       = 1
  
  # Network policy
  network_policy {
    enabled = true
  }
  
  # Addons configuration
  addons_config {
    http_load_balancing {
      disabled = false
    }
    
    horizontal_pod_autoscaling {
      disabled = false
    }
    
    network_policy_config {
      disabled = false
    }
    
    gcp_filestore_csi_driver_config {
      enabled = true
    }
    
    gce_persistent_disk_csi_driver_config {
      enabled = true
    }
  }
  
  # Workload Identity
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }
  
  # Private cluster configuration
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "**********/28"
    
    master_global_access_config {
      enabled = true
    }
  }
  
  # Master auth
  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
  
  # Logging and monitoring
  logging_service    = "logging.googleapis.com/kubernetes"
  monitoring_service = "monitoring.googleapis.com/kubernetes"
  
  # Resource usage export
  resource_usage_export_config {
    enable_network_egress_metering       = true
    enable_resource_consumption_metering = true
    
    bigquery_destination {
      dataset_id = google_bigquery_dataset.gke_usage.dataset_id
    }
  }
  
  # Maintenance policy
  maintenance_policy {
    daily_maintenance_window {
      start_time = "03:00"
    }
  }
  
  # Security configurations
  pod_security_policy_config {
    enabled = true
  }
  
  # Enable Binary Authorization
  enable_binary_authorization = true
  
  # Enable Intranode Visibility
  enable_intranode_visibility = true
  
  # Enable Shielded Nodes
  enable_shielded_nodes = true
  
  lifecycle {
    ignore_changes = [node_pool]
  }
}

# BigQuery dataset for GKE usage export
resource "google_bigquery_dataset" "gke_usage" {
  dataset_id  = "${replace(local.name, "-", "_")}_gke_usage"
  description = "GKE cluster resource usage data"
  location    = "US"
  project     = var.project_id
  
  labels = local.common_labels
}

# System node pool
resource "google_container_node_pool" "system" {
  name       = "system"
  location   = google_container_cluster.primary.location
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 3
    max_node_count = 5
  }
  
  node_config {
    preemptible  = false
    machine_type = "e2-medium"
    disk_size_gb = 50
    disk_type    = "pd-ssd"
    
    service_account = google_service_account.gke_nodes.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = merge(local.common_labels, {
      "node-pool" = "system"
      "workload"  = "system"
    })
    
    tags = ["gke-node", "${local.name}-gke-node", "system"]
    
    # Taints for system workloads
    taint {
      key    = "system"
      value  = "true"
      effect = "NO_SCHEDULE"
    }
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }
  
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  
  upgrade_settings {
    max_surge       = 1
    max_unavailable = 0
  }
}

# API Gateway node pool
resource "google_container_node_pool" "api_gateway" {
  name       = "api-gateway"
  location   = google_container_cluster.primary.location
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 3
    max_node_count = 10
  }
  
  node_config {
    preemptible  = var.spot_instances
    machine_type = "e2-standard-2"
    disk_size_gb = 100
    disk_type    = "pd-ssd"
    
    service_account = google_service_account.gke_nodes.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = merge(local.common_labels, {
      "node-pool" = "api-gateway"
      "workload"  = "api-gateway"
    })
    
    tags = ["gke-node", "${local.name}-gke-node", "api-gateway"]
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }
  
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  
  upgrade_settings {
    max_surge       = 1
    max_unavailable = 1
  }
}

# AI Services node pool
resource "google_container_node_pool" "ai_services" {
  name       = "ai-services"
  location   = google_container_cluster.primary.location
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 2
    max_node_count = 20
  }
  
  node_config {
    preemptible  = var.spot_instances
    machine_type = "c2-standard-8"
    disk_size_gb = 200
    disk_type    = "pd-ssd"
    
    service_account = google_service_account.gke_nodes.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = merge(local.common_labels, {
      "node-pool"      = "ai-services"
      "workload"       = "ai-services"
      "instance-type"  = "high-compute"
    })
    
    tags = ["gke-node", "${local.name}-gke-node", "ai-services"]
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }
  
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  
  upgrade_settings {
    max_surge       = 2
    max_unavailable = 1
  }
}

# General services node pool
resource "google_container_node_pool" "general_services" {
  name       = "general-services"
  location   = google_container_cluster.primary.location
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 3
    max_node_count = 15
  }
  
  node_config {
    preemptible  = var.spot_instances
    machine_type = "e2-standard-2"
    disk_size_gb = 100
    disk_type    = "pd-ssd"
    
    service_account = google_service_account.gke_nodes.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = merge(local.common_labels, {
      "node-pool" = "general-services"
      "workload"  = "general-services"
    })
    
    tags = ["gke-node", "${local.name}-gke-node", "general-services"]
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }
  
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  
  upgrade_settings {
    max_surge       = 1
    max_unavailable = 1
  }
}

# Infrastructure node pool
resource "google_container_node_pool" "infrastructure" {
  name       = "infrastructure"
  location   = google_container_cluster.primary.location
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 3
    max_node_count = 8
  }
  
  node_config {
    preemptible  = false  # Keep infrastructure stable
    machine_type = "e2-standard-4"
    disk_size_gb = 200
    disk_type    = "pd-ssd"
    
    service_account = google_service_account.gke_nodes.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = merge(local.common_labels, {
      "node-pool" = "infrastructure"
      "workload"  = "infrastructure"
    })
    
    tags = ["gke-node", "${local.name}-gke-node", "infrastructure"]
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }
  
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  
  upgrade_settings {
    max_surge       = 1
    max_unavailable = 1
  }
}

# Cloud SQL instance for PostgreSQL
resource "google_sql_database_instance" "main" {
  name             = "${local.name}-postgres"
  database_version = "POSTGRES_15"
  region           = var.region
  project          = var.project_id
  
  settings {
    tier              = "db-custom-2-4096"
    availability_type = var.high_availability ? "REGIONAL" : "ZONAL"
    disk_size         = 100
    disk_type         = "PD_SSD"
    disk_autoresize   = true
    
    backup_configuration {
      enabled                        = true
      start_time                     = "02:00"
      location                       = var.region
      point_in_time_recovery_enabled = true
      
      backup_retention_settings {
        retained_backups = 7
        retention_unit   = "COUNT"
      }
    }
    
    ip_configuration {
      ipv4_enabled    = false
      private_network = google_compute_network.vpc.id
      require_ssl     = true
    }
    
    database_flags {
      name  = "log_checkpoints"
      value = "on"
    }
    
    database_flags {
      name  = "log_connections"
      value = "on"
    }
    
    database_flags {
      name  = "log_disconnections"
      value = "on"
    }
    
    insights_config {
      query_insights_enabled  = true
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }
  }
  
  deletion_protection = var.environment == "production"
}

# Cloud Memorystore for Redis
resource "google_redis_instance" "cache" {
  name           = "${local.name}-redis"
  tier           = "STANDARD_HA"
  memory_size_gb = 4
  region         = var.region
  project        = var.project_id
  
  authorized_network = google_compute_network.vpc.id
  
  redis_version     = "REDIS_6_X"
  display_name      = "${local.name} Redis Cache"
  
  auth_enabled = true
  
  labels = local.common_labels
}

# Cloud Storage bucket for AI models
resource "google_storage_bucket" "ai_models" {
  name     = "${local.name}-ai-models-${random_string.bucket_suffix.result}"
  location = var.region
  project  = var.project_id
  
  uniform_bucket_level_access = true
  
  versioning {
    enabled = true
  }
  
  encryption {
    default_kms_key_name = google_kms_crypto_key.storage.id
  }
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
  
  labels = local.common_labels
}

# KMS key ring
resource "google_kms_key_ring" "main" {
  name     = "${local.name}-keyring"
  location = var.region
  project  = var.project_id
}

# KMS crypto key for storage encryption
resource "google_kms_crypto_key" "storage" {
  name     = "${local.name}-storage-key"
  key_ring = google_kms_key_ring.main.id
  
  rotation_period = "7776000s"  # 90 days
  
  lifecycle {
    prevent_destroy = true
  }
}

resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# Outputs
output "cluster_name" {
  description = "GKE cluster name"
  value       = google_container_cluster.primary.name
}

output "cluster_endpoint" {
  description = "GKE cluster endpoint"
  value       = google_container_cluster.primary.endpoint
  sensitive   = true
}

output "cluster_ca_certificate" {
  description = "GKE cluster CA certificate"
  value       = google_container_cluster.primary.master_auth[0].cluster_ca_certificate
  sensitive   = true
}

output "cluster_location" {
  description = "GKE cluster location"
  value       = google_container_cluster.primary.location
}

output "network_name" {
  description = "VPC network name"
  value       = google_compute_network.vpc.name
}

output "subnet_name" {
  description = "Subnet name"
  value       = google_compute_subnetwork.subnet.name
}

output "database_connection_name" {
  description = "Cloud SQL connection name"
  value       = google_sql_database_instance.main.connection_name
}

output "redis_host" {
  description = "Redis instance host"
  value       = google_redis_instance.cache.host
  sensitive   = true
}

output "redis_port" {
  description = "Redis instance port"
  value       = google_redis_instance.cache.port
}

output "storage_bucket_name" {
  description = "Cloud Storage bucket name"
  value       = google_storage_bucket.ai_models.name
}

output "kms_key_id" {
  description = "KMS crypto key ID"
  value       = google_kms_crypto_key.storage.id
}