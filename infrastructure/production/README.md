# Production Deployment for Publish AI

This directory contains production-ready Kubernetes configurations for the Publish AI microservices platform. This setup provides enterprise-grade scalability, security, and reliability for production workloads.

## Overview

The production deployment includes:

- **Multi-cloud Kubernetes cluster configurations** (AWS EKS, Google GKE, Azure AKS)
- **Auto-scaling and resource optimization** for all microservices
- **Production-grade security hardening** with network policies and RBAC
- **High availability and disaster recovery** configurations
- **Performance optimization** and resource limits
- **Cost optimization** strategies and resource requests
- **Backup and restore** procedures
- **Zero-downtime deployment** strategies

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                  Production Kubernetes Cluster              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                 Ingress Layer                        │  │
│  │  ┌─────────┐  ┌─────────┐  ┌──────────────────────┐ │  │
│  │  │   ALB   │  │ Cloudflare │  │    WAF/DDoS       │ │  │
│  │  │  /NLB   │  │    CDN    │  │   Protection       │ │  │
│  │  └─────────┘  └─────────┘  └──────────────────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
│                           │                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │              Istio Service Mesh                      │  │
│  │         (mTLS, Traffic Management, Security)         │  │
│  └─────────────────────────────────────────────────────┘  │
│                           │                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                API Gateway Tier                      │  │
│  │  ┌─────────────────────────────────────────────────┐ │  │
│  │  │        API Gateway (3+ replicas)                 │ │  │
│  │  │    ┌─────────────────────────────────────────┐   │ │  │
│  │  │    │  Rate Limiting, Auth, Circuit Breaker   │   │ │  │
│  │  │    └─────────────────────────────────────────┘   │ │  │
│  │  └─────────────────────────────────────────────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
│                           │                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                 Microservices Tier                   │  │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │  │
│  │  │ Content │  │ Market  │  │Publishing│  │ Cover   │ │  │
│  │  │   Gen   │  │ Intel   │  │ Service │  │Designer │ │  │
│  │  │(3-10+)  │  │(2-5+)   │  │(2-3+)   │  │(2-4+)   │ │  │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │  │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │  │
│  │  │ Sales   │  │Personal │  │Research │  │Multimodal│ │  │
│  │  │Monitor  │  │ Engine  │  │Service  │  │   Gen   │ │  │
│  │  │(1-2+)   │  │(1-3+)   │  │(1-2+)   │  │(2-6+)   │ │  │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
│                           │                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                Infrastructure Tier                   │  │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │  │
│  │  │Event Bus│  │ Service │  │ Monitoring│  │ Logging │ │  │
│  │  │ (Kafka) │  │Discovery│  │  Stack   │  │  (ELK)  │ │  │
│  │  │(3+ nodes)│  │(HA)     │  │(HA)      │  │(HA)     │ │  │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
│                           │                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                  Data Layer                          │  │
│  │  ┌─────────────────────────────────────────────────┐ │  │
│  │  │              Supabase PostgreSQL                 │ │  │
│  │  │         (High Availability + Read Replicas)      │ │  │
│  │  └─────────────────────────────────────────────────┘ │  │
│  │  ┌─────────────────────────────────────────────────┐ │  │
│  │  │                 Redis Cluster                    │ │  │
│  │  │        (Caching + Session + Rate Limiting)       │ │  │
│  │  └─────────────────────────────────────────────────┘ │  │
│  │  ┌─────────────────────────────────────────────────┐ │  │
│  │  │              Object Storage                      │ │  │
│  │  │         (S3/GCS/Azure for AI Models)             │ │  │
│  │  └─────────────────────────────────────────────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Deployment Strategy

### 1. Cluster Setup

- **Multi-zone deployment** for high availability
- **Node pools** optimized for different workload types
- **Auto-scaling** based on CPU, memory, and custom metrics
- **Spot instances** for cost optimization where appropriate

### 2. Security Hardening

- **Network policies** for micro-segmentation
- **Pod Security Standards** enforcement
- **RBAC** with least privilege access
- **Secret management** with external secret operators
- **Image scanning** and vulnerability management

### 3. Performance Optimization

- **Resource requests and limits** tuned for each service
- **Horizontal Pod Autoscaling** (HPA) with custom metrics
- **Vertical Pod Autoscaling** (VPA) for right-sizing
- **Cluster Autoscaling** for node optimization
- **KEDA** for event-driven autoscaling

### 4. Reliability Features

- **Pod Disruption Budgets** to maintain availability
- **Affinity and anti-affinity** rules for optimal placement
- **Health checks** with appropriate timeouts
- **Circuit breakers** and retry policies
- **Graceful shutdown** handling

## Node Pool Configuration

### 1. System Node Pool

- **Purpose**: Kubernetes system components
- **Size**: 3-5 nodes (t3.medium/n1-standard-2)
- **Auto-scaling**: 3-5 nodes
- **Taints**: `system=true:NoSchedule`

### 2. API Gateway Node Pool

- **Purpose**: API Gateway and ingress components
- **Size**: 3-6 nodes (t3.large/n1-standard-4)
- **Auto-scaling**: 3-10 nodes
- **Features**: High network performance

### 3. AI Services Node Pool

- **Purpose**: Content generation and AI workloads
- **Size**: 2-8 nodes (c5.2xlarge/n1-highmem-4)
- **Auto-scaling**: 2-20 nodes
- **Features**: High CPU/Memory, GPU support optional

### 4. General Services Node Pool

- **Purpose**: Other microservices
- **Size**: 3-6 nodes (t3.large/n1-standard-4)
- **Auto-scaling**: 3-15 nodes
- **Features**: Balanced compute and memory

### 5. Infrastructure Node Pool

- **Purpose**: Monitoring, logging, message queues
- **Size**: 3-5 nodes (t3.xlarge/n1-standard-8)
- **Auto-scaling**: 3-8 nodes
- **Features**: High I/O performance

## Resource Allocation

### Service Resource Profiles

| Service | Requests | Limits | Replicas | HPA Target |
|---------|----------|--------|----------|------------|
| API Gateway | 500m CPU, 1Gi RAM | 2000m CPU, 4Gi RAM | 3-10 | 70% CPU |
| Content Generation | 1000m CPU, 2Gi RAM | 4000m CPU, 8Gi RAM | 2-15 | 60% CPU |
| Market Intelligence | 500m CPU, 1Gi RAM | 2000m CPU, 4Gi RAM | 2-8 | 70% CPU |
| Publishing Service | 300m CPU, 512Mi RAM | 1000m CPU, 2Gi RAM | 2-5 | 65% CPU |
| Cover Designer | 800m CPU, 1.5Gi RAM | 3000m CPU, 6Gi RAM | 2-8 | 60% CPU |
| Sales Monitor | 200m CPU, 256Mi RAM | 500m CPU, 1Gi RAM | 1-3 | 75% CPU |
| Personalization | 300m CPU, 512Mi RAM | 1000m CPU, 2Gi RAM | 1-5 | 70% CPU |
| Research Service | 200m CPU, 256Mi RAM | 500m CPU, 1Gi RAM | 1-3 | 75% CPU |
| Multimodal Generator | 1500m CPU, 3Gi RAM | 6000m CPU, 12Gi RAM | 1-10 | 55% CPU |

## Cost Optimization

### 1. Right-Sizing Strategy

- **VPA recommendations** for optimal resource allocation
- **Spot instances** for non-critical workloads (60-90% cost savings)
- **Reserved instances** for predictable baseline capacity
- **Cluster autoscaling** to avoid over-provisioning

### 2. Workload Optimization

- **Scheduled scaling** based on traffic patterns
- **Cold storage** for infrequently accessed data
- **Intelligent caching** to reduce compute costs
- **Batch processing** during off-peak hours

### 3. Monitoring and Alerts

- **Cost monitoring** with budget alerts
- **Resource waste detection** and recommendations
- **Rightsizing alerts** for over/under-provisioned resources
- **Monthly cost optimization reports**

## Deployment Environments

### 1. Production

- **Multi-zone**: 3+ availability zones
- **Node count**: 15-50+ nodes
- **Traffic**: Real user traffic
- **Monitoring**: Full observability stack
- **Backup**: Daily automated backups

### 2. Staging

- **Single-zone**: Cost-optimized
- **Node count**: 6-12 nodes
- **Traffic**: Pre-production testing
- **Monitoring**: Basic monitoring
- **Backup**: Weekly backups

### 3. Development

- **Single-zone**: Minimal resources
- **Node count**: 3-6 nodes
- **Traffic**: Development testing
- **Monitoring**: Development metrics
- **Backup**: On-demand

## Security Configuration

### 1. Network Security

- **VPC/Network isolation** between environments
- **Private subnets** for worker nodes
- **NAT gateways** for outbound internet access
- **Security groups/Firewall rules** with minimal access
- **Network policies** for pod-to-pod communication

### 2. Identity and Access

- **IAM roles** with least privilege principle
- **Service accounts** per microservice
- **RBAC** with granular permissions
- **OIDC integration** for human access
- **Audit logging** for all cluster access

### 3. Data Protection

- **Encryption at rest** for all storage
- **Encryption in transit** with TLS 1.3
- **Secret encryption** with KMS
- **Image scanning** for vulnerabilities
- **Runtime security** monitoring

## Monitoring and Alerting

### 1. Application Metrics

- **SLI/SLO tracking** for all services
- **Business metrics** (revenue, users, books published)
- **AI model performance** (accuracy, latency, cost)
- **Error rates and latency** percentiles

### 2. Infrastructure Metrics

- **Cluster health** and resource utilization
- **Node performance** and capacity
- **Network performance** and security events
- **Storage performance** and capacity

### 3. Alerting Strategy

- **Critical alerts**: Page-worthy incidents (P0/P1)
- **Warning alerts**: Degraded performance (P2)
- **Info alerts**: Trending issues (P3)
- **Business alerts**: Revenue/user impact

## Disaster Recovery

### 1. Backup Strategy

- **Daily automated backups** of all persistent data
- **Cross-region replication** for critical data
- **Point-in-time recovery** capability
- **Regular backup testing** and validation

### 2. Recovery Procedures

- **RTO (Recovery Time Objective)**: 4 hours for full service
- **RPO (Recovery Point Objective)**: 1 hour for data loss
- **Automated failover** for infrastructure components
- **Manual approval** for critical service recovery

### 3. Business Continuity

- **Multi-region deployment** capability
- **Traffic failover** with DNS/load balancer
- **Data synchronization** between regions
- **Regular DR testing** and documentation updates

## Getting Started

### Prerequisites

- Kubernetes cluster (v1.28+)
- kubectl configured
- Helm 3.0+
- Cloud provider CLI (aws/gcloud/az)
- Terraform (optional for infrastructure provisioning)

### Quick Deployment

```bash
# Deploy production cluster
./scripts/deploy-production-cluster.sh --provider aws --region us-west-2

# Deploy all services
./scripts/deploy-production-services.sh --environment production

# Verify deployment
./scripts/verify-production-deployment.sh

# Run production readiness checks
./scripts/production-readiness-check.sh
```

### Environment-Specific Deployments

```bash
# Staging environment
./scripts/deploy-production-cluster.sh --environment staging

# Development environment  
./scripts/deploy-production-cluster.sh --environment development

# Custom configuration
./scripts/deploy-production-cluster.sh --config custom-config.yaml
```

## Support and Maintenance

### 1. Runbooks

- **Incident response** procedures
- **Scaling** and capacity planning
- **Security incident** response
- **Backup and recovery** procedures

### 2. Maintenance Windows

- **Weekly maintenance**: Non-critical updates
- **Monthly maintenance**: Security patches
- **Quarterly maintenance**: Major version upgrades
- **Emergency maintenance**: Critical security fixes

### 3. Documentation

- **Architecture decisions** and rationale
- **Operational procedures** and troubleshooting
- **Performance tuning** guidelines
- **Cost optimization** recommendations

## Best Practices

### 1. Deployment

- **Blue-green deployments** for zero downtime
- **Canary releases** for gradual rollouts
- **Feature flags** for safe deployments
- **Automated rollbacks** on failure detection

### 2. Monitoring

- **Proactive monitoring** vs reactive alerting
- **SLO-based alerting** to reduce noise
- **Distributed tracing** for complex workflows
- **Business impact** correlation

### 3. Security

- **Regular security assessments** and penetration testing
- **Automated compliance** checking
- **Incident response** planning and testing
- **Security training** for development teams

This production deployment provides enterprise-grade reliability, scalability, and security for the Publish AI platform while maintaining cost efficiency and operational simplicity.
