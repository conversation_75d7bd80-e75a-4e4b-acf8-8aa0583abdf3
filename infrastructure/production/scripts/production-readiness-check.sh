#!/bin/bash

# Production Readiness Check for Publish AI
# Comprehensive validation of production environment before going live

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PRODUCTION_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$PRODUCTION_CONFIG_DIR")")"

# Default values
NAMESPACE="publish-ai-production"
ENVIRONMENT="production"
SEVERITY_THRESHOLD="warning"
EXPORT_REPORT=false
REPORT_FORMAT="json"
FIX_ISSUES=false

# Test results
declare -A TEST_RESULTS=()
declare -A TEST_RECOMMENDATIONS=()
TOTAL_TESTS=0
PASSED_TESTS=0
WARNING_TESTS=0
FAILED_TESTS=0

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Test result functions
test_pass() {
    local test_name="$1"
    local message="$2"
    TEST_RESULTS["$test_name"]="PASS"
    TEST_RECOMMENDATIONS["$test_name"]="$message"
    ((TOTAL_TESTS++))
    ((PASSED_TESTS++))
    echo -e "✅ ${GREEN}PASS${NC}: $test_name - $message"
}

test_warn() {
    local test_name="$1"
    local message="$2"
    local recommendation="$3"
    TEST_RESULTS["$test_name"]="WARN"
    TEST_RECOMMENDATIONS["$test_name"]="$message - RECOMMENDATION: $recommendation"
    ((TOTAL_TESTS++))
    ((WARNING_TESTS++))
    echo -e "⚠️  ${YELLOW}WARN${NC}: $test_name - $message"
    echo -e "   ${YELLOW}RECOMMENDATION:${NC} $recommendation"
}

test_fail() {
    local test_name="$1"
    local message="$2"
    local fix_action="$3"
    TEST_RESULTS["$test_name"]="FAIL"
    TEST_RECOMMENDATIONS["$test_name"]="$message - REQUIRED ACTION: $fix_action"
    ((TOTAL_TESTS++))
    ((FAILED_TESTS++))
    echo -e "❌ ${RED}FAIL${NC}: $test_name - $message"
    echo -e "   ${RED}REQUIRED ACTION:${NC} $fix_action"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Production readiness check for Publish AI deployment.

Options:
  -n, --namespace NAMESPACE      Kubernetes namespace (default: publish-ai-production)
  -e, --environment ENV          Environment: production, staging (default: production)
  -s, --severity LEVEL           Minimum severity to report: info, warning, critical (default: warning)
  -r, --export-report            Export detailed report
  -f, --format FORMAT            Report format: json, yaml, html (default: json)
  -x, --fix-issues               Attempt to automatically fix issues where possible
  -h, --help                     Show this help message

Test Categories:
  - Cluster health and capacity
  - Service deployment status
  - Security configuration
  - Performance and scaling
  - Backup and disaster recovery
  - Monitoring and alerting
  - Cost optimization
  - Compliance and governance

Examples:
  $0                                    # Run all production readiness checks
  $0 --severity critical --export-report  # Only show critical issues and export report
  $0 --namespace staging --environment staging  # Check staging environment
  $0 --fix-issues                       # Run checks and attempt to fix issues

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -s|--severity)
                SEVERITY_THRESHOLD="$2"
                shift 2
                ;;
            -r|--export-report)
                EXPORT_REPORT=true
                shift
                ;;
            -f|--format)
                REPORT_FORMAT="$2"
                shift 2
                ;;
            -x|--fix-issues)
                FIX_ISSUES=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate severity threshold
    if [[ ! "$SEVERITY_THRESHOLD" =~ ^(info|warning|critical)$ ]]; then
        error "Invalid severity threshold: $SEVERITY_THRESHOLD"
        exit 1
    fi
    
    # Validate report format
    if [[ ! "$REPORT_FORMAT" =~ ^(json|yaml|html)$ ]]; then
        error "Invalid report format: $REPORT_FORMAT"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        test_fail "kubectl-availability" "kubectl is not installed or not in PATH" "Install kubectl and ensure it's in your PATH"
        return 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        test_fail "cluster-connectivity" "Cannot connect to Kubernetes cluster" "Verify kubectl configuration and cluster accessibility"
        return 1
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        test_fail "namespace-exists" "Namespace $NAMESPACE does not exist" "Create the namespace or check if services are deployed to a different namespace"
        return 1
    fi
    
    test_pass "prerequisites" "All prerequisites met"
    return 0
}

# Check cluster health
check_cluster_health() {
    log "Checking cluster health..."
    
    # Check node status
    local unhealthy_nodes=$(kubectl get nodes --no-headers | awk '$2 != "Ready" {print $1}' | wc -l)
    if [ "$unhealthy_nodes" -gt 0 ]; then
        test_fail "node-health" "$unhealthy_nodes nodes are not in Ready state" "Investigate and fix unhealthy nodes"
    else
        test_pass "node-health" "All nodes are healthy"
    fi
    
    # Check system pods
    local failing_system_pods=$(kubectl get pods -n kube-system --no-headers | awk '$3 != "Running" && $3 != "Completed" {print $1}' | wc -l)
    if [ "$failing_system_pods" -gt 0 ]; then
        test_warn "system-pods" "$failing_system_pods system pods are not running" "Check system pod logs and restart if necessary"
    else
        test_pass "system-pods" "All system pods are running"
    fi
    
    # Check cluster capacity
    local total_cpu=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum+=$3} END {print sum}' || echo 0)
    local total_memory=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum+=$5} END {print sum}' || echo 0)
    
    if [ "$total_cpu" -gt 80 ] || [ "$total_memory" -gt 80 ]; then
        test_warn "cluster-capacity" "Cluster resource utilization is high (CPU: ${total_cpu}%, Memory: ${total_memory}%)" "Consider scaling the cluster or optimizing resource requests"
    else
        test_pass "cluster-capacity" "Cluster has sufficient capacity"
    fi
}

# Check service deployment status
check_service_deployment() {
    log "Checking service deployment status..."
    
    local services=("api-gateway" "content-generation" "market-intelligence" "publishing-service" "cover-designer" "sales-monitor" "personalization" "research" "multimodal-generator")
    
    for service in "${services[@]}"; do
        # Check deployment exists
        if ! kubectl get deployment "$service" -n "$NAMESPACE" &> /dev/null; then
            test_fail "service-deployment-$service" "Service $service deployment not found" "Deploy the $service service"
            continue
        fi
        
        # Check deployment status
        local ready_replicas=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local desired_replicas=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        
        if [ "$ready_replicas" != "$desired_replicas" ] || [ "$ready_replicas" = "0" ]; then
            test_fail "service-health-$service" "Service $service has $ready_replicas/$desired_replicas replicas ready" "Check pod logs and fix deployment issues"
        else
            test_pass "service-health-$service" "Service $service is healthy ($ready_replicas/$desired_replicas replicas)"
        fi
        
        # Check HPA configuration
        if kubectl get hpa "${service}-hpa" -n "$NAMESPACE" &> /dev/null; then
            test_pass "service-hpa-$service" "Service $service has HPA configured"
        else
            test_warn "service-hpa-$service" "Service $service does not have HPA configured" "Configure HPA for better scaling"
        fi
    done
}

# Check security configuration
check_security_configuration() {
    log "Checking security configuration..."
    
    # Check pod security standards
    local pods_without_security_context=$(kubectl get pods -n "$NAMESPACE" -o json | jq -r '.items[] | select(.spec.securityContext.runAsNonRoot != true or .spec.securityContext.runAsUser == null) | .metadata.name' | wc -l)
    if [ "$pods_without_security_context" -gt 0 ]; then
        test_fail "pod-security-context" "$pods_without_security_context pods do not have proper security context" "Configure runAsNonRoot and runAsUser for all pods"
    else
        test_pass "pod-security-context" "All pods have proper security context"
    fi
    
    # Check network policies
    local network_policies=$(kubectl get networkpolicy -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$network_policies" -eq 0 ]; then
        test_warn "network-policies" "No network policies found" "Implement network policies for micro-segmentation"
    else
        test_pass "network-policies" "$network_policies network policies configured"
    fi
    
    # Check RBAC
    local service_accounts=$(kubectl get serviceaccount -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$service_accounts" -lt 3 ]; then
        test_warn "rbac-service-accounts" "Limited service accounts found ($service_accounts)" "Create dedicated service accounts for each service"
    else
        test_pass "rbac-service-accounts" "Service accounts configured ($service_accounts found)"
    fi
    
    # Check secrets encryption
    if kubectl get secrets -n "$NAMESPACE" -o json | jq -r '.items[0].data | keys[]' | head -1 | base64 -d &> /dev/null; then
        test_pass "secrets-encryption" "Secrets are properly encoded"
    else
        test_fail "secrets-encryption" "Secrets may not be properly encrypted" "Ensure secrets are base64 encoded and cluster has encryption at rest"
    fi
    
    # Check for privileged containers
    local privileged_containers=$(kubectl get pods -n "$NAMESPACE" -o json | jq -r '.items[].spec.containers[] | select(.securityContext.privileged == true) | .name' | wc -l)
    if [ "$privileged_containers" -gt 0 ]; then
        test_fail "privileged-containers" "$privileged_containers privileged containers found" "Remove privileged access from containers"
    else
        test_pass "privileged-containers" "No privileged containers found"
    fi
}

# Check performance and scaling
check_performance_scaling() {
    log "Checking performance and scaling configuration..."
    
    # Check resource requests and limits
    local pods_without_resources=$(kubectl get pods -n "$NAMESPACE" -o json | jq -r '.items[] | select(.spec.containers[].resources.requests.cpu == null or .spec.containers[].resources.limits.cpu == null) | .metadata.name' | wc -l)
    if [ "$pods_without_resources" -gt 0 ]; then
        test_warn "resource-limits" "$pods_without_resources pods do not have proper resource requests/limits" "Configure CPU and memory requests/limits for all containers"
    else
        test_pass "resource-limits" "All pods have resource requests and limits configured"
    fi
    
    # Check HPA configuration
    local hpa_count=$(kubectl get hpa -n "$NAMESPACE" --no-headers | wc -l)
    local deployment_count=$(kubectl get deployments -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$hpa_count" -lt "$deployment_count" ]; then
        test_warn "horizontal-pod-autoscaling" "Only $hpa_count/$deployment_count deployments have HPA" "Configure HPA for all production services"
    else
        test_pass "horizontal-pod-autoscaling" "All deployments have HPA configured"
    fi
    
    # Check PDB configuration
    local pdb_count=$(kubectl get pdb -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$pdb_count" -eq 0 ]; then
        test_fail "pod-disruption-budgets" "No Pod Disruption Budgets configured" "Configure PDBs to maintain availability during updates"
    else
        test_pass "pod-disruption-budgets" "$pdb_count Pod Disruption Budgets configured"
    fi
    
    # Check anti-affinity rules
    local deployments_with_antiaffinity=$(kubectl get deployments -n "$NAMESPACE" -o json | jq -r '.items[] | select(.spec.template.spec.affinity.podAntiAffinity != null) | .metadata.name' | wc -l)
    if [ "$deployments_with_antiaffinity" -lt 3 ]; then
        test_warn "pod-anti-affinity" "Only $deployments_with_antiaffinity deployments have anti-affinity rules" "Configure pod anti-affinity for better distribution"
    else
        test_pass "pod-anti-affinity" "Deployments have anti-affinity rules configured"
    fi
}

# Check backup and disaster recovery
check_backup_disaster_recovery() {
    log "Checking backup and disaster recovery..."
    
    # Check backup configuration (this would need to be customized based on backup solution)
    if kubectl get cronjobs -A | grep -q backup; then
        test_pass "backup-jobs" "Backup jobs are configured"
    else
        test_warn "backup-jobs" "No backup jobs found" "Configure automated backup jobs for persistent data"
    fi
    
    # Check persistent volume backup
    local pv_count=$(kubectl get pv | grep -c "$NAMESPACE" || echo 0)
    if [ "$pv_count" -gt 0 ]; then
        test_warn "persistent-volume-backup" "$pv_count persistent volumes need backup strategy" "Ensure persistent volumes are included in backup strategy"
    else
        test_pass "persistent-volume-backup" "No persistent volumes require backup"
    fi
    
    # Check multi-zone deployment
    local zones=$(kubectl get nodes -o json | jq -r '.items[].metadata.labels["topology.kubernetes.io/zone"]' | sort -u | wc -l)
    if [ "$zones" -lt 2 ] && [ "$ENVIRONMENT" = "production" ]; then
        test_fail "multi-zone-deployment" "Cluster spans only $zones availability zone(s)" "Deploy across multiple availability zones for high availability"
    else
        test_pass "multi-zone-deployment" "Cluster spans $zones availability zones"
    fi
}

# Check monitoring and alerting
check_monitoring_alerting() {
    log "Checking monitoring and alerting..."
    
    # Check Prometheus
    if kubectl get pods -A | grep -q prometheus; then
        test_pass "prometheus-deployment" "Prometheus is deployed"
    else
        test_fail "prometheus-deployment" "Prometheus not found" "Deploy Prometheus for metrics collection"
    fi
    
    # Check Grafana
    if kubectl get pods -A | grep -q grafana; then
        test_pass "grafana-deployment" "Grafana is deployed"
    else
        test_warn "grafana-deployment" "Grafana not found" "Deploy Grafana for metrics visualization"
    fi
    
    # Check ServiceMonitor configuration
    local service_monitors=$(kubectl get servicemonitor -n "$NAMESPACE" --no-headers 2>/dev/null | wc -l || echo 0)
    if [ "$service_monitors" -eq 0 ]; then
        test_warn "service-monitors" "No ServiceMonitors configured" "Configure ServiceMonitors for Prometheus scraping"
    else
        test_pass "service-monitors" "$service_monitors ServiceMonitors configured"
    fi
    
    # Check logging
    if kubectl get pods -A | grep -q "elasticsearch\|fluentd\|fluent-bit\|logstash"; then
        test_pass "logging-stack" "Logging stack is deployed"
    else
        test_warn "logging-stack" "Logging stack not found" "Deploy centralized logging solution"
    fi
    
    # Check alerts
    if kubectl get prometheusrules -A &> /dev/null; then
        local alert_rules=$(kubectl get prometheusrules -A --no-headers | wc -l)
        if [ "$alert_rules" -gt 0 ]; then
            test_pass "alerting-rules" "$alert_rules alert rules configured"
        else
            test_warn "alerting-rules" "No alert rules configured" "Configure alerting rules for critical metrics"
        fi
    else
        test_warn "alerting-rules" "No Prometheus rules found" "Configure alerting rules for monitoring"
    fi
}

# Check cost optimization
check_cost_optimization() {
    log "Checking cost optimization..."
    
    # Check node utilization
    if command -v kubectl &> /dev/null && kubectl top nodes &> /dev/null; then
        local avg_cpu=$(kubectl top nodes --no-headers | awk '{sum+=$3; count++} END {if(count>0) print sum/count; else print 0}')
        local avg_memory=$(kubectl top nodes --no-headers | awk '{sum+=$5; count++} END {if(count>0) print sum/count; else print 0}')
        
        if (( $(echo "$avg_cpu < 30" | bc -l) )) || (( $(echo "$avg_memory < 30" | bc -l) )); then
            test_warn "resource-utilization" "Low average resource utilization (CPU: ${avg_cpu}%, Memory: ${avg_memory}%)" "Consider downsizing cluster or using cluster autoscaler"
        else
            test_pass "resource-utilization" "Good resource utilization (CPU: ${avg_cpu}%, Memory: ${avg_memory}%)"
        fi
    else
        test_warn "resource-utilization" "Cannot check resource utilization" "Ensure metrics-server is installed"
    fi
    
    # Check for spot instances usage
    local spot_nodes=$(kubectl get nodes -o json | jq -r '.items[] | select(.metadata.labels["node.kubernetes.io/instance-type"] | contains("spot") or .metadata.labels["eks.amazonaws.com/capacityType"] == "SPOT") | .metadata.name' | wc -l)
    local total_nodes=$(kubectl get nodes --no-headers | wc -l)
    
    if [ "$spot_nodes" -eq 0 ] && [ "$total_nodes" -gt 3 ]; then
        test_warn "spot-instances" "No spot instances detected" "Consider using spot instances for cost savings"
    else
        test_pass "spot-instances" "Spot instances in use ($spot_nodes/$total_nodes nodes)"
    fi
    
    # Check cluster autoscaler
    if kubectl get pods -A | grep -q cluster-autoscaler; then
        test_pass "cluster-autoscaler" "Cluster autoscaler is deployed"
    else
        test_warn "cluster-autoscaler" "Cluster autoscaler not found" "Deploy cluster autoscaler for cost optimization"
    fi
}

# Check compliance and governance
check_compliance_governance() {
    log "Checking compliance and governance..."
    
    # Check audit logging
    if kubectl get pods -n kube-system | grep -q audit; then
        test_pass "audit-logging" "Audit logging is configured"
    else
        test_warn "audit-logging" "Audit logging not detected" "Enable Kubernetes audit logging for compliance"
    fi
    
    # Check resource quotas
    local resource_quotas=$(kubectl get resourcequota -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$resource_quotas" -eq 0 ]; then
        test_warn "resource-quotas" "No resource quotas configured" "Configure resource quotas to prevent resource exhaustion"
    else
        test_pass "resource-quotas" "$resource_quotas resource quotas configured"
    fi
    
    # Check limit ranges
    local limit_ranges=$(kubectl get limitrange -n "$NAMESPACE" --no-headers | wc -l)
    if [ "$limit_ranges" -eq 0 ]; then
        test_warn "limit-ranges" "No limit ranges configured" "Configure limit ranges for default resource limits"
    else
        test_pass "limit-ranges" "$limit_ranges limit ranges configured"
    fi
    
    # Check pod security policies/standards
    if kubectl get podsecuritypolicy &> /dev/null || kubectl get pods -n "$NAMESPACE" -o json | jq -r '.items[].metadata.labels."pod-security.kubernetes.io/enforce"' | grep -q restricted; then
        test_pass "pod-security-standards" "Pod security standards are enforced"
    else
        test_warn "pod-security-standards" "Pod security standards not detected" "Implement Pod Security Standards for compliance"
    fi
}

# Generate detailed report
generate_report() {
    if [ "$EXPORT_REPORT" = false ]; then
        return 0
    fi
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local report_file="production_readiness_report_${timestamp}.$REPORT_FORMAT"
    
    log "Generating detailed report: $report_file"
    
    case "$REPORT_FORMAT" in
        json)
            cat > "$report_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$ENVIRONMENT",
  "namespace": "$NAMESPACE",
  "summary": {
    "total_tests": $TOTAL_TESTS,
    "passed": $PASSED_TESTS,
    "warnings": $WARNING_TESTS,
    "failed": $FAILED_TESTS,
    "success_rate": $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)
  },
  "test_results": {
EOF
            local first=true
            for test_name in "${!TEST_RESULTS[@]}"; do
                if [ "$first" = false ]; then
                    echo "," >> "$report_file"
                fi
                first=false
                echo -n "    \"$test_name\": {" >> "$report_file"
                echo -n "\"status\": \"${TEST_RESULTS[$test_name]}\", " >> "$report_file"
                echo -n "\"details\": \"${TEST_RECOMMENDATIONS[$test_name]}\"" >> "$report_file"
                echo -n "}" >> "$report_file"
            done
            cat >> "$report_file" << EOF

  }
}
EOF
            ;;
        yaml)
            cat > "$report_file" << EOF
timestamp: $(date -u +%Y-%m-%dT%H:%M:%SZ)
environment: $ENVIRONMENT
namespace: $NAMESPACE
summary:
  total_tests: $TOTAL_TESTS
  passed: $PASSED_TESTS
  warnings: $WARNING_TESTS
  failed: $FAILED_TESTS
  success_rate: $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)
test_results:
EOF
            for test_name in "${!TEST_RESULTS[@]}"; do
                echo "  $test_name:" >> "$report_file"
                echo "    status: ${TEST_RESULTS[$test_name]}" >> "$report_file"
                echo "    details: \"${TEST_RECOMMENDATIONS[$test_name]}\"" >> "$report_file"
            done
            ;;
        html)
            cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Production Readiness Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .pass { background-color: #d4edda; border-left: 4px solid #28a745; }
        .warn { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .fail { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .test-name { font-weight: bold; }
        .test-details { margin-top: 5px; font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Production Readiness Report</h1>
        <p><strong>Environment:</strong> $ENVIRONMENT</p>
        <p><strong>Namespace:</strong> $NAMESPACE</p>
        <p><strong>Generated:</strong> $(date)</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Tests:</strong> $TOTAL_TESTS</p>
        <p><strong>Passed:</strong> $PASSED_TESTS</p>
        <p><strong>Warnings:</strong> $WARNING_TESTS</p>
        <p><strong>Failed:</strong> $FAILED_TESTS</p>
        <p><strong>Success Rate:</strong> $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)%</p>
    </div>
    
    <div class="results">
        <h2>Test Results</h2>
EOF
            for test_name in "${!TEST_RESULTS[@]}"; do
                local status="${TEST_RESULTS[$test_name]}"
                local css_class=$(echo "$status" | tr '[:upper:]' '[:lower:]')
                echo "        <div class=\"test-result $css_class\">" >> "$report_file"
                echo "            <div class=\"test-name\">$test_name</div>" >> "$report_file"
                echo "            <div class=\"test-details\">${TEST_RECOMMENDATIONS[$test_name]}</div>" >> "$report_file"
                echo "        </div>" >> "$report_file"
            done
            cat >> "$report_file" << EOF
    </div>
</body>
</html>
EOF
            ;;
    esac
    
    info "Report generated: $report_file"
}

# Attempt to fix issues automatically
fix_issues() {
    if [ "$FIX_ISSUES" = false ]; then
        return 0
    fi
    
    log "Attempting to fix issues automatically..."
    
    # Example fixes (would need to be expanded based on specific issues)
    for test_name in "${!TEST_RESULTS[@]}"; do
        local status="${TEST_RESULTS[$test_name]}"
        
        case "$test_name" in
            "service-hpa-"*)
                if [ "$status" = "WARN" ]; then
                    local service_name=$(echo "$test_name" | sed 's/service-hpa-//')
                    info "Creating HPA for service: $service_name"
                    # This would create an HPA configuration
                fi
                ;;
            "resource-quotas")
                if [ "$status" = "WARN" ]; then
                    info "Creating default resource quota"
                    # This would create a resource quota
                fi
                ;;
        esac
    done
}

# Main function
main() {
    log "Starting Production Readiness Check for Publish AI"
    
    parse_args "$@"
    
    # Run prerequisite checks first
    if ! check_prerequisites; then
        error "Prerequisites check failed. Cannot continue."
        exit 1
    fi
    
    # Run all checks
    check_cluster_health
    check_service_deployment
    check_security_configuration
    check_performance_scaling
    check_backup_disaster_recovery
    check_monitoring_alerting
    check_cost_optimization
    check_compliance_governance
    
    # Attempt fixes if requested
    fix_issues
    
    # Generate report
    generate_report
    
    # Summary
    echo
    log "Production Readiness Check Complete"
    echo
    info "📊 Summary:"
    info "  Total Tests: $TOTAL_TESTS"
    info "  ✅ Passed: $PASSED_TESTS"
    info "  ⚠️  Warnings: $WARNING_TESTS"
    info "  ❌ Failed: $FAILED_TESTS"
    
    local success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)
    info "  📈 Success Rate: ${success_rate}%"
    
    echo
    if [ "$FAILED_TESTS" -eq 0 ]; then
        if [ "$WARNING_TESTS" -eq 0 ]; then
            log "🎉 Production environment is ready! All checks passed."
            exit 0
        else
            warn "⚠️  Production environment has warnings but is deployable."
            warn "Address warnings to improve production readiness."
            exit 0
        fi
    else
        error "❌ Production environment is NOT ready!"
        error "Fix $FAILED_TESTS critical issues before deploying to production."
        exit 1
    fi
}

# Run main function
main "$@"