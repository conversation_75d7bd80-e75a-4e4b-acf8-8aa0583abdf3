#!/bin/bash

# Deploy Production Kubernetes Cluster for Publish AI
# Supports AWS EKS, Google GKE, and Azure AKS

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PRODUCTION_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$PRODUCTION_CONFIG_DIR")")"

# Default values
PROVIDER="aws"
REGION=""
ENVIRONMENT="production"
CLUSTER_NAME=""
CONFIG_FILE=""
DRY_RUN=false
SKIP_INFRASTRUCTURE=false
FORCE_RECREATION=false

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy production-ready Kubernetes cluster for Publish AI.

Options:
  -p, --provider PROVIDER     Cloud provider: aws, gcp, azure (default: aws)
  -r, --region REGION         Cloud region (required)
  -e, --environment ENV       Environment: production, staging, development (default: production)
  -n, --cluster-name NAME     Custom cluster name
  -c, --config FILE           Custom configuration file
  -d, --dry-run               Preview deployment without making changes
  -s, --skip-infrastructure   Skip infrastructure provisioning
  -f, --force-recreation      Force recreation of existing resources
  -h, --help                  Show this help message

Supported Providers:
  aws     - Amazon Web Services (EKS)
  gcp     - Google Cloud Platform (GKE)
  azure   - Microsoft Azure (AKS)

Environment Configurations:
  production   - Multi-zone, high availability, 15-50+ nodes
  staging      - Single-zone, cost-optimized, 6-12 nodes
  development  - Single-zone, minimal resources, 3-6 nodes

Examples:
  $0 --provider aws --region us-west-2
  $0 --provider gcp --region us-central1-a --environment staging
  $0 --provider azure --region eastus --cluster-name publish-ai-prod
  $0 --provider aws --region us-east-1 --dry-run

Prerequisites:
  AWS: aws-cli, terraform
  GCP: gcloud, terraform
  Azure: az-cli, terraform

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--provider)
                PROVIDER="$2"
                shift 2
                ;;
            -r|--region)
                REGION="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--cluster-name)
                CLUSTER_NAME="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -s|--skip-infrastructure)
                SKIP_INFRASTRUCTURE=true
                shift
                ;;
            -f|--force-recreation)
                FORCE_RECREATION=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate required parameters
    if [ -z "$REGION" ]; then
        error "Region is required. Use --region to specify."
        usage
        exit 1
    fi
    
    # Validate provider
    if [[ ! "$PROVIDER" =~ ^(aws|gcp|azure)$ ]]; then
        error "Invalid provider: $PROVIDER. Must be aws, gcp, or azure."
        exit 1
    fi
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(production|staging|development)$ ]]; then
        error "Invalid environment: $ENVIRONMENT. Must be production, staging, or development."
        exit 1
    fi
    
    # Set default cluster name if not provided
    if [ -z "$CLUSTER_NAME" ]; then
        CLUSTER_NAME="publish-ai-${ENVIRONMENT}"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites for $PROVIDER deployment..."
    
    case "$PROVIDER" in
        aws)
            # Check AWS CLI
            if ! command -v aws &> /dev/null; then
                error "AWS CLI is not installed. Please install it first."
                exit 1
            fi
            
            # Check AWS credentials
            if ! aws sts get-caller-identity &> /dev/null; then
                error "AWS credentials not configured. Run 'aws configure'."
                exit 1
            fi
            
            # Check Terraform
            if ! command -v terraform &> /dev/null; then
                error "Terraform is not installed. Please install it first."
                exit 1
            fi
            
            info "AWS CLI and Terraform found"
            ;;
        gcp)
            # Check gcloud CLI
            if ! command -v gcloud &> /dev/null; then
                error "gcloud CLI is not installed. Please install it first."
                exit 1
            fi
            
            # Check gcloud authentication
            if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
                error "gcloud not authenticated. Run 'gcloud auth login'."
                exit 1
            fi
            
            # Check Terraform
            if ! command -v terraform &> /dev/null; then
                error "Terraform is not installed. Please install it first."
                exit 1
            fi
            
            info "gcloud CLI and Terraform found"
            ;;
        azure)
            # Check Azure CLI
            if ! command -v az &> /dev/null; then
                error "Azure CLI is not installed. Please install it first."
                exit 1
            fi
            
            # Check Azure authentication
            if ! az account show &> /dev/null; then
                error "Azure CLI not authenticated. Run 'az login'."
                exit 1
            fi
            
            # Check Terraform
            if ! command -v terraform &> /dev/null; then
                error "Terraform is not installed. Please install it first."
                exit 1
            fi
            
            info "Azure CLI and Terraform found"
            ;;
    esac
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed. Please install it first."
        exit 1
    fi
    
    # Check Helm
    if ! command -v helm &> /dev/null; then
        error "Helm is not installed. Please install it first."
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Get environment-specific configuration
get_environment_config() {
    local env="$1"
    
    case "$env" in
        production)
            NODE_POOLS=5
            MIN_NODES=15
            MAX_NODES=50
            ZONES=3
            HIGH_AVAILABILITY=true
            SPOT_INSTANCES=true
            MONITORING_LEVEL="full"
            ;;
        staging)
            NODE_POOLS=3
            MIN_NODES=6
            MAX_NODES=12
            ZONES=1
            HIGH_AVAILABILITY=false
            SPOT_INSTANCES=true
            MONITORING_LEVEL="basic"
            ;;
        development)
            NODE_POOLS=2
            MIN_NODES=3
            MAX_NODES=6
            ZONES=1
            HIGH_AVAILABILITY=false
            SPOT_INSTANCES=true
            MONITORING_LEVEL="minimal"
            ;;
    esac
    
    info "Environment configuration:"
    info "  Node pools: $NODE_POOLS"
    info "  Node range: $MIN_NODES-$MAX_NODES"
    info "  Zones: $ZONES"
    info "  High availability: $HIGH_AVAILABILITY"
    info "  Spot instances: $SPOT_INSTANCES"
    info "  Monitoring: $MONITORING_LEVEL"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    if [ "$SKIP_INFRASTRUCTURE" = true ]; then
        info "Skipping infrastructure deployment as requested"
        return 0
    fi
    
    log "Deploying infrastructure with Terraform..."
    
    local terraform_dir="$PRODUCTION_CONFIG_DIR/$PROVIDER"
    local tfvars_file="$terraform_dir/terraform.tfvars"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy infrastructure with Terraform"
        return 0
    fi
    
    # Navigate to provider-specific directory
    cd "$terraform_dir"
    
    # Create terraform.tfvars file
    cat > "$tfvars_file" << EOF
cluster_name    = "$CLUSTER_NAME"
region          = "$REGION"
environment     = "$ENVIRONMENT"
cluster_version = "1.28"

# Environment-specific configuration
min_nodes       = $MIN_NODES
max_nodes       = $MAX_NODES
zones           = $ZONES
high_availability = $HIGH_AVAILABILITY
spot_instances    = $SPOT_INSTANCES
monitoring_level  = "$MONITORING_LEVEL"

# Tags
tags = {
  Environment = "$ENVIRONMENT"
  Project     = "publish-ai"
  ManagedBy   = "terraform"
  DeployedBy  = "$(whoami)"
  DeployedAt  = "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
    
    # Initialize Terraform
    log "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    log "Planning infrastructure deployment..."
    terraform plan -var-file="$tfvars_file" -out=tfplan
    
    # Apply if not dry run
    if [ "$FORCE_RECREATION" = true ]; then
        warn "Force recreation enabled - existing resources will be destroyed"
        terraform destroy -var-file="$tfvars_file" -auto-approve || true
    fi
    
    log "Applying infrastructure deployment..."
    terraform apply tfplan
    
    # Save outputs
    terraform output -json > terraform-outputs.json
    
    log "Infrastructure deployment completed"
    
    # Return to original directory
    cd - > /dev/null
}

# Configure kubectl for the cluster
configure_kubectl() {
    log "Configuring kubectl for cluster access..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would configure kubectl"
        return 0
    fi
    
    case "$PROVIDER" in
        aws)
            aws eks update-kubeconfig --region "$REGION" --name "$CLUSTER_NAME"
            ;;
        gcp)
            local zone_param=""
            if [ "$ZONES" -eq 1 ]; then
                zone_param="--zone $REGION"
            else
                zone_param="--region ${REGION%-*}"
            fi
            gcloud container clusters get-credentials "$CLUSTER_NAME" $zone_param
            ;;
        azure)
            local resource_group="publish-ai-${ENVIRONMENT}-rg"
            az aks get-credentials --resource-group "$resource_group" --name "$CLUSTER_NAME"
            ;;
    esac
    
    # Verify cluster access
    if kubectl cluster-info &> /dev/null; then
        log "kubectl configured successfully"
    else
        error "Failed to configure kubectl"
        exit 1
    fi
    
    # Display cluster information
    info "Cluster information:"
    kubectl cluster-info
}

# Install essential cluster components
install_cluster_components() {
    log "Installing essential cluster components..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would install cluster components"
        return 0
    fi
    
    # Install AWS Load Balancer Controller (AWS only)
    if [ "$PROVIDER" = "aws" ]; then
        log "Installing AWS Load Balancer Controller..."
        
        # Add EKS Helm repository
        helm repo add eks https://aws.github.io/eks-charts
        helm repo update
        
        # Install AWS Load Balancer Controller
        helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller \
            -n kube-system \
            --set clusterName="$CLUSTER_NAME" \
            --set serviceAccount.create=false \
            --set serviceAccount.name=aws-load-balancer-controller
    fi
    
    # Install Cluster Autoscaler
    log "Installing Cluster Autoscaler..."
    
    case "$PROVIDER" in
        aws)
            helm repo add autoscaler https://kubernetes.github.io/autoscaler
            helm repo update
            
            helm upgrade --install cluster-autoscaler autoscaler/cluster-autoscaler \
                -n kube-system \
                --set autoDiscovery.clusterName="$CLUSTER_NAME" \
                --set awsRegion="$REGION" \
                --set serviceAccount.create=false \
                --set serviceAccount.name=cluster-autoscaler
            ;;
        gcp)
            # GKE has built-in cluster autoscaler
            info "GKE cluster autoscaler is managed by Google"
            ;;
        azure)
            # AKS has built-in cluster autoscaler
            info "AKS cluster autoscaler is managed by Azure"
            ;;
    esac
    
    # Install Metrics Server (if not already present)
    if ! kubectl get deployment metrics-server -n kube-system &> /dev/null; then
        log "Installing Metrics Server..."
        kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    fi
    
    # Install Vertical Pod Autoscaler
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Installing Vertical Pod Autoscaler..."
        
        # Clone VPA repository
        local vpa_dir="/tmp/autoscaler"
        if [ ! -d "$vpa_dir" ]; then
            git clone https://github.com/kubernetes/autoscaler.git "$vpa_dir"
        fi
        
        cd "$vpa_dir/vertical-pod-autoscaler"
        ./hack/vpa-up.sh
        cd - > /dev/null
    fi
    
    log "Cluster components installed successfully"
}

# Install monitoring stack
install_monitoring() {
    if [ "$MONITORING_LEVEL" = "minimal" ]; then
        info "Skipping monitoring installation for minimal environment"
        return 0
    fi
    
    log "Installing monitoring stack..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would install monitoring stack"
        return 0
    fi
    
    # Run monitoring deployment script
    "$PROJECT_ROOT/infrastructure/monitoring/scripts/deploy-monitoring-stack.sh" \
        --environment "$ENVIRONMENT" \
        --cluster-name "$CLUSTER_NAME"
    
    log "Monitoring stack installed successfully"
}

# Install service mesh
install_service_mesh() {
    if [ "$ENVIRONMENT" = "development" ]; then
        info "Skipping service mesh installation for development environment"
        return 0
    fi
    
    log "Installing Istio service mesh..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would install service mesh"
        return 0
    fi
    
    # Run service mesh deployment script
    "$PROJECT_ROOT/infrastructure/service-mesh/scripts/deploy-istio.sh" \
        --environment "$ENVIRONMENT"
    
    log "Service mesh installed successfully"
}

# Install logging stack
install_logging() {
    if [ "$MONITORING_LEVEL" = "minimal" ]; then
        info "Skipping logging installation for minimal environment"
        return 0
    fi
    
    log "Installing centralized logging..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would install logging stack"
        return 0
    fi
    
    # Run logging deployment script
    "$PROJECT_ROOT/infrastructure/logging/scripts/deploy-elk-stack.sh" \
        --environment "$ENVIRONMENT"
    
    log "Logging stack installed successfully"
}

# Verify cluster deployment
verify_deployment() {
    log "Verifying cluster deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check cluster health
    info "Checking cluster health..."
    kubectl get nodes
    kubectl get pods --all-namespaces
    
    # Check system components
    info "Checking system components..."
    kubectl get pods -n kube-system
    
    # Check monitoring (if installed)
    if [ "$MONITORING_LEVEL" != "minimal" ] && kubectl get namespace monitoring &> /dev/null; then
        info "Checking monitoring stack..."
        kubectl get pods -n monitoring
    fi
    
    # Check service mesh (if installed)
    if [ "$ENVIRONMENT" != "development" ] && kubectl get namespace istio-system &> /dev/null; then
        info "Checking service mesh..."
        kubectl get pods -n istio-system
    fi
    
    # Check logging (if installed)
    if [ "$MONITORING_LEVEL" != "minimal" ] && kubectl get namespace logging &> /dev/null; then
        info "Checking logging stack..."
        kubectl get pods -n logging
    fi
    
    # Resource usage summary
    info "Cluster resource summary:"
    kubectl top nodes 2>/dev/null || info "Metrics not yet available"
    
    log "Cluster verification completed"
}

# Show access information
show_access_info() {
    log "Production cluster access information:"
    
    local kubeconfig_path="$HOME/.kube/config"
    local context_name=""
    
    case "$PROVIDER" in
        aws)
            context_name="arn:aws:eks:$REGION:$(aws sts get-caller-identity --query Account --output text):cluster/$CLUSTER_NAME"
            ;;
        gcp)
            local project_id=$(gcloud config get-value project)
            if [ "$ZONES" -eq 1 ]; then
                context_name="gke_${project_id}_${REGION}_${CLUSTER_NAME}"
            else
                context_name="gke_${project_id}_${REGION%-*}_${CLUSTER_NAME}"
            fi
            ;;
        azure)
            context_name="$CLUSTER_NAME"
            ;;
    esac
    
    info ""
    info "Cluster Details:"
    info "  Provider: $PROVIDER"
    info "  Region: $REGION"
    info "  Environment: $ENVIRONMENT"
    info "  Cluster Name: $CLUSTER_NAME"
    info "  Kubectl Context: $context_name"
    
    info ""
    info "Access Commands:"
    info "  kubectl get nodes                           # List cluster nodes"
    info "  kubectl get pods --all-namespaces           # List all pods"
    info "  kubectl config current-context              # Show current context"
    
    case "$PROVIDER" in
        aws)
            info "  aws eks update-kubeconfig --region $REGION --name $CLUSTER_NAME  # Reconfigure kubectl"
            ;;
        gcp)
            info "  gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION  # Reconfigure kubectl"
            ;;
        azure)
            info "  az aks get-credentials --resource-group publish-ai-${ENVIRONMENT}-rg --name $CLUSTER_NAME  # Reconfigure kubectl"
            ;;
    esac
    
    info ""
    info "Next Steps:"
    info "1. Deploy Publish AI services:"
    info "   ./scripts/deploy-production-services.sh --environment $ENVIRONMENT"
    info ""
    info "2. Verify deployment:"
    info "   ./scripts/verify-production-deployment.sh"
    info ""
    info "3. Run production readiness checks:"
    info "   ./scripts/production-readiness-check.sh"
    
    if [ "$MONITORING_LEVEL" != "minimal" ]; then
        info ""
        info "Monitoring Access:"
        info "  kubectl port-forward svc/grafana 3000:80 -n monitoring"
        info "  Open http://localhost:3000 (admin/admin)"
        
        info ""
        info "Logging Access:"
        info "  kubectl port-forward svc/kibana 5601:5601 -n logging"
        info "  Open http://localhost:5601"
    fi
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        error "Production cluster deployment failed with exit code $exit_code"
        
        if [ "$DRY_RUN" = false ]; then
            warn "Check the following for troubleshooting:"
            warn "  Terraform state: $PRODUCTION_CONFIG_DIR/$PROVIDER/terraform.tfstate"
            warn "  Terraform logs: Check terraform output above"
            warn "  Cloud provider console for resource status"
        fi
    fi
}

# Main function
main() {
    log "Starting Production Kubernetes Cluster Deployment for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    get_environment_config "$ENVIRONMENT"
    
    # Confirm deployment for production
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy a PRODUCTION Kubernetes cluster with:"
        warn "  Provider: $PROVIDER"
        warn "  Region: $REGION"
        warn "  Estimated cost: $50-500+ per month depending on usage"
        warn "  Node range: $MIN_NODES-$MAX_NODES nodes"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    deploy_infrastructure
    configure_kubectl
    install_cluster_components
    install_monitoring
    install_service_mesh
    install_logging
    verify_deployment
    show_access_info
    
    log "✅ Production cluster deployment completed successfully!"
    log ""
    info "🎉 Your Publish AI production cluster is ready!"
    info "💰 Remember to monitor costs and optimize resource usage"
    info "🔒 Ensure security best practices are followed"
    info "📊 Set up monitoring and alerting for production workloads"
}

# Set up signal handlers
trap cleanup EXIT

# Run main function
main "$@"