#!/bin/bash

# Deploy Production Services for Publish AI
# Deploys all microservices with production configurations, auto-scaling, and monitoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PRODUCTION_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$PRODUCTION_CONFIG_DIR")")"

# Default values
ENVIRONMENT="production"
NAMESPACE="publish-ai-production"
IMAGE_TAG="latest"
SKIP_BUILD=false
DRY_RUN=false
ROLLBACK_ON_FAILURE=true
HEALTH_CHECK_TIMEOUT=300
DEPLOY_SERVICES=""

# Service configurations
declare -A SERVICE_CONFIGS=(
    ["api-gateway"]="critical:3:10:500m:1Gi:2000m:4Gi"
    ["content-generation"]="high:2:15:1000m:2Gi:4000m:8Gi"
    ["market-intelligence"]="high:2:8:500m:1Gi:2000m:4Gi"
    ["publishing-service"]="critical:2:5:300m:512Mi:1000m:2Gi"
    ["cover-designer"]="normal:2:8:800m:1.5Gi:3000m:6Gi"
    ["sales-monitor"]="normal:1:3:200m:256Mi:500m:1Gi"
    ["personalization"]="normal:1:5:300m:512Mi:1000m:2Gi"
    ["research"]="normal:1:3:200m:256Mi:500m:1Gi"
    ["multimodal-generator"]="high:1:10:1500m:3Gi:6000m:12Gi"
)

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Publish AI microservices to production Kubernetes cluster.

Options:
  -e, --environment ENV       Environment: production, staging, development (default: production)
  -n, --namespace NAMESPACE   Kubernetes namespace (default: publish-ai-production)
  -t, --image-tag TAG         Docker image tag (default: latest)
  -s, --services SERVICES     Comma-separated list of services to deploy (default: all)
  -b, --skip-build            Skip Docker image building
  -d, --dry-run               Preview deployment without making changes
  -r, --no-rollback           Disable automatic rollback on failure
  -w, --health-timeout SEC    Health check timeout in seconds (default: 300)
  -h, --help                  Show this help message

Services:
  api-gateway           - API Gateway service (critical)
  content-generation    - Content generation service (high priority)
  market-intelligence   - Market intelligence service (high priority)
  publishing-service    - Publishing service (critical)
  cover-designer        - Cover designer service (normal priority)
  sales-monitor         - Sales monitoring service (normal priority)
  personalization       - Personalization engine (normal priority)
  research              - Research service (normal priority)
  multimodal-generator  - Multimodal generator service (high priority)

Deployment Features:
  - Zero-downtime rolling updates
  - Automatic health checks and readiness probes
  - Horizontal Pod Autoscaling (HPA)
  - Pod Disruption Budgets (PDB)
  - Resource requests and limits
  - Network policies and security contexts
  - Monitoring and logging integration

Examples:
  $0                                          # Deploy all services to production
  $0 --environment staging                    # Deploy to staging environment
  $0 --services api-gateway,content-generation  # Deploy specific services
  $0 --image-tag v1.2.3 --skip-build         # Deploy specific version without building
  $0 --dry-run                                # Preview deployment

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -t|--image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -s|--services)
                DEPLOY_SERVICES="$2"
                shift 2
                ;;
            -b|--skip-build)
                SKIP_BUILD=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -r|--no-rollback)
                ROLLBACK_ON_FAILURE=false
                shift
                ;;
            -w|--health-timeout)
                HEALTH_CHECK_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(production|staging|development)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
    
    # Set namespace based on environment if not explicitly set
    if [ "$NAMESPACE" = "publish-ai-production" ] && [ "$ENVIRONMENT" != "production" ]; then
        NAMESPACE="publish-ai-${ENVIRONMENT}"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check Docker (if not skipping build)
    if [ "$SKIP_BUILD" = false ] && ! command -v docker &> /dev/null; then
        error "Docker is not installed (required for building images)"
        exit 1
    fi
    
    # Check Helm
    if ! command -v helm &> /dev/null; then
        error "Helm is not installed"
        exit 1
    fi
    
    # Verify cluster has sufficient resources
    local nodes=$(kubectl get nodes --no-headers | wc -l)
    if [ "$nodes" -lt 3 ] && [ "$ENVIRONMENT" = "production" ]; then
        warn "Production cluster has less than 3 nodes. This may impact availability."
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        info "Namespace $NAMESPACE does not exist. It will be created."
    fi
    
    log "Prerequisites check passed"
}

# Build Docker images
build_images() {
    if [ "$SKIP_BUILD" = true ]; then
        info "Skipping Docker image build as requested"
        return 0
    fi
    
    log "Building Docker images for services..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would build Docker images"
        return 0
    fi
    
    # Get list of services to deploy
    local services_to_build
    if [ -n "$DEPLOY_SERVICES" ]; then
        IFS=',' read -ra services_to_build <<< "$DEPLOY_SERVICES"
    else
        services_to_build=("${!SERVICE_CONFIGS[@]}")
    fi
    
    # Build each service image
    for service in "${services_to_build[@]}"; do
        local service_dir="$PROJECT_ROOT/services/$service"
        
        if [ ! -d "$service_dir" ]; then
            warn "Service directory not found: $service_dir. Skipping."
            continue
        fi
        
        if [ ! -f "$service_dir/Dockerfile" ]; then
            warn "Dockerfile not found for service: $service. Skipping."
            continue
        fi
        
        info "Building image for service: $service"
        
        # Build and tag the image
        local image_name="publish-ai/$service:$IMAGE_TAG"
        docker build -t "$image_name" "$service_dir"
        
        # Also tag as latest for local development
        docker tag "$image_name" "publish-ai/$service:latest"
        
        info "Built image: $image_name"
    done
    
    log "Docker images built successfully"
}

# Create namespace and basic resources
create_namespace() {
    log "Creating namespace and basic resources..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace $NAMESPACE"
        return 0
    fi
    
    # Create namespace
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespace
    kubectl label namespace "$NAMESPACE" \
        environment="$ENVIRONMENT" \
        project="publish-ai" \
        managed-by="kubectl" \
        --overwrite
    
    # Enable Istio injection if Istio is present
    if kubectl get namespace istio-system &> /dev/null; then
        kubectl label namespace "$NAMESPACE" istio-injection=enabled --overwrite
    fi
    
    log "Namespace $NAMESPACE created and configured"
}

# Deploy secrets and configuration
deploy_configuration() {
    log "Deploying configuration and secrets..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy configuration"
        return 0
    fi
    
    # Create production configuration from template
    local config_template="$PRODUCTION_CONFIG_DIR/k8s/production-services.yaml"
    local temp_config="/tmp/production-config-${NAMESPACE}.yaml"
    
    # Replace namespace placeholders
    sed "s/namespace: publish-ai-production/namespace: $NAMESPACE/g" "$config_template" > "$temp_config"
    
    # Apply configuration
    kubectl apply -f "$temp_config" --namespace="$NAMESPACE"
    
    # Clean up temp file
    rm -f "$temp_config"
    
    # Create image pull secrets if needed
    if [ "$IMAGE_TAG" != "latest" ]; then
        info "Creating image pull secrets for private registry"
        # Add image pull secret creation logic here if using private registry
    fi
    
    log "Configuration deployed successfully"
}

# Deploy individual service
deploy_service() {
    local service_name="$1"
    local config="${SERVICE_CONFIGS[$service_name]}"
    
    # Parse service configuration
    IFS=':' read -ra config_parts <<< "$config"
    local priority="${config_parts[0]}"
    local min_replicas="${config_parts[1]}"
    local max_replicas="${config_parts[2]}"
    local cpu_request="${config_parts[3]}"
    local memory_request="${config_parts[4]}"
    local cpu_limit="${config_parts[5]}"
    local memory_limit="${config_parts[6]}"
    
    log "Deploying service: $service_name (priority: $priority)"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy $service_name with $min_replicas-$max_replicas replicas"
        return 0
    fi
    
    # Create service-specific deployment manifest
    local deployment_manifest="/tmp/${service_name}-deployment.yaml"
    
    cat > "$deployment_manifest" << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name
    tier: microservice
    version: v1
    environment: $ENVIRONMENT
spec:
  replicas: $min_replicas
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: $service_name
  template:
    metadata:
      labels:
        app: $service_name
        tier: microservice
        version: v1
        environment: $ENVIRONMENT
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      priorityClassName: ${priority}-priority
      serviceAccountName: $service_name
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["$service_name"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: $service_name
        image: publish-ai/$service_name:$IMAGE_TAG
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        - name: SERVICE_NAME
          value: "$service_name"
        - name: ENVIRONMENT
          value: "$ENVIRONMENT"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: $cpu_request
            memory: $memory_request
            ephemeral-storage: 1Gi
          limits:
            cpu: $cpu_limit
            memory: $memory_limit
            ephemeral-storage: 5Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: $service_name

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ${service_name}-hpa
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: $service_name
  minReplicas: $min_replicas
  maxReplicas: $max_replicas
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name

EOF
    
    # Apply the deployment
    kubectl apply -f "$deployment_manifest"
    
    # Clean up temp file
    rm -f "$deployment_manifest"
    
    info "Service $service_name deployed successfully"
}

# Wait for service health
wait_for_service_health() {
    local service_name="$1"
    local timeout="$HEALTH_CHECK_TIMEOUT"
    
    log "Waiting for $service_name to be healthy (timeout: ${timeout}s)..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would wait for $service_name health"
        return 0
    fi
    
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    while [ $(date +%s) -lt $end_time ]; do
        local ready_replicas=$(kubectl get deployment "$service_name" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local desired_replicas=$(kubectl get deployment "$service_name" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        
        if [ "$ready_replicas" = "$desired_replicas" ] && [ "$ready_replicas" != "0" ]; then
            info "✅ Service $service_name is healthy ($ready_replicas/$desired_replicas replicas ready)"
            return 0
        fi
        
        info "⏳ Waiting for $service_name... ($ready_replicas/$desired_replicas replicas ready)"
        sleep 10
    done
    
    error "❌ Service $service_name failed to become healthy within ${timeout}s"
    return 1
}

# Rollback service deployment
rollback_service() {
    local service_name="$1"
    
    warn "Rolling back service: $service_name"
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would rollback $service_name"
        return 0
    fi
    
    kubectl rollout undo deployment/"$service_name" -n "$NAMESPACE"
    kubectl rollout status deployment/"$service_name" -n "$NAMESPACE" --timeout=300s
    
    warn "Service $service_name rolled back to previous version"
}

# Deploy all services
deploy_services() {
    log "Deploying microservices..."
    
    # Get list of services to deploy
    local services_to_deploy
    if [ -n "$DEPLOY_SERVICES" ]; then
        IFS=',' read -ra services_to_deploy <<< "$DEPLOY_SERVICES"
    else
        services_to_deploy=("${!SERVICE_CONFIGS[@]}")
    fi
    
    # Sort services by priority for deployment order
    local critical_services=()
    local high_priority_services=()
    local normal_services=()
    
    for service in "${services_to_deploy[@]}"; do
        local config="${SERVICE_CONFIGS[$service]}"
        local priority=$(echo "$config" | cut -d':' -f1)
        
        case "$priority" in
            critical)
                critical_services+=("$service")
                ;;
            high)
                high_priority_services+=("$service")
                ;;
            normal)
                normal_services+=("$service")
                ;;
        esac
    done
    
    # Deploy services in priority order
    local all_services=("${critical_services[@]}" "${high_priority_services[@]}" "${normal_services[@]}")
    local failed_services=()
    
    for service in "${all_services[@]}"; do
        if deploy_service "$service"; then
            if wait_for_service_health "$service"; then
                info "✅ Service $service deployed and healthy"
            else
                error "❌ Service $service deployment failed health check"
                failed_services+=("$service")
                
                if [ "$ROLLBACK_ON_FAILURE" = true ]; then
                    rollback_service "$service"
                fi
            fi
        else
            error "❌ Service $service deployment failed"
            failed_services+=("$service")
        fi
    done
    
    # Report results
    if [ ${#failed_services[@]} -eq 0 ]; then
        log "✅ All services deployed successfully"
    else
        error "❌ Failed to deploy services: ${failed_services[*]}"
        return 1
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying production deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check namespace
    info "Checking namespace: $NAMESPACE"
    kubectl get namespace "$NAMESPACE"
    
    # Check all deployments
    info "Checking deployments:"
    kubectl get deployments -n "$NAMESPACE"
    
    # Check all services
    info "Checking services:"
    kubectl get services -n "$NAMESPACE"
    
    # Check HPA status
    info "Checking Horizontal Pod Autoscalers:"
    kubectl get hpa -n "$NAMESPACE"
    
    # Check pod status
    info "Checking pod status:"
    kubectl get pods -n "$NAMESPACE"
    
    # Check service health
    info "Checking service health:"
    local services
    if [ -n "$DEPLOY_SERVICES" ]; then
        IFS=',' read -ra services <<< "$DEPLOY_SERVICES"
    else
        services=("${!SERVICE_CONFIGS[@]}")
    fi
    
    for service in "${services[@]}"; do
        local replicas=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local desired=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        
        if [ "$replicas" = "$desired" ] && [ "$replicas" != "0" ]; then
            info "✅ $service: $replicas/$desired replicas ready"
        else
            warn "❌ $service: $replicas/$desired replicas ready"
        fi
    done
    
    # Resource usage
    info "Resource usage summary:"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || info "Metrics not yet available"
    
    log "Deployment verification completed"
}

# Show access information
show_access_info() {
    log "Production services access information:"
    
    info ""
    info "Deployed Services:"
    kubectl get services -n "$NAMESPACE" --no-headers | while read -r name type cluster_ip external_ip port_age; do
        info "  $name: http://$cluster_ip:${port_age%/*}"
    done
    
    info ""
    info "Useful Commands:"
    info "  kubectl get pods -n $NAMESPACE                    # List service pods"
    info "  kubectl logs -f deployment/api-gateway -n $NAMESPACE  # View API Gateway logs"
    info "  kubectl describe pod <pod-name> -n $NAMESPACE     # Debug pod issues"
    info "  kubectl port-forward svc/api-gateway 8080:8000 -n $NAMESPACE  # Access API Gateway locally"
    
    info ""
    info "Scaling Commands:"
    info "  kubectl scale deployment api-gateway --replicas=5 -n $NAMESPACE  # Manual scaling"
    info "  kubectl get hpa -n $NAMESPACE                     # View autoscaler status"
    
    info ""
    info "Monitoring:"
    if kubectl get namespace monitoring &> /dev/null; then
        info "  kubectl port-forward svc/grafana 3000:80 -n monitoring  # Access Grafana"
        info "  kubectl port-forward svc/prometheus 9090:9090 -n monitoring  # Access Prometheus"
    else
        info "  Monitoring stack not installed"
    fi
    
    info ""
    info "Health Checks:"
    local services
    if [ -n "$DEPLOY_SERVICES" ]; then
        IFS=',' read -ra services <<< "$DEPLOY_SERVICES"
    else
        services=("${!SERVICE_CONFIGS[@]}")
    fi
    
    for service in "${services[@]}"; do
        info "  curl http://<service-ip>:8000/health           # $service health"
    done
    
    info ""
    info "Next Steps:"
    info "1. Configure external access (LoadBalancer/Ingress)"
    info "2. Set up custom domain and TLS certificates"
    info "3. Configure monitoring alerts and dashboards"
    info "4. Set up backup and disaster recovery procedures"
    info "5. Run production readiness and security checks"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        error "Production services deployment failed with exit code $exit_code"
        
        if [ "$DRY_RUN" = false ]; then
            warn "Check the following for troubleshooting:"
            warn "kubectl get pods -n $NAMESPACE"
            warn "kubectl describe deployment <service-name> -n $NAMESPACE"
            warn "kubectl logs -l app=<service-name> -n $NAMESPACE"
        fi
    fi
}

# Main function
main() {
    log "Starting Production Services Deployment for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    
    # Confirm deployment for production
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy services to PRODUCTION environment:"
        warn "  Namespace: $NAMESPACE"
        warn "  Image tag: $IMAGE_TAG"
        if [ -n "$DEPLOY_SERVICES" ]; then
            warn "  Services: $DEPLOY_SERVICES"
        else
            warn "  Services: ALL (${!SERVICE_CONFIGS[*]})"
        fi
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    build_images
    create_namespace
    deploy_configuration
    deploy_services
    verify_deployment
    show_access_info
    
    log "✅ Production services deployment completed successfully!"
    log ""
    info "🎉 Your Publish AI services are now running in production!"
    info "📊 Monitor service health and performance regularly"
    info "🔄 Use rolling updates for future deployments"
    info "💰 Monitor resource usage and costs"
}

# Set up signal handlers
trap cleanup EXIT

# Run main function
main "$@"