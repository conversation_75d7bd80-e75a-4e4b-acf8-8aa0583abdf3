# Production Kubernetes Configurations for Publish AI Services
# Optimized for production workloads with auto-scaling, security, and reliability

---
# Namespace for production deployment
apiVersion: v1
kind: Namespace
metadata:
  name: publish-ai-production
  labels:
    name: publish-ai-production
    environment: production
    project: publish-ai

---
# Priority Classes for workload prioritization
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: critical-priority
value: 1000
globalDefault: false
description: "Critical priority class for essential services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
value: 800
globalDefault: false
description: "High priority class for important services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: normal-priority
value: 500
globalDefault: true
description: "Normal priority class for standard services"

---
# Pod Disruption Budgets for high availability
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: publish-ai-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: api-gateway

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: content-generation-pdb
  namespace: publish-ai-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: content-generation

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: market-intelligence-pdb
  namespace: publish-ai-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: market-intelligence

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: publishing-service-pdb
  namespace: publish-ai-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: publishing-service

---
# Network Policies for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-network-policy
  namespace: publish-ai-production
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 8000
  - from: []  # Allow from anywhere for external traffic
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          tier: microservice
    ports:
    - protocol: TCP
      port: 8000
  - to: []  # Allow outbound traffic
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: microservices-network-policy
  namespace: publish-ai-production
spec:
  podSelector:
    matchLabels:
      tier: microservice
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - podSelector:
        matchLabels:
          tier: microservice
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          tier: microservice
    ports:
    - protocol: TCP
      port: 8000
  - to: []  # Allow outbound for external APIs
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# ConfigMap for production configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: production-config
  namespace: publish-ai-production
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "info"
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
  HEALTH_CHECK_INTERVAL: "30"
  GRACEFUL_SHUTDOWN_TIMEOUT: "30"
  MAX_REQUEST_SIZE: "10485760"  # 10MB
  RATE_LIMIT_ENABLED: "true"
  CORS_ENABLED: "true"
  COMPRESSION_ENABLED: "true"
  
  # AI Service Configuration
  AI_MODEL_CACHE_SIZE: "1024"
  AI_GENERATION_TIMEOUT: "300"
  AI_QUALITY_THRESHOLD: "4.0"
  
  # Database Configuration
  DB_POOL_SIZE: "20"
  DB_MAX_OVERFLOW: "0"
  DB_POOL_TIMEOUT: "30"
  DB_POOL_RECYCLE: "3600"
  
  # Cache Configuration
  CACHE_TTL: "3600"
  CACHE_MAX_SIZE: "1000"
  
  # External API Configuration
  API_TIMEOUT: "30"
  API_RETRY_COUNT: "3"
  API_RETRY_DELAY: "1"

---
# Secret for production credentials (base64 encoded placeholders)
apiVersion: v1
kind: Secret
metadata:
  name: production-secrets
  namespace: publish-ai-production
type: Opaque
data:
  # Database credentials (base64 encoded)
  SUPABASE_URL: <base64-encoded-supabase-url>
  SUPABASE_SERVICE_KEY: <base64-encoded-supabase-service-key>
  DATABASE_URL: <base64-encoded-database-url>
  
  # AI API Keys (base64 encoded)
  OPENAI_API_KEY: <base64-encoded-openai-key>
  ANTHROPIC_API_KEY: <base64-encoded-anthropic-key>
  
  # External Service Keys (base64 encoded)
  GOOGLE_TRENDS_API_KEY: <base64-encoded-google-trends-key>
  AMAZON_KDP_API_KEY: <base64-encoded-amazon-kdp-key>
  
  # Security Keys (base64 encoded)
  JWT_SECRET: <base64-encoded-jwt-secret>
  ENCRYPTION_KEY: <base64-encoded-encryption-key>
  
  # Redis Configuration (base64 encoded)
  REDIS_URL: <base64-encoded-redis-url>
  REDIS_PASSWORD: <base64-encoded-redis-password>

---
# API Gateway Production Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: publish-ai-production
  labels:
    app: api-gateway
    tier: gateway
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        tier: gateway
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      priorityClassName: critical-priority
      serviceAccountName: api-gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node.kubernetes.io/workload
                operator: In
                values: ["api-gateway", "general-services"]
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["api-gateway"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: api-gateway
        image: publish-ai/api-gateway:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8080
          name: metrics
        env:
        - name: PORT
          value: "8000"
        - name: METRICS_PORT
          value: "8080"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
            ephemeral-storage: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
            ephemeral-storage: 5Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL

---
# API Gateway Service
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: publish-ai-production
  labels:
    app: api-gateway
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: api-gateway

---
# API Gateway HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: publish-ai-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# Content Generation Service Production Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: content-generation
  namespace: publish-ai-production
  labels:
    app: content-generation
    tier: microservice
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 2
  selector:
    matchLabels:
      app: content-generation
  template:
    metadata:
      labels:
        app: content-generation
        tier: microservice
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      priorityClassName: high-priority
      serviceAccountName: content-generation
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/workload
                operator: In
                values: ["ai-services"]
          - weight: 50
            preference:
              matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: In
                values: ["high-compute"]
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["content-generation"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: content-generation
        image: publish-ai/content-generation:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        - name: AI_MODEL_CACHE_PATH
          value: "/tmp/models"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 2Gi
          limits:
            cpu: 4000m
            memory: 8Gi
            ephemeral-storage: 10Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: model-cache
          mountPath: /app/models
      volumes:
      - name: tmp
        emptyDir: {}
      - name: model-cache
        emptyDir:
          sizeLimit: 5Gi

---
# Content Generation Service
apiVersion: v1
kind: Service
metadata:
  name: content-generation
  namespace: publish-ai-production
  labels:
    app: content-generation
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: content-generation

---
# Content Generation HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: content-generation-hpa
  namespace: publish-ai-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: content-generation
  minReplicas: 2
  maxReplicas: 15
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # Slower scale down for AI services
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60

---
# Market Intelligence Service Production Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: market-intelligence
  namespace: publish-ai-production
  labels:
    app: market-intelligence
    tier: microservice
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: market-intelligence
  template:
    metadata:
      labels:
        app: market-intelligence
        tier: microservice
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      priorityClassName: high-priority
      serviceAccountName: market-intelligence
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/workload
                operator: In
                values: ["general-services", "ai-services"]
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["market-intelligence"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: market-intelligence
        image: publish-ai/market-intelligence:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
            ephemeral-storage: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
            ephemeral-storage: 5Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}

---
# Market Intelligence Service
apiVersion: v1
kind: Service
metadata:
  name: market-intelligence
  namespace: publish-ai-production
  labels:
    app: market-intelligence
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: market-intelligence

---
# Market Intelligence HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: market-intelligence-hpa
  namespace: publish-ai-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: market-intelligence
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Percent
        value: 75
        periodSeconds: 60

---
# Publishing Service Production Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: publishing-service
  namespace: publish-ai-production
  labels:
    app: publishing-service
    tier: microservice
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0  # Never go below minimum for critical service
      maxSurge: 1
  selector:
    matchLabels:
      app: publishing-service
  template:
    metadata:
      labels:
        app: publishing-service
        tier: microservice
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      priorityClassName: critical-priority  # Critical for business
      serviceAccountName: publishing-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/workload
                operator: In
                values: ["general-services"]
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values: ["publishing-service"]
            topologyKey: kubernetes.io/hostname
      containers:
      - name: publishing-service
        image: publish-ai/publishing-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: 300m
            memory: 512Mi
            ephemeral-storage: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 3Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 20
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}

---
# Publishing Service
apiVersion: v1
kind: Service
metadata:
  name: publishing-service
  namespace: publish-ai-production
  labels:
    app: publishing-service
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: publishing-service

---
# Publishing Service HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: publishing-service-hpa
  namespace: publish-ai-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: publishing-service
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 65
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # Conservative scaling for critical service
      policies:
      - type: Pods
        value: 1
        periodSeconds: 120
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# ServiceMonitor for Prometheus monitoring
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: publish-ai-services
  namespace: publish-ai-production
  labels:
    app: publish-ai
spec:
  selector:
    matchLabels:
      tier: gateway
  endpoints:
  - port: http
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: publish-ai-microservices
  namespace: publish-ai-production
  labels:
    app: publish-ai
spec:
  selector:
    matchLabels:
      tier: microservice
  endpoints:
  - port: http
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s