# Cost Monitoring and Optimization for Publish AI Production
# Comprehensive cost tracking, alerting, and optimization recommendations

apiVersion: v1
kind: Namespace
metadata:
  name: cost-monitoring
  labels:
    name: cost-monitoring
    purpose: cost-optimization

---
# Cost monitoring deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cost-monitor
  namespace: cost-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cost-monitor
  template:
    metadata:
      labels:
        app: cost-monitor
    spec:
      serviceAccountName: cost-monitor
      containers:
      - name: cost-monitor
        image: kubecost/cost-model:v1.106.0
        ports:
        - containerPort: 9003
          name: api
        - containerPort: 9090
          name: metrics
        env:
        - name: PROMETHEUS_SERVER_ENDPOINT
          value: "http://prometheus-server.monitoring.svc.cluster.local"
        - name: CLOUD_PROVIDER_API_KEY
          valueFrom:
            secretKeyRef:
              name: cloud-provider-credentials
              key: api-key
        - name: CONFIG_PATH
          value: "/var/configs/"
        - name: DB_BASIC_AUTH_USERNAME
          value: "admin"
        - name: DB_BASIC_AUTH_PW
          valueFrom:
            secretKeyRef:
              name: cost-monitor-credentials
              key: password
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 800m
            memory: 1Gi
        volumeMounts:
        - name: cost-configs
          mountPath: /var/configs
        - name: persistent-storage
          mountPath: /var/storage
      volumes:
      - name: cost-configs
        configMap:
          name: cost-monitor-config
      - name: persistent-storage
        persistentVolumeClaim:
          claimName: cost-monitor-storage

---
# Service for cost monitor
apiVersion: v1
kind: Service
metadata:
  name: cost-monitor
  namespace: cost-monitoring
spec:
  selector:
    app: cost-monitor
  ports:
  - name: api
    port: 9003
    targetPort: api
  - name: metrics
    port: 9090
    targetPort: metrics

---
# PVC for cost monitor storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: cost-monitor-storage
  namespace: cost-monitoring
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
# Service Account for cost monitor
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cost-monitor
  namespace: cost-monitoring

---
# ClusterRole for cost monitor
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cost-monitor
rules:
- apiGroups: [""]
  resources: ["nodes", "pods", "services", "persistentvolumes", "persistentvolumeclaims", "namespaces"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
# ClusterRoleBinding for cost monitor
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cost-monitor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cost-monitor
subjects:
- kind: ServiceAccount
  name: cost-monitor
  namespace: cost-monitoring

---
# Cost monitor configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cost-monitor-config
  namespace: cost-monitoring
data:
  cost-model.yaml: |
    # Cost model configuration
    cloud_provider: "aws"  # Change to gcp or azure as needed
    
    # Node pricing (example for AWS - update with actual pricing)
    node_pricing:
      t3.medium:
        cpu_cost: 0.0208  # per CPU hour
        memory_cost: 0.0052  # per GB hour
      t3.large:
        cpu_cost: 0.0416
        memory_cost: 0.0104
      c5.2xlarge:
        cpu_cost: 0.170
        memory_cost: 0.0212
      c5.4xlarge:
        cpu_cost: 0.340
        memory_cost: 0.0425
    
    # Storage pricing
    storage_pricing:
      gp3:
        gb_month: 0.08
        iops_month: 0.005
      gp2:
        gb_month: 0.10
      io1:
        gb_month: 0.125
        iops_month: 0.065
    
    # Network pricing
    network_pricing:
      data_transfer_out_gb: 0.09
      load_balancer_hour: 0.0225
    
    # Cost allocation settings
    allocation:
      enable_namespace_allocation: true
      enable_service_allocation: true
      enable_label_allocation: true
      
    # Alert thresholds
    alerts:
      daily_budget: 500  # USD
      monthly_budget: 15000  # USD
      efficiency_threshold: 0.6  # Resource utilization threshold
      
  prometheus-rules.yaml: |
    groups:
    - name: cost-alerts
      rules:
      - alert: HighDailyCost
        expr: kubecost_cluster_cost_daily > 500
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Daily cluster cost is high"
          description: "Daily cluster cost is {{ $value }} USD, exceeding the threshold of 500 USD"
      
      - alert: MonthlyBudgetExceeded
        expr: kubecost_cluster_cost_monthly > 15000
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: "Monthly budget exceeded"
          description: "Monthly cluster cost is {{ $value }} USD, exceeding the budget of 15000 USD"
      
      - alert: LowResourceEfficiency
        expr: kubecost_cluster_efficiency < 0.6
        for: 2h
        labels:
          severity: warning
        annotations:
          summary: "Low resource efficiency"
          description: "Cluster resource efficiency is {{ $value }}, below the threshold of 60%"
      
      - alert: UnusedResources
        expr: kubecost_idle_cost_percentage > 0.3
        for: 4h
        labels:
          severity: warning
        annotations:
          summary: "High percentage of idle resources"
          description: "{{ $value }}% of resources are idle and could be optimized"

---
# Cost optimization recommendations CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cost-optimization-analyzer
  namespace: cost-monitoring
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM UTC
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: cost-analyzer
          containers:
          - name: cost-analyzer
            image: python:3.11-alpine
            command:
            - /bin/sh
            - -c
            - |
              set -e
              
              # Install required packages
              pip install kubernetes prometheus-api-client pandas requests
              
              cat << 'EOF' > /tmp/cost_analyzer.py
              import json
              import requests
              import pandas as pd
              from datetime import datetime, timedelta
              from kubernetes import client, config
              
              # Configure Kubernetes client
              config.load_incluster_config()
              v1 = client.CoreV1Api()
              apps_v1 = client.AppsV1Api()
              
              # Cost analysis functions
              def get_node_costs():
                  """Get node cost data from cost monitor API"""
                  try:
                      response = requests.get('http://cost-monitor:9003/model/allocation', 
                                            params={'window': '1d'})
                      return response.json() if response.status_code == 200 else {}
                  except Exception as e:
                      print(f"Error fetching cost data: {e}")
                      return {}
              
              def analyze_resource_utilization():
                  """Analyze resource utilization and identify optimization opportunities"""
                  nodes = v1.list_node()
                  recommendations = []
                  
                  for node in nodes.items:
                      node_name = node.metadata.name
                      
                      # Get node capacity
                      capacity = node.status.capacity
                      cpu_capacity = float(capacity.get('cpu', '0'))
                      memory_capacity = float(capacity.get('memory', '0Ki').replace('Ki', '')) / 1024 / 1024  # Convert to GB
                      
                      # Get pods on this node
                      field_selector = f'spec.nodeName={node_name}'
                      pods = v1.list_pod_for_all_namespaces(field_selector=field_selector)
                      
                      total_cpu_requests = 0
                      total_memory_requests = 0
                      
                      for pod in pods.items:
                          if pod.spec.containers:
                              for container in pod.spec.containers:
                                  if container.resources and container.resources.requests:
                                      cpu_req = container.resources.requests.get('cpu', '0')
                                      memory_req = container.resources.requests.get('memory', '0Ki')
                                      
                                      # Parse CPU (millicores to cores)
                                      if cpu_req.endswith('m'):
                                          total_cpu_requests += float(cpu_req[:-1]) / 1000
                                      else:
                                          total_cpu_requests += float(cpu_req) if cpu_req else 0
                                      
                                      # Parse memory (bytes to GB)
                                      if memory_req.endswith('Ki'):
                                          total_memory_requests += float(memory_req[:-2]) / 1024 / 1024
                                      elif memory_req.endswith('Mi'):
                                          total_memory_requests += float(memory_req[:-2]) / 1024
                                      elif memory_req.endswith('Gi'):
                                          total_memory_requests += float(memory_req[:-2])
                      
                      # Calculate utilization
                      cpu_utilization = (total_cpu_requests / cpu_capacity) * 100 if cpu_capacity > 0 else 0
                      memory_utilization = (total_memory_requests / memory_capacity) * 100 if memory_capacity > 0 else 0
                      
                      # Generate recommendations
                      if cpu_utilization < 30 and memory_utilization < 30:
                          recommendations.append({
                              'type': 'node_underutilized',
                              'resource': node_name,
                              'cpu_utilization': round(cpu_utilization, 2),
                              'memory_utilization': round(memory_utilization, 2),
                              'recommendation': 'Consider downsizing or consolidating workloads',
                              'potential_savings': 'High'
                          })
                      elif cpu_utilization > 80 or memory_utilization > 80:
                          recommendations.append({
                              'type': 'node_overutilized',
                              'resource': node_name,
                              'cpu_utilization': round(cpu_utilization, 2),
                              'memory_utilization': round(memory_utilization, 2),
                              'recommendation': 'Consider scaling up or adding nodes',
                              'potential_savings': 'Prevent performance issues'
                          })
                  
                  return recommendations
              
              def analyze_deployments():
                  """Analyze deployments for optimization opportunities"""
                  recommendations = []
                  
                  # Get all deployments
                  deployments = apps_v1.list_deployment_for_all_namespaces()
                  
                  for deployment in deployments.items:
                      name = deployment.metadata.name
                      namespace = deployment.metadata.namespace
                      
                      if deployment.spec.replicas and deployment.spec.template.spec.containers:
                          container = deployment.spec.template.spec.containers[0]
                          
                          # Check if HPA is configured
                          try:
                              autoscaling_v2 = client.AutoscalingV2Api()
                              hpas = autoscaling_v2.list_namespaced_horizontal_pod_autoscaler(namespace)
                              has_hpa = any(hpa.spec.scale_target_ref.name == name for hpa in hpas.items)
                          except:
                              has_hpa = False
                          
                          # Check resource requests/limits
                          has_requests = bool(container.resources and container.resources.requests)
                          has_limits = bool(container.resources and container.resources.limits)
                          
                          if not has_hpa and deployment.spec.replicas > 1:
                              recommendations.append({
                                  'type': 'missing_hpa',
                                  'resource': f'{namespace}/{name}',
                                  'recommendation': 'Configure HPA for automatic scaling',
                                  'potential_savings': 'Medium'
                              })
                          
                          if not has_requests:
                              recommendations.append({
                                  'type': 'missing_requests',
                                  'resource': f'{namespace}/{name}',
                                  'recommendation': 'Set resource requests for better scheduling',
                                  'potential_savings': 'Low'
                              })
                          
                          if not has_limits:
                              recommendations.append({
                                  'type': 'missing_limits',
                                  'resource': f'{namespace}/{name}',
                                  'recommendation': 'Set resource limits to prevent resource contention',
                                  'potential_savings': 'Low'
                              })
                  
                  return recommendations
              
              def generate_cost_report():
                  """Generate comprehensive cost optimization report"""
                  report = {
                      'timestamp': datetime.utcnow().isoformat(),
                      'analysis_period': '24h',
                      'recommendations': {
                          'node_optimization': analyze_resource_utilization(),
                          'deployment_optimization': analyze_deployments()
                      },
                      'summary': {
                          'total_recommendations': 0,
                          'high_priority': 0,
                          'medium_priority': 0,
                          'low_priority': 0
                      }
                  }
                  
                  # Count recommendations by priority
                  all_recommendations = (report['recommendations']['node_optimization'] + 
                                       report['recommendations']['deployment_optimization'])
                  
                  report['summary']['total_recommendations'] = len(all_recommendations)
                  
                  for rec in all_recommendations:
                      priority = rec.get('potential_savings', 'Low')
                      if priority == 'High':
                          report['summary']['high_priority'] += 1
                      elif priority == 'Medium':
                          report['summary']['medium_priority'] += 1
                      else:
                          report['summary']['low_priority'] += 1
                  
                  return report
              
              # Main execution
              if __name__ == "__main__":
                  print("Starting cost optimization analysis...")
                  
                  report = generate_cost_report()
                  
                  # Save report
                  with open('/tmp/cost_optimization_report.json', 'w') as f:
                      json.dump(report, f, indent=2)
                  
                  print(f"Analysis complete. Found {report['summary']['total_recommendations']} recommendations.")
                  print(f"High priority: {report['summary']['high_priority']}")
                  print(f"Medium priority: {report['summary']['medium_priority']}")
                  print(f"Low priority: {report['summary']['low_priority']}")
                  
                  # Send to monitoring system if configured
                  webhook_url = os.environ.get('COST_WEBHOOK_URL')
                  if webhook_url:
                      try:
                          response = requests.post(webhook_url, json=report)
                          print(f"Report sent to monitoring system: {response.status_code}")
                      except Exception as e:
                          print(f"Failed to send report: {e}")
              EOF
              
              python /tmp/cost_analyzer.py
              
            env:
            - name: COST_WEBHOOK_URL
              valueFrom:
                secretKeyRef:
                  name: monitoring-webhooks
                  key: cost-optimization-url
                  optional: true
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
          restartPolicy: OnFailure

---
# Service Account for cost analyzer
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cost-analyzer
  namespace: cost-monitoring

---
# ClusterRole for cost analyzer
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cost-analyzer
rules:
- apiGroups: [""]
  resources: ["nodes", "pods", "persistentvolumes", "persistentvolumeclaims"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
# ClusterRoleBinding for cost analyzer
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cost-analyzer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cost-analyzer
subjects:
- kind: ServiceAccount
  name: cost-analyzer
  namespace: cost-monitoring

---
# Resource usage tracking
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: resource-usage-tracker
  namespace: cost-monitoring
spec:
  selector:
    matchLabels:
      app: resource-usage-tracker
  template:
    metadata:
      labels:
        app: resource-usage-tracker
    spec:
      serviceAccountName: resource-tracker
      hostNetwork: true
      hostPID: true
      containers:
      - name: usage-tracker
        image: prom/node-exporter:v1.6.1
        args:
        - --path.rootfs=/host
        - --collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)
        - --collector.textfile.directory=/var/lib/node_exporter/textfile_collector
        ports:
        - containerPort: 9100
          name: metrics
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: root
          mountPath: /host
          readOnly: true
          mountPropagation: HostToContainer
        - name: textfile-collector
          mountPath: /var/lib/node_exporter/textfile_collector
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: root
        hostPath:
          path: /
      - name: textfile-collector
        emptyDir: {}
      tolerations:
      - effect: NoSchedule
        operator: Exists

---
# Service for resource usage tracker
apiVersion: v1
kind: Service
metadata:
  name: resource-usage-tracker
  namespace: cost-monitoring
  labels:
    app: resource-usage-tracker
spec:
  ports:
  - name: metrics
    port: 9100
    targetPort: 9100
  selector:
    app: resource-usage-tracker

---
# Service Account for resource tracker
apiVersion: v1
kind: ServiceAccount
metadata:
  name: resource-tracker
  namespace: cost-monitoring

---
# Cost dashboard configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cost-dashboard-config
  namespace: cost-monitoring
data:
  dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Publish AI Cost Optimization Dashboard",
        "tags": ["cost", "optimization"],
        "timezone": "UTC",
        "panels": [
          {
            "id": 1,
            "title": "Daily Cost Trend",
            "type": "graph",
            "targets": [
              {
                "expr": "kubecost_cluster_cost_daily",
                "legendFormat": "Daily Cost ($)"
              }
            ],
            "yAxes": [
              {
                "label": "Cost (USD)",
                "min": 0
              }
            ]
          },
          {
            "id": 2,
            "title": "Cost by Namespace",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum by (namespace) (kubecost_namespace_cost_daily)",
                "legendFormat": "{{ namespace }}"
              }
            ]
          },
          {
            "id": 3,
            "title": "Resource Efficiency",
            "type": "singlestat",
            "targets": [
              {
                "expr": "kubecost_cluster_efficiency",
                "legendFormat": "Efficiency"
              }
            ],
            "thresholds": "0.4,0.6",
            "colorBackground": true
          },
          {
            "id": 4,
            "title": "Top 10 Most Expensive Services",
            "type": "table",
            "targets": [
              {
                "expr": "topk(10, sum by (service) (kubecost_service_cost_daily))",
                "legendFormat": "{{ service }}"
              }
            ]
          },
          {
            "id": 5,
            "title": "Idle Resource Cost",
            "type": "graph",
            "targets": [
              {
                "expr": "kubecost_idle_cost_daily",
                "legendFormat": "Idle Cost ($)"
              }
            ]
          },
          {
            "id": 6,
            "title": "Node Cost Breakdown",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (node) (kubecost_node_cost_hourly)",
                "legendFormat": "{{ node }}"
              }
            ]
          }
        ],
        "time": {
          "from": "now-7d",
          "to": "now"
        },
        "refresh": "5m"
      }
    }

---
# Spot instance optimization CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: spot-instance-optimizer
  namespace: cost-monitoring
spec:
  schedule: "0 */4 * * *"  # Every 4 hours
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: spot-optimizer
          containers:
          - name: spot-optimizer
            image: alpine:3.18
            command:
            - /bin/sh
            - -c
            - |
              set -e
              
              # Install kubectl
              apk add --no-cache curl
              curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
              chmod +x kubectl && mv kubectl /usr/local/bin/
              
              echo "Analyzing spot instance opportunities..."
              
              # Get current node information
              kubectl get nodes -o json | jq -r '
                .items[] | 
                select(.metadata.labels["node.kubernetes.io/instance-type"] != null) |
                [
                  .metadata.name,
                  .metadata.labels["node.kubernetes.io/instance-type"],
                  (.metadata.labels["eks.amazonaws.com/capacityType"] // "ON_DEMAND"),
                  (.status.capacity.cpu // "0"),
                  (.status.capacity.memory // "0")
                ] | @csv
              ' > /tmp/current_nodes.csv
              
              echo "Current node configuration:"
              cat /tmp/current_nodes.csv
              
              # Calculate potential savings
              echo "Analyzing potential spot instance savings..."
              
              # Check if we have spot instances
              spot_nodes=$(kubectl get nodes -l eks.amazonaws.com/capacityType=SPOT --no-headers | wc -l)
              total_nodes=$(kubectl get nodes --no-headers | wc -l)
              
              if [ "$spot_nodes" -eq 0 ]; then
                echo "RECOMMENDATION: No spot instances found. Consider using spot instances for:"
                echo "  - Non-critical workloads"
                echo "  - Development/staging environments"
                echo "  - Batch processing jobs"
                echo "  - AI training workloads"
                echo "  Potential savings: 60-90%"
              else
                spot_percentage=$(echo "scale=1; $spot_nodes * 100 / $total_nodes" | bc)
                echo "Current spot instance usage: $spot_nodes/$total_nodes nodes (${spot_percentage}%)"
                
                if [ "$spot_percentage" -lt 50 ]; then
                  echo "RECOMMENDATION: Increase spot instance usage for cost savings"
                fi
              fi
              
              # Check for oversized instances
              echo "Checking for oversized instances..."
              kubectl top nodes --no-headers 2>/dev/null | while read node cpu_pct cpu_abs mem_pct mem_abs; do
                cpu_usage=$(echo "$cpu_pct" | sed 's/%//')
                mem_usage=$(echo "$mem_pct" | sed 's/%//')
                
                if [ "$cpu_usage" -lt 30 ] && [ "$mem_usage" -lt 30 ]; then
                  echo "RECOMMENDATION: Node $node is underutilized (CPU: ${cpu_usage}%, Memory: ${mem_usage}%)"
                  echo "  Consider downsizing or consolidating workloads"
                fi
              done || echo "Metrics not available for detailed analysis"
              
              echo "Spot instance optimization analysis completed"
              
            resources:
              requests:
                cpu: 100m
                memory: 128Mi
              limits:
                cpu: 200m
                memory: 256Mi
          restartPolicy: OnFailure

---
# Service Account for spot optimizer
apiVersion: v1
kind: ServiceAccount
metadata:
  name: spot-optimizer
  namespace: cost-monitoring

---
# RBAC for spot optimizer
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: spot-optimizer
rules:
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: spot-optimizer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: spot-optimizer
subjects:
- kind: ServiceAccount
  name: spot-optimizer
  namespace: cost-monitoring