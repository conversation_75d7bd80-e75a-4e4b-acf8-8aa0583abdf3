#!/bin/bash

# Deploy AI Model Marketplace - Advanced AI model registry and deployment platform
# Production-ready deployment with model management, security validation, and auto-scaling

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
NAMESPACE="ai-model-marketplace"
SERVICE_NAME="ai-model-marketplace"
DEPLOYMENT_NAME="ai-model-marketplace"

# Default values
ENVIRONMENT="production"
DRY_RUN=false
WAIT_FOR_READY=true
GPU_ENABLED=true
MODEL_SECURITY_ENABLED=true
HUGGINGFACE_INTEGRATION=true
AUTO_SCALING_ENABLED=true
MONITORING_ENABLED=true

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy AI Model Marketplace with comprehensive model management capabilities.

Options:
  -e, --environment ENV        Environment: development, staging, production (default: production)
  -n, --namespace NAME         Kubernetes namespace (default: ai-model-marketplace)
  -d, --dry-run               Preview deployment without making changes
  -w, --no-wait               Don't wait for rollout to complete
  -g, --no-gpu                Disable GPU support
  -s, --no-security           Disable security scanning
  -f, --no-huggingface        Disable HuggingFace integration
  -a, --no-auto-scaling       Disable auto-scaling
  -m, --no-monitoring         Disable monitoring
  -h, --help                  Show this help message

Examples:
  $0                                    # Deploy to production with all features
  $0 -e staging -n ai-staging          # Deploy to staging environment
  $0 -d                                # Dry run to preview changes
  $0 --no-gpu --no-security            # Deploy without GPU and security features

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -w|--no-wait)
                WAIT_FOR_READY=false
                shift
                ;;
            -g|--no-gpu)
                GPU_ENABLED=false
                shift
                ;;
            -s|--no-security)
                MODEL_SECURITY_ENABLED=false
                shift
                ;;
            -f|--no-huggingface)
                HUGGINGFACE_INTEGRATION=false
                shift
                ;;
            -a|--no-auto-scaling)
                AUTO_SCALING_ENABLED=false
                shift
                ;;
            -m|--no-monitoring)
                MONITORING_ENABLED=false
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check for GPU nodes if GPU is enabled
    if [ "$GPU_ENABLED" = true ]; then
        gpu_nodes=$(kubectl get nodes -l nvidia.com/gpu.present=true --no-headers 2>/dev/null | wc -l || echo "0")
        if [ "$gpu_nodes" -eq 0 ]; then
            warn "No GPU nodes found. GPU features may not work properly."
            warn "Consider running with --no-gpu flag."
        else
            info "Found $gpu_nodes GPU-enabled nodes"
        fi
    fi
    
    # Check Prometheus Operator
    if [ "$MONITORING_ENABLED" = true ]; then
        if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
            info "Prometheus Operator detected"
        else
            warn "Prometheus Operator not found. Monitoring may be limited."
        fi
    fi
    
    # Check required secrets
    required_secrets=("SUPABASE_URL" "DATABASE_URL" "SECRET_KEY" "JWT_SECRET_KEY")
    for secret in "${required_secrets[@]}"; do
        if [[ -z "${!secret:-}" ]]; then
            warn "Environment variable $secret is not set"
        fi
    done
    
    # Check HuggingFace token if integration enabled
    if [ "$HUGGINGFACE_INTEGRATION" = true ] && [[ -z "${HUGGINGFACE_TOKEN:-}" ]]; then
        warn "HUGGINGFACE_TOKEN not set. HuggingFace integration will be limited."
    fi
    
    log "Prerequisites check completed"
}

# Setup namespace and RBAC
setup_namespace() {
    log "Setting up namespace and RBAC..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace and RBAC resources"
        return 0
    fi
    
    # Create namespace if it doesn't exist
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" \
            name="$NAMESPACE" \
            project="publish-ai" \
            component="ai-marketplace" \
            environment="$ENVIRONMENT"
    fi
    
    # Apply RBAC and other base resources (included in deployment YAML)
    log "Namespace and RBAC setup completed"
}

# Setup storage
setup_storage() {
    log "Setting up persistent storage for AI models..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create storage resources"
        return 0
    fi
    
    # Storage requirements based on environment
    local model_storage_size="500Gi"
    local cache_storage_size="200Gi"
    local storage_class="fast-ssd"
    
    case "$ENVIRONMENT" in
        "development")
            model_storage_size="50Gi"
            cache_storage_size="20Gi"
            storage_class="standard"
            ;;
        "staging")
            model_storage_size="200Gi"
            cache_storage_size="100Gi"
            storage_class="fast-ssd"
            ;;
        "production")
            model_storage_size="1Ti"
            cache_storage_size="500Gi"
            storage_class="fast-ssd"
            ;;
    esac
    
    info "Creating storage with:"
    info "  Model storage: $model_storage_size"
    info "  Cache storage: $cache_storage_size"
    info "  Storage class: $storage_class"
    
    log "Storage setup completed"
}

# Deploy the main service
deploy_service() {
    log "Deploying AI Model Marketplace..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy AI Model Marketplace"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/ai-marketplace-deployment.yaml"
        return 0
    fi
    
    # Modify deployment based on options
    local deployment_file="/tmp/ai-marketplace-deployment-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/ai-marketplace-deployment.yaml" "$deployment_file"
    
    # Environment-specific modifications
    sed -i.bak "s/namespace: ai-model-marketplace/namespace: $NAMESPACE/g" "$deployment_file"
    
    # Adjust replicas and resources based on environment
    case "$ENVIRONMENT" in
        "development")
            sed -i.bak 's/replicas: 3/replicas: 1/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 3/minReplicas: 1/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 20/maxReplicas: 5/g' "$deployment_file"
            sed -i.bak 's/cpu: 1000m/cpu: 500m/g' "$deployment_file"
            sed -i.bak 's/memory: 4Gi/memory: 2Gi/g' "$deployment_file"
            sed -i.bak 's/storage: 500Gi/storage: 50Gi/g' "$deployment_file"
            sed -i.bak 's/storage: 200Gi/storage: 20Gi/g' "$deployment_file"
            ;;
        "staging")
            sed -i.bak 's/replicas: 3/replicas: 2/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 3/minReplicas: 2/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 20/maxReplicas: 10/g' "$deployment_file"
            sed -i.bak 's/storage: 500Gi/storage: 200Gi/g' "$deployment_file"
            sed -i.bak 's/storage: 200Gi/storage: 100Gi/g' "$deployment_file"
            ;;
        "production")
            # Increase storage for production
            sed -i.bak 's/storage: 500Gi/storage: 1Ti/g' "$deployment_file"
            sed -i.bak 's/storage: 200Gi/storage: 500Gi/g' "$deployment_file"
            ;;
    esac
    
    # Disable GPU if requested
    if [ "$GPU_ENABLED" = false ]; then
        sed -i.bak 's/GPU_ENABLED: "true"/GPU_ENABLED: "false"/g' "$deployment_file"
        sed -i.bak '/nvidia.com\/gpu: 1/d' "$deployment_file"
        sed -i.bak '/nvidia.com\/gpu/d' "$deployment_file"
        sed -i.bak 's/node.kubernetes.io\/instance-type: gpu-optimized/node.kubernetes.io\/instance-type: compute-optimized/g' "$deployment_file"
    fi
    
    # Disable security scanning if requested
    if [ "$MODEL_SECURITY_ENABLED" = false ]; then
        sed -i.bak 's/SECURITY_SCANNING_ENABLED: "true"/SECURITY_SCANNING_ENABLED: "false"/g' "$deployment_file"
        sed -i.bak 's/MALWARE_SCANNING_ENABLED: "true"/MALWARE_SCANNING_ENABLED: "false"/g' "$deployment_file"
        sed -i.bak 's/CODE_ANALYSIS_ENABLED: "true"/CODE_ANALYSIS_ENABLED: "false"/g' "$deployment_file"
    fi
    
    # Disable auto-scaling if requested
    if [ "$AUTO_SCALING_ENABLED" = false ]; then
        sed -i.bak 's/AUTO_SCALING_ENABLED: "true"/AUTO_SCALING_ENABLED: "false"/g' "$deployment_file"
        # Remove HPA section
        sed -i.bak '/kind: HorizontalPodAutoscaler/,/^---$/d' "$deployment_file"
    fi
    
    # Apply the deployment
    kubectl apply -f "$deployment_file"
    
    # Clean up temporary file
    rm -f "$deployment_file" "$deployment_file.bak"
    
    log "AI Model Marketplace deployed successfully"
}

# Setup monitoring
setup_monitoring() {
    if [ "$MONITORING_ENABLED" = false ]; then
        info "Monitoring disabled, skipping setup"
        return 0
    fi
    
    log "Setting up comprehensive monitoring..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup monitoring"
        return 0
    fi
    
    # Check if Prometheus Operator is available
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        # ServiceMonitor and PrometheusRule are included in the deployment YAML
        info "Prometheus monitoring configured"
    else
        warn "Prometheus Operator not found. Manual monitoring setup may be required."
    fi
    
    # Create additional dashboards
    kubectl apply -f - << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-marketplace-dashboards
  namespace: $NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  ai-marketplace-overview.json: |
    {
      "dashboard": {
        "title": "AI Model Marketplace Overview",
        "tags": ["ai", "models", "marketplace"],
        "panels": [
          {
            "title": "Total Models Registered",
            "type": "stat",
            "targets": [
              {
                "expr": "ai_models_total"
              }
            ]
          },
          {
            "title": "Model Deployments",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(model_deployments_total[5m])"
              }
            ]
          },
          {
            "title": "GPU Utilization",
            "type": "gauge",
            "targets": [
              {
                "expr": "ai_model_gpu_utilization"
              }
            ]
          },
          {
            "title": "Storage Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "ai_model_storage_usage_percent"
              }
            ]
          }
        ]
      }
    }
EOF
    
    log "Monitoring setup completed"
}

# Wait for deployment to be ready
wait_for_ready() {
    if [ "$WAIT_FOR_READY" = false ]; then
        info "Skipping readiness check"
        return 0
    fi
    
    log "Waiting for AI Model Marketplace to be ready..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would wait for readiness"
        return 0
    fi
    
    # Wait for deployment to be ready
    if kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=900s; then
        log "✅ AI Model Marketplace is ready"
    else
        error "❌ AI Model Marketplace failed to become ready"
        return 1
    fi
    
    # Check service endpoints
    local retries=30
    local count=0
    
    while [ $count -lt $retries ]; do
        if kubectl get endpoints "$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.subsets[0].addresses[0].ip}' &> /dev/null; then
            log "✅ Service endpoints are ready"
            break
        fi
        
        count=$((count + 1))
        info "Waiting for service endpoints... ($count/$retries)"
        sleep 10
    done
    
    if [ $count -eq $retries ]; then
        error "❌ Service endpoints not ready after 5 minutes"
        return 1
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying AI Model Marketplace deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check deployment status
    info "Deployment status:"
    kubectl get deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE"
    
    # Check pod status
    info "Pod status:"
    kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check service status
    info "Service status:"
    kubectl get service "$SERVICE_NAME" -n "$NAMESPACE"
    
    # Check storage
    info "Storage status:"
    kubectl get pvc -n "$NAMESPACE"
    
    # Check HPA if enabled
    if [ "$AUTO_SCALING_ENABLED" = true ]; then
        info "Auto-scaling status:"
        kubectl get hpa -n "$NAMESPACE"
    fi
    
    # Test health endpoint
    local pod_name=$(kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$pod_name" ]; then
        info "Testing health endpoint..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- curl -sf http://localhost:8000/health &> /dev/null; then
            log "✅ Health endpoint is responding"
        else
            warn "⚠️ Health endpoint is not responding yet"
        fi
        
        # Test API endpoints
        info "Testing API endpoints..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- curl -sf http://localhost:8000/api/models/statistics &> /dev/null; then
            log "✅ API endpoints are responding"
        else
            warn "⚠️ API endpoints are not responding yet"
        fi
    fi
    
    log "Deployment verification completed"
}

# Run tests
run_tests() {
    log "Running deployment tests..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would run tests"
        return 0
    fi
    
    # Basic connectivity tests
    local pod_name=$(kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$pod_name" ]; then
        # Test database connectivity
        info "Testing database connectivity..."
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- python -c "
import asyncio
import asyncpg
import os
async def test():
    conn = await asyncpg.connect(os.environ['DATABASE_URL'])
    await conn.execute('SELECT 1')
    await conn.close()
    print('Database OK')
asyncio.run(test())
" &> /dev/null; then
            log "✅ Database connectivity test passed"
        else
            warn "⚠️ Database connectivity test failed"
        fi
        
        # Test GPU availability if enabled
        if [ "$GPU_ENABLED" = true ]; then
            info "Testing GPU availability..."
            if kubectl exec -n "$NAMESPACE" "$pod_name" -- python -c "
import torch
if torch.cuda.is_available():
    print(f'GPU available: {torch.cuda.get_device_name(0)}')
else:
    print('No GPU available')
" 2>/dev/null | grep -q "GPU available"; then
                log "✅ GPU availability test passed"
            else
                warn "⚠️ GPU not available (this may be expected)"
            fi
        fi
    fi
    
    log "Tests completed"
}

# Main deployment function
main() {
    log "Starting AI Model Marketplace deployment"
    
    parse_args "$@"
    
    # Show deployment summary
    info "Deployment Configuration:"
    info "  Environment: $ENVIRONMENT"
    info "  Namespace: $NAMESPACE"
    info "  GPU Enabled: $GPU_ENABLED"
    info "  Security Scanning: $MODEL_SECURITY_ENABLED"
    info "  HuggingFace Integration: $HUGGINGFACE_INTEGRATION"
    info "  Auto-scaling: $AUTO_SCALING_ENABLED"
    info "  Monitoring: $MONITORING_ENABLED"
    info "  Dry Run: $DRY_RUN"
    
    # Confirm production deployment
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy to PRODUCTION environment!"
        warn "  Namespace: $NAMESPACE"
        warn "  GPU Support: $GPU_ENABLED"
        warn "  Security Scanning: $MODEL_SECURITY_ENABLED"
        warn "  This will create large storage volumes and GPU resources!"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    check_prerequisites
    setup_namespace
    setup_storage
    deploy_service
    setup_monitoring
    wait_for_ready
    verify_deployment
    run_tests
    
    # Final status
    if [ "$DRY_RUN" = false ]; then
        log ""
        log "🎉 AI Model Marketplace deployment completed successfully!"
        log ""
        info "Service Information:"
        info "  Environment: $ENVIRONMENT"
        info "  Namespace: $NAMESPACE"
        info "  Service: $SERVICE_NAME"
        
        if [ "$GPU_ENABLED" = true ]; then
            info "  GPU Support: Enabled - NVIDIA GPU acceleration for model operations"
        fi
        
        if [ "$MODEL_SECURITY_ENABLED" = true ]; then
            info "  Security: Enabled - Malware scanning, code analysis, license validation"
        fi
        
        if [ "$HUGGINGFACE_INTEGRATION" = true ]; then
            info "  HuggingFace: Enabled - Import models directly from HuggingFace Hub"
        fi
        
        if [ "$AUTO_SCALING_ENABLED" = true ]; then
            info "  Auto-scaling: Enabled - Automatic scaling based on model workload"
        fi
        
        log ""
        info "Access the AI Model Marketplace:"
        info "  Main API: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8000:8000"
        info "  Models API: http://localhost:8000/api/models"
        info "  Marketplace API: http://localhost:8000/api/marketplace"
        info "  Deployments API: http://localhost:8000/api/deployments"
        info "  Analytics API: http://localhost:8000/api/analytics"
        info "  Swagger UI: http://localhost:8000/docs"
        info "  Logs: kubectl logs -f -n $NAMESPACE deployment/$DEPLOYMENT_NAME"
        info "  Metrics: kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 8080:8080"
        
        log ""
        info "Key Features Available:"
        info "  ✅ Model Registry - Upload and manage AI models"
        info "  ✅ Model Deployment - Auto-scaling model serving"
        info "  ✅ Security Validation - Comprehensive security scanning"
        info "  ✅ Performance Analytics - Model usage and performance tracking"
        info "  ✅ Marketplace - Discover and share models"
        info "  ✅ Version Management - Model versioning and rollback"
        info "  ✅ Multi-format Support - PyTorch, ONNX, TensorFlow, SafeTensors, HuggingFace"
        
        if [ "$GPU_ENABLED" = true ]; then
            info "  ✅ GPU Acceleration - NVIDIA GPU support for inference"
        fi
        
    else
        log "🔍 Dry run completed - no changes were made"
    fi
}

# Run main function
main "$@"