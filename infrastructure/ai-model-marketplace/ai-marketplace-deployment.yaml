# AI Model Marketplace Kubernetes Deployment
# Production-ready deployment with model registry, deployment, and marketplace features

apiVersion: v1
kind: Namespace
metadata:
  name: ai-model-marketplace
  labels:
    name: ai-model-marketplace
    project: publish-ai
    component: ai-marketplace

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-marketplace-config
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    component: config
data:
  # Service Configuration
  HOST: "0.0.0.0"
  PORT: "8000"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Model Configuration
  MODEL_STORAGE_PATH: "/app/models"
  MAX_MODEL_SIZE_MB: "10240"  # 10GB
  SUPPORTED_MODEL_FORMATS: "pytorch,onnx,tensorflow,safetensors,huggingface"
  
  # Model Validation
  SECURITY_SCANNING_ENABLED: "true"
  MALWARE_SCANNING_ENABLED: "true"
  CODE_ANALYSIS_ENABLED: "true"
  LICENSE_VALIDATION_ENABLED: "true"
  
  # Model Deployment
  AUTO_SCALING_ENABLED: "true"
  MIN_REPLICAS: "1"
  MAX_REPLICAS: "10"
  GPU_ENABLED: "true"
  CPU_LIMIT: "2000m"
  MEMORY_LIMIT: "8Gi"
  GPU_LIMIT: "1"
  
  # Marketplace Features
  MARKETPLACE_ENABLED: "true"
  PUBLIC_MODELS_ENABLED: "true"
  PRIVATE_MODELS_ENABLED: "true"
  MODEL_SHARING_ENABLED: "true"
  
  # Performance Optimization
  MODEL_CACHING_ENABLED: "true"
  MODEL_QUANTIZATION_ENABLED: "true"
  MODEL_OPTIMIZATION_ENABLED: "true"
  BATCH_INFERENCE_ENABLED: "true"
  
  # Rate Limiting
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS_PER_MINUTE: "100"
  
  # Monitoring
  PROMETHEUS_ENABLED: "true"
  
  # Kubernetes Configuration
  KUBERNETES_NAMESPACE: "ai-model-marketplace"
  KUBERNETES_SERVICE_ACCOUNT: "ai-marketplace-service"
  
  # Container Registry
  CONTAINER_REGISTRY_URL: "ghcr.io"
  
  # Feature Flags
  EXPERIMENTAL_FEATURES_ENABLED: "false"
  BETA_FEATURES_ENABLED: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: ai-marketplace-secrets
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    component: secrets
type: Opaque
stringData:
  # Database Configuration
  SUPABASE_URL: "${SUPABASE_URL}"
  SUPABASE_ANON_KEY: "${SUPABASE_ANON_KEY}"
  SUPABASE_SERVICE_KEY: "${SUPABASE_SERVICE_KEY}"
  DATABASE_URL: "${DATABASE_URL}"
  
  # Redis Configuration
  REDIS_URL: "${REDIS_URL}"
  REDIS_PASSWORD: "${REDIS_PASSWORD}"
  
  # AI Model Registry
  HUGGINGFACE_TOKEN: "${HUGGINGFACE_TOKEN}"
  OPENAI_API_KEY: "${OPENAI_API_KEY}"
  ANTHROPIC_API_KEY: "${ANTHROPIC_API_KEY}"
  
  # Security
  SECRET_KEY: "${SECRET_KEY}"
  JWT_SECRET_KEY: "${JWT_SECRET_KEY}"
  
  # External Services
  S3_BUCKET_NAME: "${S3_BUCKET_NAME}"
  S3_ACCESS_KEY: "${S3_ACCESS_KEY}"
  S3_SECRET_KEY: "${S3_SECRET_KEY}"
  
  # Container Registry
  CONTAINER_REGISTRY_USERNAME: "${CONTAINER_REGISTRY_USERNAME}"
  CONTAINER_REGISTRY_PASSWORD: "${CONTAINER_REGISTRY_PASSWORD}"
  
  # Email Configuration
  SMTP_USERNAME: "${SMTP_USERNAME}"
  SMTP_PASSWORD: "${SMTP_PASSWORD}"
  
  # Monitoring
  SENTRY_DSN: "${SENTRY_DSN}"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-models-pvc
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi  # Large storage for AI models

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-model-cache-pvc
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    component: cache-storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 200Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-model-marketplace
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    version: v1
    component: ai-marketplace
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: "ai-marketplace"
    app.kubernetes.io/part-of: "publish-ai"
    app.kubernetes.io/managed-by: "kubernetes"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: ai-model-marketplace
  template:
    metadata:
      labels:
        app: ai-model-marketplace
        tier: application
        version: v1
        component: ai-marketplace
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        kubectl.kubernetes.io/restartedAt: "${RESTART_TIMESTAMP}"
    spec:
      serviceAccountName: ai-marketplace-service
      priorityClassName: normal-priority
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        supplementalGroups: [1000]
      
      # Node selection for AI workloads with GPU support
      nodeSelector:
        node.kubernetes.io/workload: ai-compute
        node.kubernetes.io/instance-type: gpu-optimized
      
      # Spread across different nodes for high availability
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: [ai-model-marketplace]
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: In
                values: ["gpu-optimized", "compute-optimized"]
      
      # Toleration for AI workloads
      tolerations:
      - key: "workload"
        operator: "Equal"
        value: "ai-compute"
        effect: "NoSchedule"
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"
      
      initContainers:
      # Database migration and model storage setup
      - name: db-migration
        image: python:3.11-slim
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "Setting up AI Model Marketplace..."
            pip install -q asyncpg sqlalchemy alembic
            
            # Check database connectivity
            python -c "
            import asyncio
            import asyncpg
            import os
            
            async def check_db():
              try:
                conn = await asyncpg.connect(os.environ['DATABASE_URL'])
                await conn.execute('SELECT 1')
                await conn.close()
                print('Database connection successful')
              except Exception as e:
                print(f'Database connection failed: {e}')
                exit(1)
            
            asyncio.run(check_db())
            "
            
            # Create model storage directories
            mkdir -p /app/models/{registry,deployments,cache,temp}
            chown -R 1000:1000 /app/models
            
            echo "AI Model Marketplace setup completed"
        envFrom:
        - secretRef:
            name: ai-marketplace-secrets
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      
      containers:
      - name: ai-model-marketplace
        image: ghcr.io/publishai/ai-model-marketplace:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8080
          protocol: TCP
        - name: grpc
          containerPort: 9000
          protocol: TCP
        
        env:
        - name: MODEL_STORAGE_PATH
          value: "/app/models"
        - name: CACHE_PATH
          value: "/app/cache"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        
        envFrom:
        - configMapRef:
            name: ai-marketplace-config
        - secretRef:
            name: ai-marketplace-secrets
        
        resources:
          requests:
            cpu: 1000m
            memory: 4Gi
            ephemeral-storage: 10Gi
            nvidia.com/gpu: 1
          limits:
            cpu: 4000m
            memory: 16Gi
            ephemeral-storage: 50Gi
            nvidia.com/gpu: 1
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health/startup
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 10
          failureThreshold: 30
          successThreshold: 1
        
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - |
                echo "Gracefully shutting down AI Model Marketplace..."
                # Allow time for ongoing model operations to complete
                sleep 30
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false  # Need write access for model files
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
        - name: model-cache
          mountPath: /app/cache
        - name: tmp
          mountPath: /tmp
        - name: var-tmp
          mountPath: /var/tmp
        - name: app-cache
          mountPath: /app/.cache
      
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: ai-models-pvc
      - name: model-cache
        persistentVolumeClaim:
          claimName: ai-model-cache-pvc
      - name: tmp
        emptyDir:
          sizeLimit: 10Gi
      - name: var-tmp
        emptyDir:
          sizeLimit: 5Gi
      - name: app-cache
        emptyDir:
          sizeLimit: 10Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 90

---
apiVersion: v1
kind: Service
metadata:
  name: ai-model-marketplace
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800  # 3 hours for long model operations
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: metrics
    protocol: TCP
  - name: grpc
    port: 9000
    targetPort: grpc
    protocol: TCP
  selector:
    app: ai-model-marketplace

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-marketplace-service
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT:role/ai-marketplace-service-role"
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ai-marketplace-service
  namespace: ai-model-marketplace
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims", "services", "configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["batch"]
  resources: ["jobs", "cronjobs"]
  verbs: ["get", "list", "create", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-marketplace-service
  namespace: ai-model-marketplace
subjects:
- kind: ServiceAccount
  name: ai-marketplace-service
  namespace: ai-model-marketplace
roleRef:
  kind: Role
  name: ai-marketplace-service
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-model-marketplace-hpa
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-model-marketplace
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: model_operations_per_second
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes for model operations
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 180  # 3 minutes
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 3
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-model-marketplace-pdb
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: ai-model-marketplace

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-marketplace-network-policy
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
spec:
  podSelector:
    matchLabels:
      app: ai-model-marketplace
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from API Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 8000
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8080
  # Allow traffic from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-model-marketplace
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 9000
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS outbound (for model downloads, registries)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow HTTP outbound (for internal services)
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
  # Allow database connections
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ai-model-marketplace-metrics
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: ai-model-marketplace
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s
    honorLabels: true
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'ai_model_.*'
      targetLabel: __tmp_ai_model_metric
      replacement: 'true'
  namespaceSelector:
    matchNames:
    - ai-model-marketplace

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ai-model-marketplace-alerts
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
    prometheus: kube-prometheus
spec:
  groups:
  - name: ai-model-marketplace.rules
    interval: 30s
    rules:
    - alert: AIModelMarketplaceDown
      expr: up{job="ai-model-marketplace"} == 0
      for: 5m
      labels:
        severity: critical
        service: ai-model-marketplace
        component: application
      annotations:
        summary: "AI Model Marketplace is down"
        description: "AI Model Marketplace service has been down for more than 5 minutes"
    
    - alert: HighModelRegistrationFailures
      expr: increase(model_registration_failures_total[5m]) > 5
      for: 5m
      labels:
        severity: warning
        service: ai-model-marketplace
        component: registry
      annotations:
        summary: "High model registration failures"
        description: "{{ $value }} model registration failures in the last 5 minutes"
    
    - alert: ModelDeploymentFailures
      expr: increase(model_deployment_failures_total[5m]) > 3
      for: 5m
      labels:
        severity: critical
        service: ai-model-marketplace
        component: deployment
      annotations:
        summary: "Model deployment failures detected"
        description: "{{ $value }} model deployment failures in the last 5 minutes"
    
    - alert: HighModelStorageUsage
      expr: ai_model_storage_usage_percent > 85
      for: 10m
      labels:
        severity: warning
        service: ai-model-marketplace
        component: storage
      annotations:
        summary: "High model storage usage"
        description: "Model storage usage is {{ $value }}%"
    
    - alert: GPUResourceExhaustion
      expr: ai_model_gpu_utilization > 95
      for: 15m
      labels:
        severity: warning
        service: ai-model-marketplace
        component: resources
      annotations:
        summary: "GPU resources near exhaustion"
        description: "GPU utilization is {{ $value }}%"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: model-cleanup
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    component: maintenance
spec:
  schedule: "0 2 * * *"  # Run at 2 AM daily
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: ai-model-marketplace
            component: maintenance
        spec:
          restartPolicy: OnFailure
          containers:
          - name: model-cleanup
            image: ghcr.io/publishai/ai-model-marketplace:v1.0.0
            command: ["python", "scripts/cleanup_models.py"]
            envFrom:
            - configMapRef:
                name: ai-marketplace-config
            - secretRef:
                name: ai-marketplace-secrets
            volumeMounts:
            - name: model-storage
              mountPath: /app/models
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 1000m
                memory: 2Gi
          volumes:
          - name: model-storage
            persistentVolumeClaim:
              claimName: ai-models-pvc

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-model-marketplace-ingress
  namespace: ai-model-marketplace
  labels:
    app: ai-model-marketplace
    tier: application
    component: ai-marketplace
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "10g"  # Large file uploads
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/client-max-body-size: "10g"
spec:
  tls:
  - hosts:
    - models.publishai.com
    - marketplace.publishai.com
    secretName: ai-marketplace-tls
  rules:
  - host: models.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-model-marketplace
            port:
              number: 8000
  - host: marketplace.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-model-marketplace
            port:
              number: 8000