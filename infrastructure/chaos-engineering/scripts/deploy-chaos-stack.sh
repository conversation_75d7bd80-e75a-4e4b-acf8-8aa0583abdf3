#!/bin/bash

# Deploy Chaos Engineering Stack for Publish AI
# Installs Litmus Chaos, configures experiments, and sets up monitoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHAOS_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"
NAMESPACE="litmus"
TIMEOUT="600s"

# Default values
DRY_RUN=false
INSTALL_EXPERIMENTS=true
SETUP_MONITORING=true
ENABLE_WEBHOOKS=false

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy the complete Chaos Engineering stack for Publish AI.

Options:
  -d, --dry-run           Show what would be deployed without making changes
  -e, --skip-experiments  Skip deploying chaos experiments
  -m, --skip-monitoring   Skip monitoring setup
  -w, --enable-webhooks   Enable chaos webhooks and notifications
  -t, --timeout DURATION  Deployment timeout (default: 600s)
  -h, --help              Show this help message

Components Deployed:
  - Litmus Chaos Operator and CRDs
  - Chaos experiment definitions
  - Service-specific chaos engines
  - Infrastructure chaos experiments
  - Monitoring and alerting integration
  - Safety controls and circuit breakers

Examples:
  $0                      # Deploy complete chaos stack
  $0 --dry-run            # Preview deployment without applying
  $0 --skip-experiments   # Deploy operator only
  $0 --enable-webhooks    # Deploy with notification webhooks

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -e|--skip-experiments)
                INSTALL_EXPERIMENTS=false
                shift
                ;;
            -m|--skip-monitoring)
                SETUP_MONITORING=false
                shift
                ;;
            -w|--enable-webhooks)
                ENABLE_WEBHOOKS=true
                shift
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check cluster admin privileges
    if ! kubectl auth can-i create clusterrole &> /dev/null; then
        error "Insufficient permissions. Please run with cluster admin privileges"
        exit 1
    fi
    
    # Check if required namespaces exist
    local required_namespaces=("api-gateway" "content-generation" "market-intelligence" "publishing-service")
    for ns in "${required_namespaces[@]}"; do
        if ! kubectl get namespace "$ns" &> /dev/null; then
            warn "Namespace '$ns' not found. Some experiments may not work."
        fi
    done
    
    # Check cluster resources
    local nodes=$(kubectl get nodes --no-headers | wc -l)
    if [ "$nodes" -lt 3 ]; then
        warn "Cluster has less than 3 nodes. Some chaos experiments may have limited impact."
    fi
    
    log "Prerequisites check passed"
}

# Deploy Litmus Chaos Operator
deploy_litmus_operator() {
    log "Deploying Litmus Chaos Operator..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Litmus Chaos Operator"
        return 0
    fi
    
    # Apply Litmus operator configuration
    kubectl apply -f "$CHAOS_CONFIG_DIR/litmus/litmus-operator.yaml"
    
    # Wait for operator to be ready
    log "Waiting for Litmus operator to be ready..."
    kubectl wait --for=condition=available deployment/chaos-operator-ce -n "$NAMESPACE" --timeout="$TIMEOUT"
    
    # Verify CRDs are installed
    log "Verifying Chaos CRDs..."
    local crds=("chaosengines.litmuschaos.io" "chaosexperiments.litmuschaos.io" "chaosresults.litmuschaos.io")
    for crd in "${crds[@]}"; do
        if ! kubectl get crd "$crd" &> /dev/null; then
            error "CRD $crd not found"
            exit 1
        fi
    done
    
    log "Litmus Chaos Operator deployed successfully"
}

# Create chaos service accounts
create_chaos_service_accounts() {
    log "Creating chaos service accounts in target namespaces..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create chaos service accounts"
        return 0
    fi
    
    # List of namespaces that need chaos service accounts
    local namespaces=("api-gateway" "content-generation" "market-intelligence" "publishing-service" 
                     "cover-designer" "sales-monitor" "multimodal-generator" "personalization"
                     "research" "event-bus" "monitoring" "security" "istio-system" "kube-system")
    
    for ns in "${namespaces[@]}"; do
        if kubectl get namespace "$ns" &> /dev/null; then
            info "Creating chaos service account in namespace: $ns"
            
            # Create service account
            kubectl create serviceaccount litmus -n "$ns" --dry-run=client -o yaml | kubectl apply -f -
            
            # Create role binding
            kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: litmus-chaos
  namespace: $ns
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: litmus
subjects:
- kind: ServiceAccount
  name: litmus
  namespace: $ns
EOF
        else
            warn "Namespace $ns does not exist, skipping service account creation"
        fi
    done
    
    log "Chaos service accounts created"
}

# Deploy chaos experiments
deploy_chaos_experiments() {
    if [ "$INSTALL_EXPERIMENTS" = false ]; then
        info "Skipping chaos experiments deployment as requested"
        return 0
    fi
    
    log "Deploying chaos experiments..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy chaos experiments"
        return 0
    fi
    
    # Deploy API Gateway chaos experiments
    if kubectl get namespace api-gateway &> /dev/null; then
        info "Deploying API Gateway chaos experiments..."
        kubectl apply -f "$CHAOS_CONFIG_DIR/experiments/api-gateway-chaos.yaml"
    fi
    
    # Deploy AI Services chaos experiments
    info "Deploying AI Services chaos experiments..."
    kubectl apply -f "$CHAOS_CONFIG_DIR/experiments/ai-services-chaos.yaml"
    
    # Deploy Infrastructure chaos experiments
    info "Deploying Infrastructure chaos experiments..."
    kubectl apply -f "$CHAOS_CONFIG_DIR/experiments/infrastructure-chaos.yaml"
    
    log "Chaos experiments deployed successfully"
}

# Setup chaos monitoring
setup_chaos_monitoring() {
    if [ "$SETUP_MONITORING" = false ]; then
        info "Skipping chaos monitoring setup as requested"
        return 0
    fi
    
    log "Setting up chaos monitoring integration..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup chaos monitoring"
        return 0
    fi
    
    # Create chaos monitoring dashboard
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: chaos-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  chaos-engineering.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Chaos Engineering - Publish AI",
        "tags": ["chaos", "reliability"],
        "style": "dark",
        "timezone": "browser",
        "refresh": "30s",
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "Active Chaos Experiments",
            "type": "stat",
            "targets": [
              {
                "expr": "count(kube_pod_info{namespace=\"litmus\", pod=~\".*runner.*\"})",
                "legendFormat": "Active Experiments"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Chaos Experiment Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(chaos_experiment_passed_total[5m]) / rate(chaos_experiment_total[5m]) * 100",
                "legendFormat": "Success Rate %"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Service Health During Chaos",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=~\".*gateway.*\"}[5m])",
                "legendFormat": "API Gateway RPS"
              },
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=~\".*gateway.*\"}[5m]))",
                "legendFormat": "95th Percentile Latency"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 4,
            "title": "Chaos Events Timeline",
            "type": "logs",
            "targets": [
              {
                "expr": "{namespace=\"litmus\"} |= \"chaos\"",
                "legendFormat": "Chaos Events"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
          }
        ]
      }
    }
EOF
    
    # Create chaos-specific alerting rules
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: chaos-engineering-alerts
  namespace: monitoring
  labels:
    app: prometheus
spec:
  groups:
  - name: chaos-engineering
    rules:
    - alert: ChaosExperimentFailed
      expr: increase(chaos_experiment_failed_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
        component: chaos-engineering
      annotations:
        summary: "Chaos experiment failed"
        description: "A chaos experiment has failed. Check the experiment logs for details."
    
    - alert: UnexpectedServiceDegradation
      expr: rate(http_requests_total{code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
      for: 2m
      labels:
        severity: critical
        component: service-health
      annotations:
        summary: "High error rate during potential chaos experiment"
        description: "Error rate is {{ \$value | humanizePercentage }} which may indicate unexpected impact from chaos experiments."
    
    - alert: ChaosExperimentRunningTooLong
      expr: time() - chaos_experiment_start_time > 1800  # 30 minutes
      for: 0m
      labels:
        severity: warning
        component: chaos-engineering
      annotations:
        summary: "Chaos experiment running longer than expected"
        description: "Chaos experiment {{ \$labels.experiment }} has been running for over 30 minutes."
    
    - alert: ServiceRecoveryTime
      expr: time() - on(service) group_left() (chaos_experiment_end_time) > 300  # 5 minutes
      for: 1m
      labels:
        severity: warning
        component: service-recovery
      annotations:
        summary: "Service taking too long to recover from chaos"
        description: "Service {{ \$labels.service }} is taking longer than expected to recover from chaos experiment."
EOF
    
    log "Chaos monitoring setup completed"
}

# Configure chaos webhooks
configure_webhooks() {
    if [ "$ENABLE_WEBHOOKS" = false ]; then
        info "Skipping webhook configuration"
        return 0
    fi
    
    log "Configuring chaos webhooks..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would configure chaos webhooks"
        return 0
    fi
    
    # Create webhook configuration for Slack/Teams notifications
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: chaos-webhook-config
  namespace: litmus
data:
  webhook-config.yaml: |
    webhooks:
      - name: "slack-notifications"
        url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
        events: ["experiment-start", "experiment-end", "experiment-failed"]
        template: |
          {
            "text": "Chaos Experiment: {{ .experiment_name }}",
            "attachments": [
              {
                "color": "{{ if eq .status 'failed' }}danger{{ else if eq .status 'running' }}warning{{ else }}good{{ end }}",
                "fields": [
                  {"title": "Status", "value": "{{ .status }}", "short": true},
                  {"title": "Service", "value": "{{ .target_service }}", "short": true},
                  {"title": "Duration", "value": "{{ .duration }}", "short": true},
                  {"title": "Namespace", "value": "{{ .namespace }}", "short": true}
                ]
              }
            ]
          }
      
      - name: "teams-notifications"
        url: "https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK"
        events: ["experiment-failed", "service-degradation"]
        template: |
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "{{ if eq .severity 'critical' }}FF0000{{ else }}FFA500{{ end }}",
            "summary": "Chaos Engineering Alert",
            "sections": [{
              "activityTitle": "{{ .alert_title }}",
              "activitySubtitle": "{{ .experiment_name }}",
              "facts": [
                {"name": "Service", "value": "{{ .target_service }}"},
                {"name": "Status", "value": "{{ .status }}"},
                {"name": "Time", "value": "{{ .timestamp }}"}
              ]
            }]
          }
EOF
    
    log "Webhook configuration completed"
}

# Create chaos safety controls
create_safety_controls() {
    log "Creating chaos safety controls..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create safety controls"
        return 0
    fi
    
    # Create emergency stop script
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: chaos-safety-controls
  namespace: litmus
data:
  emergency-stop.sh: |
    #!/bin/bash
    # Emergency stop all chaos experiments
    
    echo "🚨 EMERGENCY STOP: Terminating all chaos experiments..."
    
    # Stop all chaos engines
    kubectl get chaosengines -A -o json | jq -r '.items[] | "\(.metadata.namespace) \(.metadata.name)"' | while read ns name; do
        echo "Stopping chaos engine: \$name in namespace: \$ns"
        kubectl patch chaosengine "\$name" -n "\$ns" --type='merge' -p='{"spec":{"engineState":"stop"}}'
    done
    
    # Delete all running chaos experiment pods
    kubectl delete pods -l chaosUID --all-namespaces --grace-period=0 --force
    
    echo "✅ All chaos experiments stopped"
    
    # Send notification
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 EMERGENCY STOP: All chaos experiments have been terminated by operator"}' \
        \${SLACK_WEBHOOK_URL:-""}
  
  health-check.sh: |
    #!/bin/bash
    # Health check script to monitor service health during chaos
    
    services=("api-gateway" "content-generation" "market-intelligence" "publishing-service")
    failed_services=()
    
    for service in "\${services[@]}"; do
        if ! curl -f -s "http://\$service.\$service.svc.cluster.local:8000/health" > /dev/null; then
            failed_services+=("\$service")
        fi
    done
    
    if [ \${#failed_services[@]} -gt 2 ]; then
        echo "🚨 CRITICAL: Multiple services failing during chaos: \${failed_services[*]}"
        # Auto-trigger emergency stop
        ./emergency-stop.sh
        exit 1
    elif [ \${#failed_services[@]} -gt 0 ]; then
        echo "⚠️  WARNING: Services with issues: \${failed_services[*]}"
        exit 2
    else
        echo "✅ All services healthy"
        exit 0
    fi
  
  circuit-breaker-check.sh: |
    #!/bin/bash
    # Check circuit breaker status across services
    
    services=("api-gateway" "content-generation" "market-intelligence")
    open_breakers=0
    
    for service in "\${services[@]}"; do
        if curl -s "http://\$service.\$service.svc.cluster.local:8000/api/circuit-breaker/status" | grep -q '"state":"open"'; then
            echo "Circuit breaker OPEN for service: \$service"
            ((open_breakers++))
        fi
    done
    
    if [ \$open_breakers -gt 1 ]; then
        echo "🚨 ALERT: Multiple circuit breakers open (\$open_breakers)"
        exit 1
    fi
    
    echo "Circuit breakers status: \$open_breakers open"
    exit 0
EOF
    
    # Create safety monitoring job
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: chaos-safety-monitor
  namespace: litmus
spec:
  schedule: "*/2 * * * *"  # Every 2 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: safety-monitor
            image: curlimages/curl:8.4.0
            command:
            - /bin/sh
            - -c
            - |
              # Source safety scripts
              source /scripts/health-check.sh
              source /scripts/circuit-breaker-check.sh
              
              # Run health checks
              if ! /scripts/health-check.sh; then
                echo "Health check failed, chaos experiments may need attention"
              fi
              
              # Check circuit breakers
              /scripts/circuit-breaker-check.sh
            volumeMounts:
            - name: safety-scripts
              mountPath: /scripts
          volumes:
          - name: safety-scripts
            configMap:
              name: chaos-safety-controls
              defaultMode: 0755
          restartPolicy: OnFailure
EOF
    
    log "Safety controls created"
}

# Verify deployment
verify_deployment() {
    log "Verifying chaos engineering deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check Litmus operator
    local operator_ready=$(kubectl get pods -n "$NAMESPACE" -l name=chaos-operator -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    if [ "$operator_ready" -gt 0 ]; then
        info "✅ Litmus Chaos Operator: Running"
    else
        error "❌ Litmus Chaos Operator: Not running"
    fi
    
    # Check CRDs
    local crds=("chaosengines.litmuschaos.io" "chaosexperiments.litmuschaos.io" "chaosresults.litmuschaos.io")
    for crd in "${crds[@]}"; do
        if kubectl get crd "$crd" &> /dev/null; then
            info "✅ CRD installed: $crd"
        else
            error "❌ CRD missing: $crd"
        fi
    done
    
    # Check chaos experiments
    if [ "$INSTALL_EXPERIMENTS" = true ]; then
        local experiments=$(kubectl get chaosexperiments -A --no-headers 2>/dev/null | wc -l)
        if [ "$experiments" -gt 0 ]; then
            info "✅ Chaos experiments: $experiments installed"
        else
            warn "❓ No chaos experiments found"
        fi
        
        local engines=$(kubectl get chaosengines -A --no-headers 2>/dev/null | wc -l)
        if [ "$engines" -gt 0 ]; then
            info "✅ Chaos engines: $engines configured"
        else
            warn "❓ No chaos engines found"
        fi
    fi
    
    # Check monitoring integration
    if [ "$SETUP_MONITORING" = true ] && kubectl get namespace monitoring &> /dev/null; then
        if kubectl get configmap chaos-dashboard -n monitoring &> /dev/null; then
            info "✅ Chaos monitoring dashboard: Configured"
        else
            warn "❓ Chaos monitoring dashboard: Not found"
        fi
        
        if kubectl get prometheusrule chaos-engineering-alerts -n monitoring &> /dev/null; then
            info "✅ Chaos alerting rules: Configured"
        else
            warn "❓ Chaos alerting rules: Not found"
        fi
    fi
    
    log "Deployment verification completed"
}

# Show access information
show_access_info() {
    log "Chaos Engineering stack access information:"
    
    info ""
    info "Useful commands:"
    info "kubectl get chaosexperiments -A                     # List all chaos experiments"
    info "kubectl get chaosengines -A                        # List all chaos engines"
    info "kubectl get chaosresults -A                        # List chaos results"
    info "kubectl logs -l app=chaos-operator -n litmus       # Chaos operator logs"
    
    info ""
    info "Start/Stop chaos experiments:"
    info "kubectl patch chaosengine <engine-name> -n <namespace> --type='merge' -p='{\"spec\":{\"engineState\":\"active\"}}'"
    info "kubectl patch chaosengine <engine-name> -n <namespace> --type='merge' -p='{\"spec\":{\"engineState\":\"stop\"}}'"
    
    info ""
    info "Emergency commands:"
    info "kubectl exec -n litmus <chaos-pod> -- /scripts/emergency-stop.sh    # Emergency stop all chaos"
    info "kubectl exec -n litmus <chaos-pod> -- /scripts/health-check.sh      # Check service health"
    
    info ""
    info "Monitoring:"
    if kubectl get namespace monitoring &> /dev/null; then
        info "Grafana dashboard: https://grafana.publish-ai.local/d/chaos-engineering"
        info "Prometheus alerts: https://prometheus.publish-ai.local/alerts"
    else
        info "Install monitoring stack to access chaos dashboards and alerts"
    fi
    
    info ""
    info "Safety features:"
    info "- Automatic health monitoring every 2 minutes"
    info "- Circuit breaker status tracking"
    info "- Emergency stop capabilities"
    info "- Blast radius controls in all experiments"
    
    info ""
    info "Next steps:"
    info "1. Review experiment configurations in experiments/ directory"
    info "2. Start with API Gateway experiments for initial testing"
    info "3. Monitor service health during chaos experiments"
    info "4. Gradually increase chaos intensity and scope"
    info "5. Document findings and improve system resilience"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        error "Chaos engineering deployment failed with exit code $exit_code"
        
        if [ "$DRY_RUN" = false ]; then
            warn "Check the following for troubleshooting:"
            warn "kubectl get pods -n $NAMESPACE"
            warn "kubectl describe deployment chaos-operator-ce -n $NAMESPACE"
            warn "kubectl logs -l name=chaos-operator -n $NAMESPACE"
        fi
    fi
}

# Main function
main() {
    log "Starting Chaos Engineering Stack Deployment for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    deploy_litmus_operator
    create_chaos_service_accounts
    deploy_chaos_experiments
    setup_chaos_monitoring
    configure_webhooks
    create_safety_controls
    verify_deployment
    show_access_info
    
    log "✅ Chaos Engineering stack deployment completed successfully!"
    log ""
    info "⚠️  IMPORTANT SAFETY REMINDERS:"
    info "- Always start with low-impact experiments"
    info "- Monitor service health continuously during chaos"
    info "- Use emergency stop if unexpected issues occur"
    info "- Follow blast radius controls to limit impact"
    info "- Document all findings for system improvement"
    log ""
    log "Happy chaos engineering! 🧪💥"
}

# Set up signal handlers
trap cleanup EXIT

# Run main function
main "$@"