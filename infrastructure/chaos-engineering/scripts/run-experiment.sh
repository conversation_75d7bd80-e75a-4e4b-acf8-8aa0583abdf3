#!/bin/bash

# Run Individual Chaos Experiments for Publish AI
# Provides a simple interface to execute specific chaos experiments with safety controls

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHAOS_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"

# Default values
EXPERIMENT_TYPE=""
TARGET_SERVICE=""
DURATION="60"
INTENSITY="low"
DRY_RUN=false
MONITORING=true
SAFETY_CHECKS=true
AUTO_STOP=true

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 --type EXPERIMENT_TYPE [OPTIONS]

Run individual chaos experiments with safety controls.

Required:
  -t, --type TYPE         Experiment type (see list below)

Options:
  -s, --service SERVICE   Target service (required for service-specific experiments)
  -d, --duration SECONDS  Experiment duration in seconds (default: 60)
  -i, --intensity LEVEL   Intensity level: low, medium, high (default: low)
  -n, --dry-run           Preview experiment without executing
  -m, --no-monitoring     Disable monitoring during experiment
  -c, --no-safety-checks  Disable safety checks
  -a, --no-auto-stop      Disable automatic emergency stop
  -h, --help              Show this help message

Experiment Types:
  Service-Level:
    pod-failure           Random pod termination
    container-kill        Container process termination
    network-latency       Network delay injection
    cpu-stress            High CPU utilization
    memory-stress         Memory pressure simulation
    
  Infrastructure:
    dns-chaos             DNS resolution failures
    storage-chaos         Disk I/O stress and failures
    ingress-chaos         Load balancer failures
    cert-chaos            Certificate management disruption
    
  AI-Specific:
    model-failure         AI model loading failures
    token-exhaustion      API rate limit simulation
    quality-degradation   Force low-quality AI outputs
    generation-timeout    Long processing time simulation
    cost-spike            Unexpected cost increases
    
  Business-Critical:
    publishing-failure    Publishing pipeline disruption
    payment-chaos         Transaction processing failures
    auth-chaos            Authentication service failures
    data-corruption       Data consistency issues

Services:
  api-gateway, content-generation, market-intelligence, publishing-service,
  cover-designer, sales-monitor, multimodal-generator, personalization, research

Intensity Levels:
  low     - Minimal impact (10-20% of pods, short duration)
  medium  - Moderate impact (30-50% of pods, medium duration)
  high    - High impact (50%+ of pods, longer duration)

Examples:
  $0 --type pod-failure --service api-gateway
  $0 --type network-latency --service content-generation --intensity medium
  $0 --type model-failure --service content-generation --duration 120
  $0 --type infrastructure-chaos --intensity low --dry-run

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                EXPERIMENT_TYPE="$2"
                shift 2
                ;;
            -s|--service)
                TARGET_SERVICE="$2"
                shift 2
                ;;
            -d|--duration)
                DURATION="$2"
                shift 2
                ;;
            -i|--intensity)
                INTENSITY="$2"
                shift 2
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -m|--no-monitoring)
                MONITORING=false
                shift
                ;;
            -c|--no-safety-checks)
                SAFETY_CHECKS=false
                shift
                ;;
            -a|--no-auto-stop)
                AUTO_STOP=false
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate required parameters
    if [ -z "$EXPERIMENT_TYPE" ]; then
        error "Experiment type is required. Use --type to specify."
        usage
        exit 1
    fi
    
    # Validate intensity
    if [[ ! "$INTENSITY" =~ ^(low|medium|high)$ ]]; then
        error "Invalid intensity level: $INTENSITY. Must be low, medium, or high."
        exit 1
    fi
}

# Get experiment parameters based on intensity
get_experiment_params() {
    local intensity="$1"
    
    case "$intensity" in
        low)
            PODS_AFFECTED_PERC="20"
            CHAOS_INTERVAL="30"
            NETWORK_LATENCY="100"
            CPU_LOAD="50"
            MEMORY_PERCENTAGE="50"
            ;;
        medium)
            PODS_AFFECTED_PERC="40"
            CHAOS_INTERVAL="20"
            NETWORK_LATENCY="300"
            CPU_LOAD="70"
            MEMORY_PERCENTAGE="70"
            ;;
        high)
            PODS_AFFECTED_PERC="60"
            CHAOS_INTERVAL="10"
            NETWORK_LATENCY="1000"
            CPU_LOAD="90"
            MEMORY_PERCENTAGE="85"
            ;;
    esac
}

# Validate target service
validate_target_service() {
    local service="$1"
    
    if [ -z "$service" ]; then
        error "Target service is required for service-specific experiments"
        exit 1
    fi
    
    # Check if service namespace exists
    if ! kubectl get namespace "$service" &> /dev/null; then
        error "Namespace '$service' not found. Is the service deployed?"
        exit 1
    fi
    
    # Check if service has pods
    local pods=$(kubectl get pods -n "$service" -l "app=$service" --no-headers 2>/dev/null | wc -l)
    if [ "$pods" -eq 0 ]; then
        error "No pods found for service '$service' in namespace '$service'"
        exit 1
    fi
    
    info "Target service validated: $service ($pods pods found)"
}

# Pre-flight safety checks
run_safety_checks() {
    if [ "$SAFETY_CHECKS" = false ]; then
        warn "Safety checks disabled - proceeding without validation"
        return 0
    fi
    
    log "Running pre-flight safety checks..."
    
    # Check cluster health
    local unhealthy_nodes=$(kubectl get nodes --no-headers | grep -v " Ready " | wc -l)
    if [ "$unhealthy_nodes" -gt 0 ]; then
        error "Found $unhealthy_nodes unhealthy nodes. Not safe to run chaos experiments."
        exit 1
    fi
    
    # Check if other chaos experiments are running
    local running_experiments=$(kubectl get chaosengines -A -o jsonpath='{.items[?(@.spec.engineState=="active")].metadata.name}' | wc -w)
    if [ "$running_experiments" -gt 0 ]; then
        warn "$running_experiments chaos experiments already running. Consider stopping them first."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Experiment cancelled by user"
            exit 0
        fi
    fi
    
    # Check service health
    if [ -n "$TARGET_SERVICE" ]; then
        local service_health_url="http://$TARGET_SERVICE.$TARGET_SERVICE.svc.cluster.local:8000/health"
        if ! kubectl run health-check-temp --image=curlimages/curl:8.4.0 --rm -i --restart=Never -- curl -f -s "$service_health_url" &> /dev/null; then
            error "Target service '$TARGET_SERVICE' health check failed"
            exit 1
        fi
        info "Target service health check: PASSED"
    fi
    
    # Check monitoring stack
    if [ "$MONITORING" = true ] && ! kubectl get namespace monitoring &> /dev/null; then
        warn "Monitoring namespace not found. Experiment will run without monitoring."
        MONITORING=false
    fi
    
    # Check available resources
    local available_memory=$(kubectl top nodes 2>/dev/null | awk 'NR>1 {sum+=$5} END {print sum}' | sed 's/Mi//' || echo "0")
    if [ "$available_memory" -lt 1024 ] && [[ "$EXPERIMENT_TYPE" == *"memory"* ]]; then
        warn "Low available memory ($available_memory Mi). Memory stress experiments may cause node pressure."
    fi
    
    log "Safety checks completed"
}

# Generate chaos engine manifest
generate_chaos_manifest() {
    local experiment_type="$1"
    local service="$2"
    local duration="$3"
    
    get_experiment_params "$INTENSITY"
    
    local timestamp=$(date +%s)
    local engine_name="manual-chaos-$timestamp"
    local namespace="${service:-litmus}"
    
    case "$experiment_type" in
        pod-failure)
            cat << EOF
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: $engine_name
  namespace: $namespace
  labels:
    experiment-type: pod-failure
    intensity: $INTENSITY
    manual-execution: "true"
spec:
  appinfo:
    appns: '$namespace'
    applabel: 'app=$service'
    appkind: 'deployment'
  chaosServiceAccount: litmus
  monitoring: $MONITORING
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  experiments:
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '$duration'
        - name: PODS_AFFECTED_PERC
          value: '$PODS_AFFECTED_PERC'
        - name: CHAOS_INTERVAL
          value: '$CHAOS_INTERVAL'
        - name: FORCE
          value: 'false'
      probe:
      - name: "service-health-check"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 30
          retry: 3
          interval: 10
        httpProbe/inputs:
          url: "http://$service.$namespace.svc.cluster.local:8000/health"
          method:
            get:
              criteria: ==
              responseCode: "200"
EOF
            ;;
        
        network-latency)
            cat << EOF
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: $engine_name
  namespace: $namespace
  labels:
    experiment-type: network-latency
    intensity: $INTENSITY
    manual-execution: "true"
spec:
  appinfo:
    appns: '$namespace'
    applabel: 'app=$service'
    appkind: 'deployment'
  chaosServiceAccount: litmus
  monitoring: $MONITORING
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  experiments:
  - name: pod-network-latency
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '$duration'
        - name: NETWORK_LATENCY
          value: '$NETWORK_LATENCY'
        - name: PODS_AFFECTED_PERC
          value: '$PODS_AFFECTED_PERC'
        - name: NETWORK_INTERFACE
          value: 'eth0'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
      probe:
      - name: "response-time-check"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 15
        httpProbe/inputs:
          url: "http://$service.$namespace.svc.cluster.local:8000/health"
          method:
            get:
              criteria: <=
              responseTimeout: 10000
EOF
            ;;
        
        cpu-stress)
            cat << EOF
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: $engine_name
  namespace: $namespace
  labels:
    experiment-type: cpu-stress
    intensity: $INTENSITY
    manual-execution: "true"
spec:
  appinfo:
    appns: '$namespace'
    applabel: 'app=$service'
    appkind: 'deployment'
  chaosServiceAccount: litmus
  monitoring: $MONITORING
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  experiments:
  - name: pod-cpu-hog
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '$duration'
        - name: CPU_LOAD
          value: '$CPU_LOAD'
        - name: CPU_CORES
          value: '1'
        - name: PODS_AFFECTED_PERC
          value: '$PODS_AFFECTED_PERC'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
      probe:
      - name: "performance-under-load"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 45
          retry: 3
          interval: 15
        httpProbe/inputs:
          url: "http://$service.$namespace.svc.cluster.local:8000/health"
          method:
            get:
              criteria: ==
              responseCode: "200"
EOF
            ;;
        
        memory-stress)
            cat << EOF
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: $engine_name
  namespace: $namespace
  labels:
    experiment-type: memory-stress
    intensity: $INTENSITY
    manual-execution: "true"
spec:
  appinfo:
    appns: '$namespace'
    applabel: 'app=$service'
    appkind: 'deployment'
  chaosServiceAccount: litmus
  monitoring: $MONITORING
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  experiments:
  - name: pod-memory-hog
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '$duration'
        - name: MEMORY_PERCENTAGE
          value: '$MEMORY_PERCENTAGE'
        - name: PODS_AFFECTED_PERC
          value: '$PODS_AFFECTED_PERC'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
      probe:
      - name: "memory-pressure-handling"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 20
        httpProbe/inputs:
          url: "http://$service.$namespace.svc.cluster.local:8000/health"
          method:
            get:
              criteria: ==
              responseCode: "200"
EOF
            ;;
        
        *)
            error "Unsupported experiment type: $experiment_type"
            exit 1
            ;;
    esac
}

# Monitor experiment execution
monitor_experiment() {
    local engine_name="$1"
    local namespace="$2"
    local duration="$3"
    
    log "Monitoring chaos experiment: $engine_name"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + duration + 60))  # Add 1 minute buffer
    
    while [ $(date +%s) -lt $end_time ]; do
        local status=$(kubectl get chaosengine "$engine_name" -n "$namespace" -o jsonpath='{.status.engineStatus}' 2>/dev/null || echo "unknown")
        local experiment_status=$(kubectl get chaosresult -n "$namespace" -l chaosUID --no-headers 2>/dev/null | tail -1 | awk '{print $3}' || echo "running")
        
        info "Status: $status | Experiment: $experiment_status"
        
        # Check for completion
        if [[ "$status" == "completed" ]] || [[ "$experiment_status" == "Pass" ]] || [[ "$experiment_status" == "Fail" ]]; then
            break
        fi
        
        # Safety check: verify service health
        if [ "$SAFETY_CHECKS" = true ] && [ -n "$TARGET_SERVICE" ]; then
            if ! kubectl run health-check-temp --image=curlimages/curl:8.4.0 --rm -i --restart=Never -- curl -f -s "http://$TARGET_SERVICE.$TARGET_SERVICE.svc.cluster.local:8000/health" &> /dev/null; then
                warn "Service health check failed during experiment"
                if [ "$AUTO_STOP" = true ]; then
                    warn "Auto-stopping experiment due to service health failure"
                    stop_experiment "$engine_name" "$namespace"
                    break
                fi
            fi
        fi
        
        sleep 10
    done
    
    # Get final results
    local final_status=$(kubectl get chaosengine "$engine_name" -n "$namespace" -o jsonpath='{.status.engineStatus}' 2>/dev/null || echo "unknown")
    local final_result=$(kubectl get chaosresult -n "$namespace" -l chaosUID --no-headers 2>/dev/null | tail -1 | awk '{print $3}' || echo "unknown")
    
    log "Experiment completed - Status: $final_status | Result: $final_result"
    
    # Show detailed results
    show_experiment_results "$engine_name" "$namespace"
}

# Stop experiment
stop_experiment() {
    local engine_name="$1"
    local namespace="$2"
    
    log "Stopping chaos experiment: $engine_name"
    
    kubectl patch chaosengine "$engine_name" -n "$namespace" --type='merge' -p='{"spec":{"engineState":"stop"}}' 2>/dev/null || true
    
    # Wait for experiment to stop
    sleep 10
    
    log "Experiment stopped"
}

# Show experiment results
show_experiment_results() {
    local engine_name="$1"
    local namespace="$2"
    
    log "Experiment Results Summary:"
    
    # Engine status
    local engine_status=$(kubectl get chaosengine "$engine_name" -n "$namespace" -o jsonpath='{.status.engineStatus}' 2>/dev/null || echo "unknown")
    info "Engine Status: $engine_status"
    
    # Experiment results
    local results=$(kubectl get chaosresult -n "$namespace" -l chaosUID --no-headers 2>/dev/null)
    if [ -n "$results" ]; then
        info "Experiment Results:"
        echo "$results" | while read -r line; do
            local name=$(echo "$line" | awk '{print $2}')
            local verdict=$(echo "$line" | awk '{print $3}')
            info "  $name: $verdict"
        done
    fi
    
    # Probe results
    local probe_results=$(kubectl get chaosresult -n "$namespace" -l chaosUID -o jsonpath='{.items[*].status.probeSuccessPercentage}' 2>/dev/null)
    if [ -n "$probe_results" ]; then
        info "Probe Success Rate: $probe_results%"
    fi
    
    # Show logs if experiment failed
    if [[ "$engine_status" == "completed" ]] && kubectl get chaosresult -n "$namespace" -l chaosUID --no-headers 2>/dev/null | grep -q "Fail"; then
        warn "Experiment failed. Showing runner logs:"
        local runner_pod=$(kubectl get pods -n "$namespace" -l chaosUID --no-headers 2>/dev/null | tail -1 | awk '{print $1}')
        if [ -n "$runner_pod" ]; then
            kubectl logs "$runner_pod" -n "$namespace" --tail=20 || true
        fi
    fi
}

# Clean up experiment resources
cleanup_experiment() {
    local engine_name="$1"
    local namespace="$2"
    
    log "Cleaning up experiment resources..."
    
    # Delete chaos engine (this will clean up associated resources)
    kubectl delete chaosengine "$engine_name" -n "$namespace" 2>/dev/null || true
    
    # Clean up any remaining experiment pods
    kubectl delete pods -n "$namespace" -l chaosUID --grace-period=0 --force 2>/dev/null || true
    
    log "Cleanup completed"
}

# Main execution function
execute_experiment() {
    local experiment_type="$1"
    local service="$2"
    local duration="$3"
    
    # Validate service if specified
    if [ -n "$service" ]; then
        validate_target_service "$service"
    fi
    
    # Run safety checks
    run_safety_checks
    
    # Generate experiment manifest
    local manifest=$(generate_chaos_manifest "$experiment_type" "$service" "$duration")
    local engine_name=$(echo "$manifest" | grep "name:" | head -1 | awk '{print $2}')
    local namespace="${service:-litmus}"
    
    if [ "$DRY_RUN" = true ]; then
        log "DRY RUN: Would execute the following chaos experiment:"
        echo "$manifest"
        return 0
    fi
    
    # Apply experiment
    log "Starting chaos experiment: $experiment_type on service: ${service:-N/A}"
    info "Duration: ${duration}s | Intensity: $INTENSITY | Engine: $engine_name"
    
    echo "$manifest" | kubectl apply -f -
    
    # Monitor experiment
    monitor_experiment "$engine_name" "$namespace" "$duration"
    
    # Clean up
    if [[ "$AUTO_STOP" == true ]]; then
        cleanup_experiment "$engine_name" "$namespace"
    else
        info "Experiment resources left for manual cleanup. Use: kubectl delete chaosengine $engine_name -n $namespace"
    fi
}

# Main function
main() {
    log "Starting chaos experiment execution"
    
    parse_args "$@"
    
    # Check prerequisites
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if Litmus is installed
    if ! kubectl get namespace litmus &> /dev/null; then
        error "Litmus namespace not found. Please deploy the chaos stack first."
        exit 1
    fi
    
    # Execute experiment
    execute_experiment "$EXPERIMENT_TYPE" "$TARGET_SERVICE" "$DURATION"
    
    log "Chaos experiment execution completed"
}

# Set up signal handlers for emergency stop
emergency_stop() {
    warn "Emergency stop requested!"
    if [ -n "${engine_name:-}" ] && [ -n "${namespace:-}" ]; then
        stop_experiment "$engine_name" "$namespace"
        cleanup_experiment "$engine_name" "$namespace"
    fi
    exit 130
}

trap emergency_stop SIGINT SIGTERM

# Run main function
main "$@"