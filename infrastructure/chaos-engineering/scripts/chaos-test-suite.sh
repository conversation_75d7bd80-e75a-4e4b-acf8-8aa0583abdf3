#!/bin/bash

# Chaos Test Suite for Publish AI
# Runs comprehensive chaos engineering test suites with different levels of complexity

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHAOS_CONFIG_DIR="$(dirname "$SCRIPT_DIR")"

# Default values
TEST_LEVEL="basic"
TARGET_SERVICES=""
INCLUDE_INFRASTRUCTURE=false
PARALLEL_EXECUTION=false
DRY_RUN=false
REPORT_FILE=""
CONTINUOUS_MONITORING=true
SAFETY_THRESHOLD=3  # Maximum number of simultaneously failing services

# Test suite definitions
declare -A BASIC_TESTS=(
    ["api-gateway-pod-failure"]="pod-failure api-gateway 60 low"
    ["content-generation-health"]="network-latency content-generation 90 low"
    ["market-intelligence-cpu"]="cpu-stress market-intelligence 120 low"
)

declare -A INTERMEDIATE_TESTS=(
    ["api-gateway-comprehensive"]="pod-failure api-gateway 180 medium"
    ["ai-services-stress"]="memory-stress content-generation 240 medium"
    ["publishing-pipeline"]="container-kill publishing-service 150 medium"
    ["cover-generation-load"]="cpu-stress cover-designer 200 medium"
    ["sales-monitoring"]="network-latency sales-monitor 120 medium"
)

declare -A ADVANCED_TESTS=(
    ["multi-service-cascade"]="pod-failure api-gateway,content-generation 300 high"
    ["ai-model-failure"]="memory-stress content-generation,multimodal-generator 400 high"
    ["publishing-critical"]="container-kill publishing-service 250 high"
    ["network-partition"]="network-latency api-gateway,content-generation,market-intelligence 500 high"
    ["resource-exhaustion"]="cpu-stress content-generation,cover-designer 360 high"
)

declare -A INFRASTRUCTURE_TESTS=(
    ["dns-resolution"]="dns-chaos kube-system 180 medium"
    ["ingress-failover"]="pod-failure istio-system 240 medium"
    ["storage-pressure"]="storage-chaos publishing-service 300 medium"
    ["monitoring-disruption"]="pod-failure monitoring 150 low"
    ["cert-management"]="cert-chaos security 200 medium"
)

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run comprehensive chaos engineering test suites for Publish AI.

Options:
  -l, --level LEVEL           Test level: basic, intermediate, advanced, full (default: basic)
  -s, --services SERVICES     Comma-separated list of target services
  -i, --include-infrastructure Include infrastructure-level tests
  -p, --parallel              Run tests in parallel (faster but higher risk)
  -d, --dry-run               Preview tests without executing
  -r, --report FILE           Generate test report to file
  -m, --no-monitoring         Disable continuous monitoring
  -t, --safety-threshold N    Max simultaneous failing services (default: 3)
  -h, --help                  Show this help message

Test Levels:
  basic          - Low-impact tests on core services (5 tests, ~10 minutes)
  intermediate   - Medium-impact tests on all services (8 tests, ~25 minutes)
  advanced       - High-impact tests with cascading failures (10 tests, ~45 minutes)
  full           - All tests including infrastructure (20+ tests, ~90 minutes)

Services:
  api-gateway, content-generation, market-intelligence, publishing-service,
  cover-designer, sales-monitor, multimodal-generator, personalization, research

Infrastructure Components:
  DNS resolution, ingress gateways, storage systems, monitoring stack,
  certificate management, service mesh

Safety Features:
  - Automatic health monitoring
  - Emergency stop on safety threshold breach
  - Service recovery validation
  - Blast radius controls
  - Progressive failure injection

Examples:
  $0                                          # Basic test suite
  $0 --level intermediate --services api-gateway,content-generation
  $0 --level advanced --include-infrastructure --parallel
  $0 --level full --report chaos-report.json --dry-run

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -l|--level)
                TEST_LEVEL="$2"
                shift 2
                ;;
            -s|--services)
                TARGET_SERVICES="$2"
                shift 2
                ;;
            -i|--include-infrastructure)
                INCLUDE_INFRASTRUCTURE=true
                shift
                ;;
            -p|--parallel)
                PARALLEL_EXECUTION=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -r|--report)
                REPORT_FILE="$2"
                shift 2
                ;;
            -m|--no-monitoring)
                CONTINUOUS_MONITORING=false
                shift
                ;;
            -t|--safety-threshold)
                SAFETY_THRESHOLD="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                error "Unexpected argument: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate test level
    if [[ ! "$TEST_LEVEL" =~ ^(basic|intermediate|advanced|full)$ ]]; then
        error "Invalid test level: $TEST_LEVEL"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking test suite prerequisites..."
    
    # Check kubectl and cluster
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check Litmus installation
    if ! kubectl get namespace litmus &> /dev/null; then
        error "Litmus chaos engineering stack not found. Please deploy it first."
        exit 1
    fi
    
    # Check chaos operator
    local operator_ready=$(kubectl get pods -n litmus -l name=chaos-operator -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' | wc -w)
    if [ "$operator_ready" -eq 0 ]; then
        error "Litmus chaos operator is not running"
        exit 1
    fi
    
    # Validate target services
    if [ -n "$TARGET_SERVICES" ]; then
        IFS=',' read -ra services <<< "$TARGET_SERVICES"
        for service in "${services[@]}"; do
            if ! kubectl get namespace "$service" &> /dev/null; then
                warn "Service namespace '$service' not found"
            fi
        done
    fi
    
    # Check monitoring stack if enabled
    if [ "$CONTINUOUS_MONITORING" = true ]; then
        if ! kubectl get namespace monitoring &> /dev/null; then
            warn "Monitoring namespace not found. Disabling continuous monitoring."
            CONTINUOUS_MONITORING=false
        fi
    fi
    
    log "Prerequisites check completed"
}

# Get test suite based on level
get_test_suite() {
    local level="$1"
    local tests=()
    
    case "$level" in
        basic)
            for test in "${!BASIC_TESTS[@]}"; do
                tests+=("$test:${BASIC_TESTS[$test]}")
            done
            ;;
        intermediate)
            for test in "${!BASIC_TESTS[@]}"; do
                tests+=("$test:${BASIC_TESTS[$test]}")
            done
            for test in "${!INTERMEDIATE_TESTS[@]}"; do
                tests+=("$test:${INTERMEDIATE_TESTS[$test]}")
            done
            ;;
        advanced)
            for test in "${!INTERMEDIATE_TESTS[@]}"; do
                tests+=("$test:${INTERMEDIATE_TESTS[$test]}")
            done
            for test in "${!ADVANCED_TESTS[@]}"; do
                tests+=("$test:${ADVANCED_TESTS[$test]}")
            done
            ;;
        full)
            for test in "${!BASIC_TESTS[@]}"; do
                tests+=("$test:${BASIC_TESTS[$test]}")
            done
            for test in "${!INTERMEDIATE_TESTS[@]}"; do
                tests+=("$test:${INTERMEDIATE_TESTS[$test]}")
            done
            for test in "${!ADVANCED_TESTS[@]}"; do
                tests+=("$test:${ADVANCED_TESTS[$test]}")
            done
            if [ "$INCLUDE_INFRASTRUCTURE" = true ]; then
                for test in "${!INFRASTRUCTURE_TESTS[@]}"; do
                    tests+=("$test:${INFRASTRUCTURE_TESTS[$test]}")
                done
            fi
            ;;
    esac
    
    printf '%s\n' "${tests[@]}"
}

# Filter tests by target services
filter_tests_by_services() {
    local tests=("$@")
    local filtered=()
    
    if [ -z "$TARGET_SERVICES" ]; then
        printf '%s\n' "${tests[@]}"
        return
    fi
    
    IFS=',' read -ra target_services <<< "$TARGET_SERVICES"
    
    for test in "${tests[@]}"; do
        local test_services=$(echo "$test" | cut -d':' -f3)
        local match=false
        
        for target in "${target_services[@]}"; do
            if [[ "$test_services" == *"$target"* ]]; then
                match=true
                break
            fi
        done
        
        if [ "$match" = true ]; then
            filtered+=("$test")
        fi
    done
    
    printf '%s\n' "${filtered[@]}"
}

# Check system health
check_system_health() {
    local failing_services=()
    
    # List of critical services to check
    local services=("api-gateway" "content-generation" "market-intelligence" "publishing-service")
    
    for service in "${services[@]}"; do
        if kubectl get namespace "$service" &> /dev/null; then
            if ! kubectl run health-check-temp --image=curlimages/curl:8.4.0 --rm -i --restart=Never -- curl -f -s "http://$service.$service.svc.cluster.local:8000/health" &> /dev/null; then
                failing_services+=("$service")
            fi
        fi
    done
    
    echo "${#failing_services[@]}"
}

# Execute single test
execute_single_test() {
    local test_name="$1"
    local test_spec="$2"
    local test_results_var="$3"
    
    IFS=' ' read -ra spec_parts <<< "$test_spec"
    local experiment_type="${spec_parts[0]}"
    local target_service="${spec_parts[1]}"
    local duration="${spec_parts[2]}"
    local intensity="${spec_parts[3]}"
    
    info "Executing test: $test_name ($experiment_type on $target_service for ${duration}s at $intensity intensity)"
    
    local start_time=$(date +%s)
    local result="UNKNOWN"
    local details=""
    
    # Check pre-test system health
    local pre_test_failures=$(check_system_health)
    if [ "$pre_test_failures" -ge "$SAFETY_THRESHOLD" ]; then
        warn "Skipping test $test_name: too many services already failing ($pre_test_failures >= $SAFETY_THRESHOLD)"
        result="SKIPPED"
        details="Pre-test safety threshold exceeded"
    else
        # Execute the test
        if [ "$DRY_RUN" = true ]; then
            info "DRY RUN: Would execute $test_name"
            result="DRY_RUN"
            details="Test not executed (dry run mode)"
        else
            # Run the actual experiment
            if "$SCRIPT_DIR/run-experiment.sh" --type "$experiment_type" --service "$target_service" --duration "$duration" --intensity "$intensity" --no-auto-stop; then
                result="PASS"
                details="Test completed successfully"
            else
                result="FAIL"
                details="Test execution failed"
            fi
        fi
    fi
    
    local end_time=$(date +%s)
    local duration_actual=$((end_time - start_time))
    
    # Check post-test system health
    local post_test_failures=$(check_system_health)
    if [ "$post_test_failures" -ge "$SAFETY_THRESHOLD" ]; then
        warn "Safety threshold exceeded after test $test_name ($post_test_failures failures)"
        result="SAFETY_STOP"
        details="Post-test safety threshold exceeded"
    fi
    
    # Store results
    local test_result=$(cat <<EOF
{
  "test_name": "$test_name",
  "experiment_type": "$experiment_type",
  "target_service": "$target_service",
  "intensity": "$intensity",
  "planned_duration": $duration,
  "actual_duration": $duration_actual,
  "result": "$result",
  "details": "$details",
  "start_time": "$start_time",
  "end_time": "$end_time",
  "pre_test_failures": $pre_test_failures,
  "post_test_failures": $post_test_failures
}
EOF
)
    
    eval "$test_results_var+=(\"\$test_result\")"
    
    # Return status for safety checking
    if [[ "$result" == "SAFETY_STOP" ]]; then
        return 2  # Safety stop
    elif [[ "$result" == "FAIL" ]]; then
        return 1  # Test failed
    else
        return 0  # Success or skipped
    fi
}

# Execute test suite
execute_test_suite() {
    local tests=("$@")
    local test_results=()
    local total_tests=${#tests[@]}
    local passed_tests=0
    local failed_tests=0
    local skipped_tests=0
    local safety_stops=0
    
    log "Executing chaos test suite: $TEST_LEVEL level ($total_tests tests)"
    
    if [ "$PARALLEL_EXECUTION" = true ]; then
        warn "Parallel execution enabled - higher risk but faster completion"
    fi
    
    local test_number=0
    for test in "${tests[@]}"; do
        test_number=$((test_number + 1))
        local test_name=$(echo "$test" | cut -d':' -f1)
        local test_spec=$(echo "$test" | cut -d':' -f2-)
        
        log "Test $test_number/$total_tests: $test_name"
        
        if [ "$PARALLEL_EXECUTION" = true ]; then
            # Execute in background for parallel execution
            execute_single_test "$test_name" "$test_spec" "test_results" &
        else
            # Sequential execution
            local result_code=0
            execute_single_test "$test_name" "$test_spec" "test_results" || result_code=$?
            
            case $result_code in
                0) ((passed_tests++)) ;;
                1) ((failed_tests++)) ;;
                2) 
                    ((safety_stops++))
                    error "Safety stop triggered. Halting test suite execution."
                    break
                    ;;
            esac
            
            # Brief pause between tests
            if [ $test_number -lt $total_tests ]; then
                info "Waiting 30 seconds before next test..."
                sleep 30
            fi
        fi
    done
    
    # Wait for parallel jobs to complete
    if [ "$PARALLEL_EXECUTION" = true ]; then
        log "Waiting for parallel tests to complete..."
        wait
        
        # Count results (simplified for parallel execution)
        passed_tests=$total_tests  # This would need more sophisticated tracking
    fi
    
    # Calculate final stats
    local completed_tests=$((passed_tests + failed_tests))
    skipped_tests=$((total_tests - completed_tests - safety_stops))
    
    log "Test Suite Results:"
    info "Total Tests: $total_tests"
    info "Passed: $passed_tests"
    info "Failed: $failed_tests"
    info "Skipped: $skipped_tests"
    info "Safety Stops: $safety_stops"
    
    # Generate report if requested
    if [ -n "$REPORT_FILE" ]; then
        generate_test_report "$test_results" "$total_tests" "$passed_tests" "$failed_tests" "$skipped_tests" "$safety_stops"
    fi
    
    # Final system health check
    local final_failures=$(check_system_health)
    if [ "$final_failures" -gt 0 ]; then
        warn "System has $final_failures failing services after test suite completion"
        info "Consider running recovery procedures or investigating issues"
    else
        log "All services healthy after test suite completion"
    fi
}

# Generate test report
generate_test_report() {
    local test_results=("$@")
    local total=$2
    local passed=$3
    local failed=$4
    local skipped=$5
    local safety_stops=$6
    
    log "Generating test report: $REPORT_FILE"
    
    local report_content=$(cat <<EOF
{
  "test_suite_summary": {
    "level": "$TEST_LEVEL",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "total_tests": $total,
    "passed_tests": $passed,
    "failed_tests": $failed,
    "skipped_tests": $skipped,
    "safety_stops": $safety_stops,
    "success_rate": $(echo "scale=2; $passed * 100 / $total" | bc -l 2>/dev/null || echo "0"),
    "target_services": "$TARGET_SERVICES",
    "include_infrastructure": $INCLUDE_INFRASTRUCTURE,
    "parallel_execution": $PARALLEL_EXECUTION
  },
  "test_results": [
EOF
)
    
    # Add individual test results
    local first=true
    for result in "${test_results[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            report_content+=","
        fi
        report_content+="$result"
    done
    
    report_content+="]}"
    
    # Write to file
    echo "$report_content" > "$REPORT_FILE"
    
    info "Test report saved to: $REPORT_FILE"
    
    # Also create a human-readable summary
    local summary_file="${REPORT_FILE%.json}.summary.txt"
    cat > "$summary_file" << EOF
Chaos Engineering Test Suite Report
===================================

Test Level: $TEST_LEVEL
Date: $(date)
Total Tests: $total
Passed: $passed
Failed: $failed
Skipped: $skipped
Safety Stops: $safety_stops
Success Rate: $(echo "scale=1; $passed * 100 / $total" | bc -l 2>/dev/null || echo "0")%

Configuration:
- Target Services: ${TARGET_SERVICES:-"All"}
- Include Infrastructure: $INCLUDE_INFRASTRUCTURE
- Parallel Execution: $PARALLEL_EXECUTION
- Safety Threshold: $SAFETY_THRESHOLD
- Continuous Monitoring: $CONTINUOUS_MONITORING

Recommendations:
EOF
    
    if [ $failed -gt 0 ]; then
        echo "- Investigate failed tests and improve system resilience" >> "$summary_file"
    fi
    
    if [ $safety_stops -gt 0 ]; then
        echo "- Review safety thresholds and system stability" >> "$summary_file"
    fi
    
    if [ $passed -eq $total ]; then
        echo "- Excellent! Consider increasing test intensity or duration" >> "$summary_file"
    fi
    
    echo "- Review individual test results in $REPORT_FILE" >> "$summary_file"
    
    info "Human-readable summary saved to: $summary_file"
}

# Emergency stop all experiments
emergency_stop() {
    warn "Emergency stop requested! Terminating all active chaos experiments..."
    
    # Stop all active chaos engines
    kubectl get chaosengines -A -o json | jq -r '.items[] | "\(.metadata.namespace) \(.metadata.name)"' | while read -r ns name; do
        kubectl patch chaosengine "$name" -n "$ns" --type='merge' -p='{"spec":{"engineState":"stop"}}' 2>/dev/null || true
    done
    
    # Force delete chaos experiment pods
    kubectl delete pods -l chaosUID --all-namespaces --grace-period=0 --force 2>/dev/null || true
    
    log "Emergency stop completed"
    exit 130
}

# Main function
main() {
    log "Starting Chaos Engineering Test Suite for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    
    # Get test suite
    local all_tests=($(get_test_suite "$TEST_LEVEL"))
    local filtered_tests=($(filter_tests_by_services "${all_tests[@]}"))
    
    if [ ${#filtered_tests[@]} -eq 0 ]; then
        error "No tests match the specified criteria"
        exit 1
    fi
    
    info "Selected ${#filtered_tests[@]} tests for execution"
    
    # Show test plan
    if [ "$DRY_RUN" = true ]; then
        log "Test execution plan (DRY RUN):"
    else
        log "Test execution plan:"
    fi
    
    for test in "${filtered_tests[@]}"; do
        local test_name=$(echo "$test" | cut -d':' -f1)
        local test_spec=$(echo "$test" | cut -d':' -f2-)
        info "  $test_name: $test_spec"
    done
    
    # Confirm execution for non-dry runs
    if [ "$DRY_RUN" = false ] && [ "$TEST_LEVEL" != "basic" ]; then
        echo
        warn "This will execute ${#filtered_tests[@]} chaos experiments on your system."
        warn "Test level: $TEST_LEVEL"
        if [ "$PARALLEL_EXECUTION" = true ]; then
            warn "Parallel execution enabled - higher risk!"
        fi
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Test suite cancelled by user"
            exit 0
        fi
    fi
    
    # Execute test suite
    execute_test_suite "${filtered_tests[@]}"
    
    log "Chaos Engineering Test Suite completed"
}

# Set up signal handlers
trap emergency_stop SIGINT SIGTERM

# Run main function
main "$@"