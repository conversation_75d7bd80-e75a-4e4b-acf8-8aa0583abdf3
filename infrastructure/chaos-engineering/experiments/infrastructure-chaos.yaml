# Infrastructure-Level Chaos Experiments
# Tests cluster-wide resilience including networking, storage, and cross-service communication

---
# Cross-Service Communication Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: cross-service-communication-chaos
  namespace: litmus
  labels:
    chaos-type: infrastructure
    scope: cluster-wide
spec:
  appinfo:
    appns: 'api-gateway'  # Starting point for request flow
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Network partitioning between services
  - name: pod-network-latency
    spec:
      components:
        env:
        # Simulate high inter-service latency
        - name: NETWORK_LATENCY
          value: '2000'  # 2 seconds
        - name: TOTAL_CHAOS_DURATION
          value: '300'   # 5 minutes
        - name: NETWORK_INTERFACE
          value: 'eth0'
        - name: PODS_AFFECTED_PERC
          value: '50'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
        # Target specific destination IPs/services
        - name: DESTINATION_IPS
          value: 'content-generation.content-generation.svc.cluster.local,market-intelligence.market-intelligence.svc.cluster.local'
      
      probe:
      - name: "end-to-end-workflow-test"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 300
          retry: 2
          interval: 60
          probePollingInterval: 30
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/workflow/full-pipeline"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"test_workflow": true, "timeout": 240}'
              contentType: "application/json"
      
      - name: "circuit-breaker-activation"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 120
          retry: 1
          interval: 60
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/circuit-breakers/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "service-mesh-health"
        type: "k8sProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 30
          probePollingInterval: 10
        k8sProbe/inputs:
          group: "networking.istio.io"
          version: "v1beta1"
          resource: "virtualservices"
          namespace: "api-gateway"
          operation: "present"

---
# Database and Storage Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: storage-chaos
  namespace: litmus
  labels:
    chaos-type: storage
    scope: cluster-wide
spec:
  appinfo:
    appns: 'publishing-service'  # Critical service that uses storage
    applabel: 'app=publishing-service'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Disk pressure simulation
  - name: pod-memory-hog  # Using memory hog to simulate storage pressure
    spec:
      components:
        env:
        - name: MEMORY_CONSUMPTION
          value: '3072'  # High memory to simulate disk cache pressure
        - name: MEMORY_PERCENTAGE
          value: '80'
        - name: TOTAL_CHAOS_DURATION
          value: '180'
        - name: PODS_AFFECTED_PERC
          value: '30'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "database-connectivity"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 120
          retry: 3
          interval: 30
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/database/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "data-persistence-integrity"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 180
          retry: 2
          interval: 90
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/data/integrity-check"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"full_check": true}'
              contentType: "application/json"
      
      - name: "backup-system-availability"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 120
          retry: 1
          interval: 60
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/backup/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Load Balancer and Ingress Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: ingress-chaos
  namespace: istio-system
  labels:
    chaos-type: ingress
    scope: cluster-wide
spec:
  appinfo:
    appns: 'istio-system'
    applabel: 'app=istio-ingressgateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Ingress gateway pod failure
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '120'
        - name: PODS_AFFECTED_PERC
          value: '50'  # Affect half of ingress pods
        - name: CHAOS_INTERVAL
          value: '40'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "external-traffic-routing"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 20
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "load-balancer-failover"
        type: "k8sProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 30
          probePollingInterval: 15
        k8sProbe/inputs:
          group: ""
          version: "v1"
          resource: "services"
          namespace: "istio-system"
          fieldSelector: "metadata.name=istio-ingressgateway"
          operation: "present"
      
      - name: "ssl-termination-continuity"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 90
          retry: 2
          interval: 45
        httpProbe/inputs:
          url: "https://api-gateway.publish-ai.local/health"
          insecureSkipTLS: false
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# DNS and Service Discovery Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: dns-chaos
  namespace: kube-system
  labels:
    chaos-type: dns
    scope: cluster-wide
spec:
  appinfo:
    appns: 'kube-system'
    applabel: 'k8s-app=kube-dns'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # DNS service disruption
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '90'
        - name: PODS_AFFECTED_PERC
          value: '30'  # Limited impact on DNS
        - name: CHAOS_INTERVAL
          value: '30'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "service-discovery-functionality"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 20
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/services/discovery"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "inter-service-name-resolution"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 45
          retry: 3
          interval: 15
          probePollingInterval: 5
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "external-dns-resolution"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 30
          retry: 2
          interval: 15
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/external/dns-test"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Monitoring and Observability Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: monitoring-chaos
  namespace: monitoring
  labels:
    chaos-type: observability
    scope: cluster-wide
spec:
  appinfo:
    appns: 'monitoring'
    applabel: 'app=prometheus'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Monitoring stack disruption
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '150'
        - name: PODS_AFFECTED_PERC
          value: '25'
        - name: CHAOS_INTERVAL
          value: '50'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "metrics-collection-continuity"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 90
          retry: 3
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://prometheus.monitoring.svc.cluster.local:9090/api/v1/query?query=up"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "alerting-system-functionality"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 120
          retry: 2
          interval: 60
        httpProbe/inputs:
          url: "http://alertmanager.monitoring.svc.cluster.local:9093/api/v1/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "grafana-dashboard-availability"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://grafana.monitoring.svc.cluster.local:3000/api/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Event Bus and Message Queue Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: message-queue-chaos
  namespace: event-bus
  labels:
    chaos-type: messaging
    scope: cluster-wide
spec:
  appinfo:
    appns: 'event-bus'
    applabel: 'app=kafka'
    appkind: 'statefulset'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Message queue pod failure
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '180'
        - name: PODS_AFFECTED_PERC
          value: '33'  # One out of three Kafka brokers
        - name: CHAOS_INTERVAL
          value: '60'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "message-delivery-guarantee"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 120
          retry: 3
          interval: 40
          probePollingInterval: 20
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/events/test-delivery"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"test_message": "chaos_test", "delivery_guarantee": "at_least_once"}'
              contentType: "application/json"
      
      - name: "partition-rebalancing"
        type: "k8sProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 180
          retry: 2
          interval: 60
          probePollingInterval: 30
        k8sProbe/inputs:
          group: ""
          version: "v1"
          resource: "pods"
          namespace: "event-bus"
          labelSelector: "app=kafka"
          fieldSelector: "status.phase=Running"
          operation: "present"
      
      - name: "consumer-lag-monitoring"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 90
          retry: 2
          interval: 45
        httpProbe/inputs:
          url: "http://kafka-exporter.event-bus.svc.cluster.local:9308/metrics"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Security and Authentication Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: security-chaos
  namespace: security
  labels:
    chaos-type: security
    scope: cluster-wide
spec:
  appinfo:
    appns: 'security'
    applabel: 'app=cert-manager'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Certificate management disruption
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '120'
        - name: PODS_AFFECTED_PERC
          value: '50'
        - name: CHAOS_INTERVAL
          value: '40'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "tls-certificate-validity"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 20
          probePollingInterval: 10
        httpProbe/inputs:
          url: "https://api-gateway.publish-ai.local/health"
          insecureSkipTLS: false
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "mtls-service-communication"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 90
          retry: 2
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/security/mtls-test"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "certificate-rotation-capability"
        type: "k8sProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 300
          retry: 1
          interval: 120
        k8sProbe/inputs:
          group: "cert-manager.io"
          version: "v1"
          resource: "certificates"
          namespace: "security"
          operation: "present"