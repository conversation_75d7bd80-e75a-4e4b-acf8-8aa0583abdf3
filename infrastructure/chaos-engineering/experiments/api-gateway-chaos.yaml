# API Gateway Chaos Experiments
# Tests resilience of the API Gateway service under various failure conditions

apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-gateway-chaos
  namespace: api-gateway
  labels:
    app: api-gateway
    chaos-type: service-level
spec:
  # Target application information
  appinfo:
    appns: 'api-gateway'
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  # Chaos service account
  chaosServiceAccount: litmus
  
  # Enable monitoring and logging
  monitoring: true
  
  # Job cleanup policy
  jobCleanUpPolicy: 'delete'
  
  # Engine state
  engineState: 'active'
  
  # List of experiments to run
  experiments:
  
  # Experiment 1: Pod failure simulation
  - name: pod-delete
    spec:
      components:
        env:
        # Duration of chaos in seconds
        - name: TOTAL_CHAOS_DURATION
          value: '30'
        
        # Percentage of pods to affect (20% for gradual testing)
        - name: PODS_AFFECTED_PERC
          value: '20'
        
        # Time between chaos iterations
        - name: CHAOS_INTERVAL
          value: '10'
        
        # Force delete pods
        - name: FORCE
          value: 'false'
        
        # Sequence of chaos (parallel for faster testing)
        - name: SEQUENCE
          value: 'parallel'
      
      # Probes to validate system behavior during chaos
      probe:
      - name: "api-gateway-health-check"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 10
          retry: 3
          interval: 5
          probePollingInterval: 2
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
        
      - name: "api-gateway-functionality"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 15
          retry: 2
          interval: 10
          probePollingInterval: 5
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Experiment 2: Network latency simulation
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-gateway-network-chaos
  namespace: api-gateway
  labels:
    app: api-gateway
    chaos-type: network-level
spec:
  appinfo:
    appns: 'api-gateway'
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  - name: pod-network-latency
    spec:
      components:
        env:
        # Network latency in milliseconds (100ms)
        - name: NETWORK_LATENCY
          value: '100'
        
        # Duration of chaos
        - name: TOTAL_CHAOS_DURATION
          value: '60'
        
        # Network interface to affect
        - name: NETWORK_INTERFACE
          value: 'eth0'
        
        # Percentage of pods to affect
        - name: PODS_AFFECTED_PERC
          value: '30'
        
        # Container runtime
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        
        # Socket path
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "api-response-time-check"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 30
          retry: 3
          interval: 10
          probePollingInterval: 5
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: <=
              responseTimeout: 5000  # 5 seconds max response time
      
      - name: "downstream-service-connectivity"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 20
          retry: 2
          interval: 15
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/services/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Experiment 3: CPU resource stress
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-gateway-cpu-chaos
  namespace: api-gateway
  labels:
    app: api-gateway
    chaos-type: resource-stress
spec:
  appinfo:
    appns: 'api-gateway'
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  - name: pod-cpu-hog
    spec:
      components:
        env:
        # CPU load percentage
        - name: CPU_LOAD
          value: '80'
        
        # Number of CPU cores to stress
        - name: CPU_CORES
          value: '1'
        
        # Duration of chaos
        - name: TOTAL_CHAOS_DURATION
          value: '120'
        
        # Percentage of pods to affect
        - name: PODS_AFFECTED_PERC
          value: '25'
        
        # Container runtime
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        
        # Socket path
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "api-performance-under-load"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 30
          retry: 3
          interval: 15
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "circuit-breaker-functionality"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 60
          retry: 1
          interval: 30
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/api/circuit-breaker/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Experiment 4: Memory resource stress
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-gateway-memory-chaos
  namespace: api-gateway
  labels:
    app: api-gateway
    chaos-type: resource-stress
spec:
  appinfo:
    appns: 'api-gateway'
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  - name: pod-memory-hog
    spec:
      components:
        env:
        # Memory consumption in MB
        - name: MEMORY_CONSUMPTION
          value: '1024'
        
        # Memory percentage to consume
        - name: MEMORY_PERCENTAGE
          value: '70'
        
        # Duration of chaos
        - name: TOTAL_CHAOS_DURATION
          value: '90'
        
        # Percentage of pods to affect
        - name: PODS_AFFECTED_PERC
          value: '20'
        
        # Container runtime
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        
        # Socket path
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "memory-pressure-handling"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 45
          retry: 3
          interval: 20
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "oom-protection-check"
        type: "k8sProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 60
          retry: 1
          interval: 30
        k8sProbe/inputs:
          group: ""
          version: "v1"
          resource: "pods"
          namespace: "api-gateway"
          fieldSelector: "status.phase=Running"
          labelSelector: "app=api-gateway"
          operation: "present"

---
# Experiment 5: Container kill simulation
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-gateway-container-chaos
  namespace: api-gateway
  labels:
    app: api-gateway
    chaos-type: container-level
spec:
  appinfo:
    appns: 'api-gateway'
    applabel: 'app=api-gateway'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  - name: container-kill
    spec:
      components:
        env:
        # Duration of chaos
        - name: TOTAL_CHAOS_DURATION
          value: '45'
        
        # Interval between container kills
        - name: CHAOS_INTERVAL
          value: '15'
        
        # Signal to send to container (SIGKILL for immediate termination)
        - name: SIGNAL
          value: 'SIGKILL'
        
        # Container runtime
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        
        # Socket path
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
        
        # Target specific container (main API Gateway container)
        - name: TARGET_CONTAINER
          value: 'api-gateway'
      
      probe:
      - name: "container-restart-validation"
        type: "k8sProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 30
          retry: 3
          interval: 10
          probePollingInterval: 5
        k8sProbe/inputs:
          group: ""
          version: "v1"
          resource: "pods"
          namespace: "api-gateway"
          fieldSelector: "status.phase=Running"
          labelSelector: "app=api-gateway"
          operation: "present"
      
      - name: "service-recovery-time"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 5
          interval: 10
          probePollingInterval: 5
        httpProbe/inputs:
          url: "http://api-gateway.api-gateway.svc.cluster.local:8000/ready"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"