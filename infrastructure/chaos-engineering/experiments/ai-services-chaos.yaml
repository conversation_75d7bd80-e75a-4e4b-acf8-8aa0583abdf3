# AI Services Chaos Experiments
# Tests resilience of AI/ML services including content generation, market intelligence, etc.

---
# Content Generation Service Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: content-generation-chaos
  namespace: content-generation
  labels:
    app: content-generation
    chaos-type: ai-service
spec:
  appinfo:
    appns: 'content-generation'
    applabel: 'app=content-generation'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # AI Model failure simulation
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '60'
        - name: PODS_AFFECTED_PERC
          value: '30'
        - name: CHAOS_INTERVAL
          value: '20'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "ai-service-health"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 30
          retry: 3
          interval: 10
          probePollingInterval: 5
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "ai-generation-capability"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 120
          retry: 2
          interval: 60
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/api/generate/test"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"prompt": "Test content generation", "max_tokens": 100}'
              contentType: "application/json"
      
      - name: "fallback-mechanism-check"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 60
          retry: 1
          interval: 30
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/api/fallback/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Content Generation Memory Stress (AI models are memory intensive)
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: content-generation-memory-chaos
  namespace: content-generation
  labels:
    app: content-generation
    chaos-type: ai-memory-stress
spec:
  appinfo:
    appns: 'content-generation'
    applabel: 'app=content-generation'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  - name: pod-memory-hog
    spec:
      components:
        env:
        # Higher memory consumption for AI services
        - name: MEMORY_CONSUMPTION
          value: '2048'
        - name: MEMORY_PERCENTAGE
          value: '75'
        - name: TOTAL_CHAOS_DURATION
          value: '120'
        - name: PODS_AFFECTED_PERC
          value: '20'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "ai-model-loading"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 180  # AI models take time to load
          retry: 2
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/api/model/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "generation-quality-check"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 300
          retry: 1
          interval: 120
        httpProbe/inputs:
          url: "http://content-generation.content-generation.svc.cluster.local:8000/api/generate/quality-test"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"prompt": "Generate high-quality test content", "quality_threshold": 4.0}'
              contentType: "application/json"

---
# Market Intelligence Service Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: market-intelligence-chaos
  namespace: market-intelligence
  labels:
    app: market-intelligence
    chaos-type: data-service
spec:
  appinfo:
    appns: 'market-intelligence'
    applabel: 'app=market-intelligence'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Network chaos for external API dependencies
  - name: pod-network-latency
    spec:
      components:
        env:
        - name: NETWORK_LATENCY
          value: '500'  # Higher latency for external API calls
        - name: TOTAL_CHAOS_DURATION
          value: '90'
        - name: NETWORK_INTERFACE
          value: 'eth0'
        - name: PODS_AFFECTED_PERC
          value: '40'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "trend-analysis-capability"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 120
          retry: 3
          interval: 30
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://market-intelligence.market-intelligence.svc.cluster.local:8000/api/trends/analyze"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"keywords": ["test"], "timeframe": "1d"}'
              contentType: "application/json"
      
      - name: "cache-fallback-mechanism"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 30
        httpProbe/inputs:
          url: "http://market-intelligence.market-intelligence.svc.cluster.local:8000/api/cache/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "external-api-resilience"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 180
          retry: 2
          interval: 45
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://market-intelligence.market-intelligence.svc.cluster.local:8000/api/external/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Cover Designer Service Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: cover-designer-chaos
  namespace: cover-designer
  labels:
    app: cover-designer
    chaos-type: graphics-service
spec:
  appinfo:
    appns: 'cover-designer'
    applabel: 'app=cover-designer'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # CPU stress for graphics processing
  - name: pod-cpu-hog
    spec:
      components:
        env:
        - name: CPU_LOAD
          value: '90'  # High CPU for image processing
        - name: CPU_CORES
          value: '2'
        - name: TOTAL_CHAOS_DURATION
          value: '180'
        - name: PODS_AFFECTED_PERC
          value: '25'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "cover-generation-capability"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 300  # Cover generation can be slow
          retry: 2
          interval: 60
          probePollingInterval: 20
        httpProbe/inputs:
          url: "http://cover-designer.cover-designer.svc.cluster.local:8000/api/generate/cover"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"title": "Test Book", "genre": "fiction", "style": "modern"}'
              contentType: "application/json"
      
      - name: "graphics-processing-queue"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 3
          interval: 20
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://cover-designer.cover-designer.svc.cluster.local:8000/api/queue/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Publishing Service Chaos (Critical business process)
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: publishing-service-chaos
  namespace: publishing-service
  labels:
    app: publishing-service
    chaos-type: business-critical
spec:
  appinfo:
    appns: 'publishing-service'
    applabel: 'app=publishing-service'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Container kill for critical service testing
  - name: container-kill
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '90'
        - name: CHAOS_INTERVAL
          value: '30'
        - name: SIGNAL
          value: 'SIGTERM'  # Graceful termination first
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
        - name: TARGET_CONTAINER
          value: 'publishing-service'
      
      probe:
      - name: "publishing-pipeline-integrity"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 120
          retry: 3
          interval: 30
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/pipeline/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "kdp-connectivity-check"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 30
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/kdp/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "transaction-rollback-capability"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 180
          retry: 1
          interval: 60
        httpProbe/inputs:
          url: "http://publishing-service.publishing-service.svc.cluster.local:8000/api/transactions/rollback-test"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"test_mode": true}'
              contentType: "application/json"

---
# Sales Monitor Service Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: sales-monitor-chaos
  namespace: sales-monitor
  labels:
    app: sales-monitor
    chaos-type: analytics-service
spec:
  appinfo:
    appns: 'sales-monitor'
    applabel: 'app=sales-monitor'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Pod failure for analytics service
  - name: pod-delete
    spec:
      components:
        env:
        - name: TOTAL_CHAOS_DURATION
          value: '45'
        - name: PODS_AFFECTED_PERC
          value: '35'
        - name: CHAOS_INTERVAL
          value: '15'
        - name: FORCE
          value: 'false'
      
      probe:
      - name: "sales-data-collection"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 90
          retry: 3
          interval: 20
          probePollingInterval: 10
        httpProbe/inputs:
          url: "http://sales-monitor.sales-monitor.svc.cluster.local:8000/api/sales/collect"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"test_mode": true}'
              contentType: "application/json"
      
      - name: "analytics-dashboard-availability"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 60
          retry: 2
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://sales-monitor.sales-monitor.svc.cluster.local:8000/api/dashboard/health"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "data-persistence-check"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 120
          retry: 2
          interval: 60
        httpProbe/inputs:
          url: "http://sales-monitor.sales-monitor.svc.cluster.local:8000/api/data/integrity"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"

---
# Multimodal Generator Service Chaos
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: multimodal-generator-chaos
  namespace: multimodal-generator
  labels:
    app: multimodal-generator
    chaos-type: advanced-ai-service
spec:
  appinfo:
    appns: 'multimodal-generator'
    applabel: 'app=multimodal-generator'
    appkind: 'deployment'
  
  chaosServiceAccount: litmus
  monitoring: true
  jobCleanUpPolicy: 'delete'
  engineState: 'active'
  
  experiments:
  # Combined CPU and Memory stress for multimodal AI
  - name: pod-cpu-hog
    spec:
      components:
        env:
        - name: CPU_LOAD
          value: '85'
        - name: CPU_CORES
          value: '2'
        - name: TOTAL_CHAOS_DURATION
          value: '150'
        - name: PODS_AFFECTED_PERC
          value: '20'
        - name: CONTAINER_RUNTIME
          value: 'containerd'
        - name: SOCKET_PATH
          value: '/var/run/containerd/containerd.sock'
      
      probe:
      - name: "multimodal-generation-capability"
        type: "httpProbe"
        mode: "Edge"
        runProperties:
          probeTimeout: 600  # Multimodal generation can be very slow
          retry: 1
          interval: 300
        httpProbe/inputs:
          url: "http://multimodal-generator.multimodal-generator.svc.cluster.local:8000/api/generate/multimodal"
          insecureSkipTLS: true
          method:
            post:
              criteria: ==
              responseCode: "200"
              body: '{"text_prompt": "Test content", "image_style": "modern", "audio_narration": false}'
              contentType: "application/json"
      
      - name: "model-ensemble-health"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 120
          retry: 2
          interval: 60
          probePollingInterval: 20
        httpProbe/inputs:
          url: "http://multimodal-generator.multimodal-generator.svc.cluster.local:8000/api/models/ensemble/status"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"
      
      - name: "resource-optimization-check"
        type: "httpProbe"
        mode: "Continuous"
        runProperties:
          probeTimeout: 90
          retry: 3
          interval: 30
          probePollingInterval: 15
        httpProbe/inputs:
          url: "http://multimodal-generator.multimodal-generator.svc.cluster.local:8000/api/resources/optimization"
          insecureSkipTLS: true
          method:
            get:
              criteria: ==
              responseCode: "200"