# Chaos Engineering Framework for Publish AI

This directory contains the chaos engineering testing framework for the Publish AI microservices platform. Chaos engineering helps build confidence in the system's capability to withstand turbulent conditions in production.

## Overview

Chaos engineering is the discipline of experimenting on a system in order to build confidence in the system's capability to withstand turbulent conditions in production. For the Publish AI platform, we implement controlled chaos experiments to test:

- Service resilience and fault tolerance
- Graceful degradation under failures
- Recovery mechanisms and self-healing
- Circuit breaker effectiveness
- Load balancer behavior during failures
- Data consistency during network partitions

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Chaos Engineering Stack                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌──────────────┐    ┌──────────────┐  │
│  │   Litmus    │    │  Chaos Mesh  │    │   Custom     │  │
│  │   Chaos     │    │  Experiments │    │ Experiments  │  │
│  └──────┬──────┘    └──────┬───────┘    └──────┬───────┘  │
│         │                   │                    │          │
│  ┌──────┴─────────────────┴────────────────────┴───────┐  │
│  │              Chaos Orchestrator                      │  │
│  │        (Experiment Scheduling & Control)             │  │
│  └──────────────────────┬───────────────────────────────┘  │
│                         │                                   │
│  ┌──────────────────────┴───────────────────────────────┐  │
│  │                Target Services                        │  │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │  │
│  │  │ API     │  │ Content │  │ Market  │  │Publishing│ │  │
│  │  │ Gateway │  │   Gen   │  │ Intel   │  │ Service │ │  │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │  │
│  └───────────────────────────────────────────────────────┘  │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐  │
│  │           Observability & Analysis                   │  │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌────────┐ │  │
│  │  │Prometheus│  │ Grafana │  │ Jaeger  │  │ Alerts │ │  │
│  │  └─────────┘  └─────────┘  └─────────┘  └────────┘ │  │
│  └─────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. Litmus Chaos
- Kubernetes-native chaos engineering framework
- Pre-built experiments for common failure scenarios
- ChaosHub integration for experiment templates
- Workflow automation and scheduling

### 2. Chaos Mesh
- Cloud-native chaos engineering platform
- Fine-grained fault injection capabilities
- Multi-cluster chaos experiments
- Visual chaos workflow designer

### 3. Custom Experiments
- AI service-specific chaos tests
- Cost simulation experiments
- Quality degradation scenarios
- Publishing workflow disruptions

### 4. Safety Controls
- Automatic experiment termination
- Health check integration
- Rollback mechanisms
- Blast radius limitation

## Experiment Categories

### Infrastructure Chaos
- **Pod Failures**: Random pod termination
- **Node Failures**: Node shutdown simulation
- **Network Chaos**: Latency, packet loss, bandwidth limitation
- **Resource Stress**: CPU, memory, disk stress tests
- **Clock Skew**: Time manipulation experiments

### Application Chaos
- **Service Failures**: Specific microservice crashes
- **API Errors**: HTTP error injection
- **Database Chaos**: Connection failures, slow queries
- **Message Queue Chaos**: Kafka partition failures
- **Cache Chaos**: Redis connection issues

### AI-Specific Chaos
- **Model Loading Failures**: AI model unavailability
- **Token Limit Exceeded**: API rate limiting simulation
- **Quality Degradation**: Forced low-quality outputs
- **Cost Explosion**: Unexpected cost increases
- **Long Generation Times**: Timeout scenarios

### Business Logic Chaos
- **Publishing Failures**: KDP API errors
- **Payment Processing**: Transaction failures
- **User Authentication**: Auth service disruptions
- **Data Corruption**: Metadata inconsistencies

## Safety Mechanisms

### 1. Blast Radius Control
```yaml
# Example: Limit chaos to specific namespaces
selector:
  namespaces:
    - content-generation
  labelSelectors:
    environment: staging
    chaos-enabled: "true"
```

### 2. Automatic Rollback
- Health check failures trigger immediate rollback
- SLO violations stop experiments
- Manual emergency stop available

### 3. Gradual Rollout
- Start with small percentage of pods
- Increase chaos intensity gradually
- Monitor impact continuously

### 4. Time Windows
- Run experiments during low-traffic periods
- Avoid critical business hours
- Scheduled maintenance windows

## Experiment Types

### 1. Steady State Experiments
Test normal operating conditions:
- Baseline performance metrics
- Success rate tracking
- Resource utilization monitoring

### 2. Hypothesis Experiments
Test specific failure scenarios:
- "System handles 50% pod failures"
- "Circuit breakers prevent cascading failures"
- "Graceful degradation during API failures"

### 3. Game Day Experiments
Simulate major outages:
- Multi-service failures
- Infrastructure-wide issues
- Disaster recovery scenarios

### 4. Continuous Experiments
Ongoing low-level chaos:
- Random 1% failure rate
- Intermittent network issues
- Periodic resource constraints

## Running Experiments

### Prerequisites
- Kubernetes cluster with Istio service mesh
- Monitoring stack (Prometheus, Grafana, Jaeger)
- Chaos engineering operators installed
- Proper RBAC permissions

### Quick Start
```bash
# Deploy chaos engineering stack
./scripts/deploy-chaos-stack.sh

# Run basic experiments
./scripts/run-experiment.sh --type pod-failure --service api-gateway

# Run comprehensive test suite
./scripts/chaos-test-suite.sh --level basic

# Generate chaos report
./scripts/generate-chaos-report.sh
```

### Experiment Workflow
1. **Define Hypothesis**: What should happen during failure?
2. **Select Target**: Which services to test?
3. **Configure Chaos**: What type of failure to inject?
4. **Set Safety Limits**: Maximum blast radius and duration
5. **Execute Experiment**: Run with monitoring
6. **Analyze Results**: Compare with hypothesis
7. **Document Findings**: Update runbooks and procedures

## Monitoring During Chaos

### Key Metrics to Watch
- **Service Health**: Response time, error rate, throughput
- **Resource Usage**: CPU, memory, network, disk
- **Business Metrics**: Publishing success, content quality
- **Recovery Time**: MTTR for each failure type

### Dashboards
- **Chaos Overview**: Current experiments and impact
- **Service Health**: Real-time service status
- **SLO Tracking**: SLI/SLO compliance during chaos
- **Recovery Analysis**: Time to recover from failures

### Alerts
- Experiment started/completed
- SLO violations during chaos
- Unexpected service impacts
- Safety threshold breaches

## Best Practices

### 1. Start Small
- Begin with non-critical services
- Use minimal blast radius
- Short duration experiments
- Gradually increase complexity

### 2. Communication
- Notify teams before experiments
- Document expected impacts
- Share results and learnings
- Update runbooks based on findings

### 3. Automation
- Automate experiment execution
- Integrate with CI/CD pipeline
- Scheduled chaos runs
- Automated result analysis

### 4. Learning Culture
- Blameless post-mortems
- Share chaos engineering results
- Encourage hypothesis testing
- Celebrate discovered weaknesses

## Experiment Catalog

### Basic Experiments
1. **pod-failure**: Random pod termination
2. **network-delay**: Add network latency
3. **cpu-stress**: High CPU usage
4. **memory-stress**: Memory pressure
5. **disk-stress**: Disk I/O stress

### Advanced Experiments
1. **cascading-failure**: Multi-service failure chain
2. **split-brain**: Network partition simulation
3. **time-travel**: Clock skew testing
4. **api-storm**: Sudden traffic spike
5. **slow-drain**: Gradual resource exhaustion

### AI-Specific Experiments
1. **model-unavailable**: AI model loading failure
2. **token-exhaustion**: API limit reached
3. **quality-degradation**: Force low-quality outputs
4. **generation-timeout**: Long processing times
5. **cost-spike**: Unexpected cost increase

## Integration with CI/CD

### Pre-Production Chaos
```yaml
# .gitlab-ci.yml or similar
chaos-test:
  stage: chaos
  script:
    - ./scripts/run-experiment.sh --env staging --type basic-suite
    - ./scripts/verify-slos.sh --threshold 99.9
  only:
    - main
```

### Production Chaos
- Requires approval
- Limited blast radius
- Automated rollback
- Comprehensive monitoring

## Troubleshooting

### Common Issues
1. **Experiments not starting**: Check RBAC permissions
2. **Unexpected impacts**: Review blast radius configuration
3. **Monitoring gaps**: Ensure all services have metrics
4. **Recovery failures**: Verify circuit breakers and retries

### Debug Commands
```bash
# Check chaos operator status
kubectl get pods -n litmus

# View active experiments
kubectl get chaosengines -A

# Check experiment logs
kubectl logs -n litmus <chaos-pod-name>

# Emergency stop all experiments
./scripts/emergency-stop-chaos.sh
```

## Results Documentation

### Experiment Reports
Each experiment generates a report including:
- Hypothesis and actual results
- Metrics and graphs
- Discovered issues
- Recommended improvements
- Follow-up actions

### Knowledge Base
- Failure scenarios catalog
- Recovery procedures
- Service dependencies
- Weak points identified

## Future Enhancements

### Planned Features
1. **AI-Powered Chaos**: ML-driven experiment selection
2. **Cost Chaos**: Simulate budget constraints
3. **Security Chaos**: Breach simulation
4. **Compliance Chaos**: Regulatory scenarios
5. **Multi-Region Chaos**: Geographic failures

### Research Areas
- Chaos engineering for ML pipelines
- Quantum computing chaos scenarios
- Edge computing failure modes
- Blockchain consistency testing

## Contributing

To add new chaos experiments:
1. Create experiment definition in `experiments/`
2. Add safety controls and limits
3. Document expected behavior
4. Create monitoring dashboard
5. Add to experiment catalog
6. Submit PR with test results

## Resources

### Documentation
- [Litmus Chaos Documentation](https://docs.litmuschaos.io/)
- [Chaos Mesh Documentation](https://chaos-mesh.org/docs/)
- [Principles of Chaos Engineering](https://principlesofchaos.org/)

### Books
- "Chaos Engineering" by Casey Rosenthal & Nora Jones
- "Learning Chaos Engineering" by Russ Miles

### Tools
- [Gremlin](https://www.gremlin.com/)
- [Chaos Toolkit](https://chaostoolkit.org/)
- [PowerfulSeal](https://github.com/powerfulseal/powerfulseal)

Remember: The goal of chaos engineering is not to break things, but to learn how things break and build more resilient systems.