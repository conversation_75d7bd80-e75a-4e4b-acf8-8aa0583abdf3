apiVersion: v1
kind: Namespace
metadata:
  name: litmus
  labels:
    name: litmus

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: litmus
  namespace: litmus
  labels:
    app: litmus

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: litmus
  labels:
    app: litmus
rules:
  # Litmus CRDs permissions
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "pods/log", "pods/exec", "serviceaccounts"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "patch", "update"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
- apiGroups: ["litmuschaos.io"]
  resources: ["chaosengines", "chaosexperiments", "chaosresults"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["create", "get", "list", "update"]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["rolebindings", "roles", "clusterroles", "clusterrolebindings"]
  verbs: ["create", "delete", "get", "list"]
- apiGroups: ["policy"]
  resources: ["podsecuritypolicies"]
  verbs: ["use"]
- apiGroups: ["extensions"]
  resources: ["deployments", "ingresses"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: litmus
  labels:
    app: litmus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: litmus
subjects:
- kind: ServiceAccount
  name: litmus
  namespace: litmus

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chaos-operator-ce
  namespace: litmus
  labels:
    app: chaos-operator
spec:
  replicas: 1
  selector:
    matchLabels:
      name: chaos-operator
  template:
    metadata:
      labels:
        name: chaos-operator
        app: chaos-operator
    spec:
      serviceAccountName: litmus
      securityContext:
        runAsUser: 2000
        runAsGroup: 2000
        fsGroup: 2000
      containers:
      - name: chaos-operator
        image: litmuschaos/chaos-operator:3.8.0
        command:
        - chaos-operator
        imagePullPolicy: Always
        env:
        - name: CHAOS_RUNNER_IMAGE
          value: "litmuschaos/chaos-runner:3.8.0"
        - name: WATCH_NAMESPACE
          value: ""
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: OPERATOR_NAME
          value: "chaos-operator"
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        resources:
          requests:
            cpu: 125m
            memory: 300Mi
          limits:
            cpu: 500m
            memory: 500Mi
        livenessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: chaos-operator-metrics
  namespace: litmus
  labels:
    app: chaos-operator
spec:
  ports:
  - name: http-metrics
    port: 8080
    protocol: TCP
    targetPort: 8080
  selector:
    name: chaos-operator

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: chaos-operator-metrics
  namespace: litmus
  labels:
    app: chaos-operator
spec:
  selector:
    matchLabels:
      app: chaos-operator
  endpoints:
  - port: http-metrics
    interval: 30s
    path: /metrics

---
# ChaosEngine CRD
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: chaosengines.litmuschaos.io
spec:
  group: litmuschaos.io
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            properties:
              appinfo:
                type: object
                properties:
                  appns:
                    type: string
                  applabel:
                    type: string
                  appkind:
                    type: string
              chaosServiceAccount:
                type: string
              monitoring:
                type: boolean
              jobCleanUpPolicy:
                type: string
                pattern: ^(delete|retain)$
              annotationCheck:
                type: string
                pattern: ^(true|false)$
              engineState:
                type: string
                pattern: ^(active|stop)$
              auxiliaryAppInfo:
                type: string
              components:
                type: object
                properties:
                  runner:
                    type: object
                    properties:
                      image:
                        type: string
                      type:
                        type: string
                        pattern: ^(go)$
                      runnerAnnotation:
                        type: object
                        additionalProperties:
                          type: string
              experiments:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                    spec:
                      type: object
                      properties:
                        components:
                          type: object
                          properties:
                            env:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                            configMaps:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                  mountPath:
                                    type: string
                            secrets:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                  mountPath:
                                    type: string
                            experimentImage:
                              type: string
                        probe:
                          type: array
                          items:
                            type: object
          status:
            type: object
    additionalPrinterColumns:
    - name: EngineStatus
      type: string
      description: The status of Chaos Engine
      jsonPath: .status.engineStatus
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: chaosengines
    singular: chaosengine
    kind: ChaosEngine
    shortNames:
    - ce

---
# ChaosExperiment CRD
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: chaosexperiments.litmuschaos.io
spec:
  group: litmuschaos.io
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            properties:
              definition:
                type: object
                properties:
                  scope:
                    type: string
                    pattern: ^(Namespaced|Cluster)$
                  permissions:
                    type: array
                    items:
                      type: object
                      properties:
                        apiGroups:
                          type: array
                          items:
                            type: string
                        resources:
                          type: array
                          items:
                            type: string
                        verbs:
                          type: array
                          items:
                            type: string
                  image:
                    type: string
                  imagePullPolicy:
                    type: string
                  args:
                    type: array
                    items:
                      type: string
                  command:
                    type: array
                    items:
                      type: string
                  env:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                  labels:
                    type: object
                    additionalProperties:
                      type: string
                  configMaps:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        mountPath:
                          type: string
                  secrets:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        mountPath:
                          type: string
                  hostFileVolumes:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        mountPath:
                          type: string
                        nodePath:
                          type: string
          status:
            type: object
    additionalPrinterColumns:
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: chaosexperiments
    singular: chaosexperiment
    kind: ChaosExperiment
    shortNames:
    - ce

---
# ChaosResult CRD
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: chaosresults.litmuschaos.io
spec:
  group: litmuschaos.io
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            properties:
              engine:
                type: string
              experiment:
                type: string
          status:
            type: object
            properties:
              experimentStatus:
                type: object
                properties:
                  phase:
                    type: string
                  verdict:
                    type: string
              probeSuccessPercentage:
                type: string
              history:
                type: object
                properties:
                  passedRuns:
                    type: integer
                  failedRuns:
                    type: integer
                  stoppedRuns:
                    type: integer
                  targets:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        kind:
                          type: string
    additionalPrinterColumns:
    - name: Engine
      type: string
      description: The engine associated with the experiment
      jsonPath: .spec.engine
    - name: Experiment
      type: string
      description: The name of the experiment
      jsonPath: .spec.experiment
    - name: Verdict
      type: string
      description: The verdict of Chaos Result
      jsonPath: .status.experimentStatus.verdict
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: chaosresults
    singular: chaosresult
    kind: ChaosResult
    shortNames:
    - cr

---
# Chaos Experiments - Pod Delete
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosExperiment
metadata:
  name: pod-delete
  namespace: litmus
  labels:
    name: pod-delete
    app.kubernetes.io/part-of: litmus
    app.kubernetes.io/component: chaosexperiment
    app.kubernetes.io/version: 3.8.0
spec:
  definition:
    scope: Namespaced
    permissions:
      - apiGroups: [""]
        resources: ["pods"]
        verbs: ["create","delete","get","list","patch","update", "deletecollection"]
      - apiGroups: [""]
        resources: ["events"]
        verbs: ["create","get","list","patch","update"]
      - apiGroups: [""]
        resources: ["configmaps"]
        verbs: ["get","list"]
      - apiGroups: [""]
        resources: ["pods/log"]
        verbs: ["get","list","watch"]  
      - apiGroups: [""]
        resources: ["pods/exec"]
        verbs: ["get","list","create"]
      - apiGroups: ["apps"]
        resources: ["deployments","statefulsets","replicasets","daemonsets"]
        verbs: ["list","get"]
      - apiGroups: ["apps"]
        resources: ["deployments/status","statefulsets/status","replicasets/status","daemonsets/status"]
        verbs: ["get"]
      - apiGroups: ["litmuschaos.io"]
        resources: ["chaosengines","chaosexperiments","chaosresults"]
        verbs: ["create","list","get","patch","update","delete"]
      - apiGroups: ["batch"]
        resources: ["jobs"]
        verbs: ["create","list","get","delete","deletecollection"]
    image: "litmuschaos/go-runner:3.8.0"
    imagePullPolicy: Always
    args:
    - -c
    - ./experiments -name pod-delete
    command:
    - /bin/bash
    env:
    - name: TOTAL_CHAOS_DURATION
      value: '15'
    - name: RAMP_TIME
      value: ''
    - name: FORCE
      value: 'true'
    - name: CHAOS_INTERVAL
      value: '5'
    - name: PODS_AFFECTED_PERC
      value: ''
    - name: TARGET_CONTAINER
      value: ''
    - name: TARGET_PODS
      value: ''
    - name: DEFAULT_HEALTH_CHECK
      value: 'false'
    - name: NODE_LABEL
      value: ''
    - name: SEQUENCE
      value: 'parallel'
    labels:
      name: pod-delete
      app.kubernetes.io/part-of: litmus
      app.kubernetes.io/component: experiment-job
      app.kubernetes.io/version: 3.8.0

---
# Chaos Experiments - Container Kill
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosExperiment
metadata:
  name: container-kill
  namespace: litmus
  labels:
    name: container-kill
    app.kubernetes.io/part-of: litmus
    app.kubernetes.io/component: chaosexperiment
    app.kubernetes.io/version: 3.8.0
spec:
  definition:
    scope: Namespaced
    permissions:
      - apiGroups: [""]
        resources: ["pods"]
        verbs: ["create","delete","get","list","patch","update", "deletecollection"]
      - apiGroups: [""]
        resources: ["events"]
        verbs: ["create","get","list","patch","update"]
      - apiGroups: [""]
        resources: ["configmaps"]
        verbs: ["get","list"]
      - apiGroups: [""]
        resources: ["pods/log"]
        verbs: ["get","list","watch"]  
      - apiGroups: [""]
        resources: ["pods/exec"]
        verbs: ["get","list","create"]
      - apiGroups: ["apps"]
        resources: ["deployments","statefulsets","replicasets","daemonsets"]
        verbs: ["list","get"]
      - apiGroups: ["apps"]
        resources: ["deployments/status","statefulsets/status","replicasets/status","daemonsets/status"]
        verbs: ["get"]
      - apiGroups: ["litmuschaos.io"]
        resources: ["chaosengines","chaosexperiments","chaosresults"]
        verbs: ["create","list","get","patch","update","delete"]
      - apiGroups: ["batch"]
        resources: ["jobs"]
        verbs: ["create","list","get","delete","deletecollection"]
    image: "litmuschaos/go-runner:3.8.0"
    imagePullPolicy: Always
    args:
    - -c
    - ./experiments -name container-kill
    command:
    - /bin/bash
    env:
    - name: TOTAL_CHAOS_DURATION
      value: '20'
    - name: CHAOS_INTERVAL
      value: '10'
    - name: TARGET_CONTAINER
      value: ''
    - name: SIGNAL
      value: 'SIGKILL'
    - name: SOCKET_PATH
      value: '/var/run/docker.sock'
    - name: CONTAINER_RUNTIME
      value: 'containerd'
    - name: DEFAULT_HEALTH_CHECK
      value: 'false'
    - name: TARGET_PODS
      value: ''
    - name: NODE_LABEL
      value: ''
    - name: PODS_AFFECTED_PERC
      value: ''
    - name: RAMP_TIME
      value: ''
    - name: SEQUENCE
      value: 'parallel'
    labels:
      name: container-kill
      app.kubernetes.io/part-of: litmus
      app.kubernetes.io/component: experiment-job
      app.kubernetes.io/version: 3.8.0

---
# Chaos Experiments - Pod Network Latency
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosExperiment
metadata:
  name: pod-network-latency
  namespace: litmus
  labels:
    name: pod-network-latency
    app.kubernetes.io/part-of: litmus
    app.kubernetes.io/component: chaosexperiment
    app.kubernetes.io/version: 3.8.0
spec:
  definition:
    scope: Namespaced
    permissions:
      - apiGroups: [""]
        resources: ["pods"]
        verbs: ["create","delete","get","list","patch","update", "deletecollection"]
      - apiGroups: [""]
        resources: ["events"]
        verbs: ["create","get","list","patch","update"]
      - apiGroups: [""]
        resources: ["configmaps"]
        verbs: ["get","list"]
      - apiGroups: [""]
        resources: ["pods/log"]
        verbs: ["get","list","watch"]  
      - apiGroups: [""]
        resources: ["pods/exec"]
        verbs: ["get","list","create"]
      - apiGroups: ["apps"]
        resources: ["deployments","statefulsets","replicasets","daemonsets"]
        verbs: ["list","get"]
      - apiGroups: ["apps"]
        resources: ["deployments/status","statefulsets/status","replicasets/status","daemonsets/status"]
        verbs: ["get"]
      - apiGroups: ["litmuschaos.io"]
        resources: ["chaosengines","chaosexperiments","chaosresults"]
        verbs: ["create","list","get","patch","update","delete"]
      - apiGroups: ["batch"]
        resources: ["jobs"]
        verbs: ["create","list","get","delete","deletecollection"]
    image: "litmuschaos/go-runner:3.8.0"
    imagePullPolicy: Always
    args:
    - -c
    - ./experiments -name pod-network-latency
    command:
    - /bin/bash
    env:
    - name: TARGET_CONTAINER
      value: ''
    - name: NETWORK_INTERFACE
      value: 'eth0'
    - name: TC_IMAGE
      value: 'gaiadocker/iproute2'
    - name: NETWORK_LATENCY
      value: '60000'
    - name: TOTAL_CHAOS_DURATION
      value: '60'
    - name: RAMP_TIME
      value: ''
    - name: PODS_AFFECTED_PERC
      value: ''
    - name: TARGET_PODS
      value: ''
    - name: NODE_LABEL
      value: ''
    - name: JITTER
      value: '0'
    - name: CONTAINER_RUNTIME
      value: 'containerd'
    - name: SOCKET_PATH
      value: '/var/run/containerd/containerd.sock'
    - name: DESTINATION_IPS
      value: ''
    - name: DESTINATION_HOSTS
      value: ''
    - name: DEFAULT_HEALTH_CHECK
      value: 'false'
    - name: SEQUENCE
      value: 'parallel'
    labels:
      name: pod-network-latency
      app.kubernetes.io/part-of: litmus
      app.kubernetes.io/component: experiment-job
      app.kubernetes.io/version: 3.8.0

---
# Chaos Experiments - Pod CPU Hog
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosExperiment
metadata:
  name: pod-cpu-hog
  namespace: litmus
  labels:
    name: pod-cpu-hog
    app.kubernetes.io/part-of: litmus
    app.kubernetes.io/component: chaosexperiment
    app.kubernetes.io/version: 3.8.0
spec:
  definition:
    scope: Namespaced
    permissions:
      - apiGroups: [""]
        resources: ["pods"]
        verbs: ["create","delete","get","list","patch","update", "deletecollection"]
      - apiGroups: [""]
        resources: ["events"]
        verbs: ["create","get","list","patch","update"]
      - apiGroups: [""]
        resources: ["configmaps"]
        verbs: ["get","list"]
      - apiGroups: [""]
        resources: ["pods/log"]
        verbs: ["get","list","watch"]  
      - apiGroups: [""]
        resources: ["pods/exec"]
        verbs: ["get","list","create"]
      - apiGroups: ["apps"]
        resources: ["deployments","statefulsets","replicasets","daemonsets"]
        verbs: ["list","get"]
      - apiGroups: ["apps"]
        resources: ["deployments/status","statefulsets/status","replicasets/status","daemonsets/status"]
        verbs: ["get"]
      - apiGroups: ["litmuschaos.io"]
        resources: ["chaosengines","chaosexperiments","chaosresults"]
        verbs: ["create","list","get","patch","update","delete"]
      - apiGroups: ["batch"]
        resources: ["jobs"]
        verbs: ["create","list","get","delete","deletecollection"]
    image: "litmuschaos/go-runner:3.8.0"
    imagePullPolicy: Always
    args:
    - -c
    - ./experiments -name pod-cpu-hog
    command:
    - /bin/bash
    env:
    - name: TOTAL_CHAOS_DURATION
      value: '60'
    - name: CPU_CORES
      value: '1'
    - name: CPU_LOAD
      value: '100'
    - name: PODS_AFFECTED_PERC
      value: ''
    - name: RAMP_TIME
      value: ''
    - name: TARGET_PODS
      value: ''
    - name: TARGET_CONTAINER
      value: ''
    - name: NODE_LABEL
      value: ''
    - name: CONTAINER_RUNTIME
      value: 'containerd'
    - name: SOCKET_PATH
      value: '/var/run/containerd/containerd.sock'
    - name: DEFAULT_HEALTH_CHECK
      value: 'false'
    - name: SEQUENCE
      value: 'parallel'
    labels:
      name: pod-cpu-hog
      app.kubernetes.io/part-of: litmus
      app.kubernetes.io/component: experiment-job
      app.kubernetes.io/version: 3.8.0

---
# Chaos Experiments - Pod Memory Hog
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosExperiment
metadata:
  name: pod-memory-hog
  namespace: litmus
  labels:
    name: pod-memory-hog
    app.kubernetes.io/part-of: litmus
    app.kubernetes.io/component: chaosexperiment
    app.kubernetes.io/version: 3.8.0
spec:
  definition:
    scope: Namespaced
    permissions:
      - apiGroups: [""]
        resources: ["pods"]
        verbs: ["create","delete","get","list","patch","update", "deletecollection"]
      - apiGroups: [""]
        resources: ["events"]
        verbs: ["create","get","list","patch","update"]
      - apiGroups: [""]
        resources: ["configmaps"]
        verbs: ["get","list"]
      - apiGroups: [""]
        resources: ["pods/log"]
        verbs: ["get","list","watch"]  
      - apiGroups: [""]
        resources: ["pods/exec"]
        verbs: ["get","list","create"]
      - apiGroups: ["apps"]
        resources: ["deployments","statefulsets","replicasets","daemonsets"]
        verbs: ["list","get"]
      - apiGroups: ["apps"]
        resources: ["deployments/status","statefulsets/status","replicasets/status","daemonsets/status"]
        verbs: ["get"]
      - apiGroups: ["litmuschaos.io"]
        resources: ["chaosengines","chaosexperiments","chaosresults"]
        verbs: ["create","list","get","patch","update","delete"]
      - apiGroups: ["batch"]
        resources: ["jobs"]
        verbs: ["create","list","get","delete","deletecollection"]
    image: "litmuschaos/go-runner:3.8.0"
    imagePullPolicy: Always
    args:
    - -c
    - ./experiments -name pod-memory-hog
    command:
    - /bin/bash
    env:
    - name: TOTAL_CHAOS_DURATION
      value: '60'
    - name: MEMORY_CONSUMPTION
      value: '500'
    - name: MEMORY_PERCENTAGE
      value: '80'
    - name: PODS_AFFECTED_PERC
      value: ''
    - name: RAMP_TIME
      value: ''
    - name: TARGET_PODS
      value: ''
    - name: TARGET_CONTAINER
      value: ''
    - name: NODE_LABEL
      value: ''
    - name: CONTAINER_RUNTIME
      value: 'containerd'
    - name: SOCKET_PATH
      value: '/var/run/containerd/containerd.sock'
    - name: DEFAULT_HEALTH_CHECK
      value: 'false'
    - name: SEQUENCE
      value: 'parallel'
    labels:
      name: pod-memory-hog
      app.kubernetes.io/part-of: litmus
      app.kubernetes.io/component: experiment-job
      app.kubernetes.io/version: 3.8.0