# Global CDN and Edge Computing Configuration
# Multi-region deployment with edge caching and geo-distributed content delivery

apiVersion: v1
kind: Namespace
metadata:
  name: global-cdn
  labels:
    name: global-cdn
    project: publish-ai
    component: edge-computing

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cdn-config
  namespace: global-cdn
  labels:
    app: global-cdn
    component: config
data:
  # Global CDN Configuration
  CDN_PROVIDER: "cloudflare"
  EDGE_CACHING_ENABLED: "true"
  CACHE_TTL_STATIC: "31536000"  # 1 year for static assets
  CACHE_TTL_DYNAMIC: "300"      # 5 minutes for dynamic content
  CACHE_TTL_API: "60"          # 1 minute for API responses
  
  # Edge Computing Settings
  EDGE_FUNCTIONS_ENABLED: "true"
  EDGE_AUTHENTICATION: "true"
  EDGE_RATE_LIMITING: "true"
  EDGE_COMPRESSION: "true"
  
  # Geographic Distribution
  PRIMARY_REGION: "us-east-1"
  SECONDARY_REGIONS: "us-west-2,eu-west-1,ap-southeast-1"
  EDGE_LOCATIONS: "global"
  
  # Performance Optimization
  HTTP2_ENABLED: "true"
  HTTP3_ENABLED: "true"
  BROTLI_COMPRESSION: "true"
  GZIP_COMPRESSION: "true"
  IMAGE_OPTIMIZATION: "true"
  
  # Security Settings
  WAF_ENABLED: "true"
  DDOS_PROTECTION: "true"
  BOT_PROTECTION: "true"
  SSL_MODE: "strict"
  HSTS_ENABLED: "true"
  
  # Analytics and Monitoring
  REAL_TIME_ANALYTICS: "true"
  PERFORMANCE_MONITORING: "true"
  SECURITY_ANALYTICS: "true"
  CUSTOM_ANALYTICS: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: cdn-secrets
  namespace: global-cdn
  labels:
    app: global-cdn
    component: secrets
type: Opaque
stringData:
  # CDN Provider Credentials
  CLOUDFLARE_API_TOKEN: "${CLOUDFLARE_API_TOKEN}"
  CLOUDFLARE_ZONE_ID: "${CLOUDFLARE_ZONE_ID}"
  CLOUDFLARE_ACCOUNT_ID: "${CLOUDFLARE_ACCOUNT_ID}"
  
  # AWS CloudFront (Backup CDN)
  AWS_ACCESS_KEY_ID: "${AWS_ACCESS_KEY_ID}"
  AWS_SECRET_ACCESS_KEY: "${AWS_SECRET_ACCESS_KEY}"
  AWS_CLOUDFRONT_DISTRIBUTION_ID: "${AWS_CLOUDFRONT_DISTRIBUTION_ID}"
  
  # Certificate Management
  SSL_CERTIFICATE: "${SSL_CERTIFICATE}"
  SSL_PRIVATE_KEY: "${SSL_PRIVATE_KEY}"
  
  # Edge Authentication
  EDGE_AUTH_SECRET: "${EDGE_AUTH_SECRET}"
  JWT_SIGNING_KEY: "${JWT_SIGNING_KEY}"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-cache-manager
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    version: v1
    component: cache-management
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: edge-cache-manager
  template:
    metadata:
      labels:
        app: edge-cache-manager
        tier: edge
        version: v1
        component: cache-management
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: edge-cache-manager
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      containers:
      - name: edge-cache-manager
        image: ghcr.io/publishai/edge-cache-manager:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8080
          protocol: TCP
        - name: management
          containerPort: 9000
          protocol: TCP
        
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        
        envFrom:
        - configMapRef:
            name: cdn-config
        - secretRef:
            name: cdn-secrets
        
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
            ephemeral-storage: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 5Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /startup
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        volumeMounts:
        - name: cache-storage
          mountPath: /app/cache
        - name: temp-storage
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
      
      volumes:
      - name: cache-storage
        persistentVolumeClaim:
          claimName: edge-cache-pvc
      - name: temp-storage
        emptyDir:
          sizeLimit: 2Gi
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      nodeSelector:
        kubernetes.io/arch: amd64
        node.kubernetes.io/instance-type: compute-optimized
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: [edge-cache-manager]
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: edge-cache-manager
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  sessionAffinity: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: metrics
    protocol: TCP
  - name: management
    port: 9000
    targetPort: management
    protocol: TCP
  selector:
    app: edge-cache-manager

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: edge-cache-pvc
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: edge-cache-manager-hpa
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: edge-cache-manager
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: edge-cache-manager
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: global-cdn
  name: edge-cache-manager
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: edge-cache-manager
  namespace: global-cdn
subjects:
- kind: ServiceAccount
  name: edge-cache-manager
  namespace: global-cdn
roleRef:
  kind: Role
  name: edge-cache-manager
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: edge-cache-network-policy
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
spec:
  podSelector:
    matchLabels:
      app: edge-cache-manager
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from load balancer
  - from: []
    ports:
    - protocol: TCP
      port: 8000
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS to CDN providers
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow HTTP to internal services
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: edge-cache-manager-metrics
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: edge-cache-manager
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: edge-cache-alerts
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
    prometheus: kube-prometheus
spec:
  groups:
  - name: edge-cache.rules
    rules:
    - alert: EdgeCacheHighLatency
      expr: edge_cache_response_time_p95 > 500
      for: 5m
      labels:
        severity: warning
        service: edge-cache-manager
        component: cache
      annotations:
        summary: "High edge cache latency"
        description: "Edge cache P95 latency is {{ $value }}ms"
    
    - alert: EdgeCacheLowHitRate
      expr: edge_cache_hit_rate < 0.8
      for: 10m
      labels:
        severity: warning
        service: edge-cache-manager
        component: cache
      annotations:
        summary: "Low cache hit rate"
        description: "Edge cache hit rate is {{ $value }}"
    
    - alert: EdgeCacheServiceDown
      expr: up{job="edge-cache-manager"} == 0
      for: 3m
      labels:
        severity: critical
        service: edge-cache-manager
        component: cache
      annotations:
        summary: "Edge cache service is down"
        description: "Edge cache manager service is not responding"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cache-warmup
  namespace: global-cdn
  labels:
    app: cache-warmup
    tier: edge
    component: maintenance
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: cache-warmup
            tier: edge
            component: maintenance
        spec:
          restartPolicy: OnFailure
          containers:
          - name: cache-warmup
            image: ghcr.io/publishai/cache-warmup:v1.0.0
            command: ["python", "scripts/cache_warmup.py"]
            envFrom:
            - configMapRef:
                name: cdn-config
            - secretRef:
                name: cdn-secrets
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 1Gi

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: edge-ingress
  namespace: global-cdn
  labels:
    app: edge-cache-manager
    tier: edge
    component: cache-management
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    nginx.ingress.kubernetes.io/proxy-buffering: "on"
    nginx.ingress.kubernetes.io/proxy-cache-valid: "200 302 10m"
    nginx.ingress.kubernetes.io/proxy-cache-valid-404: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
spec:
  tls:
  - hosts:
    - cdn.publishai.com
    - edge.publishai.com
    secretName: edge-tls
  rules:
  - host: cdn.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-cache-manager
            port:
              number: 8000
  - host: edge.publishai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-cache-manager
            port:
              number: 8000