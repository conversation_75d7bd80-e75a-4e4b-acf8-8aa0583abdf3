# Edge Functions Configuration
# Serverless functions running at edge locations for enhanced performance

apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-functions-config
  namespace: global-cdn
  labels:
    app: edge-functions
    component: config
data:
  # Edge Function Settings
  RUNTIME_ENVIRONMENT: "nodejs18"
  EXECUTION_TIMEOUT: "30000"  # 30 seconds
  MEMORY_LIMIT: "128MB"
  CPU_LIMIT: "100m"
  
  # Function Configuration
  AUTH_FUNCTION_ENABLED: "true"
  RATE_LIMIT_FUNCTION_ENABLED: "true"
  IMAGE_OPTIMIZATION_ENABLED: "true"
  A_B_TESTING_ENABLED: "true"
  PERSONALIZATION_ENABLED: "true"
  
  # Caching Configuration
  FUNCTION_CACHE_TTL: "300"     # 5 minutes
  STATIC_CACHE_TTL: "3600"      # 1 hour
  DYNAMIC_CACHE_TTL: "60"       # 1 minute
  
  # Security Settings
  CORS_ENABLED: "true"
  CORS_ORIGINS: "https://*.publishai.com,https://publishai.com"
  CSRF_PROTECTION: "true"
  XSS_PROTECTION: "true"
  
  # Performance Monitoring
  METRICS_ENABLED: "true"
  LOGGING_ENABLED: "true"
  TRACING_ENABLED: "true"
  ERROR_TRACKING: "true"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-function-auth
  namespace: global-cdn
  labels:
    app: edge-functions
    component: auth-function
data:
  function.js: |
    // Edge Authentication Function
    // Validates JWT tokens and enforces authentication at the edge
    
    export default {
      async fetch(request, env, ctx) {
        const url = new URL(request.url);
        const pathname = url.pathname;
        
        // Skip auth for public endpoints
        const publicPaths = ['/health', '/ready', '/api/public'];
        if (publicPaths.some(path => pathname.startsWith(path))) {
          return fetch(request);
        }
        
        // Extract JWT token
        const authorization = request.headers.get('Authorization');
        const token = authorization?.replace('Bearer ', '');
        
        if (!token) {
          return new Response(JSON.stringify({
            error: 'Unauthorized',
            message: 'Missing authentication token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
        
        try {
          // Validate JWT token
          const decoded = await validateJWT(token, env.JWT_SECRET);
          
          if (!decoded || !decoded.sub) {
            throw new Error('Invalid token');
          }
          
          // Add user context to request headers
          const modifiedRequest = new Request(request, {
            headers: {
              ...request.headers,
              'X-User-ID': decoded.sub,
              'X-User-Email': decoded.email || '',
              'X-User-Role': decoded.role || 'user'
            }
          });
          
          // Forward to origin
          return fetch(modifiedRequest);
          
        } catch (error) {
          return new Response(JSON.stringify({
            error: 'Unauthorized',
            message: 'Invalid authentication token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }
    };
    
    async function validateJWT(token, secret) {
      try {
        const encoder = new TextEncoder();
        const data = encoder.encode(secret);
        const key = await crypto.subtle.importKey(
          'raw',
          data,
          { name: 'HMAC', hash: 'SHA-256' },
          false,
          ['verify']
        );
        
        const parts = token.split('.');
        if (parts.length !== 3) {
          throw new Error('Invalid token format');
        }
        
        const header = JSON.parse(atob(parts[0]));
        const payload = JSON.parse(atob(parts[1]));
        const signature = parts[2];
        
        // Verify signature
        const signatureData = encoder.encode(parts[0] + '.' + parts[1]);
        const expectedSignature = await crypto.subtle.sign('HMAC', key, signatureData);
        const expectedSignatureBase64 = btoa(String.fromCharCode(...new Uint8Array(expectedSignature)))
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '');
        
        if (signature !== expectedSignatureBase64) {
          throw new Error('Invalid signature');
        }
        
        // Check expiration
        if (payload.exp && Date.now() >= payload.exp * 1000) {
          throw new Error('Token expired');
        }
        
        return payload;
        
      } catch (error) {
        throw new Error('Token validation failed: ' + error.message);
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-function-rate-limit
  namespace: global-cdn
  labels:
    app: edge-functions
    component: rate-limit-function
data:
  function.js: |
    // Edge Rate Limiting Function
    // Implements rate limiting at the edge using distributed counters
    
    export default {
      async fetch(request, env, ctx) {
        const url = new URL(request.url);
        const clientIP = request.headers.get('CF-Connecting-IP') || 
                        request.headers.get('X-Forwarded-For') || 
                        'unknown';
        
        const userID = request.headers.get('X-User-ID');
        const rateLimitKey = userID ? `user:${userID}` : `ip:${clientIP}`;
        
        // Different rate limits for different endpoints
        const rateLimits = {
          '/api/books/generate': { requests: 5, window: 3600 },  // 5 per hour
          '/api/analytics': { requests: 100, window: 60 },       // 100 per minute
          '/api/revenue': { requests: 50, window: 60 },          // 50 per minute
          'default': { requests: 1000, window: 60 }              // 1000 per minute
        };
        
        // Determine rate limit
        let rateLimit = rateLimits.default;
        for (const [path, limit] of Object.entries(rateLimits)) {
          if (path !== 'default' && url.pathname.startsWith(path)) {
            rateLimit = limit;
            break;
          }
        }
        
        // Check rate limit
        const key = `rate_limit:${rateLimitKey}:${Math.floor(Date.now() / (rateLimit.window * 1000))}`;
        
        try {
          // Get current count from KV store (simulated with cache)
          const currentCount = await env.RATE_LIMIT_KV.get(key) || 0;
          const count = parseInt(currentCount) + 1;
          
          if (count > rateLimit.requests) {
            return new Response(JSON.stringify({
              error: 'Rate limit exceeded',
              message: `Too many requests. Limit: ${rateLimit.requests} per ${rateLimit.window} seconds`,
              resetTime: Math.ceil(Date.now() / (rateLimit.window * 1000)) * rateLimit.window
            }), {
              status: 429,
              headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': rateLimit.requests.toString(),
                'X-RateLimit-Remaining': Math.max(0, rateLimit.requests - count).toString(),
                'X-RateLimit-Reset': (Math.ceil(Date.now() / (rateLimit.window * 1000)) * rateLimit.window).toString(),
                'Retry-After': rateLimit.window.toString(),
                'Access-Control-Allow-Origin': '*'
              }
            });
          }
          
          // Update count
          await env.RATE_LIMIT_KV.put(key, count.toString(), { expirationTtl: rateLimit.window });
          
          // Add rate limit headers to response
          const response = await fetch(request);
          const modifiedResponse = new Response(response.body, response);
          
          modifiedResponse.headers.set('X-RateLimit-Limit', rateLimit.requests.toString());
          modifiedResponse.headers.set('X-RateLimit-Remaining', Math.max(0, rateLimit.requests - count).toString());
          modifiedResponse.headers.set('X-RateLimit-Reset', (Math.ceil(Date.now() / (rateLimit.window * 1000)) * rateLimit.window).toString());
          
          return modifiedResponse;
          
        } catch (error) {
          // If rate limiting fails, allow the request but log the error
          console.error('Rate limiting error:', error);
          return fetch(request);
        }
      }
    };

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-function-image-optimization
  namespace: global-cdn
  labels:
    app: edge-functions
    component: image-optimization-function
data:
  function.js: |
    // Edge Image Optimization Function
    // Automatically optimizes images based on client capabilities
    
    export default {
      async fetch(request, env, ctx) {
        const url = new URL(request.url);
        
        // Only process image requests
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp'];
        const isImageRequest = imageExtensions.some(ext => 
          url.pathname.toLowerCase().includes(ext)
        );
        
        if (!isImageRequest) {
          return fetch(request);
        }
        
        // Get client capabilities
        const accept = request.headers.get('Accept') || '';
        const userAgent = request.headers.get('User-Agent') || '';
        const viewport = request.headers.get('Viewport-Width');
        
        // Determine optimal format
        let format = 'jpeg';
        if (accept.includes('image/webp')) {
          format = 'webp';
        } else if (accept.includes('image/avif')) {
          format = 'avif';
        }
        
        // Determine optimal quality and size
        let quality = 85;
        let width = null;
        let height = null;
        
        // Parse URL parameters
        const params = url.searchParams;
        if (params.has('w')) width = parseInt(params.get('w'));
        if (params.has('h')) height = parseInt(params.get('h'));
        if (params.has('q')) quality = parseInt(params.get('q'));
        
        // Auto-detect device capabilities
        if (viewport) {
          const viewportWidth = parseInt(viewport);
          if (!width && viewportWidth) {
            width = Math.min(viewportWidth * 2, 2048); // 2x for retina, max 2048px
          }
        }
        
        // Mobile optimization
        if (userAgent.includes('Mobile')) {
          quality = Math.min(quality, 75);
          if (!width) width = 800;
        }
        
        try {
          // Fetch original image
          const response = await fetch(request);
          
          if (!response.ok || !response.body) {
            return response;
          }
          
          const originalBuffer = await response.arrayBuffer();
          
          // Apply optimizations (this would use an image processing library in practice)
          const optimizedBuffer = await optimizeImage(originalBuffer, {
            format,
            quality,
            width,
            height
          });
          
          // Return optimized image
          return new Response(optimizedBuffer, {
            status: 200,
            headers: {
              'Content-Type': `image/${format}`,
              'Cache-Control': 'public, max-age=31536000, immutable',
              'X-Image-Optimized': 'true',
              'X-Image-Format': format,
              'X-Image-Quality': quality.toString(),
              'X-Image-Width': width?.toString() || 'auto',
              'X-Image-Height': height?.toString() || 'auto',
              'Vary': 'Accept, User-Agent, Viewport-Width'
            }
          });
          
        } catch (error) {
          console.error('Image optimization error:', error);
          // Return original image on error
          return fetch(request);
        }
      }
    };
    
    async function optimizeImage(buffer, options) {
      // This is a placeholder - in practice, you would use:
      // - Cloudflare's Image Resizing service
      // - Sharp.js for Node.js
      // - Canvas API for client-side
      // - WebAssembly image processing library
      
      // For now, return the original buffer
      // In production, implement actual image optimization
      return buffer;
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-function-ab-testing
  namespace: global-cdn
  labels:
    app: edge-functions
    component: ab-testing-function
data:
  function.js: |
    // Edge A/B Testing Function
    // Implements A/B testing and feature flags at the edge
    
    export default {
      async fetch(request, env, ctx) {
        const url = new URL(request.url);
        const userID = request.headers.get('X-User-ID');
        const sessionID = request.headers.get('X-Session-ID') || 
                         request.headers.get('CF-Ray') || 
                         'anonymous';
        
        // Define active experiments
        const experiments = {
          'pricing_page_v2': {
            enabled: true,
            trafficSplit: 0.5,  // 50% traffic
            variants: ['control', 'variant_a'],
            targetPath: '/pricing'
          },
          'book_generation_ui': {
            enabled: true,
            trafficSplit: 0.3,  // 30% traffic
            variants: ['control', 'new_ui'],
            targetPath: '/generate'
          },
          'premium_features': {
            enabled: true,
            trafficSplit: 0.2,  // 20% traffic
            variants: ['control', 'early_access'],
            targetPath: '/dashboard'
          }
        };
        
        // Apply experiments
        let activeExperiments = {};
        
        for (const [experimentName, config] of Object.entries(experiments)) {
          if (!config.enabled) continue;
          
          // Check if this request should be included in the experiment
          if (!url.pathname.startsWith(config.targetPath)) continue;
          
          // Determine variant using consistent hashing
          const hashInput = userID ? `${experimentName}:${userID}` : `${experimentName}:${sessionID}`;
          const hash = await simpleHash(hashInput);
          const shouldInclude = (hash % 100) / 100 < config.trafficSplit;
          
          if (shouldInclude) {
            const variantIndex = hash % config.variants.length;
            const variant = config.variants[variantIndex];
            
            activeExperiments[experimentName] = variant;
          }
        }
        
        // Add experiment headers to request
        const modifiedRequest = new Request(request, {
          headers: {
            ...request.headers,
            'X-Experiments': JSON.stringify(activeExperiments),
            'X-Experiment-Count': Object.keys(activeExperiments).length.toString()
          }
        });
        
        // Fetch response
        const response = await fetch(modifiedRequest);
        
        // Add experiment headers to response
        const modifiedResponse = new Response(response.body, response);
        modifiedResponse.headers.set('X-Active-Experiments', JSON.stringify(activeExperiments));
        
        // Add experiment cookies for client-side tracking
        if (Object.keys(activeExperiments).length > 0) {
          const cookieValue = btoa(JSON.stringify(activeExperiments));
          modifiedResponse.headers.set('Set-Cookie', 
            `experiments=${cookieValue}; Path=/; Max-Age=86400; HttpOnly; Secure; SameSite=Strict`
          );
        }
        
        return modifiedResponse;
      }
    };
    
    async function simpleHash(str) {
      const encoder = new TextEncoder();
      const data = encoder.encode(str);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = new Uint8Array(hashBuffer);
      
      // Convert to simple integer hash
      let hash = 0;
      for (let i = 0; i < 4; i++) {
        hash = (hash << 8) | hashArray[i];
      }
      return Math.abs(hash);
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-function-runtime
  namespace: global-cdn
  labels:
    app: edge-function-runtime
    tier: edge
    version: v1
    component: function-runtime
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 2
  selector:
    matchLabels:
      app: edge-function-runtime
  template:
    metadata:
      labels:
        app: edge-function-runtime
        tier: edge
        version: v1
        component: function-runtime
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: edge-function-runtime
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      containers:
      - name: function-runtime
        image: ghcr.io/publishai/edge-function-runtime:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        
        env:
        - name: NODE_ENV
          value: "production"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        
        envFrom:
        - configMapRef:
            name: edge-functions-config
        - secretRef:
            name: cdn-secrets
        
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
            ephemeral-storage: 1Gi
          limits:
            cpu: 500m
            memory: 1Gi
            ephemeral-storage: 5Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        volumeMounts:
        - name: function-code
          mountPath: /app/functions
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
      
      volumes:
      - name: function-code
        projected:
          sources:
          - configMap:
              name: edge-function-auth
          - configMap:
              name: edge-function-rate-limit
          - configMap:
              name: edge-function-image-optimization
          - configMap:
              name: edge-function-ab-testing
      - name: temp-storage
        emptyDir:
          sizeLimit: 1Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      nodeSelector:
        kubernetes.io/arch: amd64
        node.kubernetes.io/instance-type: compute-optimized

---
apiVersion: v1
kind: Service
metadata:
  name: edge-function-runtime
  namespace: global-cdn
  labels:
    app: edge-function-runtime
    tier: edge
    component: function-runtime
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: edge-function-runtime

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: edge-function-runtime
  namespace: global-cdn
  labels:
    app: edge-function-runtime
    tier: edge
    component: function-runtime
automountServiceAccountToken: false