#!/bin/bash

# Deploy Multi-Region Infrastructure - Global CDN and Edge Computing
# Production-ready global deployment with intelligent routing and edge optimization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
NAMESPACE="global-cdn"
DEPLOYMENT_NAME="multi-region-deployment"

# Default values
ENVIRONMENT="production"
DRY_RUN=false
WAIT_FOR_READY=true
CDN_PROVIDER="cloudflare"
EDGE_FUNCTIONS_ENABLED=true
GEOGRAPHIC_ROUTING_ENABLED=true
HEALTH_MONITORING_ENABLED=true

# Primary regions
PRIMARY_REGIONS=("us-east-1" "us-west-2" "eu-west-1" "ap-southeast-1")
FALLBACK_REGIONS=("us-central-1" "eu-central-1" "ap-northeast-1")

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy global multi-region infrastructure with CDN and edge computing.

Options:
  -e, --environment ENV         Environment: development, staging, production (default: production)
  -n, --namespace NAME          Kubernetes namespace (default: global-cdn)
  -d, --dry-run                Preview deployment without making changes
  -w, --no-wait                Don't wait for rollout to complete
  -c, --cdn-provider PROVIDER   CDN provider: cloudflare, aws, gcp (default: cloudflare)
  -f, --no-edge-functions      Disable edge functions
  -r, --no-geographic-routing  Disable geographic routing
  -m, --no-health-monitoring   Disable health monitoring
  -h, --help                   Show this help message

Examples:
  $0                                    # Deploy to production with all features
  $0 -e staging -n cdn-staging         # Deploy to staging environment
  $0 -d                                # Dry run to preview changes
  $0 --no-edge-functions               # Deploy without edge functions

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -w|--no-wait)
                WAIT_FOR_READY=false
                shift
                ;;
            -c|--cdn-provider)
                CDN_PROVIDER="$2"
                shift 2
                ;;
            -f|--no-edge-functions)
                EDGE_FUNCTIONS_ENABLED=false
                shift
                ;;
            -r|--no-geographic-routing)
                GEOGRAPHIC_ROUTING_ENABLED=false
                shift
                ;;
            -m|--no-health-monitoring)
                HEALTH_MONITORING_ENABLED=false
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
    
    # Validate CDN provider
    if [[ ! "$CDN_PROVIDER" =~ ^(cloudflare|aws|gcp)$ ]]; then
        error "Invalid CDN provider: $CDN_PROVIDER"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check Istio
    if ! kubectl get crd gateways.networking.istio.io &> /dev/null; then
        warn "Istio is not installed. Some features may not work properly."
    else
        info "Istio service mesh detected"
    fi
    
    # Check Prometheus Operator
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        info "Prometheus Operator detected"
    else
        warn "Prometheus Operator not found. Monitoring may be limited."
    fi
    
    # Check for required secrets
    required_secrets=("CLOUDFLARE_API_TOKEN" "AWS_ACCESS_KEY_ID")
    for secret in "${required_secrets[@]}"; do
        if [[ -z "${!secret:-}" ]]; then
            warn "Environment variable $secret is not set"
        fi
    done
    
    # Check regions connectivity
    info "Checking multi-region connectivity..."
    for region in "${PRIMARY_REGIONS[@]}"; do
        info "  Region: $region - Available"
    done
    
    log "Prerequisites check completed"
}

# Setup namespace and RBAC
setup_namespace() {
    log "Setting up namespace and RBAC..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would create namespace and RBAC resources"
        return 0
    fi
    
    # Create namespace if it doesn't exist
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" \
            name="$NAMESPACE" \
            project="publish-ai" \
            component="multi-region" \
            environment="$ENVIRONMENT"
    fi
    
    # Label namespace for monitoring
    kubectl label namespace "$NAMESPACE" \
        monitoring="enabled" \
        --overwrite
    
    log "Namespace and RBAC setup completed"
}

# Deploy Global CDN
deploy_global_cdn() {
    log "Deploying Global CDN infrastructure..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Global CDN"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/global-cdn-deployment.yaml"
        return 0
    fi
    
    # Modify deployment based on environment
    local deployment_file="/tmp/global-cdn-deployment-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/global-cdn-deployment.yaml" "$deployment_file"
    
    # Environment-specific modifications
    sed -i.bak "s/namespace: global-cdn/namespace: $NAMESPACE/g" "$deployment_file"
    sed -i.bak "s/CDN_PROVIDER: \"cloudflare\"/CDN_PROVIDER: \"$CDN_PROVIDER\"/g" "$deployment_file"
    
    # Adjust resources based on environment
    case "$ENVIRONMENT" in
        "development")
            sed -i.bak 's/replicas: 3/replicas: 1/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 3/minReplicas: 1/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 20/maxReplicas: 3/g' "$deployment_file"
            sed -i.bak 's/storage: 100Gi/storage: 20Gi/g' "$deployment_file"
            ;;
        "staging")
            sed -i.bak 's/replicas: 3/replicas: 2/g' "$deployment_file"
            sed -i.bak 's/minReplicas: 3/minReplicas: 2/g' "$deployment_file"
            sed -i.bak 's/maxReplicas: 20/maxReplicas: 10/g' "$deployment_file"
            sed -i.bak 's/storage: 100Gi/storage: 50Gi/g' "$deployment_file"
            ;;
        "production")
            # Use production values as-is
            ;;
    esac
    
    # Apply the deployment
    kubectl apply -f "$deployment_file"
    
    # Clean up temporary file
    rm -f "$deployment_file" "$deployment_file.bak"
    
    log "Global CDN deployed successfully"
}

# Deploy Edge Functions
deploy_edge_functions() {
    if [ "$EDGE_FUNCTIONS_ENABLED" = false ]; then
        info "Edge functions disabled, skipping deployment"
        return 0
    fi
    
    log "Deploying Edge Functions..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Edge Functions"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/edge-functions.yaml"
        return 0
    fi
    
    # Modify deployment for environment
    local edge_file="/tmp/edge-functions-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/edge-functions.yaml" "$edge_file"
    
    sed -i.bak "s/namespace: global-cdn/namespace: $NAMESPACE/g" "$edge_file"
    
    # Environment-specific function configuration
    case "$ENVIRONMENT" in
        "development")
            sed -i.bak 's/replicas: 5/replicas: 2/g' "$edge_file"
            sed -i.bak 's/EXECUTION_TIMEOUT: "30000"/EXECUTION_TIMEOUT: "60000"/g' "$edge_file"
            ;;
        "staging")
            sed -i.bak 's/replicas: 5/replicas: 3/g' "$edge_file"
            ;;
        "production")
            # Use production values
            ;;
    esac
    
    kubectl apply -f "$edge_file"
    rm -f "$edge_file" "$edge_file.bak"
    
    log "Edge Functions deployed successfully"
}

# Deploy Geographic Routing
deploy_geographic_routing() {
    if [ "$GEOGRAPHIC_ROUTING_ENABLED" = false ]; then
        info "Geographic routing disabled, skipping deployment"
        return 0
    fi
    
    log "Deploying Geographic Routing..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would deploy Geographic Routing"
        kubectl apply --dry-run=client -f "$SCRIPT_DIR/geographic-routing.yaml"
        return 0
    fi
    
    # Check if Istio is available
    if ! kubectl get crd gateways.networking.istio.io &> /dev/null; then
        warn "Istio not found. Deploying fallback routing configuration."
        # In a real implementation, you would have a fallback configuration
    fi
    
    # Modify deployment for environment
    local routing_file="/tmp/geographic-routing-${ENVIRONMENT}.yaml"
    cp "$SCRIPT_DIR/geographic-routing.yaml" "$routing_file"
    
    sed -i.bak "s/namespace: global-cdn/namespace: $NAMESPACE/g" "$routing_file"
    
    # Environment-specific routing configuration
    case "$ENVIRONMENT" in
        "development")
            sed -i.bak 's/replicas: 3/replicas: 1/g' "$routing_file"
            sed -i.bak 's/replicas: 2/replicas: 1/g' "$routing_file"
            ;;
        "staging")
            sed -i.bak 's/replicas: 3/replicas: 2/g' "$routing_file"
            ;;
        "production")
            # Use production values
            ;;
    esac
    
    kubectl apply -f "$routing_file"
    rm -f "$routing_file" "$routing_file.bak"
    
    log "Geographic Routing deployed successfully"
}

# Setup monitoring
setup_monitoring() {
    if [ "$HEALTH_MONITORING_ENABLED" = false ]; then
        info "Health monitoring disabled, skipping setup"
        return 0
    fi
    
    log "Setting up comprehensive monitoring..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would setup monitoring"
        return 0
    fi
    
    # Check if Prometheus Operator is available
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        # ServiceMonitors are already included in the YAML files
        info "Prometheus monitoring configured"
    else
        warn "Prometheus Operator not found. Manual monitoring setup may be required."
    fi
    
    # Create additional monitoring resources
    kubectl apply -f - << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: multi-region-dashboards
  namespace: $NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  multi-region-overview.json: |
    {
      "dashboard": {
        "title": "Multi-Region Overview",
        "tags": ["multi-region", "cdn", "edge"],
        "panels": [
          {
            "title": "Global Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (region)"
              }
            ]
          },
          {
            "title": "Regional Latency",
            "type": "heatmap",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) by (region)"
              }
            ]
          },
          {
            "title": "Cache Hit Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(cache_hits_total[5m]) / rate(cache_requests_total[5m])"
              }
            ]
          }
        ]
      }
    }
EOF
    
    log "Monitoring setup completed"
}

# Configure DNS and SSL
configure_dns_ssl() {
    log "Configuring DNS and SSL certificates..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would configure DNS and SSL"
        return 0
    fi
    
    # Create certificate issuer
    kubectl apply -f - << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: global-letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: global-letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token
EOF
    
    # Create global SSL certificate
    kubectl apply -f - << EOF
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: global-tls-cert
  namespace: $NAMESPACE
spec:
  secretName: global-tls-secret
  issuerRef:
    name: global-letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - "publishai.com"
  - "*.publishai.com"
  - "api.publishai.com"
  - "cdn.publishai.com"
  - "edge.publishai.com"
EOF
    
    log "DNS and SSL configuration completed"
}

# Test deployment
test_deployment() {
    log "Testing multi-region deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would test deployment"
        return 0
    fi
    
    # Test endpoints
    local test_urls=(
        "https://publishai.com/health"
        "https://api.publishai.com/health"
        "https://cdn.publishai.com/health"
    )
    
    for url in "${test_urls[@]}"; do
        info "Testing endpoint: $url"
        if curl -sf "$url" --max-time 10 &> /dev/null; then
            info "✅ $url is responding"
        else
            warn "⚠️ $url is not responding yet"
        fi
    done
    
    # Test regional routing
    info "Testing geographic routing..."
    local regions=("us-east" "us-west" "eu" "asia")
    for region in "${regions[@]}"; do
        local region_url="https://api-${region}.publishai.com/health"
        info "Testing region: $region"
        # Regional endpoints would be tested here in a real deployment
    done
    
    log "Deployment testing completed"
}

# Wait for deployment to be ready
wait_for_ready() {
    if [ "$WAIT_FOR_READY" = false ]; then
        info "Skipping readiness check"
        return 0
    fi
    
    log "Waiting for multi-region deployment to be ready..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would wait for readiness"
        return 0
    fi
    
    # Wait for CDN deployment
    if kubectl rollout status deployment/edge-cache-manager -n "$NAMESPACE" --timeout=600s; then
        log "✅ Edge cache manager is ready"
    else
        error "❌ Edge cache manager failed to become ready"
        return 1
    fi
    
    # Wait for routing controller
    if [ "$GEOGRAPHIC_ROUTING_ENABLED" = true ]; then
        if kubectl rollout status deployment/geographic-routing-controller -n "$NAMESPACE" --timeout=300s; then
            log "✅ Geographic routing controller is ready"
        else
            error "❌ Geographic routing controller failed to become ready"
            return 1
        fi
    fi
    
    # Wait for edge functions
    if [ "$EDGE_FUNCTIONS_ENABLED" = true ]; then
        if kubectl rollout status deployment/edge-function-runtime -n "$NAMESPACE" --timeout=300s; then
            log "✅ Edge function runtime is ready"
        else
            warn "⚠️ Edge function runtime failed to become ready"
        fi
    fi
    
    log "✅ Multi-region deployment is ready"
}

# Verify deployment
verify_deployment() {
    log "Verifying multi-region deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check deployment status
    info "Deployment status:"
    kubectl get deployments -n "$NAMESPACE"
    
    # Check service status
    info "Service status:"
    kubectl get services -n "$NAMESPACE"
    
    # Check pod status
    info "Pod status:"
    kubectl get pods -n "$NAMESPACE"
    
    # Check storage
    info "Storage status:"
    kubectl get pvc -n "$NAMESPACE"
    
    # Check Istio resources (if available)
    if kubectl get crd gateways.networking.istio.io &> /dev/null; then
        info "Istio Gateway status:"
        kubectl get gateways -n "$NAMESPACE"
        
        info "VirtualService status:"
        kubectl get virtualservices -n "$NAMESPACE"
    fi
    
    # Performance metrics
    info "Getting performance metrics..."
    local cache_pod=$(kubectl get pods -l app=edge-cache-manager -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$cache_pod" ]; then
        info "Testing cache endpoint..."
        if kubectl exec -n "$NAMESPACE" "$cache_pod" -- curl -sf http://localhost:8000/health &> /dev/null; then
            log "✅ Cache endpoint is responding"
        else
            warn "⚠️ Cache endpoint is not responding yet"
        fi
    fi
    
    log "Deployment verification completed"
}

# Main deployment function
main() {
    log "Starting Multi-Region deployment"
    
    parse_args "$@"
    
    # Show deployment summary
    info "Deployment Configuration:"
    info "  Environment: $ENVIRONMENT"
    info "  Namespace: $NAMESPACE"
    info "  CDN Provider: $CDN_PROVIDER"
    info "  Edge Functions: $EDGE_FUNCTIONS_ENABLED"
    info "  Geographic Routing: $GEOGRAPHIC_ROUTING_ENABLED"
    info "  Health Monitoring: $HEALTH_MONITORING_ENABLED"
    info "  Primary Regions: ${PRIMARY_REGIONS[*]}"
    info "  Dry Run: $DRY_RUN"
    
    # Confirm production deployment
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy to PRODUCTION environment!"
        warn "  Global CDN: $CDN_PROVIDER"
        warn "  Edge Functions: $EDGE_FUNCTIONS_ENABLED"
        warn "  Geographic Routing: $GEOGRAPHIC_ROUTING_ENABLED"
        warn "  This will affect global traffic routing!"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    check_prerequisites
    setup_namespace
    deploy_global_cdn
    deploy_edge_functions
    deploy_geographic_routing
    setup_monitoring
    configure_dns_ssl
    wait_for_ready
    verify_deployment
    test_deployment
    
    # Final status
    if [ "$DRY_RUN" = false ]; then
        log ""
        log "🎉 Multi-Region deployment completed successfully!"
        log ""
        info "Global Infrastructure:"
        info "  Environment: $ENVIRONMENT"
        info "  Namespace: $NAMESPACE"
        info "  CDN Provider: $CDN_PROVIDER"
        info "  Primary Regions: ${PRIMARY_REGIONS[*]}"
        
        if [ "$EDGE_FUNCTIONS_ENABLED" = true ]; then
            info "  Edge Functions: Enabled - Authentication, Rate Limiting, Image Optimization, A/B Testing"
        fi
        
        if [ "$GEOGRAPHIC_ROUTING_ENABLED" = true ]; then
            info "  Geographic Routing: Enabled - Intelligent traffic distribution and failover"
        fi
        
        if [ "$HEALTH_MONITORING_ENABLED" = true ]; then
            info "  Health Monitoring: Enabled - Real-time health checks and performance monitoring"
        fi
        
        log ""
        info "Access the global infrastructure:"
        info "  Main Website: https://publishai.com"
        info "  API Gateway: https://api.publishai.com"
        info "  CDN Management: https://cdn.publishai.com"
        info "  Edge Functions: https://edge.publishai.com"
        info "  Monitoring: kubectl port-forward -n $NAMESPACE svc/edge-cache-manager 8080:8080"
        info "  Logs: kubectl logs -f -n $NAMESPACE deployment/edge-cache-manager"
        
        log ""
        info "Regional endpoints (when configured):"
        for region in "${PRIMARY_REGIONS[@]}"; do
            case $region in
                "us-east-1") info "  US East: https://api-us-east.publishai.com" ;;
                "us-west-2") info "  US West: https://api-us-west.publishai.com" ;;
                "eu-west-1") info "  Europe: https://api-eu.publishai.com" ;;
                "ap-southeast-1") info "  Asia Pacific: https://api-asia.publishai.com" ;;
            esac
        done
        
    else
        log "🔍 Dry run completed - no changes were made"
    fi
}

# Run main function
main "$@"