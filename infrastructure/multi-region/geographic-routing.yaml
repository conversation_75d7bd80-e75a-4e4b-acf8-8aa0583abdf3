# Geographic Routing and Load Balancing Configuration
# Multi-region traffic distribution with intelligent routing

apiVersion: v1
kind: ConfigMap
metadata:
  name: geographic-routing-config
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: config
data:
  # Primary Regions Configuration
  PRIMARY_REGIONS: "us-east-1,us-west-2,eu-west-1,ap-southeast-1"
  FALLBACK_REGIONS: "us-central-1,eu-central-1,ap-northeast-1"
  
  # Traffic Distribution
  TRAFFIC_DISTRIBUTION_STRATEGY: "latency_based"  # latency_based, geographic, custom
  HEALTH_CHECK_ENABLED: "true"
  FAILOVER_ENABLED: "true"
  LOAD_BALANCING_ALGORITHM: "weighted_round_robin"
  
  # Geographic Zones
  ZONE_US_EAST: "us-east-1,us-east-2"
  ZONE_US_WEST: "us-west-1,us-west-2"
  ZONE_US_CENTRAL: "us-central-1"
  ZONE_EUROPE: "eu-west-1,eu-central-1,eu-north-1"
  ZONE_ASIA_PACIFIC: "ap-southeast-1,ap-northeast-1,ap-south-1"
  ZONE_LATAM: "sa-east-1"
  ZONE_OCEANIA: "ap-southeast-2"
  
  # Performance Thresholds
  MAX_LATENCY_MS: "200"
  MAX_ERROR_RATE: "0.05"
  MIN_HEALTHY_ENDPOINTS: "2"
  HEALTH_CHECK_INTERVAL: "30"
  HEALTH_CHECK_TIMEOUT: "10"
  
  # Routing Rules
  STICKY_SESSIONS: "false"
  SESSION_AFFINITY_DURATION: "3600"
  CROSS_REGION_FALLBACK: "true"
  INTELLIGENT_ROUTING: "true"
  
  # Real-time Analytics
  ROUTING_ANALYTICS_ENABLED: "true"
  PERFORMANCE_MONITORING: "true"
  COST_OPTIMIZATION: "true"

---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: global-gateway
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: gateway
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: global-tls-secret
    hosts:
    - "*.publishai.com"
    - "publishai.com"
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.publishai.com"
    - "publishai.com"
    tls:
      httpsRedirect: true

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: geographic-routing
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: routing
spec:
  hosts:
  - "publishai.com"
  - "api.publishai.com"
  - "cdn.publishai.com"
  gateways:
  - global-gateway
  http:
  # API Traffic Routing
  - match:
    - uri:
        prefix: "/api/"
    route:
    - destination:
        host: api-gateway-service.api-gateway.svc.cluster.local
        port:
          number: 8000
      weight: 100
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: 5xx,reset,connect-failure,refused-stream
    timeout: 30s
    headers:
      request:
        add:
          x-request-source: "global-gateway"
          x-routing-region: "auto"
  
  # Static Content Routing
  - match:
    - uri:
        prefix: "/static/"
    - uri:
        prefix: "/assets/"
    - uri:
        prefix: "/images/"
    route:
    - destination:
        host: edge-cache-manager.global-cdn.svc.cluster.local
        port:
          number: 8000
      weight: 100
    headers:
      response:
        add:
          cache-control: "public, max-age=********, immutable"
  
  # Frontend Application Routing
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: frontend-service.frontend.svc.cluster.local
        port:
          number: 3000
      weight: 100
    timeout: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: global-load-balancing
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: load-balancing
spec:
  host: "*.local"
  trafficPolicy:
    loadBalancer:
      simple: LEAST_CONN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30s
        keepAlive:
          time: 7200s
          interval: 75s
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 100
        maxRequestsPerConnection: 2
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 50

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: geographic-routing-controller
  namespace: global-cdn
  labels:
    app: geographic-routing-controller
    tier: control-plane
    version: v1
    component: routing-controller
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: geographic-routing-controller
  template:
    metadata:
      labels:
        app: geographic-routing-controller
        tier: control-plane
        version: v1
        component: routing-controller
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: geographic-routing-controller
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      containers:
      - name: routing-controller
        image: ghcr.io/publishai/geographic-routing-controller:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        - name: grpc
          containerPort: 9000
          protocol: TCP
        
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: CLUSTER_DOMAIN
          value: "cluster.local"
        
        envFrom:
        - configMapRef:
            name: geographic-routing-config
        - secretRef:
            name: cdn-secrets
        
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
            ephemeral-storage: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 5Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
        - name: routing-data
          mountPath: /app/data
      
      volumes:
      - name: config
        configMap:
          name: geographic-routing-config
      - name: temp-storage
        emptyDir:
          sizeLimit: 1Gi
      - name: routing-data
        emptyDir:
          sizeLimit: 2Gi
      
      imagePullSecrets:
      - name: registry-credentials
      
      nodeSelector:
        kubernetes.io/arch: amd64
        node.kubernetes.io/instance-type: compute-optimized
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: [geographic-routing-controller]
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: geographic-routing-controller
  namespace: global-cdn
  labels:
    app: geographic-routing-controller
    tier: control-plane
    component: routing-controller
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  - name: grpc
    port: 9000
    targetPort: grpc
    protocol: TCP
  selector:
    app: geographic-routing-controller

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: geographic-routing-controller
  namespace: global-cdn
  labels:
    app: geographic-routing-controller
    tier: control-plane
    component: routing-controller
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: geographic-routing-controller
rules:
- apiGroups: [""]
  resources: ["services", "endpoints", "nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules", "gateways"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: geographic-routing-controller
subjects:
- kind: ServiceAccount
  name: geographic-routing-controller
  namespace: global-cdn
roleRef:
  kind: ClusterRole
  name: geographic-routing-controller
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: health-check-config
  namespace: global-cdn
  labels:
    app: health-checker
    component: config
data:
  endpoints.json: |
    {
      "endpoints": [
        {
          "name": "api-gateway-us-east",
          "url": "https://api-us-east.publishai.com/health",
          "region": "us-east-1",
          "priority": 1,
          "weight": 100
        },
        {
          "name": "api-gateway-us-west",
          "url": "https://api-us-west.publishai.com/health",
          "region": "us-west-2",
          "priority": 1,
          "weight": 100
        },
        {
          "name": "api-gateway-eu",
          "url": "https://api-eu.publishai.com/health",
          "region": "eu-west-1",
          "priority": 1,
          "weight": 100
        },
        {
          "name": "api-gateway-asia",
          "url": "https://api-asia.publishai.com/health",
          "region": "ap-southeast-1",
          "priority": 1,
          "weight": 100
        }
      ],
      "check_interval": 30,
      "timeout": 10,
      "failure_threshold": 3,
      "success_threshold": 2
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: health-checker
  namespace: global-cdn
  labels:
    app: health-checker
    tier: monitoring
    version: v1
    component: health-monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: health-checker
  template:
    metadata:
      labels:
        app: health-checker
        tier: monitoring
        version: v1
        component: health-monitoring
    spec:
      serviceAccountName: health-checker
      containers:
      - name: health-checker
        image: ghcr.io/publishai/health-checker:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        
        envFrom:
        - configMapRef:
            name: geographic-routing-config
        
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
        
        volumeMounts:
        - name: health-config
          mountPath: /app/config
          readOnly: true
      
      volumes:
      - name: health-config
        configMap:
          name: health-check-config
      
      imagePullSecrets:
      - name: registry-credentials

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: health-checker
  namespace: global-cdn
  labels:
    app: health-checker
    tier: monitoring
    component: health-monitoring
automountServiceAccountToken: false

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: geographic-routing-metrics
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: monitoring
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: geographic-routing-controller
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: geographic-routing-alerts
  namespace: global-cdn
  labels:
    app: geographic-routing
    component: monitoring
    prometheus: kube-prometheus
spec:
  groups:
  - name: geographic-routing.rules
    rules:
    - alert: HighLatencyRouting
      expr: routing_latency_p95 > 500
      for: 5m
      labels:
        severity: warning
        service: geographic-routing
        component: routing
      annotations:
        summary: "High routing latency detected"
        description: "P95 routing latency is {{ $value }}ms"
    
    - alert: RegionUnhealthy
      expr: region_health_score < 0.8
      for: 3m
      labels:
        severity: critical
        service: geographic-routing
        component: health
      annotations:
        summary: "Region health degraded"
        description: "Region {{ $labels.region }} health score is {{ $value }}"
    
    - alert: FailoverActivated
      expr: increase(routing_failovers_total[5m]) > 0
      for: 1m
      labels:
        severity: warning
        service: geographic-routing
        component: failover
      annotations:
        summary: "Automatic failover activated"
        description: "{{ $value }} failovers in the last 5 minutes"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: routing-optimization
  namespace: global-cdn
  labels:
    app: routing-optimization
    component: maintenance
spec:
  schedule: "*/15 * * * *"  # Every 15 minutes
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: routing-optimization
            component: maintenance
        spec:
          restartPolicy: OnFailure
          containers:
          - name: routing-optimizer
            image: ghcr.io/publishai/routing-optimizer:v1.0.0
            command: ["python", "scripts/optimize_routing.py"]
            envFrom:
            - configMapRef:
                name: geographic-routing-config
            - secretRef:
                name: cdn-secrets
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 1000m
                memory: 2Gi