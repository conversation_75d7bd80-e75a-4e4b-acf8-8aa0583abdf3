apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: api-gateway-dr
  namespace: api-gateway
spec:
  host: api-gateway.api-gateway.svc.cluster.local
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30s
        tcpKeepalive:
          time: 7200s
          interval: 75s
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 10
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50
        minHealthPercent: 50
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 50
      splitExternalLocalOriginErrors: true
  portLevelSettings:
  - port:
      number: 8080
    connectionPool:
      tcp:
        maxConnections: 50
        connectTimeout: 10s

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: infrastructure-services-dr
  namespace: infrastructure
spec:
  host: "*.infrastructure.svc.cluster.local"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 50
        connectTimeout: 10s
      http:
        http1MaxPendingRequests: 20
        http2MaxRequests: 50
        maxRequestsPerConnection: 5
        maxRetries: 3
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 30

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tier1-services-dr
  namespace: tier1-services
spec:
  host: "*.tier1-services.svc.cluster.local"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 30
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 30
        maxRequestsPerConnection: 3
        maxRetries: 5
        timeout: 300s  # Long timeout for AI operations
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 60s
      baseEjectionTime: 60s
      maxEjectionPercent: 50
  exportTo:
  - "*"

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tier2-services-dr
  namespace: tier2-services
spec:
  host: "*.tier2-services.svc.cluster.local"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 20
        connectTimeout: 20s
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 20
        maxRequestsPerConnection: 3
        maxRetries: 3
        timeout: 120s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 30

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tier3-services-dr
  namespace: tier3-services
spec:
  host: "*.tier3-services.svc.cluster.local"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 20
        connectTimeout: 10s
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 20
        maxRequestsPerConnection: 5
        maxRetries: 2
        timeout: 60s
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 30

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: external-apis-dr
  namespace: istio-system
spec:
  host: "*.com"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 10
        connectTimeout: 30s
      http:
        http1MaxPendingRequests: 5
        http2MaxRequests: 10
        maxRequestsPerConnection: 2
        maxRetries: 3
        timeout: 60s
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 60s
      baseEjectionTime: 60s
      maxEjectionPercent: 50

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: envoy-filter-config
  namespace: istio-system
data:
  wasm_modules: |
    rate_limit_module:
      name: "envoy.filters.http.wasm"
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
        config:
          name: "rate_limit"
          vm_config:
            vm_id: "rate_limit"
            runtime: "envoy.wasm.runtime.v8"
            code:
              local:
                inline_string: |
                  // Basic rate limiting WASM module
                  class RateLimitFilter {
                    constructor(rootContext) {
                      this.rootContext = rootContext;
                    }
                    
                    onRequestHeaders() {
                      const headers = this.getRequestHeaders();
                      const clientId = headers["x-client-id"] || headers["x-forwarded-for"] || "unknown";
                      
                      // Simple rate limiting logic
                      const key = `rate_limit:${clientId}`;
                      const current = this.rootContext.getSharedData(key) || 0;
                      
                      if (current > 100) { // 100 requests per window
                        this.sendLocalResponse(429, "Rate limited", "", []);
                        return FilterHeadersStatus.StopIteration;
                      }
                      
                      this.rootContext.setSharedData(key, current + 1, 60000); // 1 minute window
                      return FilterHeadersStatus.Continue;
                    }
                  }

---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: api-gateway-rate-limit
  namespace: api-gateway
spec:
  workloadSelector:
    labels:
      app: api-gateway
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.local_ratelimit
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
          value:
            stat_prefix: local_rate_limiter
            token_bucket:
              max_tokens: 1000
              tokens_per_fill: 100
              fill_interval: 60s
            filter_enabled:
              runtime_key: local_rate_limit_enabled
              default_value:
                numerator: 100
                denominator: HUNDRED
            filter_enforced:
              runtime_key: local_rate_limit_enforced
              default_value:
                numerator: 100
                denominator: HUNDRED
            response_headers_to_add:
            - append: false
              header:
                key: x-local-rate-limit
                value: 'true'

---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: metrics-enhancement
  namespace: istio-system
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.wasm
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
          config:
            name: "metrics_enhancement"
            vm_config:
              vm_id: "metrics_enhancement"
              runtime: "envoy.wasm.runtime.v8"
              code:
                local:
                  inline_string: |
                    class MetricsFilter {
                      onRequestHeaders() {
                        const headers = this.getRequestHeaders();
                        const method = headers[":method"];
                        const path = headers[":path"];
                        const userAgent = headers["user-agent"] || "unknown";
                        
                        // Add custom metrics
                        this.addMetric("custom_request_total", 1, {
                          method: method,
                          path_prefix: this.getPathPrefix(path),
                          user_agent_type: this.getUserAgentType(userAgent)
                        });
                        
                        return FilterHeadersStatus.Continue;
                      }
                      
                      getPathPrefix(path) {
                        if (path.startsWith("/api/")) {
                          return path.split("/").slice(0, 3).join("/");
                        }
                        return path.split("/")[1] || "root";
                      }
                      
                      getUserAgentType(userAgent) {
                        if (userAgent.includes("bot") || userAgent.includes("crawler")) {
                          return "bot";
                        } else if (userAgent.includes("mobile")) {
                          return "mobile";
                        } else if (userAgent.includes("curl") || userAgent.includes("wget")) {
                          return "cli";
                        }
                        return "browser";
                      }
                    }

---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: circuit-breaker-enhancement
  namespace: istio-system
spec:
  configPatches:
  - applyTo: CLUSTER
    match:
      context: SIDECAR_OUTBOUND
    patch:
      operation: MERGE
      value:
        circuit_breakers:
          thresholds:
          - priority: DEFAULT
            max_connections: 100
            max_pending_requests: 50
            max_requests: 200
            max_retries: 10
            retry_budget:
              budget_percent:
                value: 20.0
              min_retry_concurrency: 3
          - priority: HIGH
            max_connections: 200
            max_pending_requests: 100
            max_requests: 400
            max_retries: 20

---
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: default
  namespace: istio-system
spec:
  metrics:
  - providers:
    - prometheus
  - overrides:
    - match:
        metric: ALL_METRICS
      disabled: false
    - match:
        metric: REQUEST_COUNT
      dimensions:
        request_protocol: "request.protocol"
        response_code: "response.code"
        grpc_response_status: "response.grpc_status"
        source_app: "source.labels['app'] | 'unknown'"
        destination_service_name: "destination.service.name | 'unknown'"
        destination_service_namespace: "destination.service.namespace | 'unknown'"
        custom_metric: "has(request.headers['x-custom-metric']) ? request.headers['x-custom-metric'] : 'none'"
  - overrides:
    - match:
        metric: REQUEST_DURATION
      dimensions:
        request_protocol: "request.protocol"
        response_code: "response.code"
        source_app: "source.labels['app'] | 'unknown'"
        destination_service_name: "destination.service.name | 'unknown'"

---
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: api-gateway-telemetry
  namespace: api-gateway
spec:
  selector:
    matchLabels:
      app: api-gateway
  metrics:
  - providers:
    - prometheus
  - overrides:
    - match:
        metric: REQUEST_COUNT
      dimensions:
        api_version: "has(request.headers['x-api-version']) ? request.headers['x-api-version'] : 'v1'"
        client_type: "has(request.headers['x-client-type']) ? request.headers['x-client-type'] : 'unknown'"
        request_size_category: |
          size(request.total_size) < 1024 ? 'small' :
          size(request.total_size) < 10240 ? 'medium' : 'large'
  tracing:
  - providers:
    - jaeger

---
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: ai-services-telemetry
  namespace: tier1-services
spec:
  metrics:
  - providers:
    - prometheus
  - overrides:
    - match:
        metric: REQUEST_COUNT
      dimensions:
        ai_model_type: "has(request.headers['x-ai-model']) ? request.headers['x-ai-model'] : 'unknown'"
        content_type: "has(request.headers['x-content-type']) ? request.headers['x-content-type'] : 'unknown'"
        processing_complexity: "has(request.headers['x-complexity']) ? request.headers['x-complexity'] : 'normal'"
  tracing:
  - providers:
    - jaeger
  accessLogging:
  - providers:
    - envoy