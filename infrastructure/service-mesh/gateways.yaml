apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: publish-ai-gateway
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway
  servers:
  # HTTP traffic (redirect to HTTPS)
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - api.publish-ai.local
    - grafana.publish-ai.local
    - jaeger.publish-ai.local
    - prometheus.publish-ai.local
    tls:
      httpsRedirect: true
  
  # HTTPS traffic
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - api.publish-ai.local
    - grafana.publish-ai.local
    - jaeger.publish-ai.local
    - prometheus.publish-ai.local
    tls:
      mode: SIMPLE
      credentialName: publish-ai-tls-certs

---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: monitoring-gateway
  namespace: monitoring
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - grafana.publish-ai.local
    - prometheus.publish-ai.local
    - jaeger.publish-ai.local
    - alertmanager.publish-ai.local
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - grafana.publish-ai.local
    - prometheus.publish-ai.local
    - jaeger.publish-ai.local
    - alertmanager.publish-ai.local
    tls:
      mode: SIMPLE
      credentialName: monitoring-tls-certs

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-gateway-vs
  namespace: api-gateway
spec:
  hosts:
  - api.publish-ai.local
  gateways:
  - istio-system/publish-ai-gateway
  http:
  # Health check endpoints (no auth required)
  - match:
    - uri:
        prefix: /health
    - uri:
        prefix: /ready
    - uri:
        prefix: /metrics
    route:
    - destination:
        host: api-gateway.api-gateway.svc.cluster.local
        port:
          number: 8080
    timeout: 30s
    
  # API endpoints (require authentication)
  - match:
    - uri:
        prefix: /api/
    route:
    - destination:
        host: api-gateway.api-gateway.svc.cluster.local
        port:
          number: 8080
    timeout: 300s
    headers:
      request:
        add:
          x-forwarded-proto: https
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 30s
      retryOn: 5xx,reset,connect-failure,refused-stream

  # Admin endpoints (rate limited)
  - match:
    - uri:
        prefix: /admin
    route:
    - destination:
        host: api-gateway.api-gateway.svc.cluster.local
        port:
          number: 8080
    timeout: 60s
    headers:
      request:
        add:
          x-admin-request: "true"

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: grafana-vs
  namespace: monitoring
spec:
  hosts:
  - grafana.publish-ai.local
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: grafana.monitoring.svc.cluster.local
        port:
          number: 3000
    timeout: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: prometheus-vs
  namespace: monitoring
spec:
  hosts:
  - prometheus.publish-ai.local
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: prometheus.monitoring.svc.cluster.local
        port:
          number: 9090
    timeout: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: jaeger-vs
  namespace: monitoring
spec:
  hosts:
  - jaeger.publish-ai.local
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: jaeger-query.monitoring.svc.cluster.local
        port:
          number: 16686
    timeout: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: alertmanager-vs
  namespace: monitoring
spec:
  hosts:
  - alertmanager.publish-ai.local
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: alertmanager.monitoring.svc.cluster.local
        port:
          number: 9093
    timeout: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: egress-gateway
  namespace: istio-system
spec:
  selector:
    istio: egressgateway
  servers:
  # External API access (OpenAI, Anthropic, etc.)
  - port:
      number: 443
      name: tls-external-apis
      protocol: TLS
    hosts:
    - api.openai.com
    - api.anthropic.com
    - trends.google.com
    - www.googleapis.com
    - kdp.amazon.com
    tls:
      mode: PASSTHROUGH

---
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-apis
  namespace: istio-system
spec:
  hosts:
  - api.openai.com
  - api.anthropic.com
  - trends.google.com
  - www.googleapis.com
  - kdp.amazon.com
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: external-apis-vs
  namespace: istio-system
spec:
  hosts:
  - api.openai.com
  - api.anthropic.com
  - trends.google.com
  - www.googleapis.com
  - kdp.amazon.com
  gateways:
  - mesh
  - egress-gateway
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - api.openai.com
      - api.anthropic.com
      - trends.google.com
      - www.googleapis.com
      - kdp.amazon.com
    route:
    - destination:
        host: istio-egressgateway.istio-system.svc.cluster.local
        port:
          number: 443
      weight: 100
  - match:
    - gateways:
      - egress-gateway
      port: 443
      sniHosts:
      - api.openai.com
      - api.anthropic.com
      - trends.google.com
      - www.googleapis.com
      - kdp.amazon.com
    route:
    - destination:
        host: api.openai.com
        port:
          number: 443
      weight: 30
    - destination:
        host: api.anthropic.com
        port:
          number: 443
      weight: 30
    - destination:
        host: trends.google.com
        port:
          number: 443
      weight: 20
    - destination:
        host: www.googleapis.com
        port:
          number: 443
      weight: 10
    - destination:
        host: kdp.amazon.com
        port:
          number: 443
      weight: 10