apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: api-gateway-mtls
  namespace: api-gateway
spec:
  selector:
    matchLabels:
      app: api-gateway
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: infrastructure-mtls
  namespace: infrastructure
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tier1-services-mtls
  namespace: tier1-services
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tier2-services-mtls
  namespace: tier2-services
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tier3-services-mtls
  namespace: tier3-services
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: monitoring-mtls
  namespace: monitoring
spec:
  mtls:
    mode: PERMISSIVE  # Allow both mTLS and plaintext for external access

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: api-gateway-access
  namespace: api-gateway
spec:
  selector:
    matchLabels:
      app: api-gateway
  rules:
  # Allow health check endpoints from anywhere
  - to:
    - operation:
        paths: ["/health", "/ready", "/metrics"]
  
  # Allow API access with valid JWT or API key
  - to:
    - operation:
        paths: ["/api/*"]
    when:
    - key: request.headers[authorization]
      values: ["Bearer *"]
    - key: request.headers[x-api-key]
      notValues: [""]
  
  # Allow admin access only from specific sources
  - from:
    - source:
        principals: ["cluster.local/ns/api-gateway/sa/api-gateway-admin"]
    to:
    - operation:
        paths: ["/admin/*"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: infrastructure-services-access
  namespace: infrastructure
spec:
  rules:
  # Allow access from API Gateway
  - from:
    - source:
        principals: ["cluster.local/ns/api-gateway/sa/api-gateway"]
  
  # Allow access from monitoring
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus"]
  
  # Allow inter-service communication within infrastructure
  - from:
    - source:
        namespaces: ["infrastructure"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tier1-services-access
  namespace: tier1-services
spec:
  rules:
  # Allow access from API Gateway
  - from:
    - source:
        principals: ["cluster.local/ns/api-gateway/sa/api-gateway"]
  
  # Allow access from monitoring
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus"]
  
  # Allow inter-service communication within tier1
  - from:
    - source:
        namespaces: ["tier1-services"]
  
  # Allow access from other tiers for specific operations
  - from:
    - source:
        namespaces: ["tier2-services", "tier3-services"]
    to:
    - operation:
        methods: ["GET", "POST"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tier2-services-access
  namespace: tier2-services
spec:
  rules:
  # Allow access from API Gateway
  - from:
    - source:
        principals: ["cluster.local/ns/api-gateway/sa/api-gateway"]
  
  # Allow access from tier1 services
  - from:
    - source:
        namespaces: ["tier1-services"]
  
  # Allow access from monitoring
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus"]
  
  # Allow inter-service communication within tier2
  - from:
    - source:
        namespaces: ["tier2-services"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tier3-services-access
  namespace: tier3-services
spec:
  rules:
  # Allow access from API Gateway
  - from:
    - source:
        principals: ["cluster.local/ns/api-gateway/sa/api-gateway"]
  
  # Allow access from tier1 and tier2 services
  - from:
    - source:
        namespaces: ["tier1-services", "tier2-services"]
  
  # Allow access from monitoring
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus"]
  
  # Allow inter-service communication within tier3
  - from:
    - source:
        namespaces: ["tier3-services"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: monitoring-access
  namespace: monitoring
spec:
  rules:
  # Allow access from anywhere for external monitoring tools
  - {}
  
  # But restrict admin operations
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus-admin"]
    to:
    - operation:
        paths: ["/admin/*", "/api/v1/admin/*"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: external-api-access
  namespace: istio-system
spec:
  selector:
    matchLabels:
      app: istio-egressgateway
  rules:
  # Allow AI services to access external APIs
  - from:
    - source:
        namespaces: ["tier1-services", "tier2-services", "tier3-services"]
    to:
    - operation:
        hosts: ["api.openai.com", "api.anthropic.com"]
  
  # Allow market intelligence to access Google Trends
  - from:
    - source:
        principals: ["cluster.local/ns/tier1-services/sa/market-intelligence-service"]
    to:
    - operation:
        hosts: ["trends.google.com", "www.googleapis.com"]
  
  # Allow publishing service to access Amazon KDP
  - from:
    - source:
        principals: ["cluster.local/ns/tier1-services/sa/publishing-service"]
    to:
    - operation:
        hosts: ["kdp.amazon.com"]

---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: api-gateway-jwt
  namespace: api-gateway
spec:
  selector:
    matchLabels:
      app: api-gateway
  jwtRules:
  - issuer: "https://api.publish-ai.local"
    jwksUri: "https://api.publish-ai.local/.well-known/jwks.json"
    audiences:
    - "publish-ai-platform"
    fromHeaders:
    - name: Authorization
      prefix: "Bearer "
    fromParams:
    - "access_token"

---
apiVersion: v1
kind: Secret
metadata:
  name: publish-ai-tls-certs
  namespace: istio-system
type: kubernetes.io/tls
data:
  # Self-signed certificate for development (replace with real certs in production)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURQekNDQWllZ0F3SUJBZ0lVYzJkdEZLcXNkN29HaGdUODdZeW5zQ1B4cGpFd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0xURXJNQ2tHQTFVRUF3d2lZWEJwTG5CMVltSnNhWE5vTFdGcExteHZZMkZzTG1OdmJTQkRaWEowYVdacApZMkYwWlRBZUZ3MHlOREEwTVRrd01UUXhOREJhRncweU5EQTFNVGt3TVRReE5EQmFNQzB4S3pBcEJnTlZCQU1NCkltRndhUzV3ZFdKaWJHbHphQzFoYVM1c2IyTmhiQzVqYjIwZ1EyVnlkR2xtYVdOaGRHVXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRENBNUJ4NXdILy9sTVJkMklkbTdPR3JZaUZFc1ZqTEJNSwpZaGlGWGFjbjdaK2VCbGRJQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBd0lEQVFBQm8xTXdVVEFkQmdOVkhRNEVGZ1FVZEhCTFdZM3Nnak1rT0YyR2gxTXUKYnNjYVpvRXdId1lEVlIwakJCZ3dGb0FVZEhCTFdZM3Nnak1rT0YyR2gxTXVic2NhWm9Fd0R3WURWUjBUQVFILwpCQVV3QXdFQi96QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2UUlCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktjd2dnU2pBZ0VBQW9JQkFRRENBNUJ4NXdILy9sTVIKZDJJZG03T0dyWWlGRXNWakxCTUtZaGlGWGFjbjdaK2VCbGRJQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBd0lEQVFBQkFvSUJBUUNtaTdlZ1ova09SSEF4VGNQWEt3c3ZRNlFnVy92ZkJOVlUKMEE0R2V4WjFGZWJGKzBHQmhKbDhxNzJWdnJOdFptTCtGNXZYV1UzUlRCaUJaQ2dRYWVyUTFYaFdFNktrT01WZQpEUGJNTmdOZjJ6aTVPTjhpV3p4V2JDazJnYWVydkN5VERkOFNaVHJMUXNwTzVFWUZFemZGSGllSDVadUU2VVFvCkE5blBBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0K

---
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-tls-certs
  namespace: monitoring
type: kubernetes.io/tls
data:
  # Self-signed certificate for development (replace with real certs in production)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURQekNDQWllZ0F3SUJBZ0lVYzJkdEZLcXNkN29HaGdUODdZeW5zQ1B4cGpFd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0xURXJNQ2tHQTFVRUF3d2lZWEJwTG5CMVltSnNhWE5vTFdGcExteHZZMkZzTG1OdmJTQkRaWEowYVdacApZMkYwWlRBZUZ3MHlOREEwTVRrd01UUXhOREJhRncweU5EQTFNVGt3TVRReE5EQmFNQzB4S3pBcEJnTlZCQU1NCkltRndhUzV3ZFdKaWJHbHphQzFoYVM1c2IyTmhiQzVqYjIwZ1EyVnlkR2xtYVdOaGRHVXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRENBNUJ4NXdILy9sTVJkMklkbTdPR3JZaUZFc1ZqTEJNSwpZaGlGWGFjbjdaK2VCbGRJQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBd0lEQVFBQm8xTXdVVEFkQmdOVkhRNEVGZ1FVZEhCTFdZM3Nnak1rT0YyR2gxTXUKYnNjYVpvRXdId1lEVlIwakJCZ3dGb0FVZEhCTFdZM3Nnak1rT0YyR2gxTXVic2NhWm9Fd0R3WURWUjBUQVFILwpCQVV3QXdFQi96QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2UUlCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktjd2dnU2pBZ0VBQW9JQkFRRENBNUJ4NXdILy9sTVIKZDJJZG03T0dyWWlGRXNWakxCTUtZaGlGWGFjbjdaK2VCbGRJQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBd0lEQVFBQkFvSUJBUUNtaTdlZ1ova09SSEF4VGNQWEt3c3ZRNlFnVy92ZkJOVlUKMEE0R2V4WjFGZWJGKzBHQmhKbDhxNzJWdnJOdFptTCtGNXZYV1UzUlRCaUJaQ2dRYWVyUTFYaFdFNktrT01WZQpEUGJNTmdOZjJ6aTVPTjhpV3p4V2JDazJnYWVydkN5VERkOFNaVHJMUXNwTzVFWUZFemZGSGllSDVadUU2VVFvCkE5blBBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0K