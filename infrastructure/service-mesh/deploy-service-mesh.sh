#!/bin/bash

# Publish AI Service Mesh Deployment Script
# Deploys Istio service mesh with advanced traffic management and security

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ISTIO_VERSION="1.20.1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMP_DIR="/tmp/istio-install"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check cluster admin privileges
    if ! kubectl auth can-i create clusterrole &> /dev/null; then
        error "Insufficient permissions. Please run with cluster admin privileges"
        exit 1
    fi
    
    # Check cluster resources
    local nodes=$(kubectl get nodes --no-headers | wc -l)
    if [ "$nodes" -lt 3 ]; then
        warn "Cluster has less than 3 nodes. Service mesh may not be highly available"
    fi
    
    log "Prerequisites check passed"
}

# Download and install Istio
install_istio() {
    log "Installing Istio $ISTIO_VERSION..."
    
    # Create temp directory
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    # Download Istio if not already present
    if [ ! -f "istio-$ISTIO_VERSION/bin/istioctl" ]; then
        info "Downloading Istio $ISTIO_VERSION..."
        curl -L https://istio.io/downloadIstio | ISTIO_VERSION=$ISTIO_VERSION sh -
    fi
    
    # Add istioctl to PATH
    export PATH="$TEMP_DIR/istio-$ISTIO_VERSION/bin:$PATH"
    
    # Verify installation
    if ! istioctl version --remote=false &> /dev/null; then
        error "Failed to install istioctl"
        exit 1
    fi
    
    log "Istio CLI installed successfully"
}

# Pre-install validation
pre_install_validation() {
    log "Running pre-installation validation..."
    
    # Check cluster compatibility
    istioctl x precheck
    
    # Check for conflicting installations
    if kubectl get namespace istio-system &> /dev/null; then
        warn "istio-system namespace already exists"
        read -p "Do you want to continue? This may overwrite existing configuration. (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            error "Installation cancelled by user"
            exit 1
        fi
    fi
    
    log "Pre-installation validation passed"
}

# Install Istio control plane
install_control_plane() {
    log "Installing Istio control plane..."
    
    # Apply Istio installation configuration
    kubectl apply -f "$SCRIPT_DIR/istio-installation.yaml"
    
    # Wait for Istio operator to be ready
    log "Waiting for Istio operator to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/istio-operator -n istio-operator 2>/dev/null || {
        # Install Istio operator if not present
        istioctl operator init
        kubectl wait --for=condition=available --timeout=300s deployment/istio-operator -n istio-operator
    }
    
    # Install Istio control plane
    kubectl apply -f - <<EOF
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: publish-ai-control-plane
  namespace: istio-system
spec:
  values:
    global:
      meshID: publish-ai-mesh
      network: publish-ai-network
  components:
    pilot:
      k8s:
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        service:
          type: LoadBalancer
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
  meshConfig:
    accessLogFile: /dev/stdout
    defaultProviders:
      tracing:
      - "jaeger"
    extensionProviders:
    - name: jaeger
      envoyOtelAls:
        service: jaeger-collector.monitoring.svc.cluster.local
        port: 14250
EOF
    
    # Wait for control plane to be ready
    log "Waiting for Istio control plane to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/istiod -n istio-system
    
    # Verify installation
    istioctl verify-install
    
    log "Istio control plane installed successfully"
}

# Configure namespace injection
configure_namespace_injection() {
    log "Configuring automatic sidecar injection..."
    
    local namespaces=("api-gateway" "infrastructure" "tier1-services" "tier2-services" "tier3-services")
    
    for ns in "${namespaces[@]}"; do
        if kubectl get namespace "$ns" &> /dev/null; then
            info "Enabling sidecar injection for namespace $ns"
            kubectl label namespace "$ns" istio-injection=enabled --overwrite
            
            # Restart deployments to inject sidecars
            kubectl rollout restart deployment -n "$ns" 2>/dev/null || {
                warn "No deployments found in namespace $ns"
            }
        else
            warn "Namespace $ns not found"
        fi
    done
    
    # Configure monitoring namespace with permissive injection
    if kubectl get namespace monitoring &> /dev/null; then
        info "Configuring monitoring namespace with permissive injection"
        kubectl label namespace monitoring istio-injection=enabled --overwrite
        kubectl label namespace monitoring istio-injection-policy=permissive --overwrite
    fi
    
    log "Namespace injection configured"
}

# Deploy gateways and virtual services
deploy_gateways() {
    log "Deploying gateways and virtual services..."
    
    kubectl apply -f "$SCRIPT_DIR/gateways.yaml"
    
    # Wait for gateways to be ready
    log "Waiting for ingress gateway to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/istio-ingressgateway -n istio-system
    
    # Get external IP
    local external_ip=""
    local retry_count=0
    while [ -z "$external_ip" ] && [ $retry_count -lt 30 ]; do
        external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
        if [ -z "$external_ip" ]; then
            external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null)
        fi
        if [ -z "$external_ip" ]; then
            info "Waiting for external IP assignment..."
            sleep 10
            ((retry_count++))
        fi
    done
    
    if [ -n "$external_ip" ]; then
        info "Ingress Gateway external address: $external_ip"
    else
        warn "External IP not assigned yet. Check LoadBalancer service later."
    fi
    
    log "Gateways deployed successfully"
}

# Apply security policies
apply_security_policies() {
    log "Applying security policies..."
    
    kubectl apply -f "$SCRIPT_DIR/security-policies.yaml"
    
    # Verify mTLS is working
    log "Verifying mTLS configuration..."
    istioctl authn tls-check || {
        warn "mTLS verification failed. Some services may not be ready yet."
    }
    
    log "Security policies applied successfully"
}

# Apply traffic policies
apply_traffic_policies() {
    log "Applying traffic policies..."
    
    kubectl apply -f "$SCRIPT_DIR/traffic-policies.yaml"
    
    log "Traffic policies applied successfully"
}

# Configure observability
configure_observability() {
    log "Configuring enhanced observability..."
    
    # Create telemetry configuration
    kubectl apply -f - <<EOF
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: default
  namespace: istio-system
spec:
  metrics:
  - providers:
    - prometheus
  tracing:
  - providers:
    - jaeger
  accessLogging:
  - providers:
    - envoy
EOF
    
    # Configure Grafana dashboards for Istio
    if kubectl get configmap grafana-config -n monitoring &> /dev/null; then
        info "Adding Istio dashboards to Grafana..."
        
        # Add Istio dashboard ConfigMap
        kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-istio
  namespace: monitoring
data:
  istio-service-mesh.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Istio Service Mesh",
        "tags": ["istio", "service-mesh"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Mesh Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(istio_requests_total[5m])) by (destination_service_name)",
                "legendFormat": "{{destination_service_name}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "mTLS Status",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(istio_requests_total{security_policy=\"mutual_tls\"}) / sum(istio_requests_total) * 100",
                "legendFormat": "mTLS %"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "10s"
      }
    }
EOF
        
        # Restart Grafana to load new dashboard
        kubectl rollout restart deployment/grafana -n monitoring
    fi
    
    log "Observability configured successfully"
}

# Run connectivity tests
run_connectivity_tests() {
    log "Running connectivity tests..."
    
    # Create test pod
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: istio-test-pod
  namespace: default
  labels:
    app: test
spec:
  containers:
  - name: test
    image: nicolaka/netshoot
    command: ["/bin/sleep", "3600"]
  restartPolicy: Never
EOF
    
    # Wait for pod to be ready
    kubectl wait --for=condition=ready pod/istio-test-pod -n default --timeout=60s
    
    # Test internal connectivity
    info "Testing internal service connectivity..."
    kubectl exec istio-test-pod -n default -- curl -s -o /dev/null -w "%{http_code}" http://api-gateway.api-gateway:8080/health || {
        warn "Internal connectivity test failed"
    }
    
    # Cleanup test pod
    kubectl delete pod istio-test-pod -n default --ignore-not-found=true
    
    log "Connectivity tests completed"
}

# Verify deployment
verify_deployment() {
    log "Verifying service mesh deployment..."
    
    # Check Istio components
    info "Checking Istio components..."
    kubectl get pods -n istio-system
    
    # Check injection status
    info "Checking sidecar injection status..."
    local namespaces=("api-gateway" "infrastructure" "tier1-services" "tier2-services" "tier3-services")
    
    for ns in "${namespaces[@]}"; do
        if kubectl get namespace "$ns" &> /dev/null; then
            local injected=$(kubectl get pods -n "$ns" -o jsonpath='{.items[*].spec.containers[*].name}' | grep -c istio-proxy || echo "0")
            local total=$(kubectl get pods -n "$ns" --no-headers | wc -l)
            info "Namespace $ns: $injected/$total pods with sidecars"
        fi
    done
    
    # Check gateway status
    info "Checking gateway status..."
    kubectl get gateway,virtualservice -A
    
    # Check security policies
    info "Checking security policies..."
    kubectl get peerauthentication,authorizationpolicy -A
    
    # Show service mesh status
    log "Service mesh status:"
    istioctl proxy-status
    
    log "Service mesh verification completed"
}

# Show access information
show_access_info() {
    log "Service mesh access information:"
    
    # Get ingress gateway external IP
    local external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
    if [ -z "$external_ip" ]; then
        external_ip=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null)
    fi
    
    if [ -n "$external_ip" ]; then
        info "External Gateway IP: $external_ip"
        info ""
        info "Service URLs (add to /etc/hosts or DNS):"
        info "$external_ip api.publish-ai.local"
        info "$external_ip grafana.publish-ai.local"
        info "$external_ip prometheus.publish-ai.local"
        info "$external_ip jaeger.publish-ai.local"
        info ""
        info "Access URLs:"
        info "API Gateway: https://api.publish-ai.local"
        info "Grafana: https://grafana.publish-ai.local"
        info "Prometheus: https://prometheus.publish-ai.local"
        info "Jaeger: https://jaeger.publish-ai.local"
    else
        warn "External IP not available. Use port-forwarding for access:"
        info "kubectl port-forward svc/istio-ingressgateway 8080:80 -n istio-system"
    fi
    
    # Show useful commands
    info ""
    info "Useful commands:"
    info "istioctl proxy-status                     # Check proxy status"
    info "istioctl analyze                          # Analyze configuration"
    info "istioctl dashboard kiali                  # Open Kiali dashboard"
    info "istioctl dashboard grafana                # Open Grafana dashboard"
    info "istioctl dashboard jaeger                 # Open Jaeger dashboard"
}

# Main deployment function
main() {
    log "Starting Istio Service Mesh Deployment for Publish AI"
    
    check_prerequisites
    install_istio
    pre_install_validation
    install_control_plane
    configure_namespace_injection
    deploy_gateways
    apply_security_policies
    apply_traffic_policies
    configure_observability
    run_connectivity_tests
    verify_deployment
    show_access_info
    
    log "✅ Service mesh deployment completed successfully!"
    log ""
    info "Next steps:"
    info "1. Update DNS or /etc/hosts with the external IP"
    info "2. Configure TLS certificates for production"
    info "3. Review and customize security policies"
    info "4. Set up monitoring and alerting for mesh metrics"
    info "5. Implement canary deployment strategies"
    log ""
    log "For troubleshooting, use: istioctl analyze"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "verify")
        verify_deployment
        ;;
    "status")
        show_access_info
        ;;
    "cleanup")
        warn "Removing service mesh..."
        kubectl delete istiooperator publish-ai-control-plane -n istio-system --ignore-not-found=true
        kubectl delete namespace istio-system --ignore-not-found=true
        kubectl delete namespace istio-operator --ignore-not-found=true
        
        # Remove injection labels
        local namespaces=("api-gateway" "infrastructure" "tier1-services" "tier2-services" "tier3-services" "monitoring")
        for ns in "${namespaces[@]}"; do
            kubectl label namespace "$ns" istio-injection- --ignore-not-found=true
        done
        
        log "Service mesh removed"
        ;;
    "help")
        echo "Usage: $0 [deploy|verify|status|cleanup|help]"
        echo "  deploy   - Deploy the complete service mesh (default)"
        echo "  verify   - Verify existing deployment"
        echo "  status   - Show access information"
        echo "  cleanup  - Remove the service mesh"
        echo "  help     - Show this help message"
        ;;
    *)
        error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac