apiVersion: v1
kind: Namespace
metadata:
  name: istio-system
  labels:
    name: istio-system

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-config
  namespace: istio-system
data:
  mesh: |
    # Mesh configuration for Publish AI platform
    defaultConfig:
      # Enable auto mTLS
      meshMTLS:
        minProtocolVersion: TLSV1_2
      # Proxy configuration
      proxyStatsMatcher:
        inclusionRegexps:
        - ".*circuit_breakers.*"
        - ".*upstream_rq_retry.*"
        - ".*upstream_rq_pending.*"
        - ".*_cx_.*"
        exclusionRegexps:
        - ".*osconfig.*"
      # Enhanced logging
      defaultProviders:
        metrics:
        - prometheus
        tracing:
        - jaeger
        accessLogging:
        - envoy
    # Global settings
    extensionProviders:
    - name: jaeger
      envoyOtelAls:
        service: jaeger-collector.monitoring.svc.cluster.local
        port: 14250
    - name: prometheus
      prometheus:
        configOverride:
          metric_relabeling_configs:
          - source_labels: [__name__]
            regex: 'istio_.*'
            target_label: __tmp_istio_metric
    # Access logging
    defaultProviders:
      accessLogging:
      - name: otel
    meshConfig:
      defaultConfig:
        gatewayTopology:
          numTrustedProxies: 1

---
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: publish-ai-control-plane
  namespace: istio-system
spec:
  # Istio control plane configuration
  values:
    global:
      meshID: publish-ai-mesh
      network: publish-ai-network
      # Enable enhanced security
      jwtPolicy: third-party-jwt
    pilot:
      env:
        EXTERNAL_ISTIOD: false
        PILOT_ENABLE_WORKLOAD_ENTRY_AUTOREGISTRATION: true
        PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY: true
        PILOT_TRACE_SAMPLING: 100.0
    telemetry:
      v2:
        enabled: true
        prometheus:
          configOverride:
            disable_host_header_fallback: true
            
  components:
    # Istiod (control plane)
    pilot:
      k8s:
        env:
        - name: PILOT_TRACE_SAMPLING
          value: "100.0"
        - name: PILOT_ENABLE_ALPHA_GATEWAY_API
          value: "true"
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        hpaSpec:
          minReplicas: 2
          maxReplicas: 5
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80

    # Ingress Gateway
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        service:
          type: LoadBalancer
          ports:
          - port: 15021
            targetPort: 15021
            name: status-port
            protocol: TCP
          - port: 80
            targetPort: 8080
            name: http2
            protocol: TCP
          - port: 443
            targetPort: 8443
            name: https
            protocol: TCP
          - port: 15090
            targetPort: 15090
            name: http-monitoring
            protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
        hpaSpec:
          minReplicas: 2
          maxReplicas: 5
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80

    # Egress Gateway (for external API calls)
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        service:
          type: ClusterIP
          ports:
          - port: 80
            targetPort: 8080
            name: http2
            protocol: TCP
          - port: 443
            targetPort: 8443
            name: https
            protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi

  # Mesh configuration
  meshConfig:
    # Default access logging
    accessLogFile: /dev/stdout
    accessLogFormat: |
      [%START_TIME%] "%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%"
      %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT%
      %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%"
      "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"
      %UPSTREAM_CLUSTER% %UPSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_LOCAL_ADDRESS%
      %DOWNSTREAM_REMOTE_ADDRESS% %REQUESTED_SERVER_NAME% %ROUTE_NAME%
    
    # Enhanced tracing
    defaultProviders:
      tracing:
      - "jaeger"
    
    # Extension providers
    extensionProviders:
    - name: jaeger
      envoyOtelAls:
        service: jaeger-collector.monitoring.svc.cluster.local
        port: 14250
    
    # Enable auto mTLS
    trustDomain: publish-ai.local
    
    # Proxy configuration
    defaultConfig:
      proxyStatsMatcher:
        inclusionRegexps:
        - ".*outlier_detection.*"
        - ".*circuit_breakers.*"
        - ".*upstream_rq_retry.*"
        - ".*upstream_rq_pending.*"
        - ".*_cx_.*"
      gatewayTopology:
        numTrustedProxies: 1

---
apiVersion: v1
kind: Service
metadata:
  name: istiod-external
  namespace: istio-system
  labels:
    app: istiod
spec:
  type: LoadBalancer
  selector:
    app: istiod
  ports:
  - port: 15010
    name: grpc-xds
    protocol: TCP
  - port: 15011
    name: grpc-xds-secure
    protocol: TCP
  - port: 15014
    name: http-monitoring
    protocol: TCP