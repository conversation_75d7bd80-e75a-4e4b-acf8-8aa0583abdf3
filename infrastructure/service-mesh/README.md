# Istio Service Mesh for Publish AI

A production-ready Istio service mesh implementation providing advanced traffic management, security, and observability for the Publish AI microservices platform.

## Overview

The service mesh layer adds the following capabilities to the microservices architecture:

- **Traffic Management**: Intelligent load balancing, circuit breaking, and fault injection
- **Security**: Automatic mTLS, fine-grained authorization policies, and external API control
- **Observability**: Enhanced metrics, distributed tracing, and access logging
- **Gateway Management**: Unified ingress/egress control with SSL termination
- **Policy Enforcement**: Rate limiting, security policies, and compliance controls

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Istio Service Mesh                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │   Ingress   │    │   Control   │    │   Egress    │         │
│  │   Gateway   │    │    Plane    │    │   Gateway   │         │
│  │             │    │  (istiod)   │    │             │         │
│  │    :80      │    │   :15010    │    │    :443     │         │
│  │    :443     │    │             │    │             │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │               │
│         │                   │                   │               │
│  ┌──────┴─────────────────────┴─────────────────┴──────┐        │
│  │                Data Plane                           │        │
│  │              (Envoy Sidecars)                       │        │
│  │                                                     │        │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │        │
│  │  │Service A│  │Service B│  │Service C│  │Service D│ │        │
│  │  │ + Proxy │  │ + Proxy │  │ + Proxy │  │ + Proxy │ │        │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │        │
│  │                                                     │        │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐ │        │
│  │  │Service E│  │Service F│  │Service G│  │Service H│ │        │
│  │  │ + Proxy │  │ + Proxy │  │ + Proxy │  │ + Proxy │ │        │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘ │        │
│  └─────────────────────────────────────────────────────┘        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Components

### Control Plane (istiod)

- **Purpose**: Centralized configuration and certificate management
- **Features**:
  - Service discovery integration
  - Configuration validation and distribution
  - Certificate authority for mTLS
  - Sidecar proxy configuration

### Ingress Gateway

- **Purpose**: External traffic entry point
- **Features**:
  - SSL termination
  - Load balancing
  - Virtual hosting
  - Rate limiting

### Egress Gateway

- **Purpose**: Controlled external API access
- **Features**:
  - External service policies
  - Traffic monitoring
  - Compliance enforcement
  - API rate limiting

### Data Plane (Envoy Sidecars)

- **Purpose**: Service-to-service communication
- **Features**:
  - Automatic mTLS
  - Circuit breaking
  - Retry logic
  - Telemetry collection

## Installation

### Prerequisites

- Kubernetes cluster with 3+ nodes
- 8GB+ available memory across cluster
- Cluster admin permissions
- kubectl configured

### Quick Deployment

```bash
# Deploy complete service mesh
./deploy-service-mesh.sh

# Verify deployment
./deploy-service-mesh.sh verify

# Check status and access URLs
./deploy-service-mesh.sh status

# Remove service mesh
./deploy-service-mesh.sh cleanup
```

### Manual Installation

```bash
# Install Istio operator
istioctl operator init

# Apply Istio configuration
kubectl apply -f istio-installation.yaml

# Deploy gateways and policies
kubectl apply -f gateways.yaml
kubectl apply -f security-policies.yaml
kubectl apply -f traffic-policies.yaml

# Enable sidecar injection
kubectl label namespace api-gateway istio-injection=enabled
kubectl label namespace tier1-services istio-injection=enabled
kubectl label namespace tier2-services istio-injection=enabled
kubectl label namespace tier3-services istio-injection=enabled
```

## Configuration

### Service Mesh Namespaces

- **istio-system**: Control plane components
- **api-gateway**: Injection enabled, strict mTLS
- **infrastructure**: Injection enabled, strict mTLS
- **tier1-services**: Injection enabled, strict mTLS
- **tier2-services**: Injection enabled, strict mTLS
- **tier3-services**: Injection enabled, strict mTLS
- **monitoring**: Injection enabled, permissive mTLS

### Traffic Policies

#### Connection Pooling

```yaml
# API Gateway - High throughput
maxConnections: 100
connectTimeout: 30s
http1MaxPendingRequests: 50
http2MaxRequests: 100

# Tier 1 Services - AI workloads
maxConnections: 30
connectTimeout: 30s
timeout: 300s  # Long timeout for AI operations

# Tier 2/3 Services - Standard workloads
maxConnections: 20
connectTimeout: 20s
timeout: 60s
```

#### Circuit Breaking

```yaml
# Standard circuit breaker configuration
consecutiveGatewayErrors: 5
consecutive5xxErrors: 5
interval: 30s
baseEjectionTime: 30s
maxEjectionPercent: 50
```

#### Load Balancing

- **API Gateway**: LEAST_CONN for optimal distribution
- **Infrastructure**: ROUND_ROBIN for even distribution
- **AI Services**: LEAST_CONN for resource optimization
- **Standard Services**: ROUND_ROBIN for simplicity

### Security Policies

#### mTLS Configuration

```yaml
# Strict mTLS for all service communication
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: STRICT
```

#### Authorization Policies

- **API Gateway**: JWT/API key authentication required
- **Inter-service**: Namespace-based access control
- **External APIs**: Egress gateway enforcement
- **Monitoring**: Permissive access for scraping

#### External API Control

```yaml
# Controlled access to external APIs
hosts:
  - api.openai.com
  - api.anthropic.com
  - trends.google.com
  - kdp.amazon.com
```

## Gateway Configuration

### Ingress Gateways

```yaml
# External domain routing
hosts:
  - api.publish-ai.local # API Gateway
  - grafana.publish-ai.local # Grafana UI
  - prometheus.publish-ai.local # Prometheus UI
  - jaeger.publish-ai.local # Jaeger UI
```

### Virtual Services

- **API Gateway**: Advanced routing with retries and fault injection
- **Monitoring Tools**: Direct routing with health checks
- **External APIs**: Weighted routing through egress gateway

### TLS Configuration

- **Certificate Management**: Kubernetes secrets
- **SSL Termination**: At ingress gateway
- **Internal Communication**: Automatic mTLS
- **Certificate Rotation**: Istio automatic CA

## Traffic Management

### Fault Injection

```yaml
# 0.1% of requests get 5s delay for testing
fault:
  delay:
    percentage:
      value: 0.1
    fixedDelay: 5s
```

### Retry Policies

```yaml
# Intelligent retry configuration
retries:
  attempts: 3
  perTryTimeout: 30s
  retryOn: 5xx,reset,connect-failure,refused-stream
```

### Rate Limiting

- **Global**: 1000 requests/minute per IP
- **Service-specific**: Configurable per service
- **Client-based**: Enhanced limits for authenticated users
- **Admin**: Higher limits for administrative operations

## Observability

### Enhanced Metrics

```prometheus
# Service mesh specific metrics
istio_requests_total
istio_request_duration_milliseconds
istio_tcp_connections_opened_total
istio_tcp_connections_closed_total

# Custom metrics
custom_request_total{method,path_prefix,user_agent_type}
```

### Distributed Tracing

- **Integration**: Jaeger backend
- **Sampling**: 100% for development, configurable for production
- **Propagation**: Automatic trace header propagation
- **Custom Spans**: Service-specific instrumentation

### Access Logging

```json
{
  "timestamp": "[%START_TIME%]",
  "method": "%REQ(:METHOD)%",
  "path": "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%",
  "protocol": "%PROTOCOL%",
  "response_code": "%RESPONSE_CODE%",
  "response_flags": "%RESPONSE_FLAGS%",
  "bytes_received": "%BYTES_RECEIVED%",
  "bytes_sent": "%BYTES_SENT%",
  "duration": "%DURATION%",
  "upstream_service_time": "%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%",
  "x_forwarded_for": "%REQ(X-FORWARDED-FOR)%",
  "user_agent": "%REQ(USER-AGENT)%",
  "request_id": "%REQ(X-REQUEST-ID)%",
  "authority": "%REQ(:AUTHORITY)%",
  "upstream_host": "%UPSTREAM_HOST%"
}
```

## Security

### mTLS Implementation

- **Certificate Authority**: Istio-managed CA
- **Certificate Rotation**: Automatic every 24 hours
- **Cipher Suites**: TLSv1.2+ with strong ciphers
- **Verification**: Strict peer verification

### Authorization Framework

```yaml
# Example authorization policy
rules:
  - from:
      - source:
          principals: ["cluster.local/ns/api-gateway/sa/api-gateway"]
    to:
      - operation:
          methods: ["GET", "POST"]
    when:
      - key: request.headers[authorization]
        values: ["Bearer *"]
```

### External API Security

- **Egress Control**: All external traffic through egress gateway
- **API Allowlist**: Explicitly defined external hosts
- **Traffic Monitoring**: Full visibility into external calls
- **Rate Limiting**: Per-service external API limits

## Monitoring and Alerting

### Istio-specific Alerts

```yaml
# Service mesh health alerts
- alert: ServiceMeshDown
  expr: istio_build == 0
  for: 1m
  labels:
    severity: critical

- alert: HighMeshLatency
  expr: histogram_quantile(0.99, istio_request_duration_milliseconds_bucket) > 1000
  for: 5m
  labels:
    severity: warning

- alert: mTLSFailure
  expr: increase(istio_requests_total{security_policy!="mutual_tls"}[5m]) > 10
  for: 2m
  labels:
    severity: critical
```

### Grafana Dashboards

1. **Service Mesh Overview**: Global mesh health and performance
2. **Service-to-Service Communication**: Inter-service traffic analysis
3. **Security Overview**: mTLS adoption and security violations
4. **Gateway Performance**: Ingress/egress traffic patterns

## Troubleshooting

### Common Issues

#### Sidecar Injection Problems

```bash
# Check injection status
kubectl get pods -n <namespace> -o jsonpath='{.items[*].spec.containers[*].name}'

# Verify injection labels
kubectl get namespace <namespace> --show-labels

# Force injection
kubectl delete pods --all -n <namespace>
```

#### mTLS Issues

```bash
# Check mTLS configuration
istioctl authn tls-check

# Verify certificates
istioctl proxy-config secret <pod-name> -n <namespace>

# Check peer authentication
kubectl get peerauthentication -A
```

#### Gateway Connectivity

```bash
# Check gateway status
kubectl get gateway,virtualservice -A

# Verify external IP
kubectl get svc istio-ingressgateway -n istio-system

# Test connectivity
curl -v http://<external-ip>/health
```

#### Configuration Issues

```bash
# Analyze configuration
istioctl analyze

# Check proxy configuration
istioctl proxy-config cluster <pod-name> -n <namespace>

# Verify routes
istioctl proxy-config routes <pod-name> -n <namespace>
```

### Debug Commands

```bash
# Proxy status
istioctl proxy-status

# Configuration dump
istioctl proxy-config all <pod-name> -n <namespace>

# Endpoint configuration
istioctl proxy-config endpoints <pod-name> -n <namespace>

# Listener configuration
istioctl proxy-config listeners <pod-name> -n <namespace>

# Route configuration
istioctl proxy-config routes <pod-name> -n <namespace>

# Cluster configuration
istioctl proxy-config clusters <pod-name> -n <namespace>

# Secret configuration
istioctl proxy-config secrets <pod-name> -n <namespace>
```

## Performance Tuning

### Resource Optimization

```yaml
# Control plane resources
pilot:
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi

# Gateway resources
ingressGateways:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 2000m
      memory: 1024Mi
```

### Sidecar Optimization

```yaml
# Reduce memory footprint
global:
  proxy:
    resources:
      requests:
        cpu: 10m
        memory: 40Mi
      limits:
        cpu: 100m
        memory: 128Mi
```

### Configuration Optimization

```yaml
# Optimize for high throughput
meshConfig:
  defaultConfig:
    concurrency: 2
    proxyStatsMatcher:
      inclusionRegexps:
        - ".*outlier_detection.*"
        - ".*circuit_breakers.*"
        - ".*upstream_rq_retry.*"
      exclusionRegexps:
        - ".*osconfig.*"
```

## Production Considerations

### High Availability

- **Multi-zone Deployment**: Control plane across availability zones
- **Gateway Redundancy**: Multiple ingress/egress gateways
- **Backup Strategies**: Configuration and certificate backups
- **Disaster Recovery**: Cross-region mesh federation

### Scaling

- **Horizontal Pod Autoscaling**: For gateways and control plane
- **Resource Monitoring**: CPU and memory utilization
- **Connection Limits**: Per-service connection pooling
- **Load Testing**: Regular performance validation

### Security Hardening

- **Certificate Management**: External CA integration
- **RBAC**: Fine-grained service account permissions
- **Network Policies**: Additional network-level controls
- **Audit Logging**: Complete audit trail

### Compliance

- **Data Residency**: Traffic routing controls
- **Encryption**: End-to-end encryption validation
- **Access Control**: Comprehensive authorization policies
- **Monitoring**: Security event logging and alerting

## Integration with CI/CD

### GitOps Workflow

```yaml
# ArgoCD application for service mesh
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: istio-config
spec:
  source:
    repoURL: https://github.com/publish-ai/infrastructure
    path: service-mesh
  destination:
    cluster: https://kubernetes.default.svc
    namespace: istio-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

### Canary Deployment Integration

- **Traffic Splitting**: Gradual traffic migration
- **Automated Rollback**: Based on metrics
- **A/B Testing**: Feature flag integration
- **Blue/Green**: Zero-downtime deployments

---

For advanced configuration and enterprise features, refer to:

- [Istio Documentation](https://istio.io/latest/docs/)
- [Envoy Proxy Documentation](https://www.envoyproxy.io/docs/)
- [Service Mesh Patterns](https://www.oreilly.com/library/view/istio-up-and/9781492043775/)
