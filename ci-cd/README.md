# CI/CD Pipeline for Publish AI

This directory contains the complete CI/CD pipeline configuration for the Publish AI microservices platform. The pipeline provides automated testing, security scanning, building, and deployment across multiple environments.

## Pipeline Overview

### 🔄 **Pipeline Stages**

1. **Validate** - Code style, Docker files, Kubernetes manifests
2. **Test** - Unit tests, integration tests, agent tests
3. **Security** - SAST, dependency scanning, container scanning
4. **Build** - Docker images for all 9 microservices
5. **Deploy Dev** - Automatic deployment to development
6. **Integration Test** - Integration tests against dev environment
7. **Deploy Staging** - Manual deployment to staging
8. **E2E Test** - End-to-end tests against staging
9. **Deploy Production** - Manual deployment to production with canary strategy
10. **Post Deploy** - Verification and notifications

### 🏗️ **Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    GitLab CI/CD Pipeline                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Validate   │  │    Test     │  │  Security   │        │
│  │             │  │             │  │             │        │
│  │ • Code      │  │ • Unit      │  │ • SAST      │        │
│  │ • Docker    │  │ • Integration│  │ • Dependencies│      │
│  │ • K8s       │  │ • Agents    │  │ • Containers│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│           │                │                │              │
│           └────────────────┼────────────────┘              │
│                            │                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Build    │  │ Deploy Dev  │  │ Integration │        │
│  │             │  │             │  │    Test     │        │
│  │ • 9 Services│  │ • Auto      │  │ • API Tests │        │
│  │ • Multi-arch│  │ • K8s       │  │ • Load Test │        │
│  │ • Optimized │  │ • Rollout   │  │ • Smoke Test│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│           │                │                │              │
│           └────────────────┼────────────────┘              │
│                            │                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │Deploy Stage │  │  E2E Test   │  │Deploy Prod  │        │
│  │             │  │             │  │             │        │
│  │ • Manual    │  │ • Playwright│  │ • Manual    │        │
│  │ • Blue/Green│  │ • User Flow │  │ • Canary    │        │
│  │ • Rollback  │  │ • Performance│  │ • Verification│      │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Features

### 🚀 **Automated Deployment**

- **Development**: Automatic deployment on every commit to main branch
- **Staging**: Manual deployment with blue-green strategy
- **Production**: Manual deployment with canary strategy and automated rollback
- **Multi-environment**: Supports dev/staging/production with environment-specific configurations

### 🧪 **Comprehensive Testing**

- **Unit Tests**: 268+ tests with 80% coverage requirement
- **Integration Tests**: Service-to-service communication testing
- **Agent Tests**: PydanticAI agent functionality testing
- **E2E Tests**: Complete user journey testing with Playwright
- **Load Tests**: Performance and scalability validation

### 🔒 **Security Scanning**

- **SAST**: Static Application Security Testing
- **Dependency Scanning**: Vulnerability detection in dependencies
- **Container Scanning**: Docker image security analysis
- **Secret Detection**: Prevents secrets from being committed
- **License Compliance**: Open source license validation

### 📦 **Container Management**

- **Multi-stage Builds**: Optimized Docker images
- **Registry Integration**: GitLab Container Registry
- **Image Scanning**: Automated vulnerability scanning
- **Cleanup Automation**: Old image cleanup to save storage
- **Multi-architecture**: ARM64 and AMD64 support

### 📊 **Quality Gates**

- **Code Coverage**: Minimum 80% test coverage
- **Code Quality**: Black, isort, flake8, mypy validation
- **Performance**: Response time validation
- **Security**: Zero critical vulnerabilities
- **Availability**: Health check validation

## Configuration

### 🔐 **Required Variables**

Set these in GitLab CI/CD settings:

#### **Container Registry**
```bash
CI_REGISTRY                  # GitLab registry URL
CI_REGISTRY_USER            # Registry username
CI_REGISTRY_PASSWORD        # Registry password
```

#### **Kubernetes Access**
```bash
KUBECONFIG_DEV              # Base64 encoded kubeconfig for dev
KUBECONFIG_STAGING          # Base64 encoded kubeconfig for staging
KUBECONFIG_PROD             # Base64 encoded kubeconfig for production
```

#### **Environment URLs**
```bash
DEV_API_URL                 # https://dev.publishai.com
STAGING_API_URL             # https://staging.publishai.com
PROD_API_URL                # https://publishai.com
```

#### **Notifications**
```bash
SLACK_WEBHOOK_URL           # Slack webhook for notifications
TEAMS_WEBHOOK_URL           # Microsoft Teams webhook (optional)
```

#### **Security**
```bash
SAST_DISABLED              # Set to "true" to disable SAST
DEPENDENCY_SCANNING_DISABLED # Set to "true" to disable dep scanning
CONTAINER_SCANNING_DISABLED  # Set to "true" to disable container scanning
```

### 🎯 **Branch Strategies**

#### **Feature Branches**
- Code validation only
- Unit and integration tests
- Security scanning
- No deployment

#### **Main Branch**
- Full pipeline execution
- Automatic deployment to development
- Integration testing
- Ready for staging deployment

#### **Release Tags**
- Production deployment enabled
- Full test suite execution
- Canary deployment strategy
- Post-deployment verification

### 🔄 **Deployment Strategies**

#### **Development Environment**
```yaml
Strategy: Rolling Update
Automation: Automatic on main branch
Rollback: Manual
Health Checks: Basic
```

#### **Staging Environment**
```yaml
Strategy: Blue-Green
Automation: Manual trigger
Rollback: Automatic on failure
Health Checks: Comprehensive
```

#### **Production Environment**
```yaml
Strategy: Canary (5% → 25% → 50% → 100%)
Automation: Manual trigger with approval
Rollback: Automatic on metrics threshold
Health Checks: Full validation
```

## Service-Specific Configurations

### 🎯 **Build Configurations**

| Service | Build Context | Image Size | Build Time | Test Coverage |
|---------|--------------|------------|------------|---------------|
| API Gateway | `services/api-gateway/` | ~200MB | 3-5 min | 85%+ |
| Content Generation | `services/content-generation/` | ~1.2GB | 8-12 min | 80%+ |
| Market Intelligence | `services/market-intelligence/` | ~300MB | 4-6 min | 80%+ |
| Publishing Service | `services/publishing-service/` | ~250MB | 3-5 min | 85%+ |
| Cover Designer | `services/cover-designer/` | ~800MB | 6-8 min | 80%+ |
| Sales Monitor | `services/sales-monitor/` | ~200MB | 3-4 min | 80%+ |
| Personalization | `services/personalization/` | ~300MB | 4-6 min | 80%+ |
| Research Service | `services/research/` | ~200MB | 3-4 min | 80%+ |
| Multimodal Generator | `services/multimodal-generator/` | ~1.5GB | 10-15 min | 75%+ |

### 🔍 **Test Configurations**

#### **Unit Tests**
```bash
Coverage Threshold: 80%
Test Framework: pytest
Parallel Execution: Yes
Mock External APIs: Yes
Database: In-memory SQLite
```

#### **Integration Tests**
```bash
Services: PostgreSQL, Redis
Test Data: Fixtures and factories
API Testing: Full request/response cycle
Timeout: 30 minutes
```

#### **E2E Tests**
```bash
Browser: Chromium (Playwright)
Test Environment: Staging
User Journeys: Registration → Book Creation
Performance: Response time validation
```

## Monitoring and Observability

### 📊 **Pipeline Metrics**

- **Build Success Rate**: Target 95%+
- **Test Coverage**: Minimum 80%
- **Deployment Time**: Target <15 minutes
- **Time to Recovery**: Target <10 minutes
- **Security Scan Results**: Zero critical vulnerabilities

### 🚨 **Alerting**

#### **Pipeline Failures**
- Immediate Slack notification
- Email to development team
- GitHub/GitLab issue creation

#### **Security Issues**
- Critical: Immediate notification + pipeline halt
- High: Notification + manual review required
- Medium/Low: Report generation

#### **Performance Degradation**
- Response time > 2 seconds: Warning
- Response time > 5 seconds: Critical
- Error rate > 1%: Critical

### 📈 **Dashboards**

#### **GitLab CI/CD Dashboard**
- Pipeline success/failure rates
- Build duration trends
- Test coverage trends
- Deployment frequency

#### **Quality Dashboard**
- Code quality metrics
- Security scan results
- Test results
- Coverage reports

## Best Practices

### 🏆 **Development Workflow**

1. **Feature Development**
   ```bash
   git checkout -b feature/new-feature
   # Develop and test locally
   git commit -m "feat: add new feature"
   git push origin feature/new-feature
   # Create merge request
   ```

2. **Code Review**
   - Automated quality checks must pass
   - Peer review required
   - Security review for sensitive changes
   - Documentation updates

3. **Merge to Main**
   - Automated deployment to development
   - Integration tests must pass
   - Ready for staging deployment

4. **Release Process**
   ```bash
   git tag -a v1.2.3 -m "Release v1.2.3"
   git push origin v1.2.3
   # Manual production deployment
   ```

### 🔧 **Troubleshooting**

#### **Common Issues**

1. **Build Failures**
   ```bash
   # Check build logs
   gitlab-ci-multi-runner exec docker build-api-gateway
   
   # Test locally
   docker build -t test-image services/api-gateway/
   ```

2. **Test Failures**
   ```bash
   # Run tests locally
   poetry run pytest tests/unit/ -v
   
   # Check coverage
   poetry run pytest --cov=app --cov-report=html
   ```

3. **Deployment Issues**
   ```bash
   # Check cluster status
   kubectl get pods -n publish-ai-production
   
   # View deployment logs
   kubectl logs deployment/api-gateway -n publish-ai-production
   ```

#### **Performance Issues**

1. **Slow Builds**
   - Use Docker layer caching
   - Optimize Dockerfile
   - Use .dockerignore

2. **Long Test Execution**
   - Parallelize test execution
   - Use test fixtures
   - Mock external services

3. **Deployment Timeouts**
   - Increase timeout values
   - Check resource constraints
   - Verify health checks

## Security Considerations

### 🛡️ **Secret Management**

- All secrets stored in GitLab CI/CD variables
- Environment-specific secret separation
- Automatic secret rotation (where possible)
- No secrets in code or logs

### 🔐 **Access Control**

- Protected branches (main, release/*)
- Deployment approvals for production
- Role-based access to environments
- Audit logging for all deployments

### 🔍 **Vulnerability Management**

- Automated security scanning in pipeline
- Dependency vulnerability tracking
- Container image scanning
- Regular security updates

This CI/CD pipeline provides enterprise-grade automation for the Publish AI platform, ensuring reliable, secure, and efficient software delivery.