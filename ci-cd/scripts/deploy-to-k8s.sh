#!/bin/bash

# Kubernetes Deployment Script for Publish AI CI/CD
# Supports multiple environments and deployment strategies

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Default values
ENVIRONMENT="development"
NAMESPACE=""
IMAGE_TAG="latest"
DEPLOYMENT_STRATEGY="rolling"
DRY_RUN=false
WAIT_FOR_ROLLOUT=true
ROLLBACK_ON_FAILURE=true
HEALTH_CHECK_TIMEOUT=300
SERVICES=""
REGISTRY=""
KUBECONFIG_PATH=""

# Service configurations
declare -A SERVICE_CONFIGS=(
    ["api-gateway"]="critical:3:10"
    ["content-generation"]="high:2:15"
    ["market-intelligence"]="high:2:8"
    ["publishing-service"]="critical:2:5"
    ["cover-designer"]="normal:2:8"
    ["sales-monitor"]="normal:1:3"
    ["personalization"]="normal:1:5"
    ["research"]="normal:1:3"
    ["multimodal-generator"]="high:1:10"
)

# Deployment strategies
declare -A DEPLOYMENT_STRATEGIES=(
    ["rolling"]="RollingUpdate"
    ["recreate"]="Recreate"
    ["blue-green"]="BlueGreen"
    ["canary"]="Canary"
)

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Publish AI services to Kubernetes cluster.

Options:
  -e, --environment ENV       Environment: development, staging, production (default: development)
  -n, --namespace NAMESPACE   Kubernetes namespace (auto-generated if not specified)
  -t, --image-tag TAG         Docker image tag (default: latest)
  -s, --strategy STRATEGY     Deployment strategy: rolling, recreate, blue-green, canary (default: rolling)
  -S, --services SERVICES     Comma-separated list of services to deploy (default: all)
  -r, --registry REGISTRY     Container registry URL
  -k, --kubeconfig PATH       Path to kubeconfig file
  -d, --dry-run               Preview deployment without making changes
  -w, --no-wait               Don't wait for rollout to complete
  -R, --no-rollback           Don't rollback on failure
  -T, --timeout SECONDS       Health check timeout (default: 300)
  -h, --help                  Show this help message

Environments:
  development  - Development environment with minimal resources
  staging      - Staging environment with production-like setup
  production   - Production environment with full resources and HA

Deployment Strategies:
  rolling      - Rolling update (default, zero-downtime)
  recreate     - Recreate all pods (brief downtime)
  blue-green   - Blue-green deployment (zero-downtime, requires setup)
  canary       - Canary deployment (gradual rollout)

Services:
  api-gateway, content-generation, market-intelligence, publishing-service,
  cover-designer, sales-monitor, personalization, research, multimodal-generator

Examples:
  $0                                              # Deploy all services to development
  $0 -e staging -t v1.2.3                       # Deploy specific tag to staging
  $0 -e production -s canary -S api-gateway     # Canary deploy API Gateway to production
  $0 -d                                          # Dry run to preview changes

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -t|--image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -s|--strategy)
                DEPLOYMENT_STRATEGY="$2"
                shift 2
                ;;
            -S|--services)
                SERVICES="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -k|--kubeconfig)
                KUBECONFIG_PATH="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -w|--no-wait)
                WAIT_FOR_ROLLOUT=false
                shift
                ;;
            -R|--no-rollback)
                ROLLBACK_ON_FAILURE=false
                shift
                ;;
            -T|--timeout)
                HEALTH_CHECK_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi
    
    # Set default namespace if not provided
    if [ -z "$NAMESPACE" ]; then
        NAMESPACE="publish-ai-${ENVIRONMENT}"
    fi
    
    # Validate deployment strategy
    if [[ ! "$DEPLOYMENT_STRATEGY" =~ ^(rolling|recreate|blue-green|canary)$ ]]; then
        error "Invalid deployment strategy: $DEPLOYMENT_STRATEGY"
        exit 1
    fi
    
    # Set default registry if not provided
    if [ -z "$REGISTRY" ]; then
        REGISTRY="${CI_REGISTRY:-docker.io}/${CI_PROJECT_PATH:-publishai}"
    fi
    
    # Set kubeconfig if provided
    if [ -n "$KUBECONFIG_PATH" ]; then
        export KUBECONFIG="$KUBECONFIG_PATH"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        info "Namespace $NAMESPACE does not exist. Creating it..."
        if [ "$DRY_RUN" = false ]; then
            kubectl create namespace "$NAMESPACE"
            kubectl label namespace "$NAMESPACE" environment="$ENVIRONMENT" project="publish-ai"
        fi
    fi
    
    # Check for required secrets
    check_required_secrets
    
    log "Prerequisites check passed"
}

# Check required secrets
check_required_secrets() {
    local required_secrets=("production-secrets" "image-pull-secrets")
    
    for secret in "${required_secrets[@]}"; do
        if ! kubectl get secret "$secret" -n "$NAMESPACE" &> /dev/null; then
            warn "Secret $secret not found in namespace $NAMESPACE"
            
            if [ "$secret" = "image-pull-secrets" ] && [ -n "$CI_REGISTRY_PASSWORD" ]; then
                info "Creating image pull secret..."
                if [ "$DRY_RUN" = false ]; then
                    kubectl create secret docker-registry image-pull-secrets \
                        --docker-server="$CI_REGISTRY" \
                        --docker-username="$CI_REGISTRY_USER" \
                        --docker-password="$CI_REGISTRY_PASSWORD" \
                        -n "$NAMESPACE"
                fi
            fi
        fi
    done
}

# Get services to deploy
get_services_to_deploy() {
    if [ -n "$SERVICES" ]; then
        IFS=',' read -ra DEPLOY_SERVICES <<< "$SERVICES"
    else
        DEPLOY_SERVICES=("${!SERVICE_CONFIGS[@]}")
    fi
    
    info "Services to deploy: ${DEPLOY_SERVICES[*]}"
}

# Generate deployment manifest
generate_deployment_manifest() {
    local service_name="$1"
    local config="${SERVICE_CONFIGS[$service_name]}"
    
    # Parse service configuration
    IFS=':' read -ra config_parts <<< "$config"
    local priority="${config_parts[0]}"
    local min_replicas="${config_parts[1]}"
    local max_replicas="${config_parts[2]}"
    
    # Environment-specific replica adjustments
    case "$ENVIRONMENT" in
        "development")
            min_replicas=1
            max_replicas=$((max_replicas / 2))
            ;;
        "staging")
            min_replicas=$((min_replicas))
            max_replicas=$((max_replicas * 2 / 3))
            ;;
        "production")
            # Use full configuration
            ;;
    esac
    
    # Ensure minimum replicas
    if [ "$min_replicas" -lt 1 ]; then
        min_replicas=1
    fi
    if [ "$max_replicas" -lt "$min_replicas" ]; then
        max_replicas=$((min_replicas + 1))
    fi
    
    local manifest_file="/tmp/${service_name}-deployment.yaml"
    
    cat > "$manifest_file" << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name
    tier: microservice
    version: v1
    environment: $ENVIRONMENT
  annotations:
    deployment.kubernetes.io/revision: "$(date +%s)"
    app.kubernetes.io/version: "$IMAGE_TAG"
spec:
  replicas: $min_replicas
  strategy:
    type: ${DEPLOYMENT_STRATEGIES[$DEPLOYMENT_STRATEGY]}
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: $service_name
  template:
    metadata:
      labels:
        app: $service_name
        tier: microservice
        version: v1
        environment: $ENVIRONMENT
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        kubectl.kubernetes.io/restartedAt: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    spec:
      serviceAccountName: $service_name
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["$service_name"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: $service_name
        image: $REGISTRY/$service_name:$IMAGE_TAG
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        - name: SERVICE_NAME
          value: "$service_name"
        - name: ENVIRONMENT
          value: "$ENVIRONMENT"
        - name: IMAGE_TAG
          value: "$IMAGE_TAG"
        - name: DEPLOYED_AT
          value: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
        envFrom:
        - configMapRef:
            name: production-config
        - secretRef:
            name: production-secrets
        resources:
          requests:
            cpu: $(get_resource_request "$service_name" "cpu" "$ENVIRONMENT")
            memory: $(get_resource_request "$service_name" "memory" "$ENVIRONMENT")
            ephemeral-storage: 1Gi
          limits:
            cpu: $(get_resource_limit "$service_name" "cpu" "$ENVIRONMENT")
            memory: $(get_resource_limit "$service_name" "memory" "$ENVIRONMENT")
            ephemeral-storage: 5Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      imagePullSecrets:
      - name: image-pull-secrets

---
apiVersion: v1
kind: Service
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: $service_name

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ${service_name}-hpa
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: $service_name
  minReplicas: $min_replicas
  maxReplicas: $max_replicas
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: $(get_hpa_target "$service_name" "cpu")
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: $(get_hpa_target "$service_name" "memory")
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $service_name
  namespace: $NAMESPACE
  labels:
    app: $service_name

EOF
    
    echo "$manifest_file"
}

# Get resource requests based on service and environment
get_resource_request() {
    local service="$1"
    local resource="$2"
    local env="$3"
    
    # Base resource requests (production values)
    case "$service" in
        "api-gateway")
            case "$resource" in
                "cpu") echo "500m" ;;
                "memory") echo "1Gi" ;;
            esac
            ;;
        "content-generation")
            case "$resource" in
                "cpu") echo "1000m" ;;
                "memory") echo "2Gi" ;;
            esac
            ;;
        "market-intelligence"|"publishing-service"|"cover-designer"|"personalization")
            case "$resource" in
                "cpu") echo "300m" ;;
                "memory") echo "512Mi" ;;
            esac
            ;;
        "multimodal-generator")
            case "$resource" in
                "cpu") echo "1500m" ;;
                "memory") echo "3Gi" ;;
            esac
            ;;
        *)
            case "$resource" in
                "cpu") echo "200m" ;;
                "memory") echo "256Mi" ;;
            esac
            ;;
    esac
}

# Get resource limits based on service and environment
get_resource_limit() {
    local service="$1"
    local resource="$2"
    local env="$3"
    
    # Base resource limits (production values)
    case "$service" in
        "api-gateway")
            case "$resource" in
                "cpu") echo "2000m" ;;
                "memory") echo "4Gi" ;;
            esac
            ;;
        "content-generation")
            case "$resource" in
                "cpu") echo "4000m" ;;
                "memory") echo "8Gi" ;;
            esac
            ;;
        "market-intelligence"|"publishing-service"|"personalization")
            case "$resource" in
                "cpu") echo "1000m" ;;
                "memory") echo "2Gi" ;;
            esac
            ;;
        "cover-designer")
            case "$resource" in
                "cpu") echo "3000m" ;;
                "memory") echo "6Gi" ;;
            esac
            ;;
        "multimodal-generator")
            case "$resource" in
                "cpu") echo "6000m" ;;
                "memory") echo "12Gi" ;;
            esac
            ;;
        *)
            case "$resource" in
                "cpu") echo "500m" ;;
                "memory") echo "1Gi" ;;
            esac
            ;;
    esac
}

# Get HPA targets based on service
get_hpa_target() {
    local service="$1"
    local resource="$2"
    
    case "$service" in
        "api-gateway"|"publishing-service")
            case "$resource" in
                "cpu") echo "70" ;;
                "memory") echo "80" ;;
            esac
            ;;
        "content-generation"|"multimodal-generator")
            case "$resource" in
                "cpu") echo "60" ;;
                "memory") echo "70" ;;
            esac
            ;;
        *)
            case "$resource" in
                "cpu") echo "75" ;;
                "memory") echo "80" ;;
            esac
            ;;
    esac
}

# Deploy service
deploy_service() {
    local service_name="$1"
    
    log "Deploying service: $service_name"
    
    # Generate deployment manifest
    local manifest_file=$(generate_deployment_manifest "$service_name")
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would apply manifest:"
        cat "$manifest_file"
        return 0
    fi
    
    # Apply the manifest
    kubectl apply -f "$manifest_file"
    
    # Clean up temp file
    rm -f "$manifest_file"
    
    # Wait for rollout if requested
    if [ "$WAIT_FOR_ROLLOUT" = true ]; then
        wait_for_rollout "$service_name"
    fi
    
    info "Service $service_name deployed successfully"
}

# Wait for deployment rollout
wait_for_rollout() {
    local service_name="$1"
    
    info "Waiting for $service_name rollout to complete..."
    
    if kubectl rollout status deployment/"$service_name" -n "$NAMESPACE" --timeout="${HEALTH_CHECK_TIMEOUT}s"; then
        log "✅ $service_name rollout completed successfully"
        return 0
    else
        error "❌ $service_name rollout failed or timed out"
        
        if [ "$ROLLBACK_ON_FAILURE" = true ]; then
            warn "Rolling back $service_name..."
            kubectl rollout undo deployment/"$service_name" -n "$NAMESPACE"
            kubectl rollout status deployment/"$service_name" -n "$NAMESPACE" --timeout="300s"
            warn "Rollback completed for $service_name"
        fi
        
        return 1
    fi
}

# Deploy canary
deploy_canary() {
    local service_name="$1"
    
    info "Deploying canary for service: $service_name"
    
    # Create canary deployment
    local manifest_file=$(generate_deployment_manifest "$service_name")
    
    # Modify for canary
    sed -i "s/name: $service_name/name: ${service_name}-canary/g" "$manifest_file"
    sed -i "s/replicas: [0-9]*/replicas: 1/g" "$manifest_file"
    
    if [ "$DRY_RUN" = false ]; then
        kubectl apply -f "$manifest_file"
        
        # Wait for canary to be ready
        kubectl rollout status deployment/"${service_name}-canary" -n "$NAMESPACE" --timeout="300s"
        
        # Run canary validation
        validate_canary "$service_name"
        
        # If validation passes, promote canary
        if [ $? -eq 0 ]; then
            promote_canary "$service_name"
        else
            error "Canary validation failed for $service_name"
            kubectl delete deployment "${service_name}-canary" -n "$NAMESPACE"
            return 1
        fi
    fi
    
    rm -f "$manifest_file"
}

# Validate canary deployment
validate_canary() {
    local service_name="$1"
    
    info "Validating canary deployment for $service_name..."
    
    # Get canary pod
    local canary_pod=$(kubectl get pods -n "$NAMESPACE" -l app="$service_name-canary" -o jsonpath='{.items[0].metadata.name}')
    
    if [ -n "$canary_pod" ]; then
        # Test health endpoint
        if kubectl exec -n "$NAMESPACE" "$canary_pod" -- curl -sf http://localhost:8000/health &> /dev/null; then
            log "✅ Canary health check passed for $service_name"
            return 0
        else
            error "❌ Canary health check failed for $service_name"
            return 1
        fi
    else
        error "❌ Canary pod not found for $service_name"
        return 1
    fi
}

# Promote canary to production
promote_canary() {
    local service_name="$1"
    
    info "Promoting canary to production for $service_name..."
    
    # Update main deployment with canary image
    kubectl patch deployment "$service_name" -n "$NAMESPACE" \
        -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"$service_name\",\"image\":\"$REGISTRY/$service_name:$IMAGE_TAG\"}]}}}}"
    
    # Wait for main deployment rollout
    kubectl rollout status deployment/"$service_name" -n "$NAMESPACE" --timeout="${HEALTH_CHECK_TIMEOUT}s"
    
    # Clean up canary deployment
    kubectl delete deployment "${service_name}-canary" -n "$NAMESPACE"
    
    log "✅ Canary promoted successfully for $service_name"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        info "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Check all deployments
    info "Checking deployments:"
    kubectl get deployments -n "$NAMESPACE"
    
    # Check all services
    info "Checking services:"
    kubectl get services -n "$NAMESPACE"
    
    # Check HPA status
    info "Checking Horizontal Pod Autoscalers:"
    kubectl get hpa -n "$NAMESPACE"
    
    # Verify each deployed service
    for service in "${DEPLOY_SERVICES[@]}"; do
        local ready_replicas=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local desired_replicas=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        
        if [ "$ready_replicas" = "$desired_replicas" ] && [ "$ready_replicas" != "0" ]; then
            log "✅ $service: $ready_replicas/$desired_replicas replicas ready"
        else
            error "❌ $service: $ready_replicas/$desired_replicas replicas ready"
        fi
    done
    
    log "Deployment verification completed"
}

# Main function
main() {
    log "Starting Kubernetes deployment for Publish AI"
    
    parse_args "$@"
    check_prerequisites
    get_services_to_deploy
    
    # Confirm deployment for production
    if [ "$ENVIRONMENT" = "production" ] && [ "$DRY_RUN" = false ]; then
        echo
        warn "This will deploy to PRODUCTION environment:"
        warn "  Namespace: $NAMESPACE"
        warn "  Image tag: $IMAGE_TAG"
        warn "  Services: ${DEPLOY_SERVICES[*]}"
        warn "  Strategy: $DEPLOYMENT_STRATEGY"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Deploy services
    local failed_services=()
    
    for service in "${DEPLOY_SERVICES[@]}"; do
        if [ "$DEPLOYMENT_STRATEGY" = "canary" ] && [ "$ENVIRONMENT" = "production" ]; then
            if ! deploy_canary "$service"; then
                failed_services+=("$service")
            fi
        else
            if ! deploy_service "$service"; then
                failed_services+=("$service")
            fi
        fi
    done
    
    # Verify deployment
    verify_deployment
    
    # Report results
    if [ ${#failed_services[@]} -eq 0 ]; then
        log "🎉 All services deployed successfully!"
        log ""
        info "Services deployed: ${DEPLOY_SERVICES[*]}"
        info "Environment: $ENVIRONMENT"
        info "Namespace: $NAMESPACE"
        info "Image tag: $IMAGE_TAG"
        info "Strategy: $DEPLOYMENT_STRATEGY"
    else
        error "💥 Failed to deploy services: ${failed_services[*]}"
        exit 1
    fi
}

# Run main function
main "$@"