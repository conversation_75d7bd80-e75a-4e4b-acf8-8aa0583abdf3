#!/bin/bash

# Comprehensive Test Runner for Publish AI CI/CD
# Runs all test suites with proper reporting and coverage

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"
COVERAGE_DIR="$PROJECT_ROOT/htmlcov"

# Default values
TEST_SUITE="all"
COVERAGE_THRESHOLD=80
PARALLEL_JOBS=4
VERBOSE=false
MOCK_EXTERNAL_APIS=true
GENERATE_REPORTS=true
SKIP_SLOW_TESTS=false

# Test configuration
PYTEST_OPTS="--tb=short --strict-markers --disable-warnings"
COVERAGE_OPTS="--cov=app --cov-branch --cov-report=xml --cov-report=html --cov-report=term"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run comprehensive test suite for Publish AI.

Options:
  -s, --suite SUITE           Test suite: all, unit, integration, agents, e2e (default: all)
  -c, --coverage THRESHOLD    Coverage threshold percentage (default: 80)
  -j, --jobs COUNT           Number of parallel test jobs (default: 4)
  -v, --verbose              Enable verbose output
  -m, --mock-apis            Mock external APIs (default: true)
  -r, --reports              Generate test reports (default: true)
  -f, --fast                 Skip slow tests
  -h, --help                 Show this help message

Test Suites:
  all           - Run all test suites
  unit          - Unit tests only
  integration   - Integration tests only
  agents        - PydanticAI agent tests only
  e2e           - End-to-end tests only
  security      - Security tests only
  performance   - Performance tests only

Examples:
  $0                                    # Run all tests
  $0 --suite unit --coverage 85        # Run unit tests with 85% coverage
  $0 --suite integration --verbose     # Run integration tests with verbose output
  $0 --fast --jobs 8                   # Run fast tests with 8 parallel jobs

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--suite)
                TEST_SUITE="$2"
                shift 2
                ;;
            -c|--coverage)
                COVERAGE_THRESHOLD="$2"
                shift 2
                ;;
            -j|--jobs)
                PARALLEL_JOBS="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -m|--mock-apis)
                MOCK_EXTERNAL_APIS=true
                shift
                ;;
            -r|--reports)
                GENERATE_REPORTS=true
                shift
                ;;
            -f|--fast)
                SKIP_SLOW_TESTS=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate test suite
    if [[ ! "$TEST_SUITE" =~ ^(all|unit|integration|agents|e2e|security|performance)$ ]]; then
        error "Invalid test suite: $TEST_SUITE"
        exit 1
    fi
    
    # Update pytest options based on arguments
    if [ "$VERBOSE" = true ]; then
        PYTEST_OPTS="$PYTEST_OPTS -v"
    fi
    
    if [ "$SKIP_SLOW_TESTS" = true ]; then
        PYTEST_OPTS="$PYTEST_OPTS -m 'not slow'"
    fi
    
    if [ "$PARALLEL_JOBS" -gt 1 ]; then
        PYTEST_OPTS="$PYTEST_OPTS -n $PARALLEL_JOBS"
    fi
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Create test directories
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$COVERAGE_DIR"
    
    # Set environment variables for testing
    export ENVIRONMENT="test"
    export LOG_LEVEL="WARNING"
    export TESTING="true"
    
    if [ "$MOCK_EXTERNAL_APIS" = true ]; then
        export MOCK_EXTERNAL_APIS="true"
        export OPENAI_API_KEY="test-key-openai"
        export ANTHROPIC_API_KEY="test-key-anthropic"
        export GOOGLE_TRENDS_API_KEY="test-key-google"
        export AMAZON_KDP_API_KEY="test-key-amazon"
    fi
    
    # Setup test database
    export DATABASE_URL="sqlite:///tmp/test_publishai.db"
    export REDIS_URL="redis://localhost:6379/15"  # Use DB 15 for tests
    
    # Setup test secrets
    export JWT_SECRET="test-jwt-secret-key-for-testing-only"
    export ENCRYPTION_KEY="test-encryption-key-for-testing-only"
    
    info "Test environment configured"
}

# Check prerequisites
check_prerequisites() {
    log "Checking test prerequisites..."
    
    # Check Python and Poetry
    if ! command -v python3 &> /dev/null; then
        error "Python 3 is not installed"
        exit 1
    fi
    
    if ! command -v poetry &> /dev/null; then
        error "Poetry is not installed"
        exit 1
    fi
    
    # Install dependencies
    cd "$PROJECT_ROOT"
    poetry install --with dev,test
    
    # Check if we're in a virtual environment
    if [ -z "${VIRTUAL_ENV:-}" ] && [ -z "${POETRY_ACTIVE:-}" ]; then
        warn "Not in a virtual environment. Using poetry run..."
        PYTEST_CMD="poetry run pytest"
        COVERAGE_CMD="poetry run coverage"
    else
        PYTEST_CMD="pytest"
        COVERAGE_CMD="coverage"
    fi
    
    # Install pytest plugins if needed
    pip install pytest-xdist pytest-cov pytest-html pytest-json-report pytest-mock pytest-asyncio
    
    log "Prerequisites check passed"
}

# Run unit tests
run_unit_tests() {
    log "Running unit tests..."
    
    local test_files="tests/unit/"
    local output_file="$TEST_RESULTS_DIR/unit-test-results.xml"
    local html_report="$TEST_RESULTS_DIR/unit-test-report.html"
    
    if [ ! -d "$test_files" ]; then
        warn "Unit tests directory not found: $test_files"
        return 0
    fi
    
    local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS $COVERAGE_OPTS"
    
    if [ "$GENERATE_REPORTS" = true ]; then
        cmd="$cmd --junit-xml=$output_file --html=$html_report --self-contained-html"
    fi
    
    info "Running: $cmd"
    
    if $cmd; then
        log "✅ Unit tests passed"
        return 0
    else
        error "❌ Unit tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    log "Running integration tests..."
    
    local test_files="tests/integration/"
    local output_file="$TEST_RESULTS_DIR/integration-test-results.xml"
    local html_report="$TEST_RESULTS_DIR/integration-test-report.html"
    
    if [ ! -d "$test_files" ]; then
        warn "Integration tests directory not found: $test_files"
        return 0
    fi
    
    # Setup integration test services (if needed)
    setup_integration_services
    
    local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS"
    
    if [ "$GENERATE_REPORTS" = true ]; then
        cmd="$cmd --junit-xml=$output_file --html=$html_report --self-contained-html"
    fi
    
    info "Running: $cmd"
    
    if $cmd; then
        log "✅ Integration tests passed"
        cleanup_integration_services
        return 0
    else
        error "❌ Integration tests failed"
        cleanup_integration_services
        return 1
    fi
}

# Setup integration test services
setup_integration_services() {
    info "Setting up integration test services..."
    
    # Start Redis for integration tests (if not already running)
    if ! redis-cli ping &> /dev/null; then
        if command -v redis-server &> /dev/null; then
            redis-server --daemonize yes --port 6379 --dbfilename test.rdb --dir /tmp/
            sleep 2
        else
            warn "Redis not available - some integration tests may fail"
        fi
    fi
    
    # Start PostgreSQL test database (if available)
    if command -v createdb &> /dev/null; then
        createdb test_publishai_integration 2>/dev/null || true
        export DATABASE_URL="postgresql://localhost/test_publishai_integration"
    fi
}

# Cleanup integration test services
cleanup_integration_services() {
    info "Cleaning up integration test services..."
    
    # Clean up test database
    if command -v dropdb &> /dev/null; then
        dropdb test_publishai_integration 2>/dev/null || true
    fi
    
    # Clean up Redis test data
    redis-cli -n 15 flushdb 2>/dev/null || true
}

# Run agent tests
run_agent_tests() {
    log "Running PydanticAI agent tests..."
    
    local test_files="tests/test_agents/"
    local output_file="$TEST_RESULTS_DIR/agent-test-results.xml"
    local html_report="$TEST_RESULTS_DIR/agent-test-report.html"
    
    if [ ! -d "$test_files" ]; then
        warn "Agent tests directory not found: $test_files"
        return 0
    fi
    
    # Use the dedicated agent test runner
    if [ -f "$test_files/run_agent_tests.py" ]; then
        info "Using dedicated agent test runner..."
        
        local test_mode="mock-only"
        if [ "$MOCK_EXTERNAL_APIS" = false ]; then
            test_mode="all"
        fi
        
        cd "$PROJECT_ROOT"
        if python "$test_files/run_agent_tests.py" --suite all --$test_mode; then
            log "✅ Agent tests passed"
            return 0
        else
            error "❌ Agent tests failed"
            return 1
        fi
    else
        # Fallback to pytest
        local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS"
        
        if [ "$GENERATE_REPORTS" = true ]; then
            cmd="$cmd --junit-xml=$output_file --html=$html_report --self-contained-html"
        fi
        
        info "Running: $cmd"
        
        if $cmd; then
            log "✅ Agent tests passed"
            return 0
        else
            error "❌ Agent tests failed"
            return 1
        fi
    fi
}

# Run E2E tests
run_e2e_tests() {
    log "Running end-to-end tests..."
    
    local test_files="tests/e2e/"
    local output_file="$TEST_RESULTS_DIR/e2e-test-results.xml"
    local html_report="$TEST_RESULTS_DIR/e2e-test-report.html"
    
    if [ ! -d "$test_files" ]; then
        warn "E2E tests directory not found: $test_files"
        return 0
    fi
    
    # Check if API Gateway is running
    local api_url="${API_GATEWAY_URL:-http://localhost:8000}"
    if ! curl -sf "$api_url/health" &> /dev/null; then
        warn "API Gateway not available at $api_url - skipping E2E tests"
        return 0
    fi
    
    # Install Playwright if needed
    if command -v playwright &> /dev/null; then
        playwright install chromium
    else
        pip install playwright
        playwright install chromium
    fi
    
    local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS"
    
    if [ "$GENERATE_REPORTS" = true ]; then
        cmd="$cmd --junit-xml=$output_file --html=$html_report --self-contained-html"
    fi
    
    info "Running: $cmd"
    
    if $cmd; then
        log "✅ E2E tests passed"
        return 0
    else
        error "❌ E2E tests failed"
        return 1
    fi
}

# Run security tests
run_security_tests() {
    log "Running security tests..."
    
    local test_files="tests/security/"
    local output_file="$TEST_RESULTS_DIR/security-test-results.xml"
    
    if [ ! -d "$test_files" ]; then
        warn "Security tests directory not found: $test_files"
        return 0
    fi
    
    # Install security testing tools
    pip install bandit safety
    
    # Run Bandit security linter
    info "Running Bandit security analysis..."
    bandit -r app/ -f json -o "$TEST_RESULTS_DIR/bandit-report.json" || warn "Bandit found security issues"
    
    # Run Safety dependency check
    info "Running Safety dependency check..."
    safety check --json --output "$TEST_RESULTS_DIR/safety-report.json" || warn "Safety found vulnerable dependencies"
    
    # Run security-specific tests
    local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS"
    
    if [ "$GENERATE_REPORTS" = true ]; then
        cmd="$cmd --junit-xml=$output_file"
    fi
    
    info "Running: $cmd"
    
    if $cmd; then
        log "✅ Security tests passed"
        return 0
    else
        error "❌ Security tests failed"
        return 1
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    local test_files="tests/performance/"
    local output_file="$TEST_RESULTS_DIR/performance-test-results.xml"
    
    if [ ! -d "$test_files" ]; then
        warn "Performance tests directory not found: $test_files"
        return 0
    fi
    
    # Install performance testing tools
    pip install pytest-benchmark locust
    
    local cmd="$PYTEST_CMD $test_files $PYTEST_OPTS --benchmark-json=$TEST_RESULTS_DIR/benchmark-results.json"
    
    if [ "$GENERATE_REPORTS" = true ]; then
        cmd="$cmd --junit-xml=$output_file"
    fi
    
    info "Running: $cmd"
    
    if $cmd; then
        log "✅ Performance tests passed"
        return 0
    else
        error "❌ Performance tests failed"
        return 1
    fi
}

# Check test coverage
check_coverage() {
    if [ "$TEST_SUITE" != "unit" ] && [ "$TEST_SUITE" != "all" ]; then
        info "Skipping coverage check for $TEST_SUITE tests"
        return 0
    fi
    
    log "Checking test coverage..."
    
    # Generate coverage report
    $COVERAGE_CMD report --show-missing
    
    # Check coverage threshold
    local current_coverage=$($COVERAGE_CMD report | tail -1 | awk '{print $4}' | sed 's/%//')
    
    if [ -z "$current_coverage" ]; then
        warn "Could not determine coverage percentage"
        return 0
    fi
    
    info "Current coverage: ${current_coverage}%"
    info "Required coverage: ${COVERAGE_THRESHOLD}%"
    
    if (( $(echo "$current_coverage >= $COVERAGE_THRESHOLD" | bc -l) )); then
        log "✅ Coverage threshold met: ${current_coverage}% >= ${COVERAGE_THRESHOLD}%"
        return 0
    else
        error "❌ Coverage threshold not met: ${current_coverage}% < ${COVERAGE_THRESHOLD}%"
        return 1
    fi
}

# Generate test summary
generate_test_summary() {
    if [ "$GENERATE_REPORTS" = false ]; then
        return 0
    fi
    
    log "Generating test summary..."
    
    local summary_file="$TEST_RESULTS_DIR/test-summary.json"
    
    cat > "$summary_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "test_suite": "$TEST_SUITE",
  "coverage_threshold": $COVERAGE_THRESHOLD,
  "parallel_jobs": $PARALLEL_JOBS,
  "mock_external_apis": $MOCK_EXTERNAL_APIS,
  "skip_slow_tests": $SKIP_SLOW_TESTS,
  "results": {
EOF

    # Add results for each test type
    local first=true
    for result_file in "$TEST_RESULTS_DIR"/*-test-results.xml; do
        if [ -f "$result_file" ]; then
            if [ "$first" = false ]; then
                echo "," >> "$summary_file"
            fi
            first=false
            
            local test_type=$(basename "$result_file" | sed 's/-test-results.xml//')
            local test_count=$(grep -o 'tests="[0-9]*"' "$result_file" | grep -o '[0-9]*' || echo "0")
            local failure_count=$(grep -o 'failures="[0-9]*"' "$result_file" | grep -o '[0-9]*' || echo "0")
            local error_count=$(grep -o 'errors="[0-9]*"' "$result_file" | grep -o '[0-9]*' || echo "0")
            
            echo "    \"$test_type\": {" >> "$summary_file"
            echo "      \"total\": $test_count," >> "$summary_file"
            echo "      \"failures\": $failure_count," >> "$summary_file"
            echo "      \"errors\": $error_count," >> "$summary_file"
            echo "      \"passed\": $((test_count - failure_count - error_count))" >> "$summary_file"
            echo "    }" >> "$summary_file"
        fi
    done
    
    cat >> "$summary_file" << EOF
  },
  "coverage": {
    "threshold": $COVERAGE_THRESHOLD,
    "current": "$(coverage report | tail -1 | awk '{print $4}' | sed 's/%//' || echo 'N/A')"
  }
}
EOF
    
    info "Test summary generated: $summary_file"
}

# Main function
main() {
    log "Starting Publish AI Test Runner"
    
    parse_args "$@"
    check_prerequisites
    setup_test_environment
    
    local exit_code=0
    
    # Run requested test suite
    case "$TEST_SUITE" in
        "unit")
            run_unit_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "agents")
            run_agent_tests || exit_code=1
            ;;
        "e2e")
            run_e2e_tests || exit_code=1
            ;;
        "security")
            run_security_tests || exit_code=1
            ;;
        "performance")
            run_performance_tests || exit_code=1
            ;;
        "all")
            run_unit_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_agent_tests || exit_code=1
            run_security_tests || exit_code=1
            ;;
    esac
    
    # Check coverage (if applicable)
    if [ "$exit_code" -eq 0 ]; then
        check_coverage || exit_code=1
    fi
    
    # Generate summary
    generate_test_summary
    
    # Final result
    if [ "$exit_code" -eq 0 ]; then
        log "🎉 All tests passed successfully!"
    else
        error "💥 Some tests failed!"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"