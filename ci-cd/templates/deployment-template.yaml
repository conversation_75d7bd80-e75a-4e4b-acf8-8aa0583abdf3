# Kubernetes Deployment Template for Publish AI Services
# Template for generating service-specific deployments with environment configurations

apiVersion: v1
kind: Template
metadata:
  name: publish-ai-service-template
  annotations:
    description: "Template for deploying Publish AI microservices"
    tags: "microservices,publishai,deployment"
    version: "1.0.0"

parameters:
- name: SERVICE_NAME
  description: "Name of the microservice"
  required: true
  
- name: ENVIRONMENT
  description: "Deployment environment"
  value: "development"
  
- name: NAMESPACE
  description: "Kubernetes namespace"
  value: "publish-ai-development"
  
- name: IMAGE_TAG
  description: "Docker image tag"
  value: "latest"
  
- name: REGISTRY
  description: "Container registry"
  value: "ghcr.io/publishai"
  
- name: REPLICAS
  description: "Number of replicas"
  value: "1"
  
- name: MIN_REPLICAS
  description: "Minimum replicas for HPA"
  value: "1"
  
- name: MAX_REPLICAS
  description: "Maximum replicas for HPA"
  value: "5"
  
- name: CPU_REQUEST
  description: "CPU request"
  value: "200m"
  
- name: MEMORY_REQUEST
  description: "Memory request"
  value: "256Mi"
  
- name: CPU_LIMIT
  description: "CPU limit"
  value: "1000m"
  
- name: MEMORY_LIMIT
  description: "Memory limit"
  value: "1Gi"
  
- name: PRIORITY_CLASS
  description: "Pod priority class"
  value: "normal-priority"
  
- name: NODE_SELECTOR
  description: "Node selector for pod placement"
  value: ""
  
- name: HEALTH_CHECK_PATH
  description: "Health check endpoint path"
  value: "/health"
  
- name: READINESS_CHECK_PATH
  description: "Readiness check endpoint path"
  value: "/ready"

objects:
# Deployment
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: ${SERVICE_NAME}
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      version: v1
      environment: ${ENVIRONMENT}
      template: publish-ai-service-template
    annotations:
      deployment.kubernetes.io/revision: "${DEPLOYMENT_REVISION}"
      app.kubernetes.io/version: "${IMAGE_TAG}"
      app.kubernetes.io/component: "microservice"
      app.kubernetes.io/part-of: "publish-ai"
      app.kubernetes.io/managed-by: "ci-cd-pipeline"
  spec:
    replicas: ${{REPLICAS}}
    strategy:
      type: RollingUpdate
      rollingUpdate:
        maxUnavailable: 1
        maxSurge: 1
    selector:
      matchLabels:
        app: ${SERVICE_NAME}
        environment: ${ENVIRONMENT}
    template:
      metadata:
        labels:
          app: ${SERVICE_NAME}
          tier: microservice
          version: v1
          environment: ${ENVIRONMENT}
        annotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "8000"
          prometheus.io/path: "/metrics"
          kubectl.kubernetes.io/restartedAt: "${RESTART_TIMESTAMP}"
          config.linkerd.io/proxy-cpu-request: "10m"
          config.linkerd.io/proxy-memory-request: "16Mi"
      spec:
        serviceAccountName: ${SERVICE_NAME}
        priorityClassName: ${PRIORITY_CLASS}
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          fsGroup: 1000
          supplementalGroups: [1000]
        automountServiceAccountToken: false
        # Node placement
        nodeSelector:
          node.kubernetes.io/workload: ${NODE_SELECTOR}
        affinity:
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app
                    operator: In
                    values: [${SERVICE_NAME}]
                topologyKey: kubernetes.io/hostname
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                - key: node.kubernetes.io/instance-type
                  operator: In
                  values: ["general-purpose", "compute-optimized"]
        tolerations:
        - key: "workload"
          operator: "Equal"
          value: "microservices"
          effect: "NoSchedule"
        containers:
        - name: ${SERVICE_NAME}
          image: ${REGISTRY}/${SERVICE_NAME}:${IMAGE_TAG}
          imagePullPolicy: Always
          ports:
          - containerPort: 8000
            name: http
            protocol: TCP
          - containerPort: 8080
            name: metrics
            protocol: TCP
          env:
          - name: PORT
            value: "8000"
          - name: METRICS_PORT
            value: "8080"
          - name: SERVICE_NAME
            value: ${SERVICE_NAME}
          - name: ENVIRONMENT
            value: ${ENVIRONMENT}
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          - name: IMAGE_TAG
            value: ${IMAGE_TAG}
          - name: DEPLOYED_AT
            value: "${DEPLOYMENT_TIMESTAMP}"
          envFrom:
          - configMapRef:
              name: ${ENVIRONMENT}-config
          - secretRef:
              name: ${ENVIRONMENT}-secrets
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEMORY_REQUEST}
              ephemeral-storage: 1Gi
            limits:
              cpu: ${CPU_LIMIT}
              memory: ${MEMORY_LIMIT}
              ephemeral-storage: 5Gi
          livenessProbe:
            httpGet:
              path: ${HEALTH_CHECK_PATH}
              port: http
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: ${READINESS_CHECK_PATH}
              port: http
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
            successThreshold: 1
          startupProbe:
            httpGet:
              path: ${HEALTH_CHECK_PATH}
              port: http
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
            successThreshold: 1
          lifecycle:
            preStop:
              exec:
                command:
                - /bin/sh
                - -c
                - |
                  echo "Gracefully shutting down ${SERVICE_NAME}..."
                  # Send SIGTERM to main process
                  kill -TERM 1
                  # Wait for graceful shutdown
                  sleep 15
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
              - ALL
              add: []
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
          - name: tmp
            mountPath: /tmp
          - name: var-tmp
            mountPath: /var/tmp
          - name: cache
            mountPath: /app/.cache
        volumes:
        - name: tmp
          emptyDir:
            sizeLimit: 1Gi
        - name: var-tmp
          emptyDir:
            sizeLimit: 1Gi
        - name: cache
          emptyDir:
            sizeLimit: 2Gi
        imagePullSecrets:
        - name: registry-credentials
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        terminationGracePeriodSeconds: 30

# Service
- apiVersion: v1
  kind: Service
  metadata:
    name: ${SERVICE_NAME}
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
      prometheus.io/scrape: "true"
      prometheus.io/port: "8000"
      prometheus.io/path: "/metrics"
  spec:
    type: ClusterIP
    sessionAffinity: None
    ports:
    - name: http
      port: 8000
      targetPort: http
      protocol: TCP
    - name: metrics
      port: 8080
      targetPort: metrics
      protocol: TCP
    selector:
      app: ${SERVICE_NAME}
      environment: ${ENVIRONMENT}

# ServiceAccount
- apiVersion: v1
  kind: ServiceAccount
  metadata:
    name: ${SERVICE_NAME}
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
    annotations:
      eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT:role/${SERVICE_NAME}-role"
  automountServiceAccountToken: false

# HorizontalPodAutoscaler
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    name: ${SERVICE_NAME}-hpa
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
  spec:
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: ${SERVICE_NAME}
    minReplicas: ${{MIN_REPLICAS}}
    maxReplicas: ${{MAX_REPLICAS}}
    metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    behavior:
      scaleDown:
        stabilizationWindowSeconds: 300
        policies:
        - type: Percent
          value: 25
          periodSeconds: 60
        - type: Pods
          value: 1
          periodSeconds: 60
        selectPolicy: Min
      scaleUp:
        stabilizationWindowSeconds: 60
        policies:
        - type: Percent
          value: 50
          periodSeconds: 60
        - type: Pods
          value: 2
          periodSeconds: 60
        selectPolicy: Max

# PodDisruptionBudget
- apiVersion: policy/v1
  kind: PodDisruptionBudget
  metadata:
    name: ${SERVICE_NAME}-pdb
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
  spec:
    minAvailable: 1
    selector:
      matchLabels:
        app: ${SERVICE_NAME}
        environment: ${ENVIRONMENT}

# NetworkPolicy
- apiVersion: networking.k8s.io/v1
  kind: NetworkPolicy
  metadata:
    name: ${SERVICE_NAME}-network-policy
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
  spec:
    podSelector:
      matchLabels:
        app: ${SERVICE_NAME}
    policyTypes:
    - Ingress
    - Egress
    ingress:
    # Allow traffic from API Gateway
    - from:
      - podSelector:
          matchLabels:
            app: api-gateway
      ports:
      - protocol: TCP
        port: 8000
    # Allow traffic from monitoring
    - from:
      - namespaceSelector:
          matchLabels:
            name: monitoring
      ports:
      - protocol: TCP
        port: 8000
      - protocol: TCP
        port: 8080
    # Allow traffic from same namespace
    - from:
      - namespaceSelector:
          matchLabels:
            name: ${NAMESPACE}
      ports:
      - protocol: TCP
        port: 8000
    egress:
    # Allow DNS resolution
    - to: []
      ports:
      - protocol: UDP
        port: 53
      - protocol: TCP
        port: 53
    # Allow HTTPS outbound (for external APIs)
    - to: []
      ports:
      - protocol: TCP
        port: 443
    # Allow HTTP outbound (for internal services)
    - to: []
      ports:
      - protocol: TCP
        port: 80
      - protocol: TCP
        port: 8000
    # Allow database connections
    - to: []
      ports:
      - protocol: TCP
        port: 5432  # PostgreSQL
      - protocol: TCP
        port: 6379  # Redis

# ServiceMonitor for Prometheus
- apiVersion: monitoring.coreos.com/v1
  kind: ServiceMonitor
  metadata:
    name: ${SERVICE_NAME}-metrics
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
      prometheus: kube-prometheus
  spec:
    selector:
      matchLabels:
        app: ${SERVICE_NAME}
    endpoints:
    - port: metrics
      interval: 30s
      path: /metrics
      scrapeTimeout: 10s
      honorLabels: true
      metricRelabelings:
      - sourceLabels: [__name__]
        regex: 'http_request_duration_seconds_bucket'
        targetLabel: __tmp_duration_bucket
        replacement: 'true'
    namespaceSelector:
      matchNames:
      - ${NAMESPACE}

# PrometheusRule for alerting
- apiVersion: monitoring.coreos.com/v1
  kind: PrometheusRule
  metadata:
    name: ${SERVICE_NAME}-alerts
    namespace: ${NAMESPACE}
    labels:
      app: ${SERVICE_NAME}
      tier: microservice
      environment: ${ENVIRONMENT}
      prometheus: kube-prometheus
  spec:
    groups:
    - name: ${SERVICE_NAME}.rules
      interval: 30s
      rules:
      - alert: ${SERVICE_NAME}Down
        expr: up{job="${SERVICE_NAME}"} == 0
        for: 5m
        labels:
          severity: critical
          service: ${SERVICE_NAME}
          environment: ${ENVIRONMENT}
        annotations:
          summary: "${SERVICE_NAME} is down"
          description: "${SERVICE_NAME} has been down for more than 5 minutes"
      
      - alert: ${SERVICE_NAME}HighErrorRate
        expr: rate(http_requests_total{job="${SERVICE_NAME}",status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: ${SERVICE_NAME}
          environment: ${ENVIRONMENT}
        annotations:
          summary: "High error rate for ${SERVICE_NAME}"
          description: "${SERVICE_NAME} has error rate above 10% for 5 minutes"
      
      - alert: ${SERVICE_NAME}HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="${SERVICE_NAME}"}[5m])) > 1
        for: 10m
        labels:
          severity: warning
          service: ${SERVICE_NAME}
          environment: ${ENVIRONMENT}
        annotations:
          summary: "High latency for ${SERVICE_NAME}"
          description: "${SERVICE_NAME} 95th percentile latency is above 1 second"
      
      - alert: ${SERVICE_NAME}HighMemoryUsage
        expr: container_memory_usage_bytes{container="${SERVICE_NAME}"} / container_spec_memory_limit_bytes{container="${SERVICE_NAME}"} > 0.9
        for: 10m
        labels:
          severity: warning
          service: ${SERVICE_NAME}
          environment: ${ENVIRONMENT}
        annotations:
          summary: "High memory usage for ${SERVICE_NAME}"
          description: "${SERVICE_NAME} memory usage is above 90%"
      
      - alert: ${SERVICE_NAME}HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{container="${SERVICE_NAME}"}[5m]) / container_spec_cpu_quota{container="${SERVICE_NAME}"} * 100 > 90
        for: 15m
        labels:
          severity: warning
          service: ${SERVICE_NAME}
          environment: ${ENVIRONMENT}
        annotations:
          summary: "High CPU usage for ${SERVICE_NAME}"
          description: "${SERVICE_NAME} CPU usage is above 90%"