# CI/CD Pipeline Monitoring and Metrics Configuration
# Comprehensive monitoring for pipeline performance, quality, and security

# Prometheus Configuration for Pipeline Metrics
prometheus:
  scrape_configs:
  # GitLab CI/CD Metrics
  - job_name: 'gitlab-ci'
    static_configs:
    - targets: ['gitlab.com']
    scheme: https
    metrics_path: '/api/v4/projects/PROJECT_ID/pipelines/metrics'
    bearer_token: '${GITLAB_TOKEN}'
    scrape_interval: 60s
    scrape_timeout: 30s
    
  # GitHub Actions Metrics
  - job_name: 'github-actions'
    static_configs:
    - targets: ['api.github.com']
    scheme: https
    metrics_path: '/repos/OWNER/REPO/actions/runs/metrics'
    bearer_token: '${GITHUB_TOKEN}'
    scrape_interval: 60s
    scrape_timeout: 30s
    
  # Custom Pipeline Metrics
  - job_name: 'pipeline-metrics'
    static_configs:
    - targets: ['pipeline-metrics:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  # Test Results Metrics
  - job_name: 'test-metrics'
    static_configs:
    - targets: ['test-metrics:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

# Custom Metrics Definitions
metrics:
  # Pipeline Performance Metrics
  pipeline_performance:
    # Duration metrics
    - name: pipeline_duration_seconds
      type: histogram
      description: "Total pipeline execution time"
      labels: [pipeline_name, branch, environment, status]
      buckets: [30, 60, 120, 300, 600, 1200, 1800, 3600]
      
    - name: stage_duration_seconds
      type: histogram
      description: "Individual stage execution time"
      labels: [pipeline_name, stage_name, branch, status]
      buckets: [10, 30, 60, 120, 300, 600, 900]
      
    - name: job_duration_seconds
      type: histogram
      description: "Individual job execution time"
      labels: [pipeline_name, stage_name, job_name, branch, status]
      buckets: [5, 15, 30, 60, 120, 300, 600]
      
    # Success/Failure metrics
    - name: pipeline_runs_total
      type: counter
      description: "Total number of pipeline runs"
      labels: [pipeline_name, branch, trigger, status]
      
    - name: pipeline_success_rate
      type: gauge
      description: "Pipeline success rate over time window"
      labels: [pipeline_name, branch, time_window]
      
    # Queue and wait times
    - name: pipeline_queue_time_seconds
      type: histogram
      description: "Time spent waiting in queue before execution"
      labels: [pipeline_name, branch]
      buckets: [1, 5, 10, 30, 60, 120, 300]
      
    - name: runner_availability
      type: gauge
      description: "Number of available runners"
      labels: [runner_type, environment]

  # Test Quality Metrics
  test_quality:
    # Test execution metrics
    - name: test_runs_total
      type: counter
      description: "Total number of test runs"
      labels: [test_suite, branch, status]
      
    - name: test_duration_seconds
      type: histogram
      description: "Test execution time"
      labels: [test_suite, test_type, branch]
      buckets: [1, 5, 10, 30, 60, 120, 300, 600]
      
    - name: test_cases_total
      type: gauge
      description: "Total number of test cases"
      labels: [test_suite, test_type, branch]
      
    - name: test_failures_total
      type: counter
      description: "Total number of test failures"
      labels: [test_suite, test_type, branch, failure_type]
      
    # Coverage metrics
    - name: code_coverage_percentage
      type: gauge
      description: "Code coverage percentage"
      labels: [service, branch, coverage_type]
      
    - name: coverage_change_percentage
      type: gauge
      description: "Coverage change from previous run"
      labels: [service, branch]
      
    # Quality metrics
    - name: code_quality_score
      type: gauge
      description: "Overall code quality score"
      labels: [service, branch, metric_type]
      
    - name: technical_debt_ratio
      type: gauge
      description: "Technical debt ratio"
      labels: [service, branch]

  # Security Metrics
  security:
    # Vulnerability metrics
    - name: vulnerabilities_total
      type: gauge
      description: "Total number of vulnerabilities found"
      labels: [severity, scanner, service, branch]
      
    - name: vulnerability_scan_duration_seconds
      type: histogram
      description: "Security scan execution time"
      labels: [scanner, scan_type]
      buckets: [30, 60, 120, 300, 600, 900]
      
    - name: secrets_detected_total
      type: counter
      description: "Total number of secrets detected"
      labels: [detector, service, branch, secret_type]
      
    - name: compliance_score
      type: gauge
      description: "Compliance score for standards"
      labels: [standard, service, branch]
      
    # License compliance
    - name: license_violations_total
      type: gauge
      description: "Total number of license violations"
      labels: [license_type, service, branch]

  # Deployment Metrics
  deployment:
    # Deployment frequency
    - name: deployments_total
      type: counter
      description: "Total number of deployments"
      labels: [environment, service, status, strategy]
      
    - name: deployment_duration_seconds
      type: histogram
      description: "Deployment execution time"
      labels: [environment, service, strategy]
      buckets: [60, 120, 300, 600, 900, 1200, 1800]
      
    - name: deployment_frequency
      type: gauge
      description: "Deployment frequency (deployments per day)"
      labels: [environment, service]
      
    # Lead time and recovery
    - name: lead_time_seconds
      type: histogram
      description: "Lead time from commit to production"
      labels: [service, branch]
      buckets: [1800, 3600, 7200, 14400, 28800, 43200, 86400]
      
    - name: mean_time_to_recovery_seconds
      type: histogram
      description: "Mean time to recovery from failures"
      labels: [environment, service]
      buckets: [300, 600, 1800, 3600, 7200, 14400, 28800]
      
    - name: change_failure_rate
      type: gauge
      description: "Percentage of deployments causing failures"
      labels: [environment, service]

  # Resource Utilization
  resources:
    # CI/CD resource usage
    - name: ci_runner_cpu_usage_percentage
      type: gauge
      description: "CI runner CPU usage"
      labels: [runner_id, runner_type]
      
    - name: ci_runner_memory_usage_bytes
      type: gauge
      description: "CI runner memory usage"
      labels: [runner_id, runner_type]
      
    - name: ci_runner_disk_usage_bytes
      type: gauge
      description: "CI runner disk usage"
      labels: [runner_id, runner_type]
      
    # Cost metrics
    - name: ci_cost_dollars
      type: counter
      description: "CI/CD infrastructure cost"
      labels: [provider, resource_type, environment]
      
    - name: build_cost_per_minute
      type: gauge
      description: "Cost per minute for builds"
      labels: [runner_type, environment]

# Grafana Dashboard Configuration
dashboards:
  # Pipeline Overview Dashboard
  - name: "CI/CD Pipeline Overview"
    uid: "pipeline-overview"
    tags: ["ci-cd", "overview"]
    panels:
    - title: "Pipeline Success Rate"
      type: "stat"
      targets:
      - expr: "avg(pipeline_success_rate{time_window='7d'})"
      - expr: "avg(pipeline_success_rate{time_window='30d'})"
      thresholds:
        steps:
        - color: "red"
          value: 0
        - color: "yellow"
          value: 0.85
        - color: "green"
          value: 0.95
          
    - title: "Pipeline Duration Trend"
      type: "timeseries"
      targets:
      - expr: "avg(pipeline_duration_seconds) by (pipeline_name)"
      
    - title: "Build Frequency"
      type: "timeseries"
      targets:
      - expr: "rate(pipeline_runs_total[1h])"
      
    - title: "Failed Pipelines"
      type: "table"
      targets:
      - expr: "pipeline_runs_total{status='failed'}"

  # Test Quality Dashboard
  - name: "Test Quality Metrics"
    uid: "test-quality"
    tags: ["testing", "quality"]
    panels:
    - title: "Test Coverage"
      type: "timeseries"
      targets:
      - expr: "code_coverage_percentage"
      
    - title: "Test Execution Time"
      type: "heatmap"
      targets:
      - expr: "test_duration_seconds_bucket"
      
    - title: "Test Failure Rate"
      type: "stat"
      targets:
      - expr: "rate(test_failures_total[24h]) / rate(test_runs_total[24h])"
      
    - title: "Flaky Tests"
      type: "table"
      targets:
      - expr: "test_flakiness_score > 0.1"

  # Security Dashboard
  - name: "Security Metrics"
    uid: "security-metrics"
    tags: ["security", "compliance"]
    panels:
    - title: "Vulnerability Trend"
      type: "timeseries"
      targets:
      - expr: "vulnerabilities_total"
      
    - title: "Critical Vulnerabilities"
      type: "stat"
      targets:
      - expr: "vulnerabilities_total{severity='critical'}"
      thresholds:
        steps:
        - color: "green"
          value: 0
        - color: "red"
          value: 1
          
    - title: "Compliance Scores"
      type: "bargauge"
      targets:
      - expr: "compliance_score"
      
    - title: "Secret Detections"
      type: "table"
      targets:
      - expr: "secrets_detected_total"

  # Deployment Dashboard
  - name: "Deployment Metrics"
    uid: "deployment-metrics"
    tags: ["deployment", "dora"]
    panels:
    - title: "Deployment Frequency"
      type: "stat"
      targets:
      - expr: "deployment_frequency"
      
    - title: "Lead Time"
      type: "histogram"
      targets:
      - expr: "lead_time_seconds_bucket"
      
    - title: "MTTR"
      type: "stat"
      targets:
      - expr: "mean_time_to_recovery_seconds"
      
    - title: "Change Failure Rate"
      type: "gauge"
      targets:
      - expr: "change_failure_rate"
      thresholds:
        steps:
        - color: "green"
          value: 0
        - color: "yellow"
          value: 0.1
        - color: "red"
          value: 0.2

# Alerting Rules
alerting:
  groups:
  # Pipeline Health Alerts
  - name: pipeline.health
    rules:
    - alert: PipelineFailureRate
      expr: (rate(pipeline_runs_total{status="failed"}[1h]) / rate(pipeline_runs_total[1h])) > 0.2
      for: 5m
      labels:
        severity: warning
        team: devops
      annotations:
        summary: "High pipeline failure rate"
        description: "Pipeline failure rate is {{ $value | humanizePercentage }} over the last hour"
        runbook_url: "https://docs.publishai.com/runbooks/pipeline-failures"
        
    - alert: PipelineDurationHigh
      expr: avg(pipeline_duration_seconds) by (pipeline_name) > 1800
      for: 10m
      labels:
        severity: warning
        team: devops
      annotations:
        summary: "Pipeline duration is high"
        description: "Pipeline {{ $labels.pipeline_name }} duration is {{ $value | humanizeDuration }}"
        
    - alert: NoDeployments
      expr: rate(deployments_total[24h]) == 0
      for: 1h
      labels:
        severity: critical
        team: devops
      annotations:
        summary: "No deployments in 24 hours"
        description: "No deployments have occurred in the last 24 hours"

  # Quality Alerts
  - name: quality.alerts
    rules:
    - alert: CoverageDropped
      expr: coverage_change_percentage < -5
      for: 5m
      labels:
        severity: warning
        team: development
      annotations:
        summary: "Code coverage dropped significantly"
        description: "Coverage for {{ $labels.service }} dropped by {{ $value }}%"
        
    - alert: TestFailureSpike
      expr: rate(test_failures_total[1h]) > rate(test_failures_total[24h]) * 2
      for: 5m
      labels:
        severity: warning
        team: development
      annotations:
        summary: "Test failure rate spike"
        description: "Test failures increased significantly in the last hour"

  # Security Alerts
  - name: security.alerts
    rules:
    - alert: CriticalVulnerability
      expr: vulnerabilities_total{severity="critical"} > 0
      for: 1m
      labels:
        severity: critical
        team: security
      annotations:
        summary: "Critical vulnerability detected"
        description: "{{ $value }} critical vulnerabilities found in {{ $labels.service }}"
        
    - alert: SecretsDetected
      expr: increase(secrets_detected_total[1h]) > 0
      for: 1m
      labels:
        severity: critical
        team: security
      annotations:
        summary: "Secrets detected in code"
        description: "{{ $value }} secrets detected in {{ $labels.service }}"

# Notification Channels
notifications:
  slack:
    channels:
    - name: "#ci-cd-alerts"
      webhook_url: "${SLACK_WEBHOOK_CICD}"
      alert_types: ["pipeline", "deployment"]
      severity: ["warning", "critical"]
      
    - name: "#security-alerts"
      webhook_url: "${SLACK_WEBHOOK_SECURITY}"
      alert_types: ["security"]
      severity: ["critical"]
      
    - name: "#quality-alerts"
      webhook_url: "${SLACK_WEBHOOK_QUALITY}"
      alert_types: ["quality"]
      severity: ["warning", "critical"]
      
  email:
    recipients:
    - email: "<EMAIL>"
      alert_types: ["pipeline", "deployment"]
      severity: ["critical"]
      
    - email: "<EMAIL>"
      alert_types: ["security"]
      severity: ["warning", "critical"]
      
  pagerduty:
    integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
    alert_types: ["pipeline", "security"]
    severity: ["critical"]

# SLI/SLO Configuration
sli_slo:
  # Service Level Indicators
  slis:
  - name: "pipeline_success_rate"
    query: "avg(pipeline_success_rate{time_window='7d'})"
    description: "Percentage of successful pipeline runs over 7 days"
    
  - name: "deployment_success_rate"
    query: "deployments_total{status='success'} / deployments_total"
    description: "Percentage of successful deployments"
    
  - name: "test_pass_rate"
    query: "(test_runs_total - test_failures_total) / test_runs_total"
    description: "Percentage of passing tests"
    
  - name: "security_scan_pass_rate"
    query: "1 - (vulnerabilities_total{severity=~'high|critical'} > 0)"
    description: "Percentage of scans without high/critical vulnerabilities"
    
  # Service Level Objectives
  slos:
  - name: "pipeline_reliability"
    sli: "pipeline_success_rate"
    target: 0.95
    time_window: "7d"
    description: "95% of pipelines should succeed over 7 days"
    
  - name: "deployment_reliability"
    sli: "deployment_success_rate"
    target: 0.98
    time_window: "30d"
    description: "98% of deployments should succeed over 30 days"
    
  - name: "test_reliability"
    sli: "test_pass_rate"
    target: 0.99
    time_window: "1d"
    description: "99% of tests should pass daily"
    
  - name: "security_compliance"
    sli: "security_scan_pass_rate"
    target: 1.0
    time_window: "1d"
    description: "100% of scans should pass without critical vulnerabilities"

# Error Budget Configuration
error_budgets:
- name: "pipeline_reliability_budget"
  slo: "pipeline_reliability"
  budget_remaining_threshold: 0.1  # Alert when 10% budget remaining
  budget_burn_rate_threshold: 5.0  # Alert on high burn rate
  
- name: "deployment_reliability_budget"
  slo: "deployment_reliability"
  budget_remaining_threshold: 0.05  # Alert when 5% budget remaining
  budget_burn_rate_threshold: 3.0

# Report Configuration
reports:
  # Weekly CI/CD Report
  weekly_report:
    enabled: true
    schedule: "0 9 * * MON"  # Monday 9 AM
    recipients: ["<EMAIL>"]
    metrics:
    - "pipeline_success_rate"
    - "deployment_frequency"
    - "lead_time_seconds"
    - "test_coverage_percentage"
    - "vulnerabilities_total"
    
  # Monthly Quality Report
  monthly_report:
    enabled: true
    schedule: "0 9 1 * *"  # First day of month 9 AM
    recipients: ["<EMAIL>"]
    metrics:
    - "code_quality_score"
    - "technical_debt_ratio"
    - "test_reliability"
    - "security_compliance"
    - "deployment_reliability"

# Data Retention
data_retention:
  metrics:
    short_term: "7d"    # High resolution for 7 days
    medium_term: "30d"  # Medium resolution for 30 days
    long_term: "1y"     # Low resolution for 1 year
    
  logs:
    pipeline_logs: "90d"
    test_logs: "30d"
    security_logs: "1y"
    audit_logs: "7y"