# Security Configuration for CI/CD Pipeline
# Comprehensive security scanning and vulnerability management

# Bandit Configuration for Python Security Scanning
bandit:
  # Severity levels: HIGH, MEDIUM, LOW
  severity_threshold: MEDIUM
  confidence_threshold: MEDIUM
  
  # Tests to skip (with justification)
  skips:
    # B101: Skip assert_used test in test files
    - test_*.py:B101
    - tests/:B101
    
    # B311: Skip random for non-cryptographic use
    - app/utils/test_data.py:B311
    
  # Additional configuration
  exclude_dirs:
    - .venv
    - venv
    - node_modules
    - .git
    - __pycache__
    - .pytest_cache
    
  # Custom patterns to scan
  include:
    - "*.py"
    - "*.pyi"

# Safety Configuration for Dependency Scanning
safety:
  # Vulnerability database settings
  auto_update: true
  cache_dir: ".safety_cache"
  
  # Ignore specific vulnerabilities (with expiration dates)
  ignore:
    # Example: ignore a specific vulnerability until fix is available
    # - id: 12345
    #   reason: "False positive - not applicable to our use case"
    #   expires: "2024-03-01"
  
  # Severity thresholds
  severity_threshold: "medium"
  
  # Fail on vulnerabilities
  fail_on:
    - "high"
    - "critical"

# Semgrep Configuration for Static Analysis
semgrep:
  # Rule configurations
  configs:
    - "p/security-audit"
    - "p/secrets"
    - "p/python"
    - "p/docker"
    - "p/kubernetes"
    - "p/terraform"
  
  # Custom rules directory
  custom_rules: "ci-cd/security/semgrep-rules/"
  
  # Severity settings
  severity:
    error_on:
      - "ERROR"
    warning_on:
      - "WARNING"
    info_on:
      - "INFO"
  
  # Files to exclude
  exclude:
    - "tests/"
    - "*.test.py"
    - "conftest.py"
    - ".venv/"
    - "node_modules/"

# Container Security Scanning
container_scanning:
  # Scanners to use
  scanners:
    - trivy
    - grype
    - snyk
  
  # Trivy configuration
  trivy:
    severity: "HIGH,CRITICAL"
    format: "sarif"
    timeout: "10m"
    
    # Vulnerability types to scan
    vuln_types:
      - "os"
      - "library"
    
    # Skip files
    skip_files:
      - "*.md"
      - "*.txt"
      - "*.json"
  
  # Grype configuration
  grype:
    severity_threshold: "medium"
    output_format: "sarif"
    
    # Scope of scan
    scope: "AllLayers"
  
  # Snyk configuration
  snyk:
    severity_threshold: "medium"
    monitor: true
    
    # Test configuration
    test_options:
      - "--all-projects"
      - "--detection-depth=5"

# Secret Detection Configuration
secret_detection:
  # Tools to use
  tools:
    - gitleaks
    - truffleHog
    - detect-secrets
  
  # Gitleaks configuration
  gitleaks:
    config_file: "ci-cd/security/gitleaks.toml"
    baseline_file: ".gitleaks-baseline.json"
    
    # Exit codes
    exit_code:
      on_leak: 1
      on_error: 2
  
  # TruffleHog configuration
  truffleHog:
    entropy_threshold: 4.5
    regex_timeout: "10s"
    
    # Exclude patterns
    exclude:
      - "test_*"
      - "*.test.*"
      - "mock_*"
  
  # Detect-secrets configuration
  detect_secrets:
    baseline: ".secrets.baseline"
    
    # Plugins to use
    plugins:
      - "ArtifactoryDetector"
      - "AWSKeyDetector"
      - "Base64HighEntropyString"
      - "BasicAuthDetector"
      - "CloudantDetector"
      - "GitHubTokenDetector"
      - "HexHighEntropyString"
      - "IBMCloudIamDetector"
      - "IBMCosHmacDetector"
      - "JwtTokenDetector"
      - "KeywordDetector"
      - "MailchimpDetector"
      - "PrivateKeyDetector"
      - "SlackDetector"
      - "SoftlayerDetector"
      - "SquareOAuthDetector"
      - "StripeDetector"
      - "TwilioKeyDetector"

# License Compliance Configuration
license_compliance:
  # Allowed licenses
  allowed_licenses:
    - "MIT"
    - "Apache-2.0"
    - "BSD-2-Clause"
    - "BSD-3-Clause"
    - "ISC"
    - "Python-2.0"
    - "PSF-2.0"
  
  # Prohibited licenses
  prohibited_licenses:
    - "GPL-2.0"
    - "GPL-3.0"
    - "AGPL-1.0"
    - "AGPL-3.0"
    - "LGPL-2.0"
    - "LGPL-2.1"
    - "LGPL-3.0"
    - "CPAL-1.0"
    - "EPL-1.0"
    - "EPL-2.0"
  
  # Review required licenses
  review_required:
    - "CC-BY-4.0"
    - "CC-BY-SA-4.0"
    - "MPL-2.0"
    - "EUPL-1.1"
    - "EUPL-1.2"
  
  # Exceptions (with justification)
  exceptions:
    # - package: "some-package"
    #   license: "GPL-2.0"
    #   reason: "Critical dependency with no alternatives"
    #   approved_by: "security-team"
    #   expires: "2024-06-01"

# Code Quality Gates
quality_gates:
  # Coverage requirements
  coverage:
    minimum_total: 80
    minimum_new_code: 85
    
    # Per-service requirements
    services:
      api-gateway: 85
      content-generation: 80
      market-intelligence: 80
      publishing-service: 85
      cover-designer: 80
      sales-monitor: 80
      personalization: 80
      research: 80
      multimodal-generator: 75
  
  # Code complexity
  complexity:
    maximum_cyclomatic: 10
    maximum_cognitive: 15
    
    # Exceptions for specific files
    exceptions:
      - file: "app/agents/*"
        max_cyclomatic: 15
        reason: "AI agent logic inherently complex"
  
  # Duplication
  duplication:
    maximum_percentage: 5
    minimum_lines: 10
  
  # Technical debt
  technical_debt:
    maximum_ratio: 5  # Maximum 5% technical debt ratio
    
    # Debt categories
    categories:
      - "CODE_SMELL"
      - "BUG"
      - "VULNERABILITY"
      - "SECURITY_HOTSPOT"

# Security Baseline Configuration
security_baseline:
  # OWASP Top 10 checks
  owasp_top10:
    - "A01_2021_Broken_Access_Control"
    - "A02_2021_Cryptographic_Failures"
    - "A03_2021_Injection"
    - "A04_2021_Insecure_Design"
    - "A05_2021_Security_Misconfiguration"
    - "A06_2021_Vulnerable_Components"
    - "A07_2021_Identity_Authentication_Failures"
    - "A08_2021_Software_Data_Integrity_Failures"
    - "A09_2021_Security_Logging_Monitoring_Failures"
    - "A10_2021_Server_Side_Request_Forgery"
  
  # CWE (Common Weakness Enumeration) checks
  cwe_checks:
    - "CWE-79"   # Cross-site Scripting
    - "CWE-89"   # SQL Injection
    - "CWE-200"  # Information Exposure
    - "CWE-287"  # Improper Authentication
    - "CWE-295"  # Improper Certificate Validation
    - "CWE-352"  # Cross-Site Request Forgery
    - "CWE-502"  # Deserialization of Untrusted Data
    - "CWE-611"  # XML External Entity Reference
    - "CWE-798"  # Use of Hard-coded Credentials
    - "CWE-915"  # Improperly Controlled Modification

# Compliance Requirements
compliance:
  # Standards to check against
  standards:
    - "SOC2"
    - "ISO27001"
    - "GDPR"
    - "CCPA"
    - "PCI-DSS"
  
  # Required security controls
  security_controls:
    - "AC-2"   # Account Management
    - "AC-3"   # Access Enforcement
    - "AC-6"   # Least Privilege
    - "AU-2"   # Auditable Events
    - "AU-3"   # Content of Audit Records
    - "AU-12"  # Audit Generation
    - "CM-2"   # Baseline Configuration
    - "CM-3"   # Configuration Change Control
    - "IA-2"   # Identification and Authentication
    - "IA-5"   # Authenticator Management
    - "SC-8"   # Transmission Confidentiality
    - "SC-13"  # Cryptographic Protection
    - "SI-3"   # Malicious Code Protection
    - "SI-4"   # Information System Monitoring

# Notification Configuration
notifications:
  # Security alerts
  security_alerts:
    channels:
      - slack: "#security-alerts"
      - email: "<EMAIL>"
      - webhook: "${SECURITY_WEBHOOK_URL}"
    
    # Alert thresholds
    thresholds:
      critical: "immediate"
      high: "1hour"
      medium: "24hours"
      low: "weekly"
  
  # Compliance notifications
  compliance_notifications:
    channels:
      - slack: "#compliance"
      - email: "<EMAIL>"
    
    # Notification triggers
    triggers:
      - "failed_scan"
      - "new_vulnerability"
      - "policy_violation"
      - "license_issue"

# Remediation Guidelines
remediation:
  # Automatic fixes
  auto_fix:
    enabled: true
    
    # Categories eligible for auto-fix
    categories:
      - "dependency_update"
      - "license_header"
      - "code_formatting"
      - "simple_security_fix"
  
  # Manual review required
  manual_review:
    triggers:
      - "high_severity_vulnerability"
      - "license_policy_violation"
      - "critical_security_finding"
      - "compliance_deviation"
  
  # SLA for remediation
  sla:
    critical: "24hours"
    high: "7days"
    medium: "30days"
    low: "90days"

# Integration Configuration
integrations:
  # SIEM integration
  siem:
    enabled: true
    endpoint: "${SIEM_ENDPOINT}"
    format: "CEF"
  
  # Vulnerability management
  vulnerability_management:
    tool: "DefectDojo"
    endpoint: "${DEFECTDOJO_URL}"
    api_key: "${DEFECTDOJO_API_KEY}"
  
  # Issue tracking
  issue_tracking:
    tool: "Jira"
    project: "SEC"
    endpoint: "${JIRA_URL}"
    username: "${JIRA_USERNAME}"
    api_token: "${JIRA_API_TOKEN}"