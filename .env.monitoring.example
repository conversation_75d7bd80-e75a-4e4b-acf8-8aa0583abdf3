# .env.monitoring.example - Monitoring Configuration Template
# Copy this to .env and fill in your actual values

# =====================================
# SENTRY CONFIGURATION
# =====================================

# Sentry DSN for error tracking and performance monitoring
# Get this from your Sentry project settings
SENTRY_DSN=https://<EMAIL>/your-project-id

# Environment name for Sentry (development, staging, production)
SENTRY_ENVIRONMENT=development

# Release version for Sentry (optional, but recommended)
# You can use git commit hash or version number
SENTRY_RELEASE=

# Sample rate for error capture (0.0 to 1.0)
# 1.0 = capture all errors, 0.1 = capture 10% of errors
SENTRY_SAMPLE_RATE=1.0

# Sample rate for performance tracing (0.0 to 1.0)
# 0.1 = trace 10% of requests (recommended for production)
SENTRY_TRACES_SAMPLE_RATE=0.1

# Whether to send personally identifiable information
# Set to false for production environments with strict privacy requirements
SENTRY_SEND_DEFAULT_PII=false

# =====================================
# LOGFLARE CONFIGURATION
# =====================================

# Logflare API key for structured logging
# Get this from your Logflare account settings
LOGFLARE_API_KEY=your-logflare-api-key

# Logflare source ID for your application logs
# Create a source in Logflare and copy the source ID
LOGFLARE_SOURCE_ID=your-source-uuid

# Logflare API endpoint (usually don't need to change)
LOGFLARE_ENDPOINT=https://api.logflare.app/logs/json

# Batch size for log submissions (number of logs to batch together)
LOGFLARE_BATCH_SIZE=100

# Flush interval in seconds (how often to send batched logs)
LOGFLARE_FLUSH_INTERVAL=5

# =====================================
# GENERAL LOGGING CONFIGURATION
# =====================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable debug mode for detailed logging
DEBUG=false

# =====================================
# FEATURE FLAGS
# =====================================

# Enable performance monitoring
ENABLE_PERFORMANCE_MONITORING=true

# Analytics data retention in days
ANALYTICS_RETENTION_DAYS=90

# =====================================
# SECURITY SETTINGS
# =====================================

# Request timeout in seconds for monitoring operations
MONITORING_REQUEST_TIMEOUT=30

# Maximum log entry size in bytes
MAX_LOG_ENTRY_SIZE=65536

# Enable request ID tracking
ENABLE_REQUEST_ID_TRACKING=true

# Enable user action audit logging
ENABLE_USER_ACTION_LOGGING=true

# =====================================
# DEVELOPMENT/TESTING OVERRIDES
# =====================================

# Set to true to disable real monitoring in tests
TESTING=false

# Test mode - routes logs to console instead of external services
TEST_MODE=false

# Mock external monitoring services for development
MOCK_MONITORING_SERVICES=false

# =====================================
# ADVANCED CONFIGURATION
# =====================================

# Custom Sentry tags (comma-separated key:value pairs)
SENTRY_CUSTOM_TAGS=

# Sentry max breadcrumbs (default: 100)
SENTRY_MAX_BREADCRUMBS=100

# Enable Sentry debug mode for troubleshooting
SENTRY_DEBUG=false

# Logflare custom metadata (JSON string)
LOGFLARE_CUSTOM_METADATA={}

# Rate limiting for monitoring operations (requests per minute)
MONITORING_RATE_LIMIT=1000

# =====================================
# ALERTING CONFIGURATION
# =====================================

# Enable alerting for critical errors
ENABLE_CRITICAL_ERROR_ALERTS=true

# Alert threshold for error rate (errors per minute)
ALERT_ERROR_RATE_THRESHOLD=10

# Alert threshold for slow requests (seconds)
ALERT_SLOW_REQUEST_THRESHOLD=5.0

# Alert threshold for memory usage (percentage)
ALERT_MEMORY_USAGE_THRESHOLD=85

# Alert threshold for CPU usage (percentage)
ALERT_CPU_USAGE_THRESHOLD=80

# =====================================
# INTEGRATION SETTINGS
# =====================================

# Slack webhook for alerts (optional)
SLACK_WEBHOOK_URL=

# Discord webhook for alerts (optional)
DISCORD_WEBHOOK_URL=

# PagerDuty integration key (optional)
PAGERDUTY_INTEGRATION_KEY=

# Email for critical alerts (optional)
ALERT_EMAIL=

# =====================================
# PERFORMANCE TUNING
# =====================================

# Background task queue for monitoring operations
MONITORING_QUEUE_NAME=monitoring

# Worker count for monitoring tasks
MONITORING_WORKER_COUNT=2

# Monitoring data compression (gzip, none)
MONITORING_COMPRESSION=gzip

# Cache TTL for monitoring data (seconds)
MONITORING_CACHE_TTL=300

# =====================================
# COMPLIANCE AND PRIVACY
# =====================================

# Enable data anonymization for logs
ENABLE_DATA_ANONYMIZATION=false

# Data retention policy for monitoring data (days)
MONITORING_DATA_RETENTION_DAYS=30

# Enable GDPR compliance mode
GDPR_COMPLIANCE_MODE=false

# Geographic data location preference (us, eu, global)
DATA_LOCATION_PREFERENCE=us

# =====================================
# NOTES
# =====================================

# 1. Never commit actual credentials to version control
# 2. Use strong, unique API keys for production
# 3. Regularly rotate API keys and tokens
# 4. Monitor your monitoring costs and usage
# 5. Test your monitoring setup in staging before production
# 6. Set up proper alerting for monitoring system failures
# 7. Consider data sovereignty and privacy regulations
# 8. Document your monitoring setup for team members

# For production environments:
# - Set SENTRY_TRACES_SAMPLE_RATE to 0.01-0.1 to control costs
# - Enable GDPR_COMPLIANCE_MODE if serving EU users
# - Set appropriate data retention policies
# - Use environment-specific Sentry projects
# - Enable alerting for critical errors
# - Regularly review and audit monitoring data access