# Pre-commit hooks for code quality and security
# Install with: pre-commit install
# Run manually with: pre-commit run --all-files

repos:
  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Python linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  # Python security scanning
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.6
    hooks:
      - id: bandit
        args: [-r, -ll, -x, tests/]
        files: ^app/.*\.py$

  # Secrets detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: |
          (?x)^(
            .*\.lock$|
            .*\.min\.js$|
            .*\.map$|
            package-lock\.json$
          )$

  # YAML formatting and validation
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # Shell script analysis
  - repo: https://github.com/koalaman/shellcheck-precommit
    rev: v0.9.0
    hooks:
      - id: shellcheck
        args: [-x]

  # Markdown formatting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: [--fix]
        exclude: ^CHANGELOG\.md$

  # Python type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-python-dateutil
          - types-redis
          - pydantic
          - fastapi
        args: [--ignore-missing-imports, --no-strict-optional, --check-untyped-defs]
        exclude: ^(tests/|migrations/|scripts/)
        
  # Additional Python linting with pylint
  - repo: https://github.com/pylint-dev/pylint
    rev: v3.0.3
    hooks:
      - id: pylint
        args: [--disable=C0114,C0115,C0116,R0903,R0913,W0613]
        additional_dependencies:
          - pydantic
          - fastapi
          - httpx
        files: ^app/.*\.py$

  # Security scanning with safety
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        args: [--ignore=51668]  # Ignore specific CVE if false positive

  # Dockerfile security scanning
  - repo: https://github.com/IamTheFij/docker-pre-commit
    rev: v3.0.1
    hooks:
      - id: docker-compose-check

  # Additional Python security and quality checks
  - repo: local
    hooks:
      # Custom security check for hardcoded secrets
      - id: check-hardcoded-secrets
        name: Check for hardcoded secrets
        entry: python3 -c "
import re
import sys
import os

SECRET_PATTERNS = [
    r'(?i)(password|passwd|pwd)\s*=\s*[\"\']\w+[\"\']',
    r'(?i)(secret|key|token)\s*=\s*[\"\']\w{8,}[\"\']',
    r'(?i)(api_key|apikey)\s*=\s*[\"\']\w+[\"\']',
    r'(?i)(access_key|accesskey)\s*=\s*[\"\']\w+[\"\']',
    r'sk-[a-zA-Z0-9]{48}',  # OpenAI API key pattern
    r'xoxb-[0-9]{11}-[0-9]{11}-[a-zA-Z0-9]{24}',  # Slack bot token
]

def check_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for i, line in enumerate(content.splitlines(), 1):
            for pattern in SECRET_PATTERNS:
                if re.search(pattern, line):
                    print(f'Potential hardcoded secret in {filepath}:{i}: {line.strip()[:80]}...')
                    return True
    except (UnicodeDecodeError, IOError):
        pass
    return False

if __name__ == '__main__':
    files = sys.argv[1:]
    found_secrets = False
    
    for filepath in files:
        if filepath.endswith('.py') and check_file(filepath):
            found_secrets = True
    
    if found_secrets:
        print('ERROR: Potential hardcoded secrets detected!')
        sys.exit(1)
"
        language: system
        files: \.py$

      # Check for common security anti-patterns
      - id: check-security-patterns
        name: Check for security anti-patterns
        entry: python3 -c "
import re
import sys

SECURITY_PATTERNS = [
    (r'eval\s*\(', 'Use of eval() is dangerous'),
    (r'exec\s*\(', 'Use of exec() is dangerous'),
    (r'subprocess\.call\s*\([^)]*shell\s*=\s*True', 'subprocess with shell=True is dangerous'),
    (r'os\.system\s*\(', 'Use of os.system() is dangerous'),
    (r'pickle\.loads?\s*\(', 'Use of pickle.load/loads can be dangerous'),
    (r'yaml\.load\s*\([^)]*Loader\s*=\s*yaml\.Loader', 'yaml.load with Loader=yaml.Loader is unsafe'),
    (r'random\.random\s*\(', 'Use secrets module for cryptographic randomness'),
    (r'hashlib\.md5\s*\(', 'MD5 is cryptographically broken'),
    (r'hashlib\.sha1\s*\(', 'SHA1 is cryptographically weak'),
]

def check_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        found_issues = False
        for i, line in enumerate(content.splitlines(), 1):
            for pattern, message in SECURITY_PATTERNS:
                if re.search(pattern, line):
                    print(f'Security issue in {filepath}:{i}: {message}')
                    print(f'  Line: {line.strip()}')
                    found_issues = True
        return found_issues
    except (UnicodeDecodeError, IOError):
        pass
    return False

if __name__ == '__main__':
    files = sys.argv[1:]
    found_issues = False
    
    for filepath in files:
        if filepath.endswith('.py') and check_file(filepath):
            found_issues = True
    
    if found_issues:
        print('ERROR: Security anti-patterns detected!')
        sys.exit(1)
"
        language: system
        files: \.py$

      # Check for TODO/FIXME/HACK comments in production code
      - id: check-todo-fixme
        name: Check for TODO/FIXME comments
        entry: python3 -c "
import re
import sys

TODO_PATTERN = r'(?i)(TODO|FIXME|HACK|XXX|BUG):'

def check_file(filepath):
    # Skip test files and development scripts
    if 'test' in filepath or 'script' in filepath or 'example' in filepath:
        return False
        
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        found_todos = False
        for i, line in enumerate(content.splitlines(), 1):
            if re.search(TODO_PATTERN, line):
                print(f'TODO/FIXME found in {filepath}:{i}: {line.strip()}')
                found_todos = True
        return found_todos
    except (UnicodeDecodeError, IOError):
        pass
    return False

if __name__ == '__main__':
    files = sys.argv[1:]
    found_todos = False
    
    for filepath in files:
        if filepath.endswith('.py') and 'app/' in filepath and check_file(filepath):
            found_todos = True
    
    if found_todos:
        print('WARNING: TODO/FIXME comments found in production code!')
        # Don't fail the commit, just warn
        # sys.exit(1)
"
        language: system
        files: ^app/.*\.py$

# Configuration for specific tools
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false