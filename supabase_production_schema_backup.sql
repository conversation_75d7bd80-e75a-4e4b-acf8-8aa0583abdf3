-- =====================================================
-- AI E-BOOK PUBLISHING PLATFORM - PRODUCTION SCHEMA
-- =====================================================
-- Consolidated Supabase PostgreSQL schema with complete functionality
-- Includes: Core tables, VERL ML integration, Security, Compliance, Performance optimizations
-- Version: Production v2.0
-- Last Updated: 2025-06-30

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "citext";

-- =====================================================
-- CLEANUP SECTION - Safe Drop Operations
-- =====================================================

-- Drop dependent views and functions first
DROP VIEW IF EXISTS user_analytics CASCADE;
DROP VIEW IF EXISTS book_performance CASCADE;
DROP VIEW IF EXISTS prediction_performance_view CASCADE;
DROP VIEW IF EXISTS market_opportunities_view CASCADE;
DROP VIEW IF EXISTS database_health_view CASCADE;
DROP VIEW IF EXISTS query_performance_view CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column CASCADE;
DROP FUNCTION IF EXISTS calculate_book_quality_score(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_user_analytics(UUID) CASCADE;
DROP FUNCTION IF EXISTS anonymize_user_data(UUID) CASCADE;
DROP FUNCTION IF EXISTS generate_compliance_report(DATE, DATE) CASCADE;
DROP FUNCTION IF EXISTS analyze_database_performance() CASCADE;

-- Drop tables in dependency order (referencing tables first)
DROP TABLE IF EXISTS compliance_audit_events CASCADE;
DROP TABLE IF EXISTS data_retention_policies CASCADE;
DROP TABLE IF EXISTS data_anonymization_log CASCADE;
DROP TABLE IF EXISTS privacy_impact_assessments CASCADE;
DROP TABLE IF EXISTS processing_activities CASCADE;
DROP TABLE IF EXISTS gdpr_data_subject_requests CASCADE;
DROP TABLE IF EXISTS oauth_refresh_tokens CASCADE;
DROP TABLE IF EXISTS oauth_authorization_codes CASCADE;
DROP TABLE IF EXISTS oauth_access_tokens CASCADE;
DROP TABLE IF EXISTS api_key_usage CASCADE;
DROP TABLE IF EXISTS api_keys CASCADE;
DROP TABLE IF EXISTS security_audit_events CASCADE;
DROP TABLE IF EXISTS prediction_accuracy CASCADE;
DROP TABLE IF EXISTS market_analyses CASCADE;
DROP TABLE IF EXISTS sales_predictions CASCADE;
DROP TABLE IF EXISTS model_performance CASCADE;
DROP TABLE IF EXISTS verl_training_jobs CASCADE;
DROP TABLE IF EXISTS scraped_market_data CASCADE;
DROP TABLE IF EXISTS feedback_metrics CASCADE;
DROP TABLE IF EXISTS sales_data CASCADE;
DROP TABLE IF EXISTS trend_analyses CASCADE;
DROP TABLE IF EXISTS trends CASCADE;
DROP TABLE IF EXISTS publications CASCADE;
DROP TABLE IF EXISTS books CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS user_tier CASCADE;
DROP TYPE IF EXISTS book_status CASCADE;
DROP TYPE IF EXISTS publication_status CASCADE;
DROP TYPE IF EXISTS training_status CASCADE;
DROP TYPE IF EXISTS subscription_tier CASCADE;
DROP TYPE IF EXISTS gdpr_request_type CASCADE;
DROP TYPE IF EXISTS gdpr_request_status CASCADE;

-- =====================================================
-- CUSTOM TYPES DEFINITION
-- =====================================================

-- Core business types
CREATE TYPE user_tier AS ENUM ('free', 'pro', 'enterprise');
CREATE TYPE subscription_tier AS ENUM ('free', 'pro', 'enterprise');
CREATE TYPE book_status AS ENUM ('draft', 'generating', 'awaiting_approval', 'approved', 'rejected', 'failed', 'published');
CREATE TYPE publication_status AS ENUM ('draft', 'pending', 'publishing', 'published', 'failed', 'cancelled', 'unpublished');
CREATE TYPE training_status AS ENUM ('queued', 'running', 'completed', 'failed', 'cancelled');

-- Compliance types
CREATE TYPE gdpr_request_type AS ENUM ('access', 'rectification', 'erasure', 'portability', 'restriction', 'objection');
CREATE TYPE gdpr_request_status AS ENUM ('pending', 'in_progress', 'completed', 'rejected', 'cancelled');

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Users table with comprehensive user management
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    subscription_tier user_tier DEFAULT 'free',
    
    -- Publishing preferences
    preferred_ai_provider TEXT DEFAULT 'openai',
    default_writing_style TEXT DEFAULT 'professional',
    default_target_audience TEXT DEFAULT 'general adults',
    
    -- KDP Integration (encrypted)
    kdp_email TEXT,
    kdp_password_encrypted TEXT,
    kdp_settings JSONB DEFAULT '{}',
    
    -- User preferences and settings
    notification_preferences JSONB DEFAULT '{"email": true, "in_app": true}',
    content_preferences JSONB DEFAULT '{}',
    privacy_settings JSONB DEFAULT '{"data_sharing": false, "analytics": true}',
    
    -- Analytics and performance metrics
    total_books_generated INTEGER DEFAULT 0,
    total_books_published INTEGER DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0.00,
    avg_quality_score DECIMAL(3,2) DEFAULT 0.00,
    
    -- Security and compliance
    last_password_change TIMESTAMPTZ DEFAULT NOW(),
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    data_retention_consent BOOLEAN DEFAULT TRUE,
    marketing_consent BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_quality_score CHECK (avg_quality_score >= 0 AND avg_quality_score <= 100)
);

-- Books table with comprehensive manuscript management
CREATE TABLE public.books (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Basic book information
    title TEXT NOT NULL,
    subtitle TEXT,
    author TEXT,
    category TEXT NOT NULL,
    description TEXT,
    keywords TEXT[] DEFAULT '{}',
    industry_focus TEXT[] DEFAULT ARRAY['Health', 'Wealth', 'Beauty'],
    
    -- Generation configuration
    target_audience TEXT DEFAULT 'general adults',
    writing_style TEXT DEFAULT 'professional',
    ai_provider TEXT DEFAULT 'openai',
    target_length INTEGER DEFAULT 50000,
    output_formats TEXT[] DEFAULT ARRAY['docx', 'epub', 'pdf'],
    
    -- Content and structure
    content TEXT,
    outline TEXT[],
    word_count INTEGER DEFAULT 0,
    chapter_count INTEGER DEFAULT 0,
    quality_score DECIMAL(3,2),
    reading_time_minutes INTEGER,
    
    -- Generation metadata
    generation_config JSONB DEFAULT '{}',
    generation_metadata JSONB DEFAULT '{}',
    trend_source_data JSONB DEFAULT '{}',
    
    -- File management
    manuscript_path TEXT,
    cover_paths TEXT[] DEFAULT '{}',
    
    -- Status and workflow
    status book_status DEFAULT 'draft',
    rejection_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    approved_at TIMESTAMPTZ,
    published_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_word_count CHECK (word_count >= 0),
    CONSTRAINT valid_chapter_count CHECK (chapter_count >= 0),
    CONSTRAINT valid_quality_score CHECK (quality_score >= 0 AND quality_score <= 100)
);

-- Trends table for market trend tracking
CREATE TABLE public.trends (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Trend identification
    keyword TEXT NOT NULL,
    search_volume INTEGER DEFAULT 0,
    competition_level TEXT DEFAULT 'unknown',
    trend_score DECIMAL(5,2) DEFAULT 0.00,
    
    -- Market data
    category TEXT,
    region TEXT DEFAULT 'US',
    language TEXT DEFAULT 'en',
    
    -- Time series data
    interest_over_time JSONB DEFAULT '{}',
    related_queries JSONB DEFAULT '{}',
    
    -- Analysis results
    opportunity_score DECIMAL(5,2),
    recommendation TEXT,
    
    -- Metadata
    source TEXT DEFAULT 'google_trends',
    data_freshness TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_search_volume CHECK (search_volume >= 0),
    CONSTRAINT valid_trend_score CHECK (trend_score >= 0 AND trend_score <= 100)
);

-- Trend Analyses table for comprehensive market analysis
CREATE TABLE public.trend_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Analysis configuration
    analysis_name TEXT NOT NULL,
    categories TEXT[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    industry_focus TEXT[] DEFAULT '{}',
    
    -- Analysis results
    total_opportunities INTEGER DEFAULT 0,
    high_potential_count INTEGER DEFAULT 0,
    analysis_data JSONB DEFAULT '{}',
    market_insights JSONB DEFAULT '{}',
    recommendations TEXT[],
    
    -- Performance metrics
    execution_time_seconds DECIMAL(8,3),
    data_points_analyzed INTEGER DEFAULT 0,
    
    -- Status and metadata
    status TEXT DEFAULT 'completed',
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_execution_time CHECK (execution_time_seconds >= 0),
    CONSTRAINT valid_data_points CHECK (data_points_analyzed >= 0)
);

-- Publications table for tracking published books
CREATE TABLE public.publications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES public.books(id) ON DELETE CASCADE,
    
    -- Publication details
    kdp_book_id TEXT UNIQUE,
    asin TEXT UNIQUE,
    publication_url TEXT,
    
    -- Pricing and settings
    price DECIMAL(6,2) NOT NULL DEFAULT 2.99,
    royalty_rate INTEGER DEFAULT 70,
    kdp_select BOOLEAN DEFAULT TRUE,
    
    -- Market information
    category_rankings JSONB DEFAULT '{}',
    keywords_used TEXT[] DEFAULT '{}',
    marketing_description TEXT,
    
    -- Publication metadata
    publication_config JSONB DEFAULT '{}',
    platform_metadata JSONB DEFAULT '{}',
    
    -- Status tracking
    status publication_status DEFAULT 'draft',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    last_checked TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_price CHECK (price >= 0.99 AND price <= 999.99),
    CONSTRAINT valid_royalty_rate CHECK (royalty_rate IN (35, 70)),
    CONSTRAINT valid_retry_count CHECK (retry_count >= 0)
);

-- Sales Data table for performance tracking
CREATE TABLE public.sales_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    publication_id UUID NOT NULL REFERENCES public.publications(id) ON DELETE CASCADE,
    
    -- Sales metrics
    units_sold INTEGER DEFAULT 0,
    units_refunded INTEGER DEFAULT 0,
    gross_revenue DECIMAL(10,2) DEFAULT 0.00,
    net_revenue DECIMAL(10,2) DEFAULT 0.00,
    royalty_earned DECIMAL(10,2) DEFAULT 0.00,
    
    -- KDP Unlimited metrics
    pages_read INTEGER DEFAULT 0,
    kindle_unlimited_revenue DECIMAL(10,2) DEFAULT 0.00,
    
    -- Performance metrics
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    rank_in_category INTEGER,
    
    -- Time period
    reporting_date DATE NOT NULL,
    reporting_period TEXT DEFAULT 'daily',
    
    -- Metadata
    currency_code TEXT DEFAULT 'USD',
    marketplace TEXT DEFAULT 'amazon',
    data_source TEXT DEFAULT 'kdp_api',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_sales_numbers CHECK (
        units_sold >= 0 AND units_refunded >= 0 AND 
        gross_revenue >= 0 AND net_revenue >= 0 AND royalty_earned >= 0
    ),
    CONSTRAINT valid_ratings CHECK (average_rating >= 0 AND average_rating <= 5),
    CONSTRAINT unique_daily_sales UNIQUE (publication_id, reporting_date, reporting_period)
);

-- =====================================================
-- VERL ML TRAINING INFRASTRUCTURE
-- =====================================================

-- Feedback Metrics for VERL training data
CREATE TABLE public.feedback_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    book_id UUID REFERENCES public.books(id) ON DELETE CASCADE,
    
    -- Feedback data
    metric_type TEXT NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    approved BOOLEAN,
    approval_time_seconds DECIMAL(8,3) DEFAULT 0,
    rejection_reason TEXT,
    
    -- Context for training
    metric_context JSONB DEFAULT '{}',
    user_profile_snapshot JSONB DEFAULT '{}',
    
    -- VERL training metadata
    used_in_training BOOLEAN DEFAULT FALSE,
    training_weight DECIMAL(5,4) DEFAULT 1.0000,
    quality_score DECIMAL(3,2),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_metric_value CHECK (metric_value >= -1000 AND metric_value <= 1000),
    CONSTRAINT valid_training_weight CHECK (training_weight >= 0 AND training_weight <= 1),
    CONSTRAINT valid_quality_score CHECK (quality_score >= 0 AND quality_score <= 100)
);

-- VERL Training Jobs management
CREATE TABLE public.verl_training_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    
    -- Job configuration
    job_name TEXT NOT NULL,
    training_config JSONB DEFAULT '{}',
    model_version TEXT,
    dataset_size INTEGER DEFAULT 0,
    
    -- Training parameters
    learning_rate DECIMAL(10,8) DEFAULT 0.0001,
    batch_size INTEGER DEFAULT 32,
    num_epochs INTEGER DEFAULT 10,
    
    -- Training progress
    status training_status DEFAULT 'queued',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    current_epoch INTEGER DEFAULT 0,
    current_loss DECIMAL(10,6),
    
    -- Results and metrics
    final_accuracy DECIMAL(5,4),
    training_metrics JSONB DEFAULT '{}',
    model_artifacts_path TEXT,
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_progress CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    CONSTRAINT valid_epoch CHECK (current_epoch >= 0),
    CONSTRAINT valid_accuracy CHECK (final_accuracy >= 0 AND final_accuracy <= 1)
);

-- Model Performance tracking
CREATE TABLE public.model_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    training_job_id UUID REFERENCES public.verl_training_jobs(id) ON DELETE CASCADE,
    
    -- Model identification
    model_version TEXT NOT NULL,
    model_type TEXT DEFAULT 'verl',
    
    -- Performance metrics
    approval_rate DECIMAL(5,4) DEFAULT 0.0000,
    average_quality_score DECIMAL(5,4) DEFAULT 0.0000,
    user_satisfaction_score DECIMAL(5,4) DEFAULT 0.0000,
    sales_performance_score DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Comparative metrics
    improvement_over_baseline DECIMAL(5,4) DEFAULT 0.0000,
    statistical_significance DECIMAL(5,4),
    
    -- Usage statistics
    total_predictions INTEGER DEFAULT 0,
    successful_predictions INTEGER DEFAULT 0,
    
    -- Evaluation period
    evaluation_start_date DATE,
    evaluation_end_date DATE,
    
    -- Metadata
    evaluation_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rates CHECK (
        approval_rate >= 0 AND approval_rate <= 1 AND
        average_quality_score >= 0 AND average_quality_score <= 1 AND
        user_satisfaction_score >= 0 AND user_satisfaction_score <= 1
    )
);

-- =====================================================
-- PREDICTION AND MARKET ANALYSIS TABLES
-- =====================================================

-- Sales Predictions table
CREATE TABLE public.sales_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    book_id UUID REFERENCES public.books(id) ON DELETE CASCADE,
    publication_id UUID REFERENCES public.publications(id) ON DELETE CASCADE,
    
    -- Prediction details
    prediction_type TEXT NOT NULL DEFAULT 'sales_forecast',
    prediction_horizon_days INTEGER NOT NULL DEFAULT 30,
    
    -- Predicted metrics
    predicted_units_sold INTEGER DEFAULT 0,
    predicted_revenue DECIMAL(10,2) DEFAULT 0.00,
    predicted_pages_read INTEGER DEFAULT 0,
    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Input features used
    input_features JSONB DEFAULT '{}',
    model_version TEXT,
    
    -- Prediction metadata
    prediction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_horizon CHECK (prediction_horizon_days > 0 AND prediction_horizon_days <= 365),
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CONSTRAINT unique_prediction UNIQUE (book_id, prediction_type, prediction_date, prediction_horizon_days)
);

-- Market Analyses table
CREATE TABLE public.market_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Analysis details
    analysis_name TEXT NOT NULL,
    market_segment TEXT NOT NULL,
    analysis_type TEXT DEFAULT 'opportunity_assessment',
    
    -- Market metrics
    market_size_estimate DECIMAL(15,2),
    growth_rate DECIMAL(5,4),
    competition_level TEXT,
    opportunity_score DECIMAL(5,2),
    
    -- Analysis results
    key_insights TEXT[],
    recommendations TEXT[],
    risk_factors TEXT[],
    
    -- Supporting data
    data_sources TEXT[],
    analysis_data JSONB DEFAULT '{}',
    methodology_notes TEXT,
    
    -- Validity and confidence
    confidence_level DECIMAL(5,4) DEFAULT 0.0000,
    analysis_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_opportunity_score CHECK (opportunity_score >= 0 AND opportunity_score <= 100),
    CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1)
);

-- Prediction Accuracy tracking
CREATE TABLE public.prediction_accuracy (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prediction_id UUID NOT NULL REFERENCES public.sales_predictions(id) ON DELETE CASCADE,
    
    -- Actual vs Predicted
    actual_units_sold INTEGER DEFAULT 0,
    actual_revenue DECIMAL(10,2) DEFAULT 0.00,
    actual_pages_read INTEGER DEFAULT 0,
    
    -- Accuracy metrics
    units_accuracy_percentage DECIMAL(5,2),
    revenue_accuracy_percentage DECIMAL(5,2),
    pages_accuracy_percentage DECIMAL(5,2),
    overall_accuracy_score DECIMAL(5,4),
    
    -- Error analysis
    absolute_error DECIMAL(10,2),
    relative_error DECIMAL(5,4),
    error_category TEXT,
    
    -- Evaluation details
    evaluation_date DATE NOT NULL,
    evaluation_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_accuracy_percentages CHECK (
        units_accuracy_percentage >= 0 AND units_accuracy_percentage <= 100 AND
        revenue_accuracy_percentage >= 0 AND revenue_accuracy_percentage <= 100 AND
        pages_accuracy_percentage >= 0 AND pages_accuracy_percentage <= 100
    )
);

-- Scraped Market Data for caching external research
CREATE TABLE public.scraped_market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Data identification
    source_url TEXT NOT NULL,
    data_type TEXT NOT NULL,
    category TEXT,
    keywords TEXT[] DEFAULT '{}',
    
    -- Scraped content
    title TEXT,
    content TEXT,
    structured_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    -- Data quality and freshness
    quality_score DECIMAL(3,2),
    relevance_score DECIMAL(3,2),
    freshness_score DECIMAL(3,2),
    
    -- Caching and expiry
    cache_key TEXT UNIQUE,
    expires_at TIMESTAMPTZ,
    last_accessed TIMESTAMPTZ DEFAULT NOW(),
    access_count INTEGER DEFAULT 1,
    
    -- Processing status
    processing_status TEXT DEFAULT 'raw',
    extraction_method TEXT,
    
    -- Timestamps
    scraped_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_scores CHECK (
        quality_score >= 0 AND quality_score <= 100 AND
        relevance_score >= 0 AND relevance_score <= 100 AND
        freshness_score >= 0 AND freshness_score <= 100
    )
);

-- =====================================================
-- SECURITY AND API MANAGEMENT TABLES
-- =====================================================

-- API Keys management
CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Key details
    key_name TEXT NOT NULL,
    key_hash TEXT NOT NULL UNIQUE,
    key_prefix TEXT NOT NULL,
    
    -- Permissions and scope
    scopes TEXT[] DEFAULT '{}',
    permissions JSONB DEFAULT '{}',
    rate_limit_per_hour INTEGER DEFAULT 1000,
    
    -- Security settings
    allowed_ips INET[],
    allowed_domains TEXT[],
    require_https BOOLEAN DEFAULT TRUE,
    
    -- Status and lifecycle
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ,
    usage_count INTEGER DEFAULT 0,
    
    -- Expiry and rotation
    expires_at TIMESTAMPTZ,
    auto_rotate BOOLEAN DEFAULT FALSE,
    rotation_interval_days INTEGER DEFAULT 90,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rate_limit CHECK (rate_limit_per_hour > 0),
    CONSTRAINT valid_rotation_interval CHECK (rotation_interval_days > 0)
);

-- API Key Usage tracking
CREATE TABLE public.api_key_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES public.api_keys(id) ON DELETE CASCADE,
    
    -- Request details
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    
    -- Client information
    client_ip INET,
    user_agent TEXT,
    referer TEXT,
    
    -- Request metadata
    request_size_bytes INTEGER DEFAULT 0,
    response_size_bytes INTEGER DEFAULT 0,
    processing_time_ms INTEGER DEFAULT 0,
    
    -- Rate limiting
    rate_limit_remaining INTEGER,
    rate_limit_reset TIMESTAMPTZ,
    
    -- Error details
    error_message TEXT,
    error_code TEXT,
    
    -- Timestamp
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_status_code CHECK (status_code >= 100 AND status_code <= 599),
    CONSTRAINT valid_sizes CHECK (request_size_bytes >= 0 AND response_size_bytes >= 0)
);

-- Security Audit Events
CREATE TABLE public.security_audit_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    
    -- Event details
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL DEFAULT 'security',
    severity_level TEXT NOT NULL DEFAULT 'info',
    
    -- Event data
    event_description TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    
    -- Source information
    source_ip INET,
    user_agent TEXT,
    session_id TEXT,
    
    -- Risk assessment
    risk_score INTEGER DEFAULT 0,
    automated_response TEXT,
    requires_investigation BOOLEAN DEFAULT FALSE,
    
    -- Investigation status
    investigated_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    investigation_notes TEXT,
    resolution_status TEXT DEFAULT 'open',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    investigated_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_risk_score CHECK (risk_score >= 0 AND risk_score <= 100),
    CONSTRAINT valid_severity CHECK (severity_level IN ('low', 'medium', 'high', 'critical'))
);

-- =====================================================
-- GDPR COMPLIANCE TABLES
-- =====================================================

-- GDPR Data Subject Requests
CREATE TABLE public.gdpr_data_subject_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    
    -- Request details
    request_type gdpr_request_type NOT NULL,
    request_description TEXT,
    data_categories TEXT[],
    
    -- Requestor information
    requestor_email TEXT NOT NULL,
    requestor_name TEXT,
    identity_verified BOOLEAN DEFAULT FALSE,
    verification_method TEXT,
    
    -- Processing details
    status gdpr_request_status DEFAULT 'pending',
    assigned_to UUID REFERENCES public.users(id) ON DELETE SET NULL,
    estimated_completion_date DATE,
    
    -- Legal basis and notes
    legal_basis TEXT,
    processing_notes TEXT,
    rejection_reason TEXT,
    
    -- Data export/deletion
    data_export_path TEXT,
    data_deleted_at TIMESTAMPTZ,
    deletion_confirmation_sent BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_email_format CHECK (requestor_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Processing Activities register
CREATE TABLE public.processing_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Activity details
    activity_name TEXT NOT NULL,
    activity_description TEXT,
    controller_details JSONB DEFAULT '{}',
    processor_details JSONB DEFAULT '{}',
    
    -- Data processing information
    data_categories TEXT[] NOT NULL,
    data_subjects TEXT[] NOT NULL,
    purposes TEXT[] NOT NULL,
    legal_basis TEXT[] NOT NULL,
    
    -- Data flow and storage
    data_sources TEXT[],
    data_recipients TEXT[],
    international_transfers BOOLEAN DEFAULT FALSE,
    transfer_safeguards TEXT[],
    
    -- Retention and security
    retention_period TEXT,
    security_measures TEXT[],
    technical_measures JSONB DEFAULT '{}',
    organizational_measures JSONB DEFAULT '{}',
    
    -- Risk assessment
    privacy_impact_assessment_required BOOLEAN DEFAULT FALSE,
    pia_reference TEXT,
    data_breach_procedures TEXT,
    
    -- Maintenance
    responsible_person UUID REFERENCES public.users(id) ON DELETE SET NULL,
    review_date DATE,
    last_reviewed DATE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Privacy Impact Assessments
CREATE TABLE public.privacy_impact_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    processing_activity_id UUID REFERENCES public.processing_activities(id) ON DELETE CASCADE,
    
    -- Assessment details
    assessment_name TEXT NOT NULL,
    assessment_date DATE NOT NULL,
    assessor_name TEXT NOT NULL,
    assessor_role TEXT,
    
    -- Risk assessment
    privacy_risks JSONB DEFAULT '{}',
    risk_likelihood TEXT,
    risk_impact TEXT,
    overall_risk_rating TEXT,
    
    -- Mitigation measures
    proposed_measures TEXT[],
    implemented_measures TEXT[],
    residual_risk_rating TEXT,
    
    -- Stakeholder consultation
    stakeholders_consulted TEXT[],
    consultation_outcomes TEXT,
    public_consultation_required BOOLEAN DEFAULT FALSE,
    
    -- Review and monitoring
    monitoring_measures TEXT[],
    review_schedule TEXT,
    next_review_date DATE,
    
    -- Documentation
    assessment_document_path TEXT,
    supporting_documents TEXT[],
    
    -- Approval
    approved_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    approval_date DATE,
    approval_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data Retention Policies
CREATE TABLE public.data_retention_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Policy details
    policy_name TEXT NOT NULL,
    data_category TEXT NOT NULL,
    retention_period_months INTEGER NOT NULL,
    
    -- Legal basis and requirements
    legal_requirement TEXT,
    business_justification TEXT,
    regulatory_basis TEXT[],
    
    -- Deletion procedures
    deletion_method TEXT NOT NULL DEFAULT 'soft_delete',
    deletion_schedule TEXT NOT NULL DEFAULT 'monthly',
    automated_deletion BOOLEAN DEFAULT TRUE,
    
    -- Exceptions and holds
    legal_hold_exemptions TEXT[],
    user_consent_extension BOOLEAN DEFAULT FALSE,
    
    -- Implementation
    applies_to_tables TEXT[] NOT NULL,
    deletion_criteria JSONB DEFAULT '{}',
    notification_required BOOLEAN DEFAULT FALSE,
    
    -- Policy management
    policy_owner UUID REFERENCES public.users(id) ON DELETE SET NULL,
    effective_date DATE NOT NULL,
    review_date DATE,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_retention_period CHECK (retention_period_months > 0)
);

-- Data Anonymization Log
CREATE TABLE public.data_anonymization_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Anonymization details
    target_table TEXT NOT NULL,
    target_user_id UUID,
    anonymization_method TEXT NOT NULL,
    fields_anonymized TEXT[] NOT NULL,
    
    -- Process information
    records_affected INTEGER DEFAULT 0,
    anonymization_algorithm TEXT,
    privacy_level TEXT DEFAULT 'k_anonymity',
    
    -- Quality metrics
    utility_preservation_score DECIMAL(5,4),
    privacy_risk_score DECIMAL(5,4),
    data_quality_impact TEXT,
    
    -- Legal and compliance
    legal_basis TEXT,
    gdpr_request_id UUID REFERENCES public.gdpr_data_subject_requests(id) ON DELETE SET NULL,
    retention_policy_id UUID REFERENCES public.data_retention_policies(id) ON DELETE SET NULL,
    
    -- Execution details
    executed_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    execution_notes TEXT,
    rollback_possible BOOLEAN DEFAULT FALSE,
    rollback_deadline TIMESTAMPTZ,
    
    -- Timestamps
    executed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_records_affected CHECK (records_affected >= 0),
    CONSTRAINT valid_scores CHECK (
        utility_preservation_score >= 0 AND utility_preservation_score <= 1 AND
        privacy_risk_score >= 0 AND privacy_risk_score <= 1
    )
);

-- Compliance Audit Events
CREATE TABLE public.compliance_audit_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    
    -- Event classification
    event_type TEXT NOT NULL,
    compliance_framework TEXT NOT NULL DEFAULT 'gdpr',
    event_category TEXT NOT NULL,
    
    -- Event details
    event_description TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    affected_data_subjects INTEGER DEFAULT 0,
    
    -- Risk and impact assessment
    privacy_impact_level TEXT DEFAULT 'low',
    data_breach_risk BOOLEAN DEFAULT FALSE,
    notification_required BOOLEAN DEFAULT FALSE,
    regulatory_risk_score INTEGER DEFAULT 0,
    
    -- Response and remediation
    immediate_actions_taken TEXT[],
    remediation_required BOOLEAN DEFAULT FALSE,
    remediation_deadline DATE,
    remediation_status TEXT DEFAULT 'not_required',
    
    -- Reporting and notifications
    dpo_notified BOOLEAN DEFAULT FALSE,
    dpo_notification_date TIMESTAMPTZ,
    supervisory_authority_notified BOOLEAN DEFAULT FALSE,
    authority_notification_date TIMESTAMPTZ,
    data_subjects_notified BOOLEAN DEFAULT FALSE,
    subject_notification_date TIMESTAMPTZ,
    
    -- Investigation and resolution
    investigated_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    investigation_findings TEXT,
    corrective_measures TEXT[],
    preventive_measures TEXT[],
    
    -- Documentation
    incident_report_path TEXT,
    evidence_collected TEXT[],
    external_counsel_involved BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    investigated_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_risk_score CHECK (regulatory_risk_score >= 0 AND regulatory_risk_score <= 100),
    CONSTRAINT valid_privacy_impact CHECK (privacy_impact_level IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT valid_affected_subjects CHECK (affected_data_subjects >= 0)
);

-- =====================================================
-- PERFORMANCE AND MONITORING TABLES
-- =====================================================

-- Database Health monitoring
CREATE VIEW database_health_view AS
SELECT 
    'tables' as metric_type,
    COUNT(*) as value,
    'Total tables in database' as description
FROM information_schema.tables 
WHERE table_schema = 'public'

UNION ALL

SELECT 
    'total_users' as metric_type,
    COUNT(*)::bigint as value,
    'Total registered users' as description
FROM users

UNION ALL

SELECT 
    'total_books' as metric_type,
    COUNT(*)::bigint as value,
    'Total books created' as description
FROM books

UNION ALL

SELECT 
    'total_publications' as metric_type,
    COUNT(*)::bigint as value,
    'Total published books' as description
FROM publications

UNION ALL

SELECT 
    'active_users_30d' as metric_type,
    COUNT(*)::bigint as value,
    'Users active in last 30 days' as description
FROM users 
WHERE last_login > NOW() - INTERVAL '30 days';

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate book quality score
CREATE OR REPLACE FUNCTION calculate_book_quality_score(book_uuid UUID)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    quality_score DECIMAL(3,2) := 0.00;
    word_count_score DECIMAL(3,2) := 0.00;
    user_feedback_score DECIMAL(3,2) := 0.00;
    sales_performance_score DECIMAL(3,2) := 0.00;
    book_word_count INTEGER;
    avg_feedback DECIMAL(5,4);
    avg_sales DECIMAL(10,2);
BEGIN
    -- Get book word count
    SELECT word_count INTO book_word_count FROM books WHERE id = book_uuid;
    
    -- Calculate word count score (0-30 points)
    word_count_score := CASE 
        WHEN book_word_count >= 50000 THEN 30.00
        WHEN book_word_count >= 30000 THEN 25.00
        WHEN book_word_count >= 15000 THEN 20.00
        WHEN book_word_count >= 8000 THEN 15.00
        ELSE 10.00
    END;
    
    -- Calculate user feedback score (0-40 points)
    SELECT AVG(metric_value) INTO avg_feedback 
    FROM feedback_metrics 
    WHERE book_id = book_uuid AND metric_type = 'user_approval';
    
    user_feedback_score := COALESCE(avg_feedback * 40, 20.00);
    
    -- Calculate sales performance score (0-30 points)
    SELECT AVG(net_revenue) INTO avg_sales
    FROM sales_data sd
    JOIN publications p ON sd.publication_id = p.id
    WHERE p.book_id = book_uuid;
    
    sales_performance_score := CASE 
        WHEN avg_sales >= 100 THEN 30.00
        WHEN avg_sales >= 50 THEN 25.00
        WHEN avg_sales >= 20 THEN 20.00
        WHEN avg_sales >= 5 THEN 15.00
        ELSE 10.00
    END;
    
    quality_score := word_count_score + user_feedback_score + sales_performance_score;
    
    -- Update the book quality score
    UPDATE books SET quality_score = quality_score WHERE id = book_uuid;
    
    RETURN quality_score;
END;
$$ LANGUAGE plpgsql;

-- Function to update user analytics
CREATE OR REPLACE FUNCTION update_user_analytics(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    total_books INTEGER := 0;
    total_published INTEGER := 0;
    total_revenue_amount DECIMAL(10,2) := 0.00;
    avg_quality DECIMAL(3,2) := 0.00;
BEGIN
    -- Count total books
    SELECT COUNT(*) INTO total_books FROM books WHERE user_id = user_uuid;
    
    -- Count published books
    SELECT COUNT(*) INTO total_published 
    FROM books b
    JOIN publications p ON b.id = p.book_id
    WHERE b.user_id = user_uuid AND p.status = 'published';
    
    -- Calculate total revenue
    SELECT COALESCE(SUM(sd.net_revenue), 0.00) INTO total_revenue_amount
    FROM sales_data sd
    JOIN publications p ON sd.publication_id = p.id
    JOIN books b ON p.book_id = b.id
    WHERE b.user_id = user_uuid;
    
    -- Calculate average quality score
    SELECT COALESCE(AVG(quality_score), 0.00) INTO avg_quality
    FROM books 
    WHERE user_id = user_uuid AND quality_score IS NOT NULL;
    
    -- Update user record
    UPDATE users SET
        total_books_generated = total_books,
        total_books_published = total_published,
        total_revenue = total_revenue_amount,
        avg_quality_score = avg_quality,
        updated_at = NOW()
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function for GDPR data anonymization
CREATE OR REPLACE FUNCTION anonymize_user_data(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Anonymize user personal data
    UPDATE users SET
        email = 'anonymized_' || EXTRACT(epoch FROM NOW()) || '@deleted.user',
        full_name = 'Anonymized User',
        kdp_email = NULL,
        kdp_password_encrypted = NULL,
        notification_preferences = '{}',
        content_preferences = '{}',
        privacy_settings = '{}',
        updated_at = NOW()
    WHERE id = user_uuid;
    
    -- Log the anonymization
    INSERT INTO data_anonymization_log (
        target_table,
        target_user_id,
        anonymization_method,
        fields_anonymized,
        records_affected,
        executed_by
    ) VALUES (
        'users',
        user_uuid,
        'pseudonymization',
        ARRAY['email', 'full_name', 'kdp_email', 'kdp_password_encrypted'],
        1,
        user_uuid
    );
END;
$$ LANGUAGE plpgsql;

-- Function to generate compliance reports
CREATE OR REPLACE FUNCTION generate_compliance_report(start_date DATE, end_date DATE)
RETURNS TABLE (
    metric_name TEXT,
    metric_value BIGINT,
    metric_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'gdpr_requests'::TEXT as metric_name,
        COUNT(*)::BIGINT as metric_value,
        'Total GDPR requests processed'::TEXT as metric_description
    FROM gdpr_data_subject_requests 
    WHERE created_at::DATE BETWEEN start_date AND end_date
    
    UNION ALL
    
    SELECT 
        'compliance_events'::TEXT as metric_name,
        COUNT(*)::BIGINT as metric_value,
        'Total compliance audit events'::TEXT as metric_description
    FROM compliance_audit_events 
    WHERE created_at::DATE BETWEEN start_date AND end_date
    
    UNION ALL
    
    SELECT 
        'security_events'::TEXT as metric_name,
        COUNT(*)::BIGINT as metric_value,
        'Total security audit events'::TEXT as metric_description
    FROM security_audit_events 
    WHERE created_at::DATE BETWEEN start_date AND end_date
    
    UNION ALL
    
    SELECT 
        'data_anonymizations'::TEXT as metric_name,
        COUNT(*)::BIGINT as metric_value,
        'Total data anonymization events'::TEXT as metric_description
    FROM data_anonymization_log 
    WHERE executed_at::DATE BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================

-- Updated timestamp triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_books_updated_at BEFORE UPDATE ON books
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_publications_updated_at BEFORE UPDATE ON publications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_market_analyses_updated_at BEFORE UPDATE ON market_analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gdpr_requests_updated_at BEFORE UPDATE ON gdpr_data_subject_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_processing_activities_updated_at BEFORE UPDATE ON processing_activities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_privacy_assessments_updated_at BEFORE UPDATE ON privacy_impact_assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_retention_policies_updated_at BEFORE UPDATE ON data_retention_policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_compliance_events_updated_at BEFORE UPDATE ON compliance_audit_events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE books ENABLE ROW LEVEL SECURITY;
ALTER TABLE trends ENABLE ROW LEVEL SECURITY;
ALTER TABLE trend_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE publications ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE feedback_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE verl_training_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE model_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE prediction_accuracy ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraped_market_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_key_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_audit_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE gdpr_data_subject_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE processing_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_impact_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_retention_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_anonymization_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_audit_events ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Books table policies
CREATE POLICY "Users can view own books" ON books
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own books" ON books
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own books" ON books
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own books" ON books
    FOR DELETE USING (auth.uid()::text = user_id::text);

-- Publications table policies
CREATE POLICY "Users can view own publications" ON publications
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own publications" ON publications
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own publications" ON publications
    FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Sales data policies
CREATE POLICY "Users can view own sales data" ON sales_data
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own sales data" ON sales_data
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Trends and trend analyses policies
CREATE POLICY "Users can view own trends" ON trends
    FOR ALL USING (auth.uid()::text = user_id::text OR user_id IS NULL);

CREATE POLICY "Users can view own trend analyses" ON trend_analyses
    FOR ALL USING (auth.uid()::text = user_id::text OR user_id IS NULL);

-- Feedback metrics policies
CREATE POLICY "Users can view own feedback" ON feedback_metrics
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own feedback" ON feedback_metrics
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- VERL training jobs policies
CREATE POLICY "Users can view own training jobs" ON verl_training_jobs
    FOR SELECT USING (auth.uid()::text = user_id::text OR user_id IS NULL);

-- Prediction tables policies
CREATE POLICY "Users can view own predictions" ON sales_predictions
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own predictions" ON sales_predictions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view own market analyses" ON market_analyses
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own market analyses" ON market_analyses
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Scraped market data policies
CREATE POLICY "Users can view own scraped data" ON scraped_market_data
    FOR ALL USING (auth.uid()::text = user_id::text OR user_id IS NULL);

-- API keys policies
CREATE POLICY "Users can manage own API keys" ON api_keys
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Security audit events policies (admin only for viewing)
CREATE POLICY "Admin can view security events" ON security_audit_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text 
            AND subscription_tier = 'enterprise'
        )
    );

-- GDPR requests policies
CREATE POLICY "Users can view own GDPR requests" ON gdpr_data_subject_requests
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can create GDPR requests" ON gdpr_data_subject_requests
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Processing activities policies (admin only)
CREATE POLICY "Admin can manage processing activities" ON processing_activities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text 
            AND subscription_tier = 'enterprise'
        )
    );

-- =====================================================
-- ANALYTICS VIEWS
-- =====================================================

-- User analytics view
CREATE VIEW user_analytics AS
SELECT 
    u.id,
    u.email,
    u.full_name,
    u.subscription_tier,
    u.total_books_generated,
    u.total_books_published,
    u.total_revenue,
    u.avg_quality_score,
    u.created_at,
    u.last_login,
    
    -- Book statistics
    COUNT(DISTINCT b.id) as actual_books_count,
    COUNT(DISTINCT CASE WHEN b.status = 'published' THEN b.id END) as published_books_count,
    AVG(b.quality_score) as calculated_avg_quality,
    
    -- Publication statistics
    COUNT(DISTINCT p.id) as total_publications,
    COUNT(DISTINCT CASE WHEN p.status = 'published' THEN p.id END) as active_publications,
    
    -- Revenue statistics
    COALESCE(SUM(sd.net_revenue), 0) as calculated_total_revenue,
    COALESCE(AVG(sd.net_revenue), 0) as avg_revenue_per_sale,
    
    -- Engagement metrics
    COUNT(DISTINCT fm.id) as total_feedback_events,
    AVG(CASE WHEN fm.approved THEN 1.0 ELSE 0.0 END) as approval_rate,
    
    -- Recent activity
    MAX(b.created_at) as last_book_created,
    MAX(p.published_at) as last_publication_date

FROM users u
LEFT JOIN books b ON u.id = b.user_id
LEFT JOIN publications p ON b.id = p.book_id
LEFT JOIN sales_data sd ON p.id = sd.publication_id
LEFT JOIN feedback_metrics fm ON u.id = fm.user_id
GROUP BY u.id, u.email, u.full_name, u.subscription_tier, u.total_books_generated, 
         u.total_books_published, u.total_revenue, u.avg_quality_score, u.created_at, u.last_login;

-- Book performance view
CREATE VIEW book_performance AS
SELECT 
    b.id,
    b.title,
    b.category,
    b.target_audience,
    b.status,
    b.word_count,
    b.quality_score,
    b.created_at,
    b.published_at,
    
    -- User information
    u.email as user_email,
    u.subscription_tier,
    
    -- Publication metrics
    COUNT(DISTINCT p.id) as publication_count,
    MAX(p.published_at) as latest_publication_date,
    
    -- Sales performance
    COALESCE(SUM(sd.units_sold), 0) as total_units_sold,
    COALESCE(SUM(sd.net_revenue), 0) as total_revenue,
    COALESCE(AVG(sd.average_rating), 0) as avg_customer_rating,
    COALESCE(SUM(sd.total_reviews), 0) as total_reviews,
    COALESCE(SUM(sd.pages_read), 0) as total_pages_read,
    
    -- Performance ratios
    CASE 
        WHEN SUM(sd.units_sold) > 0 THEN SUM(sd.net_revenue) / SUM(sd.units_sold)
        ELSE 0 
    END as revenue_per_unit,
    
    -- Feedback metrics
    COUNT(DISTINCT fm.id) as feedback_count,
    AVG(CASE WHEN fm.approved THEN 1.0 ELSE 0.0 END) as user_approval_rate,
    AVG(fm.approval_time_seconds) as avg_approval_time,
    
    -- Market performance
    CASE 
        WHEN COUNT(DISTINCT sd.id) > 0 THEN 'active'
        WHEN COUNT(DISTINCT p.id) > 0 THEN 'published'
        ELSE 'unpublished'
    END as market_status

FROM books b
JOIN users u ON b.user_id = u.id
LEFT JOIN publications p ON b.id = p.book_id
LEFT JOIN sales_data sd ON p.id = sd.publication_id
LEFT JOIN feedback_metrics fm ON b.id = fm.book_id
GROUP BY b.id, b.title, b.category, b.target_audience, b.status, b.word_count, 
         b.quality_score, b.created_at, b.published_at, u.email, u.subscription_tier;

-- Prediction performance view
CREATE VIEW prediction_performance_view AS
SELECT 
    sp.id as prediction_id,
    sp.prediction_type,
    sp.prediction_horizon_days,
    sp.predicted_units_sold,
    sp.predicted_revenue,
    sp.confidence_score,
    sp.prediction_date,
    
    -- Accuracy metrics
    pa.actual_units_sold,
    pa.actual_revenue,
    pa.overall_accuracy_score,
    pa.evaluation_date,
    
    -- Performance calculation
    ABS(sp.predicted_units_sold - COALESCE(pa.actual_units_sold, 0)) as units_error,
    ABS(sp.predicted_revenue - COALESCE(pa.actual_revenue, 0)) as revenue_error,
    
    -- Book and user context
    b.title as book_title,
    b.category,
    u.subscription_tier,
    
    -- Model performance
    sp.model_version

FROM sales_predictions sp
LEFT JOIN prediction_accuracy pa ON sp.id = pa.prediction_id
LEFT JOIN books b ON sp.book_id = b.id
LEFT JOIN users u ON sp.user_id = u.id;

-- Market opportunities view
CREATE VIEW market_opportunities_view AS
SELECT 
    ma.id,
    ma.analysis_name,
    ma.market_segment,
    ma.opportunity_score,
    ma.market_size_estimate,
    ma.growth_rate,
    ma.competition_level,
    ma.confidence_level,
    ma.analysis_date,
    
    -- Opportunity categorization
    CASE 
        WHEN ma.opportunity_score >= 80 THEN 'high_opportunity'
        WHEN ma.opportunity_score >= 60 THEN 'medium_opportunity'
        WHEN ma.opportunity_score >= 40 THEN 'low_opportunity'
        ELSE 'poor_opportunity'
    END as opportunity_category,
    
    -- Risk assessment
    CASE 
        WHEN ma.competition_level = 'low' AND ma.opportunity_score >= 70 THEN 'low_risk'
        WHEN ma.competition_level = 'medium' AND ma.opportunity_score >= 60 THEN 'medium_risk'
        ELSE 'high_risk'
    END as risk_category,
    
    -- User context
    u.email as user_email,
    u.subscription_tier,
    
    -- Supporting data
    ma.key_insights,
    ma.recommendations,
    ma.risk_factors

FROM market_analyses ma
JOIN users u ON ma.user_id = u.id
WHERE ma.analysis_date >= CURRENT_DATE - INTERVAL '90 days';

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription_tier ON users(subscription_tier);
CREATE INDEX idx_users_last_login ON users(last_login);

-- Books table indexes
CREATE INDEX idx_books_user_id ON books(user_id);
CREATE INDEX idx_books_status ON books(status);
CREATE INDEX idx_books_category ON books(category);
CREATE INDEX idx_books_created_at ON books(created_at);
CREATE INDEX idx_books_industry_focus ON books USING GIN(industry_focus);
CREATE INDEX idx_books_keywords ON books USING GIN(keywords);

-- Publications table indexes
CREATE INDEX idx_publications_user_id ON publications(user_id);
CREATE INDEX idx_publications_book_id ON publications(book_id);
CREATE INDEX idx_publications_status ON publications(status);
CREATE INDEX idx_publications_kdp_book_id ON publications(kdp_book_id);
CREATE INDEX idx_publications_published_at ON publications(published_at);

-- Sales data indexes
CREATE INDEX idx_sales_data_user_id ON sales_data(user_id);
CREATE INDEX idx_sales_data_publication_id ON sales_data(publication_id);
CREATE INDEX idx_sales_data_reporting_date ON sales_data(reporting_date);
CREATE INDEX idx_sales_data_created_at ON sales_data(created_at);

-- Trends table indexes
CREATE INDEX idx_trends_keyword ON trends(keyword);
CREATE INDEX idx_trends_category ON trends(category);
CREATE INDEX idx_trends_user_id ON trends(user_id);
CREATE INDEX idx_trends_created_at ON trends(created_at);

-- Trend analyses indexes
CREATE INDEX idx_trend_analyses_user_id ON trend_analyses(user_id);
CREATE INDEX idx_trend_analyses_categories ON trend_analyses USING GIN(categories);
CREATE INDEX idx_trend_analyses_industry_focus ON trend_analyses USING GIN(industry_focus);
CREATE INDEX idx_trend_analyses_created_at ON trend_analyses(created_at);

-- Feedback metrics indexes
CREATE INDEX idx_feedback_metrics_user_id ON feedback_metrics(user_id);
CREATE INDEX idx_feedback_metrics_book_id ON feedback_metrics(book_id);
CREATE INDEX idx_feedback_metrics_metric_type ON feedback_metrics(metric_type);
CREATE INDEX idx_feedback_metrics_created_at ON feedback_metrics(created_at);

-- VERL training jobs indexes
CREATE INDEX idx_verl_training_jobs_status ON verl_training_jobs(status);
CREATE INDEX idx_verl_training_jobs_created_at ON verl_training_jobs(created_at);

-- Predictions indexes
CREATE INDEX idx_sales_predictions_user_id ON sales_predictions(user_id);
CREATE INDEX idx_sales_predictions_book_id ON sales_predictions(book_id);
CREATE INDEX idx_sales_predictions_prediction_date ON sales_predictions(prediction_date);

-- Market analyses indexes
CREATE INDEX idx_market_analyses_user_id ON market_analyses(user_id);
CREATE INDEX idx_market_analyses_market_segment ON market_analyses(market_segment);
CREATE INDEX idx_market_analyses_opportunity_score ON market_analyses(opportunity_score);
CREATE INDEX idx_market_analyses_analysis_date ON market_analyses(analysis_date);

-- API keys indexes
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_is_active ON api_keys(is_active);

-- Security audit events indexes
CREATE INDEX idx_security_audit_events_event_type ON security_audit_events(event_type);
CREATE INDEX idx_security_audit_events_severity_level ON security_audit_events(severity_level);
CREATE INDEX idx_security_audit_events_created_at ON security_audit_events(created_at);

-- GDPR requests indexes
CREATE INDEX idx_gdpr_requests_user_id ON gdpr_data_subject_requests(user_id);
CREATE INDEX idx_gdpr_requests_status ON gdpr_data_subject_requests(status);
CREATE INDEX idx_gdpr_requests_request_type ON gdpr_data_subject_requests(request_type);
CREATE INDEX idx_gdpr_requests_created_at ON gdpr_data_subject_requests(created_at);

-- Compliance audit events indexes
CREATE INDEX idx_compliance_events_event_type ON compliance_audit_events(event_type);
CREATE INDEX idx_compliance_events_compliance_framework ON compliance_audit_events(compliance_framework);
CREATE INDEX idx_compliance_events_created_at ON compliance_audit_events(created_at);

-- JSONB indexes for metadata queries
CREATE INDEX idx_books_generation_config ON books USING GIN(generation_config);
CREATE INDEX idx_books_generation_metadata ON books USING GIN(generation_metadata);
CREATE INDEX idx_publications_platform_metadata ON publications USING GIN(platform_metadata);
CREATE INDEX idx_trends_interest_over_time ON trends USING GIN(interest_over_time);
CREATE INDEX idx_trend_analyses_analysis_data ON trend_analyses USING GIN(analysis_data);
CREATE INDEX idx_scraped_market_data_structured_data ON scraped_market_data USING GIN(structured_data);

-- =====================================================
-- ENABLE REALTIME
-- =====================================================

-- Enable realtime for key tables
ALTER PUBLICATION supabase_realtime ADD TABLE users;
ALTER PUBLICATION supabase_realtime ADD TABLE books;
ALTER PUBLICATION supabase_realtime ADD TABLE publications;
ALTER PUBLICATION supabase_realtime ADD TABLE sales_data;
ALTER PUBLICATION supabase_realtime ADD TABLE feedback_metrics;
ALTER PUBLICATION supabase_realtime ADD TABLE verl_training_jobs;

-- =====================================================
-- DATA SEEDING (Optional Development Data)
-- =====================================================

-- Insert sample user for development (optional)
-- INSERT INTO users (email, full_name, subscription_tier) 
-- VALUES ('<EMAIL>', 'Demo User', 'pro');

-- =====================================================
-- SCHEMA VALIDATION
-- =====================================================

-- Verify all tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
    expected_tables INTEGER := 26; -- Update this number when adding tables
BEGIN
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    IF table_count < expected_tables THEN
        RAISE EXCEPTION 'Schema creation incomplete. Expected % tables, found %', expected_tables, table_count;
    END IF;
    
    RAISE NOTICE 'Schema validation successful. Created % tables.', table_count;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 
    'AI E-book Publishing Platform - Production Schema Deployment Complete' as message,
    NOW() as deployment_time,
    (
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    ) as tables_created,
    (
        SELECT COUNT(*) 
        FROM information_schema.views 
        WHERE table_schema = 'public'
    ) as views_created,
    (
        SELECT COUNT(*) 
        FROM information_schema.routines 
        WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
    ) as functions_created;