# Supabase Migration Environment Variables
# Copy this to .env and fill in your Supabase credentials

# =====================================================
# SUPABASE CONFIGURATION (REQUIRED)
# =====================================================

# Your Supabase project URL (from Supabase dashboard)
SUPABASE_URL=https://nutrbmmtnvkuxygtgrch.supabase.co

# Supabase service role key (from Supabase dashboard > Settings > API)
# WARNING: Keep this secret! Never commit to version control
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51dHJibW10bnZrdXh5Z3RncmNoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTE0MzA0NCwiZXhwIjoyMDY2NzE5MDQ0fQ.MNN5hmSbKwlky93l_dAb5xud3B8iMzlOqTdblZGUDKA

# Supabase anon/public key (from Supabase dashboard > Settings > API)
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51dHJibW10bnZrdXh5Z3RncmNoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNDMwNDQsImV4cCI6MjA2NjcxOTA0NH0.z6859iPurwIccexpkU6l7tesUK-TB30aYaj-eM1p0Ng

# Supabase JWT Secret (from Supabase dashboard > Settings > API > JWT Settings)
# Optional: Only needed if using Supabase Auth
SUPABASE_JWT_SECRET="w4j4PcKgU25GFfEaCOkbYm4R1OF5xrE2ydGT8a1G5u473eicy1vXNeVYzbjsOdMrY26vanuxczbwnnGukTwFPA=="

# Supabase Storage URL (optional, for file uploads)
SUPABASE_STORAGE_URL=https://nutrbmmtnvkuxygtgrch.supabase.co/storage/v1

# Enable Supabase Realtime features (optional)
ENABLE_REALTIME=true

# Direct PostgreSQL connection URL (from Supabase dashboard > Settings > Database)
# Format: postgresql://postgres:<EMAIL>:5432/postgres
# Note: Use port 6543 for pooled connections or 5432 for direct connections
DATABASE_URL='************************************************************************************************/postgres'

# =====================================================
# LEGACY CONFIGURATION (PRESERVED)
# =====================================================

# Redis for Celery and background tasks
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY="fF9!gLp3#vNzQ@8uK1^rXs6*WzMb2YtP0&eRq4LcHvAiZx7B"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI API Keys
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# External APIs
GOOGLE_TRENDS_API_KEY=your-google-trends-api-key
AMAZON_API_KEY=your-amazon-api-key

# Storage (now enhanced with Supabase Storage)
STORAGE_PATH=./storage

# CDN Configuration (Optional)
# Set this if you want to use a CDN for static assets
# CDN_URL=https://your-cdn-domain.com

# KDP Settings (encrypted in Supabase)
KDP_EMAIL=your-kdp-email
KDP_PASSWORD=your-kdp-password

# =====================================================
# VERL CONFIGURATION (ENHANCED)
# =====================================================

# Enable/disable VERL
ENABLE_VERL=true

# VERL Model Configuration
VERL_MODEL_NAME=microsoft/DialoGPT-medium
VERL_BATCH_SIZE=8
VERL_LEARNING_RATE=1e-5
VERL_TRAINING_EPOCHS=3

# Training Configuration
MIN_TRAINING_EXAMPLES=50
VERL_TRAINING_INTERVAL_HOURS=2
VERL_AUTO_TRIGGER=true

# VERL Service URL (if using separate VERL service)
VERL_TRAINER_URL=http://localhost:8001

# VERL PPO Settings
VERL_PPO_EPOCHS=4
VERL_CLIP_RATIO=0.2
VERL_GAMMA=0.99
VERL_ENTROPY_COEFF=0.01

# VERL Hardware
VERL_GPU_ENABLED=true
VERL_DEVICE_COUNT=1

# VERL Storage
VERL_CHECKPOINT_DIR=./storage/verl_checkpoints
VERL_TRAINING_DATA_DIR=./storage/training_data
VERL_MODEL_CACHE_DIR=./storage/model_cache

# =====================================================
# SYSTEM CONFIGURATION
# =====================================================

# Logging
LOG_LEVEL=INFO
DEBUG=false

# Performance
MAX_CONCURRENT_GENERATIONS=5
ENABLE_ASYNC_PROCESSING=true

# Features
ENABLE_TREND_ANALYSIS=true
ENABLE_AUTO_PUBLISHING=false
ENABLE_FEEDBACK_COLLECTION=true
ENABLE_COVER_GENERATION=true
ENABLE_PERFORMANCE_MONITORING=true

# Analytics
ANALYTICS_RETENTION_DAYS=90

# =====================================================
# PRODUCTION SETTINGS
# =====================================================

# Set to production for production deployment
ENVIRONMENT=development

# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://your-frontend.vercel.app

# Sentry DSN for error tracking
SENTRY_DSN=your-sentry-dsn-here

# =====================================================
# MIGRATION NOTES
# =====================================================

# 1. Create a Supabase project at https://supabase.com
# 2. Get your project URL and API keys from the dashboard
# 3. Copy the PostgreSQL connection string from Settings > Database
# 4. Run the SQL migration script (supabase_schema.sql) in the SQL Editor
# 5. Update this .env file with your credentials
# 6. Start the application with: python app/main_supabase.py

# =====================================================
# SECURITY REMINDERS
# =====================================================

# - Never commit .env files to version control
# - Use environment-specific .env files
# - Rotate API keys regularly
# - Use Supabase RLS (Row Level Security) in production
# - Enable 2FA on your Supabase account
# - Monitor usage in Supabase dashboard

# =====================================================
# ADDITIONAL SETTINGS (from original .env)
# =====================================================

ENV=development # This is a duplicate of ENVIRONMENT=development, keeping both for now
MIN_BOOK_LENGTH=5000
MAX_BOOK_LENGTH=15000
BOOKS_PER_TREND=3

# Logflare Configuration
LOGFLARE_API_KEY=p6rNnZyfbFYp
LOGFLARE_SOURCE_ID=b05b7508-9348-4ffa-9fdc-fa3db3977b59
LOGFLARE_SOURCE_TOKEN=5c6863ed0a62f25b0953ca28724da56f9fa721998ed8bb043fb6232f066da531

# Supabase Auth Settings (from original .env)
SUPABASE_JWT_SECRET="YOUR_SUPABASE_JWT_SECRET"

# Custom Setting (from original .env)
CUSTOM_SETTING="some_value"
