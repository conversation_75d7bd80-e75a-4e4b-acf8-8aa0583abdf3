# app/config.py - Enhanced Configuration Management (Pydantic v2 compliant)

import os
import secrets
import logging
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # === DATABASE / SUPABASE ===
    database_url: str = os.getenv("DATABASE_URL", "")
    supabase_url: str = os.getenv("SUPABASE_URL", "")
    supabase_service_key: str = os.getenv("SUPABASE_SERVICE_KEY", "")
    supabase_anon_key: str = os.getenv("SUPABASE_ANON_KEY", "")

    # === REDIS ===
    redis_url: str = "redis://localhost:6379"
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None

    # === SECURITY ===
    secret_key: str = os.getenv("SECRET_KEY", "") or secrets.token_urlsafe(32)
    access_token_expire_minutes: int = 30
    oauth_issuer: str = os.getenv("OAUTH_ISSUER", "publish-ai-oauth")
    frontend_url: str = os.getenv("FRONTEND_URL", "http://localhost:3000")

    # === AI + EXTERNAL APIs ===
    openai_api_key: Optional[str] = os.getenv("OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = os.getenv("ANTHROPIC_API_KEY")
    amazon_api_key: Optional[str] = os.getenv("AMAZON_API_KEY")

    # === STORAGE ===
    storage_path: str = "./storage"
    cdn_url: Optional[str] = os.getenv("CDN_URL")  # Optional CDN URL for static assets

    # === VERL SETTINGS ===
    enable_verl: bool = os.getenv("ENABLE_VERL", "true").lower() == "true"
    verl_model_name: str = "microsoft/DialoGPT-medium"
    min_training_examples: int = 50
    verl_training_interval_hours: int = 2
    verl_trainer_url: str = os.getenv("VERL_TRAINER_URL", "http://verl-trainer:8001")
    verl_auto_trigger: bool = os.getenv("VERL_AUTO_TRIGGER", "true").lower() == "true"
    verl_max_response_length: int = int(os.getenv("VERL_MAX_RESPONSE_LENGTH", "256"))
    verl_micro_batch_size: int = int(os.getenv("VERL_MICRO_BATCH_SIZE", "4"))
    verl_ppo_epochs: int = int(os.getenv("VERL_PPO_EPOCHS", "4"))
    verl_ppo_mini_batch_size: int = int(os.getenv("VERL_PPO_MINI_BATCH_SIZE", "4"))
    verl_clip_ratio: float = float(os.getenv("VERL_CLIP_RATIO", "0.2"))
    verl_cliprange_value: float = float(os.getenv("VERL_CLIPRANGE_VALUE", "0.2"))
    verl_gamma: float = float(os.getenv("VERL_GAMMA", "0.99"))
    verl_lam: float = float(os.getenv("VERL_LAM", "0.95"))
    verl_entropy_coeff: float = float(os.getenv("VERL_ENTROPY_COEFF", "0.01"))
    verl_value_loss_coeff: float = float(os.getenv("VERL_VALUE_LOSS_COEFF", "0.5"))
    verl_max_grad_norm: float = float(os.getenv("VERL_MAX_GRAD_NORM", "1.0"))
    verl_user_approval_weight: float = float(
        os.getenv("VERL_USER_APPROVAL_WEIGHT", "0.4")
    )
    verl_content_quality_weight: float = float(
        os.getenv("VERL_CONTENT_QUALITY_WEIGHT", "0.3")
    )
    verl_sales_performance_weight: float = float(
        os.getenv("VERL_SALES_PERFORMANCE_WEIGHT", "0.3")
    )
    verl_gpu_enabled: bool = os.getenv("VERL_GPU_ENABLED", "true").lower() == "true"
    verl_distributed_training: bool = (
        os.getenv("VERL_DISTRIBUTED_TRAINING", "false").lower() == "true"
    )
    verl_device_count: int = int(os.getenv("VERL_DEVICE_COUNT", "1"))
    verl_checkpoint_dir: str = os.getenv(
        "VERL_CHECKPOINT_DIR", "./storage/verl_checkpoints"
    )
    verl_training_data_dir: str = os.getenv(
        "VERL_TRAINING_DATA_DIR", "./storage/training_data"
    )
    verl_model_cache_dir: str = os.getenv(
        "VERL_MODEL_CACHE_DIR", "./storage/model_cache"
    )

    # === VERL TRAINING CONFIG ===
    verl_batch_size: int = int(os.getenv("VERL_BATCH_SIZE", "16"))
    verl_learning_rate: float = float(os.getenv("VERL_LEARNING_RATE", "5e-5"))
    verl_training_epochs: int = int(os.getenv("VERL_TRAINING_EPOCHS", "3"))

    # === KDP CONFIG ===
    kdp_email: Optional[str] = os.getenv("KDP_EMAIL")
    kdp_password: Optional[str] = os.getenv("KDP_PASSWORD")

    # === FEATURE FLAGS ===
    enable_trend_analysis: bool = os.getenv("ENABLE_TREND_ANALYSIS", "true").lower() == "true"
    enable_auto_publishing: bool = os.getenv("ENABLE_AUTO_PUBLISHING", "false").lower() == "true"
    enable_feedback_collection: bool = os.getenv("ENABLE_FEEDBACK_COLLECTION", "true").lower() == "true"
    enable_cover_generation: bool = os.getenv("ENABLE_COVER_GENERATION", "true").lower() == "true"
    enable_performance_monitoring: bool = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"

    # === SYSTEM CONFIG ===
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    enable_debug_logs: bool = os.getenv("DEBUG", "false").lower() == "true"
    max_concurrent_generations: int = int(os.getenv("MAX_CONCURRENT_GENERATIONS", "5"))
    enable_async_processing: bool = (
        os.getenv("ENABLE_ASYNC_PROCESSING", "true").lower() == "true"
    )

    # === MONITORING ===
    sentry_dsn: Optional[str] = os.getenv("SENTRY_DSN")
    sentry_environment: str = os.getenv("SENTRY_ENVIRONMENT", "development")
    sentry_release: Optional[str] = os.getenv("SENTRY_RELEASE")
    sentry_sample_rate: float = float(os.getenv("SENTRY_SAMPLE_RATE", "1.0"))
    sentry_traces_sample_rate: float = float(
        os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.1")
    )
    sentry_send_default_pii: bool = (
        os.getenv("SENTRY_SEND_DEFAULT_PII", "false").lower() == "true"
    )
    logflare_api_key: Optional[str] = os.getenv("LOGFLARE_API_KEY")
    logflare_source_id: Optional[str] = os.getenv("LOGFLARE_SOURCE_ID")
    logflare_endpoint: str = os.getenv(
        "LOGFLARE_ENDPOINT", "https://api.logflare.app/logs/json"
    )
    logflare_batch_size: int = int(os.getenv("LOGFLARE_BATCH_SIZE", "100"))
    logflare_flush_interval: int = int(os.getenv("LOGFLARE_FLUSH_INTERVAL", "5"))

    # === MODEL CONFIG ===
    model_config = {
        "env_file": ".env",
        "extra": "ignore",
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.secret_key:
            object.__setattr__(self, "secret_key", self._generate_secure_secret())

    @staticmethod
    def _generate_secure_secret() -> str:
        secret = secrets.token_urlsafe(32)
        logging.warning(
            "🔒 SECURITY WARNING: Using auto-generated secret key. "
            "Set SECRET_KEY environment variable for production!"
        )
        return secret

    # === NEW HELPER METHODS ===

    def get_verl_reward_weights(self) -> Dict[str, float]:
        """Get normalized VERL reward weights"""
        weights = {
            "user_approval": self.verl_user_approval_weight,
            "content_quality": self.verl_content_quality_weight,
            "sales_performance": self.verl_sales_performance_weight,
        }

        # Normalize weights to sum to 1.0
        total = sum(weights.values())
        if total > 0:
            weights = {k: v / total for k, v in weights.items()}
        else:
            # Fallback to equal weights
            weights = {k: 1.0 / 3 for k in weights.keys()}

        return weights

    def is_verl_configured(self) -> bool:
        """Check if VERL is properly configured"""
        if not self.enable_verl:
            return False

        # Check required components
        required_checks = [
            self.redis_url is not None,
            self.verl_trainer_url is not None,
            self.min_training_examples > 0,
            (self.openai_api_key is not None or self.anthropic_api_key is not None),
            self.verl_model_name is not None,
        ]

        return all(required_checks)

    def get_ai_provider_config(self) -> Dict[str, Any]:
        """Get available AI provider configuration"""
        config: Dict[str, Any] = {"providers": []}

        if self.openai_api_key:
            config["providers"].append("openai")
            config["openai_available"] = True

        if self.anthropic_api_key:
            config["providers"].append("anthropic")
            config["anthropic_available"] = True

        config["primary_provider"] = (
            config["providers"][0] if config["providers"] else None
        )
        config["has_ai_access"] = len(config["providers"]) > 0

        return config

    def get_verl_training_config(self) -> Dict[str, Any]:
        """Get complete VERL training configuration"""
        return {
            "model": {
                "name": self.verl_model_name,
                "max_response_length": self.verl_max_response_length,
                "trust_remote_code": True,
            },
            "training": {
                "batch_size": self.verl_batch_size,
                "micro_batch_size": self.verl_micro_batch_size,
                "learning_rate": self.verl_learning_rate,
                "num_epochs": self.verl_training_epochs,
                "ppo_epochs": self.verl_ppo_epochs,
                "ppo_mini_batch_size": self.verl_ppo_mini_batch_size,
            },
            "ppo": {
                "clip_ratio": self.verl_clip_ratio,
                "cliprange_value": self.verl_cliprange_value,
                "gamma": self.verl_gamma,
                "lam": self.verl_lam,
                "entropy_coeff": self.verl_entropy_coeff,
                "value_loss_coeff": self.verl_value_loss_coeff,
                "max_grad_norm": self.verl_max_grad_norm,
            },
            "rewards": self.get_verl_reward_weights(),
            "hardware": {
                "gpu_enabled": self.verl_gpu_enabled,
                "distributed": self.verl_distributed_training,
                "device_count": self.verl_device_count,
            },
            "storage": {
                "checkpoint_dir": self.verl_checkpoint_dir,
                "training_data_dir": self.verl_training_data_dir,
                "model_cache_dir": self.verl_model_cache_dir,
            },
        }

    def get_system_status(self) -> Dict[str, Any]:
        """Get system configuration status"""
        ai_config = self.get_ai_provider_config()

        return {
            "database_configured": bool(self.database_url),
            "redis_configured": bool(self.redis_url),
            "ai_providers": ai_config["providers"],
            "has_ai_access": ai_config["has_ai_access"],
            "verl_enabled": self.enable_verl,
            "verl_configured": self.is_verl_configured(),
            "storage_path": self.storage_path,
            "kdp_configured": bool(self.kdp_email and self.kdp_password),
            "features": {
                "trend_analysis": self.enable_trend_analysis,
                "auto_publishing": self.enable_auto_publishing,
                "feedback_collection": self.enable_feedback_collection,
                "cover_generation": self.enable_cover_generation,
                "performance_monitoring": self.enable_performance_monitoring,
            },
        }

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring configuration status"""
        return {
            "sentry_configured": bool(self.sentry_dsn),
            "logflare_configured": bool(
                self.logflare_api_key and self.logflare_source_id
            ),
            "sentry_environment": self.sentry_environment,
            "performance_monitoring": self.enable_performance_monitoring,
            "sample_rates": {
                "sentry_errors": self.sentry_sample_rate,
                "sentry_traces": self.sentry_traces_sample_rate,
            },
        }

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration and return issues"""
        issues = []
        warnings = []

        # Check required AI keys
        if not self.openai_api_key and not self.anthropic_api_key:
            issues.append("At least one AI API key (OpenAI or Anthropic) is required")

        # Check VERL configuration if enabled
        if self.enable_verl:
            if not self.redis_url:
                issues.append("Redis URL is required for VERL integration")

            if not self.verl_trainer_url:
                issues.append("VERL trainer URL is required")

            if self.min_training_examples < 10:
                warnings.append(
                    "Minimum training examples should be at least 10 for meaningful results"
                )

            if self.verl_training_interval_hours < 1:
                warnings.append("VERL training interval should be at least 1 hour")

        # Check database URL
        if not self.database_url:
            issues.append("Database URL is required")

        # Check storage path
        if not self.storage_path:
            warnings.append("Storage path not configured, using default")

        # Check KDP configuration
        if self.enable_auto_publishing and not (self.kdp_email and self.kdp_password):
            warnings.append(
                "Auto-publishing enabled but KDP credentials not configured"
            )

        # Check monitoring configuration
        if self.enable_performance_monitoring:
            if not self.sentry_dsn and not self.logflare_api_key:
                warnings.append(
                    "Performance monitoring enabled but no monitoring services configured"
                )

            if self.sentry_dsn and self.sentry_traces_sample_rate > 0.1:
                warnings.append(
                    "High Sentry trace sample rate may impact performance and costs"
                )

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "system_status": self.get_system_status(),
            "monitoring_status": self.get_monitoring_status(),
        }


# Create settings instance (your existing pattern)
settings = Settings()

# === COMPATIBILITY FUNCTIONS FOR YOUR EXISTING CODE ===


def get_settings() -> Settings:
    """Get settings instance (for compatibility with new VERL integration)"""
    return settings


def get_database_url() -> str:
    """Get database URL (compatibility function)"""
    return settings.database_url


def get_ai_api_keys() -> Dict[str, Optional[str]]:
    """Get AI API keys (compatibility function)"""
    return {"openai": settings.openai_api_key, "anthropic": settings.anthropic_api_key}


def is_verl_enabled() -> bool:
    """Check if VERL is enabled and configured"""
    return settings.enable_verl and settings.is_verl_configured()


# === CONFIGURATION VALIDATION SCRIPT ===

if __name__ == "__main__":
    # Configuration validation script
    print("🔧 Validating enhanced configuration...")

    # Show current settings
    print(f"App Configuration:")
    print(f"  Database: {settings.database_url}")
    print(f"  Redis: {settings.redis_url}")
    print(f"  Storage: {settings.storage_path}")
    print(f"  VERL Enabled: {settings.enable_verl}")

    if settings.enable_verl:
        print(f"\nVERL Configuration:")
        print(f"  Model: {settings.verl_model_name}")
        print(f"  Trainer URL: {settings.verl_trainer_url}")
        print(f"  Min Examples: {settings.min_training_examples}")
        print(f"  Training Interval: {settings.verl_training_interval_hours}h")
        print(f"  GPU Enabled: {settings.verl_gpu_enabled}")
        print(f"  Configured: {settings.is_verl_configured()}")

    # Validate configuration
    validation = settings.validate_configuration()

    print(f"\n📊 Validation Results:")
    print(f"  Valid: {'✅' if validation['valid'] else '❌'}")

    if validation["issues"]:
        print("\n❌ Issues:")
        for issue in validation["issues"]:
            print(f"  - {issue}")

    if validation["warnings"]:
        print("\n⚠️  Warnings:")
        for warning in validation["warnings"]:
            print(f"  - {warning}")

    # Show system status
    status = validation["system_status"]
    print(f"\n🎯 System Status:")
    print(
        f"  AI Providers: {', '.join(status['ai_providers']) if status['ai_providers'] else 'None'}"
    )
    print(f"  VERL Ready: {'✅' if status['verl_configured'] else '❌'}")
    print(
        f"  Features Enabled: {sum(status['features'].values())}/{len(status['features'])}"
    )


