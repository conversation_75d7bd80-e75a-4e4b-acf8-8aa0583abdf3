### app/schemas/user.py - User Schemas

from pydantic import BaseModel, EmailStr, Field, field_validator, ConfigDict
from typing import Optional, Literal
from datetime import datetime
import re

class UserBase(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True)
    
    email: EmailStr = Field(description="User email address")
    full_name: str = Field(min_length=2, max_length=100, description="User full name")
    
    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v: str) -> str:
        """Validate full name format"""
        if not re.match(r'^[a-zA-Z\s\-\.\']+$', v):
            raise ValueError("Full name can only contain letters, spaces, hyphens, dots, and apostrophes")
        return v.title()  # Capitalize properly

class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=128, description="Password (minimum 8 characters)")
    subscription_tier: Literal["free", "basic", "premium"] = Field(default="free", description="Subscription tier")
    
    @field_validator('password')
    @classmethod
    def validate_password_strength(cls, v: str) -> str:
        """Validate password strength requirements"""
        if not re.search(r'[A-Z]', v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r'[a-z]', v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r'\d', v):
            raise ValueError("Password must contain at least one digit")
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError("Password must contain at least one special character")
        return v

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(UserBase):
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)

    id: str = Field(description="User ID")
    subscription_tier: Literal["free", "basic", "premium"] = Field(description="Subscription tier")
    is_active: bool = Field(description="Whether user account is active")
    created_at: datetime = Field(description="Account creation timestamp")
    last_login_at: Optional[datetime] = Field(default=None, description="Last login timestamp")

class KDPCredentials(BaseModel):
    email: str
    password: str
    two_factor_secret: Optional[str] = None

class Token(BaseModel):
    access_token: str
    token_type: str
