## app/schemas/publication.py - Publication Schemas

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime

class PublicationCreate(BaseModel):
    """Schema for creating a new publication"""
    book_id: int = Field(description="ID of the book to publish")
    price: float = Field(default=2.99, ge=0.99, le=99.99, description="Book price")
    royalty_rate: int = Field(default=70, description="KDP royalty rate (35 or 70)")
    auto_publish: bool = Field(default=False, description="Whether to auto-publish or save as draft")
    marketing_description: Optional[str] = Field(default=None, max_length=4000, description="Marketing description")
    publication_date: Optional[datetime] = Field(default=None, description="Scheduled publication date")

class PublicationUpdate(BaseModel):
    """Schema for updating a publication"""
    price: Optional[float] = Field(default=None, ge=0.99, le=99.99, description="Book price")
    royalty_rate: Optional[int] = Field(default=None, description="KDP royalty rate (35 or 70)")
    marketing_description: Optional[str] = Field(default=None, max_length=4000, description="Marketing description")
    publication_date: Optional[datetime] = Field(default=None, description="Scheduled publication date")

class PublicationResponse(BaseModel):
    """Schema for publication response"""
    id: int
    book_id: int
    kdp_id: Optional[str] = None
    publication_url: Optional[str] = None
    price: float
    royalty_rate: int
    auto_publish: bool
    marketing_description: Optional[str] = None
    publication_date: Optional[datetime] = None
    status: str
    error_message: Optional[str] = None
    created_at: datetime
    published_at: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

class PublicationDashboard(BaseModel):
    """Schema for publication dashboard data"""
    total_publications: int
    published_count: int
    publishing_count: int
    failed_count: int
    success_rate: float
    recent_publications: list[Dict[str, Any]]