# app/schemas/security.py - Security-Enhanced Schemas

from pydantic import BaseModel, Field, validator, constr
from typing import Optional, List, Dict, Any
import re
import validators
from enum import Enum

class SecureString(constr):
    """Base secure string with common validations"""
    min_length = 1
    max_length = 1000
    regex = re.compile(r'^[^<>"\';]*$')  # No HTML/SQL injection chars

class SecureTitle(constr):
    """Secure title field"""
    min_length = 1
    max_length = 200
    regex = re.compile(r'^[a-zA-Z0-9\s\-_.,!?()&]+$')

class SecureDescription(constr):
    """Secure description field"""
    min_length = 1
    max_length = 2000
    regex = re.compile(r'^[^<>"\';]*$')

class SecureFileName(constr):
    """Secure filename field"""
    min_length = 1
    max_length = 255
    regex = re.compile(r'^[a-zA-Z0-9\-_. ]+\.[a-zA-Z0-9]+$')

class SecureURL(str):
    """Secure URL field with validation"""
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        # Basic URL validation
        if not validators.url(v):
            raise ValueError('invalid URL format')
        
        # Check for allowed schemes
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL must use http or https scheme')
        
        # Check for suspicious patterns
        suspicious_patterns = ['javascript:', 'data:', 'vbscript:', 'file:']
        if any(pattern in v.lower() for pattern in suspicious_patterns):
            raise ValueError('URL contains suspicious scheme')
        
        return v

class SecurityLevel(str, Enum):
    """Security levels for different operations"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SecureBookCreate(BaseModel):
    """Security-enhanced book creation schema"""
    title: SecureTitle = Field(..., description="Book title")
    description: SecureDescription = Field(..., description="Book description")
    category: constr(regex=r'^[a-zA-Z0-9\s\-_]+$', max_length=50) = Field(..., description="Book category")
    target_audience: constr(regex=r'^[a-zA-Z0-9\s\-_.,]+$', max_length=100) = Field(..., description="Target audience")
    writing_style: constr(regex=r'^[a-zA-Z0-9\s\-_]+$', max_length=50) = Field(..., description="Writing style")
    ai_provider: constr(regex=r'^(openai|anthropic)$') = Field(default="openai", description="AI provider")
    
    @validator('title')
    def validate_title(cls, v):
        if len(v.strip()) == 0:
            raise ValueError('Title cannot be empty')
        
        # Check for common spam/malicious patterns
        spam_patterns = ['viagra', 'casino', 'lottery', 'click here']
        if any(pattern in v.lower() for pattern in spam_patterns):
            raise ValueError('Title contains prohibited content')
        
        return v.strip()
    
    @validator('description')
    def validate_description(cls, v):
        if len(v.strip()) == 0:
            raise ValueError('Description cannot be empty')
        
        # Check for HTML/script tags
        if re.search(r'<[^>]+>', v):
            raise ValueError('Description cannot contain HTML tags')
        
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "The Complete Guide to AI",
                "description": "A comprehensive guide to artificial intelligence",
                "category": "technology",
                "target_audience": "tech professionals",
                "writing_style": "professional",
                "ai_provider": "openai"
            }
        }

class SecureManuscriptGenerationRequest(BaseModel):
    """Security-enhanced manuscript generation request"""
    title: Optional[SecureTitle] = Field(None, description="Manuscript title")
    category: constr(regex=r'^[a-zA-Z0-9\s\-_]+$', max_length=50) = Field(..., description="Content category")
    target_audience: constr(regex=r'^[a-zA-Z0-9\s\-_.,]+$', max_length=100) = Field(..., description="Target audience")
    style: constr(regex=r'^[a-zA-Z0-9\s\-_]+$', max_length=50) = Field(..., description="Writing style")
    target_length: int = Field(default=5000, ge=1000, le=50000, description="Target word count")
    ai_provider: constr(regex=r'^(openai|anthropic)$') = Field(default="openai", description="AI provider")
    output_formats: List[constr(regex=r'^(pdf|docx|epub|txt)$')] = Field(default=["pdf"], description="Output formats")
    
    # Trend data with validation
    trend_data: Dict[str, Any] = Field(default_factory=dict, description="Market trend data")
    
    @validator('target_length')
    def validate_target_length(cls, v):
        if v < 1000:
            raise ValueError('Minimum target length is 1000 words')
        if v > 50000:
            raise ValueError('Maximum target length is 50000 words')
        return v
    
    @validator('output_formats')
    def validate_output_formats(cls, v):
        if not v:
            raise ValueError('At least one output format must be specified')
        
        allowed_formats = {'pdf', 'docx', 'epub', 'txt'}
        for format_type in v:
            if format_type not in allowed_formats:
                raise ValueError(f'Invalid output format: {format_type}')
        
        return list(set(v))  # Remove duplicates
    
    @validator('trend_data')
    def validate_trend_data(cls, v):
        # Limit size of trend data to prevent DoS
        import json
        trend_data_str = json.dumps(v)
        if len(trend_data_str) > 10000:  # 10KB limit
            raise ValueError('Trend data too large')
        
        return v

class SecureFileUpload(BaseModel):
    """Security-enhanced file upload schema"""
    filename: SecureFileName = Field(..., description="Upload filename")
    content_type: constr(regex=r'^[a-zA-Z0-9\/\-+.]+$', max_length=100) = Field(..., description="File content type")
    file_size: int = Field(..., ge=1, le=10_000_000, description="File size in bytes")  # 10MB limit
    
    @validator('content_type')
    def validate_content_type(cls, v):
        # Allow only specific safe content types
        allowed_types = {
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'text/csv',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        
        if v not in allowed_types:
            raise ValueError(f'Content type not allowed: {v}')
        
        return v
    
    @validator('filename')
    def validate_filename(cls, v):
        # Check for path traversal attempts
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError('Invalid filename')
        
        # Check file extension
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.txt', '.csv', '.docx'}
        file_ext = '.' + v.split('.')[-1].lower() if '.' in v else ''
        
        if file_ext not in allowed_extensions:
            raise ValueError(f'File extension not allowed: {file_ext}')
        
        return v

class SecureUserFeedback(BaseModel):
    """Security-enhanced user feedback schema"""
    book_id: constr(regex=r'^[a-f0-9\-]{36}$') = Field(..., description="Book UUID")
    rating: int = Field(..., ge=1, le=5, description="Rating from 1 to 5")
    comment: Optional[constr(max_length=1000, regex=r'^[^<>"\';]*$')] = Field(None, description="Optional comment")
    feedback_type: constr(regex=r'^(quality|content|style|length|overall)$') = Field(..., description="Feedback type")
    
    @validator('comment')
    def validate_comment(cls, v):
        if v is None:
            return v
        
        # Check for HTML/script content
        if re.search(r'<[^>]+>', v):
            raise ValueError('Comment cannot contain HTML tags')
        
        # Check for spam patterns
        spam_patterns = ['http://', 'https://', 'www.', '.com', '.org']
        if any(pattern in v.lower() for pattern in spam_patterns):
            raise ValueError('Comment cannot contain URLs')
        
        return v.strip()

class SecureQueryParams(BaseModel):
    """Security-enhanced query parameters"""
    page: int = Field(default=1, ge=1, le=1000, description="Page number")
    limit: int = Field(default=10, ge=1, le=100, description="Items per page")
    sort_by: Optional[constr(regex=r'^[a-zA-Z_]+$', max_length=50)] = Field(None, description="Sort field")
    sort_order: Optional[constr(regex=r'^(asc|desc)$')] = Field('desc', description="Sort order")
    search: Optional[constr(max_length=100, regex=r'^[a-zA-Z0-9\s\-_.]+$')] = Field(None, description="Search term")
    
    @validator('search')
    def validate_search(cls, v):
        if v is None:
            return v
        
        # Remove extra whitespace
        v = re.sub(r'\s+', ' ', v.strip())
        
        # Check for SQL injection patterns
        sql_patterns = ['union', 'select', 'insert', 'delete', 'drop', 'update']
        for pattern in sql_patterns:
            if pattern in v.lower():
                raise ValueError('Search term contains prohibited content')
        
        return v

class SecureAPIKey(BaseModel):
    """Security-enhanced API key schema"""
    name: constr(regex=r'^[a-zA-Z0-9\s\-_]+$', max_length=50) = Field(..., description="API key name")
    permissions: List[constr(regex=r'^[a-zA-Z0-9:_]+$')] = Field(..., description="API permissions")
    expires_at: Optional[str] = Field(None, description="Expiration date (ISO format)")
    
    @validator('permissions')
    def validate_permissions(cls, v):
        # Define allowed permissions
        allowed_permissions = {
            'books:read', 'books:write', 'books:delete',
            'trends:read', 'analytics:read', 'feedback:write'
        }
        
        for permission in v:
            if permission not in allowed_permissions:
                raise ValueError(f'Invalid permission: {permission}')
        
        return v

class SecurityAuditLog(BaseModel):
    """Security audit log entry"""
    event_type: constr(regex=r'^[a-zA-Z0-9_]+$') = Field(..., description="Event type")
    user_id: Optional[constr(regex=r'^[a-f0-9\-]{36}$')] = Field(None, description="User UUID")
    ip_address: constr(regex=r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^[a-f0-9:]+$') = Field(..., description="IP address")
    user_agent: constr(max_length=500) = Field(..., description="User agent string")
    resource: constr(max_length=200) = Field(..., description="Accessed resource")
    action: constr(regex=r'^(create|read|update|delete|login|logout)$') = Field(..., description="Action performed")
    success: bool = Field(..., description="Whether action succeeded")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('user_agent')
    def validate_user_agent(cls, v):
        # Truncate very long user agent strings
        if len(v) > 500:
            v = v[:500]
        
        return v
    
    @validator('metadata')
    def validate_metadata(cls, v):
        # Limit metadata size
        import json
        metadata_str = json.dumps(v)
        if len(metadata_str) > 5000:  # 5KB limit
            raise ValueError('Metadata too large')
        
        return v

# Helper functions for validation
def validate_uuid(value: str) -> bool:
    """Validate UUID format"""
    uuid_pattern = re.compile(r'^[a-f0-9\-]{36}$')
    return bool(uuid_pattern.match(value))

def sanitize_input(value: str) -> str:
    """Sanitize user input"""
    if not isinstance(value, str):
        return value
    
    # Remove potential script tags
    value = re.sub(r'<script[^>]*>.*?</script>', '', value, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove SQL injection patterns
    sql_patterns = [
        r'union\s+select', r'drop\s+table', r'delete\s+from',
        r'insert\s+into', r'update\s+set', r'\';\s*--'
    ]
    
    for pattern in sql_patterns:
        value = re.sub(pattern, '', value, flags=re.IGNORECASE)
    
    return value.strip()

def validate_content_safety(content: str) -> bool:
    """Validate content for safety"""
    # Check for malicious patterns
    malicious_patterns = [
        r'<script', r'javascript:', r'on\w+\s*=',
        r'union\s+select', r'drop\s+table', r'\.\./',
        r'eval\s*\(', r'exec\s*\('
    ]
    
    for pattern in malicious_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            return False
    
    return True