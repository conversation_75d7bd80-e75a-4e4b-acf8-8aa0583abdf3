# app/middleware/monitoring_middleware.py - Request Monitoring Middleware

import time
import uuid
from typing import Callable, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from app.monitoring.monitoring_setup import (
    set_request_context, 
    clear_request_context, 
    get_logger,
    log_system_event,
    monitoring_manager
)

class MonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for request monitoring and context management"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Extract user info if available
        user_id = None
        if hasattr(request.state, 'user') and request.state.user:
            user_id = request.state.user.get('id')
        
        # Set monitoring context
        request_context = {
            'method': request.method,
            'url': str(request.url),
            'client_ip': request.client.host if request.client else None,
            'user_agent': request.headers.get('user-agent'),
            'request_size': request.headers.get('content-length', 0)
        }
        
        set_request_context(request_id, user_id, request_context)
        
        # Add request ID to request state for access in route handlers
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        try:
            # Log request start (handle both structured and standard loggers)
            if hasattr(self.logger, 'info') and hasattr(self.logger, 'bind'):
                # Structured logger (Logflare)
                self.logger.info(
                    "Request started",
                    request_id=request_id,
                    method=request.method,
                    path=request.url.path,
                    user_id=user_id
                )
            else:
                # Standard logger fallback
                self.logger.info(
                    f"Request started - ID: {request_id}, Method: {request.method}, Path: {request.url.path}, User: {user_id}"
                )
            
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log successful request (handle both structured and standard loggers)
            if hasattr(self.logger, 'info') and hasattr(self.logger, 'bind'):
                # Structured logger (Logflare)
                self.logger.info(
                    "Request completed",
                    request_id=request_id,
                    status_code=response.status_code,
                    duration_ms=round(duration * 1000, 2),
                    response_size=response.headers.get('content-length', 0)
                )
            else:
                # Standard logger fallback
                self.logger.info(
                    f"Request completed - ID: {request_id}, Status: {response.status_code}, Duration: {round(duration * 1000, 2)}ms"
                )
            
            # Log performance metrics
            log_system_event("request_completed", {
                'request_id': request_id,
                'method': request.method,
                'path': request.url.path,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'user_id': user_id
            })
            
            # Add request ID to response headers
            response.headers['X-Request-ID'] = request_id
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration = time.time() - start_time
            
            # Log error
            self.logger.error(
                f"Request failed - ID: {request_id}, Error: {str(e)}, Type: {type(e).__name__}, Duration: {round(duration * 1000, 2)}ms"
            )
            
            # Capture exception with context
            monitoring_manager.capture_exception(e, {
                'request_id': request_id,
                'method': request.method,
                'path': request.url.path,
                'user_id': user_id,
                'duration_ms': round(duration * 1000, 2)
            })
            
            raise
            
        finally:
            # Clear request context
            clear_request_context()

class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for detailed performance monitoring"""
    
    def __init__(self, app: ASGIApp, slow_request_threshold: float = 1.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold  # seconds
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Log slow requests (handle both structured and standard loggers)
            if duration > self.slow_request_threshold:
                if hasattr(self.logger, 'warning') and hasattr(self.logger, 'bind'):
                    # Structured logger (Logflare)
                    self.logger.warning(
                        "Slow request detected",
                        method=request.method,
                        path=request.url.path,
                        duration_ms=round(duration * 1000, 2),
                        threshold_ms=round(self.slow_request_threshold * 1000, 2)
                    )
                else:
                    # Standard logger fallback
                    self.logger.warning(
                        f"Slow request detected - Method: {request.method}, Path: {request.url.path}, Duration: {round(duration * 1000, 2)}ms (threshold: {round(self.slow_request_threshold * 1000, 2)}ms)"
                    )
                
                # Log as system event
                log_system_event("slow_request", {
                    'method': request.method,
                    'path': request.url.path,
                    'duration_ms': round(duration * 1000, 2),
                    'threshold_ms': round(self.slow_request_threshold * 1000, 2)
                })
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Log performance data for failed requests (handle both structured and standard loggers)
            if hasattr(self.logger, 'error') and hasattr(self.logger, 'bind'):
                # Structured logger (Logflare)
                self.logger.error(
                    "Request failed with performance data",
                    method=request.method,
                    path=request.url.path,
                    duration_ms=round(duration * 1000, 2),
                    error=str(e)
                )
            else:
                # Standard logger fallback
                self.logger.error(
                    f"Request failed with performance data - Method: {request.method}, Path: {request.url.path}, Duration: {round(duration * 1000, 2)}ms, Error: {str(e)}"
                )
            
            raise

class SecurityMonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for security-related monitoring"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.logger = get_logger(__name__)
        self.suspicious_patterns = [
            'SELECT', 'INSERT', 'DELETE', 'DROP', 'UNION',  # SQL injection
            '<script', 'javascript:', 'onerror=',  # XSS
            '../', '..\\',  # Path traversal
            'eval(', 'exec(',  # Code injection
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check for suspicious patterns in URL
        url_str = str(request.url).lower()
        for pattern in self.suspicious_patterns:
            if pattern.lower() in url_str:
                if hasattr(self.logger, 'warning') and hasattr(self.logger, 'bind'):
                    # Structured logger (Logflare)
                    self.logger.warning(
                        "Suspicious request pattern detected",
                        pattern=pattern,
                        url=str(request.url),
                        client_ip=request.client.host if request.client else None,
                        user_agent=request.headers.get('user-agent')
                    )
                else:
                    # Standard logger fallback
                    self.logger.warning(
                        f"Suspicious request pattern detected - Pattern: {pattern}, URL: {str(request.url)}, IP: {request.client.host if request.client else None}"
                    )
                
                log_system_event("suspicious_request", {
                    'pattern': pattern,
                    'url': str(request.url),
                    'client_ip': request.client.host if request.client else None,
                    'user_agent': request.headers.get('user-agent')
                })
                break
        
        # Monitor for unusual request sizes
        content_length = request.headers.get('content-length')
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
            if hasattr(self.logger, 'warning') and hasattr(self.logger, 'bind'):
                # Structured logger (Logflare)
                self.logger.warning(
                    "Large request detected",
                    content_length=content_length,
                    path=request.url.path,
                    client_ip=request.client.host if request.client else None
                )
            else:
                # Standard logger fallback
                self.logger.warning(
                    f"Large request detected - Size: {content_length}, Path: {request.url.path}, IP: {request.client.host if request.client else None}"
                )
        
        response = await call_next(request)
        
        # Monitor for authentication failures
        if response.status_code == 401:
            if hasattr(self.logger, 'warning') and hasattr(self.logger, 'bind'):
                # Structured logger (Logflare)
                self.logger.warning(
                    "Authentication failure",
                    path=request.url.path,
                    client_ip=request.client.host if request.client else None,
                    user_agent=request.headers.get('user-agent')
                )
            else:
                # Standard logger fallback
                self.logger.warning(
                    f"Authentication failure - Path: {request.url.path}, IP: {request.client.host if request.client else None}"
                )
            
            log_system_event("auth_failure", {
                'path': request.url.path,
                'client_ip': request.client.host if request.client else None,
                'user_agent': request.headers.get('user-agent')
            })
        
        return response