"""
Cache middleware for API responses with intelligent caching strategies.

This middleware provides:
- Automatic response caching based on request patterns
- Cache-aware response headers
- Conditional requests support (ETag, If-Modified-Since)
- Cache warming for frequently accessed endpoints
- Integration with Redis cache and invalidation system
"""

import json
import hashlib
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import asyncio

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.cache.redis_cache import get_cache, RedisCache
from app.cache.cache_invalidation import get_invalidation_manager

logger = logging.getLogger(__name__)


class CacheRule:
    """Defines caching rules for specific endpoints."""
    
    def __init__(
        self,
        path_pattern: str,
        ttl: int = 3600,
        methods: List[str] = None,
        vary_headers: List[str] = None,
        cache_key_func: Optional[Callable] = None,
        should_cache_func: Optional[Callable] = None,
        cache_tags: List[str] = None
    ):
        self.path_pattern = path_pattern
        self.ttl = ttl
        self.methods = methods or ["GET"]
        self.vary_headers = vary_headers or []
        self.cache_key_func = cache_key_func
        self.should_cache_func = should_cache_func
        self.cache_tags = cache_tags or []


class APICacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware for intelligent API response caching.
    
    Features:
    - Rule-based caching with flexible patterns
    - Conditional request support (304 Not Modified)
    - Cache-aware response headers
    - Integration with Redis and invalidation system
    - Performance monitoring and statistics
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.cache: Optional[RedisCache] = None
        self.invalidation_manager = None
        self.cache_rules: List[CacheRule] = []
        self.stats = {
            "hits": 0,
            "misses": 0,
            "errors": 0,
            "bypassed": 0
        }
        self._setup_default_rules()
    
    async def dispatch(self, request: Request, call_next):
        """Process request with caching logic."""
        # Initialize cache if needed
        if not self.cache:
            await self._initialize_cache()
        
        # Check if request should be cached
        cache_rule = self._find_matching_rule(request)
        if not cache_rule or request.method not in cache_rule.methods:
            self.stats["bypassed"] += 1
            return await call_next(request)
        
        # Generate cache key
        cache_key = await self._generate_cache_key(request, cache_rule)
        
        # Try to serve from cache
        cached_response = await self._get_cached_response(request, cache_key)
        if cached_response:
            self.stats["hits"] += 1
            return cached_response
        
        # Execute request and cache response
        response = await call_next(request)
        
        # Cache response if appropriate
        if await self._should_cache_response(request, response, cache_rule):
            await self._cache_response(cache_key, response, cache_rule)
        
        self.stats["misses"] += 1
        return response
    
    async def _initialize_cache(self):
        """Initialize cache and invalidation manager."""
        try:
            self.cache = await get_cache()
            self.invalidation_manager = await get_invalidation_manager()
        except Exception as e:
            logger.error(f"Failed to initialize cache middleware: {e}")
    
    def _setup_default_rules(self):
        """Set up default caching rules for common endpoints."""
        
        # Analytics endpoints - cache for 5 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/analytics/*",
            ttl=300,
            vary_headers=["Authorization"],
            cache_tags=["analytics"]
        ))
        
        # Trend analysis - cache for 15 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/trends/*",
            ttl=900,
            cache_tags=["trends", "market_data"]
        ))
        
        # Book data - cache for 10 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/books/*/analytics",
            ttl=600,
            vary_headers=["Authorization"],
            cache_tags=["books", "analytics"]
        ))
        
        # User data - cache for 5 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/users/*/analytics",
            ttl=300,
            vary_headers=["Authorization"],
            cache_tags=["users", "analytics"]
        ))
        
        # Publication data - cache for 30 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/publications/*",
            ttl=1800,
            vary_headers=["Authorization"],
            cache_tags=["publications"]
        ))
        
        # Health checks - cache for 1 minute
        self.add_rule(CacheRule(
            path_pattern="/health",
            ttl=60,
            cache_tags=["health"]
        ))
        
        # Monitoring endpoints - cache for 2 minutes
        self.add_rule(CacheRule(
            path_pattern="/api/monitoring/*",
            ttl=120,
            vary_headers=["Authorization"],
            cache_tags=["monitoring"]
        ))
    
    def add_rule(self, rule: CacheRule):
        """Add a caching rule."""
        self.cache_rules.append(rule)
        logger.info(f"Added cache rule for pattern: {rule.path_pattern}")
    
    def remove_rule(self, path_pattern: str):
        """Remove caching rules for a pattern."""
        self.cache_rules = [rule for rule in self.cache_rules if rule.path_pattern != path_pattern]
        logger.info(f"Removed cache rules for pattern: {path_pattern}")
    
    def _find_matching_rule(self, request: Request) -> Optional[CacheRule]:
        """Find the first matching cache rule for the request."""
        path = request.url.path
        
        for rule in self.cache_rules:
            if self._path_matches_pattern(path, rule.path_pattern):
                return rule
        
        return None
    
    def _path_matches_pattern(self, path: str, pattern: str) -> bool:
        """Check if path matches pattern with wildcards."""
        if "*" not in pattern:
            return path == pattern
        
        # Convert pattern to regex-like matching
        pattern_parts = pattern.split("*")
        path_pos = 0
        
        for i, part in enumerate(pattern_parts):
            if i == 0:  # First part
                if not path.startswith(part):
                    return False
                path_pos += len(part)
            elif i == len(pattern_parts) - 1:  # Last part
                if not path[path_pos:].endswith(part):
                    return False
            else:  # Middle parts
                pos = path.find(part, path_pos)
                if pos == -1:
                    return False
                path_pos = pos + len(part)
        
        return True
    
    async def _generate_cache_key(self, request: Request, rule: CacheRule) -> str:
        """Generate cache key for the request."""
        if rule.cache_key_func:
            return await rule.cache_key_func(request)
        
        # Default cache key generation
        key_parts = [
            request.method,
            request.url.path,
            str(sorted(request.query_params.items()))
        ]
        
        # Include vary headers in cache key
        for header in rule.vary_headers:
            header_value = request.headers.get(header, "")
            if header == "Authorization":
                # Hash auth header for privacy
                header_value = hashlib.md5(header_value.encode()).hexdigest()[:8]
            key_parts.append(f"{header}:{header_value}")
        
        cache_key = ":".join(key_parts)
        
        # Hash long keys
        if len(cache_key) > 200:
            cache_key = hashlib.md5(cache_key.encode()).hexdigest()
        
        return f"api_response:{cache_key}"
    
    async def _get_cached_response(self, request: Request, cache_key: str) -> Optional[Response]:
        """Try to get response from cache."""
        if not self.cache:
            return None
        
        try:
            cached_data = await self.cache.get(cache_key, "api_responses")
            if not cached_data:
                return None
            
            # Check conditional request headers
            if await self._handle_conditional_request(request, cached_data):
                return Response(status_code=304)
            
            # Reconstruct response
            response = JSONResponse(
                content=cached_data["content"],
                status_code=cached_data["status_code"],
                headers=cached_data["headers"]
            )
            
            # Add cache headers
            response.headers["X-Cache"] = "HIT"
            response.headers["X-Cache-Key"] = cache_key
            
            return response
            
        except Exception as e:
            logger.error(f"Error retrieving cached response: {e}")
            self.stats["errors"] += 1
            return None
    
    async def _handle_conditional_request(self, request: Request, cached_data: Dict) -> bool:
        """Handle conditional request headers (If-None-Match, If-Modified-Since)."""
        # Check ETag
        if_none_match = request.headers.get("if-none-match")
        if if_none_match and cached_data.get("etag") == if_none_match:
            return True
        
        # Check Last-Modified
        if_modified_since = request.headers.get("if-modified-since")
        if if_modified_since and cached_data.get("last_modified"):
            try:
                client_time = datetime.strptime(if_modified_since, '%a, %d %b %Y %H:%M:%S GMT')
                cached_time = datetime.fromisoformat(cached_data["last_modified"])
                if cached_time <= client_time:
                    return True
            except ValueError:
                pass
        
        return False
    
    async def _should_cache_response(
        self, 
        request: Request, 
        response: Response, 
        rule: CacheRule
    ) -> bool:
        """Determine if response should be cached."""
        # Only cache successful responses
        if response.status_code >= 400:
            return False
        
        # Check custom should_cache function
        if rule.should_cache_func:
            return await rule.should_cache_func(request, response)
        
        # Default: cache GET responses
        return request.method == "GET"
    
    async def _cache_response(self, cache_key: str, response: Response, rule: CacheRule):
        """Cache the response."""
        if not self.cache:
            return
        
        try:
            # Extract response data
            content = None
            if hasattr(response, 'body'):
                # For responses with body attribute
                content = response.body
                if isinstance(content, bytes):
                    content = content.decode('utf-8')
                try:
                    content = json.loads(content)
                except json.JSONDecodeError:
                    pass
            elif hasattr(response, '_content'):
                # For JSONResponse and similar
                content = response._content
            
            # Generate ETag
            etag = hashlib.md5(json.dumps(content, default=str).encode()).hexdigest()
            
            # Prepare cache data
            cache_data = {
                "content": content,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "etag": f'"{etag}"',
                "last_modified": datetime.utcnow().isoformat(),
                "cached_at": datetime.utcnow().isoformat(),
                "tags": rule.cache_tags
            }
            
            # Add cache headers to original response
            response.headers["ETag"] = cache_data["etag"]
            response.headers["Last-Modified"] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            response.headers["Cache-Control"] = f"public, max-age={rule.ttl}"
            response.headers["X-Cache"] = "MISS"
            response.headers["X-Cache-Key"] = cache_key
            
            # Store in cache
            await self.cache.set(cache_key, cache_data, rule.ttl, "api_responses")
            
            # Register with invalidation system
            if self.invalidation_manager and rule.cache_tags:
                # This would integrate with the invalidation system
                pass
            
        except Exception as e:
            logger.error(f"Error caching response: {e}")
            self.stats["errors"] += 1
    
    async def invalidate_cache_by_tags(self, tags: List[str]):
        """Invalidate cache entries by tags."""
        if not self.cache:
            return
        
        try:
            # This is a simplified implementation
            # In production, you'd want a more sophisticated tag-based invalidation
            pattern = "api_responses:*"
            await self.cache.delete_pattern(pattern)
            logger.info(f"Invalidated cache entries with tags: {tags}")
            
        except Exception as e:
            logger.error(f"Error invalidating cache by tags: {e}")
    
    async def invalidate_cache_by_pattern(self, pattern: str):
        """Invalidate cache entries by pattern."""
        if not self.cache:
            return
        
        try:
            full_pattern = f"api_responses:*{pattern}*"
            count = await self.cache.delete_pattern(full_pattern)
            logger.info(f"Invalidated {count} cache entries matching pattern: {pattern}")
            return count
            
        except Exception as e:
            logger.error(f"Error invalidating cache by pattern: {e}")
            return 0
    
    async def warm_cache(self, endpoints: List[str]):
        """Warm cache for specific endpoints."""
        if not self.cache:
            return
        
        # This would be implemented to pre-populate cache
        # for frequently accessed endpoints
        logger.info(f"Cache warming initiated for {len(endpoints)} endpoints")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache middleware statistics."""
        cache_stats = {}
        if self.cache:
            cache_stats = (await self.cache.get_stats()).dict()
        
        return {
            "middleware_stats": self.stats,
            "cache_stats": cache_stats,
            "active_rules": len(self.cache_rules),
            "rules": [
                {
                    "pattern": rule.path_pattern,
                    "ttl": rule.ttl,
                    "methods": rule.methods,
                    "tags": rule.cache_tags
                }
                for rule in self.cache_rules
            ]
        }


# Custom cache key generators
async def user_specific_cache_key(request: Request) -> str:
    """Generate cache key that includes user ID."""
    user_id = request.path_params.get("user_id", "anonymous")
    path = request.url.path
    query = str(sorted(request.query_params.items()))
    
    return f"api_response:user:{user_id}:{path}:{query}"


async def book_specific_cache_key(request: Request) -> str:
    """Generate cache key that includes book ID."""
    book_id = request.path_params.get("book_id", "all")
    path = request.url.path
    query = str(sorted(request.query_params.items()))
    
    return f"api_response:book:{book_id}:{path}:{query}"


# Cache warming functions
async def warm_analytics_cache():
    """Warm cache for analytics endpoints."""
    # This would be implemented to pre-populate analytics data
    pass


async def warm_trends_cache():
    """Warm cache for trend analysis endpoints."""
    # This would be implemented to pre-populate trend data
    pass