# app/middleware/security_middleware.py - Production Security Middleware

import time
import uuid
import asyncio
from typing import Callable, Dict, Any, List, Optional
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from circuitbreaker import circuit
import validators
import re
import logging
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, log_system_event

logger = get_logger(__name__)

# Rate limiter configuration
limiter = Limiter(key_func=get_remote_address)

class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware for production readiness"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.logger = get_logger(__name__)
        
        # Security patterns to detect
        self.sql_injection_patterns = [
            r'union\s+select', r'drop\s+table', r'delete\s+from', r'insert\s+into',
            r'update\s+set', r'exec\s*\(', r'xp_cmdshell', r'sp_executesql',
            r'\';\s*--', r'\';\s*#', r'\'\s*or\s+1=1', r'\'\s*or\s+\'a\'=\'a\''
        ]
        
        self.xss_patterns = [
            r'<script[^>]*>', r'javascript:', r'on\w+\s*=', r'vbscript:',
            r'<iframe[^>]*>', r'<object[^>]*>', r'<embed[^>]*>',
            r'eval\s*\(', r'document\.cookie', r'window\.location'
        ]
        
        self.path_traversal_patterns = [
            r'\.\./', r'\.\.\\', r'%2e%2e%2f', r'%2e%2e%5c',
            r'%252e%252e%252f', r'/etc/passwd', r'\/\.\.\/'
        ]
        
        # Compile patterns for performance
        self.compiled_patterns = {
            'sql_injection': [re.compile(pattern, re.IGNORECASE) for pattern in self.sql_injection_patterns],
            'xss': [re.compile(pattern, re.IGNORECASE) for pattern in self.xss_patterns],
            'path_traversal': [re.compile(pattern, re.IGNORECASE) for pattern in self.path_traversal_patterns]
        }
        
        # Blocked user agents
        self.blocked_user_agents = [
            'curl', 'wget', 'httpie', 'scanner', 'bot', 'crawler',
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap'
        ]
        
        # Rate limiting tracking
        self.request_counts: Dict[str, List[float]] = {}
        self.blocked_ips: Dict[str, float] = {}
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Check if IP is blocked
        if self._is_ip_blocked(client_ip):
            self.logger.warning(f"Blocked request from IP: {client_ip}")
            raise HTTPException(status_code=429, detail="IP temporarily blocked")
        
        # Security checks
        await self._perform_security_checks(request, client_ip)
        
        # Request size validation
        self._validate_request_size(request)
        
        # Rate limiting check
        self._check_rate_limits(request, client_ip)
        
        try:
            response = await call_next(request)
            
            # Security headers
            response = self._add_security_headers(response, request)
            
            return response
            
        except Exception as e:
            # Log security incidents
            self.logger.error(f"Security middleware error: {e}", extra={
                'client_ip': client_ip,
                'path': request.url.path,
                'method': request.method
            })
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP with proxy header support"""
        # Check for forwarded headers (behind proxy/load balancer)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fall back to direct connection
        return request.client.host if request.client else "unknown"
    
    def _is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is temporarily blocked"""
        if ip in self.blocked_ips:
            if time.time() - self.blocked_ips[ip] < 3600:  # 1 hour block
                return True
            else:
                del self.blocked_ips[ip]
        return False
    
    async def _perform_security_checks(self, request: Request, client_ip: str) -> None:
        """Perform comprehensive security pattern detection"""
        
        # Get request data
        url_str = str(request.url).lower()
        user_agent = request.headers.get('user-agent', '').lower()
        path = request.url.path.lower()
        
        # Documentation paths that should be accessible for development
        docs_paths = ['/docs', '/redoc', '/openapi.json']
        is_docs_path = any(path.startswith(doc_path) for doc_path in docs_paths)
        
        # Check user agent (but allow access to docs for development)
        if not is_docs_path and any(blocked_agent in user_agent for blocked_agent in self.blocked_user_agents):
            self._log_security_event("blocked_user_agent", {
                'client_ip': client_ip,
                'user_agent': user_agent,
                'url': url_str
            })
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Check for malicious patterns in URL
        for attack_type, patterns in self.compiled_patterns.items():
            for pattern in patterns:
                if pattern.search(url_str):
                    self._log_security_event(f"{attack_type}_attempt", {
                        'client_ip': client_ip,
                        'pattern': pattern.pattern,
                        'url': url_str
                    })
                    self._block_ip(client_ip)
                    raise HTTPException(status_code=403, detail="Malicious request detected")
        
        # Check request body for POST requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                body = await request.body()
                if body:
                    body_str = body.decode('utf-8', errors='ignore').lower()
                    
                    for attack_type, patterns in self.compiled_patterns.items():
                        for pattern in patterns:
                            if pattern.search(body_str):
                                self._log_security_event(f"{attack_type}_in_body", {
                                    'client_ip': client_ip,
                                    'pattern': pattern.pattern,
                                    'path': request.url.path
                                })
                                self._block_ip(client_ip)
                                raise HTTPException(status_code=403, detail="Malicious payload detected")
            except Exception as e:
                self.logger.warning(f"Error checking request body: {e}")
    
    def _validate_request_size(self, request: Request) -> None:
        """Validate request size to prevent DoS attacks"""
        content_length = request.headers.get('content-length')
        if content_length:
            size = int(content_length)
            max_size = 50 * 1024 * 1024  # 50MB limit
            
            if size > max_size:
                self.logger.warning(f"Large request detected: {size} bytes")
                raise HTTPException(status_code=413, detail="Request too large")
    
    def _check_rate_limits(self, request: Request, client_ip: str) -> None:
        """Implement rate limiting per IP"""
        current_time = time.time()
        window_size = 60  # 1 minute window
        max_requests = 100  # Max requests per minute
        
        # Clean old entries
        if client_ip in self.request_counts:
            self.request_counts[client_ip] = [
                req_time for req_time in self.request_counts[client_ip]
                if current_time - req_time < window_size
            ]
        else:
            self.request_counts[client_ip] = []
        
        # Add current request
        self.request_counts[client_ip].append(current_time)
        
        # Check limit
        if len(self.request_counts[client_ip]) > max_requests:
            self._log_security_event("rate_limit_exceeded", {
                'client_ip': client_ip,
                'request_count': len(self.request_counts[client_ip]),
                'path': request.url.path
            })
            self._block_ip(client_ip)
            raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    def _block_ip(self, ip: str) -> None:
        """Block IP temporarily"""
        self.blocked_ips[ip] = time.time()
        self.logger.warning(f"IP blocked due to suspicious activity: {ip}")
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Log security events"""
        log_system_event(f"security_{event_type}", details)
        
        # Also log to standard logger for immediate visibility
        self.logger.warning(f"Security event: {event_type}", extra=details)
    
    def _add_security_headers(self, response: Response, request: Request = None) -> Response:
        """Add security headers to response"""
        # Check if this is a documentation request
        is_docs = False
        if request:
            path = request.url.path.lower()
            is_docs = any(path.startswith(doc_path) for doc_path in ['/docs', '/redoc', '/openapi.json'])
        
        # More permissive CSP for docs, strict for everything else
        if is_docs:
            csp = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net 'unsafe-eval' 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; img-src 'self' https://fastapi.tiangolo.com data:; connect-src 'self'; font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com;"
            x_frame = 'SAMEORIGIN'  # Allow embedding in same origin for docs
        else:
            csp = "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self'; connect-src 'self';"
            x_frame = 'DENY'
        
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': x_frame,
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': csp,
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response

class InputValidationMiddleware(BaseHTTPMiddleware):
    """Advanced input validation middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Validate common inputs
        if request.method in ['POST', 'PUT', 'PATCH']:
            await self._validate_request_data(request)
        
        # Validate query parameters
        self._validate_query_params(request)
        
        return await call_next(request)
    
    async def _validate_request_data(self, request: Request) -> None:
        """Validate request body data"""
        try:
            content_type = request.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                body = await request.body()
                if body:
                    # Basic JSON validation happens at FastAPI level
                    # Additional custom validation can be added here
                    pass
            
            elif 'multipart/form-data' in content_type:
                # File upload validation
                self._validate_file_uploads(request)
                
        except Exception as e:
            self.logger.error(f"Request validation error: {e}")
            raise HTTPException(status_code=400, detail="Invalid request format")
    
    def _validate_query_params(self, request: Request) -> None:
        """Validate query parameters"""
        for param, value in request.query_params.items():
            # Check parameter length
            if len(param) > 100 or len(str(value)) > 1000:
                raise HTTPException(status_code=400, detail="Parameter too long")
            
            # Check for suspicious patterns
            if any(char in str(value) for char in ['<', '>', '"', "'", ';']):
                self.logger.warning(f"Suspicious query parameter: {param}={value}")
                raise HTTPException(status_code=400, detail="Invalid parameter format")
    
    def _validate_file_uploads(self, request: Request) -> None:
        """Validate file uploads"""
        # This is a placeholder for file upload validation
        # Actual implementation would check file types, sizes, content, etc.
        content_length = request.headers.get('content-length')
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=413, detail="File too large")

class CORSSecurityMiddleware(BaseHTTPMiddleware):
    """Production-ready CORS middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.allowed_origins = self._get_allowed_origins()
        self.allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
        self.allowed_headers = ['*']
        self.max_age = 3600
    
    def _get_allowed_origins(self) -> List[str]:
        """Get allowed origins based on environment"""
        if settings.sentry_environment == 'production':
            # Production origins - must be explicit
            return [
                'https://yourdomain.com',
                'https://app.yourdomain.com',
                'https://admin.yourdomain.com'
            ]
        elif settings.sentry_environment == 'staging':
            # Staging origins
            return [
                'https://staging.yourdomain.com',
                'https://preview.yourdomain.com'
            ]
        else:
            # Development origins
            return [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://127.0.0.1:3000'
            ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        origin = request.headers.get('origin')
        
        if request.method == 'OPTIONS':
            # Handle preflight requests
            return self._handle_preflight(origin)
        
        response = await call_next(request)
        
        # Add CORS headers
        if origin and self._is_origin_allowed(origin):
            response.headers['Access-Control-Allow-Origin'] = origin
            response.headers['Access-Control-Allow-Credentials'] = 'true'
            response.headers['Access-Control-Expose-Headers'] = 'X-Request-ID'
        
        return response
    
    def _is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed"""
        return origin in self.allowed_origins
    
    def _handle_preflight(self, origin: str) -> Response:
        """Handle CORS preflight requests"""
        response = Response()
        
        if origin and self._is_origin_allowed(origin):
            response.headers['Access-Control-Allow-Origin'] = origin
            response.headers['Access-Control-Allow-Methods'] = ', '.join(self.allowed_methods)
            response.headers['Access-Control-Allow-Headers'] = ', '.join(self.allowed_headers)
            response.headers['Access-Control-Allow-Credentials'] = 'true'
            response.headers['Access-Control-Max-Age'] = str(self.max_age)
        
        return response

# Circuit breaker for external services
@circuit(failure_threshold=5, recovery_timeout=30, expected_exception=Exception)
async def call_external_service(url: str, **kwargs) -> Any:
    """Circuit breaker wrapper for external service calls"""
    import httpx
    
    timeout = httpx.Timeout(30.0)  # 30 second timeout
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(url, **kwargs)
        response.raise_for_status()
        return response.json()

# Rate limiting decorators for specific endpoints
def rate_limit_ai_operations():
    """Rate limit for AI-intensive operations"""
    return limiter.limit("5/minute")

def rate_limit_file_uploads():
    """Rate limit for file upload operations"""
    return limiter.limit("10/minute")

def rate_limit_user_actions():
    """Rate limit for general user actions"""
    return limiter.limit("60/minute")

# Helper function to get limiter instance
def get_limiter() -> Limiter:
    """Get the configured rate limiter"""
    return limiter