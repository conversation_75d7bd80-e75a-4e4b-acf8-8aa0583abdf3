# app/middleware/business_metrics.py - Business Metrics Tracking Middleware

import time
import json
import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON>GIApp
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_metrics, monitor_operation

logger = get_logger(__name__)

class MetricType(Enum):
    """Types of business metrics"""
    REQUEST = "request"
    BUSINESS_OPERATION = "business_operation"
    USER_ACTION = "user_action"
    REVENUE = "revenue"
    ENGAGEMENT = "engagement"
    PERFORMANCE = "performance"
    ERROR = "error"

class MetricEvent(Enum):
    """Business metric events"""
    # User events
    USER_REGISTRATION = "user_registration"
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    
    # Book creation events
    BOOK_CREATION_STARTED = "book_creation_started"
    BOOK_CREATION_COMPLETED = "book_creation_completed"
    BOOK_CREATION_FAILED = "book_creation_failed"
    
    # Publishing events
    BOOK_PUBLISHED = "book_published"
    BOOK_UNPUBLISHED = "book_unpublished"
    PUBLISHING_FAILED = "publishing_failed"
    
    # Sales events
    BOOK_SALE = "book_sale"
    ROYALTY_EARNED = "royalty_earned"
    REFUND_PROCESSED = "refund_processed"
    
    # AI usage events
    AI_GENERATION_REQUEST = "ai_generation_request"
    AI_GENERATION_SUCCESS = "ai_generation_success"
    AI_GENERATION_FAILURE = "ai_generation_failure"
    
    # Trend analysis events
    TREND_ANALYSIS_COMPLETED = "trend_analysis_completed"
    MARKET_OPPORTUNITY_IDENTIFIED = "market_opportunity_identified"
    
    # System events
    API_REQUEST = "api_request"
    API_ERROR = "api_error"
    SYSTEM_ERROR = "system_error"

@dataclass
class BusinessMetric:
    """Business metric data structure"""
    event: MetricEvent
    metric_type: MetricType
    value: float
    unit: str
    timestamp: float
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[Dict[str, str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'event': self.event.value,
            'metric_type': self.metric_type.value,
            'value': self.value,
            'unit': self.unit,
            'timestamp': self.timestamp,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'metadata': self.metadata or {},
            'tags': self.tags or {}
        }

class MetricsCollector:
    """Business metrics collector and aggregator"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._metrics_buffer: List[BusinessMetric] = []
        self._buffer_lock = asyncio.Lock()
        self._flush_interval = 60  # Flush every minute
        self._max_buffer_size = 1000
        self._last_flush = time.time()
        
        # Metric aggregations
        self._hourly_aggregates: Dict[str, Dict[str, float]] = {}
        self._daily_aggregates: Dict[str, Dict[str, float]] = {}
        
        # Performance tracking
        self._request_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'endpoints': {}
        }
    
    async def record_metric(self, metric: BusinessMetric) -> None:
        """Record a business metric"""
        async with self._buffer_lock:
            self._metrics_buffer.append(metric)
            
            # Flush if buffer is full
            if len(self._metrics_buffer) >= self._max_buffer_size:
                await self._flush_metrics()
    
    async def record_request_metric(self, 
                                  request: Request, 
                                  response: Response, 
                                  duration_ms: float) -> None:
        """Record API request metrics"""
        
        # Determine success/failure
        is_success = 200 <= response.status_code < 400
        
        # Extract user info if available
        user_id = getattr(request.state, 'user_id', None)
        session_id = getattr(request.state, 'session_id', None)
        
        # Create request metric
        metric = BusinessMetric(
            event=MetricEvent.API_REQUEST if is_success else MetricEvent.API_ERROR,
            metric_type=MetricType.REQUEST,
            value=duration_ms,
            unit="milliseconds",
            timestamp=time.time(),
            user_id=user_id,
            session_id=session_id,
            metadata={
                'method': request.method,
                'endpoint': str(request.url.path),
                'status_code': response.status_code,
                'user_agent': request.headers.get('user-agent', 'unknown'),
                'ip_address': request.client.host if request.client else 'unknown'
            },
            tags={
                'method': request.method,
                'endpoint_group': self._categorize_endpoint(request.url.path),
                'status_group': f"{response.status_code // 100}xx"
            }
        )
        
        await self.record_metric(metric)
        
        # Update real-time request metrics
        self._update_request_metrics(request.url.path, duration_ms, is_success)
    
    def _categorize_endpoint(self, path: str) -> str:
        """Categorize endpoint for grouping"""
        if path.startswith('/api/books'):
            return 'books'
        elif path.startswith('/api/publications'):
            return 'publications'
        elif path.startswith('/api/trends'):
            return 'trends'
        elif path.startswith('/api/agents'):
            return 'agents'
        elif path.startswith('/api/auth'):
            return 'auth'
        elif path.startswith('/api/analytics'):
            return 'analytics'
        elif path.startswith('/api/feedback'):
            return 'feedback'
        elif path.startswith('/api/monitoring'):
            return 'monitoring'
        elif path.startswith('/docs') or path.startswith('/openapi'):
            return 'docs'
        elif path.startswith('/health'):
            return 'health'
        else:
            return 'other'
    
    def _update_request_metrics(self, endpoint: str, duration_ms: float, is_success: bool) -> None:
        """Update real-time request metrics"""
        self._request_metrics['total_requests'] += 1
        
        if is_success:
            self._request_metrics['successful_requests'] += 1
        else:
            self._request_metrics['failed_requests'] += 1
        
        # Update average response time
        total_requests = self._request_metrics['total_requests']
        current_avg = self._request_metrics['avg_response_time']
        self._request_metrics['avg_response_time'] = (
            (current_avg * (total_requests - 1) + duration_ms) / total_requests
        )
        
        # Update endpoint-specific metrics
        endpoint_group = self._categorize_endpoint(endpoint)
        if endpoint_group not in self._request_metrics['endpoints']:
            self._request_metrics['endpoints'][endpoint_group] = {
                'count': 0,
                'avg_duration': 0.0,
                'success_count': 0,
                'error_count': 0
            }
        
        endpoint_stats = self._request_metrics['endpoints'][endpoint_group]
        endpoint_stats['count'] += 1
        
        if is_success:
            endpoint_stats['success_count'] += 1
        else:
            endpoint_stats['error_count'] += 1
        
        # Update endpoint average duration
        endpoint_count = endpoint_stats['count']
        current_endpoint_avg = endpoint_stats['avg_duration']
        endpoint_stats['avg_duration'] = (
            (current_endpoint_avg * (endpoint_count - 1) + duration_ms) / endpoint_count
        )
    
    async def record_business_event(self,
                                  event: MetricEvent,
                                  value: float = 1.0,
                                  unit: str = "count",
                                  user_id: Optional[str] = None,
                                  metadata: Optional[Dict[str, Any]] = None,
                                  tags: Optional[Dict[str, str]] = None) -> None:
        """Record a business event metric"""
        
        metric_type = self._determine_metric_type(event)
        
        metric = BusinessMetric(
            event=event,
            metric_type=metric_type,
            value=value,
            unit=unit,
            timestamp=time.time(),
            user_id=user_id,
            metadata=metadata,
            tags=tags
        )
        
        await self.record_metric(metric)
    
    def _determine_metric_type(self, event: MetricEvent) -> MetricType:
        """Determine metric type based on event"""
        if event in [MetricEvent.USER_REGISTRATION, MetricEvent.USER_LOGIN, MetricEvent.USER_LOGOUT]:
            return MetricType.USER_ACTION
        elif event in [MetricEvent.BOOK_SALE, MetricEvent.ROYALTY_EARNED, MetricEvent.REFUND_PROCESSED]:
            return MetricType.REVENUE
        elif event in [MetricEvent.BOOK_CREATION_STARTED, MetricEvent.BOOK_PUBLISHED]:
            return MetricType.ENGAGEMENT
        elif event in [MetricEvent.AI_GENERATION_REQUEST, MetricEvent.TREND_ANALYSIS_COMPLETED]:
            return MetricType.BUSINESS_OPERATION
        elif event in [MetricEvent.API_ERROR, MetricEvent.SYSTEM_ERROR]:
            return MetricType.ERROR
        else:
            return MetricType.BUSINESS_OPERATION
    
    async def _flush_metrics(self) -> None:
        """Flush metrics to storage/monitoring system"""
        if not self._metrics_buffer:
            return
        
        try:
            # Copy buffer and clear it
            metrics_to_flush = self._metrics_buffer.copy()
            self._metrics_buffer.clear()
            self._last_flush = time.time()
            
            # Send to monitoring system
            for metric in metrics_to_flush:
                try:
                    # Send to external monitoring (Sentry, Logflare, etc.)
                    capture_metrics(
                        metric_name=f"business.{metric.event.value}",
                        value=metric.value,
                        tags=metric.tags or {},
                        timestamp=metric.timestamp
                    )
                    
                    # Log important business events
                    if metric.metric_type in [MetricType.REVENUE, MetricType.BUSINESS_OPERATION]:
                        self.logger.info(
                            f"Business event: {metric.event.value} = {metric.value} {metric.unit}",
                            extra={
                                'metric_data': metric.to_dict(),
                                'user_id': metric.user_id
                            }
                        )
                
                except Exception as e:
                    self.logger.error(f"Failed to send metric {metric.event.value}: {e}")
            
            # Update aggregates
            await self._update_aggregates(metrics_to_flush)
            
            self.logger.debug(f"Flushed {len(metrics_to_flush)} metrics to monitoring system")
            
        except Exception as e:
            self.logger.error(f"Failed to flush metrics: {e}")
            # Restore metrics to buffer if flush failed
            async with self._buffer_lock:
                self._metrics_buffer.extend(metrics_to_flush)
    
    async def _update_aggregates(self, metrics: List[BusinessMetric]) -> None:
        """Update hourly and daily aggregates"""
        now = datetime.now()
        hour_key = now.strftime("%Y-%m-%d %H:00")
        day_key = now.strftime("%Y-%m-%d")
        
        for metric in metrics:
            event_name = metric.event.value
            
            # Update hourly aggregates
            if hour_key not in self._hourly_aggregates:
                self._hourly_aggregates[hour_key] = {}
            
            if event_name not in self._hourly_aggregates[hour_key]:
                self._hourly_aggregates[hour_key][event_name] = 0
            
            self._hourly_aggregates[hour_key][event_name] += metric.value
            
            # Update daily aggregates
            if day_key not in self._daily_aggregates:
                self._daily_aggregates[day_key] = {}
            
            if event_name not in self._daily_aggregates[day_key]:
                self._daily_aggregates[day_key][event_name] = 0
            
            self._daily_aggregates[day_key][event_name] += metric.value
        
        # Clean old aggregates (keep last 7 days)
        await self._cleanup_old_aggregates()
    
    async def _cleanup_old_aggregates(self) -> None:
        """Clean up old aggregate data"""
        cutoff_date = datetime.now() - timedelta(days=7)
        
        # Clean hourly aggregates
        hours_to_remove = [
            key for key in self._hourly_aggregates.keys()
            if datetime.strptime(key, "%Y-%m-%d %H:00") < cutoff_date
        ]
        for hour_key in hours_to_remove:
            del self._hourly_aggregates[hour_key]
        
        # Clean daily aggregates  
        days_to_remove = [
            key for key in self._daily_aggregates.keys()
            if datetime.strptime(key, "%Y-%m-%d") < cutoff_date
        ]
        for day_key in days_to_remove:
            del self._daily_aggregates[day_key]
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary"""
        # Flush pending metrics
        if time.time() - self._last_flush > self._flush_interval:
            await self._flush_metrics()
        
        now = datetime.now()
        today_key = now.strftime("%Y-%m-%d")
        current_hour_key = now.strftime("%Y-%m-%d %H:00")
        
        return {
            'request_metrics': self._request_metrics.copy(),
            'buffer_size': len(self._metrics_buffer),
            'last_flush': self._last_flush,
            'today_metrics': self._daily_aggregates.get(today_key, {}),
            'current_hour_metrics': self._hourly_aggregates.get(current_hour_key, {}),
            'total_daily_metrics': len(self._daily_aggregates),
            'total_hourly_metrics': len(self._hourly_aggregates)
        }
    
    async def get_business_kpis(self) -> Dict[str, Any]:
        """Get key business KPIs"""
        now = datetime.now()
        today_key = now.strftime("%Y-%m-%d")
        yesterday_key = (now - timedelta(days=1)).strftime("%Y-%m-%d")
        
        today_metrics = self._daily_aggregates.get(today_key, {})
        yesterday_metrics = self._daily_aggregates.get(yesterday_key, {})
        
        # Calculate KPIs
        kpis = {
            'daily_active_users': today_metrics.get(MetricEvent.USER_LOGIN.value, 0),
            'new_registrations': today_metrics.get(MetricEvent.USER_REGISTRATION.value, 0),
            'books_created': today_metrics.get(MetricEvent.BOOK_CREATION_COMPLETED.value, 0),
            'books_published': today_metrics.get(MetricEvent.BOOK_PUBLISHED.value, 0),
            'total_sales': today_metrics.get(MetricEvent.BOOK_SALE.value, 0),
            'total_revenue': today_metrics.get(MetricEvent.ROYALTY_EARNED.value, 0),
            'ai_generations': today_metrics.get(MetricEvent.AI_GENERATION_SUCCESS.value, 0),
            'api_requests': today_metrics.get(MetricEvent.API_REQUEST.value, 0),
            'api_errors': today_metrics.get(MetricEvent.API_ERROR.value, 0),
            'system_errors': today_metrics.get(MetricEvent.SYSTEM_ERROR.value, 0)
        }
        
        # Calculate day-over-day changes
        for key, today_value in kpis.items():
            yesterday_value = yesterday_metrics.get(
                key.replace('_', '').replace('daily', '').replace('new', '').replace('total', ''), 
                0
            )
            
            if yesterday_value > 0:
                change_percent = ((today_value - yesterday_value) / yesterday_value) * 100
                kpis[f"{key}_change_percent"] = round(change_percent, 2)
            else:
                kpis[f"{key}_change_percent"] = 0
        
        # Calculate success rates
        total_ai_requests = (
            today_metrics.get(MetricEvent.AI_GENERATION_SUCCESS.value, 0) +
            today_metrics.get(MetricEvent.AI_GENERATION_FAILURE.value, 0)
        )
        
        if total_ai_requests > 0:
            kpis['ai_success_rate'] = round(
                (kpis['ai_generations'] / total_ai_requests) * 100, 2
            )
        else:
            kpis['ai_success_rate'] = 0
        
        total_api_requests = kpis['api_requests'] + kpis['api_errors']
        if total_api_requests > 0:
            kpis['api_success_rate'] = round(
                (kpis['api_requests'] / total_api_requests) * 100, 2
            )
        else:
            kpis['api_success_rate'] = 0
        
        kpis['timestamp'] = time.time()
        kpis['date'] = today_key
        
        return kpis

class BusinessMetricsMiddleware(BaseHTTPMiddleware):
    """FastAPI middleware for tracking business metrics"""
    
    def __init__(self, app: ASGIApp, collector: Optional[MetricsCollector] = None):
        super().__init__(app)
        self.collector = collector or MetricsCollector()
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and track metrics"""
        start_time = time.time()
        
        # Skip metrics for health checks and docs
        if request.url.path in ['/health', '/healthz', '/docs', '/redoc', '/openapi.json']:
            return await call_next(request)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            duration_ms = (time.time() - start_time) * 1000
            
            # Record request metric
            await self.collector.record_request_metric(request, response, duration_ms)
            
            # Add custom headers
            response.headers["X-Response-Time"] = f"{duration_ms:.2f}ms"
            response.headers["X-Request-ID"] = getattr(request.state, 'request_id', 'unknown')
            
            return response
            
        except Exception as e:
            # Record error metric
            duration_ms = (time.time() - start_time) * 1000
            
            await self.collector.record_business_event(
                event=MetricEvent.SYSTEM_ERROR,
                value=1.0,
                metadata={
                    'error': str(e),
                    'endpoint': str(request.url.path),
                    'method': request.method,
                    'duration_ms': duration_ms
                }
            )
            
            self.logger.error(f"Request failed: {request.method} {request.url.path} - {e}")
            raise

# Global metrics collector
metrics_collector = MetricsCollector()

# Convenience functions for recording business events
async def track_user_registration(user_id: str, metadata: Optional[Dict[str, Any]] = None):
    """Track user registration event"""
    await metrics_collector.record_business_event(
        event=MetricEvent.USER_REGISTRATION,
        user_id=user_id,
        metadata=metadata
    )

async def track_book_creation(user_id: str, book_id: str, metadata: Optional[Dict[str, Any]] = None):
    """Track book creation completion"""
    await metrics_collector.record_business_event(
        event=MetricEvent.BOOK_CREATION_COMPLETED,
        user_id=user_id,
        metadata={'book_id': book_id, **(metadata or {})}
    )

async def track_book_publication(user_id: str, book_id: str, metadata: Optional[Dict[str, Any]] = None):
    """Track book publication"""
    await metrics_collector.record_business_event(
        event=MetricEvent.BOOK_PUBLISHED,
        user_id=user_id,
        metadata={'book_id': book_id, **(metadata or {})}
    )

async def track_book_sale(user_id: str, book_id: str, sale_amount: float, metadata: Optional[Dict[str, Any]] = None):
    """Track book sale"""
    await metrics_collector.record_business_event(
        event=MetricEvent.BOOK_SALE,
        value=1.0,
        user_id=user_id,
        metadata={'book_id': book_id, 'sale_amount': sale_amount, **(metadata or {})}
    )
    
    # Also track revenue
    await metrics_collector.record_business_event(
        event=MetricEvent.ROYALTY_EARNED,
        value=sale_amount,
        unit="USD",
        user_id=user_id,
        metadata={'book_id': book_id, **(metadata or {})}
    )

async def track_ai_generation(user_id: str, success: bool, duration_ms: float, metadata: Optional[Dict[str, Any]] = None):
    """Track AI generation request"""
    event = MetricEvent.AI_GENERATION_SUCCESS if success else MetricEvent.AI_GENERATION_FAILURE
    
    await metrics_collector.record_business_event(
        event=event,
        value=duration_ms,
        unit="milliseconds",
        user_id=user_id,
        metadata=metadata
    )

async def get_business_metrics() -> Dict[str, Any]:
    """Get current business metrics summary"""
    return await metrics_collector.get_metrics_summary()

async def get_business_kpis() -> Dict[str, Any]:
    """Get key business performance indicators"""
    return await metrics_collector.get_business_kpis()