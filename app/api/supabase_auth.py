# app/api/supabase_auth.py - Supabase Authentication API

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any
import logging

from app.auth.supabase_auth import get_auth_service, get_current_user, SupabaseAuthService
from app.schemas.user import UserCreate, UserLogin, Token, UserResponse
from pydantic import BaseModel, EmailStr

logger = logging.getLogger(__name__)

router = APIRouter()

# =====================================
# REQUEST/RESPONSE SCHEMAS
# =====================================

class RegisterRequest(BaseModel):
    email: EmailStr
    password: str
    full_name: str = None

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordUpdateRequest(BaseModel):
    new_password: str

class OAuthRequest(BaseModel):
    provider: str
    redirect_url: str = None

class SSORequest(BaseModel):
    domain: str = None
    provider_id: str = None

class OAuthCallbackRequest(BaseModel):
    access_token: str
    refresh_token: str

class AuthResponse(BaseModel):
    user: Dict[str, Any]
    access_token: str
    refresh_token: str = None
    expires_at: int = None
    token_type: str = "bearer"
    provider: str = None

# =====================================
# AUTHENTICATION ENDPOINTS
# =====================================

@router.post("/register", response_model=Dict[str, Any])
async def register(
    request: RegisterRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Register a new user with Supabase Auth"""
    try:
        result = await auth_service.register_user(
            email=request.email,
            password=request.password,
            full_name=request.full_name
        )
        
        logger.info(f"User registration successful: {request.email}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=AuthResponse)
async def login(
    request: LoginRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Login user with Supabase Auth"""
    try:
        result = await auth_service.login_user(
            email=request.email,
            password=request.password
        )
        
        logger.info(f"User login successful: {request.email}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/refresh", response_model=Dict[str, Any])
async def refresh_token(
    request: RefreshTokenRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Refresh access token"""
    try:
        result = await auth_service.refresh_token(request.refresh_token)
        
        logger.info("Token refresh successful")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post("/oauth/signin", response_model=Dict[str, Any])
async def oauth_signin(
    request: OAuthRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Initiate OAuth sign-in with supported providers"""
    try:
        result = await auth_service.sign_in_with_oauth(
            provider=request.provider,
            redirect_url=request.redirect_url
        )
        
        logger.info(f"OAuth sign-in initiated: {request.provider}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth sign-in error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth sign-in failed"
        )

@router.post("/sso/signin", response_model=Dict[str, Any])
async def sso_signin(
    request: SSORequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Initiate SSO sign-in with domain or provider ID"""
    try:
        result = await auth_service.sign_in_with_sso(
            domain=request.domain,
            provider_id=request.provider_id
        )
        
        logger.info(f"SSO sign-in initiated: {request.domain or request.provider_id}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"SSO sign-in error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="SSO sign-in failed"
        )

@router.post("/oauth/callback", response_model=AuthResponse)
async def oauth_callback(
    request: OAuthCallbackRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Handle OAuth callback and complete authentication"""
    try:
        result = await auth_service.handle_oauth_callback(
            access_token=request.access_token,
            refresh_token=request.refresh_token
        )
        
        logger.info(f"OAuth callback processed successfully")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth callback failed"
        )

@router.get("/oauth/providers")
async def get_oauth_providers():
    """Get list of supported OAuth providers"""
    return {
        "providers": [
            {"name": "google", "display_name": "Google", "icon": "google"},
            {"name": "github", "display_name": "GitHub", "icon": "github"},
            {"name": "facebook", "display_name": "Facebook", "icon": "facebook"},
            {"name": "twitter", "display_name": "Twitter", "icon": "twitter"},
            {"name": "discord", "display_name": "Discord", "icon": "discord"},
            {"name": "linkedin", "display_name": "LinkedIn", "icon": "linkedin"},
            {"name": "spotify", "display_name": "Spotify", "icon": "spotify"},
            {"name": "slack", "display_name": "Slack", "icon": "slack"},
            {"name": "microsoft", "display_name": "Microsoft", "icon": "microsoft"},
            {"name": "apple", "display_name": "Apple", "icon": "apple"}
        ]
    }

@router.post("/logout")
async def logout(
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Logout current user"""
    try:
        # Note: We would need the access token to properly logout
        # For now, just return success (client should discard tokens)
        result = {"message": "Logged out successfully"}
        
        logger.info(f"User logout: {current_user.get('email', 'unknown')}")
        return result
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return {"message": "Logged out"}

@router.get("/me", response_model=Dict[str, Any])
async def get_current_user_profile(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get current user profile"""
    try:
        logger.info(f"Profile accessed: {current_user.get('email', 'unknown')}")
        return current_user
        
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get profile"
        )

@router.post("/password-reset")
async def request_password_reset(
    request: PasswordResetRequest,
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Request password reset email"""
    try:
        result = await auth_service.reset_password(request.email)
        
        logger.info(f"Password reset requested: {request.email}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

@router.post("/password-update")
async def update_password(
    request: PasswordUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: SupabaseAuthService = Depends(get_auth_service)
):
    """Update user password"""
    try:
        # Note: This would need the current access token
        # For now, just return success message
        result = {"message": "Password update functionality requires frontend integration"}
        
        logger.info(f"Password update requested: {current_user.get('email', 'unknown')}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password update failed"
        )

@router.get("/verify")
async def verify_token(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Verify if token is valid"""
    try:
        return {
            "valid": True,
            "user_id": current_user.get("id"),
            "email": current_user.get("email")
        }
        
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

# =====================================
# USER PROFILE MANAGEMENT
# =====================================

class ProfileUpdateRequest(BaseModel):
    full_name: str = None
    subscription_tier: str = None
    preferred_ai_provider: str = None
    default_writing_style: str = None
    default_target_audience: str = None
    notification_preferences: Dict[str, Any] = None
    content_preferences: Dict[str, Any] = None

@router.put("/profile", response_model=Dict[str, Any])
async def update_profile(
    request: ProfileUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update user profile"""
    try:
        from app.models.supabase_models import get_user_model
        
        user_model = await get_user_model()
        
        # Prepare updates
        updates = {}
        if request.full_name is not None:
            updates["full_name"] = request.full_name
        if request.subscription_tier is not None:
            updates["subscription_tier"] = request.subscription_tier
        if request.preferred_ai_provider is not None:
            updates["preferred_ai_provider"] = request.preferred_ai_provider
        if request.default_writing_style is not None:
            updates["default_writing_style"] = request.default_writing_style
        if request.default_target_audience is not None:
            updates["default_target_audience"] = request.default_target_audience
        if request.notification_preferences is not None:
            updates["notification_preferences"] = request.notification_preferences
        if request.content_preferences is not None:
            updates["content_preferences"] = request.content_preferences
        
        if updates:
            updated_user = await user_model.update_user(current_user["id"], updates)
            logger.info(f"Profile updated: {current_user.get('email', 'unknown')}")
            return updated_user
        else:
            return current_user
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )

# =====================================
# ADMIN ENDPOINTS
# =====================================

@router.get("/admin/users")
async def list_users(
    current_user: Dict[str, Any] = Depends(get_current_user),
    limit: int = 50,
    offset: int = 0
):
    """List all users (admin only)"""
    try:
        # Check admin permissions
        if current_user.get("subscription_tier") != "enterprise":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        from app.utils.supabase.supabase_database import get_supabase

        client = await get_supabase()

        # Get users with pagination
        result = client.table("users").select("id,email,full_name,subscription_tier,created_at,last_login") \
                       .range(offset, offset + limit - 1) \
                       .order("created_at", desc=True) \
                       .execute()

        logger.info(f"Admin user list accessed by: {current_user.get('email', 'unknown')}")
        return {
            "users": result.data or [],
            "count": len(result.data or []),
            "offset": offset,
            "limit": limit
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin user list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user list"
        )
