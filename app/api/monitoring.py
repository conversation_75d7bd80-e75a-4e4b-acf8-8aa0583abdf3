# app/api/monitoring.py - Monitoring API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_monitoring_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions

logger = get_logger(__name__)

router = APIRouter(tags=["monitoring"])


@router.get("/verl")
@rate_limit_user_actions()
async def get_verl_monitoring(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get VERL training monitoring data"""
    user_id = current_user["id"]
    
    with monitor_operation("get_verl_monitoring", user_id=user_id):
        try:
            monitoring_model = await get_monitoring_model()
            verl_data = await monitoring_model.get_verl_monitoring(user_id)
            
            logger.info(f"VERL monitoring data retrieved for user {user_id}")
            return verl_data
            
        except Exception as e:
            logger.error(f"Error fetching VERL monitoring for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "get_verl_monitoring"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch monitoring data"
            )


@router.get("/system")
@rate_limit_user_actions()
async def get_system_monitoring(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get system monitoring data"""
    user_id = current_user["id"]
    
    with monitor_operation("get_system_monitoring", user_id=user_id):
        try:
            monitoring_model = await get_monitoring_model()
            system_data = await monitoring_model.get_system_monitoring(user_id)
            
            logger.info(f"System monitoring data retrieved for user {user_id}")
            return system_data
            
        except Exception as e:
            logger.error(f"Error fetching system monitoring for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "get_system_monitoring"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch system monitoring"
            )