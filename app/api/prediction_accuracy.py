# app/api/prediction_accuracy.py - Prediction Accuracy API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_prediction_accuracy_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions

logger = get_logger(__name__)

router = APIRouter(tags=["prediction-accuracy"])


@router.get("/")
@rate_limit_user_actions()
async def get_accuracy_metrics(
    request: Request,
    days_back: int = Query(30, ge=1, le=365, description="Days back to analyze"),
    prediction_type: Optional[str] = Query(None, description="Filter by prediction type"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get prediction accuracy metrics for user"""
    user_id = current_user["id"]
    
    with monitor_operation("get_accuracy_metrics", user_id=user_id, days_back=days_back):
        try:
            accuracy_model = get_prediction_accuracy_model()
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days_back)
            
            # Get accuracy metrics
            metrics = await accuracy_model.get_user_accuracy_metrics(
                user_id, start_date, end_date, prediction_type
            )
            
            logger.info(f"Accuracy metrics retrieved for user {user_id} ({days_back} days)")
            return {
                "metrics": metrics,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days_back
                },
                "prediction_type": prediction_type
            }
            
        except Exception as e:
            logger.error(f"Error retrieving accuracy metrics for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "days_back": days_back, 
                "endpoint": "get_accuracy_metrics"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve accuracy metrics"
            )


@router.post("/")
@rate_limit_user_actions()
async def record_actual_results(
    request: Request,
    accuracy_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Record actual results to measure prediction accuracy"""
    user_id = current_user["id"]
    
    # Validate required fields
    required_fields = ["prediction_id", "actual_value", "prediction_type"]
    missing_fields = [field for field in required_fields if field not in accuracy_data]
    
    if missing_fields:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Missing required fields: {', '.join(missing_fields)}"
        )
    
    with monitor_operation("record_actual_results", user_id=user_id):
        try:
            # Add user_id and timestamp to the data
            accuracy_data["user_id"] = user_id
            accuracy_data["recorded_at"] = datetime.utcnow().isoformat()
            
            accuracy_model = get_prediction_accuracy_model()
            result = await accuracy_model.record_actual_result(accuracy_data)
            
            logger.info(f"Actual result recorded for prediction {accuracy_data['prediction_id']} by user {user_id}")
            return {"accuracy_record": result}
            
        except Exception as e:
            logger.error(f"Error recording actual results for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "accuracy_data": accuracy_data, 
                "endpoint": "record_actual_results"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to record actual results"
            )


@router.get("/{prediction_id}")
@rate_limit_user_actions()
async def get_specific_accuracy(
    request: Request,
    prediction_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get accuracy data for a specific prediction"""
    user_id = current_user["id"]
    
    with monitor_operation("get_specific_accuracy", user_id=user_id, prediction_id=prediction_id):
        try:
            accuracy_model = get_prediction_accuracy_model()
            accuracy_data = await accuracy_model.get_prediction_accuracy_by_id(
                prediction_id, user_id
            )
            
            if not accuracy_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Prediction accuracy data not found"
                )
            
            logger.info(f"Accuracy data for prediction {prediction_id} retrieved by user {user_id}")
            return {"accuracy": accuracy_data}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving accuracy for prediction {prediction_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "prediction_id": prediction_id, 
                "endpoint": "get_specific_accuracy"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve prediction accuracy"
            )


@router.get("/summary/dashboard")
@rate_limit_user_actions()
async def get_accuracy_summary(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get accuracy summary for dashboard display"""
    user_id = current_user["id"]
    
    with monitor_operation("get_accuracy_summary", user_id=user_id):
        try:
            accuracy_model = get_prediction_accuracy_model()
            summary = await accuracy_model.get_accuracy_summary_dashboard(user_id)
            
            logger.info(f"Accuracy summary dashboard data retrieved for user {user_id}")
            return {
                "summary": summary,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error retrieving accuracy summary for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "endpoint": "get_accuracy_summary"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve accuracy summary"
            )


@router.get("/models/comparison")
@rate_limit_user_actions()
async def get_model_performance_comparison(
    request: Request,
    days_back: int = Query(90, ge=7, le=365, description="Days back to analyze"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get model performance comparison across different prediction types"""
    user_id = current_user["id"]
    
    with monitor_operation("get_model_performance_comparison", user_id=user_id):
        try:
            accuracy_model = get_prediction_accuracy_model()
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days_back)
            
            comparison = await accuracy_model.compare_model_performance(
                user_id, start_date, end_date
            )
            
            logger.info(f"Model performance comparison retrieved for user {user_id} ({days_back} days)")
            return {
                "comparison": comparison,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days_back
                },
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error retrieving model performance comparison for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "days_back": days_back, 
                "endpoint": "get_model_performance_comparison"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve model performance comparison"
            )