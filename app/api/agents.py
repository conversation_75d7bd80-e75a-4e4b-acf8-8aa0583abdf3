# app/api/agents.py - Agents API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Request, Query
from typing import Dict, Any, Optional
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_agents_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["agents"])


@router.post("/generate-manuscript")
@rate_limit_ai_operations()
async def generate_manuscript(
    request: Request,
    background_tasks: BackgroundTasks,
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Start manuscript generation with PydanticAI agents"""
    user_id = current_user["id"]
    
    # Validate required fields
    if not request_data.get("topic") and not request_data.get("title"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'topic' or 'title' is required for manuscript generation"
        )
    
    with monitor_operation("generate_manuscript", user_id=user_id, request_data=request_data):
        try:
            agents_model = await get_agents_model()
            
            # Create agent task record
            task = await agents_model.create_agent_task(
                user_id=user_id,
                task_type="manuscript",
                task_data=request_data
            )
            
            # Schedule background task for actual processing
            def process_manuscript_generation():
                logger.info(f"Starting manuscript generation for user {user_id}, task {task['task_id']}")
                # Here would be the actual PydanticAI agent execution
                # For now, we track the task in the database and show realistic progress
            
            background_tasks.add_task(process_manuscript_generation)
            
            logger.info(f"Manuscript generation task {task['task_id']} created for user {user_id}")
            return task
            
        except Exception as e:
            logger.error(f"Error starting manuscript generation for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "request_data": request_data, "endpoint": "generate_manuscript"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start manuscript generation"
            )


@router.get("/status/{task_id}")
@rate_limit_user_actions()
async def get_agent_status(
    request: Request,
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get status of agent task"""
    user_id = current_user["id"]
    
    with monitor_operation("get_agent_status", user_id=user_id, task_id=task_id):
        try:
            agents_model = await get_agents_model()
            task_status = await agents_model.get_agent_task_status(task_id, user_id)
            
            logger.info(f"Agent task status retrieved for task {task_id} by user {user_id}")
            return task_status
            
        except ValueError as e:
            logger.warning(f"Task {task_id} not found for user {user_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        except Exception as e:
            logger.error(f"Error fetching agent status for task {task_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "task_id": task_id, "endpoint": "get_agent_status"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch agent status"
            )


@router.post("/trend-analysis")
@rate_limit_ai_operations()
async def start_trend_analysis(
    request: Request,
    background_tasks: BackgroundTasks,
    analysis_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Start trend analysis with agents"""
    user_id = current_user["id"]
    
    # Validate required fields
    if not analysis_data.get("keywords") and not analysis_data.get("category"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'keywords' or 'category' is required for trend analysis"
        )
    
    with monitor_operation("start_trend_analysis", user_id=user_id, analysis_data=analysis_data):
        try:
            agents_model = await get_agents_model()
            
            # Create agent task record
            task = await agents_model.create_agent_task(
                user_id=user_id,
                task_type="trend_analysis",
                task_data=analysis_data
            )
            
            # Schedule background task for actual processing
            def process_trend_analysis():
                logger.info(f"Starting trend analysis for user {user_id}, task {task['task_id']}")
                # Here would be the actual PydanticAI agent execution
                # For now, we track the task in the database and show realistic progress
            
            background_tasks.add_task(process_trend_analysis)
            
            logger.info(f"Trend analysis task {task['task_id']} created for user {user_id}")
            return task
            
        except Exception as e:
            logger.error(f"Error starting trend analysis for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "analysis_data": analysis_data, "endpoint": "start_trend_analysis"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start trend analysis"
            )


@router.get("/tasks")
@rate_limit_user_actions()
async def get_user_agent_tasks(
    request: Request,
    status: Optional[str] = Query(None, description="Filter by task status"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of tasks to return"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get all agent tasks for the current user"""
    user_id = current_user["id"]
    
    with monitor_operation("get_user_agent_tasks", user_id=user_id, status=status, limit=limit):
        try:
            agents_model = await get_agents_model()
            tasks = await agents_model.get_user_agent_tasks(user_id, status, limit)
            
            logger.info(f"Retrieved {len(tasks)} agent tasks for user {user_id}")
            return {
                "tasks": tasks,
                "total": len(tasks),
                "filter_status": status
            }
            
        except Exception as e:
            logger.error(f"Error fetching user agent tasks for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "status": status, "limit": limit, "endpoint": "get_user_agent_tasks"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch agent tasks"
            )