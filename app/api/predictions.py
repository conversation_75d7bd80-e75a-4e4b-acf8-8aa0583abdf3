# app/api/predictions.py - Predictions API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from typing import Dict, Any, Optional
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_prediction_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["predictions"])


@router.post("/sales")
@rate_limit_ai_operations()
async def predict_sales(
    request: Request,
    book_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Predict sales performance for a book"""
    user_id = current_user["id"]
    
    # Validate required fields
    if not book_data.get("category"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Book category is required for sales prediction"
        )
    
    with monitor_operation("predict_sales", user_id=user_id, book_category=book_data.get("category")):
        try:
            prediction_model = await get_prediction_model()
            prediction = await prediction_model.predict_sales(book_data, user_id)
            
            logger.info(f"Sales prediction generated for user {user_id}: {prediction['predicted_sales']} units")
            return prediction
            
        except Exception as e:
            logger.error(f"Error predicting sales for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "book_data": book_data, "endpoint": "predict_sales"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to predict sales"
            )


@router.get("/market/{category}")
@rate_limit_user_actions()
async def get_market_predictions(
    request: Request,
    category: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get market predictions for a category"""
    user_id = current_user["id"]
    
    with monitor_operation("get_market_predictions", user_id=user_id, category=category):
        try:
            prediction_model = await get_prediction_model()
            market_predictions = await prediction_model.get_market_predictions(category, user_id)
            
            logger.info(f"Market predictions retrieved for category '{category}' by user {user_id}")
            return market_predictions
            
        except Exception as e:
            logger.error(f"Error fetching market predictions for category '{category}': {e}")
            capture_exception(e, extra={"user_id": user_id, "category": category, "endpoint": "get_market_predictions"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch market predictions"
            )


@router.get("/accuracy")
@rate_limit_user_actions()
async def get_prediction_accuracy(
    request: Request,
    days_back: int = Query(30, ge=1, le=365, description="Days back to analyze accuracy"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get accuracy metrics for user's previous predictions"""
    user_id = current_user["id"]
    
    with monitor_operation("get_prediction_accuracy", user_id=user_id, days_back=days_back):
        try:
            prediction_model = await get_prediction_model()
            accuracy_metrics = await prediction_model.get_prediction_accuracy(user_id, days_back)
            
            logger.info(f"Prediction accuracy retrieved for user {user_id} ({days_back} days)")
            return accuracy_metrics
            
        except Exception as e:
            logger.error(f"Error fetching prediction accuracy for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "days_back": days_back, "endpoint": "get_prediction_accuracy"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch prediction accuracy"
            )