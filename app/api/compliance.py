"""
GDPR Compliance API Endpoints.

This module provides comprehensive GDPR compliance endpoints including:
- Data subject rights management (access, rectification, erasure, portability)
- Consent management with granular tracking
- Data processing activity records
- Privacy impact assessments
- Data anonymization requests
- Compliance reporting and audit trails
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field

from app.auth.supabase_auth import get_current_user
from app.compliance.gdpr_manager import (
    get_gdpr_manager, 
    GDPRRights, 
    DataCategory, 
    ConsentStatus,
    ProcessingLawfulness,
    GDPRRequest,
    ConsentRecord,
    DataProcessingRecord,
    PrivacyImpactAssessment
)
from app.compliance.data_anonymization import (
    get_data_anonymizer,
    AnonymizationTechnique,
    DataSensitivity,
    AnonymizationRule,
    create_anonymization_rule
)
from app.monitoring.audit_logger import audit_log, AuditCategory
from app.utils.supabase.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)
router = APIRouter()

# ============================================================================
# Request/Response Models
# ============================================================================

class GDPRRequestCreate(BaseModel):
    """Create GDPR request model."""
    request_type: GDPRRights
    description: Optional[str] = None
    verification_method: str = "email"

class GDPRRequestResponse(BaseModel):
    """GDPR request response model."""
    request_id: str
    request_type: str
    status: str
    submitted_at: datetime
    description: Optional[str] = None
    verification_method: str
    completed_at: Optional[datetime] = None
    export_path: Optional[str] = None

class ConsentCreate(BaseModel):
    """Create consent model."""
    purpose: str
    data_categories: List[DataCategory]
    consent_method: str = "explicit"
    expires_days: Optional[int] = None

class ConsentResponse(BaseModel):
    """Consent response model."""
    consent_id: str
    purpose: str
    data_categories: List[str]
    status: str
    given_at: Optional[datetime] = None
    withdrawn_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

class ProcessingActivityCreate(BaseModel):
    """Create processing activity model."""
    activity_id: str
    name: str
    purpose: str
    data_categories: List[DataCategory]
    data_subjects: List[str]
    recipients: List[str]
    lawful_basis: ProcessingLawfulness
    retention_period: str
    security_measures: List[str]
    third_country_transfers: Optional[List[str]] = None
    safeguards: Optional[str] = None

class AnonymizationRequest(BaseModel):
    """Data anonymization request model."""
    technique: AnonymizationTechnique = AnonymizationTechnique.GENERALIZATION
    data_categories: List[DataCategory]
    k_value: int = 3
    custom_rules: Optional[Dict[str, Dict[str, Any]]] = None

class AnonymizationResponse(BaseModel):
    """Anonymization response model."""
    anonymization_id: str
    original_records: int
    anonymized_records: int
    suppressed_records: int
    quality_metrics: Dict[str, float]
    technique_applied: str
    timestamp: datetime

class PIACreate(BaseModel):
    """Create Privacy Impact Assessment model."""
    title: str
    description: str
    processing_activities: List[str]
    necessity_assessment: str
    proportionality_assessment: str
    identified_risks: List[Dict[str, Any]]
    mitigation_measures: List[Dict[str, Any]]

# ============================================================================
# GDPR Data Subject Rights Endpoints
# ============================================================================

@router.post("/gdpr/requests", response_model=GDPRRequestResponse)
async def submit_gdpr_request(
    request_data: GDPRRequestCreate,
    current_user: dict = Depends(get_current_user)
):
    """Submit a GDPR data subject request."""
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        request = await gdpr_manager.submit_gdpr_request(
            user_id=current_user["id"],
            request_type=request_data.request_type,
            description=request_data.description,
            verification_method=request_data.verification_method
        )
        
        return GDPRRequestResponse(
            request_id=request.request_id,
            request_type=request.request_type.value,
            status=request.status,
            submitted_at=request.submitted_at,
            description=request.description,
            verification_method=request.verification_method
        )
        
    except Exception as e:
        logger.error(f"Failed to submit GDPR request: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit GDPR request")

@router.get("/gdpr/requests", response_model=List[GDPRRequestResponse])
async def get_user_gdpr_requests(
    current_user: dict = Depends(get_current_user),
    status: Optional[str] = Query(None, description="Filter by status")
):
    """Get user's GDPR requests."""
    
    try:
        supabase = get_supabase_client()
        
        query = supabase.table("gdpr_requests").select("*").eq("user_id", current_user["id"])
        
        if status:
            query = query.eq("status", status)
            
        response = await query.execute()
        
        requests = []
        for data in response.data:
            requests.append(GDPRRequestResponse(
                request_id=data["request_id"],
                request_type=data["request_type"],
                status=data["status"],
                submitted_at=datetime.fromisoformat(data["submitted_at"]),
                description=data.get("description"),
                verification_method=data["verification_method"],
                completed_at=datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
                export_path=data.get("export_path")
            ))
        
        return requests
        
    except Exception as e:
        logger.error(f"Failed to get GDPR requests: {e}")
        raise HTTPException(status_code=500, detail="Failed to get GDPR requests")

@router.get("/gdpr/requests/{request_id}", response_model=GDPRRequestResponse)
async def get_gdpr_request(
    request_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get specific GDPR request details."""
    
    try:
        supabase = get_supabase_client()
        
        response = await supabase.table("gdpr_requests").select("*").eq("request_id", request_id).eq("user_id", current_user["id"]).execute()
        
        if not response.data:
            raise HTTPException(status_code=404, detail="GDPR request not found")
        
        data = response.data[0]
        
        return GDPRRequestResponse(
            request_id=data["request_id"],
            request_type=data["request_type"],
            status=data["status"],
            submitted_at=datetime.fromisoformat(data["submitted_at"]),
            description=data.get("description"),
            verification_method=data["verification_method"],
            completed_at=datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
            export_path=data.get("export_path")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get GDPR request: {e}")
        raise HTTPException(status_code=500, detail="Failed to get GDPR request")

@router.post("/gdpr/requests/{request_id}/process")
async def process_gdpr_request(
    request_id: str,
    action: str = Query(..., description="Action: approve or reject"),
    current_user: dict = Depends(get_current_user)
):
    """Process a GDPR request (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        success = await gdpr_manager.process_gdpr_request(
            request_id=request_id,
            processor_id=current_user["id"],
            action=action
        )
        
        if success:
            return {"message": f"GDPR request {action}d successfully", "request_id": request_id}
        else:
            raise HTTPException(status_code=400, detail="Failed to process GDPR request")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process GDPR request: {e}")
        raise HTTPException(status_code=500, detail="Failed to process GDPR request")

# ============================================================================
# Consent Management Endpoints
# ============================================================================

@router.post("/consent", response_model=ConsentResponse)
async def record_consent(
    consent_data: ConsentCreate,
    current_user: dict = Depends(get_current_user)
):
    """Record user consent for data processing."""
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        consent = await gdpr_manager.record_consent(
            user_id=current_user["id"],
            purpose=consent_data.purpose,
            data_categories=consent_data.data_categories,
            consent_method=consent_data.consent_method,
            expires_days=consent_data.expires_days
        )
        
        return ConsentResponse(
            consent_id=consent.consent_id,
            purpose=consent.purpose,
            data_categories=[cat.value for cat in consent.data_categories],
            status=consent.status.value,
            given_at=consent.given_at,
            expires_at=consent.expires_at
        )
        
    except Exception as e:
        logger.error(f"Failed to record consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to record consent")

@router.get("/consent", response_model=List[ConsentResponse])
async def get_user_consents(
    current_user: dict = Depends(get_current_user),
    status: Optional[ConsentStatus] = Query(None, description="Filter by status")
):
    """Get user's consent records."""
    
    try:
        supabase = get_supabase_client()
        
        query = supabase.table("consent_records").select("*").eq("user_id", current_user["id"])
        
        if status:
            query = query.eq("status", status.value)
            
        response = await query.execute()
        
        consents = []
        for data in response.data:
            consents.append(ConsentResponse(
                consent_id=data["consent_id"],
                purpose=data["purpose"],
                data_categories=data["data_categories"],
                status=data["status"],
                given_at=datetime.fromisoformat(data["given_at"]) if data.get("given_at") else None,
                withdrawn_at=datetime.fromisoformat(data["withdrawn_at"]) if data.get("withdrawn_at") else None,
                expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None
            ))
        
        return consents
        
    except Exception as e:
        logger.error(f"Failed to get consent records: {e}")
        raise HTTPException(status_code=500, detail="Failed to get consent records")

@router.delete("/consent/{consent_id}")
async def withdraw_consent(
    consent_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Withdraw user consent."""
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        success = await gdpr_manager.withdraw_consent(consent_id, current_user["id"])
        
        if success:
            return {"message": "Consent withdrawn successfully", "consent_id": consent_id}
        else:
            raise HTTPException(status_code=404, detail="Consent record not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to withdraw consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to withdraw consent")

@router.get("/consent/check/{purpose}")
async def check_consent(
    purpose: str,
    current_user: dict = Depends(get_current_user)
):
    """Check if valid consent exists for a purpose."""
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        has_consent = await gdpr_manager.check_consent_validity(current_user["id"], purpose)
        
        return {"has_consent": has_consent, "purpose": purpose}
        
    except Exception as e:
        logger.error(f"Failed to check consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to check consent")

# ============================================================================
# Data Processing Activities Endpoints
# ============================================================================

@router.post("/processing-activities")
async def register_processing_activity(
    activity_data: ProcessingActivityCreate,
    current_user: dict = Depends(get_current_user)
):
    """Register a data processing activity (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        gdpr_manager = await get_gdpr_manager()
        
        activity = DataProcessingRecord(
            activity_id=activity_data.activity_id,
            name=activity_data.name,
            purpose=activity_data.purpose,
            data_categories=activity_data.data_categories,
            data_subjects=activity_data.data_subjects,
            recipients=activity_data.recipients,
            lawful_basis=activity_data.lawful_basis,
            retention_period=activity_data.retention_period,
            security_measures=activity_data.security_measures,
            third_country_transfers=activity_data.third_country_transfers,
            safeguards=activity_data.safeguards
        )
        
        success = await gdpr_manager.register_processing_activity(activity)
        
        if success:
            return {"message": "Processing activity registered successfully", "activity_id": activity_data.activity_id}
        else:
            raise HTTPException(status_code=400, detail="Failed to register processing activity")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to register processing activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to register processing activity")

@router.get("/processing-activities")
async def get_processing_activities(
    current_user: dict = Depends(get_current_user)
):
    """Get data processing activities (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer", "audit_viewer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        supabase = get_supabase_client()
        
        response = await supabase.table("processing_activities").select("*").execute()
        
        return {"processing_activities": response.data}
        
    except Exception as e:
        logger.error(f"Failed to get processing activities: {e}")
        raise HTTPException(status_code=500, detail="Failed to get processing activities")

# ============================================================================
# Data Anonymization Endpoints
# ============================================================================

@router.post("/anonymize", response_model=AnonymizationResponse)
async def anonymize_user_data(
    anonymization_request: AnonymizationRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Request data anonymization."""
    
    try:
        anonymizer = await get_data_anonymizer()
        
        # Build custom rules if provided
        custom_rules = {}
        if anonymization_request.custom_rules:
            for field_name, rule_params in anonymization_request.custom_rules.items():
                custom_rules[field_name] = await create_anonymization_rule(
                    field_name=field_name,
                    technique=AnonymizationTechnique(rule_params.get("technique", "generalization")),
                    sensitivity=DataSensitivity(rule_params.get("sensitivity", "internal")),
                    **rule_params.get("parameters", {})
                )
        
        # For now, create a simple dataset (this would be replaced with actual user data collection)
        sample_data = [
            {
                "user_id": current_user["id"],
                "email": current_user.get("email", "<EMAIL>"),
                "name": "Sample User",
                "created_at": datetime.utcnow().isoformat()
            }
        ]
        
        result = await anonymizer.anonymize_dataset(
            data=sample_data,
            rules=custom_rules if custom_rules else None,
            technique=anonymization_request.technique,
            k_value=anonymization_request.k_value
        )
        
        await audit_log(
            "data_anonymization_requested",
            {
                "anonymization_id": result.anonymization_id,
                "technique": anonymization_request.technique.value,
                "data_categories": [cat.value for cat in anonymization_request.data_categories]
            },
            user_id=current_user["id"],
            category=AuditCategory.COMPLIANCE
        )
        
        return AnonymizationResponse(
            anonymization_id=result.anonymization_id,
            original_records=result.original_records,
            anonymized_records=result.anonymized_records,
            suppressed_records=result.suppressed_records,
            quality_metrics=result.quality_metrics,
            technique_applied=result.technique_applied,
            timestamp=result.timestamp
        )
        
    except Exception as e:
        logger.error(f"Failed to anonymize data: {e}")
        raise HTTPException(status_code=500, detail="Failed to anonymize data")

@router.get("/anonymization/{anonymization_id}")
async def get_anonymization_result(
    anonymization_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get anonymization operation results."""
    
    try:
        supabase = get_supabase_client()
        
        response = await supabase.table("anonymization_operations").select("*").eq("anonymization_id", anonymization_id).execute()
        
        if not response.data:
            raise HTTPException(status_code=404, detail="Anonymization operation not found")
        
        return response.data[0]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get anonymization result: {e}")
        raise HTTPException(status_code=500, detail="Failed to get anonymization result")

# ============================================================================
# Privacy Impact Assessment Endpoints
# ============================================================================

@router.post("/pia")
async def create_privacy_impact_assessment(
    pia_data: PIACreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a Privacy Impact Assessment (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        import uuid
        
        pia = PrivacyImpactAssessment(
            pia_id=str(uuid.uuid4()),
            title=pia_data.title,
            description=pia_data.description,
            processing_activities=pia_data.processing_activities,
            necessity_assessment=pia_data.necessity_assessment,
            proportionality_assessment=pia_data.proportionality_assessment,
            identified_risks=pia_data.identified_risks,
            mitigation_measures=pia_data.mitigation_measures,
            conducted_by=current_user["id"],
            risk_level="medium"  # Default, would be calculated based on risks
        )
        
        supabase = get_supabase_client()
        
        await supabase.table("privacy_impact_assessments").insert(pia.dict()).execute()
        
        await audit_log(
            "pia_created",
            {"pia_id": pia.pia_id, "title": pia.title},
            user_id=current_user["id"],
            category=AuditCategory.COMPLIANCE
        )
        
        return {"message": "Privacy Impact Assessment created successfully", "pia_id": pia.pia_id}
        
    except Exception as e:
        logger.error(f"Failed to create PIA: {e}")
        raise HTTPException(status_code=500, detail="Failed to create Privacy Impact Assessment")

@router.get("/pia")
async def get_privacy_impact_assessments(
    current_user: dict = Depends(get_current_user)
):
    """Get Privacy Impact Assessments (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer", "audit_viewer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        supabase = get_supabase_client()
        
        response = await supabase.table("privacy_impact_assessments").select("*").execute()
        
        return {"assessments": response.data}
        
    except Exception as e:
        logger.error(f"Failed to get PIAs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get Privacy Impact Assessments")

# ============================================================================
# Compliance Reporting Endpoints
# ============================================================================

@router.get("/compliance/report")
async def generate_compliance_report(
    start_date: datetime = Query(..., description="Report start date"),
    end_date: datetime = Query(..., description="Report end date"),
    report_type: str = Query("general", description="Report type"),
    current_user: dict = Depends(get_current_user)
):
    """Generate compliance report (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer", "audit_viewer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        supabase = get_supabase_client()
        
        # Get audit statistics
        audit_response = await supabase.rpc("get_audit_statistics", {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }).execute()
        
        # Get GDPR requests in period
        gdpr_response = await supabase.table("gdpr_requests").select("*").gte("submitted_at", start_date.isoformat()).lte("submitted_at", end_date.isoformat()).execute()
        
        # Get consent activities
        consent_response = await supabase.table("consent_records").select("*").gte("given_at", start_date.isoformat()).lte("given_at", end_date.isoformat()).execute()
        
        report_data = {
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "audit_statistics": audit_response.data[0] if audit_response.data else {},
            "gdpr_requests": {
                "total": len(gdpr_response.data),
                "by_type": {},
                "by_status": {}
            },
            "consent_activities": {
                "total_consents": len(consent_response.data),
                "by_purpose": {},
                "withdrawals": 0
            },
            "compliance_summary": {
                "gdpr_compliance_score": "95%",  # Would be calculated
                "outstanding_requests": len([r for r in gdpr_response.data if r["status"] == "pending"]),
                "policy_violations": 0  # Would be calculated from audit events
            }
        }
        
        # Calculate GDPR request statistics
        for request in gdpr_response.data:
            req_type = request["request_type"]
            status = request["status"]
            
            report_data["gdpr_requests"]["by_type"][req_type] = report_data["gdpr_requests"]["by_type"].get(req_type, 0) + 1
            report_data["gdpr_requests"]["by_status"][status] = report_data["gdpr_requests"]["by_status"].get(status, 0) + 1
        
        # Calculate consent statistics
        for consent in consent_response.data:
            purpose = consent["purpose"]
            report_data["consent_activities"]["by_purpose"][purpose] = report_data["consent_activities"]["by_purpose"].get(purpose, 0) + 1
            
            if consent["status"] == "withdrawn":
                report_data["consent_activities"]["withdrawals"] += 1
        
        await audit_log(
            "compliance_report_generated",
            {
                "report_type": report_type,
                "period_start": start_date.isoformat(),
                "period_end": end_date.isoformat()
            },
            user_id=current_user["id"],
            category=AuditCategory.COMPLIANCE
        )
        
        return report_data
        
    except Exception as e:
        logger.error(f"Failed to generate compliance report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate compliance report")

@router.get("/compliance/audit-trail")
async def get_compliance_audit_trail(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    event_type: Optional[str] = Query(None, description="Event type filter"),
    user_id: Optional[str] = Query(None, description="User ID filter"),
    limit: int = Query(100, description="Limit results"),
    current_user: dict = Depends(get_current_user)
):
    """Get compliance audit trail (admin only)."""
    
    # Check if user is admin
    if current_user.get("role") not in ["admin", "compliance_officer", "audit_viewer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    try:
        supabase = get_supabase_client()
        
        query = supabase.table("audit_events").select("*").eq("category", "compliance").order("timestamp", desc=True).limit(limit)
        
        if start_date:
            query = query.gte("timestamp", start_date.isoformat())
        if end_date:
            query = query.lte("timestamp", end_date.isoformat())
        if event_type:
            query = query.eq("event_type", event_type)
        if user_id:
            query = query.eq("user_id", user_id)
        
        response = await query.execute()
        
        return {"audit_events": response.data}
        
    except Exception as e:
        logger.error(f"Failed to get compliance audit trail: {e}")
        raise HTTPException(status_code=500, detail="Failed to get compliance audit trail")
