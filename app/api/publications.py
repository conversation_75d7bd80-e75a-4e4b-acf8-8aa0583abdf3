# app/api/publications.py - Publications API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks, Query
from typing import List, Dict, Any, Optional
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_publication_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["publications"])


@router.get("/", response_model=List[Dict[str, Any]])
@rate_limit_user_actions()
async def get_publications(
    request: Request,
    status_filter: Optional[str] = Query(None, description="Filter by publication status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of publications to return"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user's publications"""
    user_id = current_user["id"]
    
    with monitor_operation("get_publications", user_id=user_id, status_filter=status_filter, limit=limit):
        try:
            publication_model = await get_publication_model()
            publications = await publication_model.get_user_publications(
                user_id=user_id,
                status=status_filter,
                limit=limit
            )
            
            logger.info(f"Retrieved {len(publications)} publications for user {user_id}")
            return publications
            
        except Exception as e:
            logger.error(f"Error fetching publications for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "status_filter": status_filter, "limit": limit, "endpoint": "get_publications"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch publications"
            )


@router.post("/")
@rate_limit_ai_operations()
async def create_publication(
    request: Request,
    publication_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new publication from a book"""
    user_id = current_user["id"]
    book_id = publication_data.get("book_id")
    
    if not book_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="book_id is required"
        )
    
    with monitor_operation("create_publication", user_id=user_id, book_id=book_id):
        try:
            publication_model = await get_publication_model()
            
            # Add user_id to publication data
            publication_data["user_id"] = user_id
            
            # Create the publication
            new_publication = await publication_model.create_publication(publication_data)
            
            logger.info(f"Created publication {new_publication['id']} for book {book_id} by user {user_id}")
            
            return {
                "id": new_publication["id"],
                "book_id": book_id,
                "status": new_publication["status"],
                "message": "Publication created successfully",
                "publication_data": new_publication
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating publication for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "book_id": book_id, "endpoint": "create_publication"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create publication"
            )


@router.get("/{publication_id}")
@rate_limit_user_actions()
async def get_publication(
    request: Request,
    publication_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get a specific publication by ID"""
    user_id = current_user["id"]
    
    with monitor_operation("get_publication", user_id=user_id, publication_id=publication_id):
        try:
            publication_model = await get_publication_model()
            publication = await publication_model.get_publication_by_id(publication_id, user_id)
            
            if not publication:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Publication not found"
                )
            
            logger.info(f"Retrieved publication {publication_id} for user {user_id}")
            return publication
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching publication {publication_id} for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "publication_id": publication_id, "endpoint": "get_publication"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch publication"
            )


@router.put("/{publication_id}/status")
@rate_limit_user_actions()
async def update_publication_status(
    request: Request,
    publication_id: str,
    status_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update publication status"""
    user_id = current_user["id"]
    new_status = status_data.get("status")
    
    if not new_status:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status is required"
        )
    
    with monitor_operation("update_publication_status", user_id=user_id, publication_id=publication_id, new_status=new_status):
        try:
            publication_model = await get_publication_model()
            updated_publication = await publication_model.update_publication_status(
                publication_id=publication_id,
                user_id=user_id,
                status=new_status,
                status_message=status_data.get("message", "")
            )
            
            if not updated_publication:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Publication not found"
                )
            
            logger.info(f"Updated publication {publication_id} status to {new_status} for user {user_id}")
            return updated_publication
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating publication {publication_id} status for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "publication_id": publication_id, "new_status": new_status, "endpoint": "update_publication_status"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update publication status"
            )


@router.get("/{publication_id}/metrics")
@rate_limit_user_actions()
async def get_publication_metrics(
    request: Request,
    publication_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get detailed metrics for a publication"""
    user_id = current_user["id"]
    
    with monitor_operation("get_publication_metrics", user_id=user_id, publication_id=publication_id):
        try:
            publication_model = await get_publication_model()
            metrics = await publication_model.get_publication_metrics(publication_id, user_id)
            
            if not metrics:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Publication not found"
                )
            
            logger.info(f"Retrieved metrics for publication {publication_id} for user {user_id}")
            return metrics
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching metrics for publication {publication_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "publication_id": publication_id, "endpoint": "get_publication_metrics"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch publication metrics"
            )


@router.get("/summary/statistics")
@rate_limit_user_actions()
async def get_publication_summary(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get publication summary statistics for the user"""
    user_id = current_user["id"]
    
    with monitor_operation("get_publication_summary", user_id=user_id):
        try:
            publication_model = await get_publication_model()
            summary = await publication_model.get_publication_summary(user_id)
            
            logger.info(f"Retrieved publication summary for user {user_id}")
            return summary
            
        except Exception as e:
            logger.error(f"Error fetching publication summary for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "get_publication_summary"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch publication summary"
            )


@router.delete("/{publication_id}")
@rate_limit_user_actions()
async def delete_publication(
    request: Request,
    publication_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a publication (unpublish)"""
    user_id = current_user["id"]
    
    with monitor_operation("delete_publication", user_id=user_id, publication_id=publication_id):
        try:
            publication_model = await get_publication_model()
            
            # Update status to unpublished instead of hard delete
            deleted_publication = await publication_model.update_publication_status(
                publication_id=publication_id,
                user_id=user_id,
                status="unpublished",
                status_message="Publication removed by user"
            )
            
            if not deleted_publication:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Publication not found"
                )
            
            logger.info(f"Deleted (unpublished) publication {publication_id} for user {user_id}")
            return {
                "message": "Publication successfully removed",
                "publication_id": publication_id,
                "status": "unpublished"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting publication {publication_id} for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "publication_id": publication_id, "endpoint": "delete_publication"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete publication"
            )