from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, Body
from pydantic import BaseModel, Field

from app.auth.oauth2 import (
    get_oauth_manager,
    OAuthClient,
    OAuthToken,
    require_oauth_scopes,
)

from app.auth.api_keys import (
    get_api_key_manager,
    APIKey,
    APIKeyPermission,
    require_api_key_permission,
)
from app.monitoring.audit_logger import (
    get_audit_logger,
    AuditEvent,
    AuditCategory,
    ComplianceReport,
)
from app.policies.data_retention import (
    get_retention_manager,
    RetentionPolicy,
    RetentionJob,
    RetentionAction,
    DataClassification,
)
from app.auth.supabase_auth import get_current_user
from app.schemas.user import UserResponse

router = APIRouter(prefix="/api/security", tags=["security"])


class EventSummaryDetails(TypedDict):
    count: int
    latest_timestamp: Optional[datetime]
    risk_scores: List[float]
    avg_risk_score: Optional[float]


class OAuthClientRequest(BaseModel):
    client_name: str
    redirect_uris: List[str]
    allowed_scopes: List[str]
    client_type: str = "public"


class AuthorizeRequest(BaseModel):
    client_id: str
    redirect_uri: str
    scope: str
    state: Optional[str] = None
    code_challenge: Optional[str] = None
    code_challenge_method: Optional[str] = None


class TokenRequest(BaseModel):
    client_id: str
    code: str
    redirect_uri: str
    code_verifier: Optional[str] = None


class RefreshTokenRequest(BaseModel):
    client_id: str
    refresh_token: str
    scope: Optional[str] = None


class APIKeyCreateRequest(BaseModel):
    name: str
    permissions: List[APIKeyPermission]
    expires_days: Optional[int] = None
    allowed_ips: List[str] = Field(default_factory=list)
    rate_limit_per_hour: int = 1000
    notes: Optional[str] = None


class APIKeyTemplateRequest(BaseModel):
    name: str
    template: str
    expires_days: Optional[int] = None
    allowed_ips: List[str] = Field(default_factory=list)
    rate_limit_per_hour: int = 1000


class AuditQueryRequest(BaseModel):
    user_id: Optional[str] = None
    event_type: Optional[str] = None
    category: Optional[AuditCategory] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(100, le=1000)
    offset: int = 0


class ComplianceReportRequest(BaseModel):
    start_date: datetime
    end_date: datetime
    report_type: str = "general"


class RetentionPolicyRequest(BaseModel):
    name: str
    description: str
    table_name: str
    retention_days: int = Field(..., gt=0)
    action: RetentionAction
    classification: DataClassification
    conditions: Optional[Dict[str, Any]] = None
    exclude_conditions: Optional[Dict[str, Any]] = None
    require_approval: bool = False
    max_records_per_run: int = Field(1000, gt=0)
    archive_location: Optional[str] = None


class RetentionExecutionRequest(BaseModel):
    policy_name: str
    dry_run: bool = True
    max_records: Optional[int] = None


# ===================== OAuth 2.0 =====================


@router.post("/oauth/clients", response_model=OAuthClient)
async def register_oauth_client(
    request: OAuthClientRequest, current_user: UserResponse = Depends(get_current_user)
):
    oauth_manager = await get_oauth_manager()
    return await oauth_manager.register_client(
        client_name=request.client_name,
        redirect_uris=request.redirect_uris,
        allowed_scopes=request.allowed_scopes,
        client_type=request.client_type,
    )


@router.post("/oauth/authorize")
async def oauth_authorize(
    request: AuthorizeRequest, current_user: UserResponse = Depends(get_current_user)
):
    oauth_manager = await get_oauth_manager()
    auth_code, redirect_url = await oauth_manager.authorize(
        client_id=request.client_id,
        redirect_uri=request.redirect_uri,
        scope=request.scope,
        state=request.state,
        code_challenge=request.code_challenge,
        code_challenge_method=request.code_challenge_method,
        user=current_user,
    )
    return {"authorization_code": auth_code, "redirect_url": redirect_url}


@router.post("/oauth/token", response_model=OAuthToken)
async def oauth_token(request: TokenRequest):
    oauth_manager = await get_oauth_manager()
    return await oauth_manager.exchange_code_for_token(
        client_id=request.client_id,
        code=request.code,
        redirect_uri=request.redirect_uri,
        code_verifier=request.code_verifier,
    )


@router.post("/oauth/refresh", response_model=OAuthToken)
async def oauth_refresh(request: RefreshTokenRequest):
    oauth_manager = await get_oauth_manager()
    return await oauth_manager.refresh_access_token(
        refresh_token=request.refresh_token,
        client_id=request.client_id,
        scope=request.scope,
    )


@router.post("/oauth/revoke")
async def oauth_revoke(token: str = Body(...), token_type: str = Body("access_token")):
    oauth_manager = await get_oauth_manager()
    await oauth_manager.revoke_token(token, token_type)
    return {"message": "Token revoked successfully"}


# ===================== API Keys =====================


@router.post("/api-keys")
async def create_api_key(
    request: APIKeyCreateRequest, current_user: UserResponse = Depends(get_current_user)
):
    api_key_manager = await get_api_key_manager()
    api_key, raw_key = await api_key_manager.create_api_key(
        name=request.name,
        user_id=str(current_user.id),
        permissions=request.permissions,
        created_by=str(current_user.id),
        expires_days=request.expires_days,
        allowed_ips=request.allowed_ips,
        rate_limit_per_hour=request.rate_limit_per_hour,
        notes=request.notes,
    )
    return {
        "api_key": api_key,
        "raw_key": raw_key,
        "warning": "Store this key securely. It will not be shown again.",
    }


@router.post("/api-keys/from-template")
async def create_api_key_from_template(
    request: APIKeyTemplateRequest,
    current_user: UserResponse = Depends(get_current_user),
):
    api_key_manager = await get_api_key_manager()
    api_key, raw_key = await api_key_manager.create_api_key_from_template(
        name=request.name,
        user_id=str(current_user.id),
        template=request.template,
        created_by=str(current_user.id),
        expires_days=request.expires_days,
        allowed_ips=request.allowed_ips,
        rate_limit_per_hour=request.rate_limit_per_hour,
    )
    return {
        "api_key": api_key,
        "raw_key": raw_key,
        "template_used": request.template,
        "warning": "Store this key securely. It will not be shown again.",
    }


@router.get("/api-keys", response_model=List[APIKey])
async def list_api_keys(current_user: UserResponse = Depends(get_current_user)):
    api_key_manager = await get_api_key_manager()
    keys = await api_key_manager.list_user_api_keys(str(current_user.id))
    for key in keys:
        key.key_hash = "***"
    return keys


@router.post("/api-keys/{key_id}/rotate")
async def rotate_api_key(
    key_id: str, current_user: UserResponse = Depends(get_current_user)
):
    api_key_manager = await get_api_key_manager()
    api_key, raw_key = await api_key_manager.rotate_api_key(
        key_id=key_id, user_id=str(current_user.id)
    )
    return {
        "api_key": api_key,
        "raw_key": raw_key,
        "warning": "Store this new key securely. The old key is now invalid.",
    }


@router.delete("/api-keys/{key_id}")
async def revoke_api_key(
    key_id: str,
    reason: str = Query("user_requested"),
    current_user: UserResponse = Depends(get_current_user),
):
    api_key_manager = await get_api_key_manager()
    await api_key_manager.revoke_api_key(
        key_id=key_id, user_id=str(current_user.id), reason=reason
    )
    return {"message": f"API key {key_id} revoked successfully"}


# ===================== API Key Usage =====================


@router.get("/api-keys/{key_id}/usage")
async def get_api_key_usage(
    key_id: str,
    days: int = Query(30, ge=1, le=365),
    current_user: UserResponse = Depends(get_current_user),
):
    api_key_manager = await get_api_key_manager()
    keys = await api_key_manager.list_user_api_keys(str(current_user.id))
    if not any(key.id == key_id for key in keys):
        raise HTTPException(status_code=404, detail="API key not found")
    usage = await api_key_manager.get_api_key_usage(key_id, days)
    return {
        "key_id": key_id,
        "period_days": days,
        "total_requests": len(usage),
        "usage_details": usage,
    }


@router.get("/api-keys/templates")
async def get_api_key_templates():
    api_key_manager = await get_api_key_manager()
    return {
        "templates": api_key_manager.permission_templates,
        "description": "Use these templates to quickly create API keys with predefined permissions",
    }


# ===================== Audit + Compliance =====================


@router.post("/audit/query", response_model=List[AuditEvent])
async def query_audit_events(
    request: AuditQueryRequest, current_user: UserResponse = Depends(get_current_user)
):
    audit_logger = await get_audit_logger()
    events = await audit_logger.query_events(
        user_id=request.user_id,
        event_type=request.event_type,
        category=request.category,
        start_time=request.start_time,
        end_time=request.end_time,
        limit=request.limit,
        offset=request.offset,
    )
    return events


@router.post("/audit/compliance-report", response_model=ComplianceReport)
async def generate_compliance_report(
    request: ComplianceReportRequest,
    current_user: UserResponse = Depends(get_current_user),
):
    audit_logger = await get_audit_logger()
    report = await audit_logger.generate_compliance_report(
        start_date=request.start_date,
        end_date=request.end_date,
        report_type=request.report_type,
    )
    return report


@router.get("/audit/verify/{event_id}")
async def verify_audit_integrity(
    event_id: str, current_user: UserResponse = Depends(get_current_user)
):
    audit_logger = await get_audit_logger()
    is_valid = await audit_logger.verify_integrity(event_id)
    return {
        "event_id": event_id,
        "integrity_valid": is_valid,
        "verified_at": datetime.utcnow().isoformat(),
    }


# ===================== Data Retention =====================


@router.get("/retention/policies")
async def get_retention_policies(
    current_user: UserResponse = Depends(get_current_user),
):
    retention_manager = await get_retention_manager()
    return await retention_manager.get_policy_status()


@router.post("/retention/policies")
async def create_retention_policy(
    request: RetentionPolicyRequest,
    current_user: UserResponse = Depends(get_current_user),
):
    retention_manager = await get_retention_manager()
    if request.conditions is not None and request.exclude_conditions is not None:
        raise HTTPException(
            status_code=400,
            detail="Cannot specify both conditions and exclude_conditions together",
        )
    policy = RetentionPolicy(
        name=request.name,
        description=request.description,
        table_name=request.table_name,
        retention_days=request.retention_days,
        action=request.action,
        classification=request.classification,
        conditions=request.conditions,
        exclude_conditions=request.exclude_conditions,
        require_approval=request.require_approval,
        max_records_per_run=request.max_records_per_run,
        archive_location=request.archive_location,
        created_at=datetime.utcnow(),
    )
    success = await retention_manager.add_policy(policy)
    if not success:
        raise HTTPException(status_code=400, detail="Failed to create retention policy")
    return {"message": f"Retention policy '{request.name}' created successfully"}


@router.post("/retention/execute", response_model=RetentionJob)
async def execute_retention_policy(
    request: RetentionExecutionRequest,
    current_user: UserResponse = Depends(get_current_user),
):
    retention_manager = await get_retention_manager()
    job = await retention_manager.execute_policy(
        policy_name=request.policy_name,
        dry_run=request.dry_run,
        max_records=request.max_records,
    )
    return job


@router.post("/retention/emergency-recovery")
async def emergency_data_recovery(
    archive_path: str = Body(...),
    table_name: str = Body(...),
    current_user: UserResponse = Depends(get_current_user),
):
    retention_manager = await get_retention_manager()
    success = await retention_manager.emergency_recovery(archive_path, table_name)
    if not success:
        raise HTTPException(status_code=400, detail="Emergency recovery failed")
    return {
        "message": "Emergency recovery completed successfully",
        "archive_path": archive_path,
        "table_name": table_name,
    }


# ===================== Security Monitoring =====================


@router.get("/monitoring/security-events")
async def get_security_events(
    hours: int = Query(24, ge=1, le=168),
    current_user: UserResponse = Depends(get_current_user),
):
    audit_logger = await get_audit_logger()
    start_time = datetime.utcnow() - timedelta(hours=hours)
    events = await audit_logger.query_events(
        category=AuditCategory.SECURITY_EVENT, start_time=start_time, limit=500
    )
    summary: Dict[str, EventSummaryDetails] = {}
    for event in events:
        etype = event.event_type
        if etype not in summary:
            summary[etype] = {
                "count": 0,
                "latest_timestamp": None,
                "risk_scores": [],
                "avg_risk_score": None,
            }
        summary[etype]["count"] += 1
        summary[etype]["risk_scores"].append(event.risk_score)
        current_latest = summary[etype]["latest_timestamp"]
        if current_latest is None or (
            event.timestamp is not None and event.timestamp > current_latest
        ):
            summary[etype]["latest_timestamp"] = event.timestamp
    for details in summary.values():
        if details["risk_scores"]:
            details["avg_risk_score"] = sum(details["risk_scores"]) / len(
                details["risk_scores"]
            )
        else:
            details["avg_risk_score"] = None
        details.pop("risk_scores")
    return {
        "period_hours": hours,
        "total_events": len(events),
        "event_summary": summary,
        "events": events[:50],
    }


@router.get("/monitoring/threats")
async def get_threat_analysis(current_user: UserResponse = Depends(get_current_user)):
    """Get real-time threat analysis from audit logs"""
    try:
        audit_logger = await get_audit_logger()
        
        # Analyze recent security events for threat assessment
        hours_back = 24
        start_time = datetime.utcnow() - timedelta(hours=hours_back)
        security_events = await audit_logger.query_events(
            category=AuditCategory.SECURITY_EVENT, 
            start_time=start_time, 
            limit=1000
        )
        
        # Count different types of threats
        failed_logins = sum(1 for e in security_events if "login_failed" in e.event_type)
        suspicious_api_calls = sum(1 for e in security_events if e.risk_score > 7.0)
        blocked_ips = len(set(e.metadata.get("ip_address") for e in security_events 
                              if e.metadata and e.metadata.get("blocked")))
        
        # Calculate threat level
        total_threats = failed_logins + suspicious_api_calls + blocked_ips
        if total_threats > 50:
            threat_level = "high"
        elif total_threats > 20:
            threat_level = "medium"
        elif total_threats > 5:
            threat_level = "low"
        else:
            threat_level = "minimal"
        
        # Generate recommendations based on analysis
        recommendations = []
        if failed_logins > 10:
            recommendations.append("Consider implementing stronger authentication policies")
        if suspicious_api_calls > 5:
            recommendations.append("Review API access patterns and rate limiting")
        if blocked_ips > 0:
            recommendations.append(f"Monitor {blocked_ips} blocked IP addresses")
        
        if not recommendations:
            recommendations = [
                "Continue monitoring for suspicious activities",
                "Review API key usage patterns", 
                "Ensure all security policies are up to date"
            ]
        
        return {
            "threat_level": threat_level,
            "active_threats": total_threats,
            "blocked_ips": blocked_ips,
            "suspicious_activities": suspicious_api_calls,
            "failed_login_attempts": failed_logins,
            "analysis_period_hours": hours_back,
            "last_updated": datetime.utcnow().isoformat(),
            "recommendations": recommendations,
        }
        
    except Exception as e:
        # Fallback to safe defaults if analysis fails
        return {
            "threat_level": "unknown",
            "active_threats": 0,
            "blocked_ips": 0,
            "suspicious_activities": 0,
            "failed_login_attempts": 0,
            "analysis_period_hours": 24,
            "last_updated": datetime.utcnow().isoformat(),
            "recommendations": [
                "Threat analysis temporarily unavailable",
                "Continue monitoring manually",
                "Check system logs for anomalies"
            ],
            "error": "Analysis service temporarily unavailable"
        }


# ===================== Health Check =====================


@router.get("/health")
async def security_system_health():
    try:
        oauth_manager = await get_oauth_manager()
        api_key_manager = await get_api_key_manager()
        audit_logger = await get_audit_logger()
        retention_manager = await get_retention_manager()
        return {
            "status": "healthy",
            "components": {
                "oauth2": "healthy",
                "api_keys": "healthy",
                "audit_logging": "healthy",
                "data_retention": "healthy",
            },
            "timestamp": datetime.utcnow().isoformat(),
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Security system health check failed: {str(e)}"
        )
