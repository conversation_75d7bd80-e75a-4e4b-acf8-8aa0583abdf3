# app/api/trends.py - Trends API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks, Query
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_trend_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["trends"])


@router.get("/", response_model=List[Dict[str, Any]])
@rate_limit_user_actions()
async def get_trends(
    request: Request,
    limit: int = Query(50, ge=1, le=100, description="Maximum number of trends to return"),
    category: Optional[str] = Query(None, description="Filter by category"),
    include_user_trends: bool = Query(False, description="Include user-specific trends"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get trending topics for book generation"""
    user_id = current_user["id"] if include_user_trends else None
    
    with monitor_operation("get_trends", user_id=current_user["id"], limit=limit, category=category):
        try:
            trend_model = await get_trend_model()
            trends = await trend_model.get_active_trends(limit=limit, user_id=user_id)
            
            # Filter by category if specified
            if category:
                trends = [t for t in trends if t.get("category", "").lower() == category.lower()]
            
            logger.info(f"Retrieved {len(trends)} trends for user {current_user['id']}")
            return trends
            
        except Exception as e:
            logger.error(f"Error fetching trends for user {current_user['id']}: {e}")
            capture_exception(e, extra={"user_id": current_user["id"], "limit": limit, "category": category, "endpoint": "get_trends"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch trends"
            )


@router.post("/analyze")
@rate_limit_ai_operations()
async def analyze_trends(
    request: Request,
    trend_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new trend analysis record"""
    user_id = current_user["id"]
    
    with monitor_operation("analyze_trends", user_id=user_id, trend_data=trend_data):
        try:
            # Validate required fields
            if not trend_data.get("keyword"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Keyword is required for trend analysis"
                )
            
            trend_model = await get_trend_model()
            
            # Add user_id to trend_data
            trend_data["user_id"] = user_id
            
            # Create the trend analysis record
            new_trend = await trend_model.create_trend_analysis(trend_data)
            
            # Schedule background task to update trend scores if needed
            background_tasks.add_task(trend_model.update_trend_scores, user_id)
            
            logger.info(f"Created trend analysis for keyword '{trend_data.get('keyword')}' by user {user_id}")
            
            return {
                "message": f"Trend analysis created for keyword: {trend_data.get('keyword')}",
                "status": "completed",
                "trend_id": new_trend.get("id"),
                "trend_score": new_trend.get("trend_score", 0),
                "competition_level": new_trend.get("competition_level", "unknown")
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating trend analysis for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "trend_data": trend_data, "endpoint": "analyze_trends"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create trend analysis"
            )


@router.get("/market/{category}")
@rate_limit_user_actions()
async def get_market_analysis(
    request: Request,
    category: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get market competition analysis for a specific category"""
    user_id = current_user["id"]
    
    with monitor_operation("get_market_analysis", user_id=user_id, category=category):
        try:
            trend_model = await get_trend_model()
            market_data = await trend_model.get_market_competition(category)
            
            logger.info(f"Market analysis retrieved for category '{category}' by user {user_id}")
            return market_data
            
        except Exception as e:
            logger.error(f"Error fetching market analysis for category '{category}': {e}")
            capture_exception(e, extra={"user_id": user_id, "category": category, "endpoint": "get_market_analysis"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch market analysis"
            )


@router.post("/keywords/analyze")
@rate_limit_ai_operations()
async def analyze_keywords(
    request: Request,
    keywords_data: Dict[str, List[str]],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Analyze trends for multiple keywords"""
    user_id = current_user["id"]
    keywords = keywords_data.get("keywords", [])
    
    # Validate input
    if not keywords or len(keywords) > 20:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Please provide 1-20 keywords for analysis"
        )
    
    with monitor_operation("analyze_keywords", user_id=user_id, keyword_count=len(keywords)):
        try:
            trend_model = await get_trend_model()
            analysis_results = await trend_model.analyze_keyword_trends(keywords, user_id)
            
            logger.info(f"Keyword analysis completed for {len(keywords)} keywords by user {user_id}")
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error analyzing keywords for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "keywords": keywords, "endpoint": "analyze_keywords"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to analyze keywords"
            )


@router.get("/categories")
@rate_limit_user_actions()
async def get_trending_categories(
    request: Request,
    limit: int = Query(10, ge=1, le=25, description="Maximum number of categories to return"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get trending categories based on recent activity"""
    user_id = current_user["id"]
    
    with monitor_operation("get_trending_categories", user_id=user_id, limit=limit):
        try:
            trend_model = await get_trend_model()
            trending_categories = await trend_model.get_trending_categories(limit=limit)
            
            logger.info(f"Retrieved {len(trending_categories)} trending categories for user {user_id}")
            return {
                "categories": trending_categories,
                "total": len(trending_categories),
                "analysis_period": "14 days",
                "last_updated": trending_categories[0].get("last_updated") if trending_categories else None
            }
            
        except Exception as e:
            logger.error(f"Error fetching trending categories for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "limit": limit, "endpoint": "get_trending_categories"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch trending categories"
            )


@router.post("/update-scores")
@rate_limit_ai_operations()
async def update_trend_scores(
    request: Request,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Manually trigger trend score updates (admin or background task)"""
    user_id = current_user["id"]
    user_role = current_user.get("role", "user")
    
    # Only allow admins to manually trigger updates
    if user_role not in ["admin", "super_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can trigger trend score updates"
        )
    
    with monitor_operation("update_trend_scores", user_id=user_id):
        try:
            trend_model = await get_trend_model()
            
            # Run the update in background
            background_tasks.add_task(trend_model.update_trend_scores)
            
            logger.info(f"Trend score update triggered by admin user {user_id}")
            
            return {
                "message": "Trend score update has been scheduled",
                "status": "processing",
                "triggered_by": user_id,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error triggering trend score update by user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "update_trend_scores"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to trigger trend score update"
            )