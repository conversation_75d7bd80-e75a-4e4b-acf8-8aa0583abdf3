# app/api/supabase_books.py - Books API using Supabase Models

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request
from typing import List, Optional, Dict, Any

from app.auth.supabase_auth import get_current_user, get_current_user_optional
from app.models.supabase_models import get_book_model, get_feedback_model
from app.schemas.book import ManuscriptGenerationRequest, BookCreate
from app.agents.pydantic_ai_manager import execute_agent
from app.agents.pydantic_ai_common import ExecutionStatus
from app.monitoring.monitoring_setup import get_logger, monitor_operation, log_user_action, capture_exception
from app.middleware.security_middleware import rate_limit_ai_operations, rate_limit_user_actions

logger = get_logger(__name__)

router = APIRouter()

# Test endpoint for debugging
@router.get("/test")
async def test_books_endpoint():
    """Simple test endpoint"""
    return {"message": "Books API is working"}

# =====================================
# BOOK CRUD OPERATIONS
# =====================================

@router.get("/public/recent", response_model=List[Dict[str, Any]])
async def get_recent_public_books(
    limit: int = 6,
    status: Optional[str] = None
):
    """Get recent public books (no authentication required)"""
    with monitor_operation("get_recent_public_books", limit=limit, status=status):
        try:
            book_model = await get_book_model()
            # Get recent published books - we'll query without user_id to get all books
            client = await book_model._get_client()
            if client is None:
                raise HTTPException(
                    status_code=503, detail="Database connection unavailable"
                )
            query = client.table("books").select("*")

            if status:
                query = query.eq("status", status)
            
            logger.info(f"Querying books with status: {status}")

            # Order by created_at descending to get recent books
            query = query.order("created_at", desc=True).limit(limit)

            response = query.execute()
            books = response.data if response.data else []

            # Remove sensitive information for public API
            public_books = []
            for book in books:
                public_book = {
                    "id": book.get("id"),
                    "title": book.get("title"),
                    "description": book.get("description"),
                    "genre": book.get("genre"),
                    "cover_image_url": book.get("cover_image_url"),
                    "status": book.get("status"),
                    "created_at": book.get("created_at"),
                    "published_date": book.get("published_date"),
                }
                public_books.append(public_book)

            logger.info(f"Retrieved {len(public_books)} recent public books")
            return public_books

        except Exception as e:
            capture_exception(e, {'limit': limit, 'status': status})
            logger.error(f"Failed to get recent public books: {e}")
            raise HTTPException(status_code=500, detail="Failed to retrieve books")

@router.get("/", response_model=List[Dict[str, Any]])
@router.get("", response_model=List[Dict[str, Any]])
async def get_books(
    status: Optional[str] = None,
    limit: int = 50
):
    """Get books - returns test books for now"""
    try:
        logger.info(f"Getting books with status={status}, limit={limit}")
        
        # Return test data for now
        return [
            {
                "id": "test-book-1",
                "title": "Sample Book 1",
                "description": "This is a test book",
                "genre": "Fiction",
                "cover_image_url": None,
                "status": "published",
                "created_at": "2025-01-01T00:00:00Z",
                "published_date": "2025-01-01T00:00:00Z",
            },
            {
                "id": "test-book-2", 
                "title": "Sample Book 2",
                "description": "Another test book",
                "genre": "Non-Fiction",
                "cover_image_url": None,
                "status": "draft",
                "created_at": "2025-01-02T00:00:00Z",
                "published_date": None,
            }
        ]
        
    except Exception as e:
        logger.error(f"Error in get_books: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


@router.post("/", response_model=Dict[str, Any])
@rate_limit_user_actions()
async def create_book(
    request: Request,
    book_data: BookCreate, 
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new book"""
    try:
        book_model = await get_book_model()

        # Prepare book data
        book_dict = {
            "user_id": current_user["id"],
            "title": book_data.title,
            "category": book_data.category,
            "description": book_data.description,
            "target_audience": book_data.target_audience,
            "writing_style": book_data.writing_style,
            "ai_provider": book_data.ai_provider,
            "status": "draft",
            "word_count": 0,
            "chapter_count": 0
        }

        book = await book_model.create_book(book_dict)

        logger.info(f"Created new book {book['id']} for user {current_user['id']}")
        return book

    except Exception as e:
        logger.error(f"Error creating book: {e}")
        raise HTTPException(status_code=500, detail="Failed to create book")


@router.get("/{book_id}", response_model=Dict[str, Any])
async def get_book(
    book_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get a specific book"""
    try:
        book_model = await get_book_model()
        book = await book_model.get_book_by_id(book_id)
        
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        
        # Check ownership
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")
        
        logger.info(f"Retrieved book {book_id} for user {current_user['id']}")
        return book
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve book")

@router.put("/{book_id}", response_model=Dict[str, Any])
async def update_book(
    book_id: str,
    book_data: BookCreate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update an existing book"""
    try:
        book_model = await get_book_model()
        
        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Check if book can be updated (not published)
        if book["status"] == "published":
            raise HTTPException(status_code=400, detail="Cannot update published book")
        
        # Prepare updates
        updates = {
            "title": book_data.title,
            "category": book_data.category,
            "description": book_data.description,
            "target_audience": book_data.target_audience,
            "writing_style": book_data.writing_style,
            "ai_provider": book_data.ai_provider
        }
        
        updated_book = await book_model.update_book(book_id, updates)
        
        logger.info(f"Updated book {book_id} for user {current_user['id']}")
        return updated_book
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update book")

@router.delete("/{book_id}")
async def delete_book(
    book_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a book"""
    try:
        book_model = await get_book_model()

        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")

        await book_model.delete_book(book_id)

        logger.info(f"Deleted book {book_id} for user {current_user['id']}")
        return {"message": "Book deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete book")

# =====================================
# MANUSCRIPT GENERATION
# =====================================


@router.post("/generate", response_model=Dict[str, Any])
@rate_limit_ai_operations()
async def generate_manuscript(
    request: Request,
    manuscript_request: ManuscriptGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Generate a new manuscript based on trend data"""
    try:
        book_model = await get_book_model()

        # Create database record
        book_dict = {
            "user_id": current_user["id"],
            "title": manuscript_request.title or "Generating...",
            "category": manuscript_request.category,
            "target_audience": manuscript_request.target_audience,
            "writing_style": manuscript_request.style,
            "ai_provider": manuscript_request.ai_provider,
            "status": "generating",
            "generation_config": manuscript_request.model_dump(),
            "target_length": manuscript_request.target_length,
            "output_formats": manuscript_request.output_formats
        }

        book = await book_model.create_book(book_dict)

        # Start background generation task
        async def generate_manuscript_with_agent():
            try:
                task_data = {
                    'trend_data': manuscript_request.trend_data,
                    'style': manuscript_request.style,
                    'target_audience': manuscript_request.target_audience,
                    'target_length': manuscript_request.target_length,
                    'category': manuscript_request.category,
                    'title': manuscript_request.title,
                    'industry_focus': manuscript_request.industry_focus
                }

                result = await execute_agent("manuscript_generator", task_data, current_user["id"])

                # Update book status based on result
                if result and result.status == ExecutionStatus.SUCCESS:
                    updates = {
                        "status": "awaiting_approval",
                        "word_count": result.data.get('word_count', 0) if result.data else 0,
                        "chapter_count": result.data.get('chapter_count', 0) if result.data else 0,
                        "content": result.data.get('content', '') if result.data else '',
                        "generation_metadata": result.metadata or {}
                    }
                else:
                    updates = {
                        "status": "failed",
                        "rejection_reason": (
                            result.error_message if result else "Generation failed"
                        ),
                    }

                await book_model.update_book(book["id"], updates)
                logger.info(f"Manuscript generation completed for book {book['id']}")

            except Exception as e:
                logger.error(f"Manuscript generation failed for book {book['id']}: {e}")
                await book_model.update_book(book["id"], {
                    "status": "failed",
                    "rejection_reason": str(e)
                })

        background_tasks.add_task(generate_manuscript_with_agent)

        logger.info(f"Started manuscript generation for book {book['id']}")

        return {
            "message": "Manuscript generation started",
            "book_id": book["id"],
            "status": "generating"
        }

    except Exception as e:
        logger.error(f"Error starting manuscript generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start manuscript generation")


# =====================================
# BOOK APPROVAL WORKFLOW
# =====================================

@router.post("/{book_id}/approve")
async def approve_manuscript(
    book_id: str,
    approval_time: float = 0.0,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Approve manuscript with VERL feedback collection"""
    try:
        book_model = await get_book_model()
        feedback_model = await get_feedback_model()

        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check status
        if book["status"] != "awaiting_approval":
            raise HTTPException(
                status_code=400, 
                detail=f"Book status is {book['status']}, not awaiting_approval"
            )

        # Update book status
        await book_model.approve_book(book_id, current_user["id"])

        # Record feedback for VERL training
        await feedback_model.record_feedback({
            "user_id": current_user["id"],
            "book_id": book_id,
            "metric_type": "user_approval",
            "metric_value": 1.0,
            "approved": True,
            "approval_time_seconds": approval_time,
            "metric_context": {
                "book_title": book["title"],
                "book_category": book["category"],
                "word_count": book["word_count"]
            }
        })

        logger.info(f"Book {book_id} approved by user {current_user['id']}")

        return {"message": "Manuscript approved", "book_id": book_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to approve manuscript")

@router.post("/{book_id}/reject")
async def reject_manuscript(
    book_id: str,
    reason: Optional[str] = None,
    rejection_time: float = 0.0,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Reject manuscript with VERL feedback collection"""
    try:
        book_model = await get_book_model()
        feedback_model = await get_feedback_model()

        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")

        # Update book status
        await book_model.reject_book(book_id, reason or "No reason provided")

        # Record rejection feedback
        await feedback_model.record_feedback({
            "user_id": current_user["id"],
            "book_id": book_id,
            "metric_type": "user_approval",
            "metric_value": 0.0,
            "approved": False,
            "approval_time_seconds": rejection_time,
            "rejection_reason": reason,
            "metric_context": {
                "book_title": book["title"],
                "book_category": book["category"],
                "word_count": book["word_count"]
            }
        })

        logger.info(f"Book {book_id} rejected by user {current_user['id']}")

        return {"message": "Manuscript rejected", "book_id": book_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rejecting book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to reject manuscript")

# =====================================
# COVER GENERATION
# =====================================

@router.post("/{book_id}/covers/generate")
async def generate_covers(
    book_id: str,
    background_tasks: BackgroundTasks,
    style: str = "modern",
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Generate covers for an approved manuscript"""
    try:
        book_model = await get_book_model()

        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check status
        if book["status"] != "approved":
            raise HTTPException(
                status_code=400, 
                detail=f"Book status is {book['status']}, not approved"
            )

        # Start cover generation using PydanticAI agent
        async def generate_cover_with_agent():
            try:
                task_data = {
                    'book_title': book["title"],
                    'author_name': current_user.get("full_name", "Author"),
                    'genre': book["category"],
                    'target_audience': book["target_audience"],
                    'style_preferences': {"style": style}
                }

                result = await execute_agent("cover_designer", task_data, current_user["id"])

                # Update book with cover information
                if result and result.status == ExecutionStatus.SUCCESS:
                    cover_data = result.data or {}
                    await book_model.update_book(book_id, {
                        "cover_paths": cover_data.get('file_paths', [])
                    })
                    logger.info(f"Cover generation completed for book {book_id}")
                else:
                    logger.error(
                        f"Cover generation failed for book {book_id}: {result.error_message if result else 'Unknown error'}"
                    )

            except Exception as e:
                logger.error(f"Cover generation failed for book {book_id}: {e}")

        background_tasks.add_task(generate_cover_with_agent)

        logger.info(f"Started cover generation for book {book_id}")

        return {
            "message": "Cover generation started",
            "book_id": book_id,
            "style": style
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting cover generation for book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to start cover generation")

# =====================================
# BOOK ANALYTICS
# =====================================

@router.get("/{book_id}/analytics")
async def get_book_analytics(
    book_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get analytics for a specific book"""
    try:
        book_model = await get_book_model()
        
        # Check if book exists and user has access
        book = await book_model.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        if book["user_id"] != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get book performance data (would be expanded with sales data, etc.)
        analytics = {
            "book_id": book_id,
            "title": book["title"],
            "status": book["status"],
            "word_count": book["word_count"],
            "chapter_count": book["chapter_count"],
            "quality_score": book.get("quality_score"),
            "created_at": book["created_at"],
            "reading_time_minutes": book.get("reading_time_minutes"),
            # Additional analytics would come from sales_data table
            "total_sales": 0,  # Placeholder
            "total_revenue": 0.0,  # Placeholder
            "avg_rating": 0.0  # Placeholder
        }
        
        logger.info(f"Analytics retrieved for book {book_id}")
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analytics for book {book_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get book analytics")
