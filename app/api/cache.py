"""
Cache management API endpoints.

This module provides API endpoints for:
- Cache statistics and monitoring
- Manual cache invalidation
- Cache warming operations
- CDN asset management
- Cache configuration updates
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Body
from pydantic import BaseModel, Field

from app.cache import (
    get_cache,
    get_invalidation_manager, 
    get_cdn_manager,
    get_supabase_cache_manager,
    invalidate_user_cache,
    invalidate_book_cache,
    invalidate_analytics_cache
)
from app.middleware.cache_middleware import APICacheMiddleware
from app.auth.supabase_auth import get_current_user
from app.schemas.user import UserResponse

router = APIRouter(prefix="/api/cache", tags=["cache"])


class CacheStats(BaseModel):
    """Cache statistics response model."""
    redis_stats: Dict[str, Any]
    api_cache_stats: Dict[str, Any]
    supabase_cache_stats: Dict[str, Any]
    invalidation_stats: Dict[str, Any]


class InvalidateRequest(BaseModel):
    """Cache invalidation request model."""
    pattern: Optional[str] = Field(None, description="Cache key pattern to invalidate")
    tags: Optional[List[str]] = Field(None, description="Cache tags to invalidate")
    immediate: bool = Field(True, description="Whether to invalidate immediately")


class CacheWarmRequest(BaseModel):
    """Cache warming request model."""
    endpoints: List[str] = Field(..., description="Endpoints to warm")
    include_common_queries: bool = Field(True, description="Include common query patterns")


@router.get("/stats", response_model=CacheStats)
async def get_cache_stats(current_user: UserResponse = Depends(get_current_user)):
    """Get comprehensive cache statistics."""
    try:
        # Get Redis cache stats
        cache = await get_cache()
        redis_stats = await cache.get_stats()
        
        # Get API cache stats (would need to access middleware instance)
        api_cache_stats = {
            "message": "API cache stats available in middleware",
            "note": "Access via /api/monitoring/performance for full stats"
        }
        
        # Get Supabase cache stats
        supabase_cache = await get_supabase_cache_manager()
        supabase_stats = await supabase_cache.get_cache_stats()
        
        # Get invalidation stats
        invalidation_manager = await get_invalidation_manager()
        invalidation_stats = await invalidation_manager.get_invalidation_stats()
        
        return CacheStats(
            redis_stats=redis_stats.dict(),
            api_cache_stats=api_cache_stats,
            supabase_cache_stats=supabase_stats,
            invalidation_stats=invalidation_stats
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")


@router.post("/invalidate")
async def invalidate_cache(
    request: InvalidateRequest,
    current_user: UserResponse = Depends(get_current_user)
):
    """Manually invalidate cache entries."""
    try:
        invalidation_manager = await get_invalidation_manager()
        invalidated_count = 0
        
        # Invalidate by pattern
        if request.pattern:
            if request.pattern == "all":
                # Clear all caches
                cache = await get_cache()
                supabase_cache = await get_supabase_cache_manager()
                
                await cache.redis_client.flushdb() if cache.redis_client else None
                await supabase_cache.clear_all_cache()
                invalidated_count = "all"
            else:
                # Invalidate specific pattern
                await invalidation_manager.invalidate_pattern(request.pattern)
                invalidated_count += 1
        
        # Invalidate by tags
        if request.tags:
            await invalidation_manager.invalidate_tags(set(request.tags), request.immediate)
            invalidated_count += len(request.tags)
        
        return {
            "message": f"Successfully invalidated cache",
            "pattern": request.pattern,
            "tags": request.tags,
            "invalidated_count": invalidated_count,
            "immediate": request.immediate
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to invalidate cache: {str(e)}")


@router.post("/invalidate/user/{user_id}")
async def invalidate_user_cache_endpoint(
    user_id: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """Invalidate all cache entries for a specific user."""
    try:
        await invalidate_user_cache(user_id)
        
        return {
            "message": f"Successfully invalidated cache for user {user_id}",
            "user_id": user_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to invalidate user cache: {str(e)}")


@router.post("/invalidate/book/{book_id}")
async def invalidate_book_cache_endpoint(
    book_id: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """Invalidate all cache entries for a specific book."""
    try:
        await invalidate_book_cache(book_id)
        
        return {
            "message": f"Successfully invalidated cache for book {book_id}",
            "book_id": book_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to invalidate book cache: {str(e)}")


@router.post("/invalidate/analytics")
async def invalidate_analytics_cache_endpoint(
    current_user: UserResponse = Depends(get_current_user)
):
    """Invalidate all analytics cache entries."""
    try:
        await invalidate_analytics_cache()
        
        return {
            "message": "Successfully invalidated analytics cache"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to invalidate analytics cache: {str(e)}")


@router.post("/warm")
async def warm_cache(
    request: CacheWarmRequest,
    current_user: UserResponse = Depends(get_current_user)
):
    """Warm cache for specified endpoints."""
    try:
        # This would implement cache warming logic
        # For now, return a placeholder response
        
        warmed_endpoints = []
        
        if request.include_common_queries:
            # Warm common Supabase queries
            supabase_cache = await get_supabase_cache_manager()
            
            # Warm common patterns for each table
            common_patterns = {
                "users": [
                    {"select": "*", "order": "-created_at", "limit": 100},
                    {"select": "count"},
                ],
                "books": [
                    {"select": "*", "order": "-created_at", "limit": 50},
                    {"select": "*", "filters": {"status": "published"}},
                ],
                "publications": [
                    {"select": "*", "order": "-published_date", "limit": 30},
                ]
            }
            
            for table, patterns in common_patterns.items():
                await supabase_cache.warm_cache(table, patterns)
                warmed_endpoints.append(f"supabase:{table}")
        
        # Warm specific endpoints
        for endpoint in request.endpoints:
            # This would implement endpoint-specific warming
            warmed_endpoints.append(endpoint)
        
        return {
            "message": "Cache warming completed",
            "warmed_endpoints": warmed_endpoints,
            "total_warmed": len(warmed_endpoints)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to warm cache: {str(e)}")


@router.get("/cdn/assets")
async def get_cdn_assets(
    pattern: Optional[str] = Query(None, description="Asset pattern to filter"),
    current_user: UserResponse = Depends(get_current_user)
):
    """Get CDN asset manifest."""
    try:
        cdn_manager = await get_cdn_manager()
        manifest = cdn_manager.get_manifest()
        
        if pattern:
            # Filter assets by pattern
            filtered_manifest = {
                path: info for path, info in manifest.items()
                if pattern in path
            }
            manifest = filtered_manifest
        
        return {
            "assets": {
                path: {
                    "size": info.size,
                    "etag": info.etag,
                    "content_type": info.content_type,
                    "compressed": info.compressed,
                    "cache_duration": info.cache_duration,
                    "url": cdn_manager.get_asset_url(path)
                }
                for path, info in manifest.items()
            },
            "total_assets": len(manifest)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get CDN assets: {str(e)}")


@router.post("/cdn/rebuild")
async def rebuild_cdn_manifest(current_user: UserResponse = Depends(get_current_user)):
    """Rebuild CDN asset manifest."""
    try:
        cdn_manager = await get_cdn_manager()
        await cdn_manager.initialize()  # Rebuilds manifest
        
        manifest = cdn_manager.get_manifest()
        
        return {
            "message": "CDN manifest rebuilt successfully",
            "total_assets": len(manifest),
            "timestamp": "now"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to rebuild CDN manifest: {str(e)}")


@router.post("/cdn/optimize")
async def optimize_cdn_assets(
    quality: int = Query(85, ge=1, le=100, description="Image quality for optimization"),
    current_user: UserResponse = Depends(get_current_user)
):
    """Optimize CDN assets (images, compression, etc.)."""
    try:
        cdn_manager = await get_cdn_manager()
        
        # Optimize images
        await cdn_manager.optimize_images(quality=quality)
        
        # Clean up old versions
        await cdn_manager.cleanup_old_versions(keep_versions=3)
        
        manifest = cdn_manager.get_manifest()
        
        return {
            "message": "CDN assets optimized successfully",
            "total_assets": len(manifest),
            "image_quality": quality,
            "optimizations_applied": [
                "image_compression",
                "old_version_cleanup"
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to optimize CDN assets: {str(e)}")


@router.get("/health")
async def cache_health_check():
    """Check health of all cache systems."""
    try:
        health_status = {
            "redis": "unknown",
            "invalidation_manager": "unknown", 
            "cdn_manager": "unknown",
            "supabase_cache": "unknown"
        }
        
        # Check Redis
        try:
            cache = await get_cache()
            if cache.redis_client:
                await cache.redis_client.ping()
                health_status["redis"] = "healthy"
            else:
                health_status["redis"] = "unavailable"
        except Exception:
            health_status["redis"] = "unhealthy"
        
        # Check invalidation manager
        try:
            invalidation_manager = await get_invalidation_manager()
            health_status["invalidation_manager"] = "healthy"
        except Exception:
            health_status["invalidation_manager"] = "unhealthy"
        
        # Check CDN manager
        try:
            cdn_manager = await get_cdn_manager()
            health_status["cdn_manager"] = "healthy"
        except Exception:
            health_status["cdn_manager"] = "unhealthy"
        
        # Check Supabase cache
        try:
            supabase_cache = await get_supabase_cache_manager()
            health_status["supabase_cache"] = "healthy"
        except Exception:
            health_status["supabase_cache"] = "unhealthy"
        
        overall_healthy = all(status in ["healthy", "unavailable"] for status in health_status.values())
        
        return {
            "status": "healthy" if overall_healthy else "degraded",
            "components": health_status,
            "timestamp": "now"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check cache health: {str(e)}")