# app/api/analytics.py - Analytics API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_analytics_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions

logger = get_logger(__name__)

router = APIRouter(tags=["analytics"])


@router.get("/dashboard")
@rate_limit_user_actions()
async def get_dashboard_analytics(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get dashboard analytics data"""
    user_id = current_user["id"]
    
    with monitor_operation("get_dashboard_analytics", user_id=user_id):
        try:
            analytics_model = await get_analytics_model()
            analytics_data = await analytics_model.get_dashboard_analytics(user_id)
            
            logger.info(f"Dashboard analytics retrieved for user {user_id}")
            return analytics_data
            
        except Exception as e:
            logger.error(f"Error fetching dashboard analytics for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "dashboard_analytics"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch dashboard analytics"
            )


@router.get("/users/{user_id}")
@rate_limit_user_actions()
async def get_user_analytics(
    request: Request,
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get analytics for a specific user"""
    requesting_user_id = current_user["id"]
    
    # Authorization check: Users can only view their own analytics unless they have admin role
    if user_id != requesting_user_id:
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "super_admin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only view your own analytics"
            )
    
    with monitor_operation("get_user_analytics", user_id=requesting_user_id, target_user_id=user_id):
        try:
            analytics_model = await get_analytics_model()
            user_analytics = await analytics_model.get_user_analytics(user_id)
            
            logger.info(f"User analytics retrieved for user {user_id} by {requesting_user_id}")
            return user_analytics
            
        except Exception as e:
            logger.error(f"Error fetching user analytics for {user_id}: {e}")
            capture_exception(e, extra={"user_id": requesting_user_id, "target_user_id": user_id, "endpoint": "user_analytics"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch user analytics"
            )


@router.get("/growth/{period}")
@rate_limit_user_actions()
async def get_growth_metrics(
    request: Request,
    period: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get growth metrics for a specific period (week, month)"""
    user_id = current_user["id"]
    
    # Validate period parameter
    if period not in ["week", "month"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Period must be 'week' or 'month'"
        )
    
    with monitor_operation("get_growth_metrics", user_id=user_id, period=period):
        try:
            analytics_model = await get_analytics_model()
            growth_metrics = await analytics_model.calculate_growth_metrics(user_id, period)
            
            logger.info(f"Growth metrics ({period}) retrieved for user {user_id}")
            return growth_metrics
            
        except Exception as e:
            logger.error(f"Error fetching growth metrics for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "period": period, "endpoint": "growth_metrics"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch growth metrics"
            )