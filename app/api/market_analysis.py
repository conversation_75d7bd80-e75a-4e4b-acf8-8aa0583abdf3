# app/api/market_analysis.py - Market Analysis API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_market_analysis_model
from app.prediction.market_analyzer import MarketIntelligenceEngine
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["market-analysis"])

market_engine = MarketIntelligenceEngine()


@router.get("/")
@rate_limit_user_actions()
async def list_market_analyses(
    request: Request,
    limit: int = Query(10, ge=1, le=100, description="Number of analyses to return"),
    offset: int = Query(0, ge=0, description="Number of analyses to skip"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """List user's market analyses with pagination"""
    user_id = current_user["id"]
    
    with monitor_operation("list_market_analyses", user_id=user_id):
        try:
            market_model = get_market_analysis_model()
            analyses = await market_model.get_user_market_analyses(user_id, limit=limit, offset=offset)
            
            logger.info(f"Retrieved {len(analyses)} market analyses for user {user_id}")
            return {
                "analyses": analyses,
                "total": len(analyses),
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            logger.error(f"Error retrieving market analyses for user {user_id}: {e}")
            capture_exception(e, extra={"user_id": user_id, "endpoint": "list_market_analyses"})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve market analyses"
            )


@router.post("/")
@rate_limit_ai_operations()
async def create_market_analysis(
    request: Request,
    analysis_request: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new market analysis for a category"""
    user_id = current_user["id"]
    
    # Validate required fields
    category = analysis_request.get("category")
    if not category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Category is required for market analysis"
        )
    
    with monitor_operation("create_market_analysis", user_id=user_id, category=category):
        try:
            # Perform market analysis using the intelligence engine
            force_refresh = analysis_request.get("force_refresh", False)
            market_conditions = await market_engine.analyze_market_conditions(
                category=category,
                force_refresh=force_refresh
            )
            
            # Create analysis data for database
            analysis_data = {
                "category": category,
                "analysis_date": datetime.utcnow().isoformat(),
                "competition_density": market_conditions.get("competition_density", 0),
                "market_saturation_score": market_conditions.get("market_saturation", 0),
                "opportunity_score": market_conditions.get("market_opportunity_score", 0),
                "average_price": market_conditions.get("avg_price", 0),
                "seasonal_multipliers": {
                    str(i): market_conditions.get("current_seasonal_factor", 1.0) 
                    for i in range(1, 13)
                },
                "trending_keywords": market_conditions.get("trending_keywords", {}),
                "emerging_subtopics": market_conditions.get("emerging_subtopics", []),
                "declining_topics": market_conditions.get("declining_topics", []),
                "price_competition": market_conditions.get("price_competition", 0),
                "quality_bar": market_conditions.get("quality_bar", 0),
                "trend_momentum": market_conditions.get("trend_momentum", 0),
                "seasonal_opportunity": market_conditions.get("seasonal_opportunity_3m", 0)
            }
            
            # Save to database
            market_model = get_market_analysis_model()
            analysis = await market_model.create_market_analysis(user_id, analysis_data)
            
            logger.info(f"Market analysis created for category '{category}' by user {user_id}")
            return {
                "analysis": analysis,
                "market_conditions": market_conditions
            }
            
        except Exception as e:
            logger.error(f"Error creating market analysis for category '{category}': {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "category": category, 
                "endpoint": "create_market_analysis"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create market analysis"
            )


@router.get("/{analysis_id}")
@rate_limit_user_actions()
async def get_market_analysis(
    request: Request,
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get a specific market analysis by ID"""
    user_id = current_user["id"]
    
    with monitor_operation("get_market_analysis", user_id=user_id, analysis_id=analysis_id):
        try:
            market_model = get_market_analysis_model()
            analysis = await market_model.get_market_analysis_by_id(analysis_id, user_id)
            
            if not analysis:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Market analysis not found"
                )
            
            logger.info(f"Market analysis {analysis_id} retrieved by user {user_id}")
            return {"analysis": analysis}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving market analysis {analysis_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "analysis_id": analysis_id, 
                "endpoint": "get_market_analysis"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve market analysis"
            )


@router.put("/{analysis_id}")
@rate_limit_user_actions()
async def update_market_analysis(
    request: Request,
    analysis_id: str,
    updates: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update a market analysis"""
    user_id = current_user["id"]
    
    with monitor_operation("update_market_analysis", user_id=user_id, analysis_id=analysis_id):
        try:
            # Add updated timestamp
            updates["updated_at"] = datetime.utcnow().isoformat()
            
            market_model = get_market_analysis_model()
            updated_analysis = await market_model.update_market_analysis(
                analysis_id, user_id, updates
            )
            
            logger.info(f"Market analysis {analysis_id} updated by user {user_id}")
            return {"analysis": updated_analysis}
            
        except Exception as e:
            logger.error(f"Error updating market analysis {analysis_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "analysis_id": analysis_id, 
                "endpoint": "update_market_analysis"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update market analysis"
            )


@router.delete("/{analysis_id}")
@rate_limit_user_actions()
async def delete_market_analysis(
    request: Request,
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a market analysis"""
    user_id = current_user["id"]
    
    with monitor_operation("delete_market_analysis", user_id=user_id, analysis_id=analysis_id):
        try:
            market_model = get_market_analysis_model()
            success = await market_model.delete_market_analysis(analysis_id, user_id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Market analysis not found"
                )
            
            logger.info(f"Market analysis {analysis_id} deleted by user {user_id}")
            return {"message": "Market analysis deleted successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting market analysis {analysis_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "analysis_id": analysis_id, 
                "endpoint": "delete_market_analysis"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete market analysis"
            )


@router.get("/{analysis_id}/insights")
@rate_limit_user_actions()
async def get_market_insights(
    request: Request,
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get detailed insights from a market analysis"""
    user_id = current_user["id"]
    
    with monitor_operation("get_market_insights", user_id=user_id, analysis_id=analysis_id):
        try:
            market_model = get_market_analysis_model()
            analysis = await market_model.get_market_analysis_by_id(analysis_id, user_id)
            
            if not analysis:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Market analysis not found"
                )
            
            # Generate insights from the analysis data
            insights = {
                "opportunity_rating": "High" if analysis.get("opportunity_score", 0) > 0.7 else 
                                   "Medium" if analysis.get("opportunity_score", 0) > 0.4 else "Low",
                "competition_level": "High" if analysis.get("competition_density", 0) > 0.7 else
                                   "Medium" if analysis.get("competition_density", 0) > 0.4 else "Low",
                "recommended_price_range": {
                    "min": max(2.99, analysis.get("average_price", 4.99) * 0.8),
                    "max": analysis.get("average_price", 4.99) * 1.2
                },
                "seasonal_timing": "Favorable" if analysis.get("seasonal_opportunity", 1.0) > 1.1 else
                                 "Neutral" if analysis.get("seasonal_opportunity", 1.0) > 0.9 else "Unfavorable",
                "trending_keywords": analysis.get("trending_keywords", {}),
                "market_gaps": analysis.get("emerging_subtopics", []),
                "avoid_topics": analysis.get("declining_topics", []),
                "success_probability": min(analysis.get("opportunity_score", 0) * 100, 95)
            }
            
            logger.info(f"Market insights generated for analysis {analysis_id} by user {user_id}")
            return {
                "analysis_id": analysis_id,
                "insights": insights,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating insights for analysis {analysis_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "analysis_id": analysis_id, 
                "endpoint": "get_market_insights"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate market insights"
            )


@router.post("/{analysis_id}/refresh")
@rate_limit_ai_operations()
async def refresh_market_analysis(
    request: Request,
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Refresh a market analysis with latest data"""
    user_id = current_user["id"]
    
    with monitor_operation("refresh_market_analysis", user_id=user_id, analysis_id=analysis_id):
        try:
            market_model = get_market_analysis_model()
            refreshed_analysis = await market_model.refresh_market_analysis(analysis_id, user_id)
            
            logger.info(f"Market analysis {analysis_id} refreshed by user {user_id}")
            return {"analysis": refreshed_analysis}
            
        except Exception as e:
            logger.error(f"Error refreshing market analysis {analysis_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id, 
                "analysis_id": analysis_id, 
                "endpoint": "refresh_market_analysis"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to refresh market analysis"
            )