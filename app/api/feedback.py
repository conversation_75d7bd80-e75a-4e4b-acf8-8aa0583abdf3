# app/api/feedback.py - Feedback API endpoints

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import get_feedback_model
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.middleware.security_middleware import rate_limit_user_actions, rate_limit_ai_operations

logger = get_logger(__name__)

router = APIRouter(tags=["feedback"])


@router.post("/")
@rate_limit_user_actions()
async def submit_feedback(
    request: Request,
    feedback_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Submit user feedback for VERL training"""
    user_id = current_user["id"]
    
    with monitor_operation("submit_feedback", user_id=user_id, feedback_data=feedback_data):
        try:
            feedback_model = await get_feedback_model()
            
            # Add user ID to feedback data
            feedback_data["user_id"] = user_id
            
            result = await feedback_model.create_feedback(feedback_data)
            
            logger.info(f"Feedback submitted successfully for user {user_id}: {result['id']}")
            
            return {
                "id": result["id"],
                "message": "Feedback submitted successfully",
                "status": "recorded"
            }
            
        except Exception as e:
            logger.error(f"Error submitting feedback for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id,
                "feedback_data": feedback_data,
                "endpoint": "submit_feedback"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to submit feedback"
            )


@router.get("/analytics/dashboard")
@rate_limit_user_actions()
async def get_feedback_analytics(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get feedback analytics for dashboard"""
    user_id = current_user["id"]
    
    with monitor_operation("get_feedback_analytics", user_id=user_id):
        try:
            feedback_model = await get_feedback_model()
            analytics = await feedback_model.get_dashboard_analytics(user_id)
            
            logger.info(f"Feedback analytics retrieved for user {user_id}")
            return analytics
            
        except Exception as e:
            logger.error(f"Error fetching feedback analytics for user {user_id}: {e}")
            capture_exception(e, extra={
                "user_id": user_id,
                "endpoint": "get_feedback_analytics"
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch feedback analytics"
            )