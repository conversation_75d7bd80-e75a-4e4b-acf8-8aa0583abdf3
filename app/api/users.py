# app/api/users.py - User Settings API Endpoints

from fastapi import APIRouter, Depends, HTTPException, Request
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging

from app.auth.supabase_auth import get_current_user
from app.models.supabase_models import (
    get_user_preferences_model,
    get_user_publishing_settings_model,
    get_user_platform_integrations_model,
    get_user_security_settings_model,
    get_user_active_sessions_model,
    get_user_api_keys_model,
    get_user_model
)
from app.middleware.security_middleware import get_limiter
from app.monitoring.monitoring_setup import monitor_operation

logger = logging.getLogger(__name__)

router = APIRouter()
limiter = get_limiter()

# ================================
# PYDANTIC SCHEMAS
# ================================

class UserProfileResponse(BaseModel):
    """User profile response schema"""
    id: str
    email: str
    full_name: Optional[str] = None
    subscription_tier: str = "free"
    created_at: str
    updated_at: Optional[str] = None

class UserProfileUpdate(BaseModel):
    """User profile update schema"""
    full_name: Optional[str] = None
    subscription_tier: Optional[str] = None

class UserPreferencesResponse(BaseModel):
    """User preferences response schema"""
    user_id: str
    theme: str = "light"
    language: str = "en"
    timezone: str = "UTC"
    email_notifications: bool = True
    push_notifications: bool = True
    marketing_emails: bool = False
    content_preferences: Dict[str, Any] = {}
    accessibility: Dict[str, Any] = {}
    privacy_level: str = "standard"
    updated_at: Optional[str] = None

class UserPreferencesUpdate(BaseModel):
    """User preferences update schema"""
    theme: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    marketing_emails: Optional[bool] = None
    content_preferences: Optional[Dict[str, Any]] = None
    accessibility: Optional[Dict[str, Any]] = None
    privacy_level: Optional[str] = None

class PublishingSettingsResponse(BaseModel):
    """Publishing settings response schema"""
    user_id: str
    preferred_ai_provider: str = "openai"
    default_writing_style: str = "professional"
    default_target_audience: str = "general adults"
    auto_publish_enabled: bool = False
    quality_threshold: float = 0.8
    content_preferences: Dict[str, Any] = {}
    publishing_defaults: Dict[str, Any] = {}
    updated_at: Optional[str] = None

class PublishingSettingsUpdate(BaseModel):
    """Publishing settings update schema"""
    preferred_ai_provider: Optional[str] = None
    default_writing_style: Optional[str] = None
    default_target_audience: Optional[str] = None
    auto_publish_enabled: Optional[bool] = None
    quality_threshold: Optional[float] = None
    content_preferences: Optional[Dict[str, Any]] = None
    publishing_defaults: Optional[Dict[str, Any]] = None

class PlatformIntegrationData(BaseModel):
    """Platform integration data schema"""
    enabled: bool
    account_id: Optional[str] = None
    username: Optional[str] = None
    publication_url: Optional[str] = None
    site_url: Optional[str] = None
    auto_publish: Optional[bool] = False

class SecuritySettingsResponse(BaseModel):
    """Security settings response schema"""
    user_id: str
    two_factor_enabled: bool = False
    login_notifications: bool = True
    session_timeout: int = 3600
    ip_whitelist: List[str] = []
    device_tracking: bool = True
    api_key_access: bool = True
    data_export_enabled: bool = True
    account_deletion_protection: bool = True
    last_password_change: Optional[str] = None
    updated_at: Optional[str] = None

class SecuritySettingsUpdate(BaseModel):
    """Security settings update schema"""
    two_factor_enabled: Optional[bool] = None
    login_notifications: Optional[bool] = None
    session_timeout: Optional[int] = None
    ip_whitelist: Optional[List[str]] = None
    device_tracking: Optional[bool] = None
    api_key_access: Optional[bool] = None
    data_export_enabled: Optional[bool] = None
    account_deletion_protection: Optional[bool] = None

class ChangePasswordRequest(BaseModel):
    """Change password request schema"""
    old_password: str
    new_password: str = Field(..., min_length=8)

class ActiveSessionResponse(BaseModel):
    """Active session response schema"""
    session_id: str
    device_type: str
    browser: str
    ip_address: str
    location: str
    created_at: str
    last_activity: str
    is_current: bool

class ApiKeyResponse(BaseModel):
    """API key response schema"""
    key_id: str
    name: str
    key_prefix: str
    permissions: List[str]
    usage_count: int
    last_used: Optional[str] = None
    created_at: str
    expires_at: Optional[str] = None
    is_active: bool

class ApiKeyCreate(BaseModel):
    """API key creation schema"""
    name: str
    permissions: List[str] = ["read"]
    usage_limit: Optional[int] = 1000
    expires_at: Optional[str] = None

class ApiKeyCreated(BaseModel):
    """API key created response schema"""
    key_id: str
    api_key: str
    name: str
    permissions: List[str]
    created_at: str
    expires_at: Optional[str] = None

# ================================
# USER PROFILE ENDPOINTS
# ================================

@router.get("/profile", response_model=UserProfileResponse)
@limiter.limit("100/minute")
async def get_user_profile(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user profile information"""
    async with monitor_operation("get_user_profile"):
        try:
            user_model = await get_user_model()
            user_data = await user_model.get_user_by_id(current_user["id"])
            
            if not user_data:
                raise HTTPException(status_code=404, detail="User not found")
            
            return UserProfileResponse(
                id=user_data["id"],
                email=user_data["email"],
                full_name=user_data.get("full_name"),
                subscription_tier=user_data.get("subscription_tier", "free"),
                created_at=user_data["created_at"],
                updated_at=user_data.get("updated_at")
            )
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            raise HTTPException(status_code=500, detail="Failed to get user profile")

@router.patch("/profile", response_model=UserProfileResponse)
@limiter.limit("50/minute")
async def update_user_profile(
    request: Request,
    profile_update: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update user profile information"""
    async with monitor_operation("update_user_profile"):
        try:
            user_model = await get_user_model()
            
            # Prepare update data
            update_data = {}
            if profile_update.full_name is not None:
                update_data["full_name"] = profile_update.full_name
            if profile_update.subscription_tier is not None:
                update_data["subscription_tier"] = profile_update.subscription_tier
            
            if not update_data:
                raise HTTPException(status_code=400, detail="No valid fields to update")
            
            updated_user = await user_model.update_user(current_user["id"], update_data)
            
            return UserProfileResponse(
                id=updated_user["id"],
                email=updated_user["email"],
                full_name=updated_user.get("full_name"),
                subscription_tier=updated_user.get("subscription_tier", "free"),
                created_at=updated_user["created_at"],
                updated_at=updated_user.get("updated_at")
            )
        except Exception as e:
            logger.error(f"Failed to update user profile: {e}")
            raise HTTPException(status_code=500, detail="Failed to update user profile")

# ================================
# USER PREFERENCES ENDPOINTS
# ================================

@router.get("/preferences", response_model=UserPreferencesResponse)
@limiter.limit("200/minute")
async def get_user_preferences(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user preferences"""
    async with monitor_operation("get_user_preferences"):
        try:
            preferences_model = await get_user_preferences_model()
            preferences = await preferences_model.get_user_preferences(current_user["id"])
            
            return UserPreferencesResponse(**preferences)
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            raise HTTPException(status_code=500, detail="Failed to get user preferences")

@router.patch("/preferences", response_model=UserPreferencesResponse)
@limiter.limit("100/minute")
async def update_user_preferences(
    request: Request,
    preferences_update: UserPreferencesUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update user preferences"""
    async with monitor_operation("update_user_preferences"):
        try:
            preferences_model = await get_user_preferences_model()
            
            # Convert to dict and filter out None values
            update_data = {k: v for k, v in preferences_update.dict().items() if v is not None}
            
            if not update_data:
                raise HTTPException(status_code=400, detail="No valid fields to update")
            
            updated_preferences = await preferences_model.update_user_preferences(
                current_user["id"], update_data
            )
            
            return UserPreferencesResponse(**updated_preferences)
        except Exception as e:
            logger.error(f"Failed to update user preferences: {e}")
            raise HTTPException(status_code=500, detail="Failed to update user preferences")

# ================================
# PUBLISHING SETTINGS ENDPOINTS
# ================================

@router.get("/publishing-settings", response_model=PublishingSettingsResponse)
@limiter.limit("200/minute")
async def get_publishing_settings(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user publishing settings"""
    async with monitor_operation("get_publishing_settings"):
        try:
            settings_model = await get_user_publishing_settings_model()
            settings = await settings_model.get_publishing_settings(current_user["id"])
            
            return PublishingSettingsResponse(**settings)
        except Exception as e:
            logger.error(f"Failed to get publishing settings: {e}")
            raise HTTPException(status_code=500, detail="Failed to get publishing settings")

@router.patch("/publishing-settings", response_model=PublishingSettingsResponse)
@limiter.limit("50/minute")
async def update_publishing_settings(
    request: Request,
    settings_update: PublishingSettingsUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update user publishing settings"""
    async with monitor_operation("update_publishing_settings"):
        try:
            settings_model = await get_user_publishing_settings_model()
            
            # Convert to dict and filter out None values
            update_data = {k: v for k, v in settings_update.dict().items() if v is not None}
            
            if not update_data:
                raise HTTPException(status_code=400, detail="No valid fields to update")
            
            updated_settings = await settings_model.update_publishing_settings(
                current_user["id"], update_data
            )
            
            return PublishingSettingsResponse(**updated_settings)
        except Exception as e:
            logger.error(f"Failed to update publishing settings: {e}")
            raise HTTPException(status_code=500, detail="Failed to update publishing settings")

# ================================
# PLATFORM INTEGRATIONS ENDPOINTS
# ================================

@router.get("/integrations")
@limiter.limit("200/minute")
async def get_platform_integrations(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user platform integrations"""
    async with monitor_operation("get_platform_integrations"):
        try:
            integrations_model = await get_user_platform_integrations_model()
            integrations = await integrations_model.get_platform_integrations(current_user["id"])
            
            return integrations
        except Exception as e:
            logger.error(f"Failed to get platform integrations: {e}")
            raise HTTPException(status_code=500, detail="Failed to get platform integrations")

@router.patch("/integrations/{platform}")
@limiter.limit("50/minute")
async def update_platform_integration(
    request: Request,
    platform: str,
    integration_data: PlatformIntegrationData,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update specific platform integration"""
    async with monitor_operation("update_platform_integration"):
        try:
            # Validate platform
            valid_platforms = ["amazon_kdp", "medium", "substack", "wordpress"]
            if platform not in valid_platforms:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid platform. Must be one of: {valid_platforms}"
                )
            
            integrations_model = await get_user_platform_integrations_model()
            
            # Convert to dict and filter out None values
            update_data = {k: v for k, v in integration_data.dict().items() if v is not None}
            
            updated_integrations = await integrations_model.update_platform_integration(
                current_user["id"], platform, update_data
            )
            
            return updated_integrations
        except Exception as e:
            logger.error(f"Failed to update platform integration: {e}")
            raise HTTPException(status_code=500, detail="Failed to update platform integration")

# ================================
# SECURITY SETTINGS ENDPOINTS
# ================================

@router.get("/security", response_model=SecuritySettingsResponse)
@limiter.limit("200/minute")
async def get_security_settings(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user security settings"""
    async with monitor_operation("get_security_settings"):
        try:
            security_model = await get_user_security_settings_model()
            settings = await security_model.get_security_settings(current_user["id"])
            
            return SecuritySettingsResponse(**settings)
        except Exception as e:
            logger.error(f"Failed to get security settings: {e}")
            raise HTTPException(status_code=500, detail="Failed to get security settings")

@router.patch("/security", response_model=SecuritySettingsResponse)
@limiter.limit("50/minute")
async def update_security_settings(
    request: Request,
    settings_update: SecuritySettingsUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update user security settings"""
    async with monitor_operation("update_security_settings"):
        try:
            security_model = await get_user_security_settings_model()
            
            # Convert to dict and filter out None values
            update_data = {k: v for k, v in settings_update.dict().items() if v is not None}
            
            if not update_data:
                raise HTTPException(status_code=400, detail="No valid fields to update")
            
            updated_settings = await security_model.update_security_settings(
                current_user["id"], update_data
            )
            
            return SecuritySettingsResponse(**updated_settings)
        except Exception as e:
            logger.error(f"Failed to update security settings: {e}")
            raise HTTPException(status_code=500, detail="Failed to update security settings")

@router.patch("/change-password")
@limiter.limit("10/minute")
async def change_password(
    request: Request,
    password_change: ChangePasswordRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Change user password"""
    async with monitor_operation("change_password"):
        try:
            security_model = await get_user_security_settings_model()
            
            result = await security_model.change_password(
                current_user["id"], 
                password_change.old_password, 
                password_change.new_password
            )
            
            return result
        except Exception as e:
            logger.error(f"Failed to change password: {e}")
            raise HTTPException(status_code=500, detail="Failed to change password")

# ================================
# ACTIVE SESSIONS ENDPOINTS
# ================================

@router.get("/sessions", response_model=List[ActiveSessionResponse])
@limiter.limit("100/minute")
async def get_active_sessions(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user's active sessions"""
    async with monitor_operation("get_active_sessions"):
        try:
            sessions_model = await get_user_active_sessions_model()
            sessions = await sessions_model.get_active_sessions(current_user["id"])
            
            return [ActiveSessionResponse(**session) for session in sessions]
        except Exception as e:
            logger.error(f"Failed to get active sessions: {e}")
            raise HTTPException(status_code=500, detail="Failed to get active sessions")

@router.delete("/sessions/{session_id}")
@limiter.limit("20/minute")
async def terminate_session(
    request: Request,
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Terminate a specific session"""
    async with monitor_operation("terminate_session"):
        try:
            sessions_model = await get_user_active_sessions_model()
            result = await sessions_model.terminate_session(current_user["id"], session_id)
            
            return result
        except Exception as e:
            logger.error(f"Failed to terminate session: {e}")
            raise HTTPException(status_code=500, detail="Failed to terminate session")

# ================================
# API KEYS ENDPOINTS
# ================================

@router.get("/api-keys", response_model=List[ApiKeyResponse])
@limiter.limit("100/minute")
async def get_api_keys(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get user's API keys"""
    async with monitor_operation("get_api_keys"):
        try:
            api_keys_model = await get_user_api_keys_model()
            keys = await api_keys_model.get_user_api_keys(current_user["id"])
            
            return [ApiKeyResponse(**key) for key in keys]
        except Exception as e:
            logger.error(f"Failed to get API keys: {e}")
            raise HTTPException(status_code=500, detail="Failed to get API keys")

@router.post("/api-keys", response_model=ApiKeyCreated)
@limiter.limit("10/minute")
async def create_api_key(
    request: Request,
    key_data: ApiKeyCreate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new API key"""
    async with monitor_operation("create_api_key"):
        try:
            api_keys_model = await get_user_api_keys_model()
            
            result = await api_keys_model.create_api_key(
                current_user["id"], 
                key_data.dict()
            )
            
            return ApiKeyCreated(**result)
        except Exception as e:
            logger.error(f"Failed to create API key: {e}")
            raise HTTPException(status_code=500, detail="Failed to create API key")

@router.post("/api-keys/{key_id}/regenerate", response_model=ApiKeyCreated)
@limiter.limit("5/minute")
async def regenerate_api_key(
    request: Request,
    key_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Regenerate an existing API key"""
    async with monitor_operation("regenerate_api_key"):
        try:
            api_keys_model = await get_user_api_keys_model()
            
            result = await api_keys_model.regenerate_api_key(
                current_user["id"], 
                key_id
            )
            
            return ApiKeyCreated(**result)
        except Exception as e:
            logger.error(f"Failed to regenerate API key: {e}")
            raise HTTPException(status_code=500, detail="Failed to regenerate API key")