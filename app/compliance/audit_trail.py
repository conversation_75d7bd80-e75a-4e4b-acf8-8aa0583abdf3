"""
Compliance Audit Trail System.

This module provides comprehensive audit trail functionality specifically for compliance:
- GDPR audit trails with detailed event tracking
- Data processing activity monitoring
- Consent management audit logs
- Privacy impact assessment tracking
- Retention policy compliance auditing
- Automated compliance reporting
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass, asdict
import uuid

from pydantic import BaseModel, Field

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.monitoring.audit_logger import audit_log, AuditCategory, AuditLevel

logger = logging.getLogger(__name__)
settings = get_settings()


class ComplianceEventType(str, Enum):
    """Compliance-specific event types."""
    # GDPR Events
    GDPR_REQUEST_SUBMITTED = "gdpr_request_submitted"
    GDPR_REQUEST_PROCESSED = "gdpr_request_processed"
    GDPR_REQUEST_COMPLETED = "gdpr_request_completed"
    GDPR_DATA_EXPORTED = "gdpr_data_exported"
    GDPR_DATA_ERASED = "gdpr_data_erased"
    
    # Consent Events
    CONSENT_GIVEN = "consent_given"
    CONSENT_WITHDRAWN = "consent_withdrawn"
    CONSENT_EXPIRED = "consent_expired"
    CONSENT_RENEWED = "consent_renewed"
    
    # Data Processing Events
    PROCESSING_ACTIVITY_REGISTERED = "processing_activity_registered"
    PROCESSING_ACTIVITY_MODIFIED = "processing_activity_modified"
    DATA_PROCESSING_STARTED = "data_processing_started"
    DATA_PROCESSING_COMPLETED = "data_processing_completed"
    
    # Anonymization Events
    DATA_ANONYMIZED = "data_anonymized"
    ANONYMIZATION_REQUESTED = "anonymization_requested"
    PSEUDONYMIZATION_APPLIED = "pseudonymization_applied"
    
    # Privacy Impact Assessment Events
    PIA_CREATED = "pia_created"
    PIA_REVIEWED = "pia_reviewed"
    PIA_APPROVED = "pia_approved"
    PIA_UPDATED = "pia_updated"
    
    # Retention Events
    RETENTION_POLICY_APPLIED = "retention_policy_applied"
    DATA_ARCHIVED = "data_archived"
    DATA_DELETED = "data_deleted"
    
    # Compliance Reporting
    COMPLIANCE_REPORT_GENERATED = "compliance_report_generated"
    COMPLIANCE_AUDIT_PERFORMED = "compliance_audit_performed"
    VIOLATION_DETECTED = "violation_detected"


class ComplianceRiskLevel(str, Enum):
    """Compliance risk levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ComplianceAuditEvent:
    """Compliance audit event model."""
    event_id: str
    event_type: ComplianceEventType
    timestamp: datetime
    user_id: Optional[str]
    
    # Event context
    resource_type: str  # gdpr_request, consent_record, processing_activity, etc.
    resource_id: str
    action: str
    
    # Event details
    details: Dict[str, Any]
    risk_level: ComplianceRiskLevel
    compliance_framework: str = "GDPR"  # GDPR, CCPA, etc.
    
    # Regulatory context
    legal_basis: Optional[str] = None
    data_categories: List[str] = None
    data_subjects: List[str] = None
    
    # Compliance metadata
    retention_period_days: int = 2555  # 7 years for GDPR
    verified: bool = False
    signature: Optional[str] = None


class ComplianceAuditTrail:
    """
    Comprehensive compliance audit trail system.
    
    Features:
    - GDPR-specific event tracking
    - Automated compliance monitoring
    - Risk assessment and alerting
    - Regulatory reporting capabilities
    - Audit trail integrity protection
    - Cross-reference validation
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        
        # Compliance frameworks configuration
        self.frameworks = {
            "GDPR": {
                "retention_days": 2555,  # 7 years
                "critical_events": [
                    ComplianceEventType.GDPR_DATA_ERASED,
                    ComplianceEventType.VIOLATION_DETECTED
                ],
                "notification_required": [
                    ComplianceEventType.GDPR_REQUEST_SUBMITTED,
                    ComplianceEventType.DATA_ANONYMIZED
                ]
            },
            "CCPA": {
                "retention_days": 1095,  # 3 years
                "critical_events": [
                    ComplianceEventType.GDPR_DATA_ERASED,  # Same as GDPR for data deletion
                    ComplianceEventType.VIOLATION_DETECTED
                ]
            }
        }
        
        # Risk scoring weights
        self.risk_weights = {
            ComplianceEventType.GDPR_DATA_ERASED: 80,
            ComplianceEventType.VIOLATION_DETECTED: 95,
            ComplianceEventType.DATA_ANONYMIZED: 60,
            ComplianceEventType.CONSENT_WITHDRAWN: 40,
            ComplianceEventType.GDPR_REQUEST_SUBMITTED: 30,
            ComplianceEventType.PIA_CREATED: 20
        }
    
    async def log_compliance_event(
        self,
        event_type: ComplianceEventType,
        resource_type: str,
        resource_id: str,
        action: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        legal_basis: Optional[str] = None,
        data_categories: Optional[List[str]] = None,
        compliance_framework: str = "GDPR"
    ) -> str:
        """Log a compliance-specific audit event."""
        
        event_id = str(uuid.uuid4())
        
        # Calculate risk level
        risk_level = self._calculate_risk_level(event_type, details)
        
        # Create compliance audit event
        event = ComplianceAuditEvent(
            event_id=event_id,
            event_type=event_type,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            details=details,
            risk_level=risk_level,
            compliance_framework=compliance_framework,
            legal_basis=legal_basis,
            data_categories=data_categories or [],
            retention_period_days=self.frameworks[compliance_framework]["retention_days"]
        )
        
        # Store in compliance audit table
        await self._store_compliance_event(event)
        
        # Also log in main audit system
        await audit_log(
            event_type.value,
            {
                **details,
                "compliance_event_id": event_id,
                "compliance_framework": compliance_framework,
                "risk_level": risk_level.value
            },
            user_id=user_id,
            category=AuditCategory.COMPLIANCE,
            level=self._audit_level_from_risk(risk_level)
        )
        
        # Check for compliance violations
        await self._check_compliance_violations(event)
        
        # Send notifications if required
        await self._send_compliance_notifications(event)
        
        logger.info(f"Compliance event logged: {event_type.value} for {resource_type}:{resource_id}")
        
        return event_id
    
    async def get_compliance_audit_trail(
        self,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        event_type: Optional[ComplianceEventType] = None,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        compliance_framework: Optional[str] = None,
        risk_level: Optional[ComplianceRiskLevel] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get compliance audit trail with filtering."""
        
        try:
            query = self.supabase.table("compliance_audit_events").select("*").order("timestamp", desc=True).limit(limit)
            
            if resource_type:
                query = query.eq("resource_type", resource_type)
            if resource_id:
                query = query.eq("resource_id", resource_id)
            if event_type:
                query = query.eq("event_type", event_type.value)
            if user_id:
                query = query.eq("user_id", user_id)
            if compliance_framework:
                query = query.eq("compliance_framework", compliance_framework)
            if risk_level:
                query = query.eq("risk_level", risk_level.value)
            if start_date:
                query = query.gte("timestamp", start_date.isoformat())
            if end_date:
                query = query.lte("timestamp", end_date.isoformat())
            
            response = await query.execute()
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get compliance audit trail: {e}")
            return []
    
    async def generate_compliance_report(
        self,
        framework: str = "GDPR",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_violations: bool = True,
        include_statistics: bool = True
    ) -> Dict[str, Any]:
        """Generate comprehensive compliance report."""
        
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()
        
        try:
            # Get compliance events for the period
            events = await self.get_compliance_audit_trail(
                compliance_framework=framework,
                start_date=start_date,
                end_date=end_date,
                limit=10000
            )
            
            report = {
                "report_id": str(uuid.uuid4()),
                "generated_at": datetime.utcnow().isoformat(),
                "framework": framework,
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "summary": {
                    "total_events": len(events),
                    "high_risk_events": len([e for e in events if e["risk_level"] in ["high", "critical"]]),
                    "violations_detected": len([e for e in events if e["event_type"] == ComplianceEventType.VIOLATION_DETECTED.value])
                }
            }
            
            if include_statistics:
                report["statistics"] = await self._generate_compliance_statistics(events, framework)
            
            if include_violations:
                report["violations"] = await self._get_compliance_violations(start_date, end_date, framework)
            
            # GDPR-specific sections
            if framework == "GDPR":
                report["gdpr_metrics"] = await self._generate_gdpr_metrics(start_date, end_date)
                report["data_subject_requests"] = await self._get_gdpr_request_summary(start_date, end_date)
                report["consent_management"] = await self._get_consent_summary(start_date, end_date)
            
            # Store report
            await self._store_compliance_report(report)
            
            # Log report generation
            await self.log_compliance_event(
                ComplianceEventType.COMPLIANCE_REPORT_GENERATED,
                "compliance_report",
                report["report_id"],
                "generate",
                {
                    "framework": framework,
                    "period_days": (end_date - start_date).days,
                    "total_events": len(events)
                },
                compliance_framework=framework
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            raise
    
    async def validate_compliance_state(
        self,
        framework: str = "GDPR"
    ) -> Dict[str, Any]:
        """Validate current compliance state."""
        
        try:
            validation_result = {
                "framework": framework,
                "validation_timestamp": datetime.utcnow().isoformat(),
                "overall_status": "compliant",
                "issues": [],
                "recommendations": []
            }
            
            # Check for overdue GDPR requests
            if framework == "GDPR":
                overdue_requests = await self._check_overdue_gdpr_requests()
                if overdue_requests > 0:
                    validation_result["issues"].append({
                        "type": "overdue_gdpr_requests",
                        "count": overdue_requests,
                        "severity": "high",
                        "description": f"{overdue_requests} GDPR requests are overdue (>30 days)"
                    })
                    validation_result["overall_status"] = "non_compliant"
            
            # Check for expired consents without renewal
            expired_consents = await self._check_expired_consents()
            if expired_consents > 0:
                validation_result["issues"].append({
                    "type": "expired_consents",
                    "count": expired_consents,
                    "severity": "medium",
                    "description": f"{expired_consents} consents have expired and need renewal"
                })
            
            # Check for missing processing activity records
            missing_activities = await self._check_missing_processing_activities()
            if missing_activities:
                validation_result["issues"].append({
                    "type": "missing_processing_activities",
                    "activities": missing_activities,
                    "severity": "medium",
                    "description": "Some data processing activities lack proper documentation"
                })
            
            # Check for recent high-risk events
            recent_high_risk = await self._check_recent_high_risk_events()
            if recent_high_risk > 0:
                validation_result["issues"].append({
                    "type": "recent_high_risk_events",
                    "count": recent_high_risk,
                    "severity": "high",
                    "description": f"{recent_high_risk} high-risk compliance events in the last 7 days"
                })
            
            # Generate recommendations
            validation_result["recommendations"] = await self._generate_compliance_recommendations(validation_result["issues"])
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Failed to validate compliance state: {e}")
            raise
    
    def _calculate_risk_level(self, event_type: ComplianceEventType, details: Dict[str, Any]) -> ComplianceRiskLevel:
        """Calculate risk level for compliance event."""
        
        base_risk = self.risk_weights.get(event_type, 10)
        
        # Adjust based on details
        if details.get("data_volume", 0) > 1000:
            base_risk += 20
        if details.get("sensitive_data", False):
            base_risk += 30
        if details.get("cross_border", False):
            base_risk += 15
        
        if base_risk >= 80:
            return ComplianceRiskLevel.CRITICAL
        elif base_risk >= 60:
            return ComplianceRiskLevel.HIGH
        elif base_risk >= 30:
            return ComplianceRiskLevel.MEDIUM
        else:
            return ComplianceRiskLevel.LOW
    
    def _audit_level_from_risk(self, risk_level: ComplianceRiskLevel) -> AuditLevel:
        """Convert compliance risk level to audit level."""
        
        mapping = {
            ComplianceRiskLevel.LOW: AuditLevel.INFO,
            ComplianceRiskLevel.MEDIUM: AuditLevel.INFO,
            ComplianceRiskLevel.HIGH: AuditLevel.WARNING,
            ComplianceRiskLevel.CRITICAL: AuditLevel.ERROR
        }
        return mapping[risk_level]
    
    async def _store_compliance_event(self, event: ComplianceAuditEvent):
        """Store compliance event in database."""
        
        try:
            event_data = {
                "event_id": event.event_id,
                "event_type": event.event_type.value,
                "timestamp": event.timestamp.isoformat(),
                "user_id": event.user_id,
                "resource_type": event.resource_type,
                "resource_id": event.resource_id,
                "action": event.action,
                "details": event.details,
                "risk_level": event.risk_level.value,
                "compliance_framework": event.compliance_framework,
                "legal_basis": event.legal_basis,
                "data_categories": event.data_categories,
                "retention_period_days": event.retention_period_days,
                "verified": event.verified,
                "signature": event.signature
            }
            
            await self.supabase.table("compliance_audit_events").insert(event_data).execute()
            
        except Exception as e:
            logger.error(f"Failed to store compliance event: {e}")
            raise
    
    async def _check_compliance_violations(self, event: ComplianceAuditEvent):
        """Check for compliance violations based on event."""
        
        violations = []
        
        # Check GDPR-specific violations
        if event.compliance_framework == "GDPR":
            # Check for data processing without legal basis
            if event.event_type == ComplianceEventType.DATA_PROCESSING_STARTED:
                if not event.legal_basis:
                    violations.append({
                        "type": "missing_legal_basis",
                        "description": "Data processing started without documented legal basis",
                        "severity": "high"
                    })
            
            # Check for data retention violations
            if event.event_type == ComplianceEventType.DATA_ARCHIVED:
                retention_days = event.details.get("retention_days", 0)
                if retention_days > 2555:  # 7 years GDPR max
                    violations.append({
                        "type": "excessive_retention",
                        "description": f"Data retained for {retention_days} days, exceeding GDPR maximum",
                        "severity": "high"
                    })
        
        # Log violations
        for violation in violations:
            await self.log_compliance_event(
                ComplianceEventType.VIOLATION_DETECTED,
                event.resource_type,
                event.resource_id,
                "violation_detected",
                {
                    "violation_type": violation["type"],
                    "violation_description": violation["description"],
                    "severity": violation["severity"],
                    "triggering_event": event.event_id
                },
                user_id=event.user_id,
                compliance_framework=event.compliance_framework
            )
    
    async def _send_compliance_notifications(self, event: ComplianceAuditEvent):
        """Send notifications for compliance events."""
        
        framework_config = self.frameworks.get(event.compliance_framework, {})
        notification_required = framework_config.get("notification_required", [])
        
        if event.event_type in notification_required or event.risk_level == ComplianceRiskLevel.CRITICAL:
            # In a real implementation, this would send notifications
            logger.info(f"Compliance notification: {event.event_type.value} - {event.risk_level.value} risk")
    
    async def _generate_compliance_statistics(self, events: List[Dict], framework: str) -> Dict[str, Any]:
        """Generate compliance statistics from events."""
        
        stats = {
            "events_by_type": {},
            "events_by_risk": {},
            "events_by_day": {},
            "compliance_score": 0.0
        }
        
        for event in events:
            # Events by type
            event_type = event["event_type"]
            stats["events_by_type"][event_type] = stats["events_by_type"].get(event_type, 0) + 1
            
            # Events by risk
            risk_level = event["risk_level"]
            stats["events_by_risk"][risk_level] = stats["events_by_risk"].get(risk_level, 0) + 1
            
            # Events by day
            event_date = datetime.fromisoformat(event["timestamp"]).date().isoformat()
            stats["events_by_day"][event_date] = stats["events_by_day"].get(event_date, 0) + 1
        
        # Calculate compliance score (simplified)
        total_events = len(events)
        high_risk_events = stats["events_by_risk"].get("high", 0) + stats["events_by_risk"].get("critical", 0)
        
        if total_events > 0:
            stats["compliance_score"] = max(0, 100 - (high_risk_events / total_events * 100))
        else:
            stats["compliance_score"] = 100.0
        
        return stats
    
    async def _get_compliance_violations(self, start_date: datetime, end_date: datetime, framework: str) -> List[Dict]:
        """Get compliance violations for period."""
        
        violations = await self.get_compliance_audit_trail(
            event_type=ComplianceEventType.VIOLATION_DETECTED,
            start_date=start_date,
            end_date=end_date,
            compliance_framework=framework
        )
        
        return violations
    
    async def _generate_gdpr_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate GDPR-specific metrics."""
        
        try:
            # Get GDPR request statistics
            gdpr_response = await self.supabase.table("gdpr_requests").select("*").gte("submitted_at", start_date.isoformat()).lte("submitted_at", end_date.isoformat()).execute()
            
            gdpr_requests = gdpr_response.data
            
            metrics = {
                "total_requests": len(gdpr_requests),
                "requests_by_type": {},
                "requests_by_status": {},
                "average_processing_time_days": 0,
                "overdue_requests": 0
            }
            
            processing_times = []
            
            for request in gdpr_requests:
                # Requests by type
                req_type = request["request_type"]
                metrics["requests_by_type"][req_type] = metrics["requests_by_type"].get(req_type, 0) + 1
                
                # Requests by status
                status = request["status"]
                metrics["requests_by_status"][status] = metrics["requests_by_status"].get(status, 0) + 1
                
                # Processing time calculation
                submitted = datetime.fromisoformat(request["submitted_at"])
                if request.get("completed_at"):
                    completed = datetime.fromisoformat(request["completed_at"])
                    processing_times.append((completed - submitted).days)
                elif (datetime.utcnow() - submitted).days > 30:
                    metrics["overdue_requests"] += 1
            
            if processing_times:
                metrics["average_processing_time_days"] = sum(processing_times) / len(processing_times)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to generate GDPR metrics: {e}")
            return {}
    
    async def _get_gdpr_request_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get GDPR request summary."""
        
        try:
            response = await self.supabase.rpc("check_gdpr_request_deadlines").execute()
            
            deadlines = response.data[0] if response.data else {}
            
            return {
                "overdue_requests": deadlines.get("overdue_requests", 0),
                "due_soon_requests": deadlines.get("due_soon_requests", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get GDPR request summary: {e}")
            return {}
    
    async def _get_consent_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get consent management summary."""
        
        try:
            response = await self.supabase.rpc("get_consent_statistics", {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }).execute()
            
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to get consent summary: {e}")
            return {}
    
    async def _store_compliance_report(self, report: Dict[str, Any]):
        """Store compliance report."""
        
        try:
            await self.supabase.table("compliance_reports").insert({
                "report_id": report["report_id"],
                "generated_at": report["generated_at"],
                "period_start": report["period"]["start"],
                "period_end": report["period"]["end"],
                "report_type": report["framework"],
                "total_events": report["summary"]["total_events"],
                "events_by_category": report.get("statistics", {}).get("events_by_type", {}),
                "security_events": report["summary"]["high_risk_events"],
                "failed_authentications": 0,  # Not applicable for compliance
                "anomalies": report.get("violations", []),
                "policy_violations": report.get("violations", []),
                "recommendations": []
            }).execute()
            
        except Exception as e:
            logger.error(f"Failed to store compliance report: {e}")
    
    async def _check_overdue_gdpr_requests(self) -> int:
        """Check for overdue GDPR requests."""
        
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            response = await self.supabase.table("gdpr_requests").select("id").eq("status", "pending").lt("submitted_at", cutoff_date.isoformat()).execute()
            
            return len(response.data)
            
        except Exception as e:
            logger.error(f"Failed to check overdue GDPR requests: {e}")
            return 0
    
    async def _check_expired_consents(self) -> int:
        """Check for expired consents."""
        
        try:
            response = await self.supabase.table("consent_records").select("id").eq("status", "expired").execute()
            
            return len(response.data)
            
        except Exception as e:
            logger.error(f"Failed to check expired consents: {e}")
            return 0
    
    async def _check_missing_processing_activities(self) -> List[str]:
        """Check for missing processing activity documentation."""
        
        # This would check for data processing activities that lack proper documentation
        # For now, return empty list
        return []
    
    async def _check_recent_high_risk_events(self) -> int:
        """Check for recent high-risk compliance events."""
        
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            
            response = await self.supabase.table("compliance_audit_events").select("id").in_("risk_level", ["high", "critical"]).gte("timestamp", cutoff_date.isoformat()).execute()
            
            return len(response.data)
            
        except Exception as e:
            logger.error(f"Failed to check recent high-risk events: {e}")
            return 0
    
    async def _generate_compliance_recommendations(self, issues: List[Dict]) -> List[Dict]:
        """Generate compliance recommendations based on issues."""
        
        recommendations = []
        
        for issue in issues:
            issue_type = issue["type"]
            
            if issue_type == "overdue_gdpr_requests":
                recommendations.append({
                    "type": "process_overdue_requests",
                    "priority": "high",
                    "description": "Process overdue GDPR requests immediately to maintain compliance",
                    "action": "Review and complete pending GDPR requests within 72 hours"
                })
            
            elif issue_type == "expired_consents":
                recommendations.append({
                    "type": "renew_consents",
                    "priority": "medium",
                    "description": "Contact users to renew expired consents",
                    "action": "Implement automated consent renewal notifications"
                })
            
            elif issue_type == "recent_high_risk_events":
                recommendations.append({
                    "type": "investigate_risks",
                    "priority": "high",
                    "description": "Investigate recent high-risk compliance events",
                    "action": "Conduct risk assessment and implement additional safeguards"
                })
        
        return recommendations


# Global compliance audit trail instance
_compliance_audit_trail: Optional[ComplianceAuditTrail] = None


async def get_compliance_audit_trail() -> ComplianceAuditTrail:
    """Get or create global compliance audit trail."""
    global _compliance_audit_trail
    
    if _compliance_audit_trail is None:
        _compliance_audit_trail = ComplianceAuditTrail()
    
    return _compliance_audit_trail


# Convenience functions
async def log_gdpr_event(
    event_type: ComplianceEventType,
    resource_id: str,
    action: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None
) -> str:
    """Log GDPR-specific compliance event."""
    trail = await get_compliance_audit_trail()
    return await trail.log_compliance_event(
        event_type=event_type,
        resource_type="gdpr_request",
        resource_id=resource_id,
        action=action,
        details=details,
        user_id=user_id,
        compliance_framework="GDPR"
    )


async def log_consent_event(
    event_type: ComplianceEventType,
    consent_id: str,
    action: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None
) -> str:
    """Log consent-related compliance event."""
    trail = await get_compliance_audit_trail()
    return await trail.log_compliance_event(
        event_type=event_type,
        resource_type="consent_record",
        resource_id=consent_id,
        action=action,
        details=details,
        user_id=user_id,
        compliance_framework="GDPR"
    )
