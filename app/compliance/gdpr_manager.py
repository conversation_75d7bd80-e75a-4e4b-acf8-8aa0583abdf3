"""
GDPR Compliance Management System.

This module provides comprehensive GDPR compliance features including:
- Data subject rights management (access, rectification, erasure, portability)
- Consent management with granular tracking
- Data processing lawfulness validation
- Privacy impact assessment tools
- Breach notification automation
- Cross-border data transfer compliance
"""

import json
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass, asdict
import zipfile
import tempfile
from pathlib import Path

from pydantic import BaseModel, Field
from fastapi import HTTPException

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.monitoring.audit_logger import audit_log, AuditCategory, AuditLevel
from app.policies.data_retention import get_retention_manager

logger = logging.getLogger(__name__)
settings = get_settings()


class GDPRRights(str, Enum):
    """GDPR data subject rights."""
    ACCESS = "access"  # Article 15
    RECTIFICATION = "rectification"  # Article 16
    ERASURE = "erasure"  # Article 17 (Right to be forgotten)
    RESTRICT_PROCESSING = "restrict_processing"  # Article 18
    DATA_PORTABILITY = "data_portability"  # Article 20
    OBJECT_PROCESSING = "object_processing"  # Article 21
    OBJECT_AUTOMATED_DECISION = "object_automated_decision"  # Article 22


class ConsentStatus(str, Enum):
    """Consent status for data processing."""
    GIVEN = "given"
    WITHDRAWN = "withdrawn"
    PENDING = "pending"
    EXPIRED = "expired"


class ProcessingLawfulness(str, Enum):
    """Legal basis for data processing under GDPR Article 6."""
    CONSENT = "consent"  # Article 6(1)(a)
    CONTRACT = "contract"  # Article 6(1)(b)
    LEGAL_OBLIGATION = "legal_obligation"  # Article 6(1)(c)
    VITAL_INTERESTS = "vital_interests"  # Article 6(1)(d)
    PUBLIC_TASK = "public_task"  # Article 6(1)(e)
    LEGITIMATE_INTERESTS = "legitimate_interests"  # Article 6(1)(f)


class DataCategory(str, Enum):
    """Categories of personal data."""
    BASIC_IDENTITY = "basic_identity"
    CONTACT_INFO = "contact_info"
    DEMOGRAPHIC = "demographic"
    BEHAVIORAL = "behavioral"
    TECHNICAL = "technical"
    LOCATION = "location"
    FINANCIAL = "financial"
    HEALTH = "health"
    BIOMETRIC = "biometric"
    SPECIAL_CATEGORY = "special_category"


@dataclass
class GDPRRequest:
    """GDPR data subject request."""
    request_id: str
    user_id: str
    request_type: GDPRRights
    status: str  # pending, in_progress, completed, rejected
    submitted_at: datetime
    completed_at: Optional[datetime] = None
    
    # Request details
    description: Optional[str] = None
    verification_method: str = "email"
    verified_at: Optional[datetime] = None
    
    # Processing
    processor_id: Optional[str] = None
    rejection_reason: Optional[str] = None
    completion_notes: Optional[str] = None
    
    # Data export (for access/portability requests)
    export_path: Optional[str] = None
    export_format: str = "json"


@dataclass
class ConsentRecord:
    """Consent tracking record."""
    consent_id: str
    user_id: str
    purpose: str
    data_categories: List[DataCategory]
    status: ConsentStatus
    given_at: Optional[datetime] = None
    withdrawn_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Context
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    consent_method: str = "explicit"  # explicit, implicit, inferred
    
    # Processing basis
    lawful_basis: ProcessingLawfulness = ProcessingLawfulness.CONSENT
    special_category_basis: Optional[str] = None


@dataclass
class DataProcessingRecord:
    """Data processing activity record."""
    # Required fields first
    activity_id: str
    name: str
    purpose: str
    data_categories: List[DataCategory]
    data_subjects: List[str]  # categories of data subjects
    recipients: List[str]  # categories of recipients
    lawful_basis: ProcessingLawfulness
    retention_period: str
    
    # Optional fields with defaults
    special_category_basis: Optional[str] = None
    third_country_transfers: Optional[List[str]] = None
    safeguards: Optional[str] = None
    security_measures: Optional[List[str]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    data_controller: str = "Publish AI"
    data_processor: Optional[str] = None


class PrivacyImpactAssessment(BaseModel):
    """Privacy Impact Assessment (PIA) model."""
    pia_id: str
    title: str
    description: str
    processing_activities: List[str]
    
    # Risk assessment
    necessity_assessment: str
    proportionality_assessment: str
    risk_level: str  # low, medium, high
    identified_risks: List[Dict[str, Any]]
    mitigation_measures: List[Dict[str, Any]]
    
    # Review
    conducted_by: str
    reviewed_by: Optional[str] = None
    approved_by: Optional[str] = None
    conducted_at: datetime = Field(default_factory=datetime.utcnow)
    review_date: Optional[datetime] = None
    next_review_date: Optional[datetime] = None
    
    # Status
    status: str = "draft"  # draft, under_review, approved, rejected


class GDPRManager:
    """
    Comprehensive GDPR compliance management system.
    
    Features:
    - Data subject rights request processing
    - Consent management with granular tracking
    - Data processing activity records
    - Privacy impact assessments
    - Breach notification workflows
    - Data anonymization and pseudonymization
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.retention_manager = None
        
        # Data mapping for exports
        self.data_tables = {
            "users": ["id", "email", "name", "created_at", "last_login"],
            "books": ["id", "title", "user_id", "created_at", "content_summary"],
            "publications": ["id", "book_id", "user_id", "published_date", "platform"],
            "user_feedback": ["id", "user_id", "feedback_text", "created_at"],
            "api_keys": ["id", "name", "user_id", "created_at", "last_used_at"],
            "audit_events": ["event_id", "user_id", "event_type", "timestamp"],
            "oauth_refresh_tokens": ["token", "user_id", "created_at", "expires_at"]
        }
        
        # Anonymization rules
        self.anonymization_rules = {
            "email": lambda: "<EMAIL>",
            "name": lambda: "Anonymous User",
            "ip_address": lambda: "0.0.0.0",
            "user_agent": lambda: "Anonymous Browser",
            "phone": lambda: "************",
            "address": lambda: "Anonymous Address"
        }
    
    async def initialize(self):
        """Initialize GDPR manager."""
        from app.policies.data_retention import get_retention_manager
        self.retention_manager = await get_retention_manager()
        
        # Set up default processing activities
        await self._setup_default_processing_activities()
        
        logger.info("GDPR manager initialized")
    
    # ============================================================================
    # Data Subject Rights Management
    # ============================================================================
    
    async def submit_gdpr_request(
        self,
        user_id: str,
        request_type: GDPRRights,
        description: Optional[str] = None,
        verification_method: str = "email"
    ) -> GDPRRequest:
        """Submit a GDPR data subject request."""
        
        import uuid
        request_id = str(uuid.uuid4())
        
        request = GDPRRequest(
            request_id=request_id,
            user_id=user_id,
            request_type=request_type,
            status="pending",
            submitted_at=datetime.utcnow(),
            description=description,
            verification_method=verification_method
        )
        
        # Store request
        await self._store_gdpr_request(request)
        
        # Send verification
        await self._send_verification(request)
        
        await audit_log(
            "gdpr_request_submitted",
            {
                "request_id": request_id,
                "request_type": request_type.value,
                "user_id": user_id
            },
            user_id=user_id,
            category=AuditCategory.COMPLIANCE
        )
        
        logger.info(f"GDPR request submitted: {request_type.value} for user {user_id}")
        
        return request
    
    async def process_gdpr_request(
        self,
        request_id: str,
        processor_id: str,
        action: str = "approve"
    ) -> bool:
        """Process a GDPR request."""
        
        request = await self._get_gdpr_request(request_id)
        if not request:
            raise HTTPException(status_code=404, detail="GDPR request not found")
        
        if request.status != "pending":
            raise HTTPException(status_code=400, detail="Request already processed")
        
        if action == "approve":
            request.status = "in_progress"
            request.processor_id = processor_id
            
            # Execute the request based on type
            if request.request_type == GDPRRights.ACCESS:
                await self._process_access_request(request)
            elif request.request_type == GDPRRights.ERASURE:
                await self._process_erasure_request(request)
            elif request.request_type == GDPRRights.DATA_PORTABILITY:
                await self._process_portability_request(request)
            elif request.request_type == GDPRRights.RECTIFICATION:
                # This would require additional data from the user
                request.completion_notes = "Awaiting corrected data from user"
            
            request.status = "completed"
            request.completed_at = datetime.utcnow()
            
        elif action == "reject":
            request.status = "rejected"
            request.completed_at = datetime.utcnow()
            # rejection_reason would be set separately
        
        # Update request
        await self._update_gdpr_request(request)
        
        await audit_log(
            "gdpr_request_processed",
            {
                "request_id": request_id,
                "action": action,
                "processor_id": processor_id
            },
            user_id=request.user_id,
            category=AuditCategory.COMPLIANCE
        )
        
        return True
    
    async def _process_access_request(self, request: GDPRRequest):
        """Process data access request (Article 15)."""
        
        user_data = await self._collect_user_data(request.user_id)
        
        # Create export file
        export_path = await self._create_data_export(user_data, request.user_id, "access")
        request.export_path = export_path
        
        # The user would be notified that their data is ready for download
    
    async def _process_erasure_request(self, request: GDPRRequest):
        """Process right to be forgotten request (Article 17)."""
        
        # Check if erasure is legally required or if exceptions apply
        can_erase = await self._can_erase_data(request.user_id)
        
        if not can_erase:
            request.status = "rejected"
            request.rejection_reason = "Legal obligation to retain data exists"
            return
        
        # Anonymize instead of delete to maintain referential integrity
        await self._anonymize_user_data(request.user_id)
        
        request.completion_notes = "User data anonymized in compliance with right to be forgotten"
    
    async def _process_portability_request(self, request: GDPRRequest):
        """Process data portability request (Article 20)."""
        
        # Only include data provided by the user or generated through their use
        portable_data = await self._collect_portable_data(request.user_id)
        
        # Create structured export
        export_path = await self._create_data_export(portable_data, request.user_id, "portability")
        request.export_path = export_path
    
    # ============================================================================
    # Consent Management
    # ============================================================================
    
    async def record_consent(
        self,
        user_id: str,
        purpose: str,
        data_categories: List[DataCategory],
        consent_method: str = "explicit",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_days: Optional[int] = None
    ) -> ConsentRecord:
        """Record user consent for data processing."""
        
        import uuid
        consent_id = str(uuid.uuid4())
        
        expires_at = None
        if expires_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        consent = ConsentRecord(
            consent_id=consent_id,
            user_id=user_id,
            purpose=purpose,
            data_categories=data_categories,
            status=ConsentStatus.GIVEN,
            given_at=datetime.utcnow(),
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
            consent_method=consent_method
        )
        
        await self._store_consent_record(consent)
        
        await audit_log(
            "consent_given",
            {
                "consent_id": consent_id,
                "purpose": purpose,
                "data_categories": [cat.value for cat in data_categories]
            },
            user_id=user_id,
            category=AuditCategory.COMPLIANCE
        )
        
        return consent
    
    async def withdraw_consent(self, consent_id: str, user_id: str) -> bool:
        """Withdraw user consent."""
        
        consent = await self._get_consent_record(consent_id)
        if not consent or consent.user_id != user_id:
            return False
        
        consent.status = ConsentStatus.WITHDRAWN
        consent.withdrawn_at = datetime.utcnow()
        
        await self._update_consent_record(consent)
        
        await audit_log(
            "consent_withdrawn",
            {"consent_id": consent_id},
            user_id=user_id,
            category=AuditCategory.COMPLIANCE
        )
        
        return True
    
    async def check_consent_validity(self, user_id: str, purpose: str) -> bool:
        """Check if valid consent exists for a purpose."""
        
        try:
            response = await self.supabase.table("consent_records").select("*").eq("user_id", user_id).eq("purpose", purpose).eq("status", "given").execute()
            
            for record in response.data:
                # Check expiration
                if record.get("expires_at"):
                    expires_at = datetime.fromisoformat(record["expires_at"])
                    if expires_at < datetime.utcnow():
                        # Mark as expired
                        await self.supabase.table("consent_records").update({"status": "expired"}).eq("consent_id", record["consent_id"]).execute()
                        continue
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to check consent validity: {e}")
            return False
    
    # ============================================================================
    # Data Processing Activities
    # ============================================================================
    
    async def register_processing_activity(self, activity: DataProcessingRecord) -> bool:
        """Register a data processing activity."""
        
        try:
            activity_data = asdict(activity)
            activity_data["created_at"] = datetime.utcnow().isoformat()
            activity_data["data_categories"] = [cat.value for cat in activity.data_categories]
            activity_data["lawful_basis"] = activity.lawful_basis.value
            
            await self.supabase.table("processing_activities").insert(activity_data).execute()
            
            await audit_log(
                "processing_activity_registered",
                {"activity_id": activity.activity_id, "name": activity.name},
                category=AuditCategory.COMPLIANCE
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to register processing activity: {e}")
            return False
    
    async def _setup_default_processing_activities(self):
        """Set up default processing activities."""
        
        activities = [
            DataProcessingRecord(
                activity_id="user_management",
                name="User Account Management",
                purpose="Provide user accounts and authentication services",
                data_categories=[DataCategory.BASIC_IDENTITY, DataCategory.CONTACT_INFO],
                data_subjects=["platform_users"],
                recipients=["internal_staff"],
                lawful_basis=ProcessingLawfulness.CONTRACT,
                retention_period="Account lifetime + 30 days",
                security_measures=["encryption", "access_controls", "audit_logging"]
            ),
            DataProcessingRecord(
                activity_id="content_generation",
                name="AI Content Generation",
                purpose="Generate books and content using AI services",
                data_categories=[DataCategory.BEHAVIORAL, DataCategory.TECHNICAL],
                data_subjects=["platform_users"],
                recipients=["ai_service_providers"],
                lawful_basis=ProcessingLawfulness.LEGITIMATE_INTERESTS,
                retention_period="2 years for analytics",
                third_country_transfers=["United States"],
                safeguards="Standard Contractual Clauses"
            ),
            DataProcessingRecord(
                activity_id="analytics",
                name="Platform Analytics",
                purpose="Improve platform performance and user experience",
                data_categories=[DataCategory.BEHAVIORAL, DataCategory.TECHNICAL],
                data_subjects=["platform_users"],
                recipients=["analytics_team"],
                lawful_basis=ProcessingLawfulness.LEGITIMATE_INTERESTS,
                retention_period="2 years"
            )
        ]
        
        for activity in activities:
            activity.created_at = datetime.utcnow()
            try:
                await self.register_processing_activity(activity)
            except Exception:
                # Activity might already exist
                pass
    
    # ============================================================================
    # Data Collection and Export
    # ============================================================================
    
    async def _collect_user_data(self, user_id: str) -> Dict[str, Any]:
        """Collect all user data for access request."""
        
        user_data = {}
        
        for table_name, columns in self.data_tables.items():
            try:
                # Build query for user-related data
                query = self.supabase.table(table_name).select(",".join(columns))
                
                if "user_id" in columns:
                    query = query.eq("user_id", user_id)
                elif table_name == "users":
                    query = query.eq("id", user_id)
                else:
                    continue  # Skip tables that don't relate to users
                
                response = await query.execute()
                user_data[table_name] = response.data
                
            except Exception as e:
                logger.error(f"Failed to collect data from {table_name}: {e}")
                user_data[table_name] = []
        
        return user_data
    
    async def _collect_portable_data(self, user_id: str) -> Dict[str, Any]:
        """Collect portable data (user-provided or generated through use)."""
        
        # Only include data that is portable under GDPR Article 20
        portable_tables = {
            "books": ["title", "content_summary", "created_at"],
            "publications": ["published_date", "platform"],
            "user_feedback": ["feedback_text", "created_at"]
        }
        
        portable_data = {}
        
        for table_name, columns in portable_tables.items():
            try:
                response = await self.supabase.table(table_name).select(",".join(columns)).eq("user_id", user_id).execute()
                portable_data[table_name] = response.data
                
            except Exception as e:
                logger.error(f"Failed to collect portable data from {table_name}: {e}")
                portable_data[table_name] = []
        
        return portable_data
    
    async def _create_data_export(self, data: Dict[str, Any], user_id: str, export_type: str) -> str:
        """Create a data export file."""
        
        # Create temporary directory for export
        export_dir = Path(settings.storage_path) / "gdpr_exports" / user_id
        export_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        export_filename = f"{export_type}_export_{timestamp}.zip"
        export_path = export_dir / export_filename
        
        with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add data files
            for table_name, records in data.items():
                if records:
                    json_content = json.dumps(records, indent=2, default=str)
                    zipf.writestr(f"{table_name}.json", json_content)
            
            # Add metadata
            metadata = {
                "export_type": export_type,
                "user_id": user_id,
                "export_date": datetime.utcnow().isoformat(),
                "data_controller": "Publish AI",
                "contact_email": "<EMAIL>",
                "retention_period": "This export will be deleted after 30 days"
            }
            zipf.writestr("metadata.json", json.dumps(metadata, indent=2))
            
            # Add privacy notice
            privacy_notice = """
GDPR Data Export

This archive contains your personal data as processed by Publish AI.
The data is provided in JSON format for portability.

For questions about this export or your data rights, contact:
<EMAIL>

This export will be automatically deleted after 30 days.
            """
            zipf.writestr("README.txt", privacy_notice.strip())
        
        return str(export_path)
    
    # ============================================================================
    # Data Anonymization
    # ============================================================================
    
    async def _anonymize_user_data(self, user_id: str) -> bool:
        """Anonymize user data while preserving referential integrity."""
        
        try:
            # Anonymize user table
            user_updates = {}
            for field, anonymizer in self.anonymization_rules.items():
                user_updates[field] = anonymizer()
            
            user_updates["anonymized_at"] = datetime.utcnow().isoformat()
            user_updates["anonymized"] = True
            
            await self.supabase.table("users").update(user_updates).eq("id", user_id).execute()
            
            # Anonymize related data
            anonymization_tasks = [
                ("user_feedback", {"feedback_text": "Anonymized feedback"}),
                ("api_keys", {"name": "Anonymized key"}),
                ("books", {"title": "Anonymized book", "content_summary": "Anonymized content"})
            ]
            
            for table_name, updates in anonymization_tasks:
                try:
                    updates["anonymized_at"] = datetime.utcnow().isoformat()
                    await self.supabase.table(table_name).update(updates).eq("user_id", user_id).execute()
                except Exception as e:
                    logger.error(f"Failed to anonymize {table_name}: {e}")
            
            await audit_log(
                "user_data_anonymized",
                {"user_id": user_id, "reason": "gdpr_erasure_request"},
                user_id=user_id,
                category=AuditCategory.COMPLIANCE,
                level=AuditLevel.WARNING
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to anonymize user data: {e}")
            return False
    
    async def _can_erase_data(self, user_id: str) -> bool:
        """Check if user data can be erased (exceptions under Article 17)."""
        
        # Check for legal obligations to retain data
        try:
            # Check for active legal holds
            response = await self.supabase.table("legal_holds").select("*").eq("user_id", user_id).eq("status", "active").execute()
            
            if response.data:
                return False
            
            # Check for ongoing legal proceedings
            response = await self.supabase.table("legal_proceedings").select("*").eq("user_id", user_id).eq("status", "ongoing").execute()
            
            if response.data:
                return False
            
            # Add other checks as needed (contract obligations, etc.)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to check erasure eligibility: {e}")
            return False
    
    # ============================================================================
    # Database Operations
    # ============================================================================
    
    async def _store_gdpr_request(self, request: GDPRRequest):
        """Store GDPR request in database."""
        try:
            request_data = asdict(request)
            request_data["submitted_at"] = request.submitted_at.isoformat()
            request_data["request_type"] = request.request_type.value
            
            await self.supabase.table("gdpr_requests").insert(request_data).execute()
        except Exception as e:
            logger.error(f"Failed to store GDPR request: {e}")
            raise
    
    async def _get_gdpr_request(self, request_id: str) -> Optional[GDPRRequest]:
        """Get GDPR request from database."""
        try:
            response = await self.supabase.table("gdpr_requests").select("*").eq("request_id", request_id).execute()
            
            if response.data:
                data = response.data[0]
                data["submitted_at"] = datetime.fromisoformat(data["submitted_at"])
                if data.get("completed_at"):
                    data["completed_at"] = datetime.fromisoformat(data["completed_at"])
                if data.get("verified_at"):
                    data["verified_at"] = datetime.fromisoformat(data["verified_at"])
                data["request_type"] = GDPRRights(data["request_type"])
                
                return GDPRRequest(**data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get GDPR request: {e}")
            return None
    
    async def _update_gdpr_request(self, request: GDPRRequest):
        """Update GDPR request in database."""
        try:
            request_data = asdict(request)
            request_data["request_type"] = request.request_type.value
            
            # Convert datetime fields
            for field in ["submitted_at", "completed_at", "verified_at"]:
                if request_data.get(field):
                    request_data[field] = request_data[field].isoformat()
            
            await self.supabase.table("gdpr_requests").update(request_data).eq("request_id", request.request_id).execute()
            
        except Exception as e:
            logger.error(f"Failed to update GDPR request: {e}")
            raise
    
    async def _store_consent_record(self, consent: ConsentRecord):
        """Store consent record in database."""
        try:
            consent_data = asdict(consent)
            consent_data["data_categories"] = [cat.value for cat in consent.data_categories]
            consent_data["status"] = consent.status.value
            consent_data["lawful_basis"] = consent.lawful_basis.value
            
            # Convert datetime fields
            for field in ["given_at", "withdrawn_at", "expires_at"]:
                if consent_data.get(field):
                    consent_data[field] = consent_data[field].isoformat()
            
            await self.supabase.table("consent_records").insert(consent_data).execute()
            
        except Exception as e:
            logger.error(f"Failed to store consent record: {e}")
            raise
    
    async def _get_consent_record(self, consent_id: str) -> Optional[ConsentRecord]:
        """Get consent record from database."""
        try:
            response = await self.supabase.table("consent_records").select("*").eq("consent_id", consent_id).execute()
            
            if response.data:
                data = response.data[0]
                
                # Parse enums and datetime fields
                data["data_categories"] = [DataCategory(cat) for cat in data["data_categories"]]
                data["status"] = ConsentStatus(data["status"])
                data["lawful_basis"] = ProcessingLawfulness(data["lawful_basis"])
                
                for field in ["given_at", "withdrawn_at", "expires_at"]:
                    if data.get(field):
                        data[field] = datetime.fromisoformat(data[field])
                
                return ConsentRecord(**data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get consent record: {e}")
            return None
    
    async def _update_consent_record(self, consent: ConsentRecord):
        """Update consent record in database."""
        try:
            consent_data = asdict(consent)
            consent_data["data_categories"] = [cat.value for cat in consent.data_categories]
            consent_data["status"] = consent.status.value
            consent_data["lawful_basis"] = consent.lawful_basis.value
            
            # Convert datetime fields
            for field in ["given_at", "withdrawn_at", "expires_at"]:
                if consent_data.get(field):
                    consent_data[field] = consent_data[field].isoformat()
            
            await self.supabase.table("consent_records").update(consent_data).eq("consent_id", consent.consent_id).execute()
            
        except Exception as e:
            logger.error(f"Failed to update consent record: {e}")
            raise
    
    async def _send_verification(self, request: GDPRRequest):
        """Send verification for GDPR request."""
        # In a real implementation, this would send an email or SMS
        logger.info(f"Verification sent for GDPR request {request.request_id}")
        
        # Mark as verified for demo purposes
        request.verified_at = datetime.utcnow()
        await self._update_gdpr_request(request)


# Global GDPR manager instance
_gdpr_manager: Optional[GDPRManager] = None


async def get_gdpr_manager() -> GDPRManager:
    """Get or create global GDPR manager."""
    global _gdpr_manager
    
    if _gdpr_manager is None:
        _gdpr_manager = GDPRManager()
        await _gdpr_manager.initialize()
    
    return _gdpr_manager


# Convenience functions
async def submit_data_access_request(user_id: str) -> GDPRRequest:
    """Submit data access request (convenience function)."""
    manager = await get_gdpr_manager()
    return await manager.submit_gdpr_request(user_id, GDPRRights.ACCESS)


async def submit_erasure_request(user_id: str, reason: str = "") -> GDPRRequest:
    """Submit right to be forgotten request (convenience function)."""
    manager = await get_gdpr_manager()
    return await manager.submit_gdpr_request(user_id, GDPRRights.ERASURE, reason)


async def check_user_consent(user_id: str, purpose: str) -> bool:
    """Check if user has valid consent for purpose (convenience function)."""
    manager = await get_gdpr_manager()
    return await manager.check_consent_validity(user_id, purpose)
