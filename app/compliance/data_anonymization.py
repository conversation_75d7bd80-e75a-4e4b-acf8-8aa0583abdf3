"""
Advanced Data Anonymization and Pseudonymization System.

This module provides comprehensive data anonymization capabilities including:
- K-anonymity and L-diversity algorithms
- Differential privacy mechanisms
- Pseudonymization with cryptographic protection
- Data masking and synthetic data generation
- Anonymization quality assessment
- Reversible pseudonymization for authorized users
"""

import hashlib
import hmac
import secrets
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import re
import random
import math

from pydantic import BaseModel, Field
import numpy as np
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.monitoring.audit_logger import audit_log, AuditCategory

logger = logging.getLogger(__name__)
settings = get_settings()


class AnonymizationTechnique(str, Enum):
    """Anonymization techniques."""
    SUPPRESSION = "suppression"
    GENERALIZATION = "generalization"
    PERTURBATION = "perturbation"
    PSEUDONYMIZATION = "pseudonymization"
    SYNTHETIC_DATA = "synthetic_data"
    DIFFERENTIAL_PRIVACY = "differential_privacy"
    K_ANONYMITY = "k_anonymity"
    L_DIVERSITY = "l_diversity"


class DataSensitivity(str, Enum):
    """Data sensitivity levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    HIGHLY_SENSITIVE = "highly_sensitive"


@dataclass
class AnonymizationRule:
    """Anonymization rule definition."""
    field_name: str
    technique: AnonymizationTechnique
    sensitivity: DataSensitivity
    parameters: Dict[str, Any]
    preserve_format: bool = True
    reversible: bool = False


@dataclass
class AnonymizationResult:
    """Result of anonymization operation."""
    original_records: int
    anonymized_records: int
    suppressed_records: int
    quality_metrics: Dict[str, float]
    technique_applied: str
    anonymization_id: str
    timestamp: datetime


class DifferentialPrivacyMechanism:
    """Differential privacy mechanisms for data protection."""
    
    def __init__(self, epsilon: float = 1.0):
        self.epsilon = epsilon  # Privacy budget
    
    def add_laplace_noise(self, value: float, sensitivity: float = 1.0) -> float:
        """Add Laplace noise for differential privacy."""
        scale = sensitivity / self.epsilon
        noise = np.random.laplace(0, scale)
        return value + noise
    
    def add_gaussian_noise(self, value: float, sensitivity: float = 1.0, delta: float = 1e-5) -> float:
        """Add Gaussian noise for differential privacy."""
        sigma = sensitivity * math.sqrt(2 * math.log(1.25 / delta)) / self.epsilon
        noise = np.random.normal(0, sigma)
        return value + noise
    
    def exponential_mechanism(self, candidates: List[Any], utility_scores: List[float], sensitivity: float = 1.0) -> Any:
        """Select candidate using exponential mechanism."""
        weights = [math.exp(self.epsilon * score / (2 * sensitivity)) for score in utility_scores]
        total_weight = sum(weights)
        probabilities = [w / total_weight for w in weights]
        
        # Select based on probability distribution
        r = random.random()
        cumulative = 0
        for i, prob in enumerate(probabilities):
            cumulative += prob
            if r <= cumulative:
                return candidates[i]
        
        return candidates[-1]


class KAnonymizer:
    """K-anonymity implementation."""
    
    def __init__(self, k: int = 3):
        self.k = k
    
    def generalize_age(self, age: int, level: int = 1) -> str:
        """Generalize age values."""
        if level == 1:
            return f"{age//10 * 10}-{age//10 * 10 + 9}"
        elif level == 2:
            return f"{age//20 * 20}-{age//20 * 20 + 19}"
        else:
            return "18+"
    
    def generalize_location(self, location: str, level: int = 1) -> str:
        """Generalize location information."""
        parts = location.split(", ")
        if level == 1 and len(parts) >= 2:
            return parts[-1]  # Country only
        elif level == 2 and len(parts) >= 3:
            return f"{parts[-2]}, {parts[-1]}"  # State, Country
        else:
            return "Unknown"
    
    def check_k_anonymity(self, data: List[Dict], quasi_identifiers: List[str]) -> Dict[str, Any]:
        """Check if dataset satisfies k-anonymity."""
        groups = {}
        
        for record in data:
            key = tuple(record.get(qi, "") for qi in quasi_identifiers)
            if key not in groups:
                groups[key] = []
            groups[key].append(record)
        
        min_group_size = min(len(group) for group in groups.values())
        satisfied = min_group_size >= self.k
        
        return {
            "k_satisfied": satisfied,
            "min_group_size": min_group_size,
            "total_groups": len(groups),
            "groups_below_k": sum(1 for group in groups.values() if len(group) < self.k)
        }


class DataAnonymizer:
    """
    Comprehensive data anonymization system.
    
    Features:
    - Multiple anonymization techniques
    - Quality-preserving anonymization
    - Reversible pseudonymization
    - Differential privacy mechanisms
    - K-anonymity and L-diversity
    - Synthetic data generation
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.encryption_key = self._derive_encryption_key()
        self.fernet = Fernet(self.encryption_key)
        
        # Default anonymization rules
        self.default_rules = {
            "email": AnonymizationRule(
                field_name="email",
                technique=AnonymizationTechnique.PSEUDONYMIZATION,
                sensitivity=DataSensitivity.CONFIDENTIAL,
                parameters={"domain": "@anonymous.local"},
                reversible=True
            ),
            "name": AnonymizationRule(
                field_name="name",
                technique=AnonymizationTechnique.GENERALIZATION,
                sensitivity=DataSensitivity.CONFIDENTIAL,
                parameters={"replacement": "Anonymous User"}
            ),
            "ip_address": AnonymizationRule(
                field_name="ip_address",
                technique=AnonymizationTechnique.GENERALIZATION,
                sensitivity=DataSensitivity.INTERNAL,
                parameters={"mask_last_octet": True}
            ),
            "phone": AnonymizationRule(
                field_name="phone",
                technique=AnonymizationTechnique.PERTURBATION,
                sensitivity=DataSensitivity.CONFIDENTIAL,
                parameters={"preserve_format": True}
            ),
            "user_agent": AnonymizationRule(
                field_name="user_agent",
                technique=AnonymizationTechnique.GENERALIZATION,
                sensitivity=DataSensitivity.INTERNAL,
                parameters={"extract_browser_only": True}
            )
        }
        
        # Differential privacy mechanism
        self.dp_mechanism = DifferentialPrivacyMechanism(epsilon=1.0)
        
        # K-anonymizer
        self.k_anonymizer = KAnonymizer(k=3)
    
    def _derive_encryption_key(self) -> bytes:
        """Derive encryption key from settings."""
        password = settings.secret_key.encode()
        salt = b"anonymization_salt"  # In production, use a proper salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    async def anonymize_dataset(
        self,
        data: List[Dict[str, Any]],
        rules: Optional[Dict[str, AnonymizationRule]] = None,
        technique: Optional[AnonymizationTechnique] = None,
        k_value: int = 3
    ) -> AnonymizationResult:
        """Anonymize a complete dataset."""
        
        anonymization_id = secrets.token_urlsafe(16)
        start_time = datetime.utcnow()
        
        # Use provided rules or defaults
        active_rules = rules or self.default_rules
        
        original_count = len(data)
        anonymized_data = []
        suppressed_count = 0
        
        for record in data:
            try:
                anonymized_record = await self._anonymize_record(record, active_rules, technique)
                if anonymized_record:
                    anonymized_data.append(anonymized_record)
                else:
                    suppressed_count += 1
            except Exception as e:
                logger.error(f"Failed to anonymize record: {e}")
                suppressed_count += 1
        
        # Apply k-anonymity if requested
        if technique == AnonymizationTechnique.K_ANONYMITY:
            anonymized_data = await self._apply_k_anonymity(anonymized_data, k_value)
        
        # Calculate quality metrics
        quality_metrics = await self._calculate_quality_metrics(data, anonymized_data)
        
        result = AnonymizationResult(
            original_records=original_count,
            anonymized_records=len(anonymized_data),
            suppressed_records=suppressed_count,
            quality_metrics=quality_metrics,
            technique_applied=technique.value if technique else "mixed",
            anonymization_id=anonymization_id,
            timestamp=start_time
        )
        
        # Store anonymization metadata
        await self._store_anonymization_metadata(result, active_rules)
        
        await audit_log(
            "dataset_anonymized",
            {
                "anonymization_id": anonymization_id,
                "original_records": original_count,
                "anonymized_records": len(anonymized_data),
                "technique": technique.value if technique else "mixed"
            },
            category=AuditCategory.DATA_MODIFICATION
        )
        
        return result
    
    async def _anonymize_record(
        self,
        record: Dict[str, Any],
        rules: Dict[str, AnonymizationRule],
        global_technique: Optional[AnonymizationTechnique] = None
    ) -> Optional[Dict[str, Any]]:
        """Anonymize a single record."""
        
        anonymized = record.copy()
        
        for field_name, value in record.items():
            if field_name in rules:
                rule = rules[field_name]
                technique = global_technique or rule.technique
                
                try:
                    anonymized[field_name] = await self._apply_technique(
                        value, technique, rule.parameters, rule.reversible
                    )
                except Exception as e:
                    logger.error(f"Failed to anonymize field {field_name}: {e}")
                    # Suppress the field if anonymization fails
                    anonymized[field_name] = None
        
        return anonymized
    
    async def _apply_technique(
        self,
        value: Any,
        technique: AnonymizationTechnique,
        parameters: Dict[str, Any],
        reversible: bool = False
    ) -> Any:
        """Apply specific anonymization technique."""
        
        if value is None:
            return None
        
        if technique == AnonymizationTechnique.SUPPRESSION:
            return None
        
        elif technique == AnonymizationTechnique.GENERALIZATION:
            return await self._generalize_value(value, parameters)
        
        elif technique == AnonymizationTechnique.PERTURBATION:
            return await self._perturb_value(value, parameters)
        
        elif technique == AnonymizationTechnique.PSEUDONYMIZATION:
            return await self._pseudonymize_value(value, parameters, reversible)
        
        elif technique == AnonymizationTechnique.DIFFERENTIAL_PRIVACY:
            return await self._apply_differential_privacy(value, parameters)
        
        elif technique == AnonymizationTechnique.SYNTHETIC_DATA:
            return await self._generate_synthetic_value(value, parameters)
        
        else:
            return value
    
    async def _generalize_value(self, value: Any, parameters: Dict[str, Any]) -> Any:
        """Apply generalization to a value."""
        
        if "replacement" in parameters:
            return parameters["replacement"]
        
        if isinstance(value, str):
            if "mask_last_octet" in parameters and self._is_ip_address(value):
                parts = value.split(".")
                if len(parts) == 4:
                    return f"{'.'.join(parts[:3])}.0"
            
            if "extract_browser_only" in parameters:
                # Extract browser name from user agent
                browser_patterns = {
                    "Chrome": r"Chrome/[\d.]+",
                    "Firefox": r"Firefox/[\d.]+",
                    "Safari": r"Safari/[\d.]+",
                    "Edge": r"Edge/[\d.]+",
                    "Opera": r"Opera/[\d.]+"
                }
                
                for browser, pattern in browser_patterns.items():
                    if re.search(pattern, value, re.IGNORECASE):
                        return browser
                
                return "Unknown Browser"
            
            if "domain" in parameters:
                # Replace email domain
                if "@" in value:
                    local_part = value.split("@")[0]
                    return f"{local_part}{parameters['domain']}"
        
        elif isinstance(value, (int, float)):
            if "age_range" in parameters:
                age = int(value)
                return self.k_anonymizer.generalize_age(age, parameters.get("level", 1))
        
        return value
    
    async def _perturb_value(self, value: Any, parameters: Dict[str, Any]) -> Any:
        """Apply perturbation to a value."""
        
        if isinstance(value, str) and parameters.get("preserve_format"):
            if self._is_phone_number(value):
                # Preserve phone format but change digits
                pattern = re.sub(r'\d', lambda m: str(random.randint(0, 9)), value)
                return pattern
            
            if self._is_email(value):
                local, domain = value.split("@")
                perturbed_local = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(len(local)))
                return f"{perturbed_local}@{domain}"
        
        elif isinstance(value, (int, float)):
            noise_factor = parameters.get("noise_factor", 0.1)
            noise = random.uniform(-noise_factor, noise_factor) * value
            return value + noise
        
        return value
    
    async def _pseudonymize_value(
        self,
        value: Any,
        parameters: Dict[str, Any],
        reversible: bool = False
    ) -> str:
        """Apply pseudonymization to a value."""
        
        if reversible:
            # Encrypted pseudonymization (reversible)
            encrypted = self.fernet.encrypt(str(value).encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        else:
            # Hash-based pseudonymization (irreversible)
            salt = parameters.get("salt", "default_salt")
            combined = f"{value}{salt}"
            return hashlib.sha256(combined.encode()).hexdigest()
    
    async def _apply_differential_privacy(self, value: Any, parameters: Dict[str, Any]) -> Any:
        """Apply differential privacy mechanisms."""
        
        if isinstance(value, (int, float)):
            sensitivity = parameters.get("sensitivity", 1.0)
            mechanism = parameters.get("mechanism", "laplace")
            
            if mechanism == "laplace":
                return self.dp_mechanism.add_laplace_noise(float(value), sensitivity)
            elif mechanism == "gaussian":
                delta = parameters.get("delta", 1e-5)
                return self.dp_mechanism.add_gaussian_noise(float(value), sensitivity, delta)
        
        return value
    
    async def _generate_synthetic_value(self, value: Any, parameters: Dict[str, Any]) -> Any:
        """Generate synthetic data based on original value."""
        
        if isinstance(value, str):
            if self._is_email(value):
                return self._generate_synthetic_email()
            elif self._is_phone_number(value):
                return self._generate_synthetic_phone()
            else:
                return self._generate_synthetic_string(len(value))
        
        elif isinstance(value, int):
            min_val = parameters.get("min_value", 0)
            max_val = parameters.get("max_value", value * 2)
            return random.randint(min_val, max_val)
        
        elif isinstance(value, float):
            min_val = parameters.get("min_value", 0.0)
            max_val = parameters.get("max_value", value * 2)
            return random.uniform(min_val, max_val)
        
        return value
    
    async def _apply_k_anonymity(self, data: List[Dict], k: int) -> List[Dict]:
        """Apply k-anonymity to dataset."""
        
        # Identify quasi-identifiers (this would be configurable)
        quasi_identifiers = ["age", "location", "gender", "occupation"]
        
        # Check current k-anonymity
        k_check = self.k_anonymizer.check_k_anonymity(data, quasi_identifiers)
        
        if k_check["k_satisfied"]:
            return data
        
        # Apply generalization to achieve k-anonymity
        generalized_data = []
        for record in data:
            generalized = record.copy()
            
            # Apply increasing levels of generalization
            if "age" in record:
                generalized["age"] = self.k_anonymizer.generalize_age(record["age"], level=2)
            
            if "location" in record:
                generalized["location"] = self.k_anonymizer.generalize_location(record["location"], level=2)
            
            generalized_data.append(generalized)
        
        return generalized_data
    
    async def _calculate_quality_metrics(
        self,
        original_data: List[Dict],
        anonymized_data: List[Dict]
    ) -> Dict[str, float]:
        """Calculate data quality metrics after anonymization."""
        
        if not original_data or not anonymized_data:
            return {"data_loss": 1.0, "information_loss": 1.0}
        
        # Calculate data loss (records suppressed)
        data_loss = 1.0 - (len(anonymized_data) / len(original_data))
        
        # Calculate information loss (simplified)
        total_fields = 0
        suppressed_fields = 0
        
        for i, orig_record in enumerate(original_data[:len(anonymized_data)]):
            anon_record = anonymized_data[i]
            
            for field, orig_value in orig_record.items():
                total_fields += 1
                anon_value = anon_record.get(field)
                
                if anon_value is None or anon_value != orig_value:
                    suppressed_fields += 1
        
        information_loss = suppressed_fields / total_fields if total_fields > 0 else 0
        
        return {
            "data_loss": data_loss,
            "information_loss": information_loss,
            "utility_score": 1.0 - information_loss,
            "privacy_score": information_loss
        }
    
    def _is_email(self, value: str) -> bool:
        """Check if value is an email address."""
        return "@" in value and "." in value.split("@")[-1]
    
    def _is_phone_number(self, value: str) -> bool:
        """Check if value is a phone number."""
        return bool(re.match(r'^[\+]?[\d\s\-\(\)]+$', value))
    
    def _is_ip_address(self, value: str) -> bool:
        """Check if value is an IP address."""
        parts = value.split(".")
        if len(parts) != 4:
            return False
        
        try:
            return all(0 <= int(part) <= 255 for part in parts)
        except ValueError:
            return False
    
    def _generate_synthetic_email(self) -> str:
        """Generate synthetic email address."""
        domains = ["example.com", "test.local", "anonymous.org"]
        username = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=8))
        domain = random.choice(domains)
        return f"{username}@{domain}"
    
    def _generate_synthetic_phone(self) -> str:
        """Generate synthetic phone number."""
        return f"+1-{random.randint(200, 999)}-{random.randint(100, 999)}-{random.randint(1000, 9999)}"
    
    def _generate_synthetic_string(self, length: int) -> str:
        """Generate synthetic string of specified length."""
        return ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', k=length))
    
    async def deanonymize_value(self, pseudonym: str) -> Optional[str]:
        """Reverse pseudonymization for authorized users."""
        
        try:
            # Only works for reversible pseudonymization
            encrypted_data = base64.urlsafe_b64decode(pseudonym.encode())
            decrypted = self.fernet.decrypt(encrypted_data)
            return decrypted.decode()
        except Exception:
            return None
    
    async def _store_anonymization_metadata(
        self,
        result: AnonymizationResult,
        rules: Dict[str, AnonymizationRule]
    ):
        """Store anonymization operation metadata."""
        
        try:
            metadata = {
                "anonymization_id": result.anonymization_id,
                "timestamp": result.timestamp.isoformat(),
                "original_records": result.original_records,
                "anonymized_records": result.anonymized_records,
                "suppressed_records": result.suppressed_records,
                "quality_metrics": result.quality_metrics,
                "technique_applied": result.technique_applied,
                "rules_applied": {
                    name: {
                        "technique": rule.technique.value,
                        "sensitivity": rule.sensitivity.value,
                        "reversible": rule.reversible
                    } for name, rule in rules.items()
                }
            }
            
            await self.supabase.table("anonymization_operations").insert(metadata).execute()
            
        except Exception as e:
            logger.error(f"Failed to store anonymization metadata: {e}")


# Global anonymizer instance
_data_anonymizer: Optional[DataAnonymizer] = None


async def get_data_anonymizer() -> DataAnonymizer:
    """Get or create global data anonymizer."""
    global _data_anonymizer
    
    if _data_anonymizer is None:
        _data_anonymizer = DataAnonymizer()
    
    return _data_anonymizer


# Convenience functions
async def anonymize_user_data(user_id: str, technique: AnonymizationTechnique = AnonymizationTechnique.GENERALIZATION) -> AnonymizationResult:
    """Anonymize all data for a specific user."""
    anonymizer = await get_data_anonymizer()
    
    # This would collect user data and anonymize it
    # Implementation would depend on specific requirements
    
    return AnonymizationResult(
        original_records=1,
        anonymized_records=1,
        suppressed_records=0,
        quality_metrics={"data_loss": 0.0, "information_loss": 0.5},
        technique_applied=technique.value,
        anonymization_id=secrets.token_urlsafe(16),
        timestamp=datetime.utcnow()
    )


async def create_anonymization_rule(
    field_name: str,
    technique: AnonymizationTechnique,
    sensitivity: DataSensitivity,
    **parameters
) -> AnonymizationRule:
    """Create a new anonymization rule."""
    return AnonymizationRule(
        field_name=field_name,
        technique=technique,
        sensitivity=sensitivity,
        parameters=parameters
    )
