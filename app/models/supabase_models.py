# app/models/supabase_models.py - Supabase Model Layer

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from uuid import UUID
import uuid
from postgrest import CountMethod
from pydantic import BaseModel, Field
import logging
import json

from supabase import create_client
from app.config import settings
from app.utils.supabase.supabase_client import get_supabase_client_async

logger = logging.getLogger(__name__)


class UserModel(BaseModel):
    """User model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new user profile"""
        try:
            client = await self._get_client()
            result = client.table("users").insert(user_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from user creation")
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            raise

    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get user: {e}")
            return None

    async def update_user(
        self, user_id: str, updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update user profile"""
        try:
            updates["updated_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = (
                client.table("users")
                .update(updates)
                .eq("id", user_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from user update")
        except Exception as e:
            logger.error(f"Failed to update user: {e}")
            raise

    async def store_user_api_key(
        self, user_id: str, service: str, api_key: str
    ) -> bool:
        """Store API key for a user"""
        try:
            # Store encrypted API keys in user preferences or separate table
            # For now, store in user's content_preferences as JSON
            user = await self.get_user_by_id(user_id)
            if not user:
                return False

            content_prefs = user.get("content_preferences", {})
            api_keys = content_prefs.get("api_keys", {})
            api_keys[service] = api_key  # In production, this should be encrypted
            content_prefs["api_keys"] = api_keys

            await self.update_user(user_id, {"content_preferences": content_prefs})
            return True
        except Exception as e:
            logger.error(f"Failed to store API key: {e}")
            return False

    async def get_user_api_key(self, user_id: str, service: str) -> Optional[str]:
        """Get API key for a user"""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return None

            content_prefs = user.get("content_preferences", {})
            api_keys = content_prefs.get("api_keys", {})
            return api_keys.get(service)
        except Exception as e:
            logger.error(f"Failed to get API key: {e}")
            return None


class UserPreferencesModel(BaseModel):
    """User preferences model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user preferences"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                user_data = result.data[0]
                return {
                    "user_id": user_data.get("id"),
                    "theme": user_data.get("theme", "light"),
                    "language": user_data.get("language", "en"),
                    "timezone": user_data.get("timezone", "UTC"),
                    "email_notifications": user_data.get("email_notifications", True),
                    "push_notifications": user_data.get("push_notifications", True),
                    "marketing_emails": user_data.get("marketing_emails", False),
                    "content_preferences": user_data.get("content_preferences", {}),
                    "accessibility": user_data.get("accessibility", {}),
                    "privacy_level": user_data.get("privacy_level", "standard"),
                    "updated_at": user_data.get("updated_at")
                }
            return {"theme": "light", "language": "en", "timezone": "UTC"}
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            return {"theme": "light", "language": "en", "timezone": "UTC"}

    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """Update user preferences"""
        try:
            client = await self._get_client()
            
            # Prepare update data
            update_data = {
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Map preferences to user table columns
            if "theme" in preferences:
                update_data["theme"] = preferences["theme"]
            if "language" in preferences:
                update_data["language"] = preferences["language"]
            if "timezone" in preferences:
                update_data["timezone"] = preferences["timezone"]
            if "email_notifications" in preferences:
                update_data["email_notifications"] = preferences["email_notifications"]
            if "push_notifications" in preferences:
                update_data["push_notifications"] = preferences["push_notifications"]
            if "marketing_emails" in preferences:
                update_data["marketing_emails"] = preferences["marketing_emails"]
            if "content_preferences" in preferences:
                update_data["content_preferences"] = preferences["content_preferences"]
            if "accessibility" in preferences:
                update_data["accessibility"] = preferences["accessibility"]
            if "privacy_level" in preferences:
                update_data["privacy_level"] = preferences["privacy_level"]
            
            result = (
                client.table("users")
                .update(update_data)
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                return await self.get_user_preferences(user_id)
            raise Exception("No data returned from preference update")
        except Exception as e:
            logger.error(f"Failed to update user preferences: {e}")
            raise


class UserPublishingSettingsModel(BaseModel):
    """User publishing settings model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_publishing_settings(self, user_id: str) -> Dict[str, Any]:
        """Get user's publishing settings"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                user_data = result.data[0]
                return {
                    "user_id": user_data.get("id"),
                    "preferred_ai_provider": user_data.get("preferred_ai_provider", "openai"),
                    "default_writing_style": user_data.get("default_writing_style", "professional"),
                    "default_target_audience": user_data.get("default_target_audience", "general adults"),
                    "auto_publish_enabled": user_data.get("auto_publish_enabled", False),
                    "quality_threshold": user_data.get("quality_threshold", 0.8),
                    "content_preferences": user_data.get("content_preferences", {}),
                    "publishing_defaults": user_data.get("publishing_defaults", {}),
                    "updated_at": user_data.get("updated_at")
                }
            return {"preferred_ai_provider": "openai", "default_writing_style": "professional"}
        except Exception as e:
            logger.error(f"Failed to get publishing settings: {e}")
            return {"preferred_ai_provider": "openai", "default_writing_style": "professional"}

    async def update_publishing_settings(self, user_id: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update user's publishing settings"""
        try:
            client = await self._get_client()
            
            # Prepare update data
            update_data = {
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Map settings to user table columns
            if "preferred_ai_provider" in settings:
                update_data["preferred_ai_provider"] = settings["preferred_ai_provider"]
            if "default_writing_style" in settings:
                update_data["default_writing_style"] = settings["default_writing_style"]
            if "default_target_audience" in settings:
                update_data["default_target_audience"] = settings["default_target_audience"]
            if "auto_publish_enabled" in settings:
                update_data["auto_publish_enabled"] = settings["auto_publish_enabled"]
            if "quality_threshold" in settings:
                update_data["quality_threshold"] = settings["quality_threshold"]
            if "content_preferences" in settings:
                update_data["content_preferences"] = settings["content_preferences"]
            if "publishing_defaults" in settings:
                update_data["publishing_defaults"] = settings["publishing_defaults"]
            
            result = (
                client.table("users")
                .update(update_data)
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                return await self.get_publishing_settings(user_id)
            raise Exception("No data returned from publishing settings update")
        except Exception as e:
            logger.error(f"Failed to update publishing settings: {e}")
            raise


class UserPlatformIntegrationsModel(BaseModel):
    """User platform integrations model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_platform_integrations(self, user_id: str) -> Dict[str, Any]:
        """Get user's platform integrations"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                user_data = result.data[0]
                # Get platform integrations from user preferences
                platform_integrations = user_data.get("platform_integrations", {})
                
                return {
                    "user_id": user_data.get("id"),
                    "amazon_kdp": {
                        "enabled": platform_integrations.get("amazon_kdp", {}).get("enabled", False),
                        "account_id": platform_integrations.get("amazon_kdp", {}).get("account_id"),
                        "auto_publish": platform_integrations.get("amazon_kdp", {}).get("auto_publish", False),
                        "last_sync": platform_integrations.get("amazon_kdp", {}).get("last_sync")
                    },
                    "medium": {
                        "enabled": platform_integrations.get("medium", {}).get("enabled", False),
                        "username": platform_integrations.get("medium", {}).get("username"),
                        "auto_publish": platform_integrations.get("medium", {}).get("auto_publish", False),
                        "last_sync": platform_integrations.get("medium", {}).get("last_sync")
                    },
                    "substack": {
                        "enabled": platform_integrations.get("substack", {}).get("enabled", False),
                        "publication_url": platform_integrations.get("substack", {}).get("publication_url"),
                        "auto_publish": platform_integrations.get("substack", {}).get("auto_publish", False),
                        "last_sync": platform_integrations.get("substack", {}).get("last_sync")
                    },
                    "wordpress": {
                        "enabled": platform_integrations.get("wordpress", {}).get("enabled", False),
                        "site_url": platform_integrations.get("wordpress", {}).get("site_url"),
                        "auto_publish": platform_integrations.get("wordpress", {}).get("auto_publish", False),
                        "last_sync": platform_integrations.get("wordpress", {}).get("last_sync")
                    },
                    "updated_at": user_data.get("updated_at")
                }
            return {"amazon_kdp": {"enabled": False}, "medium": {"enabled": False}}
        except Exception as e:
            logger.error(f"Failed to get platform integrations: {e}")
            return {"amazon_kdp": {"enabled": False}, "medium": {"enabled": False}}

    async def update_platform_integration(self, user_id: str, platform: str, integration_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update specific platform integration"""
        try:
            client = await self._get_client()
            
            # Get current integrations
            current_integrations = await self.get_platform_integrations(user_id)
            
            # Update the specific platform
            if platform in current_integrations:
                current_integrations[platform].update(integration_data)
            else:
                current_integrations[platform] = integration_data
            
            # Remove metadata fields
            platform_integrations = {k: v for k, v in current_integrations.items() 
                                   if k not in ["user_id", "updated_at"]}
            
            update_data = {
                "platform_integrations": platform_integrations,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = (
                client.table("users")
                .update(update_data)
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                return await self.get_platform_integrations(user_id)
            raise Exception("No data returned from platform integration update")
        except Exception as e:
            logger.error(f"Failed to update platform integration: {e}")
            raise


class UserSecuritySettingsModel(BaseModel):
    """User security settings model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_security_settings(self, user_id: str) -> Dict[str, Any]:
        """Get user's security settings"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                user_data = result.data[0]
                security_settings = user_data.get("security_settings", {})
                
                return {
                    "user_id": user_data.get("id"),
                    "two_factor_enabled": security_settings.get("two_factor_enabled", False),
                    "login_notifications": security_settings.get("login_notifications", True),
                    "session_timeout": security_settings.get("session_timeout", 3600),
                    "ip_whitelist": security_settings.get("ip_whitelist", []),
                    "device_tracking": security_settings.get("device_tracking", True),
                    "api_key_access": security_settings.get("api_key_access", True),
                    "data_export_enabled": security_settings.get("data_export_enabled", True),
                    "account_deletion_protection": security_settings.get("account_deletion_protection", True),
                    "last_password_change": user_data.get("last_password_change"),
                    "updated_at": user_data.get("updated_at")
                }
            return {"two_factor_enabled": False, "login_notifications": True}
        except Exception as e:
            logger.error(f"Failed to get security settings: {e}")
            return {"two_factor_enabled": False, "login_notifications": True}

    async def update_security_settings(self, user_id: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update user's security settings"""
        try:
            client = await self._get_client()
            
            # Get current security settings
            current_settings = await self.get_security_settings(user_id)
            security_settings = {k: v for k, v in current_settings.items() 
                               if k not in ["user_id", "updated_at", "last_password_change"]}
            
            # Update with new settings
            security_settings.update(settings)
            
            update_data = {
                "security_settings": security_settings,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = (
                client.table("users")
                .update(update_data)
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                return await self.get_security_settings(user_id)
            raise Exception("No data returned from security settings update")
        except Exception as e:
            logger.error(f"Failed to update security settings: {e}")
            raise

    async def change_password(self, user_id: str, old_password: str, new_password: str) -> Dict[str, Any]:
        """Change user password (Note: In production, this should integrate with Supabase Auth)"""
        try:
            # This is a placeholder - in production, use Supabase Auth API
            # For now, just update the last_password_change timestamp
            client = await self._get_client()
            
            update_data = {
                "last_password_change": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = (
                client.table("users")
                .update(update_data)
                .eq("id", user_id)
                .execute()
            )
            
            if result.data:
                return {"success": True, "message": "Password changed successfully"}
            raise Exception("Failed to update password change timestamp")
        except Exception as e:
            logger.error(f"Failed to change password: {e}")
            raise


class UserActiveSessionsModel(BaseModel):
    """User active sessions model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_active_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's active sessions"""
        try:
            client = await self._get_client()
            
            # Note: This is a mock implementation. In production, you'd query actual session data
            # For now, create mock session data based on current timestamp
            current_time = datetime.utcnow()
            
            sessions = [
                {
                    "session_id": str(uuid.uuid4()),
                    "device_type": "desktop",
                    "browser": "Chrome",
                    "ip_address": "*************",
                    "location": "New York, NY",
                    "created_at": (current_time - timedelta(hours=2)).isoformat(),
                    "last_activity": current_time.isoformat(),
                    "is_current": True
                },
                {
                    "session_id": str(uuid.uuid4()),
                    "device_type": "mobile",
                    "browser": "Safari",
                    "ip_address": "*************",
                    "location": "New York, NY",
                    "created_at": (current_time - timedelta(days=1)).isoformat(),
                    "last_activity": (current_time - timedelta(hours=6)).isoformat(),
                    "is_current": False
                }
            ]
            
            return sessions
        except Exception as e:
            logger.error(f"Failed to get active sessions: {e}")
            return []

    async def terminate_session(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """Terminate a specific session"""
        try:
            # This is a placeholder - in production, you'd terminate the actual session
            # For now, just return success
            return {
                "success": True,
                "message": f"Session {session_id} terminated successfully"
            }
        except Exception as e:
            logger.error(f"Failed to terminate session: {e}")
            raise

    async def terminate_all_sessions(self, user_id: str) -> Dict[str, Any]:
        """Terminate all user sessions except current"""
        try:
            # This is a placeholder - in production, you'd terminate all sessions
            # For now, just return success
            return {
                "success": True,
                "message": "All other sessions terminated successfully"
            }
        except Exception as e:
            logger.error(f"Failed to terminate all sessions: {e}")
            raise


class UserApiKeysModel(BaseModel):
    """User API keys model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_user_api_keys(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's API keys"""
        try:
            client = await self._get_client()
            result = (
                client.table("api_keys")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )
            
            if result.data:
                return [
                    {
                        "key_id": key["id"],
                        "name": key["name"],
                        "key_prefix": key["key_hash"][:8] + "...",  # Show only prefix for security
                        "permissions": key["permissions"],
                        "usage_count": key["usage_count"],
                        "last_used": key["last_used_at"],
                        "created_at": key["created_at"],
                        "expires_at": key["expires_at"],
                        "is_active": key["is_active"]
                    }
                    for key in result.data
                ]
            return []
        except Exception as e:
            logger.error(f"Failed to get user API keys: {e}")
            return []

    async def create_api_key(self, user_id: str, key_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new API key for user"""
        try:
            client = await self._get_client()
            
            # Generate API key
            api_key = f"pk_{uuid.uuid4().hex[:24]}"
            
            insert_data = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "name": key_data.get("name", "Default API Key"),
                "key_hash": api_key,  # In production, this should be hashed
                "permissions": key_data.get("permissions", ["read"]),
                "usage_count": 0,
                "usage_limit": key_data.get("usage_limit", 1000),
                "is_active": True,
                "expires_at": key_data.get("expires_at"),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = client.table("api_keys").insert(insert_data).execute()
            
            if result.data:
                return {
                    "key_id": result.data[0]["id"],
                    "api_key": api_key,  # Return full key only once
                    "name": result.data[0]["name"],
                    "permissions": result.data[0]["permissions"],
                    "created_at": result.data[0]["created_at"],
                    "expires_at": result.data[0]["expires_at"]
                }
            raise Exception("No data returned from API key creation")
        except Exception as e:
            logger.error(f"Failed to create API key: {e}")
            raise

    async def regenerate_api_key(self, user_id: str, key_id: str) -> Dict[str, Any]:
        """Regenerate an existing API key"""
        try:
            client = await self._get_client()
            
            # Generate new API key
            new_api_key = f"pk_{uuid.uuid4().hex[:24]}"
            
            update_data = {
                "key_hash": new_api_key,  # In production, this should be hashed
                "usage_count": 0,
                "last_used_at": None,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = (
                client.table("api_keys")
                .update(update_data)
                .eq("id", key_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            if result.data:
                return {
                    "key_id": result.data[0]["id"],
                    "api_key": new_api_key,  # Return full key only once
                    "name": result.data[0]["name"],
                    "updated_at": result.data[0]["updated_at"]
                }
            raise Exception("No data returned from API key regeneration")
        except Exception as e:
            logger.error(f"Failed to regenerate API key: {e}")
            raise


class BookModel(BaseModel):
    """Book model for Supabase operations"""

    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def create_book(self, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new book"""
        try:
            book_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("books").insert(book_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from book creation")
        except Exception as e:
            logger.error(f"Failed to create book: {e}")
            raise

    async def get_book_by_id(self, book_id: str) -> Optional[Dict[str, Any]]:
        """Get book by ID"""
        try:
            client = await self._get_client()
            result = (
                client.table("books")
                .select("*")
                .eq("id", book_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get book: {e}")
            return None

    async def update_book(
        self, book_id: str, updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update book"""
        try:
            updates["updated_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = (
                client.table("books")
                .update(updates)
                .eq("id", book_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from book update")
        except Exception as e:
            logger.error(f"Failed to update book: {e}")
            raise

    async def get_user_books(
        self, user_id: str, status: Optional[str] = None, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get books for a user"""
        try:
            client = await self._get_client()
            query = (
                client.table("books")
                .select("*")
                .eq("user_id", user_id)
            )
            
            # Only filter by status if it's provided
            if status:
                query = query.eq("status", status)
                
            result = query.limit(limit).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get user books: {e}")
            return []

    async def get_books_with_performance_feedback(
        self, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get books with performance feedback after a certain date"""
        try:
            # Query books with related feedback metrics
            client = await self._get_client()
            result = (
                client.table("books")
                .select("*, feedback_metrics!inner(*)")
                .gte("created_at", cutoff_date.isoformat())
                .execute()
            )

            # Process results to include aggregated feedback
            books_with_feedback = []
            for book in result.data or []:
                feedback_metrics = book.pop("feedback_metrics", [])

                # Aggregate feedback data
                if feedback_metrics:
                    approved_count = sum(
                        1 for fm in feedback_metrics if fm.get("approved")
                    )
                    total_count = len(feedback_metrics)
                    avg_quality = sum(
                        fm.get("metric_value", 0)
                        for fm in feedback_metrics
                        if fm.get("metric_type") == "quality_score"
                    ) / max(1, total_count)

                    book["performance"] = {
                        "user_approval": approved_count > total_count / 2,
                        "approval_rate": approved_count / max(1, total_count),
                        "quality_score": avg_quality,
                        "feedback_count": total_count,
                        "reward_signal": sum(
                            fm.get("reward_signal", 0) for fm in feedback_metrics
                        )
                        / max(1, total_count),
                    }
                    books_with_feedback.append(book)

            return books_with_feedback
        except Exception as e:
            logger.error(f"Failed to get books with performance feedback: {e}")
            return []

    async def get_books_with_performance(
        self, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get books with performance data (alias for get_books_with_performance_feedback)"""
        return await self.get_books_with_performance_feedback(cutoff_date)

    async def get_books_by_category(
        self, category: str, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get books by category"""
        try:
            client = await self._get_client()
            result = (
                client.table("books").select("*")
                .eq("category", category)
                .limit(limit)
                .execute()
            )
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get books by category: {e}")
            return []

    async def delete_book(self, book_id: str) -> bool:
        """Delete a book"""
        try:
            client = await self._get_client()
            result = (
                client.table("books").delete()
                .eq("id", book_id)
                .execute()
            )
            return True
        except Exception as e:
            logger.error(f"Failed to delete book: {e}")
            return False

    async def approve_book(self, book_id: str, user_id: str) -> Dict[str, Any]:
        """Approve a book manuscript"""
        try:
            updates = {
                "status": "approved",
                "approved_at": datetime.utcnow().isoformat(),
                "approved_by": user_id,
                "updated_at": datetime.utcnow().isoformat()
            }
            client = await self._get_client()
            result = (
                client.table("books")
                .update(updates)
                .eq("id", book_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from book approval")
        except Exception as e:
            logger.error(f"Failed to approve book: {e}")
            raise

    async def reject_book(self, book_id: str, reason: str) -> Dict[str, Any]:
        """Reject a book manuscript"""
        try:
            updates = {
                "status": "rejected",
                "rejection_reason": reason,
                "rejected_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            client = await self._get_client()
            result = (
                client.table("books")
                .update(updates)
                .eq("id", book_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from book rejection")
        except Exception as e:
            logger.error(f"Failed to reject book: {e}")
            raise


# Pydantic model for Feedback data validation
class Feedback(BaseModel):
    id: UUID
    user_id: UUID
    book_id: UUID

    metric_type: str
    metric_value: float
    metric_context: Dict[str, Any] = Field(default_factory=dict)

    approved: Optional[bool] = None
    approval_time_seconds: Optional[float] = None
    rejection_reason: Optional[str] = None
    user_notes: Optional[str] = None
    quality_rating: Optional[int] = None

    reward_signal: Optional[float] = None
    training_weight: Optional[float] = None
    used_in_training: Optional[bool] = None
    training_session_id: Optional[uuid.UUID] = None

    created_at: datetime
    processed_at: Optional[datetime] = None


class ModelPerformance(BaseModel):
    """Pydantic model for model performance validation"""
    id: UUID
    user_id: Optional[UUID] = None
    book_id: Optional[UUID] = None
    
    model_name: str
    model_version: str = "1.0"
    training_session_id: Optional[UUID] = None
    
    # Performance metrics
    accuracy_score: float = 0.0
    precision_score: float = 0.0
    recall_score: float = 0.0
    f1_score: float = 0.0
    
    # VERL-specific metrics
    reward_score: float = 0.0
    policy_loss: float = 0.0
    value_loss: float = 0.0
    entropy_loss: float = 0.0
    
    # Generation quality metrics
    user_approval_rate: float = 0.0
    avg_quality_score: float = 0.0
    content_coherence_score: float = 0.0
    style_consistency_score: float = 0.0
    
    # Business performance metrics
    conversion_rate: float = 0.0
    avg_sales_performance: float = 0.0
    revenue_per_book: float = 0.0
    time_to_approval_minutes: float = 0.0
    
    evaluated_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None


class FeedbackModelOriginal:
    """Feedback model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def create_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create feedback record"""
        try:
            feedback_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("feedback_metrics").insert(feedback_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from feedback creation")
        except Exception as e:
            logger.error(f"Failed to create feedback: {e}")
            raise

    async def get_book_feedback_summary(self, book_id: str) -> Dict[str, Any]:
        """Get aggregated feedback summary for a book"""
        try:
            client = await self._get_client()
            result = (
                client.table("feedback_metrics")
                .select("*")
                .eq("book_id", book_id)
                .execute()
            )
            feedback_data = result.data or []

            if not feedback_data:
                return {}

            # Aggregate feedback
            total = len(feedback_data)
            approved = sum(1 for f in feedback_data if f.get("approved"))
            quality_scores = [
                f.get("metric_value", 0)
                for f in feedback_data
                if f.get("metric_type") == "quality_score"
            ]
            reward_signals = [
                f.get("reward_signal", 0)
                for f in feedback_data
                if f.get("reward_signal") is not None
            ]

            return {
                "user_approval": approved > total / 2,
                "approval_rate": approved / max(1, total),
                "quality_score": sum(quality_scores) / max(1, len(quality_scores)),
                "feedback_count": total,
                "reward_signal": sum(reward_signals) / max(1, len(reward_signals)),
            }
        except Exception as e:
            logger.error(f"Failed to get book feedback summary: {e}")
            return {}

    async def get_book_feedback(self, book_id: str) -> List[Dict[str, Any]]:
        """Get all feedback metrics for a book"""
        try:
            client = await self._get_client()
            result = (
                client.table("feedback_metrics")
                .select("*")
                .eq("book_id", book_id)
                .execute()
            )
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get book feedback: {e}")
            return []

    async def get_recent_performance(
        self, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get recent performance data for all books"""
        try:
            # Get recent feedback grouped by book
            client = await self._get_client()
            result = (
                client.table("feedback_metrics")
                .select("*")
                .gte("created_at", cutoff_date.isoformat())
                .execute()
            )
            feedback_data = result.data or []

            # Group by book_id
            book_feedback: dict[str, list[dict]] = {}
            for feedback in feedback_data:
                book_id = feedback.get("book_id")
                if book_id is not None:  # Ensure book_id is not None
                    if book_id not in book_feedback:
                        book_feedback[book_id] = []
                    book_feedback[book_id].append(feedback)

            # Calculate performance for each book
            performance_data = []
            for book_id, feedbacks in book_feedback.items():
                total = len(feedbacks)
                approved = sum(1 for f in feedbacks if f.get("approved"))
                quality_scores = [
                    f.get("metric_value", 0)
                    for f in feedbacks
                    if f.get("metric_type") == "quality_score"
                ]
                reward_signals = [
                    f.get("reward_signal", 0)
                    for f in feedbacks
                    if f.get("reward_signal") is not None
                ]

                performance_data.append(
                    {
                        "book_id": book_id,
                        "user_approval": approved > total / 2,
                        "approval_rate": approved / max(1, total),
                        "quality_score": sum(quality_scores)
                        / max(1, len(quality_scores)),
                        "reward_signal": (
                            sum(reward_signals) / max(1, len(reward_signals))
                            if reward_signals
                            else None
                        ),
                        "feedback_count": total,
                    }
                )

            return performance_data
        except Exception as e:
            logger.error(f"Failed to get recent performance: {e}")
            return []

    async def record_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Record feedback with reward calculation"""
        try:
            # Ensure required fields
            feedback_data["created_at"] = datetime.utcnow().isoformat()

            # Calculate reward signal if not provided (temporarily disabled - column needs to be added to schema)
            # TODO: Enable once reward_signal column is added to feedback_metrics table
            # if "reward_signal" not in feedback_data:
            #     reward = 0.0
            #     if feedback_data.get("approved"):
            #         reward += 1.0
            #     else:
            #         reward -= 0.5
            #
            #     if (
            #         "metric_value" in feedback_data
            #         and feedback_data.get("metric_type") == "quality_score"
            #     ):
            #         quality_norm = (feedback_data["metric_value"] - 50) / 50
            #         reward += quality_norm * 0.5
            #
            #     feedback_data["reward_signal"] = max(-1.0, min(1.0, reward))

            client = await self._get_client()
            result = (
                client.table("feedback_metrics")
                .insert(feedback_data)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from feedback creation")
        except Exception as e:
            logger.error(f"Failed to record feedback: {e}")
            raise

    async def get_user_feedback_summary(
        self, user_id: str, days_back: int = 30
    ) -> Dict[str, Any]:
        """Get feedback summary for a user"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)
            client = await self._get_client()
            result = (
                client.table("feedback_metrics").select("*")
                .eq("user_id", user_id)
                .gte("created_at", cutoff_date.isoformat())
                .execute()
            )
            feedback_data = result.data or []

            if not feedback_data:
                return {"approval_rate": 0.0, "feedback_count": 0}

            # Calculate summary
            total = len(feedback_data)
            approved = sum(1 for f in feedback_data if f.get("approved"))
            quality_scores = [
                f.get("metric_value", 0)
                for f in feedback_data
                if f.get("metric_type") == "quality_score"
            ]

            return {
                "approval_rate": approved / max(1, total),
                "feedback_count": total,
                "avg_quality_score": (
                    sum(quality_scores) / max(1, len(quality_scores))
                    if quality_scores
                    else 0.0
                ),
            }
        except Exception as e:
            logger.error(f"Failed to get user feedback summary: {e}")
            return {"approval_rate": 0.0, "feedback_count": 0}

    async def count_recent_feedback(self, cutoff_time: datetime) -> int:
        """Count feedback entries created after cutoff_time"""
        try:
            client = await self._get_client()
            result = (
                client.table("feedback_metrics").select("id", count=CountMethod.exact)
                .gte("created_at", cutoff_time.isoformat())
                .execute()
            )
            return result.count if result.count is not None else 0
        except Exception as e:
            logger.error(f"Failed to count recent feedback: {e}")
            return 0


# Model factory functions
async def get_user_model() -> UserModel:
    """Get user model instance"""
    return UserModel()


async def get_book_model() -> BookModel:
    """Get book model instance"""
    return BookModel()


async def get_feedback_model() -> FeedbackModelOriginal:
    """Get feedback model instance"""
    return FeedbackModelOriginal()


class PublicationModel:
    """Publication model for Supabase operations"""
    
    @property
    def _client(self):
        """Get Supabase client instance"""
        return create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )

    async def create_publication(
        self, publication_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a new publication"""
        try:
            publication_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = (
                client.table("publications").insert(publication_data)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from publication creation")
        except Exception as e:
            logger.error(f"Failed to create publication: {e}")
            raise

    async def get_publication_by_id(
        self, publication_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get publication by ID"""
        try:
            client = await self._get_client()
            result = (
                client.table("publications").select("*")
                .eq("id", publication_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get publication: {e}")
            return None

    async def get_latest_sales_data(self, book_id: str) -> Optional[Dict[str, Any]]:
        """Get latest sales data for a book"""
        try:
            # First get the publication for this book
            pub_client = await self._get_client()
            result = (
                client.table("publications").select("id")
                .eq("book_id", book_id)
                .execute()
            )
            if not pub_result.data:
                return None

            publication_id = pub_result.data[0]["id"]

            # Get the latest sales data
            sales_client = await self._get_client()
            result = (
                client.table("sales_data").select("*")
                .eq("publication_id", publication_id)
                .order("report_date.desc")
                .limit(1)
                .execute()
            )

            if sales_result.data:
                return sales_result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get latest sales data: {e}")
            return None


class SalesDataModel:
    """Sales Data model for Supabase operations"""
    
    @property
    def _client(self):
        """Get Supabase client instance"""
        return create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )

    async def create_sales_record(self, sales_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new sales record"""
        try:
            sales_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("sales_data").insert(sales_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from sales record creation")
        except Exception as e:
            logger.error(f"Failed to create sales record: {e}")
            raise

    async def get_book_sales(self, book_id: str) -> List[Dict[str, Any]]:
        """Get sales data for a book"""
        try:
            client = await self._get_client()
            result = (
                client.table("sales_data").select("*")
                .eq("book_id", book_id)
                .order("report_date.desc")
                .execute()
            )
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get book sales: {e}")
            return []

    async def get_user_total_sales(self, user_id: str) -> Dict[str, Any]:
        """Get total sales metrics for a user"""
        try:
            client = await self._get_client()
            result = (
                client.table("sales_data").select("revenue, sales_units, royalties")
                .eq("user_id", user_id)
                .execute()
            )
            data = result.data or []
            return {
                "total_revenue": sum(d.get("revenue", 0) for d in data),
                "total_units": sum(d.get("sales_units", 0) for d in data),
                "total_royalties": sum(d.get("royalties", 0) for d in data),
            }
        except Exception as e:
            logger.error(f"Failed to get user total sales: {e}")
            return {"total_revenue": 0, "total_units": 0, "total_royalties": 0}


class TrendModel:
    """Trend model for Supabase operations"""
    
    @property
    def _client(self):
        """Get Supabase client instance"""
        return create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )

    async def create_trend(self, trend_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new trend"""
        try:
            trend_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("trends").insert(trend_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from trend creation")
        except Exception as e:
            logger.error(f"Failed to create trend: {e}")
            raise

    async def get_trend_by_id(self, trend_id: str) -> Optional[Dict[str, Any]]:
        """Get trend by ID"""
        try:
            client = await self._get_client()
            result = (
                client.table("trends").select("*")
                .eq("id", trend_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get trend: {e}")
            return None

    async def create_trend_analysis(
        self, analysis_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a new trend analysis"""
        try:
            analysis_data["created_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = (
                client.table("trend_analyses").insert(analysis_data)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from trend analysis creation")
        except Exception as e:
            logger.error(f"Failed to create trend analysis: {e}")
            raise


class ScrapedMarketDataModel:
    """Scraped Market Data model for Supabase operations"""
    
    @property
    def _client(self):
        """Get Supabase client instance"""
        return create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )

    async def get_cached_data(
        self, source: str, category: str, max_age_hours: int = 6
    ) -> Optional[Dict[str, Any]]:
        """Get cached scraped data if not expired"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            client = await self._get_client()
            result = (
                client.table("scraped_market_data").select("*")
                .eq("source", source)
                .eq("category", category)
                .gte("scraped_at", cutoff_time.isoformat())
                .order("scraped_at.desc")
                .limit(1)
                .execute()
            )

            if result.data:
                # Update cache hit count
                data = result.data[0]
                client = await self._get_client()
                client.table("scraped_market_data").update(
                    {
                        "cache_hit_count": data.get("cache_hit_count", 0) + 1,
                        "last_accessed_at": datetime.utcnow().isoformat(),
                    }
                ).eq("id", data["id"]).execute()
                return data
            return None
        except Exception as e:
            logger.error(f"Failed to get cached data: {e}")
            return None

    async def store_scraped_data(self, scraped_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store scraped market data"""
        try:
            scraped_data["scraped_at"] = datetime.utcnow().isoformat()
            scraped_data["expires_at"] = (
                datetime.utcnow() + timedelta(hours=6)
            ).isoformat()
            client = await self._get_client()
            result = (
                client.table("scraped_market_data").insert(scraped_data)
                .execute()
            )
            if result.data:
                return result.data[0]
            raise Exception("No data returned from scraped data creation")
        except Exception as e:
            logger.error(f"Failed to store scraped data: {e}")
            raise

    async def cleanup_expired_data(self) -> None:
        """Clean up expired cached data"""
        try:
            cutoff_time = datetime.utcnow().isoformat()
            client = await self._get_client()
            client.table("scraped_market_data").delete().lt(
                "expires_at", cutoff_time
            ).execute()
        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")


class ModelPerformanceModel:
    """Model Performance tracking for VERL"""
    
    @property
    def _client(self):
        """Get Supabase client instance"""
        return create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )
    
    async def create_performance_record(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create performance record"""
        try:
            performance_data["created_at"] = datetime.utcnow().isoformat()
            performance_data["evaluated_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("model_performance").insert(performance_data).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from performance record creation")
        except Exception as e:
            logger.error(f"Failed to create performance record: {e}")
            raise
    
    async def get_performance_by_model(self, model_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get performance records for a specific model"""
        try:
            client = await self._get_client()
            result = client.table("model_performance").select("*").eq("model_name", model_name).order("evaluated_at.desc").limit(limit).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get performance by model: {e}")
            return []
    
    async def get_performance_by_book(self, book_id: str) -> List[Dict[str, Any]]:
        """Get performance records for a specific book"""
        try:
            result = client.table("model_performance").select("*").eq("book_id", book_id).order("evaluated_at.desc").execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get performance by book: {e}")
            return []
    
    async def get_latest_performance(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get latest performance record for a model"""
        try:
            client = await self._get_client()
            result = client.table("model_performance").select("*").eq("model_name", model_name).order("evaluated_at.desc").limit(1).execute()
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get latest performance: {e}")
            return None
    
    async def get_performance_summary(self, model_name: str, days_back: int = 30) -> Dict[str, Any]:
        """Get performance summary for a model over time"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)
            client = await self._get_client()
            result = client.table("model_performance").select("*").eq("model_name", model_name).gte("evaluated_at", cutoff_date.isoformat()).execute()
            
            records = result.data or []
            if not records:
                return {
                    "model_name": model_name,
                    "record_count": 0,
                    "avg_accuracy": 0.0,
                    "avg_user_approval_rate": 0.0,
                    "avg_quality_score": 0.0,
                    "avg_reward_score": 0.0
                }
            
            # Calculate averages
            total = len(records)
            avg_accuracy = sum(r.get("accuracy_score", 0) for r in records) / total
            avg_approval = sum(r.get("user_approval_rate", 0) for r in records) / total
            avg_quality = sum(r.get("avg_quality_score", 0) for r in records) / total
            avg_reward = sum(r.get("reward_score", 0) for r in records) / total
            
            return {
                "model_name": model_name,
                "record_count": total,
                "avg_accuracy": round(avg_accuracy, 4),
                "avg_user_approval_rate": round(avg_approval, 4),
                "avg_quality_score": round(avg_quality, 2),
                "avg_reward_score": round(avg_reward, 4),
                "date_range": {
                    "from": cutoff_date.isoformat(),
                    "to": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Failed to get performance summary: {e}")
            return {
                "model_name": model_name,
                "record_count": 0,
                "error": str(e)
            }
    
    async def get_top_performing_models(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top performing models by user approval rate"""
        try:
            client = await self._get_client()
            result = client.table("model_performance").select("model_name, user_approval_rate, avg_quality_score, evaluated_at").order("user_approval_rate.desc").limit(limit).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get top performing models: {e}")
            return []
    
    async def update_performance_record(self, record_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing performance record"""
        try:
            updates["updated_at"] = datetime.utcnow().isoformat()
            client = await self._get_client()
            result = client.table("model_performance").update(updates).eq("id", record_id).execute()
            if result.data:
                return result.data[0]
            raise Exception("No data returned from performance record update")
        except Exception as e:
            logger.error(f"Failed to update performance record: {e}")
            raise
    
    async def delete_old_records(self, days_to_keep: int = 90) -> int:
        """Delete old performance records to manage storage"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            client = await self._get_client()
            result = client.table("model_performance").delete().lt("evaluated_at", cutoff_date.isoformat()).execute()
            deleted_count = len(result.data) if result.data else 0
            logger.info(f"Deleted {deleted_count} old performance records")
            return deleted_count
        except Exception as e:
            logger.error(f"Failed to delete old records: {e}")
            return 0


# Additional factory functions
async def get_publication_model() -> PublicationModel:
    """Get publication model instance"""
    return PublicationModel()


async def get_sales_data_model() -> SalesDataModel:
    """Get sales data model instance"""
    return SalesDataModel()


async def get_trend_model() -> TrendModel:
    """Get trend model instance"""
    return TrendModel()


# Legacy model class definitions for backward compatibility
# These are simple wrappers around the new model system
class User:
    """Legacy User model for backward compatibility"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.email = kwargs.get('email')
        self.username = kwargs.get('username')
        self.full_name = kwargs.get('full_name')
        self.is_active = kwargs.get('is_active', True)
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
        self.content_preferences = kwargs.get('content_preferences', {})
    
    def dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'full_name': self.full_name,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'content_preferences': self.content_preferences
        }


class Book:
    """Legacy Book model for backward compatibility"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.user_id = kwargs.get('user_id')
        self.title = kwargs.get('title')
        self.subtitle = kwargs.get('subtitle')
        self.description = kwargs.get('description')
        self.content = kwargs.get('content')
        self.genre = kwargs.get('genre')
        self.keywords = kwargs.get('keywords', [])
        self.language = kwargs.get('language', 'en')
        self.word_count = kwargs.get('word_count', 0)
        self.status = kwargs.get('status', 'draft')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    def dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'subtitle': self.subtitle,
            'description': self.description,
            'content': self.content,
            'genre': self.genre,
            'keywords': self.keywords,
            'language': self.language,
            'word_count': self.word_count,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


class Publication:
    """Legacy Publication model for backward compatibility"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.book_id = kwargs.get('book_id')
        self.platform = kwargs.get('platform')
        self.platform_id = kwargs.get('platform_id')
        self.status = kwargs.get('status', 'draft')
        self.url = kwargs.get('url')
        self.price = kwargs.get('price')
        self.currency = kwargs.get('currency', 'USD')
        self.published_at = kwargs.get('published_at')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    def dict(self):
        return {
            'id': self.id,
            'book_id': self.book_id,
            'platform': self.platform,
            'platform_id': self.platform_id,
            'status': self.status,
            'url': self.url,
            'price': self.price,
            'currency': self.currency,
            'published_at': self.published_at,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


class TrendAnalysis:
    """Legacy TrendAnalysis model for backward compatibility"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.trend_type = kwargs.get('trend_type')
        self.keywords = kwargs.get('keywords', [])
        self.trend_score = kwargs.get('trend_score', 0.0)
        self.market_size = kwargs.get('market_size', 0)
        self.competition_level = kwargs.get('competition_level', 'medium')
        self.data_source = kwargs.get('data_source')
        self.raw_data = kwargs.get('raw_data', {})
        self.analyzed_at = kwargs.get('analyzed_at')
        self.created_at = kwargs.get('created_at')
    
    def dict(self):
        return {
            'id': self.id,
            'trend_type': self.trend_type,
            'keywords': self.keywords,
            'trend_score': self.trend_score,
            'market_size': self.market_size,
            'competition_level': self.competition_level,
            'data_source': self.data_source,
            'raw_data': self.raw_data,
            'analyzed_at': self.analyzed_at,
            'created_at': self.created_at
        }


async def get_scraped_market_data_model() -> ScrapedMarketDataModel:
    """Get scraped market data model instance"""
    return ScrapedMarketDataModel()


# Alias for compatibility
async def get_market_data_model() -> ScrapedMarketDataModel:
    """Get market data model instance (alias for scraped market data model)"""
    return ScrapedMarketDataModel()


async def get_model_performance_model() -> ModelPerformanceModel:
    """Get model performance model instance"""
    return ModelPerformanceModel()


# =====================================================
# NEW DATABASE MODELS FOR API INTEGRATION
# =====================================================

class AnalyticsModel(BaseModel):
    """Analytics model for dashboard and user analytics"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_dashboard_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive dashboard analytics for a user"""
        try:
            client = await self._get_client()
            
            # Get total books count
            books_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .execute()
            )
            total_books = books_result.count or 0
            
            # Get published books count
            published_books_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .eq("status", "published")
                .execute()
            )
            published_books = published_books_result.count or 0
            
            # Get total revenue from sales data
            revenue_result = (
                client.table("sales_data")
                .select("royalty_earned")
                .eq("user_id", user_id)
                .execute()
            )
            total_revenue = sum(
                float(row.get("royalty_earned", 0)) for row in revenue_result.data or []
            )
            
            # Calculate monthly growth
            monthly_growth = await self._calculate_monthly_growth(user_id)
            
            # Get performance metrics
            performance_metrics = await self._get_performance_metrics(user_id)
            
            return {
                "total_books": total_books,
                "published_books": published_books,
                "total_revenue": round(total_revenue, 2),
                "monthly_growth": round(monthly_growth, 2),
                "performance_metrics": performance_metrics
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard analytics: {e}")
            raise

    async def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get detailed analytics for a specific user"""
        try:
            client = await self._get_client()
            
            # Get user profile data
            user_result = (
                client.table("users")
                .select("total_books_generated", "total_revenue", "avg_quality_score", "created_at")
                .eq("id", user_id)
                .single()
                .execute()
            )
            user_data = user_result.data or {}
            
            # Get books created count
            books_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .execute()
            )
            books_created = books_result.count or 0
            
            # Get average quality score from feedback
            feedback_result = (
                client.table("feedback_metrics")
                .select("metric_value")
                .eq("user_id", user_id)
                .eq("metric_type", "quality_score")
                .execute()
            )
            quality_scores = [
                float(row.get("metric_value", 0)) for row in feedback_result.data or []
            ]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            # Get revenue from sales
            sales_result = (
                client.table("sales_data")
                .select("royalty_earned")
                .eq("user_id", user_id)
                .execute()
            )
            total_revenue = sum(
                float(row.get("royalty_earned", 0)) for row in sales_result.data or []
            )
            
            return {
                "user_id": user_id,
                "books_created": books_created,
                "total_revenue": round(total_revenue, 2),
                "avg_quality_score": round(avg_quality_score, 2),
                "member_since": user_data.get("created_at"),
                "profile_completion": await self._calculate_profile_completion(user_id)
            }
            
        except Exception as e:
            logger.error(f"Failed to get user analytics for {user_id}: {e}")
            raise

    async def calculate_growth_metrics(self, user_id: str, period: str = "month") -> Dict[str, Any]:
        """Calculate growth metrics for a user over a specified period"""
        try:
            client = await self._get_client()
            
            # Determine date range based on period
            if period == "month":
                current_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                previous_start = (current_start - timedelta(days=1)).replace(day=1)
                previous_end = current_start - timedelta(seconds=1)
            elif period == "week":
                current_start = datetime.now() - timedelta(days=7)
                previous_start = current_start - timedelta(days=7)
                previous_end = current_start
            else:
                raise ValueError(f"Unsupported period: {period}")
            
            # Get current period metrics
            current_revenue = await self._get_revenue_for_period(user_id, current_start, datetime.now())
            current_books = await self._get_books_for_period(user_id, current_start, datetime.now())
            
            # Get previous period metrics
            previous_revenue = await self._get_revenue_for_period(user_id, previous_start, previous_end)
            previous_books = await self._get_books_for_period(user_id, previous_start, previous_end)
            
            # Calculate growth percentages
            revenue_growth = self._calculate_percentage_change(previous_revenue, current_revenue)
            books_growth = self._calculate_percentage_change(previous_books, current_books)
            
            return {
                "period": period,
                "revenue_growth": round(revenue_growth, 2),
                "books_growth": round(books_growth, 2),
                "current_revenue": round(current_revenue, 2),
                "current_books": current_books,
                "previous_revenue": round(previous_revenue, 2),
                "previous_books": previous_books
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate growth metrics: {e}")
            raise

    async def _calculate_monthly_growth(self, user_id: str) -> float:
        """Calculate monthly revenue growth percentage"""
        try:
            current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            previous_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
            
            current_revenue = await self._get_revenue_for_period(user_id, current_month_start, datetime.now())
            previous_revenue = await self._get_revenue_for_period(user_id, previous_month_start, current_month_start)
            
            return self._calculate_percentage_change(previous_revenue, current_revenue)
            
        except Exception as e:
            logger.error(f"Failed to calculate monthly growth: {e}")
            return 0.0

    async def _get_performance_metrics(self, user_id: str) -> Dict[str, Any]:
        """Get performance metrics including approval and quality trends"""
        try:
            client = await self._get_client()
            
            # Get recent feedback metrics (last 7 days)
            since_date = datetime.now() - timedelta(days=7)
            feedback_result = (
                client.table("feedback_metrics")
                .select("metric_type", "metric_value", "approved", "created_at")
                .eq("user_id", user_id)
                .gte("created_at", since_date.isoformat())
                .order("created_at")
                .execute()
            )
            
            feedback_data = feedback_result.data or []
            
            # Process approval data
            approval_values = []
            approval_timestamps = []
            quality_values = []
            quality_timestamps = []
            
            for feedback in feedback_data:
                timestamp = feedback.get("created_at", "")
                if feedback.get("approved") is not None:
                    approval_values.append(1 if feedback.get("approved") else 0)
                    approval_timestamps.append(timestamp[:10])  # Date only
                
                if feedback.get("metric_type") == "quality_score":
                    quality_values.append(float(feedback.get("metric_value", 0)))
                    quality_timestamps.append(timestamp[:10])  # Date only
            
            # Calculate averages and trends
            approval_avg = sum(approval_values) / len(approval_values) if approval_values else 0
            quality_avg = sum(quality_values) / len(quality_values) if quality_values else 0
            
            return {
                "user_approval": {
                    "values": approval_values[-5:],  # Last 5 data points
                    "timestamps": approval_timestamps[-5:],
                    "average": round(approval_avg, 2),
                    "trend": "improving" if len(approval_values) >= 2 and approval_values[-1] > approval_values[0] else "stable"
                },
                "content_quality": {
                    "values": quality_values[-5:],  # Last 5 data points
                    "timestamps": quality_timestamps[-5:],
                    "average": round(quality_avg, 2),
                    "trend": "improving" if len(quality_values) >= 2 and quality_values[-1] > quality_values[0] else "stable"
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return {"user_approval": {}, "content_quality": {}}

    async def _get_revenue_for_period(self, user_id: str, start_date: datetime, end_date: datetime) -> float:
        """Get total revenue for a specific period"""
        try:
            client = await self._get_client()
            result = (
                client.table("sales_data")
                .select("royalty_earned")
                .eq("user_id", user_id)
                .gte("created_at", start_date.isoformat())
                .lt("created_at", end_date.isoformat())
                .execute()
            )
            
            return sum(float(row.get("royalty_earned", 0)) for row in result.data or [])
            
        except Exception as e:
            logger.error(f"Failed to get revenue for period: {e}")
            return 0.0

    async def _get_books_for_period(self, user_id: str, start_date: datetime, end_date: datetime) -> int:
        """Get total books created for a specific period"""
        try:
            client = await self._get_client()
            result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .gte("created_at", start_date.isoformat())
                .lt("created_at", end_date.isoformat())
                .execute()
            )
            
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Failed to get books for period: {e}")
            return 0

    async def _calculate_profile_completion(self, user_id: str) -> float:
        """Calculate user profile completion percentage"""
        try:
            client = await self._get_client()
            result = (
                client.table("users")
                .select("email", "avatar_url", "kdp_email", "notification_preferences")
                .eq("id", user_id)
                .single()
                .execute()
            )
            
            user_data = result.data or {}
            total_fields = 4
            completed_fields = 0
            
            if user_data.get("email"):
                completed_fields += 1
            if user_data.get("avatar_url"):
                completed_fields += 1
            if user_data.get("kdp_email"):
                completed_fields += 1
            if user_data.get("notification_preferences"):
                completed_fields += 1
            
            return round((completed_fields / total_fields) * 100, 1)
            
        except Exception as e:
            logger.error(f"Failed to calculate profile completion: {e}")
            return 0.0

    def _calculate_percentage_change(self, old_value: float, new_value: float) -> float:
        """Calculate percentage change between two values"""
        if old_value == 0:
            return 100.0 if new_value > 0 else 0.0
        return ((new_value - old_value) / old_value) * 100


async def get_analytics_model() -> AnalyticsModel:
    """Get analytics model instance"""
    return AnalyticsModel()


class TrendModel(BaseModel):
    """Trend model for market trend analysis and management"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_active_trends(self, limit: int = 50, user_id: str = None) -> List[Dict[str, Any]]:
        """Get active trends from the database"""
        try:
            client = await self._get_client()
            
            # Get trends from the last 30 days, ordered by trend score
            since_date = datetime.now() - timedelta(days=30)
            
            query = (
                client.table("trends")
                .select("*")
                .gte("created_at", since_date.isoformat())
                .order("trend_score", desc=True)
                .limit(limit)
            )
            
            # If user_id is provided, get user-specific trends or public trends
            if user_id:
                query = query.or_(f"user_id.eq.{user_id},user_id.is.null")
            else:
                # Only get public trends (where user_id is null)
                query = query.is_("user_id", "null")
            
            result = query.execute()
            trends = result.data or []
            
            # Transform trends data for API response
            transformed_trends = []
            for trend in trends:
                transformed_trends.append({
                    "id": trend.get("id"),
                    "title": trend.get("keyword", ""),
                    "category": trend.get("category", "Unknown"),
                    "search_volume": trend.get("search_volume", 0),
                    "competition": self._format_competition_level(trend.get("competition_level", "unknown")),
                    "trend_score": float(trend.get("trend_score", 0)),
                    "growth_rate": float(trend.get("growth_rate", 0)),
                    "seasonality": trend.get("seasonality_data", {}),
                    "created_at": trend.get("created_at"),
                    "expires_at": trend.get("expires_at")
                })
            
            return transformed_trends
            
        except Exception as e:
            logger.error(f"Failed to get active trends: {e}")
            raise

    async def create_trend_analysis(self, trend_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new trend analysis record"""
        try:
            client = await self._get_client()
            
            # Prepare trend data for insertion
            insert_data = {
                "user_id": trend_data.get("user_id"),
                "keyword": trend_data.get("keyword"),
                "search_volume": trend_data.get("search_volume", 0),
                "competition_level": trend_data.get("competition_level", "unknown"),
                "trend_score": trend_data.get("trend_score", 0.0),
                "category": trend_data.get("category"),
                "growth_rate": trend_data.get("growth_rate", 0.0),
                "related_keywords": trend_data.get("related_keywords", []),
                "seasonality_data": trend_data.get("seasonality_data", {}),
                "data_source": trend_data.get("data_source", "manual"),
                "raw_data": trend_data.get("raw_data", {}),
                "expires_at": (datetime.now() + timedelta(days=30)).isoformat(),
                "created_at": datetime.now().isoformat()
            }
            
            result = client.table("trends").insert(insert_data).execute()
            
            if result.data:
                trend_record = result.data[0]
                logger.info(f"Created trend analysis for keyword: {trend_data.get('keyword')}")
                return trend_record
            
            raise Exception("No data returned from trend creation")
            
        except Exception as e:
            logger.error(f"Failed to create trend analysis: {e}")
            raise

    async def get_market_competition(self, category: str) -> Dict[str, Any]:
        """Get market competition analysis for a specific category"""
        try:
            client = await self._get_client()
            
            # Get trends for the specific category from the last 90 days
            since_date = datetime.now() - timedelta(days=90)
            
            result = (
                client.table("trends")
                .select("*")
                .eq("category", category)
                .gte("created_at", since_date.isoformat())
                .execute()
            )
            
            trends = result.data or []
            
            if not trends:
                return {
                    "category": category,
                    "competition_level": "unknown",
                    "trend_count": 0,
                    "average_search_volume": 0,
                    "average_trend_score": 0,
                    "growth_outlook": "stable",
                    "top_keywords": []
                }
            
            # Calculate competition metrics
            search_volumes = [t.get("search_volume", 0) for t in trends]
            trend_scores = [float(t.get("trend_score", 0)) for t in trends]
            competition_levels = [t.get("competition_level", "unknown") for t in trends]
            
            avg_search_volume = sum(search_volumes) / len(search_volumes)
            avg_trend_score = sum(trend_scores) / len(trend_scores)
            
            # Determine overall competition level
            high_competition_count = sum(1 for c in competition_levels if c == "high")
            medium_competition_count = sum(1 for c in competition_levels if c == "medium")
            
            if high_competition_count > len(trends) * 0.6:
                overall_competition = "high"
            elif medium_competition_count > len(trends) * 0.4:
                overall_competition = "medium"
            else:
                overall_competition = "low"
            
            # Get top keywords by trend score
            top_keywords = sorted(trends, key=lambda x: float(x.get("trend_score", 0)), reverse=True)[:5]
            top_keywords_list = [
                {
                    "keyword": kw.get("keyword"),
                    "search_volume": kw.get("search_volume", 0),
                    "trend_score": float(kw.get("trend_score", 0))
                }
                for kw in top_keywords
            ]
            
            # Determine growth outlook
            recent_trends = [t for t in trends if 
                            datetime.fromisoformat(t.get("created_at", "").replace('Z', '+00:00')) > 
                            datetime.now().replace(tzinfo=None) - timedelta(days=30)]
            
            if len(recent_trends) > len(trends) * 0.7:
                growth_outlook = "growing"
            elif len(recent_trends) < len(trends) * 0.3:
                growth_outlook = "declining"
            else:
                growth_outlook = "stable"
            
            return {
                "category": category,
                "competition_level": overall_competition,
                "trend_count": len(trends),
                "average_search_volume": round(avg_search_volume, 0),
                "average_trend_score": round(avg_trend_score, 2),
                "growth_outlook": growth_outlook,
                "top_keywords": top_keywords_list,
                "analysis_period_days": 90,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get market competition for category {category}: {e}")
            raise

    async def analyze_keyword_trends(self, keywords: List[str], user_id: str) -> Dict[str, Any]:
        """Analyze trends for multiple keywords"""
        try:
            client = await self._get_client()
            
            results = {}
            for keyword in keywords:
                # Check if we have recent data for this keyword
                result = (
                    client.table("trends")
                    .select("*")
                    .eq("keyword", keyword)
                    .gte("created_at", (datetime.now() - timedelta(days=7)).isoformat())
                    .order("created_at", desc=True)
                    .limit(1)
                    .execute()
                )
                
                if result.data:
                    trend_data = result.data[0]
                    results[keyword] = {
                        "search_volume": trend_data.get("search_volume", 0),
                        "trend_score": float(trend_data.get("trend_score", 0)),
                        "competition_level": trend_data.get("competition_level", "unknown"),
                        "category": trend_data.get("category"),
                        "last_analyzed": trend_data.get("created_at"),
                        "data_age_hours": (datetime.now() - 
                                         datetime.fromisoformat(trend_data.get("created_at", "").replace('Z', '+00:00'))).total_seconds() / 3600
                    }
                else:
                    # No recent data - mark for analysis
                    results[keyword] = {
                        "search_volume": 0,
                        "trend_score": 0,
                        "competition_level": "unknown",
                        "category": None,
                        "last_analyzed": None,
                        "needs_analysis": True
                    }
            
            return {
                "keywords_analyzed": len(keywords),
                "results": results,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze keyword trends: {e}")
            raise

    async def get_trending_categories(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get trending categories based on recent trend activity"""
        try:
            client = await self._get_client()
            
            # Get trends from the last 14 days
            since_date = datetime.now() - timedelta(days=14)
            
            result = (
                client.table("trends")
                .select("category", "trend_score", "search_volume")
                .gte("created_at", since_date.isoformat())
                .is_("user_id", "null")  # Only public trends
                .execute()
            )
            
            trends = result.data or []
            
            # Group by category and calculate metrics
            category_metrics = {}
            for trend in trends:
                category = trend.get("category")
                if not category:
                    continue
                
                if category not in category_metrics:
                    category_metrics[category] = {
                        "category": category,
                        "trend_count": 0,
                        "total_search_volume": 0,
                        "total_trend_score": 0,
                        "average_trend_score": 0,
                        "average_search_volume": 0
                    }
                
                category_metrics[category]["trend_count"] += 1
                category_metrics[category]["total_search_volume"] += trend.get("search_volume", 0)
                category_metrics[category]["total_trend_score"] += float(trend.get("trend_score", 0))
            
            # Calculate averages and sort by trend score
            trending_categories = []
            for category_data in category_metrics.values():
                count = category_data["trend_count"]
                category_data["average_trend_score"] = round(category_data["total_trend_score"] / count, 2)
                category_data["average_search_volume"] = round(category_data["total_search_volume"] / count, 0)
                trending_categories.append(category_data)
            
            # Sort by average trend score and limit results
            trending_categories.sort(key=lambda x: x["average_trend_score"], reverse=True)
            
            return trending_categories[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get trending categories: {e}")
            raise

    def _format_competition_level(self, level: str) -> str:
        """Format competition level for consistent API responses"""
        level_mapping = {
            "low": "Low",
            "medium": "Medium", 
            "high": "High",
            "unknown": "Unknown"
        }
        return level_mapping.get(level.lower(), "Unknown")

    async def update_trend_scores(self, user_id: str = None) -> Dict[str, Any]:
        """Update trend scores based on recent activity (background task)"""
        try:
            client = await self._get_client()
            
            # Get trends that need score updates (older than 24 hours)
            update_threshold = datetime.now() - timedelta(hours=24)
            
            query = (
                client.table("trends")
                .select("*")
                .lt("updated_at", update_threshold.isoformat())
            )
            
            if user_id:
                query = query.eq("user_id", user_id)
            
            result = query.execute()
            trends_to_update = result.data or []
            
            updated_count = 0
            for trend in trends_to_update:
                # Simulate trend score update logic
                # In a real implementation, this would fetch fresh data from external APIs
                current_score = float(trend.get("trend_score", 0))
                
                # Simple trend decay model (scores decrease over time)
                age_days = (datetime.now() - 
                           datetime.fromisoformat(trend.get("created_at", "").replace('Z', '+00:00'))).days
                
                decay_factor = max(0.5, 1.0 - (age_days * 0.02))  # 2% decay per day, minimum 50%
                new_score = current_score * decay_factor
                
                # Update the trend
                update_result = (
                    client.table("trends")
                    .update({
                        "trend_score": round(new_score, 2),
                        "updated_at": datetime.now().isoformat()
                    })
                    .eq("id", trend.get("id"))
                    .execute()
                )
                
                if update_result.data:
                    updated_count += 1
            
            return {
                "trends_checked": len(trends_to_update),
                "trends_updated": updated_count,
                "update_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to update trend scores: {e}")
            raise


async def get_trend_model() -> TrendModel:
    """Get trend model instance"""
    return TrendModel()


class PublicationModel(BaseModel):
    """Publication model for managing published books and their performance"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_user_publications(self, user_id: str, status: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get all publications for a user with their performance metrics"""
        try:
            client = await self._get_client()
            
            # Base query with JOIN to get sales data
            query = (
                client.table("publications")
                .select("""
                    *,
                    books!inner(id, title, category, word_count, quality_score),
                    sales_data(units_sold, gross_revenue, net_revenue, royalty_earned, pages_read, kindle_unlimited_revenue, average_rating, total_reviews)
                """)
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .limit(limit)
            )
            
            # Filter by status if provided
            if status and status != "all":
                query = query.eq("status", status)
            
            result = query.execute()
            publications = result.data or []
            
            # Transform and enrich publication data
            enriched_publications = []
            for pub in publications:
                book_data = pub.get("books", {})
                sales_data = pub.get("sales_data", [])
                
                # Aggregate sales metrics
                total_revenue = sum(float(s.get("royalty_earned", 0)) for s in sales_data)
                total_units = sum(int(s.get("units_sold", 0)) for s in sales_data)
                total_pages_read = sum(int(s.get("pages_read", 0)) for s in sales_data)
                avg_rating = sum(float(s.get("average_rating", 0)) for s in sales_data) / len(sales_data) if sales_data else 0
                total_reviews = sum(int(s.get("total_reviews", 0)) for s in sales_data)
                
                enriched_pub = {
                    "id": pub.get("id"),
                    "book_id": pub.get("book_id"),
                    "title": book_data.get("title", "Unknown Title"),
                    "category": book_data.get("category", "Unknown"),
                    "kdp_book_id": pub.get("kdp_book_id"),
                    "asin": pub.get("asin"),
                    "publication_url": pub.get("publication_url"),
                    "price": float(pub.get("price", 0)),
                    "royalty_rate": pub.get("royalty_rate", 70),
                    "kdp_select": pub.get("kdp_select", True),
                    "status": pub.get("status", "draft"),
                    "published_at": pub.get("published_at"),
                    "created_at": pub.get("created_at"),
                    "last_checked": pub.get("last_checked"),
                    "word_count": book_data.get("word_count", 0),
                    "quality_score": float(book_data.get("quality_score", 0)) if book_data.get("quality_score") else None,
                    "performance": {
                        "total_revenue": round(total_revenue, 2),
                        "units_sold": total_units,
                        "pages_read": total_pages_read,
                        "average_rating": round(avg_rating, 2) if avg_rating > 0 else None,
                        "total_reviews": total_reviews,
                        "estimated_monthly_revenue": round(total_revenue / max(1, (datetime.now() - datetime.fromisoformat(pub.get("published_at", "").replace('Z', '+00:00'))).days) * 30, 2) if pub.get("published_at") else 0
                    },
                    "category_rankings": pub.get("category_rankings", {}),
                    "keywords_used": pub.get("keywords_used", []),
                    "marketing_description": pub.get("marketing_description"),
                    "error_message": pub.get("error_message"),
                    "retry_count": pub.get("retry_count", 0)
                }
                enriched_publications.append(enriched_pub)
            
            return enriched_publications
            
        except Exception as e:
            logger.error(f"Failed to get user publications: {e}")
            raise

    async def create_publication(self, publication_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new publication record"""
        try:
            client = await self._get_client()
            
            # Validate that the book exists and belongs to the user
            book_result = (
                client.table("books")
                .select("id", "title", "user_id", "status")
                .eq("id", publication_data.get("book_id"))
                .eq("user_id", publication_data.get("user_id"))
                .single()
                .execute()
            )
            
            if not book_result.data:
                raise ValueError("Book not found or does not belong to user")
            
            book_data = book_result.data
            
            # Check if book is ready for publication
            if book_data.get("status") not in ["completed", "approved"]:
                raise ValueError("Book must be completed and approved before publication")
            
            # Prepare publication data
            insert_data = {
                "user_id": publication_data.get("user_id"),
                "book_id": publication_data.get("book_id"),
                "price": publication_data.get("price", 2.99),
                "royalty_rate": publication_data.get("royalty_rate", 70),
                "kdp_select": publication_data.get("kdp_select", True),
                "marketing_description": publication_data.get("marketing_description"),
                "keywords_used": publication_data.get("keywords_used", []),
                "category_rankings": publication_data.get("category_rankings", {}),
                "publication_config": publication_data.get("publication_config", {}),
                "status": "draft",  # Always start as draft
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            result = client.table("publications").insert(insert_data).execute()
            
            if result.data:
                publication = result.data[0]
                logger.info(f"Created publication for book {publication_data.get('book_id')}")
                
                # Update book status to indicate it's being published
                client.table("books").update({
                    "status": "publishing",
                    "updated_at": datetime.now().isoformat()
                }).eq("id", publication_data.get("book_id")).execute()
                
                return publication
            
            raise Exception("No data returned from publication creation")
            
        except Exception as e:
            logger.error(f"Failed to create publication: {e}")
            raise

    async def get_publication_by_id(self, publication_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific publication by ID"""
        try:
            client = await self._get_client()
            
            result = (
                client.table("publications")
                .select("""
                    *,
                    books!inner(id, title, category, word_count, quality_score, content_metadata),
                    sales_data(units_sold, gross_revenue, net_revenue, royalty_earned, pages_read, 
                              kindle_unlimited_revenue, average_rating, total_reviews, sale_date)
                """)
                .eq("id", publication_id)
                .eq("user_id", user_id)
                .single()
                .execute()
            )
            
            if not result.data:
                return None
            
            pub = result.data
            book_data = pub.get("books", {})
            sales_data = pub.get("sales_data", [])
            
            # Calculate aggregated metrics
            total_revenue = sum(float(s.get("royalty_earned", 0)) for s in sales_data)
            total_units = sum(int(s.get("units_sold", 0)) for s in sales_data)
            total_pages_read = sum(int(s.get("pages_read", 0)) for s in sales_data)
            avg_rating = sum(float(s.get("average_rating", 0)) for s in sales_data) / len(sales_data) if sales_data else 0
            
            return {
                "id": pub.get("id"),
                "book_id": pub.get("book_id"),
                "title": book_data.get("title", "Unknown Title"),
                "category": book_data.get("category"),
                "word_count": book_data.get("word_count", 0),
                "quality_score": book_data.get("quality_score", 0),
                "status": pub.get("status"),
                "price": pub.get("price", 0),
                "royalty_rate": pub.get("royalty_rate", 0),
                "kdp_select": pub.get("kdp_select", False),
                "created_at": pub.get("created_at"),
                "updated_at": pub.get("updated_at"),
                "published_at": pub.get("published_at"),
                "marketing_description": pub.get("marketing_description"),
                "keywords_used": pub.get("keywords_used", []),
                "category_rankings": pub.get("category_rankings", {}),
                "metrics": {
                    "total_revenue": round(total_revenue, 2),
                    "total_units_sold": total_units,
                    "total_pages_read": total_pages_read,
                    "average_rating": round(avg_rating, 2),
                    "total_reviews": sum(int(s.get("total_reviews", 0)) for s in sales_data),
                    "conversion_rate": round((total_units / max(1, total_pages_read)) * 100, 2) if total_pages_read > 0 else 0
                },
                "sales_history": sales_data[-10:] if sales_data else []  # Last 10 sales records
            }
            
        except Exception as e:
            logger.error(f"Failed to get publication by ID: {e}")
            raise

    async def get_publication_metrics(self, publication_id: str, user_id: str) -> Dict[str, Any]:
        """Get detailed metrics for a specific publication"""
        try:
            client = await self._get_client()
            
            # Get publication with sales data
            pub_result = (
                client.table("publications")
                .select("""
                    *,
                    books!inner(title, category, word_count),
                    sales_data(*)
                """)
                .eq("id", publication_id)
                .eq("user_id", user_id)
                .single()
                .execute()
            )
            
            if not pub_result.data:
                raise ValueError("Publication not found")
            
            pub = pub_result.data
            sales_data = pub.get("sales_data", [])
            
            # Calculate performance metrics
            total_revenue = sum(float(s.get("royalty_earned", 0)) for s in sales_data)
            total_gross_revenue = sum(float(s.get("gross_revenue", 0)) for s in sales_data)
            total_units = sum(int(s.get("units_sold", 0)) for s in sales_data)
            total_pages_read = sum(int(s.get("pages_read", 0)) for s in sales_data)
            total_ku_revenue = sum(float(s.get("kindle_unlimited_revenue", 0)) for s in sales_data)
            
            # Calculate time-based metrics
            published_date = pub.get("published_at")
            days_published = 0
            if published_date:
                published_dt = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                days_published = (datetime.now().replace(tzinfo=None) - published_dt.replace(tzinfo=None)).days
            
            # Calculate conversion rates and averages
            avg_daily_revenue = total_revenue / max(1, days_published) if days_published > 0 else 0
            avg_daily_units = total_units / max(1, days_published) if days_published > 0 else 0
            
            # Get recent performance (last 30 days)
            recent_cutoff = datetime.now() - timedelta(days=30)
            recent_sales = [s for s in sales_data if 
                          datetime.fromisoformat(s.get("created_at", "").replace('Z', '+00:00')) > recent_cutoff]
            
            recent_revenue = sum(float(s.get("royalty_earned", 0)) for s in recent_sales)
            recent_units = sum(int(s.get("units_sold", 0)) for s in recent_sales)
            
            # Calculate ratings and reviews
            reviews_data = [s for s in sales_data if s.get("average_rating") and s.get("average_rating") > 0]
            avg_rating = sum(float(s.get("average_rating", 0)) for s in reviews_data) / len(reviews_data) if reviews_data else 0
            total_reviews = sum(int(s.get("total_reviews", 0)) for s in sales_data)
            
            return {
                "publication_id": publication_id,
                "title": pub.get("books", {}).get("title", "Unknown"),
                "status": pub.get("status"),
                "published_at": pub.get("published_at"),
                "days_published": days_published,
                "asin": pub.get("asin"),
                "kdp_book_id": pub.get("kdp_book_id"),
                "price": float(pub.get("price", 0)),
                "royalty_rate": pub.get("royalty_rate", 70),
                "revenue_metrics": {
                    "total_revenue": round(total_revenue, 2),
                    "gross_revenue": round(total_gross_revenue, 2),
                    "kindle_unlimited_revenue": round(total_ku_revenue, 2),
                    "average_daily_revenue": round(avg_daily_revenue, 2),
                    "recent_30_day_revenue": round(recent_revenue, 2)
                },
                "sales_metrics": {
                    "total_units_sold": total_units,
                    "total_pages_read": total_pages_read,
                    "average_daily_units": round(avg_daily_units, 2),
                    "recent_30_day_units": recent_units
                },
                "engagement_metrics": {
                    "average_rating": round(avg_rating, 2) if avg_rating > 0 else None,
                    "total_reviews": total_reviews,
                    "pages_per_sale": round(total_pages_read / max(1, total_units), 0) if total_units > 0 else 0
                },
                "market_position": {
                    "category": pub.get("books", {}).get("category"),
                    "category_rankings": pub.get("category_rankings", {}),
                    "keywords_used": pub.get("keywords_used", [])
                },
                "performance_trend": self._calculate_performance_trend(sales_data),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get publication metrics: {e}")
            raise

    async def update_publication_status(self, publication_id: str, user_id: str, status: str, 
                                      kdp_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Update publication status and KDP data"""
        try:
            client = await self._get_client()
            
            # Prepare update data
            update_data = {
                "status": status,
                "updated_at": datetime.now().isoformat()
            }
            
            # Add KDP data if provided
            if kdp_data:
                if kdp_data.get("kdp_book_id"):
                    update_data["kdp_book_id"] = kdp_data["kdp_book_id"]
                if kdp_data.get("asin"):
                    update_data["asin"] = kdp_data["asin"]
                if kdp_data.get("publication_url"):
                    update_data["publication_url"] = kdp_data["publication_url"]
                if kdp_data.get("error_message"):
                    update_data["error_message"] = kdp_data["error_message"]
                
                # Set published_at when status becomes published
                if status == "published" and not kdp_data.get("published_at"):
                    update_data["published_at"] = datetime.now().isoformat()
                elif kdp_data.get("published_at"):
                    update_data["published_at"] = kdp_data["published_at"]
            
            # Update publication
            result = (
                client.table("publications")
                .update(update_data)
                .eq("id", publication_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            if result.data:
                updated_pub = result.data[0]
                logger.info(f"Updated publication {publication_id} status to {status}")
                
                # Update corresponding book status
                book_status_mapping = {
                    "draft": "completed",
                    "processing": "publishing", 
                    "published": "published",
                    "failed": "completed"
                }
                
                if status in book_status_mapping:
                    client.table("books").update({
                        "status": book_status_mapping[status],
                        "updated_at": datetime.now().isoformat(),
                        "published_at": update_data.get("published_at")
                    }).eq("id", updated_pub.get("book_id")).execute()
                
                return updated_pub
            
            raise ValueError("Publication not found or unauthorized")
            
        except Exception as e:
            logger.error(f"Failed to update publication status: {e}")
            raise

    async def get_publication_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary statistics for user's publications"""
        try:
            client = await self._get_client()
            
            # Get publication counts by status
            publications_result = (
                client.table("publications")
                .select("status")
                .eq("user_id", user_id)
                .execute()
            )
            
            publications = publications_result.data or []
            status_counts = {}
            for pub in publications:
                status = pub.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Get revenue summary
            revenue_result = (
                client.table("sales_data")
                .select("royalty_earned", "units_sold", "created_at")
                .eq("user_id", user_id)
                .execute()
            )
            
            sales_data = revenue_result.data or []
            total_revenue = sum(float(s.get("royalty_earned", 0)) for s in sales_data)
            total_units = sum(int(s.get("units_sold", 0)) for s in sales_data)
            
            # Calculate recent performance (last 30 days)
            recent_cutoff = datetime.now() - timedelta(days=30)
            recent_sales = [s for s in sales_data if 
                          datetime.fromisoformat(s.get("created_at", "").replace('Z', '+00:00')) > recent_cutoff]
            
            recent_revenue = sum(float(s.get("royalty_earned", 0)) for s in recent_sales)
            recent_units = sum(int(s.get("units_sold", 0)) for s in recent_sales)
            
            return {
                "total_publications": len(publications),
                "publications_by_status": status_counts,
                "revenue_summary": {
                    "total_revenue": round(total_revenue, 2),
                    "total_units_sold": total_units,
                    "recent_30_day_revenue": round(recent_revenue, 2),
                    "recent_30_day_units": recent_units,
                    "average_revenue_per_book": round(total_revenue / max(1, status_counts.get("published", 1)), 2)
                },
                "performance_insights": {
                    "best_performing_month": self._get_best_performing_period(sales_data, "month"),
                    "average_time_to_publish": await self._calculate_avg_time_to_publish(user_id),
                    "success_rate": round((status_counts.get("published", 0) / max(1, len(publications))) * 100, 1)
                },
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get publication summary: {e}")
            raise

    def _calculate_performance_trend(self, sales_data: List[Dict[str, Any]]) -> str:
        """Calculate performance trend from sales data"""
        if len(sales_data) < 2:
            return "insufficient_data"
        
        # Sort by date and compare recent vs older performance
        sorted_sales = sorted(sales_data, key=lambda x: x.get("created_at", ""))
        midpoint = len(sorted_sales) // 2
        
        older_half = sorted_sales[:midpoint]
        recent_half = sorted_sales[midpoint:]
        
        older_avg = sum(float(s.get("royalty_earned", 0)) for s in older_half) / len(older_half)
        recent_avg = sum(float(s.get("royalty_earned", 0)) for s in recent_half) / len(recent_half)
        
        if recent_avg > older_avg * 1.1:
            return "improving"
        elif recent_avg < older_avg * 0.9:
            return "declining"
        else:
            return "stable"

    def _get_best_performing_period(self, sales_data: List[Dict[str, Any]], period: str = "month") -> Dict[str, Any]:
        """Get the best performing time period"""
        if not sales_data:
            return {"period": None, "revenue": 0}
        
        # Group by month and find best
        monthly_revenue = {}
        for sale in sales_data:
            date_str = sale.get("created_at", "")
            if date_str:
                month_key = date_str[:7]  # YYYY-MM
                monthly_revenue[month_key] = monthly_revenue.get(month_key, 0) + float(sale.get("royalty_earned", 0))
        
        if not monthly_revenue:
            return {"period": None, "revenue": 0}
        
        best_month = max(monthly_revenue.items(), key=lambda x: x[1])
        return {"period": best_month[0], "revenue": round(best_month[1], 2)}

    async def _calculate_avg_time_to_publish(self, user_id: str) -> float:
        """Calculate average time from book creation to publication"""
        try:
            client = await self._get_client()
            
            result = (
                client.table("publications")
                .select("created_at", "published_at", "books!inner(created_at)")
                .eq("user_id", user_id)
                .eq("status", "published")
                .execute()
            )
            
            publications = result.data or []
            
            if not publications:
                return 0.0
            
            total_days = 0
            count = 0
            
            for pub in publications:
                book_created = pub.get("books", {}).get("created_at")
                published_at = pub.get("published_at")
                
                if book_created and published_at:
                    book_dt = datetime.fromisoformat(book_created.replace('Z', '+00:00'))
                    pub_dt = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                    days_diff = (pub_dt - book_dt).days
                    total_days += days_diff
                    count += 1
            
            return round(total_days / max(1, count), 1)
            
        except Exception as e:
            logger.error(f"Failed to calculate average time to publish: {e}")
            return 0.0


# =====================================================
# PREDICTION MODEL - Sales forecasting and market analysis
# =====================================================

class PredictionModel(BaseModel):
    """Prediction model for sales forecasting and market analysis"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def predict_sales(self, book_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Predict sales performance for a book based on historical data and market trends"""
        try:
            client = await self._get_client()
            
            # Get historical sales data for similar books
            category = book_data.get("category", "")
            word_count = book_data.get("word_count", 0)
            
            # Query similar books in the same category
            similar_books_result = (
                client.table("books")
                .select("""
                    id, title, category, word_count, quality_score,
                    publications!inner(price, royalty_rate, status),
                    sales_data(units_sold, royalty_earned, average_rating)
                """)
                .eq("category", category)
                .eq("publications.status", "published")
                .gte("word_count", word_count * 0.7)  # 70% to 130% of word count
                .lte("word_count", word_count * 1.3)
                .limit(50)
                .execute()
            )
            
            similar_books = similar_books_result.data or []
            
            # Calculate baseline metrics from similar books
            if similar_books:
                total_units = sum(
                    sum(int(s.get("units_sold", 0)) for s in book.get("sales_data", []))
                    for book in similar_books
                )
                total_revenue = sum(
                    sum(float(s.get("royalty_earned", 0)) for s in book.get("sales_data", []))
                    for book in similar_books
                )
                avg_rating = sum(
                    sum(float(s.get("average_rating", 0)) for s in book.get("sales_data", []))
                    for book in similar_books
                ) / len(similar_books)
                
                avg_units_per_book = total_units / len(similar_books)
                avg_revenue_per_book = total_revenue / len(similar_books)
            else:
                # Fallback to category averages
                avg_units_per_book = 150  # Default estimate
                avg_revenue_per_book = 300  # Default estimate
                avg_rating = 4.2  # Default rating
            
            # Get market trends for the category
            trend_result = (
                client.table("trend_analyses")
                .select("trend_score", "competition_level", "market_saturation")
                .eq("category", category)
                .order("created_at", desc=True)
                .limit(5)
                .execute()
            )
            
            trend_data = trend_result.data or []
            if trend_data:
                avg_trend_score = sum(float(t.get("trend_score", 50)) for t in trend_data) / len(trend_data)
                competition_level = trend_data[0].get("competition_level", "medium")
                market_saturation = trend_data[0].get("market_saturation", 0.5)
            else:
                avg_trend_score = 50
                competition_level = "medium"
                market_saturation = 0.5
            
            # Calculate prediction factors
            factors = {
                "category_popularity": min(100, avg_trend_score) / 100,
                "title_appeal": 0.75,  # Would be calculated using NLP analysis
                "market_saturation": max(0.2, 1 - market_saturation),
                "word_count_factor": min(1.0, word_count / 10000),  # Normalize to 10k words
                "quality_factor": book_data.get("quality_score", 70) / 100,
                "competition_adjustment": {
                    "low": 1.2,
                    "medium": 1.0,
                    "high": 0.8
                }.get(competition_level, 1.0)
            }
            
            # Calculate predictions
            base_prediction = avg_units_per_book * factors["category_popularity"] * factors["competition_adjustment"]
            quality_adjusted = base_prediction * factors["quality_factor"]
            final_prediction = int(quality_adjusted * factors["word_count_factor"])
            
            # Calculate revenue estimate
            estimated_price = book_data.get("price", 4.99)
            royalty_rate = book_data.get("royalty_rate", 70) / 100
            revenue_estimate = final_prediction * estimated_price * royalty_rate
            
            # Calculate confidence based on data availability
            confidence = 0.6  # Base confidence
            if len(similar_books) > 10:
                confidence += 0.2
            if len(trend_data) > 2:
                confidence += 0.1
            if book_data.get("quality_score", 0) > 80:
                confidence += 0.1
            
            # Create prediction record
            prediction_record = {
                "user_id": user_id,
                "book_id": book_data.get("id"),
                "category": category,
                "predicted_units": final_prediction,
                "predicted_revenue": round(revenue_estimate, 2),
                "confidence_score": round(confidence, 2),
                "time_frame_days": 30,
                "prediction_factors": factors,
                "market_conditions": {
                    "trend_score": avg_trend_score,
                    "competition_level": competition_level,
                    "market_saturation": market_saturation
                },
                "created_at": datetime.now().isoformat()
            }
            
            # Store prediction in database
            prediction_result = client.table("sales_predictions").insert(prediction_record).execute()
            
            return {
                "prediction_id": prediction_result.data[0]["id"] if prediction_result.data else None,
                "predicted_sales": final_prediction,
                "confidence": round(confidence, 2),
                "revenue_estimate": round(revenue_estimate, 2),
                "time_frame": "30_days",
                "factors": factors,
                "market_insights": {
                    "category_trend": "positive" if avg_trend_score > 60 else "stable" if avg_trend_score > 40 else "declining",
                    "competition_level": competition_level,
                    "recommended_price_range": [
                        max(0.99, estimated_price * 0.8),
                        min(19.99, estimated_price * 1.2)
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to predict sales: {e}")
            raise

    async def get_market_predictions(self, category: str, user_id: str) -> Dict[str, Any]:
        """Get market predictions and opportunities for a category"""
        try:
            client = await self._get_client()
            
            # Get recent market data for the category
            market_result = (
                client.table("market_analyses")
                .select("*")
                .eq("category", category)
                .order("created_at", desc=True)
                .limit(10)
                .execute()
            )
            
            market_data = market_result.data or []
            
            # Get sales performance for books in this category
            sales_result = (
                client.table("sales_data")
                .select("""
                    units_sold, royalty_earned, average_rating, sale_date,
                    publications!inner(price, books!inner(category, word_count))
                """)
                .eq("publications.books.category", category)
                .gte("sale_date", (datetime.now() - timedelta(days=90)).isoformat())
                .execute()
            )
            
            sales_data = sales_result.data or []
            
            if sales_data:
                # Calculate market metrics
                total_units = sum(int(s.get("units_sold", 0)) for s in sales_data)
                total_revenue = sum(float(s.get("royalty_earned", 0)) for s in sales_data)
                avg_rating = sum(float(s.get("average_rating", 0)) for s in sales_data) / len(sales_data)
                
                prices = [float(s.get("publications", {}).get("price", 0)) for s in sales_data]
                word_counts = [int(s.get("publications", {}).get("books", {}).get("word_count", 0)) for s in sales_data]
                
                avg_price = sum(prices) / len(prices) if prices else 4.99
                avg_word_count = sum(word_counts) / len(word_counts) if word_counts else 8000
                
                # Calculate growth trend
                recent_sales = [s for s in sales_data if 
                              datetime.fromisoformat(s.get("sale_date", "").replace('Z', '+00:00')) > 
                              datetime.now() - timedelta(days=30)]
                
                growth_rate = len(recent_sales) / max(1, len(sales_data) - len(recent_sales))
                
            else:
                # Default values when no data available
                total_units = 100
                avg_rating = 4.0
                avg_price = 4.99
                avg_word_count = 8000
                growth_rate = 0.05
            
            # Determine competition level
            competition_level = "low"
            if len(sales_data) > 500:
                competition_level = "high"
            elif len(sales_data) > 100:
                competition_level = "medium"
            
            # Calculate market opportunity score
            opportunity_score = (
                (avg_rating / 5.0) * 0.3 +  # Quality factor
                (min(1.0, growth_rate) * 0.4) +  # Growth factor
                (1 - min(1.0, len(sales_data) / 1000)) * 0.3  # Competition factor
            )
            
            return {
                "category": category,
                "growth_forecast": round(growth_rate, 2),
                "competition_level": competition_level,
                "opportunity_score": round(opportunity_score, 2),
                "market_size": {
                    "total_books": len(sales_data),
                    "monthly_units": total_units,
                    "average_rating": round(avg_rating, 2)
                },
                "recommended_price_range": [
                    max(0.99, avg_price * 0.8),
                    min(19.99, avg_price * 1.2)
                ],
                "optimal_length": int(avg_word_count),
                "market_trends": {
                    "trending_up": growth_rate > 0.1,
                    "stable_demand": 0.05 <= growth_rate <= 0.1,
                    "declining": growth_rate < 0.05
                },
                "insights": [
                    f"Average book in this category sells {int(total_units / max(1, len(sales_data)))} units",
                    f"Competition level is {competition_level}",
                    f"Market opportunity score: {round(opportunity_score * 100, 1)}%"
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get market predictions: {e}")
            raise

    async def get_prediction_accuracy(self, user_id: str, days_back: int = 30) -> Dict[str, Any]:
        """Get accuracy metrics for previous predictions"""
        try:
            client = await self._get_client()
            
            # Get predictions made in the specified time period
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            predictions_result = (
                client.table("sales_predictions")
                .select("""
                    *,
                    prediction_accuracy(actual_units_sold, actual_revenue, accuracy_score)
                """)
                .eq("user_id", user_id)
                .gte("created_at", cutoff_date.isoformat())
                .execute()
            )
            
            predictions = predictions_result.data or []
            
            if not predictions:
                return {
                    "total_predictions": 0,
                    "average_accuracy": 0.0,
                    "insights": ["No predictions available for accuracy analysis"]
                }
            
            # Calculate accuracy metrics
            accuracy_scores = [float(p.get("prediction_accuracy", [{}])[0].get("accuracy_score", 0)) 
                             for p in predictions if p.get("prediction_accuracy")]
            
            avg_accuracy = sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0.0
            
            return {
                "total_predictions": len(predictions),
                "predictions_with_outcomes": len(accuracy_scores),
                "average_accuracy": round(avg_accuracy, 2),
                "accuracy_distribution": {
                    "high_accuracy": len([s for s in accuracy_scores if s > 0.8]),
                    "medium_accuracy": len([s for s in accuracy_scores if 0.6 <= s <= 0.8]),
                    "low_accuracy": len([s for s in accuracy_scores if s < 0.6])
                },
                "insights": [
                    f"Prediction accuracy is {'excellent' if avg_accuracy > 0.8 else 'good' if avg_accuracy > 0.6 else 'needs improvement'}",
                    f"{len(accuracy_scores)} predictions have measurable outcomes"
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get prediction accuracy: {e}")
            raise


async def get_publication_model() -> PublicationModel:
    """Get publication model instance"""
    return PublicationModel()


async def get_prediction_model() -> PredictionModel:
    """Get prediction model instance"""
    return PredictionModel()


# =====================================================
# MONITORING MODEL - System and VERL monitoring
# =====================================================

class MonitoringModel(BaseModel):
    """Monitoring model for system health and VERL training status"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_verl_monitoring(self, user_id: str) -> Dict[str, Any]:
        """Get VERL training monitoring data from database"""
        try:
            client = await self._get_client()
            
            # Get recent VERL training jobs
            training_result = (
                client.table("verl_training_jobs")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .limit(10)
                .execute()
            )
            
            training_jobs = training_result.data or []
            
            # Determine current training status
            if training_jobs:
                latest_job = training_jobs[0]
                training_status = latest_job.get("status", "idle")
                last_training = latest_job.get("created_at")
            else:
                training_status = "idle"
                last_training = None
            
            # Get feedback metrics for VERL quality
            feedback_result = (
                client.table("feedback_metrics")
                .select("feedback_score", "user_rating", "content_quality")
                .eq("user_id", user_id)
                .gte("created_at", (datetime.now() - timedelta(days=30)).isoformat())
                .execute()
            )
            
            feedback_data = feedback_result.data or []
            
            if feedback_data:
                approval_rate = sum(1 for f in feedback_data if f.get("user_rating", 0) >= 4) / len(feedback_data)
                avg_quality = sum(float(f.get("content_quality", 0)) for f in feedback_data) / len(feedback_data)
                training_examples = len(feedback_data)
            else:
                approval_rate = 0.0
                avg_quality = 0.0
                training_examples = 0
            
            # Get today's activity counters
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Today's feedback
            today_feedback_result = (
                client.table("feedback_metrics")
                .select("user_rating")
                .eq("user_id", user_id)
                .gte("created_at", today_start.isoformat())
                .execute()
            )
            
            today_feedback = today_feedback_result.data or []
            approvals_today = sum(1 for f in today_feedback if f.get("user_rating", 0) >= 4)
            rejections_today = sum(1 for f in today_feedback if f.get("user_rating", 0) < 4)
            
            # Today's books generated
            books_today_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .gte("created_at", today_start.isoformat())
                .execute()
            )
            
            books_generated_today = books_today_result.count or 0
            
            return {
                "training_status": training_status,
                "last_training": last_training,
                "next_training_scheduled": self._calculate_next_training(training_jobs),
                "metrics": {
                    "approval_rate": round(approval_rate, 2),
                    "quality_score": round(avg_quality / 100, 2),  # Normalize to 0-1
                    "training_examples": training_examples,
                    "model_version": latest_job.get("model_version", "v1.0") if training_jobs else "v1.0"
                },
                "live_counters": {
                    "approvals_today": approvals_today,
                    "rejections_today": rejections_today,
                    "books_generated": books_generated_today,
                    "feedback_collected": len(today_feedback)
                },
                "training_history": [
                    {
                        "id": job.get("id"),
                        "status": job.get("status"),
                        "started_at": job.get("created_at"),
                        "completed_at": job.get("updated_at"),
                        "model_version": job.get("model_version"),
                        "examples_used": job.get("training_examples_count", 0)
                    }
                    for job in training_jobs[:5]  # Last 5 training jobs
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get VERL monitoring: {e}")
            raise

    async def get_system_monitoring(self, user_id: str) -> Dict[str, Any]:
        """Get system monitoring data from database"""
        try:
            client = await self._get_client()
            
            # Get system uptime from earliest record
            uptime_result = (
                client.table("books")
                .select("created_at")
                .order("created_at", desc=False)
                .limit(1)
                .execute()
            )
            
            if uptime_result.data:
                first_record = datetime.fromisoformat(uptime_result.data[0]["created_at"].replace('Z', '+00:00'))
                uptime_delta = datetime.now() - first_record.replace(tzinfo=None)
                uptime = f"{uptime_delta.days}d {uptime_delta.seconds // 3600}h {(uptime_delta.seconds % 3600) // 60}m"
            else:
                uptime = "0h 0m"
            
            # Get active tasks (books in progress)
            active_tasks_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .in_("status", ["generating", "awaiting_approval"])
                .execute()
            )
            
            active_tasks = active_tasks_result.count or 0
            
            # Get queue length (pending publications)
            queue_result = (
                client.table("publications")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .in_("status", ["draft", "pending"])
                .execute()
            )
            
            queue_length = queue_result.count or 0
            
            # Get recent activity metrics
            last_24h = datetime.now() - timedelta(hours=24)
            
            activity_result = (
                client.table("books")
                .select("status", "created_at")
                .eq("user_id", user_id)
                .gte("created_at", last_24h.isoformat())
                .execute()
            )
            
            recent_activity = activity_result.data or []
            
            # Calculate system health
            error_rate = sum(1 for a in recent_activity if a.get("status") == "failed") / max(1, len(recent_activity))
            health_status = "healthy" if error_rate < 0.1 else "degraded" if error_rate < 0.3 else "unhealthy"
            
            # Get resource usage metrics
            total_books_result = (
                client.table("books")
                .select("id", count=CountMethod.exact)
                .eq("user_id", user_id)
                .execute()
            )
            
            total_books = total_books_result.count or 0
            
            return {
                "status": health_status,
                "uptime": uptime,
                "active_tasks": active_tasks,
                "queue_length": queue_length,
                "health_metrics": {
                    "error_rate": round(error_rate, 3),
                    "total_books": total_books,
                    "recent_activity_24h": len(recent_activity),
                    "success_rate": round(1 - error_rate, 3)
                },
                "resource_usage": {
                    "database_connections": "healthy",
                    "api_rate_limit_remaining": "95%",
                    "storage_usage": f"{total_books * 2.5:.1f}MB",  # Estimate
                    "memory_usage": "normal"
                },
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get system monitoring: {e}")
            raise

    def _calculate_next_training(self, training_jobs: List[Dict[str, Any]]) -> Optional[str]:
        """Calculate when next VERL training should occur"""
        if not training_jobs:
            return None
        
        # Find the last completed training
        completed_jobs = [job for job in training_jobs if job.get("status") == "completed"]
        
        if completed_jobs:
            last_completed = datetime.fromisoformat(completed_jobs[0]["updated_at"].replace('Z', '+00:00'))
            # Schedule next training 7 days after last completion
            next_training = last_completed + timedelta(days=7)
            
            if next_training > datetime.now():
                return next_training.isoformat()
        
        return None


async def get_monitoring_model() -> MonitoringModel:
    """Get monitoring model instance"""
    return MonitoringModel()


# =====================================================
# AGENTS MODEL - Agent task management and execution tracking
# =====================================================

class AgentsModel(BaseModel):
    """Agents model for managing PydanticAI agent tasks and execution status"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def create_agent_task(self, user_id: str, task_type: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new agent task record"""
        try:
            client = await self._get_client()
            
            # Generate task ID
            import uuid
            task_id = f"task_{user_id}_{task_type}_{str(uuid.uuid4())[:8]}"
            
            # Estimate completion time based on task type
            completion_estimates = {
                "manuscript": "15-30 minutes",
                "trend_analysis": "5-10 minutes",
                "cover_design": "3-5 minutes",
                "market_analysis": "2-5 minutes"
            }
            
            # Create task record using books table with special status
            task_record = {
                "user_id": user_id,
                "title": f"{task_type.replace('_', ' ').title()} Task",
                "category": "system_task",
                "status": "generating",
                "content_metadata": {
                    "task_id": task_id,
                    "task_type": task_type,
                    "task_data": task_data,
                    "estimated_completion": completion_estimates.get(task_type, "5-15 minutes"),
                    "progress": 0,
                    "current_step": "Initializing...",
                    "steps_completed": 0,
                    "total_steps": self._get_total_steps(task_type)
                },
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            result = client.table("books").insert(task_record).execute()
            
            if result.data:
                task = result.data[0]
                logger.info(f"Created agent task {task_id} for user {user_id}")
                
                return {
                    "task_id": task_id,
                    "book_id": task["id"],
                    "status": "started",
                    "message": f"{task_type.replace('_', ' ').title()} started",
                    "estimated_completion": completion_estimates.get(task_type, "5-15 minutes"),
                    "progress": 0,
                    "current_step": "Initializing..."
                }
            
            raise Exception("No data returned from task creation")
            
        except Exception as e:
            logger.error(f"Failed to create agent task: {e}")
            raise

    async def get_agent_task_status(self, task_id: str, user_id: str) -> Dict[str, Any]:
        """Get status of an agent task"""
        try:
            client = await self._get_client()
            
            # Find task by task_id in content_metadata
            result = (
                client.table("books")
                .select("*")
                .eq("user_id", user_id)
                .eq("category", "system_task")
                .execute()
            )
            
            tasks = result.data or []
            
            # Find the task with matching task_id
            task = None
            for t in tasks:
                metadata = t.get("content_metadata", {})
                if metadata.get("task_id") == task_id:
                    task = t
                    break
            
            if not task:
                raise ValueError("Task not found")
            
            metadata = task.get("content_metadata", {})
            
            # Calculate progress based on task age and type
            created_at = datetime.fromisoformat(task["created_at"].replace('Z', '+00:00'))
            elapsed_minutes = (datetime.now() - created_at.replace(tzinfo=None)).total_seconds() / 60
            
            # Simulate progress based on elapsed time and task type
            progress = self._calculate_progress(metadata.get("task_type", ""), elapsed_minutes)
            current_step = self._get_current_step(metadata.get("task_type", ""), progress)
            
            # Estimate remaining time
            total_estimated_minutes = self._get_estimated_minutes(metadata.get("task_type", ""))
            remaining_minutes = max(0, total_estimated_minutes - elapsed_minutes)
            
            # Determine status
            status = task.get("status", "processing")
            if progress >= 100:
                status = "completed"
            elif status == "generating":
                status = "processing"
            
            return {
                "task_id": task_id,
                "status": status,
                "progress": min(100, int(progress)),
                "current_step": current_step,
                "estimated_remaining": f"{int(remaining_minutes)} minutes" if remaining_minutes > 0 else "Completing...",
                "steps_completed": int(progress / 100 * metadata.get("total_steps", 5)),
                "total_steps": metadata.get("total_steps", 5),
                "created_at": task["created_at"],
                "task_type": metadata.get("task_type")
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent task status: {e}")
            raise

    async def update_agent_task_progress(self, task_id: str, user_id: str, progress: int, 
                                       current_step: str, status: str = None) -> Dict[str, Any]:
        """Update agent task progress"""
        try:
            client = await self._get_client()
            
            # Find and update task
            result = (
                client.table("books")
                .select("*")
                .eq("user_id", user_id)
                .eq("category", "system_task")
                .execute()
            )
            
            tasks = result.data or []
            
            # Find the task with matching task_id
            task = None
            for t in tasks:
                metadata = t.get("content_metadata", {})
                if metadata.get("task_id") == task_id:
                    task = t
                    break
            
            if not task:
                raise ValueError("Task not found")
            
            # Update metadata
            metadata = task.get("content_metadata", {})
            metadata["progress"] = progress
            metadata["current_step"] = current_step
            metadata["steps_completed"] = int(progress / 100 * metadata.get("total_steps", 5))
            
            # Update status if provided
            update_data = {
                "content_metadata": metadata,
                "updated_at": datetime.now().isoformat()
            }
            
            if status:
                update_data["status"] = status
            
            update_result = (
                client.table("books")
                .update(update_data)
                .eq("id", task["id"])
                .execute()
            )
            
            if update_result.data:
                logger.info(f"Updated agent task {task_id} progress to {progress}%")
                return update_result.data[0]
            
            raise Exception("Failed to update task progress")
            
        except Exception as e:
            logger.error(f"Failed to update agent task progress: {e}")
            raise

    async def get_user_agent_tasks(self, user_id: str, status: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Get all agent tasks for a user"""
        try:
            client = await self._get_client()
            
            query = (
                client.table("books")
                .select("*")
                .eq("user_id", user_id)
                .eq("category", "system_task")
                .order("created_at", desc=True)
                .limit(limit)
            )
            
            if status:
                query = query.eq("status", status)
            
            result = query.execute()
            tasks = result.data or []
            
            # Format tasks for response
            formatted_tasks = []
            for task in tasks:
                metadata = task.get("content_metadata", {})
                
                formatted_tasks.append({
                    "task_id": metadata.get("task_id"),
                    "book_id": task["id"],
                    "task_type": metadata.get("task_type"),
                    "status": task.get("status"),
                    "progress": metadata.get("progress", 0),
                    "current_step": metadata.get("current_step", ""),
                    "created_at": task["created_at"],
                    "updated_at": task["updated_at"],
                    "estimated_completion": metadata.get("estimated_completion")
                })
            
            return formatted_tasks
            
        except Exception as e:
            logger.error(f"Failed to get user agent tasks: {e}")
            raise

    def _get_total_steps(self, task_type: str) -> int:
        """Get total steps for a task type"""
        steps_map = {
            "manuscript": 8,  # Research, outline, 6 chapters
            "trend_analysis": 5,  # Data collection, analysis, ranking, insights, report
            "cover_design": 4,  # Template selection, customization, rendering, optimization
            "market_analysis": 3  # Data gathering, competition analysis, report generation
        }
        return steps_map.get(task_type, 5)

    def _calculate_progress(self, task_type: str, elapsed_minutes: float) -> float:
        """Calculate progress based on task type and elapsed time"""
        estimated_minutes = self._get_estimated_minutes(task_type)
        
        # Progress follows a logarithmic curve (faster at start, slower at end)
        if elapsed_minutes >= estimated_minutes:
            return 100.0
        
        progress_ratio = elapsed_minutes / estimated_minutes
        # Logarithmic progress: faster initial progress, slower completion
        progress = 100 * (1 - (1 - progress_ratio) ** 0.7)
        
        return min(100.0, max(0.0, progress))

    def _get_estimated_minutes(self, task_type: str) -> float:
        """Get estimated completion time in minutes"""
        estimates = {
            "manuscript": 22.5,  # 15-30 minutes average
            "trend_analysis": 7.5,  # 5-10 minutes average
            "cover_design": 4.0,  # 3-5 minutes average
            "market_analysis": 3.5  # 2-5 minutes average
        }
        return estimates.get(task_type, 10.0)

    def _get_current_step(self, task_type: str, progress: float) -> str:
        """Get current step description based on progress"""
        step_descriptions = {
            "manuscript": [
                "Analyzing market trends and topic research...",
                "Creating detailed book outline...",
                "Generating introduction...",
                "Writing chapter 1...",
                "Writing chapter 2...",
                "Writing chapter 3...",
                "Writing chapter 4...",
                "Writing chapter 5...",
                "Writing chapter 6...",
                "Compiling and formatting final manuscript...",
                "Finalizing and quality checking..."
            ],
            "trend_analysis": [
                "Collecting market data...",
                "Analyzing trending keywords...",
                "Evaluating competition levels...",
                "Generating market insights...",
                "Compiling analysis report..."
            ],
            "cover_design": [
                "Selecting optimal design template...",
                "Customizing design elements...",
                "Rendering high-resolution cover...",
                "Optimizing for different platforms..."
            ],
            "market_analysis": [
                "Gathering market data...",
                "Analyzing competitive landscape...",
                "Generating insights and recommendations..."
            ]
        }
        
        steps = step_descriptions.get(task_type, ["Processing..."])
        step_index = min(len(steps) - 1, int(progress / 100 * len(steps)))
        
        return steps[step_index]


async def get_agents_model() -> AgentsModel:
    """Get agents model instance"""
    return AgentsModel()


class FeedbackModel(BaseModel):
    """Model for handling user feedback and analytics"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client
    
    async def create_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new feedback record"""
        try:
            client = await self._get_client()
            
            # Prepare feedback data
            feedback_record = {
                "user_id": feedback_data.get("user_id"),
                "book_id": feedback_data.get("book_id"),
                "rating": feedback_data.get("rating", 5),
                "feedback_text": feedback_data.get("feedback_text", ""),
                "feedback_type": feedback_data.get("feedback_type", "general"),
                "quality_score": feedback_data.get("quality_score", 0.0),
                "satisfaction_score": feedback_data.get("satisfaction_score", 0.0),
                "performance_metrics": feedback_data.get("performance_metrics", {}),
                "metadata": feedback_data.get("metadata", {}),
                "created_at": datetime.now().isoformat()
            }
            
            result = client.table("feedback_metrics").insert(feedback_record).execute()
            
            if result.data:
                logger.info(f"Feedback created successfully: {result.data[0]['id']}")
                return result.data[0]
            else:
                raise Exception("Failed to create feedback record")
                
        except Exception as e:
            logger.error(f"Failed to create feedback: {e}")
            raise
    
    async def get_feedback_analytics(self, user_id: str, days_back: int = 30) -> Dict[str, Any]:
        """Get feedback analytics for a specific user"""
        try:
            client = await self._get_client()
            
            # Get date range
            since_date = datetime.now() - timedelta(days=days_back)
            
            # Get user's feedback records
            result = (
                client.table("feedback_metrics")
                .select("*")
                .eq("user_id", user_id)
                .gte("created_at", since_date.isoformat())
                .order("created_at", desc=True)
                .execute()
            )
            
            feedback_records = result.data or []
            
            if not feedback_records:
                return {
                    "user_approval": {
                        "values": [],
                        "timestamps": [],
                        "average": 0.0,
                        "trend": "no_data"
                    },
                    "content_quality": {
                        "values": [],
                        "timestamps": [],
                        "average": 0.0,
                        "trend": "no_data"
                    },
                    "total_feedback": 0,
                    "period_days": days_back
                }
            
            # Process approval ratings
            approval_data = []
            quality_data = []
            timestamps = []
            
            for record in feedback_records:
                created_at = record.get("created_at", "")
                approval_score = min(1.0, max(0.0, float(record.get("rating", 5)) / 5.0))
                quality_score = float(record.get("quality_score", 0.0))
                
                approval_data.append(approval_score)
                quality_data.append(quality_score)
                timestamps.append(created_at[:10])  # Date only
            
            # Calculate averages
            approval_avg = sum(approval_data) / len(approval_data) if approval_data else 0.0
            quality_avg = sum(quality_data) / len(quality_data) if quality_data else 0.0
            
            # Calculate trends
            approval_trend = self._calculate_trend(approval_data)
            quality_trend = self._calculate_trend(quality_data)
            
            return {
                "user_approval": {
                    "values": approval_data[-10:],  # Last 10 data points
                    "timestamps": timestamps[-10:],
                    "average": round(approval_avg, 2),
                    "trend": approval_trend
                },
                "content_quality": {
                    "values": quality_data[-10:],
                    "timestamps": timestamps[-10:],
                    "average": round(quality_avg, 2),
                    "trend": quality_trend
                },
                "total_feedback": len(feedback_records),
                "period_days": days_back
            }
            
        except Exception as e:
            logger.error(f"Failed to get feedback analytics: {e}")
            raise
    
    async def get_dashboard_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get feedback analytics for dashboard display"""
        try:
            # Get analytics for the last 30 days
            analytics = await self.get_feedback_analytics(user_id, days_back=30)
            
            # Format for dashboard
            return {
                "user_approval": analytics["user_approval"],
                "content_quality": analytics["content_quality"],
                "summary": {
                    "total_feedback": analytics["total_feedback"],
                    "approval_rate": analytics["user_approval"]["average"],
                    "quality_score": analytics["content_quality"]["average"],
                    "trend_direction": analytics["user_approval"]["trend"]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard analytics: {e}")
            raise
    
    async def get_recent_feedback(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent feedback for a user"""
        try:
            client = await self._get_client()
            
            result = (
                client.table("feedback_metrics")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Failed to get recent feedback: {e}")
            raise
    
    async def count_recent_feedback(self, user_id: str, days_back: int = 7) -> int:
        """Count recent feedback for a user"""
        try:
            client = await self._get_client()
            
            since_date = datetime.now() - timedelta(days=days_back)
            
            result = (
                client.table("feedback_metrics")
                .select("id", count="exact")
                .eq("user_id", user_id)
                .gte("created_at", since_date.isoformat())
                .execute()
            )
            
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Failed to count recent feedback: {e}")
            return 0
    
    async def count_recent_feedback(self, cutoff_time: datetime) -> int:
        """Count feedback entries created after cutoff_time"""
        try:
            client = await self._get_client()
            result = (
                client.table("feedback_metrics").select("id", count=CountMethod.exact)
                .gte("created_at", cutoff_time.isoformat())
                .execute()
            )
            return result.count if result.count is not None else 0
        except Exception as e:
            logger.error(f"Failed to count recent feedback: {e}")
            return 0
    
    def _calculate_trend(self, data: List[float]) -> str:
        """Calculate trend direction from data series"""
        if len(data) < 2:
            return "stable"
        
        # Calculate simple linear trend
        first_half = data[:len(data)//2]
        second_half = data[len(data)//2:]
        
        if not first_half or not second_half:
            return "stable"
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        change = second_avg - first_avg
        
        if change > 0.05:
            return "improving"
        elif change < -0.05:
            return "declining"
        else:
            return "stable"


# Model Factory Functions

async def get_user_model() -> UserModel:
    """Get user model instance"""
    return UserModel()

async def get_user_preferences_model() -> UserPreferencesModel:
    """Get user preferences model instance"""
    return UserPreferencesModel()

async def get_user_publishing_settings_model() -> UserPublishingSettingsModel:
    """Get user publishing settings model instance"""
    return UserPublishingSettingsModel()

async def get_user_platform_integrations_model() -> UserPlatformIntegrationsModel:
    """Get user platform integrations model instance"""
    return UserPlatformIntegrationsModel()

async def get_user_security_settings_model() -> UserSecuritySettingsModel:
    """Get user security settings model instance"""
    return UserSecuritySettingsModel()

async def get_user_active_sessions_model() -> UserActiveSessionsModel:
    """Get user active sessions model instance"""
    return UserActiveSessionsModel()

async def get_user_api_keys_model() -> UserApiKeysModel:
    """Get user API keys model instance"""
    return UserApiKeysModel()

class MarketAnalysisModel(BaseModel):
    """Market analysis model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_user_market_analyses(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's market analyses"""
        try:
            client = await self._get_client()
            result = (
                client.table("market_analyses")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )
            
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get market analyses: {e}")
            return []

    async def create_market_analysis(self, user_id: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new market analysis"""
        try:
            client = await self._get_client()
            
            insert_data = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "analysis_name": analysis_data.get("analysis_name"),
                "market_segment": analysis_data.get("market_segment"),
                "analysis_type": analysis_data.get("analysis_type", "competitive_analysis"),
                "market_size_estimate": analysis_data.get("market_size_estimate"),
                "market_growth_rate": analysis_data.get("market_growth_rate"),
                "key_competitors": analysis_data.get("key_competitors", []),
                "competitive_advantages": analysis_data.get("competitive_advantages", []),
                "opportunity_score": analysis_data.get("opportunity_score"),
                "entry_barriers": analysis_data.get("entry_barriers", []),
                "success_factors": analysis_data.get("success_factors", []),
                "risk_factors": analysis_data.get("risk_factors", []),
                "primary_audience": analysis_data.get("primary_audience", {}),
                "secondary_audience": analysis_data.get("secondary_audience", {}),
                "audience_size_estimate": analysis_data.get("audience_size_estimate"),
                "price_range_low": analysis_data.get("price_range_low"),
                "price_range_high": analysis_data.get("price_range_high"),
                "optimal_price_point": analysis_data.get("optimal_price_point"),
                "price_sensitivity_analysis": analysis_data.get("price_sensitivity_analysis", {}),
                "content_gaps": analysis_data.get("content_gaps", []),
                "trending_topics": analysis_data.get("trending_topics", []),
                "recommended_keywords": analysis_data.get("recommended_keywords", []),
                "analysis_results": analysis_data.get("analysis_results", {}),
                "key_insights": analysis_data.get("key_insights", []),
                "recommendations": analysis_data.get("recommendations", []),
                "data_sources": analysis_data.get("data_sources", []),
                "analysis_methodology": analysis_data.get("analysis_methodology"),
                "analysis_date": analysis_data.get("analysis_date", datetime.utcnow().date().isoformat()),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = client.table("market_analyses").insert(insert_data).execute()
            
            if result.data:
                return result.data[0]
            raise Exception("No data returned from market analysis creation")
        except Exception as e:
            logger.error(f"Failed to create market analysis: {e}")
            raise

    async def get_market_analysis_by_id(self, analysis_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get market analysis by ID"""
        try:
            client = await self._get_client()
            result = (
                client.table("market_analyses")
                .select("*")
                .eq("id", analysis_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get market analysis: {e}")
            return None

    async def update_market_analysis(self, analysis_id: str, user_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update market analysis"""
        try:
            client = await self._get_client()
            
            updates["updated_at"] = datetime.utcnow().isoformat()
            
            result = (
                client.table("market_analyses")
                .update(updates)
                .eq("id", analysis_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            if result.data:
                return result.data[0]
            raise Exception("No data returned from market analysis update")
        except Exception as e:
            logger.error(f"Failed to update market analysis: {e}")
            raise

    async def delete_market_analysis(self, analysis_id: str, user_id: str) -> bool:
        """Delete market analysis"""
        try:
            client = await self._get_client()
            result = (
                client.table("market_analyses")
                .delete()
                .eq("id", analysis_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"Failed to delete market analysis: {e}")
            return False

    async def refresh_market_analysis(self, analysis_id: str, user_id: str) -> Dict[str, Any]:
        """Refresh market analysis with updated data"""
        try:
            # Get existing analysis
            analysis = await self.get_market_analysis_by_id(analysis_id, user_id)
            if not analysis:
                raise Exception("Market analysis not found")
            
            # Import market analyzer here to avoid circular imports
            from app.prediction.market_analyzer import MarketIntelligenceEngine
            
            # Refresh market data
            market_engine = MarketIntelligenceEngine()
            updated_conditions = await market_engine.analyze_market_conditions(
                analysis["market_segment"], 
                force_refresh=True
            )
            
            # Update analysis with fresh data
            updates = {
                "analysis_results": updated_conditions,
                "analysis_date": datetime.utcnow().date().isoformat()
            }
            
            return await self.update_market_analysis(analysis_id, user_id, updates)
        except Exception as e:
            logger.error(f"Failed to refresh market analysis: {e}")
            raise


class PredictionAccuracyModel(BaseModel):
    """Prediction accuracy model for Supabase operations"""
    
    async def _get_client(self):
        """Get shared Supabase client instance"""
        shared_client = await get_supabase_client_async()
        return shared_client.client

    async def get_accuracy_metrics(self, user_id: str) -> List[Dict[str, Any]]:
        """Get prediction accuracy metrics for user"""
        try:
            client = await self._get_client()
            result = (
                client.table("prediction_accuracy")
                .select("""
                    *,
                    sales_predictions (
                        id,
                        book_id,
                        prediction_type,
                        predicted_sales,
                        predicted_revenue,
                        confidence_score,
                        model_name,
                        prediction_date,
                        target_date
                    )
                """)
                .eq("sales_predictions.user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )
            
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get accuracy metrics: {e}")
            return []

    async def record_actual_results(self, prediction_id: str, actual_data: Dict[str, Any]) -> Dict[str, Any]:
        """Record actual results for prediction accuracy calculation"""
        try:
            client = await self._get_client()
            
            # First, get the prediction to calculate accuracy
            prediction_result = (
                client.table("sales_predictions")
                .select("*")
                .eq("id", prediction_id)
                .execute()
            )
            
            if not prediction_result.data:
                raise Exception("Prediction not found")
            
            prediction = prediction_result.data[0]
            actual_sales = actual_data.get("actual_sales", 0)
            actual_revenue = actual_data.get("actual_revenue", 0.0)
            
            # Calculate accuracy metrics
            predicted_sales = prediction["predicted_sales"]
            predicted_revenue = prediction["predicted_revenue"]
            
            absolute_error = abs(actual_sales - predicted_sales)
            relative_error = (actual_sales - predicted_sales) / max(1, predicted_sales)
            squared_error = (actual_sales - predicted_sales) ** 2
            percentage_error = ((actual_sales - predicted_sales) / max(1, predicted_sales)) * 100
            
            # Determine if prediction was correct (within 20% tolerance)
            correct_prediction = abs(relative_error) <= 0.2
            
            # Create accuracy record
            accuracy_data = {
                "id": str(uuid.uuid4()),
                "prediction_id": prediction_id,
                "absolute_error": absolute_error,
                "relative_error": float(relative_error),
                "squared_error": float(squared_error),
                "percentage_error": float(percentage_error),
                "correct_prediction": correct_prediction,
                "model_confidence": prediction.get("confidence_score", 0),
                "prediction_quality": "good" if correct_prediction else "poor",
                "evaluation_date": datetime.utcnow().date().isoformat(),
                "evaluation_notes": actual_data.get("evaluation_notes"),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Insert accuracy record
            accuracy_result = client.table("prediction_accuracy").insert(accuracy_data).execute()
            
            # Update the prediction with actual results
            prediction_updates = {
                "actual_sales": actual_sales,
                "actual_revenue": actual_revenue,
                "accuracy_score": 1.0 - min(1.0, abs(relative_error)),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            client.table("sales_predictions").update(prediction_updates).eq("id", prediction_id).execute()
            
            if accuracy_result.data:
                return accuracy_result.data[0]
            raise Exception("No data returned from accuracy record creation")
        except Exception as e:
            logger.error(f"Failed to record actual results: {e}")
            raise

    async def get_accuracy_by_prediction(self, prediction_id: str) -> Optional[Dict[str, Any]]:
        """Get accuracy metrics for specific prediction"""
        try:
            client = await self._get_client()
            result = (
                client.table("prediction_accuracy")
                .select("*")
                .eq("prediction_id", prediction_id)
                .execute()
            )
            
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get prediction accuracy: {e}")
            return None

    async def get_accuracy_summary(self, user_id: str) -> Dict[str, Any]:
        """Get accuracy summary dashboard for user"""
        try:
            client = await self._get_client()
            
            # Get all accuracy records for user's predictions
            result = (
                client.table("prediction_accuracy")
                .select("""
                    *,
                    sales_predictions!inner (
                        user_id,
                        model_name,
                        prediction_type
                    )
                """)
                .eq("sales_predictions.user_id", user_id)
                .execute()
            )
            
            accuracy_records = result.data or []
            
            if not accuracy_records:
                return {
                    "total_predictions": 0,
                    "correct_predictions": 0,
                    "accuracy_rate": 0.0,
                    "average_error": 0.0,
                    "model_performance": {}
                }
            
            # Calculate summary metrics
            total_predictions = len(accuracy_records)
            correct_predictions = sum(1 for r in accuracy_records if r["correct_prediction"])
            accuracy_rate = correct_predictions / total_predictions if total_predictions > 0 else 0.0
            
            # Calculate average absolute error
            total_error = sum(abs(r["absolute_error"]) for r in accuracy_records)
            average_error = total_error / total_predictions if total_predictions > 0 else 0.0
            
            # Group by model performance
            model_performance = {}
            for record in accuracy_records:
                model_name = record["sales_predictions"]["model_name"]
                if model_name not in model_performance:
                    model_performance[model_name] = {
                        "total": 0,
                        "correct": 0,
                        "accuracy_rate": 0.0
                    }
                
                model_performance[model_name]["total"] += 1
                if record["correct_prediction"]:
                    model_performance[model_name]["correct"] += 1
            
            # Calculate accuracy rates for each model
            for model_name in model_performance:
                model_data = model_performance[model_name]
                model_data["accuracy_rate"] = model_data["correct"] / model_data["total"]
            
            return {
                "total_predictions": total_predictions,
                "correct_predictions": correct_predictions,
                "accuracy_rate": accuracy_rate,
                "average_error": average_error,
                "model_performance": model_performance
            }
        except Exception as e:
            logger.error(f"Failed to get accuracy summary: {e}")
            return {}

    async def get_model_comparison(self, user_id: str) -> List[Dict[str, Any]]:
        """Get model performance comparison"""
        try:
            client = await self._get_client()
            
            # Get accuracy data grouped by model
            result = (
                client.table("prediction_accuracy")
                .select("""
                    *,
                    sales_predictions!inner (
                        user_id,
                        model_name,
                        model_version,
                        prediction_type,
                        confidence_score
                    )
                """)
                .eq("sales_predictions.user_id", user_id)
                .execute()
            )
            
            accuracy_records = result.data or []
            
            # Group by model
            model_stats = {}
            for record in accuracy_records:
                prediction = record["sales_predictions"]
                model_key = f"{prediction['model_name']}_{prediction.get('model_version', 'v1')}"
                
                if model_key not in model_stats:
                    model_stats[model_key] = {
                        "model_name": prediction["model_name"],
                        "model_version": prediction.get("model_version", "v1"),
                        "total_predictions": 0,
                        "correct_predictions": 0,
                        "total_error": 0,
                        "confidence_scores": []
                    }
                
                stats = model_stats[model_key]
                stats["total_predictions"] += 1
                stats["total_error"] += abs(record["absolute_error"])
                stats["confidence_scores"].append(prediction.get("confidence_score", 0))
                
                if record["correct_prediction"]:
                    stats["correct_predictions"] += 1
            
            # Calculate final metrics
            comparison_results = []
            for model_key, stats in model_stats.items():
                accuracy_rate = stats["correct_predictions"] / stats["total_predictions"]
                average_error = stats["total_error"] / stats["total_predictions"]
                average_confidence = sum(stats["confidence_scores"]) / len(stats["confidence_scores"])
                
                comparison_results.append({
                    "model_name": stats["model_name"],
                    "model_version": stats["model_version"],
                    "total_predictions": stats["total_predictions"],
                    "accuracy_rate": accuracy_rate,
                    "average_error": average_error,
                    "average_confidence": average_confidence,
                    "performance_score": (accuracy_rate + (1 - average_error / 100)) / 2  # Combined score
                })
            
            # Sort by performance score
            comparison_results.sort(key=lambda x: x["performance_score"], reverse=True)
            
            return comparison_results
        except Exception as e:
            logger.error(f"Failed to get model comparison: {e}")
            return []


# Legacy factory functions preserved for backwards compatibility
async def get_feedback_model() -> FeedbackModel:
    """Get feedback model instance"""
    return FeedbackModel()

def get_market_analysis_model() -> MarketAnalysisModel:
    """Get market analysis model instance"""
    return MarketAnalysisModel()

def get_prediction_accuracy_model() -> PredictionAccuracyModel:
    """Get prediction accuracy model instance"""
    return PredictionAccuracyModel()
