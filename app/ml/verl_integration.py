# app/ml/verl_integration.py
"""
VERL Integration for Main Publishing App
Handles communication with VERL training service
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import redis.asyncio as redis
from app.models.supabase_models import get_feedback_model, get_model_performance_model
from app.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class VERLIntegrationManager:
    """Manages integration with VERL training service"""
    
    def __init__(self):
        self.verl_url = settings.verl_trainer_url or "http://verl-trainer:8001"
        self.redis_client = None
        self.notification_subscriber = None
        self.min_training_examples = settings.min_training_examples or 50
        self.training_interval_hours = settings.verl_training_interval_hours or 24
        self.feedback_model = None
        self.model_performance_model = None
        
    async def initialize(self):
        """Initialize Redis connection and start notification listener"""
        try:
            # Initialize model instances
            self.feedback_model = await get_feedback_model()
            self.model_performance_model = await get_model_performance_model()
            
            # Try Redis connection with timeout
            try:
                self.redis_client = redis.from_url(settings.redis_url)
                await asyncio.wait_for(self.redis_client.ping(), timeout=5.0)
                
                # Start listening for VERL notifications
                await self._start_notification_listener()
                logger.info("VERL integration initialized successfully with Redis")
            except (asyncio.TimeoutError, Exception) as redis_error:
                logger.warning(f"Redis connection failed: {redis_error}")
                logger.info("VERL integration running in degraded mode (no Redis notifications)")
                self.redis_client = None
            
        except Exception as e:
            logger.warning(f"VERL integration initialization failed: {e}")
            logger.info("Application will continue without VERL features")
            # Don't raise - allow app to continue without VERL
    
    async def _start_notification_listener(self):
        """Start listening for notifications from VERL service"""
        if not self.redis_client:
            logger.warning("Redis client not available, skipping notification listener")
            return
            
        try:
            pubsub = self.redis_client.pubsub()
            await pubsub.subscribe("verl_notifications")
            
            # Start background task to handle notifications
            asyncio.create_task(self._handle_notifications(pubsub))
            
        except Exception as e:
            logger.warning(f"Failed to start notification listener: {e}")
    
    async def _handle_notifications(self, pubsub):
        """Handle notifications from VERL service"""
        try:
            async for message in pubsub.listen():
                if message["type"] == "message":
                    try:
                        data = json.loads(message["data"])
                        await self._process_notification(data)
                    except Exception as e:
                        logger.error(f"Failed to process VERL notification: {e}")
                        
        except Exception as e:
            logger.error(f"Notification listener error: {e}")
    
    async def _process_notification(self, notification: Dict):
        """Process notification from VERL service"""
        event_type = notification.get("event_type")
        data = notification.get("data", {})
        training_id = data.get("training_id")
        
        logger.info(f"Received VERL notification: {event_type} for {training_id}")
        
        if event_type == "training_completed":
            await self._handle_training_completed(training_id, data)
        elif event_type == "training_failed":
            await self._handle_training_failed(training_id, data)
        elif event_type == "training_progress":
            await self._handle_training_progress(training_id, data)
    
    async def _handle_training_completed(self, training_id: str, data: Dict):
        """Handle training completion notification"""
        try:
            # Update model performance in database
            model_path = data.get("model_path")
            metrics = data.get("final_metrics", {})
            
            # Store new model information
            await self._store_model_performance(training_id, model_path, metrics)
            
            logger.info(f"Training {training_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to handle training completion: {e}")
    
    async def _handle_training_failed(self, training_id: str, data: Dict):
        """Handle training failure notification"""
        error = data.get("error", "Unknown error")
        logger.error(f"Training {training_id} failed: {error}")
        
        # Could implement retry logic here
    
    async def _handle_training_progress(self, training_id: str, data: Dict):
        """Handle training progress notification"""
        progress = data.get("progress", 0.0)
        epoch = data.get("epoch", 0)
        metrics = data.get("metrics", {})
        
        logger.info(f"Training {training_id} progress: {progress:.1%} (epoch {epoch})")
        
        # Could store progress in database or send to frontend
    
    async def check_training_trigger(self) -> bool:
        """Check if we should trigger new training"""
        try:
            # Check if enough new feedback has been collected
            recent_feedback_count = await self._count_recent_feedback()
            
            if recent_feedback_count < self.min_training_examples:
                logger.debug(f"Not enough feedback for training: {recent_feedback_count}/{self.min_training_examples}")
                return False
            
            # Check if enough time has passed since last training
            last_training = await self._get_last_training_time()
            time_since_training = datetime.utcnow() - last_training if last_training else timedelta(days=999)
            
            if time_since_training.total_seconds() < (self.training_interval_hours * 3600):
                logger.debug(f"Not enough time since last training: {time_since_training}")
                return False
            
            logger.info(f"Training trigger conditions met: {recent_feedback_count} examples, {time_since_training} since last training")
            return True
            
        except Exception as e:
            logger.error(f"Failed to check training trigger: {e}")
            return False
    
    async def _count_recent_feedback(self) -> int:
        """Count recent feedback entries"""
        try:
            if not self.feedback_model:
                logger.warning("Feedback model not available, returning 0 count")
                return 0
                
            cutoff_time = datetime.utcnow() - timedelta(days=7)
            return await self.feedback_model.count_recent_feedback(cutoff_time)
            
        except Exception as e:
            logger.warning(f"Failed to count recent feedback: {e} - VERL training trigger disabled")
            return 0
    
    async def _get_last_training_time(self) -> Optional[datetime]:
        """Get timestamp of last training"""
        try:
            return await self.model_performance_model.get_last_training_time()
            
        except Exception as e:
            logger.error(f"Failed to get last training time: {e}")
            return None
    
    async def trigger_training(self) -> Optional[str]:
        """Trigger new VERL training"""
        try:
            # Generate training ID
            training_id = f"verl_training_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            # Prepare training request
            training_request = {
                "training_id": training_id,
                "model_name": "microsoft/DialoGPT-medium",  # Base model
                "dataset_path": f"/shared/training_data_{training_id}.parquet",
                "num_epochs": 3,
                "learning_rate": 2e-5,
                "batch_size": 8,
                "max_response_length": 256,
                "reward_weights": {
                    "user_approval": 0.4,
                    "content_quality": 0.3,
                    "sales_performance": 0.3
                }
            }
            
            # Send training request to VERL service
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.verl_url}/train",
                    json=training_request,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"VERL training started: {training_id}")
                        
                        # Record training start in database
                        await self._record_training_start(training_id)
                        
                        return training_id
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to start VERL training: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"Failed to trigger VERL training: {e}")
            return None
    
    async def _record_training_start(self, training_id: str):
        """Record training start in database"""
        try:
            performance_record = {
                "model_name": f"verl_{training_id}",
                "model_type": "verl_trained",
                "performance_score": 0.0,  # Will be updated when training completes
                "parameters": json.dumps({"training_id": training_id, "status": "training"}),
                "created_at": datetime.utcnow().isoformat()
            }
            
            await self.model_performance_model.create(performance_record)
            
        except Exception as e:
            logger.error(f"Failed to record training start: {e}")
    
    async def _store_model_performance(self, training_id: str, model_path: str, metrics: Dict):
        """Store completed model performance"""
        try:
            # Calculate performance score from metrics
            performance_score = self._calculate_performance_score(metrics)
            
            # Update database record
            await self.model_performance_model.update_performance(training_id, performance_score, model_path, metrics)
            logger.info(f"Model {training_id} completed with score: {performance_score}")
            
        except Exception as e:
            logger.error(f"Failed to store model performance: {e}")
    
    def _calculate_performance_score(self, metrics: Dict) -> float:
        """Calculate overall performance score from training metrics"""
        try:
            # Extract relevant metrics
            reward_mean = metrics.get("reward_mean", 0.0)
            kl_divergence = metrics.get("kl_divergence", 0.0)
            policy_loss = metrics.get("policy_loss", 0.0)
            value_loss = metrics.get("value_loss", 0.0)
            
            # Combine into overall score (customize based on your needs)
            score = max(0.0, min(1.0, (reward_mean + 1) / 2))  # Normalize reward to 0-1
            
            # Penalize high KL divergence (model drift)
            if kl_divergence > 0.1:
                score *= 0.8
            
            return score
            
        except Exception as e:
            logger.error(f"Failed to calculate performance score: {e}")
            return 0.0
    
    async def get_training_status(self, training_id: str) -> Optional[Dict]:
        """Get status of training job"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.verl_url}/status/{training_id}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Failed to get training status: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Failed to get training status: {e}")
            return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of VERL service"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.verl_url}/health",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"status": "unhealthy", "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            return {"status": "unreachable", "error": str(e)}

# Global VERL integration instance
verl_integration = VERLIntegrationManager()

# Background task to check for training triggers
async def verl_training_scheduler():
    """Background scheduler for VERL training"""
    while True:
        try:
            if await verl_integration.check_training_trigger():
                await verl_integration.trigger_training()
            
            # Wait 1 hour before checking again
            await asyncio.sleep(3600)
            
        except Exception as e:
            logger.error(f"VERL scheduler error: {e}")
            await asyncio.sleep(300)  # Wait 5 minutes on error

# Function to integrate with your FastAPI startup
async def initialize_verl():
    """Initialize VERL integration on app startup"""
    if settings.enable_verl:
        await verl_integration.initialize()
        # Start scheduler
        asyncio.create_task(verl_training_scheduler())
        logger.info("VERL integration started")
    else:
        logger.info("VERL integration disabled")
        
# === VERL BACKGROUND TASKS ===

async def verl_background_monitor():
    """Background task to monitor VERL integration"""
    logger.info("🔍 Starting VERL background monitor...")

    while True:
        try:
            # Check VERL health every 5 minutes
            await asyncio.sleep(300)

            if getattr(settings, 'enable_verl', False):
                health = await verl_integration.health_check()

                if health.get("status") != "healthy":
                    logger.warning(f"VERL health check failed: {health}")
                else:
                    logger.debug("VERL health check passed")

        except Exception as e:
            logger.error(f"VERL monitor error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute on error
