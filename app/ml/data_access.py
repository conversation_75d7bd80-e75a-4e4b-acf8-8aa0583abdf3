### app/ml/data_access.py - Data Access for Machine Learning
#
import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from app.models.supabase_models import (
    get_book_model,
    get_feedback_model,
    get_publication_model,
    get_user_model,
)
from typing import TypedDict


class CategoryStat(TypedDict):
    approval_count: int
    total_count: int
    quality_scores: List[float]
    reward_signals: List[float]


class VERLDataAccessor:
    """Centralized data access for VERL training"""

    def __init__(self):
        self.book_model = None
        self.feedback_model = None
        self.publication_model = None
        self.user_model = None

    async def initialize(self):
        """Initialize data accessor"""
        self.book_model = await get_book_model()
        self.feedback_model = await get_feedback_model()
        self.publication_model = await get_publication_model()
        self.user_model = await get_user_model()

    async def get_training_data(
        self, days_back: int = 60, min_examples: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Main entry point for VERL training data collection

        Returns structured training examples with:
        - Original prompts (reconstructed)
        - Generated content
        - User feedback (approval/rejection)
        - Quality scores
        - Sales performance (if available)
        - Calculated rewards
        """
        cutoff_date = datetime.now() - timedelta(days=days_back)

        # Query books with sufficient feedback
        books_with_feedback = await self._get_books_with_feedback(cutoff_date)

        if len(books_with_feedback) < min_examples:
            raise ValueError(
                f"Insufficient training data: {len(books_with_feedback)} examples (need {min_examples})"
            )

        # Process each book into training examples
        training_examples = []
        for book_data in books_with_feedback:
            examples = await self._process_book_to_training_examples(book_data)
            training_examples.extend(examples)

        return training_examples

    async def _get_books_with_feedback(
        self, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get books that have sufficient feedback for training"""

        # Query books with performance feedback
        if self.book_model is None:
            raise ValueError("Book model not initialized")
        books = await self.book_model.get_books_with_performance_feedback(cutoff_date)

        # Convert to dict format with all related data
        books_data = []
        for book in books:
            book_data = await self._extract_book_data(book)
            books_data.append(book_data)

        return books_data

    async def _extract_book_data(self, book: Dict[str, Any]) -> Dict[str, Any]:
        """Extract comprehensive data for a single book"""
        book_id = book["id"]

        # Get performance feedback
        if self.feedback_model is None:
            raise ValueError("Feedback model not initialized")
        performance = await self.feedback_model.get_book_feedback_summary(book_id)

        # Get sales data (if available)
        if self.publication_model is None:
            raise ValueError("Publication model not initialized")
        latest_sales = await self.publication_model.get_latest_sales_data(book_id)

        # Get individual feedback metrics
        feedback_metrics = await self.feedback_model.get_book_feedback(book_id)

        return {
            "book": book,
            "performance": performance,
            "sales_data": latest_sales,
            "feedback_metrics": feedback_metrics,
            "chapters": book.get("chapters", []),
            "generation_config": book.get("generation_config", {}),
        }

    async def _process_book_to_training_examples(
        self, book_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Convert book data to training examples"""
        book = book_data["book"]
        performance = book_data["performance"]
        sales_data = book_data["sales_data"]

        training_examples = []

        # Create training example for each chapter
        for chapter in book_data["chapters"]:
            # Reconstruct the original prompt
            prompt = self._reconstruct_generation_prompt(book, chapter)

            # Calculate comprehensive reward
            reward = self._calculate_training_reward(performance, sales_data, chapter)

            # Create training example
            example = {
                "book_id": book["id"],
                "chapter_id": chapter["id"],
                "prompt": prompt,
                "generated_content": chapter["content"],
                "reward": reward,
                "metadata": {
                    "category": book["category"],
                    "word_count": chapter["word_count"],
                    "user_approval": performance.get("user_approval", False),
                    "quality_score": performance.get("quality_score", 50),
                    "sales_units": (
                        sales_data.get("sales_units", 0) if sales_data else 0
                    ),
                    "average_rating": (
                        sales_data.get("average_rating", 0) if sales_data else 0
                    ),
                    "generation_config": book_data["generation_config"],
                    "created_at": book["created_at"],
                },
            }

            training_examples.append(example)

        return training_examples

    def _reconstruct_generation_prompt(
        self, book: Dict[str, Any], chapter: Dict[str, Any]
    ) -> str:
        """Reconstruct the original prompt used for generation"""

        # This reconstructs the likely prompt based on our manuscript generator
        prompt = f"""Write a compelling chapter for an e-book with the following specifications:

Title: "{book['title']}"
Category: {book['category']}
Chapter: {chapter['title']}
Target Audience: general adults
Writing Style: professional

Requirements:
- Provide practical, actionable content that delivers real value to readers
- Use engaging, professional writing style
- Include specific examples, tips, and insights
- Target approximately {chapter['word_count']} words

Write the complete chapter content:
"""
        return prompt.strip()

    def _calculate_training_reward(
        self,
        performance: Dict[str, Any],
        sales_data: Optional[Dict[str, Any]],
        chapter: Dict[str, Any],
    ) -> float:
        """Calculate reward signal for training"""

        if performance and performance.get("reward_signal") is not None:
            # Use pre-calculated reward if available
            return float(performance["reward_signal"])

        # Calculate reward from available data
        reward = 0.0

        # User approval (primary signal)
        if performance:
            reward += 1.0 if performance.get("user_approval") else -0.5

            # Quality score component
            quality_norm = (
                performance.get("quality_score", 50) - 50
            ) / 50  # Normalize to [-1, 1]
            reward += quality_norm * 0.5

        # Sales performance (if available)
        if sales_data:
            # Normalize sales units (assume 50 units = good performance)
            sales_norm = min(sales_data.get("sales_units", 0) / 50, 1.0)
            reward += sales_norm * 0.3

            # Review rating component
            if sales_data.get("average_rating", 0) > 0:
                rating_norm = (
                    sales_data["average_rating"] - 3.0
                ) / 2.0  # 1-5 scale to [-1, 1]
                reward += rating_norm * 0.2

        # Length quality (penalize too short or too long content)
        if chapter.get("word_count"):
            if 800 <= chapter["word_count"] <= 1500:  # Ideal range
                reward += 0.1
            elif chapter["word_count"] < 500:  # Too short
                reward -= 0.2
            elif chapter["word_count"] > 2500:  # Too long
                reward -= 0.1

        # Squash to [-1, 1] range
        import math

        return math.tanh(reward)

    async def get_recent_feedback_stats(self, days: int = 30) -> Dict[str, Any]:
        """Get recent feedback statistics for monitoring"""
        cutoff_date = datetime.now() - timedelta(days=days)

        # Query recent feedback
        if self.feedback_model is None:
            raise ValueError("Feedback model not initialized")
        recent_performance = await self.feedback_model.get_recent_performance(
            cutoff_date
        )

        if not recent_performance:
            return {"error": "No recent feedback data"}

        # Calculate statistics
        total_books = len(recent_performance)
        approved_books = sum(1 for p in recent_performance if p.get("user_approval"))
        avg_quality = (
            sum(p.get("quality_score", 0) for p in recent_performance) / total_books
        )
        avg_reward = (
            sum(p.get("reward_signal", 0) or 0 for p in recent_performance)
            / total_books
        )

        return {
            "total_books": total_books,
            "approval_rate": approved_books / total_books,
            "average_quality": avg_quality,
            "average_reward": avg_reward,
            "data_points": total_books,
        }

    async def get_category_performance(
        self, days: int = 60
    ) -> Dict[str, Dict[str, float]]:
        """Get performance breakdown by category"""
        cutoff_date = datetime.now() - timedelta(days=days)

        # Query performance by category
        category_stats: Dict[str, CategoryStat] = {}  # Add type hint for category_stats
        if self.book_model is None:
            raise ValueError("Book model not initialized")
        books_with_performance: List[Dict] = (
            await self.book_model.get_books_with_performance(  # Add type hint for books_with_performance
                cutoff_date
            )
        )

        for book in books_with_performance:
            category = book.get("category")
            performance = book.get("performance")

            if (
                performance is not None and category is not None
            ):  # Add check for category is not None
                if category not in category_stats:
                    category_stats[category] = {
                        "approval_count": 0,
                        "total_count": 0,
                        "quality_scores": [],
                        "reward_signals": [],
                    }

                stats = category_stats[category]
                stats["total_count"] += 1

                if performance.get("user_approval"):
                    stats["approval_count"] += 1

                stats["quality_scores"].append(performance.get("quality_score", 0))
                if performance.get("reward_signal") is not None:
                    stats["reward_signals"].append(performance["reward_signal"])

        # Calculate final statistics
        result = {}
        for category, stats in category_stats.items():
            total_count = stats["total_count"]
            approval_count = stats["approval_count"]
            quality_scores = stats["quality_scores"]
            reward_signals = stats["reward_signals"]

            if total_count >= 3:  # Minimum sample size
                result[category] = {
                    "approval_rate": (
                        approval_count / total_count if total_count > 0 else 0
                    ),  # Add division by zero check
                    "average_quality": (
                        sum(quality_scores) / len(quality_scores)
                        if quality_scores
                        else 0
                    ),  # Add division by zero check
                    "average_reward": (
                        sum(reward_signals) / len(reward_signals)
                        if reward_signals
                        else 0
                    ),  # Add division by zero check
                    "sample_size": total_count,
                }
        return result  # Add return statement
