# app/ml/verl_service.py
"""
VERL Training Service - Dedicated container for reinforcement learning training
Handles all VERL training operations and communicates with main app via Redis
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

import redis
import torch
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel
import pandas as pd

from app.models.supabase_models import get_book_model, get_feedback_model, get_model_performance_model

# VERL imports
try:
    import ray
    from verl.trainer.ppo.ray_trainer import RayPPOTrainer
    from verl.utils.reward_model import RewardModel
    from verl.single_controller.base import DefaultWorker
    from verl.trainer.main_ppo import hydra_main as verl_main
    import hydra
    from omegaconf import DictConfig, OmegaConf
except ImportError as e:
    logging.error(f"VERL imports failed: {e}")
    logging.error("Make sure VERL is properly installed in this container")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app for VERL service
app = FastAPI(title="VERL Training Service", version="1.0.0")

# Redis connection for communication with main app
redis_client = redis.from_url(os.getenv("REDIS_URL", "redis://localhost:6379"))

class TrainingRequest(BaseModel):
    """Training request from main application"""
    training_id: str
    model_name: str = "microsoft/DialoGPT-medium"
    dataset_path: str
    num_epochs: int = 3
    learning_rate: float = 2e-5
    batch_size: int = 8
    max_response_length: int = 256
    reward_weights: Dict[str, float] = {
        "user_approval": 0.4,
        "content_quality": 0.3,
        "sales_performance": 0.3
    }

class TrainingStatus(BaseModel):
    """Training status response"""
    training_id: str
    status: str  # "queued", "training", "completed", "failed"
    progress: float = 0.0
    metrics: Optional[Dict] = None
    error_message: Optional[str] = None
    model_path: Optional[str] = None

class VERLTrainingManager:
    """Manages VERL training operations"""
    
    def __init__(self):
        self.training_jobs: Dict[str, TrainingStatus] = {}
        self.models_dir = Path("/app/models")
        self.checkpoints_dir = Path("/app/checkpoints")
        self.training_data_dir = Path("/app/training_data")
        self.book_model = None
        self.feedback_model = None
        self.model_performance_model = None
        
        # Create directories
        for dir_path in [self.models_dir, self.checkpoints_dir, self.training_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize Ray for distributed training
        self._init_ray()
    
    async def initialize(self):
        """Initialize async components"""
        self.book_model = await get_book_model()
        self.feedback_model = await get_feedback_model()
        self.model_performance_model = await get_model_performance_model()
    
    def _init_ray(self):
        """Initialize Ray cluster"""
        try:
            if not ray.is_initialized():
                ray.init(
                    address="auto",  # Connect to existing cluster or start new
                    runtime_env={
                        "pip": ["transformers", "datasets", "torch", "accelerate"]
                    }
                )
            logger.info("Ray initialized successfully")
        except Exception as e:
            logger.warning(f"Ray initialization failed: {e}")
            # Fall back to local Ray
            ray.init(ignore_reinit_error=True)
    
    async def collect_training_data(self, training_id: str) -> pd.DataFrame:
        """Collect training data from database"""
        try:
            # Query for feedback data
            feedback_df = await self.feedback_model.get_feedback_for_training(30)
            
            # Query for sales data
            sales_df = await self.feedback_model.get_sales_for_training(30)
            
            # Merge and prepare training data
            training_data = self._prepare_training_data(feedback_df, sales_df)
            
            # Save to training data directory
            data_path = self.training_data_dir / f"training_data_{training_id}.parquet"
            training_data.to_parquet(data_path)
            
            logger.info(f"Collected {len(training_data)} training examples for {training_id}")
            return training_data
                
        except Exception as e:
            logger.error(f"Failed to collect training data: {e}")
            raise
    
    def _prepare_training_data(self, feedback_df: pd.DataFrame, sales_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare training data with rewards"""
        # Calculate reward scores
        training_data = []
        
        for _, row in feedback_df.iterrows():
            book_id = row['book_id']
            
            # Base reward from user approval
            user_reward = 1.0 if row['approved'] else -0.5
            
            # Quality reward (based on approval time - faster approval = higher quality)
            quality_reward = max(0, 1.0 - (row['approval_time'] / 300))  # Normalize to 5 minutes
            
            # Sales reward
            sales_row = sales_df[sales_df['book_id'] == book_id]
            if not sales_row.empty:
                sales_units = sales_row.iloc[0]['sales_units']
                revenue = sales_row.iloc[0]['revenue']
                rating = sales_row.iloc[0]['average_rating'] or 3.0
                
                # Normalize sales metrics
                sales_reward = min(1.0, sales_units / 100) * (rating / 5.0)
            else:
                sales_reward = 0.0
            
            # Combined reward
            total_reward = (
                0.4 * user_reward + 
                0.3 * quality_reward + 
                0.3 * sales_reward
            )
            
            # Create training example
            training_example = {
                'book_id': book_id,
                'prompt': f"Write a {row['category']} book about: {row['title'][:100]}",
                'response': row['content'][:1000],  # Truncate for training
                'reward': total_reward,
                'user_approved': row['approved'],
                'generation_params': row['generation_params'] or '{}'
            }
            
            training_data.append(training_example)
        
        return pd.DataFrame(training_data)
    
    def create_verl_config(self, request: TrainingRequest) -> DictConfig:
        """Create VERL training configuration"""
        config_dict = {
            "data": {
                "train_files": str(self.training_data_dir / f"training_data_{request.training_id}.parquet"),
                "val_files": str(self.training_data_dir / f"training_data_{request.training_id}.parquet"),
                "prompt_key": "prompt",
                "response_key": "response",
                "reward_key": "reward"
            },
            "actor_rollout_ref": {
                "model": {
                    "path": request.model_name,
                    "trust_remote_code": True,
                    "lora_rank": 32,
                    "lora_alpha": 16,
                    "target_modules": "all-linear",
                    "use_shm": True
                },
                "rollout": {
                    "log_prob_micro_batch_size": request.batch_size,
                    "micro_batch_size": request.batch_size,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_new_tokens": request.max_response_length,
                    "load_format": "safetensors",
                    "layered_summon": True
                }
            },
            "critic": {
                "model": {
                    "path": request.model_name,
                    "trust_remote_code": True,
                    "lora_rank": 32,
                    "lora_alpha": 16,
                    "target_modules": "all-linear"
                },
                "optim": {
                    "lr": request.learning_rate * 10,  # Higher LR for LoRA
                    "beta1": 0.9,
                    "beta2": 0.95,
                    "eps": 1e-5,
                    "weight_decay": 0.01
                }
            },
            "algorithm": {
                "kl_ctrl": {
                    "kl_coeff": 0.001,
                    "adaptive_kl": True,
                    "target_kl": 6.0
                },
                "rollout_batch_size": request.batch_size * 4,
                "n_ppo_epochs": 4,
                "ppo_mini_batch_size": request.batch_size,
                "cliprange": 0.2,
                "cliprange_value": 0.2,
                "gamma": 0.99,
                "lam": 0.95,
                "entropy_bonus_coeff": 0.01,
                "value_loss_coeff": 0.5,
                "max_grad_norm": 1.0
            },
            "trainer": {
                "default_hdfs_dir": None,
                "project_name": "publish_ai",
                "experiment_name": f"ebook_training_{request.training_id}",
                "total_epochs": request.num_epochs,
                "save_freq": 1,
                "test_freq": 1,
                "checkpoint_dir": str(self.checkpoints_dir),
                "enable_wandb": False
            },
            "strategy": "fsdp",
            "rollout": {
                "name": "vllm"
            }
        }
        
        return OmegaConf.create(config_dict)
    
    async def start_training(self, request: TrainingRequest) -> TrainingStatus:
        """Start VERL training job"""
        try:
            # Initialize training status
            status = TrainingStatus(
                training_id=request.training_id,
                status="queued",
                progress=0.0
            )
            self.training_jobs[request.training_id] = status
            
            # Collect training data
            logger.info(f"Collecting training data for {request.training_id}")
            training_data = await self.collect_training_data(request.training_id)
            
            if len(training_data) < 10:
                raise ValueError(f"Insufficient training data: {len(training_data)} examples")
            
            # Create VERL configuration
            config = self.create_verl_config(request)
            
            # Start training in background
            asyncio.create_task(self._run_training(request, config))
            
            status.status = "training"
            status.progress = 0.1
            
            # Notify main app
            await self._notify_main_app("training_started", {
                "training_id": request.training_id,
                "status": "started"
            })
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to start training {request.training_id}: {e}")
            status.status = "failed"
            status.error_message = str(e)
            return status
    
    async def _run_training(self, request: TrainingRequest, config: DictConfig):
        """Run VERL training (background task)"""
        training_id = request.training_id
        
        try:
            logger.info(f"Starting VERL training for {training_id}")
            
            # Update status
            self.training_jobs[training_id].status = "training"
            self.training_jobs[training_id].progress = 0.2
            
            # Run VERL training
            with hydra.initialize(config_path=None):
                # Create temporary config file
                config_path = self.checkpoints_dir / f"config_{training_id}.yaml"
                OmegaConf.save(config, config_path)
                
                # Start training
                trainer = RayPPOTrainer(config)
                
                # Training loop with progress updates
                for epoch in range(request.num_epochs):
                    logger.info(f"Training epoch {epoch + 1}/{request.num_epochs}")
                    
                    # Run one epoch
                    metrics = trainer.train_epoch()
                    
                    # Update progress
                    progress = 0.2 + (0.7 * (epoch + 1) / request.num_epochs)
                    self.training_jobs[training_id].progress = progress
                    self.training_jobs[training_id].metrics = metrics
                    
                    # Notify main app of progress
                    await self._notify_main_app("training_progress", {
                        "training_id": training_id,
                        "epoch": epoch + 1,
                        "total_epochs": request.num_epochs,
                        "progress": progress,
                        "metrics": metrics
                    })
                
                # Save final model
                model_path = self.models_dir / f"trained_model_{training_id}"
                trainer.save_model(str(model_path))
                
                # Complete training
                self.training_jobs[training_id].status = "completed"
                self.training_jobs[training_id].progress = 1.0
                self.training_jobs[training_id].model_path = str(model_path)
                
                logger.info(f"Training completed for {training_id}")
                
                # Notify main app
                await self._notify_main_app("training_completed", {
                    "training_id": training_id,
                    "model_path": str(model_path),
                    "final_metrics": metrics
                })
                
        except Exception as e:
            logger.error(f"Training failed for {training_id}: {e}")
            self.training_jobs[training_id].status = "failed"
            self.training_jobs[training_id].error_message = str(e)
            
            # Notify main app of failure
            await self._notify_main_app("training_failed", {
                "training_id": training_id,
                "error": str(e)
            })
    
    async def _notify_main_app(self, event_type: str, data: Dict):
        """Send notification to main app via Redis"""
        try:
            message = {
                "event_type": event_type,
                "timestamp": datetime.utcnow().isoformat(),
                "data": data
            }
            
            redis_client.publish("verl_notifications", json.dumps(message))
            logger.debug(f"Sent notification: {event_type}")
            
        except Exception as e:
            logger.error(f"Failed to notify main app: {e}")

# Global training manager
training_manager = VERLTrainingManager()

@app.post("/train", response_model=TrainingStatus)
async def start_training(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Start a new VERL training job"""
    try:
        status = await training_manager.start_training(request)
        return status
    except Exception as e:
        logger.error(f"Training request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{training_id}", response_model=TrainingStatus)
async def get_training_status(training_id: str):
    """Get training job status"""
    if training_id not in training_manager.training_jobs:
        raise HTTPException(status_code=404, detail="Training job not found")
    
    return training_manager.training_jobs[training_id]

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "ray_initialized": ray.is_initialized(),
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
    }

@app.delete("/training/{training_id}")
async def cancel_training(training_id: str):
    """Cancel a training job"""
    if training_id not in training_manager.training_jobs:
        raise HTTPException(status_code=404, detail="Training job not found")
    
    # Update status
    training_manager.training_jobs[training_id].status = "cancelled"
    
    return {"message": f"Training {training_id} cancelled"}

if __name__ == "__main__":
    import uvicorn
    
    # Start the VERL service
    logger.info("Starting VERL Training Service...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
