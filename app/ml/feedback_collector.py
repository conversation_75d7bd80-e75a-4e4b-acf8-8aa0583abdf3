import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from app.models.supabase_models import get_book_model, get_feedback_model

logger = logging.getLogger(__name__)

class MetricType(Enum):
    USER_APPROVAL = "user_approval"
    SALES_PERFORMANCE = "sales_performance"
    QUALITY_SCORE = "quality_score"
    READER_ENGAGEMENT = "reader_engagement"
    REVIEW_RATING = "review_rating"
    GENERATION_SUCCESS = "generation_success"
    TREND_ACCURACY = "trend_accuracy"

@dataclass
class PerformanceMetric:
    """Individual performance metric data point"""
    book_id: str
    metric_type: MetricType
    value: float
    timestamp: datetime
    context: Dict[str, Any]  # Additional context (category, theme, etc.)

@dataclass
class ModelFeedback:
    """Comprehensive feedback for a single generation attempt"""
    book_id: str
    generation_config: Dict[str, Any]
    user_approval: bool
    quality_score: float
    sales_performance: Optional[float] = None
    review_rating: Optional[float] = None
    engagement_score: Optional[float] = None
    time_to_approval: Optional[float] = None  # Time user took to approve/reject
    
    def to_reward_signal(self) -> float:
        """Convert feedback to reward signal for RL"""
        reward = 0.0
        
        # User approval (primary signal)
        reward += 1.0 if self.user_approval else -0.5
        
        # Quality score (normalized 0-1)
        reward += (self.quality_score - 50) / 50  # Scale 0-100 to -1 to 1
        
        # Sales performance (if available)
        if self.sales_performance is not None:
            reward += min(self.sales_performance / 100, 1.0)  # Cap at 1.0
        
        # Review rating (if available)
        if self.review_rating is not None:
            reward += (self.review_rating - 2.5) / 2.5  # Scale 1-5 to -0.6 to 1.0
        
        # Quick approval bonus (user didn't hesitate)
        if self.time_to_approval and self.time_to_approval < 300:  # 5 minutes
            reward += 0.2
        
        return np.tanh(reward)  # Squash to [-1, 1] range

class FeedbackCollector:
    """Collects and processes feedback metrics for model improvement"""
    
    def __init__(self):
        self.book_model = None
        self.feedback_model = None
        self.metrics_buffer: List[PerformanceMetric] = []
        self.feedback_buffer: List[ModelFeedback] = []
    
    async def initialize(self):
        """Initialize async components"""
        self.book_model = await get_book_model()
        self.feedback_model = await get_feedback_model()
    
    async def collect_user_approval_feedback(
        self,
        book_id: str,
        approved: bool,
        approval_time: float,
        rejection_reason: Optional[str] = None
    ):
        """Collect user approval/rejection feedback"""
        
        book = await self.book_model.get_book(book_id)
        if not book:
            return
        
        # Store approval metric
        metric = PerformanceMetric(
            book_id=book_id,
            metric_type=MetricType.USER_APPROVAL,
            value=1.0 if approved else 0.0,
            timestamp=datetime.now(),
            context={
                "category": book['category'],
                "generation_config": book['generation_config'],
                "approval_time": approval_time,
                "rejection_reason": rejection_reason,
                "word_count": book['word_count'],
                "ai_provider": book['generation_config'].get("provider") if book.get('generation_config') else None
            }
        )
        
        await self._store_metric(metric)
        
        # Create model feedback entry
        feedback = ModelFeedback(
            book_id=book_id,
            generation_config=book.get('generation_config', {}),
            user_approval=approved,
            quality_score=book.get('quality_score', 50),
            time_to_approval=approval_time
        )
        
        await self._store_feedback(feedback)
        logger.info(f"Collected approval feedback for book {book_id}: {approved}")
    
    async def collect_sales_performance_feedback(self, book_id: str):
        """Collect sales performance data as feedback"""
        
        # Get sales data for the book
        sales_data = await self.feedback_model.get_sales_data(book_id)
        
        if not sales_data:
            return
        
        # Calculate performance score
        performance_score = self._calculate_sales_performance_score(sales_data)
        
        metric = PerformanceMetric(
            book_id=book_id,
            metric_type=MetricType.SALES_PERFORMANCE,
            value=performance_score,
            timestamp=datetime.now(),
            context={
                "sales_units": sales_data['sales_units'],
                "revenue": sales_data['revenue'],
                "royalties": sales_data.get('royalties', 0),
                "reviews_count": sales_data['reviews_count'],
                "average_rating": sales_data['average_rating']
            }
        )
        
        await self._store_metric(metric)
        
        # Update existing feedback with sales data
        await self._update_feedback_with_sales(book_id, performance_score, sales_data['average_rating'])
    
    async def collect_quality_feedback(
        self,
        book_id: str,
        quality_metrics: Dict[str, float]
    ):
        """Collect quality assessment feedback"""
        
        # Aggregate quality metrics
        overall_quality = np.mean(list(quality_metrics.values()))
        
        metric = PerformanceMetric(
            book_id=book_id,
            metric_type=MetricType.QUALITY_SCORE,
            value=overall_quality,
            timestamp=datetime.now(),
            context=quality_metrics
        )
        
        await self._store_metric(metric)
    
    async def collect_trend_accuracy_feedback(
        self,
        trend_analysis_id: str,
        generated_books: List[str],
        success_metrics: Dict[str, float]
    ):
        """Collect feedback on trend analysis accuracy"""
        
        # Calculate how well the trend analysis predicted successful books
        accuracy_score = success_metrics.get("prediction_accuracy", 0.0)
        
        for book_id in generated_books:
            metric = PerformanceMetric(
                book_id=book_id,
                metric_type=MetricType.TREND_ACCURACY,
                value=accuracy_score,
                timestamp=datetime.now(),
                context={
                    "trend_analysis_id": trend_analysis_id,
                    "prediction_metrics": success_metrics
                }
            )
            
            await self._store_metric(metric)
    
    def _calculate_sales_performance_score(self, sales_data: Dict[str, Any]) -> float:
        """Calculate normalized sales performance score (0-100)"""
        
        # Weighted combination of sales metrics
        score = 0.0
        
        # Sales units (normalized by typical performance)
        if sales_data.get('sales_units', 0) > 0:
            score += min(sales_data['sales_units'] / 10, 50)  # Cap at 50 points
        
        # Revenue performance
        if sales_data.get('revenue', 0) > 0:
            score += min(sales_data['revenue'] / 100, 30)  # Cap at 30 points
        
        # Review rating
        if sales_data.get('average_rating', 0) > 0:
            score += (sales_data['average_rating'] - 1) * 5  # 1-5 star to 0-20 points
        
        return min(score, 100)
    
    async def _store_metric(self, metric: PerformanceMetric):
        """Store metric in database"""
        
        await self.feedback_model.record_feedback_metric(asdict(metric))
    
    async def _store_feedback(self, feedback: ModelFeedback):
        """Store comprehensive feedback"""
        
        await self.feedback_model.record_model_performance(asdict(feedback))
    
    async def _update_feedback_with_sales(
        self,
        book_id: str,
        sales_performance: float,
        review_rating: float
    ):
        """Update existing feedback with sales data"""
        
        await self.feedback_model.update_feedback_with_sales(book_id, sales_performance, review_rating)
    
    async def get_performance_trends(
        self,
        metric_type: str,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get performance trends over time"""
        
        return await self.feedback_model.get_performance_trends(metric_type, days)
    
    async def collect_training_data(self, days_back: int = 90) -> List[Dict[str, Any]]:
        """Collect training data for VERL model training"""
        
        start_date = datetime.now() - timedelta(days=days_back)
        
        # Get all model performance records with complete data
        performance_records = await self.feedback_model.get_training_data(start_date)
        
        training_examples = []
        
        for record in performance_records:
            # Get the associated book for content
            book = await self.book_model.get_book(record['book_id'])
            if not book:
                continue
            
            # Create training example
            training_example = {
                'book_id': record['book_id'],
                'prompt': self._reconstruct_prompt(book, record['generation_config']),
                'output': book['manuscript_path'],  # Will need to load actual content
                'reward': record['reward_signal'],
                'context': {
                    'category': book['category'],
                    'word_count': book['word_count'],
                    'user_approval': record['user_approval'],
                    'quality_score': record['quality_score'],
                    'sales_performance': record['sales_performance'],
                    'review_rating': record['review_rating'],
                    'generation_config': record['generation_config']
                },
                'timestamp': record['created_at']
            }
            
            training_examples.append(training_example)
        
        logger.info(f"Collected {len(training_examples)} training examples from last {days_back} days")
        return training_examples
    
    def _reconstruct_prompt(self, book: Dict[str, Any], generation_config: Dict[str, Any]) -> str:
        """Reconstruct the prompt used for generation"""
        
        # Basic prompt reconstruction - in practice, you'd want to store the actual prompts
        category = book['category']
        style = generation_config.get('style', 'professional')
        target_audience = generation_config.get('target_audience', 'general adults')
        
        prompt = f"""
        Write a compelling e-book chapter for the category: {category}
        Topic: {book['title']}
        Writing Style: {style}
        Target Audience: {target_audience}
        
        The content should be practical, engaging, and valuable to readers.
        """
        
        return prompt.strip()
