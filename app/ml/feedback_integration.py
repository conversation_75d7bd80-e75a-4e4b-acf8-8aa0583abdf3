### app/ml/feedback_integration.py - Live Feedback Integration

from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import asyncio

from app.models.supabase_models import get_book_model, get_feedback_model

class LiveFeedbackCollector:
    """Captures feedback in real-time as users interact with the system"""
    
    def __init__(self):
        self.book_model = None
        self.feedback_model = None
    
    async def initialize(self):
        """Initialize async components"""
        self.book_model = await get_book_model()
        self.feedback_model = await get_feedback_model()

    async def capture_approval_feedback(
        self,
        book_id: str,
        approved: bool,
        approval_time_seconds: float,
        rejection_reason: Optional[str] = None
    ):
        """Capture user approval/rejection immediately"""
        
        # Get the book to fill in generation config
        book = await self.book_model.get_book(book_id)
        generation_config = book.get('generation_config', {}) if book else {}
        
        # Store the feedback
        feedback_data = {
            'book_id': book_id,
            'user_approval': approved,
            'quality_score': 85 if approved else 35,  # Initial estimate
            'reward_signal': 1.0 if approved else -0.5,
            'generation_config': generation_config,
            'created_at': datetime.now().isoformat()
        }
        
        await self.feedback_model.record_model_performance(feedback_data)
        
        # Also store as individual metric
        metric_data = {
            'book_id': book_id,
            'metric_type': "user_approval",
            'value': 1.0 if approved else 0.0,
            'timestamp': datetime.now().isoformat(),
            'context': {
                'approval_time': approval_time_seconds,
                'rejection_reason': rejection_reason
            }
        }
        
        await self.feedback_model.record_feedback_metric(metric_data)
        
        # Trigger training if we have enough new data
        await self._check_training_trigger()
    
    async def capture_sales_feedback(self, book_id: str, sales_data: Dict[str, Any]):
        """Capture sales performance data"""
        
        # Update existing performance record with sales data
        performance = await self.feedback_model.get_model_performance(book_id)
        
        if performance:
            # Calculate sales-based reward component
            sales_reward = min(sales_data.get('sales_units', 0) / 50, 1.0)
            rating_reward = (sales_data.get('average_rating', 3.0) - 3.0) / 2.0
            
            # Update performance record
            performance['sales_performance'] = sales_reward * 100
            performance['review_rating'] = sales_data.get('average_rating', 0)
            
            # Recalculate total reward
            total_reward = 0.0
            total_reward += 1.0 if performance['user_approval'] else -0.5
            total_reward += (performance['quality_score'] - 50) / 50
            total_reward += sales_reward * 0.5
            total_reward += rating_reward * 0.3
            
            performance['reward_signal'] = total_reward
            
            await self.feedback_model.update_model_performance(book_id, performance)
    
    async def _check_training_trigger(self):
        """Check if we should trigger new training"""
        
        # Count new feedback since last training
        recent_feedback_count = await self.feedback_model.count_recent_feedback(1)
        
        # Trigger training if we have enough new data
        if recent_feedback_count >= 10:  # Threshold for retraining
            try:
                from app.tasks.verl_training import train_verl_model_task
                train_verl_model_task.delay(days_back=30)
            except ImportError:
                # Task system not yet implemented
                pass
