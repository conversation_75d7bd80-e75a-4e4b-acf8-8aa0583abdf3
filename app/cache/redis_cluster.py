# app/cache/redis_cluster.py - Redis Clustering for High Availability

import asyncio
import json
import time
import hashlib
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import redis
import redis.asyncio as aioredis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception

logger = get_logger(__name__)

class RedisNodeRole(Enum):
    """Redis node roles"""
    MASTER = "master"
    SLAVE = "slave"
    SENTINEL = "sentinel"

@dataclass
class RedisNode:
    """Redis node configuration"""
    host: str
    port: int
    role: RedisNodeRole
    password: Optional[str] = None
    db: int = 0
    max_connections: int = 50
    health_check_interval: int = 30

@dataclass
class RedisClusterConfig:
    """Redis cluster configuration"""
    nodes: List[RedisNode]
    master_name: str = "mymaster"
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = None
    connection_pool_kwargs: Dict[str, Any] = None
    retry_on_timeout: bool = True
    health_check_interval: int = 30
    failover_timeout: int = 10
    max_retries: int = 3
    backoff_factor: float = 0.5

class RedisHealthChecker:
    """Redis cluster health monitoring"""
    
    def __init__(self, cluster_config: RedisClusterConfig):
        self.config = cluster_config
        self.logger = get_logger(__name__)
        self._health_cache: Dict[str, Dict[str, Any]] = {}
        self._last_health_check = 0.0
    
    async def check_node_health(self, node: RedisNode) -> Dict[str, Any]:
        """Check health of individual Redis node"""
        node_key = f"{node.host}:{node.port}"
        
        try:
            # Create connection to node
            if node.role == RedisNodeRole.SENTINEL:
                client = aioredis.Sentinel(
                    [(node.host, node.port)],
                    socket_timeout=self.config.socket_timeout,
                    password=node.password
                )
                # Test sentinel
                masters = await client.sentinel.master
                health_info = {
                    'status': 'healthy',
                    'role': 'sentinel',
                    'masters': len(masters) if masters else 0,
                    'response_time_ms': 0
                }
            else:
                # Regular Redis node
                start_time = time.time()
                client = aioredis.Redis(
                    host=node.host,
                    port=node.port,
                    db=node.db,
                    password=node.password,
                    socket_timeout=self.config.socket_timeout,
                    socket_connect_timeout=self.config.socket_connect_timeout,
                    socket_keepalive=self.config.socket_keepalive,
                    socket_keepalive_options=self.config.socket_keepalive_options or {},
                    retry_on_timeout=self.config.retry_on_timeout,
                    max_connections=node.max_connections
                )
                
                # Test basic operations
                await client.ping()
                info = await client.info()
                response_time = (time.time() - start_time) * 1000
                
                health_info = {
                    'status': 'healthy',
                    'role': info.get('role', 'unknown'),
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory', 0),
                    'used_memory_human': info.get('used_memory_human', '0B'),
                    'uptime_in_seconds': info.get('uptime_in_seconds', 0),
                    'response_time_ms': round(response_time, 2),
                    'redis_version': info.get('redis_version', 'unknown'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0)
                }
                
                # Calculate hit ratio
                hits = health_info['keyspace_hits']
                misses = health_info['keyspace_misses']
                if hits + misses > 0:
                    health_info['hit_ratio'] = round(hits / (hits + misses) * 100, 2)
                else:
                    health_info['hit_ratio'] = 0
                
                await client.close()
            
            health_info.update({
                'host': node.host,
                'port': node.port,
                'timestamp': time.time(),
                'error': None
            })
            
            return health_info
            
        except Exception as e:
            error_info = {
                'status': 'unhealthy',
                'host': node.host,
                'port': node.port,
                'role': node.role.value,
                'error': str(e),
                'timestamp': time.time(),
                'response_time_ms': None
            }
            
            self.logger.warning(f"Redis node health check failed for {node_key}: {e}")
            return error_info
    
    async def check_cluster_health(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Check health of entire Redis cluster"""
        now = time.time()
        
        # Use cached results if recent enough
        if not force_refresh and (now - self._last_health_check) < self.config.health_check_interval:
            return self._health_cache.get('cluster', {})
        
        try:
            # Check all nodes concurrently
            health_tasks = [
                self.check_node_health(node) for node in self.config.nodes
            ]
            
            node_healths = await asyncio.gather(*health_tasks, return_exceptions=True)
            
            # Process results
            healthy_nodes = []
            unhealthy_nodes = []
            masters = []
            slaves = []
            sentinels = []
            
            for i, health in enumerate(node_healths):
                if isinstance(health, Exception):
                    unhealthy_nodes.append({
                        'host': self.config.nodes[i].host,
                        'port': self.config.nodes[i].port,
                        'error': str(health)
                    })
                    continue
                
                if health['status'] == 'healthy':
                    healthy_nodes.append(health)
                    
                    # Categorize by role
                    role = health.get('role', 'unknown')
                    if role == 'master':
                        masters.append(health)
                    elif role == 'slave':
                        slaves.append(health)
                    elif role == 'sentinel':
                        sentinels.append(health)
                else:
                    unhealthy_nodes.append(health)
            
            # Determine overall cluster status
            total_nodes = len(self.config.nodes)
            healthy_count = len(healthy_nodes)
            
            if healthy_count == total_nodes:
                cluster_status = 'healthy'
            elif healthy_count >= total_nodes / 2:
                cluster_status = 'degraded'
            else:
                cluster_status = 'critical'
            
            # Calculate cluster metrics
            avg_response_time = 0
            if healthy_nodes:
                response_times = [h['response_time_ms'] for h in healthy_nodes if h['response_time_ms'] is not None]
                if response_times:
                    avg_response_time = sum(response_times) / len(response_times)
            
            cluster_health = {
                'status': cluster_status,
                'total_nodes': total_nodes,
                'healthy_nodes': healthy_count,
                'unhealthy_nodes': len(unhealthy_nodes),
                'masters': len(masters),
                'slaves': len(slaves),
                'sentinels': len(sentinels),
                'avg_response_time_ms': round(avg_response_time, 2),
                'nodes': {
                    'healthy': healthy_nodes,
                    'unhealthy': unhealthy_nodes
                },
                'timestamp': now
            }
            
            # Cache results
            self._health_cache['cluster'] = cluster_health
            self._last_health_check = now
            
            return cluster_health
            
        except Exception as e:
            self.logger.error(f"Cluster health check failed: {e}")
            capture_exception(e)
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': now
            }

class RedisClusterClient:
    """High-availability Redis cluster client"""
    
    def __init__(self, config: RedisClusterConfig):
        self.config = config
        self.health_checker = RedisHealthChecker(config)
        self.logger = get_logger(__name__)
        
        # Connection management
        self._master_client: Optional[aioredis.Redis] = None
        self._slave_clients: List[aioredis.Redis] = []
        self._sentinel_client: Optional[aioredis.Sentinel] = None
        
        # Failover tracking
        self._master_node: Optional[RedisNode] = None
        self._last_failover_check = 0.0
        self._connection_lock = asyncio.Lock()
        
        # Performance tracking
        self._operation_stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'failovers': 0,
            'avg_response_time': 0.0
        }
    
    async def initialize(self) -> None:
        """Initialize Redis cluster connections"""
        async with self._connection_lock:
            try:
                await self._establish_connections()
                self.logger.info("Redis cluster initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Redis cluster: {e}")
                capture_exception(e)
                raise
    
    async def _establish_connections(self) -> None:
        """Establish connections to Redis cluster"""
        # Find current master and slaves
        masters = [node for node in self.config.nodes if node.role == RedisNodeRole.MASTER]
        slaves = [node for node in self.config.nodes if node.role == RedisNodeRole.SLAVE]
        sentinels = [node for node in self.config.nodes if node.role == RedisNodeRole.SENTINEL]
        
        # Connect to sentinels if available
        if sentinels:
            sentinel_hosts = [(s.host, s.port) for s in sentinels]
            self._sentinel_client = aioredis.Sentinel(
                sentinel_hosts,
                socket_timeout=self.config.socket_timeout,
                password=sentinels[0].password if sentinels else None
            )
            
            # Discover master through sentinel
            try:
                master_info = await self._sentinel_client.discover_master(self.config.master_name)
                master_host, master_port = master_info
                
                # Create master client
                self._master_client = aioredis.Redis(
                    host=master_host,
                    port=master_port,
                    password=masters[0].password if masters else None,
                    socket_timeout=self.config.socket_timeout,
                    socket_connect_timeout=self.config.socket_connect_timeout,
                    socket_keepalive=self.config.socket_keepalive,
                    socket_keepalive_options=self.config.socket_keepalive_options or {},
                    retry_on_timeout=self.config.retry_on_timeout
                )
                
                # Test master connection
                await self._master_client.ping()
                self.logger.info(f"Connected to Redis master via sentinel: {master_host}:{master_port}")
                
            except Exception as e:
                self.logger.warning(f"Failed to connect via sentinel: {e}")
                # Fall back to direct connection
        
        # Direct master connection if sentinel failed or not available
        if not self._master_client and masters:
            for master_node in masters:
                try:
                    self._master_client = aioredis.Redis(
                        host=master_node.host,
                        port=master_node.port,
                        db=master_node.db,
                        password=master_node.password,
                        socket_timeout=self.config.socket_timeout,
                        socket_connect_timeout=self.config.socket_connect_timeout,
                        socket_keepalive=self.config.socket_keepalive,
                        socket_keepalive_options=self.config.socket_keepalive_options or {},
                        retry_on_timeout=self.config.retry_on_timeout,
                        max_connections=master_node.max_connections
                    )
                    
                    await self._master_client.ping()
                    self._master_node = master_node
                    self.logger.info(f"Connected to Redis master: {master_node.host}:{master_node.port}")
                    break
                    
                except Exception as e:
                    self.logger.warning(f"Failed to connect to master {master_node.host}:{master_node.port}: {e}")
                    continue
        
        # Connect to slave nodes for read operations
        self._slave_clients = []
        for slave_node in slaves:
            try:
                slave_client = aioredis.Redis(
                    host=slave_node.host,
                    port=slave_node.port,
                    db=slave_node.db,
                    password=slave_node.password,
                    socket_timeout=self.config.socket_timeout,
                    socket_connect_timeout=self.config.socket_connect_timeout,
                    socket_keepalive=self.config.socket_keepalive,
                    socket_keepalive_options=self.config.socket_keepalive_options or {},
                    retry_on_timeout=self.config.retry_on_timeout,
                    max_connections=slave_node.max_connections
                )
                
                await slave_client.ping()
                self._slave_clients.append(slave_client)
                self.logger.info(f"Connected to Redis slave: {slave_node.host}:{slave_node.port}")
                
            except Exception as e:
                self.logger.warning(f"Failed to connect to slave {slave_node.host}:{slave_node.port}: {e}")
                continue
        
        if not self._master_client:
            raise ConnectionError("Could not establish connection to any Redis master")
    
    async def _get_read_client(self) -> aioredis.Redis:
        """Get client for read operations (prefer slaves)"""
        if self._slave_clients:
            # Round-robin among slaves
            import random
            return random.choice(self._slave_clients)
        
        # Fall back to master if no slaves available
        if not self._master_client:
            raise ConnectionError("No Redis connections available")
        
        return self._master_client
    
    async def _get_write_client(self) -> aioredis.Redis:
        """Get client for write operations (master only)"""
        if not self._master_client:
            # Attempt to reconnect
            await self._handle_failover()
        
        if not self._master_client:
            raise ConnectionError("No Redis master connection available")
        
        return self._master_client
    
    async def _handle_failover(self) -> None:
        """Handle Redis master failover"""
        async with self._connection_lock:
            self.logger.warning("Handling Redis master failover...")
            self._operation_stats['failovers'] += 1
            
            try:
                # Close existing connections
                if self._master_client:
                    await self._master_client.close()
                    self._master_client = None
                
                # Re-establish connections
                await self._establish_connections()
                self.logger.info("Redis failover completed successfully")
                
            except Exception as e:
                self.logger.error(f"Redis failover failed: {e}")
                capture_exception(e)
                raise
    
    async def _execute_with_retry(self, operation: str, func, *args, **kwargs):
        """Execute Redis operation with retry logic"""
        start_time = time.time()
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                self._operation_stats['total_operations'] += 1
                
                result = await func(*args, **kwargs)
                
                # Update success metrics
                self._operation_stats['successful_operations'] += 1
                response_time = (time.time() - start_time) * 1000
                
                # Update average response time
                current_avg = self._operation_stats['avg_response_time']
                total_ops = self._operation_stats['successful_operations']
                self._operation_stats['avg_response_time'] = (
                    (current_avg * (total_ops - 1) + response_time) / total_ops
                )
                
                return result
                
            except (ConnectionError, TimeoutError, RedisError) as e:
                last_exception = e
                self.logger.warning(f"Redis operation {operation} failed (attempt {attempt + 1}): {e}")
                
                if attempt < self.config.max_retries:
                    # Exponential backoff
                    wait_time = self.config.backoff_factor * (2 ** attempt)
                    await asyncio.sleep(wait_time)
                    
                    # Try failover if this is a connection error
                    if isinstance(e, ConnectionError):
                        try:
                            await self._handle_failover()
                        except Exception as failover_error:
                            self.logger.error(f"Failover failed: {failover_error}")
                
            except Exception as e:
                # Non-recoverable error
                last_exception = e
                self.logger.error(f"Redis operation {operation} failed with non-recoverable error: {e}")
                break
        
        # All retries failed
        self._operation_stats['failed_operations'] += 1
        capture_exception(last_exception)
        raise last_exception
    
    # Redis operations with cluster support
    async def get(self, key: str) -> Optional[bytes]:
        """Get value by key (read from slave if available)"""
        async def _get():
            client = await self._get_read_client()
            return await client.get(key)
        
        return await self._execute_with_retry('get', _get)
    
    async def set(self, key: str, value: Union[str, bytes], ex: Optional[int] = None) -> bool:
        """Set key-value pair (write to master)"""
        async def _set():
            client = await self._get_write_client()
            return await client.set(key, value, ex=ex)
        
        return await self._execute_with_retry('set', _set)
    
    async def delete(self, *keys: str) -> int:
        """Delete keys (write to master)"""
        async def _delete():
            client = await self._get_write_client()
            return await client.delete(*keys)
        
        return await self._execute_with_retry('delete', _delete)
    
    async def exists(self, *keys: str) -> int:
        """Check if keys exist (read from slave if available)"""
        async def _exists():
            client = await self._get_read_client()
            return await client.exists(*keys)
        
        return await self._execute_with_retry('exists', _exists)
    
    async def expire(self, key: str, time: int) -> bool:
        """Set key expiration (write to master)"""
        async def _expire():
            client = await self._get_write_client()
            return await client.expire(key, time)
        
        return await self._execute_with_retry('expire', _expire)
    
    async def hget(self, name: str, key: str) -> Optional[bytes]:
        """Get hash field value (read from slave if available)"""
        async def _hget():
            client = await self._get_read_client()
            return await client.hget(name, key)
        
        return await self._execute_with_retry('hget', _hget)
    
    async def hset(self, name: str, key: str, value: Union[str, bytes]) -> int:
        """Set hash field value (write to master)"""
        async def _hset():
            client = await self._get_write_client()
            return await client.hset(name, key, value)
        
        return await self._execute_with_retry('hset', _hset)
    
    async def hgetall(self, name: str) -> Dict[bytes, bytes]:
        """Get all hash fields (read from slave if available)"""
        async def _hgetall():
            client = await self._get_read_client()
            return await client.hgetall(name)
        
        return await self._execute_with_retry('hgetall', _hgetall)
    
    async def lpush(self, name: str, *values: Union[str, bytes]) -> int:
        """Push values to list (write to master)"""
        async def _lpush():
            client = await self._get_write_client()
            return await client.lpush(name, *values)
        
        return await self._execute_with_retry('lpush', _lpush)
    
    async def rpop(self, name: str) -> Optional[bytes]:
        """Pop value from list (write to master)"""
        async def _rpop():
            client = await self._get_write_client()
            return await client.rpop(name)
        
        return await self._execute_with_retry('rpop', _rpop)
    
    async def llen(self, name: str) -> int:
        """Get list length (read from slave if available)"""
        async def _llen():
            client = await self._get_read_client()
            return await client.llen(name)
        
        return await self._execute_with_retry('llen', _llen)
    
    # JSON operations for complex data
    async def set_json(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """Set JSON value"""
        json_value = json.dumps(value)
        return await self.set(key, json_value, ex=ex)
    
    async def get_json(self, key: str) -> Optional[Any]:
        """Get JSON value"""
        raw_value = await self.get(key)
        if raw_value is None:
            return None
        
        try:
            return json.loads(raw_value.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            self.logger.error(f"Failed to decode JSON from key {key}: {e}")
            return None
    
    # Cache operations with TTL
    async def cache_set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set cache value with TTL"""
        return await self.set_json(f"cache:{key}", value, ex=ttl)
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        return await self.get_json(f"cache:{key}")
    
    async def cache_delete(self, key: str) -> int:
        """Delete cache value"""
        return await self.delete(f"cache:{key}")
    
    # Cluster management
    async def get_cluster_info(self) -> Dict[str, Any]:
        """Get cluster information and statistics"""
        health = await self.health_checker.check_cluster_health()
        
        return {
            'health': health,
            'operation_stats': self._operation_stats.copy(),
            'config': {
                'total_nodes': len(self.config.nodes),
                'master_name': self.config.master_name,
                'socket_timeout': self.config.socket_timeout,
                'max_retries': self.config.max_retries
            },
            'connections': {
                'master_connected': self._master_client is not None,
                'slave_count': len(self._slave_clients),
                'sentinel_connected': self._sentinel_client is not None
            }
        }
    
    async def close(self) -> None:
        """Close all Redis connections"""
        self.logger.info("Closing Redis cluster connections...")
        
        # Close master connection
        if self._master_client:
            await self._master_client.close()
            self._master_client = None
        
        # Close slave connections
        for slave_client in self._slave_clients:
            await slave_client.close()
        self._slave_clients.clear()
        
        # Close sentinel connection
        if self._sentinel_client:
            await self._sentinel_client.close()
            self._sentinel_client = None
        
        self.logger.info("Redis cluster connections closed")

# Global Redis cluster client
redis_cluster: Optional[RedisClusterClient] = None

async def initialize_redis_cluster(config: Optional[RedisClusterConfig] = None) -> None:
    """Initialize Redis cluster"""
    global redis_cluster
    
    if redis_cluster:
        logger.warning("Redis cluster already initialized")
        return
    
    if not config:
        # Create default configuration from settings
        nodes = []
        
        # Parse Redis URL or use individual settings
        if hasattr(settings, 'redis_cluster_nodes') and settings.redis_cluster_nodes:
            # Multiple nodes specified
            for node_config in settings.redis_cluster_nodes:
                nodes.append(RedisNode(**node_config))
        else:
            # Single Redis instance
            redis_host = getattr(settings, 'redis_host', 'localhost')
            redis_port = getattr(settings, 'redis_port', 6379)
            redis_password = getattr(settings, 'redis_password', None)
            redis_db = getattr(settings, 'redis_db', 0)
            
            nodes.append(RedisNode(
                host=redis_host,
                port=redis_port,
                role=RedisNodeRole.MASTER,
                password=redis_password,
                db=redis_db
            ))
        
        config = RedisClusterConfig(nodes=nodes)
    
    redis_cluster = RedisClusterClient(config)
    await redis_cluster.initialize()
    logger.info("Redis cluster initialized")

async def close_redis_cluster() -> None:
    """Close Redis cluster"""
    global redis_cluster
    
    if redis_cluster:
        await redis_cluster.close()
        redis_cluster = None
        logger.info("Redis cluster closed")

def get_redis_cluster() -> RedisClusterClient:
    """Get Redis cluster client"""
    if not redis_cluster:
        raise RuntimeError("Redis cluster not initialized. Call initialize_redis_cluster() first.")
    
    return redis_cluster

# Convenience functions
async def cache_get(key: str) -> Optional[Any]:
    """Get value from cache"""
    cluster = get_redis_cluster()
    return await cluster.cache_get(key)

async def cache_set(key: str, value: Any, ttl: int = 3600) -> bool:
    """Set value in cache"""
    cluster = get_redis_cluster()
    return await cluster.cache_set(key, value, ttl)

async def cache_delete(key: str) -> int:
    """Delete value from cache"""
    cluster = get_redis_cluster()
    return await cluster.cache_delete(key)

async def get_redis_health() -> Dict[str, Any]:
    """Get Redis cluster health"""
    cluster = get_redis_cluster()
    return await cluster.get_cluster_info()