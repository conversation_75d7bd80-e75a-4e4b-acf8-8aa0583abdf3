"""
Intelligent cache invalidation system with event-driven patterns.

This module provides:
- Event-driven cache invalidation based on data changes
- Dependency graph for complex invalidation patterns
- Batch invalidation for efficiency
- Cache warming strategies after invalidation
- Integration with Supabase real-time events
"""

import asyncio
import logging
from typing import Dict, List, Set, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

from app.cache.redis_cache import get_cache, RedisCache
from app.utils.supabase.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


class InvalidationEvent(Enum):
    """Types of cache invalidation events."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    BULK_UPDATE = "bulk_update"
    MANUAL = "manual"


@dataclass
class CacheKey:
    """Represents a cache key with metadata."""
    key: str
    namespace: str = ""
    tags: Set[str] = field(default_factory=set)
    dependencies: Set[str] = field(default_factory=set)
    ttl: Optional[int] = None
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class InvalidationRule:
    """Defines how cache keys should be invalidated."""
    trigger_pattern: str  # Pattern that triggers invalidation
    target_patterns: List[str]  # Patterns to invalidate
    event_types: Set[InvalidationEvent] = field(default_factory=lambda: {InvalidationEvent.UPDATE, InvalidationEvent.DELETE})
    delay_seconds: int = 0  # Delay before invalidation
    batch_size: int = 100  # Batch size for bulk operations
    warm_after: bool = False  # Whether to warm cache after invalidation
    warm_function: Optional[str] = None  # Function name for warming


class CacheInvalidationManager:
    """
    Manages intelligent cache invalidation with dependency tracking.
    
    Features:
    - Event-driven invalidation based on data changes
    - Dependency graph for cascading invalidation
    - Batch processing for efficiency
    - Cache warming after invalidation
    - Real-time event integration with Supabase
    """
    
    def __init__(self, cache: Optional[RedisCache] = None):
        self.cache = cache
        self.rules: Dict[str, InvalidationRule] = {}
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.tag_mapping: Dict[str, Set[str]] = defaultdict(set)
        self.pending_invalidations: deque = deque()
        self._warming_functions: Dict[str, Callable] = {}
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize the invalidation manager."""
        if not self.cache:
            self.cache = await get_cache()
        
        # Set up default invalidation rules
        await self._setup_default_rules()
        
        # Start background tasks
        asyncio.create_task(self._process_pending_invalidations())
        asyncio.create_task(self._monitor_supabase_events())
        
        logger.info("Cache invalidation manager initialized")
    
    async def _setup_default_rules(self):
        """Set up default invalidation rules for the application."""
        
        # User data invalidation
        await self.add_rule(InvalidationRule(
            trigger_pattern="users:*",
            target_patterns=[
                "user_analytics:*",
                "user_books:*",
                "user_preferences:*"
            ],
            warm_after=True,
            warm_function="warm_user_data"
        ))
        
        # Book data invalidation
        await self.add_rule(InvalidationRule(
            trigger_pattern="books:*",
            target_patterns=[
                "book_analytics:*",
                "book_performance:*",
                "user_books:*",
                "trend_analysis:*"
            ],
            warm_after=True,
            warm_function="warm_book_data"
        ))
        
        # Publication invalidation
        await self.add_rule(InvalidationRule(
            trigger_pattern="publications:*",
            target_patterns=[
                "publication_analytics:*",
                "sales_data:*",
                "performance_metrics:*"
            ]
        ))
        
        # Trend data invalidation
        await self.add_rule(InvalidationRule(
            trigger_pattern="trends:*",
            target_patterns=[
                "trend_analysis:*",
                "market_data:*",
                "recommendations:*"
            ],
            delay_seconds=300  # Delay trend invalidation to batch updates
        ))
        
        # Analytics invalidation
        await self.add_rule(InvalidationRule(
            trigger_pattern="analytics:*",
            target_patterns=[
                "dashboard_data:*",
                "reports:*",
                "metrics:*"
            ]
        ))
    
    async def add_rule(self, rule: InvalidationRule) -> str:
        """Add an invalidation rule."""
        rule_id = f"rule_{len(self.rules)}_{rule.trigger_pattern}"
        self.rules[rule_id] = rule
        
        # Build dependency graph
        for target_pattern in rule.target_patterns:
            self.dependency_graph[rule.trigger_pattern].add(target_pattern)
        
        logger.info(f"Added invalidation rule: {rule_id}")
        return rule_id
    
    async def remove_rule(self, rule_id: str):
        """Remove an invalidation rule."""
        if rule_id in self.rules:
            rule = self.rules[rule_id]
            
            # Remove from dependency graph
            if rule.trigger_pattern in self.dependency_graph:
                for target_pattern in rule.target_patterns:
                    self.dependency_graph[rule.trigger_pattern].discard(target_pattern)
            
            del self.rules[rule_id]
            logger.info(f"Removed invalidation rule: {rule_id}")
    
    def register_warming_function(self, name: str, func: Callable):
        """Register a cache warming function."""
        self._warming_functions[name] = func
        logger.info(f"Registered warming function: {name}")
    
    async def invalidate_key(
        self,
        key: str,
        namespace: str = "",
        event_type: InvalidationEvent = InvalidationEvent.MANUAL,
        immediate: bool = False
    ):
        """Invalidate a specific cache key and its dependencies."""
        
        # Generate full cache key pattern
        if namespace:
            full_pattern = f"{namespace}:{key}"
        else:
            full_pattern = key
        
        # Find matching rules
        matching_rules = []
        for rule_id, rule in self.rules.items():
            if self._pattern_matches(full_pattern, rule.trigger_pattern):
                if event_type in rule.event_types:
                    matching_rules.append(rule)
        
        # Schedule invalidations
        for rule in matching_rules:
            if immediate or rule.delay_seconds == 0:
                await self._execute_invalidation(rule, full_pattern)
            else:
                await self._schedule_invalidation(rule, full_pattern, rule.delay_seconds)
    
    async def invalidate_tags(self, tags: Set[str], immediate: bool = False):
        """Invalidate all cache keys with specific tags."""
        keys_to_invalidate = set()
        
        for tag in tags:
            if tag in self.tag_mapping:
                keys_to_invalidate.update(self.tag_mapping[tag])
        
        for key in keys_to_invalidate:
            await self.invalidate_key(key, immediate=immediate)
    
    async def invalidate_pattern(self, pattern: str, namespace: str = ""):
        """Invalidate all keys matching a pattern."""
        if not self.cache:
            return
        
        deleted_count = await self.cache.delete_pattern(pattern, namespace)
        logger.info(f"Invalidated {deleted_count} keys matching pattern: {pattern}")
        
        return deleted_count
    
    async def batch_invalidate(self, keys: List[str], namespace: str = ""):
        """Efficiently invalidate multiple keys in batches."""
        if not keys:
            return
        
        # Group by namespace and batch
        batch_size = 100
        for i in range(0, len(keys), batch_size):
            batch = keys[i:i + batch_size]
            
            # Delete batch
            if self.cache:
                pipe = self.cache.redis_client.pipeline() if self.cache.redis_client else None
                if pipe:
                    for key in batch:
                        cache_key = self.cache._generate_cache_key(key, namespace)
                        pipe.delete(cache_key)
                    
                    await pipe.execute()
            
            # Small delay between batches to avoid overwhelming Redis
            if i + batch_size < len(keys):
                await asyncio.sleep(0.01)
        
        logger.info(f"Batch invalidated {len(keys)} keys")
    
    async def _execute_invalidation(self, rule: InvalidationRule, trigger_key: str):
        """Execute invalidation for a specific rule."""
        try:
            # Collect all keys to invalidate
            keys_to_invalidate = set()
            
            for target_pattern in rule.target_patterns:
                # Handle pattern expansion
                if "*" in target_pattern:
                    # For wildcard patterns, we need to find matching keys
                    if self.cache and self.cache.redis_client:
                        cache_pattern = self.cache._generate_cache_key(target_pattern, "")
                        matching_keys = await self.cache.redis_client.keys(cache_pattern)
                        keys_to_invalidate.update(key.decode() if isinstance(key, bytes) else key for key in matching_keys)
                else:
                    # Direct key
                    keys_to_invalidate.add(target_pattern)
            
            # Batch invalidate
            if keys_to_invalidate:
                await self.batch_invalidate(list(keys_to_invalidate))
            
            # Warm cache if requested
            if rule.warm_after and rule.warm_function:
                await self._warm_cache_after_invalidation(rule.warm_function, trigger_key)
            
            logger.info(f"Executed invalidation rule for trigger: {trigger_key}")
            
        except Exception as e:
            logger.error(f"Failed to execute invalidation for {trigger_key}: {e}")
    
    async def _schedule_invalidation(self, rule: InvalidationRule, trigger_key: str, delay: int):
        """Schedule delayed invalidation."""
        invalidation_time = datetime.utcnow() + timedelta(seconds=delay)
        
        self.pending_invalidations.append({
            "rule": rule,
            "trigger_key": trigger_key,
            "scheduled_time": invalidation_time
        })
        
        logger.info(f"Scheduled invalidation for {trigger_key} in {delay} seconds")
    
    async def _process_pending_invalidations(self):
        """Background task to process scheduled invalidations."""
        while True:
            try:
                current_time = datetime.utcnow()
                processed = []
                
                # Process due invalidations
                while self.pending_invalidations:
                    invalidation = self.pending_invalidations[0]
                    
                    if invalidation["scheduled_time"] <= current_time:
                        invalidation = self.pending_invalidations.popleft()
                        await self._execute_invalidation(
                            invalidation["rule"],
                            invalidation["trigger_key"]
                        )
                        processed.append(invalidation["trigger_key"])
                    else:
                        break
                
                if processed:
                    logger.info(f"Processed {len(processed)} scheduled invalidations")
                
                # Sleep before next check
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Error processing pending invalidations: {e}")
                await asyncio.sleep(30)
    
    async def _warm_cache_after_invalidation(self, function_name: str, trigger_key: str):
        """Warm cache after invalidation using registered function."""
        if function_name in self._warming_functions:
            try:
                warming_func = self._warming_functions[function_name]
                await warming_func(trigger_key)
                logger.info(f"Cache warmed after invalidation: {trigger_key}")
            except Exception as e:
                logger.error(f"Failed to warm cache for {trigger_key}: {e}")
    
    async def _monitor_supabase_events(self):
        """Monitor Supabase real-time events for automatic invalidation."""
        try:
            supabase = get_supabase_client()
            
            # Set up real-time subscriptions for cache invalidation
            # This would be implemented based on Supabase real-time capabilities
            
            logger.info("Started monitoring Supabase events for cache invalidation")
            
        except Exception as e:
            logger.error(f"Failed to set up Supabase event monitoring: {e}")
    
    def _pattern_matches(self, key: str, pattern: str) -> bool:
        """Check if a key matches a pattern with wildcards."""
        if "*" not in pattern:
            return key == pattern
        
        # Simple wildcard matching
        pattern_parts = pattern.split("*")
        key_pos = 0
        
        for i, part in enumerate(pattern_parts):
            if i == 0:  # First part
                if not key.startswith(part):
                    return False
                key_pos += len(part)
            elif i == len(pattern_parts) - 1:  # Last part
                if not key[key_pos:].endswith(part):
                    return False
            else:  # Middle parts
                pos = key.find(part, key_pos)
                if pos == -1:
                    return False
                key_pos = pos + len(part)
        
        return True
    
    async def get_dependency_graph(self) -> Dict[str, Set[str]]:
        """Get the current dependency graph."""
        return dict(self.dependency_graph)
    
    async def get_invalidation_stats(self) -> Dict[str, Any]:
        """Get invalidation statistics."""
        return {
            "active_rules": len(self.rules),
            "pending_invalidations": len(self.pending_invalidations),
            "dependency_mappings": len(self.dependency_graph),
            "warming_functions": len(self._warming_functions),
            "rules": {rule_id: {
                "trigger_pattern": rule.trigger_pattern,
                "target_count": len(rule.target_patterns),
                "delay_seconds": rule.delay_seconds,
                "warm_after": rule.warm_after
            } for rule_id, rule in self.rules.items()}
        }


# Global invalidation manager instance
_invalidation_manager: Optional[CacheInvalidationManager] = None


async def get_invalidation_manager() -> CacheInvalidationManager:
    """Get or create global invalidation manager."""
    global _invalidation_manager
    
    if _invalidation_manager is None:
        _invalidation_manager = CacheInvalidationManager()
        await _invalidation_manager.initialize()
    
    return _invalidation_manager


# Convenience functions for common invalidation patterns
async def invalidate_user_cache(user_id: str):
    """Invalidate all cache entries for a specific user."""
    manager = await get_invalidation_manager()
    await manager.invalidate_pattern(f"user:{user_id}:*")
    await manager.invalidate_pattern(f"users:{user_id}")


async def invalidate_book_cache(book_id: str):
    """Invalidate all cache entries for a specific book."""
    manager = await get_invalidation_manager()
    await manager.invalidate_pattern(f"book:{book_id}:*")
    await manager.invalidate_pattern(f"books:{book_id}")


async def invalidate_analytics_cache():
    """Invalidate all analytics cache entries."""
    manager = await get_invalidation_manager()
    await manager.invalidate_pattern("analytics:*")
    await manager.invalidate_pattern("dashboard:*")
    await manager.invalidate_pattern("reports:*")


# Cache warming functions
async def warm_user_data(trigger_key: str):
    """Warm user-related cache data."""
    # Extract user ID from trigger key
    user_id = trigger_key.split(":")[-1] if ":" in trigger_key else trigger_key
    
    # This would be implemented to warm specific user data
    logger.info(f"Warming user data for user: {user_id}")


async def warm_book_data(trigger_key: str):
    """Warm book-related cache data."""
    # Extract book ID from trigger key
    book_id = trigger_key.split(":")[-1] if ":" in trigger_key else trigger_key
    
    # This would be implemented to warm specific book data
    logger.info(f"Warming book data for book: {book_id}")


# Register warming functions
async def register_default_warming_functions():
    """Register default cache warming functions."""
    manager = await get_invalidation_manager()
    manager.register_warming_function("warm_user_data", warm_user_data)
    manager.register_warming_function("warm_book_data", warm_book_data)
