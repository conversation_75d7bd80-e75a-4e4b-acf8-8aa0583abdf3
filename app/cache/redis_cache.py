"""
Production-grade Redis caching layer with intelligent invalidation and failover.

This module provides a comprehensive caching solution with:
- Intelligent cache key generation and namespacing
- TTL management with staggered expiration
- Cache warming and prefetching strategies
- Distributed cache invalidation
- Failover and circuit breaker patterns
- Memory usage optimization
"""

import json
import hashlib
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import redis.asyncio as redis
from redis.asyncio.retry import Retry
from redis.backoff import ExponentialBackoff
from pydantic import BaseModel, Field
from app.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CacheConfig(BaseModel):
    """Cache configuration settings."""
    default_ttl: int = Field(default=3600, description="Default TTL in seconds")
    max_ttl: int = Field(default=86400, description="Maximum TTL in seconds")
    key_prefix: str = Field(default="publish_ai", description="Cache key prefix")
    max_memory_mb: int = Field(default=512, description="Max memory usage in MB")
    eviction_policy: str = Field(default="allkeys-lru", description="Redis eviction policy")
    enable_compression: bool = Field(default=True, description="Enable data compression")
    stagger_factor: float = Field(default=0.1, description="TTL stagger factor to prevent thundering herd")


class CacheStats(BaseModel):
    """Cache performance statistics."""
    hits: int = 0
    misses: int = 0
    errors: int = 0
    memory_usage_mb: float = 0.0
    hit_rate: float = 0.0
    last_updated: datetime = Field(default_factory=datetime.utcnow)


class RedisCache:
    """
    Production-grade Redis cache with advanced features.
    
    Features:
    - Automatic failover and circuit breaker
    - Intelligent key generation and namespacing
    - TTL staggering to prevent cache stampeding
    - Compression for large objects
    - Performance monitoring and statistics
    - Distributed cache invalidation
    """
    
    def __init__(self, config: Optional[CacheConfig] = None):
        self.config = config or CacheConfig()
        self.redis_client: Optional[redis.Redis] = None
        self.stats = CacheStats()
        self._lock = asyncio.Lock()
        self._circuit_breaker_failures = 0
        self._circuit_breaker_last_failure = None
        self._circuit_breaker_threshold = 5
        self._circuit_breaker_timeout = 60
        
    async def initialize(self) -> bool:
        """Initialize Redis connection with retry and failover."""
        try:
            # Create Redis client with retry configuration
            retry = Retry(ExponentialBackoff(), retries=3)
            
            self.redis_client = redis.Redis(
                host=settings.redis_host,
                port=settings.redis_port,
                db=settings.redis_db,
                password=settings.redis_password,
                decode_responses=False,  # Handle binary data
                retry=retry,
                retry_on_timeout=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                health_check_interval=30
            )
            
            # Test connection and handle MISCONF errors
            try:
                await self.redis_client.ping()
            except Exception as ping_error:
                if "MISCONF" in str(ping_error):
                    logger.warning("Redis MISCONF error detected, attempting to fix...")
                    # Try to fix the RDB configuration issue
                    await self._fix_redis_misconf()
                    # Retry ping
                    await self.redis_client.ping()
                else:
                    raise ping_error
            
            # Configure Redis settings (with error handling for RDB issues)
            await self._configure_redis_safe()
            
            logger.info("Redis cache initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis cache: {e}")
            self.redis_client = None
            return False
    
    async def _configure_redis(self):
        """Configure Redis for optimal caching performance."""
        try:
            # Set memory policy
            await self.redis_client.config_set("maxmemory-policy", self.config.eviction_policy)
            
            # Set max memory if specified
            if self.config.max_memory_mb > 0:
                max_memory = f"{self.config.max_memory_mb}mb"
                await self.redis_client.config_set("maxmemory", max_memory)
                
        except Exception as e:
            logger.warning(f"Failed to configure Redis settings: {e}")
    
    async def _fix_redis_misconf(self):
        """Fix Redis MISCONF error by using CONFIG SET commands that work even with RDB errors."""
        try:
            # Use a raw Redis command to disable the bgsave error checking
            # This command should work even when Redis is in MISCONF state
            await self.redis_client.execute_command("CONFIG", "SET", "stop-writes-on-bgsave-error", "no")
            logger.info("✅ Fixed Redis MISCONF by disabling stop-writes-on-bgsave-error")
        except Exception as e:
            logger.warning(f"Could not fix Redis MISCONF: {e}")
            # Try alternative approach
            try:
                await self.redis_client.execute_command("CONFIG", "SET", "save", "")
                logger.info("✅ Disabled Redis RDB snapshots as fallback")
            except Exception as e2:
                logger.error(f"Failed all Redis MISCONF fixes: {e2}")
                raise e

    async def _configure_redis_safe(self):
        """Configure Redis safely, handling RDB snapshot issues."""
        try:
            # First, try to disable RDB snapshots to avoid MISCONF errors
            try:
                await self.redis_client.config_set("stop-writes-on-bgsave-error", "no")
                logger.info("Disabled stop-writes-on-bgsave-error to handle RDB issues")
            except Exception as e:
                logger.warning(f"Could not disable stop-writes-on-bgsave-error: {e}")
            
            # Try to disable RDB snapshots entirely for development
            try:
                await self.redis_client.config_set("save", "")
                logger.info("Disabled RDB snapshots for development")
            except Exception as e:
                logger.warning(f"Could not disable RDB snapshots: {e}")
            
            # Now try the regular configuration
            await self._configure_redis()
            
        except Exception as e:
            logger.warning(f"Failed to configure Redis safely: {e}")
            # Continue anyway - Redis will work for basic operations
    
    def _generate_cache_key(self, key: str, namespace: str = "") -> str:
        """Generate a consistent cache key with namespace."""
        if namespace:
            full_key = f"{self.config.key_prefix}:{namespace}:{key}"
        else:
            full_key = f"{self.config.key_prefix}:{key}"
        
        # Hash long keys to prevent Redis key length issues
        if len(full_key) > 250:
            key_hash = hashlib.md5(full_key.encode()).hexdigest()
            return f"{self.config.key_prefix}:hash:{key_hash}"
        
        return full_key
    
    def _calculate_ttl(self, ttl: Optional[int] = None) -> int:
        """Calculate TTL with staggering to prevent thundering herd."""
        base_ttl = ttl or self.config.default_ttl
        
        # Add random stagger (±10% by default)
        import random
        stagger = int(base_ttl * self.config.stagger_factor)
        staggered_ttl = base_ttl + random.randint(-stagger, stagger)
        
        return min(staggered_ttl, self.config.max_ttl)
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for storage with optional compression."""
        try:
            json_data = json.dumps(data, default=str).encode('utf-8')
            
            if self.config.enable_compression and len(json_data) > 1024:
                import gzip
                return gzip.compress(json_data)
            
            return json_data
            
        except Exception as e:
            logger.error(f"Failed to serialize data: {e}")
            raise
    
    def _deserialize_data(self, data: bytes) -> Any:
        """Deserialize data from storage with decompression."""
        try:
            # Check if data is compressed (gzip magic number)
            if data[:2] == b'\x1f\x8b':
                import gzip
                data = gzip.decompress(data)
            
            return json.loads(data.decode('utf-8'))
            
        except Exception as e:
            logger.error(f"Failed to deserialize data: {e}")
            raise
    
    async def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open (Redis unavailable)."""
        if self._circuit_breaker_failures < self._circuit_breaker_threshold:
            return False
        
        if self._circuit_breaker_last_failure:
            time_since_failure = (datetime.utcnow() - self._circuit_breaker_last_failure).seconds
            if time_since_failure > self._circuit_breaker_timeout:
                # Reset circuit breaker
                self._circuit_breaker_failures = 0
                self._circuit_breaker_last_failure = None
                return False
        
        return True
    
    async def _handle_redis_error(self, error: Exception):
        """Handle Redis errors and update circuit breaker."""
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = datetime.utcnow()
        self.stats.errors += 1
        logger.error(f"Redis error: {error}")
    
    async def get(self, key: str, namespace: str = "") -> Optional[Any]:
        """Get value from cache with circuit breaker protection."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            self.stats.misses += 1
            return None
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            data = await self.redis_client.get(cache_key)
            
            if data is None:
                self.stats.misses += 1
                return None
            
            self.stats.hits += 1
            return self._deserialize_data(data)
            
        except Exception as e:
            await self._handle_redis_error(e)
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None, 
        namespace: str = ""
    ) -> bool:
        """Set value in cache with TTL."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            data = self._serialize_data(value)
            calculated_ttl = self._calculate_ttl(ttl)
            
            await self.redis_client.setex(cache_key, calculated_ttl, data)
            return True
            
        except Exception as e:
            await self._handle_redis_error(e)
            return False
    
    async def delete(self, key: str, namespace: str = "") -> bool:
        """Delete key from cache."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            result = await self.redis_client.delete(cache_key)
            return result > 0
            
        except Exception as e:
            await self._handle_redis_error(e)
            return False
    
    async def delete_pattern(self, pattern: str, namespace: str = "") -> int:
        """Delete multiple keys matching pattern."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return 0
        
        try:
            search_pattern = self._generate_cache_key(pattern, namespace)
            keys = await self.redis_client.keys(search_pattern)
            
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
            
        except Exception as e:
            await self._handle_redis_error(e)
            return 0
    
    async def exists(self, key: str, namespace: str = "") -> bool:
        """Check if key exists in cache."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            return await self.redis_client.exists(cache_key) > 0
            
        except Exception as e:
            await self._handle_redis_error(e)
            return False
    
    async def increment(self, key: str, amount: int = 1, namespace: str = "") -> Optional[int]:
        """Increment a counter in cache."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return None
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            return await self.redis_client.incrby(cache_key, amount)
            
        except Exception as e:
            await self._handle_redis_error(e)
            return None
    
    async def get_multiple(self, keys: List[str], namespace: str = "") -> Dict[str, Any]:
        """Get multiple values from cache."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return {}
        
        try:
            cache_keys = [self._generate_cache_key(key, namespace) for key in keys]
            values = await self.redis_client.mget(cache_keys)
            
            result = {}
            for i, (original_key, value) in enumerate(zip(keys, values)):
                if value is not None:
                    try:
                        result[original_key] = self._deserialize_data(value)
                        self.stats.hits += 1
                    except Exception:
                        self.stats.misses += 1
                else:
                    self.stats.misses += 1
            
            return result
            
        except Exception as e:
            await self._handle_redis_error(e)
            return {}
    
    async def set_multiple(
        self, 
        data: Dict[str, Any], 
        ttl: Optional[int] = None, 
        namespace: str = ""
    ) -> bool:
        """Set multiple values in cache."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return False
        
        try:
            pipe = self.redis_client.pipeline()
            calculated_ttl = self._calculate_ttl(ttl)
            
            for key, value in data.items():
                cache_key = self._generate_cache_key(key, namespace)
                serialized_data = self._serialize_data(value)
                pipe.setex(cache_key, calculated_ttl, serialized_data)
            
            await pipe.execute()
            return True
            
        except Exception as e:
            await self._handle_redis_error(e)
            return False
    
    async def get_stats(self) -> CacheStats:
        """Get cache performance statistics."""
        if self.redis_client and not await self._is_circuit_breaker_open():
            try:
                info = await self.redis_client.info('memory')
                self.stats.memory_usage_mb = float(info.get('used_memory', 0)) / (1024 * 1024)
            except Exception:
                pass
        
        # Calculate hit rate
        total_requests = self.stats.hits + self.stats.misses
        if total_requests > 0:
            self.stats.hit_rate = self.stats.hits / total_requests
        
        self.stats.last_updated = datetime.utcnow()
        return self.stats
    
    async def clear_namespace(self, namespace: str) -> int:
        """Clear all keys in a namespace."""
        pattern = f"{namespace}:*"
        return await self.delete_pattern(pattern)
    
    async def warm_cache(self, warming_func: Callable, keys: List[str], namespace: str = ""):
        """Warm cache with provided function."""
        if not self.redis_client or await self._is_circuit_breaker_open():
            return
        
        try:
            for key in keys:
                if not await self.exists(key, namespace):
                    value = await warming_func(key)
                    if value is not None:
                        await self.set(key, value, namespace=namespace)
                        
        except Exception as e:
            logger.error(f"Cache warming failed: {e}")
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()


# Global cache instance
_cache_instance: Optional[RedisCache] = None


async def get_cache() -> RedisCache:
    """Get or create global cache instance."""
    global _cache_instance
    
    if _cache_instance is None:
        _cache_instance = RedisCache()
        await _cache_instance.initialize()
    
    return _cache_instance


@asynccontextmanager
async def cache_context():
    """Context manager for cache operations."""
    cache = await get_cache()
    try:
        yield cache
    finally:
        # Cache cleanup if needed
        pass


# Decorator for automatic caching
def cache_result(ttl: int = 3600, namespace: str = "", key_func: Optional[Callable] = None):
    """
    Decorator for automatic function result caching.
    
    Args:
        ttl: Time to live in seconds
        namespace: Cache namespace
        key_func: Function to generate cache key from arguments
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache = await get_cache()
            
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation from function name and args
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = ":".join(key_parts)
            
            # Try to get from cache
            result = await cache.get(cache_key, namespace)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            if result is not None:
                await cache.set(cache_key, result, ttl, namespace)
            
            return result
        
        return wrapper
    return decorator