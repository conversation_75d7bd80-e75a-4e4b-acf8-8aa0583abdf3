"""
CDN support for static assets with intelligent caching and versioning.

This module provides:
- Static asset versioning and cache busting
- CDN-ready file serving with proper headers
- Asset optimization and compression
- Distributed cache control for static content
- Integration with cloud storage providers
"""

import os
import hashlib
import mimetypes
import gzip
import json
import logging
from typing import Dict, Optional, List, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from dataclasses import dataclass
import asyncio

from fastapi import Request, Response
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
import aiofiles

from app.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class AssetInfo:
    """Information about a static asset."""
    path: str
    size: int
    etag: str
    last_modified: datetime
    content_type: str
    compressed: bool = False
    compressed_size: Optional[int] = None
    cache_duration: int = 86400  # Default 24 hours


class CDNAssetManager:
    """
    Manages static assets for CDN delivery with intelligent caching.
    
    Features:
    - Asset versioning with content hashing
    - Automatic compression for compressible content
    - Proper cache headers for CDN optimization
    - Asset manifest generation for cache busting
    - Support for multiple storage backends
    """
    
    def __init__(self, static_dir: str = "storage", cdn_url: Optional[str] = None):
        self.static_dir = Path(static_dir)
        self.cdn_url = cdn_url or settings.cdn_url
        self.asset_manifest: Dict[str, AssetInfo] = {}
        self.compression_types = {
            'text/html', 'text/css', 'text/javascript', 'application/javascript',
            'application/json', 'text/plain', 'application/xml', 'text/xml',
            'image/svg+xml'
        }
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize the CDN asset manager."""
        await self._build_asset_manifest()
        logger.info("CDN asset manager initialized")
    
    async def _build_asset_manifest(self):
        """Build manifest of all static assets with versioning."""
        if not self.static_dir.exists():
            self.static_dir.mkdir(parents=True, exist_ok=True)
            return
        
        async with self._lock:
            for file_path in self.static_dir.rglob("*"):
                if file_path.is_file():
                    await self._process_asset(file_path)
        
        # Save manifest to file
        await self._save_manifest()
        logger.info(f"Built asset manifest with {len(self.asset_manifest)} assets")
    
    async def _process_asset(self, file_path: Path):
        """Process a single asset file."""
        try:
            stat = file_path.stat()
            relative_path = str(file_path.relative_to(self.static_dir))
            
            # Generate ETag from file content hash
            etag = await self._generate_etag(file_path)
            
            # Determine content type
            content_type, _ = mimetypes.guess_type(str(file_path))
            if not content_type:
                content_type = 'application/octet-stream'
            
            # Check if compression is beneficial
            compressed = False
            compressed_size = None
            if content_type in self.compression_types and stat.st_size > 1024:
                compressed_size = await self._get_compressed_size(file_path)
                compressed = compressed_size < stat.st_size * 0.9  # Only if >10% savings
            
            # Determine cache duration based on file type
            cache_duration = self._get_cache_duration(file_path, content_type)
            
            asset_info = AssetInfo(
                path=relative_path,
                size=stat.st_size,
                etag=etag,
                last_modified=datetime.fromtimestamp(stat.st_mtime),
                content_type=content_type,
                compressed=compressed,
                compressed_size=compressed_size,
                cache_duration=cache_duration
            )
            
            self.asset_manifest[relative_path] = asset_info
            
        except Exception as e:
            logger.error(f"Failed to process asset {file_path}: {e}")
    
    async def _generate_etag(self, file_path: Path) -> str:
        """Generate ETag from file content."""
        hash_md5 = hashlib.md5()
        
        async with aiofiles.open(file_path, 'rb') as f:
            async for chunk in self._read_chunks(f):
                hash_md5.update(chunk)
        
        return f'"{hash_md5.hexdigest()}"'
    
    async def _read_chunks(self, file_obj, chunk_size: int = 8192):
        """Read file in chunks asynchronously."""
        while True:
            chunk = await file_obj.read(chunk_size)
            if not chunk:
                break
            yield chunk
    
    async def _get_compressed_size(self, file_path: Path) -> int:
        """Get the size of file after gzip compression."""
        compressed_data = b""
        
        async with aiofiles.open(file_path, 'rb') as f:
            content = await f.read()
            compressed_data = gzip.compress(content, compresslevel=6)
        
        return len(compressed_data)
    
    def _get_cache_duration(self, file_path: Path, content_type: str) -> int:
        """Determine appropriate cache duration for asset type."""
        # Static assets with versioning can have long cache times
        if any(pattern in str(file_path) for pattern in ['.min.', '-v', '_v', '.hash.']):
            return 31536000  # 1 year for versioned assets
        
        # Images and fonts
        if content_type.startswith(('image/', 'font/')):
            return 2592000  # 30 days
        
        # CSS and JS
        if content_type in ['text/css', 'text/javascript', 'application/javascript']:
            return 604800  # 7 days
        
        # HTML and other dynamic content
        if content_type == 'text/html':
            return 3600  # 1 hour
        
        # Default cache duration
        return 86400  # 24 hours
    
    async def _save_manifest(self):
        """Save asset manifest to file."""
        manifest_path = self.static_dir / "asset-manifest.json"
        
        manifest_data = {
            "generated_at": datetime.utcnow().isoformat(),
            "assets": {
                path: {
                    "size": info.size,
                    "etag": info.etag,
                    "last_modified": info.last_modified.isoformat(),
                    "content_type": info.content_type,
                    "compressed": info.compressed,
                    "cache_duration": info.cache_duration
                }
                for path, info in self.asset_manifest.items()
            }
        }
        
        async with aiofiles.open(manifest_path, 'w') as f:
            await f.write(json.dumps(manifest_data, indent=2))
    
    def get_asset_url(self, asset_path: str, with_version: bool = True) -> str:
        """Get CDN URL for an asset with optional versioning."""
        asset_info = self.asset_manifest.get(asset_path)
        
        if self.cdn_url:
            base_url = self.cdn_url.rstrip('/')
        else:
            base_url = "/static"
        
        if with_version and asset_info:
            # Add version query parameter based on ETag
            version = asset_info.etag.strip('"')[:8]
            return f"{base_url}/{asset_path}?v={version}"
        
        return f"{base_url}/{asset_path}"
    
    def get_versioned_filename(self, asset_path: str) -> str:
        """Get versioned filename for an asset."""
        asset_info = self.asset_manifest.get(asset_path)
        if not asset_info:
            return asset_path
        
        path_obj = Path(asset_path)
        version = asset_info.etag.strip('"')[:8]
        
        # Insert version before file extension
        name = path_obj.stem
        suffix = path_obj.suffix
        
        return f"{path_obj.parent}/{name}.{version}{suffix}"
    
    async def serve_asset(self, request: Request, asset_path: str) -> Response:
        """Serve an asset with proper CDN headers."""
        asset_info = self.asset_manifest.get(asset_path)
        if not asset_info:
            return Response(status_code=404)
        
        file_path = self.static_dir / asset_path
        if not file_path.exists():
            return Response(status_code=404)
        
        # Check if client has cached version
        if_none_match = request.headers.get('if-none-match')
        if if_none_match == asset_info.etag:
            return Response(status_code=304)
        
        if_modified_since = request.headers.get('if-modified-since')
        if if_modified_since:
            try:
                client_time = datetime.strptime(if_modified_since, '%a, %d %b %Y %H:%M:%S GMT')
                if asset_info.last_modified <= client_time:
                    return Response(status_code=304)
            except ValueError:
                pass
        
        # Determine if we should serve compressed version
        accept_encoding = request.headers.get('accept-encoding', '')
        serve_compressed = (
            asset_info.compressed and
            'gzip' in accept_encoding and
            request.headers.get('accept', '*/*') != 'text/html'  # Don't compress HTML for SEO
        )
        
        # Build response headers
        headers = {
            'ETag': asset_info.etag,
            'Last-Modified': asset_info.last_modified.strftime('%a, %d %b %Y %H:%M:%S GMT'),
            'Cache-Control': f'public, max-age={asset_info.cache_duration}',
            'Content-Type': asset_info.content_type,
        }
        
        # Add CDN-specific headers
        if self.cdn_url:
            headers.update({
                'CDN-Cache-Control': f'public, max-age={asset_info.cache_duration * 2}',
                'Vary': 'Accept-Encoding',
            })
        
        if serve_compressed:
            headers['Content-Encoding'] = 'gzip'
            headers['Content-Length'] = str(asset_info.compressed_size)
            
            # Serve compressed content
            async def generate_compressed():
                async with aiofiles.open(file_path, 'rb') as f:
                    content = await f.read()
                    yield gzip.compress(content, compresslevel=6)
            
            return StreamingResponse(
                generate_compressed(),
                media_type=asset_info.content_type,
                headers=headers
            )
        else:
            # Serve uncompressed content
            return FileResponse(
                file_path,
                media_type=asset_info.content_type,
                headers=headers
            )
    
    async def invalidate_asset(self, asset_path: str):
        """Invalidate and reprocess a specific asset."""
        file_path = self.static_dir / asset_path
        if file_path.exists():
            await self._process_asset(file_path)
            await self._save_manifest()
            logger.info(f"Invalidated asset: {asset_path}")
    
    async def optimize_images(self, quality: int = 85):
        """Optimize images in the asset directory."""
        try:
            from PIL import Image
            
            for asset_path, asset_info in self.asset_manifest.items():
                if asset_info.content_type.startswith('image/'):
                    file_path = self.static_dir / asset_path
                    
                    # Skip if already optimized
                    if '.opt.' in asset_path:
                        continue
                    
                    try:
                        with Image.open(file_path) as img:
                            # Skip if already small
                            if asset_info.size < 50000:  # 50KB
                                continue
                            
                            # Optimize image
                            if img.format == 'JPEG':
                                img.save(file_path, 'JPEG', quality=quality, optimize=True)
                            elif img.format == 'PNG':
                                img.save(file_path, 'PNG', optimize=True)
                            
                            # Reprocess asset
                            await self._process_asset(file_path)
                            
                    except Exception as e:
                        logger.error(f"Failed to optimize image {asset_path}: {e}")
            
            await self._save_manifest()
            logger.info("Image optimization completed")
            
        except ImportError:
            logger.warning("PIL not available for image optimization")
    
    def get_manifest(self) -> Dict[str, AssetInfo]:
        """Get the complete asset manifest."""
        return self.asset_manifest.copy()
    
    async def cleanup_old_versions(self, keep_versions: int = 3):
        """Clean up old versioned files."""
        # Group files by base name
        file_groups = {}
        
        for asset_path in self.asset_manifest:
            # Extract base name (without version)
            path_obj = Path(asset_path)
            base_name = path_obj.stem.split('.')[0]  # Remove version hash
            
            if base_name not in file_groups:
                file_groups[base_name] = []
            file_groups[base_name].append(asset_path)
        
        # Clean up old versions
        for base_name, files in file_groups.items():
            if len(files) > keep_versions:
                # Sort by modification time, keep newest
                files_with_time = [
                    (f, self.asset_manifest[f].last_modified) for f in files
                ]
                files_with_time.sort(key=lambda x: x[1], reverse=True)
                
                # Remove old files
                for file_path, _ in files_with_time[keep_versions:]:
                    try:
                        (self.static_dir / file_path).unlink()
                        del self.asset_manifest[file_path]
                        logger.info(f"Cleaned up old version: {file_path}")
                    except Exception as e:
                        logger.error(f"Failed to clean up {file_path}: {e}")
        
        await self._save_manifest()


# Global CDN manager instance
_cdn_manager: Optional[CDNAssetManager] = None


async def get_cdn_manager() -> CDNAssetManager:
    """Get or create global CDN manager."""
    global _cdn_manager
    
    if _cdn_manager is None:
        _cdn_manager = CDNAssetManager()
        await _cdn_manager.initialize()
    
    return _cdn_manager


class CDNStaticFiles(StaticFiles):
    """Enhanced StaticFiles with CDN support."""
    
    def __init__(self, directory: str, cdn_manager: Optional[CDNAssetManager] = None):
        super().__init__(directory=directory)
        self.cdn_manager = cdn_manager
    
    async def __call__(self, scope, receive, send):
        """Handle static file requests with CDN optimization."""
        if self.cdn_manager:
            # Use CDN manager for enhanced serving
            request = Request(scope, receive)
            asset_path = scope["path"].lstrip("/static/")
            
            response = await self.cdn_manager.serve_asset(request, asset_path)
            await response(scope, receive, send)
        else:
            # Fallback to default behavior
            await super().__call__(scope, receive, send)


# Utility functions for template integration
def asset_url(asset_path: str, with_version: bool = True) -> str:
    """Template function to get asset URL with versioning."""
    if _cdn_manager:
        return _cdn_manager.get_asset_url(asset_path, with_version)
    return f"/static/{asset_path}"


def inline_css(css_path: str) -> str:
    """Inline CSS content for critical path optimization."""
    if _cdn_manager and css_path in _cdn_manager.asset_manifest:
        try:
            file_path = _cdn_manager.static_dir / css_path
            with open(file_path, 'r') as f:
                return f.read()
        except Exception:
            pass
    return ""


def preload_assets(asset_paths: List[str]) -> List[str]:
    """Generate preload link headers for critical assets."""
    if not _cdn_manager:
        return []
    
    preload_links = []
    for asset_path in asset_paths:
        asset_info = _cdn_manager.asset_manifest.get(asset_path)
        if asset_info:
            url = _cdn_manager.get_asset_url(asset_path)
            
            # Determine resource type
            if asset_info.content_type == 'text/css':
                rel_type = 'stylesheet'
            elif asset_info.content_type in ['text/javascript', 'application/javascript']:
                rel_type = 'script'
            elif asset_info.content_type.startswith('image/'):
                rel_type = 'image'
            elif asset_info.content_type.startswith('font/'):
                rel_type = 'font'
            else:
                continue
            
            preload_links.append(f'<{url}>; rel=preload; as={rel_type}')
    
    return preload_links