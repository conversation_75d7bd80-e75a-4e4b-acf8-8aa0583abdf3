"""
Cache package initialization.

This package provides comprehensive caching capabilities including:
- Redis-based response caching with circuit breakers
- Intelligent cache invalidation with dependency tracking
- CDN support for static assets with versioning
- Database query result caching for Supabase
- API response caching middleware
"""

from .redis_cache import RedisCache, get_cache, cache_result, CacheConfig, CacheStats
from .cache_invalidation import (
    CacheInvalidationManager, 
    get_invalidation_manager,
    InvalidationEvent,
    invalidate_user_cache,
    invalidate_book_cache,
    invalidate_analytics_cache
)
from .cdn_support import CDNAssetManager, get_cdn_manager, asset_url, preload_assets
from app.utils.supabase.supabase_cache import (
    SupabaseCacheManager,
    get_supabase_cache_manager,
    cached_supabase_query,
)

__all__ = [
    # Redis Cache
    "RedisCache",
    "get_cache", 
    "cache_result",
    "CacheConfig",
    "CacheStats",
    
    # Cache Invalidation
    "CacheInvalidationManager",
    "get_invalidation_manager", 
    "InvalidationEvent",
    "invalidate_user_cache",
    "invalidate_book_cache", 
    "invalidate_analytics_cache",
    
    # CDN Support
    "CDNAssetManager",
    "get_cdn_manager",
    "asset_url",
    "preload_assets",
    
    # Supabase Caching
    "SupabaseCacheManager",
    "get_supabase_cache_manager",
    "cached_supabase_query"
]
