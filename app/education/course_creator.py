### app/education/course_creator.py - Course Creator

from typing import Dict, Any, List
from pydantic import BaseModel
from app.schemas.book import Manuscript


class CourseModule(BaseModel):
    title: str
    content: str
    duration_minutes: int
    exercises: List[Dict[str, Any]]


class GeneratedCourse(BaseModel):
    title: str
    modules: List[CourseModule]
    total_duration: int
    difficulty_level: str


class CourseCreator:
    """Transform books into complete online courses"""
    
    async def create_course_from_book(
        self, 
        manuscript: Manuscript
    ) -> GeneratedCourse:
        """Generate a complete course curriculum"""
        
        # Analyze content for course structure
        course_structure = await self._analyze_course_potential(manuscript)
        
        # Create course modules
        course_modules = []
        
        for chapter in manuscript.chapters:
            # Break chapter into lessons
            lessons = await self._create_lessons_from_chapter(chapter)
            
            # Generate assignments
            assignments = await self._create_assignments(chapter)
            
            # Create assessments
            assessments = await self._create_assessments(chapter)
            
            # Generate video scripts
            video_scripts = await self._create_video_scripts(chapter)
            
            course_module = CourseModule(
                title=chapter.title,
                lessons=lessons,
                assignments=assignments,
                assessments=assessments,
                video_scripts=video_scripts,
                estimated_completion_hours=self._estimate_module_time(lessons, assignments)
            )
            
            course_modules.append(course_module)
        
        # Create course materials
        course_materials = await self._create_course_materials(manuscript, course_modules)
        
        return GeneratedCourse(
            source_manuscript=manuscript,
            course_modules=course_modules,
            course_materials=course_materials,
            total_course_hours=self._calculate_total_hours(course_modules),
            difficulty_level=self._assess_difficulty_level(course_modules),
            target_audience=await self._identify_target_audience(manuscript)
        )