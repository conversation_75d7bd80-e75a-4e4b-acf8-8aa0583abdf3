# app/utils/circuit_breakers.py - Circuit Breakers for External Services

import asyncio
import time
import logging
from typing import Any, Callable, Optional, Dict, List
from functools import wraps
from circuitbreaker import circuit
import httpx
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception

logger = get_logger(__name__)

class ServiceTimeoutError(Exception):
    """Custom exception for service timeouts"""
    pass

class ServiceUnavailableError(Exception):
    """Custom exception for service unavailability"""
    pass

# Circuit breaker configurations for different services
CIRCUIT_BREAKER_CONFIGS = {
    'openai': {
        'failure_threshold': 5,
        'recovery_timeout': 60,
        'expected_exception': (httpx.HTTPError, ServiceTimeoutError, ServiceUnavailableError)
    },
    'anthropic': {
        'failure_threshold': 5,
        'recovery_timeout': 60,
        'expected_exception': (httpx.HTTPError, ServiceTimeoutError, ServiceUnavailableError)
    },
    'supabase': {
        'failure_threshold': 3,
        'recovery_timeout': 30,
        'expected_exception': (httpx.HTTPError, ServiceTimeoutError)
    },
    'redis': {
        'failure_threshold': 3,
        'recovery_timeout': 30,
        'expected_exception': (ConnectionError, TimeoutError)
    }
}

class CircuitBreakerManager:
    """Manages circuit breakers for different services"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, Any] = {}
        self.service_stats: Dict[str, Dict[str, Any]] = {}
        self._initialize_circuit_breakers()
    
    def _initialize_circuit_breakers(self):
        """Initialize circuit breakers for all services"""
        for service_name, config in CIRCUIT_BREAKER_CONFIGS.items():
            self.circuit_breakers[service_name] = circuit(
                failure_threshold=config['failure_threshold'],
                recovery_timeout=config['recovery_timeout'],
                expected_exception=config['expected_exception']
            )
            
            self.service_stats[service_name] = {
                'total_calls': 0,
                'failed_calls': 0,
                'last_failure': None,
                'circuit_open': False
            }
    
    def get_circuit_breaker(self, service_name: str):
        """Get circuit breaker for a service"""
        if service_name not in self.circuit_breakers:
            logger.warning(f"Circuit breaker not found for service: {service_name}")
            return None
        return self.circuit_breakers[service_name]
    
    def record_call(self, service_name: str, success: bool, error: Optional[Exception] = None):
        """Record service call statistics"""
        if service_name in self.service_stats:
            stats = self.service_stats[service_name]
            stats['total_calls'] += 1
            
            if not success:
                stats['failed_calls'] += 1
                stats['last_failure'] = time.time()
                
                if error:
                    capture_exception(error, {
                        'service': service_name,
                        'circuit_breaker_stats': stats
                    })
    
    def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """Get health status for a service"""
        if service_name not in self.service_stats:
            return {'status': 'unknown'}
        
        stats = self.service_stats[service_name]
        failure_rate = stats['failed_calls'] / max(stats['total_calls'], 1)
        
        return {
            'status': 'healthy' if failure_rate < 0.1 else 'degraded' if failure_rate < 0.5 else 'unhealthy',
            'total_calls': stats['total_calls'],
            'failed_calls': stats['failed_calls'],
            'failure_rate': failure_rate,
            'last_failure': stats['last_failure'],
            'circuit_open': stats['circuit_open']
        }

# Global circuit breaker manager
circuit_manager = CircuitBreakerManager()

# Specific circuit breakers for AI services
@circuit(failure_threshold=5, recovery_timeout=60, expected_exception=(Exception,))
async def call_openai_api(prompt: str, model: str = "gpt-3.5-turbo", **kwargs) -> Dict[str, Any]:
    """Circuit breaker wrapper for OpenAI API calls"""
    if not settings.openai_api_key:
        raise ServiceUnavailableError("OpenAI API key not configured")
    
    start_time = time.time()
    
    try:
        # Import OpenAI here to avoid circular imports
        from openai import AsyncOpenAI
        
        client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            timeout=httpx.Timeout(60.0)  # 60 second timeout
        )
        
        response = await client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            **kwargs
        )
        
        duration = time.time() - start_time
        circuit_manager.record_call('openai', True)
        
        logger.info(f"OpenAI API call successful", extra={
            'model': model,
            'duration': duration,
            'tokens_used': response.usage.total_tokens if response.usage else 0
        })
        
        return {
            'content': response.choices[0].message.content,
            'model': model,
            'tokens_used': response.usage.total_tokens if response.usage else 0,
            'duration': duration
        }
        
    except httpx.TimeoutException as e:
        error = ServiceTimeoutError(f"OpenAI API timeout: {e}")
        circuit_manager.record_call('openai', False, error)
        raise error
    except Exception as e:
        circuit_manager.record_call('openai', False, e)
        logger.error(f"OpenAI API call failed: {e}")
        raise

@circuit(failure_threshold=5, recovery_timeout=60, expected_exception=(Exception,))
async def call_anthropic_api(prompt: str, model: str = "claude-3-sonnet-20240229", **kwargs) -> Dict[str, Any]:
    """Circuit breaker wrapper for Anthropic API calls"""
    if not settings.anthropic_api_key:
        raise ServiceUnavailableError("Anthropic API key not configured")
    
    start_time = time.time()
    
    try:
        # Import Anthropic here to avoid circular imports
        from anthropic import AsyncAnthropic
        
        client = AsyncAnthropic(
            api_key=settings.anthropic_api_key,
            timeout=httpx.Timeout(60.0)  # 60 second timeout
        )
        
        response = await client.messages.create(
            model=model,
            max_tokens=kwargs.get('max_tokens', 1000),
            messages=[{"role": "user", "content": prompt}],
            **{k: v for k, v in kwargs.items() if k != 'max_tokens'}
        )
        
        duration = time.time() - start_time
        circuit_manager.record_call('anthropic', True)
        
        logger.info(f"Anthropic API call successful", extra={
            'model': model,
            'duration': duration,
            'tokens_used': response.usage.input_tokens + response.usage.output_tokens
        })
        
        return {
            'content': response.content[0].text,
            'model': model,
            'tokens_used': response.usage.input_tokens + response.usage.output_tokens,
            'duration': duration
        }
        
    except httpx.TimeoutException as e:
        error = ServiceTimeoutError(f"Anthropic API timeout: {e}")
        circuit_manager.record_call('anthropic', False, error)
        raise error
    except Exception as e:
        circuit_manager.record_call('anthropic', False, e)
        logger.error(f"Anthropic API call failed: {e}")
        raise

@circuit(failure_threshold=3, recovery_timeout=30, expected_exception=(Exception,))
async def call_supabase_api(operation: str, table: str, data: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
    """Circuit breaker wrapper for Supabase API calls"""
    start_time = time.time()

    try:
        from app.utils.supabase.supabase_client import supabase_client

        if operation == 'select':
            query = supabase_client.table(table).select(kwargs.get('select', '*'))
            if kwargs.get('eq'):
                for key, value in kwargs['eq'].items():
                    query = query.eq(key, value)
            result = query.execute()
        elif operation == 'insert':
            result = supabase_client.table(table).insert(data).execute()
        elif operation == 'update':
            query = supabase_client.table(table).update(data)
            if kwargs.get('eq'):
                for key, value in kwargs['eq'].items():
                    query = query.eq(key, value)
            result = query.execute()
        elif operation == 'delete':
            query = supabase_client.table(table)
            if kwargs.get('eq'):
                for key, value in kwargs['eq'].items():
                    query = query.eq(key, value)
            result = query.delete().execute()
        else:
            raise ValueError(f"Unsupported operation: {operation}")

        duration = time.time() - start_time
        circuit_manager.record_call('supabase', True)

        logger.debug(f"Supabase {operation} operation successful", extra={
            'table': table,
            'operation': operation,
            'duration': duration
        })

        return result.data

    except Exception as e:
        duration = time.time() - start_time
        circuit_manager.record_call('supabase', False, e)
        logger.error(f"Supabase {operation} operation failed: {e}")
        raise

# Fallback strategies
class FallbackStrategy:
    """Base class for fallback strategies"""
    
    async def execute(self, *args, **kwargs) -> Any:
        raise NotImplementedError

class CachedResponseFallback(FallbackStrategy):
    """Fallback to cached responses"""
    
    def __init__(self, cache_duration: int = 3600):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_duration = cache_duration
    
    async def execute(self, key: str, default_response: Any = None) -> Any:
        current_time = time.time()
        
        if key in self.cache:
            cached_item = self.cache[key]
            if current_time - cached_item['timestamp'] < self.cache_duration:
                logger.info(f"Using cached fallback response for key: {key}")
                return cached_item['data']
        
        return default_response
    
    def cache_response(self, key: str, data: Any):
        """Cache a successful response"""
        self.cache[key] = {
            'data': data,
            'timestamp': time.time()
        }

class MockResponseFallback(FallbackStrategy):
    """Fallback to mock responses for testing/degraded mode"""
    
    MOCK_RESPONSES = {
        'openai_chat': {
            'content': 'This is a mock response generated due to service unavailability.',
            'model': 'mock-model',
            'tokens_used': 0,
            'duration': 0.1
        },
        'anthropic_chat': {
            'content': 'This is a mock response generated due to service unavailability.',
            'model': 'mock-model',
            'tokens_used': 0,
            'duration': 0.1
        }
    }
    
    async def execute(self, response_type: str) -> Any:
        logger.warning(f"Using mock fallback response for: {response_type}")
        return self.MOCK_RESPONSES.get(response_type, {'error': 'No mock response available'})

# Global fallback instances
cached_fallback = CachedResponseFallback()
mock_fallback = MockResponseFallback()

# Helper function to call AI APIs with fallbacks
async def call_ai_api_with_fallback(
    provider: str, 
    prompt: str, 
    model: Optional[str] = None,
    use_fallback: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """Call AI API with automatic fallback handling"""
    
    cache_key = f"{provider}:{hash(prompt)}"
    
    try:
        if provider == 'openai':
            result = await call_openai_api(prompt, model or "gpt-3.5-turbo", **kwargs)
            if use_fallback:
                cached_fallback.cache_response(cache_key, result)
            return result
        elif provider == 'anthropic':
            result = await call_anthropic_api(prompt, model or "claude-3-sonnet-20240229", **kwargs)
            if use_fallback:
                cached_fallback.cache_response(cache_key, result)
            return result
        else:
            raise ValueError(f"Unsupported AI provider: {provider}")
    
    except Exception as e:
        logger.error(f"AI API call failed for {provider}: {e}")
        
        if not use_fallback:
            raise
        
        # Try cached fallback first
        cached_result = await cached_fallback.execute(cache_key)
        if cached_result:
            return cached_result
        
        # Fall back to mock response
        mock_key = f"{provider}_chat"
        mock_result = await mock_fallback.execute(mock_key)
        mock_result['fallback_used'] = True
        mock_result['original_error'] = str(e)
        
        return mock_result

# Health check functions
async def check_service_health() -> Dict[str, Any]:
    """Check health of all external services"""
    health_status = {}
    
    for service_name in CIRCUIT_BREAKER_CONFIGS.keys():
        health_status[service_name] = circuit_manager.get_service_health(service_name)
    
    return health_status

# Retry decorator with exponential backoff
def retry_with_backoff(max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
    """Decorator for retrying operations with exponential backoff"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                    
                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
            
            # All retries exhausted
            logger.error(f"All {max_retries} retries exhausted for {func.__name__}")
            raise last_exception
        
        return wrapper
    return decorator
