# app/utils/file_security.py - Comprehensive File Upload Security

import os
import mimetypes
import hashlib
import magic
import tempfile
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging
from PIL import Image
import zipfile
import tarfile
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception

logger = get_logger(__name__)

class FileSecurityError(Exception):
    """Custom exception for file security violations"""
    pass

class FileSizeError(FileSecurityError):
    """Exception for file size violations"""
    pass

class FileTypeError(FileSecurityError):
    """Exception for file type violations"""
    pass

class MaliciousFileError(FileSecurityError):
    """Exception for potentially malicious files"""
    pass

class FileSecurityValidator:
    """Comprehensive file security validation"""
    
    # Allowed file types and their properties
    ALLOWED_FILE_TYPES = {
        'image': {
            'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
            'mime_types': [
                'image/jpeg', 'image/png', 'image/gif', 
                'image/webp', 'image/bmp'
            ],
            'max_size': 10 * 1024 * 1024,  # 10MB
            'magic_numbers': {
                b'\xff\xd8\xff': 'jpeg',
                b'\x89PNG\r\n\x1a\n': 'png',
                b'GIF87a': 'gif',
                b'GIF89a': 'gif',
                b'RIFF': 'webp',  # Partial signature
                b'BM': 'bmp'
            }
        },
        'document': {
            'extensions': ['.pdf', '.txt', '.docx', '.csv'],
            'mime_types': [
                'application/pdf', 'text/plain', 'text/csv',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ],
            'max_size': 50 * 1024 * 1024,  # 50MB
            'magic_numbers': {
                b'%PDF': 'pdf',
                b'PK\x03\x04': 'docx',  # ZIP-based format
            }
        },
        'audio': {
            'extensions': ['.mp3', '.wav', '.ogg', '.m4a'],
            'mime_types': [
                'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'
            ],
            'max_size': 100 * 1024 * 1024,  # 100MB
            'magic_numbers': {
                b'ID3': 'mp3',
                b'RIFF': 'wav',  # Partial signature
                b'OggS': 'ogg'
            }
        }
    }
    
    # Dangerous file extensions to always block
    DANGEROUS_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.sh', '.ps1', '.php', '.asp', '.jsp', '.py', '.pl',
        '.rb', '.go', '.rs', '.cpp', '.c', '.h', '.dll', '.so'
    ]
    
    # Maximum filename length
    MAX_FILENAME_LENGTH = 255
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def validate_file(self, file_path: str, allowed_categories: List[str] = None) -> Dict[str, Any]:
        """
        Comprehensive file validation
        
        Args:
            file_path: Path to the file to validate
            allowed_categories: List of allowed categories (image, document, audio)
        
        Returns:
            Dict with validation results
        
        Raises:
            FileSecurityError: If file fails security validation
        """
        if not os.path.exists(file_path):
            raise FileSecurityError(f"File not found: {file_path}")
        
        file_info = {
            'path': file_path,
            'name': os.path.basename(file_path),
            'size': os.path.getsize(file_path),
            'category': None,
            'validated': False
        }
        
        try:
            # Basic validation
            self._validate_filename(file_info['name'])
            self._validate_file_size(file_path, file_info['size'])
            
            # Determine file category
            file_info['category'] = self._determine_file_category(file_path)
            
            # Check if category is allowed
            if allowed_categories and file_info['category'] not in allowed_categories:
                raise FileTypeError(f"File category not allowed: {file_info['category']}")
            
            # Content validation
            self._validate_file_content(file_path, file_info['category'])
            
            # Magic number validation
            self._validate_magic_numbers(file_path, file_info['category'])
            
            # Category-specific validation
            if file_info['category'] == 'image':
                self._validate_image_file(file_path)
            elif file_info['category'] == 'document':
                self._validate_document_file(file_path)
            elif file_info['category'] == 'audio':
                self._validate_audio_file(file_path)
            
            # Malware-like pattern detection
            self._scan_for_malicious_patterns(file_path)
            
            file_info['validated'] = True
            
            self.logger.info(f"File validation successful: {file_info['name']}")
            return file_info
            
        except Exception as e:
            self.logger.error(f"File validation failed: {e}")
            capture_exception(e, {'file_path': file_path, 'file_info': file_info})
            raise
    
    def _validate_filename(self, filename: str) -> None:
        """Validate filename for security issues"""
        if not filename:
            raise FileSecurityError("Filename cannot be empty")
        
        if len(filename) > self.MAX_FILENAME_LENGTH:
            raise FileSecurityError(f"Filename too long: {len(filename)} > {self.MAX_FILENAME_LENGTH}")
        
        # Check for path traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            raise FileSecurityError("Filename contains path traversal characters")
        
        # Check for dangerous extensions
        file_ext = Path(filename).suffix.lower()
        if file_ext in self.DANGEROUS_EXTENSIONS:
            raise FileTypeError(f"Dangerous file extension: {file_ext}")
        
        # Check for hidden files (starting with .)
        if filename.startswith('.'):
            raise FileSecurityError("Hidden files not allowed")
        
        # Check for reserved names (Windows)
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
        name_without_ext = Path(filename).stem.upper()
        if name_without_ext in reserved_names:
            raise FileSecurityError(f"Reserved filename: {filename}")
        
        # Check for null bytes
        if '\x00' in filename:
            raise FileSecurityError("Null bytes in filename")
    
    def _validate_file_size(self, file_path: str, file_size: int) -> None:
        """Validate file size"""
        if file_size == 0:
            raise FileSizeError("Empty file not allowed")
        
        # Maximum file size (100MB global limit)
        max_global_size = 100 * 1024 * 1024
        if file_size > max_global_size:
            raise FileSizeError(f"File too large: {file_size} > {max_global_size}")
    
    def _determine_file_category(self, file_path: str) -> str:
        """Determine file category based on extension and MIME type"""
        file_ext = Path(file_path).suffix.lower()
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(file_path)
        
        for category, config in self.ALLOWED_FILE_TYPES.items():
            if file_ext in config['extensions'] or mime_type in config['mime_types']:
                return category
        
        raise FileTypeError(f"Unsupported file type: {file_ext} (MIME: {mime_type})")
    
    def _validate_file_content(self, file_path: str, category: str) -> None:
        """Validate file content using python-magic"""
        try:
            # Use python-magic for more accurate MIME type detection
            file_mime = magic.from_file(file_path, mime=True)
            
            category_config = self.ALLOWED_FILE_TYPES[category]
            if file_mime not in category_config['mime_types']:
                # Some flexibility for common variations
                if not self._is_mime_type_compatible(file_mime, category_config['mime_types']):
                    raise FileTypeError(f"File content doesn't match extension. Detected: {file_mime}")
            
        except Exception as e:
            self.logger.warning(f"Could not validate MIME type: {e}")
            # Continue without MIME validation if python-magic is not available
    
    def _is_mime_type_compatible(self, detected_mime: str, allowed_mimes: List[str]) -> bool:
        """Check if detected MIME type is compatible with allowed types"""
        # Handle common MIME type variations
        compatibility_map = {
            'text/plain': ['text/csv', 'application/csv'],
            'application/zip': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'image/jpg': ['image/jpeg'],
        }
        
        for allowed_mime in allowed_mimes:
            if detected_mime == allowed_mime:
                return True
            
            # Check compatibility map
            if allowed_mime in compatibility_map:
                if detected_mime in compatibility_map[allowed_mime]:
                    return True
        
        return False
    
    def _validate_magic_numbers(self, file_path: str, category: str) -> None:
        """Validate file magic numbers (file signatures)"""
        try:
            with open(file_path, 'rb') as f:
                file_header = f.read(20)  # Read first 20 bytes
            
            category_config = self.ALLOWED_FILE_TYPES[category]
            magic_numbers = category_config.get('magic_numbers', {})
            
            if not magic_numbers:
                return  # No magic number validation for this category
            
            # Check if any magic number matches
            for magic_bytes, file_type in magic_numbers.items():
                if file_header.startswith(magic_bytes):
                    return  # Valid magic number found
            
            # Special handling for some formats
            if category == 'image' and file_header.startswith(b'RIFF') and b'WEBP' in file_header[:20]:
                return  # Valid WebP
            
            if category == 'document' and file_header.startswith(b'PK'):
                # Could be DOCX (ZIP-based)
                return
            
            self.logger.warning(f"Magic number validation failed for {file_path}")
            
        except Exception as e:
            self.logger.warning(f"Could not validate magic numbers: {e}")
    
    def _validate_image_file(self, file_path: str) -> None:
        """Validate image files using PIL"""
        try:
            with Image.open(file_path) as img:
                # Verify image can be opened
                img.verify()
                
                # Check image dimensions (reasonable limits)
                if hasattr(img, 'size'):
                    width, height = img.size
                    max_dimension = 10000  # 10000px max
                    
                    if width > max_dimension or height > max_dimension:
                        raise FileSecurityError(f"Image dimensions too large: {width}x{height}")
                    
                    if width * height > 100_000_000:  # 100MP max
                        raise FileSecurityError("Image resolution too high")
        
        except Image.UnidentifiedImageError:
            raise FileTypeError("Invalid or corrupted image file")
        except Exception as e:
            if isinstance(e, FileSecurityError):
                raise
            raise FileTypeError(f"Image validation failed: {e}")
    
    def _validate_document_file(self, file_path: str) -> None:
        """Validate document files"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.pdf':
            self._validate_pdf_file(file_path)
        elif file_ext == '.docx':
            self._validate_docx_file(file_path)
        elif file_ext in ['.txt', '.csv']:
            self._validate_text_file(file_path)
    
    def _validate_pdf_file(self, file_path: str) -> None:
        """Basic PDF validation"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(10)
                if not header.startswith(b'%PDF-'):
                    raise FileTypeError("Invalid PDF file")
                
                # Check for PDF version
                pdf_version = header[5:8]
                if not pdf_version.startswith(b'1.'):
                    self.logger.warning(f"Unusual PDF version: {pdf_version}")
        
        except Exception as e:
            if isinstance(e, FileTypeError):
                raise
            raise FileTypeError(f"PDF validation failed: {e}")
    
    def _validate_docx_file(self, file_path: str) -> None:
        """Validate DOCX files (ZIP-based)"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # Check for required DOCX structure
                required_files = ['[Content_Types].xml', 'word/document.xml']
                zip_names = zip_file.namelist()
                
                for required_file in required_files:
                    if required_file not in zip_names:
                        raise FileTypeError(f"Invalid DOCX structure: missing {required_file}")
                
                # Check for suspicious files in ZIP
                for name in zip_names:
                    if name.endswith(('.exe', '.bat', '.cmd', '.sh')):
                        raise MaliciousFileError(f"Suspicious file in DOCX: {name}")
        
        except zipfile.BadZipFile:
            raise FileTypeError("Invalid or corrupted DOCX file")
        except Exception as e:
            if isinstance(e, (FileTypeError, MaliciousFileError)):
                raise
            raise FileTypeError(f"DOCX validation failed: {e}")
    
    def _validate_text_file(self, file_path: str) -> None:
        """Validate text files"""
        try:
            # Try to read as UTF-8
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1024)  # Read first 1KB
                
                # Check for null bytes (binary content)
                if '\x00' in content:
                    raise FileTypeError("Text file contains binary data")
        
        except UnicodeDecodeError:
            # Try other common encodings
            for encoding in ['latin-1', 'cp1252']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1024)
                    return  # Successfully read with alternative encoding
                except UnicodeDecodeError:
                    continue
            
            raise FileTypeError("Text file encoding not supported")
        except Exception as e:
            raise FileTypeError(f"Text file validation failed: {e}")
    
    def _validate_audio_file(self, file_path: str) -> None:
        """Basic audio file validation"""
        # This is a placeholder for audio validation
        # In production, you might want to use libraries like mutagen
        # to validate audio file structure
        pass
    
    def _scan_for_malicious_patterns(self, file_path: str) -> None:
        """Scan file for malicious patterns"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read(10240)  # Read first 10KB
            
            # Look for suspicious patterns
            malicious_patterns = [
                b'<script',
                b'javascript:',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec(',
                b'passthru(',
                b'file_get_contents(',
                b'<?php',
                b'<%',
                b'<iframe',
                b'<object',
                b'<embed'
            ]
            
            for pattern in malicious_patterns:
                if pattern in content.lower():
                    raise MaliciousFileError(f"Suspicious pattern detected: {pattern.decode('utf-8', errors='ignore')}")
        
        except Exception as e:
            if isinstance(e, MaliciousFileError):
                raise
            self.logger.warning(f"Could not scan for malicious patterns: {e}")
    
    def generate_secure_filename(self, original_filename: str) -> str:
        """Generate a secure filename"""
        # Get file extension
        file_ext = Path(original_filename).suffix.lower()
        
        # Generate secure filename using hash
        timestamp = str(int(time.time()))
        hash_input = f"{original_filename}{timestamp}".encode('utf-8')
        file_hash = hashlib.sha256(hash_input).hexdigest()[:16]
        
        return f"upload_{file_hash}{file_ext}"
    
    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()

# Global validator instance
file_validator = FileSecurityValidator()

# Convenience functions
def validate_upload_file(file_path: str, allowed_categories: List[str] = None) -> Dict[str, Any]:
    """Validate uploaded file"""
    return file_validator.validate_file(file_path, allowed_categories)

def secure_file_upload(file_data: bytes, original_filename: str, 
                      upload_dir: str, allowed_categories: List[str] = None) -> Dict[str, Any]:
    """Securely handle file upload"""
    # Generate secure filename
    secure_filename = file_validator.generate_secure_filename(original_filename)
    
    # Create upload directory if it doesn't exist
    os.makedirs(upload_dir, exist_ok=True)
    
    # Write file to temporary location first
    temp_path = os.path.join(upload_dir, f"temp_{secure_filename}")
    
    try:
        with open(temp_path, 'wb') as f:
            f.write(file_data)
        
        # Validate the file
        validation_result = validate_upload_file(temp_path, allowed_categories)
        
        # If validation passes, move to final location
        final_path = os.path.join(upload_dir, secure_filename)
        os.rename(temp_path, final_path)
        
        # Calculate file hash
        file_hash = file_validator.calculate_file_hash(final_path)
        
        return {
            'success': True,
            'filename': secure_filename,
            'path': final_path,
            'hash': file_hash,
            'validation': validation_result
        }
    
    except Exception as e:
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        logger.error(f"File upload failed: {e}")
        raise