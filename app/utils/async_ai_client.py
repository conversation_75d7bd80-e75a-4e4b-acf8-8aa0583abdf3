# app/utils/async_ai_client.py - Async AI Client with Concurrency Control

import asyncio
import time
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from dataclasses import dataclass
import openai
import anthropic
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception
from app.utils.circuit_breakers import call_ai_api_with_fallback

logger = get_logger(__name__)

@dataclass
class AIClientConfig:
    """Configuration for AI client"""
    max_concurrent_requests: int = 5
    request_timeout: float = 60.0
    max_retries: int = 3
    rate_limit_per_minute: int = 60
    enable_fallbacks: bool = True

class ConcurrencyManager:
    """Manages concurrency limits for AI API calls"""
    
    def __init__(self, max_concurrent: int = 5):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_requests = 0
        self.total_requests = 0
        self.rate_limiter = RateLimiter(requests_per_minute=60)
        
    @asynccontextmanager
    async def acquire(self):
        """Acquire concurrency slot"""
        await self.rate_limiter.acquire()
        async with self.semaphore:
            self.active_requests += 1
            self.total_requests += 1
            try:
                yield
            finally:
                self.active_requests -= 1

class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests_per_second = requests_per_minute / 60.0
        self.bucket_size = min(10, requests_per_minute)  # Burst capacity
        self.tokens = self.bucket_size
        self.last_update = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire a rate limit token"""
        async with self.lock:
            now = time.time()
            elapsed = now - self.last_update
            
            # Add tokens based on elapsed time
            self.tokens = min(
                self.bucket_size,
                self.tokens + elapsed * self.requests_per_second
            )
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return
            
            # Wait for next token
            wait_time = (1 - self.tokens) / self.requests_per_second
            await asyncio.sleep(wait_time)
            self.tokens = 0

class AsyncAIClient:
    """Async AI client with concurrency control and fallbacks"""
    
    def __init__(self, config: Optional[AIClientConfig] = None):
        self.config = config or AIClientConfig()
        self.concurrency_manager = ConcurrencyManager(self.config.max_concurrent_requests)
        
        # Initialize clients
        self.openai_client = None
        self.anthropic_client = None
        self._initialize_clients()
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'provider_usage': {'openai': 0, 'anthropic': 0}
        }
    
    def _initialize_clients(self):
        """Initialize AI provider clients"""
        if settings.openai_api_key:
            self.openai_client = openai.AsyncOpenAI(
                api_key=settings.openai_api_key,
                timeout=self.config.request_timeout
            )
        
        if settings.anthropic_api_key:
            self.anthropic_client = anthropic.AsyncAnthropic(
                api_key=settings.anthropic_api_key,
                timeout=self.config.request_timeout
            )
    
    async def chat_completion(
        self,
        provider: str,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Async chat completion with concurrency control
        
        Args:
            provider: 'openai' or 'anthropic'
            messages: List of message dicts
            model: Model name (optional)
            **kwargs: Additional parameters
        
        Returns:
            Response dict with content, usage, and metadata
        """
        async with self.concurrency_manager.acquire():
            start_time = time.time()
            
            try:
                with monitor_operation(f"ai_chat_{provider}",
                                           provider=provider,
                                           model=model,
                                           message_count=len(messages)):
                    
                    if provider == 'openai':
                        result = await self._openai_chat_completion(messages, model, **kwargs)
                    elif provider == 'anthropic':
                        result = await self._anthropic_chat_completion(messages, model, **kwargs)
                    else:
                        raise ValueError(f"Unsupported provider: {provider}")
                    
                    # Update statistics
                    duration = time.time() - start_time
                    self._update_stats(provider, duration, success=True)
                    
                    return result
            
            except Exception as e:
                duration = time.time() - start_time
                self._update_stats(provider, duration, success=False)
                
                logger.error(f"AI API call failed: {provider} - {e}")
                capture_exception(e, {
                    'provider': provider,
                    'model': model,
                    'message_count': len(messages),
                    'duration': duration
                })
                
                # Use fallback if enabled
                if self.config.enable_fallbacks:
                    logger.info(f"Using fallback for {provider}")
                    return await self._fallback_response(provider, messages)
                
                raise
    
    async def _openai_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """OpenAI chat completion"""
        if not self.openai_client:
            raise RuntimeError("OpenAI client not initialized")
        
        model = model or "gpt-3.5-turbo"
        
        response = await self.openai_client.chat.completions.create(
            model=model,
            messages=messages,
            **kwargs
        )
        
        return {
            'content': response.choices[0].message.content,
            'model': model,
            'provider': 'openai',
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                'total_tokens': response.usage.total_tokens if response.usage else 0
            },
            'finish_reason': response.choices[0].finish_reason
        }
    
    async def _anthropic_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Anthropic chat completion"""
        if not self.anthropic_client:
            raise RuntimeError("Anthropic client not initialized")
        
        model = model or "claude-3-sonnet-20240229"
        
        response = await self.anthropic_client.messages.create(
            model=model,
            messages=messages,
            max_tokens=kwargs.get('max_tokens', 1000),
            **{k: v for k, v in kwargs.items() if k != 'max_tokens'}
        )
        
        return {
            'content': response.content[0].text,
            'model': model,
            'provider': 'anthropic',
            'usage': {
                'prompt_tokens': response.usage.input_tokens,
                'completion_tokens': response.usage.output_tokens,
                'total_tokens': response.usage.input_tokens + response.usage.output_tokens
            },
            'finish_reason': 'stop'  # Anthropic doesn't provide finish_reason
        }
    
    async def _fallback_response(
        self,
        provider: str,
        messages: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """Generate fallback response when API fails"""
        return {
            'content': f"This is a fallback response generated due to {provider} API unavailability. The original request has been logged for retry.",
            'model': 'fallback',
            'provider': provider,
            'usage': {'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
            'finish_reason': 'fallback',
            'fallback_used': True
        }
    
    def _update_stats(self, provider: str, duration: float, success: bool):
        """Update client statistics"""
        self.stats['total_requests'] += 1
        self.stats['provider_usage'][provider] += 1
        
        if success:
            self.stats['successful_requests'] += 1
        else:
            self.stats['failed_requests'] += 1
        
        # Update average response time
        total_successful = self.stats['successful_requests']
        if total_successful > 0:
            current_avg = self.stats['average_response_time']
            self.stats['average_response_time'] = (
                (current_avg * (total_successful - 1) + duration) / total_successful
            )
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get client statistics"""
        return {
            **self.stats,
            'active_requests': self.concurrency_manager.active_requests,
            'success_rate': (
                self.stats['successful_requests'] / max(self.stats['total_requests'], 1)
            ),
            'config': {
                'max_concurrent': self.config.max_concurrent_requests,
                'timeout': self.config.request_timeout,
                'rate_limit': self.config.rate_limit_per_minute
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on AI providers"""
        health_status = {
            'openai': {'available': False, 'response_time': None},
            'anthropic': {'available': False, 'response_time': None}
        }
        
        # Test OpenAI
        if self.openai_client:
            try:
                start_time = time.time()
                await self.chat_completion(
                    'openai',
                    [{'role': 'user', 'content': 'Health check'}],
                    model='gpt-3.5-turbo',
                    max_tokens=1
                )
                health_status['openai'] = {
                    'available': True,
                    'response_time': time.time() - start_time
                }
            except Exception as e:
                logger.warning(f"OpenAI health check failed: {e}")
        
        # Test Anthropic
        if self.anthropic_client:
            try:
                start_time = time.time()
                await self.chat_completion(
                    'anthropic',
                    [{'role': 'user', 'content': 'Health check'}],
                    model='claude-3-sonnet-20240229',
                    max_tokens=1
                )
                health_status['anthropic'] = {
                    'available': True,
                    'response_time': time.time() - start_time
                }
            except Exception as e:
                logger.warning(f"Anthropic health check failed: {e}")
        
        return health_status

# Global async AI client
async_ai_client = AsyncAIClient()

# Convenience functions
async def async_chat_completion(
    provider: str,
    messages: List[Dict[str, str]],
    model: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """Convenience function for async chat completion"""
    return await async_ai_client.chat_completion(provider, messages, model, **kwargs)

async def get_ai_client_stats() -> Dict[str, Any]:
    """Get AI client statistics"""
    return await async_ai_client.get_stats()

async def ai_health_check() -> Dict[str, Any]:
    """Perform AI providers health check"""
    return await async_ai_client.health_check()