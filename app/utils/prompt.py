from typing import Any, Dict
import re

class PromptTemplate:
    """
    Minimal jinja-like prompt formatter.
    Replaces {{ variable }} in a prompt string with given values.
    """

    def __init__(self, template: str):
        self.template = template

    @classmethod
    def from_string(cls, template: str) -> "PromptTemplate":
        return cls(template)

    def format(self, **kwargs: Any) -> str:
        def replacer(match):
            key = match.group(1).strip()
            return str(kwargs.get(key, f"{{{{ {key} }}}}"))  # fallback: leave untouched

        return re.sub(r"{{\s*(.*?)\s*}}", replacer, self.template)
