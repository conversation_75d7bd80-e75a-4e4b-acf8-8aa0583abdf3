# app/database/supabase_database.py - Refactored Supabase Database Connection

import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from contextlib import asynccontextmanager

from supabase import create_async_client, AsyncClient
from postgrest import APIError
from gotrue.errors import AuthError
import asyncpg  # type: ignore

from app.config import settings

logger = logging.getLogger(__name__)


class SupabaseDatabase:
    """Primary database connection using Supabase - replaces SQLAlchemy"""

    def __init__(self):
        self.client: Optional[AsyncClient] = None
        self.db_pool: Optional[asyncpg.pool.Pool] = None

    async def initialize(self):
        """Initialize Supabase client and optional PG pool."""
        try:
            if not settings.supabase_url or not settings.supabase_service_key:
                raise ValueError("Supabase URL and Service Key are required")

            self.client = await create_async_client(
                supabase_url=settings.supabase_url,
                supabase_key=settings.supabase_service_key,
            )
            logger.info(f"✅ Supabase client initialized: {settings.supabase_url}")
        except Exception as e:
            logger.exception("❌ Failed to initialize Supabase client")
            raise

        await self.initialize_pool()

    async def initialize_pool(self):
        try:
            if not settings.database_url:
                logger.warning(
                    "No direct database URL provided, using Supabase client only"
                )
                return

            self.db_pool = await asyncpg.create_pool(
                settings.database_url,
                min_size=2,
                max_size=20,
                command_timeout=60,
                server_settings={"jit": "off"},
                statement_cache_size=0,  # Disable prepared statements for pgbouncer compatibility
            )
            logger.info("✅ PostgreSQL connection pool initialized")
        except Exception as e:
            logger.exception("❌ Failed to initialize database pool")

    async def close_pool(self):
        if self.db_pool:
            await self.db_pool.close()
            logger.info("✅ Database connection pool closed")

    def get_client(self) -> AsyncClient:
        if not self.client:
            raise Exception("Supabase client not initialized")
        return self.client

    @asynccontextmanager
    async def get_connection(self):
        if not self.db_pool:
            yield self.client
        else:
            async with self.db_pool.acquire() as conn:
                yield conn

    async def health_check(self) -> Dict[str, Any]:
        direct_ok = False
        user_count = 0
        supabase_ok = False
        error_details = None
        
        # Test Supabase connection (primary)
        try:
            if self.client:
                result = await self.client.table("users").select("id").limit(1).execute()
                supabase_ok = True
                user_count = len(result.data) if result and result.data else 0
        except Exception as e:
            logger.warning(f"Supabase health check failed: {e}")
            error_details = f"Supabase: {str(e)}"

        # Test direct PostgreSQL connection (secondary)
        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    # Use simple query without prepared statements to avoid pgbouncer issues
                    result = await conn.fetch("SELECT 1 as health_check")
                    if result and len(result) > 0:
                        direct_ok = True
        except Exception as e:
            logger.warning(f"Direct DB health check failed: {e}")
            if error_details:
                error_details += f"; PostgreSQL: {str(e)}"
            else:
                error_details = f"PostgreSQL: {str(e)}"

        # Determine overall status
        is_healthy = supabase_ok or direct_ok
        
        result = {
            "status": "healthy" if is_healthy else "unhealthy",
            "supabase_connected": supabase_ok,
            "direct_connection": direct_ok,
            "user_count": user_count,
            "database_url": settings.supabase_url,
        }
        
        if error_details:
            result["error"] = error_details
            
        return result

    async def validate_schema(self) -> Dict[str, Any]:
        tables = [
            "users",
            "books",
            "publications",
            "sales_data",
            "feedback_metrics",
            "trend_analyses",
            "verl_training_jobs",
        ]
        existing, missing = [], []
        try:
            for table in tables:
                try:
                    if self.client:
                        await self.client.table(table).select("id").limit(1).execute()
                        existing.append(table)
                    else:
                        missing.append(table)
                except:
                    missing.append(table)
            return {
                "schema_valid": len(missing) == 0,
                "existing_tables": existing,
                "missing_tables": missing,
            }
        except Exception as e:
            logger.exception("Error validating schema")
            return {
                "schema_valid": False,
                "error": str(e),
                "existing_tables": [],
                "missing_tables": tables,
            }


# Global instance
db = SupabaseDatabase()


def get_supabase():
    """Get Supabase client instance for compatibility"""
    try:
        return db.get_client()
    except Exception:
        # Return None if not initialized, will be handled by caller
        return None


async def init_database():
    try:
        await db.initialize()
        validation = await db.validate_schema()
        if not validation["schema_valid"]:
            logger.warning(f"Missing tables: {validation['missing_tables']}")
            logger.info("Please run the Supabase schema migration script")
        logger.info("✅ Database initialization complete")
        return True
    except Exception as e:
        logger.exception("❌ Database initialization failed")
        return False


async def cleanup_database():
    try:
        await db.close_pool()
        logger.info("✅ Database cleanup complete")
    except Exception as e:
        logger.exception("❌ Database cleanup error")
