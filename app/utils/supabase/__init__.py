# app/utils/supabase/__init__.py
"""Supabase utilities package"""

from .supabase_client import get_supabase_client, SupabaseClient
from .supabase_database import SupabaseDatabase, get_supabase, init_database, cleanup_database

__all__ = [
    'get_supabase_client',
    'SupabaseClient', 
    'SupabaseDatabase',
    'get_supabase',
    'init_database',
    'cleanup_database'
]

# Lazy import to avoid circular dependency
def get_supabase_cache():
    from .supabase_cache import SupabaseCache
    return SupabaseCache