# app/utils/supabase/supabase_client.py - Supabase Client Integration with Resilience

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime
from postgrest.types import CountMethod
from contextlib import asynccontextmanager
from functools import wraps

from supabase import create_client, Client
import asyncpg  # type: ignore

from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception

logger = get_logger(__name__)

class SupabaseConnectionError(Exception):
    """Custom exception for Supabase connection issues"""
    pass

class SupabaseFallbackMode:
    """Fallback mode when Supabase is unavailable"""
    
    def __init__(self):
        self.enabled = False
        self.last_attempt = 0
        self.retry_interval = 30  # seconds
        
    def activate(self):
        self.enabled = True
        self.last_attempt = time.time()
        logger.warning("Supabase fallback mode activated")
        
    def should_retry(self):
        return time.time() - self.last_attempt > self.retry_interval
        
    def deactivate(self):
        self.enabled = False
        logger.info("Supabase fallback mode deactivated")

def circuit_breaker(max_failures=3, timeout=60):
    """Simple circuit breaker decorator for database operations"""
    def decorator(func):
        func._failures = 0
        func._last_failure = 0
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_time = time.time()
            
            # Check if circuit is open
            if func._failures >= max_failures:
                if current_time - func._last_failure < timeout:
                    raise SupabaseConnectionError(
                        f"Circuit breaker open for {func.__name__}"
                    )
                else:
                    # Reset circuit breaker
                    func._failures = 0
                    
            try:
                result = await func(*args, **kwargs)
                func._failures = 0  # Reset on success
                return result
            except Exception as e:
                func._failures += 1
                func._last_failure = current_time
                logger.error(f"Database operation failed: {func.__name__}: {e}")
                capture_exception(e, {
                    'function': func.__name__,
                    'failures': func._failures,
                    'circuit_open': func._failures >= max_failures
                })
                raise
                
        return wrapper
    return decorator


class SupabaseClient:
    """Enhanced Supabase client for the publishing platform with resilience"""

    def __init__(self):
        self.client: Optional[Client] = None
        self.db_pool: Optional[asyncpg.Pool] = None
        self.fallback_mode = SupabaseFallbackMode()
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self._pool_lock = asyncio.Lock()
        self._initialize_client()

    def _initialize_client(self):
        """Initialize Supabase client with retry logic"""
        for attempt in range(self.max_connection_attempts):
            try:
                self.client = create_client(
                    supabase_url=settings.supabase_url,
                    supabase_key=settings.supabase_service_key,
                )
                logger.info(f"✅ Supabase client initialized: {settings.supabase_url}")
                self.fallback_mode.deactivate()
                return
                
            except Exception as e:
                self.connection_attempts += 1
                logger.error(f"❌ Supabase client init attempt {attempt + 1} failed: {e}")
                
                if attempt == self.max_connection_attempts - 1:
                    # Last attempt failed, activate fallback mode
                    self.fallback_mode.activate()
                    capture_exception(e, {
                        'component': 'supabase_client',
                        'operation': 'initialize',
                        'attempt': attempt + 1,
                        'fallback_activated': True
                    })
                    logger.warning("⚠️ Supabase client initialization failed, operating in fallback mode")
                else:
                    # Wait before retry
                    import time
                    time.sleep(2 ** attempt)  # Exponential backoff

    @circuit_breaker(max_failures=3, timeout=60)
    async def initialize_db_pool(self):
        """Initialize direct database connection pool with resilience"""
        async with self._pool_lock:
            if self.db_pool is not None:
                return  # Already initialized
                
            try:
                db_url = settings.database_url
                self.db_pool = await asyncpg.create_pool(
                    db_url, 
                    min_size=2,        # Minimum connections
                    max_size=20,       # Maximum connections for all agents
                    max_queries=50000, # Queries per connection
                    max_inactive_connection_lifetime=300,  # 5 minutes
                    command_timeout=60,
                    server_settings={
                        'application_name': 'publish-ai-shared-pool',
                        'statement_cache_size': '0'  # Disable for pgbouncer compatibility
                    }
                )
                logger.info("✅ Database connection pool initialized (2-20 connections)")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize database pool: {e}")
                capture_exception(e, {
                    'component': 'supabase_client',
                    'operation': 'initialize_db_pool'
                })
                raise SupabaseConnectionError(f"Database pool initialization failed: {e}")

    @asynccontextmanager
    async def get_db_connection(self):
        """Get a database connection from the pool with proper error handling"""
        if self.fallback_mode.enabled:
            if not self.fallback_mode.should_retry():
                raise SupabaseConnectionError("Database unavailable - fallback mode active")
            # Try to reinitialize
            try:
                await self.initialize_db_pool()
                self.fallback_mode.deactivate()
            except Exception:
                raise SupabaseConnectionError("Database still unavailable")
        
        if self.db_pool is None:
            await self.initialize_db_pool()
            
        try:
            async with self.db_pool.acquire() as connection:
                yield connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            capture_exception(e, {'operation': 'get_db_connection'})
            raise SupabaseConnectionError(f"Database connection failed: {e}")

    def is_healthy(self) -> bool:
        """Check if the Supabase client is healthy"""
        return (
            self.client is not None and 
            not self.fallback_mode.enabled
        )

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health_status = {
            'client_initialized': self.client is not None,
            'pool_initialized': self.db_pool is not None,
            'fallback_mode': self.fallback_mode.enabled,
            'connection_attempts': self.connection_attempts,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if self.db_pool:
            try:
                async with self.get_db_connection() as conn:
                    result = await conn.fetchval('SELECT 1')
                    health_status['database_accessible'] = result == 1
                    health_status['pool_size'] = self.db_pool.get_size()
            except Exception as e:
                health_status['database_accessible'] = False
                health_status['error'] = str(e)
        
        return health_status

    async def close_db_pool(self):
        """Close database connection pool"""
        async with self._pool_lock:
            if self.db_pool:
                await self.db_pool.close()
                self.db_pool = None
                logger.info("✅ Database connection pool closed")


# Shared Supabase client singleton with thread safety
class SupabaseClientSingleton:
    """Thread-safe singleton for shared Supabase client"""
    
    _instance: Optional[SupabaseClient] = None
    _lock = asyncio.Lock()
    
    @classmethod
    async def get_instance(cls) -> SupabaseClient:
        """Get or create the singleton Supabase client instance"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:  # Double-checked locking
                    cls._instance = SupabaseClient()
                    # Initialize the connection pool immediately
                    try:
                        await cls._instance.initialize_db_pool()
                        logger.info("🔗 Shared Supabase client singleton initialized")
                    except Exception as e:
                        logger.warning(f"⚠️ Connection pool init failed, will retry on demand: {e}")
        
        return cls._instance
    
    @classmethod
    async def close_instance(cls):
        """Close the singleton instance and cleanup"""
        if cls._instance:
            await cls._instance.close_db_pool()
            cls._instance = None
            logger.info("🔒 Shared Supabase client singleton closed")

# Global instance for backward compatibility
_supabase_client_singleton = SupabaseClientSingleton()

def get_supabase_client() -> SupabaseClient:
    """Synchronous FastAPI dependency to get Supabase client"""
    # For FastAPI dependencies, we need to handle async initialization
    import asyncio
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    if loop.is_running():
        # If we're already in an async context, we can't await here
        # Return a temporary client for immediate use
        if _supabase_client_singleton._instance is None:
            _supabase_client_singleton._instance = SupabaseClient()
        return _supabase_client_singleton._instance
    else:
        # We can safely await
        return loop.run_until_complete(_supabase_client_singleton.get_instance())

async def get_supabase_client_async() -> SupabaseClient:
    """Async version for proper initialization"""
    return await _supabase_client_singleton.get_instance()

async def close_supabase_client():
    """Close the shared client (for cleanup)"""
    await _supabase_client_singleton.close_instance()

# For direct module-level access (backward compatibility)
supabase_client = None


async def initialize_supabase():
    """Initialize Supabase connections"""
    try:
        client = get_supabase_client()
        await client.initialize_db_pool()
    except Exception as e:
        logger.error(f"Failed to initialize Supabase: {e}")
        raise


async def cleanup_supabase():
    """Cleanup Supabase connections"""
    try:
        client = get_supabase_client()
        await client.close_db_pool()
    except Exception as e:
        logger.error(f"Failed to cleanup Supabase: {e}")
        raise


async def example_usage():
    """Example of how to use the Supabase client"""
    try:
        await initialize_supabase()
        client = get_supabase_client()
        health = await client.health_check()
        print(f"Health check: {health}")
    except Exception as e:
        logger.error(f"Example usage failed: {e}")
    finally:
        await cleanup_supabase()


if __name__ == "__main__":
    asyncio.run(example_usage())