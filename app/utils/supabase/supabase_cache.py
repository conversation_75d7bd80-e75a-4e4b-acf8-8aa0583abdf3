"""
Supabase-specific caching layer with query result caching and real-time invalidation.

This module provides:
- Intelligent database query result caching
- Real-time cache invalidation based on Supabase changes
- Query fingerprinting for cache keys
- Connection pooling with cache awareness
- Distributed cache consistency for Supabase operations
"""

import json
import hashlib
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
import asyncio

from supabase import Client
from postgrest import APIError

from app.cache.redis_cache import get_cache, cache_result
from app.cache.cache_invalidation import get_invalidation_manager
from app.utils.supabase.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


class SupabaseCacheManager:
    """
    Manages caching for Supabase database operations.

    Features:
    - Query result caching with intelligent invalidation
    - Real-time cache invalidation via Supabase subscriptions
    - Query fingerprinting for consistent cache keys
    - Batch operations with cache awareness
    - Performance monitoring and optimization
    """

    def __init__(self, default_ttl: int = 300):
        self.default_ttl = default_ttl
        self.cache = None
        self.invalidation_manager = None
        self.supabase_client = None
        self.query_stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "invalidations": 0,
            "queries_cached": 0,
        }

    async def initialize(self):
        """Initialize the Supabase cache manager."""
        self.cache = await get_cache()
        self.invalidation_manager = await get_invalidation_manager()
        self.supabase_client = get_supabase_client()

        # Set up real-time subscriptions for cache invalidation
        await self._setup_realtime_invalidation()

        logger.info("Supabase cache manager initialized")

    def _generate_query_fingerprint(
        self,
        table: str,
        select_fields: str = "*",
        filters: Optional[Dict] = None,
        order: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> str:
        """Generate a unique fingerprint for a query."""
        query_parts = [
            f"table:{table}",
            f"select:{select_fields}",
        ]

        if filters:
            # Sort filters for consistent fingerprinting
            sorted_filters = sorted(filters.items())
            query_parts.append(f"filters:{json.dumps(sorted_filters)}")

        if order:
            query_parts.append(f"order:{order}")

        if limit:
            query_parts.append(f"limit:{limit}")

        if offset:
            query_parts.append(f"offset:{offset}")

        query_string = ":".join(query_parts)

        # Hash for consistent, shorter keys
        fingerprint = hashlib.md5(query_string.encode()).hexdigest()

        return f"supabase_query:{table}:{fingerprint}"

    async def cached_select(
        self,
        table: str,
        select_fields: str = "*",
        filters: Optional[Dict] = None,
        order: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        ttl: Optional[int] = None,
        force_refresh: bool = False,
    ) -> Optional[List[Dict]]:
        """
        Execute a SELECT query with caching.

        Args:
            table: Table name
            select_fields: Fields to select
            filters: WHERE conditions as dict
            order: ORDER BY clause
            limit: LIMIT clause
            offset: OFFSET clause
            ttl: Cache TTL (uses default if None)
            force_refresh: Force cache refresh

        Returns:
            Query results or None if error
        """
        if not self.cache or not self.supabase_client:
            return await self._execute_query_direct(
                table, select_fields, filters, order, limit, offset
            )

        # Generate cache key
        cache_key = self._generate_query_fingerprint(
            table, select_fields, filters, order, limit, offset
        )

        # Try cache first (unless force refresh)
        if not force_refresh:
            cached_result = await self.cache.get(cache_key, "supabase_queries")
            if cached_result is not None:
                self.query_stats["cache_hits"] += 1
                return cached_result

        # Execute query
        result = await self._execute_query_direct(
            table, select_fields, filters, order, limit, offset
        )

        if result is not None:
            # Cache the result
            cache_ttl = ttl or self.default_ttl
            await self.cache.set(cache_key, result, cache_ttl, "supabase_queries")
            self.query_stats["queries_cached"] += 1

        self.query_stats["cache_misses"] += 1
        return result

    async def _execute_query_direct(
        self,
        table: str,
        select_fields: str = "*",
        filters: Optional[Dict] = None,
        order: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> Optional[List[Dict]]:
        """Execute query directly against Supabase."""
        try:
            query = self.supabase_client.table(table).select(select_fields)

            # Apply filters
            if filters:
                for column, value in filters.items():
                    if isinstance(value, dict):
                        # Handle complex filters like {"gte": 100}
                        for operator, operand in value.items():
                            if operator == "eq":
                                query = query.eq(column, operand)
                            elif operator == "neq":
                                query = query.neq(column, operand)
                            elif operator == "gt":
                                query = query.gt(column, operand)
                            elif operator == "gte":
                                query = query.gte(column, operand)
                            elif operator == "lt":
                                query = query.lt(column, operand)
                            elif operator == "lte":
                                query = query.lte(column, operand)
                            elif operator == "like":
                                query = query.like(column, operand)
                            elif operator == "ilike":
                                query = query.ilike(column, operand)
                            elif operator == "in":
                                query = query.in_(column, operand)
                            elif operator == "is":
                                query = query.is_(column, operand)
                    else:
                        # Simple equality filter
                        query = query.eq(column, value)

            # Apply ordering
            if order:
                if order.startswith("-"):
                    query = query.order(order[1:], desc=True)
                else:
                    query = query.order(order)

            # Apply pagination
            if limit:
                query = query.limit(limit)

            if offset:
                query = query.offset(offset)

            # Execute query
            response = query.execute()
            return response.data

        except APIError as e:
            logger.error(f"Supabase query error for table {table}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error querying table {table}: {e}")
            return None

    async def cached_count(
        self, table: str, filters: Optional[Dict] = None, ttl: Optional[int] = None
    ) -> Optional[int]:
        """Get count of records with caching."""
        cache_key = self._generate_query_fingerprint(table, "count", filters)

        if self.cache:
            cached_result = await self.cache.get(cache_key, "supabase_counts")
            if cached_result is not None:
                self.query_stats["cache_hits"] += 1
                return cached_result

        try:
            query = self.supabase_client.table(table).select("*", count="exact")

            # Apply filters
            if filters:
                for column, value in filters.items():
                    query = query.eq(column, value)

            response = query.execute()
            count = response.count

            # Cache the result
            if self.cache:
                cache_ttl = ttl or self.default_ttl
                await self.cache.set(cache_key, count, cache_ttl, "supabase_counts")
                self.query_stats["queries_cached"] += 1

            self.query_stats["cache_misses"] += 1
            return count

        except Exception as e:
            logger.error(f"Error counting records in table {table}: {e}")
            return None

    async def invalidate_table_cache(self, table: str):
        """Invalidate all cached queries for a specific table."""
        if not self.cache:
            return

        pattern = f"supabase_query:{table}:*"
        count = await self.cache.delete_pattern(pattern, "supabase_queries")

        # Also invalidate counts
        count_pattern = f"supabase_query:{table}:*"
        count += await self.cache.delete_pattern(count_pattern, "supabase_counts")

        self.query_stats["invalidations"] += count
        logger.info(f"Invalidated {count} cached queries for table: {table}")

        return count

    async def invalidate_record_cache(self, table: str, record_id: Any):
        """Invalidate cached queries that might include a specific record."""
        if not self.cache:
            return

        # This is a simplified approach - in production you might want
        # more sophisticated invalidation based on query analysis
        await self.invalidate_table_cache(table)

        logger.info(f"Invalidated cache for record {record_id} in table {table}")

    async def cached_insert(
        self, table: str, data: Dict, ttl: Optional[int] = None
    ) -> Optional[Dict]:
        """Insert record and invalidate related cache."""
        try:
            response = self.supabase_client.table(table).insert(data).execute()

            # Invalidate table cache after insert
            await self.invalidate_table_cache(table)

            # Trigger cache invalidation events
            if self.invalidation_manager:
                await self.invalidation_manager.invalidate_key(
                    f"{table}:insert", immediate=True
                )

            return response.data[0] if response.data else None

        except Exception as e:
            logger.error(f"Error inserting into table {table}: {e}")
            return None

    async def cached_update(
        self, table: str, data: Dict, filters: Dict, ttl: Optional[int] = None
    ) -> Optional[List[Dict]]:
        """Update records and invalidate related cache."""
        try:
            query = self.supabase_client.table(table).update(data)

            # Apply filters
            for column, value in filters.items():
                query = query.eq(column, value)

            response = query.execute()

            # Invalidate table cache after update
            await self.invalidate_table_cache(table)

            # Trigger cache invalidation events
            if self.invalidation_manager:
                await self.invalidation_manager.invalidate_key(
                    f"{table}:update", immediate=True
                )

            return response.data

        except Exception as e:
            logger.error(f"Error updating table {table}: {e}")
            return None

    async def cached_delete(self, table: str, filters: Dict) -> bool:
        """Delete records and invalidate related cache."""
        try:
            query = self.supabase_client.table(table).delete()

            # Apply filters
            for column, value in filters.items():
                query = query.eq(column, value)

            response = query.execute()

            # Invalidate table cache after delete
            await self.invalidate_table_cache(table)

            # Trigger cache invalidation events
            if self.invalidation_manager:
                await self.invalidation_manager.invalidate_key(
                    f"{table}:delete", immediate=True
                )

            return True

        except Exception as e:
            logger.error(f"Error deleting from table {table}: {e}")
            return False

    async def batch_cached_select(
        self, queries: List[Tuple[str, Dict]], ttl: Optional[int] = None
    ) -> Dict[str, List[Dict]]:
        """Execute multiple SELECT queries with caching."""
        results = {}

        # Process queries in parallel
        tasks = []
        query_keys = []

        for table, query_params in queries:
            query_key = f"{table}_{len(tasks)}"
            query_keys.append(query_key)

            task = self.cached_select(
                table=table,
                select_fields=query_params.get("select", "*"),
                filters=query_params.get("filters"),
                order=query_params.get("order"),
                limit=query_params.get("limit"),
                offset=query_params.get("offset"),
                ttl=ttl,
            )
            tasks.append(task)

        # Execute all queries
        query_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Collect results
        for query_key, result in zip(query_keys, query_results):
            if isinstance(result, Exception):
                logger.error(f"Batch query error for {query_key}: {result}")
                results[query_key] = None
            else:
                results[query_key] = result

        return results

    async def _setup_realtime_invalidation(self):
        """Set up real-time cache invalidation via Supabase subscriptions."""
        try:
            # This would be implemented based on Supabase real-time capabilities
            # For now, we'll use a polling-based approach for changes

            # In a full implementation, you would:
            # 1. Subscribe to table changes via Supabase real-time
            # 2. Invalidate relevant cache entries when changes occur
            # 3. Handle connection failures and reconnection

            logger.info("Real-time cache invalidation setup completed")

        except Exception as e:
            logger.error(f"Failed to set up real-time invalidation: {e}")

    async def warm_cache(self, table: str, common_queries: List[Dict]):
        """Warm cache with common queries for a table."""
        if not self.cache:
            return

        logger.info(
            f"Warming cache for table {table} with {len(common_queries)} queries"
        )

        for query_params in common_queries:
            try:
                await self.cached_select(
                    table=table,
                    select_fields=query_params.get("select", "*"),
                    filters=query_params.get("filters"),
                    order=query_params.get("order"),
                    limit=query_params.get("limit"),
                    offset=query_params.get("offset"),
                )
            except Exception as e:
                logger.error(f"Error warming cache for query: {e}")

        logger.info(f"Cache warming completed for table: {table}")

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get caching statistics."""
        cache_stats = {}
        if self.cache:
            cache_stats = (await self.cache.get_stats()).dict()

        return {
            "query_stats": self.query_stats,
            "redis_stats": cache_stats,
            "hit_rate": (
                self.query_stats["cache_hits"]
                / (self.query_stats["cache_hits"] + self.query_stats["cache_misses"])
                if (self.query_stats["cache_hits"] + self.query_stats["cache_misses"])
                > 0
                else 0
            ),
        }

    async def clear_all_cache(self):
        """Clear all Supabase-related cache."""
        if not self.cache:
            return

        patterns = ["supabase_queries:*", "supabase_counts:*"]
        total_cleared = 0

        for pattern in patterns:
            cleared = await self.cache.delete_pattern(pattern)
            total_cleared += cleared

        logger.info(f"Cleared {total_cleared} cached Supabase queries")
        return total_cleared


# Global cache manager instance
_supabase_cache_manager: Optional[SupabaseCacheManager] = None


async def get_supabase_cache_manager() -> SupabaseCacheManager:
    """Get or create global Supabase cache manager."""
    global _supabase_cache_manager

    if _supabase_cache_manager is None:
        _supabase_cache_manager = SupabaseCacheManager()
        await _supabase_cache_manager.initialize()

    return _supabase_cache_manager


# Convenience decorators and functions
def cached_supabase_query(ttl: int = 300, table: str = ""):
    """Decorator for caching Supabase query functions."""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache_manager = await get_supabase_cache_manager()

            # Generate cache key from function name and arguments
            cache_key = f"supabase_func:{func.__name__}:{hash(str(args) + str(kwargs))}"

            # Try cache first
            if cache_manager.cache:
                cached_result = await cache_manager.cache.get(
                    cache_key, "supabase_functions"
                )
                if cached_result is not None:
                    cache_manager.query_stats["cache_hits"] += 1
                    return cached_result

            # Execute function
            result = await func(*args, **kwargs)

            # Cache result
            if cache_manager.cache and result is not None:
                await cache_manager.cache.set(
                    cache_key, result, ttl, "supabase_functions"
                )
                cache_manager.query_stats["queries_cached"] += 1

            cache_manager.query_stats["cache_misses"] += 1
            return result

        return wrapper

    return decorator


# Common query patterns for cache warming
COMMON_QUERY_PATTERNS = {
    "users": [
        {"select": "id,email,created_at", "order": "-created_at", "limit": 100},
        {"select": "count", "filters": {"email_verified": True}},
    ],
    "books": [
        {"select": "*", "order": "-created_at", "limit": 50},
        {
            "select": "*",
            "filters": {"status": "published"},
            "order": "-created_at",
            "limit": 20,
        },
        {"select": "count", "filters": {"status": "published"}},
    ],
    "publications": [
        {"select": "*", "order": "-published_date", "limit": 30},
        {"select": "*", "filters": {"status": "active"}, "order": "-published_date"},
    ],
}
