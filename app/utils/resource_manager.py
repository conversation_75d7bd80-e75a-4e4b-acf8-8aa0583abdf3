# app/utils/resource_manager.py - Resource Management and Thread Pool Limits

import asyncio
import threading
import time
import psutil
import resource
from typing import Dict, Any, Optional, List, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception

logger = get_logger(__name__)

class ResourceType(Enum):
    """Types of resources to manage"""
    CPU = "cpu"
    MEMORY = "memory"
    THREADS = "threads"
    AI_REQUESTS = "ai_requests"
    DATABASE = "database"
    STORAGE = "storage"

@dataclass
class ResourceLimits:
    """Resource limit configuration"""
    max_cpu_percent: float = 80.0
    max_memory_percent: float = 85.0
    max_threads: int = 50
    max_ai_concurrent: int = 10
    max_db_connections: int = 20
    max_storage_mb: int = 1000
    
@dataclass
class ResourceUsage:
    """Current resource usage"""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    active_threads: int = 0
    ai_requests: int = 0
    db_connections: int = 0
    storage_mb: float = 0.0
    timestamp: float = 0.0

class ResourceMonitor:
    """System resource monitoring"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._last_check = 0.0
        self._check_interval = 5.0  # 5 seconds
        self._cached_usage = ResourceUsage()
    
    async def get_current_usage(self, force_refresh: bool = False) -> ResourceUsage:
        """Get current system resource usage"""
        now = time.time()
        
        # Use cached value if recent enough
        if not force_refresh and (now - self._last_check) < self._check_interval:
            return self._cached_usage
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Thread count
            process = psutil.Process()
            active_threads = process.num_threads()
            
            # Storage usage (current directory)
            disk_usage = psutil.disk_usage('.')
            storage_mb = (disk_usage.total - disk_usage.free) / (1024 * 1024)
            
            usage = ResourceUsage(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                active_threads=active_threads,
                ai_requests=0,  # Will be set by AI client
                db_connections=0,  # Will be set by DB pool
                storage_mb=storage_mb,
                timestamp=now
            )
            
            self._cached_usage = usage
            self._last_check = now
            
            return usage
            
        except Exception as e:
            self.logger.error(f"Failed to get resource usage: {e}")
            capture_exception(e)
            return self._cached_usage

class ThreadPoolManager:
    """Manages thread pools for different types of operations"""
    
    def __init__(self, limits: ResourceLimits):
        self.limits = limits
        self.resource_monitor = ResourceMonitor()
        
        # Create specialized thread pools
        self.pools = {
            'ai_agents': ThreadPoolExecutor(
                max_workers=min(limits.max_threads // 4, 10),
                thread_name_prefix='ai-agent'
            ),
            'file_processing': ThreadPoolExecutor(
                max_workers=min(limits.max_threads // 4, 8),
                thread_name_prefix='file-proc'
            ),
            'web_scraping': ThreadPoolExecutor(
                max_workers=min(limits.max_threads // 6, 5),
                thread_name_prefix='web-scrape'
            ),
            'data_processing': ThreadPoolExecutor(
                max_workers=min(limits.max_threads // 3, 15),
                thread_name_prefix='data-proc'
            ),
            'general': ThreadPoolExecutor(
                max_workers=min(limits.max_threads // 2, 20),
                thread_name_prefix='general'
            )
        }
        
        # Track active tasks
        self.active_tasks = {pool_name: 0 for pool_name in self.pools.keys()}
        self._task_lock = threading.Lock()
        
        logger.info(f"Thread pools initialized: {list(self.pools.keys())}")
    
    async def check_resource_availability(self, pool_name: str) -> bool:
        """Check if resources are available for new task"""
        usage = await self.resource_monitor.get_current_usage()
        
        # Check CPU usage
        if usage.cpu_percent > self.limits.max_cpu_percent:
            logger.warning(f"CPU usage too high: {usage.cpu_percent}%")
            return False
        
        # Check memory usage
        if usage.memory_percent > self.limits.max_memory_percent:
            logger.warning(f"Memory usage too high: {usage.memory_percent}%")
            return False
        
        # Check thread count
        if usage.active_threads > self.limits.max_threads:
            logger.warning(f"Thread count too high: {usage.active_threads}")
            return False
        
        # Check pool-specific limits
        with self._task_lock:
            pool_tasks = self.active_tasks.get(pool_name, 0)
            pool_max = self.pools[pool_name]._max_workers
            
            if pool_tasks >= pool_max:
                logger.warning(f"Pool {pool_name} at capacity: {pool_tasks}/{pool_max}")
                return False
        
        return True
    
    @asynccontextmanager
    async def acquire_thread(self, pool_name: str = 'general'):
        """Context manager to acquire thread from pool"""
        if pool_name not in self.pools:
            raise ValueError(f"Unknown thread pool: {pool_name}")
        
        # Wait for resource availability
        max_wait = 30  # 30 seconds
        wait_start = time.time()
        
        while not await self.check_resource_availability(pool_name):
            if time.time() - wait_start > max_wait:
                raise RuntimeError(f"Timeout waiting for {pool_name} thread pool")
            await asyncio.sleep(1)
        
        # Increment active task count
        with self._task_lock:
            self.active_tasks[pool_name] += 1
        
        try:
            yield self.pools[pool_name]
        finally:
            # Decrement active task count
            with self._task_lock:
                self.active_tasks[pool_name] = max(0, self.active_tasks[pool_name] - 1)
    
    async def submit_task(self, func: Callable, *args, pool_name: str = 'general', **kwargs):
        """Submit task to thread pool"""
        async with self.acquire_thread(pool_name) as pool:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(pool, func, *args)
    
    async def submit_batch_tasks(self, tasks: List[tuple], pool_name: str = 'general', max_concurrent: int = None):
        """Submit batch of tasks with concurrency control"""
        if not tasks:
            return []
        
        if max_concurrent is None:
            max_concurrent = self.pools[pool_name]._max_workers // 2
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def run_task(task_info):
            func, args, kwargs = task_info if len(task_info) == 3 else (task_info[0], task_info[1], {})
            
            async with semaphore:
                return await self.submit_task(func, *args, pool_name=pool_name, **kwargs)
        
        # Submit all tasks concurrently
        task_coroutines = [run_task(task) for task in tasks]
        results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        return results
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get thread pool statistics"""
        stats = {}
        
        with self._task_lock:
            for pool_name, pool in self.pools.items():
                stats[pool_name] = {
                    'max_workers': pool._max_workers,
                    'active_tasks': self.active_tasks[pool_name],
                    'queue_size': pool._work_queue.qsize() if hasattr(pool._work_queue, 'qsize') else 0,
                    'utilization': (self.active_tasks[pool_name] / pool._max_workers) * 100
                }
        
        return stats
    
    async def shutdown(self, wait: bool = True):
        """Shutdown all thread pools"""
        logger.info("Shutting down thread pools...")
        
        for pool_name, pool in self.pools.items():
            pool.shutdown(wait=wait)
            logger.info(f"Pool {pool_name} shutdown complete")

class ResourceManager:
    """Central resource management system"""
    
    def __init__(self, limits: Optional[ResourceLimits] = None):
        self.limits = limits or ResourceLimits()
        self.monitor = ResourceMonitor()
        self.thread_manager = ThreadPoolManager(self.limits)
        
        # Resource tracking
        self.ai_request_count = 0
        self.db_connection_count = 0
        self._counters_lock = asyncio.Lock()
        
        # Performance history
        self.performance_history: List[ResourceUsage] = []
        self.max_history_size = 100
        
        logger.info("Resource manager initialized")
    
    async def check_system_health(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        try:
            usage = await self.monitor.get_current_usage()
            pool_stats = self.thread_manager.get_pool_stats()
            
            # Update usage with tracked counts
            async with self._counters_lock:
                usage.ai_requests = self.ai_request_count
                usage.db_connections = self.db_connection_count
            
            # Determine health status
            health_issues = []
            
            if usage.cpu_percent > self.limits.max_cpu_percent:
                health_issues.append(f"High CPU usage: {usage.cpu_percent}%")
            
            if usage.memory_percent > self.limits.max_memory_percent:
                health_issues.append(f"High memory usage: {usage.memory_percent}%")
            
            if usage.active_threads > self.limits.max_threads:
                health_issues.append(f"Too many threads: {usage.active_threads}")
            
            if usage.ai_requests > self.limits.max_ai_concurrent:
                health_issues.append(f"Too many AI requests: {usage.ai_requests}")
            
            status = "healthy" if not health_issues else "degraded" if len(health_issues) < 3 else "critical"
            
            # Store in performance history
            self.performance_history.append(usage)
            if len(self.performance_history) > self.max_history_size:
                self.performance_history.pop(0)
            
            return {
                'status': status,
                'issues': health_issues,
                'resource_usage': {
                    'cpu_percent': usage.cpu_percent,
                    'memory_percent': usage.memory_percent,
                    'active_threads': usage.active_threads,
                    'ai_requests': usage.ai_requests,
                    'db_connections': usage.db_connections,
                    'storage_mb': usage.storage_mb
                },
                'resource_limits': {
                    'max_cpu_percent': self.limits.max_cpu_percent,
                    'max_memory_percent': self.limits.max_memory_percent,
                    'max_threads': self.limits.max_threads,
                    'max_ai_concurrent': self.limits.max_ai_concurrent,
                    'max_db_connections': self.limits.max_db_connections
                },
                'thread_pools': pool_stats,
                'timestamp': usage.timestamp
            }
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            capture_exception(e)
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': time.time()
            }
    
    @asynccontextmanager
    async def track_ai_request(self):
        """Context manager to track AI requests"""
        async with self._counters_lock:
            if self.ai_request_count >= self.limits.max_ai_concurrent:
                raise RuntimeError("AI request limit exceeded")
            self.ai_request_count += 1
        
        try:
            yield
        finally:
            async with self._counters_lock:
                self.ai_request_count = max(0, self.ai_request_count - 1)
    
    @asynccontextmanager
    async def track_db_connection(self):
        """Context manager to track database connections"""
        async with self._counters_lock:
            if self.db_connection_count >= self.limits.max_db_connections:
                raise RuntimeError("Database connection limit exceeded")
            self.db_connection_count += 1
        
        try:
            yield
        finally:
            async with self._counters_lock:
                self.db_connection_count = max(0, self.db_connection_count - 1)
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics and trends"""
        if not self.performance_history:
            return {'error': 'No performance history available'}
        
        recent_history = self.performance_history[-10:]  # Last 10 entries
        
        # Calculate averages
        avg_cpu = sum(h.cpu_percent for h in recent_history) / len(recent_history)
        avg_memory = sum(h.memory_percent for h in recent_history) / len(recent_history)
        avg_threads = sum(h.active_threads for h in recent_history) / len(recent_history)
        
        # Calculate trends (positive = increasing)
        if len(recent_history) >= 2:
            cpu_trend = recent_history[-1].cpu_percent - recent_history[0].cpu_percent
            memory_trend = recent_history[-1].memory_percent - recent_history[0].memory_percent
        else:
            cpu_trend = memory_trend = 0
        
        return {
            'averages': {
                'cpu_percent': round(avg_cpu, 2),
                'memory_percent': round(avg_memory, 2),
                'active_threads': round(avg_threads, 1)
            },
            'trends': {
                'cpu_trend': round(cpu_trend, 2),
                'memory_trend': round(memory_trend, 2)
            },
            'history_size': len(self.performance_history),
            'time_span_minutes': (
                (recent_history[-1].timestamp - recent_history[0].timestamp) / 60
                if len(recent_history) >= 2 else 0
            )
        }
    
    async def optimize_resource_allocation(self) -> Dict[str, Any]:
        """Automatically optimize resource allocation"""
        health = await self.check_system_health()
        optimizations = []
        
        try:
            # CPU optimization
            if health['resource_usage']['cpu_percent'] > 70:
                # Reduce thread pool sizes
                for pool_name, pool in self.thread_manager.pools.items():
                    if pool._max_workers > 2:
                        new_size = max(2, pool._max_workers - 1)
                        # Note: ThreadPoolExecutor doesn't support dynamic resizing
                        # This would require pool recreation
                        optimizations.append(f"Recommend reducing {pool_name} pool to {new_size}")
            
            # Memory optimization
            if health['resource_usage']['memory_percent'] > 75:
                # Suggest garbage collection or cache clearing
                optimizations.append("High memory usage detected - consider cache cleanup")
            
            # AI request optimization
            if health['resource_usage']['ai_requests'] > self.limits.max_ai_concurrent * 0.8:
                optimizations.append("High AI request load - consider request queuing")
            
            return {
                'optimizations_applied': optimizations,
                'health_status': health['status'],
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Resource optimization failed: {e}")
            capture_exception(e)
            return {'error': str(e)}
    
    async def set_resource_limits(self, new_limits: ResourceLimits):
        """Update resource limits"""
        old_limits = self.limits
        self.limits = new_limits
        
        logger.info(f"Resource limits updated: {old_limits} -> {new_limits}")
    
    async def shutdown(self):
        """Shutdown resource manager"""
        logger.info("Shutting down resource manager...")
        await self.thread_manager.shutdown()
        logger.info("Resource manager shutdown complete")

# Global resource manager instance
resource_manager: Optional[ResourceManager] = None

async def initialize_resource_manager(limits: Optional[ResourceLimits] = None) -> None:
    """Initialize the global resource manager"""
    global resource_manager
    
    if resource_manager:
        logger.warning("Resource manager already initialized")
        return
    
    resource_manager = ResourceManager(limits)
    logger.info("Global resource manager initialized")

async def close_resource_manager() -> None:
    """Close the global resource manager"""
    global resource_manager
    
    if resource_manager:
        await resource_manager.shutdown()
        resource_manager = None
        logger.info("Global resource manager closed")

def get_resource_manager() -> ResourceManager:
    """Get the global resource manager"""
    if not resource_manager:
        raise RuntimeError("Resource manager not initialized. Call initialize_resource_manager() first.")
    
    return resource_manager

# Convenience functions
async def submit_ai_task(func: Callable, *args, **kwargs):
    """Submit AI-related task with resource management"""
    manager = get_resource_manager()
    
    async with manager.track_ai_request():
        async with manager.thread_manager.acquire_thread('ai_agents') as pool:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(pool, func, *args)

async def submit_file_task(func: Callable, *args, **kwargs):
    """Submit file processing task with resource management"""
    manager = get_resource_manager()
    return await manager.thread_manager.submit_task(func, *args, pool_name='file_processing', **kwargs)

async def submit_web_task(func: Callable, *args, **kwargs):
    """Submit web scraping task with resource management"""
    manager = get_resource_manager()
    return await manager.thread_manager.submit_task(func, *args, pool_name='web_scraping', **kwargs)

async def get_system_health() -> Dict[str, Any]:
    """Get current system health"""
    manager = get_resource_manager()
    return await manager.check_system_health()

async def get_performance_metrics() -> Dict[str, Any]:
    """Get performance metrics"""
    manager = get_resource_manager()
    return await manager.get_performance_metrics()

# Decorators for resource management
def with_resource_tracking(resource_type: ResourceType):
    """Decorator to track resource usage for functions"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            manager = get_resource_manager()
            
            if resource_type == ResourceType.AI_REQUESTS:
                async with manager.track_ai_request():
                    return await func(*args, **kwargs)
            elif resource_type == ResourceType.DATABASE:
                async with manager.track_db_connection():
                    return await func(*args, **kwargs)
            else:
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator