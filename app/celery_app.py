# app/celery_app.py - Optimized Celery Application with Advanced Queue Management

from celery import Celery
from celery.signals import worker_ready, worker_shutdown
from kombu import Queue, Exchange
import os
import time
import psutil
from typing import Dict, Any, Optional
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception

logger = get_logger(__name__)

# Define exchanges for better message routing
exchanges = {
    'default': Exchange('default', type='direct'),
    'priority': Exchange('priority', type='direct'),
    'ai_tasks': Exchange('ai_tasks', type='topic'),
    'data_tasks': Exchange('data_tasks', type='topic'),
    'training_tasks': Exchange('training_tasks', type='direct'),
    'monitoring': Exchange('monitoring', type='fanout')
}

# Define queues with different priorities and routing
queues = [
    # High priority queues
    Queue('critical', exchanges['priority'], routing_key='critical', 
          queue_arguments={'x-max-priority': 10}),
    Queue('high_priority', exchanges['priority'], routing_key='high',
          queue_arguments={'x-max-priority': 8}),
    
    # AI task queues
    Queue('manuscript', exchanges['ai_tasks'], routing_key='ai.manuscript',
          queue_arguments={'x-max-priority': 6, 'x-message-ttl': 3600000}),  # 1 hour TTL
    Queue('cover_generation', exchanges['ai_tasks'], routing_key='ai.cover',
          queue_arguments={'x-max-priority': 5}),
    Queue('content_analysis', exchanges['ai_tasks'], routing_key='ai.analysis',
          queue_arguments={'x-max-priority': 4}),
    
    # Data processing queues  
    Queue('analysis', exchanges['data_tasks'], routing_key='data.analysis',
          queue_arguments={'x-max-priority': 5}),
    Queue('scraping', exchanges['data_tasks'], routing_key='data.scraping',
          queue_arguments={'x-max-priority': 3, 'x-message-ttl': 1800000}),  # 30 min TTL
    Queue('processing', exchanges['data_tasks'], routing_key='data.processing',
          queue_arguments={'x-max-priority': 4}),
    
    # Publishing and training queues
    Queue('publishing', exchanges['default'], routing_key='publishing',
          queue_arguments={'x-max-priority': 7}),
    Queue('training', exchanges['training_tasks'], routing_key='training',
          queue_arguments={'x-max-priority': 2, 'x-message-ttl': 7200000}),  # 2 hour TTL
    
    # Monitoring and maintenance
    Queue('monitoring', exchanges['monitoring'], routing_key='',
          queue_arguments={'x-max-priority': 9}),
    Queue('maintenance', exchanges['default'], routing_key='maintenance',
          queue_arguments={'x-max-priority': 1}),
    
    # Default queue
    Queue('default', exchanges['default'], routing_key='default',
          queue_arguments={'x-max-priority': 3})
]

class CeleryOptimizer:
    """Celery performance optimizer"""
    
    def __init__(self, celery_app):
        self.celery_app = celery_app
        self.logger = get_logger(__name__)
        self._performance_stats = {
            'tasks_processed': 0,
            'tasks_failed': 0,
            'avg_task_time': 0.0,
            'queue_lengths': {},
            'worker_cpu_usage': 0.0,
            'worker_memory_usage': 0.0
        }
    
    def optimize_worker_settings(self) -> Dict[str, Any]:
        """Dynamically optimize worker settings based on system resources"""
        try:
            # Get system resources
            cpu_count = psutil.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # Calculate optimal settings
            optimal_concurrency = min(cpu_count * 2, 16)  # Max 16 workers
            
            # Adjust based on available memory (min 2GB per worker)
            max_workers_by_memory = max(1, int(memory_gb / 2))
            optimal_concurrency = min(optimal_concurrency, max_workers_by_memory)
            
            # Prefetch optimization
            optimal_prefetch = max(1, optimal_concurrency // 4)
            
            # Max tasks per child (prevent memory leaks)
            max_tasks_per_child = min(1000, int(memory_gb * 100))
            
            optimized_settings = {
                'worker_concurrency': optimal_concurrency,
                'worker_prefetch_multiplier': optimal_prefetch,
                'worker_max_tasks_per_child': max_tasks_per_child,
                'worker_pool_restarts': True,
                'worker_max_memory_per_child': int(memory_gb * 1024 * 0.8),  # 80% of available memory
            }
            
            self.logger.info(f"Optimized Celery worker settings: {optimized_settings}")
            return optimized_settings
            
        except Exception as e:
            self.logger.error(f"Failed to optimize worker settings: {e}")
            capture_exception(e)
            return {}
    
    def get_queue_priorities(self) -> Dict[str, int]:
        """Get queue priority mappings"""
        return {
            'critical': 10,
            'high_priority': 8,
            'monitoring': 9,
            'publishing': 7,
            'manuscript': 6,
            'analysis': 5,
            'cover_generation': 5,
            'content_analysis': 4,
            'processing': 4,
            'default': 3,
            'scraping': 3,
            'training': 2,
            'maintenance': 1
        }
    
    def update_performance_stats(self, task_name: str, execution_time: float, success: bool):
        """Update performance statistics"""
        self._performance_stats['tasks_processed'] += 1
        
        if not success:
            self._performance_stats['tasks_failed'] += 1
        
        # Update average task time
        current_avg = self._performance_stats['avg_task_time']
        total_tasks = self._performance_stats['tasks_processed']
        self._performance_stats['avg_task_time'] = (
            (current_avg * (total_tasks - 1) + execution_time) / total_tasks
        )
        
        # Update system resource usage
        try:
            self._performance_stats['worker_cpu_usage'] = psutil.cpu_percent()
            self._performance_stats['worker_memory_usage'] = psutil.virtual_memory().percent
        except Exception:
            pass
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self._performance_stats.copy()

def create_celery_app():
    """Create and configure optimized Celery app"""
    
    # Redis/RabbitMQ broker URL with connection pooling
    broker_url = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    result_backend = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/1")
    
    # Add connection pool settings to broker URL
    if "redis://" in broker_url and "?" not in broker_url:
        broker_url += "?socket_keepalive=1&socket_keepalive_options=%7BTCP_KEEPIDLE%3A300%2CTCP_KEEPINTVL%3A300%2CTCP_KEEPCNT%3A3%7D"
    
    celery_app = Celery(
        "publish_ai",
        broker=broker_url,
        backend=result_backend,
        include=[
            "app.tasks.manuscript_generation",
            "app.tasks.trend_analysis", 
            "app.tasks.publication",
            "app.tasks.verl_training",
            "app.tasks.model_training"
        ]
    )
    
    # Set up exchanges and queues
    celery_app.conf.task_queues = queues
    
    # Initialize optimizer
    optimizer = CeleryOptimizer(celery_app)
    optimized_settings = optimizer.optimize_worker_settings()
    
    # Advanced Celery configuration with optimization
    celery_app.conf.update(
        # Task routing with priority queues
        task_routes={
            # High priority tasks
            "app.tasks.monitoring.*": {"queue": "monitoring", "priority": 9},
            "app.tasks.publication.emergency_*": {"queue": "critical", "priority": 10},
            
            # AI tasks with topic routing
            "app.tasks.manuscript_generation.*": {"queue": "manuscript", "priority": 6},
            "app.tasks.trend_analysis.*": {"queue": "analysis", "priority": 5},
            "app.tasks.cover_generation.*": {"queue": "cover_generation", "priority": 5},
            "app.tasks.content_analysis.*": {"queue": "content_analysis", "priority": 4},
            
            # Data processing tasks
            "app.tasks.data_processing.*": {"queue": "processing", "priority": 4},
            "app.tasks.web_scraping.*": {"queue": "scraping", "priority": 3},
            
            # Publishing and training
            "app.tasks.publication.*": {"queue": "publishing", "priority": 7},
            "app.tasks.verl_training.*": {"queue": "training", "priority": 2},
            "app.tasks.model_training.*": {"queue": "training", "priority": 2},
            
            # Maintenance tasks
            "app.tasks.maintenance.*": {"queue": "maintenance", "priority": 1}
        },
        
        # Task execution settings with compression
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        result_compression="gzip",
        task_compression="gzip",
        timezone="UTC",
        enable_utc=True,
        
        # Optimized worker settings
        worker_prefetch_multiplier=optimized_settings.get('worker_prefetch_multiplier', 2),
        worker_concurrency=optimized_settings.get('worker_concurrency', 4),
        worker_max_tasks_per_child=optimized_settings.get('worker_max_tasks_per_child', 1000),
        worker_max_memory_per_child=optimized_settings.get('worker_max_memory_per_child', 200000),  # 200MB
        worker_pool_restarts=True,
        worker_disable_rate_limits=False,
        worker_enable_remote_control=True,
        
        # Task acknowledgment settings
        task_acks_late=True,
        task_reject_on_worker_lost=True,
        
        # Task time limits with different levels
        task_soft_time_limit=1800,   # 30 minutes soft limit
        task_time_limit=3600,        # 1 hour hard limit
        
        # Advanced retry settings
        task_retry_backoff=True,
        task_retry_backoff_max=600,   # 10 minutes max backoff
        task_retry_jitter=True,       # Add jitter to prevent thundering herd
        task_max_retries=3,
        task_default_retry_delay=60,  # 1 minute default retry delay
        
        # Connection and broker settings
        broker_connection_retry=True,
        broker_connection_retry_on_startup=True,
        broker_connection_max_retries=10,
        broker_heartbeat=30,
        broker_pool_limit=10,
        broker_transport_options={
            'visibility_timeout': 3600,     # 1 hour
            'fanout_prefix': True,
            'fanout_patterns': True,
            'socket_keepalive': True,
            'socket_keepalive_options': {
                'TCP_KEEPIDLE': 300,
                'TCP_KEEPINTVL': 300,
                'TCP_KEEPCNT': 3
            },
            'master_name': 'mymaster'  # For Redis Sentinel
        },
        
        # Result backend optimization
        result_backend_transport_options={
            'socket_keepalive': True,
            'socket_keepalive_options': {
                'TCP_KEEPIDLE': 300,
                'TCP_KEEPINTVL': 300,
                'TCP_KEEPCNT': 3
            }
        },
        
        # Enhanced beat schedule with resource-aware scheduling
        beat_schedule={
            "daily-trend-analysis": {
                "task": "app.tasks.trend_analysis.daily_trend_analysis",
                "schedule": 86400.0,  # Every 24 hours
                "options": {"queue": "analysis", "priority": 5}
            },
            "weekly-verl-training": {
                "task": "app.tasks.verl_training.train_verl_model_task",
                "schedule": 604800.0,  # Every 7 days
                "kwargs": {"days_back": 30},
                "options": {"queue": "training", "priority": 2}
            },
            "daily-verl-evaluation": {
                "task": "app.tasks.verl_training.daily_verl_evaluation",
                "schedule": 86400.0,  # Every 24 hours
                "options": {"queue": "training", "priority": 3}
            },
            "hourly-publication-monitor": {
                "task": "app.tasks.publication.monitor_publications",
                "schedule": 3600.0,  # Every hour
                "options": {"queue": "monitoring", "priority": 8}
            },
            "system-health-check": {
                "task": "app.tasks.monitoring.system_health_check",
                "schedule": 300.0,  # Every 5 minutes
                "options": {"queue": "monitoring", "priority": 9}
            },
            "queue-length-monitor": {
                "task": "app.tasks.monitoring.monitor_queue_lengths",
                "schedule": 180.0,  # Every 3 minutes
                "options": {"queue": "monitoring", "priority": 7}
            },
            "cleanup-expired-results": {
                "task": "app.tasks.maintenance.cleanup_expired_results",
                "schedule": 21600.0,  # Every 6 hours
                "options": {"queue": "maintenance", "priority": 1}
            },
            "resource-optimization": {
                "task": "app.tasks.maintenance.optimize_resources",
                "schedule": 1800.0,  # Every 30 minutes
                "options": {"queue": "maintenance", "priority": 2}
            }
        },
        
        # Result settings with optimized expiration
        result_expires=7200,          # 2 hours (longer for training tasks)
        result_persistent=True,
        result_backend_always_retry=True,
        result_backend_max_retries=3,
        
        # Task result ignore settings
        task_ignore_result=False,
        task_store_errors_even_if_ignored=True,
        
        # Queue settings with dead letter queue
        task_default_queue="default",
        task_create_missing_queues=True,
        task_default_exchange="default",
        task_default_exchange_type="direct",
        task_default_routing_key="default",
        
        # Worker state and logging
        worker_send_task_events=True,
        task_send_sent_event=True,
        worker_hijack_root_logger=False,
        worker_log_color=True,
        
        # Task execution optimization
        task_always_eager=False,       # Disable for production
        task_eager_propagates=True,     # Propagate exceptions in eager mode
        task_remote_tracebacks=True,    # Include tracebacks in task results
        
        # Security settings
        worker_enable_remote_control=True,
        worker_send_task_events=True,
        
        # Advanced monitoring
        worker_send_task_events=True,
        task_send_sent_event=True,
        worker_prefetch_multiplier=optimized_settings.get('worker_prefetch_multiplier', 2),
        
        # Task priority settings
        task_inherit_parent_priority=True,
        task_default_priority=3,
        worker_disable_rate_limits=False
    )
    
    # Store optimizer reference
    celery_app.optimizer = optimizer
    
    return celery_app

# Task monitoring and lifecycle management
@worker_ready.connect
def worker_ready_handler(sender, **kwargs):
    """Handle worker ready event"""
    logger.info(f"Celery worker ready: {sender}")
    
    # Log worker configuration
    try:
        if hasattr(sender, 'app'):
            optimizer = getattr(sender.app, 'optimizer', None)
            if optimizer:
                stats = optimizer.get_performance_stats()
                logger.info(f"Worker performance stats: {stats}")
    except Exception as e:
        logger.warning(f"Could not log worker stats: {e}")

@worker_shutdown.connect
def worker_shutdown_handler(sender, **kwargs):
    """Handle worker shutdown event"""
    logger.info(f"Celery worker shutting down: {sender}")
    
    # Log final performance statistics
    try:
        if hasattr(sender, 'app'):
            optimizer = getattr(sender.app, 'optimizer', None)
            if optimizer:
                final_stats = optimizer.get_performance_stats()
                logger.info(f"Final worker performance stats: {final_stats}")
    except Exception as e:
        logger.warning(f"Could not log final worker stats: {e}")

def create_monitoring_tasks():
    """Create monitoring and maintenance tasks"""
    
    @celery.task(bind=True, name='app.tasks.monitoring.system_health_check')
    def system_health_check(self):
        """Monitor system health"""
        try:
            # System resource check
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            health_data = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_usage': disk.percent,
                'disk_free_gb': disk.free / (1024**3),
                'timestamp': time.time()
            }
            
            # Check if system is under stress
            alerts = []
            if cpu_usage > 90:
                alerts.append(f"High CPU usage: {cpu_usage}%")
            if memory.percent > 90:
                alerts.append(f"High memory usage: {memory.percent}%")
            if disk.percent > 90:
                alerts.append(f"High disk usage: {disk.percent}%")
            
            if alerts:
                logger.warning(f"System health alerts: {alerts}")
                health_data['alerts'] = alerts
            
            return health_data
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            capture_exception(e)
            return {'error': str(e)}
    
    @celery.task(bind=True, name='app.tasks.monitoring.monitor_queue_lengths')
    def monitor_queue_lengths(self):
        """Monitor queue lengths and performance"""
        try:
            inspector = celery.control.inspect()
            
            # Get active tasks
            active_tasks = inspector.active()
            scheduled_tasks = inspector.scheduled()
            reserved_tasks = inspector.reserved()
            
            queue_stats = {
                'active_tasks': len(active_tasks) if active_tasks else 0,
                'scheduled_tasks': len(scheduled_tasks) if scheduled_tasks else 0,
                'reserved_tasks': len(reserved_tasks) if reserved_tasks else 0,
                'timestamp': time.time()
            }
            
            # Check for queue congestion
            total_pending = queue_stats['scheduled_tasks'] + queue_stats['reserved_tasks']
            if total_pending > 100:
                logger.warning(f"High queue congestion: {total_pending} pending tasks")
                queue_stats['congestion_alert'] = True
            
            return queue_stats
            
        except Exception as e:
            logger.error(f"Queue monitoring failed: {e}")
            capture_exception(e)
            return {'error': str(e)}
    
    @celery.task(bind=True, name='app.tasks.maintenance.cleanup_expired_results')
    def cleanup_expired_results(self):
        """Clean up expired task results"""
        try:
            # This would typically involve cleaning up the result backend
            # Implementation depends on the backend (Redis, database, etc.)
            
            cleaned_count = 0
            # Placeholder for actual cleanup logic
            
            logger.info(f"Cleaned up {cleaned_count} expired task results")
            return {'cleaned_count': cleaned_count, 'timestamp': time.time()}
            
        except Exception as e:
            logger.error(f"Result cleanup failed: {e}")
            capture_exception(e)
            return {'error': str(e)}
    
    @celery.task(bind=True, name='app.tasks.maintenance.optimize_resources')
    def optimize_resources(self):
        """Optimize resource allocation"""
        try:
            optimizer = getattr(celery, 'optimizer', None)
            if not optimizer:
                return {'error': 'Optimizer not available'}
            
            # Get current performance stats
            stats = optimizer.get_performance_stats()
            
            # Implement optimization logic based on stats
            optimizations = []
            
            # CPU optimization
            if stats.get('worker_cpu_usage', 0) > 80:
                optimizations.append('Recommended: Reduce worker concurrency')
            
            # Memory optimization  
            if stats.get('worker_memory_usage', 0) > 85:
                optimizations.append('Recommended: Increase worker restarts frequency')
            
            # Task performance optimization
            avg_task_time = stats.get('avg_task_time', 0)
            if avg_task_time > 300:  # 5 minutes
                optimizations.append('Recommended: Review slow tasks')
            
            result = {
                'optimizations': optimizations,
                'current_stats': stats,
                'timestamp': time.time()
            }
            
            if optimizations:
                logger.info(f"Resource optimization recommendations: {optimizations}")
            
            return result
            
        except Exception as e:
            logger.error(f"Resource optimization failed: {e}")
            capture_exception(e)
            return {'error': str(e)}

# Task execution wrapper with performance tracking
def track_task_performance(task_func):
    """Decorator to track task performance"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        
        try:
            result = task_func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            raise
        finally:
            execution_time = time.time() - start_time
            
            # Update performance stats if optimizer is available
            optimizer = getattr(celery, 'optimizer', None)
            if optimizer:
                task_name = getattr(task_func, 'name', 'unknown')
                optimizer.update_performance_stats(task_name, execution_time, success)
    
    return wrapper

# Utility functions for queue management
def get_queue_stats() -> Dict[str, Any]:
    """Get current queue statistics"""
    try:
        inspector = celery.control.inspect()
        
        return {
            'active': inspector.active(),
            'scheduled': inspector.scheduled(),
            'reserved': inspector.reserved(),
            'stats': inspector.stats(),
            'registered': inspector.registered()
        }
    except Exception as e:
        logger.error(f"Failed to get queue stats: {e}")
        return {'error': str(e)}

def purge_queue(queue_name: str) -> Dict[str, Any]:
    """Purge specified queue"""
    try:
        purged_count = celery.control.purge()
        logger.info(f"Purged {purged_count} tasks from queue {queue_name}")
        return {'purged_count': purged_count, 'queue': queue_name}
    except Exception as e:
        logger.error(f"Failed to purge queue {queue_name}: {e}")
        capture_exception(e)
        return {'error': str(e)}

def get_worker_stats() -> Dict[str, Any]:
    """Get worker statistics"""
    try:
        inspector = celery.control.inspect()
        return {
            'stats': inspector.stats(),
            'active_queues': inspector.active_queues(),
            'ping': inspector.ping(),
            'timestamp': time.time()
        }
    except Exception as e:
        logger.error(f"Failed to get worker stats: {e}")
        return {'error': str(e)}

# Create the app instance
celery = create_celery_app()

# Create monitoring tasks
create_monitoring_tasks()

# Auto-discover tasks
celery.autodiscover_tasks()

# Health check function for the application
def get_celery_health() -> Dict[str, Any]:
    """Get comprehensive Celery health information"""
    try:
        inspector = celery.control.inspect()
        
        # Basic connectivity test
        ping_result = inspector.ping()
        is_healthy = bool(ping_result)
        
        health_info = {
            'status': 'healthy' if is_healthy else 'unhealthy',
            'workers_online': len(ping_result) if ping_result else 0,
            'ping_result': ping_result,
            'timestamp': time.time()
        }
        
        if is_healthy:
            # Get detailed stats if workers are available
            try:
                stats = inspector.stats()
                active = inspector.active()
                
                health_info.update({
                    'worker_stats': stats,
                    'active_tasks': len(active) if active else 0,
                    'total_workers': len(stats) if stats else 0
                })
            except Exception as e:
                health_info['stats_error'] = str(e)
        
        return health_info
        
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': time.time()
        }

if __name__ == "__main__":
    celery.start()