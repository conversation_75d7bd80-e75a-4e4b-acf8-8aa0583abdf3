import asyncio
import base64
import hashlib
import logging
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse, urlencode

from fastapi import Depends, HTTPException
from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTTPBearer
import jwt
from pydantic import BaseModel, Field

from app.utils.supabase.supabase_client import get_supabase_client
from app.config import get_settings
from app.auth.supabase_auth import get_current_user
from app.monitoring.audit_logger import audit_log
from app.schemas.user import UserResponse

logger = logging.getLogger(__name__)
settings = get_settings()

oauth2_scheme = HTTPBearer(auto_error=False)


class OAuthScope(BaseModel):
    name: str
    description: str
    required_permissions: List[str] = Field(default_factory=list)
    sensitive: bool = False


class OAuthClient(BaseModel):
    client_id: str
    client_name: str
    redirect_uris: List[str]
    allowed_scopes: List[str]
    client_type: str = "public"
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True


class OAuthToken(BaseModel):
    access_token: str
    token_type: str = "Bearer"
    expires_in: int
    refresh_token: Optional[str] = None
    scope: str
    issued_at: datetime = Field(default_factory=datetime.utcnow)


class AuthorizationCode(BaseModel):
    code: str
    client_id: str
    user_id: str
    redirect_uri: str
    scope: str
    code_challenge: Optional[str] = None
    code_challenge_method: Optional[str] = None
    expires_at: datetime
    used: bool = False


class OAuth2Manager:
    def __init__(self):
        self.supabase_client = None
        self.token_expiry_minutes = 60
        self.refresh_token_expiry_days = 30
        self.blacklisted_tokens: set[str] = set()

    async def _get_supabase_client(self):
        if not self.supabase_client:
            self.supabase_client = await get_supabase_client()
        if not self.supabase_client:
            raise Exception("Supabase client failed to initialize")
        return self.supabase_client

    # Token blacklisting helpers
    async def _blacklist_token(self, token: str):
        self.blacklisted_tokens.add(token)
        await audit_log("oauth_token_blacklisted", {"token": token}, user_id=None)

    async def _is_token_blacklisted(self, token: str) -> bool:
        return token in self.blacklisted_tokens

    # === Public Methods ===

    async def register_client(
        self,
        client_name: str,
        redirect_uris: List[str],
        allowed_scopes: List[str],
        client_type: str = "public",
    ) -> OAuthClient:
        client_id = self._generate_client_id()
        for uri in redirect_uris:
            if not self._validate_redirect_uri(uri):
                raise HTTPException(
                    status_code=400, detail=f"Invalid redirect URI: {uri}"
                )
        client = OAuthClient(
            client_id=client_id,
            client_name=client_name,
            redirect_uris=redirect_uris,
            allowed_scopes=allowed_scopes,
            client_type=client_type,
        )
        await self._store_oauth_client(client.dict())
        await audit_log(
            "oauth_client_registered", {"client_id": client_id}, user_id=None
        )
        return client

    async def authorize(
        self,
        client_id: str,
        redirect_uri: str,
        scope: str,
        state: Optional[str] = None,
        code_challenge: Optional[str] = None,
        code_challenge_method: Optional[str] = None,
        user: UserResponse = Depends(get_current_user),
    ) -> str:
        client = await self._get_oauth_client(client_id)
        if not client or not client["is_active"]:
            raise HTTPException(status_code=400, detail="Invalid client")
        if redirect_uri not in client["redirect_uris"]:
            raise HTTPException(status_code=400, detail="Invalid redirect URI")
        auth_code = self._generate_authorization_code()
        code_data = {
            "code": auth_code,
            "client_id": client_id,
            "user_id": user.id,
            "redirect_uri": redirect_uri,
            "scope": scope,
            "code_challenge": code_challenge,
            "code_challenge_method": code_challenge_method,
            "expires_at": (datetime.utcnow() + timedelta(minutes=10)).isoformat(),
            "used": False,
        }
        await self._store_authorization_code(code_data)
        params = {"code": auth_code}
        if state:
            params["state"] = state
        redirect_url = f"{redirect_uri}?{urlencode(params)}"
        await audit_log(
            "oauth_authorization_granted",
            {"client_id": client_id, "user_id": user.id},
            user_id=user.id,
        )
        return redirect_url

    async def exchange_code_for_token(
        self,
        client_id: str,
        code: str,
        redirect_uri: str,
        code_verifier: Optional[str] = None,
    ) -> OAuthToken:
        code_data = await self._get_authorization_code(code)
        if not code_data or code_data["used"]:
            raise HTTPException(
                status_code=400, detail="Invalid or used authorization code"
            )
        if datetime.fromisoformat(code_data["expires_at"]) < datetime.utcnow():
            raise HTTPException(status_code=400, detail="Authorization code expired")
        if code_data["client_id"] != client_id:
            raise HTTPException(status_code=400, detail="Invalid client")
        if code_data["redirect_uri"] != redirect_uri:
            raise HTTPException(status_code=400, detail="Redirect URI mismatch")
        if code_data["code_challenge"]:
            if not code_verifier:
                raise HTTPException(status_code=400, detail="Code verifier required")
            if not self._verify_pkce(
                code_data["code_challenge"],
                code_verifier,
                code_data["code_challenge_method"],
            ):
                raise HTTPException(status_code=400, detail="Invalid code verifier")
        await self._mark_code_used(code)
        access_token = await self._generate_access_token(
            code_data["user_id"], client_id, code_data["scope"]
        )
        refresh_token = await self._generate_refresh_token(
            code_data["user_id"], client_id, code_data["scope"]
        )
        return OAuthToken(
            access_token=access_token,
            expires_in=self.token_expiry_minutes * 60,
            refresh_token=refresh_token,
            scope=code_data["scope"],
        )

    async def refresh_access_token(
        self, refresh_token: str, client_id: str, scope: Optional[str] = None
    ) -> OAuthToken:
        token_data = await self._validate_refresh_token(refresh_token, client_id)
        if not token_data:
            raise HTTPException(status_code=400, detail="Invalid refresh token")
        await self._revoke_refresh_token(refresh_token)
        new_scope = scope or token_data["scope"]
        access_token = await self._generate_access_token(
            token_data["user_id"], client_id, new_scope
        )
        new_refresh_token = await self._generate_refresh_token(
            token_data["user_id"], client_id, new_scope
        )
        return OAuthToken(
            access_token=access_token,
            expires_in=self.token_expiry_minutes * 60,
            refresh_token=new_refresh_token,
            scope=new_scope,
        )

    async def revoke_token(self, token: str, token_type: str = "access_token"):
        if token_type == "refresh_token":
            await self._revoke_refresh_token(token)
        else:
            await self._blacklist_token(token)
        await audit_log("oauth_token_revoked", {"token_type": token_type}, user_id=None)
        return {"status": "success"}

    async def validate_token(self, token: str) -> Dict[str, Any]:
        try:
            if await self._is_token_blacklisted(token):
                logger.warning("Attempt to use blacklisted token")
                return {}

            payload = jwt.decode(token, settings.secret_key, algorithms=["HS256"])

            if payload.get("token_type") != "access_token":
                return {}
            if payload.get("exp", 0) < datetime.utcnow().timestamp():
                return {}
            return payload
        except jwt.InvalidTokenError:
            return {}

    # === DB Helpers ===

    async def _store_oauth_client(self, client_data: Dict[str, Any]):
        client = await self._get_supabase_client()
        await asyncio.to_thread(
            lambda: client.client.table("oauth_clients").insert(client_data).execute()
        )

    async def _get_oauth_client(self, client_id: str) -> Optional[Dict[str, Any]]:
        client = await self._get_supabase_client()
        result = await asyncio.to_thread(
            lambda: client.client.table("oauth_clients")
            .select("*")
            .eq("client_id", client_id)
            .execute()
        )
        return result.data[0] if result.data else None

    async def _store_authorization_code(self, code_data: Dict[str, Any]):
        client = await self._get_supabase_client()
        await asyncio.to_thread(
            lambda: client.client.table("oauth_authorization_codes")
            .insert(code_data)
            .execute()
        )

    async def _get_authorization_code(self, code: str) -> Optional[Dict[str, Any]]:
        client = await self._get_supabase_client()
        result = await asyncio.to_thread(
            lambda: client.client.table("oauth_authorization_codes")
            .select("*")
            .eq("code", code)
            .execute()
        )
        if result.data:
            data = result.data[0]
            data["expires_at"] = datetime.fromisoformat(data["expires_at"])
            return data
        return None

    async def _mark_code_used(self, code: str):
        client = await self._get_supabase_client()
        await asyncio.to_thread(
            lambda: client.client.table("oauth_authorization_codes")
            .update({"used": True})
            .eq("code", code)
            .execute()
        )

    async def _store_refresh_token(self, token_data: Dict[str, Any]):
        client = await self._get_supabase_client()
        await asyncio.to_thread(
            lambda: client.client.table("oauth_refresh_tokens")
            .insert(token_data)
            .execute()
        )

    async def _validate_refresh_token(
        self, token: str, client_id: str
    ) -> Optional[Dict[str, Any]]:
        client = await self._get_supabase_client()
        result = await asyncio.to_thread(
            lambda: client.client.table("oauth_refresh_tokens")
            .select("*")
            .eq("token", token)
            .eq("client_id", client_id)
            .eq("revoked", False)
            .execute()
        )
        if result.data:
            data = result.data[0]
            if datetime.fromisoformat(data["expires_at"]) > datetime.utcnow():
                return data
        return None

    async def _revoke_refresh_token(self, token: str):
        client = await self._get_supabase_client()
        await asyncio.to_thread(
            lambda: client.client.table("oauth_refresh_tokens")
            .update({"revoked": True})
            .eq("token", token)
            .execute()
        )

    # === Utility ===

    def _generate_client_id(self) -> str:
        return f"client_{secrets.token_urlsafe(32)}"

    def _generate_authorization_code(self) -> str:
        return secrets.token_urlsafe(32)

    async def _generate_access_token(
        self, user_id: str, client_id: str, scope: str
    ) -> str:
        payload = {
            "sub": user_id,
            "client_id": client_id,
            "scope": scope,
            "token_type": "access_token",
            "iat": datetime.utcnow().timestamp(),
            "exp": (
                datetime.utcnow() + timedelta(minutes=self.token_expiry_minutes)
            ).timestamp(),
            "iss": "publish-ai-oauth",
        }
        return jwt.encode(payload, settings.secret_key, algorithm="HS256")

    async def _generate_refresh_token(
        self, user_id: str, client_id: str, scope: str
    ) -> str:
        token = secrets.token_urlsafe(48)
        data = {
            "token": token,
            "user_id": user_id,
            "client_id": client_id,
            "scope": scope,
            "expires_at": (
                datetime.utcnow() + timedelta(days=self.refresh_token_expiry_days)
            ).isoformat(),
            "created_at": datetime.utcnow().isoformat(),
            "revoked": False,
        }
        await self._store_refresh_token(data)
        return token

    def _validate_redirect_uri(self, uri: str) -> bool:
        try:
            parsed = urlparse(uri)
            if not parsed.scheme or not parsed.netloc:
                return False
            if settings.sentry_environment == "production" and parsed.scheme != "https":
                return False
            if settings.sentry_environment == "production" and "localhost" in parsed.netloc:
                return False
            return True
        except Exception:
            return False

    def _verify_pkce(self, challenge: str, verifier: str, method: str) -> bool:
        if method == "plain":
            return challenge == verifier
        if method == "S256":
            expected = (
                base64.urlsafe_b64encode(hashlib.sha256(verifier.encode()).digest())
                .decode()
                .rstrip("=")
            )
            return challenge == expected
        return False


_oauth_manager: Optional[OAuth2Manager] = None


async def get_oauth_manager() -> OAuth2Manager:
    global _oauth_manager
    if _oauth_manager is None:
        _oauth_manager = OAuth2Manager()
    return _oauth_manager


async def get_oauth_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(oauth2_scheme),
) -> Optional[Dict[str, Any]]:
    if not credentials:
        return None
    oauth_manager = await get_oauth_manager()
    token_data = await oauth_manager.validate_token(credentials.credentials)
    if not token_data:
        return None
    try:
        supabase_client_instance = await get_supabase_client()
        result = await asyncio.to_thread(
            lambda: supabase_client_instance.client.table("users")
            .select("*")
            .eq("id", token_data["sub"])
            .execute()
        )
        if result.data:
            user = result.data[0]
            user["scope"] = token_data.get("scope", "")
            return user
        return None
    except Exception as e:
        logger.error(f"Failed to get user from Supabase: {e}")
        return None


def require_oauth_scopes(required_scopes: List[str]):

    def check_scopes(user_payload: Dict[str, Any] = Depends(get_oauth_user)):
        if not user_payload:
            raise HTTPException(status_code=401, detail="Not authenticated")
        granted_scopes = set(user_payload.get("scope", "").split())
        if not set(required_scopes).issubset(granted_scopes):
            raise HTTPException(status_code=403, detail="Insufficient scope")
        return user_payload

    return check_scopes
