import logging
from typing import Optional, Dict, Any, Union, Literal
from datetime import datetime
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from supabase import Client
from gotrue.errors import AuthError
from gotrue.types import (
    Provider,
    SignInWithOAuthCredentialsOptions,
    SignInWithSSOCredentials,
    SignInWithSSOOptions,
)

from supabase import create_client
from app.config import settings
from app.models.supabase_models import get_user_model

logger = logging.getLogger(__name__)

security = HTTPBearer()


class SupabaseAuthService:
    """Supabase-based authentication service"""

    def __init__(self):
        self.supabase_client: Optional[Client] = None

    async def _get_supabase_client(self) -> Client:
        if not self.supabase_client:
            # Create synchronous client for auth operations
            self.supabase_client = create_client(
                supabase_url=settings.supabase_url,
                supabase_key=settings.supabase_service_key,
            )
        if not self.supabase_client:
            raise Exception("Supabase client failed to initialize")
        return self.supabase_client

    async def register_user(
        self, email: str, password: str, full_name: str
    ) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()

            # Use admin client for server-side user creation with service role
            auth_response = client.auth.admin.create_user(
                {
                    "email": email,
                    "password": password,
                    "email_confirm": True,  # Auto-confirm email for admin creation
                    "user_metadata": {"full_name": full_name or email.split("@")[0]},
                }
            )

            if not auth_response.user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User registration failed",
                )

            user_id = auth_response.user.id
            user_model = await get_user_model()
            user_profile = await user_model.create_user(
                {
                    "id": user_id,
                    "email": email,
                    "full_name": full_name or email.split("@")[0],
                    "subscription_tier": "free",
                }
            )

            # For admin-created users, generate a session manually if needed
            # or direct them to sign in after creation
            logger.info(f"User registered successfully via admin: {email}")
            return {
                "user": user_profile,
                "access_token": None,  # Admin creation doesn't return session
                "refresh_token": None,
                "message": "User registered successfully. You can now sign in with your credentials.",
            }

        except AuthError as e:
            logger.error(f"Supabase auth error during registration: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except Exception as e:
            logger.error(f"Registration error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed",
            )

    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()
            auth_response = client.auth.sign_in_with_password(
                {"email": email, "password": password}
            )

            if not auth_response.user or not auth_response.session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password",
                )

            user_id = auth_response.user.id
            user_model = await get_user_model()
            user_profile = await user_model.get_user_by_id(user_id)

            if not user_profile:
                user_profile = await user_model.create_user(
                    {
                        "id": user_id,
                        "email": email,
                        "full_name": auth_response.user.user_metadata.get(
                            "full_name", email.split("@")[0]
                        ),
                        "subscription_tier": "free",
                    }
                )

            await user_model.update_user(
                user_id, {"last_login": datetime.utcnow().isoformat()}
            )

            logger.info(f"User logged in successfully: {email}")
            return {
                "user": user_profile,
                "access_token": auth_response.session.access_token,
                "refresh_token": auth_response.session.refresh_token,
                "expires_at": auth_response.session.expires_at,
                "token_type": "bearer",
            }

        except AuthError as e:
            logger.error(f"Supabase auth error during login: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password",
            )
        except Exception as e:
            logger.error(f"Login error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Login failed"
            )

    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()
            auth_response = client.auth.refresh_session(refresh_token)

            if not auth_response.session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token",
                )

            return {
                "access_token": auth_response.session.access_token,
                "refresh_token": auth_response.session.refresh_token,
                "expires_at": auth_response.session.expires_at,
                "token_type": "bearer",
            }

        except AuthError as e:
            logger.error(f"Token refresh error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Token refresh failed"
            )

    async def logout_user(self, access_token: str) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()
            client.auth.set_session(access_token, "")
            client.auth.sign_out()
            logger.info("User logged out successfully")
            return {"message": "Logged out successfully"}
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return {"message": "Logged out"}

    async def get_current_user_from_token(
        self, access_token: str
    ) -> Optional[Dict[str, Any]]:
        try:
            client = await self._get_supabase_client()
            user_response = client.auth.get_user(access_token)
            if user_response is None or user_response.user is None:
                return None
            user_id = user_response.user.id
            user_model = await get_user_model()
            return await user_model.get_user_by_id(user_id)
        except Exception as e:
            logger.error(f"Failed to get current user: {e}")
            return None

    async def verify_token(self, token: str) -> Optional[str]:
        try:
            client = await self._get_supabase_client()
            user_response = client.auth.get_user(token)
            if user_response is None or user_response.user is None:
                return None
            return user_response.user.id
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None

    async def reset_password(self, email: str) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()
            client.auth.reset_password_email(email)
            return {"message": "Password reset email sent"}
        except AuthError as e:
            logger.error(f"Password reset error: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    async def sign_in_with_oauth(
        self, provider: str, redirect_url: str
    ) -> Dict[str, Any]:
        """Initiate OAuth sign-in with supported providers"""
        try:
            client = await self._get_supabase_client()

            # Supported OAuth providers
            supported_providers = [
                "google",
                "github",
                "facebook",
                "twitter",
                "discord",
                "linkedin",
                "spotify",
                "slack",
                "microsoft",
                "apple",
            ]

            if provider not in supported_providers:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported OAuth provider. Supported: {supported_providers}",
                )

            # Generate OAuth URL
            # Cast provider string to Provider type
            provider_typed = provider  # type: Provider  # type: ignore
            auth_response = client.auth.sign_in_with_oauth(
                credentials={
                    "provider": provider_typed,
                    "options": {
                        "redirect_to": redirect_url
                        or f"{settings.frontend_url}/auth/callback"
                    },
                }
            )

            logger.info(f"OAuth sign-in initiated for provider: {provider}")
            return {
                "provider": provider,
                "url": auth_response.url,
                "message": f"Redirect to {provider} for authentication",
            }

        except AuthError as e:
            logger.error(f"OAuth sign-in error: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except Exception as e:
            logger.error(f"OAuth error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OAuth sign-in failed",
            )

    async def sign_in_with_sso(
        self, domain: Optional[str] = None, provider_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sign in with SSO using domain or provider ID"""
        try:
            client = await self._get_supabase_client()

            if not domain and not provider_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Either domain or provider_id must be provided",
                )

            # Construct SSO credentials properly
            if domain:
                sso_credentials = SignInWithSSOCredentials(
                    domain=domain,
                    options=None  # type: ignore
                )
            elif provider_id:
                sso_credentials = SignInWithSSOCredentials(
                    provider_id=provider_id,
                    options=None  # type: ignore
                )
            else:
                # This shouldn't happen due to earlier check, but keeping for safety
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Either domain or provider_id must be provided",
                )

            auth_response = client.auth.sign_in_with_sso(credentials=sso_credentials)

            logger.info(f"SSO sign-in initiated for domain: {domain or 'provider_id'}")
            return {
                "url": auth_response.url,
                "message": "Redirect to SSO provider for authentication",
            }

        except AuthError as e:
            logger.error(f"SSO sign-in error: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except Exception as e:
            logger.error(f"SSO error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="SSO sign-in failed",
            )

    async def handle_oauth_callback(
        self, access_token: str, refresh_token: str
    ) -> Dict[str, Any]:
        """Handle OAuth callback and create/update user profile"""
        try:
            client = await self._get_supabase_client()

            # Set the session with tokens from OAuth callback
            auth_response = client.auth.set_session(access_token, refresh_token)

            if not auth_response.user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="OAuth callback failed - no user data",
                )

            user_id = auth_response.user.id
            email = auth_response.user.email

            # Get user metadata from OAuth provider
            user_metadata = auth_response.user.user_metadata or {}
            full_name = (
                user_metadata.get("full_name")
                or user_metadata.get("name")
                or f"{user_metadata.get('first_name', '')} {user_metadata.get('last_name', '')}".strip()
                or (email.split("@")[0] if email is not None else None)
            )

            # Create or update user profile
            user_model = await get_user_model()
            user_profile = await user_model.get_user_by_id(user_id)

            if not user_profile:
                # Create new user profile for OAuth user
                user_profile = await user_model.create_user(
                    {
                        "id": user_id,
                        "email": email,
                        "full_name": full_name,
                        "subscription_tier": "free",
                        "oauth_provider": user_metadata.get("provider"),
                        "oauth_metadata": user_metadata,
                    }
                )
                logger.info(f"New OAuth user created: {email}")
            else:
                # Update last login
                await user_model.update_user(
                    user_id, {"last_login": datetime.utcnow().isoformat()}
                )
                logger.info(f"OAuth user logged in: {email}")

            return {
                "user": user_profile,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_at": (
                    auth_response.session.expires_at if auth_response.session else None
                ),
                "token_type": "bearer",
                "provider": user_metadata.get("provider", "oauth"),
            }

        except AuthError as e:
            logger.error(f"OAuth callback error: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except Exception as e:
            logger.error(f"OAuth callback processing error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OAuth callback processing failed",
            )

    async def update_password(
        self, access_token: str, new_password: str
    ) -> Dict[str, Any]:
        try:
            client = await self._get_supabase_client()
            client.auth.set_session(access_token, "")
            client.auth.update_user({"password": new_password})
            return {"message": "Password updated successfully"}
        except AuthError as e:
            logger.error(f"Password update error: {e}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# Global instance
auth_service = SupabaseAuthService()

# =====================================
# FASTAPI DEPENDENCIES
# =====================================


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> Dict[str, Any]:
    """FastAPI dependency to get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        token = credentials.credentials

        # Get user from token
        user = await auth_service.get_current_user_from_token(token)

        if user is None:
            raise credentials_exception

        return user

    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise credentials_exception


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[Dict[str, Any]]:
    """Optional authentication - returns None if not authenticated"""
    if not credentials:
        return None

    try:
        token = credentials.credentials
        user = await auth_service.get_current_user_from_token(token)
        return user
    except:
        return None


async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """Require admin role"""
    if current_user.get("subscription_tier") != "enterprise":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required"
        )
    return current_user


async def require_pro_or_higher(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """Require pro or enterprise subscription"""
    tier = current_user.get("subscription_tier", "free")
    if tier not in ["pro", "enterprise"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Pro subscription required"
        )
    return current_user


# =====================================
# UTILITY FUNCTIONS
# =====================================


def get_auth_service() -> SupabaseAuthService:
    """Get auth service instance"""
    return auth_service


async def create_access_token(user_id: str, email: str) -> str:
    """Create access token (for compatibility - Supabase handles this)"""
    # This is handled by Supabase Auth, but kept for compatibility
    logger.warning("create_access_token called - Supabase handles token creation")
    return ""


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password (handled by Supabase)"""
    logger.warning("verify_password called - Supabase handles password verification")
    return False


def get_password_hash(password: str) -> str:
    """Hash password (handled by Supabase)"""
    logger.warning("get_password_hash called - Supabase handles password hashing")
    return ""


# =====================================
# COMPATIBILITY WITH OLD AUTH SYSTEM
# =====================================


# These provide compatibility with the old auth system
async def get_current_user_compat(
    credentials: HTTPAuthorizationCredentials = Depends(security),
):
    """Compatibility function for old auth system"""
    return await get_current_user(credentials)
