"""
API Key Management System with rotation and granular permissions.

This module provides:
- Secure API key generation and validation
- Key rotation and expiration policies
- Granular permission scoping
- Usage tracking and rate limiting
- Emergency revocation capabilities
- Integration with audit logging
"""

import secrets
import hashlib
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
import json

from pydantic import BaseModel, Field
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.monitoring.audit_logger import audit_log
from app.schemas.user import UserResponse

logger = logging.getLogger(__name__)
settings = get_settings()


class APIKeyStatus(str, Enum):
    """API key status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    SUSPENDED = "suspended"


class APIKeyPermission(BaseModel):
    """API key permission definition."""
    resource: str = Field(..., description="Resource name (e.g., 'books', 'analytics')")
    actions: List[str] = Field(..., description="Allowed actions (e.g., ['read', 'write'])")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Additional conditions")


class APIKey(BaseModel):
    """API key model."""
    id: str
    name: str
    key_hash: str
    key_prefix: str = Field(..., description="First 8 characters for identification")
    user_id: str
    permissions: List[APIKeyPermission]
    status: APIKeyStatus = APIKeyStatus.ACTIVE
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    usage_count: int = 0
    
    # Security
    allowed_ips: List[str] = Field(default_factory=list)
    rate_limit_per_hour: int = 1000
    
    # Management
    created_by: str
    notes: Optional[str] = None


class APIKeyUsage(BaseModel):
    """API key usage tracking."""
    key_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    endpoint: str
    method: str
    ip_address: str
    user_agent: str
    response_status: int
    processing_time_ms: float


class APIKeyManager:
    """
    Manages API keys with advanced security features.
    
    Features:
    - Secure key generation with cryptographic randomness
    - Permission-based access control
    - Usage tracking and rate limiting
    - Key rotation and expiration policies
    - IP allowlisting and security restrictions
    - Comprehensive audit logging
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.default_expiry_days = 365
        self.key_length = 32
        self.prefix_length = 8
        
        # Default permission templates
        self.permission_templates = {
            "read_only": [
                APIKeyPermission(resource="books", actions=["read"]),
                APIKeyPermission(resource="analytics", actions=["read"]),
                APIKeyPermission(resource="trends", actions=["read"]),
            ],
            "content_manager": [
                APIKeyPermission(resource="books", actions=["read", "write", "create"]),
                APIKeyPermission(resource="publications", actions=["read", "write"]),
                APIKeyPermission(resource="trends", actions=["read"]),
            ],
            "analytics_admin": [
                APIKeyPermission(resource="analytics", actions=["read", "write"]),
                APIKeyPermission(resource="monitoring", actions=["read"]),
                APIKeyPermission(resource="reports", actions=["read", "create"]),
            ],
            "full_access": [
                APIKeyPermission(resource="*", actions=["*"]),
            ]
        }
    
    async def create_api_key(
        self,
        name: str,
        user_id: str,
        permissions: List[APIKeyPermission],
        created_by: str,
        expires_days: Optional[int] = None,
        allowed_ips: List[str] = None,
        rate_limit_per_hour: int = 1000,
        notes: Optional[str] = None
    ) -> Tuple[APIKey, str]:
        """
        Create a new API key.
        
        Returns:
            Tuple of (APIKey object, raw key string)
        """
        
        # Generate secure API key
        raw_key = self._generate_api_key()
        key_hash = self._hash_key(raw_key)
        key_prefix = raw_key[:self.prefix_length]
        
        # Calculate expiration
        expires_at = None
        if expires_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_days)
        elif self.default_expiry_days:
            expires_at = datetime.utcnow() + timedelta(days=self.default_expiry_days)
        
        # Validate permissions
        await self._validate_permissions(permissions, user_id)
        
        # Create API key object
        api_key = APIKey(
            id=self._generate_key_id(),
            name=name,
            key_hash=key_hash,
            key_prefix=key_prefix,
            user_id=user_id,
            permissions=permissions,
            expires_at=expires_at,
            allowed_ips=allowed_ips or [],
            rate_limit_per_hour=rate_limit_per_hour,
            created_by=created_by,
            notes=notes
        )
        
        # Store in database
        await self._store_api_key(api_key)
        
        await audit_log(
            "api_key_created",
            {
                "key_id": api_key.id,
                "key_name": name,
                "key_prefix": key_prefix,
                "permissions": [p.dict() for p in permissions],
                "created_by": created_by
            },
            user_id=user_id
        )
        
        logger.info(f"API key created: {name} ({api_key.id}) for user {user_id}")
        
        return api_key, raw_key
    
    async def create_api_key_from_template(
        self,
        name: str,
        user_id: str,
        template: str,
        created_by: str,
        **kwargs
    ) -> Tuple[APIKey, str]:
        """Create API key from permission template."""
        
        if template not in self.permission_templates:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown template: {template}. Available: {list(self.permission_templates.keys())}"
            )
        
        permissions = self.permission_templates[template].copy()
        
        return await self.create_api_key(
            name=name,
            user_id=user_id,
            permissions=permissions,
            created_by=created_by,
            **kwargs
        )
    
    async def validate_api_key(self, raw_key: str, request: Request) -> Optional[APIKey]:
        """Validate API key and check permissions."""
        
        # Hash the provided key
        key_hash = self._hash_key(raw_key)
        key_prefix = raw_key[:self.prefix_length]
        
        # Get API key from database
        api_key = await self._get_api_key_by_hash(key_hash)
        
        if not api_key:
            await audit_log(
                "api_key_validation_failed",
                {"reason": "key_not_found", "key_prefix": key_prefix},
                user_id=None
            )
            return None
        
        # Check key status
        if api_key.status != APIKeyStatus.ACTIVE:
            await audit_log(
                "api_key_validation_failed",
                {"reason": "key_inactive", "key_id": api_key.id, "status": api_key.status},
                user_id=api_key.user_id
            )
            return None
        
        # Check expiration
        if api_key.expires_at and api_key.expires_at < datetime.utcnow():
            await self._update_key_status(api_key.id, APIKeyStatus.EXPIRED)
            await audit_log(
                "api_key_validation_failed",
                {"reason": "key_expired", "key_id": api_key.id},
                user_id=api_key.user_id
            )
            return None
        
        # Check IP allowlist
        if api_key.allowed_ips:
            client_ip = self._get_client_ip(request)
            if client_ip not in api_key.allowed_ips:
                await audit_log(
                    "api_key_validation_failed",
                    {"reason": "ip_not_allowed", "key_id": api_key.id, "client_ip": client_ip},
                    user_id=api_key.user_id
                )
                return None
        
        # Check rate limiting
        if not await self._check_rate_limit(api_key):
            await audit_log(
                "api_key_validation_failed",
                {"reason": "rate_limit_exceeded", "key_id": api_key.id},
                user_id=api_key.user_id
            )
            return None
        
        # Update usage
        await self._update_key_usage(api_key, request)
        
        return api_key
    
    async def check_permission(
        self,
        api_key: APIKey,
        resource: str,
        action: str,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Check if API key has permission for resource/action."""
        
        for permission in api_key.permissions:
            # Check for wildcard permissions
            if permission.resource == "*" and "*" in permission.actions:
                return True
            
            # Check specific resource permissions
            if permission.resource == resource or permission.resource == "*":
                if action in permission.actions or "*" in permission.actions:
                    # Check additional conditions if present
                    if permission.conditions and context:
                        if not self._check_conditions(permission.conditions, context):
                            continue
                    return True
        
        return False
    
    async def rotate_api_key(self, key_id: str, user_id: str) -> Tuple[APIKey, str]:
        """Rotate an API key (generate new key, keep same permissions)."""
        
        # Get existing key
        old_key = await self._get_api_key_by_id(key_id)
        if not old_key or old_key.user_id != user_id:
            raise HTTPException(status_code=404, detail="API key not found")
        
        # Generate new key
        raw_key = self._generate_api_key()
        key_hash = self._hash_key(raw_key)
        key_prefix = raw_key[:self.prefix_length]
        
        # Update key in database
        await self._update_key_credentials(key_id, key_hash, key_prefix)
        
        # Get updated key
        new_key = await self._get_api_key_by_id(key_id)
        
        await audit_log(
            "api_key_rotated",
            {"key_id": key_id, "old_prefix": old_key.key_prefix, "new_prefix": key_prefix},
            user_id=user_id
        )
        
        logger.info(f"API key rotated: {key_id}")
        
        return new_key, raw_key
    
    async def revoke_api_key(self, key_id: str, user_id: str, reason: str = "user_requested"):
        """Revoke an API key."""
        
        api_key = await self._get_api_key_by_id(key_id)
        if not api_key or api_key.user_id != user_id:
            raise HTTPException(status_code=404, detail="API key not found")
        
        await self._update_key_status(key_id, APIKeyStatus.REVOKED)
        
        await audit_log(
            "api_key_revoked",
            {"key_id": key_id, "reason": reason},
            user_id=user_id
        )
        
        logger.info(f"API key revoked: {key_id} (reason: {reason})")
    
    async def list_user_api_keys(self, user_id: str) -> List[APIKey]:
        """List all API keys for a user."""
        
        try:
            response = await self.supabase.table("api_keys").select("*").eq("user_id", user_id).execute()
            
            keys = []
            for data in response.data:
                # Parse permissions JSON
                permissions = [APIKeyPermission(**p) for p in json.loads(data.get("permissions", "[]"))]
                data["permissions"] = permissions
                
                # Parse datetime fields
                for field in ["created_at", "expires_at", "last_used_at"]:
                    if data.get(field):
                        data[field] = datetime.fromisoformat(data[field])
                
                keys.append(APIKey(**data))
            
            return keys
            
        except Exception as e:
            logger.error(f"Failed to list API keys for user {user_id}: {e}")
            return []
    
    async def get_api_key_usage(self, key_id: str, days: int = 30) -> List[APIKeyUsage]:
        """Get usage statistics for an API key."""
        
        since_date = datetime.utcnow() - timedelta(days=days)
        
        try:
            response = await self.supabase.table("api_key_usage").select("*").eq("key_id", key_id).gte("timestamp", since_date.isoformat()).execute()
            
            usage = []
            for data in response.data:
                data["timestamp"] = datetime.fromisoformat(data["timestamp"])
                usage.append(APIKeyUsage(**data))
            
            return usage
            
        except Exception as e:
            logger.error(f"Failed to get API key usage for {key_id}: {e}")
            return []
    
    def _generate_api_key(self) -> str:
        """Generate a cryptographically secure API key."""
        return f"pak_{secrets.token_urlsafe(self.key_length)}"
    
    def _generate_key_id(self) -> str:
        """Generate a unique key ID."""
        return f"key_{secrets.token_urlsafe(16)}"
    
    def _hash_key(self, raw_key: str) -> str:
        """Hash API key for secure storage."""
        return hashlib.pbkdf2_hmac('sha256', raw_key.encode(), settings.secret_key.encode(), 100000).hex()
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def _validate_permissions(self, permissions: List[APIKeyPermission], user_id: str):
        """Validate that user can grant these permissions."""
        # In a real implementation, you'd check user roles and permissions
        # For now, we'll do basic validation
        
        for permission in permissions:
            if permission.resource == "*" and "*" in permission.actions:
                # Only admin users can create keys with full access
                # This would check user role in a real implementation
                pass
    
    def _check_conditions(self, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Check if conditions are met given context."""
        for key, expected_value in conditions.items():
            if key not in context or context[key] != expected_value:
                return False
        return True
    
    async def _check_rate_limit(self, api_key: APIKey) -> bool:
        """Check if API key is within rate limits."""
        # Get usage in the last hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        
        try:
            response = await self.supabase.table("api_key_usage").select("id").eq("key_id", api_key.id).gte("timestamp", one_hour_ago.isoformat()).execute()
            
            usage_count = len(response.data)
            return usage_count < api_key.rate_limit_per_hour
            
        except Exception as e:
            logger.error(f"Failed to check rate limit for key {api_key.id}: {e}")
            # Allow on error to avoid blocking legitimate requests
            return True
    
    async def _store_api_key(self, api_key: APIKey):
        """Store API key in database."""
        try:
            await self.supabase.table("api_keys").insert({
                "id": api_key.id,
                "name": api_key.name,
                "key_hash": api_key.key_hash,
                "key_prefix": api_key.key_prefix,
                "user_id": api_key.user_id,
                "permissions": json.dumps([p.dict() for p in api_key.permissions]),
                "status": api_key.status.value,
                "created_at": api_key.created_at.isoformat(),
                "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
                "allowed_ips": api_key.allowed_ips,
                "rate_limit_per_hour": api_key.rate_limit_per_hour,
                "created_by": api_key.created_by,
                "notes": api_key.notes,
                "usage_count": 0
            }).execute()
        except Exception as e:
            logger.error(f"Failed to store API key: {e}")
            raise
    
    async def _get_api_key_by_hash(self, key_hash: str) -> Optional[APIKey]:
        """Get API key by hash."""
        try:
            response = await self.supabase.table("api_keys").select("*").eq("key_hash", key_hash).execute()
            
            if response.data:
                data = response.data[0]
                return self._parse_api_key_data(data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get API key by hash: {e}")
            return None
    
    async def _get_api_key_by_id(self, key_id: str) -> Optional[APIKey]:
        """Get API key by ID."""
        try:
            response = await self.supabase.table("api_keys").select("*").eq("id", key_id).execute()
            
            if response.data:
                data = response.data[0]
                return self._parse_api_key_data(data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get API key by ID: {e}")
            return None
    
    def _parse_api_key_data(self, data: Dict[str, Any]) -> APIKey:
        """Parse API key data from database."""
        # Parse permissions JSON
        permissions = [APIKeyPermission(**p) for p in json.loads(data.get("permissions", "[]"))]
        data["permissions"] = permissions
        
        # Parse datetime fields
        for field in ["created_at", "expires_at", "last_used_at"]:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # Parse status enum
        data["status"] = APIKeyStatus(data.get("status", "active"))
        
        return APIKey(**data)
    
    async def _update_key_status(self, key_id: str, status: APIKeyStatus):
        """Update API key status."""
        try:
            await self.supabase.table("api_keys").update({"status": status.value}).eq("id", key_id).execute()
        except Exception as e:
            logger.error(f"Failed to update key status: {e}")
    
    async def _update_key_credentials(self, key_id: str, key_hash: str, key_prefix: str):
        """Update API key credentials (for rotation)."""
        try:
            await self.supabase.table("api_keys").update({
                "key_hash": key_hash,
                "key_prefix": key_prefix
            }).eq("id", key_id).execute()
        except Exception as e:
            logger.error(f"Failed to update key credentials: {e}")
            raise
    
    async def _update_key_usage(self, api_key: APIKey, request: Request):
        """Update API key usage statistics."""
        try:
            # Update last used timestamp and usage count
            await self.supabase.table("api_keys").update({
                "last_used_at": datetime.utcnow().isoformat(),
                "usage_count": api_key.usage_count + 1
            }).eq("id", api_key.id).execute()
            
            # Record detailed usage
            usage = APIKeyUsage(
                key_id=api_key.id,
                endpoint=str(request.url.path),
                method=request.method,
                ip_address=self._get_client_ip(request),
                user_agent=request.headers.get("User-Agent", ""),
                response_status=200,  # Will be updated by middleware
                processing_time_ms=0  # Will be updated by middleware
            )
            
            await self.supabase.table("api_key_usage").insert(usage.dict()).execute()
            
        except Exception as e:
            logger.error(f"Failed to update key usage: {e}")


# Global API key manager
_api_key_manager: Optional[APIKeyManager] = None


async def get_api_key_manager() -> APIKeyManager:
    """Get or create API key manager."""
    global _api_key_manager
    
    if _api_key_manager is None:
        _api_key_manager = APIKeyManager()
    
    return _api_key_manager


# API key security dependency
api_key_scheme = HTTPBearer(auto_error=False)


async def get_api_key_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(api_key_scheme)
) -> Optional[UserResponse]:
    """Get user from API key."""
    if not credentials:
        return None
    
    # Check if it's an API key (starts with "pak_")
    if not credentials.credentials.startswith("pak_"):
        return None
    
    api_key_manager = await get_api_key_manager()
    api_key = await api_key_manager.validate_api_key(credentials.credentials, request)
    
    if not api_key:
        return None
    
    # Get user from Supabase
    try:
        supabase = get_supabase_client()
        response = supabase.table("users").select("*").eq("id", api_key.user_id).execute()
        
        if response.data:
            user = UserResponse(**response.data[0])
            # Add API key info to user context
            user.api_key_id = api_key.id
            user.api_key_permissions = api_key.permissions
            return user
        
    except Exception as e:
        logger.error(f"Failed to get user from API key: {e}")
    
    return None


def require_api_key_permission(resource: str, action: str):
    """Dependency to require specific API key permissions."""
    async def check_permission(
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(api_key_scheme)
    ):
        if not credentials or not credentials.credentials.startswith("pak_"):
            raise HTTPException(status_code=401, detail="API key required")
        
        api_key_manager = await get_api_key_manager()
        api_key = await api_key_manager.validate_api_key(credentials.credentials, request)
        
        if not api_key:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        # Check permission
        has_permission = await api_key_manager.check_permission(api_key, resource, action)
        if not has_permission:
            raise HTTPException(
                status_code=403,
                detail=f"Insufficient permissions. Required: {resource}:{action}"
            )
        
        return api_key
    
    return check_permission
