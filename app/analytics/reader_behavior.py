### app/analytics/reader_behavior.py - Reader Behavior Analysis

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# Import models
from app.models.supabase_models import Book

logger = logging.getLogger(__name__)

# Enums for reader behavior
class EngagementType(Enum):
    """Types of reader engagement events"""
    PAGE_VIEW = "page_view"
    TIME_SPENT = "time_spent"
    HIGHLIGHT = "highlight"
    NOTE = "note"
    BOOKMARK = "bookmark"
    SHARE = "share"
    RATING = "rating"
    REVIEW = "review"
    COMPLETION = "completion"
    DROP_OFF = "drop_off"

class ReadingSpeed(Enum):
    """Reading speed categories"""
    SLOW = "slow"
    NORMAL = "normal"
    FAST = "fast"
    SKIMMING = "skimming"

# Data classes for reader behavior analysis
@dataclass
class ReaderEngagementEvent:
    """Individual reader engagement event"""
    user_id: int
    book_id: int
    event_type: EngagementType
    timestamp: datetime
    page_number: Optional[int] = None
    chapter_number: Optional[int] = None
    time_spent_seconds: Optional[int] = None
    content_snippet: Optional[str] = None
    sentiment_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ReadingPattern:
    """Reading pattern analysis"""
    average_session_duration: float
    reading_speed: ReadingSpeed
    preferred_reading_times: List[str]
    engagement_frequency: float
    completion_rate: float
    retention_rate: float

@dataclass
class DropOffAnalysis:
    """Analysis of where readers drop off"""
    critical_drop_points: List[Dict[str, Any]]
    drop_off_rate_by_chapter: Dict[int, float]
    common_drop_off_reasons: List[str]
    recovery_opportunities: List[str]

@dataclass
class EngagementHeatmap:
    """Engagement heatmap data"""
    chapter_engagement_scores: Dict[int, float]
    page_engagement_scores: Dict[int, float]
    time_based_engagement: Dict[str, float]
    interaction_hotspots: List[Dict[str, Any]]

@dataclass
class SentimentAnalysis:
    """Reader sentiment analysis"""
    overall_sentiment: float
    sentiment_by_chapter: Dict[int, float]
    sentiment_trends: List[Dict[str, Any]]
    emotional_peaks: List[Dict[str, Any]]
    satisfaction_indicators: Dict[str, float]

@dataclass
class CompletionPredictors:
    """Factors that predict completion"""
    early_engagement_indicators: List[str]
    risk_factors: List[str]
    positive_signals: List[str]
    completion_probability_model: Dict[str, float]

@dataclass
class PersonalizationOpportunities:
    """Opportunities for content personalization"""
    content_preferences: Dict[str, Any]
    optimal_chapter_length: int
    preferred_content_types: List[str]
    engagement_triggers: List[str]
    customization_suggestions: List[str]

@dataclass
class ReaderInsights:
    """Complete reader behavior insights"""
    book_id: int
    reading_patterns: ReadingPattern
    drop_off_analysis: DropOffAnalysis
    engagement_heatmap: EngagementHeatmap
    sentiment_analysis: SentimentAnalysis
    completion_predictors: CompletionPredictors
    personalization_opportunities: PersonalizationOpportunities
    recommended_improvements: List[str]
    analysis_timestamp: datetime
    confidence_score: float

class ReaderBehaviorAnalyzer:
    """Advanced analytics on how readers interact with content"""

    def __init__(self):
        """Initialize the reader behavior analyzer"""
        self.logger = logging.getLogger(__name__)

    async def analyze_reader_engagement(
        self, 
        book: Book,
        engagement_data: List[ReaderEngagementEvent]
    ) -> ReaderInsights:
        """Deep analysis of reader behavior patterns"""
        
        # Analyze reading patterns
        reading_patterns = await self._analyze_reading_patterns(engagement_data)
        
        # Identify drop-off points
        drop_off_analysis = await self._identify_drop_off_points(engagement_data)
        
        # Engagement heatmap
        engagement_heatmap = await self._create_engagement_heatmap(engagement_data)
        
        # Reader sentiment analysis
        sentiment_analysis = await self._analyze_reader_sentiment(engagement_data)
        
        # Completion predictors
        completion_predictors = await self._identify_completion_predictors(engagement_data)
        
        # Personalization opportunities
        personalization_ops = await self._identify_personalization_opportunities(engagement_data)
        
        # Get book_id from engagement data or use default
        book_id = engagement_data[0].book_id if engagement_data else 0

        return ReaderInsights(
            book_id=book_id,
            reading_patterns=reading_patterns,
            drop_off_analysis=drop_off_analysis,
            engagement_heatmap=engagement_heatmap,
            sentiment_analysis=sentiment_analysis,
            completion_predictors=completion_predictors,
            personalization_opportunities=personalization_ops,
            recommended_improvements=await self._generate_improvement_recommendations(
                reading_patterns, drop_off_analysis, sentiment_analysis
            ),
            analysis_timestamp=datetime.now(),
            confidence_score=0.85  # Mock confidence score
        )

    async def _analyze_reading_patterns(self, engagement_data: List[ReaderEngagementEvent]) -> ReadingPattern:
        """Analyze reading patterns from engagement data"""
        try:
            if not engagement_data:
                return self._get_default_reading_pattern()

            # Calculate session durations
            sessions = self._group_events_by_session(engagement_data)
            session_durations = [session['duration'] for session in sessions if session['duration'] > 0]
            avg_session_duration = sum(session_durations) / len(session_durations) if session_durations else 0

            # Determine reading speed
            reading_speed = self._calculate_reading_speed(engagement_data)

            # Find preferred reading times
            preferred_times = self._analyze_reading_times(engagement_data)

            # Calculate engagement metrics
            engagement_frequency = len(engagement_data) / max(1, len(set(e.user_id for e in engagement_data)))
            completion_events = [e for e in engagement_data if e.event_type == EngagementType.COMPLETION]
            completion_rate = len(completion_events) / max(1, len(set(e.user_id for e in engagement_data)))

            # Calculate retention (mock calculation)
            retention_rate = min(0.95, completion_rate * 1.2)

            return ReadingPattern(
                average_session_duration=avg_session_duration,
                reading_speed=reading_speed,
                preferred_reading_times=preferred_times,
                engagement_frequency=engagement_frequency,
                completion_rate=completion_rate,
                retention_rate=retention_rate
            )
        except Exception as e:
            self.logger.error(f"Error analyzing reading patterns: {str(e)}")
            return self._get_default_reading_pattern()

    async def _identify_drop_off_points(self, engagement_data: List[ReaderEngagementEvent]) -> DropOffAnalysis:
        """Identify where readers commonly drop off"""
        try:
            if not engagement_data:
                return self._get_default_drop_off_analysis()

            # Find drop-off events
            drop_off_events = [e for e in engagement_data if e.event_type == EngagementType.DROP_OFF]

            # Analyze drop-off by chapter
            chapter_drop_offs = {}
            for event in drop_off_events:
                if event.chapter_number:
                    chapter_drop_offs[event.chapter_number] = chapter_drop_offs.get(event.chapter_number, 0) + 1

            # Calculate drop-off rates
            total_readers = len(set(e.user_id for e in engagement_data))
            drop_off_rates = {
                chapter: count / max(1, total_readers)
                for chapter, count in chapter_drop_offs.items()
            }

            # Identify critical drop points
            critical_points = [
                {'chapter': chapter, 'rate': rate, 'severity': 'high' if rate > 0.3 else 'medium'}
                for chapter, rate in drop_off_rates.items() if rate > 0.2
            ]

            return DropOffAnalysis(
                critical_drop_points=critical_points,
                drop_off_rate_by_chapter=drop_off_rates,
                common_drop_off_reasons=['pacing_issues', 'content_difficulty', 'lack_of_engagement'],
                recovery_opportunities=['improve_chapter_hooks', 'add_interactive_elements', 'optimize_pacing']
            )
        except Exception as e:
            self.logger.error(f"Error analyzing drop-off points: {str(e)}")
            return self._get_default_drop_off_analysis()

    async def _create_engagement_heatmap(self, engagement_data: List[ReaderEngagementEvent]) -> EngagementHeatmap:
        """Create engagement heatmap from data"""
        try:
            if not engagement_data:
                return self._get_default_engagement_heatmap()

            # Chapter engagement scores
            chapter_engagement = {}
            for event in engagement_data:
                if event.chapter_number:
                    chapter_engagement[event.chapter_number] = chapter_engagement.get(event.chapter_number, 0) + 1

            # Normalize chapter scores
            max_engagement = max(chapter_engagement.values()) if chapter_engagement else 1
            chapter_scores = {
                chapter: count / max_engagement * 100
                for chapter, count in chapter_engagement.items()
            }

            # Page engagement scores (mock)
            page_scores = {i: 75.0 + (i % 3) * 10 for i in range(1, 21)}  # Mock page scores

            # Time-based engagement
            time_engagement = {
                'morning': 65.0,
                'afternoon': 80.0,
                'evening': 90.0,
                'night': 45.0
            }

            # Interaction hotspots
            hotspots = [
                {'type': 'highlight', 'frequency': 25, 'chapter': 3},
                {'type': 'bookmark', 'frequency': 15, 'chapter': 7},
                {'type': 'note', 'frequency': 10, 'chapter': 12}
            ]

            return EngagementHeatmap(
                chapter_engagement_scores=chapter_scores,
                page_engagement_scores=page_scores,
                time_based_engagement=time_engagement,
                interaction_hotspots=hotspots
            )
        except Exception as e:
            self.logger.error(f"Error creating engagement heatmap: {str(e)}")
            return self._get_default_engagement_heatmap()

    async def _analyze_reader_sentiment(self, engagement_data: List[ReaderEngagementEvent]) -> SentimentAnalysis:
        """Analyze reader sentiment from engagement data"""
        try:
            if not engagement_data:
                return self._get_default_sentiment_analysis()

            # Extract sentiment scores
            sentiment_events = [e for e in engagement_data if e.sentiment_score is not None]

            if not sentiment_events:
                # Mock sentiment analysis
                return SentimentAnalysis(
                    overall_sentiment=0.7,
                    sentiment_by_chapter={i: 0.6 + (i % 4) * 0.1 for i in range(1, 11)},
                    sentiment_trends=[
                        {'chapter': i, 'sentiment': 0.6 + (i % 4) * 0.1, 'trend': 'positive' if i % 2 == 0 else 'neutral'}
                        for i in range(1, 6)
                    ],
                    emotional_peaks=[
                        {'chapter': 3, 'emotion': 'excitement', 'intensity': 0.8},
                        {'chapter': 7, 'emotion': 'suspense', 'intensity': 0.9}
                    ],
                    satisfaction_indicators={
                        'engagement_level': 0.75,
                        'completion_satisfaction': 0.8,
                        'recommendation_likelihood': 0.7
                    }
                )

            # Calculate overall sentiment
            overall_sentiment = sum(e.sentiment_score for e in sentiment_events if e.sentiment_score is not None) / len(sentiment_events)

            # Sentiment by chapter
            chapter_sentiment = {}
            for event in sentiment_events:
                if event.chapter_number:
                    if event.chapter_number not in chapter_sentiment:
                        chapter_sentiment[event.chapter_number] = []
                    chapter_sentiment[event.chapter_number].append(event.sentiment_score)

            # Average sentiment per chapter
            chapter_avg_sentiment = {
                chapter: sum(scores) / len(scores)
                for chapter, scores in chapter_sentiment.items()
            }

            return SentimentAnalysis(
                overall_sentiment=overall_sentiment,
                sentiment_by_chapter=chapter_avg_sentiment,
                sentiment_trends=[
                    {'chapter': chapter, 'sentiment': sentiment, 'trend': 'positive' if sentiment > 0.6 else 'neutral'}
                    for chapter, sentiment in chapter_avg_sentiment.items()
                ],
                emotional_peaks=[
                    {'chapter': chapter, 'emotion': 'positive', 'intensity': sentiment}
                    for chapter, sentiment in chapter_avg_sentiment.items() if sentiment > 0.8
                ],
                satisfaction_indicators={
                    'engagement_level': min(overall_sentiment + 0.1, 1.0),
                    'completion_satisfaction': overall_sentiment,
                    'recommendation_likelihood': max(overall_sentiment - 0.1, 0.0)
                }
            )
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment: {str(e)}")
            return self._get_default_sentiment_analysis()

    async def _identify_completion_predictors(self, engagement_data: List[ReaderEngagementEvent]) -> CompletionPredictors:
        """Identify factors that predict completion"""
        try:
            if not engagement_data:
                return self._get_default_completion_predictors()

            # Analyze early engagement patterns
            early_events = [e for e in engagement_data if e.chapter_number and e.chapter_number <= 3]
            completion_events = [e for e in engagement_data if e.event_type == EngagementType.COMPLETION]

            # Early engagement indicators
            early_indicators = []
            if len(early_events) > 5:
                early_indicators.append('high_early_engagement')
            if any(e.event_type == EngagementType.HIGHLIGHT for e in early_events):
                early_indicators.append('early_highlighting')
            if any(e.event_type == EngagementType.BOOKMARK for e in early_events):
                early_indicators.append('early_bookmarking')

            # Risk factors
            risk_factors = []
            drop_off_events = [e for e in engagement_data if e.event_type == EngagementType.DROP_OFF]
            if len(drop_off_events) > len(completion_events):
                risk_factors.append('high_drop_off_rate')
            if not early_events:
                risk_factors.append('low_early_engagement')

            # Positive signals
            positive_signals = []
            if len(completion_events) > 0:
                positive_signals.append('completion_achieved')
            if any(e.event_type == EngagementType.SHARE for e in engagement_data):
                positive_signals.append('content_sharing')
            if any(e.event_type == EngagementType.RATING for e in engagement_data):
                positive_signals.append('rating_provided')

            return CompletionPredictors(
                early_engagement_indicators=early_indicators,
                risk_factors=risk_factors,
                positive_signals=positive_signals,
                completion_probability_model={
                    'early_engagement_weight': 0.4,
                    'content_quality_weight': 0.3,
                    'user_behavior_weight': 0.3
                }
            )
        except Exception as e:
            self.logger.error(f"Error identifying completion predictors: {str(e)}")
            return self._get_default_completion_predictors()

    async def _identify_personalization_opportunities(self, engagement_data: List[ReaderEngagementEvent]) -> PersonalizationOpportunities:
        """Identify opportunities for content personalization"""
        try:
            if not engagement_data:
                return self._get_default_personalization_opportunities()

            # Analyze content preferences
            highlight_events = [e for e in engagement_data if e.event_type == EngagementType.HIGHLIGHT]
            note_events = [e for e in engagement_data if e.event_type == EngagementType.NOTE]

            content_preferences = {
                'prefers_highlights': len(highlight_events) > 5,
                'prefers_notes': len(note_events) > 3,
                'interactive_content': len(highlight_events) + len(note_events) > 8
            }

            # Calculate optimal chapter length based on engagement
            chapter_engagement = {}
            for event in engagement_data:
                if event.chapter_number and event.time_spent_seconds:
                    chapter_engagement[event.chapter_number] = chapter_engagement.get(event.chapter_number, 0) + event.time_spent_seconds

            avg_chapter_time = sum(chapter_engagement.values()) / len(chapter_engagement) if chapter_engagement else 600
            optimal_length = int(avg_chapter_time / 60 * 250)  # Assume 250 words per minute reading speed

            # Preferred content types
            preferred_types = []
            if len(highlight_events) > len(note_events):
                preferred_types.append('visual_content')
            if len(note_events) > 0:
                preferred_types.append('thought_provoking_content')
            if any(e.event_type == EngagementType.SHARE for e in engagement_data):
                preferred_types.append('shareable_content')

            return PersonalizationOpportunities(
                content_preferences=content_preferences,
                optimal_chapter_length=optimal_length,
                preferred_content_types=preferred_types,
                engagement_triggers=['chapter_summaries', 'interactive_elements', 'progress_tracking'],
                customization_suggestions=[
                    'add_chapter_summaries',
                    'include_interactive_exercises',
                    'provide_progress_indicators',
                    'offer_multiple_reading_paths'
                ]
            )
        except Exception as e:
            self.logger.error(f"Error identifying personalization opportunities: {str(e)}")
            return self._get_default_personalization_opportunities()

    async def _generate_improvement_recommendations(
        self,
        reading_patterns: ReadingPattern,
        drop_off_analysis: DropOffAnalysis,
        sentiment_analysis: SentimentAnalysis
    ) -> List[str]:
        """Generate improvement recommendations based on analysis"""
        try:
            recommendations = []

            # Based on reading patterns
            if reading_patterns.completion_rate < 0.5:
                recommendations.append("Improve content engagement to increase completion rates")
            if reading_patterns.average_session_duration < 300:  # Less than 5 minutes
                recommendations.append("Create more engaging opening chapters to increase session duration")

            # Based on drop-off analysis
            if drop_off_analysis.critical_drop_points:
                recommendations.append("Address critical drop-off points identified in the analysis")
            if len(drop_off_analysis.drop_off_rate_by_chapter) > 3:
                recommendations.append("Review chapter pacing and content flow")

            # Based on sentiment analysis
            if sentiment_analysis.overall_sentiment < 0.6:
                recommendations.append("Improve content quality to enhance reader satisfaction")
            if sentiment_analysis.satisfaction_indicators.get('recommendation_likelihood', 0) < 0.7:
                recommendations.append("Focus on creating more memorable and shareable content")

            # General recommendations
            if not recommendations:
                recommendations = [
                    "Continue monitoring reader engagement patterns",
                    "Consider A/B testing different content approaches",
                    "Implement reader feedback collection mechanisms"
                ]

            return recommendations
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {str(e)}")
            return ["Monitor reader engagement and gather feedback for improvements"]

    # Helper methods for data processing
    def _group_events_by_session(self, engagement_data: List[ReaderEngagementEvent]) -> List[Dict[str, Any]]:
        """Group events by reading session"""
        try:
            sessions = []
            current_session = []
            last_timestamp = None

            for event in sorted(engagement_data, key=lambda x: x.timestamp):
                if last_timestamp and (event.timestamp - last_timestamp).seconds > 1800:  # 30 minutes gap
                    if current_session:
                        session_duration = (current_session[-1].timestamp - current_session[0].timestamp).seconds
                        sessions.append({
                            'events': current_session,
                            'duration': session_duration,
                            'start_time': current_session[0].timestamp,
                            'end_time': current_session[-1].timestamp
                        })
                        current_session = []

                current_session.append(event)
                last_timestamp = event.timestamp

            # Add final session
            if current_session:
                session_duration = (current_session[-1].timestamp - current_session[0].timestamp).seconds
                sessions.append({
                    'events': current_session,
                    'duration': session_duration,
                    'start_time': current_session[0].timestamp,
                    'end_time': current_session[-1].timestamp
                })

            return sessions
        except Exception as e:
            self.logger.error(f"Error grouping events by session: {str(e)}")
            return []

    def _calculate_reading_speed(self, engagement_data: List[ReaderEngagementEvent]) -> ReadingSpeed:
        """Calculate reading speed category"""
        try:
            time_events = [e for e in engagement_data if e.time_spent_seconds and e.page_number]
            if not time_events:
                return ReadingSpeed.NORMAL

            # Calculate average time per page
            total_time = sum(e.time_spent_seconds for e in time_events if e.time_spent_seconds is not None)
            total_pages = len(set(e.page_number for e in time_events if e.page_number is not None))
            avg_time_per_page = total_time / max(1, total_pages)

            # Categorize reading speed (assuming ~250 words per page, ~200 wpm average)
            if avg_time_per_page > 90:  # More than 1.5 minutes per page
                return ReadingSpeed.SLOW
            elif avg_time_per_page < 30:  # Less than 30 seconds per page
                return ReadingSpeed.FAST
            elif avg_time_per_page < 15:  # Less than 15 seconds per page
                return ReadingSpeed.SKIMMING
            else:
                return ReadingSpeed.NORMAL
        except Exception as e:
            self.logger.error(f"Error calculating reading speed: {str(e)}")
            return ReadingSpeed.NORMAL

    def _analyze_reading_times(self, engagement_data: List[ReaderEngagementEvent]) -> List[str]:
        """Analyze preferred reading times"""
        try:
            time_counts = {'morning': 0, 'afternoon': 0, 'evening': 0, 'night': 0}

            for event in engagement_data:
                hour = event.timestamp.hour
                if 6 <= hour < 12:
                    time_counts['morning'] += 1
                elif 12 <= hour < 17:
                    time_counts['afternoon'] += 1
                elif 17 <= hour < 22:
                    time_counts['evening'] += 1
                else:
                    time_counts['night'] += 1

            # Return times sorted by preference
            sorted_times = sorted(time_counts.items(), key=lambda x: x[1], reverse=True)
            return [time for time, count in sorted_times if count > 0]
        except Exception as e:
            self.logger.error(f"Error analyzing reading times: {str(e)}")
            return ['evening', 'afternoon', 'morning', 'night']

    # Default data methods for error handling
    def _get_default_reading_pattern(self) -> ReadingPattern:
        """Get default reading pattern"""
        return ReadingPattern(
            average_session_duration=600.0,  # 10 minutes
            reading_speed=ReadingSpeed.NORMAL,
            preferred_reading_times=['evening', 'afternoon'],
            engagement_frequency=0.5,
            completion_rate=0.6,
            retention_rate=0.7
        )

    def _get_default_drop_off_analysis(self) -> DropOffAnalysis:
        """Get default drop-off analysis"""
        return DropOffAnalysis(
            critical_drop_points=[],
            drop_off_rate_by_chapter={},
            common_drop_off_reasons=['pacing_issues', 'content_difficulty'],
            recovery_opportunities=['improve_engagement', 'optimize_content_flow']
        )

    def _get_default_engagement_heatmap(self) -> EngagementHeatmap:
        """Get default engagement heatmap"""
        return EngagementHeatmap(
            chapter_engagement_scores={i: 70.0 for i in range(1, 11)},
            page_engagement_scores={i: 70.0 for i in range(1, 21)},
            time_based_engagement={
                'morning': 60.0,
                'afternoon': 75.0,
                'evening': 85.0,
                'night': 40.0
            },
            interaction_hotspots=[]
        )

    def _get_default_sentiment_analysis(self) -> SentimentAnalysis:
        """Get default sentiment analysis"""
        return SentimentAnalysis(
            overall_sentiment=0.7,
            sentiment_by_chapter={i: 0.7 for i in range(1, 11)},
            sentiment_trends=[],
            emotional_peaks=[],
            satisfaction_indicators={
                'engagement_level': 0.7,
                'completion_satisfaction': 0.7,
                'recommendation_likelihood': 0.7
            }
        )

    def _get_default_completion_predictors(self) -> CompletionPredictors:
        """Get default completion predictors"""
        return CompletionPredictors(
            early_engagement_indicators=['moderate_engagement'],
            risk_factors=['unknown_patterns'],
            positive_signals=['content_access'],
            completion_probability_model={
                'early_engagement_weight': 0.4,
                'content_quality_weight': 0.3,
                'user_behavior_weight': 0.3
            }
        )

    def _get_default_personalization_opportunities(self) -> PersonalizationOpportunities:
        """Get default personalization opportunities"""
        return PersonalizationOpportunities(
            content_preferences={'general_content': True},
            optimal_chapter_length=2000,
            preferred_content_types=['text'],
            engagement_triggers=['chapter_breaks'],
            customization_suggestions=['improve_readability', 'add_visual_elements']
        )