### app/tasks/model_training.py - Model Training Tasks

from app.celery_app import celery
from app.ml.model_trainer import ReinforcementTrainer
from app.ml.feedback_collector import FeedbackCollector
import logging
import asyncio

logger = logging.getLogger(__name__)

@celery.task(bind=True)
def auto_tune_system_task(self):
    """Background task for automatic system tuning"""
    
    try:
        trainer = ReinforcementTrainer()
        asyncio.run(trainer.auto_tune_system())
        
        return {
            "status": "success",
            "message": "System auto-tuning completed"
        }
        
    except Exception as e:
        logger.error(f"Auto-tuning failed: {str(e)}")
        raise

@celery.task(bind=True)
def collect_sales_feedback_task(self, book_id: str):
    """Background task to collect sales feedback"""
    
    try:
        collector = FeedbackCollector()
        asyncio.run(collector.collect_sales_performance_feedback(book_id))
        
        return {
            "status": "success",
            "book_id": book_id
        }
        
    except Exception as e:
        logger.error(f"Sales feedback collection failed: {str(e)}")
        raise

# Schedule periodic tasks
@celery.task
def daily_performance_analysis():
    """Daily task to analyze performance and collect feedback"""
    
    trainer = ReinforcementTrainer()
    collector = FeedbackCollector()
    
    # Collect any pending sales feedback
    # Analyze trends
    # Generate suggestions
    
    return "Daily analysis completed"
