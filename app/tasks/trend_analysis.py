### app/tasks/trend_analysis.py - Trend Analysis Tasks

from app.celery_app import celery
from app.agents.pydantic_ai_manager import execute_agent
from app.services.trend_service import TrendService
from app.models.supabase_models import get_trend_analysis_model
from app.schemas.trend import TrendAnalysisRequest
from datetime import datetime, timedelta
import logging
import asyncio

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def analyze_trends_task(
    self,
    analysis_id: str,
    categories: list = None,
    max_results: int = 50,
    min_search_volume: int = 1000,
    max_competition: float = 0.7,
    analysis_depth: str = "comprehensive"
):
    """Background task for trend analysis"""
    
    trend_analysis_model = get_trend_analysis_model()
    
    try:
        # Update analysis status
        asyncio.run(trend_analysis_model.update_trend_analysis(analysis_id, {"status": "analyzing"}))
        
        # Run trend analysis using PydanticAI agent
        task_data = {
            "categories": categories or ["self-help", "business", "health", "technology"],
            "max_results": max_results,
            "analysis_type": analysis_depth
        }
        result = asyncio.run(execute_agent("trend_analyzer", task_data, None))
        
        if result.success:
            # Update analysis with results
            update_data = {
                "status": "completed",
                "result": result.data,
                "completed_at": datetime.utcnow().isoformat()
            }
            asyncio.run(trend_analysis_model.update_trend_analysis(analysis_id, update_data))
            
            logger.info(f"Successfully completed trend analysis {analysis_id}")
            
            return {
                "status": "success",
                "analysis_id": analysis_id,
                "opportunities_found": len(result.data.get('opportunities', [])),
                "message": "Trend analysis completed successfully"
            }
        else:
            # Update status to failed
            asyncio.run(trend_analysis_model.update_trend_analysis(analysis_id, {"status": "failed", "error_message": result.error}))
            raise Exception(f"Trend analysis failed: {result.error}")
            
    except Exception as e:
        logger.error(f"Trend analysis task failed for analysis {analysis_id}: {str(e)}")
        
        # Update analysis status to failed
        asyncio.run(trend_analysis_model.update_trend_analysis(analysis_id, {"status": "failed", "error_message": str(e)}))
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying trend analysis {analysis_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        raise

@celery.task
def daily_trend_analysis():
    """Daily automated trend analysis for popular categories"""
    
    popular_categories = [
        "self-help", "business", "health", "technology", "finance",
        "relationships", "productivity", "mindfulness", "cooking", "fitness"
    ]
    
    results = []
    trend_service = TrendService()
    
    for category in popular_categories:
        try:
            # Create trend analysis request
            request = TrendAnalysisRequest(
                categories=[category],
                max_results=20,
                min_search_volume=500,
                max_competition=0.8,
                analysis_depth="standard"
            )
            
            # Start analysis (this will trigger the background task)
            analysis_response = asyncio.run(trend_service.create_trend_analysis(request, user_id=None))  # System analysis
            
            results.append({
                "category": category,
                "analysis_id": analysis_response.id,
                "status": "queued"
            })
            
        except Exception as e:
            logger.error(f"Failed to queue daily trend analysis for {category}: {str(e)}")
            results.append({
                "category": category,
                "status": "failed",
                "error": str(e)
            })
    
    logger.info(f"Daily trend analysis completed: {len(results)} categories processed")
    
    return {
        "date": datetime.utcnow().isoformat(),
        "categories_processed": len(results),
        "successful_analyses": len([r for r in results if r['status'] == 'queued']),
        "failed_analyses": len([r for r in results if r['status'] == 'failed']),
        "results": results
    }

@celery.task(bind=True)
def batch_trend_analysis(self, analysis_requests: list):
    """Run multiple trend analyses in batch"""
    
    results = []
    
    for request in analysis_requests:
        try:
            # Trigger individual trend analysis
            task_result = analyze_trends_task.delay(
                analysis_id=request['analysis_id'],
                categories=request.get('categories'),
                max_results=request.get('max_results', 50),
                min_search_volume=request.get('min_search_volume', 1000),
                max_competition=request.get('max_competition', 0.7),
                analysis_depth=request.get('analysis_depth', 'comprehensive')
            )
            
            results.append({
                "analysis_id": request['analysis_id'],
                "task_id": task_result.id,
                "status": "queued"
            })
            
        except Exception as e:
            logger.error(f"Failed to queue trend analysis {request['analysis_id']}: {str(e)}")
            results.append({
                "analysis_id": request['analysis_id'],
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "batch_id": self.request.id,
        "total_requests": len(analysis_requests),
        "queued_tasks": len([r for r in results if r['status'] == 'queued']),
        "failed_requests": len([r for r in results if r['status'] == 'failed']),
        "results": results
    }

@celery.task
def cleanup_stale_analyses():
    """Clean up trend analyses stuck in analyzing state"""
    
    trend_analysis_model = get_trend_analysis_model()
    # Find analyses stuck in analyzing status for more than 1 hour
    from datetime import datetime, timedelta
    cutoff_time = datetime.utcnow() - timedelta(hours=1)
    
    stuck_analyses = asyncio.run(trend_analysis_model.get_stuck_analyses(cutoff_time))
    
    for analysis in stuck_analyses:
        asyncio.run(trend_analysis_model.update_trend_analysis(analysis['id'], {"status": "failed", "error_message": "Analysis timed out"}))
        logger.warning(f"Marked trend analysis {analysis['id']} as failed due to timeout")
    
    return {
        "cleaned_up_analyses": len(stuck_analyses),
        "analysis_ids": [analysis['id'] for analysis in stuck_analyses]
    }

@celery.task
def generate_trend_report():
    """Generate weekly trend report"""
    
    trend_analysis_model = get_trend_analysis_model()
    # Get completed analyses from the last 7 days
    from datetime import datetime, timedelta
    since_date = datetime.utcnow() - timedelta(days=7)
    
    recent_analyses = asyncio.run(trend_analysis_model.get_recent_analyses(None, since_date))
    
    # Aggregate insights
    total_opportunities = 0
    top_categories = {}
    
    for analysis in recent_analyses:
        if analysis.get('result') and analysis['result'].get('opportunities'):
            opportunities = analysis['result']['opportunities']
            total_opportunities += len(opportunities)
            
            for opp in opportunities:
                category = opp.get('category', 'unknown')
                if category not in top_categories:
                    top_categories[category] = {
                        'count': 0,
                        'avg_profit_potential': 0,
                        'total_profit_potential': 0
                    }
                
                top_categories[category]['count'] += 1
                top_categories[category]['total_profit_potential'] += opp.get('profit_potential', 0)
    
    # Calculate averages
    for category in top_categories:
        stats = top_categories[category]
        stats['avg_profit_potential'] = stats['total_profit_potential'] / stats['count']
    
    # Sort by average profit potential
    sorted_categories = sorted(
        top_categories.items(),
        key=lambda x: x[1]['avg_profit_potential'],
        reverse=True
    )
    
    report = {
        "report_date": datetime.utcnow().isoformat(),
        "period": "7_days",
        "total_analyses": len(recent_analyses),
        "total_opportunities": total_opportunities,
        "avg_opportunities_per_analysis": total_opportunities / len(recent_analyses) if recent_analyses else 0,
        "top_categories": dict(sorted_categories[:10])  # Top 10 categories
    }
    
    logger.info(f"Generated trend report: {total_opportunities} opportunities across {len(recent_analyses)} analyses")
    
    return report
