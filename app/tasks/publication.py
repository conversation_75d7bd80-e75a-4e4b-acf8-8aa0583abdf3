### app/tasks/publication.py - Publication Tasks

from app.celery_app import celery
import asyncio
from app.agents.pydantic_ai_manager import execute_agent
from app.services.publication_service import PublicationService
from app.models.supabase_models import get_publication_model, get_book_model, get_sales_data_model
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def publish_to_kdp_task(
    self,
    publication_id: str,
    book_data: dict,
    kdp_credentials: dict
):
    """Background task for publishing to KDP"""
    
    publication_model = get_publication_model()
    book_model = get_book_model()
    
    try:
        # Update publication status
        asyncio.run(publication_model.update_publication(publication_id, {"status": "publishing"}))
        
        # Prepare publication data for PydanticAI agent
        task_data = {
            "title": book_data['title'],
            "author": book_data.get('author', 'Author'),
            "description": book_data['description'],
            "genre": book_data['category'],
            "keywords": book_data.get('keywords', []),
            "manuscript_file": book_data['manuscript_path'],
            "cover_file": book_data.get('cover_paths', [''])[0] if book_data.get('cover_paths') else ''
        }

        # Execute publication using PydanticAI agent
        result = asyncio.run(execute_agent("kdp_uploader", task_data, None))
        
        if result.success:
            # Update publication with success data
            update_data = {
                "status": "published" if book_data.get('auto_publish', True) else "draft",
                "kdp_id": result.data.get('kdp_id'),
                "publication_url": result.data.get('publication_url'),
                "published_at": datetime.utcnow().isoformat()
            }
            asyncio.run(publication_model.update_publication(publication_id, update_data))
            
            # Update book status
            asyncio.run(book_model.update_book(book_data['book_id'], {"status": "published", "published_at": datetime.utcnow().isoformat()}))
            
            logger.info(f"Successfully published to KDP: publication {publication_id}")
            
            # Schedule sales monitoring
            monitor_sales_task.apply_async(
                args=[publication_id],
                countdown=3600  # Start monitoring after 1 hour
            )
            
            return {
                "status": "success",
                "publication_id": publication_id,
                "kdp_id": result.data.get('kdp_id'),
                "publication_url": result.data.get('publication_url'),
                "message": "Published to KDP successfully"
            }
        else:
            # Update status to failed
            asyncio.run(publication_model.update_publication(publication_id, {"status": "failed", "error_message": result.error}))
            raise Exception(f"KDP publication failed: {result.error}")
            
    except Exception as e:
        logger.error(f"KDP publication task failed for publication {publication_id}: {str(e)}")
        
        # Update publication status to failed
        asyncio.run(publication_model.update_publication(publication_id, {"status": "failed", "error_message": str(e)}))
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying KDP publication {publication_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=300 * (2 ** self.request.retries))  # Start with 5-minute delay
        
        raise

@celery.task(bind=True, max_retries=2)
def monitor_sales_task(self, publication_id: str):
    """Background task for monitoring book sales"""
    
    publication_model = get_publication_model()
    sales_data_model = get_sales_data_model()
    
    try:
        publication = asyncio.run(publication_model.get_publication(publication_id))
        if not publication or not publication.get('kdp_id'):
            logger.warning(f"Publication {publication_id} not found or missing KDP ID")
            return
        
        # Get sales data using PydanticAI agent
        task_data = {
            "date_range": "last_30_days",
            "include_page_reads": True,
            "generate_insights": True
        }
        result = asyncio.run(execute_agent("sales_monitor", task_data, None))
        
        if result.success:
            sales_data = result.data.get('sales_data', {})
            
            # Store sales data
            sales_record = {
                "book_id": publication['book_id'],
                "sales_units": sales_data.get('sales_units', 0),
                "revenue": sales_data.get('revenue', 0.0),
                "royalties": sales_data.get('royalties', 0.0),
                "average_rating": sales_data.get('average_rating', 0.0),
                "reviews_count": sales_data.get('reviews_count', 0),
                "report_date": datetime.utcnow().isoformat()
            }
            asyncio.run(sales_data_model.create_sales_data(sales_record))
            
            # Capture feedback for VERL training
            from app.ml.feedback_integration import LiveFeedbackCollector
            asyncio.run(LiveFeedbackCollector().capture_sales_feedback(
                book_id=publication['book_id'],
                sales_data=sales_data
            ))
            
            logger.info(f"Updated sales data for publication {publication_id}")
            
            # Schedule next monitoring (daily)
            monitor_sales_task.apply_async(
                args=[publication_id],
                countdown=86400  # 24 hours
            )
            
            return {
                "status": "success",
                "publication_id": publication_id,
                "sales_data": sales_data
            }
        else:
            logger.warning(f"Sales monitoring failed for publication {publication_id}: {result.error}")
            
            # Retry with exponential backoff
            if self.request.retries < self.max_retries:
                raise self.retry(countdown=1800 * (2 ** self.request.retries))  # Start with 30-minute delay
            
            return {"status": "failed", "error": result.error}
            
    except Exception as e:
        logger.error(f"Sales monitoring task failed for publication {publication_id}: {str(e)}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=1800 * (2 ** self.request.retries))
        
        raise

@celery.task
def monitor_publications():
    """Periodic task to monitor all active publications"""
    
    publication_model = get_publication_model()
    active_publications = asyncio.run(publication_model.get_active_publications())
    
    results = []
    
    for publication in active_publications:
        try:
            # Trigger sales monitoring for each publication
            task_result = monitor_sales_task.delay(publication['id'])
            
            results.append({
                "publication_id": publication['id'],
                "task_id": task_result.id,
                "status": "queued"
            })
            
        except Exception as e:
            logger.error(f"Failed to queue sales monitoring for publication {publication['id']}: {str(e)}")
            results.append({
                "publication_id": publication['id'],
                "status": "failed",
                "error": str(e)
            })
    
    logger.info(f"Monitoring queued for {len(results)} publications")
    
    return {
        "monitoring_date": datetime.utcnow().isoformat(),
        "total_publications": len(active_publications),
        "queued_monitoring": len([r for r in results if r['status'] == 'queued']),
        "failed_monitoring": len([r for r in results if r['status'] == 'failed']),
        "results": results
    }

@celery.task(bind=True)
def batch_publish_books(self, publication_requests: list):
    """Publish multiple books in batch"""
    
    results = []
    
    for request in publication_requests:
        try:
            # Trigger individual publication
            task_result = publish_to_kdp_task.delay(
                publication_id=request['publication_id'],
                book_data=request['book_data'],
                kdp_credentials=request['kdp_credentials']
            )
            
            results.append({
                "publication_id": request['publication_id'],
                "task_id": task_result.id,
                "status": "queued"
            })
            
        except Exception as e:
            logger.error(f"Failed to queue publication {request['publication_id']}: {str(e)}")
            results.append({
                "publication_id": request['publication_id'],
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "batch_id": self.request.id,
        "total_requests": len(publication_requests),
        "queued_publications": len([r for r in results if r['status'] == 'queued']),
        "failed_requests": len([r for r in results if r['status'] == 'failed']),
        "results": results
    }

@celery.task
def update_book_prices():
    """Dynamic pricing updates based on sales performance"""
    
    publication_model = get_publication_model()
    cutoff_date = datetime.utcnow() - timedelta(days=30)
    publications_with_sales = asyncio.run(publication_model.get_publications_with_sales(cutoff_date))
    
    price_updates = []
    
    for publication in publications_with_sales:
        # Simple dynamic pricing logic
        current_price = float(publication['price'])
        latest_sales = publication['sales_data'][0]
        
        # Increase price if performing well
        if latest_sales['sales_units'] > 50 and latest_sales['average_rating'] > 4.0:
            new_price = min(current_price * 1.1, current_price + 2.0)  # Max 10% increase or $2
        # Decrease price if underperforming
        elif latest_sales['sales_units'] < 10 and latest_sales['average_rating'] < 3.5:
            new_price = max(current_price * 0.9, current_price - 1.0)  # Max 10% decrease or $1
        else:
            continue  # No price change needed
        
        if abs(new_price - current_price) > 0.1:  # Only update if significant change
            try:
                # Use PydanticAI KDP uploader to update price
                task_data = {
                    "title": publication['book']['title'] if publication.get('book') else "Book",
                    "author": "Author",
                    "description": "Price update",
                    "genre": "general",
                    "keywords": [],
                    "manuscript_file": "",
                    "cover_file": "",
                    "action": "update_price",
                    "kdp_id": publication['kdp_id'],
                    "new_price": new_price
                }
                result = asyncio.run(execute_agent("kdp_uploader", task_data, None))
                
                if result.success:
                    asyncio.run(publication_model.update_publication(publication['id'], {"price": new_price}))
                    price_updates.append({
                        "publication_id": publication['id'],
                        "old_price": current_price,
                        "new_price": new_price,
                        "status": "updated"
                    })
                else:
                    price_updates.append({
                        "publication_id": publication['id'],
                        "status": "failed",
                        "error": result.error
                    })
                    
            except Exception as e:
                logger.error(f"Price update failed for publication {publication['id']}: {str(e)}")
                price_updates.append({
                    "publication_id": publication['id'],
                    "status": "failed",
                    "error": str(e)
                })
    
    logger.info(f"Dynamic pricing completed: {len(price_updates)} price updates processed")
    
    return {
        "update_date": datetime.utcnow().isoformat(),
        "publications_reviewed": len(publications_with_sales),
        "price_updates": price_updates,
        "successful_updates": len([u for u in price_updates if u['status'] == 'updated']),
        "failed_updates": len([u for u in price_updates if u['status'] == 'failed'])
    }
