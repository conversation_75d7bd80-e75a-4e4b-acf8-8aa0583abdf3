# app/database/connection_pool.py - Advanced Database Connection Pooling

import asyncio
import asyncpg
import time
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from dataclasses import dataclass
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception, monitor_operation

logger = get_logger(__name__)

@dataclass
class PoolStats:
    """Database connection pool statistics"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    max_connections: int = 0
    min_connections: int = 0
    total_queries: int = 0
    failed_queries: int = 0
    avg_query_time: float = 0.0
    pool_created_at: Optional[float] = None

class DatabaseConnectionPool:
    """Advanced database connection pool manager"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.stats = PoolStats()
        self.query_times: List[float] = []
        self.max_query_history = 1000
        self._lock = asyncio.Lock()
        
    async def initialize(self) -> None:
        """Initialize the connection pool"""
        if self.pool:
            logger.warning("Connection pool already initialized")
            return
        
        try:
            # Parse database URL for connection parameters
            db_config = self._parse_database_url()
            
            # Create connection pool with optimized settings
            self.pool = await asyncpg.create_pool(
                **db_config,
                min_size=5,        # Minimum connections
                max_size=20,       # Maximum connections
                max_queries=50000, # Max queries per connection
                max_inactive_connection_lifetime=300,  # 5 minutes
                timeout=30,        # Connection timeout
                command_timeout=60, # Query timeout
                server_settings={
                    'application_name': 'publish-ai',
                    'jit': 'off',  # Disable JIT for faster connection times
                }
            )
            
            # Update statistics
            self.stats.max_connections = 20
            self.stats.min_connections = 5
            self.stats.pool_created_at = time.time()
            
            logger.info("Database connection pool initialized successfully")
            
            # Test pool connectivity
            await self._test_pool_connectivity()
            
        except Exception as e:
            logger.error(f"Failed to initialize connection pool: {e}")
            capture_exception(e)
            raise
    
    def _parse_database_url(self) -> Dict[str, Any]:
        """Parse database URL into connection parameters"""
        database_url = settings.database_url
        
        if not database_url:
            raise ValueError("DATABASE_URL not configured")
        
        # For asyncpg, we can pass the URL directly or parse it
        # Here we'll pass it directly for simplicity
        return {'dsn': database_url}
    
    async def _test_pool_connectivity(self) -> None:
        """Test pool connectivity"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchval('SELECT 1')
            if result != 1:
                raise RuntimeError("Pool connectivity test failed")
            
            logger.info("Database pool connectivity test passed")
    
    async def close(self) -> None:
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def acquire_connection(self):
        """Context manager to acquire a database connection"""
        if not self.pool:
            raise RuntimeError("Connection pool not initialized")
        
        start_time = time.time()
        connection = None
        
        try:
            async with self.pool.acquire() as conn:
                connection = conn
                self.stats.active_connections += 1
                yield conn
                
        except Exception as e:
            self.stats.failed_queries += 1
            logger.error(f"Database connection error: {e}")
            capture_exception(e)
            raise
        finally:
            if connection:
                self.stats.active_connections -= 1
            
            # Record query time
            query_time = time.time() - start_time
            self._record_query_time(query_time)
    
    def _record_query_time(self, query_time: float) -> None:
        """Record query execution time for statistics"""
        self.query_times.append(query_time)
        
        # Keep only recent query times
        if len(self.query_times) > self.max_query_history:
            self.query_times = self.query_times[-self.max_query_history:]
        
        # Update statistics
        self.stats.total_queries += 1
        if self.query_times:
            self.stats.avg_query_time = sum(self.query_times) / len(self.query_times)
    
    async def execute_query(self, query: str, *args, **kwargs) -> Any:
        """Execute a database query with monitoring"""
        with monitor_operation(f"db_query", query_type="select" if query.strip().upper().startswith("SELECT") else "other"):
            async with self.acquire_connection() as conn:
                return await conn.fetch(query, *args, **kwargs)
    
    async def execute_query_one(self, query: str, *args, **kwargs) -> Any:
        """Execute a database query expecting one result"""
        with monitor_operation(f"db_query_one", query_type="select"):
            async with self.acquire_connection() as conn:
                return await conn.fetchrow(query, *args, **kwargs)
    
    async def execute_query_value(self, query: str, *args, **kwargs) -> Any:
        """Execute a database query expecting a single value"""
        with monitor_operation(f"db_query_value", query_type="select"):
            async with self.acquire_connection() as conn:
                return await conn.fetchval(query, *args, **kwargs)
    
    async def execute_command(self, command: str, *args, **kwargs) -> str:
        """Execute a database command (INSERT, UPDATE, DELETE)"""
        command_type = command.strip().split()[0].upper()
        with monitor_operation(f"db_command", command_type=command_type):
            async with self.acquire_connection() as conn:
                return await conn.execute(command, *args, **kwargs)
    
    async def get_pool_stats(self) -> PoolStats:
        """Get current pool statistics"""
        if self.pool:
            self.stats.total_connections = len(self.pool._holders)
            self.stats.idle_connections = len(self.pool._queue._queue)
            
        return self.stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a comprehensive health check"""
        try:
            start_time = time.time()
            
            # Test query
            async with self.acquire_connection() as conn:
                await conn.fetchval('SELECT 1')
            
            query_time = time.time() - start_time
            stats = await self.get_pool_stats()
            
            return {
                'status': 'healthy',
                'query_time_ms': round(query_time * 1000, 2),
                'pool_stats': {
                    'total_connections': stats.total_connections,
                    'active_connections': stats.active_connections,
                    'idle_connections': stats.idle_connections,
                    'total_queries': stats.total_queries,
                    'failed_queries': stats.failed_queries,
                    'avg_query_time_ms': round(stats.avg_query_time * 1000, 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'pool_stats': None
            }

class SupabaseConnectionPool:
    """Supabase-specific connection pool optimizations"""
    
    def __init__(self):
        self.base_pool = DatabaseConnectionPool()
        
    async def initialize(self) -> None:
        """Initialize Supabase connection pool"""
        await self.base_pool.initialize()
        
        # Supabase-specific optimizations
        await self._optimize_for_supabase()
    
    async def _optimize_for_supabase(self) -> None:
        """Apply Supabase-specific optimizations"""
        try:
            async with self.base_pool.acquire_connection() as conn:
                # Set connection-level optimizations
                await conn.execute("SET statement_timeout = '60s'")
                await conn.execute("SET lock_timeout = '10s'")
                await conn.execute("SET idle_in_transaction_session_timeout = '10s'")
                
                # Enable query planner optimizations
                await conn.execute("SET enable_hashjoin = on")
                await conn.execute("SET enable_mergejoin = on")
                await conn.execute("SET enable_nestloop = on")
                
                logger.info("Supabase connection optimizations applied")
                
        except Exception as e:
            logger.warning(f"Could not apply Supabase optimizations: {e}")
    
    async def close(self) -> None:
        """Close the Supabase connection pool"""
        await self.base_pool.close()
    
    # Delegate methods to base pool
    async def acquire_connection(self):
        return self.base_pool.acquire_connection()
    
    async def execute_query(self, query: str, *args, **kwargs):
        return await self.base_pool.execute_query(query, *args, **kwargs)
    
    async def execute_query_one(self, query: str, *args, **kwargs):
        return await self.base_pool.execute_query_one(query, *args, **kwargs)
    
    async def execute_query_value(self, query: str, *args, **kwargs):
        return await self.base_pool.execute_query_value(query, *args, **kwargs)
    
    async def execute_command(self, command: str, *args, **kwargs):
        return await self.base_pool.execute_command(command, *args, **kwargs)
    
    async def get_pool_stats(self):
        return await self.base_pool.get_pool_stats()
    
    async def health_check(self):
        return await self.base_pool.health_check()

# Global connection pool instance
connection_pool: Optional[SupabaseConnectionPool] = None

async def initialize_connection_pool() -> None:
    """Initialize the global connection pool"""
    global connection_pool
    
    if connection_pool:
        logger.warning("Connection pool already initialized")
        return
    
    connection_pool = SupabaseConnectionPool()
    await connection_pool.initialize()
    logger.info("Global connection pool initialized")

async def close_connection_pool() -> None:
    """Close the global connection pool"""
    global connection_pool
    
    if connection_pool:
        await connection_pool.close()
        connection_pool = None
        logger.info("Global connection pool closed")

def get_connection_pool() -> SupabaseConnectionPool:
    """Get the global connection pool"""
    if not connection_pool:
        raise RuntimeError("Connection pool not initialized. Call initialize_connection_pool() first.")
    
    return connection_pool

# Context manager for database operations
@asynccontextmanager
async def get_db_connection():
    """Get a database connection from the pool"""
    pool = get_connection_pool()
    async with pool.acquire_connection() as conn:
        yield conn

# Helper functions for common database operations
async def fetch_many(query: str, *args) -> List[Dict[str, Any]]:
    """Fetch multiple rows from database"""
    pool = get_connection_pool()
    rows = await pool.execute_query(query, *args)
    return [dict(row) for row in rows]

async def fetch_one(query: str, *args) -> Optional[Dict[str, Any]]:
    """Fetch one row from database"""
    pool = get_connection_pool()
    row = await pool.execute_query_one(query, *args)
    return dict(row) if row else None

async def fetch_value(query: str, *args) -> Any:
    """Fetch a single value from database"""
    pool = get_connection_pool()
    return await pool.execute_query_value(query, *args)

async def execute_command(command: str, *args) -> str:
    """Execute a database command"""
    pool = get_connection_pool()
    return await pool.execute_command(command, *args)

# Database health monitoring
async def get_database_health() -> Dict[str, Any]:
    """Get comprehensive database health information"""
    try:
        pool = get_connection_pool()
        health = await pool.health_check()
        
        # Add additional health metrics
        async with get_db_connection() as conn:
            # Check database size
            db_size = await conn.fetchval(
                "SELECT pg_size_pretty(pg_database_size(current_database()))"
            )
            
            # Check active connections
            active_connections = await conn.fetchval(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
            )
            
            # Check for long-running queries
            long_queries = await conn.fetchval(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND query_start < now() - interval '1 minute'"
            )
            
            health['database_metrics'] = {
                'database_size': db_size,
                'active_connections': active_connections,
                'long_running_queries': long_queries
            }
        
        return health
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }