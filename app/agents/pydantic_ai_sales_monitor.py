### app/agents/pydantic_ai_sales_monitor.py - Sales Monitor Agent

from typing import Dict, Any, List, Optional, TYPE_CHECKING
from pydantic import BaseModel, Field
from datetime import datetime
import logging
import os

from app.utils.prompt import PromptTemplate
from .pydantic_ai_common import (
    AgentExecutionResult,
    create_error_result,
    create_success_result,
    with_error_handling,
    require_agents_available,
    with_execution_timing
)
from .pydantic_ai_base import agent_registry

logger = logging.getLogger(__name__)

# =========================================================================
# Output Model
# =========================================================================

class ComprehensiveSalesReport(BaseModel):
    reporting_period: str = Field(description="The reporting period for the sales data")
    books_data: List[Dict[str, Any]] = Field(description="A list of sales data for each book")
    performance_metrics: Dict[str, Any] = Field(description="A summary of key performance metrics")
    insights: Dict[str, Any] = Field(description="AI-generated insights and recommendations")

# =========================================================================
# Model Selection
# =========================================================================

try:
    from pydantic_ai import Agent # Import Agent directly for runtime
    from pydantic_ai.models.openai import OpenAIModel # Import OpenAIModel directly for runtime
    from pydantic_ai.models.anthropic import AnthropicModel # Import AnthropicModel directly for runtime
    PYDANTIC_AI_AVAILABLE = True
except ImportError:
    PYDANTIC_AI_AVAILABLE = False
    Agent = None # Assign None at runtime if import fails
    OpenAIModel = None
    AnthropicModel = None

# Use TYPE_CHECKING for the type hint with the original class name
if TYPE_CHECKING:
    from pydantic_ai import Agent as TypedAgent # Use alias for type checking consistency
    sales_monitor: Optional[TypedAgent[Any, ComprehensiveSalesReport]] = None
else:
    # At runtime, sales_monitor will be None if pydantic_ai is not available
    sales_monitor = None

def get_available_model() -> Optional[Any]:
    preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
    if PYDANTIC_AI_AVAILABLE:
        if preferred == "openai" and os.getenv("OPENAI_API_KEY") and OpenAIModel is not None:
            return OpenAIModel("gpt-4")
        elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY") and AnthropicModel is not None:
            return AnthropicModel("claude-3-sonnet-20240229")
    return None

def init_sales_monitor():
    global sales_monitor
    if not PYDANTIC_AI_AVAILABLE:
        logger.warning("PydanticAI is not available. Sales monitor agent will not be initialized.")
        return

    model = get_available_model()
    if model and Agent is not None: # Check if Agent is available at runtime
        sales_monitor = Agent( # Use Agent directly at runtime
            model=model,
            output_type=ComprehensiveSalesReport,
            system_prompt="You are an expert sales monitor..."
        )
    else:
        logger.warning("No valid model found for Sales Monitor initialization.")

init_sales_monitor()

# =========================================================================
# Agent Runner
# =========================================================================

@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_sales_monitor", logger=logger)
async def monitor_sales_performance(
    date_range: str = "last_30_days",
    include_page_reads: bool = True,
    generate_insights: bool = True,
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    agent_name = "pydantic_ai_sales_monitor"

    if sales_monitor is None:
        return create_error_result(
            agent_name=agent_name,
            error="Sales monitor agent is not initialized. Check API keys and model availability.",
            execution_time=0.0
        )

    prompt_template = PromptTemplate.from_string("""
        Monitor sales performance for the following date range: {{ date_range }}
        Include page reads: {{ include_page_reads }}
        Generate insights: {{ generate_insights }}
    """)

    prompt = prompt_template.format(
        date_range=date_range,
        include_page_reads=include_page_reads,
        generate_insights=generate_insights
    )

    result = await sales_monitor.run(prompt)

    return create_success_result(
        agent_name=agent_name,
        data=result.output.model_dump(),
        execution_time=getattr(getattr(result, "metrics", None), "total_time", 0.0),
        metadata={
            "date_range": date_range,
            "include_page_reads": include_page_reads,
            "generate_insights": generate_insights
        }
    )

# Register agent with the registry - always register the function even if agent isn't initialized
agent_registry.register_agent("sales_monitor", monitor_sales_performance)
agent_registry.register_agent("pydantic_ai_sales_monitor", monitor_sales_performance)
