"""
KDP Uploader Agent - PydanticAI Implementation with Selenium Integration
Handles automated uploading and publishing to Amazon KDP
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.agents.pydantic_ai_common import (
    create_success_result,
    create_error_result,
    AGENTS_AVAILABLE,
    AgentExecutionResult
)
from app.agents.pydantic_ai_base import agent_registry

from app.services.kdp_selenium_service import (
    KDPSeleniumService,
    create_kdp_credentials,
    create_book_metadata,
    create_book_files,
    create_pricing_config,
)

logger = logging.getLogger(__name__)

async def upload_to_kdp(
    book_data: Dict[str, Any], 
    file_paths: Dict[str, str], 
    pricing_config: Optional[Dict[str, Any]] = None, 
    auto_publish: bool = False, 
    user_id: Optional[str] = None
) -> AgentExecutionResult:
    start_time = datetime.now()
    agent_name = "pydantic_ai_kdp_uploader"

    try:
        validation_result = _validate_upload_data(book_data, file_paths)
        if not validation_result["valid"]:
            return create_error_result(agent_name, f"Validation failed: {validation_result['error']}", (datetime.now() - start_time).total_seconds())

        kdp_credentials = _get_kdp_credentials(user_id)
        if not kdp_credentials:
            return create_error_result(agent_name, "KDP credentials not configured", (datetime.now() - start_time).total_seconds())

        metadata = create_book_metadata(
            title=book_data.get("title", ""),
            subtitle=book_data.get("subtitle"),
            author=book_data.get("author", "Author"),
            description=book_data.get("description", ""),
            keywords=book_data.get("keywords", []),
            categories=book_data.get("categories", ["Fiction"]),
            language=book_data.get("language", "English"),
            series_name=book_data.get("series_name"),
            series_number=book_data.get("series_number"),
            isbn=book_data.get("isbn")
        )

        files = create_book_files(
            manuscript_path=file_paths.get("manuscript", ""),
            cover_path=file_paths.get("cover", ""),
            manuscript_format=_detect_file_format(file_paths.get("manuscript", ""))
        )

        pricing = create_pricing_config(
            price=pricing_config.get("price", 9.99) if pricing_config else 9.99,
            territories=pricing_config.get("territories", ["US", "UK", "CA"]) if pricing_config else ["US", "UK", "CA"],
            kdp_select=pricing_config.get("kdp_select", True) if pricing_config else True,
            royalty_option=pricing_config.get("royalty_option", "70") if pricing_config else "70"
        )

        headless = os.getenv("KDP_HEADLESS", "true").lower() == "true"
        kdp_service = KDPSeleniumService(headless=headless, timeout=60)

        upload_result = await kdp_service.publish_book(kdp_credentials, metadata, files, pricing, auto_publish)
        execution_time = (datetime.now() - start_time).total_seconds()

        if upload_result.get("success"):
            return create_success_result(
                agent_name,
                data={
                    "upload_status": "completed",
                    "book_metadata": metadata.__dict__,
                    "pricing_strategy": pricing.__dict__,
                    "kdp_book_id": upload_result["kdp_book_id"],
                    "publication_url": upload_result["publication_url"],
                    "status": upload_result["status"],
                    "estimated_live_date": upload_result.get("metadata", {}).get("estimated_live_date", "24-72 hours"),
                    "next_steps": _generate_next_steps(upload_result["status"], auto_publish),
                    "performance_predictions": await _generate_performance_predictions(book_data),
                    "message": upload_result["message"]
                },
                execution_time=execution_time,
                metadata={
                    "book_data": book_data,
                    "file_paths": file_paths,
                    "upload_config": {
                        "pricing_config": pricing_config or {},
                        "auto_publish": auto_publish,
                        "headless_mode": headless
                    },
                    "kdp_result": upload_result
                }
            )
        else:
            return create_error_result(
                agent_name,
                error=f"KDP upload failed: {upload_result.get('error', 'Unknown error')}",
                execution_time=execution_time,
                metadata={"upload_result": upload_result}
            )

    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Unexpected KDP upload error: {e}", exc_info=True)
        return create_error_result(
            agent_name,
            error=str(e),
            execution_time=execution_time,
            metadata={"exception": str(e)}
        )

def _detect_file_format(file_path: str) -> str:
    extension = Path(file_path).suffix.lower()
    return {
        '.docx': 'docx', '.doc': 'doc', '.pdf': 'pdf', '.epub': 'epub', '.txt': 'txt', '.rtf': 'rtf'
    }.get(extension, "unknown")

def _validate_upload_data(book_data: Dict[str, Any], file_paths: Dict[str, str]) -> Dict[str, Any]:
    errors = []
    required_fields = ["title", "author", "description"]
    for field in required_fields:
        if not book_data.get(field):
            errors.append(f"Missing field: {field}")
    if not os.path.exists(file_paths.get("manuscript", "")):
        errors.append("Manuscript file not found")
    if not os.path.exists(file_paths.get("cover", "")):
        errors.append("Cover file not found")
    return {"valid": not errors, "error": "; ".join(errors), "errors": errors}

def _get_kdp_credentials(user_id: Optional[str]):
    email = os.getenv("KDP_EMAIL")
    password = os.getenv("KDP_PASSWORD")
    codes = os.getenv("KDP_BACKUP_CODES", "").split(",") if os.getenv("KDP_BACKUP_CODES") else []
    if email and password:
        return create_kdp_credentials(email, password, [c.strip() for c in codes])
    return None

def _generate_next_steps(status: str, auto_publish: bool) -> List[str]:
    if status == "published":
        return ["Book is live", "Monitor sales", "Start marketing"]
    elif status == "draft":
        return ["Saved as draft", "Review and publish"]
    return ["Check KDP dashboard", "Contact support"]

async def _generate_performance_predictions(book_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        base_score = 50
        if book_data.get("category") in ["Romance", "Mystery", "Self-Help"]:
            base_score += 15
        if 40000 <= book_data.get("word_count", 0) <= 80000:
            base_score += 10
        if len(book_data.get("keywords", [])) >= 5:
            base_score += 5
        sales = max(10, int(base_score * 2))
        revenue = sales * book_data.get("price", 9.99) * 0.7
        return {
            "estimated_monthly_sales": sales,
            "estimated_monthly_revenue": round(revenue, 2),
            "market_competitiveness": min(100, base_score + 20),
            "optimization_suggestions": [
                "Optimize keywords", "Consider pricing strategy", "Engage reviews"
            ]
        }
    except Exception as e:
        logger.error(f"Performance prediction failed: {e}")
        return {
            "estimated_monthly_sales": "N/A",
            "estimated_monthly_revenue": "N/A",
            "market_competitiveness": "N/A",
            "optimization_suggestions": ["Manual review required"]
        }

# Public alias
kdp_uploader = upload_to_kdp

# Register agent with the registry
agent_registry.register_agent("kdp_uploader", upload_to_kdp)
agent_registry.register_agent("pydantic_ai_kdp_uploader", upload_to_kdp)
