"""
PydanticAI Base Infrastructure
Delegates all execution result models and builders to pydantic_ai_common.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
import logging

from app.agents.pydantic_ai_common import (
    AgentExecutionResult,
    create_error_result,
    create_success_result,
    create_warning_result,
    create_timeout_result,
    ExecutionStatus,
)

from app.config import settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.utils.scrapers import AmazonScraper, RedditScraper
from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception

logger = get_logger(__name__)

# =========================================================================
# Common Dependency Types for Dependency Injection
# =========================================================================

from typing import Union
from dataclasses import dataclass


@dataclass
class DatabaseDependencies:
    user_id: Optional[str] = None  # Supabase UUID

    def get_supabase(self):
        return get_supabase_client()

    async def get_user(self) -> Optional[Dict[str, Any]]:
        if not self.user_id:
            return None
        try:
            supabase_client = get_supabase_client()
            if supabase_client.client is None:
                raise Exception("Supabase client not initialized")
            result = (
                supabase_client.client.table("users")
                .select("*")
                .eq("id", self.user_id)
                .execute()
            )
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get user: {e}")
            return None


@dataclass
class AIModelDependencies:
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    preferred_model: str = "openai:gpt-4"
    temperature: float = 0.7
    max_tokens: int = 4000

    # Resource quota settings
    max_concurrent_requests: int = 2
    requests_per_minute: int = 10
    max_tokens_per_minute: int = 50000

    # Agent-specific timeouts
    request_timeout: int = 60
    retry_attempts: int = 3


@dataclass
class ScrapingDependencies:
    amazon_scraper: Optional[AmazonScraper] = None
    reddit_scraper: Optional[RedditScraper] = None
    headless: bool = True

    def __post_init__(self):
        self.amazon_scraper = self.amazon_scraper or AmazonScraper()
        self.reddit_scraper = self.reddit_scraper or RedditScraper()


@dataclass
class ManuscriptDependencies:
    db_deps: DatabaseDependencies
    ai_deps: AIModelDependencies
    target_length: int = 8000
    style: str = "professional"
    target_audience: str = "general adults"
    output_formats: Optional[List[str]] = None

    def __post_init__(self):
        self.output_formats = self.output_formats or ["docx", "epub", "pdf"]


@dataclass
class TrendAnalysisDependencies:
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    categories: Optional[List[str]] = None
    max_results: int = 50
    min_search_volume: int = 1000

    def __post_init__(self):
        self.categories = self.categories or [
            "self-help",
            "romance",
            "mystery",
            "fantasy",
            "business",
            "health",
            "cooking",
            "parenting",
            "finance",
            "productivity",
        ]


@dataclass
class SalesMonitorDependencies:
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    kdp_email: Optional[str] = None
    kdp_password: Optional[str] = None

    def __post_init__(self):
        self.kdp_email = self.kdp_email or getattr(settings, "kdp_email", None)
        self.kdp_password = self.kdp_password or getattr(settings, "kdp_password", None)


@dataclass
class CoverDesignDependencies:
    db_deps: DatabaseDependencies
    ai_deps: AIModelDependencies
    style: str = "modern"
    dimensions: tuple = (1600, 2560)


@dataclass
class KDPUploadDependencies:
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    kdp_email: Optional[str] = None
    kdp_password: Optional[str] = None

    def __post_init__(self):
        self.kdp_email = self.kdp_email or getattr(settings, "kdp_email", None)
        self.kdp_password = self.kdp_password or getattr(settings, "kdp_password", None)


# =========================================================================
# KDP Dependency Class - Production-Ready
# =========================================================================


class KDPClient:
    def __init__(self, api_key: str):
        self.api_key = api_key

    def get_sales_data(self, asin: str) -> Dict[str, Any]:
        return {
            "asin": asin,
            "sales": 120,
            "page_reads": 3000,
            "royalties": 250.75,
            "country_breakdown": {"US": 100, "UK": 20},
        }

    def list_books(self, author_id: str) -> List[Dict[str, Any]]:
        return [
            {"title": "Sample Book 1", "asin": "B001", "status": "Published"},
            {"title": "Sample Book 2", "asin": "B002", "status": "Draft"},
        ]


class KDPDependencies:
    def __init__(self, api_key: str):
        self.client = KDPClient(api_key)

    def get_kdp_client(self) -> KDPClient:
        return self.client

class TestDependencyInjection:
    def test_database_dependencies(self):
        deps = DatabaseDependencies(user_id="user-123")
        assert deps.user_id == "user-123"

    def test_ai_model_dependencies(self):
        deps = AIModelDependencies(
            openai_api_key="test-key",
            preferred_model="gpt-4",
            temperature=0.7,
            max_tokens=2048
        )
        assert deps.openai_api_key == "test-key"
        assert deps.preferred_model == "gpt-4"
        assert deps.temperature == 0.7
        assert deps.max_tokens == 2048

    def test_scraping_dependencies(self):
        deps = ScrapingDependencies(headless=True)
        assert deps.headless is True
        assert deps.amazon_scraper is None or hasattr(deps.amazon_scraper, 'run')

    def test_manuscript_dependencies(self):
        db_deps = DatabaseDependencies(user_id="abc")
        ai_deps = AIModelDependencies(openai_api_key="key")
        deps = ManuscriptDependencies(
            db_deps=db_deps,
            ai_deps=ai_deps,
            style="narrative",
            target_length=5000,
            target_audience="general",
            output_formats=["pdf"]
        )
        assert deps.db_deps.user_id == "abc"
        assert deps.ai_deps.openai_api_key == "key"
        assert deps.style == "narrative"
        assert deps.target_length == 5000
        assert deps.output_formats == ["pdf"]

# =========================================================================
# Agent Registry (unchanged)
# =========================================================================


class AgentRegistry:
    def __init__(self):
        self._agents = {}
        self.logger = logging.getLogger(__name__)

    def register_agent(self, name: str, agent):
        self._agents[name] = agent
        self.logger.info(f"Registered agent: {name}")

    def get_agent(self, name: str):
        return self._agents.get(name)

    def list_agents(self) -> List[str]:
        return list(self._agents.keys())

    def remove_agent(self, name: str):
        if name in self._agents:
            del self._agents[name]
            self.logger.info(f"Removed agent: {name}")


agent_registry = AgentRegistry()
