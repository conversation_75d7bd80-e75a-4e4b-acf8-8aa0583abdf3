"""
PydanticAI Agent Manager
Central manager for all PydanticAI agents with unified interface
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable, Awaitable
from datetime import datetime
import logging

from app.agents.pydantic_ai_common import ExecutionStatus

from .pydantic_ai_base import (
    AgentExecutionResult,
    create_error_result,
    agent_registry
)

# Import all PydanticAI agents to register them
from .pydantic_ai_manuscript_generator import generate_manuscript
from .pydantic_ai_trend_analyzer import analyze_market_trends, analyze_keyword_opportunities
from .pydantic_ai_sales_monitor import monitor_sales_performance
from .pydantic_ai_cover_designer import design_book_cover
from .pydantic_ai_kdp_uploader import upload_to_kdp
from .pydantic_ai_additional_agents import research_topic, personalize_content, generate_multimodal_content

from app.monitoring.monitoring_setup import get_logger, monitor_operation, capture_exception, log_system_event
from app.agents.agent_resource_manager import (
    get_agent_resource_manager,
    AgentResourceManager,
)

logger = get_logger(__name__)

class PydanticAIAgentManager:
    """
    Central manager for all PydanticAI agents
    Provides a unified interface for agent execution and management
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._execution_history = []
        self.resource_manager = get_agent_resource_manager()

        # Register all agent types with resource manager
        self._agent_ids = {}
        for agent_type in [
            "manuscript_generator",
            "trend_analyzer",
            "sales_monitor",
            "cover_designer",
            "kdp_uploader",
            "research_assistant",
            "personalization_engine",
            "multimodal_generator",
        ]:
            agent_id = self.resource_manager.register_agent(agent_type)
            self._agent_ids[agent_type] = agent_id

        self._agent_executors: Dict[str, Callable[..., Awaitable[AgentExecutionResult]]] = {
            "manuscript_generator": self._execute_manuscript_generator,
            "trend_analyzer": self._execute_trend_analyzer,
            "sales_monitor": self._execute_sales_monitor,
            "cover_designer": self._execute_cover_designer,
            "kdp_uploader": self._execute_kdp_uploader,
            "research_assistant": self._execute_research_assistant,
            "personalization_engine": self._execute_personalization_engine,
            "multimodal_generator": self._execute_multimodal_generator,
        }

        logger.info(
            f"🤖 PydanticAI Agent Manager initialized with {len(self._agent_ids)} agents"
        )

    async def execute_agent(
        self,
        agent_name: str,
        task_data: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> AgentExecutionResult:
        """
        Execute a specific agent with given task data
        
        Args:
            agent_name: Name of the agent to execute
            task_data: Task-specific data and parameters
            user_id: User requesting the execution
        """
        start_time = datetime.now()
        agent_id = self._agent_ids.get(agent_name)

        with monitor_operation(
            f"agent_execution_{agent_name}",
            agent_name=agent_name,
            user_id=user_id,
            task_data_size=len(str(task_data)),
        ) as operation_id:
            try:
                executor = self._agent_executors.get(agent_name)
                if not executor:
                    raise ValueError(f"Unknown agent: {agent_name}")

                # Check resource availability before execution
                if not agent_id:
                    raise ValueError(f"Agent {agent_name} not properly registered")

                # Try to acquire execution slot
                if not await self.resource_manager.acquire_execution_slot(agent_id):
                    raise ValueError(
                        f"Agent {agent_name} cannot execute - resource limits exceeded"
                    )

                logger.info(
                    f"Agent execution started - agent_name={agent_name}, agent_id={agent_id}, operation_id={operation_id}, user_id={user_id}"
                )

                result = await executor(task_data, user_id)

                # Log successful execution
                execution_time = (datetime.now() - start_time).total_seconds()
                log_system_event(
                    "agent_execution_completed",
                    {
                        "agent_name": agent_name,
                        "agent_id": agent_id,
                        "operation_id": operation_id,
                        "user_id": user_id,
                        "execution_time": execution_time,
                        "status": result.status.value,
                        "success": result.status == ExecutionStatus.SUCCESS,
                    },
                )

                self._log_execution(agent_name, result, user_id)

                return result

            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()

                # Capture exception with detailed context
                capture_exception(
                    e,
                    {
                        "agent_name": agent_name,
                        "agent_id": agent_id,
                        "operation_id": operation_id,
                        "user_id": user_id,
                        "execution_time": execution_time,
                        "task_data_keys": (
                            list(task_data.keys())
                            if isinstance(task_data, dict)
                            else None
                        ),
                    },
                )

                error_result = create_error_result(
                    agent_name=agent_name,
                    error=e,
                    error_message=str(e),
                    execution_time=execution_time
                )
                self._log_execution(agent_name, error_result, user_id)
                return error_result

            finally:
                # Always release the execution slot to prevent resource leaks
                if agent_id:
                    self.resource_manager.release_execution_slot(agent_id)

    async def execute_workflow(
        self,
        workflow_steps: List[Dict[str, Any]],
        user_id: Optional[int] = None
    ) -> List[AgentExecutionResult]:
        """
        Execute a workflow consisting of multiple agent steps
        
        Args:
            workflow_steps: List of workflow steps, each containing agent_name and task_data
            user_id: User requesting the workflow execution
        """
        results = []
        workflow_context: Dict[str, Any] = {}

        for step in workflow_steps:
            agent_name = step.get('agent_name')
            task_data = step.get('task_data', {})

            if not agent_name:
                continue

            task_data['workflow_context'] = workflow_context

            result = await self.execute_agent(agent_name, task_data, user_id)
            results.append(result)

            if result.status == ExecutionStatus.SUCCESS:
                workflow_context[agent_name] = result.data
            elif not step.get('continue_on_error', False):
                break

        return results

    async def _execute_manuscript_generator(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await generate_manuscript(
            trend_data=task_data.get('trend_data', {}),
            user_id=user_id,
            style=task_data.get('style', 'professional'),
            target_audience=task_data.get('target_audience', 'general adults'),
            target_length=task_data.get('target_length', 8000),
            output_formats=task_data.get('output_formats', ['docx', 'epub', 'pdf'])
        )

    async def _execute_trend_analyzer(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        analysis_type = task_data.get('analysis_type', 'comprehensive')

        if analysis_type == 'keyword_research':
            return await analyze_keyword_opportunities(
                keywords=task_data.get('keywords', []),
                categories=task_data.get('categories', []),
                user_id=user_id
            )
        else:
            # Use the enhanced PyTrends analysis with industry focus
            from .pydantic_ai_trend_analyzer import analyze_market_trends_with_pytrends

            return await analyze_market_trends_with_pytrends(
                categories=task_data.get('categories'),
                industry_focus=task_data.get('industry_focus'),
                analysis_type=analysis_type,
                user_id=user_id,
                max_results=task_data.get('max_results', 50)
            )

    async def _execute_sales_monitor(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await monitor_sales_performance(
            date_range=task_data.get('date_range', 'last_30_days'),
            include_page_reads=task_data.get('include_page_reads', True),
            generate_insights=task_data.get('generate_insights', True),
            user_id=user_id
        )

    async def _execute_cover_designer(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await design_book_cover(
            book_title=task_data.get('book_title', ''),
            author_name=task_data.get('author_name', ''),
            genre=task_data.get('genre', ''),
            target_audience=task_data.get('target_audience', 'general adults'),
            style_preferences=task_data.get('style_preferences'),
            market_data=task_data.get('market_data'),
            user_id=user_id
        )

    async def _execute_kdp_uploader(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[str]
    ) -> AgentExecutionResult:
        return await upload_to_kdp(
            book_data=task_data.get('book_data', {}),
            file_paths=task_data.get('file_paths', {}),
            pricing_config=task_data.get('pricing_config'),
            auto_publish=task_data.get('auto_publish', False),
            user_id=user_id
        )

    async def _execute_research_assistant(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await research_topic(
            topic=task_data.get('topic', ''),
            research_depth=task_data.get('research_depth', 'comprehensive'),
            focus_areas=task_data.get('focus_areas'),
            user_id=user_id
        )

    async def _execute_personalization_engine(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await personalize_content(
            content=task_data.get('content', ''),
            user_profile=task_data.get('user_profile', {}),
            target_audience=task_data.get('target_audience', 'general'),
            personalization_level=task_data.get('personalization_level', 'moderate'),
            user_id=user_id
        )

    async def _execute_multimodal_generator(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        return await generate_multimodal_content(
            content_brief=task_data.get('content_brief', ''),
            modalities=task_data.get('modalities'),
            style_guide=task_data.get('style_guide'),
            target_format=task_data.get('target_format', 'web'),
            user_id=user_id
        )

    def _log_execution(
        self,
        agent_name: str,
        result: AgentExecutionResult,
        user_id: Optional[int]
    ):
        execution_record = {
            'timestamp': datetime.now(),
            'agent_name': agent_name,
            'user_id': user_id,
            'success': result.status == ExecutionStatus.SUCCESS,
            'execution_time': result.execution_time,
            'error': result.status.value if not result.status == ExecutionStatus.ERROR else None
        }

        self._execution_history.append(execution_record)

        if result.status == ExecutionStatus.SUCCESS:
            self.logger.info(f"Agent {agent_name} executed successfully in {result.execution_time:.2f}s")
        else:
            self.logger.error(f"Agent {agent_name} failed: {result.status.value}")

    def get_execution_history(
        self,
        agent_name: Optional[str] = None,
        user_id: Optional[int] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        history = self._execution_history

        if agent_name:
            history = [h for h in history if h['agent_name'] == agent_name]

        if user_id:
            history = [h for h in history if h['user_id'] == user_id]

        return history[-limit:]

    def track_ai_usage(self, agent_name: str, tokens_used: int = 0):
        """Track AI API usage for an agent"""
        agent_id = self._agent_ids.get(agent_name)
        if agent_id:
            self.resource_manager.record_ai_usage(agent_id, tokens_used)
            logger.debug(f"Tracked AI usage for {agent_name}: {tokens_used} tokens")

    def track_db_usage(self, agent_name: str):
        """Track database usage for an agent"""
        agent_id = self._agent_ids.get(agent_name)
        if agent_id:
            self.resource_manager.record_db_usage(agent_id)
            logger.debug(f"Tracked DB usage for {agent_name}")

    def get_agent_resource_status(self, agent_name: str) -> Dict[str, Any]:
        """Get resource status for a specific agent"""
        agent_id = self._agent_ids.get(agent_name)
        if not agent_id:
            return {"error": f"Agent {agent_name} not found"}

        return self.resource_manager.get_agent_status(agent_id)

    def get_system_resource_overview(self) -> Dict[str, Any]:
        """Get system-wide resource overview"""
        return self.resource_manager.get_system_overview()

    def get_agent_status(self) -> Dict[str, Any]:
        return {
            "registered_agents": agent_registry.list_agents(),
            "total_executions": len(self._execution_history),
            "recent_executions": len(
                [
                    h
                    for h in self._execution_history
                    if (datetime.now() - h["timestamp"]).seconds < 3600
                ]
            ),
            "success_rate": self._calculate_success_rate(),
            "resource_overview": self.get_system_resource_overview(),
        }

    def _calculate_success_rate(self) -> float:
        if not self._execution_history:
            return 0.0

        successful = sum(1 for h in self._execution_history if h['success'])
        return (successful / len(self._execution_history)) * 100

agent_manager = PydanticAIAgentManager()

async def execute_agent(
    agent_name: str,
    task_data: Dict[str, Any],
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    return await agent_manager.execute_agent(agent_name, task_data, user_id)

async def execute_workflow(
    workflow_steps: List[Dict[str, Any]],
    user_id: Optional[int] = None
) -> List[AgentExecutionResult]:
    return await agent_manager.execute_workflow(workflow_steps, user_id)

def get_agent_status() -> Dict[str, Any]:
    return agent_manager.get_agent_status()


def track_ai_usage(agent_name: str, tokens_used: int = 0):
    """Track AI API usage for an agent"""
    agent_manager.track_ai_usage(agent_name, tokens_used)


def track_db_usage(agent_name: str):
    """Track database usage for an agent"""
    agent_manager.track_db_usage(agent_name)


def get_agent_resource_status(agent_name: str) -> Dict[str, Any]:
    """Get resource status for a specific agent"""
    return agent_manager.get_agent_resource_status(agent_name)


def get_system_resource_overview() -> Dict[str, Any]:
    """Get system-wide resource overview"""
    return agent_manager.get_system_resource_overview()
