# cspell:ignore pytrends gprop
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from pydantic import BaseModel, Field
from datetime import datetime
import logging
import os

# Import pytrends with type ignore for mypy
from pytrends.request import TrendReq  # type: ignore

from app.utils.prompt import PromptTemplate
from .pydantic_ai_common import (
    AgentExecutionResult,
    create_error_result,
    create_success_result,
    with_error_handling,
    require_agents_available,
    with_execution_timing,
)
from .pydantic_ai_base import agent_registry

logger = logging.getLogger(__name__)

# =========================================================================
# Output Models
# =========================================================================


class ComprehensiveTrendAnalysis(BaseModel):
    opportunities: List[Dict[str, Any]] = Field(
        description="A list of identified market opportunities"
    )
    market_insights: Dict[str, Any] = Field(
        description="A summary of key market insights"
    )
    recommendations: List[str] = Field(
        description="Actionable recommendations based on the analysis"
    )


class KeywordAnalysis(BaseModel):
    keywords: List[Dict[str, Any]] = Field(
        description="A list of analyzed keywords with their metrics"
    )
    opportunities: List[Dict[str, Any]] = Field(
        description="A list of opportunities based on keyword analysis"
    )
    competition_analysis: Dict[str, Any] = Field(
        description="An analysis of the competition for the given keywords"
    )


# =========================================================================
# Model Selection and Agent Initialization
# =========================================================================

# Initialize variables that might not be imported
Agent: Optional[Any] = None
OpenAIModel: Optional[Any] = None
AnthropicModel: Optional[Any] = None

try:
    from pydantic_ai import Agent as ImportedAgent
    from pydantic_ai.models.openai import OpenAIModel as ImportedOpenAIModel
    from pydantic_ai.models.anthropic import AnthropicModel as ImportedAnthropicModel

    Agent = ImportedAgent
    OpenAIModel = ImportedOpenAIModel
    AnthropicModel = ImportedAnthropicModel

    PYDANTIC_AI_AVAILABLE = True
except ImportError:
    PYDANTIC_AI_AVAILABLE = False
    # Variables remain None as initialized

if TYPE_CHECKING:
    from pydantic_ai import Agent as TypedAgent

    trend_analyzer: Optional[TypedAgent[Any, ComprehensiveTrendAnalysis]] = None
    keyword_analyzer: Optional[TypedAgent[Any, KeywordAnalysis]] = None
else:
    trend_analyzer = None
    keyword_analyzer = None


def get_available_model() -> Optional[Any]:
    preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
    if PYDANTIC_AI_AVAILABLE:
        if (
            preferred == "openai"
            and os.getenv("OPENAI_API_KEY")
            and OpenAIModel is not None
        ):
            return OpenAIModel("gpt-4")
        elif (
            preferred == "anthropic"
            and os.getenv("ANTHROPIC_API_KEY")
            and AnthropicModel is not None
        ):
            return AnthropicModel("claude-3-sonnet-20240229")
    return None


def init_trend_agents():
    global trend_analyzer, keyword_analyzer

    if PYDANTIC_AI_AVAILABLE:
        assert Agent is not None  # Explicit check for type checker
        model = get_available_model()
        if model:
            trend_analyzer = Agent(
                model=model,
                output_type=ComprehensiveTrendAnalysis,
                system_prompt="You are an expert market trend analyzer...",
            )
            keyword_analyzer = Agent(
                model=model,
                output_type=KeywordAnalysis,
                system_prompt="You are an expert keyword analyst...",
            )
        else:
            logger.warning(
                "No valid model found for Trend or Keyword Analyzer initialization."
            )
    else:
        logger.warning(
            "PydanticAI is not available. Trend and Keyword agents will not be initialized."
        )


init_trend_agents()

# =========================================================================
# PyTrends Integration Functions
# =========================================================================

def get_pytrends_data(keywords: List[str], timeframe: str = 'today 3-m', geo: str = 'US') -> Dict[str, Any]:
    """Get real trend data using pytrends library"""
    try:
        # Initialize pytrends
        pytrends = TrendReq(hl='en-US', tz=360)

        # Build payload
        pytrends.build_payload(keywords, cat=0, timeframe=timeframe, geo=geo, gprop='')

        # Get interest over time
        interest_over_time = pytrends.interest_over_time()

        # Get related queries
        related_queries = pytrends.related_queries()

        # Get trending searches (daily)
        try:
            trending_searches = pytrends.trending_searches(pn='united_states')
        except:
            trending_searches = None

        # Format the data
        trend_data = {
            "keywords": keywords,
            "timeframe": timeframe,
            "geo": geo,
            "interest_over_time": (
                interest_over_time.to_dict() if not interest_over_time.empty else {}
            ),
            "related_queries": related_queries,
            "trending_searches": (
                trending_searches.head(20).values.tolist()
                if trending_searches is not None
                else []
            ),
            "peak_popularity": {},
            "average_interest": {},
        }

        # Calculate peak popularity and averages
        if not interest_over_time.empty:
            for keyword in keywords:
                if keyword in interest_over_time.columns:
                    trend_data["peak_popularity"][keyword] = int(interest_over_time[keyword].max())
                    trend_data["average_interest"][keyword] = float(interest_over_time[keyword].mean())

        return trend_data

    except Exception as e:
        logger.error(f"PyTrends error: {e}")
        return {
            "keywords": keywords,
            "error": str(e),
            "trending_searches": [],
            "peak_popularity": {},
            "average_interest": {}
        }

def get_book_category_trends(category: str = "self help", industry_focus: Optional[List[str]] = None) -> Dict[str, Any]:
    """Get trending topics for a specific book category with industry focus"""
    try:
        # Book-related keywords by category
        category_keywords = {
            "self help": ["self improvement", "personal development", "productivity", "mindfulness", "motivation"],
            "business": ["entrepreneurship", "leadership", "marketing", "finance", "startup"],
            "health": ["fitness", "nutrition", "wellness", "mental health", "weight loss"],
            "technology": ["artificial intelligence", "programming", "tech career", "coding", "software"],
            "romance": ["romance novel", "dating advice", "relationships", "love stories"],
            "mystery": ["mystery novel", "crime fiction", "detective stories", "thriller books"],
        }
        
        # Industry-specific keywords mapping
        industry_keywords = {
            "health": ["wellness", "fitness", "nutrition", "mental health", "healthcare", "medical"],
            "wealth": ["finance", "investing", "money", "business", "entrepreneurship", "passive income"],
            "beauty": ["skincare", "cosmetics", "beauty tips", "fashion", "style", "appearance"],
            "technology": ["ai", "software", "programming", "tech", "digital", "innovation"],
            "business": ["marketing", "sales", "leadership", "management", "startup", "growth"],
            "fitness": ["workout", "exercise", "training", "bodybuilding", "yoga", "sports"],
            "nutrition": ["diet", "healthy eating", "supplements", "weight loss", "recipes", "food"],
            "self-help": ["motivation", "productivity", "personal growth", "mindset", "success", "habits"],
            "finance": ["investing", "trading", "cryptocurrency", "real estate", "retirement", "budgeting"],
            "real estate": ["property", "housing", "investment", "rental", "mortgage", "flipping"],
            "marketing": ["digital marketing", "social media", "advertising", "branding", "seo", "content"],
            "education": ["learning", "skills", "training", "courses", "teaching", "knowledge"],
            "relationships": ["dating", "marriage", "communication", "love", "family", "social"],
            "lifestyle": ["travel", "home", "organization", "minimalism", "happiness", "work-life"],
            "travel": ["destinations", "adventure", "culture", "hotels", "vacation", "backpacking"],
            "food": ["cooking", "recipes", "restaurant", "cuisine", "baking", "gourmet"],
            "fashion": ["style", "clothing", "trends", "accessories", "designer", "wardrobe"],
            "entertainment": ["movies", "music", "gaming", "tv shows", "celebrities", "streaming"],
            "sports": ["football", "basketball", "soccer", "baseball", "athletics", "competition"],
            "science": ["research", "discovery", "physics", "biology", "chemistry", "innovation"]
        }
        
        keywords = category_keywords.get(category.lower(), ["books", "reading", category])
        
        # Enhance keywords with industry focus if provided
        if industry_focus:
            industry_specific_keywords = []
            for industry in industry_focus:
                industry_key = industry.lower()
                if industry_key in industry_keywords:
                    industry_specific_keywords.extend(industry_keywords[industry_key][:3])  # Top 3 from each industry
            
            # Combine category keywords with industry-specific ones
            if industry_specific_keywords:
                keywords = keywords[:2] + industry_specific_keywords  # Keep top 2 category keywords + industry keywords
                keywords = list(set(keywords))  # Remove duplicates
        
        # Get trend data
        trend_data = get_pytrends_data(keywords)
        
        # Add category and industry context
        trend_data["category"] = category
        trend_data["industry_focus"] = industry_focus
        trend_data["book_potential"] = _calculate_book_potential(trend_data)
        trend_data["enhanced_keywords"] = keywords  # Store the enhanced keyword list
        
        return trend_data
        
    except Exception as e:
        logger.error(f"Category trends error: {e}")
        return {"category": category, "error": str(e)}

def _calculate_book_potential(trend_data: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate book publishing potential based on trend data"""
    try:
        avg_interests = trend_data.get("average_interest", {})
        peak_popularity = trend_data.get("peak_popularity", {})

        if not avg_interests:
            return {"score": 0, "recommendation": "insufficient_data"}

        # Calculate overall score
        avg_score = sum(avg_interests.values()) / len(avg_interests)
        peak_score = sum(peak_popularity.values()) / len(peak_popularity) if peak_popularity else 0

        combined_score = (avg_score * 0.6) + (peak_score * 0.4)

        # Determine recommendation
        if combined_score >= 70:
            recommendation = "high_potential"
        elif combined_score >= 40:
            recommendation = "moderate_potential"
        elif combined_score >= 20:
            recommendation = "low_potential"
        else:
            recommendation = "insufficient_interest"

        return {
            "score": round(combined_score, 2),
            "recommendation": recommendation,
            "avg_interest": round(avg_score, 2),
            "peak_interest": round(peak_score, 2)
        }

    except Exception as e:
        logger.error(f"Book potential calculation error: {e}")
        return {"score": 0, "recommendation": "calculation_error"}


async def analyze_market_trends_with_pytrends(
    categories: Optional[List[str]] = None,
    industry_focus: Optional[List[str]] = None,
    analysis_type: str = "comprehensive",
    user_id: Optional[int] = None,
    max_results: int = 50,
) -> AgentExecutionResult:
    """Enhanced trend analysis using real PyTrends data"""
    # Note: user_id parameter reserved for future user-specific analytics
    _ = user_id  # Acknowledge parameter to suppress warnings

    agent_name = "pydantic_ai_trend_analyzer"
    start_time = datetime.now()

    try:
        # Get real trend data using pytrends with industry focus
        all_trend_data = []

        # Use industry focus to determine categories if not provided
        if not categories and industry_focus:
            # Map industries to relevant book categories
            industry_to_category = {
                "health": "health",
                "wealth": "business", 
                "beauty": "lifestyle",
                "technology": "technology",
                "business": "business",
                "fitness": "health",
                "nutrition": "health",
                "self-help": "self help",
                "finance": "business"
            }
            categories = []
            for industry in industry_focus:
                category = industry_to_category.get(industry.lower(), "self help")
                if category not in categories:
                    categories.append(category)

        if categories:
            for category in categories:
                category_trends = get_book_category_trends(category, industry_focus)
                all_trend_data.append(category_trends)
        else:
            # Default categories for book publishing, enhanced with industry focus
            # Convert industry focus to book categories
            default_book_categories = ["self help", "business", "health", "technology"]
            for category in default_book_categories:
                category_trends = get_book_category_trends(category, industry_focus)
                all_trend_data.append(category_trends)

        # Combine and analyze the data
        analysis_result: Dict[str, Any] = {
            "trend_data": all_trend_data,
            "analysis_type": analysis_type,
            "total_categories": len(all_trend_data),
            "high_potential_categories": [
                data["category"]
                for data in all_trend_data
                if data.get("book_potential", {}).get("recommendation")
                == "high_potential"
            ],
            "trending_keywords": [],
            "recommendations": [],
        }

        # Extract trending keywords across all categories
        for data in all_trend_data:
            trending = data.get("trending_searches", [])
            if trending:
                analysis_result["trending_keywords"].extend(trending[:5])  # Top 5 from each category

        # Generate recommendations
        for data in all_trend_data:
            potential = data.get("book_potential", {})
            if potential.get("score", 0) >= 40:  # Moderate or high potential
                analysis_result["recommendations"].append({
                    "category": data["category"],
                    "score": potential.get("score", 0),
                    "recommendation": potential.get("recommendation"),
                    "suggested_keywords": list(data.get("average_interest", {}).keys())
                })

        execution_time = (datetime.now() - start_time).total_seconds()

        return create_success_result(
            agent_name=agent_name,
            data=analysis_result,
            execution_time=execution_time,
            metadata={
                "categories": categories or ["self help", "business", "health", "technology"],
                "industry_focus": industry_focus or ["Health", "Wealth", "Beauty"],
                "analysis_config": {
                    "analysis_type": analysis_type,
                    "max_results": max_results,
                    "data_source": "pytrends",
                    "industry_enhanced": bool(industry_focus)
                }
            }
        )

    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        return create_error_result(
            agent_name=agent_name,
            error=f"PyTrends analysis failed: {str(e)}",
            execution_time=execution_time
        )


# =========================================================================
# Agent Execution Functions
# =========================================================================


@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_trend_analyzer", logger=logger)
async def analyze_market_trends(
    categories: Optional[List[str]] = None,
    analysis_type: str = "comprehensive",
    user_id: Optional[int] = None,
    max_results: int = 50,
) -> AgentExecutionResult:
    # Note: user_id parameter reserved for future user-specific analytics
    _ = user_id  # Acknowledge parameter to suppress warnings

    agent_name = "pydantic_ai_trend_analyzer"

    if trend_analyzer is None:
        return create_error_result(
            agent_name=agent_name,
            error="Trend analyzer agent is not initialized. Check API keys and model availability.",
            execution_time=0.0,
        )

    prompt_template = PromptTemplate.from_string(
        """
        Analyze market trends for the following categories: {{ categories }}
        Analysis type: {{ analysis_type }}
        Max results: {{ max_results }}
    """
    )

    prompt = prompt_template.format(
        categories=categories, analysis_type=analysis_type, max_results=max_results
    )

    result = await trend_analyzer.run(prompt)

    return create_success_result(
        agent_name=agent_name,
        data=result.output.model_dump(),
        execution_time=(
            float(getattr(getattr(result, "metrics", None), "total_time", 0.0))
            if hasattr(getattr(result, "metrics", None), "total_time")
            else 0.0
        ),
        metadata={
            "categories": categories,
            "analysis_config": {
                "analysis_type": analysis_type,
                "max_results": max_results,
            },
        },
    )


@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_keyword_analyzer", logger=logger)
async def analyze_keyword_opportunities(
    keywords: List[str],
    categories: List[str],
    user_id: Optional[int] = None,
) -> AgentExecutionResult:
    # Note: user_id parameter reserved for future user-specific analytics
    _ = user_id  # Acknowledge parameter to suppress warnings

    agent_name = "pydantic_ai_keyword_analyzer"

    if keyword_analyzer is None:
        return create_error_result(
            agent_name=agent_name,
            error="Keyword analyzer agent is not initialized. Check API keys and model availability.",
            execution_time=0.0,
        )

    prompt_template = PromptTemplate.from_string(
        """
        Analyze keyword opportunities for the following keywords: {{ keywords }}
        Categories: {{ categories }}
    """
    )

    prompt = prompt_template.format(keywords=keywords, categories=categories)

    result = await keyword_analyzer.run(prompt)

    return create_success_result(
        agent_name=agent_name,
        data=result.output.model_dump(),
        execution_time=(
            float(getattr(getattr(result, "metrics", None), "total_time", 0.0))
            if hasattr(getattr(result, "metrics", None), "total_time")
            else 0.0
        ),
        metadata={"keywords": keywords, "categories": categories},
    )


# Register agents with the registry - always register the functions even if agents aren't initialized
if PYDANTIC_AI_AVAILABLE:
    agent_registry.register_agent("trend_analyzer", analyze_market_trends)
    agent_registry.register_agent("pydantic_ai_trend_analyzer", analyze_market_trends)
    agent_registry.register_agent("keyword_analyzer", analyze_keyword_opportunities)
    agent_registry.register_agent(
        "pydantic_ai_keyword_analyzer", analyze_keyword_opportunities
    )
