"""
PydanticAI Cover Designer Agent
This version can be imported safely without requiring API keys
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging
import os

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.agent import Agent as TypedAgent

from app.agents.pydantic_ai_common import (
    AgentExecutionResult,
    create_error_result,
    create_success_result,
    with_error_handling
)
from app.agents.pydantic_ai_base import agent_registry

logger = logging.getLogger(__name__)

# =========================================================================
# Output Model
# =========================================================================

class ComprehensiveCoverDesign(BaseModel):
    design_concept: Dict[str, Any] = Field(description="The design concept and rationale")
    color_palette: Dict[str, Any] = Field(description="The color palette specification")
    typography: Dict[str, Any] = Field(description="The typography specifications")
    layout: Dict[str, Any] = Field(description="The layout and composition details")
    image_prompts: List[str] = Field(description="A list of prompts for generating images")

# =========================================================================
# Agent Initialization
# =========================================================================

def get_available_model():
    if os.getenv("OPENAI_API_KEY"):
        return OpenAIModel("gpt-4")
    elif os.getenv("ANTHROPIC_API_KEY"):
        return AnthropicModel("claude-3-sonnet-20240229")
    return None

cover_designer: Optional[TypedAgent[Any, ComprehensiveCoverDesign]] = None
model = get_available_model()

if model:
    cover_designer = Agent(
        model=model,
        output_type=ComprehensiveCoverDesign,
        system_prompt="You are an expert cover designer..."
    )

# =========================================================================
# Agent Runner Function
# =========================================================================

@with_error_handling(agent_name="pydantic_ai_cover_designer", logger=logger)
async def design_book_cover(
    book_title: str,
    author_name: str,
    genre: str,
    target_audience: str = "general adults",
    style_preferences: Optional[Dict[str, Any]] = None,
    market_data: Optional[Dict[str, Any]] = None,
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    start_time = datetime.now()
    agent_name = "pydantic_ai_cover_designer"

    if not cover_designer:
        return create_error_result(
            agent_name=agent_name,
            error="Cover designer agent could not be initialized. Check your API keys.",
            error_message="Cover designer agent could not be initialized. Check your API keys.",
            execution_time=(datetime.now() - start_time).total_seconds()
        )

    prompt = f"""
    Design a book cover for the following book:
    Title: {book_title}
    Author: {author_name}
    Genre: {genre}
    Target Audience: {target_audience}
    Style Preferences: {style_preferences}
    Market Data: {market_data}
    """

    result = await cover_designer.run(prompt)
    execution_time = (datetime.now() - start_time).total_seconds()

    return create_success_result(
        agent_name=agent_name,
        data=result.output.model_dump(),
        execution_time=execution_time,
        metadata={
            "book_title": book_title,
            "author_name": author_name,
            "genre": genre,
            "design_config": {
                "target_audience": target_audience,
                "style_preferences": style_preferences or {},
                "market_data": market_data or {}
            }
        }
    )

# Register agent with the registry
agent_registry.register_agent("cover_designer", design_book_cover)
agent_registry.register_agent("pydantic_ai_cover_designer", design_book_cover)