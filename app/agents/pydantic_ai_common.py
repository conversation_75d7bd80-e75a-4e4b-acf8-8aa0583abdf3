"""
PydanticAI Common Utilities
Shared functionality, result handling, and utilities for all PydanticAI agents
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, Field
# Sentry initialization is handled centrally by the monitoring singleton


# =========================================================================
# Availability Flags
# =========================================================================

def check_pydantic_ai_availability() -> bool:
    try:
        import pydantic_ai
        return True
    except ImportError:
        return False

def check_api_keys_availability() -> bool:
    return bool(
        os.getenv('OPENAI_API_KEY') or 
        os.getenv('ANTHROPIC_API_KEY') or
        os.getenv('GOOGLE_API_KEY')
    )

PYDANTIC_AI_AVAILABLE = check_pydantic_ai_availability()
API_KEYS_AVAILABLE = check_api_keys_availability()
AGENTS_AVAILABLE = PYDANTIC_AI_AVAILABLE and API_KEYS_AVAILABLE

# =========================================================================
# Core Execution Models
# =========================================================================

class ExecutionStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    PENDING = "pending"

class AgentExecutionResult(BaseModel):
    agent_name: str
    status: ExecutionStatus
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: float
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None
    warnings: List[str] = Field(default_factory=list)

class AgentCapability(BaseModel):
    name: str
    description: str
    required_dependencies: List[str] = Field(default_factory=list)
    optional_dependencies: List[str] = Field(default_factory=list)
    supported_models: List[str] = Field(default_factory=list)

class AgentHealth(BaseModel):
    agent_name: str
    is_healthy: bool
    dependencies_available: bool
    api_keys_available: bool
    last_execution: Optional[datetime] = None
    error_count: int = 0
    capabilities: List[AgentCapability] = Field(default_factory=list)

# =========================================================================
# Result Builders
# =========================================================================

def safe_execution_time(result) -> float:
    """Safely extract execution time from result, handling mock objects in tests"""
    try:
        time_value = getattr(getattr(result, "metrics", None), "total_time", 0.0)
        return float(time_value)
    except (TypeError, ValueError, AttributeError):
        return 0.0


def create_success_result(agent_name: str, data: Dict[str, Any], execution_time: float, metadata: Optional[Dict[str, Any]] = None, warnings: Optional[List[str]] = None) -> AgentExecutionResult:
    return AgentExecutionResult(
        agent_name=agent_name,
        status=ExecutionStatus.SUCCESS,
        data=data,
        execution_time=execution_time,
        metadata=metadata or {},
        warnings=warnings or []
    )

def create_error_result(agent_name: str, error: Union[str, Exception], execution_time: float = 0.0, metadata: Optional[Dict[str, Any]] = None, error_message: Optional[str] = None) -> AgentExecutionResult:
    msg = error_message or str(error)
    return AgentExecutionResult(
        agent_name=agent_name,
        status=ExecutionStatus.ERROR,
        error_message=msg,
        execution_time=execution_time,
        metadata=metadata or {}
    )

def create_warning_result(agent_name: str, data: Dict[str, Any], warnings: List[str], execution_time: float, metadata: Optional[Dict[str, Any]] = None) -> AgentExecutionResult:
    return AgentExecutionResult(
        agent_name=agent_name,
        status=ExecutionStatus.WARNING,
        data=data,
        warnings=warnings,
        execution_time=execution_time,
        metadata=metadata or {}
    )

def create_timeout_result(agent_name: str, timeout_duration: float, metadata: Optional[Dict[str, Any]] = None) -> AgentExecutionResult:
    return AgentExecutionResult(
        agent_name=agent_name,
        status=ExecutionStatus.TIMEOUT,
        error_message=f"Agent execution timed out after {timeout_duration} seconds",
        execution_time=timeout_duration,
        metadata=metadata or {}
    )

def create_mock_response(agent_name: str, mock_data: Dict[str, Any], execution_time: float = 0.1, include_disclaimer: bool = True) -> AgentExecutionResult:
    if include_disclaimer:
        mock_data.update({
            '_mock_notice': f'This is mock data - {agent_name} agent is disabled',
            '_reason': 'PydanticAI agents not available (missing API keys or dependencies)'
        })
    return create_success_result(agent_name, mock_data, execution_time, metadata={"is_mock": True, "agents_available": AGENTS_AVAILABLE})

# =========================================================================
# Execution Decorators
# =========================================================================

def require_agents_available(func):
    async def wrapper(*args, **kwargs):
        if not AGENTS_AVAILABLE:
            agent_name = kwargs.get('agent_name', func.__name__)
            return create_error_result(agent_name, "PydanticAI agents not available", 0.0)
        return await func(*args, **kwargs)
    return wrapper

def with_execution_timing(func):
    async def wrapper(*args, **kwargs):
        start = datetime.now()
        try:
            result = await func(*args, **kwargs)
            if isinstance(result, AgentExecutionResult):
                result.execution_time = (datetime.now() - start).total_seconds()
            return result
        except Exception as e:
            raise e
    return wrapper


def with_error_handling(agent_name: str, logger: Optional[logging.Logger] = None):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start = datetime.now()
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if logger:
                    logger.error(f"{agent_name} execution failed: {e}", exc_info=True)
                try:
                    import sentry_sdk
                    sentry_sdk.capture_exception(e)
                except ImportError:
                    pass
                return create_error_result(agent_name, e, (datetime.now() - start).total_seconds())
        return wrapper
    return decorator

# =========================================================================
# Health Monitoring
# =========================================================================

class AgentHealthMonitor:
    def __init__(self):
        self.agent_stats: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)

    def record_execution(self, result: AgentExecutionResult):
        name = result.agent_name
        stats = self.agent_stats.setdefault(name, {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_execution_time': 0.0,
            'last_execution': None,
            'recent_errors': []
        })
        stats['total_executions'] += 1
        stats['total_execution_time'] += result.execution_time
        stats['last_execution'] = result.timestamp
        if result.status == ExecutionStatus.SUCCESS:
            stats['successful_executions'] += 1
        else:
            stats['failed_executions'] += 1
            if result.error_message:
                stats['recent_errors'].append({'timestamp': result.timestamp, 'error': result.error_message})
                stats['recent_errors'] = stats['recent_errors'][-10:]

    def get_agent_health(self, name: str) -> AgentHealth:
        stats = self.agent_stats.get(name, {})
        total = stats.get('total_executions', 0)
        failed = stats.get('failed_executions', 0)
        success_rate = (total - failed) / total if total > 0 else 1.0
        return AgentHealth(
            agent_name=name,
            is_healthy=success_rate >= 0.8 and AGENTS_AVAILABLE,
            dependencies_available=PYDANTIC_AI_AVAILABLE,
            api_keys_available=API_KEYS_AVAILABLE,
            last_execution=stats.get('last_execution'),
            error_count=len(stats.get('recent_errors', []))
        )

    def get_overall_health(self) -> Dict[str, Any]:
        health = {name: self.get_agent_health(name) for name in self.agent_stats}
        healthy_agents = sum(1 for h in health.values() if h.is_healthy)
        return {
            'system_healthy': AGENTS_AVAILABLE and healthy_agents / max(len(health), 1) >= 0.8,
            'agents_available': AGENTS_AVAILABLE,
            'pydantic_ai_available': PYDANTIC_AI_AVAILABLE,
            'api_keys_available': API_KEYS_AVAILABLE,
            'healthy_agents': healthy_agents,
            'total_agents': len(health),
            'agent_healths': health
        }

# =========================================================================
# Registries & Config
# =========================================================================

@dataclass
class AgentConfig:
    max_retries: int = 3
    timeout_seconds: float = 300.0
    rate_limit_delay: float = 1.0
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600
    log_level: str = "INFO"
    enable_metrics: bool = True

class PydanticAIRegistry:
    def __init__(self):
        self.agents: Dict[str, Any] = {}
        self.capabilities: Dict[str, List[AgentCapability]] = {}
        self.health_monitor = AgentHealthMonitor()
        self.logger = logging.getLogger(__name__)

    def register_agent(self, name: str, agent: Any, capabilities: Optional[List[AgentCapability]] = None):
        if not AGENTS_AVAILABLE:
            self.logger.warning(f"Registering agent {name} but agents are not available")
        self.agents[name] = agent
        self.capabilities[name] = capabilities or []
        self.logger.info(f"Registered agent: {name}")

    def get_agent(self, name: str) -> Optional[Any]:
        return self.agents.get(name)

    def list_agents(self) -> List[str]:
        return list(self.agents.keys())

    def get_agent_capabilities(self, name: str) -> List[AgentCapability]:
        return self.capabilities.get(name, [])

    def is_agent_available(self, name: str) -> bool:
        return name in self.agents and AGENTS_AVAILABLE

# =========================================================================
# Global Instances
# =========================================================================

agent_registry = PydanticAIRegistry()
health_monitor = AgentHealthMonitor()

class MockDataGenerator:
    @staticmethod
    def generate_book_data(title: str = "Sample Book") -> Dict[str, Any]:
        return {
            'title': title,
            'author': 'Mock Author',
            'word_count': 25000,
            'chapters': 10,
            'genre': 'Self-Help',
            'target_audience': 'General Adult',
            'estimated_completion_time': '2-3 hours reading',
            'keywords': ['productivity', 'success', 'improvement'],
            'quality_score': 85.5
        }

    @staticmethod
    def generate_market_analysis() -> Dict[str, Any]:
        return {
            'trending_categories': ['Self-Help', 'Business', 'Health & Fitness'],
            'market_saturation': 'medium',
            'competition_level': 'moderate',
            'opportunity_score': 78.5,
            'recommended_price_range': {'min': 2.99, 'max': 9.99},
            'target_keywords': ['productivity', 'habits', 'success'],
            'market_gaps': ['specific productivity techniques', 'micro-habits formation']
        }

    @staticmethod
    def generate_sales_data() -> Dict[str, Any]:
        return {
            'total_sales': 150,
            'revenue': 899.50,
            'page_reads': 12500,
            'average_rating': 4.2,
            'total_reviews': 23,
            'sales_trend': 'increasing',
            'performance_percentile': 75
        }

mock_generator = MockDataGenerator()

# =========================================================================
# Utility
# =========================================================================

async def execute_with_timeout(coro, timeout_seconds: float = 300.0, agent_name: str = "unknown") -> AgentExecutionResult:
    try:
        return await asyncio.wait_for(coro, timeout=timeout_seconds)
    except asyncio.TimeoutError:
        return create_timeout_result(agent_name, timeout_seconds)

def validate_agent_dependencies(required_deps: List[str]) -> List[str]:
    missing = []
    if "pydantic_ai" in required_deps and not PYDANTIC_AI_AVAILABLE:
        missing.append("pydantic_ai")
    if "api_keys" in required_deps and not API_KEYS_AVAILABLE:
        missing.append("api_keys")
    return missing

def get_available_models() -> List[str]:
    models = []
    if os.getenv('OPENAI_API_KEY'):
        models += ['openai:gpt-4', 'openai:gpt-4-turbo', 'openai:gpt-3.5-turbo']
    if os.getenv('ANTHROPIC_API_KEY'):
        models += ['anthropic:claude-3-sonnet', 'anthropic:claude-3-haiku']
    if os.getenv('GOOGLE_API_KEY'):
        models += ['google:gemini-pro', 'google:gemini-pro-vision']
    return models

def setup_agent_logging(log_level: str = "INFO") -> logging.Logger:
    logger = logging.getLogger("pydantic_ai_agents")
    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, log_level.upper()))
    return logger

logger = setup_agent_logging()
