"""
PydanticAI Manuscript Generator Agent
Production-ready implementation using centralized PydanticAI result schema.
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.agent import Agent as TypedAgent

from app.agents.pydantic_ai_common import (
    AgentExecutionResult,
    create_success_result,
    create_error_result,
    with_error_handling
)
from app.agents.pydantic_ai_base import agent_registry

logger = logging.getLogger(__name__)

# =========================================================================
# Output Schema
# =========================================================================

class ManuscriptGenerationResult(BaseModel):
    title: str = Field(description="The title of the generated manuscript")
    chapters: List[Dict[str, Any]] = Field(description="A list of generated chapters")
    word_count: int = Field(description="The total word count of the manuscript")
    quality_score: float = Field(description="An overall quality score for the manuscript")

# =========================================================================
# Model and Agent Setup
# =========================================================================

def get_available_model():
    if os.getenv("OPENAI_API_KEY"):
        return OpenAIModel("gpt-4")
    elif os.getenv("ANTHROPIC_API_KEY"):
        return AnthropicModel("claude-3-sonnet-20240229")
    return None

manuscript_agent: Optional[TypedAgent[Any, ManuscriptGenerationResult]] = None
model = get_available_model()

if model:
    manuscript_agent = Agent(
        model=model,
        output_type=ManuscriptGenerationResult,
        system_prompt="You are an expert manuscript generator. You write high-quality, structured non-fiction manuscripts with logical progression, compelling chapter flow, and professional tone."
    )

# =========================================================================
# Runner Function
# =========================================================================

@with_error_handling(agent_name="pydantic_ai_manuscript_generator", logger=logger)
async def generate_manuscript(
    trend_data: Dict[str, Any],
    user_id: Optional[int] = None,
    style: str = "professional",
    target_audience: str = "general adults",
    target_length: int = 8000,
    output_formats: Optional[List[str]] = None
) -> AgentExecutionResult:
    start_time = datetime.now()
    agent_name = "pydantic_ai_manuscript_generator"

    if not manuscript_agent:
        return create_error_result(
            agent_name=agent_name,
            error="Manuscript generator agent could not be initialized. Check your API keys.",
            error_message="Manuscript generator agent could not be initialized. Check your API keys.",
            execution_time=(datetime.now() - start_time).total_seconds()
        )

    prompt = f"""
    Generate a structured non-fiction manuscript with the following requirements:

    Trend Data: {trend_data}
    Style: {style}
    Target Audience: {target_audience}
    Target Length: {target_length} words
    Output Formats: {output_formats or ['docx', 'epub', 'pdf']}
    """

    result = await manuscript_agent.run(prompt)
    execution_time = (datetime.now() - start_time).total_seconds()

    return create_success_result(
        agent_name=agent_name,
        data=result.output.model_dump(),
        execution_time=execution_time,
        metadata={
            "trend_data": trend_data,
            "generation_config": {
                "style": style,
                "target_audience": target_audience,
                "target_length": target_length,
                "output_formats": output_formats or ["docx", "epub", "pdf"]
            }
        }
    )

# Register agent with the registry
agent_registry.register_agent("manuscript_generator", generate_manuscript)
agent_registry.register_agent("pydantic_ai_manuscript_generator", generate_manuscript)
