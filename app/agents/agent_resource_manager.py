# app/agents/agent_resource_manager.py - Agent Resource Management and Isolation

import asyncio
import time
import psutil
from typing import Dict, Any, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging

from app.monitoring.monitoring_setup import get_logger, capture_exception, monitor_operation

logger = get_logger(__name__)

@dataclass
class AgentResourceQuota:
    """Resource quota configuration for different agent types"""
    
    # Agent identification
    agent_type: str
    agent_id: str = field(default_factory=lambda: f"agent_{int(time.time())}")
    
    # Concurrency limits
    max_concurrent_executions: int = 1
    max_queue_size: int = 10
    
    # AI API quotas
    ai_requests_per_minute: int = 5
    ai_tokens_per_minute: int = 20000
    
    # Database quotas
    db_queries_per_minute: int = 100
    max_db_connections: int = 2
    
    # Memory and CPU limits
    max_memory_mb: int = 512
    max_cpu_percent: int = 25
    
    # Execution timeouts
    execution_timeout_seconds: int = 300  # 5 minutes
    
    @classmethod
    def get_quota_for_agent(cls, agent_type: str) -> 'AgentResourceQuota':
        """Get predefined quota based on agent type"""
        quotas = {
            'manuscript_generator': cls(
                agent_type='manuscript_generator',
                max_concurrent_executions=1,  # Heavy operations
                ai_requests_per_minute=3,
                ai_tokens_per_minute=50000,   # High token usage
                db_queries_per_minute=50,
                max_memory_mb=1024,           # More memory for content
                execution_timeout_seconds=1800  # 30 minutes
            ),
            'trend_analyzer': cls(
                agent_type='trend_analyzer',
                max_concurrent_executions=2,  # Can run multiple analyses
                ai_requests_per_minute=10,    # Frequent API calls
                ai_tokens_per_minute=20000,
                db_queries_per_minute=200,    # Lots of trend data queries
                max_memory_mb=512,
                execution_timeout_seconds=600  # 10 minutes
            ),
            'cover_designer': cls(
                agent_type='cover_designer',
                max_concurrent_executions=2,
                ai_requests_per_minute=5,     # Image generation
                ai_tokens_per_minute=10000,
                db_queries_per_minute=20,
                max_memory_mb=256,
                execution_timeout_seconds=300  # 5 minutes
            ),
            'sales_monitor': cls(
                agent_type='sales_monitor',
                max_concurrent_executions=3,  # Lightweight monitoring
                ai_requests_per_minute=2,
                ai_tokens_per_minute=5000,
                db_queries_per_minute=150,    # Lots of sales data
                max_memory_mb=256,
                execution_timeout_seconds=180  # 3 minutes
            ),
            'research_assistant': cls(
                agent_type='research_assistant',
                max_concurrent_executions=2,
                ai_requests_per_minute=8,     # Moderate API usage
                ai_tokens_per_minute=30000,
                db_queries_per_minute=100,
                max_memory_mb=512,
                execution_timeout_seconds=900  # 15 minutes
            ),
            'personalization_engine': cls(
                agent_type='personalization_engine',
                max_concurrent_executions=3,
                ai_requests_per_minute=6,
                ai_tokens_per_minute=15000,
                db_queries_per_minute=80,
                max_memory_mb=384,
                execution_timeout_seconds=300
            ),
            'multimodal_generator': cls(
                agent_type='multimodal_generator',
                max_concurrent_executions=1,  # Resource intensive
                ai_requests_per_minute=4,
                ai_tokens_per_minute=25000,
                db_queries_per_minute=40,
                max_memory_mb=768,
                execution_timeout_seconds=900
            ),
            'kdp_uploader': cls(
                agent_type='kdp_uploader',
                max_concurrent_executions=2,
                ai_requests_per_minute=1,     # Minimal AI usage
                ai_tokens_per_minute=2000,
                db_queries_per_minute=30,
                max_memory_mb=256,
                execution_timeout_seconds=600
            )
        }
        
        return quotas.get(agent_type, cls(
            agent_type=agent_type,
            # Default conservative limits
            max_concurrent_executions=1,
            ai_requests_per_minute=5,
            ai_tokens_per_minute=10000,
            db_queries_per_minute=50,
            max_memory_mb=256,
            execution_timeout_seconds=300
        ))

@dataclass
class ResourceUsage:
    """Track current resource usage for an agent"""
    
    # Current usage
    concurrent_executions: int = 0
    queue_size: int = 0
    
    # API usage tracking (last minute)
    ai_requests_last_minute: deque = field(default_factory=deque)
    ai_tokens_last_minute: int = 0
    db_queries_last_minute: deque = field(default_factory=deque)
    
    # System resources
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.utcnow)
    
    def cleanup_old_requests(self):
        """Remove requests older than 1 minute"""
        cutoff = datetime.utcnow() - timedelta(minutes=1)
        
        # Clean AI requests
        while self.ai_requests_last_minute and self.ai_requests_last_minute[0] < cutoff:
            self.ai_requests_last_minute.popleft()
            
        # Clean DB queries
        while self.db_queries_last_minute and self.db_queries_last_minute[0] < cutoff:
            self.db_queries_last_minute.popleft()
    
    def record_ai_request(self, tokens_used: int = 0):
        """Record an AI API request"""
        now = datetime.utcnow()
        self.ai_requests_last_minute.append(now)
        self.ai_tokens_last_minute += tokens_used
        self.cleanup_old_requests()
    
    def record_db_query(self):
        """Record a database query"""
        now = datetime.utcnow()
        self.db_queries_last_minute.append(now)
        self.cleanup_old_requests()
    
    def update_system_usage(self):
        """Update system resource usage"""
        try:
            process = psutil.Process()
            self.memory_usage_mb = process.memory_info().rss / 1024 / 1024
            self.cpu_usage_percent = process.cpu_percent()
            self.last_updated = datetime.utcnow()
        except Exception as e:
            logger.warning(f"Failed to update system usage: {e}")

class AgentResourceManager:
    """Manages resource allocation and quotas for agents"""
    
    def __init__(self):
        self.quotas: Dict[str, AgentResourceQuota] = {}
        self.usage: Dict[str, ResourceUsage] = defaultdict(ResourceUsage)
        self.active_agents: Set[str] = set()
        self.execution_semaphores: Dict[str, asyncio.Semaphore] = {}
        self._lock = asyncio.Lock()
        
        logger.info("🔧 Agent Resource Manager initialized")
    
    def register_agent(self, agent_type: str, agent_id: Optional[str] = None) -> str:
        """Register an agent and get its unique ID"""
        if not agent_id:
            agent_id = f"{agent_type}_{int(time.time())}_{id(self)}"
        
        quota = AgentResourceQuota.get_quota_for_agent(agent_type)
        quota.agent_id = agent_id
        
        self.quotas[agent_id] = quota
        self.usage[agent_id] = ResourceUsage()
        self.execution_semaphores[agent_id] = asyncio.Semaphore(quota.max_concurrent_executions)
        
        logger.info(f"🤖 Registered agent: {agent_type} -> {agent_id}")
        return agent_id
    
    async def can_execute(self, agent_id: str) -> bool:
        """Check if an agent can execute based on resource quotas"""
        if agent_id not in self.quotas:
            logger.warning(f"Unknown agent {agent_id}, denying execution")
            return False
        
        quota = self.quotas[agent_id]
        usage = self.usage[agent_id]
        
        # Update system usage
        usage.update_system_usage()
        usage.cleanup_old_requests()
        
        # Check concurrency limits
        if usage.concurrent_executions >= quota.max_concurrent_executions:
            logger.warning(f"Agent {agent_id} at max concurrency: {usage.concurrent_executions}")
            return False
        
        # Check queue size
        if usage.queue_size >= quota.max_queue_size:
            logger.warning(f"Agent {agent_id} queue full: {usage.queue_size}")
            return False
        
        # Check AI API limits
        if len(usage.ai_requests_last_minute) >= quota.ai_requests_per_minute:
            logger.warning(f"Agent {agent_id} hit AI request limit")
            return False
        
        if usage.ai_tokens_last_minute >= quota.ai_tokens_per_minute:
            logger.warning(f"Agent {agent_id} hit AI token limit")
            return False
        
        # Check database limits
        if len(usage.db_queries_last_minute) >= quota.db_queries_per_minute:
            logger.warning(f"Agent {agent_id} hit DB query limit")
            return False
        
        # Check memory limits
        if usage.memory_usage_mb > quota.max_memory_mb:
            logger.warning(f"Agent {agent_id} exceeds memory limit: {usage.memory_usage_mb}MB")
            return False
        
        # Check CPU limits
        if usage.cpu_usage_percent > quota.max_cpu_percent:
            logger.warning(f"Agent {agent_id} exceeds CPU limit: {usage.cpu_usage_percent}%")
            return False
        
        return True
    
    async def acquire_execution_slot(self, agent_id: str) -> bool:
        """Acquire an execution slot for an agent"""
        if not await self.can_execute(agent_id):
            return False
        
        try:
            # Try to acquire semaphore without blocking
            semaphore = self.execution_semaphores.get(agent_id)
            if not semaphore:
                return False
            
            # Check if we can acquire without blocking
            if semaphore.locked() and semaphore._value == 0:
                return False
            
            # Acquire the semaphore (this will be immediate if available)
            await semaphore.acquire()
            
            self.usage[agent_id].concurrent_executions += 1
            self.active_agents.add(agent_id)
            logger.info(f"🔒 Acquired execution slot for {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to acquire execution slot for {agent_id}: {e}")
            return False
    
    def release_execution_slot(self, agent_id: str):
        """Release an execution slot for an agent"""
        try:
            semaphore = self.execution_semaphores.get(agent_id)
            if semaphore:
                semaphore.release()
                
            if agent_id in self.usage:
                self.usage[agent_id].concurrent_executions = max(0, 
                    self.usage[agent_id].concurrent_executions - 1)
                
            if agent_id in self.active_agents:
                self.active_agents.remove(agent_id)
                
            logger.info(f"🔓 Released execution slot for {agent_id}")
            
        except Exception as e:
            logger.error(f"Failed to release execution slot for {agent_id}: {e}")
    
    def record_ai_usage(self, agent_id: str, tokens_used: int = 0):
        """Record AI API usage for an agent"""
        if agent_id in self.usage:
            self.usage[agent_id].record_ai_request(tokens_used)
    
    def record_db_usage(self, agent_id: str):
        """Record database usage for an agent"""
        if agent_id in self.usage:
            self.usage[agent_id].record_db_query()
    
    def get_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """Get current status for an agent"""
        if agent_id not in self.quotas:
            return {"error": "Agent not found"}
        
        quota = self.quotas[agent_id]
        usage = self.usage[agent_id]
        usage.cleanup_old_requests()
        
        return {
            "agent_id": agent_id,
            "agent_type": quota.agent_type,
            "quota": {
                "max_concurrent": quota.max_concurrent_executions,
                "max_queue": quota.max_queue_size,
                "ai_requests_per_min": quota.ai_requests_per_minute,
                "ai_tokens_per_min": quota.ai_tokens_per_minute,
                "db_queries_per_min": quota.db_queries_per_minute,
                "max_memory_mb": quota.max_memory_mb,
                "max_cpu_percent": quota.max_cpu_percent,
                "timeout_seconds": quota.execution_timeout_seconds
            },
            "current_usage": {
                "concurrent_executions": usage.concurrent_executions,
                "queue_size": usage.queue_size,
                "ai_requests_last_min": len(usage.ai_requests_last_minute),
                "ai_tokens_used": usage.ai_tokens_last_minute,
                "db_queries_last_min": len(usage.db_queries_last_minute),
                "memory_usage_mb": round(usage.memory_usage_mb, 2),
                "cpu_usage_percent": round(usage.cpu_usage_percent, 2),
                "last_updated": usage.last_updated.isoformat()
            },
            "can_execute": asyncio.create_task(self.can_execute(agent_id)) if asyncio.get_event_loop().is_running() else True,
            "is_active": agent_id in self.active_agents
        }
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get system-wide resource overview"""
        total_agents = len(self.quotas)
        active_agents = len(self.active_agents)
        
        # Aggregate usage
        total_ai_requests = sum(len(usage.ai_requests_last_minute) for usage in self.usage.values())
        total_db_queries = sum(len(usage.db_queries_last_minute) for usage in self.usage.values())
        total_memory = sum(usage.memory_usage_mb for usage in self.usage.values())
        
        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "utilization_percent": (active_agents / max(total_agents, 1)) * 100,
            "aggregate_usage": {
                "ai_requests_last_min": total_ai_requests,
                "db_queries_last_min": total_db_queries,
                "total_memory_mb": round(total_memory, 2)
            },
            "agent_types": list(set(quota.agent_type for quota in self.quotas.values())),
            "timestamp": datetime.utcnow().isoformat()
        }

# Global instance
agent_resource_manager = AgentResourceManager()

def get_agent_resource_manager() -> AgentResourceManager:
    """Get the global agent resource manager"""
    return agent_resource_manager