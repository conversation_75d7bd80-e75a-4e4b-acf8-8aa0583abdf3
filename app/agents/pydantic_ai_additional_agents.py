### app/agents/pydantic_ai_additional_agents.py - Additional PydanticAI Agents

from typing import Dict, Any, List, Optional, cast
from pydantic import BaseModel, Field
import logging
import os

from pydantic_ai import Agent as TypedAgent, RunContext
from pydantic_ai.usage import Usage
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from app.utils.prompt import PromptTemplate

from .pydantic_ai_common import (
    AgentExecutionResult,
    create_error_result,
    create_success_result,
    with_error_handling,
    require_agents_available,
    with_execution_timing
)
from .pydantic_ai_tools import (
    analyze_category_trends,
    validate_book_concept
)
from .pydantic_ai_base import (
    TrendAnalysisDependencies,
    DatabaseDependencies,
    ScrapingDependencies,
    agent_registry
)

logger = logging.getLogger(__name__)

# =========================================================================
# Output Models
# =========================================================================

class ResearchResult(BaseModel):
    topic: str = Field(description="The research topic")
    key_findings: List[str] = Field(description="Key research findings")
    sources: List[str] = Field(description="Source citations")

class PersonalizationProfile(BaseModel):
    user_preferences: Dict[str, Any] = Field(description="User preferences and interests")
    content_recommendations: List[str] = Field(description="Personalized content recommendations")

class MultimodalContent(BaseModel):
    text_content: str = Field(description="Generated text content")
    image_descriptions: List[str] = Field(description="Descriptions for images to generate")

class ComprehensiveCoverDesign(BaseModel):
    design_concept: Dict[str, Any] = Field(description="The design concept and rationale")
    color_palette: Dict[str, Any] = Field(description="The color palette specification")
    typography: Dict[str, Any] = Field(description="The typography specifications")
    layout: Dict[str, Any] = Field(description="The layout and composition details")
    image_prompts: List[str] = Field(description="A list of prompts for generating images")

# =========================================================================
# Model Selection
# =========================================================================

model_for_context: Optional[Model] = None

def get_available_model() -> Optional[Model]:
    global model_for_context
    preferred = os.getenv("PREFERRED_MODEL", "openai").lower()
    if preferred == "openai" and os.getenv("OPENAI_API_KEY"):
        model_for_context = OpenAIModel("gpt-4")
    elif preferred == "anthropic" and os.getenv("ANTHROPIC_API_KEY"):
        model_for_context = AnthropicModel("claude-3-sonnet-20240229")
    return model_for_context

# =========================================================================
# Agent Initialization
# =========================================================================

research_assistant: Optional[TypedAgent[Any, ResearchResult]]
personalization_engine: Optional[TypedAgent[Any, PersonalizationProfile]]
multimodal_generator: Optional[TypedAgent[Any, MultimodalContent]]
cover_designer: Optional[TypedAgent[Any, ComprehensiveCoverDesign]]

get_available_model()

def init_agents():
    global research_assistant, personalization_engine, multimodal_generator, cover_designer
    if not model_for_context:
        logger.warning("No valid model found for agent initialization.")
        return

    research_assistant = TypedAgent(
        model=model_for_context,
        output_type=ResearchResult,
        system_prompt="You are an expert research assistant..."
    )
    personalization_engine = TypedAgent(
        model=model_for_context,
        output_type=PersonalizationProfile,
        system_prompt="You are a content personalization expert..."
    )
    multimodal_generator = TypedAgent(
        model=model_for_context,
        output_type=MultimodalContent,
        system_prompt="You are a multimodal content creator..."
    )
    cover_designer = TypedAgent(
        model=model_for_context,
        output_type=ComprehensiveCoverDesign,
        system_prompt="You are an expert cover designer..."
    )

init_agents()

# =========================================================================
# Agent Execution Functions
# =========================================================================

@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_research_assistant", logger=logger)
async def research_topic(
    topic: str,
    research_depth: str = "comprehensive",
    focus_areas: Optional[List[str]] = None,
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    if research_assistant is None:
        return create_error_result(
            agent_name="pydantic_ai_research_assistant",
            error="Research assistant agent is not initialized. Check API keys and model availability.",
            execution_time=0.0
        )

    prompt_template = PromptTemplate.from_string("""
        Research the topic: "{{ topic }}"
        Research depth: {{ research_depth }}
        Focus areas: {{ focus_areas }}
    """)

    prompt = prompt_template.format(
        topic=topic,
        research_depth=research_depth,
        focus_areas=focus_areas or []
    )

    result = await research_assistant.run(prompt)
    return create_success_result(
        agent_name="pydantic_ai_research_assistant",
        data=result.output.model_dump(),
        execution_time=getattr(getattr(result, "metrics", None), "total_time", 0.0),
        metadata={
            "topic": topic,
            "research_config": {
                "research_depth": research_depth,
                "focus_areas": focus_areas or []
            }
        }
    )


@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_personalization_engine", logger=logger)
async def personalize_content(
    content: str,
    user_profile: Dict[str, Any],
    target_audience: str = "general",
    personalization_level: str = "moderate",
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    if personalization_engine is None:
        return create_error_result(
            agent_name="pydantic_ai_personalization_engine",
            error="Personalization engine agent is not initialized. Check API keys and model availability.",
            execution_time=0.0
        )

    prompt_template = PromptTemplate.from_string("""
        Personalize the following content for the user profile:
        Content: {{ content }}
        User Profile: {{ user_profile }}
        Target Audience: {{ target_audience }}
        Personalization Level: {{ personalization_level }}
    """)

    prompt = prompt_template.format(
        content=content,
        user_profile=user_profile,
        target_audience=target_audience,
        personalization_level=personalization_level
    )

    result = await personalization_engine.run(prompt)
    return create_success_result(
        agent_name="pydantic_ai_personalization_engine",
        data=result.output.model_dump(),
        execution_time=getattr(getattr(result, "metrics", None), "total_time", 0.0),
        metadata={
            "user_profile": user_profile,
            "personalization_config": {
                "target_audience": target_audience,
                "personalization_level": personalization_level
            }
        }
    )


@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_multimodal_generator", logger=logger)
async def generate_multimodal_content(
    content_brief: str,
    modalities: Optional[List[str]] = None,
    style_guide: Optional[Dict[str, Any]] = None,
    target_format: str = "web",
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    if multimodal_generator is None:
        return create_error_result(
            agent_name="pydantic_ai_multimodal_generator",
            error="Multimodal generator agent is not initialized. Check API keys and model availability.",
            execution_time=0.0
        )

    prompt_template = PromptTemplate.from_string("""
        Generate multimodal content based on the following brief:
        Content Brief: {{ content_brief }}
        Modalities: {{ modalities }}
        Style Guide: {{ style_guide }}
        Target Format: {{ target_format }}
    """)

    prompt = prompt_template.format(
        content_brief=content_brief,
        modalities=modalities or [],
        style_guide=style_guide or {},
        target_format=target_format
    )

    result = await multimodal_generator.run(prompt)
    return create_success_result(
        agent_name="pydantic_ai_multimodal_generator",
        data=result.output.model_dump(),
        execution_time=getattr(getattr(result, "metrics", None), "total_time", 0.0),
        metadata={
            "content_brief": content_brief,
            "generation_config": {
                "modalities": modalities,
                "style_guide": style_guide or {},
                "target_format": target_format
            }
        }
    )


@require_agents_available
@with_execution_timing
@with_error_handling("pydantic_ai_cover_designer", logger=logger)
async def design_book_cover(
    book_title: str,
    author_name: str,
    genre: str,
    target_audience: str = "general adults",
    style_preferences: Optional[Dict[str, Any]] = None,
    market_data: Optional[Dict[str, Any]] = None,
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    if cover_designer is None:
        return create_error_result(
            agent_name="pydantic_ai_cover_designer",
            error="Cover designer agent is not initialized. Check API keys and model availability.",
            execution_time=0.0
        )

    trend_ctx = RunContext[TrendAnalysisDependencies](
        deps=TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(),
            scraping_deps=ScrapingDependencies()
        ),
        model=cast(Model, model_for_context),
        usage=Usage(),
        prompt=""
    )

    trend_insight = await validate_book_concept(
        ctx=trend_ctx,
        title=book_title,
        category=genre
    )

    prompt_template = PromptTemplate.from_string("""
        Design a book cover for the following book:
        Title: {{ book_title }}
        Author: {{ author_name }}
        Genre: {{ genre }}
        Target Audience: {{ target_audience }}
        Style Preferences: {{ style_preferences }}
        Market Data: {{ market_data }}
    """)

    prompt = prompt_template.format(
        book_title=book_title,
        author_name=author_name,
        genre=genre,
        target_audience=target_audience,
        style_preferences=style_preferences or {},
        market_data=market_data or trend_insight.model_dump()
    )

    result = await cover_designer.run(prompt)
    return create_success_result(
        agent_name="pydantic_ai_cover_designer",
        data=result.output.model_dump(),
        execution_time=getattr(getattr(result, "metrics", None), "total_time", 0.0),
        metadata={
            "book_title": book_title,
            "author_name": author_name,
            "genre": genre,
            "design_config": {
                "target_audience": target_audience,
                "style_preferences": style_preferences or {},
                "market_data": market_data or trend_insight.model_dump()
            }
        }
    )

# Register agents with the registry
agent_registry.register_agent("research_assistant", research_topic)
agent_registry.register_agent("pydantic_ai_research_assistant", research_topic)
agent_registry.register_agent("personalization_engine", personalize_content)
agent_registry.register_agent("pydantic_ai_personalization_engine", personalize_content)
agent_registry.register_agent("multimodal_generator", generate_multimodal_content)
agent_registry.register_agent("pydantic_ai_multimodal_generator", generate_multimodal_content)
