# app/monitoring/monitoring_setup.py - Comprehensive Monitoring Setup with Performance Tweaks

import structlog
import logging
import uuid
import random
import asyncio
from typing import Callable, List, Mapping, MutableMapping, Optional, Dict, Any
from contextlib import contextmanager

from app.utils.logflare_client import LogflareClient
from app.config import settings
from app.monitoring.sentry_singleton import init_sentry, capture_exception as sentry_capture_exception, is_sentry_available

ProcessorType = Callable[
    [Any, str, MutableMapping[str, Any]],
    Mapping[str, Any] | str | bytes | bytearray | tuple[Any, ...],
]


class MonitoringManager:
    def __init__(self):
        self.sentry_initialized = False
        self.logflare_initialized = False
        self.structured_logger = None
        self.request_id = None

    def initialize_sentry(self) -> bool:
        """Initialize Sentry using the efficient singleton."""
        try:
            self.sentry_initialized = init_sentry(
                traces_sample_rate=getattr(settings, 'sentry_traces_sample_rate', 0.1),
                environment=getattr(settings, 'sentry_environment', 'production'),
                release=getattr(settings, 'sentry_release', None),
                sensitive_keys=[
                    "kdp_password", "logflare_api_key"  # App-specific sensitive keys
                ]
            )
            return self.sentry_initialized
        except Exception as e:
            logging.error(f"Failed to initialize Sentry: {e}")
            return False

    async def initialize_logflare(self) -> bool:
        try:
            processors: List[ProcessorType] = [
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                self._add_context_processor,
                structlog.processors.JSONRenderer(),
            ]
            structlog.configure(
                processors=processors,
                wrapper_class=structlog.stdlib.BoundLogger,
                logger_factory=structlog.stdlib.LoggerFactory(),
                cache_logger_on_first_use=True,
            )
            self.logflare_client = LogflareClient()
            await self.logflare_client.initialize()
            self.structured_logger = structlog.get_logger()
            self.logflare_initialized = True
            logging.info("Logflare initialized")
            return True
        except Exception as e:
            logging.error(f"Failed to initialize Logflare: {e}")
            return False

    def _add_context_processor(
        self,
        logger: Any,
        method_name: str,
        event_dict: MutableMapping[str, Any],
    ) -> Mapping[str, Any]:
        event_dict["service"] = "publish-ai"
        event_dict["version"] = settings.sentry_release or "unknown"
        event_dict["environment"] = settings.sentry_environment
        if self.request_id:
            event_dict["request_id"] = self.request_id
        return event_dict

    def set_request_context(
        self,
        request_id: str,
        user_id: Optional[str] = None,
        additional: Optional[Dict[str, Any]] = None,
    ):
        self.request_id = request_id
        if self.sentry_initialized:
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("request_id", request_id)
                if user_id:
                    scope.set_user({"id": user_id})
                if additional:
                    for k, v in additional.items():
                        scope.set_extra(k, v)

    def clear_request_context(self):
        self.request_id = None
        if self.sentry_initialized:
            sentry_sdk.configure_scope()

    @contextmanager
    def monitor_operation(self, operation_name: str, **context):
        operation_id = str(uuid.uuid4())
        try:
            if self.structured_logger:
                self.structured_logger.info(
                    "Operation started",
                    operation_id=operation_id,
                    operation_name=operation_name,
                    **context,
                )
            if self.sentry_initialized:
                with sentry_sdk.configure_scope() as scope:
                    scope.set_tag("operation", operation_name)
                    scope.set_extra("operation_id", operation_id)
            yield operation_id
            if self.structured_logger:
                self.structured_logger.info(
                    "Operation completed",
                    operation_id=operation_id,
                    operation_name=operation_name,
                )
        except Exception as e:
            if self.structured_logger:
                self.structured_logger.error(
                    "Operation failed",
                    operation_id=operation_id,
                    operation_name=operation_name,
                    error=str(e),
                    error_type=type(e).__name__,
                )
            capture_exception(e)
            raise

    def _truncate(self, data: Dict[str, Any], max_length=500):
        for k, v in data.items():
            if isinstance(v, str) and len(v) > max_length:
                data[k] = v[:max_length] + "...[TRUNCATED]"
            elif isinstance(v, dict):
                self._truncate(v, max_length)
            elif isinstance(v, list) and len(v) > 10:
                data[k] = v[:10] + ["...[TRUNCATED LIST]"]

    def _sample(self, rate: float) -> bool:
        return random.random() < rate

    async def _logflare_background(self, func, *args, **kwargs):
        try:
            await func(*args, **kwargs)
        except Exception as e:
            logging.warning(f"Logflare call failed: {e}")

    def log_to_logflare_background(self, event: Dict[str, Any], sample_rate=1.0):
        if self.logflare_initialized and self._sample(sample_rate):
            self._truncate(event)
            # Extract event type for event name, default to generic if not present
            event_name = event.get("event_type", "application_event")
            asyncio.create_task(
                self._logflare_background(self.logflare_client.send_event, event_name, **event)
            )

    def log_event_to_logflare_background(
        self, event_type: str, event: Dict[str, Any], sample_rate=1.0
    ):
        if self.logflare_initialized and self._sample(sample_rate):
            self._truncate(event)
            asyncio.create_task(
                self._logflare_background(
                    self.logflare_client.send_event, event_type, **event
                )
            )

    def log_user_action(
        self, action: str, user_id: str, details: Optional[Dict[str, Any]] = None
    ):
        data = {"event_type": "user_action", "action": action, "user_id": user_id}
        if details:
            data.update(details)
        if self.structured_logger:
            self.structured_logger.info("User action", **data)
        self.log_event_to_logflare_background("user_action", data, sample_rate=0.5)

    def log_system_event(self, event_type: str, details: Dict[str, Any]):
        data = {
            "event_type": "system_event",
            "system_event_type": event_type,
            **details,
        }
        if self.structured_logger:
            self.structured_logger.info("System event", **data)
        self.log_event_to_logflare_background("system_event", data, sample_rate=0.5)

    def log_performance_metric(
        self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None
    ):
        data = {
            "event_type": "performance_metric",
            "metric_name": metric_name,
            "metric_value": value,
        }
        if tags:
            data["tags"] = tags
        if self.structured_logger:
            self.structured_logger.info("Performance metric", **data)
        self.log_event_to_logflare_background(
            "performance_metric", data, sample_rate=0.1
        )

    def capture_exception(self, e: Exception, extra: Optional[Dict[str, Any]] = None):
        # Use the singleton for Sentry capture  
        sentry_capture_exception(e, extra=extra or {})
        if self.structured_logger:
            data = {
                "event_type": "exception",
                "error": str(e),
                "error_type": type(e).__name__,
            }
            if extra:
                data.update(extra)
            self.structured_logger.error("Exception captured", **data)
        self.log_event_to_logflare_background("exception", data, sample_rate=1.0)

    def get_logger(self, name: str):
        return (
            self.structured_logger.bind(logger_name=name)
            if self.structured_logger
            else logging.getLogger(name)
        )


monitoring_manager = MonitoringManager()


async def initialize_monitoring() -> Dict[str, bool]:
    return {
        "sentry": monitoring_manager.initialize_sentry(),
        "logflare": await monitoring_manager.initialize_logflare(),
    }


def get_logger(name: str):
    return monitoring_manager.get_logger(name)


def monitor_operation(operation_name: str, **context):
    return monitoring_manager.monitor_operation(operation_name, **context)


def log_user_action(
    action: str, user_id: str, details: Optional[Dict[str, Any]] = None
):
    monitoring_manager.log_user_action(action, user_id, details)


def log_system_event(event_type: str, details: Dict[str, Any]):
    monitoring_manager.log_system_event(event_type, details)


def capture_exception(
    exception: Exception, extra_context: Optional[Dict[str, Any]] = None
):
    monitoring_manager.capture_exception(exception, extra_context)


def set_request_context(
    request_id: str,
    user_id: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None,
):
    monitoring_manager.set_request_context(request_id, user_id, additional_context)


def clear_request_context():
    monitoring_manager.clear_request_context()
