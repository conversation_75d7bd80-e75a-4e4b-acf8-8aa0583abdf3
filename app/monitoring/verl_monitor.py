### app/monitoring/verl_monitor.py - VERL Monitor (Supabase Version)

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
from dataclasses import dataclass, asdict
import threading
import time

from app.utils.supabase.supabase_client import supabase_client
from app.ml.data_access import VERLDataAccessor

logger = logging.getLogger(__name__)

@dataclass
class DataFlowMetrics:
    """Real-time data flow metrics"""
    timestamp: datetime
    new_feedback_count: int
    approval_rate: float
    quality_average: float
    training_readiness: float
    signal_strength: float

@dataclass
class PerformanceTrend:
    """Performance trend data"""
    period: str
    metric_name: str
    current_value: float
    previous_value: float
    change_percent: float
    trend_direction: str  # 'up', 'down', 'stable'

class VERLMonitor:
    """Real-time monitoring for VERL training system"""
    
    def __init__(self):
        self.is_running = False
        self.data_accessor = VERLDataAccessor()
        
        # Monitoring intervals
        self.metrics_interval = 30  # seconds
        self.trend_interval = 300   # 5 minutes
        
        # Recent data buffer (last 24 hours)
        self.metrics_buffer = deque(maxlen=2880)  # 30-second intervals for 24 hours
        
        # Real-time counters
        self.live_counters = {
            'approvals_today': 0,
            'rejections_today': 0,
            'books_generated_today': 0,
            'training_sessions_today': 0,
            'data_points_collected': 0
        }
        
        # Performance tracking
        self.performance_trends = {
            'approval_rate': deque(maxlen=288),  # 5-min intervals for 24 hours
            'quality_scores': deque(maxlen=288),
            'training_effectiveness': deque(maxlen=288),
            'data_quality': deque(maxlen=288)
        }
        
        # Alert thresholds
        self.alert_thresholds = {
            'approval_rate_min': 0.30,
            'quality_score_min': 0.60,
            'data_quality_min': 0.70,
            'training_interval_max': 24  # hours
        }
        
        # Background tasks
        self._tasks = []
        self._lock = threading.Lock()
    
    async def start_monitoring(self):
        """Start all monitoring tasks"""
        
        if self.is_running:
            logger.warning("VERL Monitor already running")
            return
        
        self.is_running = True
        logger.info("Starting VERL Monitor...")
        
        # Start background tasks
        self._tasks = [
            asyncio.create_task(self._metrics_collection_loop()),
            asyncio.create_task(self._trend_analysis_loop()),
            asyncio.create_task(self._live_counter_update_loop()),
            asyncio.create_task(self._alert_monitoring_loop())
        ]
        
        logger.info("VERL Monitor started successfully")
    
    async def stop_monitoring(self):
        """Stop all monitoring tasks"""
        
        self.is_running = False
        
        # Cancel all tasks
        for task in self._tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self._tasks, return_exceptions=True)
        
        self._tasks.clear()
        logger.info("VERL Monitor stopped")
    
    async def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        
        latest_metrics = self.metrics_buffer[-1] if self.metrics_buffer else None
        
        if latest_metrics:
            return {
                'status': 'active',
                'last_update': latest_metrics.timestamp.isoformat(),
                'approval_rate': latest_metrics.approval_rate,
                'quality_average': latest_metrics.quality_average,
                'training_readiness': latest_metrics.training_readiness,
                'signal_strength': latest_metrics.signal_strength,
                'data_points_last_24h': len(self.metrics_buffer),
                'live_counters': self.live_counters.copy(),
                'alerts_active': await self._check_active_alerts()
            }
        else:
            return {
                'status': 'initializing',
                'message': 'Collecting initial metrics...'
            }
    
    async def get_performance_trends(self, hours: int = 24) -> Dict[str, List[PerformanceTrend]]:
        """Get performance trends for specified timeframe"""
        
        trends = {}
        
        # Calculate trends for each metric
        for metric_name, values in self.performance_trends.items():
            if len(values) >= 2:
                recent_avg = sum(list(values)[-12:]) / min(12, len(values))  # Last hour
                previous_avg = sum(list(values)[-24:-12]) / min(12, len(values))  # Previous hour
                
                if previous_avg > 0:
                    change_percent = ((recent_avg - previous_avg) / previous_avg) * 100
                else:
                    change_percent = 0
                
                trend_direction = 'up' if change_percent > 5 else 'down' if change_percent < -5 else 'stable'
                
                trends[metric_name] = PerformanceTrend(
                    period='last_hour',
                    metric_name=metric_name,
                    current_value=recent_avg,
                    previous_value=previous_avg,
                    change_percent=change_percent,
                    trend_direction=trend_direction
                )
        
        return trends
    
    async def _metrics_collection_loop(self):
        """Main metrics collection loop"""
        
        while self.is_running:
            try:
                metrics = await self._collect_current_metrics()
                
                with self._lock:
                    self.metrics_buffer.append(metrics)
                
                logger.debug(f"Collected metrics: approval_rate={metrics.approval_rate:.2f}")
                
            except Exception as e:
                logger.error(f"Error collecting metrics: {str(e)}")
            
            await asyncio.sleep(self.metrics_interval)
    
    async def _collect_current_metrics(self) -> DataFlowMetrics:
        """Collect current metrics from Supabase"""
        
        try:
            # Get recent feedback data (last hour)
            cutoff = datetime.now() - timedelta(hours=1)
            cutoff_iso = cutoff.isoformat()
            
            # Get feedback metrics
            feedback_result = await supabase_client.client.table("feedback_metrics").select("*").gte("created_at", cutoff_iso).execute()
            
            feedback_data = feedback_result.data or []
            
            # Calculate metrics
            new_feedback_count = len(feedback_data)
            
            if feedback_data:
                approvals = [f for f in feedback_data if f.get('approved', False)]
                approval_rate = len(approvals) / len(feedback_data)
                quality_average = sum(f.get('metric_value', 0) for f in feedback_data) / len(feedback_data)
            else:
                approval_rate = 0.0
                quality_average = 0.0
            
            # Calculate training readiness
            training_readiness = await self._calculate_training_readiness()
            
            # Calculate signal strength
            signal_strength = await self._calculate_signal_strength(feedback_data)
            
            return DataFlowMetrics(
                timestamp=datetime.now(),
                new_feedback_count=new_feedback_count,
                approval_rate=approval_rate,
                quality_average=quality_average,
                training_readiness=training_readiness,
                signal_strength=signal_strength
            )
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return DataFlowMetrics(
                timestamp=datetime.now(),
                new_feedback_count=0,
                approval_rate=0.0,
                quality_average=0.0,
                training_readiness=0.0,
                signal_strength=0.0
            )
    
    async def _calculate_training_readiness(self) -> float:
        """Calculate if system is ready for training"""
        
        try:
            # Get total feedback count
            total_result = await supabase_client.client.table("feedback_metrics").select("id", count="exact").execute()
            total_count = total_result.count or 0
            
            # Get recent activity (last 7 days)
            cutoff = datetime.now() - timedelta(days=7)
            recent_result = await supabase_client.client.table("feedback_metrics").select("id", count="exact").gte("created_at", cutoff.isoformat()).execute()
            recent_count = recent_result.count or 0
            
            # Minimum requirements
            min_total = 100
            min_recent = 20
            
            total_readiness = min(total_count / min_total, 1.0)
            recent_readiness = min(recent_count / min_recent, 1.0)
            
            return (total_readiness + recent_readiness) / 2
            
        except Exception as e:
            logger.error(f"Error calculating training readiness: {e}")
            return 0.0
    
    async def _calculate_signal_strength(self, feedback_data: List[Dict]) -> float:
        """Calculate strength of reward signals"""
        
        if not feedback_data:
            return 0.0
        
        try:
            # Calculate signal clarity
            clear_signals = 0
            total_signals = 0
            
            for feedback in feedback_data:
                reward_signal = feedback.get('reward_signal', 0)
                if reward_signal is not None:
                    total_signals += 1
                    if abs(reward_signal) > 0.3:  # Clear positive/negative signal
                        clear_signals += 1
            
            if total_signals > 0:
                return clear_signals / total_signals
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 0.0
    
    async def _trend_analysis_loop(self):
        """Analyze trends and update trend buffers"""
        
        while self.is_running:
            try:
                await self._update_trend_buffers()
                
            except Exception as e:
                logger.error(f"Error in trend analysis: {str(e)}")
            
            await asyncio.sleep(self.trend_interval)
    
    async def _update_trend_buffers(self):
        """Update performance trend buffers"""
        
        if len(self.metrics_buffer) < 10:  # Need some data first
            return
        
        with self._lock:
            recent_metrics = list(self.metrics_buffer)[-10:]  # Last 10 metrics
        
        # Calculate averages for trends
        approval_avg = sum(m.approval_rate for m in recent_metrics) / len(recent_metrics)
        quality_avg = sum(m.quality_average for m in recent_metrics) / len(recent_metrics)
        training_avg = sum(m.training_readiness for m in recent_metrics) / len(recent_metrics)
        signal_avg = sum(m.signal_strength for m in recent_metrics) / len(recent_metrics)
        
        # Update trend buffers
        self.performance_trends['approval_rate'].append(approval_avg)
        self.performance_trends['quality_scores'].append(quality_avg)
        self.performance_trends['training_effectiveness'].append(training_avg)
        self.performance_trends['data_quality'].append(signal_avg)
    
    async def _live_counter_update_loop(self):
        """Update live counters"""
        
        while self.is_running:
            try:
                await self._update_live_counters()
                
            except Exception as e:
                logger.error(f"Error updating live counters: {str(e)}")
            
            await asyncio.sleep(60)  # Update every minute
    
    async def _update_live_counters(self):
        """Update live counters for today"""
        
        try:
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time()).isoformat()
            
            # Count today's activities using Supabase
            books_result = await supabase_client.client.table("books").select("id", count="exact").gte("created_at", today_start).execute()
            self.live_counters['books_generated_today'] = books_result.count or 0
            
            # Count approvals/rejections today
            approvals_result = await supabase_client.client.table("feedback_metrics").select("id", count="exact").gte("created_at", today_start).eq("approved", True).execute()
            self.live_counters['approvals_today'] = approvals_result.count or 0
            
            rejections_result = await supabase_client.client.table("feedback_metrics").select("id", count="exact").gte("created_at", today_start).eq("approved", False).execute()
            self.live_counters['rejections_today'] = rejections_result.count or 0
            
            # Training sessions today
            training_result = await supabase_client.client.table("verl_training_jobs").select("id", count="exact").gte("created_at", today_start).execute()
            self.live_counters['training_sessions_today'] = training_result.count or 0
            
            # Total data points collected
            feedback_result = await supabase_client.client.table("feedback_metrics").select("id", count="exact").execute()
            self.live_counters['data_points_collected'] = feedback_result.count or 0
            
        except Exception as e:
            logger.error(f"Error updating live counters: {e}")
    
    async def _alert_monitoring_loop(self):
        """Monitor for alert conditions"""
        
        while self.is_running:
            try:
                alerts = await self._check_active_alerts()
                
                for alert in alerts:
                    logger.warning(f"VERL Alert: {alert['message']}")
                
            except Exception as e:
                logger.error(f"Error in alert monitoring: {str(e)}")
            
            await asyncio.sleep(120)  # Check every 2 minutes
    
    async def _check_active_alerts(self) -> List[Dict[str, Any]]:
        """Check for active alert conditions"""
        
        alerts = []
        
        if not self.metrics_buffer:
            return alerts
        
        latest_metrics = self.metrics_buffer[-1]
        
        # Low approval rate alert
        if latest_metrics.approval_rate < self.alert_thresholds['approval_rate_min']:
            alerts.append({
                'type': 'low_approval_rate',
                'severity': 'warning',
                'message': f"Approval rate is {latest_metrics.approval_rate:.1%} (below {self.alert_thresholds['approval_rate_min']:.1%})",
                'timestamp': latest_metrics.timestamp
            })
        
        # Low quality score alert
        if latest_metrics.quality_average < self.alert_thresholds['quality_score_min']:
            alerts.append({
                'type': 'low_quality',
                'severity': 'warning',
                'message': f"Quality score is {latest_metrics.quality_average:.2f} (below {self.alert_thresholds['quality_score_min']:.2f})",
                'timestamp': latest_metrics.timestamp
            })
        
        # Training readiness alert
        if latest_metrics.training_readiness < 0.5:
            alerts.append({
                'type': 'training_not_ready',
                'severity': 'info',
                'message': f"Training readiness is {latest_metrics.training_readiness:.1%}",
                'timestamp': latest_metrics.timestamp
            })
        
        return alerts
    
    def get_debug_info(self) -> Dict[str, Any]:
        """Get debug information"""
        
        return {
            'is_running': self.is_running,
            'metrics_buffer_size': len(self.metrics_buffer),
            'task_count': len(self._tasks),
            'performance_trends_size': {k: len(v) for k, v in self.performance_trends.items()},
            'alert_thresholds': self.alert_thresholds,
            'intervals': {
                'metrics': self.metrics_interval,
                'trends': self.trend_interval
            }
        }

# Global monitor instance
verl_monitor = VERLMonitor()

# FastAPI dependency
async def get_verl_monitor() -> VERLMonitor:
    """FastAPI dependency to get VERL monitor"""
    return verl_monitor

# Initialize monitor on startup
async def initialize_verl_monitor():
    """Initialize VERL monitor"""
    try:
        await verl_monitor.start_monitoring()
        logger.info("VERL Monitor initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize VERL monitor: {e}")
        raise

# Cleanup on shutdown
async def cleanup_verl_monitor():
    """Cleanup VERL monitor"""
    try:
        await verl_monitor.stop_monitoring()
        logger.info("VERL Monitor cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during VERL monitor cleanup: {e}")
