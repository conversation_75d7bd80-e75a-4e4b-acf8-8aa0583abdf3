# app/monitoring/alerting_rules.py - Alerting Rules for Critical System Metrics

import asyncio
import time
import json
import httpx
from typing import Dict, Any, Optional, List, Callable, Union, Set
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception
from app.monitoring.sla_monitoring import sla_tracker
from app.monitoring.health_checks import health_manager
from app.middleware.business_metrics import metrics_collector

logger = get_logger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertCategory(Enum):
    """Alert categories"""
    PERFORMANCE = "performance"
    AVAILABILITY = "availability"
    SECURITY = "security"
    BUSINESS = "business"
    RESOURCE = "resource"
    DATA_QUALITY = "data_quality"

class AlertChannel(Enum):
    """Alert notification channels"""
    LOG = "log"
    EMAIL = "email"
    WEBHOOK = "webhook"
    SLACK = "slack"
    PAGERDUTY = "pagerduty"
    SMS = "sms"

@dataclass
class AlertRule:
    """Alert rule definition"""
    id: str
    name: str
    description: str
    category: AlertCategory
    severity: AlertSeverity
    condition: str  # Expression to evaluate
    threshold: float
    comparison: str  # "gt", "lt", "eq", "gte", "lte"
    duration_seconds: int  # How long condition must be true
    channels: List[AlertChannel]
    metadata: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    cooldown_seconds: int = 300  # 5 minutes default cooldown
    
@dataclass
class Alert:
    """Active alert instance"""
    rule_id: str
    rule_name: str
    category: AlertCategory
    severity: AlertSeverity
    message: str
    details: Dict[str, Any]
    triggered_at: float
    resolved_at: Optional[float] = None
    notification_sent: bool = False
    notification_channels: List[AlertChannel] = field(default_factory=list)
    
    def is_active(self) -> bool:
        """Check if alert is still active"""
        return self.resolved_at is None

class AlertConditionEvaluator:
    """Evaluates alert conditions"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._metric_cache: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
    
    async def evaluate_condition(self, rule: AlertRule, metrics: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Evaluate if alert condition is met"""
        try:
            # Extract metric value based on condition
            metric_value = self._extract_metric_value(rule.condition, metrics)
            
            if metric_value is None:
                return False, {'reason': 'Metric not available'}
            
            # Store metric for duration checking
            self._metric_cache[rule.id].append({
                'value': metric_value,
                'timestamp': time.time()
            })
            
            # Check if threshold is exceeded
            threshold_exceeded = self._compare_values(metric_value, rule.threshold, rule.comparison)
            
            if not threshold_exceeded:
                return False, {'reason': 'Threshold not exceeded', 'value': metric_value}
            
            # Check duration requirement
            duration_met = self._check_duration_requirement(rule)
            
            if not duration_met:
                return False, {
                    'reason': 'Duration requirement not met',
                    'value': metric_value,
                    'threshold': rule.threshold
                }
            
            return True, {
                'metric_value': metric_value,
                'threshold': rule.threshold,
                'duration_seconds': rule.duration_seconds
            }
            
        except Exception as e:
            self.logger.error(f"Error evaluating condition for rule {rule.id}: {e}")
            return False, {'error': str(e)}
    
    def _extract_metric_value(self, condition: str, metrics: Dict[str, Any]) -> Optional[float]:
        """Extract metric value from condition expression"""
        # Simple metric extraction - in production, use a proper expression parser
        # Format: "metric.path.to.value"
        
        try:
            parts = condition.split('.')
            value = metrics
            
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return None
            
            return float(value) if value is not None else None
            
        except (KeyError, TypeError, ValueError):
            return None
    
    def _compare_values(self, value: float, threshold: float, comparison: str) -> bool:
        """Compare value against threshold"""
        comparisons = {
            'gt': value > threshold,
            'lt': value < threshold,
            'eq': value == threshold,
            'gte': value >= threshold,
            'lte': value <= threshold
        }
        
        return comparisons.get(comparison, False)
    
    def _check_duration_requirement(self, rule: AlertRule) -> bool:
        """Check if condition has been true for required duration"""
        if rule.duration_seconds == 0:
            return True  # No duration requirement
        
        metric_history = self._metric_cache.get(rule.id, [])
        if not metric_history:
            return False
        
        cutoff_time = time.time() - rule.duration_seconds
        
        # Check if all recent values exceed threshold
        for entry in metric_history:
            if entry['timestamp'] < cutoff_time:
                continue
                
            value = entry['value']
            if not self._compare_values(value, rule.threshold, rule.comparison):
                return False
        
        return True

class NotificationManager:
    """Manages alert notifications"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._notification_history: Dict[str, float] = {}  # Track last notification time
    
    async def send_notification(self, alert: Alert, rule: AlertRule) -> bool:
        """Send alert notification through configured channels"""
        success = True
        
        for channel in rule.channels:
            try:
                if channel == AlertChannel.LOG:
                    self._send_log_notification(alert)
                elif channel == AlertChannel.EMAIL:
                    await self._send_email_notification(alert, rule)
                elif channel == AlertChannel.WEBHOOK:
                    await self._send_webhook_notification(alert, rule)
                elif channel == AlertChannel.SLACK:
                    await self._send_slack_notification(alert, rule)
                else:
                    self.logger.warning(f"Unsupported notification channel: {channel}")
                
                alert.notification_channels.append(channel)
                
            except Exception as e:
                self.logger.error(f"Failed to send {channel.value} notification: {e}")
                success = False
        
        alert.notification_sent = success
        self._notification_history[alert.rule_id] = time.time()
        
        return success
    
    def _send_log_notification(self, alert: Alert) -> None:
        """Send alert to logs"""
        log_levels = {
            AlertSeverity.INFO: self.logger.info,
            AlertSeverity.WARNING: self.logger.warning,
            AlertSeverity.ERROR: self.logger.error,
            AlertSeverity.CRITICAL: self.logger.critical
        }
        
        log_func = log_levels.get(alert.severity, self.logger.error)
        log_func(
            f"ALERT [{alert.severity.value.upper()}] {alert.rule_name}: {alert.message}",
            extra={'alert_details': asdict(alert)}
        )
    
    async def _send_email_notification(self, alert: Alert, rule: AlertRule) -> None:
        """Send email notification"""
        if not hasattr(settings, 'alert_email_recipients'):
            self.logger.warning("Email recipients not configured")
            return
        
        # Email configuration
        smtp_host = getattr(settings, 'smtp_host', 'localhost')
        smtp_port = getattr(settings, 'smtp_port', 587)
        smtp_user = getattr(settings, 'smtp_user', '')
        smtp_password = getattr(settings, 'smtp_password', '')
        from_email = getattr(settings, 'alert_from_email', '<EMAIL>')
        
        # Create email
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.rule_name}"
        msg['From'] = from_email
        msg['To'] = ', '.join(settings.alert_email_recipients)
        
        # Email body
        text_content = f"""
Alert: {alert.rule_name}
Severity: {alert.severity.value.upper()}
Category: {alert.category.value}
Time: {datetime.fromtimestamp(alert.triggered_at).strftime('%Y-%m-%d %H:%M:%S')}

Message: {alert.message}

Details:
{json.dumps(alert.details, indent=2)}

This is an automated alert from the Publish AI system.
"""
        
        html_content = f"""
<html>
<body>
<h2 style="color: {'red' if alert.severity == AlertSeverity.CRITICAL else 'orange'};">
    Alert: {alert.rule_name}
</h2>
<p><strong>Severity:</strong> {alert.severity.value.upper()}</p>
<p><strong>Category:</strong> {alert.category.value}</p>
<p><strong>Time:</strong> {datetime.fromtimestamp(alert.triggered_at).strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Message:</strong> {alert.message}</p>
<h3>Details:</h3>
<pre>{json.dumps(alert.details, indent=2)}</pre>
<hr>
<p><em>This is an automated alert from the Publish AI system.</em></p>
</body>
</html>
"""
        
        msg.attach(MIMEText(text_content, 'plain'))
        msg.attach(MIMEText(html_content, 'html'))
        
        # Send email
        try:
            with smtplib.SMTP(smtp_host, smtp_port) as server:
                if smtp_user and smtp_password:
                    server.starttls()
                    server.login(smtp_user, smtp_password)
                server.send_message(msg)
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
            raise
    
    async def _send_webhook_notification(self, alert: Alert, rule: AlertRule) -> None:
        """Send webhook notification"""
        webhook_url = rule.metadata.get('webhook_url')
        if not webhook_url:
            self.logger.warning("Webhook URL not configured for rule")
            return
        
        payload = {
            'alert': asdict(alert),
            'rule': {
                'id': rule.id,
                'name': rule.name,
                'category': rule.category.value,
                'severity': rule.severity.value
            },
            'timestamp': time.time()
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                webhook_url,
                json=payload,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code >= 400:
                raise Exception(f"Webhook returned {response.status_code}: {response.text}")
    
    async def _send_slack_notification(self, alert: Alert, rule: AlertRule) -> None:
        """Send Slack notification"""
        slack_webhook = rule.metadata.get('slack_webhook')
        if not slack_webhook:
            self.logger.warning("Slack webhook not configured for rule")
            return
        
        # Slack message formatting
        color_map = {
            AlertSeverity.INFO: "#36a64f",
            AlertSeverity.WARNING: "#ff9900",
            AlertSeverity.ERROR: "#ff0000",
            AlertSeverity.CRITICAL: "#990000"
        }
        
        slack_message = {
            "attachments": [{
                "color": color_map.get(alert.severity, "#ff0000"),
                "title": f"{alert.rule_name}",
                "fields": [
                    {"title": "Severity", "value": alert.severity.value.upper(), "short": True},
                    {"title": "Category", "value": alert.category.value, "short": True},
                    {"title": "Message", "value": alert.message, "short": False},
                    {"title": "Time", "value": datetime.fromtimestamp(alert.triggered_at).strftime('%Y-%m-%d %H:%M:%S'), "short": True}
                ],
                "footer": "Publish AI Alert System",
                "ts": int(alert.triggered_at)
            }]
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(slack_webhook, json=slack_message)
            
            if response.status_code != 200:
                raise Exception(f"Slack webhook failed: {response.text}")
    
    def should_send_notification(self, rule: AlertRule) -> bool:
        """Check if notification should be sent (respecting cooldown)"""
        last_notification = self._notification_history.get(rule.id, 0)
        time_since_last = time.time() - last_notification
        
        return time_since_last >= rule.cooldown_seconds

class AlertingEngine:
    """Main alerting engine"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.evaluator = AlertConditionEvaluator()
        self.notifier = NotificationManager()
        
        # Alert storage
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.max_history_size = 1000
        
        # Initialize default rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default alerting rules"""
        default_rules = [
            # Performance alerts
            AlertRule(
                id="high_api_response_time",
                name="High API Response Time",
                description="API response time exceeds 1 second",
                category=AlertCategory.PERFORMANCE,
                severity=AlertSeverity.WARNING,
                condition="api.response_time",
                threshold=1000,  # 1 second
                comparison="gt",
                duration_seconds=300,  # 5 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            AlertRule(
                id="critical_api_response_time",
                name="Critical API Response Time",
                description="API response time exceeds 5 seconds",
                category=AlertCategory.PERFORMANCE,
                severity=AlertSeverity.CRITICAL,
                condition="api.response_time",
                threshold=5000,  # 5 seconds
                comparison="gt",
                duration_seconds=60,  # 1 minute
                channels=[AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.SLACK]
            ),
            
            # Availability alerts
            AlertRule(
                id="service_unavailable",
                name="Service Unavailable",
                description="Critical service is unavailable",
                category=AlertCategory.AVAILABILITY,
                severity=AlertSeverity.CRITICAL,
                condition="health.status",
                threshold=0,  # 0 = unhealthy
                comparison="eq",
                duration_seconds=30,
                channels=[AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.SLACK]
            ),
            AlertRule(
                id="database_connection_failure",
                name="Database Connection Failure",
                description="Cannot connect to database",
                category=AlertCategory.AVAILABILITY,
                severity=AlertSeverity.CRITICAL,
                condition="database.healthy",
                threshold=0,  # 0 = unhealthy
                comparison="eq",
                duration_seconds=10,
                channels=[AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.SLACK]
            ),
            
            # Resource alerts
            AlertRule(
                id="high_cpu_usage",
                name="High CPU Usage",
                description="CPU usage exceeds 90%",
                category=AlertCategory.RESOURCE,
                severity=AlertSeverity.WARNING,
                condition="system.cpu_usage",
                threshold=90,
                comparison="gt",
                duration_seconds=300,  # 5 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            AlertRule(
                id="high_memory_usage",
                name="High Memory Usage",
                description="Memory usage exceeds 90%",
                category=AlertCategory.RESOURCE,
                severity=AlertSeverity.WARNING,
                condition="system.memory_usage",
                threshold=90,
                comparison="gt",
                duration_seconds=300,  # 5 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            AlertRule(
                id="low_disk_space",
                name="Low Disk Space",
                description="Disk usage exceeds 90%",
                category=AlertCategory.RESOURCE,
                severity=AlertSeverity.ERROR,
                condition="system.disk_usage",
                threshold=90,
                comparison="gt",
                duration_seconds=600,  # 10 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            
            # Business alerts
            AlertRule(
                id="high_error_rate",
                name="High Error Rate",
                description="API error rate exceeds 5%",
                category=AlertCategory.BUSINESS,
                severity=AlertSeverity.ERROR,
                condition="api.error_rate",
                threshold=5,
                comparison="gt",
                duration_seconds=300,  # 5 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            AlertRule(
                id="low_ai_success_rate",
                name="Low AI Generation Success Rate",
                description="AI generation success rate below 80%",
                category=AlertCategory.BUSINESS,
                severity=AlertSeverity.WARNING,
                condition="ai.success_rate",
                threshold=80,
                comparison="lt",
                duration_seconds=600,  # 10 minutes
                channels=[AlertChannel.LOG, AlertChannel.EMAIL]
            ),
            AlertRule(
                id="task_queue_backlog",
                name="Task Queue Backlog",
                description="More than 100 tasks pending in queue",
                category=AlertCategory.PERFORMANCE,
                severity=AlertSeverity.WARNING,
                condition="queue.pending_tasks",
                threshold=100,
                comparison="gt",
                duration_seconds=300,  # 5 minutes
                channels=[AlertChannel.LOG]
            ),
            
            # Security alerts
            AlertRule(
                id="suspicious_activity",
                name="Suspicious Activity Detected",
                description="Potential security threat detected",
                category=AlertCategory.SECURITY,
                severity=AlertSeverity.CRITICAL,
                condition="security.threat_score",
                threshold=80,
                comparison="gt",
                duration_seconds=0,  # Immediate
                channels=[AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.SLACK],
                cooldown_seconds=60  # 1 minute cooldown
            ),
            
            # Data quality alerts
            AlertRule(
                id="low_book_quality",
                name="Low Book Generation Quality",
                description="Average book quality score below 70%",
                category=AlertCategory.DATA_QUALITY,
                severity=AlertSeverity.WARNING,
                condition="books.avg_quality_score",
                threshold=70,
                comparison="lt",
                duration_seconds=1800,  # 30 minutes
                channels=[AlertChannel.LOG]
            )
        ]
        
        # Register default rules
        for rule in default_rules:
            self.register_rule(rule)
    
    def register_rule(self, rule: AlertRule) -> None:
        """Register an alerting rule"""
        self.rules[rule.id] = rule
        self.logger.info(f"Registered alert rule: {rule.name}")
    
    def unregister_rule(self, rule_id: str) -> None:
        """Unregister an alerting rule"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            self.logger.info(f"Unregistered alert rule: {rule_id}")
    
    async def evaluate_rules(self, metrics: Dict[str, Any]) -> List[Alert]:
        """Evaluate all active rules against current metrics"""
        triggered_alerts = []
        
        for rule_id, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                # Check if rule condition is met
                condition_met, details = await self.evaluator.evaluate_condition(rule, metrics)
                
                if condition_met:
                    # Check if alert already active
                    if rule_id not in self.active_alerts:
                        # Create new alert
                        alert = Alert(
                            rule_id=rule_id,
                            rule_name=rule.name,
                            category=rule.category,
                            severity=rule.severity,
                            message=f"{rule.description} (Value: {details.get('metric_value', 'N/A')})",
                            details=details,
                            triggered_at=time.time()
                        )
                        
                        self.active_alerts[rule_id] = alert
                        triggered_alerts.append(alert)
                        
                        # Send notification if not in cooldown
                        if self.notifier.should_send_notification(rule):
                            await self.notifier.send_notification(alert, rule)
                        
                        self.logger.warning(f"Alert triggered: {rule.name}")
                else:
                    # Check if alert should be resolved
                    if rule_id in self.active_alerts:
                        alert = self.active_alerts[rule_id]
                        alert.resolved_at = time.time()
                        
                        # Move to history
                        self.alert_history.append(alert)
                        if len(self.alert_history) > self.max_history_size:
                            self.alert_history.pop(0)
                        
                        del self.active_alerts[rule_id]
                        
                        self.logger.info(f"Alert resolved: {rule.name}")
            
            except Exception as e:
                self.logger.error(f"Error evaluating rule {rule_id}: {e}")
                capture_exception(e)
        
        return triggered_alerts
    
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect all system metrics for evaluation"""
        metrics = {}
        
        try:
            # Get SLA metrics
            sla_status = await sla_tracker.get_sla_status()
            metrics['api'] = {
                'response_time': sla_status.get('compliance_report', {}).get('metrics', {}).get('api.response_time', {}).get('statistics', {}).get('mean', 0),
                'error_rate': sla_status.get('compliance_report', {}).get('metrics', {}).get('api.error_rate', {}).get('statistics', {}).get('mean', 0)
            }
            metrics['ai'] = {
                'success_rate': sla_status.get('compliance_report', {}).get('metrics', {}).get('ai.success_rate', {}).get('statistics', {}).get('mean', 100)
            }
            
            # Get health check status
            health_status = await health_manager.get_overall_health()
            metrics['health'] = {
                'status': 1 if health_status['status'] == 'healthy' else 0
            }
            metrics['database'] = {
                'healthy': 1 if any(s['service_name'] == 'supabase_database' and s['status'] == 'healthy' 
                                  for s in health_status.get('services', {}).values()) else 0
            }
            
            # Get system resource metrics
            from app.utils.resource_manager import get_system_health
            system_health = await get_system_health()
            
            metrics['system'] = {
                'cpu_usage': system_health.get('resource_usage', {}).get('cpu_percent', 0),
                'memory_usage': system_health.get('resource_usage', {}).get('memory_percent', 0),
                'disk_usage': 0  # Would need to implement disk usage check
            }
            
            # Get queue metrics
            from app.celery_app import get_queue_stats
            queue_stats = get_queue_stats()
            
            pending_tasks = 0
            if isinstance(queue_stats, dict) and 'scheduled' in queue_stats:
                scheduled = queue_stats.get('scheduled', {})
                if isinstance(scheduled, dict):
                    for worker_tasks in scheduled.values():
                        if isinstance(worker_tasks, list):
                            pending_tasks += len(worker_tasks)
            
            metrics['queue'] = {
                'pending_tasks': pending_tasks
            }
            
            # Get business metrics
            business_kpis = await metrics_collector.get_business_kpis()
            metrics['books'] = {
                'avg_quality_score': 85  # Would need to calculate from actual data
            }
            
            # Security metrics (placeholder)
            metrics['security'] = {
                'threat_score': 0  # Would need actual security monitoring
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            capture_exception(e)
        
        return metrics
    
    async def run_evaluation_cycle(self) -> List[Alert]:
        """Run a complete evaluation cycle"""
        # Collect metrics
        metrics = await self.collect_system_metrics()
        
        # Evaluate rules
        triggered_alerts = await self.evaluate_rules(metrics)
        
        return triggered_alerts
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 50) -> List[Alert]:
        """Get alert history"""
        return self.alert_history[-limit:] if self.alert_history else []
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        active_by_severity = defaultdict(int)
        active_by_category = defaultdict(int)
        
        for alert in self.active_alerts.values():
            active_by_severity[alert.severity.value] += 1
            active_by_category[alert.category.value] += 1
        
        # Calculate alert frequency
        recent_alerts = [
            alert for alert in self.alert_history
            if alert.triggered_at > time.time() - 86400  # Last 24 hours
        ]
        
        return {
            'active_alerts': len(self.active_alerts),
            'active_by_severity': dict(active_by_severity),
            'active_by_category': dict(active_by_category),
            'alerts_last_24h': len(recent_alerts),
            'total_rules': len(self.rules),
            'enabled_rules': sum(1 for rule in self.rules.values() if rule.enabled)
        }

# Global alerting engine
alerting_engine = AlertingEngine()

# Background task to run alert evaluations
async def alert_monitoring_task():
    """Background task to continuously monitor alerts"""
    logger.info("Starting alert monitoring task")
    
    while True:
        try:
            # Run evaluation cycle
            triggered_alerts = await alerting_engine.run_evaluation_cycle()
            
            if triggered_alerts:
                logger.info(f"Triggered {len(triggered_alerts)} new alerts")
            
            # Wait before next evaluation (30 seconds)
            await asyncio.sleep(30)
            
        except Exception as e:
            logger.error(f"Error in alert monitoring task: {e}")
            capture_exception(e)
            await asyncio.sleep(60)  # Wait longer on error

# Convenience functions
async def trigger_custom_alert(
    name: str,
    message: str,
    severity: AlertSeverity = AlertSeverity.WARNING,
    category: AlertCategory = AlertCategory.BUSINESS,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Trigger a custom alert"""
    alert = Alert(
        rule_id=f"custom_{int(time.time())}",
        rule_name=name,
        category=category,
        severity=severity,
        message=message,
        details=details or {},
        triggered_at=time.time()
    )
    
    # Create temporary rule for notification
    rule = AlertRule(
        id=alert.rule_id,
        name=name,
        description=message,
        category=category,
        severity=severity,
        condition="custom",
        threshold=0,
        comparison="eq",
        duration_seconds=0,
        channels=[AlertChannel.LOG, AlertChannel.EMAIL]
    )
    
    await alerting_engine.notifier.send_notification(alert, rule)

def get_active_alerts() -> List[Dict[str, Any]]:
    """Get all active alerts"""
    return [asdict(alert) for alert in alerting_engine.get_active_alerts()]

def get_alert_history(limit: int = 50) -> List[Dict[str, Any]]:
    """Get alert history"""
    return [asdict(alert) for alert in alerting_engine.get_alert_history(limit)]

def get_alert_statistics() -> Dict[str, Any]:
    """Get alert statistics"""
    return alerting_engine.get_alert_statistics()

# Integration with FastAPI startup
async def start_alert_monitoring():
    """Start alert monitoring (call from FastAPI startup)"""
    asyncio.create_task(alert_monitoring_task())