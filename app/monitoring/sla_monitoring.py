# app/monitoring/sla_monitoring.py - Performance Monitoring with SLA Tracking

import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple, Callable
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import statistics
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_metrics, monitor_operation

logger = get_logger(__name__)

class SLAMetric(Enum):
    """Types of SLA metrics"""
    AVAILABILITY = "availability"
    RESPONSE_TIME = "response_time"
    ERROR_RATE = "error_rate"
    THROUGHPUT = "throughput"
    SUCCESS_RATE = "success_rate"
    LATENCY_P95 = "latency_p95"
    LATENCY_P99 = "latency_p99"

class SLATarget(Enum):
    """SLA target definitions"""
    # API Performance
    API_AVAILABILITY = 99.9  # 99.9% uptime
    API_RESPONSE_TIME_MS = 500  # 500ms average response time
    API_ERROR_RATE = 1.0  # Less than 1% error rate
    API_THROUGHPUT_RPS = 100  # 100 requests per second
    
    # AI Generation Performance
    AI_GENERATION_SUCCESS_RATE = 95.0  # 95% success rate
    AI_GENERATION_TIME_SECONDS = 60  # Complete within 60 seconds
    AI_LATENCY_P95_MS = 5000  # 95th percentile under 5 seconds
    AI_LATENCY_P99_MS = 10000  # 99th percentile under 10 seconds
    
    # Database Performance
    DB_QUERY_TIME_MS = 100  # 100ms average query time
    DB_CONNECTION_SUCCESS_RATE = 99.5  # 99.5% connection success
    DB_TRANSACTION_SUCCESS_RATE = 99.0  # 99% transaction success
    
    # Background Tasks
    TASK_COMPLETION_RATE = 98.0  # 98% task completion
    TASK_PROCESSING_TIME_MINUTES = 30  # Process within 30 minutes
    QUEUE_PROCESSING_DELAY_SECONDS = 60  # Max 60 second queue delay
    
    # Business Operations
    BOOK_GENERATION_SUCCESS_RATE = 90.0  # 90% success rate
    PUBLICATION_SUCCESS_RATE = 95.0  # 95% publication success
    TREND_ANALYSIS_ACCURACY = 85.0  # 85% trend prediction accuracy

@dataclass
class SLAViolation:
    """SLA violation record"""
    metric: SLAMetric
    target_name: str
    target_value: float
    actual_value: float
    violation_percentage: float
    timestamp: float
    duration_seconds: float
    impact_level: str  # "low", "medium", "high", "critical"
    affected_users: int = 0
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceMetric:
    """Performance metric data point"""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class SLAMonitor:
    """Core SLA monitoring engine"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Performance data storage
        self._metrics_window = 3600  # 1 hour rolling window
        self._metrics_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        
        # SLA violation tracking
        self._active_violations: Dict[str, SLAViolation] = {}
        self._violation_history: List[SLAViolation] = []
        self._max_violation_history = 1000
        
        # Performance baselines
        self._performance_baselines: Dict[str, float] = {}
        self._baseline_update_interval = 3600  # Update baselines hourly
        self._last_baseline_update = 0
        
        # SLA compliance tracking
        self._compliance_windows = {
            'hourly': 3600,
            'daily': 86400,
            'weekly': 604800,
            'monthly': 2592000
        }
        self._compliance_data: Dict[str, Dict[str, float]] = defaultdict(dict)
    
    async def record_metric(self, metric: PerformanceMetric) -> None:
        """Record a performance metric"""
        # Store metric data
        self._metrics_data[metric.name].append({
            'value': metric.value,
            'timestamp': metric.timestamp,
            'tags': metric.tags,
            'metadata': metric.metadata
        })
        
        # Clean old data
        self._clean_old_metrics(metric.name)
        
        # Check for SLA violations
        await self._check_sla_compliance(metric)
        
        # Update baselines if needed
        if time.time() - self._last_baseline_update > self._baseline_update_interval:
            await self._update_performance_baselines()
    
    def _clean_old_metrics(self, metric_name: str) -> None:
        """Remove metrics outside the time window"""
        cutoff_time = time.time() - self._metrics_window
        
        # Remove old entries
        while (self._metrics_data[metric_name] and 
               self._metrics_data[metric_name][0]['timestamp'] < cutoff_time):
            self._metrics_data[metric_name].popleft()
    
    async def _check_sla_compliance(self, metric: PerformanceMetric) -> None:
        """Check if metric violates any SLA"""
        # Map metric names to SLA checks
        sla_checks = {
            'api.response_time': (SLATarget.API_RESPONSE_TIME_MS, SLAMetric.RESPONSE_TIME),
            'api.error_rate': (SLATarget.API_ERROR_RATE, SLAMetric.ERROR_RATE),
            'api.throughput': (SLATarget.API_THROUGHPUT_RPS, SLAMetric.THROUGHPUT),
            'ai.generation_time': (SLATarget.AI_GENERATION_TIME_SECONDS, SLAMetric.RESPONSE_TIME),
            'ai.success_rate': (SLATarget.AI_GENERATION_SUCCESS_RATE, SLAMetric.SUCCESS_RATE),
            'db.query_time': (SLATarget.DB_QUERY_TIME_MS, SLAMetric.RESPONSE_TIME),
            'task.completion_rate': (SLATarget.TASK_COMPLETION_RATE, SLAMetric.SUCCESS_RATE),
            'book.generation_success': (SLATarget.BOOK_GENERATION_SUCCESS_RATE, SLAMetric.SUCCESS_RATE)
        }
        
        if metric.name in sla_checks:
            target, sla_metric = sla_checks[metric.name]
            target_value = target.value
            
            # Check if violating SLA
            is_violation = False
            
            if sla_metric in [SLAMetric.RESPONSE_TIME, SLAMetric.LATENCY_P95, SLAMetric.LATENCY_P99]:
                # Lower is better
                is_violation = metric.value > target_value
            elif sla_metric in [SLAMetric.AVAILABILITY, SLAMetric.SUCCESS_RATE, SLAMetric.THROUGHPUT]:
                # Higher is better
                is_violation = metric.value < target_value
            elif sla_metric == SLAMetric.ERROR_RATE:
                # Lower is better
                is_violation = metric.value > target_value
            
            if is_violation:
                await self._handle_sla_violation(
                    metric, sla_metric, target.name, target_value
                )
            else:
                # Clear any active violation
                await self._clear_sla_violation(metric.name)
    
    async def _handle_sla_violation(self, 
                                   metric: PerformanceMetric, 
                                   sla_metric: SLAMetric,
                                   target_name: str,
                                   target_value: float) -> None:
        """Handle SLA violation"""
        violation_key = f"{metric.name}:{target_name}"
        
        # Calculate violation percentage
        if sla_metric in [SLAMetric.RESPONSE_TIME, SLAMetric.ERROR_RATE]:
            violation_percentage = ((metric.value - target_value) / target_value) * 100
        else:
            violation_percentage = ((target_value - metric.value) / target_value) * 100
        
        # Determine impact level
        impact_level = self._determine_impact_level(violation_percentage)
        
        if violation_key in self._active_violations:
            # Update existing violation
            violation = self._active_violations[violation_key]
            violation.actual_value = metric.value
            violation.violation_percentage = violation_percentage
            violation.duration_seconds = time.time() - violation.timestamp
        else:
            # Create new violation
            violation = SLAViolation(
                metric=sla_metric,
                target_name=target_name,
                target_value=target_value,
                actual_value=metric.value,
                violation_percentage=violation_percentage,
                timestamp=time.time(),
                duration_seconds=0,
                impact_level=impact_level,
                affected_users=metric.metadata.get('affected_users', 0),
                details=metric.metadata
            )
            
            self._active_violations[violation_key] = violation
            self.logger.warning(
                f"SLA violation detected: {target_name} - "
                f"Expected: {target_value}, Actual: {metric.value} "
                f"({violation_percentage:.1f}% violation)"
            )
        
        # Send to monitoring system
        capture_metrics(
            metric_name=f"sla.violation.{target_name}",
            value=violation_percentage,
            tags={
                'impact_level': impact_level,
                'metric_type': sla_metric.value
            }
        )
    
    async def _clear_sla_violation(self, metric_name: str) -> None:
        """Clear SLA violation when metric returns to normal"""
        violations_to_clear = [
            key for key in self._active_violations.keys()
            if key.startswith(f"{metric_name}:")
        ]
        
        for violation_key in violations_to_clear:
            violation = self._active_violations[violation_key]
            violation.duration_seconds = time.time() - violation.timestamp
            
            # Move to history
            self._violation_history.append(violation)
            if len(self._violation_history) > self._max_violation_history:
                self._violation_history.pop(0)
            
            del self._active_violations[violation_key]
            
            self.logger.info(
                f"SLA violation resolved: {violation.target_name} - "
                f"Duration: {violation.duration_seconds:.1f}s"
            )
    
    def _determine_impact_level(self, violation_percentage: float) -> str:
        """Determine impact level based on violation percentage"""
        if violation_percentage < 10:
            return "low"
        elif violation_percentage < 25:
            return "medium"
        elif violation_percentage < 50:
            return "high"
        else:
            return "critical"
    
    async def _update_performance_baselines(self) -> None:
        """Update performance baselines based on historical data"""
        self._last_baseline_update = time.time()
        
        for metric_name, data_points in self._metrics_data.items():
            if len(data_points) < 10:
                continue  # Not enough data
            
            # Calculate baseline as median of recent values
            recent_values = [dp['value'] for dp in data_points[-100:]]
            baseline = statistics.median(recent_values)
            
            self._performance_baselines[metric_name] = baseline
            
            self.logger.debug(f"Updated baseline for {metric_name}: {baseline}")
    
    def calculate_percentiles(self, metric_name: str, percentiles: List[int] = None) -> Dict[int, float]:
        """Calculate percentiles for a metric"""
        if percentiles is None:
            percentiles = [50, 75, 90, 95, 99]
        
        data_points = self._metrics_data.get(metric_name, [])
        if not data_points:
            return {p: 0.0 for p in percentiles}
        
        values = sorted([dp['value'] for dp in data_points])
        n = len(values)
        
        result = {}
        for p in percentiles:
            index = int((p / 100) * n)
            index = min(index, n - 1)
            result[p] = values[index]
        
        return result
    
    async def get_sla_compliance_report(self, window: str = 'hourly') -> Dict[str, Any]:
        """Generate SLA compliance report"""
        if window not in self._compliance_windows:
            window = 'hourly'
        
        window_seconds = self._compliance_windows[window]
        cutoff_time = time.time() - window_seconds
        
        # Calculate compliance for each metric
        compliance_data = {}
        
        for metric_name, data_points in self._metrics_data.items():
            recent_points = [
                dp for dp in data_points
                if dp['timestamp'] >= cutoff_time
            ]
            
            if not recent_points:
                continue
            
            # Calculate statistics
            values = [dp['value'] for dp in recent_points]
            
            stats = {
                'count': len(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'min': min(values),
                'max': max(values),
                'stddev': statistics.stdev(values) if len(values) > 1 else 0
            }
            
            # Calculate percentiles
            percentiles = self.calculate_percentiles(metric_name)
            stats['percentiles'] = percentiles
            
            # Check SLA compliance
            sla_compliant = True
            violations_in_window = [
                v for v in self._violation_history
                if v.timestamp >= cutoff_time and metric_name in v.details.get('metric_name', '')
            ]
            
            if violations_in_window:
                sla_compliant = False
                violation_time = sum(v.duration_seconds for v in violations_in_window)
                compliance_percentage = ((window_seconds - violation_time) / window_seconds) * 100
            else:
                compliance_percentage = 100.0
            
            compliance_data[metric_name] = {
                'statistics': stats,
                'sla_compliant': sla_compliant,
                'compliance_percentage': round(compliance_percentage, 2),
                'violations': len(violations_in_window),
                'baseline': self._performance_baselines.get(metric_name)
            }
        
        # Overall compliance
        total_metrics = len(compliance_data)
        compliant_metrics = sum(1 for data in compliance_data.values() if data['sla_compliant'])
        
        return {
            'window': window,
            'window_seconds': window_seconds,
            'timestamp': time.time(),
            'overall_compliance': {
                'total_metrics': total_metrics,
                'compliant_metrics': compliant_metrics,
                'compliance_percentage': round((compliant_metrics / total_metrics * 100), 2) if total_metrics > 0 else 100
            },
            'metrics': compliance_data,
            'active_violations': {
                key: asdict(violation) for key, violation in self._active_violations.items()
            }
        }
    
    async def get_performance_trends(self, metric_name: str, hours: int = 24) -> Dict[str, Any]:
        """Analyze performance trends for a metric"""
        cutoff_time = time.time() - (hours * 3600)
        
        data_points = [
            dp for dp in self._metrics_data.get(metric_name, [])
            if dp['timestamp'] >= cutoff_time
        ]
        
        if not data_points:
            return {'error': f'No data available for {metric_name}'}
        
        # Group by hour
        hourly_data = defaultdict(list)
        for dp in data_points:
            hour_key = datetime.fromtimestamp(dp['timestamp']).strftime('%Y-%m-%d %H:00')
            hourly_data[hour_key].append(dp['value'])
        
        # Calculate hourly statistics
        hourly_stats = {}
        for hour, values in hourly_data.items():
            hourly_stats[hour] = {
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'min': min(values),
                'max': max(values),
                'count': len(values)
            }
        
        # Calculate trend (simple linear regression)
        if len(hourly_stats) >= 2:
            hours_list = sorted(hourly_stats.keys())
            values_list = [hourly_stats[h]['mean'] for h in hours_list]
            
            # Simple trend calculation
            first_half_avg = statistics.mean(values_list[:len(values_list)//2])
            second_half_avg = statistics.mean(values_list[len(values_list)//2:])
            trend_direction = "improving" if second_half_avg < first_half_avg else "degrading"
            trend_percentage = abs((second_half_avg - first_half_avg) / first_half_avg * 100)
        else:
            trend_direction = "stable"
            trend_percentage = 0
        
        return {
            'metric_name': metric_name,
            'period_hours': hours,
            'data_points': len(data_points),
            'hourly_statistics': hourly_stats,
            'trend': {
                'direction': trend_direction,
                'change_percentage': round(trend_percentage, 2)
            },
            'current_baseline': self._performance_baselines.get(metric_name),
            'timestamp': time.time()
        }

class SLATracker:
    """High-level SLA tracking interface"""
    
    def __init__(self):
        self.monitor = SLAMonitor()
        self.logger = get_logger(__name__)
        self._tracking_enabled = True
    
    async def track_api_request(self, 
                               endpoint: str, 
                               method: str, 
                               response_time_ms: float,
                               status_code: int,
                               user_id: Optional[str] = None) -> None:
        """Track API request performance"""
        if not self._tracking_enabled:
            return
        
        # Track response time
        await self.monitor.record_metric(PerformanceMetric(
            name='api.response_time',
            value=response_time_ms,
            timestamp=time.time(),
            tags={
                'endpoint': endpoint,
                'method': method,
                'status_group': f"{status_code // 100}xx"
            },
            metadata={
                'user_id': user_id,
                'status_code': status_code
            }
        ))
        
        # Track error rate (per minute)
        is_error = status_code >= 400
        await self._update_rate_metric('api.error_rate', is_error, 60)
    
    async def track_ai_generation(self,
                                 provider: str,
                                 model: str,
                                 generation_time_seconds: float,
                                 success: bool,
                                 tokens_used: int = 0) -> None:
        """Track AI generation performance"""
        if not self._tracking_enabled:
            return
        
        # Track generation time
        await self.monitor.record_metric(PerformanceMetric(
            name='ai.generation_time',
            value=generation_time_seconds,
            timestamp=time.time(),
            tags={
                'provider': provider,
                'model': model,
                'success': str(success).lower()
            },
            metadata={
                'tokens_used': tokens_used
            }
        ))
        
        # Track success rate
        await self._update_rate_metric('ai.success_rate', success, 3600)
    
    async def track_database_query(self,
                                 query_type: str,
                                 query_time_ms: float,
                                 success: bool,
                                 rows_affected: int = 0) -> None:
        """Track database query performance"""
        if not self._tracking_enabled:
            return
        
        await self.monitor.record_metric(PerformanceMetric(
            name='db.query_time',
            value=query_time_ms,
            timestamp=time.time(),
            tags={
                'query_type': query_type,
                'success': str(success).lower()
            },
            metadata={
                'rows_affected': rows_affected
            }
        ))
    
    async def track_background_task(self,
                                  task_name: str,
                                  processing_time_seconds: float,
                                  success: bool,
                                  queue_delay_seconds: float = 0) -> None:
        """Track background task performance"""
        if not self._tracking_enabled:
            return
        
        # Track processing time
        await self.monitor.record_metric(PerformanceMetric(
            name='task.processing_time',
            value=processing_time_seconds,
            timestamp=time.time(),
            tags={
                'task_name': task_name,
                'success': str(success).lower()
            },
            metadata={
                'queue_delay_seconds': queue_delay_seconds
            }
        ))
        
        # Track completion rate
        await self._update_rate_metric('task.completion_rate', success, 3600)
    
    async def track_business_operation(self,
                                     operation: str,
                                     success: bool,
                                     duration_seconds: float = 0,
                                     metadata: Optional[Dict[str, Any]] = None) -> None:
        """Track business operation performance"""
        if not self._tracking_enabled:
            return
        
        metric_name = f"{operation}.success_rate"
        
        await self.monitor.record_metric(PerformanceMetric(
            name=metric_name,
            value=100.0 if success else 0.0,
            timestamp=time.time(),
            tags={
                'operation': operation
            },
            metadata=metadata or {}
        ))
    
    async def _update_rate_metric(self, metric_name: str, success: bool, window_seconds: int) -> None:
        """Update rate-based metrics"""
        # Get recent data points
        cutoff_time = time.time() - window_seconds
        data_points = self.monitor._metrics_data.get(metric_name, [])
        
        recent_points = [
            dp for dp in data_points
            if dp['timestamp'] >= cutoff_time
        ]
        
        # Add new data point
        recent_points.append({
            'value': 1.0 if success else 0.0,
            'timestamp': time.time()
        })
        
        # Calculate rate
        if len(recent_points) > 0:
            success_count = sum(dp['value'] for dp in recent_points)
            rate = (success_count / len(recent_points)) * 100
        else:
            rate = 100.0 if success else 0.0
        
        await self.monitor.record_metric(PerformanceMetric(
            name=metric_name,
            value=rate,
            timestamp=time.time()
        ))
    
    async def get_sla_status(self) -> Dict[str, Any]:
        """Get current SLA status"""
        compliance_report = await self.monitor.get_sla_compliance_report('hourly')
        
        # Add quick status summary
        active_violations = len(self.monitor._active_violations)
        critical_violations = sum(
            1 for v in self.monitor._active_violations.values()
            if v.impact_level == 'critical'
        )
        
        status = "healthy"
        if critical_violations > 0:
            status = "critical"
        elif active_violations > 0:
            status = "degraded"
        
        return {
            'status': status,
            'summary': {
                'active_violations': active_violations,
                'critical_violations': critical_violations,
                'overall_compliance': compliance_report['overall_compliance']
            },
            'compliance_report': compliance_report,
            'timestamp': time.time()
        }
    
    async def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data"""
        # Get compliance reports for different windows
        hourly_compliance = await self.monitor.get_sla_compliance_report('hourly')
        daily_compliance = await self.monitor.get_sla_compliance_report('daily')
        
        # Get key metric trends
        key_metrics = [
            'api.response_time',
            'api.error_rate', 
            'ai.generation_time',
            'ai.success_rate',
            'db.query_time',
            'task.completion_rate'
        ]
        
        trends = {}
        for metric in key_metrics:
            trend = await self.monitor.get_performance_trends(metric, hours=24)
            if 'error' not in trend:
                trends[metric] = trend
        
        # Calculate percentiles for key metrics
        percentiles = {}
        for metric in key_metrics:
            percentiles[metric] = self.monitor.calculate_percentiles(metric)
        
        return {
            'status': await self.get_sla_status(),
            'compliance': {
                'hourly': hourly_compliance,
                'daily': daily_compliance
            },
            'trends': trends,
            'percentiles': percentiles,
            'active_violations': {
                key: asdict(violation) 
                for key, violation in self.monitor._active_violations.items()
            },
            'recent_violations': [
                asdict(v) for v in self.monitor._violation_history[-10:]
            ],
            'timestamp': time.time()
        }

# Global SLA tracker instance
sla_tracker = SLATracker()

# Convenience functions
async def track_api_performance(endpoint: str, method: str, response_time_ms: float, 
                              status_code: int, user_id: Optional[str] = None) -> None:
    """Track API request performance"""
    await sla_tracker.track_api_request(endpoint, method, response_time_ms, status_code, user_id)

async def track_ai_performance(provider: str, model: str, generation_time: float,
                             success: bool, tokens: int = 0) -> None:
    """Track AI generation performance"""
    await sla_tracker.track_ai_generation(provider, model, generation_time, success, tokens)

async def track_db_performance(query_type: str, query_time_ms: float, 
                             success: bool, rows: int = 0) -> None:
    """Track database query performance"""
    await sla_tracker.track_database_query(query_type, query_time_ms, success, rows)

async def track_task_performance(task_name: str, processing_time: float,
                               success: bool, queue_delay: float = 0) -> None:
    """Track background task performance"""
    await sla_tracker.track_background_task(task_name, processing_time, success, queue_delay)

async def get_sla_dashboard() -> Dict[str, Any]:
    """Get SLA performance dashboard"""
    return await sla_tracker.get_performance_dashboard()

async def get_sla_compliance(window: str = 'hourly') -> Dict[str, Any]:
    """Get SLA compliance report"""
    return await sla_tracker.monitor.get_sla_compliance_report(window)

# Decorators for automatic SLA tracking
def track_sla(metric_type: str):
    """Decorator to automatically track SLA metrics"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                
                if metric_type == 'api':
                    # Extract API info from function context
                    await track_api_performance(
                        endpoint=func.__name__,
                        method='POST',
                        response_time_ms=duration * 1000,
                        status_code=200 if success else 500
                    )
                elif metric_type == 'ai':
                    await track_ai_performance(
                        provider='unknown',
                        model='unknown',
                        generation_time=duration,
                        success=success
                    )
                elif metric_type == 'db':
                    await track_db_performance(
                        query_type=func.__name__,
                        query_time_ms=duration * 1000,
                        success=success
                    )
                elif metric_type == 'task':
                    await track_task_performance(
                        task_name=func.__name__,
                        processing_time=duration,
                        success=success
                    )
        
        return wrapper
    return decorator