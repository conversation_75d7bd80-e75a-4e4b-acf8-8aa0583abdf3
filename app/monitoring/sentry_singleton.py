"""
Production-grade Sentry singleton for FastAPI applications.
Only initializes when needed and provides efficient error capture.
"""

import os
import logging
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

# Lazy import Sentry SDK to avoid import overhead when not needed
_sentry_sdk = None
_sentry_initialized = False
_sentry_available = False

logger = logging.getLogger(__name__)

def _import_sentry():
    """Lazy import Sentry SDK only when needed."""
    global _sentry_sdk
    if _sentry_sdk is None:
        try:
            import sentry_sdk
            from sentry_sdk.integrations.starlette import StarletteIntegration
            from sentry_sdk.integrations.logging import LoggingIntegration
            from sentry_sdk.integrations.redis import RedisIntegration
            from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
            from sentry_sdk.integrations.asyncio import AsyncioIntegration
            
            _sentry_sdk = sentry_sdk
            _sentry_sdk.StarletteIntegration = StarletteIntegration
            _sentry_sdk.LoggingIntegration = LoggingIntegration
            _sentry_sdk.RedisIntegration = RedisIntegration
            _sentry_sdk.SqlalchemyIntegration = SqlalchemyIntegration
            _sentry_sdk.AsyncioIntegration = AsyncioIntegration
            return True
        except ImportError:
            logger.warning("Sentry SDK not available - error reporting disabled")
            return False
    return True

class SentrySingleton:
    """Efficient Sentry singleton that only initializes when needed."""
    
    def __init__(self):
        self._initialized = False
        self._dsn = os.getenv("SENTRY_DSN", "").strip()
        # Only check if Sentry is available when DSN is provided
        self._available = bool(self._dsn)
        
    @property
    def is_available(self) -> bool:
        """Check if Sentry is available and configured."""
        return self._available
    
    @property 
    def is_initialized(self) -> bool:
        """Check if Sentry has been initialized."""
        return self._initialized
    
    def init_sentry(
        self,
        traces_sample_rate: float = 0.1,
        environment: str = "production",
        release: Optional[str] = None,
        sensitive_keys: Optional[List[str]] = None
    ) -> bool:
        """
        Initialize Sentry with production-grade configuration.
        Returns immediately if already initialized or not available.
        """
        global _sentry_initialized
        
        # Early returns to avoid unnecessary work
        if _sentry_initialized:
            return True
            
        if not self._available:
            return False  # DSN not configured, skip silently
            
        if not _import_sentry():
            self._available = False
            logger.info("Sentry SDK not available - error reporting disabled")
            return False
            
        try:
            # Default sensitive keys to scrub
            default_sensitive_keys = [
                "password", "secret", "token", "key", "authorization",
                "api_key", "access_token", "refresh_token", "session_id",
                "supabase_service_key", "openai_api_key", "anthropic_api_key"
            ]
            
            if sensitive_keys:
                default_sensitive_keys.extend(sensitive_keys)
            
            def before_send(event, hint):
                """Scrub sensitive data from events."""
                return self._scrub_sensitive_data(event, default_sensitive_keys)
            
            # Configure integrations
            integrations = [
                _sentry_sdk.StarletteIntegration(transaction_style="endpoint"),
                _sentry_sdk.LoggingIntegration(
                    level=logging.INFO,
                    event_level=logging.ERROR
                ),
                _sentry_sdk.AsyncioIntegration(),
                _sentry_sdk.RedisIntegration(),
                _sentry_sdk.SqlalchemyIntegration(),
            ]
            
            # Initialize Sentry
            _sentry_sdk.init(
                dsn=self._dsn,
                integrations=integrations,
                traces_sample_rate=traces_sample_rate,
                environment=environment,
                release=release,
                before_send=before_send,
                attach_stacktrace=True,
                send_default_pii=False,
                max_breadcrumbs=50,
                debug=False
            )
            
            _sentry_initialized = True
            self._initialized = True
            
            logger.info(f"✅ Sentry initialized (env: {environment})")
            return True
            
        except Exception as e:
            # Don't log errors for missing/empty DSN - this is expected behavior
            if "Unsupported scheme" not in str(e) and "dsn" not in str(e).lower():
                logger.error(f"❌ Failed to initialize Sentry: {e}")
            self._available = False
            return False
    
    def _scrub_sensitive_data(self, event: Dict[str, Any], sensitive_keys: List[str]) -> Dict[str, Any]:
        """Recursively scrub sensitive data from events."""
        def scrub_dict(data):
            if isinstance(data, dict):
                for key, value in data.items():
                    if any(sensitive_key.lower() in key.lower() for sensitive_key in sensitive_keys):
                        data[key] = "[REDACTED]"
                    else:
                        scrub_dict(value)
            elif isinstance(data, list):
                for item in data:
                    scrub_dict(item)
        
        scrub_dict(event)
        return event
    
    def capture_exception(self, exc: Exception, **kwargs) -> Optional[str]:
        """Capture exception only if Sentry is initialized."""
        if not self._initialized or not _sentry_sdk:
            return None
            
        try:
            with _sentry_sdk.push_scope() as scope:
                if "user" in kwargs:
                    scope.user = kwargs["user"]
                if "tags" in kwargs:
                    for key, value in kwargs["tags"].items():
                        scope.set_tag(key, value)
                if "extra" in kwargs:
                    for key, value in kwargs["extra"].items():
                        scope.set_extra(key, value)
                if "level" in kwargs:
                    scope.level = kwargs["level"]
                
                return _sentry_sdk.capture_exception(exc)
                
        except Exception as e:
            logger.debug(f"Failed to capture exception in Sentry: {e}")
            return None
    
    def capture_message(self, message: str, level: str = "info", **kwargs) -> Optional[str]:
        """Capture message only if Sentry is initialized."""
        if not self._initialized or not _sentry_sdk:
            return None
            
        try:
            with _sentry_sdk.push_scope() as scope:
                if "user" in kwargs:
                    scope.user = kwargs["user"]
                if "tags" in kwargs:
                    for key, value in kwargs["tags"].items():
                        scope.set_tag(key, value)
                if "extra" in kwargs:
                    for key, value in kwargs["extra"].items():
                        scope.set_extra(key, value)
                
                return _sentry_sdk.capture_message(message, level)
                
        except Exception:
            return None
    
    @contextmanager
    def configure_scope(self, **kwargs):
        """Context manager for Sentry scope - no-op if not initialized."""
        if not self._initialized or not _sentry_sdk:
            yield None
            return
            
        with _sentry_sdk.push_scope() as scope:
            if "user" in kwargs:
                scope.user = kwargs["user"]
            if "tags" in kwargs:
                for key, value in kwargs["tags"].items():
                    scope.set_tag(key, value)
            if "extra" in kwargs:
                for key, value in kwargs["extra"].items():
                    scope.set_extra(key, value)
            yield scope

# Global singleton instance
sentry = SentrySingleton()

# Convenience functions
def init_sentry(**kwargs) -> bool:
    """Initialize Sentry singleton."""
    return sentry.init_sentry(**kwargs)

def capture_exception(exc: Exception, extra: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[str]:
    """Capture exception via singleton - safe to call always."""
    if extra:
        kwargs["extra"] = extra
    return sentry.capture_exception(exc, **kwargs)

def capture_message(message: str, level: str = "info", **kwargs) -> Optional[str]:
    """Capture message via singleton - safe to call always.""" 
    return sentry.capture_message(message, level, **kwargs)

def is_sentry_available() -> bool:
    """Check if Sentry is available and configured."""
    return sentry.is_available

def is_sentry_initialized() -> bool:
    """Check if Sentry has been initialized."""
    return sentry.is_initialized