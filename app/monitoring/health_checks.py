# app/monitoring/health_checks.py - Comprehensive Health Checks for Critical Services

import asyncio
import time
import json
import httpx
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
from app.config import settings
from app.monitoring.monitoring_setup import get_logger, capture_exception

logger = get_logger(__name__)

class HealthStatus(Enum):
    """Health check status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

class ServiceType(Enum):
    """Types of services to monitor"""
    DATABASE = "database"
    CACHE = "cache"
    AI_PROVIDER = "ai_provider"
    EXTERNAL_API = "external_api"
    QUEUE = "queue"
    STORAGE = "storage"
    INTERNAL_SERVICE = "internal_service"

@dataclass
class HealthCheckResult:
    """Health check result data structure"""
    service_name: str
    service_type: ServiceType
    status: HealthStatus
    response_time_ms: float
    timestamp: float
    message: str
    details: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'service_name': self.service_name,
            'service_type': self.service_type.value,
            'status': self.status.value,
            'response_time_ms': self.response_time_ms,
            'timestamp': self.timestamp,
            'message': self.message,
            'details': self.details or {},
            'error': self.error
        }

class BaseHealthCheck:
    """Base class for health checks"""
    
    def __init__(self, name: str, service_type: ServiceType, timeout: float = 10.0):
        self.name = name
        self.service_type = service_type
        self.timeout = timeout
        self.logger = get_logger(__name__)
    
    async def check(self) -> HealthCheckResult:
        """Perform health check"""
        start_time = time.time()
        
        try:
            result = await asyncio.wait_for(self._perform_check(), timeout=self.timeout)
            response_time_ms = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                service_name=self.name,
                service_type=self.service_type,
                status=result.get('status', HealthStatus.UNKNOWN),
                response_time_ms=response_time_ms,
                timestamp=time.time(),
                message=result.get('message', 'Health check completed'),
                details=result.get('details'),
                error=result.get('error')
            )
        
        except asyncio.TimeoutError:
            response_time_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name=self.name,
                service_type=self.service_type,
                status=HealthStatus.CRITICAL,
                response_time_ms=response_time_ms,
                timestamp=time.time(),
                message=f"Health check timed out after {self.timeout}s",
                error="Timeout"
            )
        
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            self.logger.error(f"Health check failed for {self.name}: {e}")
            
            return HealthCheckResult(
                service_name=self.name,
                service_type=self.service_type,
                status=HealthStatus.CRITICAL,
                response_time_ms=response_time_ms,
                timestamp=time.time(),
                message=f"Health check failed: {str(e)}",
                error=str(e)
            )
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Override this method in subclasses"""
        raise NotImplementedError

class DatabaseHealthCheck(BaseHealthCheck):
    """Database connectivity health check"""
    
    def __init__(self, name: str = "supabase_database"):
        super().__init__(name, ServiceType.DATABASE, timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check database connectivity"""
        try:
            # Import here to avoid circular imports
            from app.database.connection_pool import get_connection_pool
            
            pool = get_connection_pool()
            health_result = await pool.health_check()
            
            if health_result.get('status') == 'healthy':
                return {
                    'status': HealthStatus.HEALTHY,
                    'message': 'Database connection healthy',
                    'details': health_result
                }
            else:
                return {
                    'status': HealthStatus.UNHEALTHY,
                    'message': 'Database connection issues',
                    'details': health_result,
                    'error': health_result.get('error')
                }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'Database health check failed',
                'error': str(e)
            }

class RedisHealthCheck(BaseHealthCheck):
    """Redis cache health check"""
    
    def __init__(self, name: str = "redis_cache"):
        super().__init__(name, ServiceType.CACHE, timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Redis connectivity"""
        try:
            # Import here to avoid circular imports
            from app.cache.redis_cluster import get_redis_cluster
            
            cluster = get_redis_cluster()
            cluster_info = await cluster.get_cluster_info()
            
            health_status = cluster_info.get('health', {}).get('status')
            
            if health_status == 'healthy':
                return {
                    'status': HealthStatus.HEALTHY,
                    'message': 'Redis cluster healthy',
                    'details': cluster_info
                }
            elif health_status == 'degraded':
                return {
                    'status': HealthStatus.DEGRADED,
                    'message': 'Redis cluster degraded',
                    'details': cluster_info
                }
            else:
                return {
                    'status': HealthStatus.UNHEALTHY,
                    'message': 'Redis cluster unhealthy',
                    'details': cluster_info
                }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'Redis health check failed',
                'error': str(e)
            }

class OpenAIHealthCheck(BaseHealthCheck):
    """OpenAI API health check"""
    
    def __init__(self, name: str = "openai_api"):
        super().__init__(name, ServiceType.AI_PROVIDER, timeout=15.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check OpenAI API availability"""
        try:
            if not settings.openai_api_key:
                return {
                    'status': HealthStatus.DEGRADED,
                    'message': 'OpenAI API key not configured'
                }
            
            # Import here to avoid circular imports
            from app.utils.async_ai_client import get_ai_client_stats, ai_health_check
            
            health_result = await ai_health_check()
            openai_status = health_result.get('openai', {})
            
            if openai_status.get('available'):
                response_time = openai_status.get('response_time', 0) * 1000  # Convert to ms
                
                if response_time < 5000:  # Less than 5 seconds
                    status = HealthStatus.HEALTHY
                    message = 'OpenAI API responding normally'
                elif response_time < 10000:  # Less than 10 seconds
                    status = HealthStatus.DEGRADED
                    message = 'OpenAI API responding slowly'
                else:
                    status = HealthStatus.DEGRADED
                    message = 'OpenAI API very slow'
                
                return {
                    'status': status,
                    'message': message,
                    'details': {
                        'response_time_ms': response_time,
                        'api_available': True
                    }
                }
            else:
                return {
                    'status': HealthStatus.UNHEALTHY,
                    'message': 'OpenAI API not responding',
                    'details': {'api_available': False}
                }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'OpenAI health check failed',
                'error': str(e)
            }

class AnthropicHealthCheck(BaseHealthCheck):
    """Anthropic API health check"""
    
    def __init__(self, name: str = "anthropic_api"):
        super().__init__(name, ServiceType.AI_PROVIDER, timeout=15.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Anthropic API availability"""
        try:
            if not settings.anthropic_api_key:
                return {
                    'status': HealthStatus.DEGRADED,
                    'message': 'Anthropic API key not configured'
                }
            
            # Import here to avoid circular imports
            from app.utils.async_ai_client import ai_health_check
            
            health_result = await ai_health_check()
            anthropic_status = health_result.get('anthropic', {})
            
            if anthropic_status.get('available'):
                response_time = anthropic_status.get('response_time', 0) * 1000  # Convert to ms
                
                if response_time < 5000:  # Less than 5 seconds
                    status = HealthStatus.HEALTHY
                    message = 'Anthropic API responding normally'
                elif response_time < 10000:  # Less than 10 seconds
                    status = HealthStatus.DEGRADED
                    message = 'Anthropic API responding slowly'
                else:
                    status = HealthStatus.DEGRADED
                    message = 'Anthropic API very slow'
                
                return {
                    'status': status,
                    'message': message,
                    'details': {
                        'response_time_ms': response_time,
                        'api_available': True
                    }
                }
            else:
                return {
                    'status': HealthStatus.UNHEALTHY,
                    'message': 'Anthropic API not responding',
                    'details': {'api_available': False}
                }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'Anthropic health check failed',
                'error': str(e)
            }

class CeleryHealthCheck(BaseHealthCheck):
    """Celery queue health check"""
    
    def __init__(self, name: str = "celery_queue"):
        super().__init__(name, ServiceType.QUEUE, timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Celery queue health"""
        try:
            # Import here to avoid circular imports
            from app.celery_app import get_celery_health
            
            health_result = get_celery_health()
            
            if health_result.get('status') == 'healthy':
                workers_online = health_result.get('workers_online', 0)
                
                if workers_online > 0:
                    return {
                        'status': HealthStatus.HEALTHY,
                        'message': f'Celery healthy with {workers_online} workers',
                        'details': health_result
                    }
                else:
                    return {
                        'status': HealthStatus.DEGRADED,
                        'message': 'Celery available but no workers online',
                        'details': health_result
                    }
            else:
                return {
                    'status': HealthStatus.UNHEALTHY,
                    'message': 'Celery queue unhealthy',
                    'details': health_result,
                    'error': health_result.get('error')
                }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'Celery health check failed',
                'error': str(e)
            }

class SupabaseStorageHealthCheck(BaseHealthCheck):
    """Supabase Storage health check"""
    
    def __init__(self, name: str = "supabase_storage"):
        super().__init__(name, ServiceType.STORAGE, timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Supabase Storage availability"""
        try:
            if not settings.supabase_url or not settings.supabase_service_key:
                return {
                    'status': HealthStatus.DEGRADED,
                    'message': 'Supabase credentials not configured'
                }
            
            # Test Supabase Storage API
            storage_url = f"{settings.supabase_url}/storage/v1/bucket"
            headers = {
                'Authorization': f'Bearer {settings.supabase_service_key}',
                'apikey': settings.supabase_service_key
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(storage_url, headers=headers, timeout=self.timeout)
                
                if response.status_code == 200:
                    return {
                        'status': HealthStatus.HEALTHY,
                        'message': 'Supabase Storage accessible',
                        'details': {
                            'status_code': response.status_code,
                            'buckets_available': True
                        }
                    }
                else:
                    return {
                        'status': HealthStatus.UNHEALTHY,
                        'message': f'Supabase Storage returned {response.status_code}',
                        'details': {
                            'status_code': response.status_code,
                            'response_text': response.text[:200]
                        }
                    }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'Supabase Storage health check failed',
                'error': str(e)
            }

class ExternalAPIHealthCheck(BaseHealthCheck):
    """Generic external API health check"""
    
    def __init__(self, name: str, url: str, timeout: float = 10.0, headers: Optional[Dict[str, str]] = None):
        super().__init__(name, ServiceType.EXTERNAL_API, timeout)
        self.url = url
        self.headers = headers or {}
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check external API availability"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.url, headers=self.headers, timeout=self.timeout)
                
                if 200 <= response.status_code < 400:
                    return {
                        'status': HealthStatus.HEALTHY,
                        'message': f'API responding with {response.status_code}',
                        'details': {
                            'status_code': response.status_code,
                            'response_time_ms': response.elapsed.total_seconds() * 1000
                        }
                    }
                else:
                    return {
                        'status': HealthStatus.UNHEALTHY,
                        'message': f'API returned {response.status_code}',
                        'details': {
                            'status_code': response.status_code,
                            'response_text': response.text[:200]
                        }
                    }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': f'API health check failed: {str(e)}',
                'error': str(e)
            }

class SystemResourceHealthCheck(BaseHealthCheck):
    """System resource health check"""
    
    def __init__(self, name: str = "system_resources"):
        super().__init__(name, ServiceType.INTERNAL_SERVICE, timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check system resource utilization"""
        try:
            # Import here to avoid circular imports
            from app.utils.resource_manager import get_system_health
            
            health_result = await get_system_health()
            
            status_map = {
                'healthy': HealthStatus.HEALTHY,
                'degraded': HealthStatus.DEGRADED,
                'critical': HealthStatus.CRITICAL
            }
            
            system_status = health_result.get('status', 'unknown')
            health_status = status_map.get(system_status, HealthStatus.UNKNOWN)
            
            return {
                'status': health_status,
                'message': f'System resources {system_status}',
                'details': health_result
            }
        
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL,
                'message': 'System resource check failed',
                'error': str(e)
            }

class HealthCheckManager:
    """Manages and orchestrates all health checks"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.health_checks: Dict[str, BaseHealthCheck] = {}
        self._last_check_results: Dict[str, HealthCheckResult] = {}
        self._check_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        # Register default health checks
        self._register_default_checks()
    
    def _register_default_checks(self):
        """Register default health checks"""
        self.register_check(DatabaseHealthCheck())
        self.register_check(RedisHealthCheck())
        self.register_check(OpenAIHealthCheck())
        self.register_check(AnthropicHealthCheck())
        self.register_check(CeleryHealthCheck())
        self.register_check(SupabaseStorageHealthCheck())
        self.register_check(SystemResourceHealthCheck())
        
        # Add external API checks if configured
        if hasattr(settings, 'google_trends_enabled') and settings.google_trends_enabled:
            self.register_check(ExternalAPIHealthCheck(
                'google_trends',
                'https://trends.google.com/trends/api/dailytrends'
            ))
        
        if hasattr(settings, 'amazon_kdp_enabled') and settings.amazon_kdp_enabled:
            self.register_check(ExternalAPIHealthCheck(
                'amazon_kdp',
                'https://kdp.amazon.com'
            ))
    
    def register_check(self, health_check: BaseHealthCheck):
        """Register a health check"""
        self.health_checks[health_check.name] = health_check
        self.logger.info(f"Registered health check: {health_check.name}")
    
    def unregister_check(self, name: str):
        """Unregister a health check"""
        if name in self.health_checks:
            del self.health_checks[name]
            self.logger.info(f"Unregistered health check: {name}")
    
    async def check_service(self, service_name: str) -> Optional[HealthCheckResult]:
        """Check a specific service"""
        if service_name not in self.health_checks:
            return None
        
        health_check = self.health_checks[service_name]
        result = await health_check.check()
        
        # Store result
        self._last_check_results[service_name] = result
        
        return result
    
    async def check_all_services(self, parallel: bool = True) -> Dict[str, HealthCheckResult]:
        """Check all registered services"""
        if parallel:
            # Run all checks in parallel
            tasks = {
                name: self.check_service(name) 
                for name in self.health_checks.keys()
            }
            
            results = await asyncio.gather(*tasks.values(), return_exceptions=True)
            
            service_results = {}
            for i, (service_name, task) in enumerate(tasks.items()):
                result = results[i]
                if isinstance(result, Exception):
                    # Create error result
                    service_results[service_name] = HealthCheckResult(
                        service_name=service_name,
                        service_type=self.health_checks[service_name].service_type,
                        status=HealthStatus.CRITICAL,
                        response_time_ms=0,
                        timestamp=time.time(),
                        message=f"Health check failed: {str(result)}",
                        error=str(result)
                    )
                else:
                    service_results[service_name] = result
        else:
            # Run checks sequentially
            service_results = {}
            for service_name in self.health_checks.keys():
                service_results[service_name] = await self.check_service(service_name)
        
        # Store in history
        self._store_check_history(service_results)
        
        return service_results
    
    def _store_check_history(self, results: Dict[str, HealthCheckResult]):
        """Store check results in history"""
        history_entry = {
            'timestamp': time.time(),
            'results': {name: result.to_dict() for name, result in results.items()}
        }
        
        self._check_history.append(history_entry)
        
        # Maintain history size limit
        if len(self._check_history) > self.max_history_size:
            self._check_history.pop(0)
    
    async def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        service_results = await self.check_all_services()
        
        # Calculate overall status
        service_statuses = [result.status for result in service_results.values()]
        
        if all(status == HealthStatus.HEALTHY for status in service_statuses):
            overall_status = HealthStatus.HEALTHY
        elif any(status == HealthStatus.CRITICAL for status in service_statuses):
            overall_status = HealthStatus.CRITICAL
        elif any(status == HealthStatus.UNHEALTHY for status in service_statuses):
            overall_status = HealthStatus.UNHEALTHY
        elif any(status == HealthStatus.DEGRADED for status in service_statuses):
            overall_status = HealthStatus.DEGRADED
        else:
            overall_status = HealthStatus.UNKNOWN
        
        # Calculate metrics
        healthy_count = sum(1 for status in service_statuses if status == HealthStatus.HEALTHY)
        total_count = len(service_statuses)
        
        avg_response_time = sum(
            result.response_time_ms for result in service_results.values()
        ) / total_count if total_count > 0 else 0
        
        # Group services by type
        services_by_type = {}
        for result in service_results.values():
            service_type = result.service_type.value
            if service_type not in services_by_type:
                services_by_type[service_type] = []
            services_by_type[service_type].append(result.to_dict())
        
        return {
            'status': overall_status.value,
            'timestamp': time.time(),
            'summary': {
                'total_services': total_count,
                'healthy_services': healthy_count,
                'unhealthy_services': total_count - healthy_count,
                'health_percentage': round((healthy_count / total_count) * 100, 2) if total_count > 0 else 0,
                'avg_response_time_ms': round(avg_response_time, 2)
            },
            'services': {name: result.to_dict() for name, result in service_results.items()},
            'services_by_type': services_by_type
        }
    
    def get_health_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get health check history"""
        return self._check_history[-limit:] if self._check_history else []
    
    def get_service_uptime(self, service_name: str, hours: int = 24) -> Dict[str, Any]:
        """Calculate service uptime for the specified period"""
        if not self._check_history:
            return {'error': 'No health check history available'}
        
        cutoff_time = time.time() - (hours * 3600)
        relevant_checks = [
            entry for entry in self._check_history
            if entry['timestamp'] >= cutoff_time and service_name in entry['results']
        ]
        
        if not relevant_checks:
            return {'error': f'No data for {service_name} in the last {hours} hours'}
        
        total_checks = len(relevant_checks)
        healthy_checks = sum(
            1 for entry in relevant_checks
            if entry['results'][service_name]['status'] == HealthStatus.HEALTHY.value
        )
        
        uptime_percentage = (healthy_checks / total_checks) * 100 if total_checks > 0 else 0
        
        return {
            'service_name': service_name,
            'period_hours': hours,
            'total_checks': total_checks,
            'healthy_checks': healthy_checks,
            'uptime_percentage': round(uptime_percentage, 2),
            'last_check': relevant_checks[-1]['timestamp'] if relevant_checks else None
        }

# Global health check manager
health_manager = HealthCheckManager()

# Convenience functions
async def check_all_services() -> Dict[str, Any]:
    """Check all services and return overall health"""
    return await health_manager.get_overall_health()

async def check_service(service_name: str) -> Optional[Dict[str, Any]]:
    """Check specific service"""
    result = await health_manager.check_service(service_name)
    return result.to_dict() if result else None

def get_health_history(limit: int = 10) -> List[Dict[str, Any]]:
    """Get health check history"""
    return health_manager.get_health_history(limit)

def get_service_uptime(service_name: str, hours: int = 24) -> Dict[str, Any]:
    """Get service uptime statistics"""
    return health_manager.get_service_uptime(service_name, hours)