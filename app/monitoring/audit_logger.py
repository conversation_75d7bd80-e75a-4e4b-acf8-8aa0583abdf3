"""
Comprehensive audit logging system with tamper protection.

This module provides:
- Immutable audit trail for all system operations
- Structured logging with standardized format
- Cryptographic integrity protection
- Real-time and batch log analysis
- Integration with monitoring systems
- Compliance reporting capabilities
"""

import json
import hashlib
import hmac
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import asyncio
from dataclasses import dataclass, asdict
import ipaddress

from pydantic import BaseModel, Field
from fastapi import Request

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.cache import get_cache

logger = logging.getLogger(__name__)
settings = get_settings()


class AuditLevel(str, Enum):
    """Audit event severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AuditCategory(str, Enum):
    """Audit event categories."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    SYSTEM_CONFIGURATION = "system_configuration"
    SECURITY_EVENT = "security_event"
    COMPLIANCE = "compliance"
    USER_ACTION = "user_action"
    API_ACCESS = "api_access"
    ADMIN_ACTION = "admin_action"


@dataclass
class AuditEvent:
    """Structured audit event."""
    event_id: str
    timestamp: datetime
    event_type: str
    category: AuditCategory
    level: AuditLevel
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource: Optional[str]
    action: str
    details: Dict[str, Any]
    outcome: str  # success, failure, partial
    risk_score: int  # 0-100
    
    # Integrity protection
    signature: Optional[str] = None
    sequence_number: Optional[int] = None
    
    # Context
    request_id: Optional[str] = None
    trace_id: Optional[str] = None
    
    # Compliance
    retention_days: int = 2555  # 7 years default
    data_classification: str = "internal"


class ComplianceReport(BaseModel):
    """Compliance audit report."""
    report_id: str
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    period_start: datetime
    period_end: datetime
    report_type: str
    
    # Statistics
    total_events: int
    events_by_category: Dict[str, int]
    events_by_user: Dict[str, int]
    security_events: int
    failed_authentications: int
    
    # Findings
    anomalies: List[Dict[str, Any]]
    policy_violations: List[Dict[str, Any]]
    recommendations: List[str]


class AuditLogger:
    """
    Production-grade audit logging system.
    
    Features:
    - Immutable audit trail with cryptographic integrity
    - Real-time security event detection
    - Compliance reporting and analytics
    - Tamper detection and alerting
    - High-performance async logging
    - Integration with monitoring systems
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.cache = None
        self.sequence_counter = 0
        self.batch_size = 100
        self.batch_timeout_seconds = 30
        self.pending_events: List[AuditEvent] = []
        self._lock = asyncio.Lock()
        
        # Risk scoring weights
        self.risk_weights = {
            "failed_authentication": 20,
            "privilege_escalation": 90,
            "data_export": 40,
            "admin_action": 30,
            "security_violation": 80,
            "multiple_failures": 50,
            "suspicious_ip": 60,
            "after_hours_access": 15
        }
        
        # Start background batch processor
        asyncio.create_task(self._batch_processor())
    
    async def initialize(self):
        """Initialize audit logger."""
        self.cache = await get_cache()
        
        # Get current sequence number from database
        try:
            response = await self.supabase.table("audit_events").select("sequence_number").order("sequence_number", desc=True).limit(1).execute()
            
            if response.data:
                self.sequence_counter = response.data[0]["sequence_number"] + 1
            else:
                self.sequence_counter = 1
                
        except Exception as e:
            logger.error(f"Failed to initialize audit sequence counter: {e}")
            self.sequence_counter = 1
    
    async def log_event(
        self,
        event_type: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        request: Optional[Request] = None,
        category: AuditCategory = AuditCategory.USER_ACTION,
        level: AuditLevel = AuditLevel.INFO,
        resource: Optional[str] = None,
        outcome: str = "success"
    ) -> str:
        """Log an audit event."""
        
        # Generate event ID
        event_id = self._generate_event_id()
        
        # Extract request context
        ip_address = None
        user_agent = None
        request_id = None
        
        if request:
            ip_address = self._get_client_ip(request)
            user_agent = request.headers.get("User-Agent")
            request_id = getattr(request.state, "request_id", None)
        
        # Calculate risk score
        risk_score = await self._calculate_risk_score(
            event_type, details, user_id, ip_address, outcome
        )
        
        # Create audit event
        event = AuditEvent(
            event_id=event_id,
            timestamp=datetime.utcnow(),
            event_type=event_type,
            category=category,
            level=level,
            user_id=user_id,
            session_id=getattr(request.state, "session_id", None) if request else None,
            ip_address=ip_address,
            user_agent=user_agent,
            resource=resource,
            action=details.get("action", event_type),
            details=details,
            outcome=outcome,
            risk_score=risk_score,
            request_id=request_id,
            sequence_number=await self._get_next_sequence()
        )
        
        # Add cryptographic signature
        event.signature = self._sign_event(event)
        
        # Add to batch for processing
        async with self._lock:
            self.pending_events.append(event)
        
        # Process immediately for high-risk events
        if risk_score >= 70 or level in [AuditLevel.ERROR, AuditLevel.CRITICAL]:
            await self._process_immediate(event)
        
        # Real-time security analysis
        await self._analyze_security_event(event)
        
        return event_id
    
    async def query_events(
        self,
        user_id: Optional[str] = None,
        event_type: Optional[str] = None,
        category: Optional[AuditCategory] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AuditEvent]:
        """Query audit events with filters."""
        
        try:
            query = self.supabase.table("audit_events").select("*")
            
            if user_id:
                query = query.eq("user_id", user_id)
            
            if event_type:
                query = query.eq("event_type", event_type)
            
            if category:
                query = query.eq("category", category.value)
            
            if start_time:
                query = query.gte("timestamp", start_time.isoformat())
            
            if end_time:
                query = query.lte("timestamp", end_time.isoformat())
            
            query = query.order("timestamp", desc=True).limit(limit).offset(offset)
            
            response = await query.execute()
            
            events = []
            for data in response.data:
                # Parse datetime field
                data["timestamp"] = datetime.fromisoformat(data["timestamp"])
                
                # Parse JSON fields
                data["details"] = json.loads(data.get("details", "{}"))
                
                # Convert to enum types
                data["category"] = AuditCategory(data["category"])
                data["level"] = AuditLevel(data["level"])
                
                events.append(AuditEvent(**data))
            
            return events
            
        except Exception as e:
            logger.error(f"Failed to query audit events: {e}")
            return []
    
    async def generate_compliance_report(
        self,
        start_date: datetime,
        end_date: datetime,
        report_type: str = "general"
    ) -> ComplianceReport:
        """Generate compliance audit report."""
        
        # Query events for period
        events = await self.query_events(
            start_time=start_date,
            end_time=end_date,
            limit=10000  # Large limit for reporting
        )
        
        # Analyze events
        events_by_category = {}
        events_by_user = {}
        security_events = 0
        failed_authentications = 0
        
        for event in events:
            # Count by category
            category_name = event.category.value
            events_by_category[category_name] = events_by_category.get(category_name, 0) + 1
            
            # Count by user
            if event.user_id:
                events_by_user[event.user_id] = events_by_user.get(event.user_id, 0) + 1
            
            # Count security events
            if event.category == AuditCategory.SECURITY_EVENT:
                security_events += 1
            
            # Count failed authentications
            if event.event_type == "authentication_failed":
                failed_authentications += 1
        
        # Detect anomalies
        anomalies = await self._detect_anomalies(events)
        
        # Check policy violations
        policy_violations = await self._check_policy_violations(events)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(events, anomalies, policy_violations)
        
        report = ComplianceReport(
            report_id=self._generate_event_id(),
            period_start=start_date,
            period_end=end_date,
            report_type=report_type,
            total_events=len(events),
            events_by_category=events_by_category,
            events_by_user=events_by_user,
            security_events=security_events,
            failed_authentications=failed_authentications,
            anomalies=anomalies,
            policy_violations=policy_violations,
            recommendations=recommendations
        )
        
        # Store report
        await self._store_compliance_report(report)
        
        return report
    
    async def verify_integrity(self, event_id: str) -> bool:
        """Verify the cryptographic integrity of an audit event."""
        
        try:
            response = await self.supabase.table("audit_events").select("*").eq("event_id", event_id).execute()
            
            if not response.data:
                return False
            
            data = response.data[0]
            
            # Parse the event
            data["timestamp"] = datetime.fromisoformat(data["timestamp"])
            data["details"] = json.loads(data.get("details", "{}"))
            data["category"] = AuditCategory(data["category"])
            data["level"] = AuditLevel(data["level"])
            
            event = AuditEvent(**data)
            
            # Verify signature
            expected_signature = self._sign_event(event, exclude_signature=True)
            
            return hmac.compare_digest(event.signature or "", expected_signature)
            
        except Exception as e:
            logger.error(f"Failed to verify event integrity: {e}")
            return False
    
    async def _batch_processor(self):
        """Background task to process audit events in batches."""
        while True:
            try:
                await asyncio.sleep(self.batch_timeout_seconds)
                
                async with self._lock:
                    if self.pending_events:
                        events_to_process = self.pending_events[:self.batch_size]
                        self.pending_events = self.pending_events[self.batch_size:]
                    else:
                        events_to_process = []
                
                if events_to_process:
                    await self._store_events_batch(events_to_process)
                
            except Exception as e:
                logger.error(f"Error in audit batch processor: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _store_events_batch(self, events: List[AuditEvent]):
        """Store a batch of audit events."""
        
        try:
            # Convert events to database format
            event_data = []
            for event in events:
                data = asdict(event)
                data["timestamp"] = event.timestamp.isoformat()
                data["details"] = json.dumps(event.details)
                data["category"] = event.category.value
                data["level"] = event.level.value
                event_data.append(data)
            
            # Batch insert
            await self.supabase.table("audit_events").insert(event_data).execute()
            
            logger.info(f"Stored {len(events)} audit events")
            
        except Exception as e:
            logger.error(f"Failed to store audit events batch: {e}")
            # Could implement retry logic here
    
    async def _process_immediate(self, event: AuditEvent):
        """Process high-priority events immediately."""
        
        try:
            # Store immediately
            data = asdict(event)
            data["timestamp"] = event.timestamp.isoformat()
            data["details"] = json.dumps(event.details)
            data["category"] = event.category.value
            data["level"] = event.level.value
            
            await self.supabase.table("audit_events").insert(data).execute()
            
            # Send alerts for critical events
            if event.level == AuditLevel.CRITICAL or event.risk_score >= 90:
                await self._send_security_alert(event)
            
        except Exception as e:
            logger.error(f"Failed to process immediate audit event: {e}")
    
    async def _analyze_security_event(self, event: AuditEvent):
        """Real-time security event analysis."""
        
        # Check for suspicious patterns
        if event.risk_score >= 60:
            await self._check_suspicious_patterns(event)
        
        # Check for brute force attacks
        if event.event_type == "authentication_failed":
            await self._check_brute_force(event)
        
        # Check for privilege escalation
        if "privilege" in event.event_type or "admin" in event.event_type:
            await self._check_privilege_escalation(event)
    
    async def _calculate_risk_score(
        self,
        event_type: str,
        details: Dict[str, Any],
        user_id: Optional[str],
        ip_address: Optional[str],
        outcome: str
    ) -> int:
        """Calculate risk score for an event."""
        
        score = 0
        
        # Base score by event type
        if "failed" in event_type or outcome == "failure":
            score += self.risk_weights.get("failed_authentication", 20)
        
        if "admin" in event_type:
            score += self.risk_weights.get("admin_action", 30)
        
        if "export" in event_type or "download" in event_type:
            score += self.risk_weights.get("data_export", 40)
        
        # IP-based scoring
        if ip_address:
            if await self._is_suspicious_ip(ip_address):
                score += self.risk_weights.get("suspicious_ip", 60)
        
        # Time-based scoring
        current_hour = datetime.utcnow().hour
        if current_hour < 6 or current_hour > 22:  # After hours
            score += self.risk_weights.get("after_hours_access", 15)
        
        # User-based scoring
        if user_id:
            recent_failures = await self._get_recent_failures(user_id)
            if recent_failures >= 3:
                score += self.risk_weights.get("multiple_failures", 50)
        
        return min(score, 100)  # Cap at 100
    
    def _generate_event_id(self) -> str:
        """Generate unique event ID."""
        import uuid
        return str(uuid.uuid4())
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def _get_next_sequence(self) -> int:
        """Get next sequence number."""
        async with self._lock:
            self.sequence_counter += 1
            return self.sequence_counter
    
    def _sign_event(self, event: AuditEvent, exclude_signature: bool = False) -> str:
        """Generate cryptographic signature for event."""
        
        # Create canonical representation
        event_dict = asdict(event)
        
        if exclude_signature:
            event_dict.pop("signature", None)
        
        # Convert to deterministic JSON
        canonical_json = json.dumps(event_dict, sort_keys=True, separators=(',', ':'))
        
        # Generate HMAC signature
        signature = hmac.new(
            settings.secret_key.encode(),
            canonical_json.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    async def _is_suspicious_ip(self, ip_address: str) -> bool:
        """Check if IP address is suspicious."""
        try:
            # Check if it's a private IP
            ip = ipaddress.ip_address(ip_address)
            if ip.is_private:
                return False
            
            # Check cache for known suspicious IPs
            if self.cache:
                is_suspicious = await self.cache.get(f"suspicious_ip:{ip_address}")
                if is_suspicious is not None:
                    return is_suspicious
            
            # Check against threat intelligence (simplified)
            # In production, you'd integrate with threat intel feeds
            suspicious = False
            
            # Cache result
            if self.cache:
                await self.cache.set(f"suspicious_ip:{ip_address}", suspicious, ttl=3600)
            
            return suspicious
            
        except Exception:
            return False
    
    async def _get_recent_failures(self, user_id: str, minutes: int = 30) -> int:
        """Get count of recent authentication failures for user."""
        
        since_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        try:
            response = await self.supabase.table("audit_events").select("id").eq("user_id", user_id).eq("event_type", "authentication_failed").gte("timestamp", since_time.isoformat()).execute()
            
            return len(response.data)
            
        except Exception:
            return 0
    
    async def _check_suspicious_patterns(self, event: AuditEvent):
        """Check for suspicious behavioral patterns."""
        # Implementation would analyze patterns in user behavior
        pass
    
    async def _check_brute_force(self, event: AuditEvent):
        """Check for brute force attack patterns."""
        if event.user_id:
            recent_failures = await self._get_recent_failures(event.user_id, 10)
            if recent_failures >= 5:
                await self._send_security_alert(event, "Potential brute force attack detected")
    
    async def _check_privilege_escalation(self, event: AuditEvent):
        """Check for privilege escalation attempts."""
        # Implementation would check for unauthorized privilege changes
        pass
    
    async def _send_security_alert(self, event: AuditEvent, message: str = "High-risk security event"):
        """Send security alert for critical events."""
        logger.critical(f"SECURITY ALERT: {message} - Event: {event.event_id}")
        # In production, integrate with alerting systems (Slack, email, etc.)
    
    async def _detect_anomalies(self, events: List[AuditEvent]) -> List[Dict[str, Any]]:
        """Detect anomalies in audit events."""
        anomalies = []
        
        # Simple anomaly detection patterns
        user_activity = {}
        for event in events:
            if event.user_id:
                if event.user_id not in user_activity:
                    user_activity[event.user_id] = []
                user_activity[event.user_id].append(event)
        
        # Check for unusual activity patterns
        for user_id, user_events in user_activity.items():
            if len(user_events) > 1000:  # Unusually high activity
                anomalies.append({
                    "type": "high_activity",
                    "user_id": user_id,
                    "event_count": len(user_events),
                    "description": f"User {user_id} had {len(user_events)} events in period"
                })
        
        return anomalies
    
    async def _check_policy_violations(self, events: List[AuditEvent]) -> List[Dict[str, Any]]:
        """Check for policy violations."""
        violations = []
        
        # Check for after-hours admin access
        for event in events:
            if "admin" in event.event_type:
                hour = event.timestamp.hour
                if hour < 6 or hour > 22:
                    violations.append({
                        "type": "after_hours_admin_access",
                        "event_id": event.event_id,
                        "user_id": event.user_id,
                        "timestamp": event.timestamp.isoformat(),
                        "description": "Administrative access outside business hours"
                    })
        
        return violations
    
    async def _generate_recommendations(
        self,
        events: List[AuditEvent],
        anomalies: List[Dict[str, Any]],
        violations: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate security recommendations."""
        recommendations = []
        
        if violations:
            recommendations.append("Review and strengthen access control policies")
        
        if any(a["type"] == "high_activity" for a in anomalies):
            recommendations.append("Implement stricter rate limiting for high-activity users")
        
        # Count failed authentications
        failed_auths = sum(1 for e in events if e.event_type == "authentication_failed")
        if failed_auths > 100:
            recommendations.append("Consider implementing CAPTCHA or account lockout policies")
        
        if not recommendations:
            recommendations.append("No immediate security concerns identified")
        
        return recommendations
    
    async def _store_compliance_report(self, report: ComplianceReport):
        """Store compliance report in database."""
        try:
            await self.supabase.table("compliance_reports").insert({
                "report_id": report.report_id,
                "generated_at": report.generated_at.isoformat(),
                "period_start": report.period_start.isoformat(),
                "period_end": report.period_end.isoformat(),
                "report_type": report.report_type,
                "total_events": report.total_events,
                "events_by_category": json.dumps(report.events_by_category),
                "events_by_user": json.dumps(report.events_by_user),
                "security_events": report.security_events,
                "failed_authentications": report.failed_authentications,
                "anomalies": json.dumps(report.anomalies),
                "policy_violations": json.dumps(report.policy_violations),
                "recommendations": json.dumps(report.recommendations)
            }).execute()
        except Exception as e:
            logger.error(f"Failed to store compliance report: {e}")


# Global audit logger
_audit_logger: Optional[AuditLogger] = None


async def get_audit_logger() -> AuditLogger:
    """Get or create audit logger."""
    global _audit_logger
    
    if _audit_logger is None:
        _audit_logger = AuditLogger()
        await _audit_logger.initialize()
    
    return _audit_logger


# Convenience function for logging
async def audit_log(
    event_type: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None,
    request: Optional[Request] = None,
    **kwargs
) -> str:
    """Log an audit event (convenience function)."""
    audit_logger = await get_audit_logger()
    return await audit_logger.log_event(
        event_type=event_type,
        details=details,
        user_id=user_id,
        request=request,
        **kwargs
    )


# Decorator for automatic audit logging
def audit_action(event_type: str, category: AuditCategory = AuditCategory.USER_ACTION):
    """Decorator to automatically audit function calls."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                
                await audit_log(
                    event_type=event_type,
                    details={
                        "function": func.__name__,
                        "args": str(args),
                        "kwargs": str(kwargs)
                    },
                    category=category,
                    outcome="success"
                )
                
                return result
                
            except Exception as e:
                await audit_log(
                    event_type=event_type,
                    details={
                        "function": func.__name__,
                        "error": str(e)
                    },
                    category=category,
                    level=AuditLevel.ERROR,
                    outcome="failure"
                )
                raise
        
        return wrapper
    return decorator
