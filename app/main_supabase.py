# app/main_supabase.py - FastAPI Application with Full Supabase Integration

import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

# Supabase database and auth imports
from app.utils.supabase.supabase_database import init_database, cleanup_database, db
from app.auth.supabase_auth import auth_service

# API routers - New Supabase versions
from app.api import (
    supabase_auth,
    supabase_books,
    users,
    trends,
    publications,
    monitoring,
    predictions,
    analytics,
    feedback,
    agents,
    cache,
    security,
    compliance,
    market_analysis,
    prediction_accuracy,
)

# VERL integration imports (preserved)
from app.ml.verl_integration import (
    verl_integration,
    initialize_verl,
    verl_background_monitor,
)

# Configuration
from app.config import settings

# Monitoring setup
from app.monitoring.monitoring_setup import (
    initialize_monitoring,
    get_logger,
    monitor_operation,
)

# Cache setup
from app.middleware.cache_middleware import APICacheMiddleware
from app.cache import get_cache, get_invalidation_manager, get_cdn_manager

# Initialize monitoring systems - will be done in lifespan
monitoring_status = None

# Setup logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper(), logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Enhanced lifespan management with Supabase integration"""

    # === STARTUP ===
    logger.info("🚀 Starting AI E-book Generator with Supabase...")

    try:
        # Validate critical configuration first
        logger.info("⚙️ Validating configuration...")
        config_issues = []
        
        if not settings.supabase_url:
            config_issues.append("SUPABASE_URL not set")
        if not settings.supabase_service_key:
            config_issues.append("SUPABASE_SERVICE_KEY not set")
        if not settings.secret_key:
            config_issues.append("SECRET_KEY not set")
            
        if config_issues:
            logger.warning(f"⚠️ Configuration issues detected: {', '.join(config_issues)}")
            logger.warning("Some features may not work properly")
        else:
            logger.info("✅ Critical configuration validated")
        
        # Initialize monitoring systems
        logger.info("🔍 Initializing monitoring systems...")  
        global monitoring_status
        monitoring_status = await initialize_monitoring()
        logger.info("✅ Monitoring systems initialized")
        
        # Initialize Supabase database
        logger.info("🗄️ Initializing Supabase database connection...")
        db_success = await init_database()
        
        if not db_success:
            logger.error("❌ Database initialization failed")
            raise Exception("Database initialization failed")
        
        # Initialize cache systems
        logger.info("💾 Initializing cache systems...")
        cache = await get_cache()
        invalidation_manager = await get_invalidation_manager()
        cdn_manager = await get_cdn_manager()
        logger.info("✅ Cache systems initialized")
        
        # Validate schema
        health = await db.health_check()
        if health["status"] != "healthy":
            logger.warning(f"⚠️ Database health check warning: {health}")
        
        schema_validation = await db.validate_schema()
        if not schema_validation["schema_valid"]:
            logger.warning(f"⚠️ Missing database tables: {schema_validation['missing_tables']}")
            logger.info("Please run the Supabase schema migration script")
        
        logger.info("✅ Supabase database initialized successfully")

        # Initialize VERL integration if enabled
        if getattr(settings, 'enable_verl', False):
            logger.info("🧠 Initializing VERL integration...")
            try:
                await initialize_verl()
                # Start background VERL monitoring
                asyncio.create_task(verl_background_monitor())
                logger.info("✅ VERL integration initialized successfully")
            except Exception as e:
                logger.warning(f"⚠️ VERL initialization failed: {e}")
                logger.info("Continuing without VERL features...")
        else:
            logger.info("⚠️ VERL integration disabled in settings")

        logger.info("🎉 Application startup complete!")

    except Exception as e:
        logger.error(f"❌ Failed to initialize application: {e}")
        raise

    yield

    # === SHUTDOWN ===
    logger.info("👋 Shutting down AI E-book Generator...")

    try:
        # Clean shutdown of database connections
        await cleanup_database()
        
        # Clean shutdown of cache systems
        cache = await get_cache()
        await cache.close()
        logger.info("✅ Cache systems cleaned up")
        
        # Clean shutdown of VERL integration
        if hasattr(verl_integration, 'redis_client') and verl_integration.redis_client:
            await verl_integration.redis_client.close()
            logger.info("✅ VERL integration cleaned up")
            
        logger.info("✅ Application shutdown complete")
        
    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")

# Create FastAPI app
app = FastAPI(
    title="AI E-book Generator with Supabase",
    description="AI-powered e-book generation and publishing system with Supabase backend and reinforcement learning",
    version="3.0.0",  # Updated version for Supabase migration
    lifespan=lifespan,
    redirect_slashes=False,
)

# Security and monitoring middleware (order matters - security first)
from app.middleware.security_middleware import (
    SecurityMiddleware,
    InputValidationMiddleware,
    CORSSecurityMiddleware,
    get_limiter,
)
from app.middleware.monitoring_middleware import (
    MonitoringMiddleware,
    PerformanceMonitoringMiddleware,
    SecurityMonitoringMiddleware,
)

# Add rate limiter to app state
limiter = get_limiter()
app.state.limiter = limiter

# Security middleware (first layer)
app.add_middleware(SecurityMiddleware)
app.add_middleware(InputValidationMiddleware)

# Cache middleware (before monitoring for accurate performance metrics)
app.add_middleware(APICacheMiddleware)

# Monitoring middleware
app.add_middleware(SecurityMonitoringMiddleware)
app.add_middleware(PerformanceMonitoringMiddleware, slow_request_threshold=2.0)
app.add_middleware(MonitoringMiddleware)

# CORS middleware (development-friendly configuration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
        "http://localhost:3002",
        "http://127.0.0.1:3002",
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-Request-ID",
    ],
    expose_headers=["X-Request-ID"],
)

# =====================================
# INCLUDE API ROUTERS
# =====================================

# Authentication - New Supabase Auth
app.include_router(supabase_auth.router, prefix="/api/auth", tags=["authentication"])

# Books - New Supabase Books API
app.include_router(supabase_books.router, prefix="/api/books", tags=["books"])

# Users - User Settings and Preferences API
app.include_router(users.router, prefix="/api/users", tags=["users"])

# Other APIs (updated to work with Supabase)
if trends and hasattr(trends, 'router'):
    app.include_router(trends.router, prefix="/api/trends", tags=["trends"])
if publications and hasattr(publications, 'router'):
    app.include_router(publications.router, prefix="/api/publications", tags=["publications"])
if monitoring and hasattr(monitoring, 'router'):
    app.include_router(monitoring.router, prefix="/api/monitoring", tags=["monitoring"])
if predictions and hasattr(predictions, 'router'):
    app.include_router(predictions.router, prefix="/api/predictions", tags=["predictions"])
if analytics and hasattr(analytics, 'router'):
    app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])
if feedback and hasattr(feedback, 'router'):
    app.include_router(feedback.router, prefix="/api/feedback", tags=["feedback"])
if agents and hasattr(agents, 'router'):
    app.include_router(agents.router, prefix="/api/agents", tags=["agents"])
if cache and hasattr(cache, 'router'):
    app.include_router(cache.router, tags=["cache"])
if security and hasattr(security, 'router'):
    app.include_router(security.router, tags=["security"])
if compliance and hasattr(compliance, 'router'):
    app.include_router(compliance.router, prefix="/api/compliance", tags=["compliance"])

# Business Intelligence APIs
if market_analysis and hasattr(market_analysis, "router"):
    app.include_router(
        market_analysis.router, prefix="/api/market-analysis", tags=["market-analysis"]
    )
if prediction_accuracy and hasattr(prediction_accuracy, "router"):
    app.include_router(
        prediction_accuracy.router,
        prefix="/api/predictions/accuracy",
        tags=["prediction-accuracy"],
    )

# =====================================
# VERL-SPECIFIC ENDPOINTS (Enhanced)
# =====================================

@app.get("/api/verl/health")
async def verl_health():
    """Check VERL service health status"""
    if not getattr(settings, 'enable_verl', False):
        return {"status": "disabled", "message": "VERL integration is disabled"}

    try:
        health_status = await verl_integration.health_check()
        return health_status
    except Exception as e:
        logger.error(f"VERL health check failed: {e}")
        raise HTTPException(status_code=500, detail="VERL service unavailable")

@app.get("/api/verl/training/{training_id}")
async def get_verl_training_status(training_id: str):
    """Get VERL training job status"""
    if not getattr(settings, 'enable_verl', False):
        raise HTTPException(status_code=404, detail="VERL integration disabled")

    try:
        status = await verl_integration.get_training_status(training_id)
        if not status:
            raise HTTPException(status_code=404, detail="Training job not found")
        return status
    except Exception as e:
        logger.error(f"Failed to get training status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get training status")

@app.post("/api/verl/trigger-training")
async def trigger_verl_training():
    """Manually trigger VERL training"""
    if not getattr(settings, 'enable_verl', False):
        raise HTTPException(status_code=404, detail="VERL integration disabled")

    try:
        # Trigger training logic would go here
        return {
            "message": "VERL training triggered successfully",
            "status": "queued"
        }
    except Exception as e:
        logger.error(f"Failed to trigger training: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger training")

@app.get("/api/verl/stats")
async def get_verl_stats():
    """Get VERL system statistics"""
    if not getattr(settings, 'enable_verl', False):
        return {"verl_enabled": False}

    try:
        # Get VERL service health
        health = await verl_integration.health_check()

        return {
            "verl_enabled": True,
            "service_status": health.get("status", "unknown"),
            "gpu_available": health.get("gpu_available", False),
            "gpu_count": health.get("gpu_count", 0),
            "last_check": health.get("timestamp"),
            "database_backend": "supabase"
        }

    except Exception as e:
        logger.error(f"Failed to get VERL stats: {e}")
        return {
            "verl_enabled": True,
            "service_status": "error",
            "error": str(e),
            "database_backend": "supabase"
        }

# =====================================
# SYSTEM ENDPOINTS (Enhanced)
# =====================================

@app.get("/")
async def root():
    """Root endpoint with enhanced Supabase status"""
    base_info = {
        "message": "AI E-book Generator with Supabase",
        "version": "3.0.0",
        "database_backend": "supabase",
        "verl_enabled": getattr(settings, 'enable_verl', False)
    }

    # Add database status
    try:
        health = await db.health_check()
        base_info["database_status"] = health.get("status", "unknown")
        base_info["database_connected"] = health.get("supabase_connected", False)
    except:
        base_info["database_status"] = "unavailable"
        base_info["database_connected"] = False

    # Add VERL status if enabled
    if getattr(settings, 'enable_verl', False):
        try:
            verl_health = await verl_integration.health_check()
            base_info["verl_status"] = verl_health.get("status", "unknown")
        except:
            base_info["verl_status"] = "unavailable"

    return base_info

@app.get("/health")
async def health_check():
    """Enhanced health check with Supabase status"""
    health_status = {
        "status": "healthy",
        "api_version": "3.0.0",
        "database_backend": "supabase",
        "verl_enabled": getattr(settings, 'enable_verl', False)
    }

    # Database health
    try:
        db_health = await db.health_check()
        health_status["database_status"] = db_health.get("status", "unknown")
        health_status["database_connected"] = db_health.get("supabase_connected", False)
        health_status["user_count"] = db_health.get("user_count", 0)
        
        # Schema validation
        schema_validation = await db.validate_schema()
        health_status["schema_valid"] = schema_validation["schema_valid"]
        if not schema_validation["schema_valid"]:
            health_status["missing_tables"] = schema_validation["missing_tables"]
            
    except Exception as e:
        health_status["database_status"] = "error"
        health_status["database_error"] = str(e)

    # VERL health if enabled
    if getattr(settings, 'enable_verl', False):
        try:
            verl_health = await verl_integration.health_check()
            health_status["verl_status"] = verl_health.get("status", "unknown")
            health_status["verl_gpu_available"] = verl_health.get("gpu_available", False)
        except Exception as e:
            health_status["verl_status"] = "error"
            health_status["verl_error"] = str(e)

    # Configuration validation
    config_validation = settings.validate_configuration()
    health_status["config_valid"] = config_validation["valid"]
    if not config_validation["valid"]:
        health_status["config_issues"] = config_validation["issues"]

    return health_status

@app.get("/sentry-debug")
async def trigger_error():
    """Debug endpoint for Sentry testing"""
    division_by_zero = 1 / 0

# =====================================
# MIGRATION AND SETUP ENDPOINTS
# =====================================

@app.get("/api/setup/validate")
async def validate_setup():
    """Validate Supabase setup and configuration"""
    try:
        # Database validation
        db_health = await db.health_check()
        schema_validation = await db.validate_schema()
        
        # Configuration validation
        config_validation = settings.validate_configuration()
        
        return {
            "database": {
                "connected": db_health.get("supabase_connected", False),
                "status": db_health.get("status", "unknown"),
                "schema_valid": schema_validation["schema_valid"],
                "missing_tables": schema_validation.get("missing_tables", [])
            },
            "configuration": {
                "valid": config_validation["valid"],
                "issues": config_validation.get("issues", []),
                "warnings": config_validation.get("warnings", [])
            },
            "next_steps": []
        }
        
    except Exception as e:
        logger.error(f"Setup validation error: {e}")
        return {
            "database": {"connected": False, "error": str(e)},
            "configuration": {"valid": False, "error": str(e)},
            "next_steps": [
                "Check Supabase credentials in environment variables",
                "Run schema migration script",
                "Verify network connectivity to Supabase"
            ]
        }

@app.post("/api/setup/migrate-schema")
async def migrate_schema():
    """Trigger schema migration check"""
    try:
        validation = await db.validate_schema()
        
        if validation["schema_valid"]:
            return {
                "success": True,
                "message": "Schema is already up to date",
                "existing_tables": validation["existing_tables"]
            }
        else:
            return {
                "success": False,
                "message": "Schema migration needed",
                "missing_tables": validation["missing_tables"],
                "instructions": [
                    "1. Go to your Supabase dashboard",
                    "2. Navigate to SQL Editor",
                    "3. Run the SQL script from supabase_schema.sql",
                    "4. Restart this application"
                ]
            }
            
    except Exception as e:
        logger.error(f"Schema migration check error: {e}")
        return {
            "success": False,
            "error": str(e),
            "instructions": [
                "Check database connection",
                "Verify Supabase credentials",
                "Check network connectivity"
            ]
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main_supabase:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.log_level.lower()
    )
