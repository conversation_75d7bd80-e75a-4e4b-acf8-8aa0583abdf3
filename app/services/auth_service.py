### app/services/auth_service.py - Authentication Service

from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone
import bcrypt
import jwt
import logging
from cryptography.fernet import Fernet
import base64
import os

from app.models.supabase_models import get_user_model
from app.schemas.user import UserCreate, UserResponse, UserLogin, KDPCredentials

logger = logging.getLogger(__name__)

class AuthService:
    """Service for managing authentication and user accounts"""
    
    def __init__(self):
        # JWT configuration
        self.jwt_secret = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this")
        self.jwt_algorithm = "HS256"
        self.jwt_expiration_hours = 24
        
        # Encryption for sensitive data
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.user_model = get_user_model()
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data"""
        key_env = os.getenv("ENCRYPTION_KEY")
        if key_env:
            return base64.urlsafe_b64decode(key_env.encode())
        else:
            # Generate new key (in production, store this securely)
            key = Fernet.generate_key()
            logger.warning("Generated new encryption key. Store ENCRYPTION_KEY in environment variables.")
            return key
    
    async def register_user(self, user_data: UserCreate) -> Dict[str, Any]:
        """Register a new user"""
        
        # Check if user already exists
        existing_user = await self.user_model.get_user_by_email(user_data.email)
        
        if existing_user:
            return {
                "success": False,
                "error": "User with this email already exists"
            }
        
        # Hash password
        password_hash = self._hash_password(user_data.password)
        
        # Create user
        user = await self.user_model.create_user({
            "email": user_data.email,
            "password_hash": password_hash,
            "full_name": user_data.full_name,
            "subscription_tier": user_data.subscription_tier or "free",
            "is_active": True,
            "created_at": datetime.now(timezone.utc).isoformat()
        })
        
        # Generate JWT token
        token = self._generate_jwt_token(user['id'], user['email'])
        
        logger.info(f"Registered new user: {user['email']} (ID: {user['id']})")
        
        return {
            "success": True,
            "user": UserResponse.model_validate(user),
            "access_token": token,
            "token_type": "bearer"
        }
    
    async def login_user(self, login_data: UserLogin) -> Dict[str, Any]:
        """Authenticate user login"""
        
        user = await self.user_model.get_user_by_email(login_data.email)
        
        if not user:
            return {
                "success": False,
                "error": "Invalid email or password"
            }
        
        if not user['is_active']:
            return {
                "success": False,
                "error": "Account is deactivated"
            }
        
        # Verify password
        if not self._verify_password(login_data.password, user['password_hash']):
            return {
                "success": False,
                "error": "Invalid email or password"
            }
        
        # Update last login
        await self.user_model.update_user(user['id'], {"last_login_at": datetime.now(timezone.utc).isoformat()})

        # Generate JWT token
        token = self._generate_jwt_token(user['id'], user['email'])
        
        logger.info(f"User logged in: {user['email']}")
        
        return {
            "success": True,
            "user": UserResponse.model_validate(user),
            "access_token": token,
            "token_type": "bearer"
        }
    
    async def get_current_user(self, token: str) -> Optional[UserResponse]:
        """Get current user from JWT token"""
        
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            user_id = payload.get("user_id")
            
            if not user_id:
                return None
            
            user = await self.user_model.get_user(user_id)
            
            if user and user['is_active']:
                return UserResponse.model_validate(user)
                    
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
        
        return None
    
    async def change_password(
        self, 
        user_id: str, 
        current_password: str, 
        new_password: str
    ) -> Dict[str, Any]:
        """Change user password"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"success": False, "error": "User not found"}
        
        # Verify current password
        if not self._verify_password(current_password, user['password_hash']):
            return {"success": False, "error": "Current password is incorrect"}
        
        # Update password
        await self.user_model.update_user(user_id, {"password_hash": self._hash_password(new_password)})
        
        logger.info(f"Password changed for user {user['email']}")
        
        return {
            "success": True,
            "message": "Password changed successfully"
        }
    
    async def update_user_profile(
        self, 
        user_id: str, 
        profile_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update user profile information"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"success": False, "error": "User not found"}
        
        # Update allowed fields
        allowed_fields = ["full_name", "subscription_tier"]
        updated_fields = {k: v for k, v in profile_data.items() if k in allowed_fields}
        
        if updated_fields:
            await self.user_model.update_user(user_id, updated_fields)
            logger.info(f"Updated profile for user {user['email']}: {list(updated_fields.keys())}")
        
        updated_user = await self.user_model.get_user(user_id)
        return {
            "success": True,
            "user": UserResponse.model_validate(updated_user),
            "updated_fields": list(updated_fields.keys())
        }
    
    async def store_kdp_credentials(
        self, 
        user_id: str, 
        credentials: KDPCredentials
    ) -> Dict[str, Any]:
        """Store encrypted KDP credentials"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"success": False, "error": "User not found"}
        
        try:
            # Encrypt sensitive credentials
            encrypted_creds = {
                "email": self._encrypt_data(credentials.email),
                "password": self._encrypt_data(credentials.password),
                "two_factor_secret": self._encrypt_data(credentials.two_factor_secret) if credentials.two_factor_secret else None
            }
            
            # Update KDP credentials
            await self.user_model.update_user(user_id, {
                "kdp_credentials": encrypted_creds,
                "kdp_credentials_updated_at": datetime.now(timezone.utc).isoformat()
            })
            
            logger.info(f"Stored KDP credentials for user {user['email']}")
            
            return {
                "success": True,
                "message": "KDP credentials stored successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to encrypt KDP credentials: {str(e)}")
            return {"success": False, "error": "Failed to store credentials"}
    
    async def get_kdp_credentials(self, user_id: str) -> Optional[KDPCredentials]:
        """Retrieve and decrypt KDP credentials"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user or not user.get('kdp_credentials'):
            return None
        
        try:
            # Decrypt credentials
            encrypted_creds = user['kdp_credentials']
            
            credentials = KDPCredentials(
                email=self._decrypt_data(encrypted_creds["email"]),
                password=self._decrypt_data(encrypted_creds["password"]),
                two_factor_secret=self._decrypt_data(encrypted_creds["two_factor_secret"]) if encrypted_creds.get("two_factor_secret") else None
            )
            
            return credentials
            
        except Exception as e:
            logger.error(f"Failed to decrypt KDP credentials: {str(e)}")
            return None
    
    async def delete_kdp_credentials(self, user_id: str) -> Dict[str, Any]:
        """Delete stored KDP credentials"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"success": False, "error": "User not found"}
        
        # Delete KDP credentials
        await self.user_model.update_user(user_id, {"kdp_credentials": None, "kdp_credentials_updated_at": None})
        
        logger.info(f"Deleted KDP credentials for user {user['email']}")
        
        return {
            "success": True,
            "message": "KDP credentials deleted successfully"
        }
    
    async def deactivate_user(self, user_id: str) -> Dict[str, Any]:
        """Deactivate user account"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"success": False, "error": "User not found"}
        
        # Deactivate user
        await self.user_model.update_user(user_id, {"is_active": False})
        
        logger.info(f"Deactivated user account: {user['email']}")
        
        return {
            "success": True,
            "message": "Account deactivated successfully"
        }
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def _generate_jwt_token(self, user_id: str, email: str) -> str:
        """Generate JWT token"""
        payload = {
            "user_id": user_id,
            "email": email,
            "exp": datetime.now(timezone.utc) + timedelta(hours=self.jwt_expiration_hours),
            "iat": datetime.now(timezone.utc)
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.cipher_suite.encrypt(data.encode()).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    async def get_user_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get user account statistics"""
        
        user = await self.user_model.get_user(user_id)
        
        if not user:
            return {"error": "User not found"}
        
        # Get related statistics
        stats = await self.user_model.get_user_statistics(user_id)
        
        return {
            "user_id": user['id'],
            "email": user['email'],
            "full_name": user['full_name'],
            "subscription_tier": user['subscription_tier'],
            "account_created": user['created_at'],
            "last_login": user['last_login_at'],
            "total_books": stats.get('total_books', 0),
            "published_books": stats.get('published_books', 0),
            "total_publications": stats.get('total_publications', 0),
            "has_kdp_credentials": user.get('kdp_credentials') is not None
        }
