### app/services/book_service.py - Book Business Logic Service

from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
import asyncio
from pathlib import Path

from app.models.supabase_models import get_book_model, get_chapter_model
from app.schemas.book import BookCreate, BookResponse, ManuscriptGenerationRequest, Manuscript
from app.agents.pydantic_ai_manager import execute_agent
from app.utils.layout_designer import ProfessionalLayoutDesigner
from app.ml.feedback_integration import LiveFeedbackCollector

logger = logging.getLogger(__name__)

class BookService:
    """Service for managing book operations"""
    
    def __init__(self):
        # Using PydanticAI agents through manager
        self.layout_designer = ProfessionalLayoutDesigner()
        self.book_model = get_book_model()
        self.chapter_model = get_chapter_model()
    
    async def create_book(self, book_data: BookCreate, user_id: str) -> BookResponse:
        """Create a new book record"""
        
        book = await self.book_model.create_book({
            "user_id": user_id,
            "title": book_data.title,
            "category": book_data.category,
            "description": book_data.description,
            "status": "draft",
            "generation_config": {
                "target_audience": book_data.target_audience,
                "writing_style": book_data.writing_style,
                "ai_provider": book_data.ai_provider
            }
        })
        
        logger.info(f"Created new book: {book['title']} (ID: {book['id']})")
        return BookResponse.model_validate(book)
    
    async def get_user_books(self, user_id: str, skip: int = 0, limit: int = 100) -> List[BookResponse]:
        """Get all books for a user"""
        
        books = await self.book_model.get_user_books(user_id, skip, limit)
        return [BookResponse.model_validate(book) for book in books]
    
    async def get_book_by_id(self, book_id: str, user_id: str) -> Optional[BookResponse]:
        """Get a specific book by ID"""
        
        book = await self.book_model.get_book(book_id, user_id)
        if book:
            return BookResponse.model_validate(book)
        return None
    
    async def generate_manuscript(
        self, 
        request: ManuscriptGenerationRequest, 
        user_id: str
    ) -> Dict[str, Any]:
        """Generate a complete manuscript using AI"""
        
        try:
            # Create book record
            book_data = BookCreate(
                title=request.title or "AI Generated Book",
                category=request.category,
                target_audience=request.target_audience,
                writing_style=request.writing_style,
                ai_provider=request.ai_provider
            )
            
            book_response = await self.create_book(book_data, user_id)
            book_id = book_response.id
            
            # Update status to generating
            await self._update_book_status(book_id, "generating")
            
            # Generate manuscript using PydanticAI agent
            task_data = {
                'trend_data': request.trend_data,
                'style': request.writing_style,
                'target_audience': request.target_audience,
                'target_length': 8000,
                'output_formats': request.output_formats
            }
            result = await execute_agent("manuscript_generator", task_data, user_id)
            
            if result.success:
                manuscript_data = result.data['manuscript']
                
                # Update book with generated content
                await self._update_book_with_manuscript(book_id, manuscript_data, result.data)
                
                # Update status to awaiting approval
                await self._update_book_status(book_id, "awaiting_approval")
                
                logger.info(f"Successfully generated manuscript for book {book_id}")
                
                return {
                    "success": True,
                    "book_id": book_id,
                    "manuscript": manuscript_data,
                    "file_paths": result.data.get('file_paths', {}),
                    "formatted_files": result.data.get('formatted_files', {}),
                    "quality_score": result.data.get('quality_score', 0),
                    "word_count": result.data.get('word_count', 0),
                    "layout_theme": result.data.get('layout_theme'),
                    "status": "awaiting_approval"
                }
            else:
                # Update status to failed
                await self._update_book_status(book_id, "failed", result.error)
                
                return {
                    "success": False,
                    "book_id": book_id,
                    "error": result.error,
                    "status": "failed"
                }
                
        except Exception as e:
            logger.error(f"Manuscript generation failed: {str(e)}")
            if 'book_id' in locals():
                await self._update_book_status(book_id, "failed", str(e))
            
            return {
                "success": False,
                "error": str(e),
                "status": "failed"
            }
    
    async def approve_manuscript(self, book_id: str, user_id: str, approval_time: float = 0.0) -> Dict[str, Any]:
        """Approve a generated manuscript"""
        
        book = await self.book_model.get_book(book_id, user_id)
        
        if not book:
            return {"success": False, "error": "Book not found"}
        
        if book['status'] != "awaiting_approval":
            return {"success": False, "error": "Book is not ready for approval"}
        
        # Update book status
        await self.book_model.update_book(book_id, {"status": "approved", "approved_at": datetime.utcnow().isoformat()})
        
        # Capture feedback for VERL training
        feedback_collector = LiveFeedbackCollector()
        await feedback_collector.capture_approval_feedback(
            book_id=book_id,
            approved=True,
            approval_time_seconds=approval_time
        )
        
        logger.info(f"Approved manuscript for book {book_id}")
        
        return {
            "success": True,
            "book_id": book_id,
            "status": "approved",
            "message": "Manuscript approved successfully"
        }
    
    async def reject_manuscript(
        self, 
        book_id: str, 
        user_id: str, 
        reason: Optional[str] = None,
        rejection_time: float = 0.0
    ) -> Dict[str, Any]:
        """Reject a generated manuscript"""
        
        book = await self.book_model.get_book(book_id, user_id)
        
        if not book:
            return {"success": False, "error": "Book not found"}
        
        # Update book status
        await self.book_model.update_book(book_id, {"status": "rejected", "rejection_reason": reason})
        
        # Capture feedback for VERL training
        feedback_collector = LiveFeedbackCollector()
        await feedback_collector.capture_approval_feedback(
            book_id=book_id,
            approved=False,
            approval_time_seconds=rejection_time,
            rejection_reason=reason
        )
        
        logger.info(f"Rejected manuscript for book {book_id}: {reason}")
        
        return {
            "success": True,
            "book_id": book_id,
            "status": "rejected",
            "message": "Manuscript rejected",
            "reason": reason
        }
    
    async def regenerate_manuscript(
        self, 
        book_id: str, 
        user_id: str, 
        modifications: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Regenerate manuscript with optional modifications"""
        
        book = await self.book_model.get_book(book_id, user_id)
        
        if not book:
            return {"success": False, "error": "Book not found"}
        
        # Create modified generation request
        original_config = book.get('generation_config', {})
        modified_config = {**original_config, **(modifications or {})}
        
        request = ManuscriptGenerationRequest(
            title=book['title'],
            category=book['category'],
            target_audience=modified_config.get("target_audience", "general adults"),
            writing_style=modified_config.get("writing_style", "professional"),
            ai_provider=modified_config.get("ai_provider", "openai"),
            trend_data=modified_config.get("trend_data", {}),
            output_formats=modified_config.get("output_formats", ["docx", "epub", "pdf"]),
            layout_theme=modified_config.get("layout_theme")
        )
        
        # Reset book status
        await self.book_model.update_book(book_id, {"status": "generating", "approved_at": None, "rejection_reason": None})
        
        # Generate new manuscript
        return await self.generate_manuscript(request, user_id)
    
    async def delete_book(self, book_id: str, user_id: str) -> Dict[str, Any]:
        """Delete a book and its associated files"""
        
        book = await self.book_model.get_book(book_id, user_id)
        
        if not book:
            return {"success": False, "error": "Book not found"}
        
        # Delete associated files if they exist
        if book.get('manuscript_path'):
            try:
                manuscript_path = Path(book['manuscript_path'])
                if manuscript_path.exists():
                    manuscript_path.unlink()
            except Exception as e:
                logger.warning(f"Could not delete manuscript file: {e}")
        
        # Delete cover files
        if book.get('cover_paths'):
            for cover_path in book['cover_paths'].values():
                try:
                    cover_file = Path(cover_path)
                    if cover_file.exists():
                        cover_file.unlink()
                except Exception as e:
                    logger.warning(f"Could not delete cover file: {e}")
        
        # Delete book record (cascades to related records)
        await self.book_model.delete_book(book_id)
        
        logger.info(f"Deleted book {book_id}")
        
        return {
            "success": True,
            "book_id": book_id,
            "message": "Book deleted successfully"
        }
    
    async def get_book_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get statistics about user's books"""
        
        return await self.book_model.get_book_statistics(user_id)
    
    async def _update_book_status(self, book_id: str, status: str, error_message: Optional[str] = None):
        """Update book status"""
        
        update_data = {"status": status}
        if error_message:
            update_data["rejection_reason"] = error_message
        await self.book_model.update_book(book_id, update_data)
    
    async def _update_book_with_manuscript(
        self, 
        book_id: str, 
        manuscript_data: Dict[str, Any], 
        generation_result: Dict[str, Any]
    ):
        """Update book record with generated manuscript data"""
        
        # Update book metadata
        if isinstance(manuscript_data, dict):
            update_data = {
                "title": manuscript_data.get('title'),
                "word_count": manuscript_data.get('word_count', 0),
                "chapter_count": len(manuscript_data.get('chapters', [])),
                "quality_score": generation_result.get('quality_score', 0),
                "manuscript_path": generation_result.get('file_paths', {}).get('markdown'),
                "cover_paths": generation_result.get('formatted_files', {})
            }
        else:
            # Manuscript object
            update_data = {
                "title": manuscript_data.title,
                "word_count": manuscript_data.word_count,
                "chapter_count": len(manuscript_data.chapters),
                "quality_score": generation_result.get('quality_score', 0),
                "manuscript_path": generation_result.get('file_paths', {}).get('markdown'),
                "cover_paths": generation_result.get('formatted_files', {})
            }
        
        await self.book_model.update_book(book_id, {k: v for k, v in update_data.items() if v is not None})
        
        # Store chapters if it's a Manuscript object with chapters
        if hasattr(manuscript_data, 'chapters') and manuscript_data.chapters:
            # Clear existing chapters
            await self.chapter_model.delete_chapters_by_book(book_id)
            
            # Add new chapters
            for chapter_data in manuscript_data.chapters:
                await self.chapter_model.create_chapter({
                    "book_id": book_id,
                    "chapter_number": chapter_data.chapter_number,
                    "title": chapter_data.title,
                    "content": chapter_data.content,
                    "word_count": chapter_data.word_count
                })
