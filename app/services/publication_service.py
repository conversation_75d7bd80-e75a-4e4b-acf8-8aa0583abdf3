### app/services/publication_service.py - Publication Management Service

from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
import asyncio

from app.models.supabase_models import get_publication_model, get_book_model, get_sales_data_model
from app.schemas.book import PublicationRequest, PublicationResult
from app.agents.pydantic_ai_manager import execute_agent
from app.ml.feedback_integration import LiveFeedbackCollector

logger = logging.getLogger(__name__)

class PublicationService:
    """Service for managing book publications"""
    
    def __init__(self):
        # Using PydanticAI agents through manager
        self.publication_model = get_publication_model()
        self.book_model = get_book_model()
        self.sales_data_model = get_sales_data_model()
    
    async def publish_book(
        self, 
        book_id: str, 
        user_id: str, 
        publication_request: PublicationRequest
    ) -> Dict[str, Any]:
        """Publish a book to KDP"""
        
        # Verify book exists and is approved
        book = await self.book_model.get_book(book_id, user_id)
        
        if not book or book['status'] != "approved":
            return {
                "success": False,
                "error": "Book not found or not approved for publication"
            }
        
        # Create publication record
        publication_data = {
            "book_id": book_id,
            "status": "publishing",
            "price": publication_request.price,
            "royalty_rate": publication_request.royalty_rate,
            "publication_date": publication_request.publication_date.isoformat() if publication_request.publication_date else datetime.utcnow().isoformat(),
            "marketing_description": publication_request.marketing_description,
            "auto_publish": publication_request.auto_publish
        }
        
        publication = await self.publication_model.create_publication(publication_data)
        publication_id = publication['id']
        
        logger.info(f"Starting publication process for book {book_id}")
        
        # Start background publication
        asyncio.create_task(
            self._run_publication_process(publication_id, book, publication_request)
        )
        
        return {
            "success": True,
            "publication_id": publication_id,
            "book_id": book_id,
            "status": "publishing",
            "message": "Publication process started"
        }
    
    async def get_publication_status(
        self, 
        publication_id: str, 
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get publication status"""
        
        return await self.publication_model.get_publication(publication_id, user_id)
    
    async def get_user_publications(
        self, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get all publications for a user"""
        
        return await self.publication_model.get_user_publications(user_id, skip, limit)
    
    async def update_publication_price(
        self, 
        publication_id: str, 
        user_id: str, 
        new_price: float
    ) -> Dict[str, Any]:
        """Update publication price"""
        
        publication = await self.publication_model.get_publication(publication_id, user_id)
        
        if not publication:
            return {"success": False, "error": "Publication not found"}
        
        if publication['status'] != "published":
            return {"success": False, "error": "Can only update price for published books"}
        
        # Update price using PydanticAI KDP agent
        try:
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "title": publication['book']['title'] if publication.get('book') else "Book",
                "author": "Author",
                "description": "Price update",
                "genre": "general",
                "keywords": [],
                "manuscript_file": "",
                "cover_file": "",
                "action": "update_price",
                "kdp_id": publication['kdp_id'],
                "new_price": new_price
            }
            result = await execute_agent("kdp_uploader", task_data, user_id)
            
            if result.success:
                await self.publication_model.update_publication(publication_id, {"price": new_price})
                
                logger.info(f"Updated price for publication {publication_id} to ${new_price}")
                
                return {
                    "success": True,
                    "publication_id": publication_id,
                    "new_price": new_price,
                    "message": "Price updated successfully"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to update price: {result.error}"
                }
                
        except Exception as e:
            logger.error(f"Price update failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def refresh_sales_data(self, publication_id: str, user_id: str) -> Dict[str, Any]:
        """Refresh sales data for a publication"""
        
        publication = await self.publication_model.get_publication(publication_id, user_id)
        
        if not publication:
            return {"success": False, "error": "Publication not found"}
        
        if not publication['kdp_id']:
            return {"success": False, "error": "No KDP ID available for sales monitoring"}
        
        try:
            # Get latest sales data using PydanticAI agent
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "date_range": "last_30_days",
                "include_page_reads": True,
                "generate_insights": True
            }
            result = await execute_agent("sales_monitor", task_data, user_id)
            
            if result.success:
                sales_data = result.data.get('sales_data', {})
                
                # Store sales data
                await self._store_sales_data(publication['book_id'], sales_data)
                
                # Capture feedback for VERL training
                await LiveFeedbackCollector.capture_sales_feedback(
                    book_id=publication['book_id'],
                    sales_data=sales_data
                )
                
                return {
                    "success": True,
                    "publication_id": publication_id,
                    "sales_data": sales_data,
                    "message": "Sales data refreshed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to refresh sales data: {result.error}"
                }
                
        except Exception as e:
            logger.error(f"Sales data refresh failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def unpublish_book(self, publication_id: str, user_id: str) -> Dict[str, Any]:
        """Unpublish a book from KDP"""
        
        publication = await self.publication_model.get_publication(publication_id, user_id)
        
        if not publication:
            return {"success": False, "error": "Publication not found"}
        
        if publication['status'] != "published":
            return {"success": False, "error": "Book is not currently published"}
        
        try:
            # Unpublish using PydanticAI KDP agent
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "title": publication['book']['title'] if publication.get('book') else "Book",
                "author": "Author",
                "description": "Unpublish",
                "genre": "general",
                "keywords": [],
                "manuscript_file": "",
                "cover_file": "",
                "action": "unpublish",
                "kdp_id": publication['kdp_id']
            }
            result = await execute_agent("kdp_uploader", task_data, user_id)
            
            if result.success:
                await self.publication_model.update_publication(publication_id, {"status": "unpublished"})
                await self.book_model.update_book(publication['book_id'], {"status": "approved"})
                
                logger.info(f"Unpublished book {publication['book_id']}")
                
                return {
                    "success": True,
                    "publication_id": publication_id,
                    "message": "Book unpublished successfully"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to unpublish: {result.error}"
                }
                
        except Exception as e:
            logger.error(f"Unpublish failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_sales_summary(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get sales summary for user's publications"""
        
        return await self.publication_model.get_sales_summary(user_id, days)
    
    async def _run_publication_process(
        self, 
        publication_id: str, 
        book: Dict[str, Any], 
        publication_request: PublicationRequest
    ):
        """Run publication process in background"""
        
        try:
            # Prepare publication data
            publication_data = {
                "book_id": book['id'],
                "title": book['title'],
                "category": book['category'],
                "description": book['description'],
                "manuscript_path": book['manuscript_path'],
                "cover_paths": book['cover_paths'],
                "price": publication_request.price,
                "royalty_rate": publication_request.royalty_rate,
                "marketing_description": publication_request.marketing_description,
                "auto_publish": publication_request.auto_publish
            }
            
            # Execute publication using PydanticAI KDP agent
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "title": publication_data["title"],
                "author": "Author",
                "description": publication_data["description"],
                "genre": publication_data["category"],
                "keywords": [],
                "manuscript_file": publication_data["manuscript_path"],
                "cover_file": publication_data.get("cover_paths", {}).get("main", "")
            }
            result = await execute_agent("kdp_uploader", task_data, None)
            
            publication = await self.publication_model.get_publication(publication_id)
            
            if not publication:
                return
            
            if result.success:
                update_data = {
                    "status": "published" if publication_request.auto_publish else "draft",
                    "kdp_id": result.data.get('kdp_id'),
                    "publication_url": result.data.get('publication_url'),
                    "published_at": datetime.utcnow().isoformat()
                }
                await self.publication_model.update_publication(publication_id, update_data)
                
                # Update book status
                await self.book_model.update_book(book['id'], {"status": "published", "published_at": datetime.utcnow().isoformat()})
                
                logger.info(f"Successfully published book {book['id']}")
            else:
                await self.publication_model.update_publication(publication_id, {"status": "failed", "error_message": result.error})
                logger.error(f"Publication failed for book {book['id']}: {result.error}")
                
        except Exception as e:
            logger.error(f"Publication process failed: {str(e)}")
            await self.publication_model.update_publication(publication_id, {"status": "failed", "error_message": str(e)})
    
    async def _store_sales_data(self, book_id: str, sales_data: Dict[str, Any]):
        """Store sales data in database"""
        
        sales_record = {
            "book_id": book_id,
            "sales_units": sales_data.get('sales_units', 0),
            "revenue": sales_data.get('revenue', 0.0),
            "royalties": sales_data.get('royalties', 0.0),
            "average_rating": sales_data.get('average_rating', 0.0),
            "reviews_count": sales_data.get('reviews_count', 0),
            "report_date": datetime.utcnow().isoformat()
        }
        
        await self.sales_data_model.create_sales_data(sales_record)
