"""
Automated data retention policies with compliance management.

This module provides:
- Configurable data retention policies by data type
- Automated cleanup of expired data
- Compliance with GDPR and other regulations
- Safe data archival before deletion
- Audit trail for all retention actions
- Emergency data recovery capabilities
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass
from pathlib import Path
import json
import gzip
import os

from pydantic import BaseModel, Field

from app.config import get_settings
from app.utils.supabase.supabase_client import get_supabase_client
from app.monitoring.audit_logger import audit_log, AuditCategory, AuditLevel
from app.cache import get_cache

logger = logging.getLogger(__name__)
settings = get_settings()


class RetentionAction(str, Enum):
    """Actions to take when retention period expires."""
    DELETE = "delete"
    ARCHIVE = "archive"
    ANONYMIZE = "anonymize"
    NOTIFY_ONLY = "notify_only"


class DataClassification(str, Enum):
    """Data classification levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


@dataclass
class RetentionPolicy:
    """Data retention policy definition."""
    name: str
    description: str
    table_name: str
    retention_days: int
    action: RetentionAction
    classification: DataClassification

    # Conditions
    conditions: Optional[Dict[str, Any]] = None  # Additional WHERE conditions
    exclude_conditions: Optional[Dict[str, Any]] = None  # Records to exclude

    # Safety
    require_approval: bool = False
    max_records_per_run: int = 1000
    dry_run_only: bool = False

    # Archival
    archive_location: Optional[str] = None
    compression: bool = True

    # Schedule
    schedule_cron: str = "0 2 * * *"  # Daily at 2 AM
    enabled: bool = True

    # Metadata
    created_at: Optional[datetime] = None
    last_run_at: Optional[datetime] = None
    total_processed: int = 0


class RetentionJob(BaseModel):
    """Data retention job execution record."""
    job_id: str
    policy_name: str
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    status: str = "running"  # running, completed, failed, cancelled
    
    # Results
    records_found: int = 0
    records_processed: int = 0
    records_failed: int = 0
    
    # Details
    dry_run: bool = False
    error_message: Optional[str] = None
    archive_path: Optional[str] = None


class DataRetentionManager:
    """
    Manages automated data retention with compliance features.
    
    Features:
    - Policy-based retention with flexible rules
    - Safe archival before deletion
    - Compliance reporting and audit trails
    - Emergency recovery capabilities
    - Scheduled and manual execution
    - Multi-tenant data isolation
    """

    def __init__(self):
        self.supabase = None
        self.cache = None
        self.archive_base_path = Path(settings.storage_path) / "archives"
        self.policies: Dict[str, RetentionPolicy] = {}
        self.running_jobs: Set[str] = set()

        # Ensure archive directory exists
        self.archive_base_path.mkdir(parents=True, exist_ok=True)

        # Default policies
        self._setup_default_policies()

    async def initialize(self):
        """Initialize data retention manager."""
        self.supabase = await get_supabase_client()
        self.cache = await get_cache()

        # Load policies from database
        await self._load_policies_from_database()

        # Start scheduler
        asyncio.create_task(self._retention_scheduler())

        logger.info("Data retention manager initialized")

    def _setup_default_policies(self):
        """Set up default retention policies."""

        # Audit events - 7 years retention (compliance requirement)
        self.policies["audit_events"] = RetentionPolicy(
            name="audit_events",
            description="Audit events retention for compliance",
            table_name="audit_events",
            retention_days=2555,  # 7 years
            action=RetentionAction.ARCHIVE,
            classification=DataClassification.CONFIDENTIAL,
            archive_location="audit_archives",
            require_approval=True,
            max_records_per_run=500
        )

        # User sessions - 90 days
        self.policies["user_sessions"] = RetentionPolicy(
            name="user_sessions",
            description="User session data cleanup",
            table_name="user_sessions",
            retention_days=90,
            action=RetentionAction.DELETE,
            classification=DataClassification.INTERNAL,
            conditions={"status": "expired"}
        )

        # API key usage logs - 1 year
        self.policies["api_key_usage"] = RetentionPolicy(
            name="api_key_usage",
            description="API key usage logs retention",
            table_name="api_key_usage",
            retention_days=365,
            action=RetentionAction.ARCHIVE,
            classification=DataClassification.INTERNAL,
            archive_location="api_usage_archives"
        )

        # OAuth tokens - 30 days after expiration
        self.policies["oauth_tokens"] = RetentionPolicy(
            name="oauth_tokens",
            description="Expired OAuth tokens cleanup",
            table_name="oauth_refresh_tokens",
            retention_days=30,
            action=RetentionAction.DELETE,
            classification=DataClassification.CONFIDENTIAL,
            conditions={"revoked": True}
        )

        # Cache entries - handled by Redis TTL, but clean up metadata
        self.policies["cache_metadata"] = RetentionPolicy(
            name="cache_metadata",
            description="Cache metadata cleanup",
            table_name="cache_metadata",
            retention_days=7,
            action=RetentionAction.DELETE,
            classification=DataClassification.INTERNAL
        )

        # User feedback - 2 years for analytics
        self.policies["user_feedback"] = RetentionPolicy(
            name="user_feedback",
            description="User feedback data retention",
            table_name="user_feedback",
            retention_days=730,  # 2 years
            action=RetentionAction.ANONYMIZE,
            classification=DataClassification.INTERNAL,
            exclude_conditions={"feedback_type": "complaint"}  # Keep complaints longer
        )

        # Draft manuscripts - 1 year if unpublished
        self.policies["draft_manuscripts"] = RetentionPolicy(
            name="draft_manuscripts",
            description="Unpublished draft manuscripts cleanup",
            table_name="books",
            retention_days=365,
            action=RetentionAction.ARCHIVE,
            classification=DataClassification.INTERNAL,
            conditions={"status": "draft"},
            archive_location="draft_archives"
        )

    async def add_policy(self, policy: RetentionPolicy) -> bool:
        """Add a new retention policy."""

        try:
            # Validate policy
            await self._validate_policy(policy)

            # Store in memory
            self.policies[policy.name] = policy

            # Store in database
            await self._store_policy(policy)

            await audit_log(
                "retention_policy_created",
                {"policy_name": policy.name, "retention_days": policy.retention_days},
                category=AuditCategory.SYSTEM_CONFIGURATION
            )

            logger.info(f"Added retention policy: {policy.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to add retention policy {policy.name}: {e}")
            return False

    async def execute_policy(
        self, 
        policy_name: str, 
        dry_run: bool = False,
        max_records: Optional[int] = None
    ) -> RetentionJob:
        """Execute a specific retention policy."""

        if policy_name not in self.policies:
            raise ValueError(f"Unknown policy: {policy_name}")

        policy = self.policies[policy_name]

        # Check if policy is enabled
        if not policy.enabled:
            raise ValueError(f"Policy {policy_name} is disabled")

        # Generate job ID
        job_id = f"retention_{policy_name}_{int(datetime.utcnow().timestamp())}"

        # Check if already running
        if job_id in self.running_jobs:
            raise ValueError(f"Policy {policy_name} is already running")

        # Create job record
        job = RetentionJob(
            job_id=job_id,
            policy_name=policy_name,
            dry_run=dry_run or policy.dry_run_only
        )

        self.running_jobs.add(job_id)

        try:
            # Execute retention policy
            await self._execute_retention_policy(policy, job, max_records)

            job.status = "completed"
            job.completed_at = datetime.utcnow()

            await audit_log(
                "retention_policy_executed",
                {
                    "policy_name": policy_name,
                    "job_id": job_id,
                    "records_processed": job.records_processed,
                    "dry_run": job.dry_run
                },
                category=AuditCategory.DATA_MODIFICATION,
                level=AuditLevel.INFO
            )

        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()

            await audit_log(
                "retention_policy_failed",
                {"policy_name": policy_name, "job_id": job_id, "error": str(e)},
                category=AuditCategory.SYSTEM_CONFIGURATION,
                level=AuditLevel.ERROR
            )

            logger.error(f"Retention policy {policy_name} failed: {e}")

        finally:
            self.running_jobs.discard(job_id)
            await self._store_job_result(job)

        return job

    async def _execute_retention_policy(
        self, 
        policy: RetentionPolicy, 
        job: RetentionJob,
        max_records: Optional[int] = None
    ):
        """Execute the actual retention policy."""

        # Calculate cutoff date
        cutoff_date = datetime.utcnow() - timedelta(days=policy.retention_days)

        # Build query to find records for retention
        if self.supabase is None:
            raise Exception("Supabase client not initialized")

        query = self.supabase.client.table(policy.table_name).select("*")

        # Add time-based filter
        query = query.lt("created_at", cutoff_date.isoformat())

        # Add policy conditions
        if policy.conditions:
            for column, value in policy.conditions.items():
                query = query.eq(column, value)

        # Add exclusion conditions
        if policy.exclude_conditions:
            for column, value in policy.exclude_conditions.items():
                query = query.neq(column, value)

        # Limit records per run
        limit = max_records or policy.max_records_per_run
        query = query.limit(limit)

        # Execute query
        response = query.execute()
        records = response.data

        job.records_found = len(records)

        if not records:
            logger.info(f"No records found for retention policy: {policy.name}")
            return

        logger.info(f"Found {len(records)} records for retention policy: {policy.name}")

        # Process records based on action
        if policy.action == RetentionAction.DELETE:
            await self._delete_records(policy, records, job)
        elif policy.action == RetentionAction.ARCHIVE:
            await self._archive_records(policy, records, job)
        elif policy.action == RetentionAction.ANONYMIZE:
            await self._anonymize_records(policy, records, job)
        elif policy.action == RetentionAction.NOTIFY_ONLY:
            await self._notify_records(policy, records, job)

    async def _delete_records(self, policy: RetentionPolicy, records: List[Dict], job: RetentionJob):
        """Delete records according to policy."""

        if job.dry_run:
            logger.info(f"DRY RUN: Would delete {len(records)} records from {policy.table_name}")
            job.records_processed = len(records)
            return

        # Require approval for certain policies
        if policy.require_approval:
            logger.warning(f"Deletion of {len(records)} records requires manual approval")
            return

        # Delete records in batches
        batch_size = 100
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            record_ids = [record["id"] for record in batch]

            try:
                if self.supabase is None:
                    raise Exception("Supabase client not initialized")

                self.supabase.client.table(policy.table_name).delete().in_(
                    "id", record_ids
                ).execute()
                job.records_processed += len(batch)

            except Exception as e:
                logger.error(f"Failed to delete batch: {e}")
                job.records_failed += len(batch)

        logger.info(f"Deleted {job.records_processed} records from {policy.table_name}")

    async def _archive_records(self, policy: RetentionPolicy, records: List[Dict], job: RetentionJob):
        """Archive records before deletion."""

        # Create archive directory
        archive_dir = self.archive_base_path / (policy.archive_location or policy.name)
        archive_dir.mkdir(parents=True, exist_ok=True)

        # Create archive file
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        archive_filename = f"{policy.name}_{timestamp}.json"

        if policy.compression:
            archive_filename += ".gz"

        archive_path = archive_dir / archive_filename

        if job.dry_run:
            logger.info(f"DRY RUN: Would archive {len(records)} records to {archive_path}")
            job.records_processed = len(records)
            return

        try:
            # Prepare archive data
            archive_data = {
                "policy_name": policy.name,
                "archived_at": datetime.utcnow().isoformat(),
                "retention_cutoff": (datetime.utcnow() - timedelta(days=policy.retention_days)).isoformat(),
                "record_count": len(records),
                "records": records
            }

            # Write archive file
            if policy.compression:
                with gzip.open(archive_path, 'wt', encoding='utf-8') as f:
                    json.dump(archive_data, f, indent=2, default=str)
            else:
                with open(archive_path, 'w', encoding='utf-8') as f:
                    json.dump(archive_data, f, indent=2, default=str)

            job.archive_path = str(archive_path)

            # Now delete the records
            await self._delete_records(policy, records, job)

            logger.info(f"Archived {len(records)} records to {archive_path}")

        except Exception as e:
            logger.error(f"Failed to archive records: {e}")
            job.records_failed = len(records)
            raise

    async def _anonymize_records(self, policy: RetentionPolicy, records: List[Dict], job: RetentionJob):
        """Anonymize records instead of deleting."""

        if job.dry_run:
            logger.info(f"DRY RUN: Would anonymize {len(records)} records in {policy.table_name}")
            job.records_processed = len(records)
            return

        # Define anonymization rules (could be policy-specific)
        anonymization_rules = {
            "email": "<EMAIL>",
            "name": "Anonymous User",
            "ip_address": "0.0.0.0",
            "user_agent": "Anonymous",
            "phone": "************"
        }

        # Anonymize records in batches
        batch_size = 100
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]

            for record in batch:
                record_id = record["id"]
                updates = {}

                # Apply anonymization rules
                for field, anon_value in anonymization_rules.items():
                    if field in record and record[field]:
                        updates[field] = anon_value

                # Mark as anonymized
                updates["anonymized_at"] = datetime.utcnow().isoformat()

                try:
                    if self.supabase is None:
                        raise Exception("Supabase client not initialized")

                    self.supabase.client.table(policy.table_name).update(updates).eq(
                        "id", record_id
                    ).execute()
                    job.records_processed += 1

                except Exception as e:
                    logger.error(f"Failed to anonymize record {record_id}: {e}")
                    job.records_failed += 1

        logger.info(f"Anonymized {job.records_processed} records in {policy.table_name}")

    async def _notify_records(self, policy: RetentionPolicy, records: List[Dict], job: RetentionJob):
        """Notify about records eligible for retention (no action taken)."""

        logger.info(f"NOTIFICATION: {len(records)} records in {policy.table_name} are eligible for retention")

        # Could send notifications to administrators
        # For now, just log the notification

        job.records_processed = len(records)

    async def _retention_scheduler(self):
        """Background scheduler for retention policies."""

        while True:
            try:
                current_time = datetime.utcnow()

                for policy_name, policy in self.policies.items():
                    if not policy.enabled:
                        continue

                    # Check if it's time to run (simplified daily check)
                    if policy.last_run_at:
                        time_since_last_run = current_time - policy.last_run_at
                        if time_since_last_run.total_seconds() < 86400:  # 24 hours
                            continue

                    # Check if policy should run (based on schedule)
                    hour = current_time.hour
                    if hour == 2:  # Run at 2 AM (could parse cron expression)
                        try:
                            logger.info(f"Running scheduled retention policy: {policy_name}")
                            job = await self.execute_policy(policy_name)

                            # Update last run time
                            policy.last_run_at = current_time
                            policy.total_processed += job.records_processed

                        except Exception as e:
                            logger.error(f"Scheduled retention policy {policy_name} failed: {e}")

                # Sleep for an hour before checking again
                await asyncio.sleep(3600)

            except Exception as e:
                logger.error(f"Error in retention scheduler: {e}")
                await asyncio.sleep(3600)

    async def _validate_policy(self, policy: RetentionPolicy):
        """Validate retention policy configuration."""

        # Check table exists
        try:
            if self.supabase is None:
                raise Exception("Supabase client not initialized")
            self.supabase.client.table(policy.table_name).select("id").limit(
                1
            ).execute()
        except Exception:
            raise ValueError(f"Table {policy.table_name} does not exist")

        # Validate retention period
        if policy.retention_days < 1:
            raise ValueError("Retention period must be at least 1 day")

        # Validate archive location if archiving
        if policy.action == RetentionAction.ARCHIVE and not policy.archive_location:
            policy.archive_location = policy.name

    async def _load_policies_from_database(self):
        """Load retention policies from database."""
        try:
            if self.supabase is None:
                raise Exception("Supabase client not initialized")

            response = (
                self.supabase.client.table("retention_policies").select("*").execute()
            )

            for data in response.data:
                # Parse JSON fields
                data["conditions"] = json.loads(data.get("conditions") or "{}")
                data["exclude_conditions"] = json.loads(data.get("exclude_conditions") or "{}")

                # Parse datetime fields
                if data.get("created_at"):
                    data["created_at"] = datetime.fromisoformat(data["created_at"])
                if data.get("last_run_at"):
                    data["last_run_at"] = datetime.fromisoformat(data["last_run_at"])

                # Parse enums
                data["action"] = RetentionAction(data["action"])
                data["classification"] = DataClassification(data["classification"])

                policy = RetentionPolicy(**data)
                self.policies[policy.name] = policy

            logger.info(f"Loaded {len(response.data)} retention policies from database")

        except Exception as e:
            logger.error(f"Failed to load retention policies: {e}")

    async def _store_policy(self, policy: RetentionPolicy):
        """Store retention policy in database."""
        try:
            policy_data = {
                "name": policy.name,
                "description": policy.description,
                "table_name": policy.table_name,
                "retention_days": policy.retention_days,
                "action": policy.action.value,
                "classification": policy.classification.value,
                "conditions": json.dumps(policy.conditions or {}),
                "exclude_conditions": json.dumps(policy.exclude_conditions or {}),
                "require_approval": policy.require_approval,
                "max_records_per_run": policy.max_records_per_run,
                "dry_run_only": policy.dry_run_only,
                "archive_location": policy.archive_location,
                "compression": policy.compression,
                "schedule_cron": policy.schedule_cron,
                "enabled": policy.enabled,
                "created_at": (policy.created_at or datetime.utcnow()).isoformat(),
                "last_run_at": policy.last_run_at.isoformat() if policy.last_run_at else None,
                "total_processed": policy.total_processed
            }

            # Upsert policy
            if self.supabase is None:
                raise Exception("Supabase client not initialized")
            response = (
                self.supabase.client.table("retention_policies")
                .upsert(policy_data)
                .execute()
            )
            if not response:
                logger.error(f"Insert failed with response: {response}")

        except Exception as e:
            logger.error(f"Failed to store retention policy: {e}")
            raise

    async def _store_job_result(self, job: RetentionJob):
        """Store retention job result."""
        try:
            job_data = {
                "job_id": job.job_id,
                "policy_name": job.policy_name,
                "started_at": job.started_at.isoformat(),
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "status": job.status,
                "records_found": job.records_found,
                "records_processed": job.records_processed,
                "records_failed": job.records_failed,
                "dry_run": job.dry_run,
                "error_message": job.error_message,
                "archive_path": job.archive_path
            }

            if self.supabase is None:
                raise Exception("Supabase client not initialized")
            response = (
                self.supabase.client.table("retention_jobs").insert(job_data).execute()
            )
            if not response:
                logger.error(f"Insert failed with response: {response}")

        except Exception as e:
            logger.error(f"Failed to store retention job result: {e}")

    async def get_policy_status(self) -> Dict[str, Any]:
        """Get status of all retention policies."""

        policies_status: Dict[str, Dict[str, Any]] = {}

        for name, policy in self.policies.items():
            policies_status[name] = {
                "enabled": policy.enabled,
                "retention_days": policy.retention_days,
                "action": policy.action.value,
                "last_run_at": (
                    policy.last_run_at.isoformat() if policy.last_run_at else None
                ),
                "total_processed": policy.total_processed,
            }

        status = {
            "total_policies": len(self.policies),
            "enabled_policies": sum(1 for p in self.policies.values() if p.enabled),
            "running_jobs": len(self.running_jobs),
            "policies": policies_status,
        }
        return status

    async def emergency_recovery(self, archive_path: str, table_name: str) -> bool:
        """Emergency recovery from archive file."""

        try:
            archive_file = Path(archive_path)
            if not archive_file.exists():
                raise FileNotFoundError(f"Archive file not found: {archive_path}")

            # Read archive file
            if archive_path.endswith('.gz'):
                with gzip.open(archive_file, 'rt', encoding='utf-8') as f:
                    archive_data = json.load(f)
            else:
                with open(archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)

            records = archive_data.get("records", [])

            if not records:
                logger.warning(f"No records found in archive: {archive_path}")
                return True

            # Restore records
            batch_size = 100
            restored_count = 0

            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]

                try:
                    if self.supabase is None:
                        raise Exception("Supabase client not initialized")
                    response = (
                        self.supabase.client.table(table_name).insert(batch).execute()
                    )
                    if not response:
                        logger.error(f"Insert failed with response: {response}")
                    restored_count += len(batch)
                except Exception as e:
                    logger.error(f"Failed to restore batch: {e}")

            await audit_log(
                "emergency_recovery_performed",
                {
                    "archive_path": archive_path,
                    "table_name": table_name,
                    "records_restored": restored_count
                },
                category=AuditCategory.DATA_MODIFICATION,
                level=AuditLevel.WARNING
            )

            logger.info(f"Emergency recovery completed: {restored_count} records restored to {table_name}")
            return True

        except Exception as e:
            logger.error(f"Emergency recovery failed: {e}")
            return False


# Global data retention manager
_retention_manager: Optional[DataRetentionManager] = None


async def get_retention_manager() -> DataRetentionManager:
    """Get or create data retention manager."""
    global _retention_manager
    
    if _retention_manager is None:
        _retention_manager = DataRetentionManager()
        await _retention_manager.initialize()
    
    return _retention_manager


# Convenience functions
async def execute_retention_policy(policy_name: str, dry_run: bool = False) -> RetentionJob:
    """Execute a retention policy (convenience function)."""
    manager = await get_retention_manager()
    return await manager.execute_policy(policy_name, dry_run=dry_run)


async def get_retention_status() -> Dict[str, Any]:
    """Get retention system status (convenience function)."""
    manager = await get_retention_manager()
    return await manager.get_policy_status()
