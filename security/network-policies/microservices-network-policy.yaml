# Kubernetes Network Policies for Publish AI Microservices
# Implements Zero Trust networking with micro-segmentation

apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: publish-ai-default-deny
  namespace: publish-ai
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
# Allow DNS resolution
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns
  namespace: publish-ai
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# Event Bus Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: event-bus-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: event-bus
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow API Gateway to access Event Bus
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  # Allow all microservices to access Event Bus
  - from:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8080
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090  # Metrics
  egress:
  # Allow Event Bus to access Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow Event Bus to access Service Discovery
  - to:
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8070

---
# Service Discovery Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: service-discovery-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: service-discovery
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow API Gateway to access Service Discovery
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8070
  # Allow all microservices to register and discover
  - from:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8070
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 8070
    - protocol: TCP
      port: 9090  # Metrics
  egress:
  # Allow Service Discovery to access Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6380  # Different port to avoid conflicts
  # Allow Service Discovery to access Event Bus
  - to:
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8080
  # Allow health checks to microservices
  - to:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8080

---
# API Gateway Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow external traffic to API Gateway
  - from: []
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 443  # HTTPS
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Metrics
  egress:
  # Allow API Gateway to access all microservices
  - to:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8080
  # Allow API Gateway to access Service Discovery
  - to:
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8070
  # Allow API Gateway to access Event Bus
  - to:
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8080

---
# Microservices Inter-Communication Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: microservices-intercommunication
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      component: microservice
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow API Gateway to access microservices
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  # Allow Service Discovery health checks
  - from:
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8080
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090  # Metrics
  # Allow inter-microservice communication (for direct calls if needed)
  - from:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow microservices to access Event Bus
  - to:
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 8080
  # Allow microservices to access Service Discovery
  - to:
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 8070
  # Allow microservices to call each other (for direct communication)
  - to:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 8080
  # Allow access to external APIs (OpenAI, Anthropic, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# Redis Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: redis-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: redis
  policyTypes:
  - Ingress
  ingress:
  # Allow Event Bus to access Redis
  - from:
    - podSelector:
        matchLabels:
          app: event-bus
    ports:
    - protocol: TCP
      port: 6379
  # Allow Service Discovery to access Redis (different port)
  - from:
    - podSelector:
        matchLabels:
          app: service-discovery
    ports:
    - protocol: TCP
      port: 6380
  # Allow API Gateway to access Redis for caching
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 6379
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 6380

---
# Monitoring Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      component: monitoring
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow external access to monitoring dashboards
  - from: []
    ports:
    - protocol: TCP
      port: 3000  # Grafana
    - protocol: TCP
      port: 9090  # Prometheus
  egress:
  # Allow monitoring to scrape all services
  - to:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 9090  # Metrics endpoints
    - protocol: TCP
      port: 8080  # Health endpoints

---
# Database Network Policy (for Supabase/PostgreSQL)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-policy
  namespace: publish-ai
spec:
  podSelector:
    matchLabels:
      app: postgresql
  policyTypes:
  - Ingress
  ingress:
  # Allow microservices to access database
  - from:
    - podSelector:
        matchLabels:
          component: microservice
    ports:
    - protocol: TCP
      port: 5432
  # Allow API Gateway to access database
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 5432
  # Allow monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 5432