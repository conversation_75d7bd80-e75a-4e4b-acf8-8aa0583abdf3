# Publish AI Security Foundation

Comprehensive security infrastructure for the microservices architecture, implementing defense-in-depth security principles with mTLS, API key management, and network policies.

## Security Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│    Frontend     │    │   API Gateway    │    │  Microservices  │
│   Applications  │───▶│   (TLS Term.)    │───▶│   (mTLS)        │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Certificate   │    │   API Key        │    │  Network        │
│   Authority     │    │   Management     │    │  Policies       │
│   (Root CA)     │    │   Service        │    │  (Zero Trust)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Components

### 1. Certificate Authority (CA)
- Root CA for internal mTLS communication
- Service certificates with automatic rotation
- Client certificates for external access
- Certificate revocation and validation

### 2. API Key Management
- Centralized API key generation and validation
- Role-based access control (RBAC)
- Key rotation and lifecycle management
- Rate limiting and usage tracking

### 3. Network Security
- Zero Trust network policies
- Service mesh integration (Istio/Linkerd ready)
- Traffic encryption and authentication
- Network segmentation and isolation

### 4. Authentication & Authorization
- Multi-factor authentication (MFA)
- JWT token management
- OAuth2/OIDC integration
- Service-to-service authentication

## Security Layers

### Layer 1: Network Security
- **TLS/mTLS**: All communication encrypted with mutual authentication
- **Network Policies**: Kubernetes NetworkPolicies for micro-segmentation
- **Service Mesh**: Istio/Linkerd for service-to-service security
- **Firewall Rules**: Ingress/egress traffic control

### Layer 2: Authentication
- **Client Authentication**: API keys, JWT tokens, OAuth2
- **Service Authentication**: mTLS certificates, service accounts
- **Multi-Factor Authentication**: For admin access
- **Identity Providers**: Integration with external IdPs

### Layer 3: Authorization
- **Role-Based Access Control (RBAC)**: Fine-grained permissions
- **Attribute-Based Access Control (ABAC)**: Context-aware decisions
- **API Gateway Policies**: Centralized authorization
- **Service-Level Policies**: Granular service permissions

### Layer 4: Data Protection
- **Encryption at Rest**: Database and storage encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: Hardware Security Modules (HSM)
- **Data Classification**: Sensitive data identification and protection

### Layer 5: Monitoring & Compliance
- **Security Event Monitoring**: SIEM integration
- **Audit Logging**: Comprehensive security event logs
- **Compliance Scanning**: Automated vulnerability assessment
- **Incident Response**: Automated threat detection and response

## Implementation Status

### ✅ Completed Components
- **Event Bus Security**: API key authentication with encryption
- **Service Discovery Security**: API key authentication and admin controls
- **Backend Security Middleware**: Rate limiting, input validation, CORS

### 🔄 In Progress
- **Certificate Authority Setup**: Root CA and service certificates
- **API Gateway Security**: Centralized authentication and authorization
- **mTLS Configuration**: Service-to-service mutual authentication

### 📋 Planned Components
- **Network Policies**: Kubernetes network segmentation
- **Service Mesh Integration**: Istio/Linkerd security features
- **Security Monitoring**: Centralized security event collection
- **Compliance Framework**: Automated security compliance checks

## Quick Start

### 1. Generate Root CA and Certificates
```bash
# Create root CA
./security/scripts/create-ca.sh

# Generate service certificates
./security/scripts/generate-service-certs.sh event-bus
./security/scripts/generate-service-certs.sh service-discovery
./security/scripts/generate-service-certs.sh api-gateway

# Generate client certificates
./security/scripts/generate-client-certs.sh admin-client
```

### 2. Configure API Key Management
```bash
# Initialize API key store
./security/scripts/setup-api-keys.sh

# Generate service API keys
./security/scripts/generate-api-key.sh event-bus
./security/scripts/generate-api-key.sh service-discovery
./security/scripts/generate-api-key.sh content-generator
```

### 3. Deploy Network Policies
```bash
# Apply Kubernetes network policies
kubectl apply -f security/network-policies/

# Configure service mesh policies
kubectl apply -f security/service-mesh/
```

### 4. Enable Security Monitoring
```bash
# Deploy security monitoring stack
./security/scripts/deploy-monitoring.sh

# Configure audit logging
./security/scripts/setup-audit-logging.sh
```

## Security Policies

### API Key Policy
```json
{
  "policy_name": "microservices_api_keys",
  "version": "1.0",
  "rules": {
    "key_length": 32,
    "rotation_period": "90d",
    "allowed_scopes": [
      "service:read",
      "service:write", 
      "admin:read",
      "admin:write"
    ],
    "rate_limits": {
      "default": "1000/hour",
      "admin": "10000/hour"
    }
  }
}
```

### mTLS Policy
```yaml
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: publish-ai
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: microservices-authz
  namespace: publish-ai
spec:
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/publish-ai/sa/event-bus"]
    to:
    - operation:
        methods: ["POST", "GET"]
```

### Network Policy
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: microservices-network-policy
  namespace: publish-ai
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: publish-ai
    ports:
    - protocol: TCP
      port: 8080
```

## Certificate Management

### Root CA Configuration
```bash
# Certificate Authority settings
CA_COUNTRY="US"
CA_STATE="California" 
CA_CITY="San Francisco"
CA_ORG="Publish AI"
CA_OU="Security"
CA_EMAIL="<EMAIL>"
CA_VALIDITY_DAYS=3650

# Service certificate settings
SERVICE_VALIDITY_DAYS=365
KEY_SIZE=4096
CURVE="secp384r1"
```

### Service Certificate Generation
```bash
# Generate service certificate
openssl genrsa -out service-key.pem 4096
openssl req -new -key service-key.pem -out service-csr.pem \
  -subj "/C=US/ST=CA/L=SF/O=PublishAI/OU=Services/CN=event-bus.internal"
openssl x509 -req -in service-csr.pem -CA ca-cert.pem -CAkey ca-key.pem \
  -CAcreateserial -out service-cert.pem -days 365 \
  -extensions v3_req -extfile service.conf
```

## API Key Management

### Key Generation
```python
import secrets
import hashlib
from datetime import datetime, timedelta

def generate_api_key(service_name: str, scope: str) -> dict:
    """Generate API key for service"""
    key = secrets.token_urlsafe(32)
    key_hash = hashlib.sha256(key.encode()).hexdigest()
    
    return {
        "service_name": service_name,
        "api_key": key,
        "key_hash": key_hash,
        "scope": scope,
        "created_at": datetime.utcnow(),
        "expires_at": datetime.utcnow() + timedelta(days=90),
        "active": True
    }
```

### Key Validation
```python
async def validate_api_key(key: str, required_scope: str) -> bool:
    """Validate API key and check scope"""
    key_hash = hashlib.sha256(key.encode()).hexdigest()
    
    # Look up key in store (Redis/Database)
    key_data = await api_key_store.get(key_hash)
    
    if not key_data:
        return False
    
    # Check expiration
    if datetime.utcnow() > key_data["expires_at"]:
        return False
    
    # Check scope
    if required_scope not in key_data["scope"]:
        return False
    
    return True
```

## Security Monitoring

### Event Collection
```json
{
  "security_events": [
    {
      "event_type": "authentication_failure",
      "timestamp": "2025-01-15T10:30:00Z",
      "source_ip": "*************",
      "service": "api-gateway",
      "details": {
        "reason": "invalid_api_key",
        "attempted_key": "abc123...***",
        "user_agent": "curl/7.68.0"
      }
    },
    {
      "event_type": "certificate_validation_failure", 
      "timestamp": "2025-01-15T10:31:00Z",
      "source_service": "event-bus",
      "target_service": "service-discovery",
      "details": {
        "reason": "certificate_expired",
        "certificate_cn": "event-bus.internal",
        "expiry_date": "2025-01-14T23:59:59Z"
      }
    }
  ]
}
```

### Alerting Rules
```yaml
# Prometheus alerting rules
groups:
- name: security_alerts
  rules:
  - alert: HighAuthenticationFailures
    expr: rate(authentication_failures_total[5m]) > 10
    for: 5m
    annotations:
      summary: "High authentication failure rate detected"
      
  - alert: CertificateExpiringSoon
    expr: (cert_expiry_timestamp - time()) < 86400 * 7
    for: 1h
    annotations:
      summary: "Certificate expiring within 7 days"
      
  - alert: UnauthorizedServiceAccess
    expr: rate(authorization_denied_total[5m]) > 5
    for: 2m
    annotations:
      summary: "Potential unauthorized access attempt"
```

## Compliance & Auditing

### Security Standards
- **NIST Cybersecurity Framework**: Implementation guidelines
- **ISO 27001**: Information security management
- **SOC 2 Type II**: Security, availability, and confidentiality
- **GDPR**: Data protection and privacy compliance

### Audit Requirements
- **Access Logging**: All API requests and responses logged
- **Change Tracking**: Configuration and code change audit trail
- **Security Events**: Authentication, authorization, and security incidents
- **Data Access**: All database queries and data modifications tracked

### Compliance Automation
```bash
# Security compliance checks
./security/scripts/compliance-scan.sh

# Vulnerability assessment
./security/scripts/vuln-scan.sh

# Penetration testing
./security/scripts/pentest.sh

# Compliance reporting
./security/scripts/generate-compliance-report.sh
```

This security foundation provides enterprise-grade protection for the Publish AI microservices architecture, ensuring secure communication, proper authentication/authorization, and comprehensive monitoring and compliance capabilities.