#!/bin/bash

# Generate Service Certificates for Publish AI Microservices
# This script generates mTLS certificates for individual services

set -e

if [ $# -eq 0 ]; then
    echo "Usage: $0 <service-name> [dns-names...]"
    echo "Example: $0 event-bus event-bus.internal event-bus.publish-ai.local"
    exit 1
fi

SERVICE_NAME="$1"
shift
ADDITIONAL_DNS=("$@")

SECURITY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CA_DIR="${SECURITY_DIR}/ca"
CERTS_DIR="${SECURITY_DIR}/certs"
SERVICE_CERTS_DIR="${CERTS_DIR}/services/${SERVICE_NAME}"

# Service certificate configuration
SERVICE_VALIDITY_DAYS=365
KEY_SIZE=4096

echo "🔐 Generating certificate for service: ${SERVICE_NAME}"
echo "=================================================="

# Check if CA exists
if [ ! -f "${CA_DIR}/intermediate/certs/intermediate.cert.pem" ]; then
    echo "❌ Intermediate CA not found. Please run create-ca.sh first."
    exit 1
fi

# Create service certificate directory
mkdir -p "${SERVICE_CERTS_DIR}"

# Generate service private key
if [ ! -f "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem" ]; then
    openssl genrsa -out "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem" ${KEY_SIZE}
    chmod 400 "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem"
    echo "🔑 Generated private key for ${SERVICE_NAME}"
else
    echo "🔑 Private key for ${SERVICE_NAME} already exists"
fi

# Create certificate configuration with SANs
cat > "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.conf" << EOF
[ req ]
default_bits = ${KEY_SIZE}
prompt = no
distinguished_name = req_distinguished_name
req_extensions = v3_req

[ req_distinguished_name ]
C = US
ST = California
L = San Francisco
O = Publish AI
OU = Services
CN = ${SERVICE_NAME}.internal
emailAddress = <EMAIL>

[ v3_req ]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[ alt_names ]
DNS.1 = ${SERVICE_NAME}
DNS.2 = ${SERVICE_NAME}.internal
DNS.3 = ${SERVICE_NAME}.publish-ai.local
DNS.4 = ${SERVICE_NAME}.default.svc.cluster.local
DNS.5 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Add additional DNS names if provided
counter=6
for dns_name in "${ADDITIONAL_DNS[@]}"; do
    echo "DNS.${counter} = ${dns_name}" >> "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.conf"
    ((counter++))
done

echo "📋 Created certificate configuration for ${SERVICE_NAME}"

# Generate certificate signing request
if [ ! -f "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.csr.pem" ]; then
    openssl req -new -key "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem" \
        -out "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.csr.pem" \
        -config "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.conf"
    echo "📝 Generated certificate signing request for ${SERVICE_NAME}"
else
    echo "📝 Certificate signing request for ${SERVICE_NAME} already exists"
fi

# Sign certificate with Intermediate CA
if [ ! -f "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem" ]; then
    openssl ca -config "${CA_DIR}/intermediate/openssl.cnf" \
        -extensions server_cert -days ${SERVICE_VALIDITY_DAYS} -notext -md sha256 \
        -passin pass:publish-ai-intermediate-ca-2025 -batch \
        -in "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.csr.pem" \
        -out "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem"
    
    chmod 444 "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem"
    echo "📜 Generated certificate for ${SERVICE_NAME}"
else
    echo "📜 Certificate for ${SERVICE_NAME} already exists"
fi

# Create certificate bundle with chain
if [ ! -f "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-bundle.cert.pem" ]; then
    cat "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem" \
        "${CA_DIR}/intermediate/certs/ca-chain.cert.pem" > \
        "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-bundle.cert.pem"
    chmod 444 "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-bundle.cert.pem"
    echo "🔗 Created certificate bundle for ${SERVICE_NAME}"
else
    echo "🔗 Certificate bundle for ${SERVICE_NAME} already exists"
fi

# Verify certificate
echo "🔍 Verifying certificate for ${SERVICE_NAME}..."
openssl verify -CAfile "${CA_DIR}/intermediate/certs/ca-chain.cert.pem" \
    "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem"

# Display certificate information
echo ""
echo "📋 Certificate Information:"
openssl x509 -noout -text -in "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem" | \
    grep -A 1 "Subject:\|Issuer:\|Validity\|DNS:\|IP Address:"

# Create Kubernetes secret YAML
cat > "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-secret.yaml" << EOF
apiVersion: v1
kind: Secret
metadata:
  name: ${SERVICE_NAME}-tls
  namespace: publish-ai
type: kubernetes.io/tls
data:
  tls.crt: $(base64 -w 0 "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-bundle.cert.pem")
  tls.key: $(base64 -w 0 "${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem")
  ca.crt: $(base64 -w 0 "${CERTS_DIR}/ca-chain.pem")
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ${SERVICE_NAME}-ca
  namespace: publish-ai
data:
  ca-bundle.crt: |
$(sed 's/^/    /' "${CERTS_DIR}/ca-chain.pem")
EOF

echo "🎫 Created Kubernetes secret YAML: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-secret.yaml"

# Create Docker Compose volume configuration
cat > "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-docker-volumes.yml" << EOF
# Add to your docker-compose.yml file:
volumes:
  ${SERVICE_NAME}_certs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${SERVICE_CERTS_DIR}

# Add to your service configuration:
services:
  ${SERVICE_NAME}:
    volumes:
      - ${SERVICE_NAME}_certs:/etc/ssl/certs
    environment:
      - TLS_CERT_FILE=/etc/ssl/certs/${SERVICE_NAME}.cert.pem
      - TLS_KEY_FILE=/etc/ssl/certs/${SERVICE_NAME}.key.pem
      - TLS_CA_FILE=/etc/ssl/certs/ca-chain.pem
EOF

echo "🐳 Created Docker Compose configuration: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-docker-volumes.yml"

# Create service configuration template
cat > "${SERVICE_CERTS_DIR}/${SERVICE_NAME}-mtls-config.env" << EOF
# mTLS Configuration for ${SERVICE_NAME}
TLS_ENABLED=true
TLS_CERT_FILE=${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem
TLS_KEY_FILE=${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem
TLS_CA_FILE=${CERTS_DIR}/ca-chain.pem
TLS_VERIFY_CLIENT=true
TLS_MIN_VERSION=1.3
TLS_CIPHER_SUITES=TLS_AES_256_GCM_SHA384,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_GCM_SHA256

# Service-specific settings
SERVICE_NAME=${SERVICE_NAME}
CERT_COMMON_NAME=${SERVICE_NAME}.internal
CERT_EXPIRY_DAYS=${SERVICE_VALIDITY_DAYS}
EOF

echo "⚙️  Created mTLS configuration: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-mtls-config.env"

echo ""
echo "✅ Certificate generation completed successfully!"
echo ""
echo "📁 Certificate files:"
echo "   Private key: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}.key.pem"
echo "   Certificate: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}.cert.pem"
echo "   Bundle: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-bundle.cert.pem"
echo ""
echo "🚀 Deployment files:"
echo "   Kubernetes: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-secret.yaml"
echo "   Docker: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-docker-volumes.yml"
echo "   Config: ${SERVICE_CERTS_DIR}/${SERVICE_NAME}-mtls-config.env"
echo ""
echo "📅 Certificate expires in ${SERVICE_VALIDITY_DAYS} days"
echo ""
echo "Next steps:"
echo "1. Deploy certificate to ${SERVICE_NAME} service"
echo "2. Configure mTLS in service application"
echo "3. Update service discovery with mTLS endpoints"
echo "4. Test mTLS connectivity between services"