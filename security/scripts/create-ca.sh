#!/bin/bash

# Create Certificate Authority for Publish AI Microservices
# This script creates a root CA and intermediate CA for service certificates

set -e

SECURITY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CA_DIR="${SECURITY_DIR}/ca"
CERTS_DIR="${SECURITY_DIR}/certs"

# CA Configuration
CA_COUNTRY="US"
CA_STATE="California"
CA_CITY="San Francisco"
CA_ORG="Publish AI"
CA_OU="Security"
CA_EMAIL="<EMAIL>"
CA_VALIDITY_DAYS=3650
INTERMEDIATE_VALIDITY_DAYS=1825

echo "🔐 Creating Certificate Authority for Publish AI"
echo "================================================"

# Create directories
mkdir -p "${CA_DIR}"/{root,intermediate}/{private,certs,newcerts,crl}
mkdir -p "${CERTS_DIR}"/{services,clients}

# Initialize CA database files
touch "${CA_DIR}/root/index.txt"
touch "${CA_DIR}/intermediate/index.txt"
echo 1000 > "${CA_DIR}/root/serial"
echo 1000 > "${CA_DIR}/intermediate/serial"
echo 1000 > "${CA_DIR}/intermediate/crlnumber"

echo "📁 Created CA directory structure"

# Create Root CA configuration
cat > "${CA_DIR}/root/openssl.cnf" << EOF
[ ca ]
default_ca = CA_default

[ CA_default ]
dir               = ${CA_DIR}/root
certs             = \$dir/certs
crl_dir           = \$dir/crl
new_certs_dir     = \$dir/newcerts
database          = \$dir/index.txt
serial            = \$dir/serial
RANDFILE          = \$dir/private/.rand

private_key       = \$dir/private/ca.key.pem
certificate       = \$dir/certs/ca.cert.pem

crlnumber         = \$dir/crlnumber
crl               = \$dir/crl/ca.crl.pem
crl_extensions    = crl_ext
default_crl_days  = 30

default_md        = sha256
name_opt          = ca_default
cert_opt          = ca_default
default_days      = 375
preserve          = no
policy            = policy_strict

[ policy_strict ]
countryName             = match
stateOrProvinceName     = match
organizationName        = match
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional

[ policy_loose ]
countryName             = optional
stateOrProvinceName     = optional
localityName            = optional
organizationName        = optional
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional

[ req ]
default_bits        = 4096
distinguished_name  = req_distinguished_name
string_mask         = utf8only
default_md          = sha256
x509_extensions     = v3_ca

[ req_distinguished_name ]
countryName                     = Country Name (2 letter code)
stateOrProvinceName             = State or Province Name
localityName                    = Locality Name
0.organizationName              = Organization Name
organizationalUnitName          = Organizational Unit Name
commonName                      = Common Name
emailAddress                    = Email Address

countryName_default             = ${CA_COUNTRY}
stateOrProvinceName_default     = ${CA_STATE}
localityName_default            = ${CA_CITY}
0.organizationName_default      = ${CA_ORG}
organizationalUnitName_default  = ${CA_OU}
emailAddress_default            = ${CA_EMAIL}

[ v3_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ v3_intermediate_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true, pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ usr_cert ]
basicConstraints = CA:FALSE
nsCertType = client, email
nsComment = "OpenSSL Generated Client Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth, emailProtection

[ server_cert ]
basicConstraints = CA:FALSE
nsCertType = server
nsComment = "OpenSSL Generated Server Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer:always
keyUsage = critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth

[ crl_ext ]
authorityKeyIdentifier=keyid:always

[ ocsp ]
basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, digitalSignature
extendedKeyUsage = critical, OCSPSigning
EOF

echo "📋 Created Root CA configuration"

# Generate Root CA private key
if [ ! -f "${CA_DIR}/root/private/ca.key.pem" ]; then
    openssl genrsa -aes256 -passout pass:publish-ai-root-ca-2025 \
        -out "${CA_DIR}/root/private/ca.key.pem" 4096
    chmod 400 "${CA_DIR}/root/private/ca.key.pem"
    echo "🔑 Generated Root CA private key"
else
    echo "🔑 Root CA private key already exists"
fi

# Generate Root CA certificate
if [ ! -f "${CA_DIR}/root/certs/ca.cert.pem" ]; then
    openssl req -config "${CA_DIR}/root/openssl.cnf" \
        -key "${CA_DIR}/root/private/ca.key.pem" \
        -new -x509 -days ${CA_VALIDITY_DAYS} -sha256 -extensions v3_ca \
        -passin pass:publish-ai-root-ca-2025 \
        -out "${CA_DIR}/root/certs/ca.cert.pem" \
        -subj "/C=${CA_COUNTRY}/ST=${CA_STATE}/L=${CA_CITY}/O=${CA_ORG}/OU=${CA_OU}/CN=Publish AI Root CA/emailAddress=${CA_EMAIL}"
    
    chmod 444 "${CA_DIR}/root/certs/ca.cert.pem"
    echo "📜 Generated Root CA certificate"
else
    echo "📜 Root CA certificate already exists"
fi

# Create Intermediate CA configuration
cat > "${CA_DIR}/intermediate/openssl.cnf" << EOF
[ ca ]
default_ca = CA_default

[ CA_default ]
dir               = ${CA_DIR}/intermediate
certs             = \$dir/certs
crl_dir           = \$dir/crl
new_certs_dir     = \$dir/newcerts
database          = \$dir/index.txt
serial            = \$dir/serial
RANDFILE          = \$dir/private/.rand

private_key       = \$dir/private/intermediate.key.pem
certificate       = \$dir/certs/intermediate.cert.pem

crlnumber         = \$dir/crlnumber
crl               = \$dir/crl/intermediate.crl.pem
crl_extensions    = crl_ext
default_crl_days  = 30

default_md        = sha256
name_opt          = ca_default
cert_opt          = ca_default
default_days      = 375
preserve          = no
policy            = policy_loose

[ policy_strict ]
countryName             = match
stateOrProvinceName     = match
organizationName        = match
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional

[ policy_loose ]
countryName             = optional
stateOrProvinceName     = optional
localityName            = optional
organizationName        = optional
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional

[ req ]
default_bits        = 4096
distinguished_name  = req_distinguished_name
string_mask         = utf8only
default_md          = sha256
x509_extensions     = v3_intermediate_ca

[ req_distinguished_name ]
countryName                     = Country Name (2 letter code)
stateOrProvinceName             = State or Province Name
localityName                    = Locality Name
0.organizationName              = Organization Name
organizationalUnitName          = Organizational Unit Name
commonName                      = Common Name
emailAddress                    = Email Address

countryName_default             = ${CA_COUNTRY}
stateOrProvinceName_default     = ${CA_STATE}
localityName_default            = ${CA_CITY}
0.organizationName_default      = ${CA_ORG}
organizationalUnitName_default  = Services
emailAddress_default            = ${CA_EMAIL}

[ v3_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ v3_intermediate_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true, pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ usr_cert ]
basicConstraints = CA:FALSE
nsCertType = client, email
nsComment = "OpenSSL Generated Client Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth, emailProtection

[ server_cert ]
basicConstraints = CA:FALSE
nsCertType = server
nsComment = "OpenSSL Generated Server Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer:always
keyUsage = critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[ crl_ext ]
authorityKeyIdentifier=keyid:always

[ ocsp ]
basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, digitalSignature
extendedKeyUsage = critical, OCSPSigning

[ alt_names ]
DNS.1 = localhost
DNS.2 = *.internal
DNS.3 = *.publish-ai.local
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

echo "📋 Created Intermediate CA configuration"

# Generate Intermediate CA private key
if [ ! -f "${CA_DIR}/intermediate/private/intermediate.key.pem" ]; then
    openssl genrsa -aes256 -passout pass:publish-ai-intermediate-ca-2025 \
        -out "${CA_DIR}/intermediate/private/intermediate.key.pem" 4096
    chmod 400 "${CA_DIR}/intermediate/private/intermediate.key.pem"
    echo "🔑 Generated Intermediate CA private key"
else
    echo "🔑 Intermediate CA private key already exists"
fi

# Generate Intermediate CA certificate request
if [ ! -f "${CA_DIR}/intermediate/csr/intermediate.csr.pem" ]; then
    mkdir -p "${CA_DIR}/intermediate/csr"
    openssl req -config "${CA_DIR}/intermediate/openssl.cnf" -new -sha256 \
        -key "${CA_DIR}/intermediate/private/intermediate.key.pem" \
        -passin pass:publish-ai-intermediate-ca-2025 \
        -out "${CA_DIR}/intermediate/csr/intermediate.csr.pem" \
        -subj "/C=${CA_COUNTRY}/ST=${CA_STATE}/L=${CA_CITY}/O=${CA_ORG}/OU=Services/CN=Publish AI Intermediate CA/emailAddress=${CA_EMAIL}"
    echo "📝 Generated Intermediate CA certificate request"
else
    echo "📝 Intermediate CA certificate request already exists"
fi

# Sign Intermediate CA certificate with Root CA
if [ ! -f "${CA_DIR}/intermediate/certs/intermediate.cert.pem" ]; then
    openssl ca -config "${CA_DIR}/root/openssl.cnf" -extensions v3_intermediate_ca \
        -days ${INTERMEDIATE_VALIDITY_DAYS} -notext -md sha256 \
        -passin pass:publish-ai-root-ca-2025 -batch \
        -in "${CA_DIR}/intermediate/csr/intermediate.csr.pem" \
        -out "${CA_DIR}/intermediate/certs/intermediate.cert.pem"
    
    chmod 444 "${CA_DIR}/intermediate/certs/intermediate.cert.pem"
    echo "📜 Generated Intermediate CA certificate"
else
    echo "📜 Intermediate CA certificate already exists"
fi

# Create certificate chain
if [ ! -f "${CA_DIR}/intermediate/certs/ca-chain.cert.pem" ]; then
    cat "${CA_DIR}/intermediate/certs/intermediate.cert.pem" \
        "${CA_DIR}/root/certs/ca.cert.pem" > \
        "${CA_DIR}/intermediate/certs/ca-chain.cert.pem"
    chmod 444 "${CA_DIR}/intermediate/certs/ca-chain.cert.pem"
    echo "🔗 Created certificate chain"
else
    echo "🔗 Certificate chain already exists"
fi

# Verify certificate chain
echo "🔍 Verifying certificate chain..."
openssl verify -CAfile "${CA_DIR}/root/certs/ca.cert.pem" \
    "${CA_DIR}/intermediate/certs/intermediate.cert.pem"

# Create convenience copies for services
cp "${CA_DIR}/root/certs/ca.cert.pem" "${CERTS_DIR}/root-ca.pem"
cp "${CA_DIR}/intermediate/certs/intermediate.cert.pem" "${CERTS_DIR}/intermediate-ca.pem"
cp "${CA_DIR}/intermediate/certs/ca-chain.cert.pem" "${CERTS_DIR}/ca-chain.pem"

echo ""
echo "✅ Certificate Authority setup completed successfully!"
echo ""
echo "📁 Root CA certificate: ${CERTS_DIR}/root-ca.pem"
echo "📁 Intermediate CA certificate: ${CERTS_DIR}/intermediate-ca.pem"
echo "📁 Certificate chain: ${CERTS_DIR}/ca-chain.pem"
echo ""
echo "🔐 Root CA password: publish-ai-root-ca-2025"
echo "🔐 Intermediate CA password: publish-ai-intermediate-ca-2025"
echo ""
echo "Next steps:"
echo "1. Generate service certificates with: ./generate-service-certs.sh <service-name>"
echo "2. Generate client certificates with: ./generate-client-certs.sh <client-name>"
echo "3. Deploy certificates to services and configure mTLS"