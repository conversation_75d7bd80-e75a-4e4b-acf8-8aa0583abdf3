#!/bin/bash

# Setup API Key Management for Publish AI Microservices
# This script initializes the API key management system

set -e

SECURITY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
API_KEYS_DIR="${SECURITY_DIR}/api-keys"

echo "🔑 Setting up API Key Management System"
echo "======================================="

# Create API keys directory
mkdir -p "${API_KEYS_DIR}"

# Create API key store configuration
cat > "${API_KEYS_DIR}/api-key-config.json" << EOF
{
  "api_key_config": {
    "key_length": 32,
    "hash_algorithm": "sha256",
    "default_expiry_days": 90,
    "rotation_warning_days": 7,
    "max_keys_per_service": 3,
    "rate_limits": {
      "default": "1000/hour",
      "admin": "10000/hour",
      "service": "5000/hour"
    }
  },
  "scopes": {
    "service:read": "Read access to service APIs",
    "service:write": "Write access to service APIs", 
    "service:admin": "Administrative access to service",
    "event:publish": "Publish events to Event Bus",
    "event:subscribe": "Subscribe to Event Bus topics",
    "discovery:read": "Read service discovery information",
    "discovery:register": "Register services with discovery",
    "gateway:route": "Access API Gateway routing",
    "admin:read": "Read administrative information",
    "admin:write": "Write administrative configuration",
    "admin:full": "Full administrative access"
  },
  "service_roles": {
    "event-bus": ["event:publish", "event:subscribe", "service:read", "service:write"],
    "service-discovery": ["discovery:read", "discovery:register", "service:read", "service:write"],
    "api-gateway": ["gateway:route", "discovery:read", "event:publish", "service:read"],
    "content-generator": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "trend-analyzer": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "cover-designer": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "kdp-uploader": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "sales-monitor": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "research-assistant": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "personalization-engine": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "multimodal-generator": ["event:publish", "event:subscribe", "discovery:read", "service:read"],
    "admin": ["admin:full", "service:admin", "event:publish", "event:subscribe", "discovery:register", "gateway:route"]
  }
}
EOF

echo "📋 Created API key configuration"

# Create API key generation script
cat > "${API_KEYS_DIR}/generate-api-key.py" << 'EOF'
#!/usr/bin/env python3
"""
API Key Generation Script for Publish AI Services
"""

import json
import secrets
import hashlib
import sys
from datetime import datetime, timedelta
from pathlib import Path

def generate_api_key(service_name: str, role: str = None) -> dict:
    """Generate API key for a service"""
    
    # Load configuration
    config_path = Path(__file__).parent / "api-key-config.json"
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Determine role and scopes
    if role is None:
        role = service_name
    
    if role not in config["service_roles"]:
        print(f"❌ Unknown role: {role}")
        print(f"Available roles: {list(config['service_roles'].keys())}")
        return None
    
    scopes = config["service_roles"][role]
    
    # Generate API key
    key_length = config["api_key_config"]["key_length"]
    api_key = secrets.token_urlsafe(key_length)
    
    # Create key hash for storage
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Set expiration
    expiry_days = config["api_key_config"]["default_expiry_days"]
    created_at = datetime.utcnow()
    expires_at = created_at + timedelta(days=expiry_days)
    
    # Create key metadata
    key_data = {
        "service_name": service_name,
        "role": role,
        "api_key": api_key,
        "key_hash": key_hash,
        "scopes": scopes,
        "created_at": created_at.isoformat(),
        "expires_at": expires_at.isoformat(),
        "active": True,
        "usage_count": 0,
        "last_used": None,
        "rate_limit": config["api_key_config"]["rate_limits"].get(
            role, 
            config["api_key_config"]["rate_limits"]["default"]
        )
    }
    
    return key_data

def save_api_key(key_data: dict):
    """Save API key to key store"""
    
    keys_dir = Path(__file__).parent / "keys"
    keys_dir.mkdir(exist_ok=True)
    
    service_name = key_data["service_name"]
    key_file = keys_dir / f"{service_name}.json"
    
    # Load existing keys or create new file
    if key_file.exists():
        with open(key_file, 'r') as f:
            existing_keys = json.load(f)
    else:
        existing_keys = {"service": service_name, "keys": []}
    
    # Add new key
    existing_keys["keys"].append({
        "key_hash": key_data["key_hash"],
        "role": key_data["role"],
        "scopes": key_data["scopes"],
        "created_at": key_data["created_at"],
        "expires_at": key_data["expires_at"],
        "active": key_data["active"],
        "rate_limit": key_data["rate_limit"]
    })
    
    # Save to file
    with open(key_file, 'w') as f:
        json.dump(existing_keys, f, indent=2)
    
    return key_file

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 generate-api-key.py <service-name> [role]")
        print("\nAvailable services: event-bus, service-discovery, api-gateway, content-generator, etc.")
        print("Available roles: service, admin")
        sys.exit(1)
    
    service_name = sys.argv[1]
    role = sys.argv[2] if len(sys.argv) > 2 else None
    
    print(f"🔑 Generating API key for: {service_name}")
    
    # Generate key
    key_data = generate_api_key(service_name, role)
    if not key_data:
        sys.exit(1)
    
    # Save key
    key_file = save_api_key(key_data)
    
    print(f"✅ API key generated successfully!")
    print(f"📁 Key saved to: {key_file}")
    print(f"🔐 API Key: {key_data['api_key']}")
    print(f"🏷️  Role: {key_data['role']}")
    print(f"🎯 Scopes: {', '.join(key_data['scopes'])}")
    print(f"📅 Expires: {key_data['expires_at'][:10]}")
    print(f"⚡ Rate Limit: {key_data['rate_limit']}")
    
    # Create environment file
    env_file = Path(__file__).parent / "keys" / f"{service_name}.env"
    with open(env_file, 'w') as f:
        f.write(f"# API Key for {service_name}\n")
        f.write(f"API_KEY={key_data['api_key']}\n")
        f.write(f"SERVICE_NAME={service_name}\n")
        f.write(f"ROLE={key_data['role']}\n")
        f.write(f"EXPIRES_AT={key_data['expires_at']}\n")
    
    print(f"📄 Environment file: {env_file}")

if __name__ == "__main__":
    main()
EOF

chmod +x "${API_KEYS_DIR}/generate-api-key.py"
echo "🐍 Created API key generation script"

# Create API key validation script
cat > "${API_KEYS_DIR}/validate-api-key.py" << 'EOF'
#!/usr/bin/env python3
"""
API Key Validation Script for Publish AI Services
"""

import json
import hashlib
import sys
from datetime import datetime
from pathlib import Path

def validate_api_key(api_key: str, required_scope: str = None) -> dict:
    """Validate API key and check scope"""
    
    # Hash the provided key
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Search through all service key files
    keys_dir = Path(__file__).parent / "keys"
    if not keys_dir.exists():
        return {"valid": False, "reason": "key_store_not_found"}
    
    for key_file in keys_dir.glob("*.json"):
        try:
            with open(key_file, 'r') as f:
                service_keys = json.load(f)
            
            for key_data in service_keys.get("keys", []):
                if key_data["key_hash"] == key_hash:
                    # Found matching key, check validity
                    if not key_data.get("active", False):
                        return {"valid": False, "reason": "key_inactive"}
                    
                    # Check expiration
                    expires_at = datetime.fromisoformat(key_data["expires_at"])
                    if datetime.utcnow() > expires_at:
                        return {"valid": False, "reason": "key_expired"}
                    
                    # Check scope if required
                    if required_scope and required_scope not in key_data.get("scopes", []):
                        return {"valid": False, "reason": "insufficient_scope", "required": required_scope}
                    
                    # Key is valid
                    return {
                        "valid": True,
                        "service": service_keys["service"],
                        "role": key_data["role"],
                        "scopes": key_data["scopes"],
                        "expires_at": key_data["expires_at"],
                        "rate_limit": key_data.get("rate_limit", "1000/hour")
                    }
        
        except Exception as e:
            continue
    
    return {"valid": False, "reason": "key_not_found"}

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 validate-api-key.py <api-key> [required-scope]")
        sys.exit(1)
    
    api_key = sys.argv[1]
    required_scope = sys.argv[2] if len(sys.argv) > 2 else None
    
    result = validate_api_key(api_key, required_scope)
    
    if result["valid"]:
        print("✅ API key is valid")
        print(f"🏷️  Service: {result['service']}")
        print(f"🎭 Role: {result['role']}")
        print(f"🎯 Scopes: {', '.join(result['scopes'])}")
        print(f"📅 Expires: {result['expires_at'][:10]}")
        print(f"⚡ Rate Limit: {result['rate_limit']}")
        sys.exit(0)
    else:
        print(f"❌ API key validation failed: {result['reason']}")
        if "required" in result:
            print(f"🎯 Required scope: {result['required']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

chmod +x "${API_KEYS_DIR}/validate-api-key.py"
echo "✅ Created API key validation script"

# Create key rotation script
cat > "${API_KEYS_DIR}/rotate-api-key.py" << 'EOF'
#!/usr/bin/env python3
"""
API Key Rotation Script for Publish AI Services
"""

import json
import secrets
import hashlib
import sys
from datetime import datetime, timedelta
from pathlib import Path

def rotate_api_key(service_name: str, old_key_hash: str) -> dict:
    """Rotate API key for a service"""
    
    keys_dir = Path(__file__).parent / "keys"
    key_file = keys_dir / f"{service_name}.json"
    
    if not key_file.exists():
        return {"success": False, "reason": "service_not_found"}
    
    # Load existing keys
    with open(key_file, 'r') as f:
        service_keys = json.load(f)
    
    # Find the key to rotate
    old_key_data = None
    for i, key_data in enumerate(service_keys["keys"]):
        if key_data["key_hash"] == old_key_hash:
            old_key_data = key_data
            old_key_index = i
            break
    
    if not old_key_data:
        return {"success": False, "reason": "key_not_found"}
    
    # Generate new API key
    config_path = Path(__file__).parent / "api-key-config.json"
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    key_length = config["api_key_config"]["key_length"]
    new_api_key = secrets.token_urlsafe(key_length)
    new_key_hash = hashlib.sha256(new_api_key.encode()).hexdigest()
    
    # Set new expiration
    expiry_days = config["api_key_config"]["default_expiry_days"]
    created_at = datetime.utcnow()
    expires_at = created_at + timedelta(days=expiry_days)
    
    # Create new key data
    new_key_data = old_key_data.copy()
    new_key_data.update({
        "key_hash": new_key_hash,
        "created_at": created_at.isoformat(),
        "expires_at": expires_at.isoformat(),
        "usage_count": 0,
        "last_used": None,
        "rotated_from": old_key_hash
    })
    
    # Mark old key as inactive
    service_keys["keys"][old_key_index]["active"] = False
    service_keys["keys"][old_key_index]["rotated_at"] = created_at.isoformat()
    
    # Add new key
    service_keys["keys"].append(new_key_data)
    
    # Save updated keys
    with open(key_file, 'w') as f:
        json.dump(service_keys, f, indent=2)
    
    return {
        "success": True,
        "new_api_key": new_api_key,
        "new_key_hash": new_key_hash,
        "expires_at": expires_at.isoformat()
    }

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 rotate-api-key.py <service-name> <old-key-hash>")
        sys.exit(1)
    
    service_name = sys.argv[1]
    old_key_hash = sys.argv[2]
    
    print(f"🔄 Rotating API key for: {service_name}")
    
    result = rotate_api_key(service_name, old_key_hash)
    
    if result["success"]:
        print("✅ API key rotated successfully!")
        print(f"🔐 New API Key: {result['new_api_key']}")
        print(f"📅 Expires: {result['expires_at'][:10]}")
        
        # Update environment file
        env_file = Path(__file__).parent / "keys" / f"{service_name}.env"
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Replace API key line
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('API_KEY='):
                    lines[i] = f"API_KEY={result['new_api_key']}"
                elif line.startswith('EXPIRES_AT='):
                    lines[i] = f"EXPIRES_AT={result['expires_at']}"
            
            with open(env_file, 'w') as f:
                f.write('\n'.join(lines))
            
            print(f"📄 Updated environment file: {env_file}")
    else:
        print(f"❌ Key rotation failed: {result['reason']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

chmod +x "${API_KEYS_DIR}/rotate-api-key.py"
echo "🔄 Created API key rotation script"

# Create master script for managing API keys
cat > "${SECURITY_DIR}/scripts/manage-api-keys.sh" << EOF
#!/bin/bash

# Master API Key Management Script for Publish AI

SECURITY_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")/.." && pwd)"
API_KEYS_DIR="\${SECURITY_DIR}/api-keys"

case "\$1" in
    "generate")
        if [ -z "\$2" ]; then
            echo "Usage: \$0 generate <service-name> [role]"
            exit 1
        fi
        python3 "\${API_KEYS_DIR}/generate-api-key.py" "\$2" "\$3"
        ;;
    "validate")
        if [ -z "\$2" ]; then
            echo "Usage: \$0 validate <api-key> [required-scope]"
            exit 1
        fi
        python3 "\${API_KEYS_DIR}/validate-api-key.py" "\$2" "\$3"
        ;;
    "rotate")
        if [ -z "\$3" ]; then
            echo "Usage: \$0 rotate <service-name> <old-key-hash>"
            exit 1
        fi
        python3 "\${API_KEYS_DIR}/rotate-api-key.py" "\$2" "\$3"
        ;;
    "list")
        echo "📋 Available API Keys:"
        echo "===================="
        for key_file in "\${API_KEYS_DIR}/keys/"*.json; do
            if [ -f "\$key_file" ]; then
                service=\$(basename "\$key_file" .json)
                active_keys=\$(python3 -c "
import json
with open('\$key_file', 'r') as f:
    data = json.load(f)
active = [k for k in data.get('keys', []) if k.get('active', False)]
print(len(active))
")
                echo "🔑 \$service: \$active_keys active key(s)"
            fi
        done
        ;;
    "setup-all")
        echo "🚀 Setting up API keys for all services..."
        services=("event-bus" "service-discovery" "api-gateway" "content-generator" "trend-analyzer" "cover-designer" "kdp-uploader" "sales-monitor" "research-assistant" "personalization-engine" "multimodal-generator")
        
        for service in "\${services[@]}"; do
            echo "Generating API key for \$service..."
            python3 "\${API_KEYS_DIR}/generate-api-key.py" "\$service"
        done
        
        echo "Generating admin API key..."
        python3 "\${API_KEYS_DIR}/generate-api-key.py" "admin" "admin"
        
        echo "✅ All API keys generated!"
        ;;
    *)
        echo "Usage: \$0 {generate|validate|rotate|list|setup-all}"
        echo ""
        echo "Commands:"
        echo "  generate <service> [role]     Generate new API key"
        echo "  validate <key> [scope]        Validate API key"
        echo "  rotate <service> <old-hash>   Rotate API key"
        echo "  list                          List all services with keys"
        echo "  setup-all                     Generate keys for all services"
        exit 1
        ;;
esac
EOF

chmod +x "${SECURITY_DIR}/scripts/manage-api-keys.sh"
echo "🎛️  Created master API key management script"

echo ""
echo "✅ API Key Management System setup completed!"
echo ""
echo "📁 Configuration: ${API_KEYS_DIR}/api-key-config.json"
echo "🐍 Scripts: ${API_KEYS_DIR}/*.py"
echo "🎛️  Manager: ${SECURITY_DIR}/scripts/manage-api-keys.sh"
echo ""
echo "Next steps:"
echo "1. Generate API keys: ./manage-api-keys.sh setup-all"
echo "2. Distribute keys to services"
echo "3. Configure services to use API key authentication"
echo "4. Set up key rotation schedule"