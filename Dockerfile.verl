# Dockerfile.verl - GPU-enabled container for VERL training
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3-pip \
    git \
    wget \
    curl \
    build-essential \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.11 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Install PyTorch with CUDA support
RUN pip3 install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu121

# Install VERL dependencies
RUN pip3 install "tensordict<0.6" --no-deps && \
    pip3 install accelerate \
    codetiming \
    datasets \
    dill \
    hydra-core \
    liger-kernel \
    numpy \
    pandas \
    datasets \
    peft \
    "pyarrow>=15.0.0" \
    pylatexenc \
    "ray[data,train,tune,serve]" \
    torchdata \
    transformers \
    wandb \
    orjson \
    pybind11

# Install VERL from source
WORKDIR /tmp
RUN git clone https://github.com/volcengine/verl.git && \
    cd verl && \
    pip3 install -e . --no-deps

# Install additional dependencies for our publishing app
RUN pip3 install \
    fastapi \
    uvicorn \
    sqlalchemy \
    psycopg2-binary \
    redis \
    pydantic \
    python-multipart \
    aiofiles

# Set working directory
WORKDIR /app

# Copy application files
COPY . .

# Create necessary directories
RUN mkdir -p /app/models /app/training_data /app/checkpoints /shared

# Set environment variables
ENV PYTHONPATH=/app
ENV CUDA_VISIBLE_DEVICES=0
ENV RAY_DISABLE_IMPORT_WARNING=1

# Expose port for VERL trainer API
EXPOSE 8001

# Start VERL trainer service
CMD ["python3", "-m", "app.ml.verl_service"]